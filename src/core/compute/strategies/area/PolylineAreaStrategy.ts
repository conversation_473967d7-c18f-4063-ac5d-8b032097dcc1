/**
 * Area Calculation Strategy for Polyline Elements
 *
 * @remarks
 * This strategy implements the {@link AreaCalculatorStrategy} for calculating the
 * area of Polyline elements ({@link CoreElementType.POLYLINE}).
 *
 * If the polyline is marked as 'closed', this strategy treats its vertices as
 * defining a polygon and calculates the area of that polygon using the
 * `calculatePolygonAreaUtil` function from `../../../../lib/utils/geometry/polygonUtils`.
 *
 * If the polyline is not 'closed' (i.e., it's an open path of connected line segments),
 * it is considered to have no enclosed area, and this strategy will return `0`.
 *
 * The strategy validates that the element is of type `POLYLINE` and, if closed,
 * that it has at least 3 valid points to form a polygon.
 *
 * @module core/compute/strategies/area/PolylineAreaStrategy
 * @see {@link Path.Polyline} for the polyline element type definition.
 */
import type { AreaCalculatorStrategy } from '../../../../types/core/compute'
import type { PointData as IPoint } from '../../../../types/core/element/geometry/point' // Use PointData, alias as IPoint
import type { Element, Path } from '../../../../types/core/elementDefinitions'
import { calculateArea as calculatePolygonAreaUtil } from '../../../../lib/utils/geometry/polygonUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import { ElementType as CoreElementType } from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

/**
 * Implements the {@link AreaCalculatorStrategy} for {@link CoreElementType.POLYLINE} elements.
 */
export class PolylineAreaStrategy implements AreaCalculatorStrategy {
  /**
   * Calculates the area of a Polyline element.
   *
   * @remarks
   * If the polyline is `closed`, its area is calculated as if it were a polygon
   * formed by its points. If the polyline is open, its area is considered `0`.
   *
   * @param element - The polyline element, expected to conform to {@link Path.Polyline} and have type {@link CoreElementType.POLYLINE}.
   * @returns The calculated area if the polyline is closed and forms a valid polygon, otherwise `0`.
   * @throws {@link CoreError} if the provided element is not of type `POLYLINE`.
   * @throws {@link CoreError} if the polyline is closed but has fewer than 3 points or contains invalid point data.
   */
  public calculateArea(element: Element): number {
    if (element.type !== CoreElementType.POLYLINE) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `PolylineAreaStrategy can only calculate area for POLYLINE elements, got ${element.type}`,
        undefined,
        { component: 'PolylineAreaStrategy', operation: 'calculateArea', target: element.id },
      )
    }

    const polylineElement = element as unknown as Path.Polyline // Added unknown for type safety

    const points = polylineElement.properties.points as IPoint[] | undefined
    const closed = polylineElement.properties.closed === true

    if (closed !== true) {
      return 0 // Open polylines have no area
    }

    // If closed, it must be a valid polygon
    if (points === null || points === undefined || !Array.isArray(points) || points.length < 3) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Closed polyline (ID: ${element.id}) must have at least 3 points for area calculation. Found: ${points?.length ?? 0}`,
        undefined,
        { component: 'PolylineAreaStrategy', operation: 'calculateArea', target: element.id, metadata: { pointsLength: points?.length ?? 0 } },
      )
    }

    // Ensure all points are valid
    for (let i = 0; i < points.length; i++) {
      const p = points[i]
      if (p === null || p === undefined || typeof p.x !== 'number' || typeof p.y !== 'number' || !Number.isFinite(p.x) || !Number.isFinite(p.y)) {
        throw new CoreError(
          ErrorType.InvalidParameter,
          `Polyline element (ID: ${element.id}) contains invalid point data at index ${i}.`,
          undefined,
          { component: 'PolylineAreaStrategy', operation: 'calculateArea', target: element.id, metadata: { pointIndex: i, pointData: p } },
        )
      }
    }

    // Delegate to the utility function for polygon area calculation
    return calculatePolygonAreaUtil(points)
  }

  /**
   * Returns the element type that this strategy is responsible for.
   * @returns {@link CoreElementType.POLYLINE} - The polyline element type that this strategy handles
   */
  public getElementType(): CoreElementType {
    return CoreElementType.POLYLINE
  }
}
