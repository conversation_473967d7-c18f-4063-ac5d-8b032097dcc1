import type { ValidationError, ValidationResult } from '@/types/core/element/validator'
import type { EventBus } from '@/types/services/events'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ErrorType } from '@/core/errors/CoreError'
import { checkValidationResult, handleValidationResultAndPublishError } from '@/core/utils/validationUtils'

// Import the mocked function
import { publishError } from '@/services/event-bus/helpers/error-helpers'

// Mock publishError function
vi.mock('@/services/event-bus/helpers/error-helpers', () => ({
  publishError: vi.fn(),
}))

describe('validationUtils', () => {
  // Spy on console.error
  const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

  beforeEach(() => {
    // Clear mocks before each test
    vi.clearAllMocks()
  })

  afterEach(() => {
    // Reset mocks after each test
    vi.resetAllMocks()
  })

  describe('handleValidationResultAndPublishError', () => {
    it('should return true for valid validation result without publishing error', () => {
      const mockEventBus = { publish: vi.fn() } as unknown as EventBus
      const validResult: ValidationResult = { valid: true }
      const context = { shapeId: '123' }
      const serviceName = 'TestService'

      const result = handleValidationResultAndPublishError(mockEventBus, validResult, context, serviceName)

      expect(result).toBe(true)
      expect(consoleErrorSpy).not.toHaveBeenCalled()
      expect(publishError).not.toHaveBeenCalled()
    })

    it('should return false and publish error for invalid validation result with errors', () => {
      const mockEventBus = { publish: vi.fn() } as unknown as EventBus
      const errors: ValidationError[] = [
        { code: 'ERR1', message: 'First error', path: 'prop1' },
        { code: 'ERR2', message: 'Second error', path: 'prop2' },
      ]

      const invalidResult: ValidationResult = {
        valid: false,
        errors,
      }

      const context = { shapeId: '123', operation: 'update' }
      const serviceName = 'TestService'

      const result = handleValidationResultAndPublishError(mockEventBus, invalidResult, context, serviceName)

      // Check return value
      expect(result).toBe(false)

      // Check publishError was called with correct data
      expect(publishError).toHaveBeenCalledWith(
        mockEventBus,
        ErrorType.VALIDATION_FAILED,
        'Validation failed in TestService: [ERR1] First error (Path: prop1); [ERR2] Second error (Path: prop2)',
        {
          validationErrors: errors,
          context: { ...context, service: serviceName },
        },
      )
    })

    it('should handle invalid validation result with no errors array', () => {
      const mockEventBus = { publish: vi.fn() } as unknown as EventBus
      const invalidResult: ValidationResult = { valid: false }
      const context = { shapeId: '123' }
      const serviceName = 'TestService'

      const result = handleValidationResultAndPublishError(mockEventBus, invalidResult, context, serviceName)

      expect(result).toBe(false)

      // Check publishError was called with correct data
      expect(publishError).toHaveBeenCalledWith(
        mockEventBus,
        ErrorType.VALIDATION_FAILED,
        'Validation failed in TestService: Unknown validation failure',
        {
          validationErrors: undefined,
          context: { ...context, service: serviceName },
        },
      )
    })

    it('should handle errors with missing path property', () => {
      const mockEventBus = { publish: vi.fn() } as unknown as EventBus
      const errors: ValidationError[] = [
        { code: 'ERR1', message: 'Error without path' },
      ]

      const invalidResult: ValidationResult = {
        valid: false,
        errors,
      }

      const context = { shapeId: '123' }
      const serviceName = 'TestService'

      handleValidationResultAndPublishError(mockEventBus, invalidResult, context, serviceName)

      // Check publishError was called with correct data
      expect(publishError).toHaveBeenCalledWith(
        mockEventBus,
        ErrorType.VALIDATION_FAILED,
        'Validation failed in TestService: [ERR1] Error without path (Path: ?)',
        {
          validationErrors: errors,
          context: { ...context, service: serviceName },
        },
      )
    })
  })

  describe('checkValidationResult', () => {
    it('should return { isValid: true } for a valid validation result', () => {
      const validResult: ValidationResult = { valid: true }
      const result = checkValidationResult(validResult)

      expect(result).toEqual({ isValid: true })
      expect(result.errorMessages).toBeUndefined()
    })

    it('should return { isValid: false, errorMessages: string } for an invalid validation result', () => {
      const errors: ValidationError[] = [
        { code: 'ERR001', message: 'Test error 1', path: 'field1' },
        { code: 'ERR002', message: 'Test error 2', path: 'field2' },
      ]
      const invalidResult: ValidationResult = { valid: false, errors }
      const result = checkValidationResult(invalidResult)

      expect(result.isValid).toBe(false)
      expect(result.errorMessages).toBeDefined()
      expect(result.errorMessages).toContain('[ERR001] Test error 1 (Path: field1)')
      expect(result.errorMessages).toContain('[ERR002] Test error 2 (Path: field2)')
    })

    it('should handle invalid result with no errors array', () => {
      const invalidResult: ValidationResult = { valid: false }
      const result = checkValidationResult(invalidResult)

      expect(result.isValid).toBe(false)
      expect(result.errorMessages).toBe('Unknown validation failure')
    })

    it('should handle errors with undefined path', () => {
      const errors: ValidationError[] = [
        { code: 'ERR001', message: 'Test error 1' },
      ]
      const invalidResult: ValidationResult = { valid: false, errors }
      const result = checkValidationResult(invalidResult)

      expect(result.isValid).toBe(false)
      expect(result.errorMessages).toContain('[ERR001] Test error 1 (Path: ?)')
    })
  })
})
