/**
 * Data Events Type Definitions
 *
 * This file defines the event types related to data operations
 * including data loading, history, templates, export and storage.
 *
 * @module types/services/events
 */

import type { AppEventType } from './eventTypes'
import type { ShapeElement } from '@/types/core'

/**
 * Design Data interface definition (temporary, can be moved to a proper location later)
 */
export interface DesignData {
  /** Unique identifier for the design */
  id: string
  /** Design title */
  title: string
  /** Creation timestamp */
  createdAt: number
  /** Last modification timestamp */
  updatedAt: number
  /** Shape data */
  shapes?: Record<string, unknown>
  /** Canvas settings */
  canvas?: {
    width: number
    height: number
    backgroundColor?: string
  }
  /** Additional metadata */
  metadata?: Record<string, unknown>
}

/**
 * Data Loading Events
 */
export interface DataLoadEvent {
  type: AppEventType.DataLoadRequest | AppEventType.DataLoadProgress | AppEventType.DataLoadComplete | AppEventType.DataLoadError
  payload: {
    designId?: string
    data?: DesignData
    progress?: number
    error?: string
  }
}

/**
 * History Events
 *
 * Consolidated history events into a single type with discriminated union
 */
export interface HistoryEvent {
  type: AppEventType.HistoryUndo |
    AppEventType.HistoryRedo |
    AppEventType.HistoryCheckpoint |
    AppEventType.HistoryError |
    AppEventType.HistoryUndoPerformed |
    AppEventType.HistoryRedoPerformed |
    AppEventType.HistoryBatchOperationPerformed
  payload: {
    state?: DesignData
    timestamp?: number
    checkpointId?: string
    error?: string
    remainingUndoStates?: number
    remainingRedoStates?: number
    operation?: 'undo' | 'redo'
    count?: number
  }
}

/**
 * Template Events
 *
 * Consolidated template events into a single type with discriminated union
 */
export interface TemplateEvent {
  type: AppEventType.TemplateApply | AppEventType.TemplateApplyRequest | AppEventType.TemplateApplyValidate | AppEventType.TemplateApplyComplete | AppEventType.TemplateApplyError
  payload: {
    templateId: string
    data?: DesignData
    validationResult?: {
      isValid: boolean
      errors?: string[]
    }
    error?: string
  }
}

/**
 * Export Events
 *
 * Consolidated export events into a single type with discriminated union
 */
export interface ExportEvent {
  type: AppEventType.ExportRequest | AppEventType.ExportPrepare | AppEventType.ExportProgress | AppEventType.ExportComplete | AppEventType.ExportError
  payload: {
    format: 'pdf' | 'svg' | 'png' | string
    elements?: ShapeElement[]
    data?: Blob
    error?: string
  }
}

/**
 * Storage Events
 */
export interface StorageEvent {
  type: AppEventType.StorageSaveRequest | AppEventType.StorageSaveComplete | AppEventType.StorageLoadRequest | AppEventType.StorageLoadComplete | AppEventType.StorageBatch | AppEventType.StorageError
  payload: {
    operation: 'save' | 'load' | 'batch' | 'delete' | 'init' | 'autoSave' | 'getSavedStates' | 'recover' | 'recover-fallback' | 'exportStates' | 'importStates'
    data?: DesignData
    batchId?: string
    error?: string
  }
}

/**
 * Data Store Events
 */
export interface DataStoreEvent {
  type: AppEventType.StoreStateChange | AppEventType.StoreStateSync | AppEventType.StoreStateError
  payload: {
    stateType: 'shapes' | 'canvas' | 'ui' | 'all'
    data?: Partial<DesignData>
    timestamp?: number
    error?: string
  }
}
