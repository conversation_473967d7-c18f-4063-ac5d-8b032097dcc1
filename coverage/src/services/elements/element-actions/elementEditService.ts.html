
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/services/elements/element-actions/elementEditService.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">src/services/elements/element-actions</a> elementEditService.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/1014</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/1014</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a>
<a name='L618'></a><a href='#L618'>618</a>
<a name='L619'></a><a href='#L619'>619</a>
<a name='L620'></a><a href='#L620'>620</a>
<a name='L621'></a><a href='#L621'>621</a>
<a name='L622'></a><a href='#L622'>622</a>
<a name='L623'></a><a href='#L623'>623</a>
<a name='L624'></a><a href='#L624'>624</a>
<a name='L625'></a><a href='#L625'>625</a>
<a name='L626'></a><a href='#L626'>626</a>
<a name='L627'></a><a href='#L627'>627</a>
<a name='L628'></a><a href='#L628'>628</a>
<a name='L629'></a><a href='#L629'>629</a>
<a name='L630'></a><a href='#L630'>630</a>
<a name='L631'></a><a href='#L631'>631</a>
<a name='L632'></a><a href='#L632'>632</a>
<a name='L633'></a><a href='#L633'>633</a>
<a name='L634'></a><a href='#L634'>634</a>
<a name='L635'></a><a href='#L635'>635</a>
<a name='L636'></a><a href='#L636'>636</a>
<a name='L637'></a><a href='#L637'>637</a>
<a name='L638'></a><a href='#L638'>638</a>
<a name='L639'></a><a href='#L639'>639</a>
<a name='L640'></a><a href='#L640'>640</a>
<a name='L641'></a><a href='#L641'>641</a>
<a name='L642'></a><a href='#L642'>642</a>
<a name='L643'></a><a href='#L643'>643</a>
<a name='L644'></a><a href='#L644'>644</a>
<a name='L645'></a><a href='#L645'>645</a>
<a name='L646'></a><a href='#L646'>646</a>
<a name='L647'></a><a href='#L647'>647</a>
<a name='L648'></a><a href='#L648'>648</a>
<a name='L649'></a><a href='#L649'>649</a>
<a name='L650'></a><a href='#L650'>650</a>
<a name='L651'></a><a href='#L651'>651</a>
<a name='L652'></a><a href='#L652'>652</a>
<a name='L653'></a><a href='#L653'>653</a>
<a name='L654'></a><a href='#L654'>654</a>
<a name='L655'></a><a href='#L655'>655</a>
<a name='L656'></a><a href='#L656'>656</a>
<a name='L657'></a><a href='#L657'>657</a>
<a name='L658'></a><a href='#L658'>658</a>
<a name='L659'></a><a href='#L659'>659</a>
<a name='L660'></a><a href='#L660'>660</a>
<a name='L661'></a><a href='#L661'>661</a>
<a name='L662'></a><a href='#L662'>662</a>
<a name='L663'></a><a href='#L663'>663</a>
<a name='L664'></a><a href='#L664'>664</a>
<a name='L665'></a><a href='#L665'>665</a>
<a name='L666'></a><a href='#L666'>666</a>
<a name='L667'></a><a href='#L667'>667</a>
<a name='L668'></a><a href='#L668'>668</a>
<a name='L669'></a><a href='#L669'>669</a>
<a name='L670'></a><a href='#L670'>670</a>
<a name='L671'></a><a href='#L671'>671</a>
<a name='L672'></a><a href='#L672'>672</a>
<a name='L673'></a><a href='#L673'>673</a>
<a name='L674'></a><a href='#L674'>674</a>
<a name='L675'></a><a href='#L675'>675</a>
<a name='L676'></a><a href='#L676'>676</a>
<a name='L677'></a><a href='#L677'>677</a>
<a name='L678'></a><a href='#L678'>678</a>
<a name='L679'></a><a href='#L679'>679</a>
<a name='L680'></a><a href='#L680'>680</a>
<a name='L681'></a><a href='#L681'>681</a>
<a name='L682'></a><a href='#L682'>682</a>
<a name='L683'></a><a href='#L683'>683</a>
<a name='L684'></a><a href='#L684'>684</a>
<a name='L685'></a><a href='#L685'>685</a>
<a name='L686'></a><a href='#L686'>686</a>
<a name='L687'></a><a href='#L687'>687</a>
<a name='L688'></a><a href='#L688'>688</a>
<a name='L689'></a><a href='#L689'>689</a>
<a name='L690'></a><a href='#L690'>690</a>
<a name='L691'></a><a href='#L691'>691</a>
<a name='L692'></a><a href='#L692'>692</a>
<a name='L693'></a><a href='#L693'>693</a>
<a name='L694'></a><a href='#L694'>694</a>
<a name='L695'></a><a href='#L695'>695</a>
<a name='L696'></a><a href='#L696'>696</a>
<a name='L697'></a><a href='#L697'>697</a>
<a name='L698'></a><a href='#L698'>698</a>
<a name='L699'></a><a href='#L699'>699</a>
<a name='L700'></a><a href='#L700'>700</a>
<a name='L701'></a><a href='#L701'>701</a>
<a name='L702'></a><a href='#L702'>702</a>
<a name='L703'></a><a href='#L703'>703</a>
<a name='L704'></a><a href='#L704'>704</a>
<a name='L705'></a><a href='#L705'>705</a>
<a name='L706'></a><a href='#L706'>706</a>
<a name='L707'></a><a href='#L707'>707</a>
<a name='L708'></a><a href='#L708'>708</a>
<a name='L709'></a><a href='#L709'>709</a>
<a name='L710'></a><a href='#L710'>710</a>
<a name='L711'></a><a href='#L711'>711</a>
<a name='L712'></a><a href='#L712'>712</a>
<a name='L713'></a><a href='#L713'>713</a>
<a name='L714'></a><a href='#L714'>714</a>
<a name='L715'></a><a href='#L715'>715</a>
<a name='L716'></a><a href='#L716'>716</a>
<a name='L717'></a><a href='#L717'>717</a>
<a name='L718'></a><a href='#L718'>718</a>
<a name='L719'></a><a href='#L719'>719</a>
<a name='L720'></a><a href='#L720'>720</a>
<a name='L721'></a><a href='#L721'>721</a>
<a name='L722'></a><a href='#L722'>722</a>
<a name='L723'></a><a href='#L723'>723</a>
<a name='L724'></a><a href='#L724'>724</a>
<a name='L725'></a><a href='#L725'>725</a>
<a name='L726'></a><a href='#L726'>726</a>
<a name='L727'></a><a href='#L727'>727</a>
<a name='L728'></a><a href='#L728'>728</a>
<a name='L729'></a><a href='#L729'>729</a>
<a name='L730'></a><a href='#L730'>730</a>
<a name='L731'></a><a href='#L731'>731</a>
<a name='L732'></a><a href='#L732'>732</a>
<a name='L733'></a><a href='#L733'>733</a>
<a name='L734'></a><a href='#L734'>734</a>
<a name='L735'></a><a href='#L735'>735</a>
<a name='L736'></a><a href='#L736'>736</a>
<a name='L737'></a><a href='#L737'>737</a>
<a name='L738'></a><a href='#L738'>738</a>
<a name='L739'></a><a href='#L739'>739</a>
<a name='L740'></a><a href='#L740'>740</a>
<a name='L741'></a><a href='#L741'>741</a>
<a name='L742'></a><a href='#L742'>742</a>
<a name='L743'></a><a href='#L743'>743</a>
<a name='L744'></a><a href='#L744'>744</a>
<a name='L745'></a><a href='#L745'>745</a>
<a name='L746'></a><a href='#L746'>746</a>
<a name='L747'></a><a href='#L747'>747</a>
<a name='L748'></a><a href='#L748'>748</a>
<a name='L749'></a><a href='#L749'>749</a>
<a name='L750'></a><a href='#L750'>750</a>
<a name='L751'></a><a href='#L751'>751</a>
<a name='L752'></a><a href='#L752'>752</a>
<a name='L753'></a><a href='#L753'>753</a>
<a name='L754'></a><a href='#L754'>754</a>
<a name='L755'></a><a href='#L755'>755</a>
<a name='L756'></a><a href='#L756'>756</a>
<a name='L757'></a><a href='#L757'>757</a>
<a name='L758'></a><a href='#L758'>758</a>
<a name='L759'></a><a href='#L759'>759</a>
<a name='L760'></a><a href='#L760'>760</a>
<a name='L761'></a><a href='#L761'>761</a>
<a name='L762'></a><a href='#L762'>762</a>
<a name='L763'></a><a href='#L763'>763</a>
<a name='L764'></a><a href='#L764'>764</a>
<a name='L765'></a><a href='#L765'>765</a>
<a name='L766'></a><a href='#L766'>766</a>
<a name='L767'></a><a href='#L767'>767</a>
<a name='L768'></a><a href='#L768'>768</a>
<a name='L769'></a><a href='#L769'>769</a>
<a name='L770'></a><a href='#L770'>770</a>
<a name='L771'></a><a href='#L771'>771</a>
<a name='L772'></a><a href='#L772'>772</a>
<a name='L773'></a><a href='#L773'>773</a>
<a name='L774'></a><a href='#L774'>774</a>
<a name='L775'></a><a href='#L775'>775</a>
<a name='L776'></a><a href='#L776'>776</a>
<a name='L777'></a><a href='#L777'>777</a>
<a name='L778'></a><a href='#L778'>778</a>
<a name='L779'></a><a href='#L779'>779</a>
<a name='L780'></a><a href='#L780'>780</a>
<a name='L781'></a><a href='#L781'>781</a>
<a name='L782'></a><a href='#L782'>782</a>
<a name='L783'></a><a href='#L783'>783</a>
<a name='L784'></a><a href='#L784'>784</a>
<a name='L785'></a><a href='#L785'>785</a>
<a name='L786'></a><a href='#L786'>786</a>
<a name='L787'></a><a href='#L787'>787</a>
<a name='L788'></a><a href='#L788'>788</a>
<a name='L789'></a><a href='#L789'>789</a>
<a name='L790'></a><a href='#L790'>790</a>
<a name='L791'></a><a href='#L791'>791</a>
<a name='L792'></a><a href='#L792'>792</a>
<a name='L793'></a><a href='#L793'>793</a>
<a name='L794'></a><a href='#L794'>794</a>
<a name='L795'></a><a href='#L795'>795</a>
<a name='L796'></a><a href='#L796'>796</a>
<a name='L797'></a><a href='#L797'>797</a>
<a name='L798'></a><a href='#L798'>798</a>
<a name='L799'></a><a href='#L799'>799</a>
<a name='L800'></a><a href='#L800'>800</a>
<a name='L801'></a><a href='#L801'>801</a>
<a name='L802'></a><a href='#L802'>802</a>
<a name='L803'></a><a href='#L803'>803</a>
<a name='L804'></a><a href='#L804'>804</a>
<a name='L805'></a><a href='#L805'>805</a>
<a name='L806'></a><a href='#L806'>806</a>
<a name='L807'></a><a href='#L807'>807</a>
<a name='L808'></a><a href='#L808'>808</a>
<a name='L809'></a><a href='#L809'>809</a>
<a name='L810'></a><a href='#L810'>810</a>
<a name='L811'></a><a href='#L811'>811</a>
<a name='L812'></a><a href='#L812'>812</a>
<a name='L813'></a><a href='#L813'>813</a>
<a name='L814'></a><a href='#L814'>814</a>
<a name='L815'></a><a href='#L815'>815</a>
<a name='L816'></a><a href='#L816'>816</a>
<a name='L817'></a><a href='#L817'>817</a>
<a name='L818'></a><a href='#L818'>818</a>
<a name='L819'></a><a href='#L819'>819</a>
<a name='L820'></a><a href='#L820'>820</a>
<a name='L821'></a><a href='#L821'>821</a>
<a name='L822'></a><a href='#L822'>822</a>
<a name='L823'></a><a href='#L823'>823</a>
<a name='L824'></a><a href='#L824'>824</a>
<a name='L825'></a><a href='#L825'>825</a>
<a name='L826'></a><a href='#L826'>826</a>
<a name='L827'></a><a href='#L827'>827</a>
<a name='L828'></a><a href='#L828'>828</a>
<a name='L829'></a><a href='#L829'>829</a>
<a name='L830'></a><a href='#L830'>830</a>
<a name='L831'></a><a href='#L831'>831</a>
<a name='L832'></a><a href='#L832'>832</a>
<a name='L833'></a><a href='#L833'>833</a>
<a name='L834'></a><a href='#L834'>834</a>
<a name='L835'></a><a href='#L835'>835</a>
<a name='L836'></a><a href='#L836'>836</a>
<a name='L837'></a><a href='#L837'>837</a>
<a name='L838'></a><a href='#L838'>838</a>
<a name='L839'></a><a href='#L839'>839</a>
<a name='L840'></a><a href='#L840'>840</a>
<a name='L841'></a><a href='#L841'>841</a>
<a name='L842'></a><a href='#L842'>842</a>
<a name='L843'></a><a href='#L843'>843</a>
<a name='L844'></a><a href='#L844'>844</a>
<a name='L845'></a><a href='#L845'>845</a>
<a name='L846'></a><a href='#L846'>846</a>
<a name='L847'></a><a href='#L847'>847</a>
<a name='L848'></a><a href='#L848'>848</a>
<a name='L849'></a><a href='#L849'>849</a>
<a name='L850'></a><a href='#L850'>850</a>
<a name='L851'></a><a href='#L851'>851</a>
<a name='L852'></a><a href='#L852'>852</a>
<a name='L853'></a><a href='#L853'>853</a>
<a name='L854'></a><a href='#L854'>854</a>
<a name='L855'></a><a href='#L855'>855</a>
<a name='L856'></a><a href='#L856'>856</a>
<a name='L857'></a><a href='#L857'>857</a>
<a name='L858'></a><a href='#L858'>858</a>
<a name='L859'></a><a href='#L859'>859</a>
<a name='L860'></a><a href='#L860'>860</a>
<a name='L861'></a><a href='#L861'>861</a>
<a name='L862'></a><a href='#L862'>862</a>
<a name='L863'></a><a href='#L863'>863</a>
<a name='L864'></a><a href='#L864'>864</a>
<a name='L865'></a><a href='#L865'>865</a>
<a name='L866'></a><a href='#L866'>866</a>
<a name='L867'></a><a href='#L867'>867</a>
<a name='L868'></a><a href='#L868'>868</a>
<a name='L869'></a><a href='#L869'>869</a>
<a name='L870'></a><a href='#L870'>870</a>
<a name='L871'></a><a href='#L871'>871</a>
<a name='L872'></a><a href='#L872'>872</a>
<a name='L873'></a><a href='#L873'>873</a>
<a name='L874'></a><a href='#L874'>874</a>
<a name='L875'></a><a href='#L875'>875</a>
<a name='L876'></a><a href='#L876'>876</a>
<a name='L877'></a><a href='#L877'>877</a>
<a name='L878'></a><a href='#L878'>878</a>
<a name='L879'></a><a href='#L879'>879</a>
<a name='L880'></a><a href='#L880'>880</a>
<a name='L881'></a><a href='#L881'>881</a>
<a name='L882'></a><a href='#L882'>882</a>
<a name='L883'></a><a href='#L883'>883</a>
<a name='L884'></a><a href='#L884'>884</a>
<a name='L885'></a><a href='#L885'>885</a>
<a name='L886'></a><a href='#L886'>886</a>
<a name='L887'></a><a href='#L887'>887</a>
<a name='L888'></a><a href='#L888'>888</a>
<a name='L889'></a><a href='#L889'>889</a>
<a name='L890'></a><a href='#L890'>890</a>
<a name='L891'></a><a href='#L891'>891</a>
<a name='L892'></a><a href='#L892'>892</a>
<a name='L893'></a><a href='#L893'>893</a>
<a name='L894'></a><a href='#L894'>894</a>
<a name='L895'></a><a href='#L895'>895</a>
<a name='L896'></a><a href='#L896'>896</a>
<a name='L897'></a><a href='#L897'>897</a>
<a name='L898'></a><a href='#L898'>898</a>
<a name='L899'></a><a href='#L899'>899</a>
<a name='L900'></a><a href='#L900'>900</a>
<a name='L901'></a><a href='#L901'>901</a>
<a name='L902'></a><a href='#L902'>902</a>
<a name='L903'></a><a href='#L903'>903</a>
<a name='L904'></a><a href='#L904'>904</a>
<a name='L905'></a><a href='#L905'>905</a>
<a name='L906'></a><a href='#L906'>906</a>
<a name='L907'></a><a href='#L907'>907</a>
<a name='L908'></a><a href='#L908'>908</a>
<a name='L909'></a><a href='#L909'>909</a>
<a name='L910'></a><a href='#L910'>910</a>
<a name='L911'></a><a href='#L911'>911</a>
<a name='L912'></a><a href='#L912'>912</a>
<a name='L913'></a><a href='#L913'>913</a>
<a name='L914'></a><a href='#L914'>914</a>
<a name='L915'></a><a href='#L915'>915</a>
<a name='L916'></a><a href='#L916'>916</a>
<a name='L917'></a><a href='#L917'>917</a>
<a name='L918'></a><a href='#L918'>918</a>
<a name='L919'></a><a href='#L919'>919</a>
<a name='L920'></a><a href='#L920'>920</a>
<a name='L921'></a><a href='#L921'>921</a>
<a name='L922'></a><a href='#L922'>922</a>
<a name='L923'></a><a href='#L923'>923</a>
<a name='L924'></a><a href='#L924'>924</a>
<a name='L925'></a><a href='#L925'>925</a>
<a name='L926'></a><a href='#L926'>926</a>
<a name='L927'></a><a href='#L927'>927</a>
<a name='L928'></a><a href='#L928'>928</a>
<a name='L929'></a><a href='#L929'>929</a>
<a name='L930'></a><a href='#L930'>930</a>
<a name='L931'></a><a href='#L931'>931</a>
<a name='L932'></a><a href='#L932'>932</a>
<a name='L933'></a><a href='#L933'>933</a>
<a name='L934'></a><a href='#L934'>934</a>
<a name='L935'></a><a href='#L935'>935</a>
<a name='L936'></a><a href='#L936'>936</a>
<a name='L937'></a><a href='#L937'>937</a>
<a name='L938'></a><a href='#L938'>938</a>
<a name='L939'></a><a href='#L939'>939</a>
<a name='L940'></a><a href='#L940'>940</a>
<a name='L941'></a><a href='#L941'>941</a>
<a name='L942'></a><a href='#L942'>942</a>
<a name='L943'></a><a href='#L943'>943</a>
<a name='L944'></a><a href='#L944'>944</a>
<a name='L945'></a><a href='#L945'>945</a>
<a name='L946'></a><a href='#L946'>946</a>
<a name='L947'></a><a href='#L947'>947</a>
<a name='L948'></a><a href='#L948'>948</a>
<a name='L949'></a><a href='#L949'>949</a>
<a name='L950'></a><a href='#L950'>950</a>
<a name='L951'></a><a href='#L951'>951</a>
<a name='L952'></a><a href='#L952'>952</a>
<a name='L953'></a><a href='#L953'>953</a>
<a name='L954'></a><a href='#L954'>954</a>
<a name='L955'></a><a href='#L955'>955</a>
<a name='L956'></a><a href='#L956'>956</a>
<a name='L957'></a><a href='#L957'>957</a>
<a name='L958'></a><a href='#L958'>958</a>
<a name='L959'></a><a href='#L959'>959</a>
<a name='L960'></a><a href='#L960'>960</a>
<a name='L961'></a><a href='#L961'>961</a>
<a name='L962'></a><a href='#L962'>962</a>
<a name='L963'></a><a href='#L963'>963</a>
<a name='L964'></a><a href='#L964'>964</a>
<a name='L965'></a><a href='#L965'>965</a>
<a name='L966'></a><a href='#L966'>966</a>
<a name='L967'></a><a href='#L967'>967</a>
<a name='L968'></a><a href='#L968'>968</a>
<a name='L969'></a><a href='#L969'>969</a>
<a name='L970'></a><a href='#L970'>970</a>
<a name='L971'></a><a href='#L971'>971</a>
<a name='L972'></a><a href='#L972'>972</a>
<a name='L973'></a><a href='#L973'>973</a>
<a name='L974'></a><a href='#L974'>974</a>
<a name='L975'></a><a href='#L975'>975</a>
<a name='L976'></a><a href='#L976'>976</a>
<a name='L977'></a><a href='#L977'>977</a>
<a name='L978'></a><a href='#L978'>978</a>
<a name='L979'></a><a href='#L979'>979</a>
<a name='L980'></a><a href='#L980'>980</a>
<a name='L981'></a><a href='#L981'>981</a>
<a name='L982'></a><a href='#L982'>982</a>
<a name='L983'></a><a href='#L983'>983</a>
<a name='L984'></a><a href='#L984'>984</a>
<a name='L985'></a><a href='#L985'>985</a>
<a name='L986'></a><a href='#L986'>986</a>
<a name='L987'></a><a href='#L987'>987</a>
<a name='L988'></a><a href='#L988'>988</a>
<a name='L989'></a><a href='#L989'>989</a>
<a name='L990'></a><a href='#L990'>990</a>
<a name='L991'></a><a href='#L991'>991</a>
<a name='L992'></a><a href='#L992'>992</a>
<a name='L993'></a><a href='#L993'>993</a>
<a name='L994'></a><a href='#L994'>994</a>
<a name='L995'></a><a href='#L995'>995</a>
<a name='L996'></a><a href='#L996'>996</a>
<a name='L997'></a><a href='#L997'>997</a>
<a name='L998'></a><a href='#L998'>998</a>
<a name='L999'></a><a href='#L999'>999</a>
<a name='L1000'></a><a href='#L1000'>1000</a>
<a name='L1001'></a><a href='#L1001'>1001</a>
<a name='L1002'></a><a href='#L1002'>1002</a>
<a name='L1003'></a><a href='#L1003'>1003</a>
<a name='L1004'></a><a href='#L1004'>1004</a>
<a name='L1005'></a><a href='#L1005'>1005</a>
<a name='L1006'></a><a href='#L1006'>1006</a>
<a name='L1007'></a><a href='#L1007'>1007</a>
<a name='L1008'></a><a href='#L1008'>1008</a>
<a name='L1009'></a><a href='#L1009'>1009</a>
<a name='L1010'></a><a href='#L1010'>1010</a>
<a name='L1011'></a><a href='#L1011'>1011</a>
<a name='L1012'></a><a href='#L1012'>1012</a>
<a name='L1013'></a><a href='#L1013'>1013</a>
<a name='L1014'></a><a href='#L1014'>1014</a>
<a name='L1015'></a><a href='#L1015'>1015</a>
<a name='L1016'></a><a href='#L1016'>1016</a>
<a name='L1017'></a><a href='#L1017'>1017</a>
<a name='L1018'></a><a href='#L1018'>1018</a>
<a name='L1019'></a><a href='#L1019'>1019</a>
<a name='L1020'></a><a href='#L1020'>1020</a>
<a name='L1021'></a><a href='#L1021'>1021</a>
<a name='L1022'></a><a href='#L1022'>1022</a>
<a name='L1023'></a><a href='#L1023'>1023</a>
<a name='L1024'></a><a href='#L1024'>1024</a>
<a name='L1025'></a><a href='#L1025'>1025</a>
<a name='L1026'></a><a href='#L1026'>1026</a>
<a name='L1027'></a><a href='#L1027'>1027</a>
<a name='L1028'></a><a href='#L1028'>1028</a>
<a name='L1029'></a><a href='#L1029'>1029</a>
<a name='L1030'></a><a href='#L1030'>1030</a>
<a name='L1031'></a><a href='#L1031'>1031</a>
<a name='L1032'></a><a href='#L1032'>1032</a>
<a name='L1033'></a><a href='#L1033'>1033</a>
<a name='L1034'></a><a href='#L1034'>1034</a>
<a name='L1035'></a><a href='#L1035'>1035</a>
<a name='L1036'></a><a href='#L1036'>1036</a>
<a name='L1037'></a><a href='#L1037'>1037</a>
<a name='L1038'></a><a href='#L1038'>1038</a>
<a name='L1039'></a><a href='#L1039'>1039</a>
<a name='L1040'></a><a href='#L1040'>1040</a>
<a name='L1041'></a><a href='#L1041'>1041</a>
<a name='L1042'></a><a href='#L1042'>1042</a>
<a name='L1043'></a><a href='#L1043'>1043</a>
<a name='L1044'></a><a href='#L1044'>1044</a>
<a name='L1045'></a><a href='#L1045'>1045</a>
<a name='L1046'></a><a href='#L1046'>1046</a>
<a name='L1047'></a><a href='#L1047'>1047</a>
<a name='L1048'></a><a href='#L1048'>1048</a>
<a name='L1049'></a><a href='#L1049'>1049</a>
<a name='L1050'></a><a href='#L1050'>1050</a>
<a name='L1051'></a><a href='#L1051'>1051</a>
<a name='L1052'></a><a href='#L1052'>1052</a>
<a name='L1053'></a><a href='#L1053'>1053</a>
<a name='L1054'></a><a href='#L1054'>1054</a>
<a name='L1055'></a><a href='#L1055'>1055</a>
<a name='L1056'></a><a href='#L1056'>1056</a>
<a name='L1057'></a><a href='#L1057'>1057</a>
<a name='L1058'></a><a href='#L1058'>1058</a>
<a name='L1059'></a><a href='#L1059'>1059</a>
<a name='L1060'></a><a href='#L1060'>1060</a>
<a name='L1061'></a><a href='#L1061'>1061</a>
<a name='L1062'></a><a href='#L1062'>1062</a>
<a name='L1063'></a><a href='#L1063'>1063</a>
<a name='L1064'></a><a href='#L1064'>1064</a>
<a name='L1065'></a><a href='#L1065'>1065</a>
<a name='L1066'></a><a href='#L1066'>1066</a>
<a name='L1067'></a><a href='#L1067'>1067</a>
<a name='L1068'></a><a href='#L1068'>1068</a>
<a name='L1069'></a><a href='#L1069'>1069</a>
<a name='L1070'></a><a href='#L1070'>1070</a>
<a name='L1071'></a><a href='#L1071'>1071</a>
<a name='L1072'></a><a href='#L1072'>1072</a>
<a name='L1073'></a><a href='#L1073'>1073</a>
<a name='L1074'></a><a href='#L1074'>1074</a>
<a name='L1075'></a><a href='#L1075'>1075</a>
<a name='L1076'></a><a href='#L1076'>1076</a>
<a name='L1077'></a><a href='#L1077'>1077</a>
<a name='L1078'></a><a href='#L1078'>1078</a>
<a name='L1079'></a><a href='#L1079'>1079</a>
<a name='L1080'></a><a href='#L1080'>1080</a>
<a name='L1081'></a><a href='#L1081'>1081</a>
<a name='L1082'></a><a href='#L1082'>1082</a>
<a name='L1083'></a><a href='#L1083'>1083</a>
<a name='L1084'></a><a href='#L1084'>1084</a>
<a name='L1085'></a><a href='#L1085'>1085</a>
<a name='L1086'></a><a href='#L1086'>1086</a>
<a name='L1087'></a><a href='#L1087'>1087</a>
<a name='L1088'></a><a href='#L1088'>1088</a>
<a name='L1089'></a><a href='#L1089'>1089</a>
<a name='L1090'></a><a href='#L1090'>1090</a>
<a name='L1091'></a><a href='#L1091'>1091</a>
<a name='L1092'></a><a href='#L1092'>1092</a>
<a name='L1093'></a><a href='#L1093'>1093</a>
<a name='L1094'></a><a href='#L1094'>1094</a>
<a name='L1095'></a><a href='#L1095'>1095</a>
<a name='L1096'></a><a href='#L1096'>1096</a>
<a name='L1097'></a><a href='#L1097'>1097</a>
<a name='L1098'></a><a href='#L1098'>1098</a>
<a name='L1099'></a><a href='#L1099'>1099</a>
<a name='L1100'></a><a href='#L1100'>1100</a>
<a name='L1101'></a><a href='#L1101'>1101</a>
<a name='L1102'></a><a href='#L1102'>1102</a>
<a name='L1103'></a><a href='#L1103'>1103</a>
<a name='L1104'></a><a href='#L1104'>1104</a>
<a name='L1105'></a><a href='#L1105'>1105</a>
<a name='L1106'></a><a href='#L1106'>1106</a>
<a name='L1107'></a><a href='#L1107'>1107</a>
<a name='L1108'></a><a href='#L1108'>1108</a>
<a name='L1109'></a><a href='#L1109'>1109</a>
<a name='L1110'></a><a href='#L1110'>1110</a>
<a name='L1111'></a><a href='#L1111'>1111</a>
<a name='L1112'></a><a href='#L1112'>1112</a>
<a name='L1113'></a><a href='#L1113'>1113</a>
<a name='L1114'></a><a href='#L1114'>1114</a>
<a name='L1115'></a><a href='#L1115'>1115</a>
<a name='L1116'></a><a href='#L1116'>1116</a>
<a name='L1117'></a><a href='#L1117'>1117</a>
<a name='L1118'></a><a href='#L1118'>1118</a>
<a name='L1119'></a><a href='#L1119'>1119</a>
<a name='L1120'></a><a href='#L1120'>1120</a>
<a name='L1121'></a><a href='#L1121'>1121</a>
<a name='L1122'></a><a href='#L1122'>1122</a>
<a name='L1123'></a><a href='#L1123'>1123</a>
<a name='L1124'></a><a href='#L1124'>1124</a>
<a name='L1125'></a><a href='#L1125'>1125</a>
<a name='L1126'></a><a href='#L1126'>1126</a>
<a name='L1127'></a><a href='#L1127'>1127</a>
<a name='L1128'></a><a href='#L1128'>1128</a>
<a name='L1129'></a><a href='#L1129'>1129</a>
<a name='L1130'></a><a href='#L1130'>1130</a>
<a name='L1131'></a><a href='#L1131'>1131</a>
<a name='L1132'></a><a href='#L1132'>1132</a>
<a name='L1133'></a><a href='#L1133'>1133</a>
<a name='L1134'></a><a href='#L1134'>1134</a>
<a name='L1135'></a><a href='#L1135'>1135</a>
<a name='L1136'></a><a href='#L1136'>1136</a>
<a name='L1137'></a><a href='#L1137'>1137</a>
<a name='L1138'></a><a href='#L1138'>1138</a>
<a name='L1139'></a><a href='#L1139'>1139</a>
<a name='L1140'></a><a href='#L1140'>1140</a>
<a name='L1141'></a><a href='#L1141'>1141</a>
<a name='L1142'></a><a href='#L1142'>1142</a>
<a name='L1143'></a><a href='#L1143'>1143</a>
<a name='L1144'></a><a href='#L1144'>1144</a>
<a name='L1145'></a><a href='#L1145'>1145</a>
<a name='L1146'></a><a href='#L1146'>1146</a>
<a name='L1147'></a><a href='#L1147'>1147</a>
<a name='L1148'></a><a href='#L1148'>1148</a>
<a name='L1149'></a><a href='#L1149'>1149</a>
<a name='L1150'></a><a href='#L1150'>1150</a>
<a name='L1151'></a><a href='#L1151'>1151</a>
<a name='L1152'></a><a href='#L1152'>1152</a>
<a name='L1153'></a><a href='#L1153'>1153</a>
<a name='L1154'></a><a href='#L1154'>1154</a>
<a name='L1155'></a><a href='#L1155'>1155</a>
<a name='L1156'></a><a href='#L1156'>1156</a>
<a name='L1157'></a><a href='#L1157'>1157</a>
<a name='L1158'></a><a href='#L1158'>1158</a>
<a name='L1159'></a><a href='#L1159'>1159</a>
<a name='L1160'></a><a href='#L1160'>1160</a>
<a name='L1161'></a><a href='#L1161'>1161</a>
<a name='L1162'></a><a href='#L1162'>1162</a>
<a name='L1163'></a><a href='#L1163'>1163</a>
<a name='L1164'></a><a href='#L1164'>1164</a>
<a name='L1165'></a><a href='#L1165'>1165</a>
<a name='L1166'></a><a href='#L1166'>1166</a>
<a name='L1167'></a><a href='#L1167'>1167</a>
<a name='L1168'></a><a href='#L1168'>1168</a>
<a name='L1169'></a><a href='#L1169'>1169</a>
<a name='L1170'></a><a href='#L1170'>1170</a>
<a name='L1171'></a><a href='#L1171'>1171</a>
<a name='L1172'></a><a href='#L1172'>1172</a>
<a name='L1173'></a><a href='#L1173'>1173</a>
<a name='L1174'></a><a href='#L1174'>1174</a>
<a name='L1175'></a><a href='#L1175'>1175</a>
<a name='L1176'></a><a href='#L1176'>1176</a>
<a name='L1177'></a><a href='#L1177'>1177</a>
<a name='L1178'></a><a href='#L1178'>1178</a>
<a name='L1179'></a><a href='#L1179'>1179</a>
<a name='L1180'></a><a href='#L1180'>1180</a>
<a name='L1181'></a><a href='#L1181'>1181</a>
<a name='L1182'></a><a href='#L1182'>1182</a>
<a name='L1183'></a><a href='#L1183'>1183</a>
<a name='L1184'></a><a href='#L1184'>1184</a>
<a name='L1185'></a><a href='#L1185'>1185</a>
<a name='L1186'></a><a href='#L1186'>1186</a>
<a name='L1187'></a><a href='#L1187'>1187</a>
<a name='L1188'></a><a href='#L1188'>1188</a>
<a name='L1189'></a><a href='#L1189'>1189</a>
<a name='L1190'></a><a href='#L1190'>1190</a>
<a name='L1191'></a><a href='#L1191'>1191</a>
<a name='L1192'></a><a href='#L1192'>1192</a>
<a name='L1193'></a><a href='#L1193'>1193</a>
<a name='L1194'></a><a href='#L1194'>1194</a>
<a name='L1195'></a><a href='#L1195'>1195</a>
<a name='L1196'></a><a href='#L1196'>1196</a>
<a name='L1197'></a><a href='#L1197'>1197</a>
<a name='L1198'></a><a href='#L1198'>1198</a>
<a name='L1199'></a><a href='#L1199'>1199</a>
<a name='L1200'></a><a href='#L1200'>1200</a>
<a name='L1201'></a><a href='#L1201'>1201</a>
<a name='L1202'></a><a href='#L1202'>1202</a>
<a name='L1203'></a><a href='#L1203'>1203</a>
<a name='L1204'></a><a href='#L1204'>1204</a>
<a name='L1205'></a><a href='#L1205'>1205</a>
<a name='L1206'></a><a href='#L1206'>1206</a>
<a name='L1207'></a><a href='#L1207'>1207</a>
<a name='L1208'></a><a href='#L1208'>1208</a>
<a name='L1209'></a><a href='#L1209'>1209</a>
<a name='L1210'></a><a href='#L1210'>1210</a>
<a name='L1211'></a><a href='#L1211'>1211</a>
<a name='L1212'></a><a href='#L1212'>1212</a>
<a name='L1213'></a><a href='#L1213'>1213</a>
<a name='L1214'></a><a href='#L1214'>1214</a>
<a name='L1215'></a><a href='#L1215'>1215</a>
<a name='L1216'></a><a href='#L1216'>1216</a>
<a name='L1217'></a><a href='#L1217'>1217</a>
<a name='L1218'></a><a href='#L1218'>1218</a>
<a name='L1219'></a><a href='#L1219'>1219</a>
<a name='L1220'></a><a href='#L1220'>1220</a>
<a name='L1221'></a><a href='#L1221'>1221</a>
<a name='L1222'></a><a href='#L1222'>1222</a>
<a name='L1223'></a><a href='#L1223'>1223</a>
<a name='L1224'></a><a href='#L1224'>1224</a>
<a name='L1225'></a><a href='#L1225'>1225</a>
<a name='L1226'></a><a href='#L1226'>1226</a>
<a name='L1227'></a><a href='#L1227'>1227</a>
<a name='L1228'></a><a href='#L1228'>1228</a>
<a name='L1229'></a><a href='#L1229'>1229</a>
<a name='L1230'></a><a href='#L1230'>1230</a>
<a name='L1231'></a><a href='#L1231'>1231</a>
<a name='L1232'></a><a href='#L1232'>1232</a>
<a name='L1233'></a><a href='#L1233'>1233</a>
<a name='L1234'></a><a href='#L1234'>1234</a>
<a name='L1235'></a><a href='#L1235'>1235</a>
<a name='L1236'></a><a href='#L1236'>1236</a>
<a name='L1237'></a><a href='#L1237'>1237</a>
<a name='L1238'></a><a href='#L1238'>1238</a>
<a name='L1239'></a><a href='#L1239'>1239</a>
<a name='L1240'></a><a href='#L1240'>1240</a>
<a name='L1241'></a><a href='#L1241'>1241</a>
<a name='L1242'></a><a href='#L1242'>1242</a>
<a name='L1243'></a><a href='#L1243'>1243</a>
<a name='L1244'></a><a href='#L1244'>1244</a>
<a name='L1245'></a><a href='#L1245'>1245</a>
<a name='L1246'></a><a href='#L1246'>1246</a>
<a name='L1247'></a><a href='#L1247'>1247</a>
<a name='L1248'></a><a href='#L1248'>1248</a>
<a name='L1249'></a><a href='#L1249'>1249</a>
<a name='L1250'></a><a href='#L1250'>1250</a>
<a name='L1251'></a><a href='#L1251'>1251</a>
<a name='L1252'></a><a href='#L1252'>1252</a>
<a name='L1253'></a><a href='#L1253'>1253</a>
<a name='L1254'></a><a href='#L1254'>1254</a>
<a name='L1255'></a><a href='#L1255'>1255</a>
<a name='L1256'></a><a href='#L1256'>1256</a>
<a name='L1257'></a><a href='#L1257'>1257</a>
<a name='L1258'></a><a href='#L1258'>1258</a>
<a name='L1259'></a><a href='#L1259'>1259</a>
<a name='L1260'></a><a href='#L1260'>1260</a>
<a name='L1261'></a><a href='#L1261'>1261</a>
<a name='L1262'></a><a href='#L1262'>1262</a>
<a name='L1263'></a><a href='#L1263'>1263</a>
<a name='L1264'></a><a href='#L1264'>1264</a>
<a name='L1265'></a><a href='#L1265'>1265</a>
<a name='L1266'></a><a href='#L1266'>1266</a>
<a name='L1267'></a><a href='#L1267'>1267</a>
<a name='L1268'></a><a href='#L1268'>1268</a>
<a name='L1269'></a><a href='#L1269'>1269</a>
<a name='L1270'></a><a href='#L1270'>1270</a>
<a name='L1271'></a><a href='#L1271'>1271</a>
<a name='L1272'></a><a href='#L1272'>1272</a>
<a name='L1273'></a><a href='#L1273'>1273</a>
<a name='L1274'></a><a href='#L1274'>1274</a>
<a name='L1275'></a><a href='#L1275'>1275</a>
<a name='L1276'></a><a href='#L1276'>1276</a>
<a name='L1277'></a><a href='#L1277'>1277</a>
<a name='L1278'></a><a href='#L1278'>1278</a>
<a name='L1279'></a><a href='#L1279'>1279</a>
<a name='L1280'></a><a href='#L1280'>1280</a>
<a name='L1281'></a><a href='#L1281'>1281</a>
<a name='L1282'></a><a href='#L1282'>1282</a>
<a name='L1283'></a><a href='#L1283'>1283</a>
<a name='L1284'></a><a href='#L1284'>1284</a>
<a name='L1285'></a><a href='#L1285'>1285</a>
<a name='L1286'></a><a href='#L1286'>1286</a>
<a name='L1287'></a><a href='#L1287'>1287</a>
<a name='L1288'></a><a href='#L1288'>1288</a>
<a name='L1289'></a><a href='#L1289'>1289</a>
<a name='L1290'></a><a href='#L1290'>1290</a>
<a name='L1291'></a><a href='#L1291'>1291</a>
<a name='L1292'></a><a href='#L1292'>1292</a>
<a name='L1293'></a><a href='#L1293'>1293</a>
<a name='L1294'></a><a href='#L1294'>1294</a>
<a name='L1295'></a><a href='#L1295'>1295</a>
<a name='L1296'></a><a href='#L1296'>1296</a>
<a name='L1297'></a><a href='#L1297'>1297</a>
<a name='L1298'></a><a href='#L1298'>1298</a>
<a name='L1299'></a><a href='#L1299'>1299</a>
<a name='L1300'></a><a href='#L1300'>1300</a>
<a name='L1301'></a><a href='#L1301'>1301</a>
<a name='L1302'></a><a href='#L1302'>1302</a>
<a name='L1303'></a><a href='#L1303'>1303</a>
<a name='L1304'></a><a href='#L1304'>1304</a>
<a name='L1305'></a><a href='#L1305'>1305</a>
<a name='L1306'></a><a href='#L1306'>1306</a>
<a name='L1307'></a><a href='#L1307'>1307</a>
<a name='L1308'></a><a href='#L1308'>1308</a>
<a name='L1309'></a><a href='#L1309'>1309</a>
<a name='L1310'></a><a href='#L1310'>1310</a>
<a name='L1311'></a><a href='#L1311'>1311</a>
<a name='L1312'></a><a href='#L1312'>1312</a>
<a name='L1313'></a><a href='#L1313'>1313</a>
<a name='L1314'></a><a href='#L1314'>1314</a>
<a name='L1315'></a><a href='#L1315'>1315</a>
<a name='L1316'></a><a href='#L1316'>1316</a>
<a name='L1317'></a><a href='#L1317'>1317</a>
<a name='L1318'></a><a href='#L1318'>1318</a>
<a name='L1319'></a><a href='#L1319'>1319</a>
<a name='L1320'></a><a href='#L1320'>1320</a>
<a name='L1321'></a><a href='#L1321'>1321</a>
<a name='L1322'></a><a href='#L1322'>1322</a>
<a name='L1323'></a><a href='#L1323'>1323</a>
<a name='L1324'></a><a href='#L1324'>1324</a>
<a name='L1325'></a><a href='#L1325'>1325</a>
<a name='L1326'></a><a href='#L1326'>1326</a>
<a name='L1327'></a><a href='#L1327'>1327</a>
<a name='L1328'></a><a href='#L1328'>1328</a>
<a name='L1329'></a><a href='#L1329'>1329</a>
<a name='L1330'></a><a href='#L1330'>1330</a>
<a name='L1331'></a><a href='#L1331'>1331</a>
<a name='L1332'></a><a href='#L1332'>1332</a>
<a name='L1333'></a><a href='#L1333'>1333</a>
<a name='L1334'></a><a href='#L1334'>1334</a>
<a name='L1335'></a><a href='#L1335'>1335</a>
<a name='L1336'></a><a href='#L1336'>1336</a>
<a name='L1337'></a><a href='#L1337'>1337</a>
<a name='L1338'></a><a href='#L1338'>1338</a>
<a name='L1339'></a><a href='#L1339'>1339</a>
<a name='L1340'></a><a href='#L1340'>1340</a>
<a name='L1341'></a><a href='#L1341'>1341</a>
<a name='L1342'></a><a href='#L1342'>1342</a>
<a name='L1343'></a><a href='#L1343'>1343</a>
<a name='L1344'></a><a href='#L1344'>1344</a>
<a name='L1345'></a><a href='#L1345'>1345</a>
<a name='L1346'></a><a href='#L1346'>1346</a>
<a name='L1347'></a><a href='#L1347'>1347</a>
<a name='L1348'></a><a href='#L1348'>1348</a>
<a name='L1349'></a><a href='#L1349'>1349</a>
<a name='L1350'></a><a href='#L1350'>1350</a>
<a name='L1351'></a><a href='#L1351'>1351</a>
<a name='L1352'></a><a href='#L1352'>1352</a>
<a name='L1353'></a><a href='#L1353'>1353</a>
<a name='L1354'></a><a href='#L1354'>1354</a>
<a name='L1355'></a><a href='#L1355'>1355</a>
<a name='L1356'></a><a href='#L1356'>1356</a>
<a name='L1357'></a><a href='#L1357'>1357</a>
<a name='L1358'></a><a href='#L1358'>1358</a>
<a name='L1359'></a><a href='#L1359'>1359</a>
<a name='L1360'></a><a href='#L1360'>1360</a>
<a name='L1361'></a><a href='#L1361'>1361</a>
<a name='L1362'></a><a href='#L1362'>1362</a>
<a name='L1363'></a><a href='#L1363'>1363</a>
<a name='L1364'></a><a href='#L1364'>1364</a>
<a name='L1365'></a><a href='#L1365'>1365</a>
<a name='L1366'></a><a href='#L1366'>1366</a>
<a name='L1367'></a><a href='#L1367'>1367</a>
<a name='L1368'></a><a href='#L1368'>1368</a>
<a name='L1369'></a><a href='#L1369'>1369</a>
<a name='L1370'></a><a href='#L1370'>1370</a>
<a name='L1371'></a><a href='#L1371'>1371</a>
<a name='L1372'></a><a href='#L1372'>1372</a>
<a name='L1373'></a><a href='#L1373'>1373</a>
<a name='L1374'></a><a href='#L1374'>1374</a>
<a name='L1375'></a><a href='#L1375'>1375</a>
<a name='L1376'></a><a href='#L1376'>1376</a>
<a name='L1377'></a><a href='#L1377'>1377</a>
<a name='L1378'></a><a href='#L1378'>1378</a>
<a name='L1379'></a><a href='#L1379'>1379</a>
<a name='L1380'></a><a href='#L1380'>1380</a>
<a name='L1381'></a><a href='#L1381'>1381</a>
<a name='L1382'></a><a href='#L1382'>1382</a>
<a name='L1383'></a><a href='#L1383'>1383</a>
<a name='L1384'></a><a href='#L1384'>1384</a>
<a name='L1385'></a><a href='#L1385'>1385</a>
<a name='L1386'></a><a href='#L1386'>1386</a>
<a name='L1387'></a><a href='#L1387'>1387</a>
<a name='L1388'></a><a href='#L1388'>1388</a>
<a name='L1389'></a><a href='#L1389'>1389</a>
<a name='L1390'></a><a href='#L1390'>1390</a>
<a name='L1391'></a><a href='#L1391'>1391</a>
<a name='L1392'></a><a href='#L1392'>1392</a>
<a name='L1393'></a><a href='#L1393'>1393</a>
<a name='L1394'></a><a href='#L1394'>1394</a>
<a name='L1395'></a><a href='#L1395'>1395</a>
<a name='L1396'></a><a href='#L1396'>1396</a>
<a name='L1397'></a><a href='#L1397'>1397</a>
<a name='L1398'></a><a href='#L1398'>1398</a>
<a name='L1399'></a><a href='#L1399'>1399</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * Shape Edit Service Implementation
 *
 * Provides functionality for editing existing shapes.
 * Implements the ElementEditService interface.
 * Includes property update functionality for consistent property updates.
 *
 * @module services/shapes/shape-actions
 */
&nbsp;
import type { ShapeRepository } from '@/core/state/ShapeRepository'
import type { ShapeElement } from '@/types/core'
import type { PatternDefinition } from '@/types/core/element/elementPatternTypes'
import type { ServiceResult } from '@/types/services/core/serviceResult'
import type { EventBus } from '@/types/services/events'
import type { AppEventMap } from '@/types/services/events/eventRegistry'
import type { LoggerService } from '@/types/services/logging'
import type {
  ElementEditResult,
} from '@/types/services/shapes'
import type { ElementEditService } from '@/types/services/shapes/shapeService'
&nbsp;
<span class="cstat-no" title="statement not covered" >import { v4 as uuidv4 } from 'uuid'</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >import { getService } from '@/services/core/registry'</span>
<span class="cstat-no" title="statement not covered" >import { getLoggerService } from '@/services/system/logging'</span>
<span class="cstat-no" title="statement not covered" >import { ServiceId } from '@/types/services/core/serviceIdentifier'</span>
<span class="cstat-no" title="statement not covered" >import { AppEventType } from '@/types/services/events'</span>
&nbsp;
// 本地类型定义以解决类型推断问题
interface LocalElementEditRequest {
  /** Element ID to edit */
  id: string
  /** New position */
  position?: { x: number, y: number }
  /** New style properties */
  style?: Record&lt;string, unknown&gt;
  /** Other property updates */
  properties?: Record&lt;string, unknown&gt;
  /** Whether to select the element after editing */
  selectAfterEdit?: boolean
  /** Major category for layer organization (e.g., 'floor', 'ceiling', 'furniture') */
  majorCategory?: string
  /** Minor category within the major category (e.g., 'coverings', 'lighting') */
  minorCategory?: string
  /** Z-level identifier for ordering within a layer */
  zLevelId?: string
}
&nbsp;
// Style properties that can exist at both top level and in properties
<span class="cstat-no" title="statement not covered" >export const STYLE_PROPERTIES = [</span>
<span class="cstat-no" title="statement not covered" >  'fill',</span>
<span class="cstat-no" title="statement not covered" >  'stroke',</span>
<span class="cstat-no" title="statement not covered" >  'strokeWidth', // 数值类型</span>
<span class="cstat-no" title="statement not covered" >  'opacity', // 数值类型</span>
<span class="cstat-no" title="statement not covered" >  'strokeDasharray',</span>
<span class="cstat-no" title="statement not covered" >  'strokeLinecap',</span>
<span class="cstat-no" title="statement not covered" >  'strokeLinejoin',</span>
<span class="cstat-no" title="statement not covered" >  'fontStyle', // 🔧 添加文本样式属性</span>
<span class="cstat-no" title="statement not covered" >  'fontWeight', // 🔧 添加文本粗细属性</span>
<span class="cstat-no" title="statement not covered" >  'fontSize', // 🔧 添加文本大小属性</span>
<span class="cstat-no" title="statement not covered" >  'fontFamily', // 🔧 添加文本字体属性</span>
<span class="cstat-no" title="statement not covered" >  'textAlign', // 🔧 添加文本对齐属性</span>
<span class="cstat-no" title="statement not covered" >] as const</span>
&nbsp;
// Numeric properties that need special handling
<span class="cstat-no" title="statement not covered" >export const NUMERIC_PROPERTIES = [</span>
<span class="cstat-no" title="statement not covered" >  'width',</span>
<span class="cstat-no" title="statement not covered" >  'height',</span>
<span class="cstat-no" title="statement not covered" >  'rotation',</span>
<span class="cstat-no" title="statement not covered" >  'cornerRadius',</span>
<span class="cstat-no" title="statement not covered" >  'costUnitPrice',</span>
<span class="cstat-no" title="statement not covered" >  'costMultiplierOrCount',</span>
<span class="cstat-no" title="statement not covered" >  'strokeWidth', // 添加到数值属性列表</span>
<span class="cstat-no" title="statement not covered" >  'opacity', // 添加到数值属性列表</span>
<span class="cstat-no" title="statement not covered" >  'creationRadius', // 添加多边形创建半径属性</span>
<span class="cstat-no" title="statement not covered" >  'radius', // 添加圆形半径属性</span>
<span class="cstat-no" title="statement not covered" >  'radiusX', // 🔧 添加椭圆X半径属性</span>
<span class="cstat-no" title="statement not covered" >  'radiusY', // 🔧 添加椭圆Y半径属性</span>
<span class="cstat-no" title="statement not covered" >  'startAngle', // 🔧 添加圆弧起始角度属性</span>
<span class="cstat-no" title="statement not covered" >  'endAngle', // 🔧 添加圆弧结束角度属性</span>
<span class="cstat-no" title="statement not covered" >  'fontSize', // 🔧 添加文本大小属性（数值类型）</span>
<span class="cstat-no" title="statement not covered" >] as const</span>
&nbsp;
// Cost-related properties
<span class="cstat-no" title="statement not covered" >export const COST_PROPERTIES = [</span>
<span class="cstat-no" title="statement not covered" >  'costUnitPrice',</span>
<span class="cstat-no" title="statement not covered" >  'costMultiplierOrCount',</span>
<span class="cstat-no" title="statement not covered" >  'costBasis',</span>
<span class="cstat-no" title="statement not covered" >  'costTotal',</span>
<span class="cstat-no" title="statement not covered" >  'computeCostEnabled', // 🔧 添加成本计算开关属性</span>
<span class="cstat-no" title="statement not covered" >] as const</span>
&nbsp;
// Properties that exist at both top level and in properties
<span class="cstat-no" title="statement not covered" >export const DUAL_LEVEL_PROPERTIES = [</span>
<span class="cstat-no" title="statement not covered" >  ...STYLE_PROPERTIES,</span>
<span class="cstat-no" title="statement not covered" >  ...NUMERIC_PROPERTIES,</span>
<span class="cstat-no" title="statement not covered" >  'majorCategory',</span>
<span class="cstat-no" title="statement not covered" >  'minorCategory',</span>
<span class="cstat-no" title="statement not covered" >  'zLevelId',</span>
<span class="cstat-no" title="statement not covered" >  'name',</span>
<span class="cstat-no" title="statement not covered" >] as const</span>
&nbsp;
export type StyleProperty = typeof STYLE_PROPERTIES[number]
export type NumericProperty = typeof NUMERIC_PROPERTIES[number]
export type CostProperty = typeof COST_PROPERTIES[number]
export type DualLevelProperty = typeof DUAL_LEVEL_PROPERTIES[number]
&nbsp;
/**
 * Creates a property update patch for a single element.
 *
 * @remarks
 * This function handles various property types including nested properties,
 * position coordinates, numeric values, and style properties. It ensures
 * proper validation and maintains consistency between top-level and nested
 * property values.
 *
 * @param element - The element to create a patch for
 * @param propertyPath - The path to the property to update (e.g., 'width', 'position.x', 'properties.fill')
 * @param value - The new value for the property
 * @returns A patch object that can be used to update the element, or null if the update is invalid
 *
 * @example
 * ```typescript
 * const patch = createPropertyPatch(element, 'width', 100);
 * if (patch) {
 *   // Apply patch to element
 * }
 * ```
 */
<span class="cstat-no" title="statement not covered" >export function createPropertyPatch(</span>
<span class="cstat-no" title="statement not covered" >  element: ShapeElement,</span>
<span class="cstat-no" title="statement not covered" >  propertyPath: string,</span>
<span class="cstat-no" title="statement not covered" >  value: unknown,</span>
<span class="cstat-no" title="statement not covered" >): { id: string, properties: Record&lt;string, unknown&gt;, [key: string]: unknown } | null {</span>
<span class="cstat-no" title="statement not covered" >  const logger = getLoggerService()</span>
&nbsp;
  // Special case for pattern property - handle it first before initializing patch
<span class="cstat-no" title="statement not covered" >  if (propertyPath === 'pattern') {</span>
<span class="cstat-no" title="statement not covered" >    logger.debug('[createPropertyPatch] Processing pattern property for element:', element.id)</span>
<span class="cstat-no" title="statement not covered" >    logger.debug('[createPropertyPatch] Pattern value:', JSON.stringify(value, null, 2))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const isValid = isValidPatternValue(value)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (isValid) {</span>
<span class="cstat-no" title="statement not covered" >      if (value === undefined) {</span>
        // 使用特殊标记来表示删除操作，因为JSON.stringify会过滤掉undefined值
<span class="cstat-no" title="statement not covered" >        const patch: { id: string, properties: Record&lt;string, unknown&gt;, [key: string]: unknown } = {</span>
<span class="cstat-no" title="statement not covered" >          id: element.id,</span>
<span class="cstat-no" title="statement not covered" >          properties: { ...(element.properties || {}) },</span>
<span class="cstat-no" title="statement not covered" >          __deletePattern: true, // 特殊标记表示要删除pattern属性</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        logger.debug('[createPropertyPatch] Pattern set to undefined for deletion')</span>
<span class="cstat-no" title="statement not covered" >        return patch</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
        // 正常情况下包含pattern
<span class="cstat-no" title="statement not covered" >        const patch: { id: string, properties: Record&lt;string, unknown&gt;, [key: string]: unknown } = {</span>
<span class="cstat-no" title="statement not covered" >          id: element.id,</span>
<span class="cstat-no" title="statement not covered" >          properties: { ...(element.properties || {}) },</span>
<span class="cstat-no" title="statement not covered" >          pattern: value as PatternDefinition,</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        logger.debug('[createPropertyPatch] Pattern validation passed, patch created:', JSON.stringify(patch, null, 2))</span>
<span class="cstat-no" title="statement not covered" >        return patch</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    logger.warn('[createPropertyPatch] Pattern validation failed for element:', element.id, 'value:', JSON.stringify(value, null, 2))</span>
<span class="cstat-no" title="statement not covered" >    return null</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Initialize patch with element ID and properties for non-pattern properties
<span class="cstat-no" title="statement not covered" >  const patch: { id: string, properties: Record&lt;string, unknown&gt;, [key: string]: unknown } = {</span>
<span class="cstat-no" title="statement not covered" >    id: element.id,</span>
<span class="cstat-no" title="statement not covered" >    properties: { ...(element.properties || {}) },</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Handle properties.* paths (nested properties)
<span class="cstat-no" title="statement not covered" >  if (propertyPath.startsWith('properties.')) {</span>
<span class="cstat-no" title="statement not covered" >    const parts = propertyPath.split('.')</span>
<span class="cstat-no" title="statement not covered" >    if (parts.length &lt; 2) {</span>
<span class="cstat-no" title="statement not covered" >      return null</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // Remove 'properties.' prefix
<span class="cstat-no" title="statement not covered" >    const propertyKey = parts.slice(1).join('.')</span>
<span class="cstat-no" title="statement not covered" >    logger.debug(`[PropertyUpdateService] Processing properties.* path: ${propertyPath} -&gt; propertyKey: ${propertyKey}`)</span>
<span class="cstat-no" title="statement not covered" >    return updateNestedProperty(element, patch, propertyKey, value)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Handle position.x and position.y
<span class="cstat-no" title="statement not covered" >  if (propertyPath === 'position.x' || propertyPath === 'position.y') {</span>
<span class="cstat-no" title="statement not covered" >    return updatePositionProperty(element, patch, propertyPath, value)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // 特殊处理元数据名称属性 (metadata.name)
<span class="cstat-no" title="statement not covered" >  if (propertyPath === 'metadata.name') {</span>
    // 确保 metadata 对象存在
<span class="cstat-no" title="statement not covered" >    if (typeof patch.metadata === 'undefined') {</span>
<span class="cstat-no" title="statement not covered" >      patch.metadata = {}</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    (patch.metadata as any).name = value</span>
&nbsp;
    // 同时更新 properties.metadata.name
<span class="cstat-no" title="statement not covered" >    if (typeof patch.properties === 'undefined') {</span>
<span class="cstat-no" title="statement not covered" >      patch.properties = {}</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    if (typeof patch.properties.metadata === 'undefined') {</span>
<span class="cstat-no" title="statement not covered" >      patch.properties.metadata = {}</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    (patch.properties.metadata as any).name = value</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    logger.debug(`[PropertyUpdateService] Updated metadata.name to: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >    return patch</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // 特殊处理名称属性 (name)
<span class="cstat-no" title="statement not covered" >  if (propertyPath === 'name') {</span>
    // 更新顶层 name 属性
<span class="cstat-no" title="statement not covered" >    patch.name = value</span>
&nbsp;
    // 同时更新 properties.name
<span class="cstat-no" title="statement not covered" >    if (typeof patch.properties === 'undefined') {</span>
<span class="cstat-no" title="statement not covered" >      patch.properties = {}</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    patch.properties.name = value</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    logger.debug(`[PropertyUpdateService] Updated name to: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >    return patch</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // 特殊处理图层属性 (majorCategory, minorCategory, zLevelId)
<span class="cstat-no" title="statement not covered" >  if (propertyPath === 'majorCategory' || propertyPath === 'minorCategory' || propertyPath === 'zLevelId') {</span>
    // 同时更新顶层和properties中的图层属性
<span class="cstat-no" title="statement not covered" >    patch[propertyPath] = value</span>
<span class="cstat-no" title="statement not covered" >    if (typeof patch.properties === 'undefined') {</span>
<span class="cstat-no" title="statement not covered" >      patch.properties = {}</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    patch.properties[propertyPath] = value</span>
&nbsp;
    // 添加更多日志
<span class="cstat-no" title="statement not covered" >    logger.debug(`[PropertyUpdateService] Updated layer property ${propertyPath} to: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >    logger.debug(`[PropertyUpdateService] Element before update: ${element.id}`)</span>
<span class="cstat-no" title="statement not covered" >    logger.debug(`[PropertyUpdateService] Patch created for ${propertyPath}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return patch</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Handle direct properties (width, height, etc.) - only for non-nested paths
<span class="cstat-no" title="statement not covered" >  if (!propertyPath.includes('.') &amp;&amp; NUMERIC_PROPERTIES.includes(propertyPath as NumericProperty)) {</span>
<span class="cstat-no" title="statement not covered" >    return updateNumericProperty(element, patch, propertyPath, value)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Handle cost properties - only for non-nested paths
<span class="cstat-no" title="statement not covered" >  if (!propertyPath.includes('.') &amp;&amp; COST_PROPERTIES.includes(propertyPath as CostProperty)) {</span>
<span class="cstat-no" title="statement not covered" >    return updateCostProperty(element, patch, propertyPath, value)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // 特殊处理 strokeWidth 和 opacity
<span class="cstat-no" title="statement not covered" >  if (propertyPath === 'strokeWidth' || propertyPath === 'opacity') {</span>
<span class="cstat-no" title="statement not covered" >    if (typeof value === 'number') {</span>
<span class="cstat-no" title="statement not covered" >      patch[propertyPath] = value</span>
<span class="cstat-no" title="statement not covered" >      if (typeof patch.properties === 'undefined') {</span>
<span class="cstat-no" title="statement not covered" >        patch.properties = {}</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      patch.properties[propertyPath] = value</span>
<span class="cstat-no" title="statement not covered" >      return patch</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else if (typeof value === 'string') {</span>
<span class="cstat-no" title="statement not covered" >      const numValue = Number.parseFloat(value)</span>
<span class="cstat-no" title="statement not covered" >      if (!Number.isNaN(numValue)) {</span>
<span class="cstat-no" title="statement not covered" >        patch[propertyPath] = numValue</span>
<span class="cstat-no" title="statement not covered" >        if (typeof patch.properties === 'undefined') {</span>
<span class="cstat-no" title="statement not covered" >          patch.properties = {}</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        patch.properties[propertyPath] = numValue</span>
<span class="cstat-no" title="statement not covered" >        return patch</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    logger.warn(`[PropertyUpdateService] Invalid value for ${propertyPath}: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >    return null</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // 处理其他样式属性
<span class="cstat-no" title="statement not covered" >  if (STYLE_PROPERTIES.includes(propertyPath as StyleProperty)) {</span>
<span class="cstat-no" title="statement not covered" >    return updateStyleProperty(element, patch, propertyPath, value)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Handle other top-level properties
<span class="cstat-no" title="statement not covered" >  if (propertyPath in element) {</span>
<span class="cstat-no" title="statement not covered" >    patch[propertyPath] = value</span>
<span class="cstat-no" title="statement not covered" >    patch.properties[propertyPath] = value</span>
<span class="cstat-no" title="statement not covered" >    return patch</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // For any other property, just update it in properties
<span class="cstat-no" title="statement not covered" >  patch.properties[propertyPath] = value</span>
<span class="cstat-no" title="statement not covered" >  return patch</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Validates a pattern value to ensure it meets the required structure.
 *
 * @param value - The value to validate as a pattern
 * @returns True if the value is a valid pattern or undefined, false otherwise
 */
<span class="cstat-no" title="statement not covered" >function isValidPatternValue(value: unknown): boolean {</span>
<span class="cstat-no" title="statement not covered" >  const logger = getLoggerService()</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  logger.debug('[isValidPatternValue] Validating pattern value:', JSON.stringify(value, null, 2))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (value === undefined) {</span>
<span class="cstat-no" title="statement not covered" >    logger.debug('[isValidPatternValue] Pattern is undefined - valid for removal')</span>
<span class="cstat-no" title="statement not covered" >    return true</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  if (value === null) {</span>
<span class="cstat-no" title="statement not covered" >    logger.debug('[isValidPatternValue] Pattern is null - invalid')</span>
<span class="cstat-no" title="statement not covered" >    return false</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (typeof value === 'object' &amp;&amp; !Array.isArray(value)) {</span>
<span class="cstat-no" title="statement not covered" >    const pattern = value as Partial&lt;PatternDefinition&gt;</span>
<span class="cstat-no" title="statement not covered" >    const hasId = typeof pattern.id === 'string'</span>
<span class="cstat-no" title="statement not covered" >    const hasType = typeof pattern.type === 'string'</span>
<span class="cstat-no" title="statement not covered" >    const hasTextureType = typeof pattern.textureType === 'string'</span>
&nbsp;
    // Check if type values are valid
<span class="cstat-no" title="statement not covered" >    const validTypes = ['lines', 'circles', 'paths', 'texture-lines', 'texture-circles', 'texture-paths']</span>
<span class="cstat-no" title="statement not covered" >    const typeIsValid = hasType &amp;&amp; validTypes.includes(pattern.type!)</span>
<span class="cstat-no" title="statement not covered" >    const textureTypeIsValid = hasTextureType &amp;&amp; validTypes.includes(pattern.textureType!)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    logger.debug('[isValidPatternValue] Pattern validation details:', {</span>
<span class="cstat-no" title="statement not covered" >      hasId,</span>
<span class="cstat-no" title="statement not covered" >      hasType,</span>
<span class="cstat-no" title="statement not covered" >      hasTextureType,</span>
<span class="cstat-no" title="statement not covered" >      typeIsValid,</span>
<span class="cstat-no" title="statement not covered" >      textureTypeIsValid,</span>
<span class="cstat-no" title="statement not covered" >      id: pattern.id,</span>
<span class="cstat-no" title="statement not covered" >      type: pattern.type,</span>
<span class="cstat-no" title="statement not covered" >      textureType: pattern.textureType,</span>
<span class="cstat-no" title="statement not covered" >    })</span>
&nbsp;
    // Accept pattern if it has id and either a valid type or valid textureType
<span class="cstat-no" title="statement not covered" >    const isValid = hasId &amp;&amp; (typeIsValid || textureTypeIsValid)</span>
<span class="cstat-no" title="statement not covered" >    logger.debug('[isValidPatternValue] Pattern validation result:', isValid)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return isValid</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  logger.debug('[isValidPatternValue] Pattern is not a valid object - invalid')</span>
<span class="cstat-no" title="statement not covered" >  return false</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Updates a nested property in the patch
 */
<span class="cstat-no" title="statement not covered" >function updateNestedProperty(</span>
<span class="cstat-no" title="statement not covered" >  element: ShapeElement,</span>
<span class="cstat-no" title="statement not covered" >  patch: { id: string, properties: Record&lt;string, unknown&gt;, [key: string]: unknown },</span>
<span class="cstat-no" title="statement not covered" >  propertyKey: string,</span>
<span class="cstat-no" title="statement not covered" >  value: unknown,</span>
<span class="cstat-no" title="statement not covered" >): { id: string, properties: Record&lt;string, unknown&gt;, [key: string]: unknown } | null {</span>
<span class="cstat-no" title="statement not covered" >  const logger = getLoggerService()</span>
<span class="cstat-no" title="statement not covered" >  logger.debug(`[PropertyUpdateService] updateNestedProperty called with propertyKey: ${propertyKey}, value: ${String(value)}`)</span>
&nbsp;
  // Handle nested properties (e.g., controlPoint.x)
<span class="cstat-no" title="statement not covered" >  if (propertyKey.includes('.')) {</span>
<span class="cstat-no" title="statement not covered" >    const parts = propertyKey.split('.')</span>
<span class="cstat-no" title="statement not covered" >    let current = patch.properties</span>
&nbsp;
    // Navigate to the correct nesting level
<span class="cstat-no" title="statement not covered" >    for (let i = 0; i &lt; parts.length - 1; i++) {</span>
<span class="cstat-no" title="statement not covered" >      const part = parts[i]</span>
<span class="cstat-no" title="statement not covered" >      if (current[part] === undefined || current[part] === null || typeof current[part] !== 'object') {</span>
<span class="cstat-no" title="statement not covered" >        current[part] = {}</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      current = current[part] as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const finalKey = parts[parts.length - 1]</span>
&nbsp;
    // Handle numeric properties specially
<span class="cstat-no" title="statement not covered" >    if (NUMERIC_PROPERTIES.includes(finalKey as NumericProperty)) {</span>
<span class="cstat-no" title="statement not covered" >      const numValue = parseNumericValue(value)</span>
<span class="cstat-no" title="statement not covered" >      if (numValue !== null) {</span>
<span class="cstat-no" title="statement not covered" >        current[finalKey] = numValue</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (value === '' || value === null || value === undefined) {</span>
<span class="cstat-no" title="statement not covered" >        current[finalKey] = getDefaultValueForNumericProperty(finalKey as NumericProperty)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
<span class="cstat-no" title="statement not covered" >        logger.warn(`[PropertyUpdateService] Invalid numeric value for ${propertyKey}: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >        return null</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
      // For non-numeric properties, just set the value
<span class="cstat-no" title="statement not covered" >      current[finalKey] = value</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return patch</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Handle direct properties in properties object
  // 特殊处理多边形的creationRadius/radius更新，需要重新生成点
<span class="cstat-no" title="statement not covered" >  if (propertyKey === 'creationRadius' || propertyKey === 'radius') {</span>
<span class="cstat-no" title="statement not covered" >    const numValue = parseNumericValue(value)</span>
<span class="cstat-no" title="statement not covered" >    logger.debug(`[PropertyUpdateService] Processing ${propertyKey} update: value=${String(value)}, numValue=${String(numValue)}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (numValue !== null &amp;&amp; numValue &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      patch.properties[propertyKey] = numValue</span>
&nbsp;
      // 对于多边形，同时更新 radius 和 creationRadius 以保持一致性
<span class="cstat-no" title="statement not covered" >      if (propertyKey === 'radius') {</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.creationRadius = numValue</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (propertyKey === 'creationRadius') {</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.radius = numValue</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // 检查是否是多边形类型
<span class="cstat-no" title="statement not covered" >      const isPolygonType = [</span>
<span class="cstat-no" title="statement not covered" >        'POLYGON',</span>
<span class="cstat-no" title="statement not covered" >        'TRIANGLE',</span>
<span class="cstat-no" title="statement not covered" >        'QUADRILATERAL',</span>
<span class="cstat-no" title="statement not covered" >        'PENTAGON',</span>
<span class="cstat-no" title="statement not covered" >        'HEXAGON',</span>
<span class="cstat-no" title="statement not covered" >        'polygon',</span>
<span class="cstat-no" title="statement not covered" >        'triangle',</span>
<span class="cstat-no" title="statement not covered" >        'quadrilateral',</span>
<span class="cstat-no" title="statement not covered" >        'pentagon',</span>
<span class="cstat-no" title="statement not covered" >        'hexagon',</span>
<span class="cstat-no" title="statement not covered" >      ].includes(element.type)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      logger.debug(`[PropertyUpdateService] Element type: ${element.type}, isPolygonType: ${isPolygonType}`)</span>
<span class="cstat-no" title="statement not covered" >      logger.debug(`[PropertyUpdateService] Element properties: sides=${String(element.properties?.sides)}, isRegular=${String(element.properties?.isRegular)}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      logger.debug(`[PropertyUpdateService] Checking polygon regeneration conditions: willRegenerate=${isPolygonType &amp;&amp; Boolean(element.properties?.sides) &amp;&amp; Boolean(element.properties?.isRegular)}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (isPolygonType &amp;&amp; element.properties?.sides !== undefined &amp;&amp; element.properties?.isRegular !== undefined) {</span>
        // 重新生成多边形的点
<span class="cstat-no" title="statement not covered" >        const sides = Number(element.properties.sides)</span>
<span class="cstat-no" title="statement not covered" >        const radius = numValue</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        logger.debug(`[PropertyUpdateService] Regenerating polygon points: sides=${sides}, radius=${radius}, isRegular=${String(element.properties.isRegular)}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (sides &gt;= 3) {</span>
          // 生成相对于原点(0,0)的顶点坐标
<span class="cstat-no" title="statement not covered" >          const newPoints = []</span>
<span class="cstat-no" title="statement not covered" >          const angleStep = (2 * Math.PI) / sides</span>
<span class="cstat-no" title="statement not covered" >          const startAngle = -Math.PI / 2 // 默认起始角度，顶点向上</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          for (let i = 0; i &lt; sides; i++) {</span>
<span class="cstat-no" title="statement not covered" >            const angle = startAngle + i * angleStep</span>
<span class="cstat-no" title="statement not covered" >            const x = radius * Math.cos(angle)</span>
<span class="cstat-no" title="statement not covered" >            const y = radius * Math.sin(angle)</span>
<span class="cstat-no" title="statement not covered" >            newPoints.push({ x, y, z: 0 })</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
          // 计算几何中心并调整点，确保几何中心在原点
<span class="cstat-no" title="statement not covered" >          let sumX = 0</span>
<span class="cstat-no" title="statement not covered" >          let sumY = 0</span>
<span class="cstat-no" title="statement not covered" >          for (const point of newPoints) {</span>
<span class="cstat-no" title="statement not covered" >            sumX += point.x</span>
<span class="cstat-no" title="statement not covered" >            sumY += point.y</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          const centroidX = sumX / sides</span>
<span class="cstat-no" title="statement not covered" >          const centroidY = sumY / sides</span>
&nbsp;
          // 调整点坐标
<span class="cstat-no" title="statement not covered" >          const adjustedPoints = newPoints.map(p =&gt; ({</span>
<span class="cstat-no" title="statement not covered" >            x: p.x - centroidX,</span>
<span class="cstat-no" title="statement not covered" >            y: p.y - centroidY,</span>
<span class="cstat-no" title="statement not covered" >            z: p.z,</span>
<span class="cstat-no" title="statement not covered" >          }))</span>
&nbsp;
          // 更新points属性
<span class="cstat-no" title="statement not covered" >          patch.properties.points = adjustedPoints</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          logger.debug(`[PropertyUpdateService] Successfully regenerated polygon points: radius=${radius}, sides=${sides}, newPointsCount=${adjustedPoints.length}`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        else {</span>
<span class="cstat-no" title="statement not covered" >          logger.warn(`[PropertyUpdateService] Invalid sides count for polygon: ${sides}`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
<span class="cstat-no" title="statement not covered" >        logger.debug(`[PropertyUpdateService] Skipping point regeneration: isPolygonType=${isPolygonType}`)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // Also update top-level property if it exists there
<span class="cstat-no" title="statement not covered" >      if (propertyKey in element) {</span>
<span class="cstat-no" title="statement not covered" >        patch[propertyKey] = numValue</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
<span class="cstat-no" title="statement not covered" >      logger.warn(`[PropertyUpdateService] Invalid creationRadius/radius value: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >      return null</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else if (NUMERIC_PROPERTIES.includes(propertyKey as NumericProperty)) {</span>
<span class="cstat-no" title="statement not covered" >    const numValue = parseNumericValue(value)</span>
<span class="cstat-no" title="statement not covered" >    if (numValue !== null) {</span>
<span class="cstat-no" title="statement not covered" >      patch.properties[propertyKey] = numValue</span>
      // Also update top-level property if it exists there
<span class="cstat-no" title="statement not covered" >      if (propertyKey in element) {</span>
<span class="cstat-no" title="statement not covered" >        patch[propertyKey] = numValue</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else if (value === '' || value === null || value === undefined) {</span>
<span class="cstat-no" title="statement not covered" >      patch.properties[propertyKey] = getDefaultValueForNumericProperty(propertyKey as NumericProperty)</span>
<span class="cstat-no" title="statement not covered" >      if (propertyKey in element) {</span>
<span class="cstat-no" title="statement not covered" >        patch[propertyKey] = getDefaultValueForNumericProperty(propertyKey as NumericProperty)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
<span class="cstat-no" title="statement not covered" >      logger.warn(`[PropertyUpdateService] Invalid numeric value for ${propertyKey}: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >      return null</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else if (COST_PROPERTIES.includes(propertyKey as CostProperty)) {</span>
<span class="cstat-no" title="statement not covered" >    if (propertyKey === 'costBasis') {</span>
<span class="cstat-no" title="statement not covered" >      patch.properties[propertyKey] = value</span>
      // 同时更新顶层属性（如果存在）
<span class="cstat-no" title="statement not covered" >      if (propertyKey in element) {</span>
<span class="cstat-no" title="statement not covered" >        patch[propertyKey] = value</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
<span class="cstat-no" title="statement not covered" >      const numValue = parseNumericValue(value)</span>
<span class="cstat-no" title="statement not covered" >      if (numValue !== null) {</span>
<span class="cstat-no" title="statement not covered" >        patch.properties[propertyKey] = numValue</span>
        // 同时更新顶层属性（如果存在）
<span class="cstat-no" title="statement not covered" >        if (propertyKey in element) {</span>
<span class="cstat-no" title="statement not covered" >          patch[propertyKey] = numValue</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (value === '' || value === null || value === undefined) {</span>
<span class="cstat-no" title="statement not covered" >        const defaultValue = getDefaultValueForCostProperty(propertyKey as CostProperty)</span>
<span class="cstat-no" title="statement not covered" >        patch.properties[propertyKey] = defaultValue</span>
        // 同时更新顶层属性（如果存在）
<span class="cstat-no" title="statement not covered" >        if (propertyKey in element) {</span>
<span class="cstat-no" title="statement not covered" >          patch[propertyKey] = defaultValue</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
<span class="cstat-no" title="statement not covered" >        logger.warn(`[PropertyUpdateService] Invalid numeric value for ${propertyKey}: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >        return null</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else if (propertyKey === 'majorCategory' || propertyKey === 'minorCategory' || propertyKey === 'zLevelId') {</span>
    // 特殊处理图层属性，确保同时更新顶层和properties中的值
<span class="cstat-no" title="statement not covered" >    patch.properties[propertyKey] = value</span>
<span class="cstat-no" title="statement not covered" >    patch[propertyKey] = value</span>
<span class="cstat-no" title="statement not covered" >    logger.debug(`[PropertyUpdateService] Updated nested layer property ${propertyKey} to: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else if (STYLE_PROPERTIES.includes(propertyKey as StyleProperty)) {</span>
<span class="cstat-no" title="statement not covered" >    patch.properties[propertyKey] = value</span>
    // Also update top-level property if it exists there
<span class="cstat-no" title="statement not covered" >    if (propertyKey in element) {</span>
<span class="cstat-no" title="statement not covered" >      patch[propertyKey] = value</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else if (propertyKey === 'start' || propertyKey === 'end') {</span>
    // 特殊处理LINE元素的start和end属性
    // properties中存储相对坐标，顶层存储绝对坐标
<span class="cstat-no" title="statement not covered" >    patch.properties[propertyKey] = value</span>
&nbsp;
    // 计算绝对坐标：position + 相对坐标
<span class="cstat-no" title="statement not covered" >    const position = element.position || { x: 0, y: 0, z: 0 }</span>
<span class="cstat-no" title="statement not covered" >    const relativePoint = value as { x: number, y: number, z?: number }</span>
<span class="cstat-no" title="statement not covered" >    const absolutePoint = {</span>
<span class="cstat-no" title="statement not covered" >      x: position.x + relativePoint.x,</span>
<span class="cstat-no" title="statement not covered" >      y: position.y + relativePoint.y,</span>
<span class="cstat-no" title="statement not covered" >      z: (position.z || 0) + (relativePoint.z || 0),</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    patch[propertyKey] = absolutePoint</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    logger.debug(`[PropertyUpdateService] Updated LINE ${propertyKey}:`, {</span>
<span class="cstat-no" title="statement not covered" >      relative: value,</span>
<span class="cstat-no" title="statement not covered" >      position,</span>
<span class="cstat-no" title="statement not covered" >      absolute: absolutePoint,</span>
<span class="cstat-no" title="statement not covered" >    })</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else {</span>
    // For any other property, just set it
<span class="cstat-no" title="statement not covered" >    patch.properties[propertyKey] = value</span>
    // Also update top-level property if it exists there
<span class="cstat-no" title="statement not covered" >    if (propertyKey in element) {</span>
<span class="cstat-no" title="statement not covered" >      patch[propertyKey] = value</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return patch</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Updates a position property in the patch
 */
<span class="cstat-no" title="statement not covered" >function updatePositionProperty(</span>
<span class="cstat-no" title="statement not covered" >  element: ShapeElement,</span>
<span class="cstat-no" title="statement not covered" >  patch: { id: string, properties: Record&lt;string, unknown&gt;, [key: string]: unknown },</span>
<span class="cstat-no" title="statement not covered" >  propertyPath: string,</span>
<span class="cstat-no" title="statement not covered" >  value: unknown,</span>
<span class="cstat-no" title="statement not covered" >): { id: string, properties: Record&lt;string, unknown&gt;, [key: string]: unknown } | null {</span>
<span class="cstat-no" title="statement not covered" >  const logger = getLoggerService()</span>
<span class="cstat-no" title="statement not covered" >  const key = propertyPath.split('.')[1] as 'x' | 'y'</span>
<span class="cstat-no" title="statement not covered" >  const prevPosition = element.position ?? { x: 0, y: 0 }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const numValue = parseNumericValue(value)</span>
<span class="cstat-no" title="statement not covered" >  if (numValue !== null) {</span>
<span class="cstat-no" title="statement not covered" >    const newPosition = { ...prevPosition, [key]: numValue }</span>
<span class="cstat-no" title="statement not covered" >    patch.position = newPosition</span>
<span class="cstat-no" title="statement not covered" >    patch.properties.position = newPosition</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else if (value === '' || value === null || value === undefined) {</span>
    // Keep existing position for empty values
<span class="cstat-no" title="statement not covered" >    patch.position = prevPosition</span>
<span class="cstat-no" title="statement not covered" >    patch.properties.position = prevPosition</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else {</span>
<span class="cstat-no" title="statement not covered" >    logger.warn(`[PropertyUpdateService] Invalid value for position.${key}: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >    return null</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return patch</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Updates a numeric property in the patch
 */
<span class="cstat-no" title="statement not covered" >function updateNumericProperty(</span>
<span class="cstat-no" title="statement not covered" >  element: ShapeElement,</span>
<span class="cstat-no" title="statement not covered" >  patch: { id: string, properties: Record&lt;string, unknown&gt;, [key: string]: unknown },</span>
<span class="cstat-no" title="statement not covered" >  propertyPath: string,</span>
<span class="cstat-no" title="statement not covered" >  value: unknown,</span>
<span class="cstat-no" title="statement not covered" >): { id: string, properties: Record&lt;string, unknown&gt;, [key: string]: unknown } | null {</span>
<span class="cstat-no" title="statement not covered" >  const logger = getLoggerService()</span>
<span class="cstat-no" title="statement not covered" >  const key = propertyPath as NumericProperty</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const numValue = parseNumericValue(value)</span>
<span class="cstat-no" title="statement not covered" >  if (numValue !== null) {</span>
<span class="cstat-no" title="statement not covered" >    if (key in element) {</span>
<span class="cstat-no" title="statement not covered" >      patch[key] = numValue</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    patch.properties[key] = numValue</span>
&nbsp;
    // Special handling for squares: keep width and height synchronized
<span class="cstat-no" title="statement not covered" >    if (element.type === 'SQUARE' &amp;&amp; (key === 'width' || key === 'height')) {</span>
<span class="cstat-no" title="statement not covered" >      logger.debug(`[PropertyUpdateService] Square ${key} updated to ${numValue}, synchronizing other dimension`)</span>
<span class="cstat-no" title="statement not covered" >      const otherDimension = key === 'width' ? 'height' : 'width'</span>
&nbsp;
      // Update the other dimension to the same value
<span class="cstat-no" title="statement not covered" >      if (otherDimension in element) {</span>
<span class="cstat-no" title="statement not covered" >        patch[otherDimension] = numValue</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      patch.properties[otherDimension] = numValue</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      logger.debug(`[PropertyUpdateService] Square dimensions synchronized: ${key}=${numValue}, ${otherDimension}=${numValue}`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // Special handling for ellipses: keep radiusX/radiusY synchronized with width/height
<span class="cstat-no" title="statement not covered" >    if (element.type === 'ELLIPSE') {</span>
      // 🔧 修复：从store获取最新的元素状态，确保获取到最新的半径值
<span class="cstat-no" title="statement not covered" >      let currentElement = element</span>
<span class="cstat-no" title="statement not covered" >      if (typeof window !== 'undefined' &amp;&amp; (window as any).__ZUSTAND_SHAPES_STORE__) {</span>
<span class="cstat-no" title="statement not covered" >        const shapesStore = (window as any).__ZUSTAND_SHAPES_STORE__</span>
<span class="cstat-no" title="statement not covered" >        const state = shapesStore.getState()</span>
<span class="cstat-no" title="statement not covered" >        const storeElement = state.shapes.find((s: any) =&gt; s.id === element.id)</span>
<span class="cstat-no" title="statement not covered" >        if (storeElement) {</span>
<span class="cstat-no" title="statement not covered" >          currentElement = storeElement</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (key === 'radiusX') {</span>
<span class="cstat-no" title="statement not covered" >        const width = numValue * 2</span>
<span class="cstat-no" title="statement not covered" >        patch.width = width</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.width = width</span>
        // 🔧 修复：从最新的元素状态获取radiusY值
<span class="cstat-no" title="statement not covered" >        const currentRadiusY = currentElement.properties?.radiusY ?? (currentElement as any).radiusY ?? 80</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.radiusY = currentRadiusY</span>
<span class="cstat-no" title="statement not covered" >        patch.radiusY = currentRadiusY</span>
<span class="cstat-no" title="statement not covered" >        patch.height = currentRadiusY * 2</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.height = currentRadiusY * 2</span>
<span class="cstat-no" title="statement not covered" >        logger.debug(`[PropertyUpdateService] Ellipse radiusX updated to ${numValue}, synchronized width to ${width}, preserved radiusY: ${currentRadiusY}`)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (key === 'radiusY') {</span>
<span class="cstat-no" title="statement not covered" >        const height = numValue * 2</span>
<span class="cstat-no" title="statement not covered" >        patch.height = height</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.height = height</span>
        // 🔧 修复：从最新的元素状态获取radiusX值
<span class="cstat-no" title="statement not covered" >        const currentRadiusX = currentElement.properties?.radiusX ?? (currentElement as any).radiusX ?? 120</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.radiusX = currentRadiusX</span>
<span class="cstat-no" title="statement not covered" >        patch.radiusX = currentRadiusX</span>
<span class="cstat-no" title="statement not covered" >        patch.width = currentRadiusX * 2</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.width = currentRadiusX * 2</span>
<span class="cstat-no" title="statement not covered" >        logger.debug(`[PropertyUpdateService] Ellipse radiusY updated to ${numValue}, synchronized height to ${height}, preserved radiusX: ${currentRadiusX}`)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (key === 'width') {</span>
<span class="cstat-no" title="statement not covered" >        const radiusX = numValue / 2</span>
<span class="cstat-no" title="statement not covered" >        patch.radiusX = radiusX</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.radiusX = radiusX</span>
        // 🔧 修复：从最新的元素状态获取radiusY值
<span class="cstat-no" title="statement not covered" >        const currentRadiusY = currentElement.properties?.radiusY ?? (currentElement as any).radiusY ?? 80</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.radiusY = currentRadiusY</span>
<span class="cstat-no" title="statement not covered" >        patch.radiusY = currentRadiusY</span>
<span class="cstat-no" title="statement not covered" >        patch.height = currentRadiusY * 2</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.height = currentRadiusY * 2</span>
<span class="cstat-no" title="statement not covered" >        logger.debug(`[PropertyUpdateService] Ellipse width updated to ${numValue}, synchronized radiusX to ${radiusX}, preserved radiusY: ${currentRadiusY}`)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (key === 'height') {</span>
<span class="cstat-no" title="statement not covered" >        const radiusY = numValue / 2</span>
<span class="cstat-no" title="statement not covered" >        patch.radiusY = radiusY</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.radiusY = radiusY</span>
        // 🔧 修复：从最新的元素状态获取radiusX值
<span class="cstat-no" title="statement not covered" >        const currentRadiusX = currentElement.properties?.radiusX ?? (currentElement as any).radiusX ?? 120</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.radiusX = currentRadiusX</span>
<span class="cstat-no" title="statement not covered" >        patch.radiusX = currentRadiusX</span>
<span class="cstat-no" title="statement not covered" >        patch.width = currentRadiusX * 2</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.width = currentRadiusX * 2</span>
<span class="cstat-no" title="statement not covered" >        logger.debug(`[PropertyUpdateService] Ellipse height updated to ${numValue}, synchronized radiusY to ${radiusY}, preserved radiusX: ${currentRadiusX}`)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // Special handling for polygons: regenerate points when radius changes
<span class="cstat-no" title="statement not covered" >    if ((key === 'radius' || key === 'creationRadius')) {</span>
      // 对于多边形，同时更新 radius 和 creationRadius 以保持一致性
<span class="cstat-no" title="statement not covered" >      if (key === 'radius') {</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.creationRadius = numValue</span>
<span class="cstat-no" title="statement not covered" >        if ('creationRadius' in element) {</span>
<span class="cstat-no" title="statement not covered" >          patch.creationRadius = numValue</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (key === 'creationRadius') {</span>
<span class="cstat-no" title="statement not covered" >        patch.properties.radius = numValue</span>
<span class="cstat-no" title="statement not covered" >        if ('radius' in element) {</span>
<span class="cstat-no" title="statement not covered" >          patch.radius = numValue</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // 检查是否是多边形类型
<span class="cstat-no" title="statement not covered" >      const isPolygonType = [</span>
<span class="cstat-no" title="statement not covered" >        'POLYGON',</span>
<span class="cstat-no" title="statement not covered" >        'TRIANGLE',</span>
<span class="cstat-no" title="statement not covered" >        'QUADRILATERAL',</span>
<span class="cstat-no" title="statement not covered" >        'PENTAGON',</span>
<span class="cstat-no" title="statement not covered" >        'HEXAGON',</span>
<span class="cstat-no" title="statement not covered" >        'polygon',</span>
<span class="cstat-no" title="statement not covered" >        'triangle',</span>
<span class="cstat-no" title="statement not covered" >        'quadrilateral',</span>
<span class="cstat-no" title="statement not covered" >        'pentagon',</span>
<span class="cstat-no" title="statement not covered" >        'hexagon',</span>
<span class="cstat-no" title="statement not covered" >      ].includes(element.type)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      logger.debug(`[PropertyUpdateService] Element type: ${element.type}, isPolygonType: ${isPolygonType}`)</span>
<span class="cstat-no" title="statement not covered" >      logger.debug(`[PropertyUpdateService] Element properties: sides=${String(element.properties?.sides)}, isRegular=${String(element.properties?.isRegular)}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (isPolygonType &amp;&amp; element.properties?.sides !== undefined &amp;&amp; element.properties?.isRegular !== undefined) {</span>
        // 重新生成多边形的点
<span class="cstat-no" title="statement not covered" >        const sides = Number(element.properties.sides)</span>
<span class="cstat-no" title="statement not covered" >        const radius = numValue</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        logger.debug(`[PropertyUpdateService] Regenerating polygon points: sides=${sides}, radius=${radius}, isRegular=${String(element.properties.isRegular)}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (sides &gt;= 3) {</span>
          // 生成相对于原点(0,0)的顶点坐标
<span class="cstat-no" title="statement not covered" >          const newPoints = []</span>
<span class="cstat-no" title="statement not covered" >          const angleStep = (2 * Math.PI) / sides</span>
<span class="cstat-no" title="statement not covered" >          const startAngle = -Math.PI / 2 // 默认起始角度，顶点向上</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          for (let i = 0; i &lt; sides; i++) {</span>
<span class="cstat-no" title="statement not covered" >            const angle = startAngle + i * angleStep</span>
<span class="cstat-no" title="statement not covered" >            const x = radius * Math.cos(angle)</span>
<span class="cstat-no" title="statement not covered" >            const y = radius * Math.sin(angle)</span>
<span class="cstat-no" title="statement not covered" >            newPoints.push({ x, y, z: 0 })</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
          // 计算几何中心并调整点，确保几何中心在原点
<span class="cstat-no" title="statement not covered" >          let sumX = 0</span>
<span class="cstat-no" title="statement not covered" >          let sumY = 0</span>
<span class="cstat-no" title="statement not covered" >          for (const point of newPoints) {</span>
<span class="cstat-no" title="statement not covered" >            sumX += point.x</span>
<span class="cstat-no" title="statement not covered" >            sumY += point.y</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          const centroidX = sumX / sides</span>
<span class="cstat-no" title="statement not covered" >          const centroidY = sumY / sides</span>
&nbsp;
          // 调整点坐标
<span class="cstat-no" title="statement not covered" >          const adjustedPoints = newPoints.map(p =&gt; ({</span>
<span class="cstat-no" title="statement not covered" >            x: p.x - centroidX,</span>
<span class="cstat-no" title="statement not covered" >            y: p.y - centroidY,</span>
<span class="cstat-no" title="statement not covered" >            z: p.z,</span>
<span class="cstat-no" title="statement not covered" >          }))</span>
&nbsp;
          // 更新points属性
<span class="cstat-no" title="statement not covered" >          patch.properties.points = adjustedPoints</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          logger.debug(`[PropertyUpdateService] Successfully regenerated polygon points: radius=${radius}, sides=${sides}, newPointsCount=${adjustedPoints.length}`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        else {</span>
<span class="cstat-no" title="statement not covered" >          logger.warn(`[PropertyUpdateService] Invalid sides count for polygon: ${sides}`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
<span class="cstat-no" title="statement not covered" >        logger.debug(`[PropertyUpdateService] Skipping point regeneration: isPolygonType=${isPolygonType}`)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else if (value === '' || value === null || value === undefined) {</span>
<span class="cstat-no" title="statement not covered" >    const defaultValue = getDefaultValueForNumericProperty(key)</span>
<span class="cstat-no" title="statement not covered" >    if (key in element) {</span>
<span class="cstat-no" title="statement not covered" >      patch[key] = defaultValue</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    patch.properties[key] = defaultValue</span>
&nbsp;
    // Special handling for squares: keep width and height synchronized with default values
<span class="cstat-no" title="statement not covered" >    if (element.type === 'SQUARE' &amp;&amp; (key === 'width' || key === 'height')) {</span>
<span class="cstat-no" title="statement not covered" >      logger.debug(`[PropertyUpdateService] Square ${key} reset to default ${defaultValue}, synchronizing other dimension`)</span>
<span class="cstat-no" title="statement not covered" >      const otherDimension = key === 'width' ? 'height' : 'width'</span>
&nbsp;
      // Update the other dimension to the same default value
<span class="cstat-no" title="statement not covered" >      if (otherDimension in element) {</span>
<span class="cstat-no" title="statement not covered" >        patch[otherDimension] = defaultValue</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      patch.properties[otherDimension] = defaultValue</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      logger.debug(`[PropertyUpdateService] Square dimensions synchronized with defaults: ${key}=${defaultValue}, ${otherDimension}=${defaultValue}`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else {</span>
<span class="cstat-no" title="statement not covered" >    logger.warn(`[PropertyUpdateService] Invalid value for ${propertyPath}: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >    return null</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return patch</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Updates a cost property in the patch
 */
<span class="cstat-no" title="statement not covered" >function updateCostProperty(</span>
<span class="cstat-no" title="statement not covered" >  _element: ShapeElement,</span>
<span class="cstat-no" title="statement not covered" >  patch: { id: string, properties: Record&lt;string, unknown&gt;, [key: string]: unknown },</span>
<span class="cstat-no" title="statement not covered" >  propertyPath: string,</span>
<span class="cstat-no" title="statement not covered" >  value: unknown,</span>
<span class="cstat-no" title="statement not covered" >): { id: string, properties: Record&lt;string, unknown&gt;, [key: string]: unknown } | null {</span>
<span class="cstat-no" title="statement not covered" >  const logger = getLoggerService()</span>
<span class="cstat-no" title="statement not covered" >  const key = propertyPath as CostProperty</span>
&nbsp;
  // 🔧 特殊处理 computeCostEnabled 布尔值
<span class="cstat-no" title="statement not covered" >  if (key === 'computeCostEnabled') {</span>
<span class="cstat-no" title="statement not covered" >    if (typeof value === 'boolean') {</span>
<span class="cstat-no" title="statement not covered" >      patch.properties[key] = value</span>
<span class="cstat-no" title="statement not covered" >      logger.debug(`[PropertyUpdateService] Updated computeCostEnabled to: ${value}`)</span>
<span class="cstat-no" title="statement not covered" >      return patch</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
<span class="cstat-no" title="statement not covered" >      logger.warn(`[PropertyUpdateService] Invalid boolean value for computeCostEnabled: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >      return null</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (key === 'costBasis') {</span>
<span class="cstat-no" title="statement not covered" >    patch.properties[key] = value</span>
<span class="cstat-no" title="statement not covered" >    return patch</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const numValue = parseNumericValue(value)</span>
<span class="cstat-no" title="statement not covered" >  if (numValue !== null) {</span>
<span class="cstat-no" title="statement not covered" >    patch.properties[key] = numValue</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else if (value === '' || value === null || value === undefined) {</span>
<span class="cstat-no" title="statement not covered" >    patch.properties[key] = getDefaultValueForCostProperty(key)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else {</span>
<span class="cstat-no" title="statement not covered" >    logger.warn(`[PropertyUpdateService] Invalid value for ${propertyPath}: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >    return null</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return patch</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Updates a style property in the patch
 */
<span class="cstat-no" title="statement not covered" >function updateStyleProperty(</span>
<span class="cstat-no" title="statement not covered" >  element: ShapeElement,</span>
<span class="cstat-no" title="statement not covered" >  patch: { id: string, properties: Record&lt;string, unknown&gt;, [key: string]: unknown },</span>
<span class="cstat-no" title="statement not covered" >  propertyPath: string,</span>
<span class="cstat-no" title="statement not covered" >  value: unknown,</span>
<span class="cstat-no" title="statement not covered" >): { id: string, properties: Record&lt;string, unknown&gt;, [key: string]: unknown } | null {</span>
<span class="cstat-no" title="statement not covered" >  const logger = getLoggerService()</span>
<span class="cstat-no" title="statement not covered" >  const key = propertyPath as StyleProperty</span>
&nbsp;
  // 特殊处理 strokeWidth 和 opacity，它们应该是数字类型
<span class="cstat-no" title="statement not covered" >  if (key === 'strokeWidth' || key === 'opacity') {</span>
<span class="cstat-no" title="statement not covered" >    if (typeof value === 'number') {</span>
<span class="cstat-no" title="statement not covered" >      if (key in element) {</span>
<span class="cstat-no" title="statement not covered" >        patch[key] = value</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      patch.properties[key] = value</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else if (typeof value === 'string') {</span>
<span class="cstat-no" title="statement not covered" >      const numValue = Number.parseFloat(value)</span>
<span class="cstat-no" title="statement not covered" >      if (!Number.isNaN(numValue)) {</span>
<span class="cstat-no" title="statement not covered" >        if (key in element) {</span>
<span class="cstat-no" title="statement not covered" >          patch[key] = numValue</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        patch.properties[key] = numValue</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
<span class="cstat-no" title="statement not covered" >        logger.warn(`[PropertyUpdateService] Invalid numeric value for style property ${propertyPath}: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >        return null</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
<span class="cstat-no" title="statement not covered" >      logger.warn(`[PropertyUpdateService] Invalid type for style property ${propertyPath}: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >      return null</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else if (typeof value === 'string' || value === undefined || value === null) {</span>
    // 其他样式属性应该是字符串类型
<span class="cstat-no" title="statement not covered" >    if (key in element) {</span>
<span class="cstat-no" title="statement not covered" >      patch[key] = value</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    patch.properties[key] = value</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else {</span>
<span class="cstat-no" title="statement not covered" >    logger.warn(`[PropertyUpdateService] Invalid value for style property ${propertyPath}: ${String(value)}`)</span>
<span class="cstat-no" title="statement not covered" >    return null</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return patch</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Parses a value as a number, returning null if it's not a valid number
 */
<span class="cstat-no" title="statement not covered" >export function parseNumericValue(value: unknown): number | null {</span>
<span class="cstat-no" title="statement not covered" >  if (typeof value === 'number' &amp;&amp; Number.isFinite(value)) {</span>
<span class="cstat-no" title="statement not covered" >    return value</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (typeof value === 'string') {</span>
    // Clean the string value (remove non-numeric characters except decimal point and minus sign)
<span class="cstat-no" title="statement not covered" >    const cleanedValue = value.replace(/[^\d.-]/g, '').replace(/^-+/, '-')</span>
<span class="cstat-no" title="statement not covered" >    const numValue = Number.parseFloat(cleanedValue)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (Number.isFinite(numValue)) {</span>
<span class="cstat-no" title="statement not covered" >      return numValue</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return null</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Gets the default value for a numeric property
 */
<span class="cstat-no" title="statement not covered" >export function getDefaultValueForNumericProperty(property: NumericProperty): number {</span>
<span class="cstat-no" title="statement not covered" >  switch (property) {</span>
<span class="cstat-no" title="statement not covered" >    case 'rotation':</span>
<span class="cstat-no" title="statement not covered" >      return 0</span>
<span class="cstat-no" title="statement not covered" >    case 'width':</span>
<span class="cstat-no" title="statement not covered" >    case 'height':</span>
<span class="cstat-no" title="statement not covered" >      return 100</span>
<span class="cstat-no" title="statement not covered" >    case 'cornerRadius':</span>
<span class="cstat-no" title="statement not covered" >      return 0</span>
<span class="cstat-no" title="statement not covered" >    case 'strokeWidth':</span>
<span class="cstat-no" title="statement not covered" >      return 1</span>
<span class="cstat-no" title="statement not covered" >    case 'opacity':</span>
<span class="cstat-no" title="statement not covered" >      return 1</span>
<span class="cstat-no" title="statement not covered" >    case 'creationRadius':</span>
<span class="cstat-no" title="statement not covered" >    case 'radius':</span>
<span class="cstat-no" title="statement not covered" >      return 50</span>
<span class="cstat-no" title="statement not covered" >    case 'radiusX':</span>
<span class="cstat-no" title="statement not covered" >      return 120 // 椭圆默认X半径</span>
<span class="cstat-no" title="statement not covered" >    case 'radiusY':</span>
<span class="cstat-no" title="statement not covered" >      return 80 // 椭圆默认Y半径</span>
<span class="cstat-no" title="statement not covered" >    case 'startAngle':</span>
<span class="cstat-no" title="statement not covered" >      return 0 // 圆弧默认起始角度</span>
<span class="cstat-no" title="statement not covered" >    case 'endAngle':</span>
<span class="cstat-no" title="statement not covered" >      return 90 // 圆弧默认结束角度</span>
<span class="cstat-no" title="statement not covered" >    case 'costUnitPrice':</span>
<span class="cstat-no" title="statement not covered" >      return 1 // 默认单位成本为1而不是0</span>
<span class="cstat-no" title="statement not covered" >    case 'costMultiplierOrCount':</span>
<span class="cstat-no" title="statement not covered" >      return 1 // 🔧 修复：默认乘数为1，确保成本计算正常工作</span>
<span class="cstat-no" title="statement not covered" >    default:</span>
<span class="cstat-no" title="statement not covered" >      return 0</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Gets the default value for a cost property
 */
<span class="cstat-no" title="statement not covered" >export function getDefaultValueForCostProperty(property: CostProperty): number | string | boolean {</span>
<span class="cstat-no" title="statement not covered" >  switch (property) {</span>
<span class="cstat-no" title="statement not covered" >    case 'costUnitPrice':</span>
<span class="cstat-no" title="statement not covered" >      return 1 // 默认单位成本为1</span>
<span class="cstat-no" title="statement not covered" >    case 'costMultiplierOrCount':</span>
<span class="cstat-no" title="statement not covered" >      return 1 // 🔧 修复：默认乘数为1，确保成本计算正常工作</span>
<span class="cstat-no" title="statement not covered" >    case 'costBasis':</span>
<span class="cstat-no" title="statement not covered" >      return 'unit' // 默认成本基础为unit</span>
<span class="cstat-no" title="statement not covered" >    case 'costTotal':</span>
<span class="cstat-no" title="statement not covered" >      return 0</span>
<span class="cstat-no" title="statement not covered" >    case 'computeCostEnabled':</span>
<span class="cstat-no" title="statement not covered" >      return true // 🔧 默认启用成本计算</span>
<span class="cstat-no" title="statement not covered" >    default:</span>
<span class="cstat-no" title="statement not covered" >      return 0</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Implements ElementEditService interface
 * @implements {ElementEditService}
 */
<span class="cstat-no" title="statement not covered" >export class ElementEditServiceImpl implements ElementEditService {</span>
<span class="cstat-no" title="statement not covered" >  readonly serviceId: string = ServiceId.ElementEditService as string</span>
&nbsp;
  /**
   * Creates a new instance of the ElementEditServiceImpl
   * @param eventBus - Event bus for publishing events
   * @param logger - Logger service for logging
   * @param repository - Repository for accessing shapes
   */
<span class="cstat-no" title="statement not covered" >  constructor(</span>
<span class="cstat-no" title="statement not covered" >    private eventBus: EventBus&lt;AppEventMap&gt;,</span>
<span class="cstat-no" title="statement not covered" >    private logger: LoggerService,</span>
<span class="cstat-no" title="statement not covered" >    private repository?: ShapeRepository,</span>
<span class="cstat-no" title="statement not covered" >  ) {</span>
<span class="cstat-no" title="statement not covered" >    this.logger.info('[ElementEditServiceImpl] Initialized.')</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Factory method to create a ElementEditServiceImpl instance
   * @param logger - Optional logger service
   * @returns A new ElementEditServiceImpl instance
   */
<span class="cstat-no" title="statement not covered" >  public static create(</span>
<span class="cstat-no" title="statement not covered" >    logger?: LoggerService,</span>
<span class="cstat-no" title="statement not covered" >  ): ElementEditServiceImpl {</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      const eventBus = getService&lt;EventBus&lt;AppEventMap&gt;&gt;(ServiceId.EventBus)</span>
<span class="cstat-no" title="statement not covered" >      const loggerService = logger || getService&lt;LoggerService&gt;(ServiceId.Logger)</span>
      // 临时删除存储库获取，因为ServiceId没有ShapeRepository枚举值
      // const repository = getService&lt;ShapeRepository&gt;(ServiceId.ShapeRepository)
&nbsp;
<span class="cstat-no" title="statement not covered" >      return new ElementEditServiceImpl(eventBus, loggerService)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    catch (error) {</span>
<span class="cstat-no" title="statement not covered" >      throw new Error(`Failed to create ElementEditServiceImpl: ${error instanceof Error ? error.message : String(error)}`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * 从ElementEditRequest中提取需要发送到事件中的changes
   * 使用PropertyUpdateService的逻辑来确保一致的属性更新
   */
<span class="cstat-no" title="statement not covered" >  private extractChanges(request: LocalElementEditRequest): Record&lt;string, unknown&gt; {</span>
    // 直接使用 request，因为类型已经确定
<span class="cstat-no" title="statement not covered" >    const typedRequest = request</span>
&nbsp;
    // 创建一个基本的元素对象，用于创建补丁
<span class="cstat-no" title="statement not covered" >    const baseElement: Partial&lt;ShapeElement&gt; = {</span>
<span class="cstat-no" title="statement not covered" >      id: typedRequest.id,</span>
<span class="cstat-no" title="statement not covered" >      type: 'unknown', // 类型不重要，因为我们只关心属性更新</span>
<span class="cstat-no" title="statement not covered" >      position: { x: 0, y: 0 },</span>
<span class="cstat-no" title="statement not covered" >      properties: {},</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // 使用现有属性更新baseElement
<span class="cstat-no" title="statement not covered" >    if (typedRequest.position !== undefined &amp;&amp; typedRequest.position !== null) {</span>
<span class="cstat-no" title="statement not covered" >      baseElement.position = { ...typedRequest.position }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // 合并所有属性到一个补丁对象
<span class="cstat-no" title="statement not covered" >    const patch: Record&lt;string, unknown&gt; = { id: typedRequest.id }</span>
&nbsp;
    // 处理样式信息
<span class="cstat-no" title="statement not covered" >    if (typedRequest.style !== undefined &amp;&amp; typedRequest.style !== null) {</span>
<span class="cstat-no" title="statement not covered" >      Object.entries(typedRequest.style).forEach(([key, value]) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const propertyPatch = createPropertyPatch(baseElement as ShapeElement, key, value)</span>
<span class="cstat-no" title="statement not covered" >        if (propertyPatch !== null) {</span>
<span class="cstat-no" title="statement not covered" >          Object.entries(propertyPatch).forEach(([patchKey, patchValue]) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            if (patchKey !== 'id') {</span>
<span class="cstat-no" title="statement not covered" >              patch[patchKey] = patchValue</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          })</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      })</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // 处理图层信息
<span class="cstat-no" title="statement not covered" >    if (typedRequest.majorCategory !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug(`[ElementEditServiceImpl] Processing majorCategory: ${String(typedRequest.majorCategory)}`)</span>
      // 直接设置，不需要通过 createPropertyPatch
<span class="cstat-no" title="statement not covered" >      patch.majorCategory = typedRequest.majorCategory</span>
<span class="cstat-no" title="statement not covered" >      if (patch.properties === undefined || patch.properties === null)</span>
<span class="cstat-no" title="statement not covered" >        patch.properties = {}</span>
<span class="cstat-no" title="statement not covered" >      ;(patch.properties as Record&lt;string, unknown&gt;).majorCategory = typedRequest.majorCategory</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug(`[ElementEditServiceImpl] Set majorCategory to: ${String(typedRequest.majorCategory)}`)</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug(`[ElementEditServiceImpl] Current patch after setting majorCategory:`, JSON.stringify(patch, null, 2))</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (typedRequest.minorCategory !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug(`[ElementEditServiceImpl] Processing minorCategory: ${String(typedRequest.minorCategory)}`)</span>
      // 直接设置，不需要通过 createPropertyPatch
<span class="cstat-no" title="statement not covered" >      patch.minorCategory = typedRequest.minorCategory</span>
<span class="cstat-no" title="statement not covered" >      if (patch.properties === undefined || patch.properties === null)</span>
<span class="cstat-no" title="statement not covered" >        patch.properties = {}</span>
<span class="cstat-no" title="statement not covered" >      ;(patch.properties as Record&lt;string, unknown&gt;).minorCategory = typedRequest.minorCategory</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug(`[ElementEditServiceImpl] Set minorCategory to: ${String(typedRequest.minorCategory)}`)</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug(`[ElementEditServiceImpl] Current patch after setting minorCategory:`, JSON.stringify(patch, null, 2))</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (typedRequest.zLevelId !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug(`[ElementEditServiceImpl] Processing zLevelId: ${String(typedRequest.zLevelId)}`)</span>
      // 直接设置，不需要通过 createPropertyPatch
<span class="cstat-no" title="statement not covered" >      patch.zLevelId = typedRequest.zLevelId</span>
<span class="cstat-no" title="statement not covered" >      if (patch.properties === undefined || patch.properties === null)</span>
<span class="cstat-no" title="statement not covered" >        patch.properties = {}</span>
<span class="cstat-no" title="statement not covered" >      ;(patch.properties as Record&lt;string, unknown&gt;).zLevelId = typedRequest.zLevelId</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug(`[ElementEditServiceImpl] Set zLevelId to: ${String(typedRequest.zLevelId)}`)</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug(`[ElementEditServiceImpl] Current patch after setting zLevelId:`, JSON.stringify(patch, null, 2))</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // 确保图层属性已正确设置
<span class="cstat-no" title="statement not covered" >    if (typedRequest.majorCategory !== undefined || typedRequest.minorCategory !== undefined || typedRequest.zLevelId !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug(`[ElementEditServiceImpl] Final layer properties in patch:`, {</span>
<span class="cstat-no" title="statement not covered" >        majorCategory: patch.majorCategory,</span>
<span class="cstat-no" title="statement not covered" >        minorCategory: patch.minorCategory,</span>
<span class="cstat-no" title="statement not covered" >        zLevelId: patch.zLevelId,</span>
<span class="cstat-no" title="statement not covered" >        propertiesMajorCategory: (patch.properties as Record&lt;string, unknown&gt;)?.majorCategory,</span>
<span class="cstat-no" title="statement not covered" >        propertiesMinorCategory: (patch.properties as Record&lt;string, unknown&gt;)?.minorCategory,</span>
<span class="cstat-no" title="statement not covered" >        propertiesZLevelId: (patch.properties as Record&lt;string, unknown&gt;)?.zLevelId,</span>
<span class="cstat-no" title="statement not covered" >      })</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // 处理其他属性
<span class="cstat-no" title="statement not covered" >    if (typedRequest.properties !== undefined &amp;&amp; typedRequest.properties !== null) {</span>
<span class="cstat-no" title="statement not covered" >      Object.entries(typedRequest.properties).forEach(([key, value]) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const propertyPatch = createPropertyPatch(baseElement as ShapeElement, key, value)</span>
<span class="cstat-no" title="statement not covered" >        if (propertyPatch !== null) {</span>
          // 合并属性
<span class="cstat-no" title="statement not covered" >          if (propertyPatch.properties !== undefined &amp;&amp; propertyPatch.properties !== null) {</span>
<span class="cstat-no" title="statement not covered" >            if (patch.properties === undefined || patch.properties === null)</span>
<span class="cstat-no" title="statement not covered" >              patch.properties = {}</span>
<span class="cstat-no" title="statement not covered" >            Object.entries(propertyPatch.properties).forEach(([propKey, propValue]) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >              ;(patch.properties as Record&lt;string, unknown&gt;)[propKey] = propValue</span>
<span class="cstat-no" title="statement not covered" >            })</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
          // 合并顶层属性（除了id和properties）
<span class="cstat-no" title="statement not covered" >          Object.entries(propertyPatch).forEach(([patchKey, patchValue]) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            if (patchKey !== 'id' &amp;&amp; patchKey !== 'properties') {</span>
<span class="cstat-no" title="statement not covered" >              patch[patchKey] = patchValue</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          })</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      })</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return patch</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Edit a shape based on the provided request
   * @param request - Shape edit request
   * @returns Promise resolving to a ElementEditResult
   */
<span class="cstat-no" title="statement not covered" >  async editShape(request: unknown): Promise&lt;ElementEditResult&gt; {</span>
<span class="cstat-no" title="statement not covered" >    this.logger.debug('[ElementEditServiceImpl] Edit request details:', request)</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      // 运行时类型检查和转换
<span class="cstat-no" title="statement not covered" >      if (request === null || request === undefined || typeof request !== 'object' || !('id' in request) || typeof (request as { id: unknown }).id !== 'string') {</span>
<span class="cstat-no" title="statement not covered" >        throw new Error('Invalid ElementEditRequest: missing or invalid id property')</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // 转换为本地类型以确保类型安全
<span class="cstat-no" title="statement not covered" >      const requestObj = request as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >      const typedRequest: LocalElementEditRequest = {</span>
<span class="cstat-no" title="statement not covered" >        id: requestObj.id as string,</span>
<span class="cstat-no" title="statement not covered" >        position: requestObj.position as { x: number, y: number } | undefined,</span>
<span class="cstat-no" title="statement not covered" >        style: requestObj.style as Record&lt;string, unknown&gt; | undefined,</span>
<span class="cstat-no" title="statement not covered" >        properties: requestObj.properties as Record&lt;string, unknown&gt; | undefined,</span>
<span class="cstat-no" title="statement not covered" >        selectAfterEdit: requestObj.selectAfterEdit as boolean | undefined,</span>
<span class="cstat-no" title="statement not covered" >        majorCategory: requestObj.majorCategory as string | undefined,</span>
<span class="cstat-no" title="statement not covered" >        minorCategory: requestObj.minorCategory as string | undefined,</span>
<span class="cstat-no" title="statement not covered" >        zLevelId: requestObj.zLevelId as string | undefined,</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // 发布StateUpdated事件以记录编辑前的状态（用于撤销/重做）
<span class="cstat-no" title="statement not covered" >      this.logger.debug('[ElementEditServiceImpl] Publishing StateUpdated event with preEdit action')</span>
<span class="cstat-no" title="statement not covered" >      this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >        type: AppEventType.StateUpdated,</span>
<span class="cstat-no" title="statement not covered" >        timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >        payload: {</span>
<span class="cstat-no" title="statement not covered" >          action: 'preEdit',</span>
<span class="cstat-no" title="statement not covered" >          elementIds: [typedRequest.id],</span>
<span class="cstat-no" title="statement not covered" >        },</span>
<span class="cstat-no" title="statement not covered" >      })</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      return await new Promise&lt;ElementEditResult&gt;((resolve) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (!typedRequest.id || typeof typedRequest.id !== 'string') {</span>
<span class="cstat-no" title="statement not covered" >          this.emitError('SHAPE_EDIT_ERROR', 'Missing shape ID in edit request', { request: typedRequest })</span>
<span class="cstat-no" title="statement not covered" >          resolve({</span>
<span class="cstat-no" title="statement not covered" >            success: false,</span>
<span class="cstat-no" title="statement not covered" >            error: {</span>
<span class="cstat-no" title="statement not covered" >              code: 'SHAPE_EDIT_ERROR',</span>
<span class="cstat-no" title="statement not covered" >              message: 'Missing shape ID in edit request',</span>
<span class="cstat-no" title="statement not covered" >              details: { request: typedRequest },</span>
<span class="cstat-no" title="statement not covered" >            },</span>
<span class="cstat-no" title="statement not covered" >            timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >          })</span>
<span class="cstat-no" title="statement not covered" >          return</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // 获取正在编辑的形状ID
<span class="cstat-no" title="statement not covered" >        const shapeId = typedRequest.id</span>
&nbsp;
        // 构建编辑后的数据对象 - 返回undefined，因为实际的形状数据由repository管理
<span class="cstat-no" title="statement not covered" >        const editedShapeData: ShapeElement | undefined = undefined // ElementEditResult期望ShapeElement类型</span>
&nbsp;
        // 发布编辑事件
<span class="cstat-no" title="statement not covered" >        this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >          type: AppEventType.ShapeEditRequest,</span>
<span class="cstat-no" title="statement not covered" >          timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >          payload: {</span>
<span class="cstat-no" title="statement not covered" >            shapeId,</span>
<span class="cstat-no" title="statement not covered" >            changes: this.extractChanges(typedRequest),</span>
<span class="cstat-no" title="statement not covered" >            source: 'ElementEditServiceImpl',</span>
<span class="cstat-no" title="statement not covered" >          },</span>
<span class="cstat-no" title="statement not covered" >        })</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        resolve({ success: true, data: editedShapeData, timestamp: Date.now() })</span>
&nbsp;
        // 发布编辑完成事件，传递更新后的shape对象（通过 ShapeEditComplete 事件）
<span class="cstat-no" title="statement not covered" >        if (this.repository &amp;&amp; shapeId) {</span>
          // 获取更新后的shape对象
<span class="cstat-no" title="statement not covered" >          const updatedShape = this.repository.getById(shapeId)</span>
<span class="cstat-no" title="statement not covered" >          if (updatedShape) {</span>
            // 获取当前选中状态以便在事件处理时使用
<span class="cstat-no" title="statement not covered" >            const selectedIds = Array.from(this.repository.getSelectedIds())</span>
&nbsp;
            // 发布ShapeEditComplete事件
<span class="cstat-no" title="statement not covered" >            this.logger.debug('[ElementEditServiceImpl] Publishing ShapeEditComplete event. Selected IDs:', selectedIds)</span>
<span class="cstat-no" title="statement not covered" >            this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >              type: AppEventType.ShapeEditComplete,</span>
<span class="cstat-no" title="statement not covered" >              timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >              payload: {</span>
<span class="cstat-no" title="statement not covered" >                shape: updatedShape,</span>
<span class="cstat-no" title="statement not covered" >                selectedIds, // 传递当前选中ID数组</span>
<span class="cstat-no" title="statement not covered" >              },</span>
<span class="cstat-no" title="statement not covered" >            })</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          else {</span>
<span class="cstat-no" title="statement not covered" >            this.logger.warn(`[ElementEditServiceImpl] Cannot find updated shape with ID ${shapeId} in repository`)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      })</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    catch (error) {</span>
<span class="cstat-no" title="statement not covered" >      const errorDetails = { request: request as Record&lt;string, unknown&gt;, error }</span>
<span class="cstat-no" title="statement not covered" >      this.emitError('SHAPE_EDIT_ERROR', `Error editing shape: ${error instanceof Error ? error.message : String(error)}`, errorDetails)</span>
<span class="cstat-no" title="statement not covered" >      return {</span>
<span class="cstat-no" title="statement not covered" >        success: false,</span>
<span class="cstat-no" title="statement not covered" >        error: {</span>
<span class="cstat-no" title="statement not covered" >          code: 'SHAPE_EDIT_ERROR',</span>
<span class="cstat-no" title="statement not covered" >          message: `Error editing shape: ${error instanceof Error ? error.message : String(error)}`,</span>
<span class="cstat-no" title="statement not covered" >          details: errorDetails,</span>
<span class="cstat-no" title="statement not covered" >        },</span>
<span class="cstat-no" title="statement not covered" >        timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Emits an error event for shape edit failures.
   */
<span class="cstat-no" title="statement not covered" >  private emitError(errorType: string, message: string, context?: Record&lt;string, unknown&gt;): void {</span>
<span class="cstat-no" title="statement not covered" >    const errorId = uuidv4()</span>
<span class="cstat-no" title="statement not covered" >    const fullMessage = `[${errorType}] ${message}`</span>
<span class="cstat-no" title="statement not covered" >    this.logger.error(fullMessage, { errorId, ...(context || {}) })</span>
<span class="cstat-no" title="statement not covered" >    this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >      type: AppEventType.EventError,</span>
<span class="cstat-no" title="statement not covered" >      timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >      payload: {</span>
<span class="cstat-no" title="statement not covered" >        error: {</span>
<span class="cstat-no" title="statement not covered" >          code: errorType,</span>
<span class="cstat-no" title="statement not covered" >          message: fullMessage,</span>
<span class="cstat-no" title="statement not covered" >          details: context,</span>
<span class="cstat-no" title="statement not covered" >          errorId,</span>
<span class="cstat-no" title="statement not covered" >        },</span>
<span class="cstat-no" title="statement not covered" >        context,</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    })</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Transforms one or more shapes by publishing edit events for each.
   *
   * @param shapeIds Array of shape IDs to transform
   * @param transformations Transform operations to apply
   * @returns Result containing success or error
   */
<span class="cstat-no" title="statement not covered" >  async transformShapes(</span>
<span class="cstat-no" title="statement not covered" >    shapeIds: string[],</span>
<span class="cstat-no" title="statement not covered" >    transformations: Record&lt;string, unknown&gt;,</span>
<span class="cstat-no" title="statement not covered" >  ): Promise&lt;ServiceResult&lt;ShapeElement[]&gt;&gt; {</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug(`[ElementEditServiceImpl] Publishing transform events for ${shapeIds.length} shapes`, { shapeIds, transformations })</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug(`[ElementEditServiceImpl] transformShapes called with transformations:`, JSON.stringify(transformations, null, 2))</span>
&nbsp;
      // 先发布StateUpdated事件，确保当前状态被记录到历史中，以便后续可以撤销
<span class="cstat-no" title="statement not covered" >      this.logger.debug('[ElementEditServiceImpl] Publishing StateUpdated event with preEdit action for batch transform')</span>
<span class="cstat-no" title="statement not covered" >      this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >        type: AppEventType.StateUpdated,</span>
<span class="cstat-no" title="statement not covered" >        timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >        payload: {</span>
<span class="cstat-no" title="statement not covered" >          action: 'preEdit',</span>
<span class="cstat-no" title="statement not covered" >          elementIds: shapeIds,</span>
<span class="cstat-no" title="statement not covered" >        },</span>
<span class="cstat-no" title="statement not covered" >      })</span>
&nbsp;
      // 轻微等待，确保StateUpdated事件已被处理
<span class="cstat-no" title="statement not covered" >      await new Promise(resolve =&gt; setTimeout(resolve, 10))</span>
&nbsp;
      // 然后为每个shapeId发布编辑请求事件
<span class="cstat-no" title="statement not covered" >      for (const shapeId of shapeIds) {</span>
        // 获取当前形状，以便保留现有属性
<span class="cstat-no" title="statement not covered" >        const currentShape = this.repository?.getById(shapeId)</span>
&nbsp;
        // 创建变更对象，确保保留现有属性
<span class="cstat-no" title="statement not covered" >        const changes: Record&lt;string, unknown&gt; = {}</span>
&nbsp;
        // 如果找到了当前形状，先复制其所有属性
<span class="cstat-no" title="statement not covered" >        if (currentShape) {</span>
          // 确保properties对象存在
<span class="cstat-no" title="statement not covered" >          if (changes.properties === undefined || changes.properties === null) {</span>
<span class="cstat-no" title="statement not covered" >            changes.properties = { ...(currentShape.properties || {}) }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // 处理properties对象
<span class="cstat-no" title="statement not covered" >        if ('properties' in transformations &amp;&amp; typeof transformations.properties === 'object' &amp;&amp; transformations.properties !== null) {</span>
          // 确保changes.properties存在
<span class="cstat-no" title="statement not covered" >          if (changes.properties === undefined || changes.properties === null) {</span>
<span class="cstat-no" title="statement not covered" >            changes.properties = {}</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
          // 将transformations.properties中的属性合并到changes.properties中
<span class="cstat-no" title="statement not covered" >          const properties = transformations.properties as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >          for (const key in properties) {</span>
<span class="cstat-no" title="statement not covered" >            if (Object.prototype.hasOwnProperty.call(properties, key)) {</span>
<span class="cstat-no" title="statement not covered" >              ;(changes.properties as Record&lt;string, unknown&gt;)[key] = properties[key]</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // 处理其他顶层属性
<span class="cstat-no" title="statement not covered" >        for (const key in transformations) {</span>
<span class="cstat-no" title="statement not covered" >          if (key !== 'properties' &amp;&amp; Object.prototype.hasOwnProperty.call(transformations, key)) {</span>
<span class="cstat-no" title="statement not covered" >            changes[key] = transformations[key]</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        this.logger.debug(`[ElementEditServiceImpl] Publishing ShapeEditRequest for shape ${shapeId} with changes:`, JSON.stringify(changes, null, 2))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >          type: AppEventType.ShapeEditRequest,</span>
<span class="cstat-no" title="statement not covered" >          timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >          payload: {</span>
<span class="cstat-no" title="statement not covered" >            shapeId,</span>
<span class="cstat-no" title="statement not covered" >            changes,</span>
<span class="cstat-no" title="statement not covered" >            source: ElementEditServiceImpl.name,</span>
<span class="cstat-no" title="statement not covered" >          },</span>
<span class="cstat-no" title="statement not covered" >        })</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      return {</span>
<span class="cstat-no" title="statement not covered" >        success: true,</span>
<span class="cstat-no" title="statement not covered" >        data: [], // No direct data, as store update is handled by CoreCoordinator</span>
<span class="cstat-no" title="statement not covered" >        timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    catch (error) {</span>
<span class="cstat-no" title="statement not covered" >      this.emitError('SHAPE_TRANSFORM_ERROR', error instanceof Error ? error.message : 'Unknown error', { shapeIds, transformations })</span>
<span class="cstat-no" title="statement not covered" >      return {</span>
<span class="cstat-no" title="statement not covered" >        success: false,</span>
<span class="cstat-no" title="statement not covered" >        error: {</span>
<span class="cstat-no" title="statement not covered" >          code: 'SHAPE_TRANSFORM_ERROR',</span>
<span class="cstat-no" title="statement not covered" >          message: error instanceof Error ? error.message : 'Unknown error',</span>
<span class="cstat-no" title="statement not covered" >          details: error,</span>
<span class="cstat-no" title="statement not covered" >        },</span>
<span class="cstat-no" title="statement not covered" >        timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:58:01.486Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    