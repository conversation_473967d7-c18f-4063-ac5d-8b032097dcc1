# Deployment Guide

This document provides instructions and considerations for deploying the RenoPilot.JS.Shapes2 application to a production environment.

## Overview

RenoPilot.JS.Shapes2 is a client-side single-page application (SPA) built with Vite. Deployment involves building the static assets and serving them via a web server or a static hosting platform.

## Build Process

Before deployment, you need to create a production-ready build of the application.

1.  **Ensure your local repository is up-to-date:**
    ```bash
    git pull origin main  # Or your primary branch
    ```
2.  **Install/Update Dependencies (if necessary):**
    ```bash
    npm install
    # or
    yarn install
    ```
3.  **Run the Production Build Command:**
    ```bash
    npm run build
    # or
    yarn build
    ```
    This command executes Vite's build process, which typically includes:
    *   TypeScript compilation.
    *   Code bundling and minification (JavaScript and CSS).
    *   Tree shaking to remove unused code.
    *   Asset optimization (e.g., image compression).
    *   Generation of static HTML, JS, and CSS files.

    The output will be placed in the `dist/` directory in the project root.

## Deployment Options

The contents of the `dist/` directory can be deployed to various platforms.

### 1. Static Hosting Platforms

Platforms like Vercel, Netlify, GitHub Pages, AWS S3 (with CloudFront), Google Firebase Hosting, or Azure Static Web Apps are excellent choices for hosting SPAs.

*   **Vercel/Netlify:**
    1.  Connect your Git repository (GitHub, GitLab, Bitbucket) to Vercel or Netlify.
    2.  Configure the build settings:
        *   **Build Command:** `npm run build` (or `yarn build`)
        *   **Publish Directory:** `dist`
        *   **Node.js Version:** Specify a compatible version (e.g., 18.x).
    3.  These platforms often automatically deploy on every push to the configured branch.
    4.  **SPA Configuration:** Ensure the platform is configured to handle client-side routing. This usually involves setting up a rewrite rule so that all paths are directed to `index.html`. Most modern static hosting platforms handle this automatically for Vite/React projects or provide simple configuration options.
        *   For Netlify, a `_redirects` file in your `public/` directory (which gets copied to `dist/`) might look like:
            ```
            /*    /index.html   200
            ```
        *   For Vercel, this is typically handled automatically. You can add a `vercel.json` if needed:
            ```json
            {
              "rewrites": [
                { "source": "/(.*)", "destination": "/index.html" }
              ]
            }
            ```

*   **GitHub Pages:**
    1.  Configure your repository to deploy from a specific branch (e.g., `gh-pages`) or a `/docs` folder on your main branch.
    2.  You might need to adjust the `base` path in `vite.config.ts` if deploying to a subdirectory (e.g., `https://<username>.github.io/<repository-name>/`).
        ```typescript
        // vite.config.ts
        export default defineConfig({
          base: '/<repository-name>/',
          // ...
        });
        ```
    3.  Use GitHub Actions to automate the build and deployment to the `gh-pages` branch.

*   **AWS S3 + CloudFront:**
    1.  Upload the contents of the `dist/` directory to an S3 bucket configured for static website hosting.
    2.  Configure a CloudFront distribution to serve the S3 bucket content, providing HTTPS, caching, and CDN benefits.
    3.  Ensure error pages (e.g., 404, 403) are configured to return `index.html` with a 200 status code to support client-side routing.

### 2. Traditional Web Servers (Nginx, Apache)

If you are using a traditional web server:

1.  Upload the contents of the `dist/` directory to your server.
2.  Configure your web server to serve these static files.
3.  **SPA Configuration (Crucial):** You must configure the server to redirect all requests for non-existent files (routes handled by the client-side router) to your `index.html` file.

    *   **Nginx Example Configuration:**
        ```nginx
        server {
          listen 80;
          server_name yourdomain.com;

          root /path/to/your/RenoPilot.JS.Shapes2/dist;
          index index.html;

          location / {
            try_files $uri $uri/ /index.html;
          }

          # Optional: Add headers for caching, security, etc.
          location ~* \.(?:css|js)$ {
            expires 1y;
            add_header Cache-Control "public";
          }
        }
        ```

    *   **Apache Example Configuration (using `.htaccess` in the `dist/` directory):**
        ```apache
        <IfModule mod_rewrite.c>
          RewriteEngine On
          RewriteBase /
          RewriteRule ^index\.html$ - [L]
          RewriteCond %{REQUEST_FILENAME} !-f
          RewriteCond %{REQUEST_FILENAME} !-d
          RewriteCond %{REQUEST_FILENAME} !-l
          RewriteRule . /index.html [L]
        </IfModule>
        ```

## Environment Variables

If your application relies on runtime environment variables (though Vite typically embeds them at build time via `import.meta.env`), ensure they are configured correctly in your deployment environment.

*   For Vite, environment variables prefixed with `VITE_` are embedded into the client bundle from `.env` files during the build process (`.env.production`, `.env.production.local`).
*   If you need truly runtime configuration (e.g., API URLs that differ per environment without rebuilding), you might need to:
    1.  Load a configuration file at runtime (e.g., `config.json` served alongside `index.html`).
    2.  Inject configuration via a script tag in `index.html` that sets global variables, populated by your deployment environment.

## Continuous Deployment (CD)

Automating your deployment process is highly recommended.

*   **GitHub Actions, GitLab CI/CD, Jenkins, etc.:**
    *   Set up a CI/CD pipeline that triggers on pushes or merges to your main/production branch.
    *   The pipeline should:
        1.  Check out the code.
        2.  Install dependencies.
        3.  Run linters and tests.
        4.  Build the application (`npm run build`).
        5.  Deploy the `dist/` directory to your chosen hosting platform.

## Post-Deployment Checks

After deployment, always verify:

*   The application loads correctly.
*   Client-side routing works for various paths.
*   Core functionalities are working as expected.
*   Check the browser console for any errors.
*   If applicable, ensure HTTPS is enforced.

## Rollbacks

Have a rollback strategy in place. Most modern static hosting platforms (Vercel, Netlify) offer easy rollbacks to previous deployments. For manual deployments, this might involve redeploying a previous version of the `dist/` directory.

By following these steps, you can successfully deploy RenoPilot.JS.Shapes2 to a production environment.