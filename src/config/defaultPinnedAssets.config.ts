import { ElementType as CoreElementType } from '@/types/core/elementDefinitions'

export interface DefaultPinnedAssetConfig {
  type: CoreElementType
  isSpecific: boolean
  assetName?: string // For specific predefined elements, not used by these universal defaults but kept for interface consistency
}

export const defaultPinnedBasicShapes: DefaultPinnedAssetConfig[] = [
  // --- Draggable Defaults (User Requested - Left Side of Collapsed Bar, Rendered Center-to-Left) ---
  { type: CoreElementType.SQUARE, isSpecific: false, assetName: 'Square' },
  { type: CoreElementType.CIRCLE, isSpecific: false, assetName: 'Circle' },
  { type: CoreElementType.TEXT, isSpecific: false, assetName: 'Text' },
  { type: CoreElementType.IMAGE, isSpecific: false, assetName: 'Image' },

  // --- Drawing Tool Defaults (User Requested - Right Side of Collapsed Bar, Rendered Center-to-Right) ---
  { type: CoreElementType.LINE, isSpecific: false, assetName: 'Line' },
  { type: CoreElementType.POLYLINE, isSpecific: false, assetName: 'Polyline' },
  { type: CoreElementType.ARC, isSpecific: false, assetName: 'Arc' },
  { type: CoreElementType.QUADRATIC, isSpecific: false, assetName: 'Quadratic' },
]
