/**
 * Computation Strategies Index
 *
 * @remarks
 * This module serves as a barrel export for all computation strategy categories
 * within the `strategies` directory. It re-exports all modules from subdirectories
 * such as `area`, `bounding-box`, `cost`, `distance`, `material`, `perimeter`,
 * and `space`. This allows for a centralized import point for accessing various
 * computation strategy implementations.
 *
 * @module core/compute/strategies/index
 */

// Export all strategy categories
export * from './area'
export * from './bounding-box'
export * from './cost'
export * from './distance'
export * from './material'
export * from './perimeter'
export * from './space'
