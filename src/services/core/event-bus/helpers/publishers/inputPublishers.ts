/**
 * Input Event Publishers Module
 *
 * This module provides helper functions for publishing input-related events
 * to the application's event bus. It covers keyboard events (key pressed, released)
 * and canvas mouse events (click, move, down, up).
 *
 * All functions follow dependency injection principles by accepting an EventBus
 * instance as their first parameter.
 *
 * @module services/core/event-bus/helpers/publishers/inputPublishers
 */

import type { CanvasMouseMoveEvent, EventBus } from '@/types/services/events'
import type {
  CanvasClickEvent,
  CanvasMouseDownEvent,
  CanvasMouseEventPayload,
  CanvasMouseUpEvent,
} from '@/types/services/events/eventRegistry'
import type {
  KeyModifiers,
  KeyPressedEvent,
  KeyReleasedEvent,
} from '@/types/services/events/keyboardEvents'
import { AppEventType } from '@/types/services/events' // Added AppEventMap

// --- Keyboard Events ---

/**
 * Publish key pressed event
 *
 * Notifies the system that a keyboard key has been pressed.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param key - The key value (e.g., "a", "Enter")
 * @param code - Key code (e.g., "KeyA", "Enter")
 * @param modifiers - Object containing state of modifier keys
 * @param originalEvent - Optional reference to the original keyboard event
 */
export function publishKeyPressed(
  eventBus: EventBus,
  key: string,
  code: string,
  modifiers: KeyModifiers,
  originalEvent?: KeyboardEvent,
): void {
  const event: KeyPressedEvent = {
    type: AppEventType.KeyPressed,
    payload: {
      key,
      code,
      modifiers,
      originalEvent,
    },
  }
  eventBus.publish(event)
}

/**
 * Publish key released event
 *
 * Notifies the system that a keyboard key has been released.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param key - The key value (e.g., "a", "Enter")
 * @param code - Key code (e.g., "KeyA", "Enter")
 * @param modifiers - Object containing state of modifier keys
 * @param originalEvent - Optional reference to the original keyboard event
 */
export function publishKeyReleased(
  eventBus: EventBus,
  key: string,
  code: string,
  modifiers: KeyModifiers,
  originalEvent?: KeyboardEvent,
): void {
  const event: KeyReleasedEvent = {
    type: AppEventType.KeyReleased,
    payload: {
      key,
      code,
      modifiers,
      originalEvent,
    },
  }
  eventBus.publish(event)
}

// --- Canvas Mouse Events ---

/**
 * Publish canvas clicked event
 *
 * Notifies the system that the canvas has been clicked.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param options - Object containing position and other click details
 */
export function publishCanvasClicked(eventBus: EventBus, options: CanvasMouseEventPayload): void {
  const event: CanvasClickEvent = {
    type: AppEventType.CanvasClicked,
    payload: options,
  }
  eventBus.publish(event)
}

/**
 * Publish canvas mouse down event
 *
 * Notifies the system that a mouse button has been pressed on the canvas.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param options - Object containing position and button information
 */
export function publishCanvasMouseDown(eventBus: EventBus, options: CanvasMouseEventPayload): void {
  const event: CanvasMouseDownEvent = {
    type: AppEventType.CanvasMouseDown,
    payload: options,
  }
  eventBus.publish(event)
}

/**
 * Publish canvas mouse move event
 *
 * Notifies the system that the mouse has moved over the canvas.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param x - X coordinate in canvas space
 * @param y - Y coordinate in canvas space
 */
export function publishCanvasMouseMove(eventBus: EventBus, x: number, y: number): void {
  const event: CanvasMouseMoveEvent = {
    type: AppEventType.CanvasMouseMove,
    payload: { x, y },
  }
  eventBus.publish(event)
}

/**
 * Publish canvas mouse up event
 *
 * Notifies the system that a mouse button has been released on the canvas.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param options - Object containing position and button information
 */
export function publishCanvasMouseUp(eventBus: EventBus, options: CanvasMouseEventPayload): void {
  const event: CanvasMouseUpEvent = {
    type: AppEventType.CanvasMouseUp,
    payload: options,
  }
  eventBus.publish(event)
}
