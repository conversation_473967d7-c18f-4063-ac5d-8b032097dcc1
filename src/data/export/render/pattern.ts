/**
 * Utility functions for SVG texture (pattern) generation.
 *
 * This module provides generators for SVG fill textures, including lines, circles, and paths.
 * Each texture type is implemented as a pattern definition for use in SVG fills.
 *
 * @module render/pattern
 */
import type { PatternDefinition, TextureLinesOptions } from '@/types/core'
import { PatternType } from '@/types/core'

type PatternRenderer = (definition: PatternDefinition, id: string) => string

// ========== lines ==========
function renderLine(id: string, opts: TextureLinesOptions, lineAttr: string): string {
  const s = opts.size ?? 10
  const stroke = opts.color ?? '#000'
  const strokeWidth = opts.strokeWidth ?? 1
  const shapeRendering = opts.shapeRendering ?? 'auto'
  const background = opts.background ?? 'transparent'

  return `
<pattern id="${id}" patternUnits="userSpaceOnUse" width="${s}" height="${s}">
  <rect width="${s}" height="${s}" fill="${background}" />
  <line ${lineAttr} stroke="${stroke}" stroke-width="${strokeWidth}" shape-rendering="${shapeRendering}" />
</pattern>`.trim()
}

const lineMap: Record<string, PatternRenderer> = {
  horizontal(def, id) {
    const opts = def.linesOptions
    if (!opts)
      return ''
    const y = (opts.size ?? 10) / 2
    return renderLine(id, opts, `x1="0" y1="${y}" x2="${opts.size ?? 10}" y2="${y}"`)
  },
  vertical(def, id) {
    const opts = def.linesOptions
    if (!opts)
      return ''
    const x = (opts.size ?? 10) / 2
    return renderLine(id, opts, `x1="${x}" y1="0" x2="${x}" y2="${opts.size ?? 10}"`)
  },
  diagonal(def, id) {
    const opts = def.linesOptions
    if (!opts)
      return ''
    const s = opts.size ?? 10
    return renderLine(id, opts, `x1="0" y1="0" x2="${s}" y2="${s}"`)
  },
  'diagonal-sw-ne': function (def, id) {
    const opts = def.linesOptions
    if (!opts)
      return ''
    const s = opts.size ?? 10
    return renderLine(id, opts, `x1="0" y1="${s}" x2="${s}" y2="0"`)
  },
  radial(def, id) {
    const opts = def.linesOptions
    if (!opts)
      return ''
    const s = opts.size ?? 10
    const cx = s / 2; const cy = s / 2
    const stroke = opts.color ?? '#000'
    const strokeWidth = opts.strokeWidth ?? 1
    const shapeRendering = opts.shapeRendering ?? 'auto'
    const background = opts.background ?? 'transparent'

    const lines = [
      `x1="${cx}" y1="0" x2="${cx}" y2="${s}"`,
      `x1="0" y1="${cy}" x2="${s}" y2="${cy}"`,
      `x1="0" y1="0" x2="${s}" y2="${s}"`,
      `x1="0" y1="${s}" x2="${s}" y2="0"`,
    ]

    return `
<pattern id="${id}" patternUnits="userSpaceOnUse" width="${s}" height="${s}">
  <rect width="${s}" height="${s}" fill="${background}" />
  ${lines.map(l =>
    `<line ${l} stroke="${stroke}" stroke-width="${strokeWidth}" shape-rendering="${shapeRendering}" />`,
  ).join('\n  ')}
</pattern>`.trim()
  },
}

// ========== circles ==========

function renderCircle(def: PatternDefinition, id: string): string {
  const opts = def.circlesOptions
  if (!opts)
    return ''

  const size = opts.size ?? 20
  const radius = opts.radius ?? 4
  const cx = size / 2
  const cy = size / 2

  const fill = opts.fill ?? '#000'
  const stroke = opts.stroke ?? 'none'
  const shapeRendering = opts.shapeRendering ?? 'auto'
  const background = opts.background ?? 'transparent'

  return `
<pattern id="${id}" patternUnits="userSpaceOnUse" width="${size}" height="${size}">
  <rect width="${size}" height="${size}" fill="${background}" />
  <circle cx="${cx}" cy="${cy}" r="${radius}" fill="${fill}" stroke="${stroke}" shape-rendering="${shapeRendering}" />
</pattern>`.trim()
}

// ========== paths ==========

const pathMap: Record<string, string> = {
  squares: 'M1 1h8v8h-8z',
  waves: 'M0 5 Q 2.5 0 5 5 T 10 5',
  woven: 'M0 0 H10 V10 H0 Z M2 2 H8 V8 H2 Z',
  crosses: 'M4 0h2v4h4v2h-4v4h-2v-4h-4v-2h4z',
  caps: 'M0 5a5 5 0 0 1 10 0v5h-10z',
  hexes: 'M5,0 L10,2.5 L10,7.5 L5,10 L0,7.5 L0,2.5 Z',
}

function renderPath(def: PatternDefinition, id: string): string {
  const opts = def.pathsOptions
  if (!opts)
    return ''

  const size = opts.size ?? 20
  const shapeRendering = opts.shapeRendering ?? 'auto'
  const background = opts.background ?? 'transparent'
  const fill = opts.fill ?? 'black'

  const d = opts.d ?? 'crosses'
  const pathD = pathMap[d]
  if (!pathD)
    return ''

  return `
<pattern id="${id}" patternUnits="userSpaceOnUse" width="${size}" height="${size}">
  <rect width="${size}" height="${size}" fill="${background}" />
  <path d="${pathD}" fill="${fill}" shape-rendering="${shapeRendering}" />
</pattern>`.trim()
}

// ========== dispatch table ==========

const generatorMap: Partial<Record<PatternType, (def: PatternDefinition) => PatternRenderer>> = {
  [PatternType.LINES](def) {
    const orientation = def.linesOptions?.orientation?.[0] ?? 'diagonal'
    return lineMap[orientation] ?? (() => '')
  },
  [PatternType.CIRCLES]() {
    return renderCircle
  },
  [PatternType.PATHS](def) {
    const d = def.pathsOptions?.d ?? 'crosses'
    return pathMap[d] ? renderPath : () => ''
  },
}

// ========== Main entry ==========

export function getPattern(def: PatternDefinition): string {
  const type = def.textureType as PatternType
  const selector = generatorMap[type]
  const render = selector ? selector(def) : () => ''
  return render(def, def.id)
}
