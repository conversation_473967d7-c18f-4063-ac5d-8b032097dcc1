import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ConsoleLoggerService } from '@/services/logging/ConsoleLoggerService'

describe('consoleLoggerService', () => {
  let logger: ConsoleLoggerService
  let originalConsole: any
  let mockConsole: any

  beforeEach(() => {
    // Save original console methods
    originalConsole = {
      log: console.log,
      info: console.info,
      warn: console.warn,
      error: console.error,
      debug: console.debug,
    }

    // Create mock console methods
    mockConsole = {
      log: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    }

    // Replace console methods with mocks
    console.log = mockConsole.log
    console.info = mockConsole.info
    console.warn = mockConsole.warn
    console.error = mockConsole.error
    console.debug = mockConsole.debug

    // Create logger instance
    logger = new ConsoleLoggerService()
  })

  afterEach(() => {
    // Restore original console methods
    console.log = originalConsole.log
    console.info = originalConsole.info
    console.warn = originalConsole.warn
    console.error = originalConsole.error
    console.debug = originalConsole.debug
  })

  describe('info method', () => {
    it('should log info messages with correct prefix', () => {
      logger.info('Test info message')

      expect(mockConsole.info).toHaveBeenCalledWith('[INFO] Test info message')
    })

    it('should handle multiple arguments', () => {
      const userData = { id: 123, name: 'Test User' }
      logger.info('User logged in', userData)

      expect(mockConsole.info).toHaveBeenCalledWith('[INFO] User logged in', userData)
    })
  })

  describe('warn method', () => {
    it('should log warning messages with correct prefix', () => {
      logger.warn('Test warning message')

      expect(mockConsole.warn).toHaveBeenCalledWith('[WARN] Test warning message')
    })

    it('should handle multiple arguments', () => {
      const warningData = { code: 'RATE_LIMIT', threshold: 100 }
      logger.warn('Rate limit approaching', warningData)

      expect(mockConsole.warn).toHaveBeenCalledWith('[WARN] Rate limit approaching', warningData)
    })
  })

  describe('error method', () => {
    it('should log error messages with correct prefix', () => {
      logger.error('Test error message')

      expect(mockConsole.error).toHaveBeenCalledWith('[ERROR] Test error message')
    })

    it('should handle error objects', () => {
      const error = new Error('Something went wrong')
      logger.error('Operation failed', error)

      expect(mockConsole.error).toHaveBeenCalledWith('[ERROR] Operation failed', error)
    })

    it('should handle multiple arguments', () => {
      const errorData = { code: 'API_ERROR', status: 500 }
      logger.error('API request failed', errorData)

      expect(mockConsole.error).toHaveBeenCalledWith('[ERROR] API request failed', errorData)
    })
  })

  describe('debug method', () => {
    it('should log debug messages with correct prefix', () => {
      logger.debug('Test debug message')

      expect(mockConsole.debug).toHaveBeenCalledWith('[DEBUG] Test debug message')
    })

    it('should handle complex objects', () => {
      const debugData = {
        component: 'ShapeEditor',
        action: 'resize',
        dimensions: { width: 100, height: 200 },
      }

      logger.debug('Shape operation details', debugData)

      expect(mockConsole.debug).toHaveBeenCalledWith('[DEBUG] Shape operation details', debugData)
    })
  })

  describe('implementation', () => {
    it('should implement LoggerService interface', () => {
      // This is a type check that verifies ConsoleLoggerService implements LoggerService
      const loggerService: LoggerService = logger

      // Verify the required methods exist
      expect(typeof loggerService.info).toBe('function')
      expect(typeof loggerService.warn).toBe('function')
      expect(typeof loggerService.error).toBe('function')
      expect(typeof loggerService.debug).toBe('function')
    })
  })
})
