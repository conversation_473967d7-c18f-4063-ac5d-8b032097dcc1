import type { ShapeElement } from '@/types/core/elementDefinitions'
import type { EventBus } from '@/types/services/events'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  publishDataUpdated,
  publishRenderTrigger,
} from '@/services/core/event-bus/helpers/publishers/renderPublishers'
import { ElementType } from '@/types/core/elementDefinitions'
import { AppEventType } from '@/types/services/events'

describe('render Publishers', () => {
  let mockEventBus: EventBus
  let publishSpy: ReturnType<typeof vi.fn>

  beforeEach(() => {
    publishSpy = vi.fn()
    mockEventBus = {
      publish: publishSpy,
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
      clear: vi.fn(),
    } as unknown as EventBus
  })

  // Helper function to create test shapes
  const createTestShape = (id: string, type: ElementType = ElementType.RECTANGLE): ShapeElement => ({
    id,
    type,
    position: { x: 100, y: 200, z: 0 },
    properties: {
      width: 150,
      height: 100,
      fill: '#ff0000',
    },
  })

  describe('publishRenderTrigger', () => {
    it('should publish render trigger event with default reason', () => {
      const shapes = [
        createTestShape('shape-1'),
        createTestShape('shape-2', ElementType.CIRCLE),
      ]

      publishRenderTrigger(mockEventBus, shapes)

      expect(publishSpy).toHaveBeenCalledTimes(1)
      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.RenderTrigger,
        payload: {
          options: {
            elementIds: ['shape-1', 'shape-2'],
            highPerformance: false,
          },
        },
      })
    })

    it('should publish render trigger event with custom reason', () => {
      const shapes = [createTestShape('shape-1')]
      const reason = 'shape_updated'

      publishRenderTrigger(mockEventBus, shapes, reason)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.RenderTrigger,
        payload: {
          options: {
            elementIds: ['shape-1'],
            highPerformance: false,
          },
        },
      })
    })

    it('should enable high performance mode for dragging', () => {
      const shapes = [createTestShape('shape-1')]
      const reason = 'dragging'

      publishRenderTrigger(mockEventBus, shapes, reason)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.RenderTrigger,
        payload: {
          options: {
            elementIds: ['shape-1'],
            highPerformance: true,
          },
        },
      })
    })

    it('should handle empty shapes array', () => {
      publishRenderTrigger(mockEventBus, [])

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.RenderTrigger,
        payload: {
          options: {
            elementIds: [],
            highPerformance: false,
          },
        },
      })
    })

    it('should handle single shape', () => {
      const shape = createTestShape('single-shape')

      publishRenderTrigger(mockEventBus, [shape])

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.RenderTrigger,
        payload: {
          options: {
            elementIds: ['single-shape'],
            highPerformance: false,
          },
        },
      })
    })

    it('should handle multiple shapes of different types', () => {
      const shapes = [
        createTestShape('rect-1', ElementType.RECTANGLE),
        createTestShape('circle-1', ElementType.CIRCLE),
        createTestShape('line-1', ElementType.LINE),
        createTestShape('polygon-1', ElementType.POLYGON),
      ]

      publishRenderTrigger(mockEventBus, shapes)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.RenderTrigger,
        payload: {
          options: {
            elementIds: ['rect-1', 'circle-1', 'line-1', 'polygon-1'],
            highPerformance: false,
          },
        },
      })
    })

    it('should handle shapes with special character IDs', () => {
      const shapes = [
        createTestShape('shape-@#$%^&*()'),
        createTestShape('shape_with_underscores'),
        createTestShape('shape-with-dashes'),
        createTestShape('shape.with.dots'),
      ]

      publishRenderTrigger(mockEventBus, shapes)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.RenderTrigger,
        payload: {
          options: {
            elementIds: [
              'shape-@#$%^&*()',
              'shape_with_underscores',
              'shape-with-dashes',
              'shape.with.dots',
            ],
            highPerformance: false,
          },
        },
      })
    })

    it('should handle shapes with unicode IDs', () => {
      const shapes = [
        createTestShape('形状-1'),
        createTestShape('shape-🚀'),
        createTestShape('αβγ-shape'),
      ]

      publishRenderTrigger(mockEventBus, shapes)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.RenderTrigger,
        payload: {
          options: {
            elementIds: ['形状-1', 'shape-🚀', 'αβγ-shape'],
            highPerformance: false,
          },
        },
      })
    })

    it('should handle different reasons correctly', () => {
      const shape = createTestShape('test-shape')
      const reasons = [
        { reason: 'manual_trigger', expectedHighPerf: false },
        { reason: 'dragging', expectedHighPerf: true },
        { reason: 'shape_created', expectedHighPerf: false },
        { reason: 'shape_updated', expectedHighPerf: false },
        { reason: 'view_changed', expectedHighPerf: false },
      ]

      reasons.forEach(({ reason, expectedHighPerf }) => {
        publishRenderTrigger(mockEventBus, [shape], reason)
      })

      expect(publishSpy).toHaveBeenCalledTimes(5)
      reasons.forEach(({ expectedHighPerf }, index) => {
        expect(publishSpy.mock.calls[index][0].payload.options.highPerformance).toBe(expectedHighPerf)
      })
    })

    it('should handle very large arrays of shapes', () => {
      const shapes = Array.from({ length: 1000 }, (_, i) => createTestShape(`shape-${i}`))

      publishRenderTrigger(mockEventBus, shapes)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.RenderTrigger,
        payload: {
          options: {
            elementIds: shapes.map(s => s.id),
            highPerformance: false,
          },
        },
      })
    })
  })

  describe('publishDataUpdated', () => {
    it('should publish data updated event with correct structure', () => {
      const shapes = [
        createTestShape('shape-1'),
        createTestShape('shape-2', ElementType.CIRCLE),
      ]

      publishDataUpdated(mockEventBus, shapes)

      expect(publishSpy).toHaveBeenCalledTimes(1)
      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.DataUpdated,
        payload: {
          type: 'shapes',
          data: shapes,
        },
      })
    })

    it('should handle empty shapes array', () => {
      publishDataUpdated(mockEventBus, [])

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.DataUpdated,
        payload: {
          type: 'shapes',
          data: [],
        },
      })
    })

    it('should handle single shape', () => {
      const shape = createTestShape('single-shape')

      publishDataUpdated(mockEventBus, [shape])

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.DataUpdated,
        payload: {
          type: 'shapes',
          data: [shape],
        },
      })
    })

    it('should handle multiple shapes with different properties', () => {
      const shapes = [
        {
          id: 'rect-1',
          type: ElementType.RECTANGLE,
          position: { x: 0, y: 0, z: 0 },
          properties: { width: 100, height: 50, fill: '#ff0000' },
        },
        {
          id: 'circle-1',
          type: ElementType.CIRCLE,
          position: { x: 200, y: 300, z: 1 },
          properties: { radius: 75, fill: '#00ff00', stroke: '#000000' },
        },
        {
          id: 'line-1',
          type: ElementType.LINE,
          position: { x: 50, y: 100, z: 2 },
          properties: {
            startPoint: { x: 0, y: 0 },
            endPoint: { x: 100, y: 100 },
            stroke: '#0000ff',
          },
        },
      ] as ShapeElement[]

      publishDataUpdated(mockEventBus, shapes)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.DataUpdated,
        payload: {
          type: 'shapes',
          data: shapes,
        },
      })
    })

    it('should preserve shape data integrity', () => {
      const originalShape = createTestShape('test-shape')
      const shapeCopy = { ...originalShape }

      publishDataUpdated(mockEventBus, [originalShape])

      // Verify the original shape wasn't modified
      expect(originalShape).toEqual(shapeCopy)

      // Verify the event contains the shape data
      const publishedData = publishSpy.mock.calls[0][0].payload.data
      expect(publishedData).toEqual([originalShape])
    })

    it('should handle shapes with complex properties', () => {
      const complexShape: ShapeElement = {
        id: 'complex-shape',
        type: ElementType.POLYGON,
        position: { x: 100, y: 200, z: 0 },
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 100, y: 0 },
            { x: 100, y: 100 },
            { x: 0, y: 100 },
          ],
          fill: '#ff0000',
          stroke: '#000000',
          strokeWidth: 2,
          opacity: 0.8,
          metadata: {
            created: new Date().toISOString(),
            author: 'test-user',
            tags: ['test', 'polygon'],
          },
        },
      }

      publishDataUpdated(mockEventBus, [complexShape])

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.DataUpdated,
        payload: {
          type: 'shapes',
          data: [complexShape],
        },
      })
    })

    it('should handle very large arrays of shapes', () => {
      const shapes = Array.from({ length: 500 }, (_, i) => createTestShape(`shape-${i}`))

      publishDataUpdated(mockEventBus, shapes)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.DataUpdated,
        payload: {
          type: 'shapes',
          data: shapes,
        },
      })
    })
  })

  describe('error handling', () => {
    it('should propagate eventBus errors for all publishers', () => {
      const error = new Error('EventBus publish failed')
      publishSpy.mockImplementation(() => {
        throw error
      })

      const shapes = [createTestShape('test-shape')]

      expect(() => publishRenderTrigger(mockEventBus, shapes)).toThrow(error)
      expect(() => publishDataUpdated(mockEventBus, shapes)).toThrow(error)
    })

    it('should handle eventBus returning false', () => {
      publishSpy.mockReturnValue(false)

      const shapes = [createTestShape('test-shape')]

      // These should not throw even if eventBus returns false
      expect(() => publishRenderTrigger(mockEventBus, shapes)).not.toThrow()
      expect(() => publishDataUpdated(mockEventBus, shapes)).not.toThrow()
    })
  })

  describe('edge cases', () => {
    it('should handle rapid successive calls', () => {
      const shape = createTestShape('test-shape')

      for (let i = 0; i < 50; i++) {
        publishRenderTrigger(mockEventBus, [shape])
        publishDataUpdated(mockEventBus, [shape])
      }

      expect(publishSpy).toHaveBeenCalledTimes(100)
    })

    it('should handle mixed publisher calls', () => {
      const shapes = [createTestShape('shape-1'), createTestShape('shape-2')]

      publishRenderTrigger(mockEventBus, shapes, 'manual_trigger')
      publishDataUpdated(mockEventBus, shapes)
      publishRenderTrigger(mockEventBus, shapes, 'dragging')

      expect(publishSpy).toHaveBeenCalledTimes(3)
      expect(publishSpy.mock.calls[0][0].type).toBe(AppEventType.RenderTrigger)
      expect(publishSpy.mock.calls[1][0].type).toBe(AppEventType.DataUpdated)
      expect(publishSpy.mock.calls[2][0].type).toBe(AppEventType.RenderTrigger)
      expect(publishSpy.mock.calls[2][0].payload.options.highPerformance).toBe(true)
    })

    it('should handle shapes with duplicate IDs', () => {
      const shapes = [
        createTestShape('duplicate-id'),
        createTestShape('duplicate-id'),
        createTestShape('unique-id'),
      ]

      publishRenderTrigger(mockEventBus, shapes)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.RenderTrigger,
        payload: {
          options: {
            elementIds: ['duplicate-id', 'duplicate-id', 'unique-id'],
            highPerformance: false,
          },
        },
      })
    })
  })
})
