/**
 * Contains validation logic specific to Polyline shapes.
 */

import type { ValidatableShape, ValidationError } from '@/types/core/validator/validator-interface' // Corrected
import { ElementType } from '@/types/core/elementDefinitions' // Import ElementType only
import { ValidationErrorCode } from '@/types/core/validator/error-codes' // Corrected
import { isValidPoint } from '../common/validationUtils'
import { BaseShapeValidator } from '../common/validatorBase'

/**
 * Concrete validator implementation for Polyline shapes.
 *
 * @extends BaseShapeValidator
 */
export class PolylineValidator extends BaseShapeValidator {
  /**
   * Validates properties specific to a Polyline element data structure.
   *
   * @param shape - The shape to validate.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected validateSpecific(shape: ValidatableShape): ValidationError[] {
    const errors: ValidationError[] = []

    if (shape.type !== ElementType.POLYLINE) {
      errors.push({
        code: ValidationErrorCode.INVALID_SHAPE_TYPE,
        message: `Internal Error: PolylineValidator received shape type ${shape.type}`,
        path: 'type',
      })
      return errors
    }

    const props = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined || {}
    const points = props.points as { x: number, y: number }[] | undefined // Assuming points is an array of point-like objects
    const curved = props.curved as boolean | undefined
    const tension = props.tension as number | undefined

    // Validate points array
    if (points == null || !Array.isArray(points)) {
      errors.push({
        code: ValidationErrorCode.MISSING_PROPERTY,
        message: 'Polyline requires a points array in its properties.',
        path: 'properties.points',
        value: points,
      })
    }
    else if (points.length < 2) {
      errors.push({
        code: ValidationErrorCode.INSUFFICIENT_POINTS,
        message: 'Polyline requires at least 2 points in its properties.',
        path: 'properties.points',
        value: points.length,
      })
    }
    else {
      // Check each point for validity
      for (let i = 0; i < points.length; i++) {
        const point = points[i]
        if (!isValidPoint(point)) {
          errors.push({
            code: ValidationErrorCode.INVALID_POINT,
            message: `Invalid point coordinates at index ${i} in properties.points. Received: ${JSON.stringify(points[i])}`,
            path: `properties.points[${i}]`,
            value: points[i],
          })
        }
      }
    }

    // Validate curved property from props
    if (typeof curved !== 'boolean') {
      errors.push({
        code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
        message: `Polyline 'curved' property must be a boolean. Received: ${curved}`,
        path: 'properties.curved',
        value: curved,
      })
    }

    // Validate tension property from props
    if (typeof tension !== 'number' || !Number.isFinite(tension)) {
      errors.push({
        code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
        message: `Polyline 'tension' property must be a finite number. Received: ${tension}`,
        path: 'properties.tension',
        value: tension,
      })
    }
    else if (tension < 0 || tension > 1) {
      // Assuming tension should be within [0, 1] based on common usage
      errors.push({
        code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
        message: `Polyline 'tension' property must be between 0 and 1. Received: ${tension}`,
        path: 'properties.tension',
        value: tension,
      })
    }

    return errors
  }

  /**
   * Applies polyline-specific business rules.
   *
   * @param _shape - The shape to validate against business rules.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected applySpecificRules(_shape: ValidatableShape): ValidationError[] {
    // No polyline-specific business rules currently identified beyond common ones.
    return []
  }
}
