import type {
  <PERSON>rror<PERSON>ontext,
  ICompute<PERSON>rror,
  ICoreError,
  IFactoryError,
  IValidationResultError,
  IValidatorError,
} from '@/types/services/errors'
import { describe, expect, it } from 'vitest'
import {
  ErrorSeverity,
  ErrorType,
} from '@/types/services/errors'

describe('error Types', () => {
  describe('errorType', () => {
    it('should define all expected error types', () => {
      expect(ErrorType.VALIDATION).toBeDefined()
      expect(ErrorType.RUNTIME).toBeDefined()
      expect(ErrorType.NETWORK).toBeDefined()
      expect(ErrorType.FATAL).toBeDefined()
      expect(ErrorType.WARNING).toBeDefined()
      expect(ErrorType.INFO).toBeDefined()
      expect(ErrorType.INVALID_PARAMETER).toBeDefined()
      expect(ErrorType.INVALID_ELEMENT_TYPE).toBeDefined()
      expect(ErrorType.COMPUTATION_ERROR).toBeDefined()
      expect(ErrorType.CONFIGURATION).toBeDefined()
      expect(ErrorType.COORDINATOR_SHAPE_NOT_FOUND).toBeDefined()
      expect(ErrorType.COORDINATOR_OPERATION_FAILED).toBeDefined()
      expect(ErrorType.FACTORY_FAILED).toBeDefined()
      expect(ErrorType.INVALID_PAYLOAD).toBeDefined()
      expect(ErrorType.COMPUTE_BOUNDS_ERROR).toBeDefined()
    })

    it('should have the correct string values', () => {
      expect(ErrorType.VALIDATION).toBe('VALIDATION')
      expect(ErrorType.RUNTIME).toBe('RUNTIME')
      expect(ErrorType.NETWORK).toBe('NETWORK')
      expect(ErrorType.FATAL).toBe('FATAL')
      expect(ErrorType.WARNING).toBe('WARNING')
      expect(ErrorType.INFO).toBe('INFO')
      expect(ErrorType.INVALID_PARAMETER).toBe('INVALID_PARAMETER')
      expect(ErrorType.INVALID_ELEMENT_TYPE).toBe('INVALID_ELEMENT_TYPE')
      expect(ErrorType.COMPUTATION_ERROR).toBe('COMPUTATION_ERROR')
      expect(ErrorType.CONFIGURATION).toBe('CONFIGURATION')
      expect(ErrorType.COORDINATOR_SHAPE_NOT_FOUND).toBe('COORDINATOR_SHAPE_NOT_FOUND')
      expect(ErrorType.COORDINATOR_OPERATION_FAILED).toBe('COORDINATOR_OPERATION_FAILED')
      expect(ErrorType.FACTORY_FAILED).toBe('FACTORY_FAILED')
      expect(ErrorType.INVALID_PAYLOAD).toBe('INVALID_PAYLOAD')
      expect(ErrorType.COMPUTE_BOUNDS_ERROR).toBe('COMPUTE_BOUNDS_ERROR')
    })
  })

  describe('errorSeverity', () => {
    it('should define all expected severity levels', () => {
      expect(ErrorSeverity.LOW).toBeDefined()
      expect(ErrorSeverity.MEDIUM).toBeDefined()
      expect(ErrorSeverity.HIGH).toBeDefined()
      expect(ErrorSeverity.CRITICAL).toBeDefined()
    })

    it('should have the correct string values', () => {
      expect(ErrorSeverity.LOW).toBe('LOW')
      expect(ErrorSeverity.MEDIUM).toBe('MEDIUM')
      expect(ErrorSeverity.HIGH).toBe('HIGH')
      expect(ErrorSeverity.CRITICAL).toBe('CRITICAL')
    })
  })

  describe('errorContext', () => {
    it('should allow creating a context with all properties', () => {
      const context: ErrorContext = {
        component: 'TestComponent',
        operation: 'testOperation',
        target: 'testTarget',
        metadata: { key: 'value', count: 42 },
        recoverable: true,
        timestamp: Date.now(),
      }

      expect(context.component).toBe('TestComponent')
      expect(context.operation).toBe('testOperation')
      expect(context.target).toBe('testTarget')
      expect(context.metadata?.key).toBe('value')
      expect(context.metadata?.count).toBe(42)
      expect(context.recoverable).toBe(true)
      expect(context.timestamp).toBeGreaterThan(0)
    })

    it('should allow creating a context with minimal properties', () => {
      const minimalContext: ErrorContext = {
        component: 'MinimalComponent',
      }

      expect(minimalContext.component).toBe('MinimalComponent')
      expect(minimalContext.operation).toBeUndefined()
      expect(minimalContext.target).toBeUndefined()
      expect(minimalContext.metadata).toBeUndefined()
      expect(minimalContext.recoverable).toBeUndefined()
      expect(minimalContext.timestamp).toBeUndefined()
    })
  })

  describe('iCoreError', () => {
    it('should allow creating a core error', () => {
      // Create a mock implementation of ICoreError
      const error: ICoreError = {
        name: 'TestError',
        message: 'Test error message',
        type: ErrorType.RUNTIME,
        severity: ErrorSeverity.MEDIUM,
        context: {
          component: 'TestComponent',
          operation: 'testOperation',
        },
      }

      expect(error.name).toBe('TestError')
      expect(error.message).toBe('Test error message')
      expect(error.type).toBe(ErrorType.RUNTIME)
      expect(error.severity).toBe(ErrorSeverity.MEDIUM)
      expect(error.context?.component).toBe('TestComponent')
    })
  })

  describe('specialized Error Interfaces', () => {
    it('should allow creating specialized error types', () => {
      // Factory Error
      const factoryError: IFactoryError = {
        name: 'FactoryError',
        message: 'Failed to create shape',
        type: ErrorType.FACTORY_FAILED,
        severity: ErrorSeverity.HIGH,
        context: {
          component: 'ShapeFactory',
          operation: 'createRectangle',
        },
      }
      expect(factoryError.type).toBe(ErrorType.FACTORY_FAILED)

      // Validator Error
      const validatorError: IValidatorError = {
        name: 'ValidatorError',
        message: 'Invalid shape properties',
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.MEDIUM,
        context: {
          component: 'ShapeValidator',
          operation: 'validateRectangle',
        },
      }
      expect(validatorError.type).toBe(ErrorType.VALIDATION)

      // Compute Error
      const computeError: IComputeError = {
        name: 'ComputeError',
        message: 'Failed to compute area',
        type: ErrorType.COMPUTATION_ERROR,
        severity: ErrorSeverity.MEDIUM,
        context: {
          component: 'AreaComputer',
          operation: 'computeArea',
        },
      }
      expect(computeError.type).toBe(ErrorType.COMPUTATION_ERROR)
    })

    it('should allow creating validation result error with validation errors', () => {
      const validationResultError: IValidationResultError = {
        name: 'ValidationResultError',
        message: 'Multiple validation errors',
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.HIGH,
        validationErrors: [
          { field: 'width', message: 'Width must be positive' },
          { field: 'height', message: 'Height must be positive' },
        ],
        context: {
          component: 'ShapeValidator',
          operation: 'validateRectangle',
        },
      }

      expect(validationResultError.type).toBe(ErrorType.VALIDATION)
      expect(validationResultError.validationErrors).toHaveLength(2)
      expect(validationResultError.validationErrors[0].field).toBe('width')
      expect(validationResultError.validationErrors[1].field).toBe('height')
    })
  })
})
