import { describe, expect, it } from 'vitest'
import * as validatorIndex from '@/core/validator'
import {
  isPolygonClosed,
  isValidColor,
  isValidPoint,
  isValueInRange,
} from '@/core/validator/common/validationUtils'
import { BaseShapeValidator } from '@/core/validator/common/validatorBase'
// Import individual validators
// CircleValidator has been merged into EllipseValidator
import { EllipseValidator } from '@/core/validator/elements/ellipseValidator'
import { LineValidator } from '@/core/validator/elements/lineValidator'
import { PolygonValidator } from '@/core/validator/elements/polygonValidator'
import { PolylineValidator } from '@/core/validator/elements/polylineValidator'
import { RectangleValidator } from '@/core/validator/elements/rectangleValidator'
import { ElementValidator } from '@/core/validator/ElementValidator'
import {
  applyBusinessRules,
  idRule,
  minSizeRule,
  pointCountRule,
  polygonClosureRule,
  sizeRule,
} from '@/core/validator/rules/businessRules'
import { createValidationRule } from '@/core/validator/rules/ruleInterface'

describe('validator Index', () => {
  it('should export ElementValidator', () => {
    expect(validatorIndex.ElementValidator).toBe(ElementValidator)
  })

  it('should export ElementValidator as ShapeValidator for backward compatibility', () => {
    expect(validatorIndex.ShapeValidator).toBe(ElementValidator)
  })

  it('should export BaseShapeValidator', () => {
    expect(validatorIndex.BaseShapeValidator).toBe(BaseShapeValidator)
  })

  it('should export business rules', () => {
    expect(validatorIndex.idRule).toBe(idRule)
    expect(validatorIndex.sizeRule).toBe(sizeRule)
    expect(validatorIndex.minSizeRule).toBe(minSizeRule)
    expect(validatorIndex.pointCountRule).toBe(pointCountRule)
    expect(validatorIndex.polygonClosureRule).toBe(polygonClosureRule)
    expect(validatorIndex.applyBusinessRules).toBe(applyBusinessRules)
  })

  it('should export validation utility functions', () => {
    expect(validatorIndex.isValidPoint).toBe(isValidPoint)
    expect(validatorIndex.isValidColor).toBe(isValidColor)
    expect(validatorIndex.isPolygonClosed).toBe(isPolygonClosed)
    expect(validatorIndex.isValueInRange).toBe(isValueInRange)
  })

  it('should export createValidationRule factory function', () => {
    expect(validatorIndex.createValidationRule).toBe(createValidationRule)
  })

  it('should export concrete element validator classes', () => {
    // CircleValidator has been merged into EllipseValidator
    expect(validatorIndex.EllipseValidator).toBe(EllipseValidator)
    expect(validatorIndex.LineValidator).toBe(LineValidator)
    expect(validatorIndex.PolygonValidator).toBe(PolygonValidator)
    expect(validatorIndex.PolylineValidator).toBe(PolylineValidator)
    expect(validatorIndex.RectangleValidator).toBe(RectangleValidator)
  })

  it('should re-export validation result types', () => {
    // These are type exports, so we can't directly test them
    // But we can verify the module structure is as expected
    expect(typeof validatorIndex).toBe('object')
    expect(Object.keys(validatorIndex).length).toBeGreaterThanOrEqual(15)
  })
})
