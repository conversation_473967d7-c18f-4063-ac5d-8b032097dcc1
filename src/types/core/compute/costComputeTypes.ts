/**
 * Core Type Definitions for Cost Calculation Strategies
 *
 * @remarks
 * This module defines the interfaces and types necessary for implementing cost
 * calculation strategies within the application. It follows the strategy design
 * pattern to allow for flexible and interchangeable costing algorithms based on
 * element type, calculation basis (e.g., area, unit), and other configurable options.
 *
 * Key definitions include:
 * - {@link CostCalculationOptions}: An interface for specifying detailed parameters
 *   that influence cost calculation, such as cost type, unit costs, rates, and
 *   various adjustment factors.
 * - {@link CostCalculatorStrategy}: The core strategy interface that all concrete
 *   cost calculation strategies must implement.
 *
 * @module types/core/compute/costComputeTypes
 * @see {@link Element} from '@/types/core/elementDefinitions'
 * @see {@link ../computeInterfaces} for other computation strategy interfaces.
 */
import type { Element } from '@/types/core/elementDefinitions'

/**
 * Defines a comprehensive set of configurable parameters for cost calculation operations.
 *
 * @remarks
 * This interface is utilized by {@link CostCalculatorStrategy} implementations to allow
 * for detailed customization of how costs are determined for various {@link Element | elements}.
 * It supports different costing methodologies (e.g., by area, per unit) and includes
 * fields for various cost components like materials, labor, overheads, as well as
 * adjustments like discounts and taxes.
 *
 * Specific properties might only be relevant for certain `costType` values or
 * particular strategy implementations.
 */
export interface CostCalculationOptions {
  /**
   * Specifies the basis for cost calculation.
   * - `'area'`: Cost is calculated based on the element's area (e.g., for flooring, paint).
   * - `'perimeter'`: Cost is calculated based on the element's perimeter (e.g., for baseboards, trim).
   * - `'unit'`: Cost is calculated per item or unit (e.g., for light fixtures, appliances).
   * - `'segment'`: Cost is calculated per segment (e.g., for polylines).
   * - `'fixed'`: A fixed base cost is applied, potentially modified by factors.
   */
  costType: 'area' | 'perimeter' | 'unit' | 'segment' | 'fixed'

  /** The cost of materials per defined unit (e.g., per square meter, per meter, per item). */
  materialCostPerUnit?: number

  /** The cost of labor per defined unit or per hour, depending on the `costType`. */
  laborCostPerUnit?: number

  /** The cost associated with design services per unit or as a flat fee. */
  designCostPerUnit?: number

  /** Overhead costs allocated per unit. */
  overheadCostPerUnit?: number

  /** A flat or per-unit cost for installation. */
  installationCost?: number

  /** The number of units, applicable when `costType` is 'unit' or for items with multiple quantities. */
  quantity?: number

  /**
   * A multiplier to apply to the total cost, applicable when `costType` is 'area' or 'perimeter'.
   * This is used to adjust the cost based on factors like material quality, complexity, etc.
   */
  multiplier?: number

  /** Any other additional flat costs (e.g., delivery fees, special permits). */
  additionalCost?: number

  /**
   * The discount rate to be applied to the total or subtotal cost.
   * Represented as a decimal (e.g., 0.1 for 10% discount).
   */
  discountRate?: number

  /**
   * The tax rate to be applied to the cost.
   * Represented as a decimal (e.g., 0.05 for 5% tax).
   */
  taxRate?: number

  // Properties used by CurveCostStrategy
  /** Optional: A factor to adjust cost based on the complexity of a curve. Defaults to 1.0. */
  complexityFactor?: number
  /** Optional: A factor to adjust cost based on the material of a curve. Defaults to 1.0. */
  materialFactor?: number // Note: materialCostPerUnit is already present for general use
  /** Optional: A base cost, used when costType is 'fixed'. */
  baseCost?: number
  /** Optional: A specific cost adjustment factor for arc elements when costType is 'fixed'. */
  arcFactor?: number
  /** Optional: A specific cost adjustment factor for quadratic bezier elements when costType is 'fixed'. */
  quadraticFactor?: number
  /** Optional: A specific cost adjustment factor for cubic bezier elements when costType is 'fixed'. */
  cubicFactor?: number

  // Properties used by PolylineCostStrategy
  /** Optional: A factor to adjust cost based on the number or nature of segments in a polyline. Defaults to 1.0. */
  segmentFactor?: number
  /** Optional: The cost per segment, used when costType is 'segment'. */
  segmentUnitCost?: number
}

/**
 * Defines the contract for a cost calculation strategy.
 *
 * @remarks
 * Any class implementing this interface is responsible for providing a specific
 * algorithm to calculate the cost associated with an {@link Element}. Concrete strategies
 * will typically be specialized for different {@link ElementType | element types} or
 * different costing methodologies (e.g., area-based, unit-based).
 *
 * @example
 * ```typescript
 * class WallCostStrategy implements CostCalculatorStrategy {
 *   public calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
 *     // Example: Calculate cost based on wall area and material
 *     const wall = element as Design.WallElement; // Assuming Design.WallElement type
 *     const area = (wall.properties.length * wall.properties.height) / 1000000; // Example if in mm
 *     let cost = area * (options?.materialCostPerUnit ?? unitCost);
 *     if (options?.taxRate) cost *= (1 + options.taxRate);
 *     return cost;
 *   }
 *
 *   public getElementType(): string { // Or ElementType
 *     return ElementType.WALL;
 *   }
 * }
 * ```
 * @see {@link Element} from '@/types/core/elementDefinitions'
 * @see {@link ElementType} from '@/types/core/elementDefinitions'
 * @see {@link CostCalculationOptions}
 */
export interface CostCalculatorStrategy {
  /**
   * Calculates the cost of a given element.
   *
   * @param element - The {@link Element} for which to calculate the cost.
   * @param unitCost - A base cost per unit. The interpretation of "unit" (e.g., per square meter,
   *                   per linear meter, per item) depends on the strategy and `options.costType`.
   * @param options - Optional {@link CostCalculationOptions} to further customize the calculation,
   *                  such as specifying the `costType`, material costs, labor costs, taxes, etc.
   * @returns The calculated total cost as a `number`.
   */
  calculateCost: (element: Element, unitCost: number, options?: CostCalculationOptions) => number

  /**
   * Gets the specific element type (or types) that this strategy is designed to handle.
   *
   * @returns A string identifier (or an array of string identifiers) corresponding to
   *          one or more {@link ElementType | ElementTypes} that this strategy supports.
   * @remarks Consider returning `ElementType` or `ElementType[]` for enhanced type safety in future revisions.
   */
  getElementType: () => string | string[]
}
