import type { PerimeterCalculatorStrategy } from '@/types/core/compute/perimeterComputeTypes'

import type { Element } from '@/types/core/element/element'
import { describe, expect, it } from 'vitest'

describe('perimeter Calculator Strategy Interface', () => {
  it('should define the required methods for perimeter calculation strategies', () => {
    // Create a mock implementation of PerimeterCalculatorStrategy
    class MockPerimeterCalculator implements PerimeterCalculatorStrategy {
      calculatePerimeter(element: Element): number {
        // Simple mock implementation
        return 100
      }

      getElementType(): string {
        return 'mock-element'
      }
    }

    const calculator = new MockPerimeterCalculator()

    // Test the interface implementation
    expect(typeof calculator.calculatePerimeter).toBe('function')
    expect(typeof calculator.getElementType).toBe('function')

    // Test the mock implementation
    const mockElement = { id: 'test-element' } as Element
    expect(calculator.calculatePerimeter(mockElement)).toBe(100)
    expect(calculator.getElementType()).toBe('mock-element')
  })

  it('should allow implementation for different shape types', () => {
    // Create a mock rectangle perimeter calculator
    class MockRectanglePerimeterCalculator implements PerimeterCalculatorStrategy {
      calculatePerimeter(element: Element): number {
        // Mock implementation for rectangle
        return 200
      }

      getElementType(): string {
        return 'rectangle'
      }
    }

    // Create a mock circle perimeter calculator
    class MockCirclePerimeterCalculator implements PerimeterCalculatorStrategy {
      calculatePerimeter(element: Element): number {
        // Mock implementation for circle
        return Math.PI * 20
      }

      getElementType(): string {
        return 'circle'
      }
    }

    const rectangleCalculator = new MockRectanglePerimeterCalculator()
    const circleCalculator = new MockCirclePerimeterCalculator()

    // Test the implementations
    const mockElement = { id: 'test-element' } as Element

    expect(rectangleCalculator.calculatePerimeter(mockElement)).toBe(200)
    expect(rectangleCalculator.getElementType()).toBe('rectangle')

    expect(circleCalculator.calculatePerimeter(mockElement)).toBeCloseTo(Math.PI * 20)
    expect(circleCalculator.getElementType()).toBe('circle')
  })

  it('should allow for complex perimeter calculation logic', () => {
    // Create a mock polygon perimeter calculator with more complex logic
    class MockPolygonPerimeterCalculator implements PerimeterCalculatorStrategy {
      calculatePerimeter(element: Element): number {
        // Mock implementation that simulates getting points from the element
        const mockPoints = [
          { x: 0, y: 0 },
          { x: 10, y: 0 },
          { x: 10, y: 10 },
          { x: 0, y: 10 },
        ]

        // Calculate perimeter by summing the distances between consecutive points
        let perimeter = 0
        for (let i = 0; i < mockPoints.length; i++) {
          const p1 = mockPoints[i]
          const p2 = mockPoints[(i + 1) % mockPoints.length]

          // Calculate distance between points
          const dx = p2.x - p1.x
          const dy = p2.y - p1.y
          perimeter += Math.sqrt(dx * dx + dy * dy)
        }

        return perimeter
      }

      getElementType(): string {
        return 'polygon'
      }
    }

    const polygonCalculator = new MockPolygonPerimeterCalculator()

    // Test the implementation
    const mockElement = { id: 'test-element' } as Element
    expect(polygonCalculator.calculatePerimeter(mockElement)).toBe(40) // 10 + 10 + 10 + 10 = 40
    expect(polygonCalculator.getElementType()).toBe('polygon')
  })

  it('should allow for error handling in implementations', () => {
    // Create a mock perimeter calculator with error handling
    class MockErrorHandlingCalculator implements PerimeterCalculatorStrategy {
      calculatePerimeter(element: Element): number {
        // Check if element has required properties
        if (!element?.id) {
          throw new Error('Invalid element')
        }

        // Mock implementation
        return 300
      }

      getElementType(): string {
        return 'error-handling-element'
      }
    }

    const errorHandlingCalculator = new MockErrorHandlingCalculator()

    // Test valid case
    const validElement = { id: 'test-element' } as Element
    expect(errorHandlingCalculator.calculatePerimeter(validElement)).toBe(300)

    // Test error case
    const invalidElement = {} as Element
    expect(() => errorHandlingCalculator.calculatePerimeter(invalidElement)).toThrow('Invalid element')
  })

  it('should support different perimeter calculation formulas', () => {
    // Create a mock calculator with multiple formulas
    class MockMultiFormulaCalculator implements PerimeterCalculatorStrategy {
      calculatePerimeter(element: Element): number {
        // Get element type to determine which formula to use
        const type = (element as any).type || 'unknown'

        // Use different formulas based on element type
        switch (type) {
          case 'rectangle':
            return this.calculateRectanglePerimeter(element)
          case 'circle':
            return this.calculateCirclePerimeter(element)
          case 'polygon':
            return this.calculatePolygonPerimeter(element)
          default:
            return 0
        }
      }

      private calculateRectanglePerimeter(element: Element): number {
        // Mock rectangle perimeter calculation: 2 * (width + height)
        const width = (element as any).width || 0
        const height = (element as any).height || 0
        return 2 * (width + height)
      }

      private calculateCirclePerimeter(element: Element): number {
        // Mock circle perimeter calculation: 2 * π * radius
        const radius = (element as any).radius || 0
        return 2 * Math.PI * radius
      }

      private calculatePolygonPerimeter(element: Element): number {
        // Mock polygon perimeter calculation
        return 100
      }

      getElementType(): string {
        return 'multi-formula-element'
      }
    }

    const multiFormulaCalculator = new MockMultiFormulaCalculator()

    // Test with different element types
    const rectangleElement = { id: 'rect-1', type: 'rectangle', width: 10, height: 20 } as Element
    const circleElement = { id: 'circle-1', type: 'circle', radius: 5 } as Element
    const polygonElement = { id: 'polygon-1', type: 'polygon' } as Element
    const unknownElement = { id: 'unknown-1', type: 'unknown' } as Element

    expect(multiFormulaCalculator.calculatePerimeter(rectangleElement)).toBe(60) // 2 * (10 + 20) = 60
    expect(multiFormulaCalculator.calculatePerimeter(circleElement)).toBeCloseTo(2 * Math.PI * 5)
    expect(multiFormulaCalculator.calculatePerimeter(polygonElement)).toBe(100)
    expect(multiFormulaCalculator.calculatePerimeter(unknownElement)).toBe(0)
    expect(multiFormulaCalculator.getElementType()).toBe('multi-formula-element')
  })
})
