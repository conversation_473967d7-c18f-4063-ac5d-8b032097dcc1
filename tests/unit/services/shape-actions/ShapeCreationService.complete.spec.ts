import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ErrorType } from '@/core/errors/CoreError'
import { ElementFactory } from '@/core/factory'
import { ShapeRepository } from '@/core/state/ShapeRepository'

// Import first, then mock
import { ShapeCreationService } from '@/services/shape-actions/ShapeCreationService'
import { ElementType } from '@/types/core/shape-type'
import { AppEventType } from '@/types/services/events'

// Create mock implementations
const mockCreateShape = vi.fn()
const mockAdd = vi.fn()
const mockValidateElement = vi.fn()
const mockEnsurePointInstance = vi.fn()
const mockHandleValidationResultAndPublishError = vi.fn()

// Mock dependencies
vi.mock('@/core/factory', () => ({
  ElementFactory: vi.fn().mockImplementation(() => ({
    createShape: mockCreateShape,
  })),
}))

vi.mock('@/core/state/ShapeRepository', () => ({
  ShapeRepository: vi.fn().mockImplementation(() => ({
    add: mockAdd,
  })),
}))

vi.mock('@/core/validator', () => ({
  ElementValidator: {
    validateElement: mockValidateElement,
  },
}))

vi.mock('@/core/utils/geometryUtils', () => ({
  ensurePointInstance: mockEnsurePointInstance,
}))

vi.mock('@/core/utils/validationUtils', () => ({
  handleValidationResultAndPublishError: mockHandleValidationResultAndPublishError,
}))

describe('shapeCreationService', () => {
  let service: ShapeCreationService
  let mockFactory: any
  let mockRepository: any
  let mockEventBus: any
  let mockLogger: any

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()
    mockCreateShape.mockReset()
    mockAdd.mockReset()
    mockValidateElement.mockReset()
    mockEnsurePointInstance.mockReset()
    mockHandleValidationResultAndPublishError.mockReset()

    // Create mock implementations
    mockFactory = new ElementFactory()
    mockRepository = new ShapeRepository()

    mockEventBus = {
      publish: vi.fn(),
    }

    mockLogger = {
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    }

    // Mock utility functions
    mockEnsurePointInstance.mockImplementation(point => ({
      x: point.x,
      y: point.y,
      toJson: () => ({ x: point.x, y: point.y }),
    }))

    mockHandleValidationResultAndPublishError.mockReturnValue(true)

    mockValidateElement.mockResolvedValue({
      isValid: true,
      errors: [],
    })

    // Create service instance with mocks
    service = new ShapeCreationService(
      mockFactory,
      mockRepository,
      mockEventBus,
      mockLogger,
    )
  })

  describe('handleRequest', () => {
    it('should handle invalid payload (missing ElementType)', async () => {
      // Call the method with invalid payload
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: null as any,
          position: { x: 100, y: 100 },
        },
      })

      // Verify error event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          code: ErrorType.INVALID_PAYLOAD,
          message: expect.stringContaining('Invalid payload for shape creation'),
        }),
      })

      // Verify factory and repository methods were not called
      expect(mockCreateShape).not.toHaveBeenCalled()
      expect(mockAdd).not.toHaveBeenCalled()
    })

    it('should handle invalid payload (missing position)', async () => {
      // Call the method with invalid payload
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: null as any,
        },
      })

      // Verify error event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          code: ErrorType.VALIDATION_FAILED,
          message: expect.stringContaining('Invalid position data'),
        }),
      })

      // Verify factory and repository methods were not called
      expect(mockCreateShape).not.toHaveBeenCalled()
      expect(mockAdd).not.toHaveBeenCalled()
    })

    it('should handle validation failure', async () => {
      // Mock validation failure
      mockHandleValidationResultAndPublishError.mockReturnValueOnce(false)

      // Call the method
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: -10, // Invalid width
            height: 50,
          },
        },
      })

      // Verify validation was called
      expect(mockValidateElement).toHaveBeenCalled()

      // Verify factory and repository methods were not called
      expect(mockCreateShape).not.toHaveBeenCalled()
      expect(mockAdd).not.toHaveBeenCalled()
    })

    it('should successfully create a rectangle shape', async () => {
      // Mock factory to return a shape
      const mockShape = {
        id: 'rectangle-1',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
        properties: {
          width: 100,
          height: 50,
        },
      }
      mockCreateShape.mockResolvedValueOnce(mockShape)

      // Call the method with valid data
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 100,
            height: 50,
          },
        },
      })

      // Verify factory was called with correct parameters
      expect(mockCreateShape).toHaveBeenCalledWith(
        ElementType.RECTANGLE,
        expect.objectContaining({
          type: ElementType.RECTANGLE,
          position: expect.anything(),
          properties: undefined, // Note: properties are spread into the params object
        }),
      )

      // Verify repository was called with the created shape
      expect(mockAdd).toHaveBeenCalledWith(mockShape)

      // Verify success event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_CREATE_COMPLETE,
        payload: {
          shapeId: mockShape.id,
          ElementType: mockShape.type,
          position: mockShape.position,
          properties: mockShape.properties,
        },
      })
    })

    it('should handle factory creation failure', async () => {
      // Mock factory to return null
      mockCreateShape.mockResolvedValueOnce(null)

      // Call the method
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 100,
            height: 50,
          },
        },
      })

      // Verify error event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          code: ErrorType.FACTORY_CREATION_FAILED,
        }),
      })

      // Verify repository method was not called
      expect(mockAdd).not.toHaveBeenCalled()
    })

    it('should handle repository addition failure', async () => {
      // Mock factory to return a shape
      const mockShape = {
        id: 'rectangle-1',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
        properties: {
          width: 100,
          height: 50,
        },
      }
      mockCreateShape.mockResolvedValueOnce(mockShape)

      // Mock repository to throw an error
      mockAdd.mockRejectedValueOnce(new Error('Repository error'))

      // Call the method
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 100,
            height: 50,
          },
        },
      })

      // Verify error event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          code: ErrorType.COORDINATOR_OPERATION_FAILED,
        }),
      })
    })
  })
})
