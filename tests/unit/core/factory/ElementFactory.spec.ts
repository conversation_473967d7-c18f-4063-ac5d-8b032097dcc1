import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ElementFactory } from '@/core/factory/ElementFactory'
import { ElementType } from '@/types/core/elementDefinitions'

// Mock all dependencies
vi.mock('@/core/factory/creators', () => ({
  RectangleCreator: vi.fn().mockImplementation(() => ({
    create: vi.fn().mockResolvedValue({
      id: 'test-rect',
      type: ElementType.RECTANGLE,
      position: { x: 0, y: 0 },
      properties: { width: 100, height: 50 },
      majorCategory: 'shape',
      minorCategory: 'rectangle',
      zLevelId: 'main'
    })
  })),
  EllipseCreator: vi.fn().mockImplementation(() => ({
    create: vi.fn().mockResolvedValue({
      id: 'test-ellipse',
      type: ElementType.ELLIPSE,
      position: { x: 0, y: 0 },
      properties: { radiusX: 50, radiusY: 30 },
      majorCategory: 'shape',
      minorCategory: 'ellipse',
      zLevelId: 'main'
    })
  })),
  PolygonCreator: vi.fn().mockImplementation(() => ({
    create: vi.fn().mockResolvedValue({
      id: 'test-polygon',
      type: ElementType.POLYGON,
      position: { x: 0, y: 0 },
      properties: { points: [] },
      majorCategory: 'shape',
      minorCategory: 'polygon',
      zLevelId: 'main'
    })
  })),
  LineCreator: vi.fn().mockImplementation(() => ({
    create: vi.fn().mockResolvedValue({
      id: 'test-line',
      type: ElementType.LINE,
      properties: { start: { x: 0, y: 0 }, end: { x: 100, y: 50 } },
      majorCategory: 'path',
      minorCategory: 'line',
      zLevelId: 'main'
    })
  })),
  PolylineCreator: vi.fn().mockImplementation(() => ({
    create: vi.fn().mockResolvedValue({
      id: 'test-polyline',
      type: ElementType.POLYLINE,
      properties: { points: [] },
      majorCategory: 'path',
      minorCategory: 'polyline',
      zLevelId: 'main'
    })
  })),
  ArcCreator: vi.fn().mockImplementation(() => ({
    create: vi.fn().mockResolvedValue({
      id: 'test-arc',
      type: ElementType.ARC,
      properties: { center: { x: 0, y: 0 }, radius: 25 },
      majorCategory: 'path',
      minorCategory: 'arc',
      zLevelId: 'main'
    })
  })),
  QuadraticCreator: vi.fn().mockImplementation(() => ({
    create: vi.fn().mockResolvedValue({
      id: 'test-quadratic',
      type: ElementType.QUADRATIC,
      properties: { start: { x: 0, y: 0 }, control: { x: 50, y: 50 }, end: { x: 100, y: 0 } },
      majorCategory: 'path',
      minorCategory: 'quadratic',
      zLevelId: 'main'
    })
  })),
  CubicCreator: vi.fn().mockImplementation(() => ({
    create: vi.fn().mockResolvedValue({
      id: 'test-cubic',
      type: ElementType.CUBIC,
      properties: { start: { x: 0, y: 0 }, control1: { x: 25, y: 50 }, control2: { x: 75, y: 50 }, end: { x: 100, y: 0 } },
      majorCategory: 'path',
      minorCategory: 'cubic',
      zLevelId: 'main'
    })
  }))
}))

vi.mock('@/core/factory/creators/media/TextCreator', () => ({
  TextCreator: vi.fn().mockImplementation(() => ({
    create: vi.fn().mockResolvedValue({
      id: 'test-text',
      type: ElementType.TEXT,
      position: { x: 0, y: 0 },
      properties: { text: 'Test Text' },
      majorCategory: 'text',
      minorCategory: 'text',
      zLevelId: 'main'
    })
  }))
}))

vi.mock('@/core/factory/creators/media/ImageCreator', () => ({
  default: vi.fn().mockImplementation(() => ({
    create: vi.fn().mockResolvedValue({
      id: 'test-image',
      type: ElementType.IMAGE,
      position: { x: 0, y: 0 },
      properties: { src: 'test.png' },
      majorCategory: 'media',
      minorCategory: 'image',
      zLevelId: 'main'
    })
  }))
}))

vi.mock('@/lib/utils/element/elementUtils', () => ({
  ensureCompleteMetadata: vi.fn().mockImplementation((metadata) => ({
    ...metadata,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    version: '1.0'
  }))
}))

vi.mock('@/core/factory/creators/shape/ShapeCreator', () => ({
  ShapeCreator: vi.fn().mockImplementation(() => ({
    create: vi.fn().mockResolvedValue({
      id: 'test-shape',
      type: ElementType.RECTANGLE,
      position: { x: 0, y: 0 }
    })
  }))
}))

describe('ElementFactory', () => {
  let factory: ElementFactory

  beforeEach(() => {
    vi.clearAllMocks()
    factory = new ElementFactory()
  })

  describe('Constructor', () => {
    it('should initialize and register default creators', () => {
      expect(factory).toBeDefined()
      expect(factory).toBeInstanceOf(ElementFactory)
    })
  })

  describe('Position Normalization', () => {
    it('should normalize undefined position to origin', async () => {
      const result = await factory.createRectangle({
        width: 100,
        height: 50,
        majorCategory: 'shape' as any
      })
      expect(result).toBeDefined()
    })

    it('should normalize array position input', async () => {
      const result = await factory.createRectangle({
        width: 100,
        height: 50,
        position: [10, 20],
        majorCategory: 'shape' as any
      })
      expect(result).toBeDefined()
    })

    it('should normalize 3D array position input', async () => {
      const result = await factory.createRectangle({
        width: 100,
        height: 50,
        position: [10, 20, 5],
        majorCategory: 'shape' as any
      })
      expect(result).toBeDefined()
    })

    it('should normalize object position input', async () => {
      const result = await factory.createRectangle({
        width: 100,
        height: 50,
        position: { x: 10, y: 20 },
        majorCategory: 'shape' as any
      })
      expect(result).toBeDefined()
    })

    it('should handle invalid array position', async () => {
      const result = await factory.createRectangle({
        width: 100,
        height: 50,
        position: ['invalid', 'data'] as any,
        majorCategory: 'shape' as any
      })
      expect(result).toBeDefined()
    })

    it('should handle invalid object position', async () => {
      const result = await factory.createRectangle({
        width: 100,
        height: 50,
        position: { invalid: 'data' } as any,
        majorCategory: 'shape' as any
      })
      expect(result).toBeDefined()
    })
  })

  describe('Shape Creation Methods', () => {
    it('should create rectangle', async () => {
      const result = await factory.createRectangle({
        width: 100,
        height: 50,
        position: { x: 0, y: 0 },
        majorCategory: 'shape' as any
      })
      expect(result).toBeDefined()
      expect(result.id).toBeDefined()
    })

    it('should create square', async () => {
      const result = await factory.createSquare({
        size: 50,
        position: { x: 0, y: 0 },
        majorCategory: 'shape' as any
      })
      expect(result).toBeDefined()
      expect(result.id).toBeDefined()
    })

    it('should create ellipse', async () => {
      const result = await factory.createEllipse({
        radiusX: 50,
        radiusY: 30,
        position: { x: 0, y: 0 },
        majorCategory: 'shape' as any
      })
      expect(result).toBeDefined()
      expect(result.id).toBeDefined()
    })

    it('should create circle', async () => {
      const result = await factory.createCircle({
        radius: 25,
        position: { x: 0, y: 0 },
        majorCategory: 'shape' as any
      })
      expect(result).toBeDefined()
      expect(result.id).toBeDefined()
    })

    it('should create line', async () => {
      const result = await factory.createLine({
        start: { x: 0, y: 0 },
        end: { x: 100, y: 50 },
        majorCategory: 'path' as any
      })
      expect(result).toBeDefined()
      expect(result.id).toBeDefined()
    })

    it('should create polyline', async () => {
      const result = await factory.createPolyline({
        points: [{ x: 0, y: 0 }, { x: 50, y: 25 }, { x: 100, y: 0 }],
        majorCategory: 'path' as any
      })
      expect(result).toBeDefined()
      expect(result.id).toBeDefined()
    })

    it('should create regular polygon', async () => {
      const result = await factory.createRegularPolygon({
        sides: 6,
        radius: 50,
        center: { x: 0, y: 0 },
        majorCategory: 'shape' as any
      })
      expect(result).toBeDefined()
      expect(result.id).toBeDefined()
    })

    it('should create custom polygon', async () => {
      const result = await factory.createCustomPolygon({
        points: [{ x: 0, y: 0 }, { x: 100, y: 0 }, { x: 50, y: 100 }],
        majorCategory: 'shape' as any
      })
      expect(result).toBeDefined()
      expect(result.id).toBeDefined()
    })

    it('should create arc', async () => {
      const result = await factory.createArc({
        radius: 50,
        startAngle: 0,
        endAngle: 90,
        position: { x: 0, y: 0 },
        majorCategory: 'path' as any
      })
      expect(result).toBeDefined()
      expect(result.id).toBeDefined()
    })

  })

  describe('Default Shape Creation', () => {
    it('should create default rectangle', async () => {
      const result = await factory.createDefaultShape(
        ElementType.RECTANGLE,
        'test-id',
        { x: 0, y: 0 },
        'shape' as any
      )
      expect(result).toBeDefined()
      expect(result.id).toBe('test-id')
    })

    it('should create default square', async () => {
      const result = await factory.createDefaultShape(
        ElementType.SQUARE,
        'test-id',
        { x: 0, y: 0 },
        'shape' as any
      )
      expect(result).toBeDefined()
      expect(result.id).toBe('test-id')
    })

    it('should create default circle', async () => {
      const result = await factory.createDefaultShape(
        ElementType.CIRCLE,
        'test-id',
        { x: 0, y: 0 },
        'shape' as any
      )
      expect(result).toBeDefined()
      expect(result.id).toBe('test-id')
    })

    it('should create default ellipse', async () => {
      const result = await factory.createDefaultShape(
        ElementType.ELLIPSE,
        'test-id',
        { x: 0, y: 0 },
        'shape' as any
      )
      expect(result).toBeDefined()
      expect(result.id).toBe('test-id')
    })

    it('should create default shape for unknown type', async () => {
      const result = await factory.createDefaultShape(
        'UNKNOWN_TYPE' as any,
        'test-id',
        { x: 0, y: 0 },
        'shape' as any
      )
      expect(result).toBeDefined()
      expect(result.id).toBe('test-id')
    })

    it('should handle array position in default shape creation', async () => {
      const result = await factory.createDefaultShape(
        ElementType.RECTANGLE,
        'test-id',
        [10, 20],
        'shape' as any
      )
      expect(result).toBeDefined()
      expect(result.id).toBe('test-id')
    })

    it('should handle 3D array position in default shape creation', async () => {
      const result = await factory.createDefaultShape(
        ElementType.RECTANGLE,
        'test-id',
        [10, 20, 5],
        'shape' as any
      )
      expect(result).toBeDefined()
      expect(result.id).toBe('test-id')
    })
  })

  describe('Creator Registration', () => {
    it('should register custom creator', () => {
      const mockCreator = {
        create: vi.fn().mockResolvedValue({ id: 'test', type: 'CUSTOM' })
      }

      factory.registerCreator('CUSTOM_TYPE', mockCreator as any)
      expect(true).toBe(true) // Should not throw
    })

    it('should override existing creator', () => {
      const mockCreator = {
        create: vi.fn().mockResolvedValue({ id: 'test', type: ElementType.RECTANGLE })
      }

      factory.registerCreator(ElementType.RECTANGLE, mockCreator as any)
      expect(true).toBe(true) // Should not throw
    })
  })

  describe('Error Handling', () => {
    it('should throw error for unregistered shape type', async () => {
      await expect(factory.createShape('UNREGISTERED_TYPE', {
        id: 'test',
        type: 'UNREGISTERED_TYPE',
        majorCategory: 'shape' as any
      } as any)).rejects.toThrow()
    })

    it('should throw error for unregistered path type', async () => {
      await expect(factory.createPath('UNREGISTERED_PATH_TYPE', {
        id: 'test',
        type: 'UNREGISTERED_PATH_TYPE',
        majorCategory: 'path' as any
      } as any)).rejects.toThrow()
    })
  })

  describe('UUID Generation', () => {
    it('should generate unique IDs for multiple shapes', async () => {
      const rect1 = await factory.createRectangle({
        width: 100,
        height: 50,
        majorCategory: 'shape' as any
      })

      const rect2 = await factory.createRectangle({
        width: 100,
        height: 50,
        majorCategory: 'shape' as any
      })

      expect(rect1.id).toBeDefined()
      expect(rect2.id).toBeDefined()
      expect(rect1.id).not.toBe(rect2.id)
    })
  })

  describe('Metadata Handling', () => {
    it('should handle custom metadata', async () => {
      const result = await factory.createRectangle({
        width: 100,
        height: 50,
        majorCategory: 'shape' as any,
        metadata: {
          name: 'Custom Rectangle',
          description: 'A test rectangle'
        }
      })
      expect(result).toBeDefined()
    })

    it('should handle all optional parameters', async () => {
      const result = await factory.createRectangle({
        width: 100,
        height: 50,
        position: { x: 10, y: 20 },
        majorCategory: 'shape' as any,
        minorCategory: 'rectangle' as any,
        zLevelId: 'main',
        isFixedCategory: true,
        visible: false,
        locked: true,
        rotation: 45,
        selectable: false,
        draggable: false,
        showHandles: false,
        zIndex: 10,
        fill: '#ff0000',
        stroke: '#000000',
        strokeWidth: 2,
        opacity: 0.8
      })
      expect(result).toBeDefined()
    })

  })
})
