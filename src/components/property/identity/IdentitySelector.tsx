/**
 * Identity Selector Component
 *
 * Implements the standardized identity layout for element identity and layer management
 * according to the design document specifications.
 *
 * Layout:
 * - Layer Assignment: Module, Step, Z-Level selection
 * - Layer Order: Bring to front/send to back controls
 * - Element Information: Type, creation/update timestamps
 *
 * Features:
 * - Hierarchical layer selection with cascading dropdowns
 * - Multi-element selection support with mixed value handling
 * - Layer order controls for single elements
 * - Element metadata display
 * - Consistent styling with geometry components
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'
import type { TaskModule } from '@/types/core/layerPanelTypes'
import { ArrowDownToLine, ArrowUpToLine } from 'lucide-react'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useShapesStore } from '@/store/shapesStore'
import { GeometrySection } from '../shared'

interface IdentitySelectorProps {
  /** Array of currently selected elements */
  selectedElements: ShapeElement[]
  /** Function to get common property values across selected elements */
  getCommonValue: (propertyPath: string, elements?: ShapeElement[]) => unknown
  /** Function to update property values for selected elements */
  updateProperty: (propertyPath: string, value: unknown) => void
  /** Available layer panel modules for assignment */
  layerPanelModules: TaskModule[]
}

export function IdentitySelector({
  selectedElements,
  getCommonValue,
  updateProperty,
  layerPanelModules,
}: IdentitySelectorProps) {
  const [updateSuccess, setUpdateSuccess] = useState(false)
  const storeShapes = useShapesStore(state => state.shapes)
  const selectedElementsLatest = selectedElements.map((el) => {
    const storeElement = storeShapes.find(s => s.id === el.id)
    return storeElement ?? el
  })

  const isSingleSelection = selectedElements.length === 1
  const elementName = isSingleSelection ? selectedElementsLatest[0] : null

  const bringToFrontInLayer = useShapesStore(state => state.bringToFrontInLayer)
  const sendToBackInLayer = useShapesStore(state => state.sendToBackInLayer)

  const rawMajorCategory = getCommonValue('majorCategory', selectedElementsLatest)
  const currentMajorCategory = typeof rawMajorCategory === 'string' ? rawMajorCategory : (rawMajorCategory === 'mixed' ? 'mixed' : '')

  const rawMinorCategory = getCommonValue('minorCategory', selectedElementsLatest)
  const currentMinorCategory = typeof rawMinorCategory === 'string' ? rawMinorCategory : (rawMinorCategory === 'mixed' ? 'mixed' : '')

  const rawZLevelId = getCommonValue('zLevelId', selectedElementsLatest)
  const currentZLevelId = typeof rawZLevelId === 'string' ? rawZLevelId : (rawZLevelId === 'mixed' ? 'mixed' : '')

  const isFixedCategory = getCommonValue('isFixedCategory', selectedElementsLatest) === true

  const availableMajorCategories = layerPanelModules.map(module => ({
    value: module.id,
    label: module.name,
  }))

  const selectedMajorModule = layerPanelModules.find(m => m.id === currentMajorCategory)

  const availableMinorCategories = selectedMajorModule?.steps.map(step => ({
    value: step.id,
    label: step.name,
  })) || []

  const selectedMinorStep = selectedMajorModule?.steps.find(s => s.id === currentMinorCategory)

  const availableZLevels = selectedMinorStep?.zLevels.map(zLevel => ({
    value: zLevel.id,
    label: zLevel.name,
  })) || []

  const handleMajorCategoryChange = (value: string) => {
    updateProperty('majorCategory', value)
    setUpdateSuccess(true)
    setTimeout(() => setUpdateSuccess(false), 100)
  }

  const handleMinorCategoryChange = (value: string) => {
    updateProperty('minorCategory', value)
    setUpdateSuccess(true)
    setTimeout(() => setUpdateSuccess(false), 100)
  }

  const handleZLevelChange = (value: string) => {
    updateProperty('zLevelId', value)
    setUpdateSuccess(true)
    setTimeout(() => setUpdateSuccess(false), 100)
  }

  const allowLayerChange = !isFixedCategory
  const canModifyLayerOrder = !!(
    isSingleSelection
    && elementName !== null
    && typeof elementName.zLevelId === 'string'
    && elementName.zLevelId.trim() !== ''
  )

  if (selectedElements.length === 0) {
    return (
      <GeometrySection type="special" title="Layer Assignment" columns={1}>
        <p className="text-xs text-muted-foreground p-2">
          Select an element to see layer options.
        </p>
      </GeometrySection>
    )
  }

  return (
    <>
      {updateSuccess && (
        <div className="text-green-600 text-xs font-bold animate-pulse mb-2">✓ Updated</div>
      )}

      {/* Layer Assignment */}
      <GeometrySection type="special" title="Layer Assignment" columns={1}>
        <div className="space-y-3">
          {/* Module (Major Category) */}
          <div className="space-y-1">
            <Label htmlFor="majorCategory" className="text-sm font-medium">Module (Major Category)</Label>
            <Select
              value={currentMajorCategory === 'mixed' ? '__mixed__' : currentMajorCategory}
              onValueChange={handleMajorCategoryChange}
              disabled={isFixedCategory || currentMajorCategory === 'mixed' || selectedElementsLatest.length === 0}
            >
              <SelectTrigger id="majorCategory" className="h-10">
                <SelectValue placeholder={currentMajorCategory === 'mixed' ? 'Mixed Values' : (selectedElementsLatest.length === 0 ? 'N/A' : 'Select Module')} />
              </SelectTrigger>
              <SelectContent>
                {currentMajorCategory === 'mixed' && <SelectItem value="__mixed__" disabled>Mixed Values</SelectItem>}
                {availableMajorCategories.map(cat => (
                  <SelectItem key={cat.value} value={cat.value}>{cat.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Step (Minor Category) */}
          {(typeof currentMajorCategory === 'string' && currentMajorCategory !== '' && currentMajorCategory !== 'mixed' && availableMinorCategories.length > 0) && (
            <div className="space-y-1">
              <Label htmlFor="minorCategory" className="text-sm font-medium">Step (Minor Category)</Label>
              <Select
                value={currentMinorCategory === 'mixed' ? '__mixed__' : currentMinorCategory}
                onValueChange={handleMinorCategoryChange}
                disabled={!allowLayerChange || currentMinorCategory === 'mixed' || selectedElementsLatest.length === 0}
              >
                <SelectTrigger id="minorCategory" className="h-10">
                  <SelectValue placeholder={currentMinorCategory === 'mixed' ? 'Mixed Values' : 'Select Step'} />
                </SelectTrigger>
                <SelectContent>
                  {currentMinorCategory === 'mixed' && <SelectItem value="__mixed__" disabled>Mixed Values</SelectItem>}
                  {availableMinorCategories.map(cat => (
                    <SelectItem key={cat.value} value={cat.value}>{cat.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Z-Level (Layer) */}
          {(typeof currentMinorCategory === 'string' && currentMinorCategory !== '' && currentMinorCategory !== 'mixed' && availableZLevels.length > 0) && (
            <div className="space-y-1">
              <Label htmlFor="zLevel" className="text-sm font-medium">Z-Level (Layer)</Label>
              <Select
                value={currentZLevelId === 'mixed' ? '__mixed__' : currentZLevelId}
                onValueChange={handleZLevelChange}
                disabled={!allowLayerChange || currentZLevelId === 'mixed' || selectedElementsLatest.length === 0}
              >
                <SelectTrigger id="zLevel" className="h-10">
                  <SelectValue placeholder={currentZLevelId === 'mixed' ? 'Mixed Values' : 'Select Z-Level'} />
                </SelectTrigger>
                <SelectContent>
                  {currentZLevelId === 'mixed' && <SelectItem value="__mixed__" disabled>Mixed Values</SelectItem>}
                  {availableZLevels.map(level => (
                    <SelectItem key={level.value} value={level.value}>{level.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Status Messages */}
          {!allowLayerChange && selectedElementsLatest.length > 0 && (
            <p className="text-xs text-muted-foreground">
              Layer assignment for this element is fixed.
            </p>
          )}
          {currentMajorCategory === 'mixed' && selectedElementsLatest.length > 0 && (
            <p className="text-xs text-muted-foreground">
              Multiple elements with different layer assignments selected. Edit individually or select elements with the same assignment.
            </p>
          )}
        </div>
      </GeometrySection>

      {/* Layer Order Controls */}
      {isSingleSelection && elementName !== null && (
        <GeometrySection type="transform" title="Layer Order" columns={2}>
          {canModifyLayerOrder
            ? (
                <>
                  <Button
                    onClick={() => bringToFrontInLayer(elementName.id)}
                    variant="outline"
                    size="sm"
                    className="w-full h-10"
                    title="Bring to Front in Layer"
                  >
                    <ArrowUpToLine className="mr-2 h-4 w-4" />
                    <span>Bring to Front</span>
                  </Button>
                  <Button
                    onClick={() => sendToBackInLayer(elementName.id)}
                    variant="outline"
                    size="sm"
                    className="w-full h-10"
                    title="Send to Back in Layer"
                  >
                    <ArrowDownToLine className="mr-2 h-4 w-4" />
                    <span>Send to Back</span>
                  </Button>
                </>
              )
            : (
                <div className="col-span-2">
                  <p className="text-xs text-muted-foreground">
                    Assign a Z-Level to this element to enable layer ordering.
                  </p>
                </div>
              )}
        </GeometrySection>
      )}

      {/* Element Information */}
      {isSingleSelection && elementName !== null && (
        <GeometrySection type="content" title="Element Information" columns={1}>
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Type:</span>
              <span className="font-medium">{elementName.type.charAt(0).toUpperCase() + elementName.type.slice(1)}</span>
            </div>
            {elementName.metadata !== null && elementName.metadata !== undefined && (
              <>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Created:</span>
                  <span>{new Date(elementName.metadata.createdAt).toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Updated:</span>
                  <span>{new Date(elementName.metadata.updatedAt).toLocaleString()}</span>
                </div>
              </>
            )}
          </div>
        </GeometrySection>
      )}
    </>
  )
}
