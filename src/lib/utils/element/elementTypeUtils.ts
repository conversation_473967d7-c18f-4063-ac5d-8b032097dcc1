/**
 * Element Type Utilities
 *
 * @remarks
 * This module provides utility functions for working with {@link ElementType} values,
 * primarily for checking if an element type belongs to a specific category
 * (e.g., basic shapes, paths, interior design elements) as defined in
 * {@link ElementCategories}.
 *
 * These functions rely on the `ElementType` enum and `ElementCategories` constant
 * imported from ` '@/types/core/elementDefinitions`.
 *
 * @module lib/utils/element/elementTypeUtils
 */

import type { ElementType } from '@/types/core/elementDefinitions'
import { ElementCategories } from '@/types/core/elementDefinitions' // Corrected import path

/**
 * Retrieves the category key for a given {@link ElementType}.
 *
 * @param type - The {@link ElementType} to categorize.
 * @returns The key of the category in {@link ElementCategories} (e.g., 'BASIC_SHAPES', 'PATHS')
 *          to which the element type belongs, or `undefined` if the type is not found in any category.
 */
export function getElementCategory(type: ElementType): keyof typeof ElementCategories | undefined {
  // Check each category to see if it contains the type
  for (const [category, types] of Object.entries(ElementCategories)) {
    if (types.includes(type)) {
      return category as keyof typeof ElementCategories
    }
  }

  // Return undefined if the type is not found in any category
  return undefined
}

/**
 * Checks if a given {@link ElementType} is classified as a basic shape or a polygon.
 *
 * @param type - The {@link ElementType} to check.
 * @returns `true` if the element type belongs to the `BASIC_SHAPES` or `POLYGONS` categories, `false` otherwise.
 */
export function isElementType(type: ElementType): boolean {
  // Check if the type is in the BASIC_SHAPES or POLYGONS categories
  return (
    ElementCategories.BASIC_SHAPES.includes(type)
    || ElementCategories.POLYGONS.includes(type)
  )
}

/**
 * Checks if a given {@link ElementType} is classified as a path.
 *
 * @param type - The {@link ElementType} to check.
 * @returns `true` if the element type belongs to the `PATHS` category, `false` otherwise.
 */
export function isPathType(type: ElementType): boolean {
  // Check if the type is in the PATHS category
  return ElementCategories.PATHS.includes(type)
}

// Removed isSpecialType function as ElementCategories.SPECIAL does not exist
// /**
//  * Check if an element type is a special element
//  *
//  * @param type - Element type
//  * @returns True if the element type is a special element
//  */
// export function isSpecialType(type: ElementType): boolean {
//   // Check if the type is in the SPECIAL category
//   // return ElementCategories.SPECIAL.includes(type); // ElementCategories.SPECIAL is not defined
//   return false; // Placeholder if function signature needs to be kept for some reason
// }

/**
 * Checks if a given {@link ElementType} is classified as an interior design element.
 *
 * @param type - The {@link ElementType} to check.
 * @returns `true` if the element type belongs to the `INTERIOR_DESIGN` category, `false` otherwise.
 */
export function isInteriorDesignType(type: ElementType): boolean {
  // Check if the type is in the INTERIOR_DESIGN category
  return ElementCategories.INTERIOR_DESIGN.includes(type)
}
