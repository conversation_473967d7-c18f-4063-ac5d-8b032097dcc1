/**
 * Material Calculation Strategy for Curtains
 *
 * @remarks
 * This strategy implements the {@link IMaterialCalculatorStrategy} to calculate
 * the required materials for curtains. It considers factors such as fabric area
 * (including fullness and hems), number of fabric panels, track length,
 * and various accessories like hooks, rings, and tiebacks.
 *
 * The calculation is based on the bounding box (width and height) of the
 * element to which the curtain is applied (typically a window, often modeled
 * as a RECTANGLE).
 *
 * It expects 'curtain' as the `materialType` and uses various properties from
 * {@link MaterialCalculationOptions} to refine the calculation, such as
 * `curtainType`, `fabricWidth`, `fullness`, `hemTop`, `hemBottom`, `extraHeight`,
 * `extraWidthPerSide`, `wastageRate`, and `hooksSpacing`.
 *
 * The strategy includes a `calculateDetailedMaterial` private method to compute
 * specific quantities for different components, and the main `calculateMaterialAmount`
 * method returns a summarized {@link MaterialCalculationResult}.
 *
 * **TODO:** The current implementation directly accesses `getBoundingBox` or
 * `compute.boundingBox` on the element. This should ideally be refactored to
 * receive pre-calculated dimensions or use a dedicated bounding box calculation service
 * for better separation of concerns.
 *
 * @module core/compute/strategies/material/CurtainMaterialStrategy
 * @see {@link IMaterialCalculatorStrategy}
 * @see {@link MaterialCalculationOptions}
 * @see {@link MaterialCalculationResult}
 */
import type { MaterialCalculatorStrategy as IMaterialCalculatorStrategy, MaterialCalculationOptions, MaterialCalculationResult } from '@/types/core/compute' // Corrected path
import type { BoundingBox } from '@/types/core/element/geometry/bounding-box'
import type { Element } from '@/types/core/elementDefinitions'
import { CoreError, ErrorType } from '@/services/system/error-service'
import { ElementType as CoreElementType } from '@/types/core/elementDefinitions'

export class CurtainMaterialStrategy implements IMaterialCalculatorStrategy {
  /**
   * Calculates the primary material amount for curtains, typically the fabric area.
   *
   * @param element - The element (e.g., window) for which curtain materials are being calculated.
   * @param materialType - The type of material, expected to be 'curtain'.
   * @param options - Optional {@link MaterialCalculationOptions} to customize the calculation.
   * @returns A {@link MaterialCalculationResult} summarizing the main material quantity.
   * @throws {@link CoreError} if `materialType` is not 'curtain' or if bounding box/dimensions cannot be determined.
   */
  public calculateMaterialAmount(element: Element, materialType: string, options?: MaterialCalculationOptions): MaterialCalculationResult {
    const detailedResult = this.calculateDetailedMaterial(element, materialType, options)
    return {
      amount: detailedResult.fabricArea, // This is fabricAreaWithWastage from detailedResult
      unit: 'm²', // Standard symbol for square meters
      unitCount: detailedResult.fabricPanels,
      unitType: 'panel',
      amountWithWastage: detailedResult.fabricArea, // fabricArea from detailedResult already includes wastage
      // The 'details' field was removed as it's not part of MaterialCalculationResult interface.
      // If detailed breakdown is needed, a separate method or a modified interface would be required.
    }
  }

  /**
   * Calculates a detailed breakdown of materials required for curtains.
   *
   * @private
   * @param element - The element (e.g., window).
   * @param materialType - Expected to be 'curtain'.
   * @param options - Optional {@link MaterialCalculationOptions}.
   * @returns A record containing quantities for various materials (fabric area, panels, track length, accessories).
   *          The key 'fabricArea' in the returned record includes wastage.
   * @throws {@link CoreError} if `materialType` is not 'curtain' or if bounding box/dimensions cannot be determined.
   */
  private calculateDetailedMaterial(element: Element, materialType: string, options?: MaterialCalculationOptions): Record<string, number> {
    if (materialType !== 'curtain') {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `CurtainMaterialStrategy can only calculate for 'curtain' material type, got ${materialType}`,
      )
    }

    // TODO: Refactor to avoid direct access to a potential 'getBoundingBox' or 'compute' property.
    // Strategy should ideally receive pre-calculated values or a dedicated compute service.
    const computableElement = element as unknown as { getBoundingBox?: () => BoundingBox, compute?: { boundingBox?: () => BoundingBox } }
    let bbox: BoundingBox | undefined

    if (computableElement.getBoundingBox !== null && computableElement.getBoundingBox !== undefined && typeof computableElement.getBoundingBox === 'function') {
      bbox = computableElement.getBoundingBox()
    }
    else if (computableElement.compute?.boundingBox && typeof computableElement.compute.boundingBox === 'function') {
      bbox = computableElement.compute.boundingBox()
    }

    if (bbox === null || bbox === undefined || typeof bbox.width !== 'number' || typeof bbox.height !== 'number') {
      throw new CoreError(
        ErrorType.ComputationError,
        `Cannot get valid bounding box for element ${element.id}`,
      )
    }

    const windowWidth = bbox.width
    const windowHeight = bbox.height

    const materialOptions = options ?? {}
    const curtainType = materialOptions.curtainType ?? 'standard'
    const fabricWidth = materialOptions.fabricWidth ?? 1.4
    const fullness = materialOptions.fullness ?? 2.0 // Corrected to match interface
    const hemTop = materialOptions.hemTop ?? 0.1
    const hemBottom = materialOptions.hemBottom ?? 0.2
    const extraHeight = materialOptions.extraHeight ?? 0.3 // Corrected to match interface
    const extraWidth = (materialOptions.extraWidthPerSide !== null && materialOptions.extraWidthPerSide !== undefined) ? materialOptions.extraWidthPerSide * 2 : 0.3 // Corrected to match interface

    const trackLength = windowWidth + extraWidth
    const fabricWidthNeeded = trackLength * fullness
    const fabricHeightNeeded = windowHeight + hemTop + hemBottom + extraHeight
    const fabricPanels = Math.ceil(fabricWidthNeeded / fabricWidth)
    const totalFabricArea = fabricPanels * fabricWidth * fabricHeightNeeded // This is the raw area before wastage

    // Wastage should be applied to totalFabricArea if not already included
    const wastageRate = materialOptions.wastageRate ?? 10 // Default 10%
    const fabricAreaWithWastage = totalFabricArea * (1 + wastageRate / 100)

    const hooksSpacing = materialOptions.hooksSpacing ?? 0.15
    const hooksCount = Math.ceil(fabricWidthNeeded / hooksSpacing)
    const ringsCount = curtainType === 'rings' ? Math.ceil(trackLength / 0.1) : 0
    const tiebackPairs = materialOptions.tiebackPairs ?? 1

    let specificAccessories: Record<string, number> = {}
    switch (curtainType) {
      case 'roman':
        specificAccessories = {
          rings: Math.ceil(fabricWidthNeeded / 0.2),
          cord: fabricHeightNeeded * 3,
        }
        break
      case 'venetian':
        specificAccessories = {
          slats: Math.ceil(windowHeight / 0.05),
        }
        break
      case 'roller':
        specificAccessories = {
          roller: 1,
          bottomWeight: 1,
        }
        break
    }

    return {
      fabricArea: fabricAreaWithWastage, // Return area with wastage
      fabricPanels,
      trackLength,
      hooks: hooksCount,
      rings: ringsCount,
      tiebackPairs,
      ...specificAccessories,
    }
  }

  /**
   * Returns the element type this strategy is typically associated with.
   * @remarks Curtains are often applied to window elements, which are commonly modeled as rectangles.
   * @returns {@link CoreElementType.RECTANGLE} - The rectangle element type typically used for windows
   */
  public getElementType(): string {
    // This strategy is typically applied to window elements, which might be modeled as rectangles.
    return CoreElementType.RECTANGLE // Assuming windows are primarily rectangular
  }

  /**
   * Returns the specific material type this strategy calculates.
   * @returns The string 'curtain'.
   */
  public getMaterialType(): string {
    return 'curtain'
  }
}
