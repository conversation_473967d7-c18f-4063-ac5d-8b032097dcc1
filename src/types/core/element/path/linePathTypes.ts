/**
 * Defines the type for a straight line path element.
 *
 * @remarks
 * This module provides the `Line` interface, which extends {@link ShapeElement}
 * to include properties specific to defining a line segment, namely its start
 * and end points.
 *
 * @module types/core/element/path/linePathTypes
 */
import type { ShapeElement } from '../../elementDefinitions' // Corrected import path
import type Point from '@/types/core/element/geometry/point'

/**
 * Represents a straight line segment defined by a start and an end point.
 *
 * @remarks
 * This interface extends the base {@link ShapeElement} and adds specific properties
 * for line path elements. Lines are fundamental building blocks for creating more
 * complex shapes and paths within the application.
 *
 * @see {@link ShapeElement} for base shape element properties.
 * @see {@link Point} for the structure of coordinate points used by this interface.
 */
export interface Line extends ShapeElement {
  /**
   * Properties specific to a Line element.
   */
  properties: ShapeElement['properties'] & {
    start: Point
    end: Point
    arrowStart?: boolean
    arrowEnd?: boolean
  }

  // Direct access properties for compatibility with existing code
  /** The starting point of the line segment. */
  start: Point
  /** The ending point of the line segment. */
  end: Point
  /** Optional: Whether to display an arrow at the start of the line. */
  arrowStart?: boolean
  /** Optional: Whether to display an arrow at the end of the line. */
  arrowEnd?: boolean
}
