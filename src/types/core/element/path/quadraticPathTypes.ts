/**
 * Defines the type for a quadratic Bézier curve path element.
 *
 * @remarks
 * This module provides the `Quadratic` interface, which extends {@link ShapeElement}
 * to include properties specific to defining a quadratic Bézier curve: a start point,
 * an end point, and a single control point.
 *
 * @module types/core/element/path/quadraticPathTypes
 */
import type { ShapeElement } from '../../elementDefinitions' // Corrected import path
import type Point from '@/types/core/element/geometry/point'

/**
 * Represents a quadratic Bézier curve path, defined by a start point, an end point, and one control point.
 *
 * @remarks
 * A quadratic Bézier curve is a smooth curve that passes through its start and end points.
 * Its shape is determined by a single control point, which influences the curve's curvature.
 * - The curve begins at `start` and ends at `end`.
 * - The `control` point dictates the curve's trajectory between the start and end points.
 * The curve does not typically pass through the control point itself.
 *
 * @example
 * ```typescript
 * const myQuadraticCurve: Quadratic = {
 *   // Properties from ShapeElement (id, type, visible, etc.)
 *   id: 'quad-001',
 *   type: 'quadratic', // Or a more specific ElementType if defined
 *   visible: true,
 *   // ... other ShapeElement properties
 *   // Quadratic-specific properties
 *   start: { x: 10, y: 10 },
 *   control: { x: 100, y: 150 },
 *   end: { x: 190, y: 10 }
 * };
 * ```
 *
 * @see {@link ShapeElement} for base shape properties.
 * @see {@link Point} for the structure of coordinate points.
 * @see {@link ../../../lib/utils/element/path/quadraticImplementation.ts} for potential implementation details.
 */
export interface Quadratic extends ShapeElement {
  /**
   * Properties specific to a Quadratic Bezier curve element.
   */
  properties: ShapeElement['properties'] & {
    start: Point
    control: Point // Renderer expects controlPoint1, will adjust renderer later or this type
    end: Point
    closed?: boolean
  }

  // Direct access properties for compatibility with existing code
  /** The starting point of the quadratic Bezier curve. */
  start: Point
  /** The control point of the quadratic Bezier curve. */
  control: Point
  /** The ending point of the quadratic Bezier curve. */
  end: Point
  /** Optional: Whether the curve should be closed (connecting end to start). */
  closed?: boolean
}
