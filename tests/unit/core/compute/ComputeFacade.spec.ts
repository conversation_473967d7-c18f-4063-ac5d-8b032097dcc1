/**
 * Unit tests for ComputeFacade
 */
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ComputeFacade } from '@/core/compute/ComputeFacade'
import { CoreError } from '@/services/system/error-service/coreError'
import { ElementType } from '@/types/core/elementDefinitions'

describe('computeFacade', () => {
  let facade: ComputeFacade
  let mockStrategyRegistry: any
  let mockShapeRepository: any
  let mockElementFactory: any
  let mockAreaStrategy: any
  let mockPerimeterStrategy: any
  let mockBoundingBoxStrategy: any
  let mockDistanceStrategy: any
  let mockCostStrategy: any
  let mockMaterialStrategy: any

  const mockElement = {
    id: 'test-shape',
    type: ElementType.RECTANGLE,
    visible: true,
    locked: false,
    metadata: {},
    width: 10,
    height: 5,
  }

  const mockShapeModel = {
    id: 'test-shape',
    type: ElementType.RECTANGLE,
    visible: true,
    locked: false,
    metadata: {},
    width: 10,
    height: 5,
  }

  beforeEach(() => {
    // Suppress console methods for cleaner test output
    vi.spyOn(console, 'warn').mockImplementation(() => {})
    vi.spyOn(console, 'error').mockImplementation(() => {})

    // Create mock strategies
    mockAreaStrategy = {
      getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      calculateArea: vi.fn().mockReturnValue(50),
    }

    mockPerimeterStrategy = {
      getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      calculatePerimeter: vi.fn().mockReturnValue(30),
    }

    mockBoundingBoxStrategy = {
      getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      calculateBoundingBox: vi.fn().mockReturnValue({ x: 0, y: 0, width: 10, height: 5 }),
    }

    mockDistanceStrategy = {
      getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      calculateDistance: vi.fn().mockReturnValue(15),
    }

    mockCostStrategy = {
      getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      calculateCost: vi.fn().mockReturnValue(100),
    }

    mockMaterialStrategy = {
      getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      calculateMaterialAmount: vi.fn().mockReturnValue({ type: 'wood', amount: 5 }),
    }

    // Create mock registry
    mockStrategyRegistry = {
      getAreaStrategy: vi.fn().mockReturnValue(mockAreaStrategy),
      getPerimeterStrategy: vi.fn().mockReturnValue(mockPerimeterStrategy),
      getBoundingBoxStrategy: vi.fn().mockReturnValue(mockBoundingBoxStrategy),
      getDistanceStrategy: vi.fn().mockReturnValue(mockDistanceStrategy),
      getCostStrategy: vi.fn().mockReturnValue(mockCostStrategy),
      getMaterialStrategy: vi.fn().mockReturnValue(mockMaterialStrategy),
      hasStrategy: vi.fn().mockReturnValue(true),
    }

    // Create mock repository
    mockShapeRepository = {
      getById: vi.fn().mockReturnValue(mockShapeModel),
    }

    // Create mock factory
    mockElementFactory = {
      createShape: vi.fn().mockResolvedValue(mockElement),
    }

    facade = new ComputeFacade(mockStrategyRegistry, mockShapeRepository, mockElementFactory)
  })

  describe('constructor', () => {
    it('should initialize with required dependencies', () => {
      expect(facade).toBeInstanceOf(ComputeFacade)
    })
  })

  describe('computeArea', () => {
    it('should compute area successfully', async () => {
      const result = await facade.computeArea('test-shape')

      expect(mockShapeRepository.getById).toHaveBeenCalledWith('test-shape')
      expect(mockElementFactory.createShape).toHaveBeenCalledWith(ElementType.RECTANGLE, mockShapeModel)
      expect(mockStrategyRegistry.getAreaStrategy).toHaveBeenCalledWith(ElementType.RECTANGLE)
      expect(mockAreaStrategy.calculateArea).toHaveBeenCalledWith(mockElement)
      expect(result).toBe(50)
    })

    it('should throw error when shape not found in repository', async () => {
      mockShapeRepository.getById.mockReturnValue(null)

      await expect(facade.computeArea('nonexistent')).rejects.toThrow(CoreError)
    })

    it('should throw error when element factory fails', async () => {
      mockElementFactory.createShape.mockRejectedValue(new Error('Factory failed'))

      await expect(facade.computeArea('test-shape')).rejects.toThrow(CoreError)
    })
  })

  describe('computePerimeter', () => {
    it('should compute perimeter successfully', async () => {
      const result = await facade.computePerimeter('test-shape')

      expect(mockStrategyRegistry.getPerimeterStrategy).toHaveBeenCalledWith(ElementType.RECTANGLE)
      expect(mockPerimeterStrategy.calculatePerimeter).toHaveBeenCalledWith(mockElement)
      expect(result).toBe(30)
    })

    it('should throw error when shape not found', async () => {
      mockShapeRepository.getById.mockReturnValue(null)

      await expect(facade.computePerimeter('nonexistent')).rejects.toThrow(CoreError)
    })
  })

  describe('computeBoundingBox', () => {
    it('should compute bounding box successfully', async () => {
      const result = await facade.computeBoundingBox('test-shape')

      expect(mockStrategyRegistry.getBoundingBoxStrategy).toHaveBeenCalledWith(ElementType.RECTANGLE)
      expect(mockBoundingBoxStrategy.calculateBoundingBox).toHaveBeenCalledWith(mockElement)
      expect(result).toEqual({ x: 0, y: 0, width: 10, height: 5 })
    })

    it('should throw error when shape not found', async () => {
      mockShapeRepository.getById.mockReturnValue(null)

      await expect(facade.computeBoundingBox('nonexistent')).rejects.toThrow(CoreError)
    })
  })

  describe('computeDistance', () => {
    it('should compute distance between two shapes successfully', async () => {
      const result = await facade.computeDistance('shape1', 'shape2')

      expect(mockShapeRepository.getById).toHaveBeenCalledWith('shape1')
      expect(mockShapeRepository.getById).toHaveBeenCalledWith('shape2')
      expect(mockStrategyRegistry.getDistanceStrategy).toHaveBeenCalledWith(ElementType.RECTANGLE)
      expect(mockDistanceStrategy.calculateDistance).toHaveBeenCalledWith(mockElement, mockElement)
      expect(result).toBe(15)
    })

    it('should throw error when first shape not found', async () => {
      mockShapeRepository.getById.mockReturnValueOnce(null).mockReturnValueOnce(mockShapeModel)

      await expect(facade.computeDistance('nonexistent', 'shape2')).rejects.toThrow(CoreError)
    })
  })

  describe('computeCost', () => {
    it('should compute cost successfully', async () => {
      const result = await facade.computeCost('test-shape', 2.5)

      expect(mockStrategyRegistry.getCostStrategy).toHaveBeenCalledWith(ElementType.RECTANGLE)
      expect(mockCostStrategy.calculateCost).toHaveBeenCalledWith(mockElement, 2.5, undefined)
      expect(result).toBe(100)
    })

    it('should use default unit cost when invalid cost provided', async () => {
      const result = await facade.computeCost('test-shape', -1)

      expect(mockCostStrategy.calculateCost).toHaveBeenCalledWith(mockElement, 1, undefined)
      expect(result).toBe(100)
    })
  })

  describe('computeMaterial', () => {
    it('should compute material successfully', async () => {
      const result = await facade.computeMaterial('test-shape', 'wood')

      expect(mockStrategyRegistry.getMaterialStrategy).toHaveBeenCalledWith(ElementType.RECTANGLE)
      expect(mockMaterialStrategy.calculateMaterialAmount).toHaveBeenCalledWith(mockElement, 'wood', undefined)
      expect(result).toEqual({ type: 'wood', amount: 5 })
    })

    it('should throw error when shape not found', async () => {
      mockShapeRepository.getById.mockReturnValue(null)

      await expect(facade.computeMaterial('nonexistent', 'wood')).rejects.toThrow(CoreError)
    })
  })

  describe('computeSpacePlanning', () => {
    let mockSpacePlanningStrategy: any

    beforeEach(() => {
      mockSpacePlanningStrategy = {
        getSpaceType: vi.fn().mockReturnValue('kitchen'),
        calculateSpaceUtilization: vi.fn().mockReturnValue({ utilization: 0.8 }),
        checkPathwayWidth: vi.fn().mockReturnValue({ isValid: true }),
        calculateLayoutScore: vi.fn().mockReturnValue({ score: 85 }),
      }
      mockStrategyRegistry.getSpacePlanningStrategy = vi.fn().mockReturnValue(mockSpacePlanningStrategy)
    })

    it('should compute space utilization successfully', async () => {
      const result = await facade.computeSpacePlanning(['room', 'furniture1'], 'kitchen', 'utilization')

      expect(mockStrategyRegistry.getSpacePlanningStrategy).toHaveBeenCalledWith('kitchen')
      expect(mockSpacePlanningStrategy.calculateSpaceUtilization).toHaveBeenCalledWith([mockElement], mockElement)
      expect(result).toEqual({ utilization: 0.8 })
    })

    it('should compute pathway check successfully', async () => {
      const options = { pathways: [{ start: { x: 0, y: 0 }, end: { x: 10, y: 0 } }], minWidth: 1.2 }
      const result = await facade.computeSpacePlanning(['room', 'furniture1'], 'kitchen', 'pathwayCheck', options)

      expect(mockSpacePlanningStrategy.checkPathwayWidth).toHaveBeenCalledWith([mockElement], options.pathways, options.minWidth)
      expect(result).toEqual({ isValid: true })
    })

    it('should compute layout score successfully', async () => {
      const result = await facade.computeSpacePlanning(['room', 'furniture1'], 'kitchen', 'layoutScore')

      expect(mockSpacePlanningStrategy.calculateLayoutScore).toHaveBeenCalledWith([mockElement], mockElement, undefined)
      expect(result).toEqual({ score: 85 })
    })

    it('should throw error when room boundary not found', async () => {
      mockShapeRepository.getById.mockReturnValueOnce(null)

      await expect(facade.computeSpacePlanning(['nonexistent'], 'kitchen', 'utilization')).rejects.toThrow(CoreError)
    })

    it('should throw error for unknown space planning operation', async () => {
      await expect(facade.computeSpacePlanning(['room'], 'kitchen', 'unknownOperation')).rejects.toThrow(CoreError)
    })

    it('should throw error when pathways are missing for pathway check', async () => {
      const options = { minWidth: 1.2 } // missing pathways
      await expect(facade.computeSpacePlanning(['room'], 'kitchen', 'pathwayCheck', options)).rejects.toThrow(CoreError)
    })

    it('should throw error when layout score is not implemented', async () => {
      const strategyWithoutLayoutScore = {
        getSpaceType: vi.fn().mockReturnValue('kitchen'),
        calculateSpaceUtilization: vi.fn().mockReturnValue({ utilization: 0.8 }),
        checkPathwayWidth: vi.fn().mockReturnValue({ isValid: true }),
      }
      mockStrategyRegistry.getSpacePlanningStrategy = vi.fn().mockReturnValue(strategyWithoutLayoutScore)

      await expect(facade.computeSpacePlanning(['room'], 'kitchen', 'layoutScore')).rejects.toThrow(CoreError)
    })
  })

  describe('registerOperation', () => {
    it('should register custom operation', () => {
      const customOperation = vi.fn().mockResolvedValue(42)

      facade.registerOperation('customOp', customOperation)

      // Should not throw any errors
      expect(true).toBe(true)
    })
  })

  describe('isComputationSupported', () => {
    it('should return true for supported area operation', () => {
      const result = facade.isComputationSupported('area', 'rectangle')

      expect(mockStrategyRegistry.hasStrategy).toHaveBeenCalledWith('rectangle', 'area')
      expect(result).toBe(true)
    })

    it('should return true for supported space planning operation', () => {
      const result = facade.isComputationSupported('space', 'kitchen')

      expect(mockStrategyRegistry.hasStrategy).toHaveBeenCalledWith('kitchen', 'spacePlanning')
      expect(result).toBe(true)
    })

    it('should return false for unsupported operation', () => {
      mockStrategyRegistry.hasStrategy.mockReturnValue(false)

      const result = facade.isComputationSupported('unknownOp', 'rectangle')

      expect(result).toBe(false)
    })

    it('should return true for custom operations', () => {
      const customOperation = vi.fn().mockResolvedValue(42)
      facade.registerOperation('customOp', customOperation)

      const result = facade.isComputationSupported('customOp', 'rectangle')

      expect(result).toBe(true)
    })

    it('should handle errors gracefully', () => {
      mockStrategyRegistry.hasStrategy.mockImplementation(() => {
        throw new Error('Strategy error')
      })

      const result = facade.isComputationSupported('area', 'rectangle')

      expect(result).toBe(false)
    })
  })

  describe('compute', () => {
    it('should throw error for empty shape IDs', async () => {
      await expect(facade.compute('area', [])).rejects.toThrow(CoreError)
    })

    it('should compute area operation', async () => {
      const result = await facade.compute('area', ['test-shape'])

      expect(result.operation).toBe('area')
      expect(result.result).toBe(50)
      expect(result.metadata).toHaveProperty('executionTime')
      expect(result.metadata.shapeIds).toEqual(['test-shape'])
    })

    it('should compute distance operation with target shape ID', async () => {
      const result = await facade.compute('distance', ['shape1', 'shape2'])

      expect(result.operation).toBe('distance')
      expect(result.result).toBe(15)
    })

    it('should compute distance operation with target in options', async () => {
      const options = { targetShapeId: 'shape2' }
      const result = await facade.compute('distance', ['shape1'], options)

      expect(result.operation).toBe('distance')
      expect(result.result).toBe(15)
    })

    it('should throw error for distance operation without target', async () => {
      await expect(facade.compute('distance', ['shape1'])).rejects.toThrow(CoreError)
    })

    it('should compute cost operation', async () => {
      const options = { unitCost: 2.5 }
      const result = await facade.compute('cost', ['test-shape'], options)

      expect(result.operation).toBe('cost')
      expect(result.result).toBe(100)
    })

    it('should throw error for cost operation without unit cost', async () => {
      await expect(facade.compute('cost', ['test-shape'])).rejects.toThrow(CoreError)
    })

    it('should compute material operation', async () => {
      const options = { materialType: 'wood' }
      const result = await facade.compute('material', ['test-shape'], options)

      expect(result.operation).toBe('material')
      expect(result.result).toEqual({ type: 'wood', amount: 5 })
    })

    it('should throw error for material operation without material type', async () => {
      await expect(facade.compute('material', ['test-shape'])).rejects.toThrow(CoreError)
    })

    it('should compute space planning operation', async () => {
      const mockSpacePlanningStrategy = {
        calculateSpaceUtilization: vi.fn().mockReturnValue({ utilization: 0.8 }),
      }
      mockStrategyRegistry.getSpacePlanningStrategy = vi.fn().mockReturnValue(mockSpacePlanningStrategy)

      const options = { spaceType: 'kitchen' }
      const result = await facade.compute('space_utilization', ['room', 'furniture'], options)

      expect(result.operation).toBe('space_utilization')
      expect(result.result).toEqual({ utilization: 0.8 })
    })

    it('should throw error for space operation without space type', async () => {
      await expect(facade.compute('space', ['test-shape'])).rejects.toThrow(CoreError)
    })

    it('should execute custom operation', async () => {
      const customOperation = vi.fn().mockResolvedValue(42)
      facade.registerOperation('customOp', customOperation)

      const result = await facade.compute('customOp', ['test-shape'])

      expect(customOperation).toHaveBeenCalledWith(['test-shape'], undefined)
      expect(result.operation).toBe('customOp')
      expect(result.result).toBe(42)
    })

    it('should throw error for unsupported operation', async () => {
      await expect(facade.compute('unknownOp', ['test-shape'])).rejects.toThrow(CoreError)
    })

    it('should throw error when result is undefined', async () => {
      const customOperation = vi.fn().mockResolvedValue(undefined)
      facade.registerOperation('undefinedOp', customOperation)

      await expect(facade.compute('undefinedOp', ['test-shape'])).rejects.toThrow(CoreError)
    })

    it('should handle non-CoreError exceptions', async () => {
      mockAreaStrategy.calculateArea.mockImplementation(() => {
        throw new Error('Strategy error')
      })

      await expect(facade.compute('area', ['test-shape'])).rejects.toThrow(CoreError)
    })
  })
})
