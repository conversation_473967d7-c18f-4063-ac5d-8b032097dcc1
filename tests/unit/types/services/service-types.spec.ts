import type { ServiceConfig, ServiceConfiguration, ServiceEvent, ServiceResult } from '@/types/services/service-types'

import { describe, expect, it } from 'vitest'
import {
  // Alias

  ServiceStatus,
} from '@/types/services/service-types'

describe('core Service Types', () => {
  it('serviceStatus enum should contain correct string values', () => {
    expect(ServiceStatus.IDLE).toBe('idle')
    expect(ServiceStatus.INITIALIZING).toBe('initializing')
    expect(ServiceStatus.READY).toBe('ready')
    expect(ServiceStatus.BUSY).toBe('busy')
    expect(ServiceStatus.ERROR).toBe('error')
  })

  it('should allow declaration using ServiceConfiguration interface and alias', () => {
    const config: ServiceConfiguration | undefined = undefined
    expect(config).toBeUndefined()
    let configAlias: ServiceConfig | undefined
    expect(configAlias).toBeUndefined()

    // Example usage
    const exampleConfig: ServiceConfiguration = {
      enabled: true,
      autoStart: false,
      logLevel: 'info',
    }
    expect(exampleConfig.enabled).toBe(true)
    expect(exampleConfig.logLevel).toBe('info')

    configAlias = exampleConfig // Check alias compatibility
    expect(configAlias.autoStart).toBe(false)
  })

  it('should allow declaration using ServiceEvent interface', () => {
    const event: ServiceEvent | undefined = undefined
    expect(event).toBeUndefined()

    // Example usage
    const exampleEvent: ServiceEvent = {
      type: 'SERVICE_READY',
      source: 'ComputeService',
      timestamp: Date.now(),
      data: { info: 'Service started' },
    }
    expect(exampleEvent.type).toBe('SERVICE_READY')
    expect(exampleEvent.source).toBe('ComputeService')
    expect(typeof (exampleEvent.data as any).info).toBe('string')
  })

  it('should allow declaration using ServiceResult interface', () => {
    const result: ServiceResult<string> | undefined = undefined
    expect(result).toBeUndefined()

    // Example usage (success)
    const successResult: ServiceResult<number> = {
      success: true,
      data: 123,
      timestamp: Date.now(),
    }
    expect(successResult.success).toBe(true)
    expect(successResult.data).toBe(123)
    expect(successResult.error).toBeUndefined()

    // Example usage (failure)
    const failureResult: ServiceResult<null> = {
      success: false,
      error: 'Operation failed',
      timestamp: Date.now(),
    }
    expect(failureResult.success).toBe(false)
    expect(failureResult.data).toBeUndefined()
    expect(failureResult.error).toBe('Operation failed')
  })
})
