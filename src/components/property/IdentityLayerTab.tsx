/**
 * Identity Layer Tab Component
 *
 * A property panel tab that manages element identity and layer assignment.
 * This component provides controls for organizing elements within the layer
 * hierarchy and managing their z-order within layers.
 *
 * Features:
 * - Element type identification with visual badges
 * - Element name editing with consistent styling
 * - Layer assignment controls (Module, Step, Z-Level)
 * - Hierarchical layer selection with cascading dropdowns
 * - Multi-element selection support with mixed value handling
 * - Layer order controls (bring to front/send to back within layer)
 * - Element metadata display (type, creation/update timestamps)
 * - Fixed category protection for system elements
 * - Visual feedback for successful updates
 * - Unified design system with consistent spacing and typography
 *
 * Layer Hierarchy:
 * - Module (Major Category): Top-level organizational unit
 * - Step (Minor Category): Sub-category within a module
 * - Z-Level: Specific layer within a step for z-ordering
 *
 * Multi-Selection Behavior:
 * - Shows "Mixed Values" when selected elements have different values
 * - Disables controls when mixed values are detected
 * - Provides guidance text for resolving mixed selections
 *
 * @example
 * ```tsx
 * <IdentityLayerTab
 *   selectedElements={selectedShapes}
 *   getCommonValue={getPropertyValue}
 *   updateProperty={updateShapeProperty}
 *   layerPanelModules={layerModules}
 * />
 * ```
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'
import type { TaskModule } from '@/types/core/layerPanelTypes'
import React from 'react'
import { IdentitySelector } from './identity'
import { UnifiedGeometryLayout } from './shared'

/**
 * Props for the IdentityLayerTab component
 */
interface IdentityLayerTabProps {
  /** Array of currently selected elements */
  selectedElements: ShapeElement[]
  /** Function to get common property values across selected elements */
  getCommonValue: (propertyPath: string, elements?: ShapeElement[]) => unknown
  /** Function to update property values for selected elements */
  updateProperty: (propertyPath: string, value: unknown) => void
  /** Available layer panel modules for assignment */
  layerPanelModules: TaskModule[]
}

/**
 * IdentityLayerTab component that provides layer assignment and element identity management.
 *
 * This component renders a comprehensive interface for managing element layer assignments
 * and z-ordering. It handles hierarchical layer selection, multi-element scenarios,
 * and provides visual feedback for user actions.
 *
 * @param props - The component props
 * @param props.selectedElements - Array of currently selected elements
 * @param props.getCommonValue - Function to get common property values across selected elements
 * @param props.updateProperty - Function to update property values for selected elements
 * @param props.layerPanelModules - Available layer panel modules for assignment
 * @returns The rendered identity layer tab interface
 */
const IdentityLayerTab: React.FC<IdentityLayerTabProps> = ({
  selectedElements,
  getCommonValue,
  updateProperty,
  layerPanelModules,
}) => {
  // Early return if no elements are selected
  if (selectedElements.length === 0) {
    return (
      <UnifiedGeometryLayout>
        <div className="p-4 text-center text-muted-foreground">
          <p>No elements selected</p>
          <p className="text-xs mt-2">Please select one or more elements to edit their identity and layer properties.</p>
        </div>
      </UnifiedGeometryLayout>
    )
  }

  return (
    <UnifiedGeometryLayout>
      <IdentitySelector
        selectedElements={selectedElements}
        getCommonValue={getCommonValue}
        updateProperty={updateProperty}
        layerPanelModules={layerPanelModules}
      />
    </UnifiedGeometryLayout>
  )
}

export default IdentityLayerTab
