/**
 * Canvas Layer Utilities
 *
 * @remarks
 * This module provides utility functions for working with canvas layers,
 * such as retrieving layers within a specific group, getting their display names,
 * or fetching their associated colors. It relies on type definitions from
 * ` '@/types/core/canvasTypes`.
 *
 * @module lib/utils/canvas/layerUtils
 */

import type { CanvasLayer, LayerGroup } from '@/types/core/canvasTypes'
import { LayerColors, LayerDisplayNames, LayerGroupMapping } from '@/types/core/canvasTypes'

/**
 * Retrieves all canvas layers belonging to a specified layer group.
 *
 * @param group - The {@link LayerGroup} to filter by.
 * @returns An array of {@link CanvasLayer} enums that are part of the specified group.
 */
export function getLayersInGroup(group: LayerGroup): CanvasLayer[] {
  return Object.entries(LayerGroupMapping)
    .filter(([_, layerGroup]) => layerGroup === group)
    .map(([layer]) => layer as CanvasLayer)
}

/**
 * Gets the human-readable display name for a given canvas layer.
 *
 * @param layer - The {@link CanvasLayer} enum member.
 * @returns The display name string for the layer, or the layer's enum value if no display name is defined.
 */
export function getLayerDisplayName(layer: CanvasLayer): string {
  return LayerDisplayNames[layer] || layer
}

/**
 * Gets the hexadecimal color code associated with a given canvas layer.
 *
 * @param layer - The {@link CanvasLayer} enum member.
 * @returns The color code string (e.g., "#RRGGBB") for the layer, or a default black ('#000000') if no color is defined.
 */
export function getLayerColor(layer: CanvasLayer): string {
  return LayerColors[layer] || '#000000'
}
