// Import all interfaces from the config file
import type {
  CoreConfig,
  ShapeConfigInterface,
  ShapeCreationModesConfig,
  ShapeDimensionsConfig,
  ShapeExportOptionsConfig,
  ShapeGridOptionsConfig,
  ShapePropertiesConfig,
  ShapeRenderingOptionsConfig,
  ShapeSelectionOptionsConfig,
} from '@/types/core/config'

import { describe, expect, it } from 'vitest'

describe('core Configuration Types', () => {
  // Since these are only type definitions (interfaces),
  // we can't directly test their "values".
  // The test primarily ensures that the types can be imported without error,
  // confirming they are correctly defined and exported.

  it('should allow declaration of shape properties configuration', () => {
    // Create an object of ShapePropertiesConfig type
    const shapeProps: ShapePropertiesConfig = {
      strokeWidth: 1,
      strokeColor: '#000',
      fillColor: '#fff',
      opacity: 1,
      rotation: 0,
    }

    // Verify object is created correctly
    expect(shapeProps.strokeWidth).toBe(1)
    expect(shapeProps.strokeColor).toBe('#000')
    expect(shapeProps.fillColor).toBe('#fff')
    expect(shapeProps.opacity).toBe(1)
    expect(shapeProps.rotation).toBe(0)
  })

  it('should allow declaration of shape dimensions configuration', () => {
    // Create an object of ShapeDimensionsConfig type
    const shapeDims: ShapeDimensionsConfig = {
      unit: 1,
      rectangleWidth: 100,
      rectangleHeight: 50,
      ellipseRadiusX: 50,
      ellipseRadiusY: 25,
      circleRadius: 50,
      polygonRadius: 50,
      lineLength: 100,
    }

    // Verify object is created correctly
    expect(shapeDims.unit).toBe(1)
    expect(shapeDims.rectangleWidth).toBe(100)
    expect(shapeDims.rectangleHeight).toBe(50)
    expect(shapeDims.ellipseRadiusX).toBe(50)
    expect(shapeDims.ellipseRadiusY).toBe(25)
    expect(shapeDims.circleRadius).toBe(50)
    expect(shapeDims.polygonRadius).toBe(50)
    expect(shapeDims.lineLength).toBe(100)
  })

  it('should allow declaration of shape creation modes configuration', () => {
    // Create an object of ShapeCreationModesConfig type
    const creationModes: ShapeCreationModesConfig = {
      RECTANGLE: 'rectangle',
      SQUARE: 'rectangle',
      ELLIPSE: 'ellipse',
      CIRCLE: 'ellipse',
    }

    // Verify object is created correctly
    expect(creationModes.RECTANGLE).toBe('rectangle')
    expect(creationModes.SQUARE).toBe('rectangle')
    expect(creationModes.ELLIPSE).toBe('ellipse')
    expect(creationModes.CIRCLE).toBe('ellipse')
  })

  it('should allow declaration of shape rendering options configuration', () => {
    // Create an object of ShapeRenderingOptionsConfig type
    const renderingOptions: ShapeRenderingOptionsConfig = {
      strokeLinecap: 'round',
      strokeLinejoin: 'round',
      strokeMiterlimit: 4,
      fillRule: 'nonzero',
      pointerEvents: 'all',
    }

    // Verify object is created correctly
    expect(renderingOptions.strokeLinecap).toBe('round')
    expect(renderingOptions.strokeLinejoin).toBe('round')
    expect(renderingOptions.strokeMiterlimit).toBe(4)
    expect(renderingOptions.fillRule).toBe('nonzero')
    expect(renderingOptions.pointerEvents).toBe('all')
  })

  it('should allow declaration of shape selection options configuration', () => {
    // Create an object of ShapeSelectionOptionsConfig type
    const selectionOptions: ShapeSelectionOptionsConfig = {
      handleSize: 8,
      handleFillColor: '#fff',
      handleStrokeColor: '#000',
      handleStrokeWidth: 1,
      selectionBoxStrokeColor: '#00f',
      selectionBoxStrokeWidth: 1,
      selectionBoxStrokeDasharray: '5,5',
      selectionBoxFillColor: 'rgba(0,0,255,0.1)',
    }

    // Verify object is created correctly
    expect(selectionOptions.handleSize).toBe(8)
    expect(selectionOptions.handleFillColor).toBe('#fff')
    expect(selectionOptions.handleStrokeColor).toBe('#000')
    expect(selectionOptions.handleStrokeWidth).toBe(1)
    expect(selectionOptions.selectionBoxStrokeColor).toBe('#00f')
    expect(selectionOptions.selectionBoxStrokeWidth).toBe(1)
    expect(selectionOptions.selectionBoxStrokeDasharray).toBe('5,5')
    expect(selectionOptions.selectionBoxFillColor).toBe('rgba(0,0,255,0.1)')
  })

  it('should allow declaration of shape grid options configuration', () => {
    // Create an object of ShapeGridOptionsConfig type
    const gridOptions: ShapeGridOptionsConfig = {
      gridSize: 10,
      gridColor: '#ccc',
      gridOpacity: 0.5,
      snapToGrid: true,
      showGrid: true,
    }

    // Verify object is created correctly
    expect(gridOptions.gridSize).toBe(10)
    expect(gridOptions.gridColor).toBe('#ccc')
    expect(gridOptions.gridOpacity).toBe(0.5)
    expect(gridOptions.snapToGrid).toBe(true)
    expect(gridOptions.showGrid).toBe(true)
  })

  it('should allow declaration of shape export options configuration', () => {
    // Create an object of ShapeExportOptionsConfig type
    const exportOptions: ShapeExportOptionsConfig = {
      defaultFormat: 'svg',
      svgPrecision: 2,
      pngScale: 2,
      jpgQuality: 0.9,
    }

    // Verify object is created correctly
    expect(exportOptions.defaultFormat).toBe('svg')
    expect(exportOptions.svgPrecision).toBe(2)
    expect(exportOptions.pngScale).toBe(2)
    expect(exportOptions.jpgQuality).toBe(0.9)
  })

  it('should allow declaration of complete shape configuration', () => {
    // Create an object of ShapeConfigInterface type
    const shapeConfig: ShapeConfigInterface = {
      properties: {
        strokeWidth: 1,
        strokeColor: '#000',
        fillColor: '#fff',
        opacity: 1,
        rotation: 0,
      },
      dimensions: {
        unit: 1,
        rectangleWidth: 100,
        rectangleHeight: 50,
        ellipseRadiusX: 50,
        ellipseRadiusY: 25,
        circleRadius: 50,
        polygonRadius: 50,
        lineLength: 100,
      },
      creationModes: {
        RECTANGLE: 'rectangle',
        SQUARE: 'rectangle',
        ELLIPSE: 'ellipse',
        CIRCLE: 'ellipse',
      },
      renderingOptions: {
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        strokeMiterlimit: 4,
        fillRule: 'nonzero',
        pointerEvents: 'all',
      },
      selectionOptions: {
        handleSize: 8,
        handleFillColor: '#fff',
        handleStrokeColor: '#000',
        handleStrokeWidth: 1,
        selectionBoxStrokeColor: '#00f',
        selectionBoxStrokeWidth: 1,
        selectionBoxStrokeDasharray: '5,5',
        selectionBoxFillColor: 'rgba(0,0,255,0.1)',
      },
      gridOptions: {
        gridSize: 10,
        gridColor: '#ccc',
        gridOpacity: 0.5,
        snapToGrid: true,
        showGrid: true,
      },
      exportOptions: {
        defaultFormat: 'svg',
        svgPrecision: 2,
        pngScale: 2,
        jpgQuality: 0.9,
      },
    }

    // Verify object is created correctly
    expect(shapeConfig.properties.strokeWidth).toBe(1)
    expect(shapeConfig.dimensions.rectangleWidth).toBe(100)
    expect(shapeConfig.creationModes.RECTANGLE).toBe('rectangle')
    expect(shapeConfig.renderingOptions.strokeLinecap).toBe('round')
    expect(shapeConfig.selectionOptions.handleSize).toBe(8)
    expect(shapeConfig.gridOptions.gridSize).toBe(10)
    expect(shapeConfig.exportOptions.defaultFormat).toBe('svg')
  })

  it('should allow declaration of core configuration', () => {
    // Create an object of CoreConfig type
    const coreConfig: CoreConfig = {
      factory: {
        defaultShapeSize: 100,
        idPrefix: 'shape',
        generateUniqueId: true,
      },
      validator: {
        strictMode: true,
        maxShapeSize: 1000,
        minShapeSize: 1,
      },
      compute: {
        transformPrecision: 2,
        useHardwareAcceleration: true,
        batchOperations: true,
      },
      render: {
        defaultStrokeWidth: 1,
        defaultFillColor: '#fff',
        defaultStrokeColor: '#000',
        highlightColor: '#00f',
        selectionColor: '#f00',
        renderQuality: 'high',
      },
      units: {
        drawingScale: 1,
        unitType: 'px',
      },
    }

    // Verify object is created correctly
    expect(coreConfig.factory.defaultShapeSize).toBe(100)
    expect(coreConfig.validator.strictMode).toBe(true)
    expect(coreConfig.compute.transformPrecision).toBe(2)
    expect(coreConfig.render.defaultStrokeWidth).toBe(1)
    expect(coreConfig.units.drawingScale).toBe(1)
  })

  it('shapeCreationModesConfig should allow assignment of valid configurations', () => {
    const config: ShapeCreationModesConfig = {
      RECTANGLE: 'rectangle',
      SQUARE: 'rectangle',
      ELLIPSE: 'ellipse',
      CIRCLE: 'ellipse',
    }

    expect(config.RECTANGLE).toBe('rectangle')
    expect(config.SQUARE).toBe('rectangle')
    expect(config.ELLIPSE).toBe('ellipse')
    expect(config.CIRCLE).toBe('ellipse')
  })

  it('shapeCreationModesConfig should allow partial configurations', () => {
    const partialConfig: Partial<ShapeCreationModesConfig> = {
      CIRCLE: 'ellipse',
    }
    expect(partialConfig.CIRCLE).toBe('ellipse')
    expect(partialConfig.RECTANGLE).toBeUndefined()
  })

  it('other config interfaces should be usable as types', () => {
    const shapeConfig: ShapeConfigInterface | undefined = undefined
    expect(shapeConfig).toBeUndefined()

    let coreConfig: CoreConfig | undefined
    expect(coreConfig).toBeUndefined()
  })
})
