/**
 * Drag and Drop Hook
 *
 * This module provides a React hook for managing drag and drop operations
 * within the canvas. It handles element dragging from toolbars, drop validation,
 * and element creation on drop events.
 *
 * @module hooks/useDragAndDrop
 */

import type Point from '../types/core/element/geometry/point'
import type { ElementType } from '../types/core/elementDefinitions'
import type { InitialElementProperties } from '@/config/defaultElementSettings'
import { useCallback, useState } from 'react'

/**
 * Information passed during drag and drop
 */
export interface DraggedElementInfo {
  type: ElementType
  id: string
  properties?: Record<string, unknown>
}

// Interface for the data parsed from JSON string in dataTransfer
interface ParsedDragData {
  elementType: ElementType
  properties: InitialElementProperties
  name?: string
  id?: string
  elementSpecificData?: Record<string, unknown>
  from?: string
}

// Updated interface for structured drop data passed to the callback
export interface DroppedElementData {
  elementType: ElementType
  position: Point
  properties: InitialElementProperties
  name?: string
  id?: string // assetId
  elementSpecificData?: Record<string, unknown>
}

/**
 * Hook for managing drag and drop of elements from the sidebar to the canvas
 */
export function useDragAndDrop() {
  // Currently dragged element
  const [draggedElement, setDraggedElement] = useState<DraggedElementInfo | null>(null)

  // Flag for when element is being dragged over the canvas
  const [isDraggingOverCanvas, setIsDraggingOverCanvas] = useState(false)

  /**
   * Start dragging an element from the asset sidebar
   */
  const onDragStart = useCallback((elementType: ElementType, assetId: string, initialProperties?: Record<string, unknown>) => {
    setDraggedElement({
      type: elementType,
      id: assetId,
      properties: initialProperties,
    })
  }, [])

  /**
   * End the dragging operation
   */
  const endDrag = useCallback(() => {
    setDraggedElement(null)
    setIsDraggingOverCanvas(false)
  }, [])

  /**
   * When element is dragged over the canvas
   */
  const onDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsDraggingOverCanvas(true)
  }, [])

  /**
   * When element leaves the canvas area
   */
  const onDragLeave = useCallback(() => {
    setIsDraggingOverCanvas(false)
  }, [])

  /**
   * When element is dropped on the canvas
   */
  const onDropCanvas = useCallback(
    (
      event: React.DragEvent<HTMLDivElement>,
      canvasRef: React.RefObject<HTMLDivElement>,
      onElementDropped: (data: DroppedElementData) => void,
    ) => {
      event.preventDefault()
      const rawData = event.dataTransfer.getData('application/json')
      setIsDraggingOverCanvas(false)

      if (rawData === '' || canvasRef.current === null) {
        endDrag()
        return
      }

      try {
        const parsedData = JSON.parse(rawData) as ParsedDragData

        const canvasRect = canvasRef.current.getBoundingClientRect()
        const x = event.clientX - canvasRect.left
        const y = event.clientY - canvasRect.top

        const dropData: DroppedElementData = {
          elementType: parsedData.elementType,
          position: { x, y },
          properties: parsedData.properties,
          name: parsedData.name,
          id: parsedData.id,
          elementSpecificData: parsedData.elementSpecificData,
        }

        onElementDropped(dropData)
      }
      catch (error) {
        console.error('Error processing dropped data:', error instanceof Error ? error.message : String(error))
      }

      endDrag()
    },
    [endDrag],
  )

  return {
    draggedElement,
    isDraggingOverCanvas,
    onDragStart,
    endDrag,
    onDragOver,
    onDragLeave,
    onDropCanvas,
  }
}
