/**
 * Service Status Types
 *
 * This file defines the possible states of a service.
 *
 * @module types/services/core
 */

/**
 * Possible states of a service.
 */
export enum ServiceStatus {
  /** Service is idle and not processing any requests */
  IDLE = 'idle',
  /** Service is starting up */
  INITIALIZING = 'initializing',
  /** Service is ready to process requests */
  READY = 'ready',
  /** Service is currently processing requests */
  BUSY = 'busy',
  /** Service has encountered an error */
  ERROR = 'error',
}
