# Installation Guide

RenoPilot.JS.Shapes2 is a web-based application, which means there's typically no traditional "installation" process required on your local computer to use it. You can access it directly through your web browser.

## Accessing the Application

1.  **Open Your Web Browser:**
    *   We recommend using a modern web browser such as Google Chrome, Mozilla Firefox, Safari, or Microsoft Edge for the best experience.

2.  **Navigate to the Application URL:**
    *   If the application is deployed publicly, you will be provided with a URL (web address) to access it. For example:
        `https://your-renopilot-instance.com`
    *   If you are running a local development version (see [Development Setup](../development/setup.md)), the application is typically accessible at:
        `http://localhost:5173` (or another port specified in your terminal).

## System Requirements (for accessing the web application)

*   **A Modern Web Browser:**
    *   Google Chrome (latest versions)
    *   Mozilla Firefox (latest versions)
    *   Safari (latest versions)
    *   Microsoft Edge (Chromium-based, latest versions)
*   **Internet Connection:** Required to load the application initially. Once loaded, some functionalities might work offline depending on the application's design (e.g., if it uses Progressive Web App features, though this is not explicitly stated in the current project scope).
*   **Screen Resolution:** A minimum screen resolution of 1024x768 is recommended for optimal usability, though the application aims to be responsive.

## No Local Installation Needed

As a web application, all the necessary files and code are hosted on a web server and downloaded by your browser when you visit the URL. You do not need to download or install any software packages onto your computer to use RenoPilot.JS.Shapes2.

## For Developers / Self-Hosting

If you are a developer looking to run the application locally or host it on your own server, please refer to the following documents:

*   **Development Environment Setup:** [docs/development/setup.md](../development/setup.md)
*   **Deployment Guide:** [docs/development/deployment.md](../development/deployment.md)

These guides provide detailed instructions on how to clone the project, install dependencies, run the development server, and build the application for production deployment.

## Troubleshooting Access Issues

*   **Check your internet connection.**
*   **Ensure you have the correct URL.** Typos are common.
*   **Try clearing your browser's cache and cookies** for the site if you encounter loading problems or outdated content.
*   **Try a different web browser** to see if the issue is browser-specific.
*   **Check the browser's developer console** (usually by pressing F12) for any error messages that might provide clues.

Enjoy using RenoPilot.JS.Shapes2!