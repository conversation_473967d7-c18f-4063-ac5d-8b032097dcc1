import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { CoreError, createError, DefaultErrorLogger, ErrorType, logError } from '@/core/errors'

describe('coreError', () => {
  describe('constructor', () => {
    it('should create a CoreError with the specified type and message', () => {
      const error = new CoreError(ErrorType.VALIDATION_FAILED, 'Validation failed')

      expect(error).toBeInstanceOf(CoreError)
      expect(error).toBeInstanceOf(Error)
      expect(error.type).toBe(ErrorType.VALIDATION_FAILED)
      expect(error.message).toBe('Validation failed')
      expect(error.name).toBe('CoreError')
    })

    it('should include metadata when provided', () => {
      const metadata = { shapeId: 'test-shape', property: 'width' }
      const error = new CoreError(ErrorType.VALIDATION_FAILED, 'Validation failed', metadata)

      expect(error.metadata).toEqual(metadata)
    })

    it('should set the name property', () => {
      const error = new CoreError(ErrorType.VALIDATION_FAILED, 'Validation failed')

      expect(error.name).toBe('CoreError')
    })

    it('should include the error message in the toString output', () => {
      const error = new CoreError(ErrorType.VALIDATION_FAILED, 'Validation failed')

      expect(error.toString()).toContain('Validation failed')
    })
  })

  describe('createError', () => {
    it('should create a CoreError instance', () => {
      const error = createError(ErrorType.VALIDATION_FAILED, 'Validation failed')

      expect(error).toBeInstanceOf(CoreError)
      expect(error.type).toBe(ErrorType.VALIDATION_FAILED)
      expect(error.message).toBe('Validation failed')
    })

    it('should include metadata when provided', () => {
      const metadata = { shapeId: 'test-shape', property: 'width' }
      const error = createError(ErrorType.VALIDATION_FAILED, 'Validation failed', metadata)

      expect(error.metadata).toEqual(metadata)
    })
  })

  describe('defaultErrorLogger', () => {
    let consoleSpy: any

    beforeEach(() => {
      consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    })

    afterEach(() => {
      consoleSpy.mockRestore()
    })

    it('should log error message', () => {
      const logger = new DefaultErrorLogger()
      const error = new Error('Test error')

      logger.logError(error)

      expect(consoleSpy).toHaveBeenCalledWith('Error:', 'Test error')
    })

    it('should log stack trace if available', () => {
      const logger = new DefaultErrorLogger()
      const error = new Error('Test error')

      logger.logError(error)

      expect(consoleSpy).toHaveBeenCalledWith('Stack:', error.stack)
    })

    it('should log context if provided', () => {
      const logger = new DefaultErrorLogger()
      const error = new Error('Test error')
      const context = { operation: 'test', shapeId: 'test-shape' }

      logger.logError(error, context)

      expect(consoleSpy).toHaveBeenCalledWith('Context:', context)
    })

    it('should log metadata for CoreError', () => {
      const logger = new DefaultErrorLogger()
      const metadata = { shapeId: 'test-shape', property: 'width' }
      const error = new CoreError(ErrorType.VALIDATION_FAILED, 'Validation failed', metadata)

      logger.logError(error)

      expect(consoleSpy).toHaveBeenCalledWith('Metadata:', metadata)
    })
  })

  describe('logError', () => {
    let consoleSpy: any

    beforeEach(() => {
      consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    })

    afterEach(() => {
      consoleSpy.mockRestore()
    })

    it('should log error using the global errorLogger', () => {
      const error = new Error('Test error')

      logError(error)

      expect(consoleSpy).toHaveBeenCalledWith('Error:', 'Test error')
    })

    it('should log context if provided', () => {
      const error = new Error('Test error')
      const context = { operation: 'test', shapeId: 'test-shape' }

      logError(error, context)

      expect(consoleSpy).toHaveBeenCalledWith('Context:', context)
    })
  })
})
