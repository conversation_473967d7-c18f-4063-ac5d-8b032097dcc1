import type { Point } from '@/types/core/element/geometry/point'
import type { ValidationError } from '@/types/core/element/validator'
import { describe, expect, it, vi } from 'vitest'
import * as validationUtils from '@/core/validator/common/validationUtils'

describe('validationUtils', () => {
  describe('isValidPoint', () => {
    it('should return true for valid points', () => {
      const validPoint: Point = { x: 10, y: 20 }
      expect(validationUtils.isValidPoint(validPoint)).toBe(true)
    })

    it('should return false for points with missing x', () => {
      const invalidPoint: any = { y: 20 }
      expect(validationUtils.isValidPoint(invalidPoint)).toBe(false)
    })

    it('should return false for points with missing y', () => {
      const invalidPoint: any = { x: 10 }
      expect(validationUtils.isValidPoint(invalidPoint)).toBe(false)
    })

    it('should return false for points with non-numeric x', () => {
      const invalidPoint: any = { x: 'ten', y: 20 }
      expect(validationUtils.isValidPoint(invalidPoint)).toBe(false)
    })

    it('should return false for points with non-numeric y', () => {
      const invalidPoint: any = { x: 10, y: 'twenty' }
      expect(validationUtils.isValidPoint(invalidPoint)).toBe(false)
    })

    it('should return false for null or undefined', () => {
      expect(validationUtils.isValidPoint(null)).toBe(false)
      expect(validationUtils.isValidPoint(undefined)).toBe(false)
    })
  })

  describe('isValidColor', () => {
    it('should return true for valid hex colors', () => {
      expect(validationUtils.isValidColor('#000')).toBe(true)
      expect(validationUtils.isValidColor('#000000')).toBe(true)
      expect(validationUtils.isValidColor('#fff')).toBe(true)
      expect(validationUtils.isValidColor('#FFFFFF')).toBe(true)
    })

    it('should return true for valid rgb colors', () => {
      expect(validationUtils.isValidColor('rgb(0,0,0)')).toBe(true)
      expect(validationUtils.isValidColor('rgb(255, 255, 255)')).toBe(true)
    })

    it('should return true for valid rgba colors', () => {
      expect(validationUtils.isValidColor('rgba(0,0,0,0)')).toBe(true)
      expect(validationUtils.isValidColor('rgba(255, 255, 255, 1)')).toBe(true)
      expect(validationUtils.isValidColor('rgba(255, 255, 255, 0.5)')).toBe(true)
    })

    it('should return true for valid color names', () => {
      expect(validationUtils.isValidColor('red')).toBe(true)
      expect(validationUtils.isValidColor('blue')).toBe(true)
      expect(validationUtils.isValidColor('transparent')).toBe(true)
    })

    it('should return false for invalid colors', () => {
      expect(validationUtils.isValidColor('#gggggg')).toBe(false)
      expect(validationUtils.isValidColor('rgb(300,0,0)')).toBe(false)
      expect(validationUtils.isValidColor('rgba(0,0,0,2)')).toBe(false)
      expect(validationUtils.isValidColor('not-a-color')).toBe(false)
    })

    it('should return false for null or undefined', () => {
      expect(validationUtils.isValidColor(null)).toBe(false)
      expect(validationUtils.isValidColor(undefined)).toBe(false)
    })
  })

  describe('isPolygonClosed', () => {
    it('should return true for closed polygons', () => {
      const closedPolygon: Point[] = [
        { x: 0, y: 0 },
        { x: 10, y: 0 },
        { x: 10, y: 10 },
        { x: 0, y: 0 }, // Same as first point
      ]
      expect(validationUtils.isPolygonClosed(closedPolygon)).toBe(true)
    })

    it('should return true for nearly closed polygons within epsilon', () => {
      // Mock console.warn to avoid test pollution
      const warnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      // Use a larger epsilon value for the test
      const almostClosedPolygon: Point[] = [
        { x: 0, y: 0 },
        { x: 10, y: 0 },
        { x: 10, y: 10 },
        { x: 0.0009, y: 0.0009 }, // Very close to first point
      ]
      expect(validationUtils.isPolygonClosed(almostClosedPolygon, 0.01)).toBe(true)

      warnSpy.mockRestore()
    })

    it('should return false for open polygons', () => {
      const openPolygon: Point[] = [
        { x: 0, y: 0 },
        { x: 10, y: 0 },
        { x: 10, y: 10 },
        { x: 0, y: 10 }, // Not the same as first point
      ]
      expect(validationUtils.isPolygonClosed(openPolygon)).toBe(false)
    })

    it('should return false for polygons with less than 3 points', () => {
      const tooFewPoints: Point[] = [
        { x: 0, y: 0 },
        { x: 10, y: 0 },
      ]
      expect(validationUtils.isPolygonClosed(tooFewPoints)).toBe(false)
    })

    it('should return false for null or undefined', () => {
      expect(validationUtils.isPolygonClosed(null as any)).toBe(false)
      expect(validationUtils.isPolygonClosed(undefined as any)).toBe(false)
    })
  })

  describe('isValueInRange', () => {
    it('should return true for values within range', () => {
      expect(validationUtils.isValueInRange(5, 0, 10)).toBe(true)
      expect(validationUtils.isValueInRange(0, 0, 10)).toBe(true) // Min boundary
      expect(validationUtils.isValueInRange(10, 0, 10)).toBe(true) // Max boundary
    })

    it('should return false for values outside range', () => {
      expect(validationUtils.isValueInRange(-1, 0, 10)).toBe(false)
      expect(validationUtils.isValueInRange(11, 0, 10)).toBe(false)
    })

    it('should return false for non-numeric values', () => {
      expect(validationUtils.isValueInRange(Number.NaN, 0, 10)).toBe(false)
      expect(validationUtils.isValueInRange(Infinity, 0, 10)).toBe(false)
      expect(validationUtils.isValueInRange(null as any, 0, 10)).toBe(false)
      expect(validationUtils.isValueInRange(undefined as any, 0, 10)).toBe(false)
    })
  })

  describe('createValidationResult', () => {
    it('should create a valid result with no errors', () => {
      const result = validationUtils.createValidationResult(true)
      expect(result.valid).toBe(true)
      expect(result.errors).toBeUndefined()
      expect(result.message).toBeUndefined()
    })

    it('should create an invalid result with string errors', () => {
      const result = validationUtils.createValidationResult(false, ['Error 1', 'Error 2'])
      expect(result.valid).toBe(false)
      expect(result.errors).toHaveLength(2)
      expect(result.errors?.[0].code).toBe('GENERIC_VALIDATION')
      expect(result.errors?.[0].message).toBe('Error 1')
      expect(result.message).toBe('Error 1') // First error message used as top-level message
    })

    it('should create an invalid result with ValidationError objects', () => {
      const errors: ValidationError[] = [
        { code: 'ERROR_1', message: 'First error', path: 'prop1' },
        { code: 'ERROR_2', message: 'Second error', path: 'prop2' },
      ]
      const result = validationUtils.createValidationResult(false, errors)
      expect(result.valid).toBe(false)
      expect(result.errors).toHaveLength(2)
      expect(result.errors?.[0].code).toBe('ERROR_1')
      expect(result.errors?.[1].code).toBe('ERROR_2')
      expect(result.message).toBe('First error') // First error message used as top-level message
    })

    it('should use provided message over error messages', () => {
      const errors: ValidationError[] = [
        { code: 'ERROR_1', message: 'First error', path: 'prop1' },
      ]
      const result = validationUtils.createValidationResult(false, errors, 'Custom message')
      expect(result.valid).toBe(false)
      expect(result.message).toBe('Custom message')
    })

    it('should handle mixed string and ValidationError objects', () => {
      const errors: (ValidationError | string)[] = [
        'String error',
        { code: 'ERROR_1', message: 'Object error', path: 'prop1' },
      ]
      const result = validationUtils.createValidationResult(false, errors)
      expect(result.valid).toBe(false)
      expect(result.errors).toHaveLength(2)
      expect(result.errors?.[0].code).toBe('GENERIC_VALIDATION')
      expect(result.errors?.[0].message).toBe('String error')
      expect(result.errors?.[1].code).toBe('ERROR_1')
    })

    it('should skip invalid items in errors array', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      const errors: any[] = [
        'Valid string',
        null,
        { not: 'a valid error object' },
        { code: 'VALID', message: 'Valid error object' },
      ]
      const result = validationUtils.createValidationResult(false, errors)
      expect(result.valid).toBe(false)
      expect(result.errors).toHaveLength(2)
      expect(consoleSpy).toHaveBeenCalledTimes(2) // Two invalid items
      consoleSpy.mockRestore()
    })
  })
})
