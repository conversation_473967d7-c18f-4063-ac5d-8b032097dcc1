/**
 * Compute Events Type Definitions
 *
 * This file defines the event types related to computation operations
 * on elements, such as area calculation, distance measurement, etc.
 *
 * @module types/services/events
 */

import type { AppEventType } from './eventTypes'
import type { ComputeOperation, ComputeOperationType } from '@/types/core/compute'

/**
 * Defines options for transforming an element, such as rotation, scaling, and translation.
 */
export interface TransformOptions {
  /** The rotation angle in degrees. */
  rotation?: number
  /** The scale factor along the X-axis. */
  scaleX?: number
  /** The scale factor along the Y-axis. */
  scaleY?: number
  /** The translation amount along the X-axis. */
  translateX?: number
  /** The translation amount along the Y-axis. */
  translateY?: number
}

/**
 * Represents an event related to a computation task.
 * This can be a request to start a computation, a progress update,
 * completion of a computation, or an error during computation.
 */
export interface ComputeEvent {
  /** The type of compute event. */
  type: AppEventType.ComputeRequest | AppEventType.ComputeProgress | AppEventType.ComputeComplete | AppEventType.ComputeError
  /** The payload associated with the compute event. */
  payload: {
    /** The specific computation operation being performed. */
    operation: ComputeOperation | ComputeOperationType
    /** The ID of the element being computed, if applicable. */
    elementId?: string
    /** The IDs of the shapes involved in the computation, if applicable (e.g. for multi-shape operations or when result pertains to multiple shapes). */
    shapeIds?: string[]
    /** Additional options for the computation. */
    options?: unknown
    /** The result of the computation, if successful. */
    result?: unknown
    /** An error object, if the computation failed. */
    error?: Error
  }
}

/**
 * Validation Events
 *
 * These are event types for validation-related events, not to be confused with
 * the core validator types which define the actual validation interfaces and results.
 */
export interface ValidationEvent {
  /** The type of validation event. */
  type: AppEventType.ValidateShape | AppEventType.ValidateTemplate | AppEventType.ValidateOperation | AppEventType.ValidateConstraint
  /** The payload associated with the validation event. */
  payload: {
    /** The ID of the target being validated. */
    targetId: string
    /** The validation rules being applied. */
    rules: unknown // Consider defining a more specific type for rules
    /** Contextual information for the validation. */
    context?: {
      /** The context type (e.g., creation, editing). */
      type: 'creation' | 'editing' | 'template' | 'constraint'
      /** IDs of related shapes, if any. */
      relatedShapes?: string[]
      /** Specific constraints being validated, if any. */
      constraints?: unknown // Consider defining a more specific type for constraints
    }
    /** The result of the validation. */
    result?: {
      /** Indicates if the validation was successful. */
      isValid: boolean
      /** A list of error messages, if validation failed. */
      errors?: string[]
      /** A list of warning messages. */
      warnings?: string[]
    }
  }
}

/**
 * Batch Events
 *
 * These are event types for batch operation events, which allow multiple
 * operations to be processed as a single transaction.
 */
export interface BatchEvent {
  /** The type of batch event. */
  type: AppEventType.BatchStart | AppEventType.BatchProgress | AppEventType.BatchComplete | AppEventType.BatchError
  /** The payload associated with the batch event. */
  payload: {
    /** A unique identifier for the batch operation. */
    batchId: string
    /** An array of operations included in the batch. */
    operations: Array<{
      /** The type of individual operation within the batch. */
      type: string // Could be an AppEventType or other operation identifier
      /** The payload for the individual operation. */
      payload: unknown
    }>
    /** The progress of the batch operation (e.g., a percentage from 0 to 100). */
    progress?: number
    /** The results of the individual operations within the batch. */
    results?: Array<unknown>
    /** An error message, if the batch operation failed. */
    error?: string
  }
}

/**
 * Transform Event
 *
 * Event for requesting shape transformations.
 * Uses the standard TransformOptions type.
 */
export interface TransformEvent {
  /** The type of transform event, typically indicating a compute transform operation. */
  type: AppEventType.ComputeTransform
  /** The payload associated with the transform event. */
  payload: {
    /** An array of shape IDs to be transformed. */
    shapeIds: string[]
    /** The transformation options to apply. */
    transformOptions: TransformOptions
  }
}
