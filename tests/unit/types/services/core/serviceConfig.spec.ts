import type { ServiceConfig, ServiceConfiguration } from '@/types/services/core/serviceConfig'
import { describe, expect, it } from 'vitest'

describe('service Configuration Types', () => {
  describe('serviceConfiguration', () => {
    it('should allow creating a basic configuration', () => {
      const config: ServiceConfiguration = {
        enabled: true,
        autoStart: true,
        logLevel: 'info',
      }

      expect(config.enabled).toBe(true)
      expect(config.autoStart).toBe(true)
      expect(config.logLevel).toBe('info')
    })

    it('should allow all log level options', () => {
      // Test all valid log levels
      const debugConfig: ServiceConfiguration = {
        enabled: true,
        autoStart: true,
        logLevel: 'debug',
      }
      expect(debugConfig.logLevel).toBe('debug')

      const infoConfig: ServiceConfiguration = {
        enabled: true,
        autoStart: true,
        logLevel: 'info',
      }
      expect(infoConfig.logLevel).toBe('info')

      const warnConfig: ServiceConfiguration = {
        enabled: true,
        autoStart: true,
        logLevel: 'warn',
      }
      expect(warnConfig.logLevel).toBe('warn')

      const errorConfig: ServiceConfiguration = {
        enabled: true,
        autoStart: true,
        logLevel: 'error',
      }
      expect(errorConfig.logLevel).toBe('error')
    })

    it('should allow additional service-specific options', () => {
      const configWithOptions: ServiceConfiguration = {
        enabled: true,
        autoStart: false,
        logLevel: 'warn',
        timeout: 5000,
        retryCount: 3,
        customSetting: 'value',
      }

      expect(configWithOptions.enabled).toBe(true)
      expect(configWithOptions.autoStart).toBe(false)
      expect(configWithOptions.logLevel).toBe('warn')
      expect(configWithOptions.timeout).toBe(5000)
      expect(configWithOptions.retryCount).toBe(3)
      expect(configWithOptions.customSetting).toBe('value')
    })
  })

  describe('serviceConfig', () => {
    it('should be an alias for ServiceConfiguration', () => {
      const config: ServiceConfig = {
        enabled: true,
        autoStart: true,
        logLevel: 'info',
      }

      expect(config.enabled).toBe(true)
      expect(config.autoStart).toBe(true)
      expect(config.logLevel).toBe('info')

      // Verify it works with additional properties
      const extendedConfig: ServiceConfig = {
        enabled: false,
        autoStart: false,
        logLevel: 'error',
        maxRetries: 5,
        cacheSize: 100,
      }

      expect(extendedConfig.enabled).toBe(false)
      expect(extendedConfig.autoStart).toBe(false)
      expect(extendedConfig.logLevel).toBe('error')
      expect(extendedConfig.maxRetries).toBe(5)
      expect(extendedConfig.cacheSize).toBe(100)
    })
  })
})
