import type { AppEventMap, EventBus } from '@/types/services/events'
import type { KeyboardEventContext, KeyboardServiceConfig } from '@/types/services/keyboard'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { KeyboardServiceImpl } from '@/services/input/keyboard-service'

// Mock the registry
vi.mock('@/services/core/registry', () => ({
  getService: vi.fn(),
  ServiceId: {
    Logger: 'logger',
  },
}))

// Mock the event bus helpers
vi.mock('@/services/core/event-bus/helpers/publishers/inputPublishers', () => ({
  publishKeyPressed: vi.fn(),
  publishKeyReleased: vi.fn(),
}))

describe('keyboardServiceImpl', () => {
  let keyboardService: KeyboardServiceImpl
  let mockEventBus: EventBus<AppEventMap>
  let mockLogger: LoggerService
  let mockAddEventListener: ReturnType<typeof vi.fn>
  let mockRemoveEventListener: ReturnType<typeof vi.fn>

  beforeEach(() => {
    mockEventBus = {
      publish: vi.fn(),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
      clear: vi.fn(),
    } as unknown as EventBus<AppEventMap>

    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    }

    // Mock document event listeners
    mockAddEventListener = vi.fn()
    mockRemoveEventListener = vi.fn()
    Object.defineProperty(document, 'addEventListener', {
      value: mockAddEventListener,
      writable: true,
    })
    Object.defineProperty(document, 'removeEventListener', {
      value: mockRemoveEventListener,
      writable: true,
    })

    keyboardService = new KeyboardServiceImpl(mockEventBus, mockLogger)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('constructor', () => {
    it('should initialize with event bus and logger', () => {
      expect(keyboardService).toBeInstanceOf(KeyboardServiceImpl)
      expect(mockLogger.info).toHaveBeenCalledWith('[KeyboardServiceImpl] Initialized.')
    })

    it('should set up document event listeners', () => {
      // The KeyboardServiceImpl doesn't automatically set up listeners in constructor
      // Instead, it provides methods to add listeners manually
      expect(typeof keyboardService.addKeyDownListener).toBe('function')
      expect(typeof keyboardService.addKeyUpListener).toBe('function')
    })
  })

  describe('configuration', () => {
    it('should configure keyboard service', () => {
      const config: Partial<KeyboardServiceConfig> = {
        enabled: false,
        preventDefaultForRegistered: false,
      }

      keyboardService.setConfig(config)

      const currentConfig = keyboardService.getConfig()
      expect(currentConfig.enabled).toBe(false)
      expect(currentConfig.preventDefaultForRegistered).toBe(false)
    })

    it('should merge configuration with defaults', () => {
      keyboardService.setConfig({ enabled: false })

      const config = keyboardService.getConfig()
      expect(config.enabled).toBe(false)
      expect(config.preventDefaultForRegistered).toBe(true) // default value
    })

    it('should get current configuration', () => {
      const config = keyboardService.getConfig()

      expect(config).toHaveProperty('enabled')
      expect(config).toHaveProperty('preventDefaultForRegistered')
      expect(config).toHaveProperty('stopPropagationForRegistered')
    })
  })

  describe('key binding management', () => {
    it('should register key binding', () => {
      const callback = vi.fn()
      const keyCombo = 'ctrl+s'

      const unregister = keyboardService.registerKeyBinding(keyCombo, callback)

      expect(typeof unregister).toBe('function')
      const bindings = keyboardService.getKeyBindings()
      expect(bindings.has(keyCombo)).toBe(true)
    })

    it('should register key binding with options', () => {
      const callback = vi.fn()
      const keyCombo = 'ctrl+s'
      const options = {
        preventDefault: true,
        stopPropagation: false,
        onKeyUp: true,
        description: 'Save document',
      }

      keyboardService.registerKeyBinding(keyCombo, callback, options)

      const bindings = keyboardService.getKeyBindings()
      const binding = bindings.get(keyCombo)
      expect(binding?.options).toEqual(expect.objectContaining(options))
    })

    it('should unregister key binding', () => {
      const callback = vi.fn()
      const keyCombo = 'ctrl+s'

      keyboardService.registerKeyBinding(keyCombo, callback)
      keyboardService.unregisterKeyBinding(keyCombo)

      const bindings = keyboardService.getKeyBindings()
      expect(bindings.has(keyCombo)).toBe(false)
    })

    it('should return unregister function', () => {
      const callback = vi.fn()
      const keyCombo = 'ctrl+s'

      const unregister = keyboardService.registerKeyBinding(keyCombo, callback)
      expect(typeof unregister).toBe('function')

      unregister()
      const bindings = keyboardService.getKeyBindings()
      expect(bindings.has(keyCombo)).toBe(false)
    })

    it('should handle multiple key bindings', () => {
      keyboardService.registerKeyBinding('ctrl+s', vi.fn())
      keyboardService.registerKeyBinding('ctrl+c', vi.fn())

      const bindings = keyboardService.getKeyBindings()
      expect(bindings.size).toBe(2)
      expect(bindings.has('ctrl+s')).toBe(true)
      expect(bindings.has('ctrl+c')).toBe(true)
    })
  })

  describe('shortcut management', () => {
    it('should register shortcut', () => {
      const callback = vi.fn()
      const shortcut = {
        id: 'save',
        keys: { key: 's', ctrlKey: true },
        callback,
        description: 'Save document',
      }

      keyboardService.registerShortcut(shortcut)

      const registeredShortcut = keyboardService.getShortcut('save')
      expect(registeredShortcut).toBeDefined()
      expect(registeredShortcut?.id).toBe('save')
    })

    it('should unregister shortcut', () => {
      const shortcut = {
        id: 'save',
        keys: { key: 's', ctrlKey: true },
        callback: vi.fn(),
        description: 'Save document',
      }

      keyboardService.registerShortcut(shortcut)
      const result = keyboardService.unregisterShortcut('save')

      expect(result).toBe(true)
      const registeredShortcut = keyboardService.getShortcut('save')
      expect(registeredShortcut).toBeUndefined()
    })

    it('should return false when unregistering non-existent shortcut', () => {
      const result = keyboardService.unregisterShortcut('nonexistent')
      expect(result).toBe(false)
    })

    it('should get all shortcuts', () => {
      const shortcut1 = {
        id: 'save',
        keys: { key: 's', ctrlKey: true },
        callback: vi.fn(),
      }
      const shortcut2 = {
        id: 'copy',
        keys: { key: 'c', ctrlKey: true },
        callback: vi.fn(),
      }

      keyboardService.registerShortcut(shortcut1)
      keyboardService.registerShortcut(shortcut2)

      const shortcuts = keyboardService.getAllShortcuts()
      expect(shortcuts).toHaveLength(2)
      expect(shortcuts.find(s => s.id === 'save')).toBeDefined()
      expect(shortcuts.find(s => s.id === 'copy')).toBeDefined()
    })
  })

  describe('context management', () => {
    it('should set keyboard context', () => {
      const context: KeyboardEventContext = {
        source: 'canvas',
        elementId: 'shape-1',
        metadata: { mode: 'edit' },
      }

      keyboardService.setContext(context)

      const currentContext = keyboardService.getContext()
      expect(currentContext).toEqual(expect.objectContaining(context))
    })

    it('should merge context with existing context', () => {
      keyboardService.setContext({ source: 'canvas' })
      keyboardService.setContext({ elementId: 'shape-1' })

      const context = keyboardService.getContext()
      expect(context.source).toBe('canvas')
      expect(context.elementId).toBe('shape-1')
    })

    it('should get current context', () => {
      const context = keyboardService.getContext()
      expect(context).toHaveProperty('source')
    })
  })

  describe('event handling', () => {
    it('should handle key events manually', () => {
      const keydownEvent = new KeyboardEvent('keydown', {
        key: 's',
        code: 'KeyS',
        ctrlKey: true,
      })

      const result = keyboardService.handleKeyEvent(keydownEvent)
      expect(result).toBe(true)
    })

    it('should handle keyup events manually', () => {
      const keyupEvent = new KeyboardEvent('keyup', {
        key: 's',
        code: 'KeyS',
        ctrlKey: true,
      })

      const result = keyboardService.handleKeyEvent(keyupEvent)
      expect(result).toBe(true)
    })

    it('should return false for unsupported event types', () => {
      const invalidEvent = new KeyboardEvent('keypress', {
        key: 's',
      })

      const result = keyboardService.handleKeyEvent(invalidEvent)
      expect(result).toBe(false)
    })

    it('should add key down listener', () => {
      const listener = vi.fn()

      const removeListener = keyboardService.addKeyDownListener(listener)

      expect(typeof removeListener).toBe('function')
      expect(mockAddEventListener).toHaveBeenCalledWith('keydown', expect.any(Function))
    })

    it('should add key up listener', () => {
      const listener = vi.fn()

      const removeListener = keyboardService.addKeyUpListener(listener)

      expect(typeof removeListener).toBe('function')
      expect(mockAddEventListener).toHaveBeenCalledWith('keyup', expect.any(Function))
    })
  })

  describe('utility methods', () => {
    it('should enable shortcut', () => {
      const shortcut = {
        id: 'save',
        keys: { key: 's', ctrlKey: true },
        callback: vi.fn(),
        enabled: false,
      }

      keyboardService.registerShortcut(shortcut)
      const result = keyboardService.enableShortcut('save')

      expect(result).toBe(true)
      const registeredShortcut = keyboardService.getShortcut('save')
      expect(registeredShortcut?.enabled).toBe(true)
    })

    it('should disable shortcut', () => {
      const shortcut = {
        id: 'save',
        keys: { key: 's', ctrlKey: true },
        callback: vi.fn(),
        enabled: true,
      }

      keyboardService.registerShortcut(shortcut)
      const result = keyboardService.disableShortcut('save')

      expect(result).toBe(true)
      const registeredShortcut = keyboardService.getShortcut('save')
      expect(registeredShortcut?.enabled).toBe(false)
    })

    it('should return false when enabling non-existent shortcut', () => {
      const result = keyboardService.enableShortcut('nonexistent')
      expect(result).toBe(false)
    })

    it('should return false when disabling non-existent shortcut', () => {
      const result = keyboardService.disableShortcut('nonexistent')
      expect(result).toBe(false)
    })
  })

  describe('integration scenarios', () => {
    it('should handle complete key binding workflow', () => {
      const callback = vi.fn()
      const keyCombo = 'ctrl+s'

      // Register binding
      keyboardService.registerKeyBinding(keyCombo, callback)

      // Simulate key event
      const keyEvent = new KeyboardEvent('keydown', {
        key: 's',
        code: 'KeyS',
        ctrlKey: true,
      })

      keyboardService.handleKeyEvent(keyEvent)

      // Verify binding was registered
      const bindings = keyboardService.getKeyBindings()
      expect(bindings.has(keyCombo)).toBe(true)
    })

    it('should handle shortcut registration and context switching', () => {
      const shortcut = {
        id: 'save',
        keys: { key: 's', ctrlKey: true },
        callback: vi.fn(),
        context: 'canvas',
      }

      keyboardService.registerShortcut(shortcut)
      keyboardService.setContext({ source: 'canvas' })

      const registeredShortcut = keyboardService.getShortcut('save')
      const context = keyboardService.getContext()

      expect(registeredShortcut).toBeDefined()
      expect(registeredShortcut?.id).toBe('save')
      expect(context.source).toBe('canvas')
    })

    it('should handle service lifecycle', () => {
      // Configure service
      keyboardService.setConfig({ enabled: true })

      // Register bindings
      keyboardService.registerKeyBinding('ctrl+s', vi.fn())
      keyboardService.registerShortcut({
        id: 'copy',
        keys: { key: 'c', ctrlKey: true },
        callback: vi.fn(),
      })

      // Set context
      keyboardService.setContext({ source: 'canvas' })

      // Verify state
      const config = keyboardService.getConfig()
      expect(config.enabled).toBe(true)
      expect(keyboardService.getKeyBindings().size).toBe(1)
      expect(keyboardService.getAllShortcuts()).toHaveLength(1)

      // Verify context
      const context = keyboardService.getContext()
      expect(context.source).toBe('canvas')
    })

    it('should handle multiple event listeners', () => {
      const listener1 = vi.fn()
      const listener2 = vi.fn()

      const removeListener1 = keyboardService.addKeyDownListener(listener1)
      const removeListener2 = keyboardService.addKeyUpListener(listener2)

      expect(typeof removeListener1).toBe('function')
      expect(typeof removeListener2).toBe('function')

      // Clean up
      removeListener1()
      removeListener2()
    })
  })
})
