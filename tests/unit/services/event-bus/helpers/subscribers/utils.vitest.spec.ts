import { describe, expect, it, vi } from 'vitest'
import {
  getRenderEventType,
  getShapeCreateEventType,
  getShapeDeleteEventType,
  getShapeEditEventType,
  typedSubscribe,
} from '@/services/event-bus/helpers/subscribers/utils'
import { AppEventType } from '@/types/services/events'

describe('subscriber Utilities', () => {
  describe('typedSubscribe', () => {
    it('should call eventBus.subscribe with correct parameters', () => {
      const mockEventBus = {
        subscribe: vi.fn().mockReturnValue(() => {}),
      }
      const mockHandler = vi.fn()
      const mockOptions = { once: true }

      const unsubscribe = typedSubscribe(
        mockEventBus as any,
        AppEventType.SHAPE_CREATE_REQUEST,
        mockHandler,
        mockOptions,
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.SHAPE_CREATE_REQUEST,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should return the unsubscribe function from eventBus.subscribe', () => {
      const mockUnsubscribe = vi.fn()
      const mockEventBus = {
        subscribe: vi.fn().mockReturnValue(mockUnsubscribe),
      }

      const unsubscribe = typedSubscribe(
        mockEventBus as any,
        AppEventType.SHAPE_CREATE_REQUEST,
        vi.fn(),
      )

      expect(unsubscribe).toBe(mockUnsubscribe)
    })
  })

  describe('getShapeCreateEventType', () => {
    it('should return correct event type for request phase', () => {
      expect(getShapeCreateEventType('request')).toBe(AppEventType.SHAPE_CREATE_REQUEST)
    })

    it('should return correct event type for validate phase', () => {
      expect(getShapeCreateEventType('validate')).toBe(AppEventType.SHAPE_CREATE_VALIDATE)
    })

    it('should return correct event type for complete phase', () => {
      expect(getShapeCreateEventType('complete')).toBe(AppEventType.SHAPE_CREATE_COMPLETE)
    })

    it('should return correct event type for error phase', () => {
      expect(getShapeCreateEventType('error')).toBe(AppEventType.SHAPE_CREATE_ERROR)
    })

    it('should throw error for invalid phase', () => {
      expect(() => getShapeCreateEventType('invalid')).toThrow('Invalid phase: invalid')
    })
  })

  describe('getShapeEditEventType', () => {
    it('should return correct event type for request phase', () => {
      expect(getShapeEditEventType('request')).toBe(AppEventType.SHAPE_EDIT_REQUEST)
    })

    it('should return correct event type for compute phase', () => {
      expect(getShapeEditEventType('compute')).toBe(AppEventType.SHAPE_EDIT_COMPUTE)
    })

    it('should return correct event type for complete phase', () => {
      expect(getShapeEditEventType('complete')).toBe(AppEventType.SHAPE_EDIT_COMPLETE)
    })

    it('should return correct event type for error phase', () => {
      expect(getShapeEditEventType('error')).toBe(AppEventType.SHAPE_EDIT_ERROR)
    })

    it('should throw error for invalid phase', () => {
      expect(() => getShapeEditEventType('invalid')).toThrow('Invalid phase: invalid')
    })
  })

  describe('getShapeDeleteEventType', () => {
    it('should return correct event type for request phase', () => {
      expect(getShapeDeleteEventType('request')).toBe(AppEventType.SHAPE_DELETE_REQUEST)
    })

    it('should return correct event type for complete phase', () => {
      expect(getShapeDeleteEventType('complete')).toBe(AppEventType.SHAPE_DELETE_COMPLETE)
    })

    it('should return correct event type for error phase', () => {
      expect(getShapeDeleteEventType('error')).toBe(AppEventType.SHAPE_DELETE_ERROR)
    })

    it('should throw error for invalid phase', () => {
      expect(() => getShapeDeleteEventType('invalid')).toThrow('Invalid phase: invalid')
    })
  })

  describe('getRenderEventType', () => {
    it('should return correct event type for trigger phase', () => {
      expect(getRenderEventType('trigger')).toBe(AppEventType.RENDER_TRIGGER)
    })

    it('should return correct event type for start phase', () => {
      expect(getRenderEventType('start')).toBe(AppEventType.RENDER_START)
    })

    it('should return correct event type for complete phase', () => {
      expect(getRenderEventType('complete')).toBe(AppEventType.RENDER_COMPLETE)
    })

    it('should return correct event type for error phase', () => {
      expect(getRenderEventType('error')).toBe(AppEventType.RENDER_ERROR)
    })

    it('should throw error for invalid phase', () => {
      expect(() => getRenderEventType('invalid')).toThrow('Invalid phase: invalid')
    })
  })
})
