/**
 * Quadratic Geometry Component
 *
 * Implements the standardized geometry layout for Quadratic Bézier curve elements
 * according to the design document specifications.
 *
 * Layout:
 * - Key Points: Start, Control, and End points with canvas position editing
 * - Calculation & Cost (unified layout)
 *
 * Note: Quadratic elements use control points instead of standard position controls.
 * They don't support rotation as the curve orientation is defined by the control points.
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ElementType } from '@/types/core/elementDefinitions'
import { GeometrySection } from '../shared'

interface QuadraticGeometryProps {
  /** The quadratic curve element being edited */
  element: ShapeElement
  /** Function to get input values with proper formatting */
  getValueForInput: (path: string) => string
  /** Function to get placeholder values */
  getPlaceholderForInput: (path: string) => string
  /** Function to handle input changes */
  handleInputChange: (path: string, value: string, isNumeric?: boolean, convertToPixels?: boolean) => void
  /** Unit name for display (e.g., "mm", "cm") */
  unitName: string
  /** Function to convert internal units to display units */
  toDisplayUnit?: (value: number) => number
  /** Function to convert display units to internal units */
  toInternalUnit?: (value: number) => number
  /** Function to update element properties directly */
  updateProperty?: (path: string, value: unknown) => void
}

/**
 * QuadraticGeometry component for editing quadratic Bézier curve properties.
 *
 * Provides a standardized layout for quadratic curve geometry editing
 * with proper grouping and responsive design. Quadratic curves use
 * three control points: start, control, and end points with canvas position editing.
 */
// Stable function references to avoid infinite render loops
const identityFunction = (value: number) => value

// Type guard functions for safe property access
function isPoint(value: unknown): value is { x: number, y: number, z?: number } {
  return (
    typeof value === 'object'
    && value !== null
    && typeof (value as { x: unknown }).x === 'number'
    && typeof (value as { y: unknown }).y === 'number'
  )
}

function getPointProperty(properties: Record<string, unknown> | undefined, key: string, defaultValue: { x: number, y: number, z?: number }): { x: number, y: number, z?: number } {
  const value = properties?.[key]
  return isPoint(value) ? value : defaultValue
}

function getPosition(element: ShapeElement): { x: number, y: number, z?: number } {
  return isPoint(element.position) ? element.position : { x: 0, y: 0 }
}

export function QuadraticGeometry({
  element,
  getValueForInput: _getValueForInput,
  getPlaceholderForInput: _getPlaceholderForInput,
  handleInputChange: _handleInputChange,
  unitName,
  toDisplayUnit = identityFunction,
  toInternalUnit = identityFunction,
  updateProperty,
}: QuadraticGeometryProps) {
  // Verify this is a quadratic element
  if (element.type !== ElementType.QUADRATIC) {
    return null
  }

  // Note: We don't need to calculate coordinates here since getValueForInput
  // and handleInputChange already handle the coordinate conversion in GeometryTransformTab

  return (
    <>
      {/* Key Points - Canvas Position */}
      <GeometrySection type="special" title="Key Points" columns={1}>
        <div className="space-y-2">
          {/* Header */}
          <Label className="text-sm font-medium">
            Canvas Position (
            {unitName}
            )
          </Label>

          {/* Points list */}
          <div className="space-y-2">
            {/* Start Point */}
            <div className="grid grid-cols-3 gap-2 items-center">
              <div className="text-xs text-muted-foreground">
                Start Point
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">X</Label>
                <Input
                  type="number"
                  value={(() => {
                    // 🔧 修复：使用类型安全的属性访问
                    const startPoint = getPointProperty(element.properties, 'start', { x: 0, y: 0, z: 0 })
                    const position = getPosition(element)
                    const absoluteX = position.x + startPoint.x
                    return Number(toDisplayUnit(absoluteX)).toFixed(2)
                  })()}
                  onChange={(e) => {
                    // 🔧 修复：使用类型安全的属性访问
                    const newCanvasValue = Number.parseFloat(e.target.value) || 0
                    const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
                    const position = getPosition(element)
                    const relativeX = newCanvasPixelValue - position.x

                    const currentStart = getPointProperty(element.properties, 'start', { x: 0, y: 0, z: 0 })
                    const updatedStart = { ...currentStart, x: relativeX }

                    // 🔧 关键修复：使用直接store更新，确保坐标控制正确工作
                    if (window.__ZUSTAND_SHAPES_STORE__) {
                      const shapesStore = window.__ZUSTAND_SHAPES_STORE__
                      const state = shapesStore.getState()

                      // 创建更新后的元素
                      const updatedElement = {
                        ...element,
                        properties: {
                          ...element.properties,
                          start: updatedStart,
                        },
                      }

                      // 更新store中的shapes数组
                      const newShapes = state.shapes.map(s =>
                        s.id === element.id ? updatedElement : s,
                      )

                      // 保持选中状态
                      shapesStore.setState({
                        ...state,
                        shapes: newShapes,
                        selectedShapeIds: [element.id],
                      })
                    }
                  }}
                  placeholder="X"
                  className="h-8 text-xs"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Y</Label>
                <Input
                  type="number"
                  value={(() => {
                    // 🔧 修复：使用类型安全的属性访问
                    const startPoint = getPointProperty(element.properties, 'start', { x: 0, y: 0, z: 0 })
                    const position = getPosition(element)
                    const absoluteY = position.y + startPoint.y
                    return Number(toDisplayUnit(absoluteY)).toFixed(2)
                  })()}
                  onChange={(e) => {
                    // 🔧 修复：使用类型安全的属性访问
                    const newCanvasValue = Number.parseFloat(e.target.value) || 0
                    const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
                    const position = getPosition(element)
                    const relativeY = newCanvasPixelValue - position.y

                    const currentStart = getPointProperty(element.properties, 'start', { x: 0, y: 0, z: 0 })
                    const updatedStart = { ...currentStart, y: relativeY }

                    // 🔧 关键修复：使用直接store更新，确保坐标控制正确工作
                    if (window.__ZUSTAND_SHAPES_STORE__) {
                      const shapesStore = window.__ZUSTAND_SHAPES_STORE__
                      const state = shapesStore.getState()

                      // 创建更新后的元素
                      const updatedElement = {
                        ...element,
                        properties: {
                          ...element.properties,
                          start: updatedStart,
                        },
                      }

                      // 更新store中的shapes数组
                      const newShapes = state.shapes.map(s =>
                        s.id === element.id ? updatedElement : s,
                      )

                      // 保持选中状态
                      shapesStore.setState({
                        ...state,
                        shapes: newShapes,
                        selectedShapeIds: [element.id],
                      })
                    }
                  }}
                  placeholder="Y"
                  className="h-8 text-xs"
                />
              </div>
            </div>

            {/* Control Point */}
            <div className="grid grid-cols-3 gap-2 items-center">
              <div className="text-xs text-muted-foreground">
                Control Point
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">X</Label>
                <Input
                  type="number"
                  value={(() => {
                    // 🔧 修复：使用类型安全的属性访问
                    const controlPoint = getPointProperty(element.properties, 'control', { x: 50, y: -50, z: 0 })
                    const position = getPosition(element)
                    const absoluteX = position.x + controlPoint.x
                    return Number(toDisplayUnit(absoluteX)).toFixed(2)
                  })()}
                  onChange={(e) => {
                    // 🔧 修复：使用类型安全的属性访问
                    const newCanvasValue = Number.parseFloat(e.target.value) || 0
                    const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
                    const position = getPosition(element)
                    const relativeX = newCanvasPixelValue - position.x

                    const currentControl = getPointProperty(element.properties, 'control', { x: 50, y: -50, z: 0 })
                    const updatedControl = { ...currentControl, x: relativeX }

                    updateProperty?.('properties.control', updatedControl)
                  }}
                  placeholder="X"
                  className="h-8 text-xs"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Y</Label>
                <Input
                  type="number"
                  value={(() => {
                    // 🔧 修复：使用类型安全的属性访问
                    const controlPoint = getPointProperty(element.properties, 'control', { x: 50, y: -50, z: 0 })
                    const position = getPosition(element)
                    const absoluteY = position.y + controlPoint.y
                    return Number(toDisplayUnit(absoluteY)).toFixed(2)
                  })()}
                  onChange={(e) => {
                    // 🔧 修复：使用类型安全的属性访问
                    const newCanvasValue = Number.parseFloat(e.target.value) || 0
                    const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
                    const position = getPosition(element)
                    const relativeY = newCanvasPixelValue - position.y

                    const currentControl = getPointProperty(element.properties, 'control', { x: 50, y: -50, z: 0 })
                    const updatedControl = { ...currentControl, y: relativeY }

                    updateProperty?.('properties.control', updatedControl)
                  }}
                  placeholder="Y"
                  className="h-8 text-xs"
                />
              </div>
            </div>

            {/* End Point */}
            <div className="grid grid-cols-3 gap-2 items-center">
              <div className="text-xs text-muted-foreground">
                End Point
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">X</Label>
                <Input
                  type="number"
                  value={(() => {
                    // 🔧 修复：使用类型安全的属性访问
                    const endPoint = getPointProperty(element.properties, 'end', { x: 100, y: 0, z: 0 })
                    const position = getPosition(element)
                    const absoluteX = position.x + endPoint.x
                    return Number(toDisplayUnit(absoluteX)).toFixed(2)
                  })()}
                  onChange={(e) => {
                    // 🔧 修复：使用类型安全的属性访问
                    const newCanvasValue = Number.parseFloat(e.target.value) || 0
                    const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
                    const position = getPosition(element)
                    const relativeX = newCanvasPixelValue - position.x

                    const currentEnd = getPointProperty(element.properties, 'end', { x: 100, y: 0, z: 0 })
                    const updatedEnd = { ...currentEnd, x: relativeX }

                    // 🔧 关键修复：使用直接store更新，确保坐标控制正确工作
                    if (window.__ZUSTAND_SHAPES_STORE__) {
                      const shapesStore = window.__ZUSTAND_SHAPES_STORE__
                      const state = shapesStore.getState()

                      // 创建更新后的元素
                      const updatedElement = {
                        ...element,
                        properties: {
                          ...element.properties,
                          end: updatedEnd,
                        },
                      }

                      // 更新store中的shapes数组
                      const newShapes = state.shapes.map(s =>
                        s.id === element.id ? updatedElement : s,
                      )

                      // 保持选中状态
                      shapesStore.setState({
                        ...state,
                        shapes: newShapes,
                        selectedShapeIds: [element.id],
                      })
                    }
                  }}
                  placeholder="X"
                  className="h-8 text-xs"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Y</Label>
                <Input
                  type="number"
                  value={(() => {
                    // 🔧 修复：使用类型安全的属性访问
                    const endPoint = getPointProperty(element.properties, 'end', { x: 100, y: 0, z: 0 })
                    const position = getPosition(element)
                    const absoluteY = position.y + endPoint.y
                    return Number(toDisplayUnit(absoluteY)).toFixed(2)
                  })()}
                  onChange={(e) => {
                    // 🔧 修复：使用类型安全的属性访问
                    const newCanvasValue = Number.parseFloat(e.target.value) || 0
                    const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
                    const position = getPosition(element)
                    const relativeY = newCanvasPixelValue - position.y

                    const currentEnd = getPointProperty(element.properties, 'end', { x: 100, y: 0, z: 0 })
                    const updatedEnd = { ...currentEnd, y: relativeY }

                    // 🔧 关键修复：使用直接store更新，确保坐标控制正确工作
                    if (window.__ZUSTAND_SHAPES_STORE__) {
                      const shapesStore = window.__ZUSTAND_SHAPES_STORE__
                      const state = shapesStore.getState()

                      // 创建更新后的元素
                      const updatedElement = {
                        ...element,
                        properties: {
                          ...element.properties,
                          end: updatedEnd,
                        },
                      }

                      // 更新store中的shapes数组
                      const newShapes = state.shapes.map(s =>
                        s.id === element.id ? updatedElement : s,
                      )

                      // 保持选中状态
                      shapesStore.setState({
                        ...state,
                        shapes: newShapes,
                        selectedShapeIds: [element.id],
                      })
                    }
                  }}
                  placeholder="Y"
                  className="h-8 text-xs"
                />
              </div>
            </div>
          </div>
        </div>
      </GeometrySection>

      {/* Note: No standard Position or Transform sections for quadratic curves */}
      {/* Curve position and orientation are fully defined by the control points */}
    </>
  )
}
