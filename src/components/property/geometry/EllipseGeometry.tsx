/**
 * Ellipse Geometry Component
 *
 * Implements the standardized geometry layout for Ellipse elements
 * according to the design document specifications.
 *
 * Layout:
 * - Position (2 columns): Position X, Position Y
 * - Scale (2 columns): Radius X, Radius Y
 * - Transform (1 column): Rotation
 * - Calculation & Cost (unified layout)
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'

import { ElementType } from '@/types/core/elementDefinitions'
import { GeometrySection, PropertyField } from '../shared'

interface EllipseGeometryProps {
  /** The ellipse element being edited */
  element: ShapeElement
  /** Function to get input values with proper formatting */
  getValueForInput: (path: string) => string
  /** Function to get placeholder values */
  getPlaceholderForInput: (path: string) => string
  /** Function to handle input changes */
  handleInputChange: (path: string, value: string, isNumeric?: boolean, convertToPixels?: boolean) => void
  /** Unit name for display (e.g., "mm", "cm") */
  unitName: string
}

/**
 * EllipseGeometry component for editing ellipse properties.
 *
 * Provides a standardized layout for ellipse geometry editing
 * with proper grouping and responsive design. Uses separate
 * radius controls for X and Y axes.
 */
export function EllipseGeometry({
  element,
  getValueForInput,
  getPlaceholderForInput,
  handleInputChange,
  unitName,
}: EllipseGeometryProps) {
  // Verify this is an ellipse element
  if (element.type !== ElementType.ELLIPSE) {
    return null
  }

  return (
    <>
      {/* Position (2 columns) */}
      <GeometrySection type="position" columns={2}>
        <PropertyField
          label={`Position X (${unitName})`}
          type="number"
          value={getValueForInput('position.x')}
          onChange={value => handleInputChange('position.x', value)}
          placeholder={getPlaceholderForInput('position.x')}
        />
        <PropertyField
          label={`Position Y (${unitName})`}
          type="number"
          value={getValueForInput('position.y')}
          onChange={value => handleInputChange('position.y', value)}
          placeholder={getPlaceholderForInput('position.y')}
        />
      </GeometrySection>

      {/* Scale (2 columns) */}
      <GeometrySection type="dimensions" columns={2}>
        <PropertyField
          label={`Radius X (${unitName})`}
          type="number"
          value={getValueForInput('radiusX')}
          onChange={value => handleInputChange('radiusX', value)}
          placeholder="0"
          helpText="Horizontal radius"
        />
        <PropertyField
          label={`Radius Y (${unitName})`}
          type="number"
          value={getValueForInput('radiusY')}
          onChange={value => handleInputChange('radiusY', value)}
          placeholder="0"
          helpText="Vertical radius"
        />
      </GeometrySection>

      {/* Transform (1 column) */}
      <GeometrySection type="transform" columns={1}>
        <PropertyField
          label="Rotation (°)"
          type="number"
          value={getValueForInput('rotation')}
          onChange={value => handleInputChange('rotation', value, true, false)}
          placeholder="0"
        />
      </GeometrySection>
    </>
  )
}
