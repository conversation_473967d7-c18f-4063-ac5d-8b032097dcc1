/**
 * Image Geometry Component
 *
 * Implements the standardized geometry layout for Image elements
 * according to the design document specifications.
 *
 * Layout:
 * - Position (2 columns): Position X, Position Y
 * - Scale (2 columns): Width, Height
 * - Transform (1 column): Rotation
 * - Calculation & Cost (unified layout)
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'

import { ElementType } from '@/types/core/elementDefinitions'
import { GeometrySection, PropertyField } from '../shared'

interface ImageGeometryProps {
  /** The image element being edited */
  element: ShapeElement
  /** Function to get input values with proper formatting */
  getValueForInput: (path: string) => string
  /** Function to get placeholder values */
  getPlaceholderForInput: (path: string) => string
  /** Function to handle input changes */
  handleInputChange: (path: string, value: string, isNumeric?: boolean, convertToPixels?: boolean) => void
  /** Unit name for display (e.g., "mm", "cm") */
  unitName: string
}

/**
 * ImageGeometry component for editing image properties.
 *
 * Provides a standardized layout for image geometry editing
 * with proper grouping and responsive design. Images support
 * standard position, dimensions, and rotation controls.
 */
export function ImageGeometry({
  element,
  getValueForInput,
  getPlaceholderForInput,
  handleInputChange,
  unitName,
}: ImageGeometryProps) {
  // Verify this is an image element
  if (element.type !== ElementType.IMAGE) {
    return null
  }

  return (
    <>
      {/* Position (2 columns) */}
      <GeometrySection type="position" columns={2}>
        <PropertyField
          label={`Position X (${unitName})`}
          type="number"
          value={getValueForInput('position.x')}
          onChange={value => handleInputChange('position.x', value)}
          placeholder={getPlaceholderForInput('position.x')}
        />
        <PropertyField
          label={`Position Y (${unitName})`}
          type="number"
          value={getValueForInput('position.y')}
          onChange={value => handleInputChange('position.y', value)}
          placeholder={getPlaceholderForInput('position.y')}
        />
      </GeometrySection>

      {/* Scale (2 columns) */}
      <GeometrySection type="dimensions" columns={2}>
        <PropertyField
          label={`Width (${unitName})`}
          type="number"
          value={getValueForInput('width')}
          onChange={value => handleInputChange('width', value)}
          placeholder="0"
          helpText="Image display width"
        />
        <PropertyField
          label={`Height (${unitName})`}
          type="number"
          value={getValueForInput('height')}
          onChange={value => handleInputChange('height', value)}
          placeholder="0"
          helpText="Image display height"
        />
      </GeometrySection>

      {/* Transform (1 column) */}
      <GeometrySection type="transform" columns={1}>
        <PropertyField
          label="Rotation (°)"
          type="number"
          value={getValueForInput('rotation')}
          onChange={value => handleInputChange('rotation', value, true, false)}
          placeholder="0"
        />
      </GeometrySection>
    </>
  )
}
