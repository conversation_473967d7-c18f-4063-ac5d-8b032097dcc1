/**
 * Specialized Error Classes
 *
 * This file implements specialized error classes based on the interfaces defined in types/services/errors.
 */

import type {
  ErrorContext,
  IComputeError,
  IElementNotFoundError,
  IFactoryError,
  IUnsupportedElementTypeError,
  IUnsupportedOperationError,
  IValidationResultError,
  IValidatorError,
} from '@/types/services/errors'
import {
  ErrorSeverity,
  ErrorType,
} from '@/types/services/errors' // Corrected path alias
import { CoreError } from './coreError' // Import CoreError implementation from local coreError.ts

/**
 * Error thrown when a factory operation fails
 */
export class FactoryError extends CoreError implements IFactoryError {
  constructor(message: string, context?: ErrorContext) {
    super(ErrorType.Runtime, message, ErrorSeverity.High, context)
    this.name = 'FactoryError'
  }
}

/**
 * Error thrown when validation fails
 */
export class ValidatorError extends CoreError implements IValidatorError {
  constructor(message: string, context?: ErrorContext) {
    super(ErrorType.Validation, message, ErrorSeverity.Medium, context)
    this.name = 'ValidatorError'
  }
}

/**
 * Error thrown during computation operations
 */
export class ComputeError extends CoreError implements IComputeError {
  constructor(message: string, context?: ErrorContext) {
    super(ErrorType.Runtime, message, ErrorSeverity.Medium, context)
    this.name = 'ComputeError'
  }
}

/**
 * Error class for validation results
 */
export class ValidationResultError extends ValidatorError implements IValidationResultError {
  constructor(message: string, public validationErrors: unknown[], context?: ErrorContext) {
    super(message, context)
    this.name = 'ValidationResultError'
  }
}

/**
 * Error for unsupported element types
 */
export class UnsupportedElementTypeError extends CoreError implements IUnsupportedElementTypeError {
  constructor(elementType: string, context?: ErrorContext) {
    super(
      ErrorType.Runtime,
      `Element type '${elementType}' is not supported`,
      ErrorSeverity.High,
      context,
    )
    this.name = 'UnsupportedElementTypeError'
  }
}

/**
 * Error for unsupported operations
 */
export class UnsupportedOperationError extends CoreError implements IUnsupportedOperationError {
  constructor(operation: string, context?: ErrorContext) {
    super(
      ErrorType.Runtime,
      `Operation '${operation}' is not supported`,
      ErrorSeverity.High,
      context,
    )
    this.name = 'UnsupportedOperationError'
  }
}

/**
 * Error for element not found situations
 */
export class ElementNotFoundError extends CoreError implements IElementNotFoundError {
  constructor(elementId: string, context?: ErrorContext) {
    super(
      ErrorType.Runtime,
      `Element with ID '${elementId}' not found`,
      ErrorSeverity.Medium,
      context,
    )
    this.name = 'ElementNotFoundError'
  }
}
