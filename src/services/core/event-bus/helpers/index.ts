/**
 * Event Bus Helpers Module
 *
 * This module exports utility functions that simplify working with the event bus
 * by providing type-safe wrappers around common event publishing operations.
 *
 * These helper functions ensure consistent event payloads and reduce
 * boilerplate code throughout the application.
 *
 * @module event-bus/helpers
 */

// Error helpers have been moved to @/lib/utils/error

export * as ComputeEvents from './publishers/computePublishers'
export * as DataEvents from './publishers/dataPublishers'
export * as GridEvents from './publishers/gridPublishers'
export * as InputEvents from './publishers/inputPublishers'
export * as RenderEvents from './publishers/renderPublishers'
// Export publisher functions organized by domain
// This allows importing domain-specific publishers directly:
// import { ShapeEvents } from '@/services/event-bus/helpers';
export * as ShapeEvents from './publishers/shapePublishers'
export * as UIEvents from './publishers/uiPublishers'

export * as ComputeSubscribers from './subscribers/computeSubscribers'
export * as DataSubscribers from './subscribers/dataSubscribers'
export * as InputSubscribers from './subscribers/inputSubscribers'
export * as RenderSubscribers from './subscribers/renderSubscribers'
// Export subscriber functions organized by domain
export * as ShapeSubscribers from './subscribers/shapeSubscribers'
export * as UISubscribers from './subscribers/uiSubscribers'
