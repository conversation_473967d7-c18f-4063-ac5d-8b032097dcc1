/**
 * Defines the data structure for representing a 2D/3D point in a Cartesian coordinate system.
 *
 * @remarks
 * This module is fundamental for all geometric shapes and calculations within the application,
 * serving as a basic building block for positions, vertices, and other coordinate-based data.
 * It exports the {@link PointData} interface.
 *
 * @module types/core/element/geometry/point
 * @see {@link BoundingBox} - An interface that utilizes points for defining shape boundaries.
 * @see {@link ShapeElement} - The base interface for shape elements, which typically involve points for their definition.
 */

/**
 * Represents the data for a point with x and y coordinates, and an optional z coordinate.
 *
 * @remarks
 * Point data is used to define locations, vertices of shapes, origins for transformations, etc.
 * Coordinates are numeric and typically represent pixel values or abstract units
 * within a 2D or 3D Cartesian plane.
 */
export interface PointData {
  /**
   * The x-coordinate of the point in the Cartesian system.
   * @defaultValue 0
   */
  x: number

  /**
   * The y-coordinate of the point in the Cartesian system.
   * @defaultValue 0
   */
  y: number
  /**
   * Optional: The z-coordinate of the point, for 3D contexts.
   * @defaultValue undefined
   */
  z?: number
}

// Default export is the PointData interface for broader compatibility where a plain object is sufficient.
export default PointData
