/**
 * Canvas Grid Component
 *
 * A dynamic grid overlay component that renders a responsive grid on the canvas.
 * The grid automatically adjusts its density based on zoom level to maintain
 * optimal visibility and performance.
 *
 * Features:
 * - Level-of-detail (LOD) system that adapts grid density to zoom level
 * - Efficient rendering using direct SVG lines instead of patterns
 * - Viewport-based culling to only render visible grid lines
 * - Responsive grid sizing with configurable base size
 * - Smooth zoom transitions with consistent grid appearance
 *
 * @example
 * ```tsx
 * <CanvasGrid
 *   showGrid={true}
 *   gridSize={50}
 *   zoom={1.0}
 *   pan={{ x: 0, y: 0 }}
 *   canvasPhysicalSize={{ width: 800, height: 600 }}
 * />
 * ```
 */

import type Point from '@/types/core/element/geometry/point' // Pan is now used

import React from 'react'

/**
 * Props for the CanvasGrid component
 */
interface CanvasGridProps {
  /** Whether the grid should be visible */
  showGrid: boolean
  /** Base grid size in world units (before LOD scaling) */
  gridSize: number
  /** Current zoom level of the canvas */
  zoom: number
  /** Current pan offset of the canvas */
  pan: Point
  /** Physical size of the canvas in pixels */
  canvasPhysicalSize: { width: number, height: number }
}

/** Target minimum screen size for a grid cell in pixels */
const MIN_EFFECTIVE_PATTERN_SIZE_PX = 30
/** Level-of-detail multipliers for grid density scaling */
const GRID_LOD_MULTIPLIERS = [1, 5, 10, 50, 100, 500]

/**
 * CanvasGrid component that renders an adaptive grid overlay on the canvas.
 *
 * This component implements a sophisticated level-of-detail system that automatically
 * adjusts grid density based on the current zoom level. It ensures optimal performance
 * by only rendering grid lines within the visible viewport and uses efficient SVG
 * line rendering instead of patterns.
 *
 * The grid adapts its spacing using predefined multipliers to maintain readability
 * at different zoom levels, preventing visual clutter when zoomed out and ensuring
 * sufficient detail when zoomed in.
 *
 * @param props - The component props
 * @param props.showGrid - Controls grid visibility
 * @param props.gridSize - Base grid size in world units
 * @param props.zoom - Current canvas zoom level
 * @param props.pan - Current canvas pan offset
 * @param props.canvasPhysicalSize - Canvas dimensions in pixels
 * @returns The rendered grid component or null if grid is hidden
 */
const CanvasGrid: React.FC<CanvasGridProps> = ({ showGrid, gridSize, zoom, pan, canvasPhysicalSize }: CanvasGridProps) => {
  // 确保所有必要的属性都有有效值
  if (!showGrid) {
    return null
  }
  if (typeof gridSize !== 'number' || gridSize <= 0) {
    return null
  }
  if (typeof zoom !== 'number' || zoom <= 0) {
    return null
  }
  if (pan === null || pan === undefined || typeof pan.x !== 'number' || typeof pan.y !== 'number') {
    return null
  }
  if (canvasPhysicalSize === null || canvasPhysicalSize === undefined) {
    return null
  }
  if (typeof canvasPhysicalSize.width !== 'number' || canvasPhysicalSize.width <= 0) {
    return null
  }
  if (typeof canvasPhysicalSize.height !== 'number' || canvasPhysicalSize.height <= 0) {
    return null
  }

  let effectiveGridSize = gridSize
  let foundSuitableGrid = false

  for (const multiplier of GRID_LOD_MULTIPLIERS) {
    const potentialGridSize = gridSize * multiplier
    const cellVisibleSizePx = potentialGridSize * zoom

    // 在极低缩放级别(1%-10%)时，使用更大的最小像素要求
    const minPixelSize = zoom < 0.1 ? 50 : MIN_EFFECTIVE_PATTERN_SIZE_PX

    if (cellVisibleSizePx >= minPixelSize) {
      effectiveGridSize = potentialGridSize
      foundSuitableGrid = true
      break
    }
  }

  // 即使没有找到合适的网格尺寸，也尝试渲染网格
  // 使用最小的网格尺寸，确保网格始终可见
  if (foundSuitableGrid === false) {
    effectiveGridSize = gridSize * GRID_LOD_MULTIPLIERS[0] // 使用最小的乘数
  }

  const patternDimensionInWorldUnits = effectiveGridSize

  // Calculate the viewBox in world coordinates
  const viewBoxX = -pan.x / zoom
  const viewBoxY = -pan.y / zoom
  const viewBoxWidth = canvasPhysicalSize.width / zoom
  const viewBoxHeight = canvasPhysicalSize.height / zoom

  // Make the grid rect slightly larger than the viewBox to avoid edge issues
  const padding = patternDimensionInWorldUnits * 2 // Overdraw by two grid cells

  const rectX = viewBoxX - padding
  const rectY = viewBoxY - padding
  const rectWidth = viewBoxWidth + (padding * 2)
  const rectHeight = viewBoxHeight + (padding * 2)

  // 准备渲染网格

  // 使用简单的线条样式，确保网格能正确渲染
  // 使用直接绘制线条的方式，而不是pattern，避免pattern引用问题

  // 计算网格线的数量
  const horizontalLines = Math.ceil(rectHeight / effectiveGridSize) + 1
  const verticalLines = Math.ceil(rectWidth / effectiveGridSize) + 1

  // 计算起始位置（对齐到网格）
  const startX = Math.floor(rectX / effectiveGridSize) * effectiveGridSize
  const startY = Math.floor(rectY / effectiveGridSize) * effectiveGridSize

  // 创建网格线
  const gridLines = []

  // 水平线
  for (let i = 0; i < horizontalLines; i++) {
    const y = startY + i * effectiveGridSize
    gridLines.push(
      <line
        key={`h-${i}`}
        x1={rectX}
        y1={y}
        x2={rectX + rectWidth}
        y2={y}
        stroke="rgba(128,128,128,0.3)"
        strokeWidth={0.5 / zoom}
      />,
    )
  }

  // 垂直线
  for (let i = 0; i < verticalLines; i++) {
    const x = startX + i * effectiveGridSize
    gridLines.push(
      <line
        key={`v-${i}`}
        x1={x}
        y1={rectY}
        x2={x}
        y2={rectY + rectHeight}
        stroke="rgba(128,128,128,0.3)"
        strokeWidth={0.5 / zoom}
      />,
    )
  }

  return (
    <g className="canvas-grid-lines">
      {gridLines}
    </g>
  )
}

export default CanvasGrid
