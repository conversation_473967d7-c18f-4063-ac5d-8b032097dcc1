import type { PointData } from '@/types/core/element/geometry/point'
import type { ShapeElement } from '@/types/core/elementDefinitions'
import { ElementType } from '@/types/core/elementDefinitions'
// import { CreateShapeParams } from '@/core/factory/ElementFactory'; // Removed problematic import

// More specific type for what can be reasonably defaulted at creation time from sidebar.
// This avoids trying to default complex things like 'points' for a POLYGON directly here,
// as those might need dynamic calculation based on a simpler concept like 'numberOfSides' or default placeholder.
export type InitialElementProperties = Partial<Omit<ShapeElement, 'id' | 'type' | 'position' | 'metadata'> & {
  // Common geometric hints for creators or ShapeCreationService
  width?: number
  height?: number
  radius?: number
  radiusX?: number
  radiusY?: number
  sides?: number
  startAngle?: number
  endAngle?: number
  cornerRadius?: number
  points?: PointData[] // For Polyline, Polygon (though polygon points often derived)
  // Example for styles that are commonly defaulted
  fill?: string
  stroke?: string
  strokeWidth?: number
  opacity?: number // Added opacity
  strokeDasharray?: string // Added strokeDasharray
  // Specific to certain types if needed
  text?: string
  fontSize?: number
  fontFamily?: string
  fontWeight?: 'normal' | 'bold' | 'bolder' | 'lighter' | number
  fontStyle?: 'normal' | 'italic' | 'oblique'
  textAlign?: 'left' | 'right' | 'center' | 'start' | 'end'
  textBaseline?: 'top' | 'hanging' | 'middle' | 'alphabetic' | 'ideographic' | 'bottom'
  src?: string

  alt?: string // Added for Image alt text
  lineHeight?: number // Added for Text line height
  // For Arc
  closed?: boolean
  counterClockwise?: boolean
  // For Polygon
  isRegular?: boolean
  center?: PointData // Added: Could be useful for regular polygons if position means top-left
  // For Line
  arrowStart?: boolean
  arrowEnd?: boolean
  // For Polyline
  curved?: boolean
  tension?: number
  // For Quadratic Bezier
  start?: PointData
  control?: PointData
  end?: PointData
  // For Cubic Bezier
  control1?: PointData
  control2?: PointData
  // Note: For Bezier curves, 'start' and 'end' can reuse the PointData properties above.
  // Ensure ShapeElement base properties like visible, locked, selectable, draggable, showHandles, layer, zIndex are here if they can be defaulted.
  visible?: boolean
  locked?: boolean
  rotation?: number // rotation is a common top-level property
  selectable?: boolean
  draggable?: boolean
  showHandles?: boolean
  layer?: string
  zIndex?: number
  // Cost-related properties
  costUnitPrice?: number
  costMultiplierOrCount?: number
  costBasis?: string
  costTotal?: number
}>

export const defaultElementSettings: Partial<Record<ElementType, InitialElementProperties>> = {
  [ElementType.RECTANGLE]: {
    width: 240,
    height: 160,
    fill: '#F8D7DA', // 淡粉色马卡龙
    stroke: '#E1A3A8',
    strokeWidth: 1,
    cornerRadius: 0,
    // 设置默认的成本相关属性 - 默认不启用成本计算
    costUnitPrice: 1,
    costMultiplierOrCount: 1,
    costBasis: 'unit', // 默认使用单位计算，用户需要手动选择面积或周长
  },
  [ElementType.SQUARE]: { // Often handled as a RECTANGLE with aspect lock or same width/height
    width: 200,
    height: 200,
    fill: '#D1ECF1', // 淡蓝色马卡龙
    stroke: '#A3C4D1',
    strokeWidth: 1,
    cornerRadius: 0,
    // 设置默认的成本相关属性 - 默认不启用成本计算
    costUnitPrice: 1,
    costMultiplierOrCount: 1,
    costBasis: 'unit', // 默认使用单位计算，用户需要手动选择面积或周长
  },
  [ElementType.ELLIPSE]: {
    radiusX: 120,
    radiusY: 80,
    fill: '#D4F4DD', // 淡绿色马卡龙
    stroke: '#A8D4B8',
    strokeWidth: 1,
    // 🔧 修复：设置默认的成本相关属性
    costUnitPrice: 1,
    costMultiplierOrCount: 1, // 默认乘数改为1
    costBasis: 'unit', // 🔧 修复：椭圆默认使用单位计算，而不是面积
  },
  [ElementType.CIRCLE]: {
    radius: 100,
    fill: '#FFF3CD', // 淡黄色马卡龙
    stroke: '#E6D39A',
    strokeWidth: 1,
    // 🔧 修复：设置默认的成本相关属性
    costUnitPrice: 1,
    costMultiplierOrCount: 1, // 默认乘数改为1
    costBasis: 'unit', // 🔧 修复：圆形默认使用单位计算，而不是面积
  },
  [ElementType.TRIANGLE]: {
    sides: 3,
    radius: 100, // Assuming equilateral, so radius to vertices
    isRegular: true,
    fill: '#E2D9F3', // 淡紫色马卡龙
    stroke: '#C5B3E6',
    strokeWidth: 1,
    // 🔧 修复：设置默认的成本相关属性
    costUnitPrice: 1,
    costMultiplierOrCount: 1, // 默认乘数改为1
    costBasis: 'unit', // 🔧 修复：三角形默认使用单位计算，而不是面积
  },

  [ElementType.PENTAGON]: {
    sides: 5,
    radius: 100,
    isRegular: true,
    fill: '#FFE5CC', // 淡橙色马卡龙
    stroke: '#E6C299',
    strokeWidth: 1,
    // 🔧 修复：设置默认的成本相关属性
    costUnitPrice: 1,
    costMultiplierOrCount: 1, // 默认乘数改为1
    costBasis: 'unit', // 🔧 修复：五边形默认使用单位计算，而不是面积
  },
  [ElementType.HEXAGON]: {
    sides: 6,
    radius: 100,
    isRegular: true,
    fill: '#D5F5F0', // 淡薄荷色马卡龙
    stroke: '#A8E6D8',
    strokeWidth: 1,
    // 🔧 修复：设置默认的成本相关属性
    costUnitPrice: 1,
    costMultiplierOrCount: 1, // 默认乘数改为1
    costBasis: 'unit', // 🔧 修复：六边形默认使用单位计算，而不是面积
  },
  [ElementType.LINE]: {
    // Default length for a line could be tricky.
    // ShapeCreationService might create it from drop point with a fixed length/direction.
    // For now, primarily style defaults.
    stroke: '#FF9999', // 淡珊瑚色马卡龙
    strokeWidth: 2,
    opacity: 1, // 🔧 添加默认透明度
    // 🔧 修复：设置默认的成本相关属性
    costUnitPrice: 1,
    costMultiplierOrCount: 1, // 默认乘数改为1
    costBasis: 'length', // 线条使用长度计算
    // Default properties for start/end points (e.g., a fixed length line)
    // These would be relative to the drop position in ShapeCreationService
    // For example, end could be { x: 100, y: 0 } relative to start (drop position)
  },
  [ElementType.POLYLINE]: {
    stroke: '#87CEEB', // 淡天蓝色马卡龙
    strokeWidth: 2,
    opacity: 1, // 🔧 添加默认透明度
    // 🔧 修复：设置默认的成本相关属性
    costUnitPrice: 1,
    costMultiplierOrCount: 1, // 默认乘数改为1
    costBasis: 'length', // 折线使用长度计算
    // Default points might be a small sample polyline, e.g.:
    points: [{ x: 0, y: 0, z: 0 }, { x: 50, y: -20, z: 0 }, { x: 100, y: 0, z: 0 }],
    curved: false,
    tension: 0.5,
    // These would be relative to the drop position.
  },
  [ElementType.ARC]: {
    stroke: '#DDA0DD', // 淡薰衣草色马卡龙
    strokeWidth: 2,
    opacity: 1, // 🔧 添加默认透明度
    // 🔧 修复：设置默认的成本相关属性
    costUnitPrice: 1,
    costMultiplierOrCount: 1, // 默认乘数改为1
    costBasis: 'length', // 弧线使用长度计算
  },
  [ElementType.QUADRATIC]: {
    stroke: '#FFCCCB', // 淡桃色马卡龙
    strokeWidth: 2,
    opacity: 1, // 🔧 添加默认透明度
    // 🔧 修复：设置默认的成本相关属性
    costUnitPrice: 1,
    costMultiplierOrCount: 1, // 默认乘数改为1
    costBasis: 'length', // 二次贝塞尔曲线使用长度计算
    // Default points (start, control, end) relative to drop position
    start: { x: 0, y: 0, z: 0 },
    control: { x: 50, y: -50, z: 0 },
    end: { x: 100, y: 0, z: 0 },
  },
  [ElementType.CUBIC]: {
    stroke: '#FFFACD', // 淡柠檬色马卡龙
    strokeWidth: 2,
    opacity: 1, // 🔧 添加默认透明度
    // 🔧 修复：设置默认的成本相关属性
    costUnitPrice: 1,
    costMultiplierOrCount: 1, // 默认乘数改为1
    costBasis: 'length', // 三次贝塞尔曲线使用长度计算
    // Default points (start, control1, control2, end) relative to drop position
    start: { x: 0, y: 0, z: 0 },
    control1: { x: 30, y: -50, z: 0 },
    control2: { x: 70, y: 50, z: 0 },
    end: { x: 100, y: 0, z: 0 },
  },
  [ElementType.TEXT]: {
    text: 'Text',
    fontSize: 16,
    fontFamily: 'Inter, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    fill: '#2C3E50',
    width: 120, // 恢复默认边界框宽度
    height: 24, // 恢复默认边界框高度
    textAlign: 'left', // 默认左对齐
    // 🔧 修复：设置默认的成本相关属性
    costUnitPrice: 1,
    costMultiplierOrCount: 1, // 默认乘数改为1
    costBasis: 'unit', // 文本使用单位计算
  },
  [ElementType.IMAGE]: {
    width: 200,
    height: 200,
    src: '/public/icon/image.svg',
    opacity: 1,
    // 🔧 修复：设置默认的成本相关属性
    costUnitPrice: 1,
    costMultiplierOrCount: 1, // 默认乘数改为1
    costBasis: 'unit', // 图片使用单位计算
  },

  // Add other element types as needed
}

/**
 * Helper function to get default properties for a given element type.
 * Returns an empty object if no defaults are defined for the type.
 */
export function getDefaultSettingsForElementType(elementType: ElementType): InitialElementProperties {
  return defaultElementSettings[elementType] || {}
}
