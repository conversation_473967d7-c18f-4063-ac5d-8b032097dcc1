/**
 * Utility Functions for Path Elements
 *
 * @remarks
 * This module provides a collection of utility functions specifically for working with
 * path elements such as Lines ({@link ElementType.LINE}), Polylines ({@link ElementType.POLYLINE}),
 * and Arcs ({@link ElementType.ARC}). These utilities assist in calculations related to
 * their geometric properties, like length and bounding boxes.
 *
 * The functions are exposed as static methods of the {@link PathElementUtils} class
 * and are also re-exported directly and via the {@link PathUtils} namespace object
 * for convenient access.
 *
 * It assumes that path-specific geometric data (e.g., start/end points for lines,
 * points array for polylines, radius/angles for arcs) are stored within the
 * `properties` object of the input {@link ShapeModel}.
 *
 * @module lib/utils/element/pathElementUtils
 * @see {@link ShapeModel}
 * @see {@link ElementType}
 * @see {@link Point}
 * @see {@link BoundingBox}
 */

import type { PointData as IPoint } from '../../../types/core/element/geometry/point' // Use PointData and alias
import type { ShapeElement as ShapeModel } from '@/types/core/elementDefinitions' // Use type import
import { ElementType } from '@/types/core/elementDefinitions' // Import ElementType separately
import { BoundingBoxClass as BoundingBox } from '../geometry/BoundingBoxClass' // Import BoundingBoxClass and alias
import { PointClass as Point } from '../geometry/PointClass' // Import PointClass as Point
import { calculateDistance as distanceBetweenPoints } from '../geometry/pointUtils' // Corrected import path

/**
 * Provides static utility methods for calculations related to path elements.
 */
export class PathElementUtils {
  /**
   * Calculates the length of a line element.
   *
   * @remarks
   * Assumes the line's start and end points are stored in `element.properties.start`
   * and `element.properties.end`, relative to `element.position`.
   *
   * @param element - The line element ({@link ShapeModel} with `type` {@link ElementType.LINE}).
   * @returns The calculated length of the line. Returns 0 if the element is invalid or properties are missing.
   */
  static calculateLineLength(element: ShapeModel): number {
    if (!element || element.type !== ElementType.LINE || !element.properties?.start || !element.properties.end) {
      console.warn('[PathElementUtils.calculateLineLength] Invalid line element or missing properties.')
      return 0
    }
    const start = element.properties.start as IPoint
    const end = element.properties.end as IPoint
    // Assuming start/end points in properties are relative to element.position
    const p1 = new Point(element.position.x + start.x, element.position.y + start.y)
    const p2 = new Point(element.position.x + end.x, element.position.y + end.y)
    return distanceBetweenPoints(p1, p2)
  }

  /**
   * Calculates the total length of a polyline element.
   *
   * @remarks
   * Assumes the polyline's vertices are stored in `element.properties.points`,
   * relative to `element.position`.
   *
   * @param element - The polyline element ({@link ShapeModel} with `type` {@link ElementType.POLYLINE}).
   * @returns The total length of all segments in the polyline. Returns 0 if invalid or has fewer than 2 points.
   */
  static calculatePolylineLength(element: ShapeModel): number {
    if (!element || element.type !== ElementType.POLYLINE || !element.properties || !Array.isArray(element.properties.points)) {
      console.warn('[PathElementUtils.calculatePolylineLength] Invalid polyline element or missing points property.')
      return 0
    }
    const polyPoints = element.properties.points as IPoint[]
    if (polyPoints.length < 2) {
      return 0
    }

    let totalLength = 0
    for (let i = 0; i < polyPoints.length - 1; i++) {
      const p1 = new Point(polyPoints[i].x + element.position.x, polyPoints[i].y + element.position.y)
      const p2 = new Point(polyPoints[i + 1].x + element.position.x, polyPoints[i + 1].y + element.position.y)
      totalLength += distanceBetweenPoints(p1, p2)
    }
    return totalLength
  }

  /**
   * Calculates the length of an arc element.
   *
   * @remarks
   * Assumes the arc's radius, start angle (degrees), and end angle (degrees) are stored
   * in `element.properties`.
   *
   * @param element - The arc element ({@link ShapeModel} with `type` {@link ElementType.ARC}).
   * @returns The calculated arc length. Returns 0 if the element is invalid or properties are missing/invalid.
   */
  static calculateArcLength(element: ShapeModel): number {
    if (!element || element.type !== ElementType.ARC || !element.properties) {
      console.warn('[PathElementUtils.calculateArcLength] Invalid arc element or missing properties.')
      return 0
    }
    const props = element.properties as any // Cast for simplicity, assuming Arc specific properties
    if (typeof props.radius !== 'number' || typeof props.startAngle !== 'number' || typeof props.endAngle !== 'number') {
      console.warn('[PathElementUtils.calculateArcLength] Arc element missing radius, startAngle, or endAngle.')
      return 0
    }
    const radius = props.radius
    const startAngle = props.startAngle
    const endAngle = props.endAngle
    const angleSpanDegrees = Math.abs(endAngle - startAngle)
    const angleSpanRadians = angleSpanDegrees * (Math.PI / 180)
    return radius * angleSpanRadians
  }

  /**
   * Calculates the length of a generic path element by dispatching to type-specific length calculators.
   *
   * @param element - The path element ({@link ShapeModel}) whose length is to be calculated.
   *                  Supports LINE, POLYLINE, ARC.
   * @returns The calculated length of the path. Returns 0 for unsupported types or invalid elements.
   */
  static calculatePathLength(element: ShapeModel): number {
    switch (element.type) {
      case ElementType.LINE:
        return PathElementUtils.calculateLineLength(element)
      case ElementType.POLYLINE:
        return PathElementUtils.calculatePolylineLength(element)
      case ElementType.ARC:
        return PathElementUtils.calculateArcLength(element)
      // Add cases for QUADRATIC, CUBIC if needed
      default:
        console.warn(`[PathElementUtils.calculatePathLength] Length calculation not supported for type: ${element.type}`)
        return 0
    }
  }

  /**
   * Calculates the bounding box of a line element.
   *
   * @remarks
   * Assumes the line's start and end points are stored in `element.properties.start`
   * and `element.properties.end`, relative to `element.position`.
   *
   * @param element - The line element ({@link ShapeModel} with `type` {@link ElementType.LINE}).
   * @returns A {@link BoundingBox} instance or `null` if the element is invalid.
   */
  static calculateLineBoundingBox(element: ShapeModel): BoundingBox | null {
    if (!element || element.type !== ElementType.LINE || !element.properties?.start || !element.properties.end) {
      return null
    }
    const startProp = element.properties.start as IPoint
    const endProp = element.properties.end as IPoint
    const absPoints = [
      new Point(startProp.x + element.position.x, startProp.y + element.position.y),
      new Point(endProp.x + element.position.x, endProp.y + element.position.y),
    ]
    return BoundingBox.fromPointsArray(absPoints)
  }

  /**
   * Calculates the bounding box of a polyline element.
   *
   * @remarks
   * Assumes the polyline's vertices are stored in `element.properties.points`,
   * relative to `element.position`.
   *
   * @param element - The polyline element ({@link ShapeModel} with `type` {@link ElementType.POLYLINE}).
   * @returns A {@link BoundingBox} instance or `null` if the element is invalid or has no points.
   */
  static calculatePolylineBoundingBox(element: ShapeModel): BoundingBox | null {
    if (!element || element.type !== ElementType.POLYLINE || !element.properties || !Array.isArray(element.properties.points) || element.properties.points.length === 0) {
      return null
    }
    const polyPoints = element.properties.points as IPoint[]
    const absPoints = polyPoints.map(p => new Point(p.x + element.position.x, p.y + element.position.y))
    return BoundingBox.fromPointsArray(absPoints)
  }

  /**
   * Calculates the bounding box of an arc element.
   *
   * @remarks
   * Assumes the arc's center (`element.position`), radius, start angle (degrees),
   * and end angle (degrees) are available. It considers the arc's start/end points
   * and any critical angle points (0, 90, 180, 270 degrees) that fall within the arc's span.
   *
   * @param element - The arc element ({@link ShapeModel} with `type` {@link ElementType.ARC}).
   * @returns A {@link BoundingBox} instance or `null` if the element is invalid or properties are missing.
   */
  static calculateArcBoundingBox(element: ShapeModel): BoundingBox | null {
    if (!element || element.type !== ElementType.ARC || !element.properties) {
      return null
    }
    const props = element.properties as any // Cast for simplicity
    if (typeof props.radius !== 'number' || typeof props.startAngle !== 'number' || typeof props.endAngle !== 'number' || !element.position) {
      console.warn('[PathElementUtils.calculateArcBoundingBox] Invalid arc element or missing properties for BBox calculation.')
      return null
    }
    const radius = props.radius
    const startAngleDegrees = props.startAngle
    const endAngleDegrees = props.endAngle
    const centerX = element.position.x
    const centerY = element.position.y

    const pointsOnArc: IPoint[] = []
    const startRad = startAngleDegrees * Math.PI / 180
    const endRad = endAngleDegrees * Math.PI / 180

    pointsOnArc.push({ x: centerX + radius * Math.cos(startRad), y: centerY + radius * Math.sin(startRad) })
    pointsOnArc.push({ x: centerX + radius * Math.cos(endRad), y: centerY + radius * Math.sin(endRad) })

    // Check critical angle points (0, 90, 180, 270 degrees) if they fall within the arc span
    const criticalAnglesRad = [0, Math.PI / 2, Math.PI, 3 * Math.PI / 2]
    let sAngle = startRad
    let eAngle = endRad

    // Normalize angles to be 0 <= angle < 2PI
    // And ensure sAngle < eAngle for span calculation, adjusting eAngle if it wraps around
    while (sAngle < 0) sAngle += 2 * Math.PI
    sAngle %= (2 * Math.PI)
    while (eAngle < 0) eAngle += 2 * Math.PI
    eAngle %= (2 * Math.PI)

    if (eAngle < sAngle) { // Arc crosses the 0-radian line
      criticalAnglesRad.forEach((angle) => {
        if (angle >= sAngle || angle <= eAngle) {
          pointsOnArc.push({ x: centerX + radius * Math.cos(angle), y: centerY + radius * Math.sin(angle) })
        }
      })
    }
    else {
      criticalAnglesRad.forEach((angle) => {
        if (angle >= sAngle && angle <= eAngle) {
          pointsOnArc.push({ x: centerX + radius * Math.cos(angle), y: centerY + radius * Math.sin(angle) })
        }
      })
    }
    if (pointsOnArc.length === 0)
      return null // Should not happen if start/end points are pushed
    return BoundingBox.fromPointsArray(pointsOnArc)
  }

  /**
   * Calculates the bounding box of a generic path element by dispatching to type-specific calculators.
   *
   * @param element - The path element ({@link ShapeModel}) whose bounding box is to be calculated.
   *                  Supports LINE, POLYLINE, ARC.
   * @returns A {@link BoundingBox} instance or `null` for unsupported types or invalid elements.
   */
  static calculatePathBoundingBox(element: ShapeModel): BoundingBox | null {
    switch (element.type) {
      case ElementType.LINE:
        return PathElementUtils.calculateLineBoundingBox(element)
      case ElementType.POLYLINE:
        return PathElementUtils.calculatePolylineBoundingBox(element)
      case ElementType.ARC:
        return PathElementUtils.calculateArcBoundingBox(element)
      // Add cases for QUADRATIC, CUBIC if BBox calculations are needed
      default:
        console.warn(`[PathElementUtils.calculatePathBoundingBox] BBox calculation not supported for type: ${element.type}`)
        return null
    }
  }
}

// Export all static methods for direct use
export const {
  calculateLineLength,
  calculatePolylineLength,
  calculateArcLength,
  calculatePathLength,
  calculateLineBoundingBox,
  calculatePolylineBoundingBox,
  calculateArcBoundingBox,
  calculatePathBoundingBox,
} = PathElementUtils

/**
 * Namespace object for convenient access to path element utility functions.
 *
 * @remarks
 * This object groups the static methods from {@link PathElementUtils} under a
 * single export, allowing for usage like `PathUtils.calculateLineLength(...)`.
 */
export const PathUtils = {
  calculateLineLength,
  calculatePolylineLength,
  calculateArcLength,
  calculatePathLength,
  calculateLineBoundingBox,
  calculatePolylineBoundingBox,
  calculateArcBoundingBox,
  calculatePathBoundingBox,
}
