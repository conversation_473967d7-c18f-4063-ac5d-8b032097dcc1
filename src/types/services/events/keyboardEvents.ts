/**
 * Keyboard Events
 *
 * This file defines keyboard-related event types used throughout the application.
 * These events are dispatched through the application's event bus when keyboard
 * actions occur, allowing components to react to keyboard input.
 *
 * @module types/services/events/KeyboardEvents
 */

// Import KeyModifiers from keyboard.ts
import type { KeyModifiers } from '../keyboard'
// Import AppEventType
import type { AppEventType } from './eventTypes'

// Re-export KeyModifiers
export type { KeyModifiers }

/**
 * Key Pressed Event
 *
 * Represents an event fired when a key is pressed down.
 * This event contains information about which key was pressed
 * and the state of modifier keys at the time of the event.
 */
export interface KeyPressedEvent {
  /** The event type identifier */
  type: AppEventType.KeyPressed
  /** Event payload containing key information */
  payload: {
    /** The key value (e.g., 'a', 'Enter', 'Escape') */
    key: string
    /** The physical key code (e.g., 'KeyA', 'Enter', 'Escape') */
    code: string
    /** State of modifier keys when the event occurred */
    modifiers: KeyModifiers
    /** Reference to the original browser keyboard event, if available */
    originalEvent?: KeyboardEvent
  }
}

/**
 * Key Released Event
 *
 * Represents an event fired when a previously pressed key is released.
 * This event contains information about which key was released
 * and the state of modifier keys at the time of the event.
 */
export interface KeyReleasedEvent {
  /** The event type identifier */
  type: AppEventType.KeyReleased
  /** Event payload containing key information */
  payload: {
    /** The key value (e.g., 'a', 'Enter', 'Escape') */
    key: string
    /** The physical key code (e.g., 'KeyA', 'Enter', 'Escape') */
    code: string
    /** State of modifier keys when the event occurred */
    modifiers: KeyModifiers
    /** Reference to the original browser keyboard event, if available */
    originalEvent?: KeyboardEvent
  }
}
