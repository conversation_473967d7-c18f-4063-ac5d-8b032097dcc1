import type { BaseEvent, EventSubscriptionOptions } from '@/types/services/events/eventCore'
import type { AppEventMap } from '@/types/services/events/eventRegistry'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { AppEventBusImpl } from '@/services/core/event-bus/appEventBus'
import { AppEventType } from '@/types/services/events'

// Use a consistent event type for testing
const TEST_EVENT_TYPE = AppEventType.ShapeCreateComplete

// Mock console methods
const mockConsole = {
  warn: vi.spyOn(console, 'warn').mockImplementation(() => {}),
  error: vi.spyOn(console, 'error').mockImplementation(() => {}),
  log: vi.spyOn(console, 'log').mockImplementation(() => {}),
}

describe('appEventBusImpl', () => {
  let eventBus: AppEventBusImpl

  beforeEach(() => {
    // Reset singleton instance before each test
    AppEventBusImpl.resetInstance()
    eventBus = AppEventBusImpl.getInstance()
    vi.clearAllMocks()
  })

  afterEach(() => {
    eventBus.clear()
    vi.restoreAllMocks()
  })

  describe('singleton pattern', () => {
    it('should return the same instance when called multiple times', () => {
      const instance1 = AppEventBusImpl.getInstance()
      const instance2 = AppEventBusImpl.getInstance()

      expect(instance1).toBe(instance2)
      expect(instance1).toBe(eventBus)
    })

    it('should reset instance correctly', () => {
      const instance1 = AppEventBusImpl.getInstance()
      AppEventBusImpl.resetInstance()
      const instance2 = AppEventBusImpl.getInstance()

      expect(instance1).not.toBe(instance2)
    })
  })

  describe('configuration', () => {
    it('should configure event bus with partial config', () => {
      eventBus.configure({ enableLogging: true })

      // Test that configuration was applied
      const handler = vi.fn()
      eventBus.subscribe(TEST_EVENT_TYPE, handler)

      // Configuration should be applied successfully
      expect(true).toBe(true)
    })

    it('should merge configuration with existing config', () => {
      eventBus.configure({ enableLogging: true })
      eventBus.configure({ defaultPriority: 5 })

      // Both settings should be active
      const handler = vi.fn()
      eventBus.subscribe(TEST_EVENT_TYPE, handler)

      // Configuration should be merged successfully
      expect(true).toBe(true)
    })
  })

  describe('subscribe and unsubscribe', () => {
    it('should subscribe to events', () => {
      const handler = vi.fn()
      const unsubscribe = eventBus.subscribe(TEST_EVENT_TYPE, handler)

      expect(typeof unsubscribe).toBe('function')
    })

    it('should subscribe with options', () => {
      const handler = vi.fn()
      const options: EventSubscriptionOptions = {
        once: true,
        async: true,
      }

      eventBus.subscribe(TEST_EVENT_TYPE, handler, options)

      // Should not throw
      expect(true).toBe(true)
    })

    it('should unsubscribe from events', () => {
      const handler = vi.fn()
      eventBus.subscribe(TEST_EVENT_TYPE, handler)

      const result = eventBus.unsubscribe(TEST_EVENT_TYPE, handler)

      expect(result).toBe(true)
    })

    it('should return false when unsubscribing non-existent handler', () => {
      const handler = vi.fn()

      const result = eventBus.unsubscribe(TEST_EVENT_TYPE, handler)

      expect(result).toBe(false)
    })

    it('should use unsubscribe function returned by subscribe', () => {
      const handler = vi.fn()
      const unsubscribe = eventBus.subscribe(TEST_EVENT_TYPE, handler)

      unsubscribe()

      // Handler should be removed
      const event: AppEventMap[typeof TEST_EVENT_TYPE] = {
        type: TEST_EVENT_TYPE,
        timestamp: Date.now(),
        payload: { shapeId: 'test-shape' },
      }

      eventBus.publish(event)
      expect(handler).not.toHaveBeenCalled()
    })

    it('should support "on" alias for subscribe', () => {
      const handler = vi.fn()
      const unsubscribe = eventBus.on(TEST_EVENT_TYPE, handler)

      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('publish and emit', () => {
    it('should publish events to subscribers', () => {
      const handler = vi.fn()
      eventBus.subscribe(TEST_EVENT_TYPE, handler)

      const event: AppEventMap[typeof TEST_EVENT_TYPE] = {
        type: TEST_EVENT_TYPE,
        timestamp: Date.now(),
        payload: { shapeId: 'test-shape' },
      }

      const result = eventBus.publish(event)

      expect(result).toBe(true)
      expect(handler).toHaveBeenCalledWith(event)
    })

    it('should add timestamp if not provided', () => {
      const handler = vi.fn()
      eventBus.subscribe(TEST_EVENT_TYPE, handler)

      const event: AppEventMap[typeof TEST_EVENT_TYPE] = {
        type: TEST_EVENT_TYPE,
        payload: { shapeId: 'test-shape' },
      } as any

      eventBus.publish(event)

      expect(handler).toHaveBeenCalledWith(
        expect.objectContaining({
          timestamp: expect.any(Number),
        }),
      )
    })

    it('should return false for invalid events', () => {
      const invalidEvent = null as any

      const result = eventBus.publish(invalidEvent)

      expect(result).toBe(false)
      // Note: Console error is logged but we don't test the exact message
    })

    it('should return false when no handlers exist', () => {
      const event: AppEventMap[typeof TEST_EVENT_TYPE] = {
        type: TEST_EVENT_TYPE,
        timestamp: Date.now(),
        payload: { shapeId: 'test-shape' },
      }

      const result = eventBus.publish(event)

      expect(result).toBe(false)
    })

    it('should support "emit" alias for publish', () => {
      const handler = vi.fn()
      eventBus.subscribe(TEST_EVENT_TYPE, handler)

      const event: AppEventMap[typeof TEST_EVENT_TYPE] = {
        type: TEST_EVENT_TYPE,
        timestamp: Date.now(),
        payload: { shapeId: 'test-shape' },
      }

      const result = eventBus.emit(event)

      expect(result).toBe(true)
      expect(handler).toHaveBeenCalledWith(event)
    })

    it('should handle multiple subscribers', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      const handler3 = vi.fn()

      eventBus.subscribe(TEST_EVENT_TYPE, handler1)
      eventBus.subscribe(TEST_EVENT_TYPE, handler2)
      eventBus.subscribe(TEST_EVENT_TYPE, handler3)

      const event: AppEventMap[typeof TEST_EVENT_TYPE] = {
        type: TEST_EVENT_TYPE,
        timestamp: Date.now(),
        payload: { shapeId: 'test-shape' },
      }

      eventBus.publish(event)

      expect(handler1).toHaveBeenCalledWith(event)
      expect(handler2).toHaveBeenCalledWith(event)
      expect(handler3).toHaveBeenCalledWith(event)
    })
  })

  describe('subscription options', () => {
    it('should handle "once" option', () => {
      const handler = vi.fn()
      eventBus.subscribe(TEST_EVENT_TYPE, handler, { once: true })

      const event: AppEventMap[typeof TEST_EVENT_TYPE] = {
        type: TEST_EVENT_TYPE,
        timestamp: Date.now(),
        payload: { shapeId: 'test-shape' },
      }

      eventBus.publish(event)
      eventBus.publish(event)

      expect(handler).toHaveBeenCalledTimes(1)
    })

    it('should handle "filter" option', () => {
      const handler = vi.fn()
      const filter = (event: BaseEvent) => event.payload?.shapeId === 'allowed-shape'

      eventBus.subscribe(TEST_EVENT_TYPE, handler, { filter })

      const allowedEvent: AppEventMap[typeof TEST_EVENT_TYPE] = {
        type: TEST_EVENT_TYPE,
        timestamp: Date.now(),
        payload: { shapeId: 'allowed-shape' },
      }

      const blockedEvent: AppEventMap[typeof TEST_EVENT_TYPE] = {
        type: TEST_EVENT_TYPE,
        timestamp: Date.now(),
        payload: { shapeId: 'blocked-shape' },
      }

      eventBus.publish(allowedEvent)
      eventBus.publish(blockedEvent)

      expect(handler).toHaveBeenCalledTimes(1)
      expect(handler).toHaveBeenCalledWith(allowedEvent)
    })

    it('should handle "async" option', () => {
      const handler = vi.fn()
      eventBus.subscribe(TEST_EVENT_TYPE, handler, { async: true })

      const event: AppEventMap[typeof TEST_EVENT_TYPE] = {
        type: TEST_EVENT_TYPE,
        timestamp: Date.now(),
        payload: { shapeId: 'test-shape' },
      }

      eventBus.publish(event)

      // Async handlers are called via Promise.resolve(), so they won't be called immediately
      expect(handler).not.toHaveBeenCalled()
    })
  })

  describe('error handling', () => {
    it('should handle errors in event handlers', () => {
      const handler = vi.fn().mockImplementation(() => {
        throw new Error('Handler error')
      })

      eventBus.subscribe(TEST_EVENT_TYPE, handler)

      const event: AppEventMap[typeof TEST_EVENT_TYPE] = {
        type: TEST_EVENT_TYPE,
        timestamp: Date.now(),
        payload: { shapeId: 'test-shape' },
      }

      const result = eventBus.publish(event)

      expect(result).toBe(true)
      // Note: Error is logged to console but we don't test the exact message
    })

    it('should continue processing other handlers after error', () => {
      const errorHandler = vi.fn().mockImplementation(() => {
        throw new Error('Handler error')
      })
      const successHandler = vi.fn()

      eventBus.subscribe(TEST_EVENT_TYPE, errorHandler)
      eventBus.subscribe(TEST_EVENT_TYPE, successHandler)

      const event: AppEventMap[typeof TEST_EVENT_TYPE] = {
        type: TEST_EVENT_TYPE,
        timestamp: Date.now(),
        payload: { shapeId: 'test-shape' },
      }

      eventBus.publish(event)

      expect(errorHandler).toHaveBeenCalled()
      expect(successHandler).toHaveBeenCalled()
    })
  })

  describe('clear functionality', () => {
    it('should clear all handlers', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()

      eventBus.subscribe(TEST_EVENT_TYPE, handler1)
      eventBus.subscribe(AppEventType.ShapeDeleteComplete, handler2)

      eventBus.clear()

      const event1: AppEventMap[typeof TEST_EVENT_TYPE] = {
        type: TEST_EVENT_TYPE,
        timestamp: Date.now(),
        payload: { shapeId: 'test-shape' },
      }

      const event2: AppEventMap[typeof AppEventType.ShapeDeleteComplete] = {
        type: AppEventType.ShapeDeleteComplete,
        timestamp: Date.now(),
        payload: { shapeId: 'test-shape' },
      }

      eventBus.publish(event1)
      eventBus.publish(event2)

      expect(handler1).not.toHaveBeenCalled()
      expect(handler2).not.toHaveBeenCalled()
    })
  })
})
