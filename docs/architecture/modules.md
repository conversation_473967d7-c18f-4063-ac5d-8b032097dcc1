# Module Design Document

This document provides a detailed breakdown of the key modules within the RenoPilot.JS.Shapes2 application, their responsibilities, and their primary interactions. For a higher-level overview, please refer to the [System Architecture Overview](./overview.md) and the [Data Flow Document](./data-flow.md).

## Core Module Categories

The application is structured into several core module categories, each with a distinct set of responsibilities:

### 1. `src/components/` - UI Components

-   **Responsibility**: Contains all React components responsible for rendering the user interface and handling user interactions.
-   **Key Sub-modules/Examples**:
    -   `canvas/`: Components related to the main drawing canvas.
    -   `dialogs/`: Reusable dialog components for various user interactions.
    -   `drawer/`: Components for the asset drawer/panel.
    -   `layout/`: Main application layout structures, including `EditorLayout.tsx`.
    -   `property/`: Components for the property inspector panels (e.g., `RectangleGeometry.tsx`, `GeometrySelector.tsx`).
    -   `toolbar/`: Components for the main application toolbar.
    -   `ui/`: Generic, reusable UI elements (buttons, inputs, etc.).

### 2. `src/core/` - Core Application Logic

-   **Responsibility**: Encapsulates the fundamental business logic of the application, independent of the UI framework. This includes element creation, manipulation, computation, and validation.
-   **Key Sub-modules/Examples**:
    -   `CoreCoordinator.ts`: A central orchestrator for core logic operations.
    -   `compute/`: Logic for calculations related to shapes (e.g., area, perimeter). Includes strategy patterns for different calculations (e.g., `strategies/area/`, `strategies/perimeter/`).
    -   `factory/`: Factories for creating shape elements and other core objects.
    -   `state/`: Core state management utilities or initial state definitions, if not solely handled by Zustand stores.
    -   `validator/`: Logic for validating shape properties and configurations.

### 3. `src/services/` - Service Layer

-   **Responsibility**: Provides services that orchestrate operations, manage side effects, and interact with other parts of the system (like storage or core logic).
-   **Key Sub-modules/Examples**:
    -   `core/`: Services that interact directly with the `src/core` modules.
    -   `elements/`: Services for managing and manipulating elements (e.g., creation, deletion, modification).
    -   `storage/`: Services for handling local storage or other data persistence mechanisms.
    -   `template/`: Services for managing templates, such as `templateIntegrationService.ts`.
    -   `tutorial/`: Services related to the application's tutorial system.

### 4. `src/store/` - State Management

-   **Responsibility**: Contains all Zustand stores for managing the application's global state.
-   **Key Sub-modules/Examples**:
    -   `shapesStore.ts`: Manages the state of all shapes on the canvas, including undo/redo functionality.
    -   `canvasStore.ts`: Manages canvas-specific state (zoom, pan, grid, etc.).
    -   `uiStore.ts`: Manages the state of UI elements (e.g., open dialogs, active tools).
    -   `layerStore.ts`: Manages layer-related state.

### 5. `src/types/` - TypeScript Definitions

-   **Responsibility**: Defines all custom TypeScript types, interfaces, and enums used throughout the application, ensuring type safety and clear data contracts.
-   **Key Sub-modules/Examples**:
    -   `core/`: Types related to core elements, properties, and computations (e.g., `element.types.ts`, `computeStatus.ts`, `design/opening.ts`).
    -   `canvas.types.ts`: Types specific to canvas operations.
    -   `template.ts`: Types for template data structures.
    -   `tutorial.ts`: Types for the tutorial system, like `TutorialModule` and `TutorialStep`.

### 6. `src/hooks/` - Custom React Hooks

-   **Responsibility**: Contains custom React Hooks to encapsulate and reuse stateful logic and side effects across components.
-   **Key Sub-modules/Examples**:
    -   `useElementActions.ts`: Hook for managing element-related actions.
    -   `useUndoRedo.ts`: Hook for interacting with the undo/redo functionality.
    -   `useTemplate.ts`: Hook for template management logic.
    -   `useTutorial.ts`: Hook for managing tutorial state and progression.

### 7. `src/config/` - Application Configuration

-   **Responsibility**: Stores application-wide configurations, default settings, and environment variables.
-   **Key Sub-modules/Examples**:
    -   `defaultElementSettings.ts`: Default properties for new elements.
    -   `environment.ts`: Environment-specific settings.
    -   `tutorialConfig.ts`: Configuration for tutorial modules and steps.

### 8. `src/lib/` - Utility Libraries

-   **Responsibility**: Contains general-purpose utility functions and helper modules that can be used across the application.
-   **Key Sub-modules/Examples**:
    -   `utils/`: A common place for various helper functions.

### 9. `src/data/` - Data Handling

-   **Responsibility**: Modules related to data import, export, storage interaction, and versioning.
-   **Key Sub-modules/Examples**:
    -   `export/`: Logic for exporting canvas content or data.
    -   `storage/`: Low-level storage interaction utilities.
    -   `version/`: Data versioning or migration logic.

## Interactions and Dependencies

-   **UI Components (`src/components`)** primarily interact with **State Management (`src/store`)** to read data and dispatch actions. They also utilize **Custom Hooks (`src/hooks`)** for complex stateful logic and **Services (`src/services`)** for orchestrating operations.
-   **Services (`src/services`)** often act as intermediaries, calling methods from **Core Application Logic (`src/core`)** and updating **State Management (`src/store`)**.
-   **Core Application Logic (`src/core`)** is designed to be self-contained but provides the foundational logic used by services and, indirectly, by UI components.
-   **TypeScript Definitions (`src/types`)** are used globally to ensure data consistency and type safety across all modules.
-   **Configuration (`src/config`)** provides settings and defaults that can be accessed by various modules.

This modular structure promotes separation of concerns, making the codebase easier to understand, maintain, and scale. Each module has a well-defined responsibility, reducing coupling and improving testability.