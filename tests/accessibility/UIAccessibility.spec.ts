import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

/**
 * UI可访问性测试
 * 测试应用程序的可访问性合规性
 */
test.describe('UI可访问性测试', () => {
  /**
   * 每个测试前的设置
   * 导航到应用程序并等待加载
   */
  test.beforeEach(async ({ page }) => {
    // 导航到应用程序
    await page.goto('/');
    
    // 等待应用程序加载
    await page.waitForSelector('.canvas-container');
  });

  /**
   * 测试主页面的可访问性
   * 验证主页面是否符合WCAG 2.1 AA标准
   */
  test('主页面应符合WCAG 2.1 AA可访问性标准', async ({ page }) => {
    // 执行可访问性测试
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa'])
      .analyze();
    
    // 断言 - 检查是否有可访问性违规
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  /**
   * 测试工具栏的可访问性
   * 验证工具栏是否可以通过键盘访问
   */
  test('工具栏应可以通过键盘访问', async ({ page }) => {
    // 聚焦到第一个工具按钮
    await page.focus('[data-tool]:first-child');
    
    // 使用Tab键导航工具栏
    const toolCount = await page.locator('[data-tool]').count();
    
    for (let i = 0; i < toolCount; i++) {
      // 验证当前聚焦的元素
      const focusedElement = await page.evaluate(() => {
        return document.activeElement?.getAttribute('data-tool');
      });
      
      // 验证元素是否可聚焦
      expect(focusedElement).not.toBeNull();
      
      // 按Tab键移动到下一个元素
      await page.keyboard.press('Tab');
    }
    
    // 测试工具选择
    await page.focus('[data-tool="rectangle"]');
    await page.keyboard.press('Enter');
    
    // 验证矩形工具是否被选中
    const isRectangleSelected = await page.evaluate(() => {
      return document.querySelector('[data-tool="rectangle"]')?.classList.contains('active');
    });
    
    expect(isRectangleSelected).toBeTruthy();
  });

  /**
   * 测试颜色选择器的可访问性
   * 验证颜色选择器是否提供足够的颜色对比度
   */
  test('颜色选择器应提供足够的颜色对比度', async ({ page }) => {
    // 创建一个形状
    await page.click('[data-tool="rectangle"]');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    if (!boundingBox) {
      throw new Error('Canvas container not found');
    }
    
    await page.mouse.move(boundingBox.x + 100, boundingBox.y + 100);
    await page.mouse.down();
    await page.mouse.move(boundingBox.x + 200, boundingBox.y + 200);
    await page.mouse.up();
    
    // 选择形状
    await page.click('[data-tool="select"]');
    await page.click('svg rect');
    
    // 打开颜色选择器
    await page.click('.fill-color-picker');
    
    // 等待颜色选择器打开
    await page.waitForSelector('.color-picker-panel');
    
    // 执行可访问性测试
    const accessibilityScanResults = await new AxeBuilder({ page })
      .include('.color-picker-panel')
      .withTags(['wcag2a', 'wcag2aa'])
      .analyze();
    
    // 断言 - 检查是否有可访问性违规
    expect(accessibilityScanResults.violations).toEqual([]);
    
    // 检查颜色选项是否有文本替代
    const colorOptions = await page.locator('.color-option').all();
    
    for (const option of colorOptions) {
      const ariaLabel = await option.getAttribute('aria-label');
      expect(ariaLabel).not.toBeNull();
      expect(ariaLabel?.length).toBeGreaterThan(0);
    }
  });

  /**
   * 测试模态窗口的可访问性
   * 验证模态窗口是否可以通过键盘访问和关闭
   */
  test('模态窗口应可以通过键盘访问和关闭', async ({ page }) => {
    // 打开导出模态窗口
    await page.click('.export-button');
    
    // 等待模态窗口打开
    await page.waitForSelector('.export-modal');
    
    // 验证焦点是否在模态窗口内
    const isFocusInModal = await page.evaluate(() => {
      return document.activeElement?.closest('.export-modal') !== null;
    });
    
    expect(isFocusInModal).toBeTruthy();
    
    // 测试模态窗口的键盘导航
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // 验证焦点是否仍在模态窗口内
    const isFocusStillInModal = await page.evaluate(() => {
      return document.activeElement?.closest('.export-modal') !== null;
    });
    
    expect(isFocusStillInModal).toBeTruthy();
    
    // 测试关闭模态窗口
    await page.keyboard.press('Escape');
    
    // 验证模态窗口是否已关闭
    const isModalClosed = await page.locator('.export-modal').count() === 0;
    expect(isModalClosed).toBeTruthy();
  });

  /**
   * 测试画布操作的可访问性
   * 验证画布操作是否有键盘替代方式
   */
  test('画布操作应有键盘替代方式', async ({ page }) => {
    // 创建一个形状
    await page.click('[data-tool="rectangle"]');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    if (!boundingBox) {
      throw new Error('Canvas container not found');
    }
    
    await page.mouse.move(boundingBox.x + 100, boundingBox.y + 100);
    await page.mouse.down();
    await page.mouse.move(boundingBox.x + 200, boundingBox.y + 200);
    await page.mouse.up();
    
    // 选择形状
    await page.click('[data-tool="select"]');
    await page.click('svg rect');
    
    // 测试键盘移动
    await page.keyboard.press('ArrowRight');
    await page.keyboard.press('ArrowRight');
    await page.keyboard.press('ArrowDown');
    await page.keyboard.press('ArrowDown');
    
    // 获取移动后的位置
    const position = await page.evaluate(() => {
      const rect = document.querySelector('svg rect');
      if (!rect) return null;
      
      return {
        x: parseFloat(rect.getAttribute('x') || '0'),
        y: parseFloat(rect.getAttribute('y') || '0')
      };
    });
    
    // 验证位置已更改
    expect(position).not.toBeNull();
    
    // 测试键盘删除
    await page.keyboard.press('Delete');
    
    // 验证形状已删除
    const isShapeDeleted = await page.locator('svg rect').count() === 0;
    expect(isShapeDeleted).toBeTruthy();
  });

  /**
   * 测试高对比度模式
   * 验证应用程序在高对比度模式下是否可用
   */
  test('应用程序在高对比度模式下应可用', async ({ page }) => {
    // 启用高对比度模式
    await page.evaluate(() => {
      // 模拟高对比度模式
      document.documentElement.classList.add('high-contrast');
      
      // 应用高对比度样式
      const style = document.createElement('style');
      style.textContent = `
        .high-contrast * {
          background-color: black !important;
          color: white !important;
          border-color: white !important;
        }
        .high-contrast svg * {
          stroke: white !important;
          fill: black !important;
        }
      `;
      document.head.appendChild(style);
    });
    
    // 创建一个形状
    await page.click('[data-tool="rectangle"]');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    if (!boundingBox) {
      throw new Error('Canvas container not found');
    }
    
    await page.mouse.move(boundingBox.x + 100, boundingBox.y + 100);
    await page.mouse.down();
    await page.mouse.move(boundingBox.x + 200, boundingBox.y + 200);
    await page.mouse.up();
    
    // 验证形状是否可见
    const isShapeVisible = await page.locator('svg rect').isVisible();
    expect(isShapeVisible).toBeTruthy();
    
    // 执行可访问性测试
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa'])
      .analyze();
    
    // 断言 - 检查是否有可访问性违规
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  /**
   * 测试屏幕阅读器支持
   * 验证应用程序是否提供适当的ARIA属性
   */
  test('应用程序应提供适当的ARIA属性', async ({ page }) => {
    // 检查主要区域是否有适当的角色
    const hasMainRole = await page.locator('main, [role="main"]').count() > 0;
    expect(hasMainRole).toBeTruthy();
    
    // 检查导航区域是否有适当的角色
    const hasNavRole = await page.locator('nav, [role="navigation"]').count() > 0;
    expect(hasNavRole).toBeTruthy();
    
    // 检查工具栏是否有适当的角色
    const hasToolbarRole = await page.locator('[role="toolbar"]').count() > 0;
    expect(hasToolbarRole).toBeTruthy();
    
    // 检查按钮是否有适当的标签
    const buttons = await page.locator('button, [role="button"]').all();
    
    for (const button of buttons) {
      const ariaLabel = await button.getAttribute('aria-label');
      const innerText = await button.innerText();
      
      // 按钮应该有aria-label或内部文本
      expect(ariaLabel || innerText).not.toBeNull();
      expect((ariaLabel || innerText)?.length).toBeGreaterThan(0);
    }
    
    // 检查画布是否有适当的角色和标签
    const canvas = page.locator('.canvas-container');
    const canvasRole = await canvas.getAttribute('role');
    const canvasLabel = await canvas.getAttribute('aria-label');
    
    expect(canvasRole).not.toBeNull();
    expect(canvasLabel).not.toBeNull();
  });

  /**
   * 测试表单控件的可访问性
   * 验证表单控件是否有适当的标签
   */
  test('表单控件应有适当的标签', async ({ page }) => {
    // 创建一个形状
    await page.click('[data-tool="rectangle"]');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    if (!boundingBox) {
      throw new Error('Canvas container not found');
    }
    
    await page.mouse.move(boundingBox.x + 100, boundingBox.y + 100);
    await page.mouse.down();
    await page.mouse.move(boundingBox.x + 200, boundingBox.y + 200);
    await page.mouse.up();
    
    // 选择形状
    await page.click('[data-tool="select"]');
    await page.click('svg rect');
    
    // 等待属性面板更新
    await page.waitForSelector('.properties-panel');
    
    // 检查输入控件是否有标签
    const inputs = await page.locator('.properties-panel input, .properties-panel select').all();
    
    for (const input of inputs) {
      // 获取输入控件的ID
      const id = await input.getAttribute('id');
      
      if (id) {
        // 检查是否有关联的标签
        const hasLabel = await page.locator(`label[for="${id}"]`).count() > 0;
        
        // 如果没有显式标签，检查是否有aria-label或aria-labelledby
        if (!hasLabel) {
          const ariaLabel = await input.getAttribute('aria-label');
          const ariaLabelledBy = await input.getAttribute('aria-labelledby');
          
          expect(ariaLabel || ariaLabelledBy).not.toBeNull();
        }
      } else {
        // 如果没有ID，必须有aria-label
        const ariaLabel = await input.getAttribute('aria-label');
        expect(ariaLabel).not.toBeNull();
      }
    }
  });
});
