/**
 * UI Events
 *
 * This file defines UI-related event types used throughout the application.
 * These events handle user interface interactions such as tool selection,
 * view manipulation, mouse movements, notifications, and modal dialogs.
 *
 * @module types/services/events/UIEvents
 */

// Import AppEventType
import type { AppEventType } from './eventTypes'

/**
 * Tool Change Event
 *
 * Fired when the active tool is changed in the application.
 * Tools represent different modes of interaction with the canvas
 * such as selection, drawing, or editing tools.
 */
export interface ToolChangeEvent {
  // Use the correct enum member
  type: AppEventType.ToolChanged
  payload: {
    /** The identifier of the new active tool */
    tool: string
  }
}

/**
 * View Pan Toggle Event
 *
 * Fired when panning mode is enabled or disabled.
 * Panning allows users to navigate across the canvas by dragging.
 */
export interface ViewPanToggleEvent {
  // Use AppEventType enum members
  type: AppEventType.ViewPanToggle
  payload: {
    /** Whether panning is enabled (true) or disabled (false) */
    enabled: boolean
  }
}

/**
 * View Zoom Event
 *
 * Fired when zoom level changes through zoom in, zoom out,
 * or zoom reset actions. Controls the scale at which content
 * is displayed on the canvas.
 */
export interface ViewZoomEvent {
  // Use AppEventType enum members
  type: AppEventType.ViewZoomIn | AppEventType.ViewZoomOut | AppEventType.ViewZoomReset
  payload: {
    /**
     * Scale factor for zoom in/out operations.
     * Values > 1 zoom in, values < 1 zoom out.
     * Undefined for zoom reset.
     */
    scale?: number
  }
}

/**
 * Selection Mode Change Event
 *
 * Fired when the user changes the selection mode between
 * rectangular selection and lasso (free-form) selection.
 * Affects how users select multiple items on the canvas.
 */
export interface SelectionModeChangeEvent {
  // Use AppEventType enum members
  type: AppEventType.SelectionModeChange
  payload: {
    /**
     * The selection mode to use.
     * - "rect": Rectangular selection area
     * - "lasso": Free-form selection area
     */
    mode: 'rect' | 'lasso'
  }
}

/**
 * Canvas Mouse Move Event
 *
 * Fired continuously as the mouse cursor moves over the canvas.
 * Provides updated cursor coordinates and information about any
 * elements the cursor is currently hovering over.
 */
export interface CanvasMouseMoveEvent {
  // Use AppEventType enum members
  type: AppEventType.CanvasMouseMove
  payload: {
    /** X-coordinate of the mouse cursor relative to the canvas */
    x: number
    /** Y-coordinate of the mouse cursor relative to the canvas */
    y: number
    /**
     * ID of the element currently being hovered over, if any.
     * Undefined if no element is under the cursor.
     */
    hoveredElementId?: string
  }
}

/**
 * Toast Show Event
 *
 * Fired to display a temporary notification message (toast).
 * Toasts are non-intrusive notifications that appear briefly
 * and automatically dismiss after a set duration.
 */
export interface ToastShowEvent {
  // Use AppEventType enum members
  type: AppEventType.ToastShow
  payload: {
    /** Text message to display in the toast */
    message: string
    /**
     * Type of toast that affects its styling.
     * - "info": Informational message (default if not specified)
     * - "success": Success message
     * - "warning": Warning message
     * - "error": Error message
     */
    type?: 'info' | 'success' | 'warning' | 'error'
    /**
     * Duration in milliseconds the toast should remain visible.
     * If not specified, a default duration will be used.
     */
    duration?: number
  }
}

/**
 * Notification Add Event
 *
 * Fired to add a notification to the notification center.
 * Unlike toasts, these notifications remain in the notification
 * center until dismissed by the user.
 */
export interface NotificationAddEvent {
  // Use AppEventType enum members
  type: AppEventType.NotificationAdd
  payload: {
    /** Text message for the notification */
    message: string
    /**
     * Type of notification that affects its styling.
     * - "info": Informational notification (default if not specified)
     * - "success": Success notification
     * - "warning": Warning notification
     * - "error": Error notification
     */
    type?: 'info' | 'success' | 'warning' | 'error'
  }
}

/**
 * Modal Export Open Event
 *
 * Fired to open the export modal dialog.
 * The export modal allows users to configure and initiate
 * the export of canvas content to various file formats.
 */
export interface ModalExportOpenEvent {
  // Use AppEventType enum members
  type: AppEventType.ModalExportOpen
  payload: Record<string, never> // Empty payload
}

/**
 * Modal Template Open Event
 *
 * Fired to open the template selection modal dialog.
 * The template modal allows users to browse and select
 * from available templates to apply to the canvas.
 */
export interface ModalTemplateOpenEvent {
  // Use AppEventType enum members
  type: AppEventType.ModalTemplateOpen
  payload: Record<string, never> // Empty payload
}
