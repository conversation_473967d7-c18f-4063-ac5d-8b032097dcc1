import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ErrorType } from '@/core/errors/CoreError'

import * as geometryUtils from '@/core/utils/geometryUtils'
import * as validationUtils from '@/core/utils/validationUtils'
import { ElementValidator } from '@/core/validator'
// Import after mocking
import { ShapeCreationService } from '@/services/shape-actions/ShapeCreationService'
import { ElementType } from '@/types/core/shape-type'
import { AppEventType } from '@/types/services/events'

// Mock dependencies before importing the service
vi.mock('@/core/factory', () => ({
  ElementFactory: vi.fn().mockImplementation(() => ({
    createShape: vi.fn(),
  })),
}))

vi.mock('@/core/state/ShapeRepository', () => ({
  ShapeRepository: vi.fn().mockImplementation(() => ({
    add: vi.fn(),
  })),
}))

vi.mock('@/core/validator', () => ({
  ElementValidator: {
    validateElement: vi.fn(),
  },
}))

vi.mock('@/core/utils/geometryUtils', () => ({
  ensurePointInstance: vi.fn(),
}))

vi.mock('@/core/utils/validationUtils', () => ({
  handleValidationResultAndPublishError: vi.fn(),
}))

describe('shapeCreationService', () => {
  let service: ShapeCreationService
  let mockFactory: any
  let mockRepository: any
  let mockEventBus: any
  let mockLogger: any

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()

    // Create mock implementations
    mockFactory = {
      createShape: vi.fn(),
    }

    mockRepository = {
      add: vi.fn(),
    }

    mockEventBus = {
      publish: vi.fn(),
    }

    mockLogger = {
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    }

    // Mock utility functions
    vi.mocked(geometryUtils.ensurePointInstance).mockImplementation(point => ({
      x: point.x,
      y: point.y,
      toJson: () => ({ x: point.x, y: point.y }),
    }))

    vi.mocked(validationUtils.handleValidationResultAndPublishError).mockReturnValue(true)

    vi.mocked(ElementValidator.validateElement).mockResolvedValue({
      isValid: true,
      errors: [],
    })

    // Create service instance with mocks
    service = new ShapeCreationService(
      mockFactory,
      mockRepository,
      mockEventBus,
      mockLogger,
    )
  })

  describe('handleRequest', () => {
    it('should handle invalid payload', async () => {
      // Call the method with invalid payload
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: null as any,
          position: { x: 100, y: 100 },
        },
      })

      // Verify error event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          code: ErrorType.INVALID_PAYLOAD,
          message: expect.stringContaining('Invalid payload for shape creation'),
        }),
      })

      // Verify factory and repository methods were not called
      expect(mockFactory.createShape).not.toHaveBeenCalled()
      expect(mockRepository.add).not.toHaveBeenCalled()
    })

    it('should handle invalid position', async () => {
      // Mock ensurePointInstance to throw an error
      vi.mocked(geometryUtils.ensurePointInstance).mockImplementationOnce(() => {
        throw new Error('Invalid position')
      })

      // Call the method with invalid position
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: null as any,
        },
      })

      // Verify error event was published with any error payload
      expect(mockEventBus.publish).toHaveBeenCalledWith(
        expect.objectContaining({
          type: AppEventType.ERROR_OCCURRED,
          payload: expect.any(Object),
        }),
      )

      // Verify factory and repository methods were not called
      expect(mockFactory.createShape).not.toHaveBeenCalled()
      expect(mockRepository.add).not.toHaveBeenCalled()
    })

    it('should handle validation failure', async () => {
      // Mock validation failure
      vi.mocked(validationUtils.handleValidationResultAndPublishError).mockReturnValueOnce(false)

      // Call the method
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: -10, // Invalid width
            height: 50,
          },
        },
      })

      // Verify validation was called
      expect(ElementValidator.validateElement).toHaveBeenCalled()

      // Verify factory and repository methods were not called
      expect(mockFactory.createShape).not.toHaveBeenCalled()
      expect(mockRepository.add).not.toHaveBeenCalled()
    })

    it('should successfully create a rectangle shape', async () => {
      // Mock factory to return a shape
      const mockShape = {
        id: 'rectangle-1',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
        properties: {
          width: 100,
          height: 50,
        },
      }
      mockFactory.createShape.mockResolvedValueOnce(mockShape)

      // Call the method with valid data
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 100,
            height: 50,
          },
        },
      })

      // Verify factory was called with correct parameters
      expect(mockFactory.createShape).toHaveBeenCalledWith(
        ElementType.RECTANGLE,
        expect.any(Object),
      )

      // Verify repository was called with the created shape
      expect(mockRepository.add).toHaveBeenCalledWith(mockShape)

      // Verify success event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_CREATE_COMPLETE,
        payload: {
          shapeId: mockShape.id,
          ElementType: mockShape.type,
          position: mockShape.position,
          properties: mockShape.properties,
        },
      })
    })

    it('should handle factory creation failure', async () => {
      // Mock factory to return null
      mockFactory.createShape.mockResolvedValueOnce(null)

      // Call the method
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 100,
            height: 50,
          },
        },
      })

      // Verify error event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          code: ErrorType.FACTORY_CREATION_FAILED,
        }),
      })

      // Verify repository method was not called
      expect(mockRepository.add).not.toHaveBeenCalled()
    })

    it('should handle repository addition failure', async () => {
      // Mock factory to return a shape
      const mockShape = {
        id: 'rectangle-1',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
        properties: {
          width: 100,
          height: 50,
        },
      }
      mockFactory.createShape.mockResolvedValueOnce(mockShape)

      // Mock repository to throw an error
      mockRepository.add.mockRejectedValueOnce(new Error('Repository error'))

      // Call the method
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 100,
            height: 50,
          },
        },
      })

      // Verify error event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          code: ErrorType.COORDINATOR_OPERATION_FAILED,
        }),
      })
    })

    it('should handle validation failure with specific error messages', async () => {
      // Mock validation failure with specific errors
      vi.mocked(ElementValidator.validateElement).mockResolvedValueOnce({
        isValid: false,
        errors: ['Width must be positive', 'Height must be positive'],
      })

      // Mock validation handler to return false (validation failed)
      vi.mocked(validationUtils.handleValidationResultAndPublishError).mockReturnValueOnce(false)

      // Call the method
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: -10, // Invalid width
            height: -5, // Invalid height
          },
        },
      })

      // Verify validation was called
      expect(ElementValidator.validateElement).toHaveBeenCalled()

      // Verify validation handler was called with the errors
      expect(validationUtils.handleValidationResultAndPublishError).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          isValid: false,
          errors: expect.arrayContaining(['Width must be positive', 'Height must be positive']),
        }),
        expect.anything(),
        expect.anything(),
      )

      // Verify factory and repository methods were not called
      expect(mockFactory.createShape).not.toHaveBeenCalled()
      expect(mockRepository.add).not.toHaveBeenCalled()
    })

    it('should handle position conversion error', async () => {
      // Mock ensurePointInstance to throw an error
      vi.mocked(geometryUtils.ensurePointInstance).mockImplementationOnce(() => {
        throw new Error('Invalid position format')
      })

      // Call the method
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { invalid: 'position' } as any,
          properties: {
            width: 100,
            height: 50,
          },
        },
      })

      // Verify error event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          code: ErrorType.VALIDATION_FAILED,
          message: expect.stringContaining('Invalid position data'),
        }),
      })

      // Verify factory and repository methods were not called
      expect(mockFactory.createShape).not.toHaveBeenCalled()
      expect(mockRepository.add).not.toHaveBeenCalled()
    })

    it('should handle unexpected errors during processing', async () => {
      // Mock factory to throw an unexpected error
      mockFactory.createShape.mockImplementationOnce(() => {
        throw new Error('Unexpected factory error')
      })

      // Call the method
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 100,
            height: 50,
          },
        },
      })

      // Verify error event was published with any error payload
      expect(mockEventBus.publish).toHaveBeenCalledWith(
        expect.objectContaining({
          type: AppEventType.ERROR_OCCURRED,
          payload: expect.any(Object),
        }),
      )

      // Verify repository method was not called
      expect(mockRepository.add).not.toHaveBeenCalled()
    })

    it('should create a circle shape with correct parameters', async () => {
      // Mock shape to be created
      const mockCircleShape = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: { x: 100, y: 100 },
        properties: {
          radius: 50,
        },
      }

      // Setup factory to return mock shape
      mockFactory.createShape.mockResolvedValueOnce(mockCircleShape)

      // Call the method with valid data
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.CIRCLE,
          position: { x: 100, y: 100 },
          properties: {
            radius: 50,
          },
        },
      })

      // Verify factory was called with correct parameters
      expect(mockFactory.createShape).toHaveBeenCalledWith(
        ElementType.CIRCLE,
        expect.any(Object),
      )

      // Verify repository was called with the created shape
      expect(mockRepository.add).toHaveBeenCalledWith(mockCircleShape)

      // Verify success event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_CREATE_COMPLETE,
        payload: {
          shapeId: mockCircleShape.id,
          ElementType: mockCircleShape.type,
          position: mockCircleShape.position,
          properties: mockCircleShape.properties,
        },
      })
    })

    it('should create a polygon shape with correct parameters', async () => {
      // Mock shape to be created
      const mockPolygonShape = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 100, y: 100 },
        properties: {
          points: [
            { x: 100, y: 50 },
            { x: 150, y: 100 },
            { x: 100, y: 150 },
            { x: 50, y: 100 },
          ],
        },
      }

      // Setup factory to return mock shape
      mockFactory.createShape.mockResolvedValueOnce(mockPolygonShape)

      // Call the method with valid data
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.POLYGON,
          position: { x: 100, y: 100 },
          properties: {
            points: [
              { x: 100, y: 50 },
              { x: 150, y: 100 },
              { x: 100, y: 150 },
              { x: 50, y: 100 },
            ],
          },
        },
      })

      // Verify factory was called with correct parameters
      expect(mockFactory.createShape).toHaveBeenCalledWith(
        ElementType.POLYGON,
        expect.any(Object),
      )

      // Verify repository was called with the created shape
      expect(mockRepository.add).toHaveBeenCalledWith(mockPolygonShape)

      // Verify success event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_CREATE_COMPLETE,
        payload: expect.objectContaining({
          shapeId: mockPolygonShape.id,
          ElementType: mockPolygonShape.type,
        }),
      })
    })

    it('should create a line shape with correct parameters', async () => {
      // Mock shape to be created
      const mockLineShape = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        properties: {
          start: { x: 0, y: 0 },
          end: { x: 100, y: 100 },
        },
      }

      // Setup factory to return mock shape
      mockFactory.createShape.mockResolvedValueOnce(mockLineShape)

      // Call the method with valid data
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.LINE,
          position: { x: 0, y: 0 },
          properties: {
            start: { x: 0, y: 0 },
            end: { x: 100, y: 100 },
          },
        },
      })

      // Verify factory was called with correct parameters
      expect(mockFactory.createShape).toHaveBeenCalledWith(
        ElementType.LINE,
        expect.objectContaining({
          start: expect.anything(),
          end: expect.anything(),
        }),
      )

      // Verify repository was called with the created shape
      expect(mockRepository.add).toHaveBeenCalledWith(mockLineShape)

      // Verify success event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_CREATE_COMPLETE,
        payload: expect.objectContaining({
          shapeId: mockLineShape.id,
          ElementType: mockLineShape.type,
        }),
      })
    })

    it('should create a polyline shape with correct parameters', async () => {
      // Mock shape to be created
      const mockPolylineShape = {
        id: 'polyline-1',
        type: ElementType.POLYLINE,
        position: { x: 0, y: 0 },
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 50, y: 50 },
            { x: 100, y: 0 },
          ],
        },
      }

      // Setup factory to return mock shape
      mockFactory.createShape.mockResolvedValueOnce(mockPolylineShape)

      // Call the method with valid data
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.POLYLINE,
          position: { x: 0, y: 0 },
          properties: {
            points: [
              { x: 0, y: 0 },
              { x: 50, y: 50 },
              { x: 100, y: 0 },
            ],
          },
        },
      })

      // Verify factory was called with correct parameters
      expect(mockFactory.createShape).toHaveBeenCalledWith(
        ElementType.POLYLINE,
        expect.objectContaining({
          points: expect.arrayContaining([
            expect.objectContaining({ x: 0, y: 0 }),
            expect.objectContaining({ x: 50, y: 50 }),
            expect.objectContaining({ x: 100, y: 0 }),
          ]),
        }),
      )

      // Verify repository was called with the created shape
      expect(mockRepository.add).toHaveBeenCalledWith(mockPolylineShape)

      // Verify success event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_CREATE_COMPLETE,
        payload: expect.objectContaining({
          shapeId: mockPolylineShape.id,
          ElementType: mockPolylineShape.type,
        }),
      })
    })

    it('should handle custom ID in properties', async () => {
      // Mock shape to be created with custom ID
      const customId = 'custom-rectangle-id'
      const mockRectangleShape = {
        id: customId,
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
        properties: {
          width: 100,
          height: 50,
        },
      }

      // Setup factory to return mock shape
      mockFactory.createShape.mockResolvedValueOnce(mockRectangleShape)

      // Call the method with valid data including custom ID
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            id: customId,
            width: 100,
            height: 50,
          },
        },
      })

      // Verify factory was called with correct parameters including custom ID
      expect(mockFactory.createShape).toHaveBeenCalledWith(
        ElementType.RECTANGLE,
        expect.objectContaining({
          id: customId,
        }),
      )

      // Verify repository was called with the created shape
      expect(mockRepository.add).toHaveBeenCalledWith(mockRectangleShape)

      // Verify success event was published with custom ID
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_CREATE_COMPLETE,
        payload: expect.objectContaining({
          shapeId: customId,
        }),
      })
    })

    it('should handle complex validation scenarios', async () => {
      // Mock validation with warnings but still valid
      vi.mocked(ElementValidator.validateElement).mockResolvedValueOnce({
        isValid: true,
        warnings: ['Property X is deprecated'],
        errors: [],
      })

      // Mock shape to be created
      const mockShape = {
        id: 'rectangle-warning-1',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
        properties: {
          width: 100,
          height: 50,
        },
      }

      // Setup factory to return mock shape
      mockFactory.createShape.mockResolvedValueOnce(mockShape)

      // Call the method
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 100,
            height: 50,
          },
        },
      })

      // Verify validation was called
      expect(ElementValidator.validateElement).toHaveBeenCalled()

      // Verify factory and repository were still called despite warnings
      expect(mockFactory.createShape).toHaveBeenCalled()
      expect(mockRepository.add).toHaveBeenCalled()

      // Verify success event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_CREATE_COMPLETE,
        payload: expect.any(Object),
      })
    })
  })
})
