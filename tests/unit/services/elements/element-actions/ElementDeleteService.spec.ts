import type { AppEventMap, EventBus, ShapeDeleteEvent } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import type { ElementDeleteRequest } from '@/types/services/shapes'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ElementDeleteService } from '@/services/elements/element-actions/elementDeleteService'
import { AppEventType } from '@/types/services/events'

// Mock dependencies
const mockEventBus = {
  publish: vi.fn(),
  subscribe: vi.fn(),
  unsubscribe: vi.fn(),
  clear: vi.fn(),
} as unknown as EventBus<AppEventMap>

const mockLogger = {
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  debug: vi.fn(),
  setContext: vi.fn(),
  getConfig: vi.fn(),
  setConfig: vi.fn(),
} as unknown as LoggerService

// Mock external dependencies
vi.mock('@/services/core/registry', () => ({
  getService: vi.fn((id) => {
    if (id === 'event-bus' || id.includes('EventBus'))
      return mockEventBus
    if (id === 'logger' || id.includes('Logger'))
      return mockLogger
    throw new Error(`Service ${id} not found`)
  }),
  ServiceId: {
    EventBus: 'event-bus',
    Logger: 'logger',
    ElementDeleteService: 'element-delete-service',
  },
}))

vi.mock('uuid', () => ({
  v4: vi.fn(() => 'mock-uuid-1234'),
}))

describe('elementDeleteService', () => {
  let deleteService: ElementDeleteService

  beforeEach(() => {
    vi.clearAllMocks()
    deleteService = new ElementDeleteService(mockEventBus, mockLogger)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('constructor', () => {
    it('should initialize with dependencies', () => {
      expect(deleteService).toBeDefined()
      expect(deleteService.serviceId).toBe('element-delete-service')
      expect(mockLogger.info).toHaveBeenCalledWith('[ElementDeleteService] Initialized.')
    })
  })

  describe('static create method', () => {
    it('should create instance with provided logger', () => {
      const instance = ElementDeleteService.create(mockLogger)
      expect(instance).toBeInstanceOf(ElementDeleteService)
    })

    it('should create instance without logger (using registry)', () => {
      const instance = ElementDeleteService.create()
      expect(instance).toBeInstanceOf(ElementDeleteService)
    })

    it('should throw error when dependencies cannot be resolved', async () => {
      // Import the mocked module and override the mock temporarily
      const registryModule = await import('@/services/core/registry')
      const mockGetService = vi.mocked(registryModule.getService)

      mockGetService.mockImplementationOnce(() => {
        throw new Error('Service not found')
      })

      expect(() => ElementDeleteService.create()).toThrow(
        /Failed to create ElementDeleteService:/,
      )
    })
  })

  describe('deleteShape', () => {
    it('should delete a single shape successfully', async () => {
      const request: ElementDeleteRequest = {
        id: 'shape-1',
      }

      const result = await deleteService.deleteShape(request)

      expect(result.success).toBe(true)
      expect(result.data).toBeUndefined()
      expect(result.timestamp).toBeDefined()

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeDeleteRequest,
        timestamp: expect.any(Number),
        payload: {
          shapeId: 'shape-1',
          source: 'ElementDeleteService',
        },
      })

      expect(mockLogger.debug).toHaveBeenCalledWith(
        '[ElementDeleteService] Publishing ShapeDeleteRequest for ID shape-1',
        { request },
      )
    })

    it('should handle errors during deletion', async () => {
      const request: ElementDeleteRequest = {
        id: 'shape-1',
      }

      // Mock eventBus.publish to throw an error
      mockEventBus.publish.mockImplementationOnce(() => {
        throw new Error('Event bus error')
      })

      const result = await deleteService.deleteShape(request)

      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
      expect(result.error?.code).toBe('SHAPE_DELETE_ERROR')
      expect(result.error?.message).toBe('Event bus error')
      expect(result.timestamp).toBeDefined()
    })

    it('should handle unknown errors', async () => {
      const request: ElementDeleteRequest = {
        id: 'shape-1',
      }

      // Mock eventBus.publish to throw a non-Error object
      mockEventBus.publish.mockImplementationOnce(() => {
        throw 'String error'
      })

      const result = await deleteService.deleteShape(request)

      expect(result.success).toBe(false)
      expect(result.error?.message).toBe('Unknown error')
    })
  })

  describe('deleteShapes', () => {
    it('should delete multiple shapes successfully', async () => {
      const shapeIds = ['shape-1', 'shape-2', 'shape-3']

      const result = await deleteService.deleteShapes(shapeIds)

      expect(result.success).toBe(true)
      expect(result.data).toEqual([])
      expect(result.timestamp).toBeDefined()

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeDeleteRequest,
        timestamp: expect.any(Number),
        payload: {
          shapeId: shapeIds,
          source: 'ElementDeleteService',
        },
      })

      expect(mockLogger.debug).toHaveBeenCalledWith(
        '[ElementDeleteService] Publishing ShapeDeleteRequest for IDs',
        { shapeIds },
      )
    })

    it('should handle empty array', async () => {
      const result = await deleteService.deleteShapes([])

      expect(result.success).toBe(true)
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeDeleteRequest,
        timestamp: expect.any(Number),
        payload: {
          shapeId: [],
          source: 'ElementDeleteService',
        },
      })
    })

    it('should handle errors during multiple deletion', async () => {
      const shapeIds = ['shape-1', 'shape-2']

      // Mock eventBus.publish to throw an error
      mockEventBus.publish.mockImplementationOnce(() => {
        throw new Error('Event bus error')
      })

      const result = await deleteService.deleteShapes(shapeIds)

      expect(result.success).toBe(false)
      expect(result.error?.code).toBe('SHAPE_DELETE_ERROR')
      expect(result.error?.message).toBe('Event bus error')
    })
  })

  describe('handleRequest', () => {
    it('should handle single shape delete request', async () => {
      const event: ShapeDeleteEvent = {
        type: AppEventType.ShapeDeleteRequest,
        timestamp: Date.now(),
        payload: {
          shapeId: 'shape-1',
          source: 'test',
        },
      }

      await deleteService.handleRequest(event)

      expect(mockLogger.info).toHaveBeenCalledWith(
        'ElementDeleteService handling SHAPE_DELETE_REQUEST:',
        event.payload,
      )

      // Should publish StateUpdated event
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.StateUpdated,
        timestamp: expect.any(Number),
        payload: {
          action: 'preDelete',
          elementIds: ['shape-1'],
        },
      })

      // Should publish ShapeDeleteComplete event
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeDeleteComplete,
        timestamp: expect.any(Number),
        payload: {
          shapeId: 'shape-1',
          source: 'ElementDeleteService',
        },
      })
    })

    it('should handle multiple shape delete request', async () => {
      const event: ShapeDeleteEvent = {
        type: AppEventType.ShapeDeleteRequest,
        timestamp: Date.now(),
        payload: {
          shapeId: ['shape-1', 'shape-2', 'shape-3'],
          source: 'test',
        },
      }

      await deleteService.handleRequest(event)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.StateUpdated,
        timestamp: expect.any(Number),
        payload: {
          action: 'preDelete',
          elementIds: ['shape-1', 'shape-2', 'shape-3'],
        },
      })

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeDeleteComplete,
        timestamp: expect.any(Number),
        payload: {
          shapeId: ['shape-1', 'shape-2', 'shape-3'],
          source: 'ElementDeleteService',
        },
      })
    })

    it('should handle invalid payload - missing shapeId', async () => {
      const event: ShapeDeleteEvent = {
        type: AppEventType.ShapeDeleteRequest,
        timestamp: Date.now(),
        payload: {
          shapeId: undefined as any,
          source: 'test',
        },
      }

      await deleteService.handleRequest(event)

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Invalid or missing shapeId payload for SHAPE_DELETE_REQUEST',
        event.payload,
      )

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ErrorOccurred,
        payload: {
          code: 'INVALID_PAYLOAD',
          message: 'Invalid payload for shape delete: shapeId (string or array) is required.',
          details: expect.any(Object),
        },
      })
    })

    it('should handle invalid payload - null shapeId', async () => {
      const event: ShapeDeleteEvent = {
        type: AppEventType.ShapeDeleteRequest,
        timestamp: Date.now(),
        payload: {
          shapeId: null as any,
          source: 'test',
        },
      }

      await deleteService.handleRequest(event)

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Invalid or missing shapeId payload for SHAPE_DELETE_REQUEST',
        event.payload,
      )
    })

    it('should handle invalid payload - empty string shapeId', async () => {
      const event: ShapeDeleteEvent = {
        type: AppEventType.ShapeDeleteRequest,
        timestamp: Date.now(),
        payload: {
          shapeId: '',
          source: 'test',
        },
      }

      await deleteService.handleRequest(event)

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Invalid or missing shapeId payload for SHAPE_DELETE_REQUEST',
        event.payload,
      )
    })

    it('should handle empty array shapeId', async () => {
      const event: ShapeDeleteEvent = {
        type: AppEventType.ShapeDeleteRequest,
        timestamp: Date.now(),
        payload: {
          shapeId: [],
          source: 'test',
        },
      }

      await deleteService.handleRequest(event)

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Empty idsToDelete array after processing shapeId payload',
        event.payload,
      )

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ErrorOccurred,
        payload: {
          code: 'INVALID_PAYLOAD',
          message: 'Invalid payload for shape delete: resulting ID list is empty.',
          details: expect.any(Object),
        },
      })
    })

    it('should handle malformed event', async () => {
      const event = null as any

      await deleteService.handleRequest(event)

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Invalid or missing shapeId payload for SHAPE_DELETE_REQUEST',
        undefined,
      )
    })
  })

  describe('error handling', () => {
    it('should emit error events correctly', async () => {
      const request: ElementDeleteRequest = {
        id: 'shape-1',
      }

      // Mock eventBus.publish to throw an error
      mockEventBus.publish.mockImplementationOnce(() => {
        throw new Error('Test error')
      })

      await deleteService.deleteShape(request)

      expect(mockLogger.error).toHaveBeenCalledWith(
        '[SHAPE_DELETE_ERROR] Test error',
        expect.objectContaining({
          errorId: 'mock-uuid-1234',
          request,
        }),
      )

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.EventError,
        timestamp: expect.any(Number),
        payload: {
          error: {
            code: 'SHAPE_DELETE_ERROR',
            message: '[SHAPE_DELETE_ERROR] Test error',
            details: { request },
            errorId: 'mock-uuid-1234',
          },
          context: { request },
        },
      })
    })
  })

  describe('edge cases', () => {
    it('should handle very long shape ID arrays', async () => {
      const longArray = Array.from({ length: 1000 }, (_, i) => `shape-${i}`)

      const result = await deleteService.deleteShapes(longArray)

      expect(result.success).toBe(true)
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeDeleteRequest,
        timestamp: expect.any(Number),
        payload: {
          shapeId: longArray,
          source: 'ElementDeleteService',
        },
      })
    })

    it('should handle special characters in shape IDs', async () => {
      const specialIds = ['shape-@#$%', 'shape-with-unicode-🚀', 'shape with spaces']

      const result = await deleteService.deleteShapes(specialIds)

      expect(result.success).toBe(true)
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeDeleteRequest,
        timestamp: expect.any(Number),
        payload: {
          shapeId: specialIds,
          source: 'ElementDeleteService',
        },
      })
    })
  })
})
