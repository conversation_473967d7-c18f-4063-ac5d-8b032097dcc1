/**
 * Provides centralized exports for image-related type definitions.
 *
 * @remarks
 * This module serves as the barrel file for image element types, primarily re-exporting
 * the {@link Image} interface from the `./image` module.
 *
 * @module types/core/element/image
 * @example
 * ```typescript
 * import { Image } from '@/types/core/element/image'; // Assuming path alias
 * // or
 * // import { Image } from './image'; // If used within the same directory structure
 *
 * const myImage: Image = {
 *   id: 'img1',
 *   type: 'image',
 *   visible: true,
 *   locked: false,
 *   position: { x: 0, y: 0 },
 *   rotation: 0,
 *   scale: { x: 1, y: 1 },
 *   selectable: true,
 *   draggable: true,
 *   showHandles: true,
 *   src: 'path/to/image.png',
 *   width: 100,
 *   height: 100
 * };
 * ```
 *
 * @see {@link Image} for the main image element interface.
 * @see {@link ShapeElement} for base shape properties that an Image element extends.
 * @see {@link Point} for the position coordinate structure.
 */

/** Re-exports all types from the `./image` module, including the {@link Image} interface. */
export * from './imageElementTypes'
