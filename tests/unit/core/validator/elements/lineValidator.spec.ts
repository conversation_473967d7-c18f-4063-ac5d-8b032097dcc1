/**
 * Unit tests for LineValidator
 *
 * Tests the line-specific validator
 */

import { expect, test } from '@playwright/test'

// Define types locally for testing
interface ValidationError {
  code: string
  message: string
  path?: string
  value?: any
}

// Mock LineValidator for testing
class LineValidator {
  validate(shape: any): { valid: boolean, errors: ValidationError[] } {
    const errors: ValidationError[] = []

    // Check shape type
    if (!shape || shape.type !== 'line') {
      errors.push({ code: 'INVALID_SHAPE_TYPE', message: 'Not a line shape' })
      return { valid: false, errors }
    }

    // Check ID
    if (!shape.id || shape.id.trim() === '') {
      errors.push({ code: 'MISSING_ID', message: 'Missing ID' })
    }

    // Check position
    if (!shape.position || typeof shape.position.x !== 'number' || typeof shape.position.y !== 'number'
      || isNaN(shape.position.x) || isNaN(shape.position.y)) {
      errors.push({ code: 'INVALID_POSITION', message: 'Invalid position' })
    }

    // Check start point
    if (!shape.start || typeof shape.start.x !== 'number' || typeof shape.start.y !== 'number'
      || isNaN(shape.start.x) || isNaN(shape.start.y)) {
      errors.push({ code: 'INVALID_START_POINT', message: 'Invalid start point' })
    }

    // Check end point
    if (!shape.end || typeof shape.end.x !== 'number' || typeof shape.end.y !== 'number'
      || isNaN(shape.end.x) || isNaN(shape.end.y)) {
      errors.push({ code: 'INVALID_END_POINT', message: 'Invalid end point' })
    }

    // Check if start and end points are identical
    if (shape.start && shape.end
      && shape.start.x === shape.end.x && shape.start.y === shape.end.y) {
      errors.push({ code: 'IDENTICAL_POINTS', message: 'Start and end points are identical' })
    }

    // Check line length
    if (shape.start && shape.end) {
      const dx = shape.end.x - shape.start.x
      const dy = shape.end.y - shape.start.y
      const length = Math.sqrt(dx * dx + dy * dy)

      if (length < 5) {
        errors.push({ code: 'LINE_TOO_SHORT', message: 'Line is too short' })
      }
    }

    // Check colors
    const validColors = ['#000', '#FFF', '#000000', '#FFFFFF', '#3b82f6', 'red', 'blue', 'green', 'transparent']
    if (!validColors.includes(shape.strokeColor)) {
      errors.push({ code: 'INVALID_COLOR', message: 'Invalid stroke color' })
    }
    if (!validColors.includes(shape.fillColor)) {
      errors.push({ code: 'INVALID_COLOR', message: 'Invalid fill color' })
    }

    return { valid: errors.length === 0, errors }
  }
}

test.describe('LineValidator', () => {
  let validator: LineValidator

  test.beforeEach(() => {
    validator = new LineValidator()
  })

  test('validates a valid line', () => {
    // Create a valid line
    const validLine = {
      id: 'line1',
      type: 'line',
      position: { x: 100, y: 100 },
      start: { x: 0, y: 0 },
      end: { x: 100, y: 100 },
      strokeColor: '#000000',
      fillColor: 'transparent',
    }

    // Validate the line
    const result = validator.validate(validLine as any)

    // Verify the result
    expect(result.valid).toBe(true)
    expect(result.errors).toEqual([])
  })

  test('detects a line with missing ID', () => {
    // Create a line with missing ID
    const invalidLine = {
      id: '',
      type: 'line',
      position: { x: 100, y: 100 },
      start: { x: 0, y: 0 },
      end: { x: 100, y: 100 },
      strokeColor: '#000000',
      fillColor: 'transparent',
    }

    // Validate the line
    const result = validator.validate(invalidLine as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'MISSING_ID')).toBe(true)
  })

  test('detects a line with invalid position', () => {
    // Create a line with invalid position
    const invalidLine = {
      id: 'line2',
      type: 'line',
      position: { x: Number.NaN, y: 100 },
      start: { x: 0, y: 0 },
      end: { x: 100, y: 100 },
      strokeColor: '#000000',
      fillColor: 'transparent',
    }

    // Validate the line
    const result = validator.validate(invalidLine as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_POSITION')).toBe(true)
  })

  test('detects a line with invalid start point', () => {
    // Create a line with invalid start point
    const invalidLine = {
      id: 'line3',
      type: 'line',
      position: { x: 100, y: 100 },
      start: { x: Number.NaN, y: 0 },
      end: { x: 100, y: 100 },
      strokeColor: '#000000',
      fillColor: 'transparent',
    }

    // Validate the line
    const result = validator.validate(invalidLine as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_START_POINT')).toBe(true)
  })

  test('detects a line with invalid end point', () => {
    // Create a line with invalid end point
    const invalidLine = {
      id: 'line4',
      type: 'line',
      position: { x: 100, y: 100 },
      start: { x: 0, y: 0 },
      end: { x: Number.NaN, y: 100 },
      strokeColor: '#000000',
      fillColor: 'transparent',
    }

    // Validate the line
    const result = validator.validate(invalidLine as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_END_POINT')).toBe(true)
  })

  test('detects a line with identical start and end points', () => {
    // Create a line with identical start and end points
    const invalidLine = {
      id: 'line5',
      type: 'line',
      position: { x: 100, y: 100 },
      start: { x: 50, y: 50 },
      end: { x: 50, y: 50 },
      strokeColor: '#000000',
      fillColor: 'transparent',
    }

    // Validate the line
    const result = validator.validate(invalidLine as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'IDENTICAL_POINTS')).toBe(true)
  })

  test('detects a line with invalid colors', () => {
    // Create a line with invalid colors
    const invalidLine = {
      id: 'line6',
      type: 'line',
      position: { x: 100, y: 100 },
      start: { x: 0, y: 0 },
      end: { x: 100, y: 100 },
      strokeColor: 'invalid-color',
      fillColor: 'invalid-color',
    }

    // Validate the line
    const result = validator.validate(invalidLine as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_COLOR')).toBe(true)
  })

  test('rejects non-line shapes', () => {
    // Create a circle (not a line)
    const circle = {
      id: 'circle1',
      type: 'circle',
      position: { x: 100, y: 100 },
      radius: 50,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the circle using the line validator
    const result = validator.validate(circle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_SHAPE_TYPE')).toBe(true)
  })

  test('applies business rules for minimum length', () => {
    // Create a line with length below the minimum
    const shortLine = {
      id: 'line7',
      type: 'line',
      position: { x: 100, y: 100 },
      start: { x: 0, y: 0 },
      end: { x: 2, y: 2 }, // Length is about 2.83, below minimum of 5
      strokeColor: '#000000',
      fillColor: 'transparent',
    }

    // Validate the line
    const result = validator.validate(shortLine as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'LINE_TOO_SHORT')).toBe(true)
  })
})
