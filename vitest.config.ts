import { resolve } from 'node:path'
import react from '@vitejs/plugin-react'
/// <reference types="vitest" />
import { defineConfig } from 'vite'
import tsconfigPaths from 'vite-tsconfig-paths' // Import plugin

export default defineConfig({
  plugins: [
    react(),
    tsconfigPaths({
      projects: [resolve('tsconfig.json'), resolve('tsconfig.test.json')],
      loose: true,
    }),
  ],
  resolve: {
    alias: [
      // '@' alias is handled by vite-tsconfig-paths plugin
      { find: '@tests', replacement: resolve(__dirname, './tests') }, // Consider moving this to tsconfig.paths.json
    ],
  },
  test: {
    globals: true,
    environment: 'jsdom',
    // Include both unit and integration tests
    include: [
      'tests/unit/**/*.spec.ts',
      'tests/unit/**/*.test.ts',
      'tests/unit/**/*.test.tsx',
      'tests/unit/**/*.spec.tsx',
      'tests/unit/**/*.vitest.spec.ts',
      'tests/integration/**/*.spec.ts',
      'tests/integration/**/*.test.ts',
      'tests/integration/**/*.test.tsx',
      'tests/integration/**/*.spec.tsx',
    ],
    exclude: ['**/node_modules/**', '**/dist/**'],
    // setupFiles: ['./tests/setup/test-setup.js'], // Keep setup file for now
    deps: {
      inline: ['@testing-library/react'],
    },
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      include: [
        'src/**/*.{ts,tsx}', // Include all ts/tsx files in src
      ],
      exclude: [
        '**/*.d.ts',
        '**/index.ts',
        '**/*.spec.ts',
        '**/*.test.ts',
      ],
      thresholds: {
        'lines': 80,
        'functions': 80,
        'branches': 80,
        'statements': 80,
        // Specific thresholds for src/core directory - 90% coverage required
        'src/core/**/*.ts': {
          lines: 90,
          functions: 90,
          branches: 90,
          statements: 90,
        },
      },
    },
    mockReset: true,
    restoreMocks: true,
    clearMocks: true,
  },
})
