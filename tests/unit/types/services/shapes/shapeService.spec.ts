import type {
  ShapeCreationRequest,
  ShapeDeleteRequest,
  ShapeEditRequest,
  ShapeSelectRequest,
} from '@/types/services/shapes'
import type {
  ShapeCreationService,
  ShapeDeleteService,
  ShapeEditService,
  ShapeSelectionService,
  ShapeService,
} from '@/types/services/shapes/shapeService'
import { describe, expect, it, vi } from 'vitest'
import { ElementType } from '@/types/core'

describe('shape Service Types', () => {
  describe('shapeService', () => {
    it('should define the base service interface', () => {
      // Create a mock implementation of ShapeService
      const mockShapeService: ShapeService = {
        serviceId: 'test-shape-service',
      }

      expect(mockShapeService.serviceId).toBe('test-shape-service')
    })
  })

  describe('shapeCreationService', () => {
    it('should define all required methods', () => {
      // Create a mock implementation of ShapeCreationService
      const mockCreationService: ShapeCreationService = {
        serviceId: 'shape-creation-service',
        createShape: vi.fn(),
        duplicateShape: vi.fn(),
      }

      expect(mockCreationService.serviceId).toBe('shape-creation-service')
      expect(typeof mockCreationService.createShape).toBe('function')
      expect(typeof mockCreationService.duplicateShape).toBe('function')
    })

    it('should allow calling createShape with a request', async () => {
      // Create a mock implementation of ShapeCreationService
      const mockCreationService: ShapeCreationService = {
        serviceId: 'shape-creation-service',
        createShape: vi.fn().mockResolvedValue({
          success: true,
          data: {
            id: 'shape-123',
            type: ElementType.RECTANGLE,
            position: { x: 100, y: 200 },
          },
          timestamp: Date.now(),
        }),
        duplicateShape: vi.fn(),
      }

      const request: ShapeCreationRequest = {
        elementType: ElementType.RECTANGLE,
        position: { x: 100, y: 200 },
      }

      const result = await mockCreationService.createShape(request)

      expect(mockCreationService.createShape).toHaveBeenCalledWith(request)
      expect(result.success).toBe(true)
      expect(result.data?.id).toBe('shape-123')
    })

    it('should allow calling duplicateShape with parameters', async () => {
      // Create a mock implementation of ShapeCreationService
      const mockCreationService: ShapeCreationService = {
        serviceId: 'shape-creation-service',
        createShape: vi.fn(),
        duplicateShape: vi.fn().mockResolvedValue({
          success: true,
          data: {
            id: 'shape-456',
            type: ElementType.RECTANGLE,
            position: { x: 150, y: 250 },
          },
          timestamp: Date.now(),
        }),
      }

      const result = await mockCreationService.duplicateShape('shape-123', { x: 50, y: 50 })

      expect(mockCreationService.duplicateShape).toHaveBeenCalledWith('shape-123', { x: 50, y: 50 })
      expect(result.success).toBe(true)
      expect(result.data?.id).toBe('shape-456')
    })
  })

  describe('shapeEditService', () => {
    it('should define all required methods', () => {
      // Create a mock implementation of ShapeEditService
      const mockEditService: ShapeEditService = {
        serviceId: 'shape-edit-service',
        editShape: vi.fn(),
        transformShapes: vi.fn(),
      }

      expect(mockEditService.serviceId).toBe('shape-edit-service')
      expect(typeof mockEditService.editShape).toBe('function')
      expect(typeof mockEditService.transformShapes).toBe('function')
    })

    it('should allow calling editShape with a request', async () => {
      // Create a mock implementation of ShapeEditService
      const mockEditService: ShapeEditService = {
        serviceId: 'shape-edit-service',
        editShape: vi.fn().mockResolvedValue({
          success: true,
          data: {
            id: 'shape-123',
            type: ElementType.RECTANGLE,
            position: { x: 150, y: 250 },
          },
          timestamp: Date.now(),
        }),
        transformShapes: vi.fn(),
      }

      const request: ShapeEditRequest = {
        id: 'shape-123',
        position: { x: 150, y: 250 },
      }

      const result = await mockEditService.editShape(request)

      expect(mockEditService.editShape).toHaveBeenCalledWith(request)
      expect(result.success).toBe(true)
      expect(result.data?.position.x).toBe(150)
      expect(result.data?.position.y).toBe(250)
    })

    it('should allow calling transformShapes with parameters', async () => {
      // Create a mock implementation of ShapeEditService
      const mockEditService: ShapeEditService = {
        serviceId: 'shape-edit-service',
        editShape: vi.fn(),
        transformShapes: vi.fn().mockResolvedValue({
          success: true,
          data: [
            { id: 'shape-123', type: ElementType.RECTANGLE, position: { x: 150, y: 250 } },
            { id: 'shape-456', type: ElementType.ELLIPSE, position: { x: 350, y: 450 } },
          ],
          timestamp: Date.now(),
        }),
      }

      const transformations = { scale: 1.5, rotate: 45 }
      const result = await mockEditService.transformShapes(['shape-123', 'shape-456'], transformations)

      expect(mockEditService.transformShapes).toHaveBeenCalledWith(['shape-123', 'shape-456'], transformations)
      expect(result.success).toBe(true)
      expect(result.data).toHaveLength(2)
      expect(result.data?.[0].id).toBe('shape-123')
      expect(result.data?.[1].id).toBe('shape-456')
    })
  })

  describe('shapeDeleteService', () => {
    it('should define all required methods', () => {
      // Create a mock implementation of ShapeDeleteService
      const mockDeleteService: ShapeDeleteService = {
        serviceId: 'shape-delete-service',
        deleteShape: vi.fn(),
        deleteShapes: vi.fn(),
      }

      expect(mockDeleteService.serviceId).toBe('shape-delete-service')
      expect(typeof mockDeleteService.deleteShape).toBe('function')
      expect(typeof mockDeleteService.deleteShapes).toBe('function')
    })

    it('should allow calling deleteShape with a request', async () => {
      // Create a mock implementation of ShapeDeleteService
      const mockDeleteService: ShapeDeleteService = {
        serviceId: 'shape-delete-service',
        deleteShape: vi.fn().mockResolvedValue({
          success: true,
          data: 'shape-123',
          timestamp: Date.now(),
        }),
        deleteShapes: vi.fn(),
      }

      const request: ShapeDeleteRequest = {
        id: 'shape-123',
      }

      const result = await mockDeleteService.deleteShape(request)

      expect(mockDeleteService.deleteShape).toHaveBeenCalledWith(request)
      expect(result.success).toBe(true)
      expect(result.data).toBe('shape-123')
    })

    it('should allow calling deleteShapes with parameters', async () => {
      // Create a mock implementation of ShapeDeleteService
      const mockDeleteService: ShapeDeleteService = {
        serviceId: 'shape-delete-service',
        deleteShape: vi.fn(),
        deleteShapes: vi.fn().mockResolvedValue({
          success: true,
          data: ['shape-123', 'shape-456'],
          timestamp: Date.now(),
        }),
      }

      const result = await mockDeleteService.deleteShapes(['shape-123', 'shape-456'])

      expect(mockDeleteService.deleteShapes).toHaveBeenCalledWith(['shape-123', 'shape-456'])
      expect(result.success).toBe(true)
      expect(result.data).toHaveLength(2)
      expect(result.data?.[0]).toBe('shape-123')
      expect(result.data?.[1]).toBe('shape-456')
    })
  })

  describe('shapeSelectionService', () => {
    it('should define all required methods', () => {
      // Create a mock implementation of ShapeSelectionService
      const mockSelectionService: ShapeSelectionService = {
        serviceId: 'shape-selection-service',
        selectShape: vi.fn(),
        selectShapes: vi.fn(),
        clearSelection: vi.fn(),
      }

      expect(mockSelectionService.serviceId).toBe('shape-selection-service')
      expect(typeof mockSelectionService.selectShape).toBe('function')
      expect(typeof mockSelectionService.selectShapes).toBe('function')
      expect(typeof mockSelectionService.clearSelection).toBe('function')
    })

    it('should allow calling selectShape with a request', async () => {
      // Create a mock implementation of ShapeSelectionService
      const mockSelectionService: ShapeSelectionService = {
        serviceId: 'shape-selection-service',
        selectShape: vi.fn().mockResolvedValue({
          success: true,
          data: ['shape-123'],
          timestamp: Date.now(),
        }),
        selectShapes: vi.fn(),
        clearSelection: vi.fn(),
      }

      const request: ShapeSelectRequest = {
        id: 'shape-123',
      }

      const result = await mockSelectionService.selectShape(request)

      expect(mockSelectionService.selectShape).toHaveBeenCalledWith(request)
      expect(result.success).toBe(true)
      expect(result.data).toHaveLength(1)
      expect(result.data?.[0]).toBe('shape-123')
    })

    it('should allow calling selectShapes with parameters', async () => {
      // Create a mock implementation of ShapeSelectionService
      const mockSelectionService: ShapeSelectionService = {
        serviceId: 'shape-selection-service',
        selectShape: vi.fn(),
        selectShapes: vi.fn().mockResolvedValue({
          success: true,
          data: ['shape-123', 'shape-456'],
          timestamp: Date.now(),
        }),
        clearSelection: vi.fn(),
      }

      const result = await mockSelectionService.selectShapes(['shape-123', 'shape-456'], true)

      expect(mockSelectionService.selectShapes).toHaveBeenCalledWith(['shape-123', 'shape-456'], true)
      expect(result.success).toBe(true)
      expect(result.data).toHaveLength(2)
      expect(result.data?.[0]).toBe('shape-123')
      expect(result.data?.[1]).toBe('shape-456')
    })

    it('should allow calling clearSelection', async () => {
      // Create a mock implementation of ShapeSelectionService
      const mockSelectionService: ShapeSelectionService = {
        serviceId: 'shape-selection-service',
        selectShape: vi.fn(),
        selectShapes: vi.fn(),
        clearSelection: vi.fn().mockResolvedValue({
          success: true,
          timestamp: Date.now(),
        }),
      }

      const result = await mockSelectionService.clearSelection()

      expect(mockSelectionService.clearSelection).toHaveBeenCalled()
      expect(result.success).toBe(true)
    })
  })
})
