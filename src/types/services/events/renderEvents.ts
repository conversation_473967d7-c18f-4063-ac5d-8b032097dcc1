/**
 * Render Events Type Definitions
 *
 * This file defines the event types related to rendering operations,
 * including render triggers, completion events, and errors.
 *
 * @module types/services/events
 */

import type { BaseEvent } from './eventCore'
import type { AppEventType } from './eventTypes'

/**
 * Basic render options type
 */
export interface RenderOptions {
  /** Render quality level */
  quality?: 'low' | 'medium' | 'high'
  /** Whether to clear the canvas before rendering */
  clear?: boolean
  /** Layer to render (if omitted, all layers are rendered) */
  layer?: string
  /** Specific elements to render (if omitted, all elements are rendered) */
  elementIds?: string[]
  /** Whether to include decorations (selection handles, etc.) */
  includeDecorations?: boolean
  /** Whether to run in high performance mode (may reduce quality) */
  highPerformance?: boolean
}

/**
 * General Render Event type
 *
 * Union type for all render-related events
 */
export type RenderEvent =
  | RenderTriggerEvent
  | RenderStartEvent
  | RenderCompleteEvent
  | RenderErrorEvent

/**
 * Event triggered when a render operation is requested.
 */
export interface RenderTriggerEvent extends BaseEvent {
  type: AppEventType.RenderTrigger
  payload: {
    /** Optional rendering options */
    options?: RenderOptions
  }
}

/**
 * Event triggered when a render operation begins.
 */
export interface RenderStartEvent extends BaseEvent {
  type: AppEventType.RenderStart
  payload: {
    /** Optional timestamp for performance tracking */
    timestamp?: number
    /** Options used for this render operation */
    options?: RenderOptions
  }
}

/**
 * Event triggered when a render operation completes.
 */
export interface RenderCompleteEvent extends BaseEvent {
  type: AppEventType.RenderComplete
  payload: {
    /** Duration of the render operation in milliseconds */
    duration?: number
    /** Frame rate (if applicable) */
    fps?: number
    /** Number of elements rendered */
    elementCount?: number
    /** Options used for this render operation */
    options?: RenderOptions
  }
}

/**
 * Event triggered when a render operation encounters an error.
 */
export interface RenderErrorEvent extends BaseEvent {
  type: AppEventType.RenderError
  payload: {
    /** Error message */
    message: string
    /** Error details */
    error?: Error
    /** Options used for this render operation */
    options?: RenderOptions
  }
}

/**
 * Layer Events
 */
export interface LayerEvent {
  type: AppEventType.LayerCreate | AppEventType.LayerUpdate | AppEventType.LayerDelete | AppEventType.LayerOrder
  payload: {
    layerId: string
    config?: {
      zIndex: number
      visible: boolean
      opacity: number
      name?: string
    }
    shapes?: string[] // Shape IDs contained in the layer
  }
}

/**
 * Layer Visibility Change Event
 */
export interface LayerVisibilityChangeEvent {
  type: AppEventType.LayerVisibilityChange
  payload: {
    layerId: string
    visible: boolean
  }
}

/**
 * Layer Lock Change Event
 */
export interface LayerLockChangeEvent {
  type: AppEventType.LayerLockChange
  payload: {
    layerId: string
    locked: boolean
  }
}

/**
 * Layer Order Change Event
 */
export interface LayerOrderChangeEvent {
  type: AppEventType.LayerOrderChange
  payload: {
    layerId: string
    newOrder: number
  }
}
