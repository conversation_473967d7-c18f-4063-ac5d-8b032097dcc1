import type { ValidatorShape } from '@/types/core/element/validator'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { LineValidator } from '@/core/validator/elements/lineValidator'
import { ElementType } from '@/types/core/shape-type'

describe('lineValidator', () => {
  let validator: LineValidator

  beforeEach(() => {
    validator = new LineValidator()
  })

  describe('validateSpecific', () => {
    it('should return error for invalid shape type', () => {
      const invalidLine: ValidatorShape = {
        id: 'line-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 50,
      }

      const errors = (validator as any).validateSpecific(invalidLine)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_SHAPE_TYPE')
    })
    it('should validate a valid line', () => {
      const validLine: ValidatorShape = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        start: { x: 0, y: 0 },
        end: { x: 100, y: 100 },
      }

      const errors = (validator as any).validateSpecific(validLine)
      expect(errors).toEqual([])
    })

    it('should return error for missing start point', () => {
      const invalidLine: ValidatorShape = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        end: { x: 100, y: 100 },
      }

      const errors = (validator as any).validateSpecific(invalidLine)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_START_POINT')
    })

    it('should return error for missing end point', () => {
      const invalidLine: ValidatorShape = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        start: { x: 0, y: 0 },
      }

      const errors = (validator as any).validateSpecific(invalidLine)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_END_POINT')
    })

    it('should return error for invalid start point', () => {
      const invalidLine: any = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        start: 'origin',
        end: { x: 100, y: 100 },
      }

      const errors = (validator as any).validateSpecific(invalidLine)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_START_POINT')
    })

    it('should return error for invalid end point', () => {
      const invalidLine: any = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        start: { x: 0, y: 0 },
        end: 'destination',
      }

      const errors = (validator as any).validateSpecific(invalidLine)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_END_POINT')
    })

    it('should return error for start point missing x coordinate', () => {
      const invalidLine: any = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        start: { y: 0 },
        end: { x: 100, y: 100 },
      }

      const errors = (validator as any).validateSpecific(invalidLine)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_START_POINT')
    })

    it('should return error for start point missing y coordinate', () => {
      const invalidLine: any = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        start: { x: 0 },
        end: { x: 100, y: 100 },
      }

      const errors = (validator as any).validateSpecific(invalidLine)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_START_POINT')
    })

    it('should return error for end point missing x coordinate', () => {
      const invalidLine: any = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        start: { x: 0, y: 0 },
        end: { y: 100 },
      }

      const errors = (validator as any).validateSpecific(invalidLine)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_END_POINT')
    })

    it('should return error for end point missing y coordinate', () => {
      const invalidLine: any = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        start: { x: 0, y: 0 },
        end: { x: 100 },
      }

      const errors = (validator as any).validateSpecific(invalidLine)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_END_POINT')
    })

    it('should return error for identical start and end points', () => {
      const invalidLine: ValidatorShape = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        start: { x: 50, y: 50 },
        end: { x: 50, y: 50 },
      }

      const errors = (validator as any).validateSpecific(invalidLine)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('IDENTICAL_POINTS')
    })
  })

  describe('applySpecificRules', () => {
    it('should apply specific rules for lines', () => {
      // This is a protected method, so we need to cast to any
      const applySpecificRulesSpy = vi.spyOn(validator as any, 'applySpecificRules')

      const validLine: ValidatorShape = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        start: { x: 0, y: 0 },
        end: { x: 100, y: 100 },
      }

      validator.validate(validLine)
      expect(applySpecificRulesSpy).toHaveBeenCalled()
    })
  })

  describe('validate', () => {
    it('should call validateSpecific', () => {
      const validLine: ValidatorShape = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        start: { x: 0, y: 0 },
        end: { x: 100, y: 100 },
      }

      const validateSpecificSpy = vi.spyOn(validator as any, 'validateSpecific').mockReturnValue([])

      const result = validator.validate(validLine)

      expect(validateSpecificSpy).toHaveBeenCalledWith(validLine)
      expect(result.valid).toBe(true)
      expect(result.errors).toEqual([])

      validateSpecificSpy.mockRestore()
    })

    it('should return invalid result when errors are found', () => {
      const invalidLine: ValidatorShape = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        start: { x: 50, y: 50 },
        end: { x: 50, y: 50 },
      }

      const result = validator.validate(invalidLine)

      expect(result.valid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
      expect(result.errors.some(error => error.code === 'IDENTICAL_POINTS')).toBe(true)
    })

    it('should detect multiple validation errors', () => {
      const invalidLine: ValidatorShape = {
        // Missing id
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        // Missing start and end points
      }

      const result = validator.validate(invalidLine)

      expect(result.valid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(1)
      expect(result.errors.some(error => error.code === 'MISSING_OR_INVALID_ID')).toBe(true)
      expect(result.errors.some(error => error.code === 'INVALID_START_POINT')).toBe(true)
      expect(result.errors.some(error => error.code === 'INVALID_END_POINT')).toBe(true)
    })
  })
})
