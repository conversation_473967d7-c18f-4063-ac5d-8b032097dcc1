/**
 * Cost Calculation Strategies Index
 *
 * @remarks
 * This module serves as a barrel export for all concrete strategy implementations
 * related to cost calculation. It re-exports the `CostCalculatorStrategy` interface
 * (and other relevant cost computation types) from `@/types/core/compute/costComputeTypes`,
 * and then exports all specific cost calculation strategy classes defined within this
 * directory (e.g., {@link AreaBasedCostStrategy}, {@link UnitBasedCostStrategy}).
 *
 * This allows for a centralized import point for accessing various cost
 * calculation strategies.
 *
 * @module core/compute/strategies/cost/index
 */

// Export concrete strategy implementations from the current directory
export * from './AreaBasedCostStrategy'

export * from './CompositeCostStrategy'
export * from './CurveCostStrategy'
export * from './PerimeterBasedCostStrategy'
export * from './PolylineCostStrategy'
export * from './UnitBasedCostStrategy'
// Export interface first for better code organization
export * from '@/types/core/compute/costComputeTypes' // This exports CostCalculatorStrategy etc.
