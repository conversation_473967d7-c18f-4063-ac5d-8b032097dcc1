import type { AppEventMap, EventBus } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ServicesErrorService } from '@/services/system/error-service/servicesErrorService'
import { ErrorSeverity, ErrorType } from '@/types/services/errors'

describe('servicesErrorService', () => {
  let servicesErrorService: ServicesErrorService
  let mockEventBus: EventBus<AppEventMap>
  let mockLogger: LoggerService

  beforeEach(() => {
    mockEventBus = {
      publish: vi.fn(),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
      clear: vi.fn(),
    } as unknown as EventBus<AppEventMap>

    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    }

    servicesErrorService = new ServicesErrorService(mockEventBus, mockLogger)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('constructor', () => {
    it('should initialize with event bus and logger', () => {
      // Create a fresh instance to test constructor behavior
      const freshLogger = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
      }

      const freshService = new ServicesErrorService(mockEventBus, freshLogger)

      expect(freshService).toBeInstanceOf(ServicesErrorService)
      expect(freshLogger.info).toHaveBeenCalledWith('[ServicesErrorService] Initialized.')
    })

    it('should extend ErrorService', () => {
      expect(servicesErrorService).toBeDefined()
      expect(typeof servicesErrorService.handleError).toBe('function')
      expect(typeof servicesErrorService.logError).toBe('function')
      expect(typeof servicesErrorService.handleServiceError).toBe('function')
      expect(typeof servicesErrorService.logServiceWarning).toBe('function')
    })
  })

  describe('handleServiceError', () => {
    it('should handle service error with all parameters', () => {
      const serviceName = 'TestService'
      const error = new Error('Test error message')
      const operation = 'testOperation'
      const metadata = { userId: '123', action: 'create' }

      // Test that the method executes without throwing
      expect(() => {
        servicesErrorService.handleServiceError(serviceName, error, operation, metadata)
      }).not.toThrow()
    })

    it('should handle service error without metadata', () => {
      const serviceName = 'TestService'
      const error = new Error('Test error message')
      const operation = 'testOperation'

      expect(() => {
        servicesErrorService.handleServiceError(serviceName, error, operation)
      }).not.toThrow()
    })

    it('should handle error with custom error code', () => {
      const serviceName = 'TestService'
      const error = new Error('Test error message') as Error & { code?: string }
      error.code = 'CUSTOM_ERROR_CODE'
      const operation = 'testOperation'

      expect(() => {
        servicesErrorService.handleServiceError(serviceName, error, operation)
      }).not.toThrow()
    })

    it('should handle different service names', () => {
      const serviceNames = ['ValidationService', 'StorageService', 'EventBus', 'Logger']
      const error = new Error('Test error')
      const operation = 'testOperation'

      serviceNames.forEach((serviceName) => {
        expect(() => {
          servicesErrorService.handleServiceError(serviceName, error, operation)
        }).not.toThrow()
      })
    })
  })

  describe('logServiceWarning', () => {
    it('should log service warning with all parameters', () => {
      const serviceName = 'TestService'
      const message = 'Test warning message'
      const operation = 'testOperation'
      const metadata = { userId: '123', action: 'update' }

      servicesErrorService.logServiceWarning(serviceName, message, operation, metadata)

      expect(mockLogger.warn).toHaveBeenCalledWith(
        `Service Log [${ErrorType.Warning}][${ErrorSeverity.Medium}]: ${message}`,
        expect.objectContaining({
          component: 'Service:TestService',
          operation: 'testOperation',
          metadata: { userId: '123', action: 'update' },
        }),
      )
    })

    it('should log service warning without metadata', () => {
      const serviceName = 'TestService'
      const message = 'Test warning message'
      const operation = 'testOperation'

      servicesErrorService.logServiceWarning(serviceName, message, operation)

      expect(mockLogger.warn).toHaveBeenCalledWith(
        `Service Log [${ErrorType.Warning}][${ErrorSeverity.Medium}]: ${message}`,
        expect.objectContaining({
          component: 'Service:TestService',
          operation: 'testOperation',
          metadata: undefined,
        }),
      )
    })

    it('should handle different service names for warnings', () => {
      const serviceNames = ['ValidationService', 'StorageService', 'EventBus']
      const message = 'Warning message'
      const operation = 'testOperation'

      serviceNames.forEach((serviceName) => {
        vi.clearAllMocks()
        servicesErrorService.logServiceWarning(serviceName, message, operation)

        expect(mockLogger.warn).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            component: `Service:${serviceName}`,
          }),
        )
      })
    })

    it('should handle empty and special characters in messages', () => {
      const serviceName = 'TestService'
      const operation = 'testOperation'
      const testMessages = [
        '',
        'Message with special chars: @#$%^&*()',
        'Message with unicode: 测试消息 🚨',
      ]

      testMessages.forEach((message) => {
        vi.clearAllMocks()
        servicesErrorService.logServiceWarning(serviceName, message, operation)

        expect(mockLogger.warn).toHaveBeenCalledWith(
          `Service Log [${ErrorType.Warning}][${ErrorSeverity.Medium}]: ${message}`,
          expect.any(Object),
        )
      })
    })
  })

  describe('integration scenarios', () => {
    it('should handle complete error flow from service to logging', () => {
      const serviceName = 'ValidationService'
      const error = new Error('Validation failed')
      const operation = 'validateElement'
      const metadata = { elementId: 'rect-1', elementType: 'RECTANGLE' }

      expect(() => {
        servicesErrorService.handleServiceError(serviceName, error, operation, metadata)
      }).not.toThrow()
    })

    it('should handle mixed error and warning scenarios', () => {
      const serviceName = 'StorageService'

      // First log a warning
      servicesErrorService.logServiceWarning(serviceName, 'Storage quota warning', 'save', { usage: '90%' })
      expect(mockLogger.warn).toHaveBeenCalled()

      // Then handle an error
      const error = new Error('Storage failed')
      expect(() => {
        servicesErrorService.handleServiceError(serviceName, error, 'save', { usage: '100%' })
      }).not.toThrow()
    })

    it('should maintain service context across multiple operations', () => {
      const serviceName = 'EventBus'
      const operations = ['subscribe', 'publish', 'unsubscribe']

      operations.forEach((operation) => {
        const error = new Error(`${operation} failed`)
        expect(() => {
          servicesErrorService.handleServiceError(serviceName, error, operation)
        }).not.toThrow()
      })
    })

    it('should handle concurrent error reporting', () => {
      const services = ['Service1', 'Service2', 'Service3']

      // Simulate concurrent errors from different services
      services.forEach((serviceName) => {
        const error = new Error(`Error from ${serviceName}`)
        expect(() => {
          servicesErrorService.handleServiceError(serviceName, error, 'operation')
        }).not.toThrow()
      })
    })

    it('should preserve error details through inheritance', () => {
      const serviceName = 'TestService'
      const originalError = new Error('Original error')
      originalError.stack = 'Original stack trace'
      const operation = 'testOperation'
      const metadata = { preserveThis: true }

      expect(() => {
        servicesErrorService.handleServiceError(serviceName, originalError, operation, metadata)
      }).not.toThrow()
    })
  })
})
