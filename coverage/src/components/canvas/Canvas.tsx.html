
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/canvas/Canvas.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">src/components/canvas</a> Canvas.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/797</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/797</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a>
<a name='L618'></a><a href='#L618'>618</a>
<a name='L619'></a><a href='#L619'>619</a>
<a name='L620'></a><a href='#L620'>620</a>
<a name='L621'></a><a href='#L621'>621</a>
<a name='L622'></a><a href='#L622'>622</a>
<a name='L623'></a><a href='#L623'>623</a>
<a name='L624'></a><a href='#L624'>624</a>
<a name='L625'></a><a href='#L625'>625</a>
<a name='L626'></a><a href='#L626'>626</a>
<a name='L627'></a><a href='#L627'>627</a>
<a name='L628'></a><a href='#L628'>628</a>
<a name='L629'></a><a href='#L629'>629</a>
<a name='L630'></a><a href='#L630'>630</a>
<a name='L631'></a><a href='#L631'>631</a>
<a name='L632'></a><a href='#L632'>632</a>
<a name='L633'></a><a href='#L633'>633</a>
<a name='L634'></a><a href='#L634'>634</a>
<a name='L635'></a><a href='#L635'>635</a>
<a name='L636'></a><a href='#L636'>636</a>
<a name='L637'></a><a href='#L637'>637</a>
<a name='L638'></a><a href='#L638'>638</a>
<a name='L639'></a><a href='#L639'>639</a>
<a name='L640'></a><a href='#L640'>640</a>
<a name='L641'></a><a href='#L641'>641</a>
<a name='L642'></a><a href='#L642'>642</a>
<a name='L643'></a><a href='#L643'>643</a>
<a name='L644'></a><a href='#L644'>644</a>
<a name='L645'></a><a href='#L645'>645</a>
<a name='L646'></a><a href='#L646'>646</a>
<a name='L647'></a><a href='#L647'>647</a>
<a name='L648'></a><a href='#L648'>648</a>
<a name='L649'></a><a href='#L649'>649</a>
<a name='L650'></a><a href='#L650'>650</a>
<a name='L651'></a><a href='#L651'>651</a>
<a name='L652'></a><a href='#L652'>652</a>
<a name='L653'></a><a href='#L653'>653</a>
<a name='L654'></a><a href='#L654'>654</a>
<a name='L655'></a><a href='#L655'>655</a>
<a name='L656'></a><a href='#L656'>656</a>
<a name='L657'></a><a href='#L657'>657</a>
<a name='L658'></a><a href='#L658'>658</a>
<a name='L659'></a><a href='#L659'>659</a>
<a name='L660'></a><a href='#L660'>660</a>
<a name='L661'></a><a href='#L661'>661</a>
<a name='L662'></a><a href='#L662'>662</a>
<a name='L663'></a><a href='#L663'>663</a>
<a name='L664'></a><a href='#L664'>664</a>
<a name='L665'></a><a href='#L665'>665</a>
<a name='L666'></a><a href='#L666'>666</a>
<a name='L667'></a><a href='#L667'>667</a>
<a name='L668'></a><a href='#L668'>668</a>
<a name='L669'></a><a href='#L669'>669</a>
<a name='L670'></a><a href='#L670'>670</a>
<a name='L671'></a><a href='#L671'>671</a>
<a name='L672'></a><a href='#L672'>672</a>
<a name='L673'></a><a href='#L673'>673</a>
<a name='L674'></a><a href='#L674'>674</a>
<a name='L675'></a><a href='#L675'>675</a>
<a name='L676'></a><a href='#L676'>676</a>
<a name='L677'></a><a href='#L677'>677</a>
<a name='L678'></a><a href='#L678'>678</a>
<a name='L679'></a><a href='#L679'>679</a>
<a name='L680'></a><a href='#L680'>680</a>
<a name='L681'></a><a href='#L681'>681</a>
<a name='L682'></a><a href='#L682'>682</a>
<a name='L683'></a><a href='#L683'>683</a>
<a name='L684'></a><a href='#L684'>684</a>
<a name='L685'></a><a href='#L685'>685</a>
<a name='L686'></a><a href='#L686'>686</a>
<a name='L687'></a><a href='#L687'>687</a>
<a name='L688'></a><a href='#L688'>688</a>
<a name='L689'></a><a href='#L689'>689</a>
<a name='L690'></a><a href='#L690'>690</a>
<a name='L691'></a><a href='#L691'>691</a>
<a name='L692'></a><a href='#L692'>692</a>
<a name='L693'></a><a href='#L693'>693</a>
<a name='L694'></a><a href='#L694'>694</a>
<a name='L695'></a><a href='#L695'>695</a>
<a name='L696'></a><a href='#L696'>696</a>
<a name='L697'></a><a href='#L697'>697</a>
<a name='L698'></a><a href='#L698'>698</a>
<a name='L699'></a><a href='#L699'>699</a>
<a name='L700'></a><a href='#L700'>700</a>
<a name='L701'></a><a href='#L701'>701</a>
<a name='L702'></a><a href='#L702'>702</a>
<a name='L703'></a><a href='#L703'>703</a>
<a name='L704'></a><a href='#L704'>704</a>
<a name='L705'></a><a href='#L705'>705</a>
<a name='L706'></a><a href='#L706'>706</a>
<a name='L707'></a><a href='#L707'>707</a>
<a name='L708'></a><a href='#L708'>708</a>
<a name='L709'></a><a href='#L709'>709</a>
<a name='L710'></a><a href='#L710'>710</a>
<a name='L711'></a><a href='#L711'>711</a>
<a name='L712'></a><a href='#L712'>712</a>
<a name='L713'></a><a href='#L713'>713</a>
<a name='L714'></a><a href='#L714'>714</a>
<a name='L715'></a><a href='#L715'>715</a>
<a name='L716'></a><a href='#L716'>716</a>
<a name='L717'></a><a href='#L717'>717</a>
<a name='L718'></a><a href='#L718'>718</a>
<a name='L719'></a><a href='#L719'>719</a>
<a name='L720'></a><a href='#L720'>720</a>
<a name='L721'></a><a href='#L721'>721</a>
<a name='L722'></a><a href='#L722'>722</a>
<a name='L723'></a><a href='#L723'>723</a>
<a name='L724'></a><a href='#L724'>724</a>
<a name='L725'></a><a href='#L725'>725</a>
<a name='L726'></a><a href='#L726'>726</a>
<a name='L727'></a><a href='#L727'>727</a>
<a name='L728'></a><a href='#L728'>728</a>
<a name='L729'></a><a href='#L729'>729</a>
<a name='L730'></a><a href='#L730'>730</a>
<a name='L731'></a><a href='#L731'>731</a>
<a name='L732'></a><a href='#L732'>732</a>
<a name='L733'></a><a href='#L733'>733</a>
<a name='L734'></a><a href='#L734'>734</a>
<a name='L735'></a><a href='#L735'>735</a>
<a name='L736'></a><a href='#L736'>736</a>
<a name='L737'></a><a href='#L737'>737</a>
<a name='L738'></a><a href='#L738'>738</a>
<a name='L739'></a><a href='#L739'>739</a>
<a name='L740'></a><a href='#L740'>740</a>
<a name='L741'></a><a href='#L741'>741</a>
<a name='L742'></a><a href='#L742'>742</a>
<a name='L743'></a><a href='#L743'>743</a>
<a name='L744'></a><a href='#L744'>744</a>
<a name='L745'></a><a href='#L745'>745</a>
<a name='L746'></a><a href='#L746'>746</a>
<a name='L747'></a><a href='#L747'>747</a>
<a name='L748'></a><a href='#L748'>748</a>
<a name='L749'></a><a href='#L749'>749</a>
<a name='L750'></a><a href='#L750'>750</a>
<a name='L751'></a><a href='#L751'>751</a>
<a name='L752'></a><a href='#L752'>752</a>
<a name='L753'></a><a href='#L753'>753</a>
<a name='L754'></a><a href='#L754'>754</a>
<a name='L755'></a><a href='#L755'>755</a>
<a name='L756'></a><a href='#L756'>756</a>
<a name='L757'></a><a href='#L757'>757</a>
<a name='L758'></a><a href='#L758'>758</a>
<a name='L759'></a><a href='#L759'>759</a>
<a name='L760'></a><a href='#L760'>760</a>
<a name='L761'></a><a href='#L761'>761</a>
<a name='L762'></a><a href='#L762'>762</a>
<a name='L763'></a><a href='#L763'>763</a>
<a name='L764'></a><a href='#L764'>764</a>
<a name='L765'></a><a href='#L765'>765</a>
<a name='L766'></a><a href='#L766'>766</a>
<a name='L767'></a><a href='#L767'>767</a>
<a name='L768'></a><a href='#L768'>768</a>
<a name='L769'></a><a href='#L769'>769</a>
<a name='L770'></a><a href='#L770'>770</a>
<a name='L771'></a><a href='#L771'>771</a>
<a name='L772'></a><a href='#L772'>772</a>
<a name='L773'></a><a href='#L773'>773</a>
<a name='L774'></a><a href='#L774'>774</a>
<a name='L775'></a><a href='#L775'>775</a>
<a name='L776'></a><a href='#L776'>776</a>
<a name='L777'></a><a href='#L777'>777</a>
<a name='L778'></a><a href='#L778'>778</a>
<a name='L779'></a><a href='#L779'>779</a>
<a name='L780'></a><a href='#L780'>780</a>
<a name='L781'></a><a href='#L781'>781</a>
<a name='L782'></a><a href='#L782'>782</a>
<a name='L783'></a><a href='#L783'>783</a>
<a name='L784'></a><a href='#L784'>784</a>
<a name='L785'></a><a href='#L785'>785</a>
<a name='L786'></a><a href='#L786'>786</a>
<a name='L787'></a><a href='#L787'>787</a>
<a name='L788'></a><a href='#L788'>788</a>
<a name='L789'></a><a href='#L789'>789</a>
<a name='L790'></a><a href='#L790'>790</a>
<a name='L791'></a><a href='#L791'>791</a>
<a name='L792'></a><a href='#L792'>792</a>
<a name='L793'></a><a href='#L793'>793</a>
<a name='L794'></a><a href='#L794'>794</a>
<a name='L795'></a><a href='#L795'>795</a>
<a name='L796'></a><a href='#L796'>796</a>
<a name='L797'></a><a href='#L797'>797</a>
<a name='L798'></a><a href='#L798'>798</a>
<a name='L799'></a><a href='#L799'>799</a>
<a name='L800'></a><a href='#L800'>800</a>
<a name='L801'></a><a href='#L801'>801</a>
<a name='L802'></a><a href='#L802'>802</a>
<a name='L803'></a><a href='#L803'>803</a>
<a name='L804'></a><a href='#L804'>804</a>
<a name='L805'></a><a href='#L805'>805</a>
<a name='L806'></a><a href='#L806'>806</a>
<a name='L807'></a><a href='#L807'>807</a>
<a name='L808'></a><a href='#L808'>808</a>
<a name='L809'></a><a href='#L809'>809</a>
<a name='L810'></a><a href='#L810'>810</a>
<a name='L811'></a><a href='#L811'>811</a>
<a name='L812'></a><a href='#L812'>812</a>
<a name='L813'></a><a href='#L813'>813</a>
<a name='L814'></a><a href='#L814'>814</a>
<a name='L815'></a><a href='#L815'>815</a>
<a name='L816'></a><a href='#L816'>816</a>
<a name='L817'></a><a href='#L817'>817</a>
<a name='L818'></a><a href='#L818'>818</a>
<a name='L819'></a><a href='#L819'>819</a>
<a name='L820'></a><a href='#L820'>820</a>
<a name='L821'></a><a href='#L821'>821</a>
<a name='L822'></a><a href='#L822'>822</a>
<a name='L823'></a><a href='#L823'>823</a>
<a name='L824'></a><a href='#L824'>824</a>
<a name='L825'></a><a href='#L825'>825</a>
<a name='L826'></a><a href='#L826'>826</a>
<a name='L827'></a><a href='#L827'>827</a>
<a name='L828'></a><a href='#L828'>828</a>
<a name='L829'></a><a href='#L829'>829</a>
<a name='L830'></a><a href='#L830'>830</a>
<a name='L831'></a><a href='#L831'>831</a>
<a name='L832'></a><a href='#L832'>832</a>
<a name='L833'></a><a href='#L833'>833</a>
<a name='L834'></a><a href='#L834'>834</a>
<a name='L835'></a><a href='#L835'>835</a>
<a name='L836'></a><a href='#L836'>836</a>
<a name='L837'></a><a href='#L837'>837</a>
<a name='L838'></a><a href='#L838'>838</a>
<a name='L839'></a><a href='#L839'>839</a>
<a name='L840'></a><a href='#L840'>840</a>
<a name='L841'></a><a href='#L841'>841</a>
<a name='L842'></a><a href='#L842'>842</a>
<a name='L843'></a><a href='#L843'>843</a>
<a name='L844'></a><a href='#L844'>844</a>
<a name='L845'></a><a href='#L845'>845</a>
<a name='L846'></a><a href='#L846'>846</a>
<a name='L847'></a><a href='#L847'>847</a>
<a name='L848'></a><a href='#L848'>848</a>
<a name='L849'></a><a href='#L849'>849</a>
<a name='L850'></a><a href='#L850'>850</a>
<a name='L851'></a><a href='#L851'>851</a>
<a name='L852'></a><a href='#L852'>852</a>
<a name='L853'></a><a href='#L853'>853</a>
<a name='L854'></a><a href='#L854'>854</a>
<a name='L855'></a><a href='#L855'>855</a>
<a name='L856'></a><a href='#L856'>856</a>
<a name='L857'></a><a href='#L857'>857</a>
<a name='L858'></a><a href='#L858'>858</a>
<a name='L859'></a><a href='#L859'>859</a>
<a name='L860'></a><a href='#L860'>860</a>
<a name='L861'></a><a href='#L861'>861</a>
<a name='L862'></a><a href='#L862'>862</a>
<a name='L863'></a><a href='#L863'>863</a>
<a name='L864'></a><a href='#L864'>864</a>
<a name='L865'></a><a href='#L865'>865</a>
<a name='L866'></a><a href='#L866'>866</a>
<a name='L867'></a><a href='#L867'>867</a>
<a name='L868'></a><a href='#L868'>868</a>
<a name='L869'></a><a href='#L869'>869</a>
<a name='L870'></a><a href='#L870'>870</a>
<a name='L871'></a><a href='#L871'>871</a>
<a name='L872'></a><a href='#L872'>872</a>
<a name='L873'></a><a href='#L873'>873</a>
<a name='L874'></a><a href='#L874'>874</a>
<a name='L875'></a><a href='#L875'>875</a>
<a name='L876'></a><a href='#L876'>876</a>
<a name='L877'></a><a href='#L877'>877</a>
<a name='L878'></a><a href='#L878'>878</a>
<a name='L879'></a><a href='#L879'>879</a>
<a name='L880'></a><a href='#L880'>880</a>
<a name='L881'></a><a href='#L881'>881</a>
<a name='L882'></a><a href='#L882'>882</a>
<a name='L883'></a><a href='#L883'>883</a>
<a name='L884'></a><a href='#L884'>884</a>
<a name='L885'></a><a href='#L885'>885</a>
<a name='L886'></a><a href='#L886'>886</a>
<a name='L887'></a><a href='#L887'>887</a>
<a name='L888'></a><a href='#L888'>888</a>
<a name='L889'></a><a href='#L889'>889</a>
<a name='L890'></a><a href='#L890'>890</a>
<a name='L891'></a><a href='#L891'>891</a>
<a name='L892'></a><a href='#L892'>892</a>
<a name='L893'></a><a href='#L893'>893</a>
<a name='L894'></a><a href='#L894'>894</a>
<a name='L895'></a><a href='#L895'>895</a>
<a name='L896'></a><a href='#L896'>896</a>
<a name='L897'></a><a href='#L897'>897</a>
<a name='L898'></a><a href='#L898'>898</a>
<a name='L899'></a><a href='#L899'>899</a>
<a name='L900'></a><a href='#L900'>900</a>
<a name='L901'></a><a href='#L901'>901</a>
<a name='L902'></a><a href='#L902'>902</a>
<a name='L903'></a><a href='#L903'>903</a>
<a name='L904'></a><a href='#L904'>904</a>
<a name='L905'></a><a href='#L905'>905</a>
<a name='L906'></a><a href='#L906'>906</a>
<a name='L907'></a><a href='#L907'>907</a>
<a name='L908'></a><a href='#L908'>908</a>
<a name='L909'></a><a href='#L909'>909</a>
<a name='L910'></a><a href='#L910'>910</a>
<a name='L911'></a><a href='#L911'>911</a>
<a name='L912'></a><a href='#L912'>912</a>
<a name='L913'></a><a href='#L913'>913</a>
<a name='L914'></a><a href='#L914'>914</a>
<a name='L915'></a><a href='#L915'>915</a>
<a name='L916'></a><a href='#L916'>916</a>
<a name='L917'></a><a href='#L917'>917</a>
<a name='L918'></a><a href='#L918'>918</a>
<a name='L919'></a><a href='#L919'>919</a>
<a name='L920'></a><a href='#L920'>920</a>
<a name='L921'></a><a href='#L921'>921</a>
<a name='L922'></a><a href='#L922'>922</a>
<a name='L923'></a><a href='#L923'>923</a>
<a name='L924'></a><a href='#L924'>924</a>
<a name='L925'></a><a href='#L925'>925</a>
<a name='L926'></a><a href='#L926'>926</a>
<a name='L927'></a><a href='#L927'>927</a>
<a name='L928'></a><a href='#L928'>928</a>
<a name='L929'></a><a href='#L929'>929</a>
<a name='L930'></a><a href='#L930'>930</a>
<a name='L931'></a><a href='#L931'>931</a>
<a name='L932'></a><a href='#L932'>932</a>
<a name='L933'></a><a href='#L933'>933</a>
<a name='L934'></a><a href='#L934'>934</a>
<a name='L935'></a><a href='#L935'>935</a>
<a name='L936'></a><a href='#L936'>936</a>
<a name='L937'></a><a href='#L937'>937</a>
<a name='L938'></a><a href='#L938'>938</a>
<a name='L939'></a><a href='#L939'>939</a>
<a name='L940'></a><a href='#L940'>940</a>
<a name='L941'></a><a href='#L941'>941</a>
<a name='L942'></a><a href='#L942'>942</a>
<a name='L943'></a><a href='#L943'>943</a>
<a name='L944'></a><a href='#L944'>944</a>
<a name='L945'></a><a href='#L945'>945</a>
<a name='L946'></a><a href='#L946'>946</a>
<a name='L947'></a><a href='#L947'>947</a>
<a name='L948'></a><a href='#L948'>948</a>
<a name='L949'></a><a href='#L949'>949</a>
<a name='L950'></a><a href='#L950'>950</a>
<a name='L951'></a><a href='#L951'>951</a>
<a name='L952'></a><a href='#L952'>952</a>
<a name='L953'></a><a href='#L953'>953</a>
<a name='L954'></a><a href='#L954'>954</a>
<a name='L955'></a><a href='#L955'>955</a>
<a name='L956'></a><a href='#L956'>956</a>
<a name='L957'></a><a href='#L957'>957</a>
<a name='L958'></a><a href='#L958'>958</a>
<a name='L959'></a><a href='#L959'>959</a>
<a name='L960'></a><a href='#L960'>960</a>
<a name='L961'></a><a href='#L961'>961</a>
<a name='L962'></a><a href='#L962'>962</a>
<a name='L963'></a><a href='#L963'>963</a>
<a name='L964'></a><a href='#L964'>964</a>
<a name='L965'></a><a href='#L965'>965</a>
<a name='L966'></a><a href='#L966'>966</a>
<a name='L967'></a><a href='#L967'>967</a>
<a name='L968'></a><a href='#L968'>968</a>
<a name='L969'></a><a href='#L969'>969</a>
<a name='L970'></a><a href='#L970'>970</a>
<a name='L971'></a><a href='#L971'>971</a>
<a name='L972'></a><a href='#L972'>972</a>
<a name='L973'></a><a href='#L973'>973</a>
<a name='L974'></a><a href='#L974'>974</a>
<a name='L975'></a><a href='#L975'>975</a>
<a name='L976'></a><a href='#L976'>976</a>
<a name='L977'></a><a href='#L977'>977</a>
<a name='L978'></a><a href='#L978'>978</a>
<a name='L979'></a><a href='#L979'>979</a>
<a name='L980'></a><a href='#L980'>980</a>
<a name='L981'></a><a href='#L981'>981</a>
<a name='L982'></a><a href='#L982'>982</a>
<a name='L983'></a><a href='#L983'>983</a>
<a name='L984'></a><a href='#L984'>984</a>
<a name='L985'></a><a href='#L985'>985</a>
<a name='L986'></a><a href='#L986'>986</a>
<a name='L987'></a><a href='#L987'>987</a>
<a name='L988'></a><a href='#L988'>988</a>
<a name='L989'></a><a href='#L989'>989</a>
<a name='L990'></a><a href='#L990'>990</a>
<a name='L991'></a><a href='#L991'>991</a>
<a name='L992'></a><a href='#L992'>992</a>
<a name='L993'></a><a href='#L993'>993</a>
<a name='L994'></a><a href='#L994'>994</a>
<a name='L995'></a><a href='#L995'>995</a>
<a name='L996'></a><a href='#L996'>996</a>
<a name='L997'></a><a href='#L997'>997</a>
<a name='L998'></a><a href='#L998'>998</a>
<a name='L999'></a><a href='#L999'>999</a>
<a name='L1000'></a><a href='#L1000'>1000</a>
<a name='L1001'></a><a href='#L1001'>1001</a>
<a name='L1002'></a><a href='#L1002'>1002</a>
<a name='L1003'></a><a href='#L1003'>1003</a>
<a name='L1004'></a><a href='#L1004'>1004</a>
<a name='L1005'></a><a href='#L1005'>1005</a>
<a name='L1006'></a><a href='#L1006'>1006</a>
<a name='L1007'></a><a href='#L1007'>1007</a>
<a name='L1008'></a><a href='#L1008'>1008</a>
<a name='L1009'></a><a href='#L1009'>1009</a>
<a name='L1010'></a><a href='#L1010'>1010</a>
<a name='L1011'></a><a href='#L1011'>1011</a>
<a name='L1012'></a><a href='#L1012'>1012</a>
<a name='L1013'></a><a href='#L1013'>1013</a>
<a name='L1014'></a><a href='#L1014'>1014</a>
<a name='L1015'></a><a href='#L1015'>1015</a>
<a name='L1016'></a><a href='#L1016'>1016</a>
<a name='L1017'></a><a href='#L1017'>1017</a>
<a name='L1018'></a><a href='#L1018'>1018</a>
<a name='L1019'></a><a href='#L1019'>1019</a>
<a name='L1020'></a><a href='#L1020'>1020</a>
<a name='L1021'></a><a href='#L1021'>1021</a>
<a name='L1022'></a><a href='#L1022'>1022</a>
<a name='L1023'></a><a href='#L1023'>1023</a>
<a name='L1024'></a><a href='#L1024'>1024</a>
<a name='L1025'></a><a href='#L1025'>1025</a>
<a name='L1026'></a><a href='#L1026'>1026</a>
<a name='L1027'></a><a href='#L1027'>1027</a>
<a name='L1028'></a><a href='#L1028'>1028</a>
<a name='L1029'></a><a href='#L1029'>1029</a>
<a name='L1030'></a><a href='#L1030'>1030</a>
<a name='L1031'></a><a href='#L1031'>1031</a>
<a name='L1032'></a><a href='#L1032'>1032</a>
<a name='L1033'></a><a href='#L1033'>1033</a>
<a name='L1034'></a><a href='#L1034'>1034</a>
<a name='L1035'></a><a href='#L1035'>1035</a>
<a name='L1036'></a><a href='#L1036'>1036</a>
<a name='L1037'></a><a href='#L1037'>1037</a>
<a name='L1038'></a><a href='#L1038'>1038</a>
<a name='L1039'></a><a href='#L1039'>1039</a>
<a name='L1040'></a><a href='#L1040'>1040</a>
<a name='L1041'></a><a href='#L1041'>1041</a>
<a name='L1042'></a><a href='#L1042'>1042</a>
<a name='L1043'></a><a href='#L1043'>1043</a>
<a name='L1044'></a><a href='#L1044'>1044</a>
<a name='L1045'></a><a href='#L1045'>1045</a>
<a name='L1046'></a><a href='#L1046'>1046</a>
<a name='L1047'></a><a href='#L1047'>1047</a>
<a name='L1048'></a><a href='#L1048'>1048</a>
<a name='L1049'></a><a href='#L1049'>1049</a>
<a name='L1050'></a><a href='#L1050'>1050</a>
<a name='L1051'></a><a href='#L1051'>1051</a>
<a name='L1052'></a><a href='#L1052'>1052</a>
<a name='L1053'></a><a href='#L1053'>1053</a>
<a name='L1054'></a><a href='#L1054'>1054</a>
<a name='L1055'></a><a href='#L1055'>1055</a>
<a name='L1056'></a><a href='#L1056'>1056</a>
<a name='L1057'></a><a href='#L1057'>1057</a>
<a name='L1058'></a><a href='#L1058'>1058</a>
<a name='L1059'></a><a href='#L1059'>1059</a>
<a name='L1060'></a><a href='#L1060'>1060</a>
<a name='L1061'></a><a href='#L1061'>1061</a>
<a name='L1062'></a><a href='#L1062'>1062</a>
<a name='L1063'></a><a href='#L1063'>1063</a>
<a name='L1064'></a><a href='#L1064'>1064</a>
<a name='L1065'></a><a href='#L1065'>1065</a>
<a name='L1066'></a><a href='#L1066'>1066</a>
<a name='L1067'></a><a href='#L1067'>1067</a>
<a name='L1068'></a><a href='#L1068'>1068</a>
<a name='L1069'></a><a href='#L1069'>1069</a>
<a name='L1070'></a><a href='#L1070'>1070</a>
<a name='L1071'></a><a href='#L1071'>1071</a>
<a name='L1072'></a><a href='#L1072'>1072</a>
<a name='L1073'></a><a href='#L1073'>1073</a>
<a name='L1074'></a><a href='#L1074'>1074</a>
<a name='L1075'></a><a href='#L1075'>1075</a>
<a name='L1076'></a><a href='#L1076'>1076</a>
<a name='L1077'></a><a href='#L1077'>1077</a>
<a name='L1078'></a><a href='#L1078'>1078</a>
<a name='L1079'></a><a href='#L1079'>1079</a>
<a name='L1080'></a><a href='#L1080'>1080</a>
<a name='L1081'></a><a href='#L1081'>1081</a>
<a name='L1082'></a><a href='#L1082'>1082</a>
<a name='L1083'></a><a href='#L1083'>1083</a>
<a name='L1084'></a><a href='#L1084'>1084</a>
<a name='L1085'></a><a href='#L1085'>1085</a>
<a name='L1086'></a><a href='#L1086'>1086</a>
<a name='L1087'></a><a href='#L1087'>1087</a>
<a name='L1088'></a><a href='#L1088'>1088</a>
<a name='L1089'></a><a href='#L1089'>1089</a>
<a name='L1090'></a><a href='#L1090'>1090</a>
<a name='L1091'></a><a href='#L1091'>1091</a>
<a name='L1092'></a><a href='#L1092'>1092</a>
<a name='L1093'></a><a href='#L1093'>1093</a>
<a name='L1094'></a><a href='#L1094'>1094</a>
<a name='L1095'></a><a href='#L1095'>1095</a>
<a name='L1096'></a><a href='#L1096'>1096</a>
<a name='L1097'></a><a href='#L1097'>1097</a>
<a name='L1098'></a><a href='#L1098'>1098</a>
<a name='L1099'></a><a href='#L1099'>1099</a>
<a name='L1100'></a><a href='#L1100'>1100</a>
<a name='L1101'></a><a href='#L1101'>1101</a>
<a name='L1102'></a><a href='#L1102'>1102</a>
<a name='L1103'></a><a href='#L1103'>1103</a>
<a name='L1104'></a><a href='#L1104'>1104</a>
<a name='L1105'></a><a href='#L1105'>1105</a>
<a name='L1106'></a><a href='#L1106'>1106</a>
<a name='L1107'></a><a href='#L1107'>1107</a>
<a name='L1108'></a><a href='#L1108'>1108</a>
<a name='L1109'></a><a href='#L1109'>1109</a>
<a name='L1110'></a><a href='#L1110'>1110</a>
<a name='L1111'></a><a href='#L1111'>1111</a>
<a name='L1112'></a><a href='#L1112'>1112</a>
<a name='L1113'></a><a href='#L1113'>1113</a>
<a name='L1114'></a><a href='#L1114'>1114</a>
<a name='L1115'></a><a href='#L1115'>1115</a>
<a name='L1116'></a><a href='#L1116'>1116</a>
<a name='L1117'></a><a href='#L1117'>1117</a>
<a name='L1118'></a><a href='#L1118'>1118</a>
<a name='L1119'></a><a href='#L1119'>1119</a>
<a name='L1120'></a><a href='#L1120'>1120</a>
<a name='L1121'></a><a href='#L1121'>1121</a>
<a name='L1122'></a><a href='#L1122'>1122</a>
<a name='L1123'></a><a href='#L1123'>1123</a>
<a name='L1124'></a><a href='#L1124'>1124</a>
<a name='L1125'></a><a href='#L1125'>1125</a>
<a name='L1126'></a><a href='#L1126'>1126</a>
<a name='L1127'></a><a href='#L1127'>1127</a>
<a name='L1128'></a><a href='#L1128'>1128</a>
<a name='L1129'></a><a href='#L1129'>1129</a>
<a name='L1130'></a><a href='#L1130'>1130</a>
<a name='L1131'></a><a href='#L1131'>1131</a>
<a name='L1132'></a><a href='#L1132'>1132</a>
<a name='L1133'></a><a href='#L1133'>1133</a>
<a name='L1134'></a><a href='#L1134'>1134</a>
<a name='L1135'></a><a href='#L1135'>1135</a>
<a name='L1136'></a><a href='#L1136'>1136</a>
<a name='L1137'></a><a href='#L1137'>1137</a>
<a name='L1138'></a><a href='#L1138'>1138</a>
<a name='L1139'></a><a href='#L1139'>1139</a>
<a name='L1140'></a><a href='#L1140'>1140</a>
<a name='L1141'></a><a href='#L1141'>1141</a>
<a name='L1142'></a><a href='#L1142'>1142</a>
<a name='L1143'></a><a href='#L1143'>1143</a>
<a name='L1144'></a><a href='#L1144'>1144</a>
<a name='L1145'></a><a href='#L1145'>1145</a>
<a name='L1146'></a><a href='#L1146'>1146</a>
<a name='L1147'></a><a href='#L1147'>1147</a>
<a name='L1148'></a><a href='#L1148'>1148</a>
<a name='L1149'></a><a href='#L1149'>1149</a>
<a name='L1150'></a><a href='#L1150'>1150</a>
<a name='L1151'></a><a href='#L1151'>1151</a>
<a name='L1152'></a><a href='#L1152'>1152</a>
<a name='L1153'></a><a href='#L1153'>1153</a>
<a name='L1154'></a><a href='#L1154'>1154</a>
<a name='L1155'></a><a href='#L1155'>1155</a>
<a name='L1156'></a><a href='#L1156'>1156</a>
<a name='L1157'></a><a href='#L1157'>1157</a>
<a name='L1158'></a><a href='#L1158'>1158</a>
<a name='L1159'></a><a href='#L1159'>1159</a>
<a name='L1160'></a><a href='#L1160'>1160</a>
<a name='L1161'></a><a href='#L1161'>1161</a>
<a name='L1162'></a><a href='#L1162'>1162</a>
<a name='L1163'></a><a href='#L1163'>1163</a>
<a name='L1164'></a><a href='#L1164'>1164</a>
<a name='L1165'></a><a href='#L1165'>1165</a>
<a name='L1166'></a><a href='#L1166'>1166</a>
<a name='L1167'></a><a href='#L1167'>1167</a>
<a name='L1168'></a><a href='#L1168'>1168</a>
<a name='L1169'></a><a href='#L1169'>1169</a>
<a name='L1170'></a><a href='#L1170'>1170</a>
<a name='L1171'></a><a href='#L1171'>1171</a>
<a name='L1172'></a><a href='#L1172'>1172</a>
<a name='L1173'></a><a href='#L1173'>1173</a>
<a name='L1174'></a><a href='#L1174'>1174</a>
<a name='L1175'></a><a href='#L1175'>1175</a>
<a name='L1176'></a><a href='#L1176'>1176</a>
<a name='L1177'></a><a href='#L1177'>1177</a>
<a name='L1178'></a><a href='#L1178'>1178</a>
<a name='L1179'></a><a href='#L1179'>1179</a>
<a name='L1180'></a><a href='#L1180'>1180</a>
<a name='L1181'></a><a href='#L1181'>1181</a>
<a name='L1182'></a><a href='#L1182'>1182</a>
<a name='L1183'></a><a href='#L1183'>1183</a>
<a name='L1184'></a><a href='#L1184'>1184</a>
<a name='L1185'></a><a href='#L1185'>1185</a>
<a name='L1186'></a><a href='#L1186'>1186</a>
<a name='L1187'></a><a href='#L1187'>1187</a>
<a name='L1188'></a><a href='#L1188'>1188</a>
<a name='L1189'></a><a href='#L1189'>1189</a>
<a name='L1190'></a><a href='#L1190'>1190</a>
<a name='L1191'></a><a href='#L1191'>1191</a>
<a name='L1192'></a><a href='#L1192'>1192</a>
<a name='L1193'></a><a href='#L1193'>1193</a>
<a name='L1194'></a><a href='#L1194'>1194</a>
<a name='L1195'></a><a href='#L1195'>1195</a>
<a name='L1196'></a><a href='#L1196'>1196</a>
<a name='L1197'></a><a href='#L1197'>1197</a>
<a name='L1198'></a><a href='#L1198'>1198</a>
<a name='L1199'></a><a href='#L1199'>1199</a>
<a name='L1200'></a><a href='#L1200'>1200</a>
<a name='L1201'></a><a href='#L1201'>1201</a>
<a name='L1202'></a><a href='#L1202'>1202</a>
<a name='L1203'></a><a href='#L1203'>1203</a>
<a name='L1204'></a><a href='#L1204'>1204</a>
<a name='L1205'></a><a href='#L1205'>1205</a>
<a name='L1206'></a><a href='#L1206'>1206</a>
<a name='L1207'></a><a href='#L1207'>1207</a>
<a name='L1208'></a><a href='#L1208'>1208</a>
<a name='L1209'></a><a href='#L1209'>1209</a>
<a name='L1210'></a><a href='#L1210'>1210</a>
<a name='L1211'></a><a href='#L1211'>1211</a>
<a name='L1212'></a><a href='#L1212'>1212</a>
<a name='L1213'></a><a href='#L1213'>1213</a>
<a name='L1214'></a><a href='#L1214'>1214</a>
<a name='L1215'></a><a href='#L1215'>1215</a>
<a name='L1216'></a><a href='#L1216'>1216</a>
<a name='L1217'></a><a href='#L1217'>1217</a>
<a name='L1218'></a><a href='#L1218'>1218</a>
<a name='L1219'></a><a href='#L1219'>1219</a>
<a name='L1220'></a><a href='#L1220'>1220</a>
<a name='L1221'></a><a href='#L1221'>1221</a>
<a name='L1222'></a><a href='#L1222'>1222</a>
<a name='L1223'></a><a href='#L1223'>1223</a>
<a name='L1224'></a><a href='#L1224'>1224</a>
<a name='L1225'></a><a href='#L1225'>1225</a>
<a name='L1226'></a><a href='#L1226'>1226</a>
<a name='L1227'></a><a href='#L1227'>1227</a>
<a name='L1228'></a><a href='#L1228'>1228</a>
<a name='L1229'></a><a href='#L1229'>1229</a>
<a name='L1230'></a><a href='#L1230'>1230</a>
<a name='L1231'></a><a href='#L1231'>1231</a>
<a name='L1232'></a><a href='#L1232'>1232</a>
<a name='L1233'></a><a href='#L1233'>1233</a>
<a name='L1234'></a><a href='#L1234'>1234</a>
<a name='L1235'></a><a href='#L1235'>1235</a>
<a name='L1236'></a><a href='#L1236'>1236</a>
<a name='L1237'></a><a href='#L1237'>1237</a>
<a name='L1238'></a><a href='#L1238'>1238</a>
<a name='L1239'></a><a href='#L1239'>1239</a>
<a name='L1240'></a><a href='#L1240'>1240</a>
<a name='L1241'></a><a href='#L1241'>1241</a>
<a name='L1242'></a><a href='#L1242'>1242</a>
<a name='L1243'></a><a href='#L1243'>1243</a>
<a name='L1244'></a><a href='#L1244'>1244</a>
<a name='L1245'></a><a href='#L1245'>1245</a>
<a name='L1246'></a><a href='#L1246'>1246</a>
<a name='L1247'></a><a href='#L1247'>1247</a>
<a name='L1248'></a><a href='#L1248'>1248</a>
<a name='L1249'></a><a href='#L1249'>1249</a>
<a name='L1250'></a><a href='#L1250'>1250</a>
<a name='L1251'></a><a href='#L1251'>1251</a>
<a name='L1252'></a><a href='#L1252'>1252</a>
<a name='L1253'></a><a href='#L1253'>1253</a>
<a name='L1254'></a><a href='#L1254'>1254</a>
<a name='L1255'></a><a href='#L1255'>1255</a>
<a name='L1256'></a><a href='#L1256'>1256</a>
<a name='L1257'></a><a href='#L1257'>1257</a>
<a name='L1258'></a><a href='#L1258'>1258</a>
<a name='L1259'></a><a href='#L1259'>1259</a>
<a name='L1260'></a><a href='#L1260'>1260</a>
<a name='L1261'></a><a href='#L1261'>1261</a>
<a name='L1262'></a><a href='#L1262'>1262</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * Canvas Component
 *
 * The primary drawing surface and interaction area for the design application.
 * This component serves as the central workspace where users can view, create,
 * and manipulate design elements with comprehensive interaction capabilities.
 *
 * Features:
 * - Element rendering with layer-based z-ordering
 * - Interactive zoom and pan controls
 * - Grid display with responsive scaling
 * - Multi-selection with marquee selection
 * - Drag-and-drop asset placement
 * - Path drawing mode support
 * - Real-time coordinate transformation
 * - Responsive canvas sizing
 * - Scale bar with unit conversion
 * - Context-aware interaction modes
 *
 * Interaction Modes:
 * - Pan Mode: Navigate the canvas by dragging
 * - Draw Mode: Create paths and shapes interactively
 * - Select Mode: Select and manipulate existing elements
 *
 * Coordinate Systems:
 * - Screen coordinates: Browser viewport pixels
 * - SVG coordinates: SVG element coordinate space
 * - World coordinates: Design space with physical units
 *
 * @example
 * ```tsx
 * &lt;Canvas
 *   elements={designElements}
 *   selectedElementIds={selectedIds}
 *   showGrid={true}
 *   zoom={1.0}
 *   pan={{ x: 0, y: 0 }}
 *   onElementSelect={handleSelection}
 *   onElementAdd={handleAddElement}
 *   onMouseDown={handleMouseDown}
 * /&gt;
 * ```
 */
&nbsp;
// Internal Type Imports
import type {
  CanvasDimensionChangeCallback,
  CanvasMouseEvent,
  PointData as Point,
} from '@/types'
import type { ElementType, ShapeElement } from '@/types/core/elementDefinitions'
// import type { ZLevel } from '@/types/core/layerPanelTypes' // Not directly used after changes
&nbsp;
// External Libraries
<span class="cstat-no" title="statement not covered" >import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'</span>
&nbsp;
// Hooks &amp; Utilities &amp; Core
<span class="cstat-no" title="statement not covered" >import { useCoordinateSystem } from '@/hooks/canvas/useCoordinateSystem'</span>
<span class="cstat-no" title="statement not covered" >import { calculateScaleBarInfo } from '@/lib/utils/canvas/scaleBarUtils'</span>
<span class="cstat-no" title="statement not covered" >import {</span>
  extractKeyboardModifiers,
  handleMarqueeSelection,
  shouldClearSelectionOnBackgroundClick,
} from '@/lib/utils/canvas/selectionUtils'
<span class="cstat-no" title="statement not covered" >import { ShapeElementUtils } from '@/lib/utils/element/shapeElementUtils'</span>
<span class="cstat-no" title="statement not covered" >import { BoundingBoxClass } from '@/lib/utils/geometry/BoundingBoxClass'</span>
&nbsp;
// Store Imports
<span class="cstat-no" title="statement not covered" >import { useLayerStore } from '@/store/layerStore'</span>
&nbsp;
// Internal Components
<span class="cstat-no" title="statement not covered" >import CanvasScaleBar from './CanvasScaleBar'</span>
<span class="cstat-no" title="statement not covered" >import PathPreview from './PathPreview'</span>
<span class="cstat-no" title="statement not covered" >import { ShapeRenderer } from './ShapeRenderer'</span>
&nbsp;
/** Default pan position for canvas initialization */
<span class="cstat-no" title="statement not covered" >const INITIAL_PAN: Point = { x: 0, y: 0 }</span>
/** Default zoom level for canvas initialization */
<span class="cstat-no" title="statement not covered" >const INITIAL_ZOOM = 1.0</span>
&nbsp;
/**
 * Calculates the bounding box for marquee selection.
 *
 * This function computes the correct rectangle coordinates for a selection box
 * based on the drag start point and current mouse position, ensuring the box
 * is always drawn with positive width and height regardless of drag direction.
 *
 * @param dragStartPoint - The point where the drag operation started
 * @param currentMousePoint - The current mouse position
 * @returns Rectangle coordinates with x, y, width, and height
 */
<span class="cstat-no" title="statement not covered" >function calculateMarqueeBox(</span>
<span class="cstat-no" title="statement not covered" >  dragStartPoint: Point,</span>
<span class="cstat-no" title="statement not covered" >  currentMousePoint: Point,</span>
<span class="cstat-no" title="statement not covered" >): { x: number, y: number, width: number, height: number } {</span>
<span class="cstat-no" title="statement not covered" >  const x = Math.min(dragStartPoint.x, currentMousePoint.x)</span>
<span class="cstat-no" title="statement not covered" >  const y = Math.min(dragStartPoint.y, currentMousePoint.y)</span>
<span class="cstat-no" title="statement not covered" >  const width = Math.abs(dragStartPoint.x - currentMousePoint.x)</span>
<span class="cstat-no" title="statement not covered" >  const height = Math.abs(dragStartPoint.y - currentMousePoint.y)</span>
<span class="cstat-no" title="statement not covered" >  return { x, y, width, height }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Props interface for the Canvas component
 */
export interface CanvasProps {
  /** Array of design elements to render on the canvas */
  elements: ShapeElement[]
  /** IDs of currently selected elements */
  selectedElementIds: string[]
  /** Whether the canvas is in pan mode for navigation */
  isPanMode?: boolean
  /** Whether the canvas is in drawing mode for creating paths */
  isDrawMode?: boolean
  /** Whether to display the grid overlay */
  showGrid?: boolean
  /** Size of grid cells in world units */
  gridSize?: number
  /** Current zoom level (1.0 = 100%) */
  zoom?: number
  /** Current pan offset in screen coordinates */
  pan?: Point
  /** Asset type primed for single-click placement */
  primedAssetTypeForCanvasClick?: ElementType | null
  /** Current state of path drawing operation */
  pathDrawState?: {
    /** Whether a path is currently being drawn */
    isDrawing: boolean
    /** Type of path being drawn */
    pathType: ElementType | null
    /** Starting point of the path */
    startPoint: Point | null
    /** Current cursor position */
    currentPoint: Point | null
    /** All points for multi-point paths */
    allPoints?: Point[]
    /** Number of clicks completed */
    clickCount?: number
  }
  /** Callback for mouse down events */
  onMouseDown?: (event: CanvasMouseEvent) =&gt; void
  /** Callback for mouse move events */
  onMouseMove?: (event: CanvasMouseEvent) =&gt; void
  /** Callback for mouse up events */
  onMouseUp?: (event: CanvasMouseEvent) =&gt; void
  /** Callback for drag over events */
  onDragOver?: (event: React.DragEvent&lt;Element&gt;) =&gt; void
  /** Callback for drop events */
  onDrop?: (event: CanvasMouseEvent) =&gt; void
  /** Callback for adding new elements */
  onElementAdd?: (
    asset: { elementType: string, properties?: Record&lt;string, unknown&gt;, id?: string },
    position: Point
  ) =&gt; void
  /** Callback for element selection changes */
  onElementSelect?: (
    elementIdOrIds: string | string[] | null,
    isMultiSelect?: boolean,
    selectionMode?: 'replace' | 'toggle' | 'clear' | 'add'
  ) =&gt; void
  /** Callback for world coordinate mouse movement */
  onWorldMouseMove?: (worldPoint: Point) =&gt; void
  /** Callback for canvas dimension changes */
  onDimensionsChange?: CanvasDimensionChangeCallback
  /** Whether the property sidebar is open */
  isPropertySidebarOpen?: boolean
  /** Current selection box coordinates */
  selectionBox?: { x: number, y: number, width: number, height: number } | null
  /** Callback for zoom level changes */
  onZoomChange?: (zoom: number) =&gt; void
  /** Callback for pan offset changes */
  onPanChange?: (pan: Point) =&gt; void
  /** Callback for wheel events (zoom/scroll) */
  onWheel?: (event: React.WheelEvent&lt;SVGSVGElement&gt;) =&gt; void
  /** Callback to start pan operation */
  doStartPan?: (event: React.MouseEvent&lt;SVGElement&gt;) =&gt; void
  /** Callback to continue pan operation */
  doContinuePan?: (event: React.MouseEvent&lt;SVGElement&gt;) =&gt; void
  /** Callback to end pan operation */
  doEndPan?: () =&gt; void
  /** Callback for shape drop events */
  onShapeDrop?: (
    event: React.DragEvent&lt;SVGSVGElement&gt;,
    droppedAssetData: { elementType: string, properties?: Record&lt;string, unknown&gt;, name?: string, id?: string } | null
  ) =&gt; void
  /** Whether the bottom asset drawer sheet is open */
  sheetOpen?: boolean
}
&nbsp;
/** Empty function used as default callback to avoid null checks */
<span class="cstat-no" title="statement not covered" >function EMPTY_FUNCTION() {}</span>
&nbsp;
// Type definitions for environment checking
interface ImportMetaWithEnv {
  env?: {
    DEV?: boolean
    NODE_ENV?: string
  }
}
&nbsp;
interface WindowWithDev {
  __DEV__?: boolean
}
&nbsp;
// Debug utility function to replace console.log in production
<span class="cstat-no" title="statement not covered" >function debug(message: string, ...args: unknown[]) {</span>
  // Use environment variable check without require
<span class="cstat-no" title="statement not covered" >  try {</span>
    // Check if we're in development mode using various methods
    // Use import.meta.env for Vite environments, fallback to NODE_ENV check
<span class="cstat-no" title="statement not covered" >    const hasImportMeta = typeof import.meta !== 'undefined'</span>
<span class="cstat-no" title="statement not covered" >    const importMetaWithEnv = hasImportMeta ? (import.meta as ImportMetaWithEnv) : null</span>
<span class="cstat-no" title="statement not covered" >    const hasImportMetaEnv = importMetaWithEnv?.env !== undefined</span>
<span class="cstat-no" title="statement not covered" >    const isViteDev = hasImportMetaEnv &amp;&amp; importMetaWithEnv?.env?.DEV === true</span>
&nbsp;
    // Skip process-based environment detection to avoid ESLint node/prefer-global/process rule
    // In browser environments, we rely on import.meta.env and window.__DEV__ instead
<span class="cstat-no" title="statement not covered" >    const isNodeDev = false</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const hasWindow = typeof window !== 'undefined'</span>
<span class="cstat-no" title="statement not covered" >    const windowWithDev = hasWindow ? (window as WindowWithDev) : null</span>
<span class="cstat-no" title="statement not covered" >    const hasWindowDev = windowWithDev?.__DEV__ !== undefined &amp;&amp; typeof windowWithDev.__DEV__ === 'boolean'</span>
<span class="cstat-no" title="statement not covered" >    const isWindowDev = hasWindowDev &amp;&amp; windowWithDev?.__DEV__ === true</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const isDevelopment = isViteDev || isNodeDev || isWindowDev</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (isDevelopment) {</span>
<span class="cstat-no" title="statement not covered" >      console.warn(`[Canvas Debug] ${message}`, ...args)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  catch {</span>
    // Fallback for environments where process is not available
    // Only log in development mode
<span class="cstat-no" title="statement not covered" >    const hasWindow = typeof window !== 'undefined'</span>
<span class="cstat-no" title="statement not covered" >    const windowWithDev = hasWindow ? (window as WindowWithDev) : null</span>
<span class="cstat-no" title="statement not covered" >    const hasWindowDev = windowWithDev?.__DEV__ !== undefined &amp;&amp; typeof windowWithDev.__DEV__ === 'boolean'</span>
<span class="cstat-no" title="statement not covered" >    const isWindowDev = hasWindowDev &amp;&amp; windowWithDev?.__DEV__ === true</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (isWindowDev) {</span>
<span class="cstat-no" title="statement not covered" >      console.warn(`[Canvas Debug] ${message}`, ...args)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Formats a dimension value in millimeters to a human-readable string with appropriate units.
 *
 * This function automatically selects the most appropriate unit (mm, cm, or m) based on
 * the magnitude of the value to provide the most readable representation.
 *
 * @param valueInMM - The dimension value in millimeters
 * @returns Formatted string with value and unit (e.g., "150 mm", "1.5 m")
 */
<span class="cstat-no" title="statement not covered" >function formatDimensionForDisplay(valueInMM: number): string {</span>
<span class="cstat-no" title="statement not covered" >  const CM_IN_MM = 10</span>
<span class="cstat-no" title="statement not covered" >  const M_IN_MM = 1000</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (valueInMM &gt;= M_IN_MM) {</span>
<span class="cstat-no" title="statement not covered" >    return `${(valueInMM / M_IN_MM).toFixed(2)} m`</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  if (valueInMM &gt;= CM_IN_MM) {</span>
<span class="cstat-no" title="statement not covered" >    return `${(valueInMM / CM_IN_MM).toFixed(1)} cm`</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  return `${valueInMM.toFixed(0)} mm`</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Conversion factor from pixels to millimeters for the canvas coordinate system.
 *
 * This value determines the scale relationship between screen pixels and real-world
 * measurements. A value of 1.0 means 1 pixel = 1 millimeter, which provides a
 * reasonable scale for interior design applications where elements need to be
 * clearly visible and manipulable.
 *
 * Previous value of 0.08 caused display issues where a 200px square showed as 2500mm.
 * With 1.0, a 200px square will display as 200mm, which is more reasonable.
 */
<span class="cstat-no" title="statement not covered" >export const CANVAS_PIXELS_PER_MM = 1.0</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const Canvas: React.FC&lt;CanvasProps&gt; = React.memo(</span>
<span class="cstat-no" title="statement not covered" >  ({</span>
<span class="cstat-no" title="statement not covered" >    elements,</span>
<span class="cstat-no" title="statement not covered" >    selectedElementIds,</span>
<span class="cstat-no" title="statement not covered" >    isPanMode = false,</span>
<span class="cstat-no" title="statement not covered" >    isDrawMode = false,</span>
<span class="cstat-no" title="statement not covered" >    showGrid = true,</span>
<span class="cstat-no" title="statement not covered" >    gridSize = 50,</span>
<span class="cstat-no" title="statement not covered" >    zoom = INITIAL_ZOOM,</span>
<span class="cstat-no" title="statement not covered" >    pan = INITIAL_PAN,</span>
<span class="cstat-no" title="statement not covered" >    primedAssetTypeForCanvasClick: _primedAssetTypeForCanvasClick = null,</span>
<span class="cstat-no" title="statement not covered" >    pathDrawState: pathDrawStateProp,</span>
<span class="cstat-no" title="statement not covered" >    onMouseDown: onMouseDownProp = EMPTY_FUNCTION,</span>
<span class="cstat-no" title="statement not covered" >    onMouseMove: onMouseMoveProp = EMPTY_FUNCTION,</span>
<span class="cstat-no" title="statement not covered" >    onMouseUp: onMouseUpProp = EMPTY_FUNCTION,</span>
<span class="cstat-no" title="statement not covered" >    onDragOver = EMPTY_FUNCTION,</span>
<span class="cstat-no" title="statement not covered" >    onDrop: onDropProp = EMPTY_FUNCTION,</span>
<span class="cstat-no" title="statement not covered" >    onElementAdd = EMPTY_FUNCTION,</span>
<span class="cstat-no" title="statement not covered" >    onElementSelect = EMPTY_FUNCTION,</span>
<span class="cstat-no" title="statement not covered" >    onWorldMouseMove: _onWorldMouseMove,</span>
<span class="cstat-no" title="statement not covered" >    onDimensionsChange,</span>
<span class="cstat-no" title="statement not covered" >    isPropertySidebarOpen,</span>
<span class="cstat-no" title="statement not covered" >    onZoomChange: _onZoomChange,</span>
<span class="cstat-no" title="statement not covered" >    onPanChange: _onPanChange,</span>
<span class="cstat-no" title="statement not covered" >    onWheel,</span>
<span class="cstat-no" title="statement not covered" >    doStartPan,</span>
<span class="cstat-no" title="statement not covered" >    doContinuePan,</span>
<span class="cstat-no" title="statement not covered" >    doEndPan,</span>
<span class="cstat-no" title="statement not covered" >    onShapeDrop,</span>
<span class="cstat-no" title="statement not covered" >    sheetOpen,</span>
<span class="cstat-no" title="statement not covered" >  }: CanvasProps) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const svgRef = useRef&lt;SVGSVGElement | null&gt;(null)</span>
<span class="cstat-no" title="statement not covered" >    const canvasWrapperRef = useRef&lt;HTMLDivElement | null&gt;(null)</span>
<span class="cstat-no" title="statement not covered" >    const mainGroupRef = useRef&lt;SVGGElement | null&gt;(null)</span>
<span class="cstat-no" title="statement not covered" >    const shapesLayerRef = useRef&lt;SVGGElement | null&gt;(null)</span>
<span class="cstat-no" title="statement not covered" >    const interactionLayerRef = useRef&lt;SVGGElement | null&gt;(null)</span>
<span class="cstat-no" title="statement not covered" >    const handlesLayerRef = useRef&lt;SVGGElement | null&gt;(null)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const [currentSelectionBox, setCurrentSelectionBox] = useState&lt;{ x: number, y: number, width: number, height: number } | null&gt;(null)</span>
<span class="cstat-no" title="statement not covered" >    const [dragStartWorld, setDragStartWorld] = useState&lt;Point | null&gt;(null)</span>
&nbsp;
    // 路径绘制状态现在从props传入
&nbsp;
    // --- NEW: Responsive canvas size ---
<span class="cstat-no" title="statement not covered" >    const [canvasWidth, setCanvasWidth] = useState&lt;number&gt;(window.innerWidth)</span>
<span class="cstat-no" title="statement not covered" >    const [canvasHeight, setCanvasHeight] = useState&lt;number&gt;(window.innerHeight)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const updateSize = useCallback(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (canvasWrapperRef.current) {</span>
<span class="cstat-no" title="statement not covered" >        const rect = canvasWrapperRef.current.getBoundingClientRect()</span>
<span class="cstat-no" title="statement not covered" >        return { width: rect.width, height: rect.height }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
<span class="cstat-no" title="statement not covered" >        return { width: window.innerWidth, height: window.innerHeight }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }, [])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      const initialSize = updateSize()</span>
&nbsp;
      // Use a flag to track if component is still mounted
<span class="cstat-no" title="statement not covered" >      let isMounted = true</span>
&nbsp;
      // Schedule initial state updates using requestAnimationFrame to avoid direct setState in useEffect
<span class="cstat-no" title="statement not covered" >      requestAnimationFrame(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (isMounted) {</span>
<span class="cstat-no" title="statement not covered" >          setCanvasWidth(prevWidth =&gt; initialSize.width !== prevWidth ? initialSize.width : prevWidth)</span>
<span class="cstat-no" title="statement not covered" >          setCanvasHeight(prevHeight =&gt; initialSize.height !== prevHeight ? initialSize.height : prevHeight)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      })</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const timeoutId = setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (isMounted) {</span>
<span class="cstat-no" title="statement not covered" >          const delayedSize = updateSize()</span>
<span class="cstat-no" title="statement not covered" >          requestAnimationFrame(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            if (isMounted) {</span>
<span class="cstat-no" title="statement not covered" >              setCanvasWidth(prevWidth =&gt; delayedSize.width !== prevWidth ? delayedSize.width : prevWidth)</span>
<span class="cstat-no" title="statement not covered" >              setCanvasHeight(prevHeight =&gt; delayedSize.height !== prevHeight ? delayedSize.height : prevHeight)</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          })</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }, 0) // 关键：初始渲染后再测量一次，确保布局完成</span>
&nbsp;
      // Use ResizeObserver for container
<span class="cstat-no" title="statement not covered" >      let observer: ResizeObserver | null = null</span>
<span class="cstat-no" title="statement not covered" >      const currentElement = canvasWrapperRef.current</span>
<span class="cstat-no" title="statement not covered" >      if (currentElement &amp;&amp; typeof ResizeObserver !== 'undefined') {</span>
<span class="cstat-no" title="statement not covered" >        observer = new ResizeObserver(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >          const newSize = updateSize()</span>
          // Use requestAnimationFrame to defer state updates
<span class="cstat-no" title="statement not covered" >          requestAnimationFrame(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            setCanvasWidth(prevWidth =&gt; newSize.width !== prevWidth ? newSize.width : prevWidth)</span>
<span class="cstat-no" title="statement not covered" >            setCanvasHeight(prevHeight =&gt; newSize.height !== prevHeight ? newSize.height : prevHeight)</span>
<span class="cstat-no" title="statement not covered" >          })</span>
<span class="cstat-no" title="statement not covered" >        })</span>
<span class="cstat-no" title="statement not covered" >        observer.observe(currentElement)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
<span class="cstat-no" title="statement not covered" >        const handleResize = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >          const newSize = updateSize()</span>
          // Use requestAnimationFrame to defer state updates
<span class="cstat-no" title="statement not covered" >          requestAnimationFrame(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            setCanvasWidth(prevWidth =&gt; newSize.width !== prevWidth ? newSize.width : prevWidth)</span>
<span class="cstat-no" title="statement not covered" >            setCanvasHeight(prevHeight =&gt; newSize.height !== prevHeight ? newSize.height : prevHeight)</span>
<span class="cstat-no" title="statement not covered" >          })</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        window.addEventListener('resize', handleResize)</span>
<span class="cstat-no" title="statement not covered" >        return () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >          clearTimeout(timeoutId)</span>
<span class="cstat-no" title="statement not covered" >          window.removeEventListener('resize', handleResize)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      return () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        isMounted = false</span>
<span class="cstat-no" title="statement not covered" >        clearTimeout(timeoutId)</span>
        // Use the captured element reference for cleanup
<span class="cstat-no" title="statement not covered" >        if (observer !== null &amp;&amp; currentElement !== null) {</span>
<span class="cstat-no" title="statement not covered" >          observer.unobserve(currentElement)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }, [updateSize])</span>
    // --- END Responsive canvas size ---
&nbsp;
    // Get Z-Level data from layerStore
<span class="cstat-no" title="statement not covered" >    const layerModulesFromStore = useLayerStore(state =&gt; state.modules) // Get all modules</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const { activeZLevelIds, zLevelZIndexMap } = useMemo(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      const activeIds = new Set&lt;string&gt;()</span>
<span class="cstat-no" title="statement not covered" >      const zIndexMap = new Map&lt;string, number&gt;()</span>
<span class="cstat-no" title="statement not covered" >      layerModulesFromStore.forEach((module) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        module.steps.forEach((step) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >          step.zLevels.forEach((zLevel) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            zIndexMap.set(zLevel.id, zLevel.zIndex) // Populate zIndexMap for all layers</span>
<span class="cstat-no" title="statement not covered" >            if (zLevel.active === true) {</span>
<span class="cstat-no" title="statement not covered" >              activeIds.add(zLevel.id) // Add to active set if zLevel.active is true</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          })</span>
<span class="cstat-no" title="statement not covered" >        })</span>
<span class="cstat-no" title="statement not covered" >      })</span>
      // Debug: Derived activeZLevelIds
<span class="cstat-no" title="statement not covered" >      return { activeZLevelIds: activeIds, zLevelZIndexMap: zIndexMap }</span>
<span class="cstat-no" title="statement not covered" >    }, [layerModulesFromStore])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const visibleAndSortedElements = useMemo(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      const visibleElements = elements.filter((element: ShapeElement) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (element.zLevelId === null || element.zLevelId === undefined || element.zLevelId === '') {</span>
          // console.log('[Canvas Filter] Element lacks zLevelId:', element.id);
<span class="cstat-no" title="statement not covered" >          return false</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        const isActive = activeZLevelIds.has(element.zLevelId)</span>
        // console.log(`[Canvas Filter] Element ID: ${element.id}, zLevelId: ${element.zLevelId}, Is In Active Set: ${isActive}`);
&nbsp;
        // TEMPORARY FIX: Allow pattern test elements to bypass layer filtering
<span class="cstat-no" title="statement not covered" >        if (element.id !== undefined &amp;&amp; element.id.includes('test-square') &amp;&amp; element.pattern !== undefined) {</span>
          // Debug: Allowing pattern test element to bypass layer filtering
<span class="cstat-no" title="statement not covered" >          return true</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // TEMPORARY FIX: Allow TEXT elements to bypass layer filtering for testing
<span class="cstat-no" title="statement not covered" >        if (element.type === 'TEXT') {</span>
          // Debug: Allowing TEXT element to bypass layer filtering
<span class="cstat-no" title="statement not covered" >          return true</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        return isActive</span>
<span class="cstat-no" title="statement not covered" >      })</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const sorted = visibleElements.sort((a: ShapeElement, b: ShapeElement) =&gt; {</span>
        // 首先按zLevelId的zIndex比较
<span class="cstat-no" title="statement not covered" >        const zIndexA = (a.zLevelId !== null &amp;&amp; a.zLevelId !== undefined &amp;&amp; a.zLevelId !== '') ? zLevelZIndexMap.get(a.zLevelId) ?? Infinity : Infinity</span>
<span class="cstat-no" title="statement not covered" >        const zIndexB = (b.zLevelId !== null &amp;&amp; b.zLevelId !== undefined &amp;&amp; b.zLevelId !== '') ? zLevelZIndexMap.get(b.zLevelId) ?? Infinity : Infinity</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (zIndexA !== zIndexB) {</span>
<span class="cstat-no" title="statement not covered" >          return zIndexA - zIndexB</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // 如果zLevelId的zIndex相同，则按intraLayerZIndex比较
        // 确保intraLayerZIndex为数字，默认值为0
<span class="cstat-no" title="statement not covered" >        const intraLayerZIndexA = typeof a.intraLayerZIndex === 'number' ? a.intraLayerZIndex : 0</span>
<span class="cstat-no" title="statement not covered" >        const intraLayerZIndexB = typeof b.intraLayerZIndex === 'number' ? b.intraLayerZIndex : 0</span>
&nbsp;
        // Debug: Compare intraLayerZIndex for different elements
&nbsp;
<span class="cstat-no" title="statement not covered" >        return intraLayerZIndexA - intraLayerZIndexB</span>
<span class="cstat-no" title="statement not covered" >      })</span>
&nbsp;
      // Debug: Log rendering order and count
<span class="cstat-no" title="statement not covered" >      return sorted</span>
<span class="cstat-no" title="statement not covered" >    }, [elements, activeZLevelIds, zLevelZIndexMap])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const { createMouseEventDetails } = useCoordinateSystem({</span>
<span class="cstat-no" title="statement not covered" >      svgRef,</span>
<span class="cstat-no" title="statement not covered" >      pan,</span>
<span class="cstat-no" title="statement not covered" >      zoom,</span>
<span class="cstat-no" title="statement not covered" >    })</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const isPanningRef = useRef&lt;boolean&gt;(false)</span>
&nbsp;
    // 用于跟踪document级别的事件监听器
<span class="cstat-no" title="statement not covered" >    const documentEventListenersRef = useRef&lt;{</span>
      mousemove?: (e: MouseEvent) =&gt; void
      mouseup?: (e: MouseEvent) =&gt; void
<span class="cstat-no" title="statement not covered" >    }&gt;({})</span>
&nbsp;
    // 坐标转换函数：将document事件转换为SVG坐标
<span class="cstat-no" title="statement not covered" >    const convertDocumentEventToSvgCoordinates = useCallback((event: MouseEvent): CanvasMouseEvent | null =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (svgRef.current === null) {</span>
<span class="cstat-no" title="statement not covered" >        debug('convertDocumentEventToSvgCoordinates: svgRef.current is null')</span>
<span class="cstat-no" title="statement not covered" >        return null</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      try {</span>
<span class="cstat-no" title="statement not covered" >        const svgRect = svgRef.current.getBoundingClientRect()</span>
<span class="cstat-no" title="statement not covered" >        const svgX = event.clientX - svgRect.left</span>
<span class="cstat-no" title="statement not covered" >        const svgY = event.clientY - svgRect.top</span>
&nbsp;
        // 检查鼠标是否在Canvas区域内
<span class="cstat-no" title="statement not covered" >        const isInsideCanvas = svgX &gt;= 0 &amp;&amp; svgX &lt;= svgRect.width &amp;&amp; svgY &gt;= 0 &amp;&amp; svgY &lt;= svgRect.height</span>
&nbsp;
        // 对于框选，我们需要允许鼠标移动到Canvas外部，但仍然计算有效的坐标
        // 将超出边界的坐标限制在Canvas边界内，这样框选框会正确显示
<span class="cstat-no" title="statement not covered" >        const clampedSvgX = Math.max(0, Math.min(svgRect.width, svgX))</span>
<span class="cstat-no" title="statement not covered" >        const clampedSvgY = Math.max(0, Math.min(svgRect.height, svgY))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        const worldX = (clampedSvgX - pan.x) / zoom</span>
<span class="cstat-no" title="statement not covered" >        const worldY = (clampedSvgY - pan.y) / zoom</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        return {</span>
<span class="cstat-no" title="statement not covered" >          svgPosition: { x: clampedSvgX, y: clampedSvgY },</span>
<span class="cstat-no" title="statement not covered" >          worldPosition: { x: worldX, y: worldY },</span>
<span class="cstat-no" title="statement not covered" >          originalEvent: event,</span>
<span class="cstat-no" title="statement not covered" >          isInsideCanvas, // 添加标记，指示鼠标是否在Canvas内</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      catch (error) {</span>
<span class="cstat-no" title="statement not covered" >        debug('[Canvas] convertDocumentEventToSvgCoordinates: Error converting coordinates', error)</span>
<span class="cstat-no" title="statement not covered" >        return null</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }, [pan, zoom])</span>
&nbsp;
    // 清理document事件监听器的函数
<span class="cstat-no" title="statement not covered" >    const cleanupDocumentEventListeners = useCallback(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (documentEventListenersRef.current.mousemove) {</span>
<span class="cstat-no" title="statement not covered" >        document.removeEventListener('mousemove', documentEventListenersRef.current.mousemove)</span>
<span class="cstat-no" title="statement not covered" >        documentEventListenersRef.current.mousemove = undefined</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      if (documentEventListenersRef.current.mouseup) {</span>
<span class="cstat-no" title="statement not covered" >        document.removeEventListener('mouseup', documentEventListenersRef.current.mouseup)</span>
<span class="cstat-no" title="statement not covered" >        documentEventListenersRef.current.mouseup = undefined</span>
<span class="cstat-no" title="statement not covered" >      }</span>
      // Debug: Document event listeners cleaned up
<span class="cstat-no" title="statement not covered" >    }, [])</span>
&nbsp;
    // 组件卸载时清理事件监听器
<span class="cstat-no" title="statement not covered" >    useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      return () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        cleanupDocumentEventListeners()</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }, [cleanupDocumentEventListeners])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const canvasSize = useMemo(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      const w = canvasWidth || 0</span>
<span class="cstat-no" title="statement not covered" >      const h = canvasHeight || 0</span>
<span class="cstat-no" title="statement not covered" >      return { width: w, height: h, viewBoxX: -w / 2, viewBoxY: -h / 2 }</span>
<span class="cstat-no" title="statement not covered" >    }, [canvasWidth, canvasHeight])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const debouncedOnDimensionsChange = onDimensionsChange || EMPTY_FUNCTION</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (canvasSize.width !== undefined &amp;&amp; canvasSize.height !== undefined &amp;&amp; canvasSize.width &gt; 0 &amp;&amp; canvasSize.height &gt; 0 &amp;&amp; typeof zoom === 'number' &amp;&amp; zoom &gt; 0) {</span>
        // 使用CANVAS_PIXELS_PER_MM来正确计算物理尺寸
<span class="cstat-no" title="statement not covered" >        const displayWidth = formatDimensionForDisplay(canvasSize.width / (zoom * CANVAS_PIXELS_PER_MM))</span>
<span class="cstat-no" title="statement not covered" >        const displayHeight = formatDimensionForDisplay(canvasSize.height / (zoom * CANVAS_PIXELS_PER_MM))</span>
<span class="cstat-no" title="statement not covered" >        debouncedOnDimensionsChange(displayWidth, displayHeight)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }, [canvasSize, zoom, debouncedOnDimensionsChange])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (</span>
<span class="cstat-no" title="statement not covered" >        mainGroupRef.current !== undefined &amp;&amp; mainGroupRef.current !== null &amp;&amp; pan !== undefined &amp;&amp; pan !== null &amp;&amp; typeof pan.x === 'number' &amp;&amp; typeof pan.y === 'number' &amp;&amp; typeof zoom === 'number'</span>
<span class="cstat-no" title="statement not covered" >      ) {</span>
<span class="cstat-no" title="statement not covered" >        mainGroupRef.current.setAttribute(</span>
<span class="cstat-no" title="statement not covered" >          'transform',</span>
<span class="cstat-no" title="statement not covered" >          `translate(${pan.x}, ${pan.y}) scale(${zoom})`,</span>
<span class="cstat-no" title="statement not covered" >        )</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }, [pan, zoom])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const handleSvgMouseDown = useCallback((event: React.MouseEvent&lt;SVGElement&gt;) =&gt; {</span>
      // Debug: handleSvgMouseDown
&nbsp;
      // 如果处于平移模式，启动平移
<span class="cstat-no" title="statement not covered" >      if (isPanMode &amp;&amp; doStartPan &amp;&amp; event.button === 0) {</span>
        // Debug: Calling doStartPan
<span class="cstat-no" title="statement not covered" >        doStartPan(event)</span>
<span class="cstat-no" title="statement not covered" >        isPanningRef.current = true</span>
        // Debug: isPanningRef.current set to true
<span class="cstat-no" title="statement not covered" >        event.preventDefault()</span>
<span class="cstat-no" title="statement not covered" >        return</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      try {</span>
        // Debug: Event target
<span class="cstat-no" title="statement not covered" >        const mouseDetails: CanvasMouseEvent = createMouseEventDetails(event, svgRef.current)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (svgRef.current === undefined || svgRef.current === null) {</span>
<span class="cstat-no" title="statement not covered" >          return</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // 绘制模式处理已移至EditorLayout，这里不再处理
        // 避免重复的绘制处理逻辑
&nbsp;
<span class="cstat-no" title="statement not covered" >        const targetIsSvgBackground</span>
<span class="cstat-no" title="statement not covered" >          = event.target === svgRef.current</span>
<span class="cstat-no" title="statement not covered" >            || (event.target instanceof Element</span>
<span class="cstat-no" title="statement not covered" >              &amp;&amp; event.target.classList.contains('canvas-grid-rect'))</span>
&nbsp;
        // 如果点击了背景，且不在绘制模式下，处理选择逻辑
<span class="cstat-no" title="statement not covered" >        if (targetIsSvgBackground &amp;&amp; !isDrawMode) {</span>
<span class="cstat-no" title="statement not covered" >          debug('handleSvgMouseDown: Clicked on SVG background or grid.')</span>
&nbsp;
          // 提取键盘修饰键
<span class="cstat-no" title="statement not covered" >          const modifiers = extractKeyboardModifiers(event)</span>
&nbsp;
          // 检查是否应该清空选择
<span class="cstat-no" title="statement not covered" >          if (shouldClearSelectionOnBackgroundClick(modifiers) &amp;&amp; selectedElementIds.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            debug('handleSvgMouseDown: Clearing selection (background click without modifier)')</span>
<span class="cstat-no" title="statement not covered" >            if (typeof onElementSelect === 'function') {</span>
<span class="cstat-no" title="statement not covered" >              onElementSelect(null, false, 'clear')</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
          // 开始框选操作
<span class="cstat-no" title="statement not covered" >          if (mouseDetails.svgPosition != null &amp;&amp; pan != null &amp;&amp; typeof pan.x === 'number' &amp;&amp; typeof pan.y === 'number' &amp;&amp; typeof zoom === 'number' &amp;&amp; zoom !== 0) {</span>
<span class="cstat-no" title="statement not covered" >            const trueWorldDragStart = {</span>
<span class="cstat-no" title="statement not covered" >              x: (mouseDetails.svgPosition.x - pan.x) / zoom,</span>
<span class="cstat-no" title="statement not covered" >              y: (mouseDetails.svgPosition.y - pan.y) / zoom,</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            setDragStartWorld(trueWorldDragStart)</span>
<span class="cstat-no" title="statement not covered" >            debug('handleSvgMouseDown: Set dragStartWorld (true world):', trueWorldDragStart)</span>
&nbsp;
            // 添加document级别的事件监听，确保即使鼠标离开SVG也能继续框选
<span class="cstat-no" title="statement not covered" >            const handleDocumentMouseMove = (e: MouseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >              const details = convertDocumentEventToSvgCoordinates(e)</span>
<span class="cstat-no" title="statement not covered" >              if (!details?.svgPosition) {</span>
<span class="cstat-no" title="statement not covered" >                return</span>
<span class="cstat-no" title="statement not covered" >              }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              const currentTrueWorldMousePosition = {</span>
<span class="cstat-no" title="statement not covered" >                x: (details.svgPosition.x - pan.x) / zoom,</span>
<span class="cstat-no" title="statement not covered" >                y: (details.svgPosition.y - pan.y) / zoom,</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              const newSelectionBox = calculateMarqueeBox(trueWorldDragStart, currentTrueWorldMousePosition)</span>
<span class="cstat-no" title="statement not covered" >              setCurrentSelectionBox(newSelectionBox)</span>
<span class="cstat-no" title="statement not covered" >              debug('Document mousemove: Updated selection box', newSelectionBox)</span>
<span class="cstat-no" title="statement not covered" >            }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            const handleDocumentMouseUp = (e: MouseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >              debug('Document mouseup: Completing marquee selection')</span>
&nbsp;
              // 清理document事件监听器
<span class="cstat-no" title="statement not covered" >              cleanupDocumentEventListeners()</span>
&nbsp;
              // 完成框选逻辑
<span class="cstat-no" title="statement not covered" >              if (currentSelectionBox &amp;&amp; (currentSelectionBox.width &gt; 0 || currentSelectionBox.height &gt; 0)) {</span>
<span class="cstat-no" title="statement not covered" >                const selBox = new BoundingBoxClass(</span>
<span class="cstat-no" title="statement not covered" >                  currentSelectionBox.x,</span>
<span class="cstat-no" title="statement not covered" >                  currentSelectionBox.y,</span>
<span class="cstat-no" title="statement not covered" >                  currentSelectionBox.width,</span>
<span class="cstat-no" title="statement not covered" >                  currentSelectionBox.height,</span>
<span class="cstat-no" title="statement not covered" >                )</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                const idsToSelect = elements</span>
<span class="cstat-no" title="statement not covered" >                  .filter((el: ShapeElement) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                    const bbox = ShapeElementUtils.calculateShapeBoundingBox(el)</span>
<span class="cstat-no" title="statement not covered" >                    if (!bbox) {</span>
<span class="cstat-no" title="statement not covered" >                      return false</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                    return bbox.intersects(selBox)</span>
<span class="cstat-no" title="statement not covered" >                  })</span>
<span class="cstat-no" title="statement not covered" >                  .map((el: ShapeElement) =&gt; el.id)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                debug('Document mouseup: IDs to select from marquee:', idsToSelect)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                if (typeof onElementSelect === 'function') {</span>
<span class="cstat-no" title="statement not covered" >                  const modifiers = extractKeyboardModifiers(e)</span>
<span class="cstat-no" title="statement not covered" >                  const newSelection = handleMarqueeSelection(idsToSelect, selectedElementIds, modifiers)</span>
<span class="cstat-no" title="statement not covered" >                  const isMultiSelect = modifiers.shiftKey || modifiers.ctrlKey || modifiers.metaKey</span>
<span class="cstat-no" title="statement not covered" >                  const selectionMode = isMultiSelect ? 'add' : 'replace'</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                  onElementSelect(newSelection.length &gt; 0 ? newSelection : null, isMultiSelect, selectionMode)</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >              }</span>
&nbsp;
              // 重置状态
<span class="cstat-no" title="statement not covered" >              setDragStartWorld(null)</span>
<span class="cstat-no" title="statement not covered" >              setCurrentSelectionBox(null)</span>
<span class="cstat-no" title="statement not covered" >            }</span>
&nbsp;
            // 先清理之前的监听器，然后添加新的
<span class="cstat-no" title="statement not covered" >            cleanupDocumentEventListeners()</span>
<span class="cstat-no" title="statement not covered" >            documentEventListenersRef.current.mousemove = handleDocumentMouseMove</span>
<span class="cstat-no" title="statement not covered" >            documentEventListenersRef.current.mouseup = handleDocumentMouseUp</span>
<span class="cstat-no" title="statement not covered" >            document.addEventListener('mousemove', handleDocumentMouseMove)</span>
<span class="cstat-no" title="statement not covered" >            document.addEventListener('mouseup', handleDocumentMouseUp)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            debug('Document event listeners added for marquee selection')</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          else {</span>
<span class="cstat-no" title="statement not covered" >            debug('[Canvas] handleSvgMouseDown: Could not calculate trueWorldDragStart due to missing data. Using raw worldPosition as fallback.', { svgPos: mouseDetails.svgPosition, pan, zoom })</span>
<span class="cstat-no" title="statement not covered" >            setDragStartWorld(mouseDetails.worldPosition)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          setCurrentSelectionBox(null)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // 总是调用传入的鼠标按下处理函数
<span class="cstat-no" title="statement not covered" >        if (typeof onMouseDownProp === 'function') {</span>
<span class="cstat-no" title="statement not covered" >          onMouseDownProp(mouseDetails)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      catch (error: unknown) {</span>
<span class="cstat-no" title="statement not covered" >        if (error instanceof Error) {</span>
<span class="cstat-no" title="statement not covered" >          debug('Error in handleSvgMouseDown creating event details:', error.message)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        else {</span>
<span class="cstat-no" title="statement not covered" >          debug('Unknown error in handleSvgMouseDown:', error)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }, [isPanMode, isDrawMode, svgRef, pan, zoom, selectedElementIds, onElementSelect, onMouseDownProp, doStartPan, createMouseEventDetails, convertDocumentEventToSvgCoordinates, cleanupDocumentEventListeners, currentSelectionBox, elements, setDragStartWorld, setCurrentSelectionBox])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const handleSvgMouseMove = useCallback((event: React.MouseEvent&lt;SVGElement&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      try {</span>
        // 检查事件是否有效
<span class="cstat-no" title="statement not covered" >        if (event == null || svgRef.current == null) {</span>
<span class="cstat-no" title="statement not covered" >          debug('[Canvas] handleSvgMouseMove: Invalid event or svgRef')</span>
<span class="cstat-no" title="statement not covered" >          return</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // 安全地创建鼠标事件详情
<span class="cstat-no" title="statement not covered" >        let details: CanvasMouseEvent</span>
<span class="cstat-no" title="statement not covered" >        try {</span>
<span class="cstat-no" title="statement not covered" >          details = createMouseEventDetails(event, svgRef.current)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        catch (error) {</span>
<span class="cstat-no" title="statement not covered" >          debug('[Canvas] handleSvgMouseMove: Error creating mouse event details', error)</span>
<span class="cstat-no" title="statement not covered" >          return</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // 如果处于平移模式且正在平移，继续平移
<span class="cstat-no" title="statement not covered" >        if (isPanMode &amp;&amp; isPanningRef.current &amp;&amp; doContinuePan) {</span>
<span class="cstat-no" title="statement not covered" >          debug('handleSvgMouseMove: Calling doContinuePan.')</span>
<span class="cstat-no" title="statement not covered" >          doContinuePan(event)</span>
<span class="cstat-no" title="statement not covered" >          event.preventDefault()</span>
<span class="cstat-no" title="statement not covered" >          return</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // 绘制模式的鼠标移动处理已移至EditorLayout，这里不再处理
&nbsp;
        // 如果有拖动起点且不在绘制模式下，更新选择框
<span class="cstat-no" title="statement not covered" >        if (dragStartWorld !== undefined &amp;&amp; dragStartWorld !== null</span>
<span class="cstat-no" title="statement not covered" >          &amp;&amp; details.worldPosition !== undefined &amp;&amp; details.worldPosition !== null</span>
<span class="cstat-no" title="statement not covered" >          &amp;&amp; !isDrawMode) {</span>
<span class="cstat-no" title="statement not covered" >          if (details.svgPosition != null &amp;&amp; pan != null &amp;&amp; typeof pan.x === 'number' &amp;&amp; typeof pan.y === 'number' &amp;&amp; typeof zoom === 'number' &amp;&amp; zoom !== 0) {</span>
<span class="cstat-no" title="statement not covered" >            const currentTrueWorldMousePosition = {</span>
<span class="cstat-no" title="statement not covered" >              x: (details.svgPosition.x - pan.x) / zoom,</span>
<span class="cstat-no" title="statement not covered" >              y: (details.svgPosition.y - pan.y) / zoom,</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            const newSelectionBox = calculateMarqueeBox(dragStartWorld, currentTrueWorldMousePosition)</span>
<span class="cstat-no" title="statement not covered" >            setCurrentSelectionBox(newSelectionBox)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          else {</span>
<span class="cstat-no" title="statement not covered" >            debug('[Canvas] handleSvgMouseMove: Could not calculate currentTrueWorldMousePosition. Marquee might be incorrect.', { svgPos: details.svgPosition, pan, zoom })</span>
<span class="cstat-no" title="statement not covered" >            const fallbackWorldPos = (details.worldPosition !== undefined &amp;&amp; details.worldPosition !== null) ? details.worldPosition : { x: 0, y: 0 }</span>
<span class="cstat-no" title="statement not covered" >            const newSelectionBox = calculateMarqueeBox(dragStartWorld, fallbackWorldPos)</span>
<span class="cstat-no" title="statement not covered" >            setCurrentSelectionBox(newSelectionBox)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // 总是调用传入的鼠标移动处理函数
<span class="cstat-no" title="statement not covered" >        if (typeof onMouseMoveProp === 'function') {</span>
<span class="cstat-no" title="statement not covered" >          onMouseMoveProp(details)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      catch (error: unknown) {</span>
<span class="cstat-no" title="statement not covered" >        if (error instanceof Error) {</span>
<span class="cstat-no" title="statement not covered" >          debug('Error in handleSvgMouseMove creating/using event details:', error.message)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        else {</span>
<span class="cstat-no" title="statement not covered" >          debug('Unknown error in handleSvgMouseMove:', error)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }, [createMouseEventDetails, dragStartWorld, pan, zoom, onMouseMoveProp, isPanMode, isDrawMode, doContinuePan, setCurrentSelectionBox, svgRef])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const handleSvgMouseUp = useCallback((event: React.MouseEvent&lt;SVGElement&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      debug('handleSvgMouseUp. isPanMode:', isPanMode, 'isDrawMode:', isDrawMode, 'isPanningRef.current:', isPanningRef.current)</span>
&nbsp;
      // 如果处于平移模式且正在平移，结束平移
<span class="cstat-no" title="statement not covered" >      if (isPanMode &amp;&amp; isPanningRef.current &amp;&amp; doEndPan) {</span>
<span class="cstat-no" title="statement not covered" >        debug('handleSvgMouseUp: Calling doEndPan.')</span>
<span class="cstat-no" title="statement not covered" >        doEndPan()</span>
<span class="cstat-no" title="statement not covered" >        isPanningRef.current = false</span>
<span class="cstat-no" title="statement not covered" >        debug('handleSvgMouseUp: isPanningRef.current set to false.')</span>
<span class="cstat-no" title="statement not covered" >        event.preventDefault()</span>
<span class="cstat-no" title="statement not covered" >        setDragStartWorld(null)</span>
<span class="cstat-no" title="statement not covered" >        setCurrentSelectionBox(null)</span>
<span class="cstat-no" title="statement not covered" >        return</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      try {</span>
<span class="cstat-no" title="statement not covered" >        const details: CanvasMouseEvent = createMouseEventDetails(event, svgRef.current)</span>
&nbsp;
        // 绘制模式的mouseup处理已移至EditorLayout，这里不再处理
&nbsp;
        // 如果不在绘制模式下，处理选择框
<span class="cstat-no" title="statement not covered" >        if (!isDrawMode &amp;&amp; dragStartWorld !== undefined &amp;&amp; dragStartWorld !== null</span>
<span class="cstat-no" title="statement not covered" >          &amp;&amp; currentSelectionBox !== undefined &amp;&amp; currentSelectionBox !== null</span>
<span class="cstat-no" title="statement not covered" >          &amp;&amp; details.worldPosition !== undefined &amp;&amp; details.worldPosition !== null) {</span>
<span class="cstat-no" title="statement not covered" >          debug('handleSvgMouseUp: Marquee selection ending.', { dragStartWorld, currentSelectionBox })</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          const selBox = new BoundingBoxClass(</span>
<span class="cstat-no" title="statement not covered" >            currentSelectionBox.x,</span>
<span class="cstat-no" title="statement not covered" >            currentSelectionBox.y,</span>
<span class="cstat-no" title="statement not covered" >            currentSelectionBox.width,</span>
<span class="cstat-no" title="statement not covered" >            currentSelectionBox.height,</span>
<span class="cstat-no" title="statement not covered" >          )</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (selBox.width &gt; 0 || selBox.height &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            const idsToSelect = elements</span>
<span class="cstat-no" title="statement not covered" >              .filter((el: ShapeElement) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                const bbox = ShapeElementUtils.calculateShapeBoundingBox(el)</span>
<span class="cstat-no" title="statement not covered" >                if (!bbox) {</span>
<span class="cstat-no" title="statement not covered" >                  return false</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >                const intersects = bbox.intersects(selBox)</span>
<span class="cstat-no" title="statement not covered" >                return intersects</span>
<span class="cstat-no" title="statement not covered" >              })</span>
<span class="cstat-no" title="statement not covered" >              .map((el: ShapeElement) =&gt; el.id)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            debug('handleSvgMouseUp: IDs to select from marquee:', idsToSelect)</span>
<span class="cstat-no" title="statement not covered" >            if (typeof onElementSelect === 'function') {</span>
              // 提取键盘修饰键
<span class="cstat-no" title="statement not covered" >              const modifiers = extractKeyboardModifiers(details.originalEvent ?? {})</span>
&nbsp;
              // 使用工具函数处理框选逻辑
<span class="cstat-no" title="statement not covered" >              const newSelection = handleMarqueeSelection(</span>
<span class="cstat-no" title="statement not covered" >                idsToSelect,</span>
<span class="cstat-no" title="statement not covered" >                selectedElementIds,</span>
<span class="cstat-no" title="statement not covered" >                modifiers,</span>
<span class="cstat-no" title="statement not covered" >              )</span>
&nbsp;
              // 确定选择模式
<span class="cstat-no" title="statement not covered" >              const isMultiSelect = modifiers.shiftKey || modifiers.ctrlKey || modifiers.metaKey</span>
<span class="cstat-no" title="statement not covered" >              const selectionMode = isMultiSelect ? 'add' : 'replace'</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              debug('handleSvgMouseUp: Marquee selection result:', {</span>
<span class="cstat-no" title="statement not covered" >                idsToSelect,</span>
<span class="cstat-no" title="statement not covered" >                currentSelection: selectedElementIds,</span>
<span class="cstat-no" title="statement not covered" >                newSelection,</span>
<span class="cstat-no" title="statement not covered" >                selectionMode,</span>
<span class="cstat-no" title="statement not covered" >              })</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              onElementSelect(newSelection.length &gt; 0 ? newSelection : null, isMultiSelect, selectionMode)</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          else if (event.target === svgRef.current || (event.target instanceof Element &amp;&amp; event.target.classList.contains('canvas-grid-rect'))) {</span>
            // 提取键盘修饰键
<span class="cstat-no" title="statement not covered" >            const modifiers = extractKeyboardModifiers(details.originalEvent ?? {})</span>
&nbsp;
            // 检查是否应该清空选择
<span class="cstat-no" title="statement not covered" >            if (shouldClearSelectionOnBackgroundClick(modifiers)) {</span>
<span class="cstat-no" title="statement not covered" >              debug('handleSvgMouseUp: Click on background (not marquee drag), clearing selection.')</span>
<span class="cstat-no" title="statement not covered" >              if (typeof onElementSelect === 'function') {</span>
<span class="cstat-no" title="statement not covered" >                onElementSelect(null, false, 'clear')</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            else {</span>
<span class="cstat-no" title="statement not covered" >              debug('handleSvgMouseUp: Click on background with multi-select key, keeping selection.')</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
        // 如果不在绘制模式下，且点击了背景，处理选择清除
<span class="cstat-no" title="statement not covered" >        else if (!isDrawMode</span>
<span class="cstat-no" title="statement not covered" >          &amp;&amp; (event.target === svgRef.current</span>
<span class="cstat-no" title="statement not covered" >            || (event.target instanceof Element &amp;&amp; event.target.classList.contains('canvas-grid-rect')))) {</span>
          // 提取键盘修饰键
<span class="cstat-no" title="statement not covered" >          const modifiers = extractKeyboardModifiers(details.originalEvent ?? {})</span>
&nbsp;
          // 检查是否应该清空选择
<span class="cstat-no" title="statement not covered" >          if (shouldClearSelectionOnBackgroundClick(modifiers)) {</span>
<span class="cstat-no" title="statement not covered" >            debug('handleSvgMouseUp: Simple click on background, clearing selection.')</span>
<span class="cstat-no" title="statement not covered" >            if (typeof onElementSelect === 'function') {</span>
<span class="cstat-no" title="statement not covered" >              onElementSelect(null, false, 'clear')</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          else {</span>
<span class="cstat-no" title="statement not covered" >            debug('handleSvgMouseUp: Simple click on background with multi-select key, keeping selection.')</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
        // 总是重置拖动状态和清理document事件监听器
<span class="cstat-no" title="statement not covered" >        cleanupDocumentEventListeners()</span>
<span class="cstat-no" title="statement not covered" >        setDragStartWorld(null)</span>
<span class="cstat-no" title="statement not covered" >        setCurrentSelectionBox(null)</span>
&nbsp;
        // 总是调用传入的鼠标抬起处理函数
<span class="cstat-no" title="statement not covered" >        if (typeof onMouseUpProp === 'function') {</span>
<span class="cstat-no" title="statement not covered" >          onMouseUpProp(details)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      catch (error: unknown) {</span>
<span class="cstat-no" title="statement not covered" >        if (error instanceof Error) {</span>
<span class="cstat-no" title="statement not covered" >          debug('Error in handleSvgMouseUp creating/using event details:', error.message)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        else {</span>
<span class="cstat-no" title="statement not covered" >          debug('Unknown error in handleSvgMouseUp:', error)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }, [isPanMode, isDrawMode, doEndPan, createMouseEventDetails, dragStartWorld, currentSelectionBox, elements, onElementSelect, onMouseUpProp, cleanupDocumentEventListeners, selectedElementIds])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const handleSvgMouseLeave = useCallback((event: React.MouseEvent&lt;SVGElement&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      debug('handleSvgMouseLeave. isPanMode:', isPanMode, 'isDrawMode:', isDrawMode, 'isPanningRef.current:', isPanningRef.current)</span>
&nbsp;
      // 如果处于平移模式且正在平移，结束平移
<span class="cstat-no" title="statement not covered" >      if (isPanMode &amp;&amp; isPanningRef.current &amp;&amp; doEndPan) {</span>
<span class="cstat-no" title="statement not covered" >        debug('handleSvgMouseLeave: Calling doEndPan.')</span>
<span class="cstat-no" title="statement not covered" >        doEndPan()</span>
<span class="cstat-no" title="statement not covered" >        isPanningRef.current = false</span>
<span class="cstat-no" title="statement not covered" >        debug('handleSvgMouseLeave: isPanningRef.current set to false.')</span>
<span class="cstat-no" title="statement not covered" >        event.preventDefault()</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // 不再清理框选状态，因为现在使用document事件监听器来处理框选
      // 框选会在document mouseup时完成，而不是在鼠标离开SVG时
<span class="cstat-no" title="statement not covered" >      debug('handleSvgMouseLeave: Mouse left SVG, but marquee selection continues via document listeners.')</span>
<span class="cstat-no" title="statement not covered" >    }, [isPanMode, isDrawMode, doEndPan])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const handleSvgDragOver = (event: React.DragEvent&lt;SVGSVGElement&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      event.preventDefault()</span>
<span class="cstat-no" title="statement not covered" >      if (typeof onDragOver === 'function') {</span>
<span class="cstat-no" title="statement not covered" >        onDragOver(event)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const handleSvgDrop = useCallback(</span>
<span class="cstat-no" title="statement not covered" >      (event: React.DragEvent&lt;SVGSVGElement&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        event.preventDefault()</span>
<span class="cstat-no" title="statement not covered" >        event.stopPropagation()</span>
<span class="cstat-no" title="statement not covered" >        debug('handleSvgDrop: Entered function.')</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        const mouseDetails = createMouseEventDetails(event as React.MouseEvent&lt;SVGSVGElement&gt;, svgRef.current)</span>
<span class="cstat-no" title="statement not covered" >        debug(</span>
<span class="cstat-no" title="statement not covered" >          'handleSvgDrop: Raw mouse event details created. svgPosition:',</span>
<span class="cstat-no" title="statement not covered" >          mouseDetails.svgPosition,</span>
<span class="cstat-no" title="statement not covered" >          'worldPosition (from createMouseEventDetails):',</span>
<span class="cstat-no" title="statement not covered" >          mouseDetails.worldPosition,</span>
<span class="cstat-no" title="statement not covered" >        )</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (!svgRef.current) {</span>
<span class="cstat-no" title="statement not covered" >          debug('[Canvas] handleSvgDrop: svgRef.current is null, cannot proceed.')</span>
<span class="cstat-no" title="statement not covered" >          return</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        const rawData = event.dataTransfer.getData('application/json')</span>
<span class="cstat-no" title="statement not covered" >        debug('handleSvgDrop: Raw data from dataTransfer:', rawData)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (!rawData) {</span>
<span class="cstat-no" title="statement not covered" >          debug('[Canvas] handleSvgDrop: No data transferred.')</span>
<span class="cstat-no" title="statement not covered" >          return</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        try {</span>
<span class="cstat-no" title="statement not covered" >          const parsedAssetData = JSON.parse(rawData) as {</span>
            elementType: string
            properties?: Record&lt;string, unknown&gt;
            id?: string
            name?: string
            elementSpecificData?: Record&lt;string, unknown&gt;
          }
<span class="cstat-no" title="statement not covered" >          debug('handleSvgDrop: Parsed droppedAssetData:', parsedAssetData)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          let trueWorldPosition: Point</span>
<span class="cstat-no" title="statement not covered" >          if (</span>
<span class="cstat-no" title="statement not covered" >            mouseDetails.svgPosition != null</span>
<span class="cstat-no" title="statement not covered" >            &amp;&amp; pan != null</span>
<span class="cstat-no" title="statement not covered" >            &amp;&amp; typeof mouseDetails.svgPosition.x === 'number'</span>
<span class="cstat-no" title="statement not covered" >            &amp;&amp; typeof mouseDetails.svgPosition.y === 'number'</span>
<span class="cstat-no" title="statement not covered" >            &amp;&amp; typeof pan.x === 'number'</span>
<span class="cstat-no" title="statement not covered" >            &amp;&amp; typeof pan.y === 'number'</span>
<span class="cstat-no" title="statement not covered" >            &amp;&amp; typeof zoom === 'number'</span>
<span class="cstat-no" title="statement not covered" >            &amp;&amp; zoom !== 0</span>
<span class="cstat-no" title="statement not covered" >          ) {</span>
            // 计算真实世界坐标 - 这是鼠标落点的精确位置
<span class="cstat-no" title="statement not covered" >            trueWorldPosition = {</span>
<span class="cstat-no" title="statement not covered" >              x: (mouseDetails.svgPosition.x - pan.x) / zoom,</span>
<span class="cstat-no" title="statement not covered" >              y: (mouseDetails.svgPosition.y - pan.y) / zoom,</span>
<span class="cstat-no" title="statement not covered" >            }</span>
&nbsp;
            // 确保多边形的中心点就是鼠标落点位置
            // 多边形的points是相对于原点(0,0)的，position是中心点
&nbsp;
<span class="cstat-no" title="statement not covered" >            debug(</span>
<span class="cstat-no" title="statement not covered" >              'handleSvgDrop: Recalculated trueWorldPosition:',</span>
<span class="cstat-no" title="statement not covered" >              trueWorldPosition,</span>
<span class="cstat-no" title="statement not covered" >              'from svgPosition:',</span>
<span class="cstat-no" title="statement not covered" >              mouseDetails.svgPosition,</span>
<span class="cstat-no" title="statement not covered" >              'pan:',</span>
<span class="cstat-no" title="statement not covered" >              pan,</span>
<span class="cstat-no" title="statement not covered" >              'zoom:',</span>
<span class="cstat-no" title="statement not covered" >              zoom,</span>
<span class="cstat-no" title="statement not covered" >            )</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          else {</span>
<span class="cstat-no" title="statement not covered" >            debug(</span>
<span class="cstat-no" title="statement not covered" >              '[Canvas] handleSvgDrop: Using potentially incorrect mouseEventDetails.worldPosition due to missing svgPosition, pan, or zoom.',</span>
<span class="cstat-no" title="statement not covered" >              { svgPos: mouseDetails.svgPosition, panVal: pan, zoomVal: zoom },</span>
<span class="cstat-no" title="statement not covered" >            )</span>
<span class="cstat-no" title="statement not covered" >            trueWorldPosition = mouseDetails.worldPosition ?? { x: 0, y: 0 }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          debug('handleSvgDrop: typeof onElementAdd:', typeof onElementAdd)</span>
<span class="cstat-no" title="statement not covered" >          if (typeof onElementAdd === 'function') {</span>
<span class="cstat-no" title="statement not covered" >            debug('handleSvgDrop: Calling onElementAdd with asset and trueWorldPosition.')</span>
<span class="cstat-no" title="statement not covered" >            onElementAdd(parsedAssetData, trueWorldPosition)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (typeof onDropProp === 'function') {</span>
<span class="cstat-no" title="statement not covered" >            debug('handleSvgDrop: Calling legacy onDropProp.')</span>
<span class="cstat-no" title="statement not covered" >            onDropProp(mouseDetails)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (typeof onShapeDrop === 'function') {</span>
<span class="cstat-no" title="statement not covered" >            debug('handleSvgDrop: Calling onShapeDrop.')</span>
<span class="cstat-no" title="statement not covered" >            onShapeDrop(event, parsedAssetData)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        catch (error) {</span>
<span class="cstat-no" title="statement not covered" >          debug('[Canvas] handleSvgDrop: Error parsing data or calling handlers:', error)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >      [createMouseEventDetails, onElementAdd, onDropProp, onShapeDrop, pan, zoom, svgRef],</span>
<span class="cstat-no" title="statement not covered" >    )</span>
&nbsp;
    // 监听 showGrid 变化，强制重新渲染
<span class="cstat-no" title="statement not covered" >    useEffect(() =&gt; {</span>
      // 当 showGrid 变化时，强制重新渲染
<span class="cstat-no" title="statement not covered" >      if (mainGroupRef.current) {</span>
        // 触发一个微小的变化，强制 SVG 重新渲染
<span class="cstat-no" title="statement not covered" >        const currentTransform = mainGroupRef.current.getAttribute('transform')</span>
<span class="cstat-no" title="statement not covered" >        mainGroupRef.current.setAttribute('transform', currentTransform ?? '')</span>
&nbsp;
        // 如果网格应该显示，确保它被正确渲染
<span class="cstat-no" title="statement not covered" >        if (showGrid) {</span>
          // 强制重新计算布局
<span class="cstat-no" title="statement not covered" >          const timeoutId = setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            if (mainGroupRef.current) {</span>
              // 再次触发变化，确保网格被渲染
<span class="cstat-no" title="statement not covered" >              mainGroupRef.current.setAttribute('transform', currentTransform ?? '')</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }, 50)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          return () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            clearTimeout(timeoutId)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }, [showGrid])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    useEffect(() =&gt; {</span>
      // This effect is intentionally empty but needed for dependency tracking
      // It ensures the component re-renders when these dependencies change
<span class="cstat-no" title="statement not covered" >    }, [visibleAndSortedElements, selectedElementIds, zoom])</span>
&nbsp;
    // 注意：网格的渲染逻辑已移至 CanvasGrid 组件内部
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="relative w-full h-full overflow-hidden" ref={canvasWrapperRef}&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;svg</span>
<span class="cstat-no" title="statement not covered" >          ref={svgRef}</span>
<span class="cstat-no" title="statement not covered" >          className="w-full h-full bg-gray-100 dark:bg-gray-800 cursor-default select-none"</span>
<span class="cstat-no" title="statement not covered" >          onMouseDown={handleSvgMouseDown}</span>
<span class="cstat-no" title="statement not covered" >          onMouseMove={handleSvgMouseMove}</span>
<span class="cstat-no" title="statement not covered" >          onMouseUp={handleSvgMouseUp}</span>
<span class="cstat-no" title="statement not covered" >          onMouseLeave={handleSvgMouseLeave}</span>
<span class="cstat-no" title="statement not covered" >          onWheel={onWheel as React.WheelEventHandler&lt;SVGSVGElement&gt;}</span>
<span class="cstat-no" title="statement not covered" >          onDragOver={handleSvgDragOver}</span>
<span class="cstat-no" title="statement not covered" >          onDrop={handleSvgDrop}</span>
        &gt;
<span class="cstat-no" title="statement not covered" >          &lt;g ref={mainGroupRef}&gt;</span>
            {/* 直接在Canvas组件中渲染网格 */}
<span class="cstat-no" title="statement not covered" >            {showGrid &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >              &lt;g className="canvas-grid-lines"&gt;</span>
<span class="cstat-no" title="statement not covered" >                {(() =&gt; {</span>
                  // 只有在showGrid为true且所有必要参数有效时才渲染网格
<span class="cstat-no" title="statement not covered" >                  if (!gridSize || !zoom || !canvasSize.width || !canvasSize.height) {</span>
<span class="cstat-no" title="statement not covered" >                    return null</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
&nbsp;
                  // 确保pan对象有效
<span class="cstat-no" title="statement not covered" >                  if (typeof pan.x !== 'number' || typeof pan.y !== 'number') {</span>
<span class="cstat-no" title="statement not covered" >                    return null</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
&nbsp;
                  // 计算有效网格尺寸 - 改进低缩放级别的处理
<span class="cstat-no" title="statement not covered" >                  let effectiveGridSize = gridSize</span>
<span class="cstat-no" title="statement not covered" >                  const multipliers = [1, 5, 10, 50, 100, 500] // 增加更大的乘数</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                  for (const multiplier of multipliers) {</span>
<span class="cstat-no" title="statement not covered" >                    const potentialGridSize = gridSize * multiplier</span>
<span class="cstat-no" title="statement not covered" >                    const cellVisibleSizePx = potentialGridSize * zoom</span>
&nbsp;
                    // 在极低缩放级别(1%-10%)时，使用更大的最小像素要求
<span class="cstat-no" title="statement not covered" >                    const minPixelSize = zoom &lt; 0.1 ? 50 : 30</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                    if (cellVisibleSizePx &gt;= minPixelSize) {</span>
<span class="cstat-no" title="statement not covered" >                      effectiveGridSize = potentialGridSize</span>
<span class="cstat-no" title="statement not covered" >                      break</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
&nbsp;
                  // 计算视口坐标
<span class="cstat-no" title="statement not covered" >                  const viewBoxX = -pan.x / zoom</span>
<span class="cstat-no" title="statement not covered" >                  const viewBoxY = -pan.y / zoom</span>
<span class="cstat-no" title="statement not covered" >                  const viewBoxWidth = canvasSize.width / zoom</span>
<span class="cstat-no" title="statement not covered" >                  const viewBoxHeight = canvasSize.height / zoom</span>
&nbsp;
                  // 添加边距避免边缘问题
<span class="cstat-no" title="statement not covered" >                  const padding = effectiveGridSize * 2</span>
<span class="cstat-no" title="statement not covered" >                  const rectX = viewBoxX - padding</span>
<span class="cstat-no" title="statement not covered" >                  const rectY = viewBoxY - padding</span>
<span class="cstat-no" title="statement not covered" >                  const rectWidth = viewBoxWidth + (padding * 2)</span>
<span class="cstat-no" title="statement not covered" >                  const rectHeight = viewBoxHeight + (padding * 2)</span>
&nbsp;
                  // 计算网格线数量
<span class="cstat-no" title="statement not covered" >                  const horizontalLines = Math.ceil(rectHeight / effectiveGridSize) + 1</span>
<span class="cstat-no" title="statement not covered" >                  const verticalLines = Math.ceil(rectWidth / effectiveGridSize) + 1</span>
&nbsp;
                  // 计算起始位置（对齐到网格）
<span class="cstat-no" title="statement not covered" >                  const startX = Math.floor(rectX / effectiveGridSize) * effectiveGridSize</span>
<span class="cstat-no" title="statement not covered" >                  const startY = Math.floor(rectY / effectiveGridSize) * effectiveGridSize</span>
&nbsp;
                  // 创建网格线
<span class="cstat-no" title="statement not covered" >                  const gridLines = []</span>
&nbsp;
                  // 水平线
<span class="cstat-no" title="statement not covered" >                  for (let i = 0; i &lt; horizontalLines; i++) {</span>
<span class="cstat-no" title="statement not covered" >                    const y = startY + i * effectiveGridSize</span>
<span class="cstat-no" title="statement not covered" >                    gridLines.push(</span>
<span class="cstat-no" title="statement not covered" >                      &lt;line</span>
<span class="cstat-no" title="statement not covered" >                        key={`h-${i}`}</span>
<span class="cstat-no" title="statement not covered" >                        x1={rectX}</span>
<span class="cstat-no" title="statement not covered" >                        y1={y}</span>
<span class="cstat-no" title="statement not covered" >                        x2={rectX + rectWidth}</span>
<span class="cstat-no" title="statement not covered" >                        y2={y}</span>
<span class="cstat-no" title="statement not covered" >                        stroke="rgba(128,128,128,0.3)"</span>
<span class="cstat-no" title="statement not covered" >                        strokeWidth={0.5 / zoom}</span>
<span class="cstat-no" title="statement not covered" >                      /&gt;,</span>
<span class="cstat-no" title="statement not covered" >                    )</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
&nbsp;
                  // 垂直线
<span class="cstat-no" title="statement not covered" >                  for (let i = 0; i &lt; verticalLines; i++) {</span>
<span class="cstat-no" title="statement not covered" >                    const x = startX + i * effectiveGridSize</span>
<span class="cstat-no" title="statement not covered" >                    gridLines.push(</span>
<span class="cstat-no" title="statement not covered" >                      &lt;line</span>
<span class="cstat-no" title="statement not covered" >                        key={`v-${i}`}</span>
<span class="cstat-no" title="statement not covered" >                        x1={x}</span>
<span class="cstat-no" title="statement not covered" >                        y1={rectY}</span>
<span class="cstat-no" title="statement not covered" >                        x2={x}</span>
<span class="cstat-no" title="statement not covered" >                        y2={rectY + rectHeight}</span>
<span class="cstat-no" title="statement not covered" >                        stroke="rgba(128,128,128,0.3)"</span>
<span class="cstat-no" title="statement not covered" >                        strokeWidth={0.5 / zoom}</span>
<span class="cstat-no" title="statement not covered" >                      /&gt;,</span>
<span class="cstat-no" title="statement not covered" >                    )</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                  return gridLines</span>
<span class="cstat-no" title="statement not covered" >                })()}</span>
<span class="cstat-no" title="statement not covered" >              &lt;/g&gt;</span>
            )}
<span class="cstat-no" title="statement not covered" >            &lt;g ref={shapesLayerRef} className="shapes-layer"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;ShapeRenderer</span>
<span class="cstat-no" title="statement not covered" >                shapesLayerRef={shapesLayerRef}</span>
<span class="cstat-no" title="statement not covered" >                shapes={visibleAndSortedElements}</span>
<span class="cstat-no" title="statement not covered" >                selectedIds={selectedElementIds}</span>
<span class="cstat-no" title="statement not covered" >                currentZoom={zoom}</span>
<span class="cstat-no" title="statement not covered" >                onShapeSelected={(elementId: string | string[] | null, isMultiSelect?: boolean) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                  debug('ShapeRenderer onShapeSelected called:', { elementId, isMultiSelect })</span>
<span class="cstat-no" title="statement not covered" >                  if (typeof onElementSelect === 'function') {</span>
<span class="cstat-no" title="statement not covered" >                    onElementSelect(elementId, isMultiSelect, isMultiSelect ? 'toggle' : 'replace')</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                }}</span>
<span class="cstat-no" title="statement not covered" >                isPanMode={isPanMode}</span>
<span class="cstat-no" title="statement not covered" >              /&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/g&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;g ref={interactionLayerRef} className="interaction-layer"&gt;</span>
              {/* 选择框 */}
<span class="cstat-no" title="statement not covered" >              {currentSelectionBox &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                &lt;rect</span>
<span class="cstat-no" title="statement not covered" >                  x={currentSelectionBox.x}</span>
<span class="cstat-no" title="statement not covered" >                  y={currentSelectionBox.y}</span>
<span class="cstat-no" title="statement not covered" >                  width={currentSelectionBox.width}</span>
<span class="cstat-no" title="statement not covered" >                  height={currentSelectionBox.height}</span>
<span class="cstat-no" title="statement not covered" >                  fill="rgba(0, 120, 255, 0.2)"</span>
<span class="cstat-no" title="statement not covered" >                  stroke="rgba(0, 120, 255, 0.8)"</span>
<span class="cstat-no" title="statement not covered" >                  strokeWidth={1 / (zoom !== 0 ? zoom : 1)}</span>
<span class="cstat-no" title="statement not covered" >                  pointerEvents="none"</span>
<span class="cstat-no" title="statement not covered" >                /&gt;</span>
              )}
&nbsp;
              {/* 路径预览 */}
<span class="cstat-no" title="statement not covered" >              &lt;PathPreview</span>
<span class="cstat-no" title="statement not covered" >                isDrawing={pathDrawStateProp?.isDrawing ?? false}</span>
<span class="cstat-no" title="statement not covered" >                pathType={pathDrawStateProp?.pathType ?? null}</span>
<span class="cstat-no" title="statement not covered" >                startPoint={pathDrawStateProp?.startPoint ?? null}</span>
<span class="cstat-no" title="statement not covered" >                currentPoint={pathDrawStateProp?.currentPoint ?? null}</span>
<span class="cstat-no" title="statement not covered" >                allPoints={pathDrawStateProp?.allPoints ?? []}</span>
<span class="cstat-no" title="statement not covered" >                clickCount={pathDrawStateProp?.clickCount ?? 0}</span>
<span class="cstat-no" title="statement not covered" >              /&gt;</span>
&nbsp;
              {/* 路径绘制处理已移至EditorLayout，这里不再需要PathDrawHandler */}
<span class="cstat-no" title="statement not covered" >            &lt;/g&gt;</span>
&nbsp;
            {/* 边界框和控制手柄层 */}
<span class="cstat-no" title="statement not covered" >            &lt;g ref={handlesLayerRef} className="handles-layer"&gt;</span>
              {/* HandlesRenderer will be rendered via useEffect */}
<span class="cstat-no" title="statement not covered" >            &lt;/g&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/g&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/svg&gt;</span>
        {/* Scale bar absolute positioning and animation */}
<span class="cstat-no" title="statement not covered" >        &lt;CanvasScaleBar</span>
<span class="cstat-no" title="statement not covered" >          zoom={zoom}</span>
<span class="cstat-no" title="statement not covered" >          pixelsPerMM={CANVAS_PIXELS_PER_MM}</span>
<span class="cstat-no" title="statement not covered" >          canvasWidth={canvasSize.width}</span>
<span class="cstat-no" title="statement not covered" >          position={isPropertySidebarOpen === true ? 'sidebar' : (sheetOpen ? 'left' : 'right')}</span>
<span class="cstat-no" title="statement not covered" >          offset={(() =&gt; {</span>
            // 计算比例尺的实际宽度
<span class="cstat-no" title="statement not covered" >            const { pixelLength } = calculateScaleBarInfo(zoom, CANVAS_PIXELS_PER_MM, canvasSize.width)</span>
<span class="cstat-no" title="statement not covered" >            const actualBarWidth = pixelLength + 4 // 加上左右两侧的TICK_WIDTH (2px each)</span>
&nbsp;
            // 设置安全边距
<span class="cstat-no" title="statement not covered" >            const barHeight = 60</span>
<span class="cstat-no" title="statement not covered" >            const sidebarWidth = 440 // 属性栏宽度</span>
<span class="cstat-no" title="statement not covered" >            const sidebarRightMargin = 8 // 属性栏右侧边距（从className='right-2'推断）</span>
<span class="cstat-no" title="statement not covered" >            const margin = 16 // 减小边距，给比例尺更多空间</span>
<span class="cstat-no" title="statement not covered" >            const drawerHeight = 56 // 底部抽屉收起时的高度（h-14 = 56px）</span>
<span class="cstat-no" title="statement not covered" >            const safetyGap = 20 // 比例尺和属性栏之间的安全间距</span>
&nbsp;
            // 计算属性栏左边缘的位置
<span class="cstat-no" title="statement not covered" >            const sidebarLeftEdge = isPropertySidebarOpen</span>
<span class="cstat-no" title="statement not covered" >              ? canvasSize.width - sidebarWidth - sidebarRightMargin</span>
<span class="cstat-no" title="statement not covered" >              : canvasSize.width</span>
&nbsp;
            // 默认位置：右下角
<span class="cstat-no" title="statement not covered" >            let x = canvasSize.width - margin - actualBarWidth</span>
<span class="cstat-no" title="statement not covered" >            const y = canvasSize.height - drawerHeight - barHeight</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (isPropertySidebarOpen === true) {</span>
              // 当属性栏打开时，将比例尺放在属性栏左侧，保持安全间距
<span class="cstat-no" title="statement not covered" >              x = sidebarLeftEdge - safetyGap - actualBarWidth</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            else if (sheetOpen) {</span>
              // 当sheet打开时，将比例尺放在左侧
<span class="cstat-no" title="statement not covered" >              x = margin</span>
<span class="cstat-no" title="statement not covered" >            }</span>
&nbsp;
            // 确保比例尺不会超出画布左边界
<span class="cstat-no" title="statement not covered" >            x = Math.max(margin, x)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            return { x, y }</span>
<span class="cstat-no" title="statement not covered" >          })()}</span>
<span class="cstat-no" title="statement not covered" >        /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="absolute bottom-2 left-2 p-1 bg-gray-700 bg-opacity-50 text-white text-xs rounded"&gt;</span>
<span class="cstat-no" title="statement not covered" >          {`Zoom: ${(zoom * 100).toFixed(0)}% | Pan: (${pan.x.toFixed(0)}, ${pan.y.toFixed(0)})`}</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
    )
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >Canvas.displayName = 'Canvas'</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default Canvas</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:58:01.486Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    