import type {
  BaseEvent,
  BaseEventWithTimestamp,
  EventBus,
  EventBusConfig,
  EventHandler,
  EventHandlerWithOptions,
  EventSubscriptionOptions,
  ExtendedEventBus,
  TypedEvent,
} from '@/types/services/events/eventCore'
import { describe, expect, it, vi } from 'vitest'
import { AppEventType } from '@/types/services/events/eventTypes'

describe('event Core Types', () => {
  describe('baseEvent', () => {
    it('should create a valid BaseEvent instance', () => {
      const event: BaseEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle' },
      }

      expect(event.type).toBe(AppEventType.SHAPE_CREATE_REQUEST)
      expect(event.payload).toEqual({ ElementType: 'rectangle' })
      expect(event.timestamp).toBeUndefined()
    })

    it('should allow creating events with custom timestamp', () => {
      const customTimestamp = 1620000000000
      const event: BaseEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle' },
        timestamp: customTimestamp,
      }

      expect(event.type).toBe(AppEventType.SHAPE_CREATE_REQUEST)
      expect(event.payload).toEqual({ ElementType: 'rectangle' })
      expect(event.timestamp).toBe(customTimestamp)
    })
  })

  describe('baseEventWithTimestamp', () => {
    it('should require a timestamp', () => {
      const event: BaseEventWithTimestamp = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle' },
        timestamp: Date.now(),
      }

      expect(event.timestamp).toBeDefined()
      expect(typeof event.timestamp).toBe('number')
    })
  })

  describe('typedEvent', () => {
    it('should create a typed event with specific payload type', () => {
      interface RectanglePayload {
        ElementType: string
        width: number
        height: number
      }

      const event: TypedEvent<RectanglePayload> = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: 'rectangle',
          width: 100,
          height: 50,
        },
      }

      expect(event.type).toBe(AppEventType.SHAPE_CREATE_REQUEST)
      expect(event.payload.ElementType).toBe('rectangle')
      expect(event.payload.width).toBe(100)
      expect(event.payload.height).toBe(50)
    })
  })

  describe('eventHandler', () => {
    it('should create a valid event handler function', () => {
      const handler: EventHandler = (event: BaseEvent) => {
        return event.type === AppEventType.SHAPE_CREATE_REQUEST
      }

      const event: BaseEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle' },
      }

      expect(handler(event)).toBe(true)
    })

    it('should handle different event types correctly', () => {
      const handler: EventHandler = (event: BaseEvent) => {
        return event.type === AppEventType.SHAPE_CREATE_REQUEST
      }

      const event1: BaseEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle' },
      }

      const event2: BaseEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: { shapeId: '123' },
      }

      expect(handler(event1)).toBe(true)
      expect(handler(event2)).toBe(false)
    })
  })

  describe('eventSubscriptionOptions', () => {
    it('should create valid subscription options with once flag', () => {
      const options: EventSubscriptionOptions = {
        once: true,
      }

      expect(options.once).toBe(true)
      expect(options.async).toBeUndefined()
      expect(options.filter).toBeUndefined()
      expect(options.context).toBeUndefined()
    })

    it('should create valid subscription options with async flag', () => {
      const options: EventSubscriptionOptions = {
        async: true,
      }

      expect(options.async).toBe(true)
      expect(options.once).toBeUndefined()
      expect(options.filter).toBeUndefined()
      expect(options.context).toBeUndefined()
    })

    it('should create valid subscription options with filter function', () => {
      const filter = (event: BaseEvent) => event.type === AppEventType.SHAPE_CREATE_REQUEST
      const options: EventSubscriptionOptions = {
        filter,
      }

      expect(options.filter).toBe(filter)
      expect(options.once).toBeUndefined()
      expect(options.async).toBeUndefined()
      expect(options.context).toBeUndefined()
    })

    it('should create valid subscription options with context', () => {
      const context = { value: 42 }
      const options: EventSubscriptionOptions = {
        context,
      }

      expect(options.context).toBe(context)
      expect(options.once).toBeUndefined()
      expect(options.async).toBeUndefined()
      expect(options.filter).toBeUndefined()
    })

    it('should create valid subscription options with all properties', () => {
      const context = { value: 42 }
      const filter = (event: BaseEvent) => event.type === AppEventType.SHAPE_CREATE_REQUEST
      const options: EventSubscriptionOptions = {
        once: true,
        async: true,
        filter,
        context,
        priority: 10,
        description: 'Test subscription',
        debounce: 100,
        throttle: 200,
      }

      expect(options.once).toBe(true)
      expect(options.async).toBe(true)
      expect(options.filter).toBe(filter)
      expect(options.context).toBe(context)
      expect(options.priority).toBe(10)
      expect(options.description).toBe('Test subscription')
      expect(options.debounce).toBe(100)
      expect(options.throttle).toBe(200)
    })
  })

  describe('eventHandlerWithOptions', () => {
    it('should create a handler with options', () => {
      const handler: EventHandler = vi.fn()
      const options: EventSubscriptionOptions = {
        once: true,
        priority: 5,
      }

      const handlerWithOptions: EventHandlerWithOptions = {
        handler,
        options,
      }

      expect(handlerWithOptions.handler).toBe(handler)
      expect(handlerWithOptions.options).toBe(options)
      expect(handlerWithOptions.options.once).toBe(true)
      expect(handlerWithOptions.options.priority).toBe(5)
    })
  })

  describe('eventBusConfig', () => {
    it('should create a valid event bus configuration', () => {
      const config: EventBusConfig = {
        enableLogging: true,
        defaultPriority: 0,
        maxAsyncHandlers: 10,
        defaultDebounceTime: 100,
        defaultThrottleTime: 200,
        enablePerformanceTracking: true,
        maxHandlersPerEventType: 50,
      }

      expect(config.enableLogging).toBe(true)
      expect(config.defaultPriority).toBe(0)
      expect(config.maxAsyncHandlers).toBe(10)
      expect(config.defaultDebounceTime).toBe(100)
      expect(config.defaultThrottleTime).toBe(200)
      expect(config.enablePerformanceTracking).toBe(true)
      expect(config.maxHandlersPerEventType).toBe(50)
    })
  })

  describe('eventBus', () => {
    it('should define the required methods', () => {
      // Create a mock implementation of EventBus to verify the interface
      const mockEventBus: EventBus = {
        subscribe: vi.fn(),
        publish: vi.fn(),
        unsubscribe: vi.fn(),
        unsubscribeAll: vi.fn(),
        clear: vi.fn(),
        reset: vi.fn(),
        getSubscriptions: vi.fn(),
      }

      expect(typeof mockEventBus.subscribe).toBe('function')
      expect(typeof mockEventBus.publish).toBe('function')
      expect(typeof mockEventBus.unsubscribe).toBe('function')
      expect(typeof mockEventBus.unsubscribeAll).toBe('function')
      expect(typeof mockEventBus.clear).toBe('function')
      expect(typeof mockEventBus.reset).toBe('function')
      expect(typeof mockEventBus.getSubscriptions).toBe('function')
    })
  })

  describe('extendedEventBus', () => {
    it('should define the required methods including extended ones', () => {
      // Create a mock implementation of ExtendedEventBus to verify the interface
      const mockExtendedEventBus: ExtendedEventBus = {
        subscribe: vi.fn(),
        publish: vi.fn(),
        unsubscribe: vi.fn(),
        unsubscribeAll: vi.fn(),
        clear: vi.fn(),
        reset: vi.fn(),
        getSubscriptions: vi.fn(),
        configure: vi.fn(),
        on: vi.fn(),
        off: vi.fn(),
        emit: vi.fn(),
        publishAsync: vi.fn(),
        emitAsync: vi.fn(),
        once: vi.fn(),
      }

      expect(typeof mockExtendedEventBus.subscribe).toBe('function')
      expect(typeof mockExtendedEventBus.publish).toBe('function')
      expect(typeof mockExtendedEventBus.unsubscribe).toBe('function')
      expect(typeof mockExtendedEventBus.unsubscribeAll).toBe('function')
      expect(typeof mockExtendedEventBus.clear).toBe('function')
      expect(typeof mockExtendedEventBus.reset).toBe('function')
      expect(typeof mockExtendedEventBus.getSubscriptions).toBe('function')
      expect(typeof mockExtendedEventBus.configure).toBe('function')
      expect(typeof mockExtendedEventBus.on).toBe('function')
      expect(typeof mockExtendedEventBus.off).toBe('function')
      expect(typeof mockExtendedEventBus.emit).toBe('function')
      expect(typeof mockExtendedEventBus.publishAsync).toBe('function')
      expect(typeof mockExtendedEventBus.emitAsync).toBe('function')
      expect(typeof mockExtendedEventBus.once).toBe('function')
    })
  })
})
