/**
 * Store Index
 *
 * This file serves as the central entry point for all store hooks in the application.
 * It consolidates and re-exports store hooks from individual store modules,
 * making them available through a single import statement.
 *
 * This approach simplifies imports in components and maintains a clean
 * separation of concerns while providing a unified API for state management.
 */

export * from './canvasStore' // Exports useCanvasStore and CanvasState
// Export all store hooks from this central file
export * from './shapesStore'
export * from './uiStore'
// Note: historyStore is integrated into shapesStore via temporal middleware
// You can access history controls via useShapesHistory() from shapesStore.ts
// export * from './historyStore'; // Add later
