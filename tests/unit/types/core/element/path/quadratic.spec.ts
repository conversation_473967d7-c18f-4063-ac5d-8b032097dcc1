import type { Quadratic } from '@/types/core/element/path/quadraticPathTypes'
import type { ShapeElement } from '@/types/core/elementDefinitions'

import { describe, expect, it } from 'vitest'

describe('quadratic Interface', () => {
  it('should define a quadratic bezier curve with required properties', () => {
    // Create a valid Quadratic object
    const quadratic: Quadratic = {
      id: 'quadratic-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 0 },
      control: { x: 100, y: 100 },
      end: { x: 200, y: 0 },
    }

    // Verify the object is valid
    expect(quadratic.id).toBe('quadratic-1')
    expect(quadratic.type).toBe('shape')
    expect(quadratic.start.x).toBe(0)
    expect(quadratic.start.y).toBe(0)
    expect(quadratic.control.x).toBe(100)
    expect(quadratic.control.y).toBe(100)
    expect(quadratic.end.x).toBe(200)
    expect(quadratic.end.y).toBe(0)
  })

  it('should extend ShapeElement interface', () => {
    // Create a quadratic bezier curve
    const quadratic: Quadratic = {
      id: 'quadratic-2',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 10, y: 10 },
      control: { x: 110, y: 110 },
      end: { x: 210, y: 10 },
    }

    // Verify the quadratic extends ShapeElement
    const shapeElement: ShapeElement = quadratic
    expect(shapeElement.id).toBe('quadratic-2')
    expect(shapeElement.type).toBe('shape')
  })

  it('should allow creating quadratic curves with different control points', () => {
    // Create quadratic curves with different control points
    const curve1: Quadratic = {
      id: 'curve-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 0 },
      control: { x: 100, y: 100 },
      end: { x: 200, y: 0 },
    }

    const curve2: Quadratic = {
      id: 'curve-2',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 0 },
      control: { x: 100, y: -50 },
      end: { x: 200, y: 0 },
    }

    const curve3: Quadratic = {
      id: 'curve-3',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 0 },
      control: { x: 200, y: 100 },
      end: { x: 200, y: 0 },
    }

    // Verify all curves are valid
    expect(curve1.start.x).toBe(0)
    expect(curve1.start.y).toBe(0)
    expect(curve1.control.x).toBe(100)
    expect(curve1.control.y).toBe(100)
    expect(curve1.end.x).toBe(200)
    expect(curve1.end.y).toBe(0)

    expect(curve2.start.x).toBe(0)
    expect(curve2.start.y).toBe(0)
    expect(curve2.control.x).toBe(100)
    expect(curve2.control.y).toBe(-50)
    expect(curve2.end.x).toBe(200)
    expect(curve2.end.y).toBe(0)

    expect(curve3.start.x).toBe(0)
    expect(curve3.start.y).toBe(0)
    expect(curve3.control.x).toBe(200)
    expect(curve3.control.y).toBe(100)
    expect(curve3.end.x).toBe(200)
    expect(curve3.end.y).toBe(0)
  })

  it('should be usable in arrays and collections', () => {
    // Create an array of quadratic curves
    const curves: Quadratic[] = [
      {
        id: 'quadratic-3',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 100, y: 100 },
        rotation: 0,

        selectable: true,
        draggable: true,
        showHandles: true,
        start: { x: 0, y: 0 },
        control: { x: 100, y: 100 },
        end: { x: 200, y: 0 },
      },
      {
        id: 'quadratic-4',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 100, y: 100 },
        rotation: 0,

        selectable: true,
        draggable: true,
        showHandles: true,
        start: { x: 0, y: 0 },
        control: { x: 100, y: -50 },
        end: { x: 200, y: 0 },
      },
    ]

    // Verify the array is valid
    expect(curves.length).toBe(2)
    expect(curves[0].id).toBe('quadratic-3')
    expect(curves[0].start.x).toBe(0)
    expect(curves[0].start.y).toBe(0)
    expect(curves[0].control.x).toBe(100)
    expect(curves[0].control.y).toBe(100)

    expect(curves[1].id).toBe('quadratic-4')
    expect(curves[1].start.x).toBe(0)
    expect(curves[1].start.y).toBe(0)
    expect(curves[1].control.x).toBe(100)
    expect(curves[1].control.y).toBe(-50)
  })

  it('should be usable in functions that require Quadratic', () => {
    // Define a function that uses Quadratic
    function calculateQuadraticCurveLength(quadratic: Quadratic): number {
      // Simple approximation of curve length by dividing into segments
      const segments = 10
      let length = 0
      let prevX = quadratic.start.x
      let prevY = quadratic.start.y

      for (let i = 1; i <= segments; i++) {
        const t = i / segments
        const oneMinusT = 1 - t

        // Quadratic Bezier formula
        const x = oneMinusT ** 2 * quadratic.start.x
          + 2 * oneMinusT * t * quadratic.control.x
          + t ** 2 * quadratic.end.x

        const y = oneMinusT ** 2 * quadratic.start.y
          + 2 * oneMinusT * t * quadratic.control.y
          + t ** 2 * quadratic.end.y

        // Calculate segment length
        const dx = x - prevX
        const dy = y - prevY
        length += Math.sqrt(dx * dx + dy * dy)

        prevX = x
        prevY = y
      }

      return length
    }

    // Create a quadratic curve
    const quadratic: Quadratic = {
      id: 'quadratic-5',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 0 },
      control: { x: 100, y: 100 },
      end: { x: 200, y: 0 },
    }

    // Test the function
    const length = calculateQuadraticCurveLength(quadratic)
    expect(length).toBeGreaterThan(0)
  })

  it('should be compatible with JSON serialization/deserialization', () => {
    // Create a quadratic curve
    const quadratic: Quadratic = {
      id: 'quadratic-6',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 0 },
      control: { x: 100, y: 100 },
      end: { x: 200, y: 0 },
    }

    // Serialize to JSON and deserialize
    const serialized = JSON.stringify(quadratic)
    const deserialized = JSON.parse(serialized) as Quadratic

    // Verify the deserialized object is valid
    expect(deserialized.id).toBe('quadratic-6')
    expect(deserialized.type).toBe('shape')
    expect(deserialized.start.x).toBe(0)
    expect(deserialized.start.y).toBe(0)
    expect(deserialized.control.x).toBe(100)
    expect(deserialized.control.y).toBe(100)
    expect(deserialized.end.x).toBe(200)
    expect(deserialized.end.y).toBe(0)
  })
})
