/**
 * @file ConsoleLogger.spec.ts
 * @description Unit tests for ConsoleLogger class
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ConsoleLogger } from '@/core/services/logger/ConsoleLogger'
import { LogLevel } from '@/core/services/logger/LogLevel'

describe('consoleLogger', () => {
  let logger: ConsoleLogger
  let consoleInfoSpy: any
  let consoleWarnSpy: any
  let consoleErrorSpy: any
  let consoleDebugSpy: any

  beforeEach(() => {
    // Spy on console methods
    consoleInfoSpy = vi.spyOn(console, 'info').mockImplementation(() => {})
    consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
    consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    consoleDebugSpy = vi.spyOn(console, 'debug').mockImplementation(() => {})

    // Create logger with default log level (INFO)
    logger = new ConsoleLogger()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('constructor', () => {
    it('should set default log level to INFO if not provided', () => {
      expect(logger.getLogLevel()).toBe(LogLevel.INFO)
    })

    it('should set log level to provided value', () => {
      const debugLogger = new ConsoleLogger(LogLevel.DEBUG)
      expect(debugLogger.getLogLevel()).toBe(LogLevel.DEBUG)
    })
  })

  describe('setLogLevel', () => {
    it('should update the log level', () => {
      logger.setLogLevel(LogLevel.ERROR)
      expect(logger.getLogLevel()).toBe(LogLevel.ERROR)
    })
  })

  describe('info', () => {
    it('should log info message when log level is INFO', () => {
      logger.setLogLevel(LogLevel.INFO)
      logger.info('Test info message')
      expect(consoleInfoSpy).toHaveBeenCalledWith('[INFO]', 'Test info message')
    })

    it('should log info message when log level is DEBUG', () => {
      logger.setLogLevel(LogLevel.DEBUG)
      logger.info('Test info message')
      expect(consoleInfoSpy).toHaveBeenCalledWith('[INFO]', 'Test info message')
    })

    it('should not log info message when log level is WARN', () => {
      logger.setLogLevel(LogLevel.WARN)
      logger.info('Test info message')
      expect(consoleInfoSpy).not.toHaveBeenCalled()
    })

    it('should not log info message when log level is ERROR', () => {
      logger.setLogLevel(LogLevel.ERROR)
      logger.info('Test info message')
      expect(consoleInfoSpy).not.toHaveBeenCalled()
    })

    it('should not log info message when log level is NONE', () => {
      logger.setLogLevel(LogLevel.NONE)
      logger.info('Test info message')
      expect(consoleInfoSpy).not.toHaveBeenCalled()
    })

    it('should include context when provided', () => {
      const context = { userId: '123', action: 'login' }
      logger.info('Test info message', context)
      expect(consoleInfoSpy).toHaveBeenCalledWith('[INFO]', 'Test info message', context)
    })
  })

  describe('warn', () => {
    it('should log warn message when log level is WARN', () => {
      logger.setLogLevel(LogLevel.WARN)
      logger.warn('Test warn message')
      expect(consoleWarnSpy).toHaveBeenCalledWith('[WARN]', 'Test warn message')
    })

    it('should log warn message when log level is INFO', () => {
      logger.setLogLevel(LogLevel.INFO)
      logger.warn('Test warn message')
      expect(consoleWarnSpy).toHaveBeenCalledWith('[WARN]', 'Test warn message')
    })

    it('should log warn message when log level is DEBUG', () => {
      logger.setLogLevel(LogLevel.DEBUG)
      logger.warn('Test warn message')
      expect(consoleWarnSpy).toHaveBeenCalledWith('[WARN]', 'Test warn message')
    })

    it('should not log warn message when log level is ERROR', () => {
      logger.setLogLevel(LogLevel.ERROR)
      logger.warn('Test warn message')
      expect(consoleWarnSpy).not.toHaveBeenCalled()
    })

    it('should not log warn message when log level is NONE', () => {
      logger.setLogLevel(LogLevel.NONE)
      logger.warn('Test warn message')
      expect(consoleWarnSpy).not.toHaveBeenCalled()
    })

    it('should include context when provided', () => {
      const context = { userId: '123', action: 'login' }
      logger.warn('Test warn message', context)
      expect(consoleWarnSpy).toHaveBeenCalledWith('[WARN]', 'Test warn message', context)
    })
  })

  describe('error', () => {
    it('should log error message when log level is ERROR', () => {
      logger.setLogLevel(LogLevel.ERROR)
      logger.error('Test error message')
      expect(consoleErrorSpy).toHaveBeenCalledWith('[ERROR]', 'Test error message')
    })

    it('should log error message when log level is WARN', () => {
      logger.setLogLevel(LogLevel.WARN)
      logger.error('Test error message')
      expect(consoleErrorSpy).toHaveBeenCalledWith('[ERROR]', 'Test error message')
    })

    it('should log error message when log level is INFO', () => {
      logger.setLogLevel(LogLevel.INFO)
      logger.error('Test error message')
      expect(consoleErrorSpy).toHaveBeenCalledWith('[ERROR]', 'Test error message')
    })

    it('should log error message when log level is DEBUG', () => {
      logger.setLogLevel(LogLevel.DEBUG)
      logger.error('Test error message')
      expect(consoleErrorSpy).toHaveBeenCalledWith('[ERROR]', 'Test error message')
    })

    it('should not log error message when log level is NONE', () => {
      logger.setLogLevel(LogLevel.NONE)
      logger.error('Test error message')
      expect(consoleErrorSpy).not.toHaveBeenCalled()
    })

    it('should include context when provided', () => {
      const context = { userId: '123', action: 'login' }
      logger.error('Test error message', context)
      expect(consoleErrorSpy).toHaveBeenCalledWith('[ERROR]', 'Test error message', context)
    })

    it('should handle Error objects', () => {
      const error = new Error('Test error')
      logger.error('Operation failed', error)
      expect(consoleErrorSpy).toHaveBeenCalledWith('[ERROR]', 'Operation failed', error)
    })
  })

  describe('debug', () => {
    it('should log debug message when log level is DEBUG', () => {
      logger.setLogLevel(LogLevel.DEBUG)
      logger.debug('Test debug message')
      expect(consoleDebugSpy).toHaveBeenCalledWith('[DEBUG]', 'Test debug message')
    })

    it('should not log debug message when log level is INFO', () => {
      logger.setLogLevel(LogLevel.INFO)
      logger.debug('Test debug message')
      expect(consoleDebugSpy).not.toHaveBeenCalled()
    })

    it('should not log debug message when log level is WARN', () => {
      logger.setLogLevel(LogLevel.WARN)
      logger.debug('Test debug message')
      expect(consoleDebugSpy).not.toHaveBeenCalled()
    })

    it('should not log debug message when log level is ERROR', () => {
      logger.setLogLevel(LogLevel.ERROR)
      logger.debug('Test debug message')
      expect(consoleDebugSpy).not.toHaveBeenCalled()
    })

    it('should not log debug message when log level is NONE', () => {
      logger.setLogLevel(LogLevel.NONE)
      logger.debug('Test debug message')
      expect(consoleDebugSpy).not.toHaveBeenCalled()
    })

    it('should include context when provided', () => {
      logger.setLogLevel(LogLevel.DEBUG)
      const context = { userId: '123', action: 'login' }
      logger.debug('Test debug message', context)
      expect(consoleDebugSpy).toHaveBeenCalledWith('[DEBUG]', 'Test debug message', context)
    })
  })
})
