import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  getLayerDisplayName,
  getLayerColor,
} from '@/lib/utils/canvas/layerUtils'
import { CanvasLayer } from '@/types/core/canvas/layers'

describe('Layer Utils (Real Implementation)', () => {
  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Layer Display Names', () => {
    it('should return display name for valid layers', () => {
      expect(getLayerDisplayName(CanvasLayer.BACKGROUND)).toBe('Background')
      expect(getLayerDisplayName(CanvasLayer.GRID)).toBe('Grid')
      expect(getLayerDisplayName(CanvasLayer.SHAPES)).toBe('Shapes')
      expect(getLayerDisplayName(CanvasLayer.SELECTION)).toBe('Selection')
      expect(getLayerDisplayName(CanvasLayer.UI)).toBe('UI')
    })

    it('should return layer enum value for unknown layers', () => {
      const unknownLayer = 'UNKNOWN_LAYER' as CanvasLayer
      expect(getLayerDisplayName(unknownLayer)).toBe('UNKNOWN_LAYER')
    })

    it('should handle all defined canvas layers', () => {
      const allLayers = Object.values(CanvasLayer)
      
      allLayers.forEach(layer => {
        const displayName = getLayerDisplayName(layer)
        expect(typeof displayName).toBe('string')
        expect(displayName.length).toBeGreaterThan(0)
      })
    })

    it('should return consistent display names', () => {
      const layer = CanvasLayer.SHAPES
      const displayName1 = getLayerDisplayName(layer)
      const displayName2 = getLayerDisplayName(layer)
      expect(displayName1).toBe(displayName2)
    })
  })

  describe('Layer Colors', () => {
    it('should return color codes for valid layers', () => {
      const backgroundColorCode = getLayerColor(CanvasLayer.BACKGROUND)
      expect(backgroundColorCode).toMatch(/^#[0-9A-Fa-f]{6}$/)

      const gridColorCode = getLayerColor(CanvasLayer.GRID)
      expect(gridColorCode).toMatch(/^#[0-9A-Fa-f]{6}$/)

      const shapesColorCode = getLayerColor(CanvasLayer.SHAPES)
      expect(shapesColorCode).toMatch(/^#[0-9A-Fa-f]{6}$/)

      const selectionColorCode = getLayerColor(CanvasLayer.SELECTION)
      expect(selectionColorCode).toMatch(/^#[0-9A-Fa-f]{6}$/)

      const uiColorCode = getLayerColor(CanvasLayer.UI)
      expect(uiColorCode).toMatch(/^#[0-9A-Fa-f]{6}$/)
    })

    it('should return default black color for unknown layers', () => {
      const unknownLayer = 'UNKNOWN_LAYER' as CanvasLayer
      expect(getLayerColor(unknownLayer)).toBe('#000000')
    })

    it('should handle all defined canvas layers', () => {
      const allLayers = Object.values(CanvasLayer)
      
      allLayers.forEach(layer => {
        const color = getLayerColor(layer)
        expect(typeof color).toBe('string')
        expect(color).toMatch(/^#[0-9A-Fa-f]{6}$/)
      })
    })

    it('should return consistent colors', () => {
      const layer = CanvasLayer.SHAPES
      const color1 = getLayerColor(layer)
      const color2 = getLayerColor(layer)
      expect(color1).toBe(color2)
    })

    it('should return different colors for different layers', () => {
      const backgroundColorCode = getLayerColor(CanvasLayer.BACKGROUND)
      const shapesColorCode = getLayerColor(CanvasLayer.SHAPES)
      const selectionColorCode = getLayerColor(CanvasLayer.SELECTION)

      // Colors should be different (though this might not always be true)
      const colors = [backgroundColorCode, shapesColorCode, selectionColorCode]
      const uniqueColors = new Set(colors)
      
      // At least some colors should be different
      expect(uniqueColors.size).toBeGreaterThan(0)
    })
  })

  describe('Error Handling', () => {
    it('should handle null layer gracefully', () => {
      expect(() => getLayerDisplayName(null as any)).not.toThrow()
      expect(() => getLayerColor(null as any)).not.toThrow()
    })

    it('should handle undefined layer gracefully', () => {
      expect(() => getLayerDisplayName(undefined as any)).not.toThrow()
      expect(() => getLayerColor(undefined as any)).not.toThrow()
    })

    it('should handle empty string layer', () => {
      const emptyLayer = '' as CanvasLayer
      expect(getLayerDisplayName(emptyLayer)).toBe('')
      expect(getLayerColor(emptyLayer)).toBe('#000000')
    })

    it('should handle numeric layer values', () => {
      const numericLayer = 123 as any as CanvasLayer
      expect(() => getLayerDisplayName(numericLayer)).not.toThrow()
      expect(() => getLayerColor(numericLayer)).not.toThrow()
    })
  })

  describe('Layer Consistency', () => {
    it('should have display names for all layers that have colors', () => {
      const allLayers = Object.values(CanvasLayer)
      
      allLayers.forEach(layer => {
        const displayName = getLayerDisplayName(layer)
        const color = getLayerColor(layer)
        
        // If a layer has a specific color (not default), it should have a display name
        if (color !== '#000000') {
          expect(displayName).not.toBe(layer) // Should have a proper display name
        }
      })
    })

    it('should maintain layer order consistency', () => {
      const layers = [
        CanvasLayer.BACKGROUND,
        CanvasLayer.GRID,
        CanvasLayer.SHAPES,
        CanvasLayer.SELECTION,
        CanvasLayer.UI,
      ]

      layers.forEach(layer => {
        expect(getLayerDisplayName(layer)).toBeTruthy()
        expect(getLayerColor(layer)).toBeTruthy()
      })
    })
  })

  describe('Performance', () => {
    it('should handle many layer lookups efficiently', () => {
      const startTime = Date.now()
      
      for (let i = 0; i < 10000; i++) {
        getLayerDisplayName(CanvasLayer.SHAPES)
        getLayerColor(CanvasLayer.SHAPES)
        getLayerDisplayName(CanvasLayer.SELECTION)
        getLayerColor(CanvasLayer.SELECTION)
      }
      
      const endTime = Date.now()
      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly
    })

    it('should handle all layer types efficiently', () => {
      const allLayers = Object.values(CanvasLayer)
      const startTime = Date.now()
      
      for (let i = 0; i < 1000; i++) {
        allLayers.forEach(layer => {
          getLayerDisplayName(layer)
          getLayerColor(layer)
        })
      }
      
      const endTime = Date.now()
      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly
    })
  })

  describe('Layer Validation', () => {
    it('should validate layer enum values', () => {
      const validLayers = Object.values(CanvasLayer)
      
      validLayers.forEach(layer => {
        expect(typeof layer).toBe('string')
        expect(layer.length).toBeGreaterThan(0)
      })
    })

    it('should have unique layer values', () => {
      const allLayers = Object.values(CanvasLayer)
      const uniqueLayers = new Set(allLayers)
      
      expect(uniqueLayers.size).toBe(allLayers.length)
    })

    it('should follow naming conventions', () => {
      const allLayers = Object.values(CanvasLayer)
      
      allLayers.forEach(layer => {
        // Layer names should be uppercase with underscores
        expect(layer).toMatch(/^[A-Z_]+$/)
      })
    })
  })

  describe('Integration', () => {
    it('should work with layer enumeration', () => {
      const layerEntries = Object.entries(CanvasLayer)
      
      layerEntries.forEach(([key, value]) => {
        const displayName = getLayerDisplayName(value)
        const color = getLayerColor(value)
        
        expect(typeof displayName).toBe('string')
        expect(typeof color).toBe('string')
        expect(color).toMatch(/^#[0-9A-Fa-f]{6}$/)
      })
    })

    it('should support layer mapping operations', () => {
      const allLayers = Object.values(CanvasLayer)
      
      const layerInfo = allLayers.map(layer => ({
        layer,
        displayName: getLayerDisplayName(layer),
        color: getLayerColor(layer),
      }))
      
      expect(layerInfo).toHaveLength(allLayers.length)
      
      layerInfo.forEach(info => {
        expect(info.layer).toBeTruthy()
        expect(info.displayName).toBeTruthy()
        expect(info.color).toMatch(/^#[0-9A-Fa-f]{6}$/)
      })
    })

    it('should support layer filtering operations', () => {
      const allLayers = Object.values(CanvasLayer)
      
      const layersWithSpecificColors = allLayers.filter(layer => {
        const color = getLayerColor(layer)
        return color !== '#000000' // Not default color
      })
      
      expect(layersWithSpecificColors.length).toBeGreaterThan(0)
    })
  })

  describe('Edge Cases', () => {
    it('should handle case sensitivity', () => {
      const upperLayer = CanvasLayer.SHAPES
      const lowerLayer = CanvasLayer.SHAPES.toLowerCase() as CanvasLayer
      
      const upperDisplayName = getLayerDisplayName(upperLayer)
      const lowerDisplayName = getLayerDisplayName(lowerLayer)
      
      // Should handle case differences appropriately
      expect(typeof upperDisplayName).toBe('string')
      expect(typeof lowerDisplayName).toBe('string')
    })

    it('should handle special characters in layer names', () => {
      const specialLayer = 'LAYER_WITH-SPECIAL.CHARS' as CanvasLayer
      
      expect(() => getLayerDisplayName(specialLayer)).not.toThrow()
      expect(() => getLayerColor(specialLayer)).not.toThrow()
    })

    it('should handle very long layer names', () => {
      const longLayer = 'A'.repeat(1000) as CanvasLayer
      
      expect(() => getLayerDisplayName(longLayer)).not.toThrow()
      expect(() => getLayerColor(longLayer)).not.toThrow()
    })
  })
})
