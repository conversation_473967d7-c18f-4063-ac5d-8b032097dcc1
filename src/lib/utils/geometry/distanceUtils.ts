/**
 * Aggregated Distance Calculation Utilities
 *
 * @remarks
 * This module serves as a convenience barrel file that re-exports various distance
 * calculation functions from more specialized utility modules within the geometry package.
 * It aims to provide a single import point for common distance-related utilities.
 *
 * Functions are re-exported from:
 * - `./pointUtils`: For point-to-point, point-to-circle, point-to-ellipse distances.
 * - `./arcUtils`: For distances involving arcs (e.g., point-to-arc).
 * - `./coordinates`: For point-to-line segment distance.
 *
 * Future distance functions (e.g., line-to-line, polygon-to-polygon) would also be
 * re-exported from here.
 *
 * @module lib/utils/geometry/distanceUtils
 */

export {
  calculateArcArcDistance,
  calculateArcLineDistance,
  calculateArcPointDistance,
} from './arcUtils'

export {
  calculatePointToLineDistance,
  pointToLineDistance, // Alias
} from './coordinates'

export {
  calculateDistance,
  calculatePointToCircleDistance,
  calculatePointToEllipseDistance,
  calculatePointToPointDistance, // Alias for calculateDistance
} from './pointUtils'

// Add other distance functions as they are created or migrated, e.g.:
// - lineToLineDistance
// - lineToPolygonDistance
// - polygonToPolygonDistance
// etc.
