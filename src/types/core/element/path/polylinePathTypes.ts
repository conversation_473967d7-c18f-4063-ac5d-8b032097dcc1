/**
 * Defines the type for a polyline path element.
 *
 * @remarks
 * This module provides the `Polyline` interface, which extends {@link ShapeElement}
 * to include properties specific to defining a polyline, such as an array of points
 * and options for curved segments.
 *
 * @module types/core/element/path/polylinePathTypes
 */
import type { ShapeElement } from '../../elementDefinitions' // Corrected import path
import type Point from '@/types/core/element/geometry/point'

/**
 * Represents a polyline element, which is an open path connecting a sequence of points.
 *
 * @remarks
 * A polyline consists of a series of connected straight line segments that form an open shape.
 * It differs from a polygon in that its start and end points are not automatically connected
 * to close the path. This interface allows for both straight and curved polylines.
 * For implementation details, refer to `src/lib/utils/element/path/polylineImplementation.ts`.
 *
 * @see {@link ShapeElement} for base shape element properties.
 * @see {@link Point} for the structure of coordinate points used by this interface.
 */
export interface Polyline extends ShapeElement {
  /**
   * Properties specific to a Polyline element.
   */
  properties: ShapeElement['properties'] & {
    points: Point[]
    curved?: boolean
    tension?: number
    closed?: boolean
  }
}
