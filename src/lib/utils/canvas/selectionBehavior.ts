/**
 * Canvas Selection Behavior Configuration
 *
 * This module defines the selection behavior rules for different interaction scenarios
 * in the canvas, providing a centralized configuration for selection logic.
 */

import type { KeyboardModifiers, SelectionMode } from './selectionUtils'
import { isMultiSelectKeyPressed } from './selectionUtils'

/**
 * Selection interaction types
 */
export type SelectionInteractionType =
  | 'single-click' // Click on a single element
  | 'background-click' // Click on empty canvas background
  | 'marquee-selection' // Drag selection box
  | 'keyboard-shortcut' // Keyboard-initiated selection (e.g., Ctrl+A)

/**
 * Selection context information
 */
export interface SelectionContext {
  /** Type of interaction that triggered the selection */
  interactionType: SelectionInteractionType
  /** Keyboard modifiers pressed during interaction */
  modifiers: Partial<KeyboardModifiers>
  /** Whether there are currently selected elements */
  hasCurrentSelection: boolean
  /** Whether the target element is already selected (for single-click) */
  isTargetSelected?: boolean
  /** Number of elements in marquee selection */
  marqueeElementCount?: number
}

/**
 * Selection behavior rules configuration
 */
export interface SelectionBehaviorConfig {
  /** Enable multi-selection with modifier keys */
  enableMultiSelect: boolean
  /** Enable marquee (box) selection */
  enableMarqueeSelection: boolean
  /** Clear selection when clicking on background */
  clearOnBackgroundClick: boolean
  /** Modifier keys that enable multi-selection */
  multiSelectKeys: {
    shift: boolean
    ctrl: boolean
    meta: boolean // Cmd key on Mac
  }
  /** Behavior for different interaction types */
  behaviors: {
    singleClick: SelectionBehaviorRule
    backgroundClick: SelectionBehaviorRule
    marqueeSelection: SelectionBehaviorRule
  }
}

/**
 * Selection behavior rule for a specific interaction type
 */
export interface SelectionBehaviorRule {
  /** Default mode when no modifiers are pressed */
  defaultMode: SelectionMode
  /** Mode when multi-select modifier is pressed */
  multiSelectMode: SelectionMode
  /** Whether to prevent default browser behavior */
  preventDefault: boolean
  /** Whether to stop event propagation */
  stopPropagation: boolean
}

/**
 * Default selection behavior configuration
 */
export const DEFAULT_SELECTION_BEHAVIOR: SelectionBehaviorConfig = {
  enableMultiSelect: true,
  enableMarqueeSelection: true,
  clearOnBackgroundClick: true,
  multiSelectKeys: {
    shift: true,
    ctrl: true,
    meta: true, // Cmd key on Mac
  },
  behaviors: {
    singleClick: {
      defaultMode: 'replace',
      multiSelectMode: 'toggle',
      preventDefault: true,
      stopPropagation: true,
    },
    backgroundClick: {
      defaultMode: 'clear',
      multiSelectMode: 'clear', // Keep current selection when modifier is pressed
      preventDefault: false,
      stopPropagation: false,
    },
    marqueeSelection: {
      defaultMode: 'replace',
      multiSelectMode: 'add',
      preventDefault: true,
      stopPropagation: true,
    },
  },
}

/**
 * Determines the selection mode based on context and configuration
 *
 * @param context - Selection context information
 * @param config - Selection behavior configuration
 * @returns The appropriate selection mode
 */
export function determineSelectionMode(
  context: SelectionContext,
  config: SelectionBehaviorConfig = DEFAULT_SELECTION_BEHAVIOR,
): SelectionMode {
  const { interactionType, modifiers } = context
  const isMultiSelect = config.enableMultiSelect && isMultiSelectKeyPressed(modifiers)

  let behaviorRule: SelectionBehaviorRule

  switch (interactionType) {
    case 'single-click':
      behaviorRule = config.behaviors.singleClick
      break
    case 'background-click':
      behaviorRule = config.behaviors.backgroundClick
      // Special case: don't clear selection if multi-select key is pressed
      if (isMultiSelect && config.clearOnBackgroundClick) {
        return 'clear' // Keep current selection
      }
      break
    case 'marquee-selection':
      if (!config.enableMarqueeSelection) {
        return 'replace'
      }
      behaviorRule = config.behaviors.marqueeSelection
      break
    case 'keyboard-shortcut':
      // Keyboard shortcuts typically replace selection (e.g., Ctrl+A)
      return 'replace'
    default:
      console.warn(`Unknown interaction type: ${interactionType}`)
      return 'replace'
  }

  return isMultiSelect ? behaviorRule.multiSelectMode : behaviorRule.defaultMode
}

/**
 * Checks if an event should be prevented based on selection behavior
 *
 * @param context - Selection context information
 * @param config - Selection behavior configuration
 * @returns true if preventDefault should be called
 */
export function shouldPreventDefault(
  context: SelectionContext,
  config: SelectionBehaviorConfig = DEFAULT_SELECTION_BEHAVIOR,
): boolean {
  const { interactionType } = context

  switch (interactionType) {
    case 'single-click':
      return config.behaviors.singleClick.preventDefault
    case 'background-click':
      return config.behaviors.backgroundClick.preventDefault
    case 'marquee-selection':
      return config.behaviors.marqueeSelection.preventDefault
    default:
      return false
  }
}

/**
 * Checks if event propagation should be stopped based on selection behavior
 *
 * @param context - Selection context information
 * @param config - Selection behavior configuration
 * @returns true if stopPropagation should be called
 */
export function shouldStopPropagation(
  context: SelectionContext,
  config: SelectionBehaviorConfig = DEFAULT_SELECTION_BEHAVIOR,
): boolean {
  const { interactionType } = context

  switch (interactionType) {
    case 'single-click':
      return config.behaviors.singleClick.stopPropagation
    case 'background-click':
      return config.behaviors.backgroundClick.stopPropagation
    case 'marquee-selection':
      return config.behaviors.marqueeSelection.stopPropagation
    default:
      return false
  }
}

/**
 * Creates a selection context for single element click
 *
 * @param modifiers - Keyboard modifiers from the click event
 * @param hasCurrentSelection - Whether there are currently selected elements
 * @param isTargetSelected - Whether the clicked element is already selected
 * @returns Selection context object
 */
export function createSingleClickContext(
  modifiers: Partial<KeyboardModifiers>,
  hasCurrentSelection: boolean,
  isTargetSelected: boolean = false,
): SelectionContext {
  return {
    interactionType: 'single-click',
    modifiers,
    hasCurrentSelection,
    isTargetSelected,
  }
}

/**
 * Creates a selection context for background click
 *
 * @param modifiers - Keyboard modifiers from the click event
 * @param hasCurrentSelection - Whether there are currently selected elements
 * @returns Selection context object
 */
export function createBackgroundClickContext(
  modifiers: Partial<KeyboardModifiers>,
  hasCurrentSelection: boolean,
): SelectionContext {
  return {
    interactionType: 'background-click',
    modifiers,
    hasCurrentSelection,
  }
}

/**
 * Creates a selection context for marquee selection
 *
 * @param modifiers - Keyboard modifiers from the selection event
 * @param hasCurrentSelection - Whether there are currently selected elements
 * @param marqueeElementCount - Number of elements selected by marquee
 * @returns Selection context object
 */
export function createMarqueeSelectionContext(
  modifiers: Partial<KeyboardModifiers>,
  hasCurrentSelection: boolean,
  marqueeElementCount: number = 0,
): SelectionContext {
  return {
    interactionType: 'marquee-selection',
    modifiers,
    hasCurrentSelection,
    marqueeElementCount,
  }
}

/**
 * Selection behavior manager class
 */
export class SelectionBehaviorManager {
  private config: SelectionBehaviorConfig

  constructor(config: SelectionBehaviorConfig = DEFAULT_SELECTION_BEHAVIOR) {
    this.config = { ...config }
  }

  /**
   * Updates the behavior configuration
   */
  updateConfig(newConfig: Partial<SelectionBehaviorConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * Gets the current configuration
   */
  getConfig(): SelectionBehaviorConfig {
    return { ...this.config }
  }

  /**
   * Determines selection mode for given context
   */
  getSelectionMode(context: SelectionContext): SelectionMode {
    return determineSelectionMode(context, this.config)
  }

  /**
   * Checks if event should be prevented
   */
  shouldPreventDefault(context: SelectionContext): boolean {
    return shouldPreventDefault(context, this.config)
  }

  /**
   * Checks if event propagation should be stopped
   */
  shouldStopPropagation(context: SelectionContext): boolean {
    return shouldStopPropagation(context, this.config)
  }
}
