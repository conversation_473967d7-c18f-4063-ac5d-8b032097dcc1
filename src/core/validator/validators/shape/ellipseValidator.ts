/**
 * Contains validation logic for both Ellipse and Circle shapes.
 */

import type { Shape } from '@/types/core/elementDefinitions'
import type { ValidationError, ValidatableShape as ValidatorShape } from '@/types/core/validator/validator-interface'
import { ElementType } from '@/types/core/elementDefinitions' // Import ElementType and Shape namespace
import { ValidationErrorCode } from '@/types/core/validator/error-codes'
import { isValidPoint } from '../common/validationUtils'
import { BaseShapeValidator } from '../common/validatorBase'

/**
 * Concrete validator implementation for Ellipse and Circle shapes.
 *
 * @extends BaseShapeValidator
 */
export class EllipseValidator extends BaseShapeValidator {
  /**
   * Validates the specific properties of an Ellipse or Circle shape.
   *
   * @param shape - The shape to validate.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected validateSpecific(shape: ValidatorShape): ValidationError[] {
    const errors: ValidationError[] = []

    if (shape.type === ElementType.ELLIPSE) {
      // Cast to Shape.Ellipse for specific validation
      return this.validateEllipse(shape as unknown as Shape.Ellipse)
    }
    else if (shape.type === ElementType.CIRCLE) {
      // Cast to Shape.Circle for specific validation
      return this.validateCircle(shape as unknown as Shape.Circle)
    }
    else {
      errors.push({
        code: ValidationErrorCode.INVALID_SHAPE_TYPE,
        message: `Internal Error: EllipseValidator received shape type ${shape.type}`,
        path: 'type',
        value: shape.type,
      })
      return errors
    }
  }

  /**
   * Validates an ellipse shape.
   *
   * @param ellipse - The ellipse shape to validate.
   * @returns An array of validation errors.
   * @private
   */
  private validateEllipse(ellipse: Shape.Ellipse): ValidationError[] {
    const errors: ValidationError[] = []

    // Validate radiusX from properties
    const props = (ellipse as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined || {}
    const radiusX = props.radiusX as number | undefined
    if (typeof radiusX !== 'number' || radiusX <= 0 || !Number.isFinite(radiusX)) {
      errors.push({
        code: ValidationErrorCode.INVALID_RADIUS,
        message: `Ellipse radiusX must be a positive finite number. Received: ${radiusX}`,
        path: 'properties.radiusX',
        value: radiusX,
      })
    }

    // Validate radiusY from properties
    const radiusY = props.radiusY as number | undefined
    if (typeof radiusY !== 'number' || radiusY <= 0 || !Number.isFinite(radiusY)) {
      errors.push({
        code: ValidationErrorCode.INVALID_RADIUS,
        message: `Ellipse radiusY must be a positive finite number. Received: ${radiusY}`,
        path: 'properties.radiusY',
        value: radiusY,
      })
    }

    // Validate position (ellipse is ValidatableShape which now has an optional position property)
    if (ellipse.position === undefined || !isValidPoint(ellipse.position)) {
      errors.push({
        code: ValidationErrorCode.INVALID_POSITION,
        message: 'Ellipse position must have valid x and y coordinates.',
        path: 'position',
        value: ellipse.position,
      })
    }

    return errors
  }

  /**
   * Validates a circle shape.
   *
   * @param circle - The circle shape to validate.
   * @returns An array of validation errors.
   * @private
   */
  private validateCircle(circle: Shape.Circle): ValidationError[] {
    const errors: ValidationError[] = []

    // Validate radius from properties
    const props = (circle as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined || {}
    const radius = props.radius as number | undefined
    if (typeof radius !== 'number' || radius <= 0 || !Number.isFinite(radius)) {
      errors.push({
        code: ValidationErrorCode.INVALID_RADIUS,
        message: `Circle radius must be a positive finite number. Received: ${radius}`,
        path: 'properties.radius',
        value: radius,
      })
    }

    // Validate position (circle is ValidatableShape which now has an optional position property)
    if (circle.position === undefined || !isValidPoint(circle.position)) {
      errors.push({
        code: ValidationErrorCode.INVALID_POSITION,
        message: 'Circle position must have valid x and y coordinates.',
        path: 'position',
        value: circle.position,
      })
    }

    return errors
  }

  /**
   * Applies ellipse and circle specific business rules.
   *
   * @param _shape - The shape to validate against business rules.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected applySpecificRules(_shape: ValidatorShape): ValidationError[] {
    // No ellipse or circle specific business rules currently identified beyond common ones.
    return []
  }
}
