/**
 * Validation Error Codes
 *
 * Defines the enum for all validation error codes used in the system.
 * This ensures type safety and standardization across the validation system.
 *
 * @remarks
 * This enum is used throughout the validation system to provide consistent
 * error codes that can be easily referenced and handled.
 * Each error code has a corresponding description that explains its usage.
 *
 * @see {@link ValidationError} for the error interface that uses these codes
 * @module types/core/validator/error-codes
 */

/**
 * Enumeration of all possible validation error codes
 *
 * Using an enum instead of string literals provides better type safety,
 * auto-completion, and prevents typos in error codes.
 *
 * @remarks
 * The error codes are grouped into logical categories:
 * - General validation errors
 * - Shape type errors
 * - Dimension/property errors
 * - Polygon specific errors
 * - Design element errors
 */
export enum ValidationErrorCode {
  // General validation errors
  /** Indicates that an element's ID property is missing or does not conform to expected formats. */
  MISSING_OR_INVALID_ID = 'MISSING_OR_INVALID_ID',
  /** Indicates that a provided stroke color string is not in a valid format (e.g., hex, rgb). */
  INVALID_STROKE_COLOR = 'INVALID_STROKE_COLOR',
  /** Indicates that a provided fill color string is not in a valid format. */
  INVALID_FILL_COLOR = 'INVALID_FILL_COLOR',
  /** A generic error code for issues occurring during a specific, non-standard validation process. */
  VALIDATION_SPECIFIC_ERROR = 'VALIDATION_SPECIFIC_ERROR',
  /** An error occurred during the execution or evaluation of a specific validation rule. */
  VALIDATION_RULE_ERROR = 'VALIDATION_RULE_ERROR',

  // Shape type errors
  /** Indicates that the type of a shape is not recognized, not supported, or invalid in the current context. */
  INVALID_SHAPE_TYPE = 'INVALID_SHAPE_TYPE',

  // Dimension and property errors
  /** Indicates that a dimension value (e.g., width, height) is invalid (e.g., negative, non-numeric). */
  INVALID_DIMENSION = 'INVALID_DIMENSION',
  /** Indicates that a radius value is invalid (e.g., negative for a circle). */
  INVALID_RADIUS = 'INVALID_RADIUS',
  /** Indicates that an element's position coordinates are invalid or out of acceptable bounds. */
  INVALID_POSITION = 'INVALID_POSITION',
  /** Indicates that the value of a specific property is invalid or does not meet constraints. */
  INVALID_PROPERTY_VALUE = 'INVALID_PROPERTY_VALUE',
  /** Indicates that the number of sides specified for a polygon or regular shape is invalid (e.g., less than 3). */
  INVALID_SIDES = 'INVALID_SIDES',

  // Polygon specific errors
  /** Indicates that a polygon has an insufficient number of points to form a valid shape (e.g., less than 3). */
  INSUFFICIENT_POINTS = 'INSUFFICIENT_POINTS',
  /** Indicates that one or more point coordinates within a polygon's definition are invalid. */
  INVALID_POINT = 'INVALID_POINT',
  /** Indicates that a polygon is not closed, meaning its first and last points do not coincide. */
  POLYGON_NOT_CLOSED = 'POLYGON_NOT_CLOSED',
  /** Indicates that a polygon's definition contains duplicate (coincident) points. */
  DUPLICATE_POINTS = 'DUPLICATE_POINTS',
  /** Indicates that essential defining properties for a polygon are missing (e.g., no points array or sides property). */
  MISSING_POLYGON_DEFINITION = 'MISSING_POLYGON_DEFINITION',

  // Design element errors
  /** Indicates that a category value assigned to a design element is invalid or not recognized. */
  INVALID_CATEGORY = 'INVALID_CATEGORY',
  /** Indicates that a required property for a design element is missing. */
  MISSING_PROPERTY = 'MISSING_PROPERTY',
}
