import type { ServiceRegistry } from '@/services/core/registry'
import type { ErrorDetails, ErrorService as ErrorServiceInterface } from '@/types/services/core/errorService'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { getServiceFactory } from '@/services/core/registry'
import { registerErrorService } from '@/services/system/error-service/registry'
import { ServiceId } from '@/types/services/core/serviceIdentifier'

// Mock dependencies
vi.mock('@/services/core/registry', () => ({
  getServiceFactory: vi.fn(),
}))

describe('error Service Registry', () => {
  let mockRegistry: ServiceRegistry
  let mockLogger: LoggerService
  let mockErrorServiceImpl: any
  let mockServiceFactory: any

  beforeEach(() => {
    mockErrorServiceImpl = {
      logError: vi.fn(),
      handleError: vi.fn(),
      reportError: vi.fn(),
      createError: vi.fn(),
    }

    mockServiceFactory = {
      createErrorService: vi.fn().mockReturnValue(mockErrorServiceImpl),
    }

    mockRegistry = {
      register: vi.fn(),
      get: vi.fn(),
      has: vi.fn(),
      unregister: vi.fn(),
      clear: vi.fn(),
      getAll: vi.fn(),
    }

    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    }

    vi.mocked(getServiceFactory).mockReturnValue(mockServiceFactory)
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('registerErrorService', () => {
    it('should register error service successfully', () => {
      const result = registerErrorService(mockRegistry, mockLogger)

      expect(getServiceFactory).toHaveBeenCalledTimes(1)
      expect(mockServiceFactory.createErrorService).toHaveBeenCalledWith(mockLogger)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ErrorService,
        expect.any(Object),
      )
      expect(mockLogger.info).toHaveBeenCalledWith('Error service registered successfully.')
      expect(result).toBeDefined()
    })

    it('should return an adapted error service instance', () => {
      const result = registerErrorService(mockRegistry, mockLogger)

      expect(result).toBeDefined()
      expect(typeof result.createError).toBe('function')
      expect(typeof result.logError).toBe('function')
      expect(typeof result.reportError).toBe('function')
      expect(typeof result.handleError).toBe('function')
    })

    it('should call functions in correct order', () => {
      const callOrder: string[] = []

      vi.mocked(getServiceFactory).mockImplementation(() => {
        callOrder.push('getServiceFactory')
        return mockServiceFactory
      })

      mockServiceFactory.createErrorService.mockImplementation(() => {
        callOrder.push('createErrorService')
        return mockErrorServiceImpl
      })

      mockRegistry.register.mockImplementation(() => {
        callOrder.push('register')
      })

      mockLogger.info.mockImplementation(() => {
        callOrder.push('logger.info')
      })

      registerErrorService(mockRegistry, mockLogger)

      expect(callOrder).toEqual([
        'getServiceFactory',
        'createErrorService',
        'register',
        'logger.info',
      ])
    })

    it('should handle service factory errors gracefully', () => {
      const error = new Error('Factory creation failed')
      vi.mocked(getServiceFactory).mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerErrorService(mockRegistry, mockLogger)
      }).toThrow(error)

      expect(getServiceFactory).toHaveBeenCalledTimes(1)
      expect(mockRegistry.register).not.toHaveBeenCalled()
      expect(mockLogger.info).not.toHaveBeenCalled()
    })

    it('should handle error service creation errors gracefully', () => {
      const error = new Error('Error service creation failed')
      mockServiceFactory.createErrorService.mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerErrorService(mockRegistry, mockLogger)
      }).toThrow(error)

      expect(mockServiceFactory.createErrorService).toHaveBeenCalledWith(mockLogger)
      expect(mockRegistry.register).not.toHaveBeenCalled()
      expect(mockLogger.info).not.toHaveBeenCalled()
    })

    it('should handle registry registration errors gracefully', () => {
      const error = new Error('Registration failed')
      mockRegistry.register.mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerErrorService(mockRegistry, mockLogger)
      }).toThrow(error)

      expect(mockServiceFactory.createErrorService).toHaveBeenCalledWith(mockLogger)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ErrorService,
        expect.any(Object),
      )
      expect(mockLogger.info).not.toHaveBeenCalled()
    })

    it('should work with different logger implementations', () => {
      const customLogger = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
        trace: vi.fn(), // Additional method
      }

      const result = registerErrorService(mockRegistry, customLogger)

      expect(mockServiceFactory.createErrorService).toHaveBeenCalledWith(customLogger)
      expect(customLogger.info).toHaveBeenCalledWith('Error service registered successfully.')
      expect(result).toBeDefined()
    })

    it('should work with different registry implementations', () => {
      const customRegistry = {
        register: vi.fn(),
        get: vi.fn(),
        has: vi.fn(),
        unregister: vi.fn(),
        clear: vi.fn(),
        getAll: vi.fn(),
        size: vi.fn(), // Additional method
      }

      const result = registerErrorService(customRegistry, mockLogger)

      expect(customRegistry.register).toHaveBeenCalledWith(
        ServiceId.ErrorService,
        expect.any(Object),
      )
      expect(result).toBeDefined()
    })

    it('should handle multiple registration calls', () => {
      const result1 = registerErrorService(mockRegistry, mockLogger)
      const result2 = registerErrorService(mockRegistry, mockLogger)

      expect(getServiceFactory).toHaveBeenCalledTimes(2)
      expect(mockServiceFactory.createErrorService).toHaveBeenCalledTimes(2)
      expect(mockRegistry.register).toHaveBeenCalledTimes(2)
      expect(mockLogger.info).toHaveBeenCalledTimes(2)
      expect(result1).toBeDefined()
      expect(result2).toBeDefined()
    })

    it('should create new error service instance for each registration', () => {
      const errorService1 = { logError: vi.fn(), handleError: vi.fn(), id: 'service1' }
      const errorService2 = { logError: vi.fn(), handleError: vi.fn(), id: 'service2' }

      mockServiceFactory.createErrorService
        .mockReturnValueOnce(errorService1)
        .mockReturnValueOnce(errorService2)

      const result1 = registerErrorService(mockRegistry, mockLogger)
      const result2 = registerErrorService(mockRegistry, mockLogger)

      expect(result1).toBeDefined()
      expect(result2).toBeDefined()
      expect(result1).not.toBe(result2) // Different adapter instances
    })

    it('should use correct service identifier', () => {
      registerErrorService(mockRegistry, mockLogger)

      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ErrorService,
        expect.any(Object),
      )

      const registrationCall = mockRegistry.register.mock.calls[0]
      expect(registrationCall[0]).toBe(ServiceId.ErrorService)
      expect(typeof registrationCall[0]).toBe('string')
    })

    it('should handle logger method failures gracefully', () => {
      const faultyLogger = {
        info: vi.fn().mockImplementation(() => {
          throw new Error('Logging failed')
        }),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
      }

      expect(() => {
        registerErrorService(mockRegistry, faultyLogger)
      }).toThrow('Logging failed')

      // Service should still be created and registered even if logging fails
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ErrorService,
        expect.any(Object),
      )
    })
  })

  describe('errorServiceAdapter functionality', () => {
    let adaptedService: ErrorServiceInterface

    beforeEach(() => {
      adaptedService = registerErrorService(mockRegistry, mockLogger)
    })

    it('should delegate createError to adapter implementation', () => {
      const errorDetails: ErrorDetails = {
        message: 'Test error',
        code: 'TEST_ERROR',
      }

      const result = adaptedService.createError(errorDetails)

      expect(result).toBeInstanceOf(Error)
      expect(result.message).toBe('Test error')
      expect(result.name).toBe('TEST_ERROR')
    })

    it('should handle createError with empty code', () => {
      const errorDetails: ErrorDetails = {
        message: 'Test error',
        code: '',
      }

      const result = adaptedService.createError(errorDetails)

      expect(result).toBeInstanceOf(Error)
      expect(result.message).toBe('Test error')
      expect(result.name).toBe('Error')
    })

    it('should handle createError with null code', () => {
      const errorDetails: ErrorDetails = {
        message: 'Test error',
        code: null as any,
      }

      const result = adaptedService.createError(errorDetails)

      expect(result).toBeInstanceOf(Error)
      expect(result.message).toBe('Test error')
      expect(result.name).toBe('Error')
    })

    it('should delegate logError to underlying service', () => {
      const error = new Error('Test error')

      adaptedService.logError(error)

      expect(mockErrorServiceImpl.logError).toHaveBeenCalledWith(error)
    })

    it('should delegate reportError to logError', () => {
      const error = new Error('Test error')

      adaptedService.reportError(error)

      expect(mockErrorServiceImpl.logError).toHaveBeenCalledWith(error)
    })

    it('should delegate handleError to underlying service', () => {
      const error = new Error('Test error')

      adaptedService.handleError(error)

      expect(mockErrorServiceImpl.handleError).toHaveBeenCalledWith(error)
    })

    it('should handle ErrorDetails objects in all methods', () => {
      const errorDetails: ErrorDetails = {
        message: 'Test error',
        code: 'TEST_ERROR',
      }

      adaptedService.logError(errorDetails)
      adaptedService.reportError(errorDetails)
      adaptedService.handleError(errorDetails)

      expect(mockErrorServiceImpl.logError).toHaveBeenCalledWith(errorDetails)
      expect(mockErrorServiceImpl.handleError).toHaveBeenCalledWith(errorDetails)
    })
  })

  describe('integration scenarios', () => {
    it('should work in a typical application initialization flow', () => {
      const result = registerErrorService(mockRegistry, mockLogger)

      // Verify the complete flow
      expect(getServiceFactory).toHaveBeenCalledTimes(1)
      expect(mockServiceFactory.createErrorService).toHaveBeenCalledWith(mockLogger)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ErrorService,
        expect.any(Object),
      )
      expect(mockLogger.info).toHaveBeenCalledWith('Error service registered successfully.')
      expect(result).toBeDefined()

      // Test the adapter functionality
      const error = new Error('Test error')
      result.handleError(error)
      expect(mockErrorServiceImpl.handleError).toHaveBeenCalledWith(error)
    })

    it('should handle service replacement scenarios', () => {
      const errorService1 = { logError: vi.fn(), handleError: vi.fn(), id: 'service1' }
      const errorService2 = { logError: vi.fn(), handleError: vi.fn(), id: 'service2' }

      mockServiceFactory.createErrorService
        .mockReturnValueOnce(errorService1)
        .mockReturnValueOnce(errorService2)

      // Initial registration
      const result1 = registerErrorService(mockRegistry, mockLogger)

      // Service replacement
      const result2 = registerErrorService(mockRegistry, mockLogger)

      expect(mockRegistry.register).toHaveBeenCalledTimes(2)
      expect(result1).toBeDefined()
      expect(result2).toBeDefined()

      // Test that different underlying services are used
      const testError = new Error('Test')
      result1.handleError(testError)
      result2.handleError(testError)

      expect(errorService1.handleError).toHaveBeenCalledWith(testError)
      expect(errorService2.handleError).toHaveBeenCalledWith(testError)
    })

    it('should work with real-world error handling scenarios', () => {
      const result = registerErrorService(mockRegistry, mockLogger)

      // Test error creation
      const errorDetails: ErrorDetails = {
        message: 'Database connection failed',
        code: 'DB_CONNECTION_ERROR',
      }

      const error = result.createError(errorDetails)
      expect(error.message).toBe('Database connection failed')
      expect(error.name).toBe('DB_CONNECTION_ERROR')

      // Test error handling
      result.handleError(error)
      expect(mockErrorServiceImpl.handleError).toHaveBeenCalledWith(error)

      // Test error reporting
      result.reportError(errorDetails)
      expect(mockErrorServiceImpl.logError).toHaveBeenCalledWith(errorDetails)
    })
  })
})
