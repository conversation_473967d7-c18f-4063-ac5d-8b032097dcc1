
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/services/elements/element-actions/elementCreationService.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">src/services/elements/element-actions</a> elementCreationService.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/806</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/806</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a>
<a name='L618'></a><a href='#L618'>618</a>
<a name='L619'></a><a href='#L619'>619</a>
<a name='L620'></a><a href='#L620'>620</a>
<a name='L621'></a><a href='#L621'>621</a>
<a name='L622'></a><a href='#L622'>622</a>
<a name='L623'></a><a href='#L623'>623</a>
<a name='L624'></a><a href='#L624'>624</a>
<a name='L625'></a><a href='#L625'>625</a>
<a name='L626'></a><a href='#L626'>626</a>
<a name='L627'></a><a href='#L627'>627</a>
<a name='L628'></a><a href='#L628'>628</a>
<a name='L629'></a><a href='#L629'>629</a>
<a name='L630'></a><a href='#L630'>630</a>
<a name='L631'></a><a href='#L631'>631</a>
<a name='L632'></a><a href='#L632'>632</a>
<a name='L633'></a><a href='#L633'>633</a>
<a name='L634'></a><a href='#L634'>634</a>
<a name='L635'></a><a href='#L635'>635</a>
<a name='L636'></a><a href='#L636'>636</a>
<a name='L637'></a><a href='#L637'>637</a>
<a name='L638'></a><a href='#L638'>638</a>
<a name='L639'></a><a href='#L639'>639</a>
<a name='L640'></a><a href='#L640'>640</a>
<a name='L641'></a><a href='#L641'>641</a>
<a name='L642'></a><a href='#L642'>642</a>
<a name='L643'></a><a href='#L643'>643</a>
<a name='L644'></a><a href='#L644'>644</a>
<a name='L645'></a><a href='#L645'>645</a>
<a name='L646'></a><a href='#L646'>646</a>
<a name='L647'></a><a href='#L647'>647</a>
<a name='L648'></a><a href='#L648'>648</a>
<a name='L649'></a><a href='#L649'>649</a>
<a name='L650'></a><a href='#L650'>650</a>
<a name='L651'></a><a href='#L651'>651</a>
<a name='L652'></a><a href='#L652'>652</a>
<a name='L653'></a><a href='#L653'>653</a>
<a name='L654'></a><a href='#L654'>654</a>
<a name='L655'></a><a href='#L655'>655</a>
<a name='L656'></a><a href='#L656'>656</a>
<a name='L657'></a><a href='#L657'>657</a>
<a name='L658'></a><a href='#L658'>658</a>
<a name='L659'></a><a href='#L659'>659</a>
<a name='L660'></a><a href='#L660'>660</a>
<a name='L661'></a><a href='#L661'>661</a>
<a name='L662'></a><a href='#L662'>662</a>
<a name='L663'></a><a href='#L663'>663</a>
<a name='L664'></a><a href='#L664'>664</a>
<a name='L665'></a><a href='#L665'>665</a>
<a name='L666'></a><a href='#L666'>666</a>
<a name='L667'></a><a href='#L667'>667</a>
<a name='L668'></a><a href='#L668'>668</a>
<a name='L669'></a><a href='#L669'>669</a>
<a name='L670'></a><a href='#L670'>670</a>
<a name='L671'></a><a href='#L671'>671</a>
<a name='L672'></a><a href='#L672'>672</a>
<a name='L673'></a><a href='#L673'>673</a>
<a name='L674'></a><a href='#L674'>674</a>
<a name='L675'></a><a href='#L675'>675</a>
<a name='L676'></a><a href='#L676'>676</a>
<a name='L677'></a><a href='#L677'>677</a>
<a name='L678'></a><a href='#L678'>678</a>
<a name='L679'></a><a href='#L679'>679</a>
<a name='L680'></a><a href='#L680'>680</a>
<a name='L681'></a><a href='#L681'>681</a>
<a name='L682'></a><a href='#L682'>682</a>
<a name='L683'></a><a href='#L683'>683</a>
<a name='L684'></a><a href='#L684'>684</a>
<a name='L685'></a><a href='#L685'>685</a>
<a name='L686'></a><a href='#L686'>686</a>
<a name='L687'></a><a href='#L687'>687</a>
<a name='L688'></a><a href='#L688'>688</a>
<a name='L689'></a><a href='#L689'>689</a>
<a name='L690'></a><a href='#L690'>690</a>
<a name='L691'></a><a href='#L691'>691</a>
<a name='L692'></a><a href='#L692'>692</a>
<a name='L693'></a><a href='#L693'>693</a>
<a name='L694'></a><a href='#L694'>694</a>
<a name='L695'></a><a href='#L695'>695</a>
<a name='L696'></a><a href='#L696'>696</a>
<a name='L697'></a><a href='#L697'>697</a>
<a name='L698'></a><a href='#L698'>698</a>
<a name='L699'></a><a href='#L699'>699</a>
<a name='L700'></a><a href='#L700'>700</a>
<a name='L701'></a><a href='#L701'>701</a>
<a name='L702'></a><a href='#L702'>702</a>
<a name='L703'></a><a href='#L703'>703</a>
<a name='L704'></a><a href='#L704'>704</a>
<a name='L705'></a><a href='#L705'>705</a>
<a name='L706'></a><a href='#L706'>706</a>
<a name='L707'></a><a href='#L707'>707</a>
<a name='L708'></a><a href='#L708'>708</a>
<a name='L709'></a><a href='#L709'>709</a>
<a name='L710'></a><a href='#L710'>710</a>
<a name='L711'></a><a href='#L711'>711</a>
<a name='L712'></a><a href='#L712'>712</a>
<a name='L713'></a><a href='#L713'>713</a>
<a name='L714'></a><a href='#L714'>714</a>
<a name='L715'></a><a href='#L715'>715</a>
<a name='L716'></a><a href='#L716'>716</a>
<a name='L717'></a><a href='#L717'>717</a>
<a name='L718'></a><a href='#L718'>718</a>
<a name='L719'></a><a href='#L719'>719</a>
<a name='L720'></a><a href='#L720'>720</a>
<a name='L721'></a><a href='#L721'>721</a>
<a name='L722'></a><a href='#L722'>722</a>
<a name='L723'></a><a href='#L723'>723</a>
<a name='L724'></a><a href='#L724'>724</a>
<a name='L725'></a><a href='#L725'>725</a>
<a name='L726'></a><a href='#L726'>726</a>
<a name='L727'></a><a href='#L727'>727</a>
<a name='L728'></a><a href='#L728'>728</a>
<a name='L729'></a><a href='#L729'>729</a>
<a name='L730'></a><a href='#L730'>730</a>
<a name='L731'></a><a href='#L731'>731</a>
<a name='L732'></a><a href='#L732'>732</a>
<a name='L733'></a><a href='#L733'>733</a>
<a name='L734'></a><a href='#L734'>734</a>
<a name='L735'></a><a href='#L735'>735</a>
<a name='L736'></a><a href='#L736'>736</a>
<a name='L737'></a><a href='#L737'>737</a>
<a name='L738'></a><a href='#L738'>738</a>
<a name='L739'></a><a href='#L739'>739</a>
<a name='L740'></a><a href='#L740'>740</a>
<a name='L741'></a><a href='#L741'>741</a>
<a name='L742'></a><a href='#L742'>742</a>
<a name='L743'></a><a href='#L743'>743</a>
<a name='L744'></a><a href='#L744'>744</a>
<a name='L745'></a><a href='#L745'>745</a>
<a name='L746'></a><a href='#L746'>746</a>
<a name='L747'></a><a href='#L747'>747</a>
<a name='L748'></a><a href='#L748'>748</a>
<a name='L749'></a><a href='#L749'>749</a>
<a name='L750'></a><a href='#L750'>750</a>
<a name='L751'></a><a href='#L751'>751</a>
<a name='L752'></a><a href='#L752'>752</a>
<a name='L753'></a><a href='#L753'>753</a>
<a name='L754'></a><a href='#L754'>754</a>
<a name='L755'></a><a href='#L755'>755</a>
<a name='L756'></a><a href='#L756'>756</a>
<a name='L757'></a><a href='#L757'>757</a>
<a name='L758'></a><a href='#L758'>758</a>
<a name='L759'></a><a href='#L759'>759</a>
<a name='L760'></a><a href='#L760'>760</a>
<a name='L761'></a><a href='#L761'>761</a>
<a name='L762'></a><a href='#L762'>762</a>
<a name='L763'></a><a href='#L763'>763</a>
<a name='L764'></a><a href='#L764'>764</a>
<a name='L765'></a><a href='#L765'>765</a>
<a name='L766'></a><a href='#L766'>766</a>
<a name='L767'></a><a href='#L767'>767</a>
<a name='L768'></a><a href='#L768'>768</a>
<a name='L769'></a><a href='#L769'>769</a>
<a name='L770'></a><a href='#L770'>770</a>
<a name='L771'></a><a href='#L771'>771</a>
<a name='L772'></a><a href='#L772'>772</a>
<a name='L773'></a><a href='#L773'>773</a>
<a name='L774'></a><a href='#L774'>774</a>
<a name='L775'></a><a href='#L775'>775</a>
<a name='L776'></a><a href='#L776'>776</a>
<a name='L777'></a><a href='#L777'>777</a>
<a name='L778'></a><a href='#L778'>778</a>
<a name='L779'></a><a href='#L779'>779</a>
<a name='L780'></a><a href='#L780'>780</a>
<a name='L781'></a><a href='#L781'>781</a>
<a name='L782'></a><a href='#L782'>782</a>
<a name='L783'></a><a href='#L783'>783</a>
<a name='L784'></a><a href='#L784'>784</a>
<a name='L785'></a><a href='#L785'>785</a>
<a name='L786'></a><a href='#L786'>786</a>
<a name='L787'></a><a href='#L787'>787</a>
<a name='L788'></a><a href='#L788'>788</a>
<a name='L789'></a><a href='#L789'>789</a>
<a name='L790'></a><a href='#L790'>790</a>
<a name='L791'></a><a href='#L791'>791</a>
<a name='L792'></a><a href='#L792'>792</a>
<a name='L793'></a><a href='#L793'>793</a>
<a name='L794'></a><a href='#L794'>794</a>
<a name='L795'></a><a href='#L795'>795</a>
<a name='L796'></a><a href='#L796'>796</a>
<a name='L797'></a><a href='#L797'>797</a>
<a name='L798'></a><a href='#L798'>798</a>
<a name='L799'></a><a href='#L799'>799</a>
<a name='L800'></a><a href='#L800'>800</a>
<a name='L801'></a><a href='#L801'>801</a>
<a name='L802'></a><a href='#L802'>802</a>
<a name='L803'></a><a href='#L803'>803</a>
<a name='L804'></a><a href='#L804'>804</a>
<a name='L805'></a><a href='#L805'>805</a>
<a name='L806'></a><a href='#L806'>806</a>
<a name='L807'></a><a href='#L807'>807</a>
<a name='L808'></a><a href='#L808'>808</a>
<a name='L809'></a><a href='#L809'>809</a>
<a name='L810'></a><a href='#L810'>810</a>
<a name='L811'></a><a href='#L811'>811</a>
<a name='L812'></a><a href='#L812'>812</a>
<a name='L813'></a><a href='#L813'>813</a>
<a name='L814'></a><a href='#L814'>814</a>
<a name='L815'></a><a href='#L815'>815</a>
<a name='L816'></a><a href='#L816'>816</a>
<a name='L817'></a><a href='#L817'>817</a>
<a name='L818'></a><a href='#L818'>818</a>
<a name='L819'></a><a href='#L819'>819</a>
<a name='L820'></a><a href='#L820'>820</a>
<a name='L821'></a><a href='#L821'>821</a>
<a name='L822'></a><a href='#L822'>822</a>
<a name='L823'></a><a href='#L823'>823</a>
<a name='L824'></a><a href='#L824'>824</a>
<a name='L825'></a><a href='#L825'>825</a>
<a name='L826'></a><a href='#L826'>826</a>
<a name='L827'></a><a href='#L827'>827</a>
<a name='L828'></a><a href='#L828'>828</a>
<a name='L829'></a><a href='#L829'>829</a>
<a name='L830'></a><a href='#L830'>830</a>
<a name='L831'></a><a href='#L831'>831</a>
<a name='L832'></a><a href='#L832'>832</a>
<a name='L833'></a><a href='#L833'>833</a>
<a name='L834'></a><a href='#L834'>834</a>
<a name='L835'></a><a href='#L835'>835</a>
<a name='L836'></a><a href='#L836'>836</a>
<a name='L837'></a><a href='#L837'>837</a>
<a name='L838'></a><a href='#L838'>838</a>
<a name='L839'></a><a href='#L839'>839</a>
<a name='L840'></a><a href='#L840'>840</a>
<a name='L841'></a><a href='#L841'>841</a>
<a name='L842'></a><a href='#L842'>842</a>
<a name='L843'></a><a href='#L843'>843</a>
<a name='L844'></a><a href='#L844'>844</a>
<a name='L845'></a><a href='#L845'>845</a>
<a name='L846'></a><a href='#L846'>846</a>
<a name='L847'></a><a href='#L847'>847</a>
<a name='L848'></a><a href='#L848'>848</a>
<a name='L849'></a><a href='#L849'>849</a>
<a name='L850'></a><a href='#L850'>850</a>
<a name='L851'></a><a href='#L851'>851</a>
<a name='L852'></a><a href='#L852'>852</a>
<a name='L853'></a><a href='#L853'>853</a>
<a name='L854'></a><a href='#L854'>854</a>
<a name='L855'></a><a href='#L855'>855</a>
<a name='L856'></a><a href='#L856'>856</a>
<a name='L857'></a><a href='#L857'>857</a>
<a name='L858'></a><a href='#L858'>858</a>
<a name='L859'></a><a href='#L859'>859</a>
<a name='L860'></a><a href='#L860'>860</a>
<a name='L861'></a><a href='#L861'>861</a>
<a name='L862'></a><a href='#L862'>862</a>
<a name='L863'></a><a href='#L863'>863</a>
<a name='L864'></a><a href='#L864'>864</a>
<a name='L865'></a><a href='#L865'>865</a>
<a name='L866'></a><a href='#L866'>866</a>
<a name='L867'></a><a href='#L867'>867</a>
<a name='L868'></a><a href='#L868'>868</a>
<a name='L869'></a><a href='#L869'>869</a>
<a name='L870'></a><a href='#L870'>870</a>
<a name='L871'></a><a href='#L871'>871</a>
<a name='L872'></a><a href='#L872'>872</a>
<a name='L873'></a><a href='#L873'>873</a>
<a name='L874'></a><a href='#L874'>874</a>
<a name='L875'></a><a href='#L875'>875</a>
<a name='L876'></a><a href='#L876'>876</a>
<a name='L877'></a><a href='#L877'>877</a>
<a name='L878'></a><a href='#L878'>878</a>
<a name='L879'></a><a href='#L879'>879</a>
<a name='L880'></a><a href='#L880'>880</a>
<a name='L881'></a><a href='#L881'>881</a>
<a name='L882'></a><a href='#L882'>882</a>
<a name='L883'></a><a href='#L883'>883</a>
<a name='L884'></a><a href='#L884'>884</a>
<a name='L885'></a><a href='#L885'>885</a>
<a name='L886'></a><a href='#L886'>886</a>
<a name='L887'></a><a href='#L887'>887</a>
<a name='L888'></a><a href='#L888'>888</a>
<a name='L889'></a><a href='#L889'>889</a>
<a name='L890'></a><a href='#L890'>890</a>
<a name='L891'></a><a href='#L891'>891</a>
<a name='L892'></a><a href='#L892'>892</a>
<a name='L893'></a><a href='#L893'>893</a>
<a name='L894'></a><a href='#L894'>894</a>
<a name='L895'></a><a href='#L895'>895</a>
<a name='L896'></a><a href='#L896'>896</a>
<a name='L897'></a><a href='#L897'>897</a>
<a name='L898'></a><a href='#L898'>898</a>
<a name='L899'></a><a href='#L899'>899</a>
<a name='L900'></a><a href='#L900'>900</a>
<a name='L901'></a><a href='#L901'>901</a>
<a name='L902'></a><a href='#L902'>902</a>
<a name='L903'></a><a href='#L903'>903</a>
<a name='L904'></a><a href='#L904'>904</a>
<a name='L905'></a><a href='#L905'>905</a>
<a name='L906'></a><a href='#L906'>906</a>
<a name='L907'></a><a href='#L907'>907</a>
<a name='L908'></a><a href='#L908'>908</a>
<a name='L909'></a><a href='#L909'>909</a>
<a name='L910'></a><a href='#L910'>910</a>
<a name='L911'></a><a href='#L911'>911</a>
<a name='L912'></a><a href='#L912'>912</a>
<a name='L913'></a><a href='#L913'>913</a>
<a name='L914'></a><a href='#L914'>914</a>
<a name='L915'></a><a href='#L915'>915</a>
<a name='L916'></a><a href='#L916'>916</a>
<a name='L917'></a><a href='#L917'>917</a>
<a name='L918'></a><a href='#L918'>918</a>
<a name='L919'></a><a href='#L919'>919</a>
<a name='L920'></a><a href='#L920'>920</a>
<a name='L921'></a><a href='#L921'>921</a>
<a name='L922'></a><a href='#L922'>922</a>
<a name='L923'></a><a href='#L923'>923</a>
<a name='L924'></a><a href='#L924'>924</a>
<a name='L925'></a><a href='#L925'>925</a>
<a name='L926'></a><a href='#L926'>926</a>
<a name='L927'></a><a href='#L927'>927</a>
<a name='L928'></a><a href='#L928'>928</a>
<a name='L929'></a><a href='#L929'>929</a>
<a name='L930'></a><a href='#L930'>930</a>
<a name='L931'></a><a href='#L931'>931</a>
<a name='L932'></a><a href='#L932'>932</a>
<a name='L933'></a><a href='#L933'>933</a>
<a name='L934'></a><a href='#L934'>934</a>
<a name='L935'></a><a href='#L935'>935</a>
<a name='L936'></a><a href='#L936'>936</a>
<a name='L937'></a><a href='#L937'>937</a>
<a name='L938'></a><a href='#L938'>938</a>
<a name='L939'></a><a href='#L939'>939</a>
<a name='L940'></a><a href='#L940'>940</a>
<a name='L941'></a><a href='#L941'>941</a>
<a name='L942'></a><a href='#L942'>942</a>
<a name='L943'></a><a href='#L943'>943</a>
<a name='L944'></a><a href='#L944'>944</a>
<a name='L945'></a><a href='#L945'>945</a>
<a name='L946'></a><a href='#L946'>946</a>
<a name='L947'></a><a href='#L947'>947</a>
<a name='L948'></a><a href='#L948'>948</a>
<a name='L949'></a><a href='#L949'>949</a>
<a name='L950'></a><a href='#L950'>950</a>
<a name='L951'></a><a href='#L951'>951</a>
<a name='L952'></a><a href='#L952'>952</a>
<a name='L953'></a><a href='#L953'>953</a>
<a name='L954'></a><a href='#L954'>954</a>
<a name='L955'></a><a href='#L955'>955</a>
<a name='L956'></a><a href='#L956'>956</a>
<a name='L957'></a><a href='#L957'>957</a>
<a name='L958'></a><a href='#L958'>958</a>
<a name='L959'></a><a href='#L959'>959</a>
<a name='L960'></a><a href='#L960'>960</a>
<a name='L961'></a><a href='#L961'>961</a>
<a name='L962'></a><a href='#L962'>962</a>
<a name='L963'></a><a href='#L963'>963</a>
<a name='L964'></a><a href='#L964'>964</a>
<a name='L965'></a><a href='#L965'>965</a>
<a name='L966'></a><a href='#L966'>966</a>
<a name='L967'></a><a href='#L967'>967</a>
<a name='L968'></a><a href='#L968'>968</a>
<a name='L969'></a><a href='#L969'>969</a>
<a name='L970'></a><a href='#L970'>970</a>
<a name='L971'></a><a href='#L971'>971</a>
<a name='L972'></a><a href='#L972'>972</a>
<a name='L973'></a><a href='#L973'>973</a>
<a name='L974'></a><a href='#L974'>974</a>
<a name='L975'></a><a href='#L975'>975</a>
<a name='L976'></a><a href='#L976'>976</a>
<a name='L977'></a><a href='#L977'>977</a>
<a name='L978'></a><a href='#L978'>978</a>
<a name='L979'></a><a href='#L979'>979</a>
<a name='L980'></a><a href='#L980'>980</a>
<a name='L981'></a><a href='#L981'>981</a>
<a name='L982'></a><a href='#L982'>982</a>
<a name='L983'></a><a href='#L983'>983</a>
<a name='L984'></a><a href='#L984'>984</a>
<a name='L985'></a><a href='#L985'>985</a>
<a name='L986'></a><a href='#L986'>986</a>
<a name='L987'></a><a href='#L987'>987</a>
<a name='L988'></a><a href='#L988'>988</a>
<a name='L989'></a><a href='#L989'>989</a>
<a name='L990'></a><a href='#L990'>990</a>
<a name='L991'></a><a href='#L991'>991</a>
<a name='L992'></a><a href='#L992'>992</a>
<a name='L993'></a><a href='#L993'>993</a>
<a name='L994'></a><a href='#L994'>994</a>
<a name='L995'></a><a href='#L995'>995</a>
<a name='L996'></a><a href='#L996'>996</a>
<a name='L997'></a><a href='#L997'>997</a>
<a name='L998'></a><a href='#L998'>998</a>
<a name='L999'></a><a href='#L999'>999</a>
<a name='L1000'></a><a href='#L1000'>1000</a>
<a name='L1001'></a><a href='#L1001'>1001</a>
<a name='L1002'></a><a href='#L1002'>1002</a>
<a name='L1003'></a><a href='#L1003'>1003</a>
<a name='L1004'></a><a href='#L1004'>1004</a>
<a name='L1005'></a><a href='#L1005'>1005</a>
<a name='L1006'></a><a href='#L1006'>1006</a>
<a name='L1007'></a><a href='#L1007'>1007</a>
<a name='L1008'></a><a href='#L1008'>1008</a>
<a name='L1009'></a><a href='#L1009'>1009</a>
<a name='L1010'></a><a href='#L1010'>1010</a>
<a name='L1011'></a><a href='#L1011'>1011</a>
<a name='L1012'></a><a href='#L1012'>1012</a>
<a name='L1013'></a><a href='#L1013'>1013</a>
<a name='L1014'></a><a href='#L1014'>1014</a>
<a name='L1015'></a><a href='#L1015'>1015</a>
<a name='L1016'></a><a href='#L1016'>1016</a>
<a name='L1017'></a><a href='#L1017'>1017</a>
<a name='L1018'></a><a href='#L1018'>1018</a>
<a name='L1019'></a><a href='#L1019'>1019</a>
<a name='L1020'></a><a href='#L1020'>1020</a>
<a name='L1021'></a><a href='#L1021'>1021</a>
<a name='L1022'></a><a href='#L1022'>1022</a>
<a name='L1023'></a><a href='#L1023'>1023</a>
<a name='L1024'></a><a href='#L1024'>1024</a>
<a name='L1025'></a><a href='#L1025'>1025</a>
<a name='L1026'></a><a href='#L1026'>1026</a>
<a name='L1027'></a><a href='#L1027'>1027</a>
<a name='L1028'></a><a href='#L1028'>1028</a>
<a name='L1029'></a><a href='#L1029'>1029</a>
<a name='L1030'></a><a href='#L1030'>1030</a>
<a name='L1031'></a><a href='#L1031'>1031</a>
<a name='L1032'></a><a href='#L1032'>1032</a>
<a name='L1033'></a><a href='#L1033'>1033</a>
<a name='L1034'></a><a href='#L1034'>1034</a>
<a name='L1035'></a><a href='#L1035'>1035</a>
<a name='L1036'></a><a href='#L1036'>1036</a>
<a name='L1037'></a><a href='#L1037'>1037</a>
<a name='L1038'></a><a href='#L1038'>1038</a>
<a name='L1039'></a><a href='#L1039'>1039</a>
<a name='L1040'></a><a href='#L1040'>1040</a>
<a name='L1041'></a><a href='#L1041'>1041</a>
<a name='L1042'></a><a href='#L1042'>1042</a>
<a name='L1043'></a><a href='#L1043'>1043</a>
<a name='L1044'></a><a href='#L1044'>1044</a>
<a name='L1045'></a><a href='#L1045'>1045</a>
<a name='L1046'></a><a href='#L1046'>1046</a>
<a name='L1047'></a><a href='#L1047'>1047</a>
<a name='L1048'></a><a href='#L1048'>1048</a>
<a name='L1049'></a><a href='#L1049'>1049</a>
<a name='L1050'></a><a href='#L1050'>1050</a>
<a name='L1051'></a><a href='#L1051'>1051</a>
<a name='L1052'></a><a href='#L1052'>1052</a>
<a name='L1053'></a><a href='#L1053'>1053</a>
<a name='L1054'></a><a href='#L1054'>1054</a>
<a name='L1055'></a><a href='#L1055'>1055</a>
<a name='L1056'></a><a href='#L1056'>1056</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// cspell:ignore uuidv4
import type { ElementFactory, PathCreationOptionsUnion, ShapeCreationParamsUnion } from '@/core/factory'
import type { PointData } from '@/types/core/element/geometry/point'
import type { ShapeElement as ShapeModel } from '@/types/core/elementDefinitions'
import type { ValidatableShape } from '@/types/core/validator/validator-interface'
import type { ErrorContext as CoreErrorContext } from '@/types/services/errors'
import type { AppEventMap, EventBus } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import type {
  ElementCreationRequest,
  ElementCreationResult,
  ElementCreationService as IElementCreationService,
} from '@/types/services/shapes'
&nbsp;
<span class="cstat-no" title="statement not covered" >import { v4 as uuidv4 } from 'uuid'</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >import { getDefaultSettingsForElementType } from '@/config/defaultElementSettings' // InitialElementProperties removed as unused</span>
<span class="cstat-no" title="statement not covered" >import { ElementValidator } from '@/core/validator'</span>
<span class="cstat-no" title="statement not covered" >import { safeExecuteAsync } from '@/lib/utils/errorUtils'</span>
<span class="cstat-no" title="statement not covered" >import { PointClass } from '@/lib/utils/geometry/PointClass'</span>
<span class="cstat-no" title="statement not covered" >import { getService, ServiceId } from '@/services/core/registry'</span>
<span class="cstat-no" title="statement not covered" >import { ElementType } from '@/types/core/elementDefinitions'</span>
<span class="cstat-no" title="statement not covered" >import { ErrorType as CoreErrorType } from '@/types/services/errors'</span>
<span class="cstat-no" title="statement not covered" >import { AppEventType } from '@/types/services/events'</span>
&nbsp;
/**
 * Defines the payload for an element creation request event.
 * @interface ElementCreateEventPayload
 */
export interface ElementCreateEventPayload {
  /** The type of element to create. */
  elementType: ElementType
  /** The initial position for the new element. Can be a simple object or a PointClass instance. */
  position: { x: number, y: number } | PointClass
  /** Optional additional properties for the element. */
  properties?: Record&lt;string, unknown&gt;
}
&nbsp;
/**
 * Defines the structure of an event that requests element creation.
 * @interface ElementCreateEvent
 */
export interface ElementCreateEvent {
  /** The type of the event, typically corresponding to an {@link AppEventType}. */
  type: string
  /** The payload containing details for element creation. */
  payload: ElementCreateEventPayload
  /** Optional timestamp of when the event was created. */
  timestamp?: number
}
&nbsp;
/**
 * Enumerates specific error types that can occur during element creation.
 * @enum {string}
 */
<span class="cstat-no" title="statement not covered" >enum ElementCreationErrorType {</span>
  /** Indicates that the payload provided for element creation was invalid or incomplete. */
<span class="cstat-no" title="statement not covered" >  InvalidPayload = 'INVALID_PAYLOAD',</span>
  /** Indicates that the element data failed validation against predefined rules. */
<span class="cstat-no" title="statement not covered" >  ValidationFailed = 'VALIDATION_FAILED',</span>
  /** Indicates an error occurred within the ElementFactory during element instantiation. */
<span class="cstat-no" title="statement not covered" >  FactoryCreationFailed = 'FACTORY_CREATION_FAILED',</span>
  /** Indicates a general failure in the coordination of the element creation process. */
<span class="cstat-no" title="statement not covered" >  CoordinatorOperationFailed = 'COORDINATOR_OPERATION_FAILED',</span>
}
&nbsp;
// Removed unused ShapeCreationError class
// enum ElementCreationErrorType is used for error codes in emitError
&nbsp;
/**
 * Service responsible for orchestrating the creation of new shapes.
 *
 * @remarks
 * This service handles the entire lifecycle of shape creation, including:
 * 1. Validating the input data for the new shape.
 * 2. Using the {@link ElementValidator} to prepare and validate shape data against specific rules.
 * 3. Employing the {@link ElementFactory} to instantiate the {@link ShapeModel}.
 * 4. Adding the newly created shape to the {@link ShapeRepository}.
 * 5. Publishing success or error events via the {@link EventBus}.
 *
 * The service supports various shape types including rectangles, circles, ellipses, polygons,
 * and custom paths. It ensures proper validation, default property application, and event
 * publishing for successful shape creation or error handling.
 *
 * @example
 * ```typescript
 * const service = ElementCreationService.create(factory, logger);
 * await service.handleRequest({
 *   type: 'SHAPE_CREATE',
 *   payload: {
 *     elementType: ElementType.RECTANGLE,
 *     position: { x: 100, y: 100 },
 *     properties: { width: 200, height: 150 }
 *   }
 * });
 * ```
 *
 * @see {@link ElementFactory}
 * @see {@link ShapeRepository}
 * @see {@link ElementValidator}
 * @see {@link EventBus}
 * @see {@link LoggerService}
 *
 * @module services/elements/element-actions/elementCreationService
 */
<span class="cstat-no" title="statement not covered" >export class ElementCreationService implements IElementCreationService {</span>
<span class="cstat-no" title="statement not covered" >  readonly serviceId: string = ServiceId.ElementCreationService as string</span>
  /**
   * Creates an instance of ElementCreationService.
   *
   * @param {ElementFactory} factory - The factory instance used for creating shape elements.
   * @param {EventBus} eventBus - The application's event bus for publishing creation events.
   * @param {LoggerService} logger - The logger service for recording service activities.
   */
<span class="cstat-no" title="statement not covered" >  constructor(</span>
<span class="cstat-no" title="statement not covered" >    private factory: ElementFactory,</span>
<span class="cstat-no" title="statement not covered" >    private eventBus: EventBus&lt;AppEventMap&gt;,</span>
<span class="cstat-no" title="statement not covered" >    private logger: LoggerService,</span>
<span class="cstat-no" title="statement not covered" >  ) {</span>
<span class="cstat-no" title="statement not covered" >    this.logger.info('[ElementCreationService] Initialized.')</span>
<span class="cstat-no" title="statement not covered" >    this.logger.debug('[ElementCreationService CONSTRUCTOR] Initialized with factory, eventBus, logger.')</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Factory method to create a {@link ElementCreationService} instance.
   *
   * @remarks
   * This method attempts to resolve dependencies like {@link EventBus} and {@link LoggerService}
   * from the service registry. The {@link ElementFactory} must be provided.
   *
   * @param {ElementFactory} [factory] - The element factory instance. Must be provided.
   * @param {LoggerService} [logger] - Optional logger service. If not provided, it's retrieved from the registry.
   * @returns {ElementCreationService} A new instance of the ElementCreationService.
   * @throws {Error} If `factory` is not provided, or if dependencies cannot be resolved.
   */
<span class="cstat-no" title="statement not covered" >  public static create(</span>
<span class="cstat-no" title="statement not covered" >    factory?: ElementFactory,</span>
<span class="cstat-no" title="statement not covered" >    logger?: LoggerService,</span>
<span class="cstat-no" title="statement not covered" >  ): ElementCreationService {</span>
<span class="cstat-no" title="statement not covered" >    const tempLogger = logger || getService&lt;LoggerService&gt;(ServiceId.Logger)</span>
<span class="cstat-no" title="statement not covered" >    tempLogger.debug('[ElementCreationService CREATE_STATIC] Attempting to create instance.')</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      const eventBus = getService&lt;EventBus&lt;AppEventMap&gt;&gt;(ServiceId.EventBus)</span>
<span class="cstat-no" title="statement not covered" >      const loggerService = logger || getService&lt;LoggerService&gt;(ServiceId.Logger)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!factory) {</span>
<span class="cstat-no" title="statement not covered" >        throw new Error('ElementFactory must be provided to create ElementCreationService.')</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      loggerService.debug('[ElementCreationService CREATE_STATIC] Dependencies resolved. Creating new instance.')</span>
<span class="cstat-no" title="statement not covered" >      return new ElementCreationService(factory, eventBus, loggerService)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    catch (error) {</span>
<span class="cstat-no" title="statement not covered" >      const serviceName = ElementCreationService.name</span>
<span class="cstat-no" title="statement not covered" >      tempLogger.error(`[${serviceName} CREATE_STATIC] Failed to create instance:`, error instanceof Error ? error.message : String(error))</span>
<span class="cstat-no" title="statement not covered" >      throw new Error(`[${serviceName}] Failed to create instance: ${error instanceof Error ? error.message : String(error)}`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Handles an incoming request to create a new shape via an event.
   *
   * @param {ElementCreateEvent} event - The event object containing the details for the shape to be created.
   * @returns {Promise&lt;void&gt;} A promise that resolves when the request has been processed.
   */
<span class="cstat-no" title="statement not covered" >  public async handleRequest(</span>
<span class="cstat-no" title="statement not covered" >    event: ElementCreateEvent,</span>
<span class="cstat-no" title="statement not covered" >  ): Promise&lt;void&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const operation = 'handleElementCreationRequest'</span>
<span class="cstat-no" title="statement not covered" >    this.logger.debug(`[ElementCreationService handleRequest] Received event type: ${event.type}, payload:`, JSON.stringify(event.payload, null, 2))</span>
<span class="cstat-no" title="statement not covered" >    const { payload } = event</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const safeExecuteErrorContext: CoreErrorContext = {</span>
<span class="cstat-no" title="statement not covered" >      component: 'ElementCreationService',</span>
<span class="cstat-no" title="statement not covered" >      operation,</span>
<span class="cstat-no" title="statement not covered" >      metadata: { eventType: event.type, payload },</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    await safeExecuteAsync(async () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (payload?.elementType === undefined || payload?.elementType === null || payload?.position === undefined || payload?.position === null) {</span>
<span class="cstat-no" title="statement not covered" >        this.logger.error('Invalid or missing payload in ElementCreateEvent', payload)</span>
<span class="cstat-no" title="statement not covered" >        this.logger.debug('[ElementCreationService handleRequest] Invalid or missing payload:', JSON.stringify(payload, null, 2))</span>
<span class="cstat-no" title="statement not covered" >        this.emitError(ElementCreationErrorType.InvalidPayload, 'Shape creation payload is invalid.', safeExecuteErrorContext)</span>
<span class="cstat-no" title="statement not covered" >        return</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const creationRequest: ElementCreationRequest = {</span>
<span class="cstat-no" title="statement not covered" >        elementType: payload.elementType,</span>
<span class="cstat-no" title="statement not covered" >        position: payload.position,</span>
<span class="cstat-no" title="statement not covered" >        properties: payload.properties,</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const result = await this.createShapeInternal(creationRequest, operation, payload.elementType.toString())</span>
<span class="cstat-no" title="statement not covered" >      if (result.success === false) {</span>
<span class="cstat-no" title="statement not covered" >        const errorMessage = typeof result.error === 'string' ? result.error : result.error?.message</span>
<span class="cstat-no" title="statement not covered" >        this.logger.error(`Failed to handle shape creation request due to internal error: ${errorMessage}`, result.error)</span>
<span class="cstat-no" title="statement not covered" >        this.logger.error(`[ElementCreationService handleRequest] createShapeInternal failed: ${errorMessage}`, result.error !== undefined ? JSON.stringify(result.error, null, 2) : 'Unknown error')</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }, safeExecuteErrorContext)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  private async createShapeInternal(</span>
<span class="cstat-no" title="statement not covered" >    request: ElementCreationRequest,</span>
<span class="cstat-no" title="statement not covered" >    operationName: string,</span>
<span class="cstat-no" title="statement not covered" >    triggeringEventType?: string,</span>
<span class="cstat-no" title="statement not covered" >  ): Promise&lt;ElementCreationResult&gt; {</span>
<span class="cstat-no" title="statement not covered" >    this.logger.debug(`[ElementCreationService createShapeInternal ENTER] Operation: ${operationName}, EventType: ${triggeringEventType}, Request:`, JSON.stringify(request, null, 2))</span>
<span class="cstat-no" title="statement not covered" >    const { elementType: originalElementType, position, properties: rawProperties } = request</span>
&nbsp;
    // Ensure elementType is treated as uppercase for consistency with ElementType enum keys
<span class="cstat-no" title="statement not covered" >    const elementType = originalElementType.toUpperCase() as ElementType</span>
<span class="cstat-no" title="statement not covered" >    this.logger.debug(`[ElementCreationService createShapeInternal] Original ElementType: ${originalElementType}, Uppercased ElementType: ${elementType}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const id = (typeof rawProperties?.id === 'string' &amp;&amp; rawProperties.id.length &gt; 0) ? rawProperties.id : `${elementType}-${uuidv4()}`</span>
<span class="cstat-no" title="statement not covered" >    this.logger.debug(`[ElementCreationService createShapeInternal] Generated ID: ${id} for elementType: ${elementType}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const currentDefaults = getDefaultSettingsForElementType(elementType)</span>
<span class="cstat-no" title="statement not covered" >    this.logger.debug(`[ElementCreationService createShapeInternal] Defaults loaded EARLY for ${elementType}:`, JSON.stringify(currentDefaults, null, 2))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const baseContext: CoreErrorContext = {</span>
<span class="cstat-no" title="statement not covered" >      component: 'ElementCreationService',</span>
<span class="cstat-no" title="statement not covered" >      operation: operationName,</span>
<span class="cstat-no" title="statement not covered" >      metadata: { elementType, targetId: id, triggeringEventType, requestProperties: rawProperties },</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug?.(`[${operationName}] ENTERING createShapeInternal. elementType: ${elementType}, rawProperties:`, JSON.stringify(rawProperties))</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug?.(`[${operationName}] Creating shape: ${elementType} with id: ${id}`, request)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      let positionInstance: PointClass</span>
<span class="cstat-no" title="statement not covered" >      try {</span>
<span class="cstat-no" title="statement not covered" >        positionInstance = position instanceof PointClass</span>
<span class="cstat-no" title="statement not covered" >          ? position</span>
<span class="cstat-no" title="statement not covered" >          : new PointClass(position.x, position.y)</span>
        // Log plain object for PointClass
<span class="cstat-no" title="statement not covered" >        this.logger.debug(`[ElementCreationService createShapeInternal] PositionInstance created:`, JSON.stringify({ x: positionInstance.x, y: positionInstance.y, z: positionInstance.z }, null, 2))</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      catch (posError: unknown) {</span>
<span class="cstat-no" title="statement not covered" >        const errorMessage = posError instanceof Error ? posError.message : String(posError)</span>
<span class="cstat-no" title="statement not covered" >        this.logger.error(`[${operationName}] Invalid position data for shape ${id}:`, posError)</span>
<span class="cstat-no" title="statement not covered" >        this.logger.error(`[ElementCreationService createShapeInternal] Invalid position data for shape ${id}:`, errorMessage)</span>
<span class="cstat-no" title="statement not covered" >        this.emitError(ElementCreationErrorType.ValidationFailed, `Invalid position data: ${errorMessage}`, { ...baseContext, metadata: { ...baseContext.metadata, originalError: posError } })</span>
<span class="cstat-no" title="statement not covered" >        return { success: false, error: { code: ElementCreationErrorType.ValidationFailed, message: `Invalid position data: ${errorMessage}`, details: posError }, timestamp: Date.now() }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // MOVED baseParams INITIALIZATION BLOCK
<span class="cstat-no" title="statement not covered" >      const baseParams: Record&lt;string, unknown&gt; = {</span>
<span class="cstat-no" title="statement not covered" >        id,</span>
<span class="cstat-no" title="statement not covered" >        metadata: (rawProperties?.metadata !== undefined &amp;&amp; rawProperties.metadata !== null) ? rawProperties.metadata : { createdAt: Date.now(), updatedAt: Date.now() },</span>
<span class="cstat-no" title="statement not covered" >        visible: rawProperties?.visible ?? currentDefaults.visible ?? true,</span>
<span class="cstat-no" title="statement not covered" >        locked: rawProperties?.locked ?? currentDefaults.locked ?? false,</span>
<span class="cstat-no" title="statement not covered" >        rotation: rawProperties?.rotation ?? currentDefaults.rotation ?? 0,</span>
<span class="cstat-no" title="statement not covered" >        selectable: rawProperties?.selectable ?? currentDefaults.selectable ?? true,</span>
<span class="cstat-no" title="statement not covered" >        draggable: rawProperties?.draggable ?? currentDefaults.draggable ?? true,</span>
<span class="cstat-no" title="statement not covered" >        showHandles: rawProperties?.showHandles ?? currentDefaults.showHandles ?? true,</span>
<span class="cstat-no" title="statement not covered" >        layer: rawProperties?.layer ?? currentDefaults.layer, // Layer can be undefined</span>
<span class="cstat-no" title="statement not covered" >        zIndex: rawProperties?.zIndex, // Use zIndex from rawProperties if available (EditorLayout provides this)</span>
<span class="cstat-no" title="statement not covered" >        opacity: rawProperties?.opacity ?? currentDefaults.opacity ?? 1,</span>
        // Style properties - get from raw or defaults, then delete if undefined to let creator defaults apply
<span class="cstat-no" title="statement not covered" >        fill: rawProperties?.fill ?? currentDefaults.fill,</span>
<span class="cstat-no" title="statement not covered" >        stroke: rawProperties?.stroke ?? currentDefaults.stroke,</span>
<span class="cstat-no" title="statement not covered" >        strokeWidth: rawProperties?.strokeWidth ?? currentDefaults.strokeWidth,</span>
<span class="cstat-no" title="statement not covered" >        strokeDasharray: rawProperties?.strokeDasharray ?? currentDefaults.strokeDasharray,</span>
        // --- Add Layer Panel Integration Properties ---
<span class="cstat-no" title="statement not covered" >        majorCategory: rawProperties?.majorCategory, // Get from rawProperties</span>
<span class="cstat-no" title="statement not covered" >        minorCategory: rawProperties?.minorCategory, // Get from rawProperties</span>
<span class="cstat-no" title="statement not covered" >        zLevelId: rawProperties?.zLevelId, // Get from rawProperties</span>
<span class="cstat-no" title="statement not covered" >        isFixedCategory: rawProperties?.isFixedCategory, // Get from rawProperties</span>
        // Add other common base properties here, following the rawProperties ?? currentDefaults.property ?? fallback pattern
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // Log rawProperties and the just-formed baseParams for layer prop check
<span class="cstat-no" title="statement not covered" >      this.logger.debug(`[ElementCreationService createShapeInternal] CHECKING rawProperties for layer props:`, JSON.stringify({</span>
<span class="cstat-no" title="statement not covered" >        majorCategory: String(rawProperties?.majorCategory ?? 'undefined'),</span>
<span class="cstat-no" title="statement not covered" >        minorCategory: String(rawProperties?.minorCategory ?? 'undefined'),</span>
<span class="cstat-no" title="statement not covered" >        zLevelId: String(rawProperties?.zLevelId ?? 'undefined'),</span>
<span class="cstat-no" title="statement not covered" >        isFixedCategory: String(rawProperties?.isFixedCategory ?? 'undefined'),</span>
<span class="cstat-no" title="statement not covered" >        zIndex: String(rawProperties?.zIndex ?? 'undefined'),</span>
<span class="cstat-no" title="statement not covered" >        fill: String(rawProperties?.fill ?? 'undefined'), // for comparison</span>
<span class="cstat-no" title="statement not covered" >      }, null, 2))</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug(`[ElementCreationService createShapeInternal] CHECKING baseParams for layer props:`, JSON.stringify({</span>
<span class="cstat-no" title="statement not covered" >        majorCategory: String(baseParams?.majorCategory ?? 'undefined'),</span>
<span class="cstat-no" title="statement not covered" >        minorCategory: String(baseParams?.minorCategory ?? 'undefined'),</span>
<span class="cstat-no" title="statement not covered" >        zLevelId: String(baseParams?.zLevelId ?? 'undefined'),</span>
<span class="cstat-no" title="statement not covered" >        isFixedCategory: String(baseParams?.isFixedCategory ?? 'undefined'),</span>
<span class="cstat-no" title="statement not covered" >        zIndex: String(baseParams?.zIndex ?? 'undefined'),</span>
<span class="cstat-no" title="statement not covered" >        fill: String(baseParams?.fill ?? 'undefined'), // for comparison</span>
<span class="cstat-no" title="statement not covered" >      }, null, 2))</span>
&nbsp;
      // If zIndex is still undefined after checking rawProperties, then consider currentDefaults
<span class="cstat-no" title="statement not covered" >      if (baseParams.zIndex === undefined &amp;&amp; currentDefaults.zIndex !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >        baseParams.zIndex = currentDefaults.zIndex</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // If style properties are still undefined after checking raw and defaults, remove them
      // so that creator-specific defaults can take precedence.
<span class="cstat-no" title="statement not covered" >      if (baseParams.fill === undefined)</span>
<span class="cstat-no" title="statement not covered" >        delete baseParams.fill</span>
<span class="cstat-no" title="statement not covered" >      if (baseParams.stroke === undefined)</span>
<span class="cstat-no" title="statement not covered" >        delete baseParams.stroke</span>
<span class="cstat-no" title="statement not covered" >      if (baseParams.strokeWidth === undefined)</span>
<span class="cstat-no" title="statement not covered" >        delete baseParams.strokeWidth</span>
<span class="cstat-no" title="statement not covered" >      if (baseParams.strokeDasharray === undefined)</span>
<span class="cstat-no" title="statement not covered" >        delete baseParams.strokeDasharray</span>
<span class="cstat-no" title="statement not covered" >      if (baseParams.layer === undefined)</span>
<span class="cstat-no" title="statement not covered" >        delete baseParams.layer // explicitly remove if still undefined</span>
<span class="cstat-no" title="statement not covered" >      if (baseParams.zIndex === undefined)</span>
<span class="cstat-no" title="statement not covered" >        delete baseParams.zIndex // explicitly remove if still undefined</span>
      // END OF MOVED baseParams INITIALIZATION BLOCK
&nbsp;
      // Initialize propertiesForValidation, ensuring it's an object.
      // Order: Start with defaults from currentDefaults.properties,
      // then override with anything from a nested rawProperties.properties,
      // finally override with any flat properties from rawProperties.
<span class="cstat-no" title="statement not covered" >      const propertiesForValidation: Record&lt;string, unknown&gt; = {</span>
<span class="cstat-no" title="statement not covered" >        ...(currentDefaults.properties !== undefined &amp;&amp; currentDefaults.properties !== null ? currentDefaults.properties : {}),</span>
<span class="cstat-no" title="statement not covered" >        ...(rawProperties?.properties !== undefined &amp;&amp; rawProperties?.properties !== null ? rawProperties.properties as Record&lt;string, unknown&gt; : {}),</span>
<span class="cstat-no" title="statement not covered" >        ...(rawProperties !== undefined &amp;&amp; rawProperties !== null ? rawProperties : {}),</span>
<span class="cstat-no" title="statement not covered" >      }</span>
      // Remove common top-level style properties from propertiesForValidation if they were duplicated by spreading rawProperties,
      // as they are handled by baseParams.
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.fill</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.stroke</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.strokeWidth</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.opacity</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.strokeDasharray</span>
      // Also remove transform/interaction properties that are top-level on ShapeModel
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.position</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.rotation</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.visible</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.locked</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.selectable</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.draggable</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.showHandles</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.layer</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.zIndex</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.id</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.type</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.metadata</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.majorCategory</span>
<span class="cstat-no" title="statement not covered" >      delete propertiesForValidation.minorCategory</span>
&nbsp;
      // Add specific geometric properties from rawProperties to propertiesForValidation
<span class="cstat-no" title="statement not covered" >      if (elementType === ElementType.ELLIPSE || elementType === ElementType.CIRCLE) {</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.radiusX = rawProperties?.radiusX ?? currentDefaults.radiusX</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.radiusY = rawProperties?.radiusY ?? currentDefaults.radiusY</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (elementType === ElementType.CIRCLE) {</span>
<span class="cstat-no" title="statement not covered" >          const circleRadius = (typeof rawProperties?.radius === 'number') ? rawProperties.radius : (typeof currentDefaults.radius === 'number') ? currentDefaults.radius : undefined</span>
<span class="cstat-no" title="statement not covered" >          propertiesForValidation.radius = circleRadius</span>
          // Ensure radiusX and radiusY are consistent with radius for Circle if they are also used by validator/creator
<span class="cstat-no" title="statement not covered" >          if (propertiesForValidation.radiusX === undefined &amp;&amp; circleRadius !== undefined)</span>
<span class="cstat-no" title="statement not covered" >            propertiesForValidation.radiusX = circleRadius</span>
<span class="cstat-no" title="statement not covered" >          if (propertiesForValidation.radiusY === undefined &amp;&amp; circleRadius !== undefined)</span>
<span class="cstat-no" title="statement not covered" >            propertiesForValidation.radiusY = circleRadius</span>
          // If radiusX/Y were defined but radius wasn't, and it's a circle, make them equal
<span class="cstat-no" title="statement not covered" >          if (circleRadius === undefined &amp;&amp; propertiesForValidation.radiusX !== undefined &amp;&amp; propertiesForValidation.radiusY === undefined)</span>
<span class="cstat-no" title="statement not covered" >            propertiesForValidation.radiusY = propertiesForValidation.radiusX</span>
<span class="cstat-no" title="statement not covered" >          if (circleRadius === undefined &amp;&amp; propertiesForValidation.radiusY !== undefined &amp;&amp; propertiesForValidation.radiusX === undefined)</span>
<span class="cstat-no" title="statement not covered" >            propertiesForValidation.radiusX = propertiesForValidation.radiusY</span>
          // If both radiusX and Y defined but not radius, for a circle they must be equal. Default to radiusX if different.
<span class="cstat-no" title="statement not covered" >          if (circleRadius === undefined &amp;&amp; propertiesForValidation.radiusX !== propertiesForValidation.radiusY) {</span>
<span class="cstat-no" title="statement not covered" >            this.logger.warn(`[${operationName}] Circle ${id} has differing radiusX/radiusY from defaults/raw. Using radiusX.`)</span>
<span class="cstat-no" title="statement not covered" >            propertiesForValidation.radiusY = propertiesForValidation.radiusX</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          if (propertiesForValidation.radius === undefined &amp;&amp; propertiesForValidation.radiusX !== undefined) { // If radius still not set, but radiusX is</span>
<span class="cstat-no" title="statement not covered" >            propertiesForValidation.radius = propertiesForValidation.radiusX</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (propertiesForValidation.radius === undefined) {</span>
<span class="cstat-no" title="statement not covered" >            this.logger.warn(`[${operationName}] Circle radius is undefined for ${id} after checking rawProperties and defaults. Validator might fail.`)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        if (propertiesForValidation.radiusX === undefined &amp;&amp; elementType === ElementType.ELLIPSE) {</span>
<span class="cstat-no" title="statement not covered" >          this.logger.warn(`[${operationName}] Ellipse radiusX is undefined for ${id}. Validator might fail.`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        if (propertiesForValidation.radiusY === undefined &amp;&amp; elementType === ElementType.ELLIPSE) {</span>
<span class="cstat-no" title="statement not covered" >          this.logger.warn(`[${operationName}] Ellipse radiusY is undefined for ${id}. Validator might fail.`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (elementType === ElementType.RECTANGLE || elementType === ElementType.SQUARE) {</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.width = rawProperties?.width ?? currentDefaults.width</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.height = rawProperties?.height ?? currentDefaults.height</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (elementType === ElementType.SQUARE) {</span>
<span class="cstat-no" title="statement not covered" >          const side = propertiesForValidation.width ?? propertiesForValidation.height</span>
<span class="cstat-no" title="statement not covered" >          if (side !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >            propertiesForValidation.width = side</span>
<span class="cstat-no" title="statement not covered" >            propertiesForValidation.height = side</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          if (propertiesForValidation.width === undefined) { // Still undefined implies both were undefined</span>
<span class="cstat-no" title="statement not covered" >            this.logger.warn(`[${operationName}] Square side length is undefined for ${id} after checking rawProperties and defaults. Validator might fail.`)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.cornerRadius = rawProperties?.cornerRadius ?? currentDefaults.cornerRadius ?? 0 // Keep this fallback</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (propertiesForValidation.width === undefined &amp;&amp; elementType === ElementType.RECTANGLE) {</span>
<span class="cstat-no" title="statement not covered" >          this.logger.warn(`[${operationName}] Rectangle width is undefined for ${id}. Validator might fail.`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        if (propertiesForValidation.height === undefined &amp;&amp; elementType === ElementType.RECTANGLE) {</span>
<span class="cstat-no" title="statement not covered" >          this.logger.warn(`[${operationName}] Rectangle height is undefined for ${id}. Validator might fail.`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (elementType === ElementType.POLYGON || elementType === ElementType.TRIANGLE || elementType === ElementType.QUADRILATERAL || elementType === ElementType.PENTAGON || elementType === ElementType.HEXAGON) {</span>
<span class="cstat-no" title="statement not covered" >        this.logger.debug?.(`[${operationName}] Polygon-like type. Raw: sides=${String(rawProperties?.sides ?? 'undefined')}, radius=${String(rawProperties?.radius ?? 'undefined')}, isRegular=${String(rawProperties?.isRegular ?? 'undefined')}, points_provided=${rawProperties?.points !== undefined &amp;&amp; rawProperties?.points !== null}. Defaults: sides=${String(currentDefaults.sides ?? 'undefined')}, radius=${String(currentDefaults.radius ?? 'undefined')}, isRegular=${String(currentDefaults.isRegular ?? 'undefined')}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        let validationSides: number</span>
<span class="cstat-no" title="statement not covered" >        if (rawProperties?.sides !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >          validationSides = Number(rawProperties.sides)</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug?.(`[${operationName}] Validation sides: Using rawProperties.sides: ${validationSides} for ${elementType}`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        else if (currentDefaults.sides !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >          validationSides = Number(currentDefaults.sides)</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug?.(`[${operationName}] Validation sides: No rawProperties.sides, using currentDefaults.sides: ${validationSides} for ${elementType}`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        else {</span>
          // Fallback if neither rawProperties nor currentDefaults define sides
<span class="cstat-no" title="statement not covered" >          validationSides = (elementType === ElementType.TRIANGLE)</span>
<span class="cstat-no" title="statement not covered" >            ? 3</span>
<span class="cstat-no" title="statement not covered" >            : (elementType === ElementType.QUADRILATERAL)</span>
<span class="cstat-no" title="statement not covered" >                ? 4</span>
<span class="cstat-no" title="statement not covered" >                : (elementType === ElementType.PENTAGON)</span>
<span class="cstat-no" title="statement not covered" >                    ? 5</span>
<span class="cstat-no" title="statement not covered" >                    : (elementType === ElementType.HEXAGON) ? 6 : 4 // Default to 4 for generic POLYGON or other</span>
<span class="cstat-no" title="statement not covered" >          this.logger.warn(`[${operationName}] Validation sides: ${elementType} missing rawProperties.sides AND currentDefaults.sides. Defaulting to ${validationSides} sides.`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.sides = validationSides</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.radius = rawProperties?.radius ?? currentDefaults.radius ?? 50 // Use currentDefaults for specific type (e.g. Triangle)</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.isRegular = rawProperties?.isRegular ?? currentDefaults.isRegular ?? true</span>
<span class="cstat-no" title="statement not covered" >        const startAngleRad = (typeof rawProperties?.startAngle === 'number') ? rawProperties.startAngle : (typeof currentDefaults.startAngle === 'number') ? currentDefaults.startAngle : undefined</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (rawProperties?.points !== undefined &amp;&amp; rawProperties?.points !== null &amp;&amp; Array.isArray(rawProperties.points) &amp;&amp; rawProperties.points.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug(`[${operationName}] Using provided points for polygon ${id}. Length: ${rawProperties.points.length}`)</span>
<span class="cstat-no" title="statement not covered" >          propertiesForValidation.points = rawProperties.points.map((p: PointData) =&gt; ({ x: p.x, y: p.y, z: p.z }))</span>
          // Infer sides from points if not explicitly set or if different (e.g. for closed polygons)
<span class="cstat-no" title="statement not covered" >          const points = rawProperties.points as PointData[]</span>
<span class="cstat-no" title="statement not covered" >          const inferredSides = points.length &gt; 1</span>
<span class="cstat-no" title="statement not covered" >            &amp;&amp; points[0].x === points[points.length - 1].x</span>
<span class="cstat-no" title="statement not covered" >            &amp;&amp; points[0].y === points[points.length - 1].y</span>
<span class="cstat-no" title="statement not covered" >            ? points.length - 1</span>
<span class="cstat-no" title="statement not covered" >            : points.length</span>
<span class="cstat-no" title="statement not covered" >          if (propertiesForValidation.sides === undefined || propertiesForValidation.sides !== inferredSides) {</span>
<span class="cstat-no" title="statement not covered" >            this.logger.debug(`[${operationName}] Inferring sides (${inferredSides}) from provided points for ${id}. Original sides: ${String(propertiesForValidation.sides)}`)</span>
<span class="cstat-no" title="statement not covered" >            propertiesForValidation.sides = inferredSides</span>
<span class="cstat-no" title="statement not covered" >          }</span>
          // If points are provided, isRegular might be implicitly false unless specified
<span class="cstat-no" title="statement not covered" >          if (rawProperties.isRegular === undefined &amp;&amp; currentDefaults.isRegular === undefined) {</span>
<span class="cstat-no" title="statement not covered" >            propertiesForValidation.isRegular = false // Default to false if points are manually given and no explicit isRegular</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        else if (propertiesForValidation.sides !== undefined &amp;&amp; propertiesForValidation.sides !== null &amp;&amp; propertiesForValidation.radius !== undefined &amp;&amp; propertiesForValidation.radius !== null) {</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug(`[${operationName}] Generating points for polygon ${id} from sides=${String(propertiesForValidation.sides)}, radius=${String(propertiesForValidation.radius)}, isRegular=${String(propertiesForValidation.isRegular)}`)</span>
<span class="cstat-no" title="statement not covered" >          const numSides = Number(propertiesForValidation.sides)</span>
<span class="cstat-no" title="statement not covered" >          const rad = Number(propertiesForValidation.radius)</span>
<span class="cstat-no" title="statement not covered" >          const tempPoints: PointData[] = []</span>
<span class="cstat-no" title="statement not covered" >          const angleStep = (2 * Math.PI) / numSides</span>
          // Use the startAngle for point generation if available (from defaults or raw), else default to -PI/2 (point up)
<span class="cstat-no" title="statement not covered" >          const effectiveStartAngle = (typeof startAngleRad === 'number') ? (startAngleRad * Math.PI / 180) : -Math.PI / 2</span>
&nbsp;
          // 使用鼠标落点位置作为多边形的中心点
<span class="cstat-no" title="statement not covered" >          const tempCenter = {</span>
<span class="cstat-no" title="statement not covered" >            x: positionInstance.x, // 直接使用鼠标落点的位置作为中心点</span>
<span class="cstat-no" title="statement not covered" >            y: positionInstance.y,</span>
<span class="cstat-no" title="statement not covered" >            z: positionInstance.z,</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
          // 记录中心点位置，便于调试
<span class="cstat-no" title="statement not covered" >          this.logger.debug('[ElementCreationService] 多边形中心点(鼠标落点位置):', tempCenter)</span>
&nbsp;
          // 首先生成相对于原点(0,0)的顶点坐标
<span class="cstat-no" title="statement not covered" >          for (let i = 0; i &lt; numSides; i++) {</span>
<span class="cstat-no" title="statement not covered" >            const angle = effectiveStartAngle + (i * angleStep)</span>
<span class="cstat-no" title="statement not covered" >            tempPoints.push({</span>
<span class="cstat-no" title="statement not covered" >              x: rad * Math.cos(angle),</span>
<span class="cstat-no" title="statement not covered" >              y: rad * Math.sin(angle),</span>
<span class="cstat-no" title="statement not covered" >              z: tempCenter.z,</span>
<span class="cstat-no" title="statement not covered" >            })</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
          // 计算几何中心
<span class="cstat-no" title="statement not covered" >          let sumX = 0</span>
<span class="cstat-no" title="statement not covered" >          let sumY = 0</span>
<span class="cstat-no" title="statement not covered" >          for (const point of tempPoints) {</span>
<span class="cstat-no" title="statement not covered" >            sumX += point.x</span>
<span class="cstat-no" title="statement not covered" >            sumY += point.y</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          const centroidX = sumX / numSides</span>
<span class="cstat-no" title="statement not covered" >          const centroidY = sumY / numSides</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          this.logger.debug(`[ElementCreationService] 原始几何中心: (${centroidX}, ${centroidY})`)</span>
&nbsp;
          // 调整顶点坐标，使几何中心与原点(0,0)重合
<span class="cstat-no" title="statement not covered" >          const adjustedPoints: PointData[] = []</span>
<span class="cstat-no" title="statement not covered" >          for (const point of tempPoints) {</span>
<span class="cstat-no" title="statement not covered" >            adjustedPoints.push({</span>
<span class="cstat-no" title="statement not covered" >              x: point.x - centroidX,</span>
<span class="cstat-no" title="statement not covered" >              y: point.y - centroidY,</span>
<span class="cstat-no" title="statement not covered" >              z: point.z,</span>
<span class="cstat-no" title="statement not covered" >            })</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
          // 使用调整后的点替换原始点
<span class="cstat-no" title="statement not covered" >          tempPoints.length = 0</span>
<span class="cstat-no" title="statement not covered" >          for (const point of adjustedPoints) {</span>
<span class="cstat-no" title="statement not covered" >            tempPoints.push(point)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          this.logger.debug(`[ElementCreationService] 调整后的点，确保几何中心在(0,0)：`, tempPoints)</span>
          // Close the polygon for validation if it's meant to be closed (usually true for these types)
<span class="cstat-no" title="statement not covered" >          if (tempPoints.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            tempPoints.push({ ...tempPoints[0] }) // Ensure a new object for the closing point</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          propertiesForValidation.points = tempPoints</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug?.(`[${operationName}] Generated points for validation (closed):`, JSON.stringify(tempPoints))</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        else {</span>
<span class="cstat-no" title="statement not covered" >          this.logger.warn(`[${operationName}] Polygon ${id} lacks points and sufficient (sides/radius) to generate them for validation. Sides: ${String(propertiesForValidation.sides)}, Radius: ${String(propertiesForValidation.radius)}`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        if (propertiesForValidation.sides === undefined) { // Should be caught by earlier logic, but as a safeguard</span>
<span class="cstat-no" title="statement not covered" >          this.logger.warn(`[${operationName}] Polygon sides is undefined for ${id} AT THE END OF VALIDATION PREP. Validator might fail.`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (elementType === ElementType.LINE) {</span>
<span class="cstat-no" title="statement not covered" >        this.logger.debug(`[${operationName}] Populating/defaulting properties for LINE. Raw: start=${JSON.stringify(rawProperties?.start)}, end=${JSON.stringify(rawProperties?.end)}, Defaults: start=${JSON.stringify(currentDefaults.start)}, end=${JSON.stringify(currentDefaults.end)}`)</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.start = rawProperties?.start ?? currentDefaults.start</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.end = rawProperties?.end ?? currentDefaults.end</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.arrowStart = rawProperties?.arrowStart ?? currentDefaults.arrowStart ?? false</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.arrowEnd = rawProperties?.arrowEnd ?? currentDefaults.arrowEnd ?? false</span>
<span class="cstat-no" title="statement not covered" >        if (propertiesForValidation.start === undefined || propertiesForValidation.start === null || propertiesForValidation.end === undefined || propertiesForValidation.end === null) {</span>
<span class="cstat-no" title="statement not covered" >          this.logger.warn(`[${operationName}] LINE ${id} is missing start or end points for validation.`)</span>
          // Potentially create default points if validator strictly needs them
<span class="cstat-no" title="statement not covered" >          if (propertiesForValidation.start === undefined || propertiesForValidation.start === null)</span>
<span class="cstat-no" title="statement not covered" >            propertiesForValidation.start = { x: positionInstance.x, y: positionInstance.y, z: positionInstance.z }</span>
<span class="cstat-no" title="statement not covered" >          if (propertiesForValidation.end === undefined || propertiesForValidation.end === null)</span>
<span class="cstat-no" title="statement not covered" >            propertiesForValidation.end = { x: positionInstance.x + (currentDefaults.width ?? 100), y: positionInstance.y, z: positionInstance.z } // Default length of 100 if end missing</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (elementType === ElementType.POLYLINE) {</span>
<span class="cstat-no" title="statement not covered" >        const rawPointsLength = Array.isArray(rawProperties?.points) ? rawProperties?.points?.length ?? 0 : 'undefined'</span>
<span class="cstat-no" title="statement not covered" >        const defaultPointsLength = Array.isArray(currentDefaults.points) ? currentDefaults.points.length : 'undefined'</span>
<span class="cstat-no" title="statement not covered" >        this.logger.debug(`[${operationName}] Populating/defaulting properties for POLYLINE. Raw points count: ${rawPointsLength}, Default points count: ${defaultPointsLength}`)</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.points = rawProperties?.points ?? currentDefaults.points</span>
<span class="cstat-no" title="statement not covered" >        if (propertiesForValidation.points === undefined || propertiesForValidation.points === null || !Array.isArray(propertiesForValidation.points) || propertiesForValidation.points.length &lt; 2) {</span>
<span class="cstat-no" title="statement not covered" >          this.logger.warn(`[${operationName}] POLYLINE ${id} has insufficient points for validation.`)</span>
          // Create default points if missing/insufficient
<span class="cstat-no" title="statement not covered" >          propertiesForValidation.points = currentDefaults.points &amp;&amp; currentDefaults.points.length &gt;= 2</span>
<span class="cstat-no" title="statement not covered" >            ? currentDefaults.points</span>
<span class="cstat-no" title="statement not covered" >            : [</span>
<span class="cstat-no" title="statement not covered" >                { x: positionInstance.x, y: positionInstance.y, z: positionInstance.z },</span>
<span class="cstat-no" title="statement not covered" >                { x: positionInstance.x + 50, y: positionInstance.y + 50, z: positionInstance.z },</span>
<span class="cstat-no" title="statement not covered" >              ]</span>
<span class="cstat-no" title="statement not covered" >        }</span>
        // For Polyline, 'points' are fundamental.
<span class="cstat-no" title="statement not covered" >        if ((propertiesForValidation.points === undefined || propertiesForValidation.points === null) &amp;&amp; currentDefaults.points !== undefined &amp;&amp; currentDefaults.points !== null) {</span>
<span class="cstat-no" title="statement not covered" >          propertiesForValidation.points = currentDefaults.points</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug?.(`[${operationName}] Polyline 'points' defaulted.`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
        // Ensure 'curved' and 'tension' are present for validation if defined in defaults
<span class="cstat-no" title="statement not covered" >        if (propertiesForValidation.curved === undefined &amp;&amp; currentDefaults.curved !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >          propertiesForValidation.curved = currentDefaults.curved</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug?.(`[${operationName}] Polyline 'curved' defaulted to ${currentDefaults.curved} for validation/factory.`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        if (propertiesForValidation.tension === undefined &amp;&amp; currentDefaults.tension !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >          propertiesForValidation.tension = currentDefaults.tension</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug?.(`[${operationName}] Polyline 'tension' defaulted to ${currentDefaults.tension} for validation/factory.`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (elementType === ElementType.ARC) {</span>
<span class="cstat-no" title="statement not covered" >        this.logger.debug(`[${operationName}] Populating/defaulting properties for ARC. Raw: radius=${String(rawProperties?.radius)}, startAngle=${String(rawProperties?.startAngle)}, endAngle=${String(rawProperties?.endAngle)}. Defaults: radius=${String(currentDefaults.radius)}, startAngle=${String(currentDefaults.startAngle)}, endAngle=${String(currentDefaults.endAngle)}`)</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.radius = rawProperties?.radius ?? currentDefaults.radius</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.startAngle = rawProperties?.startAngle ?? currentDefaults.startAngle</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.endAngle = rawProperties?.endAngle ?? currentDefaults.endAngle</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.counterClockwise = rawProperties?.counterClockwise ?? currentDefaults.counterClockwise ?? false</span>
<span class="cstat-no" title="statement not covered" >        if (propertiesForValidation.radius === undefined || propertiesForValidation.startAngle === undefined || propertiesForValidation.endAngle === undefined) {</span>
<span class="cstat-no" title="statement not covered" >          this.logger.warn(`[${operationName}] ARC ${id} is missing radius, startAngle, or endAngle for validation.`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (elementType === ElementType.QUADRATIC) {</span>
<span class="cstat-no" title="statement not covered" >        this.logger.debug(`[${operationName}] Populating/defaulting properties for QUADRATIC.`)</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.start = rawProperties?.start ?? currentDefaults.start</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.control = rawProperties?.control ?? currentDefaults.control</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.end = rawProperties?.end ?? currentDefaults.end</span>
<span class="cstat-no" title="statement not covered" >        if (propertiesForValidation.start === undefined || propertiesForValidation.start === null || propertiesForValidation.control === undefined || propertiesForValidation.control === null || propertiesForValidation.end === undefined || propertiesForValidation.end === null) {</span>
<span class="cstat-no" title="statement not covered" >          this.logger.warn(`[${operationName}] QUADRATIC ${id} is missing start, control, or end points for validation.`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (elementType === ElementType.CUBIC) {</span>
<span class="cstat-no" title="statement not covered" >        this.logger.debug?.(`[${operationName}] Populating/defaulting properties for CUBIC.`)</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.start = rawProperties?.start ?? currentDefaults.start</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.control1 = rawProperties?.control1 ?? currentDefaults.control1</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.control2 = rawProperties?.control2 ?? currentDefaults.control2</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.end = rawProperties?.end ?? currentDefaults.end</span>
<span class="cstat-no" title="statement not covered" >        if (propertiesForValidation.start === undefined || propertiesForValidation.start === null || propertiesForValidation.control1 === undefined || propertiesForValidation.control1 === null || propertiesForValidation.control2 === undefined || propertiesForValidation.control2 === null || propertiesForValidation.end === undefined || propertiesForValidation.end === null) {</span>
<span class="cstat-no" title="statement not covered" >          this.logger.warn(`[${operationName}] CUBIC ${id} is missing start, control1, control2, or end points for validation.`)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (elementType === ElementType.IMAGE) {</span>
<span class="cstat-no" title="statement not covered" >        this.logger.debug?.(`[${operationName}] Populating/defaulting IMAGE properties into propertiesForValidation.`)</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.src = rawProperties?.src ?? currentDefaults.src ?? '/public/icon/image.svg'</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.width = rawProperties?.width ?? currentDefaults.width ?? 100</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.height = rawProperties?.height ?? currentDefaults.height ?? 100</span>
<span class="cstat-no" title="statement not covered" >        propertiesForValidation.alt = rawProperties?.alt ?? currentDefaults.alt ?? 'Image Placeholder'</span>
<span class="cstat-no" title="statement not covered" >      }</span>
      // propertiesForValidation should now correctly contain the specific sub-properties for the element type.
&nbsp;
<span class="cstat-no" title="statement not covered" >      this.logger.debug?.(`[${operationName}] Data for validation for ${id} will use properties:`, JSON.stringify(propertiesForValidation))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const validatableData: Record&lt;string, unknown&gt; = {</span>
<span class="cstat-no" title="statement not covered" >        type: elementType,</span>
<span class="cstat-no" title="statement not covered" >        id,</span>
<span class="cstat-no" title="statement not covered" >        position: { x: positionInstance.x, y: positionInstance.y, z: positionInstance.z }, // Use initial drop position</span>
<span class="cstat-no" title="statement not covered" >        properties: propertiesForValidation, // Use the populated propertiesForValidation</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      this.logger.debug?.(`[${operationName}] Data prepared for validation for ${id}:`, JSON.stringify(validatableData, null, 2))</span>
<span class="cstat-no" title="statement not covered" >      const validationResult = await ElementValidator.validateElement(validatableData as unknown as ValidatableShape)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (validationResult.valid === false) {</span>
<span class="cstat-no" title="statement not covered" >        const errorMsg = `Shape validation failed: ${validationResult.errors?.join(', ')}`</span>
<span class="cstat-no" title="statement not covered" >        this.logger.warn(`[${operationName}] Validation failed for shape ${id}:`, validationResult.errors)</span>
<span class="cstat-no" title="statement not covered" >        this.logger.error(`[ElementCreationService createShapeInternal] Validation Errors for ${elementType} ${id}:`, validationResult.errors)</span>
<span class="cstat-no" title="statement not covered" >        this.emitError(ElementCreationErrorType.ValidationFailed, errorMsg, { component: 'ElementCreationService', operation: operationName, metadata: { errors: validationResult.errors } })</span>
<span class="cstat-no" title="statement not covered" >        return {</span>
<span class="cstat-no" title="statement not covered" >          success: false,</span>
<span class="cstat-no" title="statement not covered" >          error: {</span>
<span class="cstat-no" title="statement not covered" >            code: ElementCreationErrorType.ValidationFailed.toString(),</span>
<span class="cstat-no" title="statement not covered" >            message: errorMsg,</span>
<span class="cstat-no" title="statement not covered" >            details: validationResult.errors,</span>
<span class="cstat-no" title="statement not covered" >          },</span>
<span class="cstat-no" title="statement not covered" >          timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      this.logger.info(`[${operationName}] Validation successful for shape ${id}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      let specificParams: Record&lt;string, unknown&gt; = { position: positionInstance } // Default position</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      switch (elementType) {</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.LINE:</span>
<span class="cstat-no" title="statement not covered" >          specificParams = {</span>
<span class="cstat-no" title="statement not covered" >            ...specificParams,</span>
<span class="cstat-no" title="statement not covered" >            start: rawProperties?.start ?? currentDefaults.start ?? propertiesForValidation.start ?? { x: positionInstance.x, y: positionInstance.y, z: positionInstance.z },</span>
<span class="cstat-no" title="statement not covered" >            end: rawProperties?.end ?? currentDefaults.end ?? propertiesForValidation.end ?? { x: positionInstance.x + (currentDefaults.width ?? 100), y: positionInstance.y, z: positionInstance.z },</span>
<span class="cstat-no" title="statement not covered" >            arrowStart: rawProperties?.arrowStart ?? currentDefaults.arrowStart ?? false,</span>
<span class="cstat-no" title="statement not covered" >            arrowEnd: rawProperties?.arrowEnd ?? currentDefaults.arrowEnd ?? false,</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug(`[${operationName}] LINE specificParams for factory:`, specificParams)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.RECTANGLE:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.SQUARE: {</span>
<span class="cstat-no" title="statement not covered" >          let width = (typeof rawProperties?.width === 'number') ? rawProperties.width : (typeof currentDefaults.width === 'number') ? currentDefaults.width : 100</span>
<span class="cstat-no" title="statement not covered" >          let height = (typeof rawProperties?.height === 'number') ? rawProperties.height : (typeof currentDefaults.height === 'number') ? currentDefaults.height : 100</span>
<span class="cstat-no" title="statement not covered" >          if (elementType === ElementType.SQUARE) {</span>
<span class="cstat-no" title="statement not covered" >            const side = (typeof width === 'number') ? width : (typeof height === 'number') ? height : 100</span>
<span class="cstat-no" title="statement not covered" >            width = side</span>
<span class="cstat-no" title="statement not covered" >            height = side</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          specificParams = {</span>
<span class="cstat-no" title="statement not covered" >            ...specificParams,</span>
<span class="cstat-no" title="statement not covered" >            width,</span>
<span class="cstat-no" title="statement not covered" >            height,</span>
<span class="cstat-no" title="statement not covered" >            cornerRadius: rawProperties?.cornerRadius ?? currentDefaults.cornerRadius ?? 0,</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug(`[${operationName}] RECT/SQUARE specificParams for factory:`, specificParams)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.ELLIPSE:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.CIRCLE: {</span>
<span class="cstat-no" title="statement not covered" >          let radiusX, radiusY, radius</span>
<span class="cstat-no" title="statement not covered" >          if (elementType === ElementType.CIRCLE) {</span>
<span class="cstat-no" title="statement not covered" >            const rP_radius = (typeof rawProperties?.radius === 'number') ? rawProperties.radius : undefined</span>
<span class="cstat-no" title="statement not covered" >            const cD_radius = (typeof currentDefaults.radius === 'number') ? currentDefaults.radius : undefined</span>
<span class="cstat-no" title="statement not covered" >            const rP_radiusX = (typeof rawProperties?.radiusX === 'number') ? rawProperties.radiusX : undefined</span>
<span class="cstat-no" title="statement not covered" >            const cD_radiusX = (typeof currentDefaults.radiusX === 'number') ? currentDefaults.radiusX : undefined</span>
<span class="cstat-no" title="statement not covered" >            const rP_radiusY = (typeof rawProperties?.radiusY === 'number') ? rawProperties.radiusY : undefined</span>
<span class="cstat-no" title="statement not covered" >            const cD_radiusY = (typeof currentDefaults.radiusY === 'number') ? currentDefaults.radiusY : undefined</span>
<span class="cstat-no" title="statement not covered" >            radius = rP_radius ?? cD_radius ?? rP_radiusX ?? cD_radiusX ?? rP_radiusY ?? cD_radiusY ?? 50</span>
<span class="cstat-no" title="statement not covered" >            radiusX = radius</span>
<span class="cstat-no" title="statement not covered" >            radiusY = radius</span>
<span class="cstat-no" title="statement not covered" >            specificParams = {</span>
<span class="cstat-no" title="statement not covered" >              ...specificParams,</span>
<span class="cstat-no" title="statement not covered" >              radius,</span>
<span class="cstat-no" title="statement not covered" >              radiusX,</span>
<span class="cstat-no" title="statement not covered" >              radiusY,</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          else { // ELLIPSE</span>
<span class="cstat-no" title="statement not covered" >            radiusX = (typeof rawProperties?.radiusX === 'number') ? rawProperties.radiusX : (typeof currentDefaults.radiusX === 'number') ? currentDefaults.radiusX : 50</span>
<span class="cstat-no" title="statement not covered" >            radiusY = (typeof rawProperties?.radiusY === 'number') ? rawProperties.radiusY : (typeof currentDefaults.radiusY === 'number') ? currentDefaults.radiusY : 30</span>
<span class="cstat-no" title="statement not covered" >            specificParams = {</span>
<span class="cstat-no" title="statement not covered" >              ...specificParams,</span>
<span class="cstat-no" title="statement not covered" >              radiusX,</span>
<span class="cstat-no" title="statement not covered" >              radiusY,</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug(`[${operationName}] ELLIPSE/CIRCLE specificParams for factory:`, specificParams)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.POLYGON:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.TRIANGLE:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.QUADRILATERAL:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.PENTAGON:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.HEXAGON:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.HEPTAGON:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.OCTAGON:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.NONAGON:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.DECAGON: {</span>
<span class="cstat-no" title="statement not covered" >          let sides: number</span>
<span class="cstat-no" title="statement not covered" >          if (rawProperties?.sides !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >            sides = Number(rawProperties.sides)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          else if (currentDefaults.sides !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >            sides = Number(currentDefaults.sides)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          else {</span>
<span class="cstat-no" title="statement not covered" >            sides = (elementType === ElementType.TRIANGLE)</span>
<span class="cstat-no" title="statement not covered" >              ? 3</span>
<span class="cstat-no" title="statement not covered" >              : (elementType === ElementType.QUADRILATERAL)</span>
<span class="cstat-no" title="statement not covered" >                  ? 4</span>
<span class="cstat-no" title="statement not covered" >                  : (elementType === ElementType.PENTAGON)</span>
<span class="cstat-no" title="statement not covered" >                      ? 5</span>
<span class="cstat-no" title="statement not covered" >                      : (elementType === ElementType.HEXAGON) ? 6 : 4</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          const radius = (typeof rawProperties?.radius === 'number') ? rawProperties.radius : (typeof currentDefaults.radius === 'number') ? currentDefaults.radius : 50</span>
<span class="cstat-no" title="statement not covered" >          const isRegular = (typeof rawProperties?.isRegular === 'boolean') ? rawProperties.isRegular : (typeof currentDefaults.isRegular === 'boolean') ? currentDefaults.isRegular : true</span>
<span class="cstat-no" title="statement not covered" >          const startAngle = (typeof rawProperties?.startAngle === 'number') ? rawProperties.startAngle : (typeof currentDefaults.startAngle === 'number') ? currentDefaults.startAngle : undefined</span>
<span class="cstat-no" title="statement not covered" >          const center: PointData | undefined = (rawProperties?.center !== undefined &amp;&amp; rawProperties?.center !== null) ? rawProperties.center as PointData : (currentDefaults.center !== undefined &amp;&amp; currentDefaults.center !== null) ? currentDefaults.center : undefined</span>
<span class="cstat-no" title="statement not covered" >          specificParams = {</span>
<span class="cstat-no" title="statement not covered" >            ...specificParams,</span>
<span class="cstat-no" title="statement not covered" >            sides,</span>
<span class="cstat-no" title="statement not covered" >            radius,</span>
<span class="cstat-no" title="statement not covered" >            isRegular,</span>
            // 确保将position传递给PolygonCreator，这样多边形的中心点就是鼠标落点位置
<span class="cstat-no" title="statement not covered" >            position: positionInstance,</span>
<span class="cstat-no" title="statement not covered" >            points: (rawProperties?.points !== undefined &amp;&amp; rawProperties?.points !== null &amp;&amp; Array.isArray(rawProperties.points) &amp;&amp; rawProperties.points.length &gt; 0)</span>
<span class="cstat-no" title="statement not covered" >              ? rawProperties.points</span>
<span class="cstat-no" title="statement not covered" >              : (propertiesForValidation.points !== undefined &amp;&amp; propertiesForValidation.points !== null &amp;&amp; Array.isArray(propertiesForValidation.points) &amp;&amp; propertiesForValidation.sides === sides</span>
<span class="cstat-no" title="statement not covered" >                  ? propertiesForValidation.points</span>
<span class="cstat-no" title="statement not covered" >                  : undefined),</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
          // 确保points属性也在properties中，因为渲染器和验证器期望在properties中找到points
<span class="cstat-no" title="statement not covered" >          if (specificParams.points !== undefined &amp;&amp; Array.isArray(specificParams.points) &amp;&amp; specificParams.points.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            if (specificParams.properties === undefined || specificParams.properties === null) {</span>
<span class="cstat-no" title="statement not covered" >              specificParams.properties = {}</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            const properties = specificParams.properties as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >            properties.points = specificParams.points</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          if (startAngle !== undefined)</span>
<span class="cstat-no" title="statement not covered" >            specificParams.startAngle = startAngle</span>
<span class="cstat-no" title="statement not covered" >          if (center !== undefined)</span>
<span class="cstat-no" title="statement not covered" >            specificParams.center = center</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug(`[${operationName}] POLYGON/TRIANGLE/HEXAGON specificParams for factory (Sides: ${sides}, Radius: ${radius}):`, specificParams)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.ARC: {</span>
<span class="cstat-no" title="statement not covered" >          const radius = (typeof rawProperties?.radius === 'number') ? rawProperties.radius : (typeof currentDefaults.radius === 'number') ? currentDefaults.radius : 50</span>
<span class="cstat-no" title="statement not covered" >          const startAngle = (typeof rawProperties?.startAngle === 'number') ? rawProperties.startAngle : (typeof currentDefaults.startAngle === 'number') ? currentDefaults.startAngle : 0</span>
<span class="cstat-no" title="statement not covered" >          const endAngle = (typeof rawProperties?.endAngle === 'number') ? rawProperties.endAngle : (typeof currentDefaults.endAngle === 'number') ? currentDefaults.endAngle : 90</span>
<span class="cstat-no" title="statement not covered" >          const counterClockwise = (typeof rawProperties?.counterClockwise === 'boolean') ? rawProperties.counterClockwise : (typeof currentDefaults.counterClockwise === 'boolean') ? currentDefaults.counterClockwise : false</span>
<span class="cstat-no" title="statement not covered" >          const closed = (typeof rawProperties?.closed === 'boolean') ? rawProperties.closed : (typeof currentDefaults.closed === 'boolean') ? currentDefaults.closed : false</span>
<span class="cstat-no" title="statement not covered" >          specificParams = {</span>
<span class="cstat-no" title="statement not covered" >            ...specificParams,</span>
<span class="cstat-no" title="statement not covered" >            radius,</span>
<span class="cstat-no" title="statement not covered" >            startAngle,</span>
<span class="cstat-no" title="statement not covered" >            endAngle,</span>
<span class="cstat-no" title="statement not covered" >            counterClockwise,</span>
<span class="cstat-no" title="statement not covered" >            closed,</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug(`[${operationName}] ARC specificParams for factory:`, specificParams)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.QUADRATIC: {</span>
<span class="cstat-no" title="statement not covered" >          const start = (rawProperties?.start as PointData) ?? (currentDefaults.start as PointData) ?? (propertiesForValidation.start !== undefined ? propertiesForValidation.start as PointData : undefined) ?? { x: positionInstance.x, y: positionInstance.y, z: positionInstance.z }</span>
<span class="cstat-no" title="statement not covered" >          const control = (rawProperties?.control as PointData) ?? (currentDefaults.control as PointData) ?? (propertiesForValidation.control !== undefined ? propertiesForValidation.control as PointData : undefined) ?? { x: positionInstance.x + 50, y: positionInstance.y - 50, z: positionInstance.z }</span>
<span class="cstat-no" title="statement not covered" >          const end = (rawProperties?.end as PointData) ?? (currentDefaults.end as PointData) ?? (propertiesForValidation.end !== undefined ? propertiesForValidation.end as PointData : undefined) ?? { x: positionInstance.x + 100, y: positionInstance.y, z: positionInstance.z }</span>
<span class="cstat-no" title="statement not covered" >          specificParams = {</span>
<span class="cstat-no" title="statement not covered" >            ...specificParams,</span>
<span class="cstat-no" title="statement not covered" >            start,</span>
<span class="cstat-no" title="statement not covered" >            control,</span>
<span class="cstat-no" title="statement not covered" >            end,</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug(`[${operationName}] QUADRATIC specificParams for factory:`, specificParams)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.CUBIC: {</span>
<span class="cstat-no" title="statement not covered" >          const start = (rawProperties?.start as PointData) ?? (currentDefaults.start as PointData) ?? (propertiesForValidation.start !== undefined ? propertiesForValidation.start as PointData : undefined) ?? { x: positionInstance.x, y: positionInstance.y, z: positionInstance.z }</span>
<span class="cstat-no" title="statement not covered" >          const control1 = (rawProperties?.control1 as PointData) ?? (currentDefaults.control1 as PointData) ?? (propertiesForValidation.control1 !== undefined ? propertiesForValidation.control1 as PointData : undefined) ?? { x: positionInstance.x + 30, y: positionInstance.y - 50, z: positionInstance.z }</span>
<span class="cstat-no" title="statement not covered" >          const control2 = (rawProperties?.control2 as PointData) ?? (currentDefaults.control2 as PointData) ?? (propertiesForValidation.control2 !== undefined ? propertiesForValidation.control2 as PointData : undefined) ?? { x: positionInstance.x + 70, y: positionInstance.y + 50, z: positionInstance.z }</span>
<span class="cstat-no" title="statement not covered" >          const end = (rawProperties?.end as PointData) ?? (currentDefaults.end as PointData) ?? (propertiesForValidation.end !== undefined ? propertiesForValidation.end as PointData : undefined) ?? { x: positionInstance.x + 100, y: positionInstance.y, z: positionInstance.z }</span>
<span class="cstat-no" title="statement not covered" >          specificParams = {</span>
<span class="cstat-no" title="statement not covered" >            ...specificParams,</span>
<span class="cstat-no" title="statement not covered" >            start,</span>
<span class="cstat-no" title="statement not covered" >            control1,</span>
<span class="cstat-no" title="statement not covered" >            control2,</span>
<span class="cstat-no" title="statement not covered" >            end,</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug(`[${operationName}] CUBIC specificParams for factory:`, specificParams)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.POLYLINE: {</span>
<span class="cstat-no" title="statement not covered" >          let points = (rawProperties?.points as PointData[]) ?? (currentDefaults.points as PointData[]) ?? (propertiesForValidation.points !== undefined ? propertiesForValidation.points as PointData[] : undefined)</span>
<span class="cstat-no" title="statement not covered" >          if (points === undefined || points === null || (Array.isArray(points) &amp;&amp; points.length &lt; 2)) {</span>
<span class="cstat-no" title="statement not covered" >            points = [</span>
<span class="cstat-no" title="statement not covered" >              { x: positionInstance.x, y: positionInstance.y, z: positionInstance.z },</span>
<span class="cstat-no" title="statement not covered" >              { x: positionInstance.x + 50, y: positionInstance.y + 20, z: positionInstance.z },</span>
<span class="cstat-no" title="statement not covered" >              { x: positionInstance.x + 100, y: positionInstance.y, z: positionInstance.z },</span>
<span class="cstat-no" title="statement not covered" >            ]</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          specificParams = {</span>
<span class="cstat-no" title="statement not covered" >            ...specificParams,</span>
<span class="cstat-no" title="statement not covered" >            points,</span>
<span class="cstat-no" title="statement not covered" >            curved: rawProperties?.curved ?? currentDefaults.curved ?? false,</span>
<span class="cstat-no" title="statement not covered" >            tension: rawProperties?.tension ?? currentDefaults.tension ?? 0.5,</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug(`[${operationName}] POLYLINE specificParams for factory:`, specificParams)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.TEXT: {</span>
<span class="cstat-no" title="statement not covered" >          specificParams = {</span>
<span class="cstat-no" title="statement not covered" >            ...specificParams, // Contains position</span>
<span class="cstat-no" title="statement not covered" >            text: rawProperties?.text ?? currentDefaults.text ?? 'Text',</span>
<span class="cstat-no" title="statement not covered" >            fontSize: rawProperties?.fontSize ?? currentDefaults.fontSize ?? 16,</span>
<span class="cstat-no" title="statement not covered" >            fontFamily: rawProperties?.fontFamily ?? currentDefaults.fontFamily ?? 'Arial',</span>
<span class="cstat-no" title="statement not covered" >            fill: rawProperties?.fill ?? currentDefaults.fill, // Text-specific fill</span>
<span class="cstat-no" title="statement not covered" >            textAlign: rawProperties?.textAlign ?? currentDefaults.textAlign ?? 'left',</span>
<span class="cstat-no" title="statement not covered" >            textBaseline: rawProperties?.textBaseline ?? currentDefaults.textBaseline ?? 'top',</span>
<span class="cstat-no" title="statement not covered" >            fontWeight: rawProperties?.fontWeight ?? currentDefaults.fontWeight ?? 'normal',</span>
<span class="cstat-no" title="statement not covered" >            fontStyle: rawProperties?.fontStyle ?? currentDefaults.fontStyle ?? 'normal',</span>
<span class="cstat-no" title="statement not covered" >            lineHeight: rawProperties?.lineHeight ?? currentDefaults.lineHeight,</span>
<span class="cstat-no" title="statement not covered" >          }</span>
          // Clean up undefined properties to allow creator defaults if not specified here
<span class="cstat-no" title="statement not covered" >          Object.keys(specificParams).forEach((key) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            if (specificParams[key] === undefined) {</span>
<span class="cstat-no" title="statement not covered" >              delete specificParams[key]</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          })</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug(`[${operationName}] TEXT specificParams for factory:`, specificParams)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.IMAGE: {</span>
<span class="cstat-no" title="statement not covered" >          const src = (typeof rawProperties?.src === 'string') ? rawProperties.src : (typeof currentDefaults.src === 'string') ? currentDefaults.src : ''</span>
<span class="cstat-no" title="statement not covered" >          const width = (typeof rawProperties?.width === 'number') ? rawProperties.width : (typeof currentDefaults.width === 'number') ? currentDefaults.width : 100</span>
<span class="cstat-no" title="statement not covered" >          const height = (typeof rawProperties?.height === 'number') ? rawProperties.height : (typeof currentDefaults.height === 'number') ? currentDefaults.height : 100</span>
<span class="cstat-no" title="statement not covered" >          specificParams = {</span>
<span class="cstat-no" title="statement not covered" >            ...specificParams,</span>
<span class="cstat-no" title="statement not covered" >            src,</span>
<span class="cstat-no" title="statement not covered" >            width,</span>
<span class="cstat-no" title="statement not covered" >            height,</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug(`[${operationName}] IMAGE specificParams for factory:`, specificParams)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.TEXT_LABEL:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.WALL:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.DOOR:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.WINDOW:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.FURNITURE:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.FIXTURE:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.ROOM:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.LIGHT:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.FLOOR_AREA:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.HANDRAIL:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.ELECTRICAL_OUTLET:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.ROOM_BOUNDARY:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.APPLIANCE:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.GROUP:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.OPENING:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.WALL_PAINT:</span>
<span class="cstat-no" title="statement not covered" >        case ElementType.WALL_PAPER:</span>
          // These element types use default parameters for now
<span class="cstat-no" title="statement not covered" >          this.logger.debug(`[${operationName}] ${elementType} using default parameters`)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        default:</span>
<span class="cstat-no" title="statement not covered" >          this.logger.warn(`[${operationName}] Unhandled element type: ${String(elementType)}. Using default parameters.`)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      this.logger.debug(`[ElementCreationService createShapeInternal] Populated specificParams for ${elementType}:`, JSON.stringify(specificParams, null, 2))</span>
&nbsp;
      // 准备properties对象，确保包含所有必要的属性
      // 首先从默认设置中获取成本相关属性，然后用rawProperties覆盖
<span class="cstat-no" title="statement not covered" >      const mergedProperties = {</span>
        // 从默认设置中获取成本相关属性
<span class="cstat-no" title="statement not covered" >        costUnitPrice: currentDefaults.costUnitPrice ?? 1,</span>
<span class="cstat-no" title="statement not covered" >        costMultiplierOrCount: currentDefaults.costMultiplierOrCount ?? 0,</span>
<span class="cstat-no" title="statement not covered" >        costBasis: currentDefaults.costBasis ?? 'unit',</span>
        // 然后合并其他属性
<span class="cstat-no" title="statement not covered" >        ...(rawProperties || {}),</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // 对于多边形类型，确保points属性在properties中
<span class="cstat-no" title="statement not covered" >      const isPolygonType = [</span>
<span class="cstat-no" title="statement not covered" >        ElementType.POLYGON,</span>
<span class="cstat-no" title="statement not covered" >        ElementType.TRIANGLE,</span>
<span class="cstat-no" title="statement not covered" >        ElementType.QUADRILATERAL,</span>
<span class="cstat-no" title="statement not covered" >        ElementType.PENTAGON,</span>
<span class="cstat-no" title="statement not covered" >        ElementType.HEXAGON,</span>
<span class="cstat-no" title="statement not covered" >      ].includes(elementType)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (isPolygonType</span>
<span class="cstat-no" title="statement not covered" >        &amp;&amp; specificParams.points !== undefined</span>
<span class="cstat-no" title="statement not covered" >        &amp;&amp; Array.isArray(specificParams.points)</span>
<span class="cstat-no" title="statement not covered" >        &amp;&amp; specificParams.points.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >        (mergedProperties as any).points = specificParams.points</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const paramsForFactory = {</span>
<span class="cstat-no" title="statement not covered" >        ...baseParams,</span>
<span class="cstat-no" title="statement not covered" >        ...specificParams,</span>
<span class="cstat-no" title="statement not covered" >        type: elementType,</span>
        // Ensure all original custom properties from the request are passed to the factory
        // under the 'properties' field.
<span class="cstat-no" title="statement not covered" >        properties: mergedProperties,</span>
<span class="cstat-no" title="statement not covered" >      } as unknown as ShapeCreationParamsUnion</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      this.logger.debug?.(`[${operationName}] FINAL Params being passed to factory:`, JSON.stringify(paramsForFactory, null, 2))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      let createdElementModel: ShapeModel</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const pathTypes: string[] = [</span>
<span class="cstat-no" title="statement not covered" >        ElementType.LINE.toString(),</span>
<span class="cstat-no" title="statement not covered" >        ElementType.POLYLINE.toString(),</span>
<span class="cstat-no" title="statement not covered" >        ElementType.ARC.toString(),</span>
<span class="cstat-no" title="statement not covered" >        ElementType.QUADRATIC.toString(),</span>
<span class="cstat-no" title="statement not covered" >        ElementType.CUBIC.toString(),</span>
<span class="cstat-no" title="statement not covered" >      ]</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (pathTypes.includes(elementType.toString())) {</span>
<span class="cstat-no" title="statement not covered" >        this.logger.info?.(`[${operationName}] Element type ${elementType} is a Path. Calling factory.createPath().`)</span>
<span class="cstat-no" title="statement not covered" >        createdElementModel = await this.factory.createPath(elementType, paramsForFactory as PathCreationOptionsUnion) as ShapeModel</span>
<span class="cstat-no" title="statement not covered" >        this.logger.debug(`[ElementCreationService createShapeInternal] Path model RECEIVED FROM FACTORY (ID: ${createdElementModel.id}):`, JSON.stringify(createdElementModel, null, 2))</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
<span class="cstat-no" title="statement not covered" >        this.logger.info?.(`[${operationName}] Element type ${elementType} is a Shape. Calling factory.createShape().`)</span>
<span class="cstat-no" title="statement not covered" >        createdElementModel = await this.factory.createShape(elementType, paramsForFactory)</span>
<span class="cstat-no" title="statement not covered" >        this.logger.debug(`[ElementCreationService createShapeInternal] Shape model RECEIVED FROM FACTORY (ID: ${createdElementModel.id}):`, JSON.stringify(createdElementModel, null, 2))</span>
<span class="cstat-no" title="statement not covered" >        this.logger.debug(`[ElementCreationService createShapeInternal] DETAILED CHECK of Shape model from factory:`, JSON.stringify({</span>
<span class="cstat-no" title="statement not covered" >          id: createdElementModel.id,</span>
<span class="cstat-no" title="statement not covered" >          type: createdElementModel.type,</span>
<span class="cstat-no" title="statement not covered" >          majorCategory: createdElementModel.majorCategory,</span>
<span class="cstat-no" title="statement not covered" >          minorCategory: createdElementModel.minorCategory,</span>
<span class="cstat-no" title="statement not covered" >          zLevelId: createdElementModel.zLevelId,</span>
<span class="cstat-no" title="statement not covered" >          isFixedCategory: createdElementModel.isFixedCategory,</span>
<span class="cstat-no" title="statement not covered" >          zIndex: createdElementModel.zIndex,</span>
<span class="cstat-no" title="statement not covered" >          position: createdElementModel.position,</span>
<span class="cstat-no" title="statement not covered" >        }, null, 2))</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      this.logger.debug?.(`[${operationName}] Created element model:`, createdElementModel)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      this.logger.info(`[${operationName}] Shape "${id}" created (not added to repository here). Emitting event.`)</span>
<span class="cstat-no" title="statement not covered" >      this.emitSuccess(createdElementModel)</span>
<span class="cstat-no" title="statement not covered" >      return { success: true, data: createdElementModel, timestamp: Date.now() }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    catch (creationError) {</span>
<span class="cstat-no" title="statement not covered" >      const err = creationError instanceof Error ? creationError : new Error(String(creationError))</span>
<span class="cstat-no" title="statement not covered" >      this.logger.error(`[${operationName}] Error creating or adding shape ${id}:`, err)</span>
<span class="cstat-no" title="statement not covered" >      this.emitError(</span>
<span class="cstat-no" title="statement not covered" >        ElementCreationErrorType.FactoryCreationFailed,</span>
<span class="cstat-no" title="statement not covered" >        `Failed to create or add shape: ${err.message}`,</span>
<span class="cstat-no" title="statement not covered" >        { ...baseContext, metadata: { ...baseContext.metadata, originalError: err } },</span>
<span class="cstat-no" title="statement not covered" >      )</span>
<span class="cstat-no" title="statement not covered" >      return {</span>
<span class="cstat-no" title="statement not covered" >        success: false,</span>
<span class="cstat-no" title="statement not covered" >        error: {</span>
<span class="cstat-no" title="statement not covered" >          code: ElementCreationErrorType.FactoryCreationFailed.toString(),</span>
<span class="cstat-no" title="statement not covered" >          message: err.message,</span>
<span class="cstat-no" title="statement not covered" >          details: err,</span>
<span class="cstat-no" title="statement not covered" >        },</span>
<span class="cstat-no" title="statement not covered" >        timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Emits an error event for shape creation failures.
   * @private
   * @param {ElementCreationErrorType} errorType - The type of shape creation error.
   * @param {string} message - A detailed error message.
   * @param {CoreErrorContext} [context] - Contextual information about the error.
   */
<span class="cstat-no" title="statement not covered" >  private emitError(errorType: ElementCreationErrorType, message: string, context?: CoreErrorContext): void {</span>
<span class="cstat-no" title="statement not covered" >    const errorId = uuidv4()</span>
<span class="cstat-no" title="statement not covered" >    const fullMessage = `[${errorType}] ${message}${(context?.operation !== undefined &amp;&amp; context.operation !== null &amp;&amp; context.operation !== '') ? ` in ${context.operation}` : ''}`</span>
<span class="cstat-no" title="statement not covered" >    this.logger.error(fullMessage, { errorId, ...(context?.metadata || {}) })</span>
<span class="cstat-no" title="statement not covered" >    this.logger.error(`[ElementCreationService emitError] Publishing AppEventType.EventError. ErrorType: ${errorType}, Message: ${message}, Context:`, JSON.stringify(context, null, 2))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >      type: AppEventType.EventError,</span>
<span class="cstat-no" title="statement not covered" >      timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >      payload: {</span>
<span class="cstat-no" title="statement not covered" >        error: {</span>
<span class="cstat-no" title="statement not covered" >          code: errorType.toString(),</span>
<span class="cstat-no" title="statement not covered" >          message: fullMessage,</span>
<span class="cstat-no" title="statement not covered" >          details: context?.metadata,</span>
<span class="cstat-no" title="statement not covered" >          errorId,</span>
<span class="cstat-no" title="statement not covered" >        },</span>
<span class="cstat-no" title="statement not covered" >        context,</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    })</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Emits a success event after a shape is successfully created.
   * @private
   * @param {ShapeModel} shapeModel - The successfully created shape model.
   */
<span class="cstat-no" title="statement not covered" >  private emitSuccess(shapeModel: ShapeModel): void {</span>
<span class="cstat-no" title="statement not covered" >    this.logger.info(`Successfully created shape: ${shapeModel.id} of type ${shapeModel.type}`)</span>
<span class="cstat-no" title="statement not covered" >    this.logger.debug(`[ElementCreationService emitSuccess] Publishing AppEventType.ShapeCreateComplete for shape ID: ${shapeModel.id}, Type: ${shapeModel.type}, Shape:`, JSON.stringify(shapeModel, null, 2))</span>
<span class="cstat-no" title="statement not covered" >    this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >      type: AppEventType.ShapeCreateComplete,</span>
<span class="cstat-no" title="statement not covered" >      timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >      payload: {</span>
<span class="cstat-no" title="statement not covered" >        shape: shapeModel,</span>
<span class="cstat-no" title="statement not covered" >        source: ElementCreationService.name,</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    })</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.logger.debug?.(`[ElementCreationService] Emitted AppEventType.ShapeCreateComplete for shape: ${shapeModel.id} (emitted a clone)`)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // --- Methods to satisfy IElementCreationService ---
&nbsp;
  /**
   * Creates a new shape based on the provided request.
   * @param {ElementCreationRequest} request - Parameters for creating the shape.
   * @returns {Promise&lt;ElementCreationResult&gt;} A promise that resolves with the result of the creation attempt.
   */
<span class="cstat-no" title="statement not covered" >  public async createShape(request: ElementCreationRequest): Promise&lt;ElementCreationResult&gt; {</span>
<span class="cstat-no" title="statement not covered" >    this.logger.info('[ElementCreationService] createShape called directly.', request)</span>
<span class="cstat-no" title="statement not covered" >    return this.createShapeInternal(request, 'createShape', request.elementType.toString())</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Duplicates an existing shape with an optional offset.
   * @param {string} shapeId - The ID of the shape to duplicate.
   * @param {{ x: number; y: number }} [offset] - Optional offset for the new shape's position.
   * @returns {Promise&lt;ElementCreationResult&gt;} A promise that resolves with the result of the duplication attempt.
   */
<span class="cstat-no" title="statement not covered" >  public async duplicateShape(shapeId: string, offset?: { x: number, y: number }): Promise&lt;ElementCreationResult&gt; {</span>
<span class="cstat-no" title="statement not covered" >    this.logger.info(`[ElementCreationService] duplicateShape called for ID: ${shapeId}`, offset)</span>
    // No direct repository access; duplication should be handled via event-driven flow or by CoreCoordinator
<span class="cstat-no" title="statement not covered" >    this.emitError(ElementCreationErrorType.CoordinatorOperationFailed, 'Direct duplication not supported in event-driven mode.', {</span>
<span class="cstat-no" title="statement not covered" >      component: 'ElementCreationService',</span>
<span class="cstat-no" title="statement not covered" >      operation: 'duplicateShape',</span>
<span class="cstat-no" title="statement not covered" >      metadata: { shapeId },</span>
<span class="cstat-no" title="statement not covered" >    })</span>
<span class="cstat-no" title="statement not covered" >    return {</span>
<span class="cstat-no" title="statement not covered" >      success: false,</span>
<span class="cstat-no" title="statement not covered" >      error: { code: CoreErrorType.NotFound.toString(), message: 'Direct duplication not supported in event-driven mode.' },</span>
<span class="cstat-no" title="statement not covered" >      timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:58:01.486Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    