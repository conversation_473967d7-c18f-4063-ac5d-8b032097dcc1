// Import base event interfaces and types
import type {
  BaseEvent,
  BaseEventWithTimestamp,
  EventBus,
  EventBusConfig,
  EventHandler,
  EventHandlerWithOptions,
  EventSubscriptionOptions,
  TypedEvent,
} from '@/types/services/events/base'

import type { AppEventMap } from '@/types/services/events/eventRegistry'

import { describe, expect, it } from 'vitest'
// Import AppEventType and AppEventMap for context
import { AppEventType } from '@/types/services/events/eventTypes'

describe('service Event Base Types', () => {
  it('should allow declaration using BaseEvent interfaces', () => {
    const base: BaseEvent | undefined = undefined
    expect(base).toBeUndefined()
    let withTs: BaseEventWithTimestamp | undefined
    expect(withTs).toBeUndefined()
    let typed: TypedEvent<{ id: string }> | undefined
    expect(typed).toBeUndefined()

    // Example BaseEvent
    const exampleBase: BaseEvent = { type: AppEventType.VIEW_ZOOM_IN, payload: { level: 1.1 } }
    expect(exampleBase.type).toBe(AppEventType.VIEW_ZOOM_IN)
    expect((exampleBase.payload as any).level).toBe(1.1)

    // Example TypedEvent
    const exampleTyped: TypedEvent<{ value: boolean }> = { type: AppEventType.GRID_ENABLED, payload: { value: true } }
    expect(exampleTyped.type).toBe(AppEventType.GRID_ENABLED)
    expect(exampleTyped.payload.value).toBe(true)
  })

  it('should allow declaration using EventHandler type', () => {
    const handler: EventHandler | undefined = undefined
    expect(handler).toBeUndefined()

    // Define a handler
    const myHandler: EventHandler = (event) => { /* console.log(event.type); */ }
    expect(typeof myHandler).toBe('function')

    // Define a typed handler
    type SpecificEvent = TypedEvent<{ message: string }>
    const specificHandler: EventHandler<SpecificEvent> = (event) => {
      expect(typeof event.payload.message).toBe('string')
    }
    specificHandler({ type: AppEventType.TOAST_SHOW, payload: { message: 'Hi' } })
  })

  it('should allow declaration using option interfaces', () => {
    const subOpts: EventSubscriptionOptions | undefined = undefined
    expect(subOpts).toBeUndefined()
    let handlerOpts: EventHandlerWithOptions | undefined
    expect(handlerOpts).toBeUndefined()
    let busConf: EventBusConfig | undefined
    expect(busConf).toBeUndefined()

    // Example EventSubscriptionOptions
    const exampleSubOpts: EventSubscriptionOptions = {
      once: true,
      priority: 10,
      filter: event => event.type === AppEventType.SHAPE_SELECTED,
    }
    expect(exampleSubOpts.once).toBe(true)
    expect(typeof exampleSubOpts.filter).toBe('function')
  })

  it('should allow declaration using EventBus interface', () => {
    const bus: EventBus | undefined = undefined
    expect(bus).toBeUndefined()

    // Mock EventBus (structure check)
    const mockBus: EventBus = {
      subscribe: <K extends keyof AppEventMap>(eventType: K, handler: EventHandler<AppEventMap[K]>) => { return () => {} },
      publish: <E extends AppEventMap[keyof AppEventMap]>(event: E) => { return true },
      unsubscribe: <K extends keyof AppEventMap>(eventType: K, handler: EventHandler<AppEventMap[K]>) => { return true },
      unsubscribeAll: (eventType: keyof AppEventMap) => { return true },
      clear: () => {},
      reset: () => {},
      getSubscriptions: () => new Map(),
    }
    expect(typeof mockBus.subscribe).toBe('function')
    expect(typeof mockBus.publish).toBe('function')
    expect(typeof mockBus.clear).toBe('function')
  })
})
