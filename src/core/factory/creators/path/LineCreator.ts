import type { Create<PERSON>ineParams } from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point'
import type {
  Path, // Import Path namespace for Path.Line
} from '@/types/core/elementDefinitions'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
/**
 * Creator for Line Path Elements
 *
 * @remarks
 * This class extends {@link PathCreator} to specialize in creating
 * Line ({@link CoreElementType.LINE}) path elements. It uses `createCommonProperties`
 * for base attributes and then adds line-specific properties (start/end points, arrows).
 *
 * @module core/factory/creators/path/LineCreator
 */
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  ElementType as CoreElementType, // Import Path namespace for Path.Line
} from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'
import { PathCreator } from './PathCreator' // Changed from interface to abstract class

export class LineCreator extends PathCreator<Path.Line, CreateLineParams> {
  constructor() {
    super(CoreElementType.LINE)
  }

  /**
   * Creates a Line {@link Path.Line} element based on the provided parameters.
   *
   * @param params - A {@link CreateLineParams} object
   *                 containing all necessary information for line element creation.
   * @returns A Promise resolving to the created {@link Path.Line} element.
   */
  public async create(params: CreateLineParams): Promise<Path.Line> {
    if (params.type !== CoreElementType.LINE && params.type !== 'line') {
      throw new CoreError(ErrorType.InvalidPayload, `LineCreator cannot create type: ${String(params.type)}`)
    }

    const { id, start, end } = params
    if (!id)
      throw new CoreError(ErrorType.InvalidPayload, 'Line element requires an ID.')
    if (start == null || end == null)
      throw new CoreError(ErrorType.InvalidPayload, 'Line element requires start and end points.')

    // Normalize start and end points (assuming they are PointData)
    const startPoint: PointData = { x: start.x, y: start.y, z: start.z ?? 0 }
    const endPoint: PointData = { x: end.x, y: end.y, z: end.z ?? 0 }

    // Position for commonProps could be the start point or midpoint. Using start for simplicity.
    // The PathCreator's createCommonProperties will use params.start if params.position is not set.
    const commonProps = this.createCommonProperties(id, params)

    const lineElement: Path.Line = {
      ...commonProps,
      // type: CoreElementType.LINE, // Already set by commonProps from params.type
      // position: commonProps.position, // Determined by createCommonProperties, often from params.start
      start: startPoint,
      end: endPoint,
      properties: {
        // 设置默认的成本相关属性
        costUnitPrice: 1,
        costMultiplierOrCount: 0,
        costBasis: 'unit',
        // 然后合并其他属性
        ...(params.properties || {}),
        start: startPoint,
        end: endPoint,
        arrowStart: params.arrowStart ?? false,
        arrowEnd: params.arrowEnd ?? false,
      },
    }

    return lineElement
  }

  /**
   * Creates a default Line element.
   *
   * @param id - The unique identifier for the default line element.
   * @param position - The {@link PointData} for the default line's start position.
   * @param majorCategoryOverride - Optional override for majorCategory.
   * @param minorCategoryOverride - Optional override for minorCategory.
   * @param zLevelIdOverride - Optional override for zLevelId.
   * @param isFixedCategoryOverride - Optional override for isFixedCategory.
   * @returns A Promise resolving to the created default {@link Path.Line}.
   */
  public async createDefault(
    id: string,
    position: PointData, // Used as start point for the default line
    majorCategoryOverride?: MajorCategory,
    minorCategoryOverride?: MinorCategory,
    zLevelIdOverride?: string,
    isFixedCategoryOverride?: boolean,
  ): Promise<Path.Line> {
    // Get default settings for LINE elements
    const { getDefaultSettingsForElementType } = await import('@/config/defaultElementSettings')
    const defaults = getDefaultSettingsForElementType('LINE' as any)

    const params: CreateLineParams = {
      id,
      type: CoreElementType.LINE,
      majorCategory: majorCategoryOverride ?? MajorCategory.FURNITURE, // Default
      minorCategory: minorCategoryOverride,
      zLevelId: zLevelIdOverride,
      isFixedCategory: isFixedCategoryOverride,
      start: position,
      end: { x: position.x + 100, y: position.y + 50, z: position.z }, // Default end point
      metadata: { name: 'Line' },
      stroke: defaults.stroke ?? '#333333',
      strokeWidth: defaults.strokeWidth ?? 2,
      opacity: defaults.opacity ?? 1,
      visible: true,
      locked: false,
      properties: {
        // 设置默认的成本相关属性
        costUnitPrice: 1,
        costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1
        costBasis: 'unit', // 默认使用单位计算
        // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
        computedAreaStatus: 'none',
        computedPerimeterStatus: 'none',
        computedLengthStatus: 'none',
      },
    }

    return this.create(params)
  }
}
