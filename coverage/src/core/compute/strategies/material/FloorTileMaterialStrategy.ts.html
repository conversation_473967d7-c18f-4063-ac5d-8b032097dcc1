
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/core/compute/strategies/material/FloorTileMaterialStrategy.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> / <a href="index.html">src/core/compute/strategies/material</a> FloorTileMaterialStrategy.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/105</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/105</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * Material Calculation Strategy for Floor Tiles
 *
 * @remarks
 * This strategy implements the {@link MaterialCalculatorStrategy} to calculate
 * the required amount of floor tiles for a given area.
 *
 * It first calculates the area of the input `element` based on its type. Supported
 * element types for area calculation include `RECTANGLE`, `SQUARE`, `CIRCLE`, `ELLIPSE`,
 * `POLYGON` (and its variants like `TRIANGLE`, `HEXAGON`), `POLYLINE` (if closed),
 * and `ARC` (if a closed sector). Area calculation for basic shapes uses direct utility
 * functions, while for `POLYLINE` and `ARC`, it delegates to their respective area strategies
 * (`PolylineAreaStrategy`, `ArcAreaStrategy`).
 *
 * Once the area is determined, the strategy uses the `calculateTileCount` utility function
 * from `./materialUtils.ts` to compute the number of tiles, total area including wastage,
 * and optionally the number of boxes. This utility requires `tileWidth` and `tileHeight`
 * (from `options.unitSize`) and can consider `wastageRate`, `includeJoints`, `jointWidth`,
 * and `unitsPerBox` from {@link MaterialCalculationOptions}.
 *
 * The strategy validates that the `materialType` is 'tile' and that `options.unitSize`
 * is provided with positive dimensions.
 *
 * The `getElementType()` method returns a generic string 'tileAbleAreaShape', indicating
 * its applicability to any shape for which an area can be determined and tiling is relevant.
 *
 * @module core/compute/strategies/material/FloorTileMaterialStrategy
 * @see {@link MaterialCalculatorStrategy}
 * @see {@link MaterialCalculationOptions}
 * @see {@link MaterialCalculationResult}
 * @see {@link calculateTileCount}
 */
import type {
  MaterialCalculationOptions,
  MaterialCalculationResult,
  MaterialCalculatorStrategy, // Renamed IMaterialCalculatorStrategy for consistency
} from '@/types/core/compute' // Corrected import path
// IPoint interface
import type {
  Element,
  Shape,
} from '@/types/core/elementDefinitions'
// Import area strategies from their new location via the re-exporting index
<span class="cstat-no" title="statement not covered" >import { ArcAreaStrategy, PolylineAreaStrategy } from '@/core/compute/strategies/area'</span>
<span class="cstat-no" title="statement not covered" >import { calculateTileCount } from '@/core/compute/strategies/material/materialUtils'</span>
<span class="cstat-no" title="statement not covered" >import { calculateCircleArea, calculateEllipseArea } from '@/lib/utils/geometry/ellipseUtils'</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >import { calculateArea as calculatePolygonArea } from '@/lib/utils/geometry/polygonUtils'</span>
// Import area calculation utilities
<span class="cstat-no" title="statement not covered" >import { calculateRectangleArea } from '@/lib/utils/geometry/rectangleUtils' // calculateArea is exported as calculateRectangleArea from rectangleUtils</span>
<span class="cstat-no" title="statement not covered" >import { CoreError, ErrorType } from '@/services/system/error-service'</span>
<span class="cstat-no" title="statement not covered" >import {</span>
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'
&nbsp;
<span class="cstat-no" title="statement not covered" >export class FloorTileMaterialStrategy implements MaterialCalculatorStrategy {</span>
  // Renamed from IMaterialCalculatorStrategy for consistency
<span class="cstat-no" title="statement not covered" >  private polylineAreaStrategy: PolylineAreaStrategy</span>
<span class="cstat-no" title="statement not covered" >  private arcAreaStrategy: ArcAreaStrategy</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  constructor() {</span>
<span class="cstat-no" title="statement not covered" >    this.polylineAreaStrategy = new PolylineAreaStrategy()</span>
<span class="cstat-no" title="statement not covered" >    this.arcAreaStrategy = new ArcAreaStrategy()</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Calculates the amount of floor tile material required for a given element's area.
   *
   * @param element - The element (e.g., room, floor area) for which to calculate tile materials.
   *                  Its area will be calculated based on its type.
   * @param materialType - The type of material, expected to be 'tile'.
   * @param options - Optional {@link MaterialCalculationOptions}. Must include `unitSize` (tile dimensions).
   *                  Can also include `wastageRate`, `jointWidth`, `unitsPerBox`.
   * @returns A {@link MaterialCalculationResult} detailing the required tile amount, count, and wastage.
   * @throws {@link CoreError} if `materialType` is not 'tile', if `options.unitSize` is missing or invalid,
   *         or if the element's area cannot be determined or is invalid.
   */
<span class="cstat-no" title="statement not covered" >  public calculateMaterialAmount(</span>
<span class="cstat-no" title="statement not covered" >    element: Element,</span>
<span class="cstat-no" title="statement not covered" >    materialType: string,</span>
<span class="cstat-no" title="statement not covered" >    options?: MaterialCalculationOptions,</span>
<span class="cstat-no" title="statement not covered" >  ): MaterialCalculationResult {</span>
<span class="cstat-no" title="statement not covered" >    if (materialType.toLowerCase() !== 'tile') {</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(</span>
<span class="cstat-no" title="statement not covered" >        ErrorType.InvalidParameter,</span>
<span class="cstat-no" title="statement not covered" >        `FloorTileMaterialStrategy only supports 'tile' material type, got '${materialType}'`,</span>
<span class="cstat-no" title="statement not covered" >      )</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!options?.unitSize || options.unitSize.width &lt;= 0 || options.unitSize.height &lt;= 0) {</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(</span>
<span class="cstat-no" title="statement not covered" >        ErrorType.InvalidParameter,</span>
<span class="cstat-no" title="statement not covered" >        'Tile size (options.unitSize with positive width and height) must be provided for FloorTileMaterialStrategy.',</span>
<span class="cstat-no" title="statement not covered" >      )</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    const tileWidth = options.unitSize.width</span>
<span class="cstat-no" title="statement not covered" >    const tileHeight = options.unitSize.height</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    let area: number</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      switch (element.type) {</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.RECTANGLE:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.SQUARE: {</span>
<span class="cstat-no" title="statement not covered" >          const rect = element as unknown as Shape.Rectangle // Added unknown</span>
<span class="cstat-no" title="statement not covered" >          const width = typeof rect.properties?.width === 'number' ? rect.properties.width : 0</span>
<span class="cstat-no" title="statement not covered" >          const height = typeof rect.properties?.height === 'number' ? rect.properties.height : 0</span>
<span class="cstat-no" title="statement not covered" >          area = calculateRectangleArea(width, height)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.CIRCLE: {</span>
<span class="cstat-no" title="statement not covered" >          const circle = element as unknown as Shape.Circle // Added unknown</span>
<span class="cstat-no" title="statement not covered" >          const radius = typeof circle.properties?.radius === 'number' ? circle.properties.radius : 0</span>
<span class="cstat-no" title="statement not covered" >          area = calculateCircleArea(radius)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.ELLIPSE: {</span>
<span class="cstat-no" title="statement not covered" >          const ellipse = element as unknown as Shape.Ellipse // Added unknown</span>
<span class="cstat-no" title="statement not covered" >          const radiusX = typeof ellipse.properties?.radiusX === 'number' ? ellipse.properties.radiusX : 0</span>
<span class="cstat-no" title="statement not covered" >          const radiusY = typeof ellipse.properties?.radiusY === 'number' ? ellipse.properties.radiusY : 0</span>
<span class="cstat-no" title="statement not covered" >          area = calculateEllipseArea(radiusX, radiusY)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.POLYGON:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.TRIANGLE:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.HEXAGON: {</span>
<span class="cstat-no" title="statement not covered" >          const polygon = element as unknown as Shape.Polygon // Added unknown</span>
<span class="cstat-no" title="statement not covered" >          if (polygon.points === null || polygon.points === undefined || !Array.isArray(polygon.points) || polygon.points.length &lt; 3) { // Area calculation needs at least 3 points</span>
<span class="cstat-no" title="statement not covered" >            throw new CoreError(ErrorType.InvalidParameter, `Polygon (ID: ${element.id}) has insufficient points for area calculation.`)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          area = calculatePolygonArea(polygon.points)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.POLYLINE: // Area of a polyline is usually 0 unless it's closed and treated as a polygon</span>
<span class="cstat-no" title="statement not covered" >          area = this.polylineAreaStrategy.calculateArea(element)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.ARC: // Area of an arc is usually 0 unless it's a closed sector</span>
<span class="cstat-no" title="statement not covered" >          area = this.arcAreaStrategy.calculateArea(element)</span>
<span class="cstat-no" title="statement not covered" >          break</span>
<span class="cstat-no" title="statement not covered" >        default:</span>
<span class="cstat-no" title="statement not covered" >          throw new CoreError(</span>
<span class="cstat-no" title="statement not covered" >            ErrorType.InvalidElementType,</span>
<span class="cstat-no" title="statement not covered" >            `FloorTileMaterialStrategy cannot calculate area for element type: ${element.type}`,</span>
<span class="cstat-no" title="statement not covered" >          )</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    catch (error) {</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(</span>
<span class="cstat-no" title="statement not covered" >        ErrorType.ComputationError,</span>
<span class="cstat-no" title="statement not covered" >        `Failed to calculate area for element (ID: ${element.id}): ${error instanceof Error ? error.message : String(error)}`,</span>
<span class="cstat-no" title="statement not covered" >      )</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (typeof area !== 'number' || Number.isNaN(area) || area &lt; 0) { // Area can be 0</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(ErrorType.ComputationError, `Invalid or negative area calculated: ${area} for element ID: ${element.id}`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    if (area === 0) {</span>
<span class="cstat-no" title="statement not covered" >      console.warn(`[FloorTileMaterialStrategy] Calculated area for element ${element.id} is 0. No tiles needed.`)</span>
<span class="cstat-no" title="statement not covered" >      return { amount: 0, unit: 'm²', unitCount: 0, unitType: 'tile', amountWithWastage: 0 }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const tileOptions = {</span>
<span class="cstat-no" title="statement not covered" >      wastageRate: options?.wastageRate,</span>
<span class="cstat-no" title="statement not covered" >      includeJoints: options?.includeJoints,</span>
<span class="cstat-no" title="statement not covered" >      jointWidth: options?.jointWidth,</span>
<span class="cstat-no" title="statement not covered" >      unitsPerBox: options?.unitsPerBox,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return calculateTileCount(area, tileWidth, tileHeight, tileOptions)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Returns a generic string identifier for this strategy.
   * @remarks This strategy applies to any shape for which an area can be calculated
   *          and tiling is a relevant material application.
   * @returns The string 'tileAbleAreaShape'.
   */
<span class="cstat-no" title="statement not covered" >  public getElementType(): string {</span>
<span class="cstat-no" title="statement not covered" >    return 'tileAbleAreaShape' // Generic identifier indicating applicability to any shape whose area can be tiled</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // getMaterialType is not part of the MaterialCalculatorStrategy interface.
  // The specific material type (e.g., "ceramic_tile", "porcelain_tile") would be
  // passed in the `materialType` argument of `calculateMaterialAmount`.
  // This strategy specifically handles the generic 'tile' materialType logic.
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:58:01.486Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    