/**
 * Bounding Box Calculation Strategy for Text Elements
 *
 * @remarks
 * This strategy implements the {@link BoundingBoxCalculatorStrategy} for calculating
 * the axis-aligned bounding box of Text elements ({@link CoreElementType.TEXT}).
 *
 * The calculation involves:
 * 1. Retrieving the text content, font size, and font family from the element's properties.
 * 2. Estimating the text dimensions based on font metrics and character count.
 * 3. Using the element's position as the center point for the text.
 * 4. Creating a bounding box that encompasses the estimated text area.
 *
 * Since precise text measurement requires DOM/Canvas context, this strategy provides
 * a reasonable approximation based on typical font metrics.
 *
 * @module core/compute/strategies/bounding-box/TextBoundingBoxStrategy
 */

import type { BoundingBoxCalculatorStrategy } from '../../../../types/core/compute'
import type { BoundingBox as BoundingBoxInterface } from '../../../../types/core/element/geometry/bounding-box'
import type { Element, ShapeElement } from '../../../../types/core/elementDefinitions'
import { BoundingBoxClass } from '../../../../lib/utils/geometry/BoundingBoxClass'
import { CoreError } from '../../../../services/system/error-service/coreError'
import { ElementType as CoreElementType } from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

/**
 * Strategy for calculating bounding boxes of text elements.
 *
 * @implements {BoundingBoxCalculatorStrategy}
 */
export class TextBoundingBoxStrategy implements BoundingBoxCalculatorStrategy {
  /**
   * Returns the element type(s) this strategy handles.
   * @returns The TEXT element type.
   */
  public getElementType(): CoreElementType {
    return CoreElementType.TEXT
  }

  /**
   * Calculates the axis-aligned bounding box of a text element.
   *
   * @param element - The text element, expected to be of type {@link ShapeElement}
   *                  and of type {@link CoreElementType.TEXT}.
   * @returns A {@link BoundingBoxInterface} object representing the bounding box.
   * @throws {@link CoreError} if the element type is not TEXT.
   * @throws {@link CoreError} if the text element has invalid properties.
   */
  public calculateBoundingBox(element: Element): BoundingBoxInterface {
    if (element.type !== CoreElementType.TEXT) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `Expected TEXT, got ${element.type} for ID: ${element.id}`,
        undefined,
        { component: 'TextBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    const shapeElement = element as ShapeElement
    const position = shapeElement.position

    if (position == null || typeof position.x !== 'number' || typeof position.y !== 'number') {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid position for Text element ID: ${element.id}`,
        undefined,
        { component: 'TextBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    // Extract text properties
    const properties = shapeElement.properties || {}
    const textContent = (properties.text as string) || (properties.content as string) || 'Text'
    const fontSize = (properties.fontSize as number) || 16
    const fontFamily = (properties.fontFamily as string) || 'Arial'

    // Validate text properties
    if (typeof fontSize !== 'number' || fontSize <= 0) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid fontSize for Text element ID: ${element.id}`,
        undefined,
        { component: 'TextBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    // Estimate text dimensions
    const estimatedWidth = this.estimateTextWidth(textContent, fontSize, fontFamily)
    const estimatedHeight = this.estimateTextHeight(fontSize)

    // Text is typically centered at the position point
    // Calculate top-left corner for bounding box
    const topLeftX = position.x - estimatedWidth / 2
    const topLeftY = position.y - estimatedHeight / 2

    return new BoundingBoxClass(
      topLeftX,
      topLeftY,
      estimatedWidth,
      estimatedHeight,
      `bbox-${element.id}`,
    )
  }

  /**
   * Estimates the width of text based on character count and font size.
   * This is an approximation since precise measurement requires DOM/Canvas context.
   *
   * @param text - The text content.
   * @param fontSize - The font size in pixels.
   * @param fontFamily - The font family (used for basic adjustments).
   * @returns The estimated width in pixels.
   */
  private estimateTextWidth(text: string, fontSize: number, fontFamily: string): number {
    // Basic character width estimation
    // Most fonts have an average character width of about 0.6 times the font size
    let charWidthRatio = 0.6

    // Adjust for common font families
    const lowerFontFamily = fontFamily.toLowerCase()
    if (lowerFontFamily.includes('monospace') || lowerFontFamily.includes('courier')) {
      charWidthRatio = 0.6 // Monospace fonts are more predictable
    }
    else if (lowerFontFamily.includes('arial') || lowerFontFamily.includes('helvetica')) {
      charWidthRatio = 0.55
    }
    else if (lowerFontFamily.includes('times')) {
      charWidthRatio = 0.5
    }

    const estimatedWidth = text.length * fontSize * charWidthRatio

    // Add some padding to ensure the bounding box encompasses the text
    return Math.max(estimatedWidth + fontSize * 0.2, fontSize) // Minimum width of one font size
  }

  /**
   * Estimates the height of text based on font size.
   *
   * @param fontSize - The font size in pixels.
   * @returns The estimated height in pixels.
   */
  private estimateTextHeight(fontSize: number): number {
    // Text height is typically 1.2 to 1.5 times the font size to account for ascenders and descenders
    const lineHeight = fontSize * 1.3

    // Add some padding
    return lineHeight + fontSize * 0.1
  }
}
