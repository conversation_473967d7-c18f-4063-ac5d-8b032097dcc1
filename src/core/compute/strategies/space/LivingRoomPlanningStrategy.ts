// import Point from '@/types/core/element/geometry/point'; // Point interface (IPoint) // Unused
import type {
  ErgonomicsEvaluationResult,
  PathwayCheckResult,
  SpacePlanningStrategy,
  SpaceType,
} from '@/types/core/compute'
/**
 * Living Room Space Planning Strategy
 * Provides planning functionality for living room spaces, including space utilization calculation, pathway width check, etc.
 */
import type {
  ElementType as CoreElementType,
  Element,
  Shape,
  // Path, // Unused
  ShapeElement, // For properties access
} from '@/types/core/elementDefinitions'
// import { CoreError, ErrorType } from '@/services/system/error-service'; // Commented out as unused for now
// import {
//     calculateSpaceUtilization,
//     checkPathwaysWidth,
//     evaluateFurnitureLayout as evaluateFurnitureLayoutUtil
// } from '@/lib/utils/space/spacePlanning'; // Module not found
// import { BoundingBoxClass } from '@/lib/utils/geometry'; // BoundingBoxClass is exported from here // Unused
// import { PointClass } from '@/lib/utils/geometry'; // PointClass is exported from here // Unused

// Helper to get BoundingBoxClass from an Element // Unused
// function getElementBoundingBoxClass(element: Element): BoundingBoxClass | null {
//     if (!element) return null;
//     if (element.type === CoreElementType.RECTANGLE || element.type === CoreElementType.SQUARE) {
//         const shape = element as Shape.Rectangle;
//         if (shape.position && typeof shape.width === 'number' && typeof shape.height === 'number') {
//             return new BoundingBoxClass(shape.position.x - shape.width / 2, shape.position.y - shape.height / 2, shape.width, shape.height);
//         }
//     }
//      if (element.type === CoreElementType.CIRCLE) {
//         const shape = element as Shape.Circle;
//         if (shape.position && typeof shape.radius === 'number') {
//             return new BoundingBoxClass(shape.position.x - shape.radius, shape.position.y - shape.radius, shape.radius * 2, shape.radius * 2);
//         }
//     }
//     console.warn(`[LivingRoomPlanningStrategy] getElementBoundingBoxClass: Bounding box for type ${element.type} not fully implemented here.`);
//     return null;
// }

/**
 * Living Room Space Planning Strategy
 */
export class LivingRoomPlanningStrategy implements SpacePlanningStrategy {
  public calculateSpaceUtilization(/* elements: Element[], roomBoundaryElement: Element */): number { // Params unused due to commented out body
    // const roomBBoxClass = getElementBoundingBoxClass(roomBoundaryElement);
    // if (!roomBBoxClass) {
    //   throw new CoreError( ErrorType.INVALID_PARAMETER, 'Room boundary must be an element from which a bounding box can be derived.');
    // }
    // return calculateSpaceUtilization(elements, roomBBoxClass);
    console.warn('[LivingRoomPlanningStrategy] calculateSpaceUtilization is temporarily disabled due to missing dependencies.')
    return 0
  }

  public checkPathwayWidth(/* elements: Element[], pathways: Path.Line[], minWidth: number */): PathwayCheckResult[] { // Params unused due to commented out body
    // if (!Array.isArray(elements)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Elements must be an array');
    // if (!Array.isArray(pathways)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Pathways must be an array');
    // if (typeof minWidth !== 'number' || minWidth <= 0) {
    //   throw new CoreError(ErrorType.INVALID_PARAMETER, 'Minimum width must be a positive number');
    // }

    // const formattedPathways = pathways.map((path, index) => {
    //     const lineForUtil = {
    //         id: path.id ?? `pathway-${index}`,
    //         start: path.start as Point,
    //         end: path.end as Point,
    //         type: CoreElementType.LINE,
    //         getSubType: () => CoreElementType.LINE,
    //     } as any;
    //     return { id: path.id ?? `pathway-${index + 1}`, line: lineForUtil as import('@/lib/utils/element/path/lineImplementation').Line };
    // });

    // return checkPathwaysWidth(elements, formattedPathways, minWidth);
    console.warn('[LivingRoomPlanningStrategy] checkPathwayWidth is temporarily disabled due to missing dependencies.')
    return []
  }

  // This method is specific to this strategy or should align with evaluateErgonomics
  public evaluateLivingRoomFurnitureLayout(
    /* elements: Element[], roomBoundaryElement: Element, sofaElement?: Element */
    // tvElement?: Element, // Unused
    // coffeeTableElement?: Element // Unused
  ): { score: number, suggestions: string[] } { // Params unused due to commented out body
    // const roomBBoxClass = getElementBoundingBoxClass(roomBoundaryElement);
    // if (!roomBBoxClass) {
    //   throw new CoreError(ErrorType.INVALID_PARAMETER, 'Room boundary for furniture layout must be derivable to a BoundingBox.');
    // }

    // // Use the utility if it's generic enough, or implement specific living room logic here.
    // // For now, assuming evaluateFurnitureLayoutUtil can be adapted or is a placeholder for more specific logic.
    // // We might need to pass a primary seating element instead of just 'bed'.
    // const primarySeating = sofaElement ?? elements.find(el => (el as ShapeElement).properties?.customType === 'sofa' || el.type === CoreElementType.FURNITURE);

    // if (primarySeating) {
    //     const seatingShape = primarySeating as Shape.Rectangle; // Assuming sofa is rectangular
    //     const seatingPos = seatingShape.position;
    //     const seatingWidth = seatingShape.width;
    //     const seatingHeight = seatingShape.height;

    //     if (seatingPos && typeof seatingWidth === 'number' && typeof seatingHeight === 'number') {
    //         return evaluateFurnitureLayoutUtil(
    //           elements.filter(el => el.id !== primarySeating.id),
    //           roomBBoxClass,
    //           {
    //             position: new PointClass(seatingPos.x, seatingPos.y),
    //             width: seatingWidth,
    //             height: seatingHeight
    //           }
    //         );
    //     }
    // }
    // return { score: 0, suggestions: ["Primary seating element not found or has invalid dimensions for layout evaluation."] };
    console.warn('[LivingRoomPlanningStrategy] evaluateLivingRoomFurnitureLayout is temporarily disabled due to missing dependencies.')
    return { score: 0, suggestions: ['Layout evaluation temporarily disabled.'] }
  }

  public evaluateErgonomics(
    elements: Element[],
    deskElement: Element, // Could be a coffee table or a small desk
    chairElement: Element, // Could be a sofa, armchair, or an actual chair
  ): ErgonomicsEvaluationResult {
    const issues: string[] = []
    const recommendations: string[] = []

    if (!deskElement?.type || !chairElement?.type) {
      // Allow for cases where a formal desk/chair setup isn't the focus of a living room
      recommendations.push('Living room ergonomics often prioritize comfort and viewing angles over traditional desk setups.')
      // return { isValid: true, issues, recommendations }; // Or continue with checks if elements are present
    }

    const workSurface = deskElement as unknown as Shape.Rectangle // Assuming it's a surface
    const seating = chairElement as unknown as Shape.Rectangle // Assuming it's a seating item

    const surfaceHeight = workSurface.height ?? 0
    const seatingShapeElement = seating as unknown as ShapeElement
    const seatingProperties = seatingShapeElement.properties as { seatHeight?: number } | undefined
    const seatHeight = seatingProperties?.seatHeight ?? (seating.height ? seating.height * 0.6 : 0.4)

    if (deskElement != null && chairElement != null) { // Only proceed if both are somewhat defined
      if (surfaceHeight >= 0.7 && surfaceHeight <= 0.76) { // Standard desk height
        recommendations.push(`Desk/table height of ${surfaceHeight.toFixed(2)}m is suitable for upright work.`)
        if (seatHeight < 0.4 || seatHeight > 0.55) {
          issues.push(`Chair seat height ${seatHeight.toFixed(2)}m may not be ideal for desk height ${surfaceHeight.toFixed(2)}m.`)
        }
      }
      else if (surfaceHeight >= 0.4 && surfaceHeight < 0.7) { // Coffee table / low table
        recommendations.push(`Table height ${surfaceHeight.toFixed(2)}m suggests casual use (e.g., coffee table).`)
        if (seatHeight > 0.45) { // If using a regular chair with a low table
          issues.push(`Using a chair with seat height ${seatHeight.toFixed(2)}m at a low table may be uncomfortable.`)
        }
      }
    }

    if (elements.some(el => (el as ShapeElement).properties?.customType === 'tv_stand' || el.type === ('television' as CoreElementType))) {
      recommendations.push('Ensure comfortable viewing angles and distances to the TV from main seating areas.')
    }

    recommendations.push('Arrange seating to facilitate conversation.')
    recommendations.push('Ensure adequate task lighting for reading or other activities.')

    return {
      isValid: issues.length === 0,
      issues,
      recommendations,
    }
  }

  public getSpaceType(): SpaceType {
    return 'living'
  }
}
