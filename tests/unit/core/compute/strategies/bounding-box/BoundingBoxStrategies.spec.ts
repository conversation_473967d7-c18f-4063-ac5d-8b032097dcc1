import { beforeEach, describe, expect, it } from 'vitest'
import { CircleBoundingBoxStrategy } from '@/core/compute/strategies/bounding-box/CircleBoundingBoxStrategy'
import { EllipseBoundingBoxStrategy } from '@/core/compute/strategies/bounding-box/EllipseBoundingBoxStrategy'
import { LineBoundingBoxStrategy } from '@/core/compute/strategies/bounding-box/LineBoundingBoxStrategy'
import { PolygonBoundingBoxStrategy } from '@/core/compute/strategies/bounding-box/PolygonBoundingBoxStrategy'
import { PolylineBoundingBoxStrategy } from '@/core/compute/strategies/bounding-box/PolylineBoundingBoxStrategy'
import { RectangleBoundingBoxStrategy } from '@/core/compute/strategies/bounding-box/RectangleBoundingBoxStrategy'
import { Line } from '@/types/core/element/path/linePathTypes'
import { Polyline } from '@/types/core/element/path/polylinePathTypes'
import { Circle, Ellipse } from '@/types/core/element/shape/ellipseShapeTypes'
import { Rectangle } from '@/types/core/element/shape/rectangleShapeTypes'
import { Polygon } from '@/types/core/element/shape/shape'

describe('boundingBox Strategies Integration', () => {
  let circleStrategy: CircleBoundingBoxStrategy
  let ellipseStrategy: EllipseBoundingBoxStrategy
  let lineStrategy: LineBoundingBoxStrategy
  let polygonStrategy: PolygonBoundingBoxStrategy
  let polylineStrategy: PolylineBoundingBoxStrategy
  let rectangleStrategy: RectangleBoundingBoxStrategy

  beforeEach(() => {
    circleStrategy = new CircleBoundingBoxStrategy()
    ellipseStrategy = new EllipseBoundingBoxStrategy()
    lineStrategy = new LineBoundingBoxStrategy()
    polygonStrategy = new PolygonBoundingBoxStrategy()
    polylineStrategy = new PolylineBoundingBoxStrategy()
    rectangleStrategy = new RectangleBoundingBoxStrategy()
  })

  it('should return correct shape types', () => {
    expect(circleStrategy.getElementType()).toBe('circle')
    expect(ellipseStrategy.getElementType()).toBe('ellipse')
    expect(lineStrategy.getElementType()).toBe('line')
    expect(polygonStrategy.getElementType()).toBe('polygon')
    expect(polylineStrategy.getElementType()).toBe('polyline')
    expect(rectangleStrategy.getElementType()).toBe('rectangle')
  })

  it('should handle null elements gracefully', () => {
    // @ts-ignore - Testing null input
    expect(circleStrategy.calculateBoundingBox(null)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })

    // @ts-ignore - Testing null input
    expect(ellipseStrategy.calculateBoundingBox(null)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })

    // @ts-ignore - Testing null input
    expect(lineStrategy.calculateBoundingBox(null)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })

    // @ts-ignore - Testing null input
    expect(polygonStrategy.calculateBoundingBox(null)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })

    // @ts-ignore - Testing null input
    expect(polylineStrategy.calculateBoundingBox(null)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })

    // @ts-ignore - Testing null input
    expect(rectangleStrategy.calculateBoundingBox(null)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle undefined elements gracefully', () => {
    // @ts-ignore - Testing undefined input
    expect(circleStrategy.calculateBoundingBox(undefined)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })

    // @ts-ignore - Testing undefined input
    expect(ellipseStrategy.calculateBoundingBox(undefined)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })

    // @ts-ignore - Testing undefined input
    expect(lineStrategy.calculateBoundingBox(undefined)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })

    // @ts-ignore - Testing undefined input
    expect(polygonStrategy.calculateBoundingBox(undefined)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })

    // @ts-ignore - Testing undefined input
    expect(polylineStrategy.calculateBoundingBox(undefined)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })

    // @ts-ignore - Testing undefined input
    expect(rectangleStrategy.calculateBoundingBox(undefined)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle elements with missing required properties', () => {
    // Create mock elements with missing properties
    const invalidCircle = { id: 'invalid-circle', constructor: { name: 'Circle' } }
    Object.setPrototypeOf(invalidCircle, Circle.prototype)

    const invalidEllipse = { id: 'invalid-ellipse', constructor: { name: 'Ellipse' } }
    Object.setPrototypeOf(invalidEllipse, Ellipse.prototype)

    const invalidLine = { id: 'invalid-line', constructor: { name: 'Line' } }
    Object.setPrototypeOf(invalidLine, Line.prototype)

    const invalidPolygon = { id: 'invalid-polygon', constructor: { name: 'Polygon' } }
    Object.setPrototypeOf(invalidPolygon, Polygon.prototype)

    const invalidPolyline = { id: 'invalid-polyline', constructor: { name: 'Polyline' } }
    Object.setPrototypeOf(invalidPolyline, Polyline.prototype)

    const invalidRectangle = { id: 'invalid-rectangle', constructor: { name: 'Rectangle' } }
    Object.setPrototypeOf(invalidRectangle, Rectangle.prototype)

    // Test with invalid elements
    expect(circleStrategy.calculateBoundingBox(invalidCircle as any)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })

    expect(ellipseStrategy.calculateBoundingBox(invalidEllipse as any)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })

    expect(lineStrategy.calculateBoundingBox(invalidLine as any)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })

    expect(polygonStrategy.calculateBoundingBox(invalidPolygon as any)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })

    expect(polylineStrategy.calculateBoundingBox(invalidPolyline as any)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })

    expect(rectangleStrategy.calculateBoundingBox(invalidRectangle as any)).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })
})
