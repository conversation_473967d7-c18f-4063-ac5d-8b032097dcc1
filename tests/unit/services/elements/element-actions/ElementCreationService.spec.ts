import type { ElementFactory } from '@/core/factory'
import type { AppEventMap, EventBus } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ElementCreationService } from '@/services/elements/element-actions/elementCreationService'
import { ElementType } from '@/types/core/elementDefinitions'

// Mock dependencies
const mockElementFactory = {
  createRectangle: vi.fn(),
  createCircle: vi.fn(),
  createEllipse: vi.fn(),
  createPolygon: vi.fn(),
  createTriangle: vi.fn(),
  createLine: vi.fn(),
  createPolyline: vi.fn(),
  createShape: vi.fn(),
  createElement: vi.fn(),
} as unknown as ElementFactory

const mockEventBus = {
  publish: vi.fn(),
  subscribe: vi.fn(),
  unsubscribe: vi.fn(),
  clear: vi.fn(),
} as unknown as EventBus<AppEventMap>

const mockLogger = {
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  debug: vi.fn(),
  setContext: vi.fn(),
  getConfig: vi.fn(),
  setConfig: vi.fn(),
} as unknown as LoggerService

describe('elementCreationService', () => {
  let service: ElementCreationService

  beforeEach(() => {
    vi.clearAllMocks()
    service = new ElementCreationService(mockElementFactory, mockEventBus, mockLogger)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('constructor', () => {
    it('should initialize with dependencies', () => {
      expect(service).toBeDefined()
      expect(service.serviceId).toBeDefined()
      expect(mockLogger.info).toHaveBeenCalledWith('[ElementCreationService] Initialized.')
    })

    it('should log debug information', () => {
      expect(mockLogger.debug).toHaveBeenCalledWith(
        '[ElementCreationService CONSTRUCTOR] Initialized with factory, eventBus, logger.',
      )
    })
  })

  describe('static create method', () => {
    it('should throw error when factory is not provided', () => {
      expect(() => ElementCreationService.create()).toThrow()
    })

    it('should create instance with provided dependencies', () => {
      const instance = new ElementCreationService(mockElementFactory, mockEventBus, mockLogger)
      expect(instance).toBeInstanceOf(ElementCreationService)
      expect(instance.serviceId).toBeDefined()
    })
  })

  describe('handleRequest', () => {
    it('should handle valid rectangle creation request', async () => {
      const mockShape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100, z: 0 },
        properties: { width: 200, height: 150 },
      }

      mockElementFactory.createShape = vi.fn().mockResolvedValue(mockShape)

      const event = {
        type: 'SHAPE_CREATE',
        payload: {
          elementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: { width: 200, height: 150 },
        },
        timestamp: Date.now(),
      }

      await service.handleRequest(event)

      expect(mockLogger.debug).toHaveBeenCalled()
      expect(mockElementFactory.createShape).toHaveBeenCalled()
    })

    it('should handle invalid payload gracefully', async () => {
      const event = {
        type: 'SHAPE_CREATE',
        payload: {
          // Missing required fields
        },
        timestamp: Date.now(),
      }

      await service.handleRequest(event as any)

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Invalid or missing payload in ElementCreateEvent',
        event.payload,
      )
    })

    it('should handle missing elementType', async () => {
      const event = {
        type: 'SHAPE_CREATE',
        payload: {
          position: { x: 100, y: 100 },
          // Missing elementType
        },
        timestamp: Date.now(),
      }

      await service.handleRequest(event as any)

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Invalid or missing payload in ElementCreateEvent',
        event.payload,
      )
    })

    it('should handle missing position', async () => {
      const event = {
        type: 'SHAPE_CREATE',
        payload: {
          elementType: ElementType.RECTANGLE,
          // Missing position
        },
        timestamp: Date.now(),
      }

      await service.handleRequest(event as any)

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Invalid or missing payload in ElementCreateEvent',
        event.payload,
      )
    })

    it('should handle circle creation request', async () => {
      const event = {
        type: 'SHAPE_CREATE',
        payload: {
          elementType: ElementType.CIRCLE,
          position: { x: 150, y: 150 },
          properties: { radius: 50 },
        },
        timestamp: Date.now(),
      }

      await service.handleRequest(event)

      expect(mockLogger.debug).toHaveBeenCalled()
    })

    it('should handle ellipse creation request', async () => {
      const event = {
        type: 'SHAPE_CREATE',
        payload: {
          elementType: ElementType.ELLIPSE,
          position: { x: 200, y: 200 },
          properties: { radiusX: 60, radiusY: 40 },
        },
        timestamp: Date.now(),
      }

      await service.handleRequest(event)

      expect(mockLogger.debug).toHaveBeenCalled()
    })

    it('should handle polygon creation request', async () => {
      const event = {
        type: 'SHAPE_CREATE',
        payload: {
          elementType: ElementType.POLYGON,
          position: { x: 250, y: 250 },
          properties: { sides: 6, radius: 80 },
        },
        timestamp: Date.now(),
      }

      await service.handleRequest(event)

      expect(mockLogger.debug).toHaveBeenCalled()
    })

    it('should handle triangle creation request', async () => {
      const event = {
        type: 'SHAPE_CREATE',
        payload: {
          elementType: ElementType.TRIANGLE,
          position: { x: 300, y: 300 },
          properties: { sides: 3, radius: 70 },
        },
        timestamp: Date.now(),
      }

      await service.handleRequest(event)

      expect(mockLogger.debug).toHaveBeenCalled()
    })

    it('should handle line creation request', async () => {
      const event = {
        type: 'SHAPE_CREATE',
        payload: {
          elementType: ElementType.LINE,
          position: { x: 0, y: 0 },
          properties: {
            startPoint: { x: 0, y: 0, z: 0 },
            endPoint: { x: 100, y: 100, z: 0 },
          },
        },
        timestamp: Date.now(),
      }

      await service.handleRequest(event)

      expect(mockLogger.debug).toHaveBeenCalled()
    })

    it('should handle requests with additional properties', async () => {
      const event = {
        type: 'SHAPE_CREATE',
        payload: {
          elementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 200,
            height: 150,
            fill: '#ff0000',
            stroke: '#000000',
            strokeWidth: 2,
            opacity: 0.8,
            rotation: 45,
            layer: 'foreground',
          },
        },
        timestamp: Date.now(),
      }

      await service.handleRequest(event)

      expect(mockLogger.debug).toHaveBeenCalled()
    })

    it('should handle requests with custom id', async () => {
      const event = {
        type: 'SHAPE_CREATE',
        payload: {
          elementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            id: 'custom-rect-id',
            width: 200,
            height: 150,
          },
        },
        timestamp: Date.now(),
      }

      await service.handleRequest(event)

      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Generated ID: custom-rect-id'),
      )
    })

    it('should generate UUID when no id provided', async () => {
      const event = {
        type: 'SHAPE_CREATE',
        payload: {
          elementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: { width: 200, height: 150 },
        },
        timestamp: Date.now(),
      }

      await service.handleRequest(event)

      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringMatching(/Generated ID: RECTANGLE-[a-f0-9-]{36}/),
      )
    })
  })

  describe('error handling', () => {
    it('should handle factory errors gracefully', async () => {
      mockElementFactory.createShape = vi.fn().mockRejectedValue(new Error('Factory error'))

      const event = {
        type: 'SHAPE_CREATE',
        payload: {
          elementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: { width: 200, height: 150 },
        },
        timestamp: Date.now(),
      }

      await expect(service.handleRequest(event)).resolves.not.toThrow()
    })

    it('should handle validation errors', async () => {
      const event = {
        type: 'SHAPE_CREATE',
        payload: {
          elementType: ElementType.RECTANGLE,
          position: { x: 'invalid', y: 'invalid' }, // Invalid position
          properties: { width: 200, height: 150 },
        },
        timestamp: Date.now(),
      }

      await service.handleRequest(event as any)

      expect(mockLogger.error).toHaveBeenCalled()
    })
  })
})
