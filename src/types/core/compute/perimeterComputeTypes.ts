/**
 * Defines the Strategy Interface for Perimeter Calculation
 *
 * @remarks
 * This module introduces the {@link PerimeterCalculatorStrategy} interface,
 * a core component of the strategy design pattern applied to geometric computations.
 * This pattern allows for the encapsulation of different perimeter (or length for open paths)
 * calculation algorithms, each tailored to specific types of {@link Element | elements}.
 *
 * Concrete implementations of this interface will provide the specific logic for
 * computing the perimeter for one or more element types.
 *
 * @module types/core/compute/perimeterComputeTypes
 * @see {@link Element} from '@/types/core/elementDefinitions'
 * @see {@link ../computeInterfaces} for other computation strategy interfaces.
 */

import type { Element } from '@/types/core/elementDefinitions'

/**
 * Defines the contract for a perimeter or length calculation strategy.
 *
 * @remarks
 * Any class implementing this interface is responsible for providing a specific
 * algorithm to calculate the perimeter (for closed shapes) or length (for open paths)
 * of an {@link Element}. Concrete strategies will typically be specialized for one or
 * more {@link ElementType | element types}.
 *
 * @see {@link Element} from '@/types/core/elementDefinitions'
 * @see {@link ElementType} from '@/types/core/elementDefinitions'
 */
export interface PerimeterCalculatorStrategy {
  /**
   * Calculates the perimeter (for closed shapes) or length (for open paths) of the provided element.
   *
   * @param element - The {@link Element} for which to calculate the perimeter/length.
   *                  The element is expected to have properties necessary for the specific
   *                  strategy's calculation (e.g., width, height, radius, points).
   * @returns The calculated perimeter or length as a `number`. The units are implicitly
   *          defined by the units used in the element's dimensions.
   * @throws {@link Error} if the element type is invalid or unsupported by the strategy,
   *         or if the element's properties are insufficient for calculation.
   */
  calculatePerimeter: (element: Element) => number

  /**
   * Gets the specific element type (or types) that this strategy is designed to handle.
   *
   * @returns A string identifier (or an array of string identifiers) corresponding to
   *          one or more {@link ElementType | ElementTypes} that this strategy supports.
   * @remarks Consider returning `ElementType` or `ElementType[]` for enhanced type safety in future revisions.
   */
  getElementType: () => string | string[]
}
