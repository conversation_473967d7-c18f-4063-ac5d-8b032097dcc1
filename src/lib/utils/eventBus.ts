/**
 * 简单的事件总线实现，用于组件间通信
 */

// 事件类型
export enum EventType {
  PATH_DRAWING_COMPLETED = 'PATH_DRAWING_COMPLETED',
}

// 事件监听器类型
type EventListener = (data?: any) => void

// 事件总线类
class EventBus {
  private listeners: Map<EventType, EventListener[]> = new Map()

  /**
   * 订阅事件
   * @param eventType 事件类型
   * @param listener 事件监听器
   * @returns 取消订阅的函数
   */
  subscribe(eventType: EventType, listener: EventListener): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, [])
    }

    const eventListeners = this.listeners.get(eventType)!
    eventListeners.push(listener)

    // 返回取消订阅的函数
    return () => {
      const index = eventListeners.indexOf(listener)
      if (index !== -1) {
        eventListeners.splice(index, 1)
      }
    }
  }

  /**
   * 发布事件
   * @param eventType 事件类型
   * @param data 事件数据
   */
  publish(eventType: EventType, data?: any): void {
    if (!this.listeners.has(eventType)) {
      return
    }

    const eventListeners = this.listeners.get(eventType)!
    eventListeners.forEach((listener) => {
      try {
        listener(data)
      }
      catch (error) {
        console.error(`Error in event listener for ${eventType}:`, error)
      }
    })
  }

  /**
   * 清除特定事件类型的所有监听器
   * @param eventType 事件类型
   */
  clear(eventType: EventType): void {
    this.listeners.delete(eventType)
  }

  /**
   * 清除所有事件监听器
   */
  clearAll(): void {
    this.listeners.clear()
  }
}

// 创建单例实例
export const eventBus = new EventBus()
