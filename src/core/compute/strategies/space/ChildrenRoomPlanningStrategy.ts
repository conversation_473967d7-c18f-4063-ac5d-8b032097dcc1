// import Point from '@/types/core/element/geometry/point'; // Unused after commenting out methods
import type {
  ErgonomicsEvaluationResult,
  PathwayCheckResult,
  SpacePlanningStrategy,
  SpaceType,
} from '@/types/core/compute'
/**
 * Children's Room Planning Strategy
 * Provides planning functionality for children's rooms, including safety factors, growth space requirements, storage space, etc.
 */
import type {
  Element,
  Shape,
  // Path, // Unused
  // MetadataProperties, // Unused
  ShapeElement,
} from '@/types/core/elementDefinitions'
// import {
//     calculateSpaceUtilization,
//     checkPathwaysWidth
// } from '@/lib/utils/space/spacePlanning'; // Module not found
import { BoundingBoxClass } from '@/lib/utils/geometry' // BoundingBoxClass is exported from here
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'

// Helper to get BoundingBoxClass from an Element
function getElementBoundingBoxClass(element: Element): BoundingBoxClass | null {
  if (element === null || element === undefined)
    return null
  if (element.type === CoreElementType.RECTANGLE || element.type === CoreElementType.SQUARE) {
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement.position !== null && shapeElement.position !== undefined && shapeElement.properties !== null && shapeElement.properties !== undefined
      && typeof shapeElement.properties.width === 'number'
      && typeof shapeElement.properties.height === 'number') {
      return new BoundingBoxClass(
        shapeElement.position.x - shapeElement.properties.width / 2,
        shapeElement.position.y - shapeElement.properties.height / 2,
        shapeElement.properties.width,
        shapeElement.properties.height,
      )
    }
  }
  if (element.type === CoreElementType.CIRCLE) {
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement.position !== null && shapeElement.position !== undefined && shapeElement.properties !== null && shapeElement.properties !== undefined
      && typeof shapeElement.properties.radius === 'number') {
      return new BoundingBoxClass(
        shapeElement.position.x - shapeElement.properties.radius,
        shapeElement.position.y - shapeElement.properties.radius,
        shapeElement.properties.radius * 2,
        shapeElement.properties.radius * 2,
      )
    }
  }
  console.warn(`[ChildrenRoomPlanningStrategy] getElementBoundingBoxClass: Bounding box for type ${element.type} not fully implemented or properties missing.`)
  return null
}

/**
 * Children's Room Planning Strategy
 */
export class ChildrenRoomPlanningStrategy implements SpacePlanningStrategy {
  public calculateSpaceUtilization(/* elements: Element[], roomBoundaryElement: Element */): number { // Params unused due to commented out body
    // const roomBBoxClass = getElementBoundingBoxClass(roomBoundaryElement);
    // if (!roomBBoxClass) {
    //   throw new CoreError( ErrorType.INVALID_PARAMETER, 'Room boundary must be an element from which a bounding box can be derived.');
    // }
    // return calculateSpaceUtilization(elements, roomBBoxClass);
    console.warn('[ChildrenRoomPlanningStrategy] calculateSpaceUtilization is temporarily disabled due to missing dependencies.')
    return 0
  }

  public checkPathwayWidth(/* elements: Element[], pathways: Path.Line[], minWidth: number */): PathwayCheckResult[] { // Params unused due to commented out body
    // if (!Array.isArray(elements)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Elements must be an array');
    // if (!Array.isArray(pathways)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Pathways must be an array');
    // if (typeof minWidth !== 'number' || minWidth <= 0) {
    //   throw new CoreError(ErrorType.INVALID_PARAMETER, 'Minimum width must be a positive number');
    // }

    // const formattedPathways = pathways.map((path, index) => {
    //     const lineForUtil = {
    //         id: path.id ?? `pathway-${index}`,
    //         start: path.start as Point,
    //         end: path.end as Point,
    //         type: CoreElementType.LINE,
    //         getSubType: () => CoreElementType.LINE,
    //     } as any;
    //     return { id: path.id ?? `pathway-${index + 1}`, line: lineForUtil as import('@/lib/utils/element/path/lineImplementation').Line };
    // });

    // return checkPathwaysWidth(elements, formattedPathways, minWidth);
    console.warn('[ChildrenRoomPlanningStrategy] checkPathwayWidth is temporarily disabled due to missing dependencies.')
    return []
  }

  public evaluateSafety(elements: Element[]): { isValid: boolean, issues: string[], recommendations: string[] } {
    const issues: string[] = []
    const recommendations: string[] = []

    for (const element of elements) {
      const elWithProps = element as ShapeElement
      const elementType = elWithProps.type as CoreElementType
      const elementCategory = elWithProps.properties?.category as string || ''
      const elementMaterial = elWithProps.properties?.material as string || ''

      if ((elementCategory === 'furniture' || elementType === CoreElementType.FURNITURE) && (elWithProps.properties?.isSecured !== true)) {
        issues.push(`Furniture (${elWithProps.id}) is not secured, risk of tipping over`)
        recommendations.push('Secure tall furniture to walls to prevent tipping')
      }

      if ((elementCategory === 'furniture' || elementType === CoreElementType.FURNITURE) && (elWithProps.properties?.roundedCorners !== true)) {
        recommendations.push(`Consider furniture with rounded corners or use corner protectors for element ${elWithProps.id}.`)
      }

      if (elementType === CoreElementType.WINDOW) {
        const windowProps = elWithProps.properties as { hasSafetyLock?: boolean, sillHeight?: number } | undefined
        if (windowProps?.hasSafetyLock !== true) {
          issues.push(`Window (${elWithProps.id}) lacks child safety locks, fall risk`)
          recommendations.push('Install child safety locks to limit window opening')
        }
        const windowSillHeight = windowProps?.sillHeight ?? 0.9
        if (windowSillHeight < 0.9) {
          issues.push(`Window (${elWithProps.id}) sill height is ${windowSillHeight}m, below 0.9m, fall risk`)
          recommendations.push('Install window guards or ensure windows have limiters if sill height is low.')
        }
      }

      // Assuming 'electrical_outlet' might be a custom type string or a property
      if (elementType === ('electrical_outlet' as CoreElementType) || elWithProps.properties?.customType === 'electrical_outlet') {
        const outletProps = elWithProps.properties as { hasChildProof?: boolean } | undefined
        if (!outletProps?.hasChildProof) {
          issues.push(`Electrical outlet (${elWithProps.id}) lacks child protection`)
          recommendations.push('Install outlet covers or use child-safe outlets')
        }
      }

      if (elementMaterial.includes('glass') && (elWithProps.properties?.isSafetyGlass !== true)) {
        issues.push(`Element with glass material (${elWithProps.id}) is not safety glass`)
        recommendations.push(`Use tempered glass or acrylic glass instead of regular glass for element ${elWithProps.id}`)
      }
    }
    recommendations.push('Ensure all furniture and decorations are free of harmful substances like lead paint.')
    return { isValid: issues.length === 0, issues, recommendations }
  }

  public evaluateGrowthSpace(elements: Element[], childAge: number): { isValid: boolean, recommendations: string[] } {
    if (typeof childAge !== 'number' || childAge < 0 || childAge > 18) {
      throw new CoreError(ErrorType.InvalidParameter, 'Child age must be a number between 0-18')
    }
    const recommendations: string[] = []
    let isValid = true
    const roomArea = this.calculateRoomArea(elements)

    if (childAge < 3) {
      if (roomArea < 8) {
        isValid = false
        recommendations.push(`Room area ${roomArea.toFixed(2)}m² is small for 0-3 yrs (min 8m²).`)
      }
      recommendations.push('Ensure ample crawling/activity space for infants.')
    }
    else if (childAge < 6) {
      if (roomArea < 10) {
        isValid = false
        recommendations.push(`Room area ${roomArea.toFixed(2)}m² is small for 3-6 yrs (min 10m²).`)
      }
      recommendations.push('Provide play and simple learning areas for preschoolers.')
    }
    else if (childAge < 12) {
      if (roomArea < 12) {
        isValid = false
        recommendations.push(`Room area ${roomArea.toFixed(2)}m² is small for 6-12 yrs (min 12m²).`)
      }
      recommendations.push('Dedicate study area with desk/bookshelf for school-age children.')
    }
    else {
      if (roomArea < 15) {
        isValid = false
        recommendations.push(`Room area ${roomArea.toFixed(2)}m² is small for 12-18 yrs (min 15m²).`)
      }
      recommendations.push('Provide separate study/rest areas for teenagers.')
    }
    return { isValid, recommendations }
  }

  public evaluateErgonomics(elements: Element[], deskElement: Element, chairElement: Element): ErgonomicsEvaluationResult {
    const issues: string[] = []
    const recommendations: string[] = []
    if (deskElement === null || deskElement === undefined || chairElement === null || chairElement === undefined) { // Check if elements are provided
      throw new CoreError(ErrorType.InvalidParameter, 'Must provide desk and chair elements for ergonomics evaluation.')
    }
    const childAge = this.extractChildAgeFromElements(elements) ?? 10

    // Assume desk and chair are Shape.Rectangle or have a 'height' property and 'properties'
    const deskShape = deskElement as unknown as Shape.Rectangle
    const chairShape = chairElement as unknown as Shape.Rectangle

    const deskHeight = deskShape.height ?? 0

    if (childAge < 6 && (deskHeight < 0.5 || deskHeight > 0.6))
      issues.push(`Desk height ${deskHeight.toFixed(2)}m not ideal for 3-6 yrs (0.5-0.6m).`)
    else if (childAge < 12 && (deskHeight < 0.6 || deskHeight > 0.7))
      issues.push(`Desk height ${deskHeight.toFixed(2)}m not ideal for 6-12 yrs (0.6-0.7m).`)
    else if (childAge >= 12 && (deskHeight < 0.7 || deskHeight > 0.75))
      issues.push(`Desk height ${deskHeight.toFixed(2)}m not ideal for 12+ yrs (0.7-0.75m).`)

    const chairShapeAsShapeElement = chairShape as unknown as ShapeElement
    const chairProperties = chairShapeAsShapeElement.properties as { seatHeight?: number } | undefined
    const chairSeatHeight = chairProperties?.seatHeight ?? (chairShape.height ? chairShape.height * 0.6 : 0.45)
    if (chairSeatHeight < 0.3 || chairSeatHeight > 0.55) { // Adjusted range slightly
      issues.push(`Chair seat height of ${chairSeatHeight.toFixed(2)}m may not be appropriate for the child's age.`)
      recommendations.push('Consider an adjustable height chair.')
    }

    recommendations.push('Use adjustable furniture. Ensure feet are flat on floor/footrest.')
    return { isValid: issues.length === 0, issues, recommendations }
  }

  private extractChildAgeFromElements(elements: Element[]): number | undefined {
    for (const element of elements) {
      const elWithProps = element as ShapeElement
      if (elWithProps.properties?.roomType === 'children' && typeof elWithProps.properties.childAge === 'number')
        return elWithProps.properties.childAge
      if (typeof element.metadata?.childAge === 'number')
        return element.metadata.childAge
    }
    return undefined
  }

  private calculateRoomArea(elements: Element[]): number {
    const roomElement = elements.find(el => (el as ShapeElement).properties?.isRoomBoundary === true || el.type === ('room_boundary' as CoreElementType))
    if (roomElement) {
      const bbox = getElementBoundingBoxClass(roomElement)
      return bbox ? bbox.width * bbox.height : 0
    }
    const overallBBox = this.calculateOverallBoundingBox(elements)
    return overallBBox ? overallBBox.width * overallBBox.height : 0
  }

  private calculateOverallBoundingBox(elements: Element[]): BoundingBoxClass | null {
    if (elements === null || elements === undefined || elements.length === 0)
      return null
    let minX = Infinity
    let minY = Infinity
    let maxX = -Infinity
    let maxY = -Infinity
    let hasBBox = false
    for (const element of elements) {
      const bbox = getElementBoundingBoxClass(element)
      if (bbox) {
        hasBBox = true
        minX = Math.min(minX, bbox.position.x)
        minY = Math.min(minY, bbox.position.y)
        maxX = Math.max(maxX, bbox.position.x + bbox.width)
        maxY = Math.max(maxY, bbox.position.y + bbox.height)
      }
    }
    return hasBBox ? new BoundingBoxClass(minX, minY, maxX - minX, maxY - minY) : null
  }

  public getSpaceType(): SpaceType {
    return 'children'
  }
}
