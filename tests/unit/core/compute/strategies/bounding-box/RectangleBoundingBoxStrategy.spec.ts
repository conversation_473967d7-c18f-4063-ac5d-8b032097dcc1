import { beforeEach, describe, expect, it } from 'vitest'
import { RectangleBoundingBoxStrategy } from '../../../../../../src/core/compute/strategies/bounding-box/RectangleBoundingBoxStrategy'
import { Point } from '../../../../../../src/types/core/element/geometry/point'

// Import Circle class
import { Circle } from '../../../../../../src/types/core/element/shape/ellipseShapeTypes'

// Import Rectangle class
import { Rectangle } from '../../../../../../src/types/core/element/shape/rectangleShapeTypes'

// Mock Rectangle class
class MockRectangle extends Rectangle {
  constructor(x: number, y: number, width: number, height: number) {
    super('mock-rectangle', new Point(x, y), { type: 'rectangle', width, height })
  }
}

// Mock non-rectangle element
class MockCircle extends Circle {
  constructor() {
    super('mock-circle', new Point(0, 0), { type: 'circle', radius: 10 })
  }
}

/**
 * Unit tests for the RectangleBoundingBoxStrategy class
 */
describe('rectangleBoundingBoxStrategy', () => {
  let strategy: RectangleBoundingBoxStrategy

  beforeEach(() => {
    strategy = new RectangleBoundingBoxStrategy()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('rectangle')
  })

  it('should calculate bounding box correctly', () => {
    const x = 100
    const y = 100
    const width = 200
    const height = 150
    const rectangle = new MockRectangle(x, y, width, height)

    const boundingBox = strategy.calculateBoundingBox(rectangle as any)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBe(x - width / 2)
    expect(boundingBox.y).toBe(y - height / 2)
    expect(boundingBox.width).toBe(width)
    expect(boundingBox.height).toBe(height)
  })

  it('should handle small dimensions', () => {
    const rectangle = new MockRectangle(100, 100, 0.1, 0.1)
    const boundingBox = strategy.calculateBoundingBox(rectangle)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBeCloseTo(100 - 0.1 / 2)
    expect(boundingBox.y).toBeCloseTo(100 - 0.1 / 2)
    expect(boundingBox.width).toBeCloseTo(0.1)
    expect(boundingBox.height).toBeCloseTo(0.1)
  })

  it('should handle absolute values for dimensions', () => {
    // Create a rectangle with positive dimensions (since Rectangle enforces positive values)
    const rectangle = new MockRectangle(100, 100, 200, 150)
    const boundingBox = strategy.calculateBoundingBox(rectangle)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBe(100 - 200 / 2)
    expect(boundingBox.y).toBe(100 - 150 / 2)
    expect(boundingBox.width).toBe(200)
    expect(boundingBox.height).toBe(150)
  })

  it('should handle non-rectangle elements', () => {
    const circle = new MockCircle()
    const boundingBox = strategy.calculateBoundingBox(circle as any)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBe(0)
    expect(boundingBox.y).toBe(0)
    expect(boundingBox.width).toBe(0)
    expect(boundingBox.height).toBe(0)
  })
})
