/**
 * Keyboard Shortcuts Guide Component
 *
 * A comprehensive guide component that displays all available keyboard shortcuts
 * in the application. Designed to be used within popovers or dialogs to help
 * users discover and learn keyboard shortcuts for improved productivity.
 *
 * Features:
 * - Organized list of keyboard shortcuts with descriptions
 * - Cross-platform key notation (Cmd/Ctrl)
 * - Responsive design with scrollable content
 * - Dark mode support
 * - Hover effects for better interactivity
 *
 * @example
 * ```tsx
 * <Popover>
 *   <PopoverTrigger asChild>
 *     <Button>Show Shortcuts</Button>
 *   </PopoverTrigger>
 *   <PopoverContent>
 *     <KeyboardShortcutsContent />
 *   </PopoverContent>
 * </Popover>
 * ```
 */

import type React from 'react'
import {
  getShortcutCategories,
  getShortcutDisplayText,
  getShortcutsByCategory,
} from '@/hooks/useKeyboardShortcuts'

/**
 * Interface defining the structure of a keyboard shortcut for display
 */
interface KeyboardShortcut {
  /** The key combination string (e.g., "⌘S", "Ctrl+S") */
  keys: string
  /** Description of what the shortcut does */
  action: string
  /** Category of the shortcut */
  category?: string
}

/**
 * Category display names mapping (English and Chinese)
 */
const CATEGORY_NAMES: Record<string, { en: string, zh: string }> = {
  edit: { en: 'Edit', zh: '编辑操作' },
  selection: { en: 'Selection', zh: '选择操作' },
  view: { en: 'View', zh: '视图操作' },
  file: { en: 'File', zh: '文件操作' },
  tool: { en: 'Tools', zh: '工具选择' },
  ui: { en: 'Interface', zh: '界面切换' },
}

/**
 * Action descriptions mapping (English and Chinese)
 * Only includes actually implemented features
 */
const ACTION_DESCRIPTIONS: Record<string, { en: string, zh: string }> = {
  // Edit operations
  撤销: { en: 'Undo', zh: '撤销' },
  重做: { en: 'Redo', zh: '重做' },
  删除选中元素: { en: 'Delete Selected Elements', zh: '删除选中元素' },

  // Selection operations
  全选: { en: 'Select All', zh: '全选' },
  清除选择: { en: 'Clear Selection', zh: '清除选择' },
  多选元素: { en: 'Multi-select Elements', zh: '多选元素' },
  框选多选: { en: 'Marquee Multi-select', zh: '框选多选' },

  // View operations
  放大: { en: 'Zoom In', zh: '放大' },
  缩小: { en: 'Zoom Out', zh: '缩小' },
  重置缩放: { en: 'Reset Zoom', zh: '重置缩放' },
  切换平移模式: { en: 'Toggle Pan Mode', zh: '切换平移模式' },

  // File operations
  新建画布: { en: 'New Canvas', zh: '新建画布' },
  保存画布: { en: 'Save Canvas', zh: '保存画布' },
  导出画布: { en: 'Export Canvas', zh: '导出画布' },

  // Tool selection
  选择工具: { en: 'Select Tool', zh: '选择工具' },
  平移工具: { en: 'Pan Tool', zh: '平移工具' },

  // Interface
  切换左侧面板: { en: 'Toggle Left Panel', zh: '切换左侧面板' },
  切换右侧面板: { en: 'Toggle Right Panel', zh: '切换右侧面板' },
  切换网格: { en: 'Toggle Grid', zh: '切换网格' },
  打开设置: { en: 'Open Settings', zh: '打开设置' },
  打开快捷键指南: { en: 'Open Shortcuts Guide', zh: '打开快捷键指南' },
}

/**
 * Convert shortcuts configuration to display format
 * @param language - Language preference ('en' or 'zh')
 * @returns Categorized shortcuts for display
 */
function convertShortcutsForDisplay(language: 'en' | 'zh' = 'en'): Record<string, KeyboardShortcut[]> {
  const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.userAgent)
  const categorizedShortcuts: Record<string, KeyboardShortcut[]> = {}

  // Group by category
  const categories = getShortcutCategories()

  categories.forEach((category) => {
    const shortcuts = getShortcutsByCategory(category)
    categorizedShortcuts[category] = shortcuts.map((shortcut) => {
      const description = shortcut.description ?? shortcut.key
      const translatedAction = ACTION_DESCRIPTIONS[description]?.[language] ?? description

      return {
        keys: getShortcutDisplayText(shortcut, isMac),
        action: translatedAction,
        category: shortcut.category,
      }
    })
  })

  // Add additional selection shortcuts
  if (categorizedShortcuts.selection === undefined) {
    categorizedShortcuts.selection = []
  }

  // Combined multi-select operations
  categorizedShortcuts.selection.push({
    keys: isMac ? '⇧/⌘ + Click/Drag' : 'Shift/Ctrl + Click/Drag',
    action: 'Multi-select/Marquee Elements',
    category: 'selection',
  })

  return categorizedShortcuts
}

/**
 * Props for KeyboardShortcutsContent component
 */
interface KeyboardShortcutsContentProps {
  /** Language preference for display ('en' or 'zh') */
  language?: 'en' | 'zh'
}

/**
 * Keyboard Shortcuts Content component that renders the list of available shortcuts.
 *
 * This component displays a formatted list of keyboard shortcuts with their descriptions,
 * organized by category. It's designed to be used within popovers, dialogs, or other
 * overlay components. The content is scrollable and responsive, making it suitable for
 * various container sizes.
 *
 * @param props - Component props
 * @param props.language - Language preference for display ('en' or 'zh')
 * @returns The rendered keyboard shortcuts content
 */
export const KeyboardShortcutsContent: React.FC<KeyboardShortcutsContentProps> = ({
  language = 'en',
}) => {
  const categorizedShortcuts = convertShortcutsForDisplay(language)

  // Language-specific content
  const content = {
    en: {
      title: 'Keyboard Shortcuts',
      subtitle: 'Here are the available keyboard shortcuts to help you work more efficiently.',
      notesTitle: 'Notes',
      notes: [
        'Some shortcuts are disabled when typing in input fields',
        `Mac uses ⌘ key, Windows/Linux uses Ctrl key`,
        'Multi-selection supports Shift, Ctrl, and Cmd keys',
      ],
    },
    zh: {
      title: '快捷键指南',
      subtitle: '以下是可用的键盘快捷键，帮助您更高效地工作。',
      notesTitle: '说明',
      notes: [
        '在输入框中时，只有部分快捷键可用',
        'Mac 系统使用 ⌘ 键，Windows/Linux 使用 Ctrl 键',
        '多选操作支持 Shift、Ctrl 和 Cmd 键',
      ],
    },
  }

  const currentContent = content[language]

  return (
    <>
      {/* Header */}
      <div className="mb-4 text-center sm:text-left">
        <h3 className="text-lg font-medium leading-6 text-popover-foreground">
          {currentContent.title}
        </h3>
        <p className="mt-1 text-sm text-muted-foreground">
          {currentContent.subtitle}
        </p>
      </div>

      {/* Shortcuts by category */}
      <div className="max-h-[60vh] overflow-y-auto pr-2">
        <div className="space-y-6">
          {Object.entries(categorizedShortcuts).map(([category, shortcuts]) => (
            <div key={category}>
              <h4 className="text-sm font-semibold text-foreground mb-3 border-b border-border pb-1">
                {CATEGORY_NAMES[category]?.[language] || category}
              </h4>
              <ul className="space-y-2">
                {shortcuts.map((shortcut, _index) => (
                  <li
                    key={`${category}-${shortcut.keys}-${shortcut.action}`}
                    className="flex justify-between items-center text-sm p-2 rounded-md bg-muted/30 hover:bg-muted/50 transition-colors"
                  >
                    <span className="font-medium text-foreground">
                      {shortcut.action}
                    </span>
                    <kbd className="px-2 py-1 text-xs font-mono font-semibold text-muted-foreground bg-background border border-border rounded shadow-sm">
                      {shortcut.keys}
                    </kbd>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Additional notes */}
        <div className="mt-6 pt-4 border-t border-border">
          <h4 className="text-sm font-semibold text-foreground mb-2">
            {currentContent.notesTitle}
          </h4>
          <ul className="text-xs text-muted-foreground space-y-1">
            {currentContent.notes.map((note) => (
              <li key={`note-${note.slice(0, 20).replace(/\s+/g, '-')}-${note.length}`}>
                •
                {note}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </>
  )
}
