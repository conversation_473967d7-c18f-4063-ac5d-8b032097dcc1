/**
 * Provides centralized exports for all design-specific element types.
 *
 * @remarks
 * This module serves as a barrel file, re-exporting types related to common
 * interior design elements such as walls, fixtures, furniture, and rooms.
 * It simplifies importing these types into other parts of the application.
 *
 * @module types/core/element/design
 * @see {@link ./wall} for wall element type definitions.
 * @see {@link ./fixture} for fixture element type definitions.
 * @see {@link ./furniture} for furniture element type definitions.
 * @see {@link ./room} for room element type definitions.
 * @see {@link ../../../../core/factory/creators/design} for related design element creators.
 * @see {@link ../../../../core/validator/validators/design} for related design element validators.
 */

/** Re-exports all types from the `./opening` module. */
export * from './openingDesignTypes'
/** Re-exports all types from the `./room` module. */
export * from './roomDesignTypes'
/** Re-exports all types from the `./wall` module. */
export * from './wallDesignTypes'
