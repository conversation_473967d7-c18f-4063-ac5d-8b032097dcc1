import type {
  TemplateApplied<PERSON><PERSON>,
  Template<PERSON>reatedE<PERSON>,
  TemplateDefaultChangedEvent,
  TemplateDeletedEvent,
  TemplateErrorEvent,
  TemplatesImportedEvent,
  TemplateUpdatedEvent,
} from '@/types/services/events/templateEvents'
import { describe, expect, it } from 'vitest'
import { AppEventType } from '@/types/services/events/eventTypes'
import { ExportFormat } from '@/types/services/events/exportEvents'

describe('template Events', () => {
  describe('template Event Types', () => {
    it('should define all expected event types in AppEventType', () => {
      expect(AppEventType.TEMPLATE_ERROR).toBeDefined()
      expect(AppEventType.TEMPLATE_CREATED).toBeDefined()
      expect(AppEventType.TEMPLATE_UPDATED).toBeDefined()
      expect(AppEventType.TEMPLATE_DELETED).toBeDefined()
      expect(AppEventType.TEMPLATE_DEFAULT_CHANGED).toBeDefined()
      expect(AppEventType.TEMPLATES_IMPORTED).toBeDefined()
      expect(AppEventType.TEMPLATE_APPLIED).toBeDefined()
    })

    it('should have the correct string values', () => {
      expect(AppEventType.TEMPLATE_ERROR).toBe('template.error')
      expect(AppEventType.TEMPLATE_CREATED).toBe('template.created')
      expect(AppEventType.TEMPLATE_UPDATED).toBe('template.updated')
      expect(AppEventType.TEMPLATE_DELETED).toBe('template.deleted')
      expect(AppEventType.TEMPLATE_DEFAULT_CHANGED).toBe('template.default.changed')
      expect(AppEventType.TEMPLATES_IMPORTED).toBe('templates.imported')
      expect(AppEventType.TEMPLATE_APPLIED).toBe('template.applied')
    })
  })

  describe('templateErrorEvent', () => {
    it('should allow creating a template error event', () => {
      const event: TemplateErrorEvent = {
        type: AppEventType.TEMPLATE_ERROR,
        payload: {
          operation: 'apply',
          error: 'Template not found',
        },
      }

      expect(event.type).toBe(AppEventType.TEMPLATE_ERROR)
      expect(event.payload.operation).toBe('apply')
      expect(event.payload.error).toBe('Template not found')
    })
  })

  describe('templateCreatedEvent', () => {
    it('should allow creating a template created event', () => {
      const event: TemplateCreatedEvent = {
        type: AppEventType.TEMPLATE_CREATED,
        payload: {
          templateId: 'template-123',
          format: ExportFormat.SVG,
        },
      }

      expect(event.type).toBe(AppEventType.TEMPLATE_CREATED)
      expect(event.payload.templateId).toBe('template-123')
      expect(event.payload.format).toBe(ExportFormat.SVG)
    })
  })

  describe('templateUpdatedEvent', () => {
    it('should allow creating a template updated event', () => {
      const event: TemplateUpdatedEvent = {
        type: AppEventType.TEMPLATE_UPDATED,
        payload: {
          templateId: 'template-123',
          format: ExportFormat.PNG,
        },
      }

      expect(event.type).toBe(AppEventType.TEMPLATE_UPDATED)
      expect(event.payload.templateId).toBe('template-123')
      expect(event.payload.format).toBe(ExportFormat.PNG)
    })
  })

  describe('templateDeletedEvent', () => {
    it('should allow creating a template deleted event', () => {
      const event: TemplateDeletedEvent = {
        type: AppEventType.TEMPLATE_DELETED,
        payload: {
          templateId: 'template-123',
        },
      }

      expect(event.type).toBe(AppEventType.TEMPLATE_DELETED)
      expect(event.payload.templateId).toBe('template-123')
    })
  })

  describe('templateDefaultChangedEvent', () => {
    it('should allow creating a template default changed event', () => {
      const event: TemplateDefaultChangedEvent = {
        type: AppEventType.TEMPLATE_DEFAULT_CHANGED,
        payload: {
          templateId: 'template-123',
        },
      }

      expect(event.type).toBe(AppEventType.TEMPLATE_DEFAULT_CHANGED)
      expect(event.payload.templateId).toBe('template-123')
    })
  })

  describe('templatesImportedEvent', () => {
    it('should allow creating a templates imported event', () => {
      const event: TemplatesImportedEvent = {
        type: AppEventType.TEMPLATES_IMPORTED,
        payload: {
          count: 5,
        },
      }

      expect(event.type).toBe(AppEventType.TEMPLATES_IMPORTED)
      expect(event.payload.count).toBe(5)
    })
  })

  describe('templateAppliedEvent', () => {
    it('should allow creating a template applied event', () => {
      const event: TemplateAppliedEvent = {
        type: AppEventType.TEMPLATE_APPLIED,
        payload: {
          templateId: 'template-123',
          format: ExportFormat.SVG,
        },
      }

      expect(event.type).toBe(AppEventType.TEMPLATE_APPLIED)
      expect(event.payload.templateId).toBe('template-123')
      expect(event.payload.format).toBe(ExportFormat.SVG)
    })
  })
})
