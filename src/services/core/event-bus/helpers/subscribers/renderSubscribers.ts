/**
 * Render Event Subscribers Module
 *
 * This module provides helper functions for subscribing to rendering-related events
 * from the application's event bus. It covers canvas rendering, animations, and visual effects.
 *
 * All functions follow dependency injection principles by accepting an EventBus
 * instance as their first parameter.
 *
 * @module event-bus/helpers/subscribers/render-subscribers
 */

import type { EventBus } from '@/types/services/events'
import type { EventSubscriptionOptions } from '@/types/services/events/eventCore'
import type {
  LayerLockChangeEvent,
  LayerOrderChangeEvent,
  LayerVisibilityChangeEvent,
  RenderEvent,
} from '@/types/services/events/renderEvents'
import { AppEventType } from '@/types/services/events'
import { getRenderEventType, typedSubscribe } from './utils'

/**
 * Subscribe to render events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param phase - The phase of the process to listen for (trigger, complete, etc.)
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToRenderEvents(
  eventBus: EventBus,
  handler: (event: RenderEvent) => void,
  phase: 'trigger' | 'start' | 'complete' | 'error',
  options?: EventSubscriptionOptions,
): () => void {
  const eventType = getRenderEventType(phase)
  return typedSubscribe<RenderEvent>(eventBus, eventType, handler, options)
}

/**
 * Subscribe to layer visibility change events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToLayerVisibilityChangeEvents(
  eventBus: EventBus,
  handler: (event: LayerVisibilityChangeEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<LayerVisibilityChangeEvent>(
    eventBus,
    AppEventType.LayerVisibilityChange,
    handler,
    options,
  )
}

/**
 * Subscribe to layer lock change events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToLayerLockChangeEvents(
  eventBus: EventBus,
  handler: (event: LayerLockChangeEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<LayerLockChangeEvent>(
    eventBus,
    AppEventType.LayerLockChange,
    handler,
    options,
  )
}

/**
 * Subscribe to layer order change events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToLayerOrderChangeEvents(
  eventBus: EventBus,
  handler: (event: LayerOrderChangeEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<LayerOrderChangeEvent>(
    eventBus,
    AppEventType.LayerOrderChange,
    handler,
    options,
  )
}
