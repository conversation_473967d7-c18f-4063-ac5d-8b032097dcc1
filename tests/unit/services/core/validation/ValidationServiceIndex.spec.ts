import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ElementValidator } from '@/core/validator'
import { getService, ServiceId } from '@/services/core/registry'
import { getValidationService } from '@/services/core/validation'

// Mock dependencies
vi.mock('@/core/validator', () => ({
  ElementValidator: {
    initializeValidatorModules: vi.fn(),
  },
  validator: {
    validateElement: vi.fn(),
    validateElements: vi.fn(),
  },
}))

vi.mock('@/services/core/registry', () => ({
  getService: vi.fn(),
  ServiceId: {
    ValidationService: 'ValidationService',
  },
}))

describe('validation Service Index', () => {
  let mockGetService: ReturnType<typeof vi.fn>
  let mockConsoleWarn: ReturnType<typeof vi.fn>
  let mockInitializeValidatorModules: ReturnType<typeof vi.fn>

  beforeEach(() => {
    mockGetService = vi.mocked(getService)
    mockConsoleWarn = vi.spyOn(console, 'warn').mockImplementation(() => {})
    mockInitializeValidatorModules = vi.mocked(ElementValidator.initializeValidatorModules)

    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getValidationService', () => {
    it('should return service from registry when available', () => {
      const mockValidationService = {
        validateElement: vi.fn(),
        validateElements: vi.fn(),
      }

      mockGetService.mockReturnValue(mockValidationService)

      const result = getValidationService()

      expect(mockGetService).toHaveBeenCalledWith(ServiceId.ValidationService)
      expect(result).toBe(mockValidationService)
      expect(mockConsoleWarn).not.toHaveBeenCalled()
      expect(mockInitializeValidatorModules).not.toHaveBeenCalled()
    })

    it('should return singleton when service not in registry', () => {
      const error = new Error('Service not found')
      mockGetService.mockImplementation(() => {
        throw error
      })

      const result = getValidationService()

      expect(mockGetService).toHaveBeenCalledWith(ServiceId.ValidationService)
      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '验证服务未在注册表中找到，返回直接的单例实例。',
        error,
      )
      expect(mockInitializeValidatorModules).toHaveBeenCalled()
      expect(result).toBeDefined()
    })

    it('should handle registry errors gracefully', () => {
      const error = new Error('Registry connection failed')
      mockGetService.mockImplementation(() => {
        throw error
      })

      const result = getValidationService()

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        '验证服务未在注册表中找到，返回直接的单例实例。',
        error,
      )
      expect(mockInitializeValidatorModules).toHaveBeenCalled()
      expect(result).toBeDefined()
    })

    it('should handle different types of registry errors', () => {
      const testCases = [
        new Error('Service not registered'),
        new TypeError('Invalid service type'),
        new ReferenceError('Service reference not found'),
        'String error',
        null,
        undefined,
      ]

      testCases.forEach((error, index) => {
        vi.clearAllMocks()

        mockGetService.mockImplementation(() => {
          throw error
        })

        const result = getValidationService()

        expect(mockConsoleWarn).toHaveBeenCalledWith(
          '验证服务未在注册表中找到，返回直接的单例实例。',
          error,
        )
        expect(mockInitializeValidatorModules).toHaveBeenCalled()
        expect(result).toBeDefined()
      })
    })

    it('should call initializeValidatorModules only when falling back to singleton', () => {
      // First call - service available in registry
      const mockValidationService = { validateElement: vi.fn() }
      mockGetService.mockReturnValue(mockValidationService)

      getValidationService()
      expect(mockInitializeValidatorModules).not.toHaveBeenCalled()

      // Second call - service not available, fallback to singleton
      mockGetService.mockImplementation(() => {
        throw new Error('Service not found')
      })

      getValidationService()
      expect(mockInitializeValidatorModules).toHaveBeenCalledTimes(1)
    })

    it('should handle multiple consecutive calls consistently', () => {
      const error = new Error('Service not found')
      mockGetService.mockImplementation(() => {
        throw error
      })

      const result1 = getValidationService()
      const result2 = getValidationService()
      const result3 = getValidationService()

      expect(mockGetService).toHaveBeenCalledTimes(3)
      expect(mockConsoleWarn).toHaveBeenCalledTimes(3)
      expect(mockInitializeValidatorModules).toHaveBeenCalledTimes(3)

      // All results should be the same singleton instance
      expect(result1).toBe(result2)
      expect(result2).toBe(result3)
    })

    it('should handle mixed success and failure scenarios', () => {
      const mockValidationService = { validateElement: vi.fn() }

      // First call succeeds
      mockGetService.mockReturnValueOnce(mockValidationService)
      const result1 = getValidationService()
      expect(result1).toBe(mockValidationService)
      expect(mockConsoleWarn).not.toHaveBeenCalled()

      // Second call fails
      mockGetService.mockImplementationOnce(() => {
        throw new Error('Service temporarily unavailable')
      })
      const result2 = getValidationService()
      expect(mockConsoleWarn).toHaveBeenCalledTimes(1)
      expect(mockInitializeValidatorModules).toHaveBeenCalledTimes(1)

      // Third call succeeds again
      mockGetService.mockReturnValueOnce(mockValidationService)
      const result3 = getValidationService()
      expect(result3).toBe(mockValidationService)
      expect(mockConsoleWarn).toHaveBeenCalledTimes(1) // No additional warnings
    })

    it('should preserve service instance identity when retrieved from registry', () => {
      const mockValidationService = {
        validateElement: vi.fn(),
        uniqueId: 'test-service-123',
      }

      mockGetService.mockReturnValue(mockValidationService)

      const result1 = getValidationService()
      const result2 = getValidationService()

      expect(result1).toBe(result2)
      expect(result1).toBe(mockValidationService)
      expect(mockGetService).toHaveBeenCalledTimes(2)
    })

    it('should handle edge cases with service registry', () => {
      // Test with null return from registry
      mockGetService.mockReturnValue(null)

      let result = getValidationService()
      expect(result).toBeNull()
      expect(mockConsoleWarn).not.toHaveBeenCalled()

      // Test with undefined return from registry
      mockGetService.mockReturnValue(undefined)

      result = getValidationService()
      expect(result).toBeUndefined()
      expect(mockConsoleWarn).not.toHaveBeenCalled()
    })

    it('should handle service registry throwing non-Error objects', () => {
      const nonErrorObjects = [
        'string error',
        { message: 'object error' },
        123,
        true,
        [],
        Symbol('error'),
      ]

      nonErrorObjects.forEach((errorObj, index) => {
        vi.clearAllMocks()

        mockGetService.mockImplementation(() => {
          throw errorObj
        })

        const result = getValidationService()

        expect(mockConsoleWarn).toHaveBeenCalledWith(
          '验证服务未在注册表中找到，返回直接的单例实例。',
          errorObj,
        )
        expect(mockInitializeValidatorModules).toHaveBeenCalled()
        expect(result).toBeDefined()
      })
    })
  })

  describe('module exports', () => {
    it('should export getValidationService function', () => {
      expect(typeof getValidationService).toBe('function')
    })

    it('should export registry functions', async () => {
      // Test that registry exports are available
      const module = await import('@/services/core/validation')
      expect(module.getValidationService).toBeDefined()
      expect(typeof module.getValidationService).toBe('function')
    })
  })

  describe('integration scenarios', () => {
    it('should work correctly in a typical application flow', () => {
      // Simulate application startup - service not yet registered
      mockGetService.mockImplementationOnce(() => {
        throw new Error('Service not initialized')
      })

      const initialService = getValidationService()
      expect(mockInitializeValidatorModules).toHaveBeenCalled()
      expect(mockConsoleWarn).toHaveBeenCalled()

      // Simulate service registration
      const registeredService = { validateElement: vi.fn() }
      mockGetService.mockReturnValue(registeredService)

      const laterService = getValidationService()
      expect(laterService).toBe(registeredService)
      expect(mockConsoleWarn).toHaveBeenCalledTimes(1) // No additional warnings
    })

    it('should handle service registry state changes', () => {
      const service1 = { validateElement: vi.fn(), id: 'service1' }
      const service2 = { validateElement: vi.fn(), id: 'service2' }

      // First service registered
      mockGetService.mockReturnValueOnce(service1)
      expect(getValidationService()).toBe(service1)

      // Service changed
      mockGetService.mockReturnValueOnce(service2)
      expect(getValidationService()).toBe(service2)

      // Service unregistered
      mockGetService.mockImplementationOnce(() => {
        throw new Error('Service unregistered')
      })
      const fallbackService = getValidationService()
      expect(mockConsoleWarn).toHaveBeenCalled()
      expect(mockInitializeValidatorModules).toHaveBeenCalled()
    })
  })
})
