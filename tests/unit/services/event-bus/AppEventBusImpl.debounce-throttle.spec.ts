import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { AppEventBusImpl } from '@/services/event-bus/AppEventBus'
import { AppEventType } from '@/types/services/events/eventTypes'

describe('appEventBusImpl Debounce and Throttle', () => {
  let eventBus: AppEventBusImpl

  beforeEach(() => {
    eventBus = new AppEventBusImpl()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
    vi.clearAllMocks()
  })

  describe('debounce and Throttle Configuration', () => {
    it('should support debounce configuration', () => {
      // Test that the configuration accepts debounce settings
      eventBus.configure({
        defaultDebounceTime: 200,
      })

      // This is just testing that the configuration doesn't throw an error
      expect(true).toBe(true)
    })

    it('should support throttle configuration', () => {
      // Test that the configuration accepts throttle settings
      eventBus.configure({
        defaultThrottleTime: 200,
      })

      // This is just testing that the configuration doesn't throw an error
      expect(true).toBe(true)
    })
  })

  describe('subscription Options', () => {
    it('should accept debounce option in subscription', () => {
      const handler = vi.fn()

      // Subscribe with debounce option
      const subscriptionId = eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler, {
        debounce: 100,
      })

      // Verify subscription was created
      expect(subscriptionId).toBeDefined()
    })

    it('should accept throttle option in subscription', () => {
      const handler = vi.fn()

      // Subscribe with throttle option
      const subscriptionId = eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler, {
        throttle: 100,
      })

      // Verify subscription was created
      expect(subscriptionId).toBeDefined()
    })

    it('should accept combined options in subscription', () => {
      const handler = vi.fn()

      // Subscribe with multiple options
      const subscriptionId = eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler, {
        debounce: 100,
        once: true,
        priority: 10,
      })

      // Verify subscription was created
      expect(subscriptionId).toBeDefined()
    })
  })

  describe('event Processing', () => {
    it('should process events with debounce/throttle handlers', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()

      // Subscribe with different options
      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler1, {
        debounce: 100,
      })

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler2, {
        throttle: 200,
      })

      const event = { type: AppEventType.SHAPE_CREATE_REQUEST, payload: { ElementType: 'rectangle' } }

      // Publish event
      eventBus.publish(event)

      // Advance time to process all events
      vi.runAllTimers()

      // Both handlers should have been called at least once
      expect(handler1.mock.calls.length).toBeGreaterThan(0)
      expect(handler2.mock.calls.length).toBeGreaterThan(0)
    })
  })
})
