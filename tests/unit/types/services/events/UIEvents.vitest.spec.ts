import type {
  DialogEvent,
  <PERSON>ridEvent,
  KeyboardEvent,
  ModalEvent,
  MouseEvent,
  NotificationEvent,
  SidebarEvent,
  ToastEvent,
  ToolEvent,
  ViewEvent,
} from '@/types/services/events/uiEvents'
import { describe, expect, it } from 'vitest'
import { AppEventType } from '@/types/services/events/eventTypes'

describe('uIEvents', () => {
  describe('viewEvent', () => {
    it('should create view zoomed event correctly', () => {
      const event: ViewEvent = {
        type: AppEventType.VIEW_ZOOMED,
        payload: {
          zoomLevel: 1.5,
          zoomPoint: { x: 100, y: 100 },
        },
      }

      expect(event.type).toBe(AppEventType.VIEW_ZOOMED)
      expect(event.payload.zoomLevel).toBe(1.5)
      expect(event.payload.zoomPoint).toEqual({ x: 100, y: 100 })
    })

    it('should create view panned event correctly', () => {
      const event: ViewEvent = {
        type: AppEventType.VIEW_PANNED,
        payload: {
          panX: 50,
          panY: -30,
        },
      }

      expect(event.type).toBe(AppEventType.VIEW_PANNED)
      expect(event.payload.panX).toBe(50)
      expect(event.payload.panY).toBe(-30)
    })

    it('should create view reset event correctly', () => {
      const event: ViewEvent = {
        type: AppEventType.VIEW_RESET,
        payload: {},
      }

      expect(event.type).toBe(AppEventType.VIEW_RESET)
      expect(event.payload).toEqual({})
    })
  })

  describe('toolEvent', () => {
    it('should create tool changed event correctly', () => {
      const event: ToolEvent = {
        type: AppEventType.TOOL_CHANGED,
        payload: {
          tool: 'rectangle',
        },
      }

      expect(event.type).toBe(AppEventType.TOOL_CHANGED)
      expect(event.payload.tool).toBe('rectangle')
    })

    it('should create tool changed event with options', () => {
      const event: ToolEvent = {
        type: AppEventType.TOOL_CHANGED,
        payload: {
          tool: 'circle',
          options: {
            fillColor: '#FF0000',
            strokeWidth: 2,
          },
        },
      }

      expect(event.type).toBe(AppEventType.TOOL_CHANGED)
      expect(event.payload.tool).toBe('circle')
      expect(event.payload.options).toEqual({
        fillColor: '#FF0000',
        strokeWidth: 2,
      })
    })
  })

  describe('notificationEvent', () => {
    it('should create notification add event correctly', () => {
      const event: NotificationEvent = {
        type: AppEventType.NOTIFICATION_ADD,
        payload: {
          id: 'notif-123',
          type: 'info',
          message: 'Operation completed successfully',
          title: 'Success',
        },
      }

      expect(event.type).toBe(AppEventType.NOTIFICATION_ADD)
      expect(event.payload.id).toBe('notif-123')
      expect(event.payload.type).toBe('info')
      expect(event.payload.message).toBe('Operation completed successfully')
      expect(event.payload.title).toBe('Success')
    })

    it('should create notification add event with minimal payload', () => {
      const event: NotificationEvent = {
        type: AppEventType.NOTIFICATION_ADD,
        payload: {
          type: 'warning',
          message: 'Warning message',
        },
      }

      expect(event.type).toBe(AppEventType.NOTIFICATION_ADD)
      expect(event.payload.type).toBe('warning')
      expect(event.payload.message).toBe('Warning message')
      expect(event.payload.id).toBeUndefined()
      expect(event.payload.title).toBeUndefined()
    })

    it('should create notification remove event correctly', () => {
      const event: NotificationEvent = {
        type: AppEventType.NOTIFICATION_REMOVE,
        payload: {
          id: 'notif-123',
        },
      }

      expect(event.type).toBe(AppEventType.NOTIFICATION_REMOVE)
      expect(event.payload.id).toBe('notif-123')
    })

    it('should create notification clear all event correctly', () => {
      const event: NotificationEvent = {
        type: AppEventType.NOTIFICATION_CLEAR_ALL,
        payload: {},
      }

      expect(event.type).toBe(AppEventType.NOTIFICATION_CLEAR_ALL)
      expect(event.payload).toEqual({})
    })
  })

  describe('toastEvent', () => {
    it('should create toast show event correctly', () => {
      const event: ToastEvent = {
        type: AppEventType.TOAST_SHOW,
        payload: {
          id: 'toast-123',
          message: 'Operation completed',
          type: 'success',
          duration: 3000,
        },
      }

      expect(event.type).toBe(AppEventType.TOAST_SHOW)
      expect(event.payload.id).toBe('toast-123')
      expect(event.payload.message).toBe('Operation completed')
      expect(event.payload.type).toBe('success')
      expect(event.payload.duration).toBe(3000)
    })

    it('should create toast show event with minimal payload', () => {
      const event: ToastEvent = {
        type: AppEventType.TOAST_SHOW,
        payload: {
          message: 'Operation completed',
        },
      }

      expect(event.type).toBe(AppEventType.TOAST_SHOW)
      expect(event.payload.message).toBe('Operation completed')
      expect(event.payload.id).toBeUndefined()
      expect(event.payload.type).toBeUndefined()
      expect(event.payload.duration).toBeUndefined()
    })

    it('should create toast hide event correctly', () => {
      const event: ToastEvent = {
        type: AppEventType.TOAST_HIDE,
        payload: {
          id: 'toast-123',
        },
      }

      expect(event.type).toBe(AppEventType.TOAST_HIDE)
      expect(event.payload.id).toBe('toast-123')
    })
  })

  describe('dialogEvent', () => {
    it('should create dialog show event correctly', () => {
      const event: DialogEvent = {
        type: AppEventType.DIALOG_SHOW,
        payload: {
          dialogId: 'dialog-123',
          dialogType: 'confirmation',
          dialogData: {
            title: 'Confirm Action',
            message: 'Are you sure you want to proceed?',
            confirmText: 'Yes',
            cancelText: 'No',
          },
        },
      }

      expect(event.type).toBe(AppEventType.DIALOG_SHOW)
      expect(event.payload.dialogId).toBe('dialog-123')
      expect(event.payload.dialogType).toBe('confirmation')
      expect(event.payload.dialogData).toEqual({
        title: 'Confirm Action',
        message: 'Are you sure you want to proceed?',
        confirmText: 'Yes',
        cancelText: 'No',
      })
    })

    it('should create dialog hide event correctly', () => {
      const event: DialogEvent = {
        type: AppEventType.DIALOG_HIDE,
        payload: {
          dialogId: 'dialog-123',
          result: {
            confirmed: true,
          },
        },
      }

      expect(event.type).toBe(AppEventType.DIALOG_HIDE)
      expect(event.payload.dialogId).toBe('dialog-123')
      expect(event.payload.result).toEqual({
        confirmed: true,
      })
    })
  })

  describe('modalEvent', () => {
    it('should create modal show event correctly', () => {
      const event: ModalEvent = {
        type: AppEventType.MODAL_SHOW,
        payload: {
          modalId: 'modal-123',
          modalData: {
            title: 'Settings',
            initialTab: 'general',
          },
        },
      }

      expect(event.type).toBe(AppEventType.MODAL_SHOW)
      expect(event.payload.modalId).toBe('modal-123')
      expect(event.payload.modalData).toEqual({
        title: 'Settings',
        initialTab: 'general',
      })
    })

    it('should create modal hide event correctly', () => {
      const event: ModalEvent = {
        type: AppEventType.MODAL_HIDE,
        payload: {
          modalId: 'modal-123',
          result: {
            saved: true,
            settings: {
              theme: 'dark',
            },
          },
        },
      }

      expect(event.type).toBe(AppEventType.MODAL_HIDE)
      expect(event.payload.modalId).toBe('modal-123')
      expect(event.payload.result).toEqual({
        saved: true,
        settings: {
          theme: 'dark',
        },
      })
    })
  })

  describe('sidebarEvent', () => {
    it('should create sidebar toggle event correctly', () => {
      const event: SidebarEvent = {
        type: AppEventType.SIDEBAR_TOGGLE,
        payload: {
          sidebarId: 'left-sidebar',
          isOpen: true,
        },
      }

      expect(event.type).toBe(AppEventType.SIDEBAR_TOGGLE)
      expect(event.payload.sidebarId).toBe('left-sidebar')
      expect(event.payload.isOpen).toBe(true)
    })
  })

  describe('gridEvent', () => {
    it('should create grid visibility changed event correctly', () => {
      const event: GridEvent = {
        type: AppEventType.GRID_VISIBILITY_CHANGED,
        payload: {
          isVisible: true,
        },
      }

      expect(event.type).toBe(AppEventType.GRID_VISIBILITY_CHANGED)
      expect(event.payload.isVisible).toBe(true)
    })

    it('should create grid size changed event correctly', () => {
      const event: GridEvent = {
        type: AppEventType.GRID_SIZE_CHANGED,
        payload: {
          size: 20,
        },
      }

      expect(event.type).toBe(AppEventType.GRID_SIZE_CHANGED)
      expect(event.payload.size).toBe(20)
    })

    it('should create grid color changed event correctly', () => {
      const event: GridEvent = {
        type: AppEventType.GRID_COLOR_CHANGED,
        payload: {
          color: '#CCCCCC',
        },
      }

      expect(event.type).toBe(AppEventType.GRID_COLOR_CHANGED)
      expect(event.payload.color).toBe('#CCCCCC')
    })

    it('should create grid snap changed event correctly', () => {
      const event: GridEvent = {
        type: AppEventType.GRID_SNAP_CHANGED,
        payload: {
          snapToGrid: true,
        },
      }

      expect(event.type).toBe(AppEventType.GRID_SNAP_CHANGED)
      expect(event.payload.snapToGrid).toBe(true)
    })
  })

  describe('keyboardEvent', () => {
    it('should create key pressed event correctly', () => {
      const event: KeyboardEvent = {
        type: AppEventType.KEY_PRESSED,
        payload: {
          key: 'Delete',
          ctrlKey: false,
          shiftKey: false,
          altKey: false,
        },
      }

      expect(event.type).toBe(AppEventType.KEY_PRESSED)
      expect(event.payload.key).toBe('Delete')
      expect(event.payload.ctrlKey).toBe(false)
      expect(event.payload.shiftKey).toBe(false)
      expect(event.payload.altKey).toBe(false)
    })

    it('should create key released event correctly', () => {
      const event: KeyboardEvent = {
        type: AppEventType.KEY_RELEASED,
        payload: {
          key: 'Delete',
        },
      }

      expect(event.type).toBe(AppEventType.KEY_RELEASED)
      expect(event.payload.key).toBe('Delete')
    })
  })

  describe('mouseEvent', () => {
    it('should create mouse move event correctly', () => {
      const event: MouseEvent = {
        type: AppEventType.CANVAS_MOUSE_MOVE,
        payload: {
          x: 100,
          y: 200,
          clientX: 300,
          clientY: 400,
        },
      }

      expect(event.type).toBe(AppEventType.CANVAS_MOUSE_MOVE)
      expect(event.payload.x).toBe(100)
      expect(event.payload.y).toBe(200)
      expect(event.payload.clientX).toBe(300)
      expect(event.payload.clientY).toBe(400)
    })

    it('should create mouse down event correctly', () => {
      const event: MouseEvent = {
        type: AppEventType.CANVAS_MOUSE_DOWN,
        payload: {
          x: 100,
          y: 200,
          button: 0,
        },
      }

      expect(event.type).toBe(AppEventType.CANVAS_MOUSE_DOWN)
      expect(event.payload.x).toBe(100)
      expect(event.payload.y).toBe(200)
      expect(event.payload.button).toBe(0)
    })

    it('should create mouse up event correctly', () => {
      const event: MouseEvent = {
        type: AppEventType.CANVAS_MOUSE_UP,
        payload: {
          x: 100,
          y: 200,
          button: 0,
        },
      }

      expect(event.type).toBe(AppEventType.CANVAS_MOUSE_UP)
      expect(event.payload.x).toBe(100)
      expect(event.payload.y).toBe(200)
      expect(event.payload.button).toBe(0)
    })
  })
})
