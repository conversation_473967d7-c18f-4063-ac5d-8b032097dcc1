import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { setupEventHandlers } from '@/services/event-bus/handlers/EventHandlers'

// Import after mocking
import { getEventBus } from '@/services/event-bus/index'
import { AppEventType } from '@/types/services/events'

// Mock the event bus module before importing the handlers
vi.mock('@/services/event-bus/index', () => {
  const mockEventBus = {
    subscribe: vi.fn(),
    publish: vi.fn(),
    unsubscribe: vi.fn(),
    reset: vi.fn(),
  }

  return {
    getEventBus: vi.fn(() => mockEventBus),
  }
})

describe('eventHandlers', () => {
  let mockEventBus: any

  beforeEach(() => {
    // Get the mocked event bus
    mockEventBus = getEventBus()

    // Clear all mocks before each test
    vi.clearAllMocks()

    // Mock console methods
    vi.spyOn(console, 'log').mockImplementation(() => {})
  })

  afterEach(() => {
    // Restore console methods
    vi.restoreAllMocks()
  })

  describe('setupEventHandlers', () => {
    it('should set up all event handlers', () => {
      // Call the function
      setupEventHandlers()

      // Verify that subscribe was called for all expected event types
      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.CANVAS_CLEARED,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.CANVAS_RESIZED,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.CANVAS_MOUSE_MOVE,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.TOOL_CHANGED,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.HISTORY_CHECKPOINT,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.HISTORY_UNDO,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.HISTORY_REDO,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.LAYER_VISIBILITY_CHANGE,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.LAYER_LOCK_CHANGE,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.LAYER_ORDER_CHANGE,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.FILE_IMPORTED,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.FILE_EXPORTED,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.VIEW_ZOOMED,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.VIEW_PANNED,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.TEMPLATE_APPLY,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.NOTIFICATION_ADD,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.TOAST_SHOW,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.SIDEBAR_LEFT_TOGGLE,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.SIDEBAR_RIGHT_TOGGLE,
        expect.any(Function),
      )
    })
  })

  describe('event Handlers', () => {
    it('should log canvas cleared events', () => {
      setupEventHandlers()

      // Find the canvas cleared handler
      const canvasClearedHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.CANVAS_CLEARED,
      )[1]

      // Call the handler with a mock event
      canvasClearedHandler({
        type: AppEventType.CANVAS_CLEARED,
        payload: { canvasId: 'test-canvas' },
      })

      expect(console.log).toHaveBeenCalledWith('Canvas cleared:', 'test-canvas')
    })

    it('should log canvas resized events', () => {
      setupEventHandlers()

      const canvasResizedHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.CANVAS_RESIZED,
      )[1]

      canvasResizedHandler({
        type: AppEventType.CANVAS_RESIZED,
        payload: { width: 800, height: 600 },
      })

      expect(console.log).toHaveBeenCalledWith('Canvas resized:', 800, 600)
    })

    it('should log canvas mouse move events occasionally', () => {
      setupEventHandlers()

      const canvasMouseMoveHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.CANVAS_MOUSE_MOVE,
      )[1]

      // Mock Math.random to return a value less than 0.01 to ensure logging
      const originalRandom = Math.random
      Math.random = vi.fn().mockReturnValue(0.005)

      canvasMouseMoveHandler({
        type: AppEventType.CANVAS_MOUSE_MOVE,
        payload: { x: 100, y: 200 },
      })

      expect(console.log).toHaveBeenCalledWith('Canvas mouse move:', 100, 200)

      // Restore Math.random
      Math.random = originalRandom
    })

    it('should not log canvas mouse move events most of the time', () => {
      setupEventHandlers()

      const canvasMouseMoveHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.CANVAS_MOUSE_MOVE,
      )[1]

      // Mock Math.random to return a value greater than 0.01 to prevent logging
      const originalRandom = Math.random
      Math.random = vi.fn().mockReturnValue(0.5)

      canvasMouseMoveHandler({
        type: AppEventType.CANVAS_MOUSE_MOVE,
        payload: { x: 100, y: 200 },
      })

      expect(console.log).not.toHaveBeenCalled()

      // Restore Math.random
      Math.random = originalRandom
    })

    it('should log tool changed events', () => {
      setupEventHandlers()

      const toolChangedHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.TOOL_CHANGED,
      )[1]

      toolChangedHandler({
        type: AppEventType.TOOL_CHANGED,
        payload: { tool: 'rectangle' },
      })

      expect(console.log).toHaveBeenCalledWith('Tool changed:', 'rectangle')
    })

    it('should log history checkpoint events', () => {
      setupEventHandlers()

      const historyCheckpointHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.HISTORY_CHECKPOINT,
      )[1]

      const payload = { description: 'test checkpoint' }
      historyCheckpointHandler({
        type: AppEventType.HISTORY_CHECKPOINT,
        payload,
      })

      expect(console.log).toHaveBeenCalledWith('History checkpoint:', payload)
    })

    it('should log history undo events', () => {
      setupEventHandlers()

      const historyUndoHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.HISTORY_UNDO,
      )[1]

      const payload = { actionId: 'action-1' }
      historyUndoHandler({
        type: AppEventType.HISTORY_UNDO,
        payload,
      })

      expect(console.log).toHaveBeenCalledWith('History undo:', payload)
    })

    it('should log history redo events', () => {
      setupEventHandlers()

      const historyRedoHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.HISTORY_REDO,
      )[1]

      const payload = { actionId: 'action-1' }
      historyRedoHandler({
        type: AppEventType.HISTORY_REDO,
        payload,
      })

      expect(console.log).toHaveBeenCalledWith('History redo:', payload)
    })

    it('should log layer visibility change events', () => {
      setupEventHandlers()

      const layerVisibilityHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.LAYER_VISIBILITY_CHANGE,
      )[1]

      layerVisibilityHandler({
        type: AppEventType.LAYER_VISIBILITY_CHANGE,
        payload: { layerId: 'layer-1', visible: true },
      })

      expect(console.log).toHaveBeenCalledWith('Layer visibility change:', 'layer-1', true)
    })

    it('should log layer lock change events', () => {
      setupEventHandlers()

      const layerLockHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.LAYER_LOCK_CHANGE,
      )[1]

      layerLockHandler({
        type: AppEventType.LAYER_LOCK_CHANGE,
        payload: { layerId: 'layer-1', locked: true },
      })

      expect(console.log).toHaveBeenCalledWith('Layer lock change:', 'layer-1', true)
    })

    it('should log layer order change events', () => {
      setupEventHandlers()

      const layerOrderHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.LAYER_ORDER_CHANGE,
      )[1]

      layerOrderHandler({
        type: AppEventType.LAYER_ORDER_CHANGE,
        payload: { layerId: 'layer-1', newOrder: 2 },
      })

      expect(console.log).toHaveBeenCalledWith('Layer order change:', 'layer-1', 2)
    })

    it('should log file imported events', () => {
      setupEventHandlers()

      const fileImportedHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.FILE_IMPORTED,
      )[1]

      const event = {
        type: AppEventType.FILE_IMPORTED,
        payload: { filename: 'test.json', size: 1024 },
      }
      fileImportedHandler(event)

      expect(console.log).toHaveBeenCalledWith('File imported:', event)
    })

    it('should log file exported events', () => {
      setupEventHandlers()

      const fileExportedHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.FILE_EXPORTED,
      )[1]

      const event = {
        type: AppEventType.FILE_EXPORTED,
        payload: { filename: 'export.json', format: 'json' },
      }
      fileExportedHandler(event)

      expect(console.log).toHaveBeenCalledWith('File exported:', event)
    })

    it('should log view zoomed events', () => {
      setupEventHandlers()

      const viewZoomedHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.VIEW_ZOOMED,
      )[1]

      viewZoomedHandler({
        type: AppEventType.VIEW_ZOOMED,
        payload: { scale: 1.5 },
      })

      expect(console.log).toHaveBeenCalledWith('View zoomed:', 1.5)
    })

    it('should log view panned events', () => {
      setupEventHandlers()

      const viewPannedHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.VIEW_PANNED,
      )[1]

      viewPannedHandler({
        type: AppEventType.VIEW_PANNED,
        payload: { x: 100, y: 200 },
      })

      expect(console.log).toHaveBeenCalledWith('View panned:', 100, 200)
    })

    it('should log template apply events', () => {
      setupEventHandlers()

      const templateApplyHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.TEMPLATE_APPLY,
      )[1]

      templateApplyHandler({
        type: AppEventType.TEMPLATE_APPLY,
        payload: { templateId: 'template-1' },
      })

      expect(console.log).toHaveBeenCalledWith('Template apply:', 'template-1')
    })

    it('should log notification add events', () => {
      setupEventHandlers()

      const notificationAddHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.NOTIFICATION_ADD,
      )[1]

      notificationAddHandler({
        type: AppEventType.NOTIFICATION_ADD,
        payload: { message: 'Test notification', type: 'info' },
      })

      expect(console.log).toHaveBeenCalledWith('Notification add:', 'Test notification', 'info')
    })

    it('should log toast show events', () => {
      setupEventHandlers()

      const toastShowHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.TOAST_SHOW,
      )[1]

      toastShowHandler({
        type: AppEventType.TOAST_SHOW,
        payload: { message: 'Test toast', type: 'success', duration: 3000 },
      })

      expect(console.log).toHaveBeenCalledWith('Toast show:', 'Test toast', 'success', 3000)
    })

    it('should log sidebar left toggle events', () => {
      setupEventHandlers()

      const sidebarLeftToggleHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.SIDEBAR_LEFT_TOGGLE,
      )[1]

      sidebarLeftToggleHandler({
        type: AppEventType.SIDEBAR_LEFT_TOGGLE,
        payload: {},
      })

      expect(console.log).toHaveBeenCalledWith('Sidebar left toggle')
    })

    it('should log sidebar right toggle events', () => {
      setupEventHandlers()

      const sidebarRightToggleHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.SIDEBAR_RIGHT_TOGGLE,
      )[1]

      sidebarRightToggleHandler({
        type: AppEventType.SIDEBAR_RIGHT_TOGGLE,
        payload: {},
      })

      expect(console.log).toHaveBeenCalledWith('Sidebar right toggle')
    })
  })

  describe('shape-related event handlers', () => {
    it('should log shape create request events', () => {
      setupEventHandlers()

      // Find the shape create request handler
      const shapeCreateHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.SHAPE_CREATE_REQUEST,
      )?.[1]

      // If the handler exists, call it with a mock event
      if (shapeCreateHandler) {
        shapeCreateHandler({
          type: AppEventType.SHAPE_CREATE_REQUEST,
          payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
        })

        expect(console.log).toHaveBeenCalledWith(
          'Shape create request:',
          'rectangle',
          expect.objectContaining({ x: 100, y: 100 }),
        )
      }
    })

    it('should log shape create complete events', () => {
      setupEventHandlers()

      // Find the shape create complete handler
      const shapeCreateCompleteHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.SHAPE_CREATE_COMPLETE,
      )?.[1]

      // If the handler exists, call it with a mock event
      if (shapeCreateCompleteHandler) {
        const mockShape = { id: 'shape-1', type: 'rectangle' }
        shapeCreateCompleteHandler({
          type: AppEventType.SHAPE_CREATE_COMPLETE,
          payload: { shape: mockShape },
        })

        expect(console.log).toHaveBeenCalledWith(
          'Shape create complete:',
          expect.objectContaining({ id: 'shape-1', type: 'rectangle' }),
        )
      }
    })

    it('should log shape edit request events', () => {
      setupEventHandlers()

      // Find the shape edit request handler
      const shapeEditHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.SHAPE_EDIT_REQUEST,
      )?.[1]

      // If the handler exists, call it with a mock event
      if (shapeEditHandler) {
        shapeEditHandler({
          type: AppEventType.SHAPE_EDIT_REQUEST,
          payload: {
            shapeId: 'shape-1',
            changes: { position: { x: 200, y: 200 } },
          },
        })

        expect(console.log).toHaveBeenCalledWith(
          'Shape edit request:',
          'shape-1',
          expect.objectContaining({ position: expect.objectContaining({ x: 200, y: 200 }) }),
        )
      }
    })

    it('should log shape edit complete events', () => {
      setupEventHandlers()

      // Find the shape edit complete handler
      const shapeEditCompleteHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.SHAPE_EDIT_COMPLETE,
      )?.[1]

      // If the handler exists, call it with a mock event
      if (shapeEditCompleteHandler) {
        shapeEditCompleteHandler({
          type: AppEventType.SHAPE_EDIT_COMPLETE,
          payload: {
            shapeId: 'shape-1',
            changes: { position: { x: 200, y: 200 } },
          },
        })

        expect(console.log).toHaveBeenCalledWith(
          'Shape edit complete:',
          'shape-1',
          expect.objectContaining({ position: expect.objectContaining({ x: 200, y: 200 }) }),
        )
      }
    })

    it('should log shape delete request events', () => {
      setupEventHandlers()

      // Find the shape delete request handler
      const shapeDeleteHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.SHAPE_DELETE_REQUEST,
      )?.[1]

      // If the handler exists, call it with a mock event
      if (shapeDeleteHandler) {
        shapeDeleteHandler({
          type: AppEventType.SHAPE_DELETE_REQUEST,
          payload: { shapeIds: ['shape-1', 'shape-2'] },
        })

        expect(console.log).toHaveBeenCalledWith(
          'Shape delete request:',
          ['shape-1', 'shape-2'],
        )
      }
    })

    it('should log shape delete complete events', () => {
      setupEventHandlers()

      // Find the shape delete complete handler
      const shapeDeleteCompleteHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.SHAPE_DELETE_COMPLETE,
      )?.[1]

      // If the handler exists, call it with a mock event
      if (shapeDeleteCompleteHandler) {
        shapeDeleteCompleteHandler({
          type: AppEventType.SHAPE_DELETE_COMPLETE,
          payload: { shapeIds: ['shape-1', 'shape-2'] },
        })

        expect(console.log).toHaveBeenCalledWith(
          'Shape delete complete:',
          ['shape-1', 'shape-2'],
        )
      }
    })

    it('should log selection changed events', () => {
      setupEventHandlers()

      // Find the selection changed handler
      const selectionChangedHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.SELECTION_CHANGED,
      )?.[1]

      // If the handler exists, call it with a mock event
      if (selectionChangedHandler) {
        selectionChangedHandler({
          type: AppEventType.SELECTION_CHANGED,
          payload: { selectedIds: ['shape-1', 'shape-2'] },
        })

        expect(console.log).toHaveBeenCalledWith(
          'Selection changed:',
          ['shape-1', 'shape-2'],
        )
      }
    })
  })

  describe('error handling', () => {
    it('should log error occurred events', () => {
      setupEventHandlers()

      // Find the error occurred handler
      const errorOccurredHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.ERROR_OCCURRED,
      )?.[1]

      // If the handler exists, call it with a mock event
      if (errorOccurredHandler) {
        errorOccurredHandler({
          type: AppEventType.ERROR_OCCURRED,
          payload: {
            message: 'Test error',
            code: 'TEST_ERROR',
            details: { source: 'test' },
          },
        })

        expect(console.log).toHaveBeenCalledWith(
          'Error occurred:',
          'Test error',
          'TEST_ERROR',
          expect.objectContaining({ source: 'test' }),
        )
      }
    })

    it('should handle errors in event handlers', () => {
      setupEventHandlers()

      // Find any handler
      const handler = mockEventBus.subscribe.mock.calls[0][1]

      // Mock console.error
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      // Create a mock event that will cause an error when processed
      const event = {
        type: 'test-event',
        payload: null, // This will cause an error when trying to access properties
      }

      // Call the handler with the problematic event
      // This should not throw an error
      expect(() => handler(event)).not.toThrow()

      // Verify that the error was logged
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error in event handler'),
        expect.any(Error),
      )

      // Restore console.error
      consoleErrorSpy.mockRestore()
    })
  })

  describe('custom event bus', () => {
    it('should use provided event bus instead of global one', () => {
      // Create a custom event bus
      const customEventBus = {
        subscribe: vi.fn(),
        publish: vi.fn(),
        unsubscribe: vi.fn(),
        reset: vi.fn(),
      }

      // Call setupEventHandlers with the custom event bus
      setupEventHandlers(customEventBus)

      // Verify that the custom event bus was used
      expect(customEventBus.subscribe).toHaveBeenCalled()

      // Verify that the global event bus was not used
      expect(mockEventBus.subscribe).not.toHaveBeenCalled()
    })
  })
})
