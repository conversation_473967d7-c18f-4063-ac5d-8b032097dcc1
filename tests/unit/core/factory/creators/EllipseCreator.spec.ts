import type { MockInstance } from 'vitest'
import type { Create<PERSON>ircleParams, CreateEllipseParams } from '@/core/factory/ElementFactory'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { EllipseCreator } from '@/core/factory/creators/EllipseCreator'
import { Point } from '@/types/core/element/geometry/point'
import { Ellipse } from '@/types/core/element/shape/ellipseShapeTypes' // Import Ellipse, not Circle
// Import specific property types for assertions
import { ElementType } from '@/types/core/shape-type'

// Mock the Ellipse class constructor (since Circle uses Ellipse)
vi.mock('@/types/core/element/shape/ellipse', () => {
  const mockEllipse = vi.fn()
  mockEllipse.mockImplementation((id, pos, props, opts) => {
    // Create a clean copy of the options to avoid test pollution
    const cleanOpts = { ...opts }
    // Create a clean copy of the properties to avoid test pollution
    const cleanProps = { ...props }

    return {
      id,
      position: pos,
      properties: cleanProps,
      options: cleanOpts,
      // --- Provide properties directly on the mock for easier testing ---
      type: cleanProps.type,
      radiusX: cleanProps.radiusX ?? cleanProps.radius,
      radiusY: cleanProps.radiusY ?? cleanProps.radius,
      radius: cleanProps.radius,
      // -----------------------------------------------------------------
      // Mock necessary methods
      getType: () => cleanProps.type,
    }
  })

  return {
    Ellipse: mockEllipse,
  }
})

describe('ellipseCreator', () => {
  let creator: EllipseCreator

  beforeEach(() => {
    creator = new EllipseCreator()
    vi.clearAllMocks()
  })

  it('should create a basic Ellipse', async () => {
    const params: CreateEllipseParams = {
      id: 'ellipse-1',
      type: ElementType.ELLIPSE,
      position: { x: 200, y: 250 },
      radiusX: 100,
      radiusY: 50,
      stroke: '#0000ff',
      fill: '#aaaaff',
    }

    const ellipse = await creator.create(params)

    // Instead of checking exact arguments, verify key properties
    expect(Ellipse).toHaveBeenCalledTimes(1)
    const mockEllipse = Ellipse as unknown as MockInstance
    const callArgs = mockEllipse.mock.calls[0]

    // Check ID
    expect(callArgs[0]).toBe('ellipse-1')

    // Check position
    expect(callArgs[1]).toEqual({ x: 200, y: 250 })

    // Check properties
    expect(callArgs[2].type).toBe(ElementType.ELLIPSE)
    expect(callArgs[2].radiusX).toBe(100)
    expect(callArgs[2].radiusY).toBe(50)

    // Check properties instead of options
    expect(callArgs[2].stroke).toBe('#0000ff')
    expect(callArgs[2].fill).toBe('#aaaaff')

    expect(ellipse).toBeDefined()
    // We're now testing the mock object directly
    expect(mockEllipse).toHaveBeenCalled()
    // Check that the mock was called with the right parameters
    expect(callArgs[0]).toBe('ellipse-1')
    expect(callArgs[2].type).toBe(ElementType.ELLIPSE)
    expect(callArgs[2].radiusX).toBe(100)
    expect(callArgs[2].radiusY).toBe(50)
  })

  it('should create a Circle', async () => {
    const params: CreateCircleParams = {
      id: 'circle-1',
      type: ElementType.CIRCLE,
      position: { x: 300, y: 300 },
      radius: 75,
      fill: '#ffcc00',
    }

    const circle = await creator.create(params)

    // Instead of checking exact arguments, verify key properties
    expect(Ellipse).toHaveBeenCalledTimes(1)
    const mockEllipse = Ellipse as unknown as MockInstance
    const callArgs = mockEllipse.mock.calls[0]

    // Check ID
    expect(callArgs[0]).toBe('circle-1')

    // Check position
    expect(callArgs[1]).toEqual({ x: 300, y: 300 })

    // Check properties
    expect(callArgs[2].type).toBe(ElementType.CIRCLE)
    expect(callArgs[2].radius).toBe(75)

    // Check properties instead of options
    expect(callArgs[2].fill).toBe('#ffcc00')

    expect(circle).toBeDefined()
    // We're now testing the mock object directly
    expect(mockEllipse).toHaveBeenCalled()
    // Check that the mock was called with the right parameters
    expect(callArgs[0]).toBe('circle-1')
    expect(callArgs[2].type).toBe(ElementType.CIRCLE)
    expect(callArgs[2].radius).toBe(75)
  })

  it('should throw error for incorrect shape type', async () => {
    const params = {
      id: 'wrong-type-ellipse',
      type: ElementType.RECTANGLE,
      position: { x: 10, y: 10 },
      width: 50,
      height: 50,
    } as any

    await expect(creator.create(params)).rejects.toThrow(
      'EllipseCreator cannot create type: rectangle',
    )
    expect(Ellipse).not.toHaveBeenCalled()
  })

  it('should throw error for invalid ellipse radii (radiusX <= 0)', async () => {
    const params: CreateEllipseParams = {
      id: 'invalid-rx',
      type: ElementType.ELLIPSE,
      position: { x: 10, y: 10 },
      radiusX: 0,
      radiusY: 50,
    }
    await expect(creator.create(params)).rejects.toThrow(
      'Ellipse creation requires valid positive radiusX and radiusY.',
    )
    expect(Ellipse).not.toHaveBeenCalled()
  })

  it('should throw error for invalid ellipse radii (radiusY <= 0)', async () => {
    const params: CreateEllipseParams = {
      id: 'invalid-ry',
      type: ElementType.ELLIPSE,
      position: { x: 10, y: 10 },
      radiusX: 50,
      radiusY: -10,
    }
    await expect(creator.create(params)).rejects.toThrow(
      'Ellipse creation requires valid positive radiusX and radiusY.',
    )
    expect(Ellipse).not.toHaveBeenCalled()
  })

  it('should throw error for invalid circle radius (radius <= 0)', async () => {
    const params: CreateCircleParams = {
      id: 'invalid-radius',
      type: ElementType.CIRCLE,
      position: { x: 10, y: 10 },
      radius: 0,
    }
    await expect(creator.create(params)).rejects.toThrow(
      'Circle creation requires a valid positive radius.',
    )
    expect(Ellipse).not.toHaveBeenCalled()
  })

  it('should create a default Ellipse', async () => {
    const id = 'default-ellipse'
    const position = new Point(10, 10)
    const defaultRadiusX = 50
    const defaultRadiusY = 30

    const ellipse = await creator.createDefault(id, position)

    // Instead of checking exact arguments, verify key properties
    expect(Ellipse).toHaveBeenCalledTimes(1)
    const mockEllipse = Ellipse as unknown as MockInstance
    const callArgs = mockEllipse.mock.calls[0]

    // Check ID
    expect(callArgs[0]).toBe(id)

    // Check position
    expect(callArgs[1]).toBe(position)

    // Check properties
    expect(callArgs[2].type).toBe(ElementType.ELLIPSE)
    expect(callArgs[2].radiusX).toBe(defaultRadiusX)
    expect(callArgs[2].radiusY).toBe(defaultRadiusY)
    // Default styles are now in the properties object
    expect(callArgs[2]).toHaveProperty('stroke')
    expect(callArgs[2]).toHaveProperty('strokeWidth')
    expect(callArgs[2]).toHaveProperty('fill')

    expect(ellipse).toBeDefined()
    // We're now testing the mock object directly
    expect(mockEllipse).toHaveBeenCalled()
    // Check that the mock was called with the right parameters
    expect(callArgs[0]).toBe(id)
    expect(callArgs[2].type).toBe(ElementType.ELLIPSE)
    expect(callArgs[2].radiusX).toBe(defaultRadiusX)
    expect(callArgs[2].radiusY).toBe(defaultRadiusY)
  })
})
