import type { ElementFactory } from '@/core/factory'
import type { ShapeRepository } from '@/core/state/ShapeRepository'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
// Import mocked modules
import { appEventBus, cleanupEventSystem, initializeEventSystem } from '@/services/core/event-bus'
import { getServiceRegistry } from '@/services/core/registry'

import { cleanupServices, getService, initializeServices } from '@/services/core/registry/initialize'
import { registerValidationService } from '@/services/core/validation/registry'
import { registerElementServices } from '@/services/elements/element-actions'
import { registerKeyboardService } from '@/services/input/keyboard-service/registry'
import { registerStorageService } from '@/services/storage/registry'
import { registerErrorService } from '@/services/system/error-service/registry'
import { registerLoggerService } from '@/services/system/logging/registry'
import { ServiceId } from '@/types/services/core/serviceIdentifier'

// Mock all dependencies
vi.mock('@/services/core/event-bus', () => ({
  appEventBus: {
    reset: vi.fn(),
    subscribe: vi.fn(),
    publish: vi.fn(),
    unsubscribe: vi.fn(),
    clear: vi.fn(),
  },
  initializeEventSystem: vi.fn(),
  cleanupEventSystem: vi.fn(),
}))

vi.mock('@/services/core/validation/registry', () => ({
  registerValidationService: vi.fn(),
}))

vi.mock('@/services/input/keyboard-service/registry', () => ({
  registerKeyboardService: vi.fn(),
}))

vi.mock('@/services/elements/element-actions', () => ({
  registerElementServices: vi.fn(),
}))

vi.mock('@/services/storage/registry', () => ({
  registerStorageService: vi.fn(),
}))

vi.mock('@/services/system/error-service/registry', () => ({
  registerErrorService: vi.fn(),
}))

vi.mock('@/services/system/logging/registry', () => ({
  registerLoggerService: vi.fn(),
}))

vi.mock('@/services/core/registry', () => ({
  getServiceRegistry: vi.fn(),
}))

describe('registry Initialize', () => {
  let mockRegistry: any
  let mockLogger: LoggerService
  let mockElementFactory: ElementFactory
  let mockShapeRepository: ShapeRepository
  let mockLoggerService: LoggerService

  beforeEach(() => {
    mockRegistry = {
      register: vi.fn(),
      getById: vi.fn(),
      clear: vi.fn(),
      has: vi.fn(),
      unregister: vi.fn(),
      getAll: vi.fn(),
    }

    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    }

    mockLoggerService = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    }

    mockElementFactory = {
      createElement: vi.fn(),
      createShape: vi.fn(),
    } as unknown as ElementFactory

    mockShapeRepository = {
      add: vi.fn(),
      remove: vi.fn(),
      get: vi.fn(),
      getAll: vi.fn(),
    } as unknown as ShapeRepository

    vi.mocked(getServiceRegistry).mockReturnValue(mockRegistry)
    vi.mocked(registerLoggerService).mockReturnValue(mockLoggerService)

    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('initializeServices', () => {
    it('should initialize all services successfully', async () => {
      const dependencies = {
        elementFactory: mockElementFactory,
        shapeRepository: mockShapeRepository,
        logger: mockLogger,
      }

      await initializeServices(dependencies)

      expect(initializeEventSystem).toHaveBeenCalledTimes(1)
      expect(mockRegistry.register).toHaveBeenCalledWith(ServiceId.EventBus, appEventBus)
      expect(registerLoggerService).toHaveBeenCalledWith(mockRegistry)
      expect(registerValidationService).toHaveBeenCalledWith(mockRegistry, mockLoggerService)
      expect(registerErrorService).toHaveBeenCalledWith(mockRegistry, mockLoggerService)
      expect(mockRegistry.register).toHaveBeenCalledWith(ServiceId.ShapeRepository, mockShapeRepository)
      expect(registerElementServices).toHaveBeenCalledWith(mockRegistry, mockElementFactory, mockShapeRepository, mockLoggerService)
      expect(registerKeyboardService).toHaveBeenCalledWith(mockRegistry, mockLoggerService)
      expect(registerStorageService).toHaveBeenCalledWith(mockRegistry, mockLoggerService)
      expect(mockLogger.info).toHaveBeenCalledWith('Services initialized successfully')
    })

    it('should call services in correct order', async () => {
      const callOrder: string[] = []

      vi.mocked(initializeEventSystem).mockImplementation(() => {
        callOrder.push('initializeEventSystem')
      })

      mockRegistry.register.mockImplementation((serviceId: string) => {
        if (serviceId === ServiceId.EventBus) {
          callOrder.push('register-eventbus')
        }
        else if (serviceId === ServiceId.ShapeRepository) {
          callOrder.push('register-shaperepository')
        }
      })

      vi.mocked(registerLoggerService).mockImplementation(() => {
        callOrder.push('registerLoggerService')
        return mockLoggerService
      })

      vi.mocked(registerValidationService).mockImplementation(() => {
        callOrder.push('registerValidationService')
      })

      vi.mocked(registerErrorService).mockImplementation(() => {
        callOrder.push('registerErrorService')
      })

      vi.mocked(registerElementServices).mockImplementation(() => {
        callOrder.push('registerElementServices')
      })

      vi.mocked(registerKeyboardService).mockImplementation(() => {
        callOrder.push('registerKeyboardService')
      })

      vi.mocked(registerStorageService).mockImplementation(() => {
        callOrder.push('registerStorageService')
      })

      const dependencies = {
        elementFactory: mockElementFactory,
        shapeRepository: mockShapeRepository,
        logger: mockLogger,
      }

      await initializeServices(dependencies)

      expect(callOrder).toEqual([
        'initializeEventSystem',
        'register-eventbus',
        'registerLoggerService',
        'registerValidationService',
        'registerErrorService',
        'register-shaperepository',
        'registerElementServices',
        'registerKeyboardService',
        'registerStorageService',
      ])
    })

    it('should handle initialization errors gracefully', async () => {
      const error = new Error('Initialization failed')
      vi.mocked(initializeEventSystem).mockImplementation(() => {
        throw error
      })

      const dependencies = {
        elementFactory: mockElementFactory,
        shapeRepository: mockShapeRepository,
        logger: mockLogger,
      }

      await expect(initializeServices(dependencies)).rejects.toThrow(error)
      expect(mockLogger.error).toHaveBeenCalledWith('Service initialization failed', error)
    })

    it('should handle service registration errors', async () => {
      const error = new Error('Registration failed')
      vi.mocked(registerLoggerService).mockImplementation(() => {
        throw error
      })

      const dependencies = {
        elementFactory: mockElementFactory,
        shapeRepository: mockShapeRepository,
        logger: mockLogger,
      }

      await expect(initializeServices(dependencies)).rejects.toThrow(error)
      expect(mockLogger.error).toHaveBeenCalledWith('Service initialization failed', error)
    })

    it('should handle registry errors', async () => {
      const error = new Error('Registry error')
      mockRegistry.register.mockImplementation(() => {
        throw error
      })

      const dependencies = {
        elementFactory: mockElementFactory,
        shapeRepository: mockShapeRepository,
        logger: mockLogger,
      }

      await expect(initializeServices(dependencies)).rejects.toThrow(error)
      expect(mockLogger.error).toHaveBeenCalledWith('Service initialization failed', error)
    })

    it('should register ShapeRepository with correct service ID', async () => {
      const dependencies = {
        elementFactory: mockElementFactory,
        shapeRepository: mockShapeRepository,
        logger: mockLogger,
      }

      await initializeServices(dependencies)

      expect(mockRegistry.register).toHaveBeenCalledWith(ServiceId.ShapeRepository, mockShapeRepository)
      expect(mockLoggerService.info).toHaveBeenCalledWith('ShapeRepository registered successfully')
    })

    it('should work with different dependency implementations', async () => {
      const customElementFactory = {
        createElement: vi.fn(),
        createShape: vi.fn(),
        customMethod: vi.fn(),
      } as unknown as ElementFactory

      const customShapeRepository = {
        add: vi.fn(),
        remove: vi.fn(),
        get: vi.fn(),
        getAll: vi.fn(),
        customMethod: vi.fn(),
      } as unknown as ShapeRepository

      const dependencies = {
        elementFactory: customElementFactory,
        shapeRepository: customShapeRepository,
        logger: mockLogger,
      }

      await initializeServices(dependencies)

      expect(registerElementServices).toHaveBeenCalledWith(mockRegistry, customElementFactory, customShapeRepository, mockLoggerService)
      expect(mockRegistry.register).toHaveBeenCalledWith(ServiceId.ShapeRepository, customShapeRepository)
    })
  })

  describe('cleanupServices', () => {
    it('should cleanup all services successfully', async () => {
      await cleanupServices()

      expect(cleanupEventSystem).toHaveBeenCalledTimes(1)
      expect(mockRegistry.clear).toHaveBeenCalledTimes(1)
    })

    it('should call cleanup functions in correct order', async () => {
      const callOrder: string[] = []

      vi.mocked(cleanupEventSystem).mockImplementation(() => {
        callOrder.push('cleanupEventSystem')
      })

      mockRegistry.clear.mockImplementation(() => {
        callOrder.push('registry.clear')
      })

      await cleanupServices()

      expect(callOrder).toEqual([
        'cleanupEventSystem',
        'registry.clear',
      ])
    })

    it('should handle cleanup errors gracefully', async () => {
      const error = new Error('Cleanup failed')
      vi.mocked(cleanupEventSystem).mockImplementation(() => {
        throw error
      })

      await expect(cleanupServices()).rejects.toThrow(error)
      expect(cleanupEventSystem).toHaveBeenCalledTimes(1)
      expect(mockRegistry.clear).not.toHaveBeenCalled()
    })

    it('should handle registry clear errors', async () => {
      const error = new Error('Registry clear failed')
      mockRegistry.clear.mockImplementation(() => {
        throw error
      })

      await expect(cleanupServices()).rejects.toThrow(error)
      expect(cleanupEventSystem).toHaveBeenCalledTimes(1)
      expect(mockRegistry.clear).toHaveBeenCalledTimes(1)
    })

    it('should handle multiple cleanup calls', async () => {
      await cleanupServices()
      await cleanupServices()
      await cleanupServices()

      expect(cleanupEventSystem).toHaveBeenCalledTimes(3)
      expect(mockRegistry.clear).toHaveBeenCalledTimes(3)
    })
  })

  describe('getService', () => {
    it('should get service from registry successfully', () => {
      const mockService = { test: 'service' }
      mockRegistry.getById.mockReturnValue(mockService)

      const result = getService(ServiceId.EventBus)

      expect(mockRegistry.getById).toHaveBeenCalledWith(ServiceId.EventBus)
      expect(result).toBe(mockService)
    })

    it('should handle service not found errors', () => {
      const error = new Error('Service not found')
      mockRegistry.getById.mockImplementation(() => {
        throw error
      })

      expect(() => getService(ServiceId.EventBus)).toThrow(error)
      expect(mockRegistry.getById).toHaveBeenCalledWith(ServiceId.EventBus)
    })

    it('should work with different service IDs', () => {
      const services = {
        [ServiceId.EventBus]: { type: 'eventbus' },
        [ServiceId.ValidationService]: { type: 'validation' },
        [ServiceId.LoggerService]: { type: 'logger' },
      }

      mockRegistry.getById.mockImplementation((serviceId: string) => services[serviceId])

      expect(getService(ServiceId.EventBus)).toBe(services[ServiceId.EventBus])
      expect(getService(ServiceId.ValidationService)).toBe(services[ServiceId.ValidationService])
      expect(getService(ServiceId.LoggerService)).toBe(services[ServiceId.LoggerService])
    })

    it('should provide type safety', () => {
      const mockService = { test: 'service' }
      mockRegistry.getById.mockReturnValue(mockService)

      const result = getService<typeof mockService>(ServiceId.EventBus)

      expect(result).toBe(mockService)
      expect(typeof result.test).toBe('string')
    })
  })

  describe('integration scenarios', () => {
    it('should handle complete initialization and cleanup cycle', async () => {
      const dependencies = {
        elementFactory: mockElementFactory,
        shapeRepository: mockShapeRepository,
        logger: mockLogger,
      }

      // Initialize
      await initializeServices(dependencies)

      expect(initializeEventSystem).toHaveBeenCalledTimes(1)
      expect(mockRegistry.register).toHaveBeenCalled()

      // Cleanup
      await cleanupServices()

      expect(cleanupEventSystem).toHaveBeenCalledTimes(1)
      expect(mockRegistry.clear).toHaveBeenCalledTimes(1)
    })

    it('should handle service retrieval after initialization', async () => {
      const dependencies = {
        elementFactory: mockElementFactory,
        shapeRepository: mockShapeRepository,
        logger: mockLogger,
      }

      await initializeServices(dependencies)

      const mockService = { test: 'service' }
      mockRegistry.getById.mockReturnValue(mockService)

      const result = getService(ServiceId.EventBus)
      expect(result).toBe(mockService)
    })
  })
})
