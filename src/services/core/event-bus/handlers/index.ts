/**
 * Event Handlers Module Index
 *
 * This module exports functions for setting up and managing event handlers
 * throughout the application. These handlers respond to events published
 * on the application's event bus.
 *
 * The primary export is the setupEventHandlers function, which registers
 * core event handlers for monitoring and responding to system events.
 *
 * @module services/core/event-bus/handlers
 */

// Remove exports related to DOMEventHandler
// export * from './DOMEventHandler';

// Export the main function for setting up core event handlers
export { setupEventHandlers } from './eventHandlers' // Corrected to camelCase
