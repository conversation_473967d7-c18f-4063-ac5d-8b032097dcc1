import type { Line } from '@/types/core/element/path/linePathTypes'
import type { ShapeElement } from '@/types/core/elementDefinitions'

import { describe, expect, it } from 'vitest'

describe('line Interface', () => {
  it('should define a line with start and end points', () => {
    // Create a valid Line object
    const line: Line = {
      id: 'line-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 55, y: 110 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 10, y: 20 },
      end: { x: 100, y: 200 },
    }

    // Verify the object is valid
    expect(line.id).toBe('line-1')
    expect(line.type).toBe('shape')
    expect(line.start.x).toBe(10)
    expect(line.start.y).toBe(20)
    expect(line.end.x).toBe(100)
    expect(line.end.y).toBe(200)
  })

  it('should extend ShapeElement interface', () => {
    // Create a line
    const line: Line = {
      id: 'line-2',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 25, y: 25 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 0 },
      end: { x: 50, y: 50 },
    }

    // Verify the line extends ShapeElement
    const shapeElement: ShapeElement = line
    expect(shapeElement.id).toBe('line-2')
    expect(shapeElement.type).toBe('shape')
  })

  it('should allow creating lines with different point values', () => {
    // Create lines with different values
    const horizontalLine: Line = {
      id: 'horizontal',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 10 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 10 },
      end: { x: 100, y: 10 },
    }

    const verticalLine: Line = {
      id: 'vertical',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 10, y: 50 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 10, y: 0 },
      end: { x: 10, y: 100 },
    }

    const diagonalLine: Line = {
      id: 'diagonal',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 0 },
      end: { x: 100, y: 100 },
    }

    // Verify all lines are valid
    expect(horizontalLine.start.x).toBe(0)
    expect(horizontalLine.start.y).toBe(10)
    expect(horizontalLine.end.x).toBe(100)
    expect(horizontalLine.end.y).toBe(10)

    expect(verticalLine.start.x).toBe(10)
    expect(verticalLine.start.y).toBe(0)
    expect(verticalLine.end.x).toBe(10)
    expect(verticalLine.end.y).toBe(100)

    expect(diagonalLine.start.x).toBe(0)
    expect(diagonalLine.start.y).toBe(0)
    expect(diagonalLine.end.x).toBe(100)
    expect(diagonalLine.end.y).toBe(100)
  })

  it('should be usable in arrays and collections', () => {
    // Create an array of lines
    const lines: Line[] = [
      {
        id: 'line-3',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 50, y: 0 },
        rotation: 0,

        selectable: true,
        draggable: true,
        showHandles: true,
        start: { x: 0, y: 0 },
        end: { x: 100, y: 0 },
      },
      {
        id: 'line-4',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 100, y: 50 },
        rotation: 0,

        selectable: true,
        draggable: true,
        showHandles: true,
        start: { x: 100, y: 0 },
        end: { x: 100, y: 100 },
      },
      {
        id: 'line-5',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 50, y: 100 },
        rotation: 0,

        selectable: true,
        draggable: true,
        showHandles: true,
        start: { x: 100, y: 100 },
        end: { x: 0, y: 100 },
      },
      {
        id: 'line-6',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 0, y: 50 },
        rotation: 0,

        selectable: true,
        draggable: true,
        showHandles: true,
        start: { x: 0, y: 100 },
        end: { x: 0, y: 0 },
      },
    ]

    // Verify the array is valid
    expect(lines.length).toBe(4)
    expect(lines[0].id).toBe('line-3')
    expect(lines[0].start.x).toBe(0)
    expect(lines[0].start.y).toBe(0)
    expect(lines[0].end.x).toBe(100)
    expect(lines[0].end.y).toBe(0)

    expect(lines[1].id).toBe('line-4')
    expect(lines[1].start.x).toBe(100)
    expect(lines[1].start.y).toBe(0)
    expect(lines[1].end.x).toBe(100)
    expect(lines[1].end.y).toBe(100)

    expect(lines[2].id).toBe('line-5')
    expect(lines[2].start.x).toBe(100)
    expect(lines[2].start.y).toBe(100)
    expect(lines[2].end.x).toBe(0)
    expect(lines[2].end.y).toBe(100)

    expect(lines[3].id).toBe('line-6')
    expect(lines[3].start.x).toBe(0)
    expect(lines[3].start.y).toBe(100)
    expect(lines[3].end.x).toBe(0)
    expect(lines[3].end.y).toBe(0)
  })

  it('should be usable in functions that require Line', () => {
    // Define a function that uses Line
    function calculateLineLength(line: Line): number {
      const dx = line.end.x - line.start.x
      const dy = line.end.y - line.start.y
      return Math.sqrt(dx * dx + dy * dy)
    }

    // Create a line
    const line: Line = {
      id: 'line-7',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 1.5, y: 2 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 0 },
      end: { x: 3, y: 4 },
    }

    // Test the function
    const length = calculateLineLength(line)
    expect(length).toBe(5)
  })

  it('should be compatible with JSON serialization/deserialization', () => {
    // Create a line
    const line: Line = {
      id: 'line-8',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 20, y: 30 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 10, y: 20 },
      end: { x: 30, y: 40 },
    }

    // Serialize to JSON and deserialize
    const serialized = JSON.stringify(line)
    const deserialized = JSON.parse(serialized) as Line

    // Verify the deserialized object is valid
    expect(deserialized.id).toBe('line-8')
    expect(deserialized.type).toBe('shape')
    expect(deserialized.start.x).toBe(10)
    expect(deserialized.start.y).toBe(20)
    expect(deserialized.end.x).toBe(30)
    expect(deserialized.end.y).toBe(40)
  })
})
