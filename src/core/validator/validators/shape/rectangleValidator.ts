/**
 * Contains validation logic specific to Rectangle and Square shapes.
 */

import type { Shape } from '@/types/core/elementDefinitions'
import type { ValidationError, ValidatableShape as ValidatorShape } from '@/types/core/validator/validator-interface'
import { ElementType } from '@/types/core/elementDefinitions' // Added ElementType and Shape
import { ValidationErrorCode } from '@/types/core/validator/error-codes'
import { BaseShapeValidator } from '../common/validatorBase'
// isValidPoint might be needed if position validation becomes more complex
// import { isValidPoint } from '../common/validationUtils';

/**
 * Concrete validator implementation for Rectangle and Square shapes.
 *
 * @extends BaseShapeValidator
 */
export class RectangleValidator extends BaseShapeValidator {
  /**
   * Validates properties specific to a Rectangle or Square element data structure.
   *
   * @param shape - The shape to validate.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected validateSpecific(shape: ValidatorShape): ValidationError[] {
    const errors: ValidationError[] = []

    if (shape.type !== ElementType.RECTANGLE && shape.type !== ElementType.SQUARE) {
      errors.push({
        code: ValidationErrorCode.INVALID_SHAPE_TYPE,
        message: `Internal Error: RectangleValidator received shape type ${shape.type}`,
        path: 'type',
      })
      return errors
    }

    const props = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined || {}
    const width = props.width as number | undefined
    const height = props.height as number | undefined
    const cornerRadius = props.cornerRadius as number | undefined

    // Validate width
    if (typeof width !== 'number' || width <= 0 || !Number.isFinite(width)) {
      errors.push({
        code: ValidationErrorCode.INVALID_DIMENSION,
        message: `Rectangle width must be a positive finite number. Received: ${width}`,
        path: 'properties.width',
        value: width,
      })
    }

    // Validate height
    if (typeof height !== 'number' || height <= 0 || !Number.isFinite(height)) {
      errors.push({
        code: ValidationErrorCode.INVALID_DIMENSION,
        message: `Rectangle height must be a positive finite number. Received: ${height}`,
        path: 'properties.height',
        value: height,
      })
    }

    // Validate cornerRadius (optional, but must be valid if present)
    if (cornerRadius !== undefined
      && (typeof cornerRadius !== 'number' || cornerRadius < 0 || !Number.isFinite(cornerRadius))) {
      errors.push({
        code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
        message: `Rectangle cornerRadius must be a non-negative finite number. Received: ${cornerRadius}`,
        path: 'properties.cornerRadius',
        value: cornerRadius,
      })
    }

    // Position validation is typically handled by BaseShapeValidator if it's a common property
    // or if specific rules for position are needed here.
    // Shape.Rectangle inherits position from ShapeElement.
    // if (rectangle.position !== undefined) { // Position is required on ShapeElement
    //   if (!isValidPoint(rectangle.position)) { // Assuming isValidPoint is available
    //     errors.push({
    //       code: ValidationErrorCode.INVALID_POSITION,
    //       message: 'Rectangle position must have valid x and y coordinates.',
    //       path: 'position',
    //       value: rectangle.position
    //     });
    //   }
    // }

    return errors
  }

  /**
   * Applies rectangle/square-specific business rules.
   *
   * @param shape - The shape to validate against business rules.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected applySpecificRules(shape: ValidatorShape): ValidationError[] { // Added shape parameter
    const errors: ValidationError[] = []
    const rectangle = shape as unknown as Shape.Rectangle

    if (rectangle.cornerRadius !== undefined
      && typeof rectangle.width === 'number' && Number.isFinite(rectangle.width)
      && typeof rectangle.height === 'number' && Number.isFinite(rectangle.height)
      && rectangle.cornerRadius > Math.min(rectangle.width, rectangle.height) / 2) {
      errors.push({
        code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
        message: 'Corner radius cannot be greater than half the smaller dimension of the rectangle.',
        path: 'cornerRadius',
        value: rectangle.cornerRadius,
      })
    }
    return errors
  }
}
