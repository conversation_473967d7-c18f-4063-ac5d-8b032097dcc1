import { expect, test } from '@playwright/test'
import { appEventBus } from '@/services/event-bus/AppEventBus'

test.describe('AppEventBus', () => {
  test.beforeEach(() => {
    // 重置事件总线
    appEventBus.reset()
  })

  test('should subscribe and publish events correctly', () => {
    let receivedEvent = null

    // 订阅测试事件
    appEventBus.subscribe('TEST_EVENT', (event) => {
      receivedEvent = event
    })

    // 发布事件
    const testEvent = {
      type: 'TEST_EVENT',
      payload: { data: 'test' },
    }

    appEventBus.publish(testEvent)

    expect(receivedEvent).toBeDefined()
    expect(receivedEvent.type).toBe('TEST_EVENT')
    expect(receivedEvent.payload).toEqual({ data: 'test' })
  })

  test('should handle async events correctly', async () => {
    const results: string[] = []

    // 订阅异步事件
    appEventBus.subscribe('ASYNC_EVENT', async (event) => {
      await new Promise(resolve => setTimeout(resolve, 100))
      results.push(event.payload.data)
    }, { async: true })

    // 发布异步事件
    await appEventBus.publishAsync({
      type: 'ASYNC_EVENT',
      payload: { data: 'async test' },
    })

    expect(results).toContain('async test')
  })

  test('should handle debounced events', async () => {
    let callCount = 0

    // 订阅防抖事件
    appEventBus.subscribe('DEBOUNCE_EVENT', () => {
      callCount++
    }, { debounce: 100 })

    // 快速发布多个事件
    appEventBus.publish({ type: 'DEBOUNCE_EVENT', payload: {} })
    appEventBus.publish({ type: 'DEBOUNCE_EVENT', payload: {} })
    appEventBus.publish({ type: 'DEBOUNCE_EVENT', payload: {} })

    // 等待防抖时间
    await new Promise(resolve => setTimeout(resolve, 200))

    expect(callCount).toBe(1)
  })

  test('should handle throttled events', async () => {
    let callCount = 0

    // 订阅节流事件
    appEventBus.subscribe('THROTTLE_EVENT', () => {
      callCount++
    }, { throttle: 100 })

    // 快速发布多个事件
    appEventBus.publish({ type: 'THROTTLE_EVENT', payload: {} })
    appEventBus.publish({ type: 'THROTTLE_EVENT', payload: {} })
    appEventBus.publish({ type: 'THROTTLE_EVENT', payload: {} })

    // 等待节流时间
    await new Promise(resolve => setTimeout(resolve, 200))

    expect(callCount).toBe(1)
  })

  test('should handle once subscription', () => {
    let callCount = 0

    // 订阅一次性事件
    appEventBus.once('ONCE_EVENT', () => {
      callCount++
    })

    // 发布多次事件
    appEventBus.publish({ type: 'ONCE_EVENT', payload: {} })
    appEventBus.publish({ type: 'ONCE_EVENT', payload: {} })

    expect(callCount).toBe(1)
  })

  test('should unsubscribe correctly', () => {
    let callCount = 0
    const handler = () => { callCount++ }

    // 订阅事件
    const unsubscribe = appEventBus.subscribe('UNSUBSCRIBE_EVENT', handler)

    // 发布事件
    appEventBus.publish({ type: 'UNSUBSCRIBE_EVENT', payload: {} })
    expect(callCount).toBe(1)

    // 取消订阅
    unsubscribe()

    // 再次发布事件
    appEventBus.publish({ type: 'UNSUBSCRIBE_EVENT', payload: {} })
    expect(callCount).toBe(1) // 计数应该没有增加
  })

  test('should handle event filtering', () => {
    let receivedEvent = null

    // 订阅带过滤器的事件
    appEventBus.subscribe('FILTER_EVENT', (event) => {
      receivedEvent = event
    }, {
      filter: event => event.payload.value > 10,
    })

    // 发布不符合过滤条件的事件
    appEventBus.publish({
      type: 'FILTER_EVENT',
      payload: { value: 5 },
    })
    expect(receivedEvent).toBeNull()

    // 发布符合过滤条件的事件
    appEventBus.publish({
      type: 'FILTER_EVENT',
      payload: { value: 15 },
    })
    expect(receivedEvent).toBeDefined()
    expect(receivedEvent.payload.value).toBe(15)
  })

  test('should handle event bus configuration', () => {
    // 配置事件总线
    appEventBus.configure({
      enableLogging: true,
      defaultDebounceTime: 500,
    })

    const subscriptions = appEventBus.getSubscriptions()
    expect(subscriptions.size).toBe(0) // 新的配置不应影响现有订阅

    // 重置事件总线
    appEventBus.reset()
  })

  test('should create events with timestamp', () => {
    const event = appEventBus.createEvent('TIMESTAMP_EVENT', { data: 'test' })

    expect(event.type).toBe('TIMESTAMP_EVENT')
    expect(event.timestamp).toBeDefined()
    expect(typeof event.timestamp).toBe('number')
    expect(event.payload).toEqual({ data: 'test' })
  })
})
