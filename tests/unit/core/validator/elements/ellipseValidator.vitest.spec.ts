import type { ValidatorShape } from '@/types/core/element/validator'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { EllipseValidator } from '@/core/validator/elements/ellipseValidator'
import { ElementType } from '@/types/core/shape-type'

describe('ellipseValidator', () => {
  let validator: EllipseValidator

  beforeEach(() => {
    validator = new EllipseValidator()
  })

  // 测试椭圆和圆形验证器的共同功能

  // 测试圆形验证
  describe('validateSpecific for Circle', () => {
    it('should validate a valid circle', () => {
      const validCircle: ValidatorShape = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
        radius: 10,
      }

      const errors = (validator as any).validateSpecific(validCircle)
      expect(errors).toEqual([])
    })

    it('should return error for missing radius', () => {
      const invalidCircle: ValidatorShape = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
      }

      const errors = (validator as any).validateSpecific(invalidCircle)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_RADIUS')
    })

    it('should return error for invalid radius type', () => {
      const invalidCircle: any = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
        radius: 'ten',
      }

      const errors = (validator as any).validateSpecific(invalidCircle)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_RADIUS')
    })

    it('should return error for negative radius', () => {
      const invalidCircle: any = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
        radius: -10,
      }

      const errors = (validator as any).validateSpecific(invalidCircle)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_RADIUS')
    })

    it('should return error for zero radius', () => {
      const invalidCircle: any = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
        radius: 0,
      }

      const errors = (validator as any).validateSpecific(invalidCircle)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_RADIUS')
    })

    it('should return error for invalid position in circle', () => {
      const invalidCircle: any = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: null,
        radius: 10,
      }

      const errors = (validator as any).validateSpecific(invalidCircle)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_POSITION')
    })
  })

  // 测试椭圆验证
  describe('validateSpecific for Ellipse', () => {
    it('should return error for invalid shape type', () => {
      const invalidEllipse: ValidatorShape = {
        id: 'ellipse-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        radiusX: 20,
        radiusY: 10,
      }

      const errors = (validator as any).validateSpecific(invalidEllipse)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_SHAPE_TYPE')
    })
    it('should validate a valid ellipse', () => {
      const validEllipse: ValidatorShape = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: 20,
        radiusY: 10,
      }

      const errors = (validator as any).validateSpecific(validEllipse)
      expect(errors).toEqual([])
    })

    it('should return error for missing radiusX', () => {
      const invalidEllipse: ValidatorShape = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusY: 10,
      }

      const errors = (validator as any).validateSpecific(invalidEllipse)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_RADIUS')
    })

    it('should return error for missing radiusY', () => {
      const invalidEllipse: ValidatorShape = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: 20,
      }

      const errors = (validator as any).validateSpecific(invalidEllipse)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_RADIUS')
    })

    it('should return error for invalid radiusX type', () => {
      const invalidEllipse: any = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: 'twenty',
        radiusY: 10,
      }

      const errors = (validator as any).validateSpecific(invalidEllipse)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_RADIUS')
    })

    it('should return error for invalid radiusY type', () => {
      const invalidEllipse: any = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: 20,
        radiusY: 'ten',
      }

      const errors = (validator as any).validateSpecific(invalidEllipse)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_RADIUS')
    })

    it('should return error for negative radiusX', () => {
      const invalidEllipse: any = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: -20,
        radiusY: 10,
      }

      const errors = (validator as any).validateSpecific(invalidEllipse)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_RADIUS')
    })

    it('should return error for negative radiusY', () => {
      const invalidEllipse: any = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: 20,
        radiusY: -10,
      }

      const errors = (validator as any).validateSpecific(invalidEllipse)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_RADIUS')
    })

    it('should return error for zero radiusX', () => {
      const invalidEllipse: any = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: 0,
        radiusY: 10,
      }

      const errors = (validator as any).validateSpecific(invalidEllipse)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_RADIUS')
    })

    it('should return error for zero radiusY', () => {
      const invalidEllipse: any = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: 20,
        radiusY: 0,
      }

      const errors = (validator as any).validateSpecific(invalidEllipse)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_RADIUS')
    })

    it('should return error for invalid position', () => {
      const invalidEllipse: any = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: null,
        radiusX: 20,
        radiusY: 10,
      }

      const errors = (validator as any).validateSpecific(invalidEllipse)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_POSITION')
    })
  })

  describe('applySpecificRules', () => {
    it('should apply specific rules for ellipses', () => {
      // This is a protected method, so we need to cast to any
      const applySpecificRulesSpy = vi.spyOn(validator as any, 'applySpecificRules')

      const validEllipse: ValidatorShape = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: 20,
        radiusY: 10,
      }

      validator.validate(validEllipse)
      expect(applySpecificRulesSpy).toHaveBeenCalled()
    })

    it('should apply specific rules for circles', () => {
      // This is a protected method, so we need to cast to any
      const applySpecificRulesSpy = vi.spyOn(validator as any, 'applySpecificRules')

      const validCircle: ValidatorShape = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
        radius: 10,
      }

      validator.validate(validCircle)
      expect(applySpecificRulesSpy).toHaveBeenCalled()
    })
  })

  describe('validate for Circle', () => {
    it('should validate a valid circle', () => {
      const validCircle: ValidatorShape = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
        radius: 10,
      }

      const result = validator.validate(validCircle)
      expect(result.valid).toBe(true)
      expect(result.errors).toEqual([])
    })

    it('should return invalid result when circle errors are found', () => {
      const invalidCircle: ValidatorShape = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
        // Missing radius
      }

      const result = validator.validate(invalidCircle)

      expect(result.valid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
      expect(result.errors.some(error => error.code === 'INVALID_RADIUS')).toBe(true)
    })

    it('should detect multiple validation errors for circle', () => {
      const invalidCircle: ValidatorShape = {
        // Missing id
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
        radius: 0, // Invalid radius
      }

      const result = validator.validate(invalidCircle)

      expect(result.valid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(1)
      expect(result.errors.some(error => error.code === 'MISSING_OR_INVALID_ID')).toBe(true)
      expect(result.errors.some(error => error.code === 'INVALID_RADIUS')).toBe(true)
    })
  })

  describe('validate for Ellipse', () => {
    it('should call validateSpecific', () => {
      const validEllipse: ValidatorShape = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: 20,
        radiusY: 10,
      }

      const validateSpecificSpy = vi.spyOn(validator as any, 'validateSpecific').mockReturnValue([])

      const result = validator.validate(validEllipse)

      expect(validateSpecificSpy).toHaveBeenCalledWith(validEllipse)
      expect(result.valid).toBe(true)
      expect(result.errors).toEqual([])

      validateSpecificSpy.mockRestore()
    })

    it('should return invalid result when errors are found', () => {
      const invalidEllipse: ValidatorShape = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: 0,
        radiusY: 10,
      }

      const result = validator.validate(invalidEllipse)

      expect(result.valid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
      expect(result.errors.some(error => error.code === 'INVALID_RADIUS')).toBe(true)
    })

    it('should detect multiple validation errors', () => {
      const invalidEllipse: ValidatorShape = {
        // Missing id
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: 0,
        radiusY: 0,
      }

      const result = validator.validate(invalidEllipse)

      expect(result.valid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(1)
      expect(result.errors.some(error => error.code === 'MISSING_OR_INVALID_ID')).toBe(true)
      expect(result.errors.some(error => error.code === 'INVALID_RADIUS')).toBe(true)
    })
  })
})
