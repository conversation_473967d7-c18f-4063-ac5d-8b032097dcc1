import type { CoreConfig } from '@/config'
import type { EventBus } from '@/types/services/events'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  publishConfigUpdated,
  publishExportRequest,
  publishHistoryRedo,
  publishHistoryUndo,
  publishTemplateApply,
} from '@/services/core/event-bus/helpers/publishers/dataPublishers'
import { AppEventType } from '@/types/services/events'

describe('data Publishers', () => {
  let mockEventBus: EventBus
  let publishSpy: ReturnType<typeof vi.fn>

  beforeEach(() => {
    publishSpy = vi.fn()
    mockEventBus = {
      publish: publishSpy,
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
      clear: vi.fn(),
    } as unknown as EventBus
  })

  describe('publishHistoryUndo', () => {
    it('should publish history undo event with correct structure', () => {
      publishHistoryUndo(mockEventBus)

      expect(publishSpy).toHaveBeenCalledTimes(1)
      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.HistoryUndo,
        payload: {},
      })
    })

    it('should handle multiple calls', () => {
      publishHistoryUndo(mockEventBus)
      publishHistoryUndo(mockEventBus)
      publishHistoryUndo(mockEventBus)

      expect(publishSpy).toHaveBeenCalledTimes(3)
      publishSpy.mock.calls.forEach((call) => {
        expect(call[0]).toEqual({
          type: AppEventType.HistoryUndo,
          payload: {},
        })
      })
    })

    it('should not throw when eventBus publish fails', () => {
      publishSpy.mockImplementation(() => {
        throw new Error('EventBus error')
      })

      expect(() => publishHistoryUndo(mockEventBus)).toThrow('EventBus error')
    })
  })

  describe('publishHistoryRedo', () => {
    it('should publish history redo event with correct structure', () => {
      publishHistoryRedo(mockEventBus)

      expect(publishSpy).toHaveBeenCalledTimes(1)
      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.HistoryRedo,
        payload: {},
      })
    })

    it('should handle multiple calls', () => {
      publishHistoryRedo(mockEventBus)
      publishHistoryRedo(mockEventBus)

      expect(publishSpy).toHaveBeenCalledTimes(2)
      publishSpy.mock.calls.forEach((call) => {
        expect(call[0]).toEqual({
          type: AppEventType.HistoryRedo,
          payload: {},
        })
      })
    })

    it('should not throw when eventBus publish fails', () => {
      publishSpy.mockImplementation(() => {
        throw new Error('EventBus error')
      })

      expect(() => publishHistoryRedo(mockEventBus)).toThrow('EventBus error')
    })
  })

  describe('publishTemplateApply', () => {
    it('should publish template apply event with correct structure', () => {
      const templateId = 'template-123'

      publishTemplateApply(mockEventBus, templateId)

      expect(publishSpy).toHaveBeenCalledTimes(1)
      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.TemplateApply,
        payload: { templateId },
      })
    })

    it('should handle different template IDs', () => {
      const templateIds = ['template-1', 'template-2', 'custom-template']

      templateIds.forEach((templateId) => {
        publishTemplateApply(mockEventBus, templateId)
      })

      expect(publishSpy).toHaveBeenCalledTimes(3)
      templateIds.forEach((templateId, index) => {
        expect(publishSpy.mock.calls[index][0]).toEqual({
          type: AppEventType.TemplateApply,
          payload: { templateId },
        })
      })
    })

    it('should handle empty template ID', () => {
      publishTemplateApply(mockEventBus, '')

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.TemplateApply,
        payload: { templateId: '' },
      })
    })

    it('should handle special characters in template ID', () => {
      const specialTemplateId = 'template-@#$%^&*()_+-=[]{}|;:,.<>?'

      publishTemplateApply(mockEventBus, specialTemplateId)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.TemplateApply,
        payload: { templateId: specialTemplateId },
      })
    })

    it('should handle unicode characters in template ID', () => {
      const unicodeTemplateId = '模板-🚀-αβγ'

      publishTemplateApply(mockEventBus, unicodeTemplateId)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.TemplateApply,
        payload: { templateId: unicodeTemplateId },
      })
    })
  })

  describe('publishExportRequest', () => {
    it('should publish export request event with correct structure', () => {
      const format = 'png'

      publishExportRequest(mockEventBus, format)

      expect(publishSpy).toHaveBeenCalledTimes(1)
      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ExportRequest,
        payload: { format },
      })
    })

    it('should handle different export formats', () => {
      const formats = ['png', 'svg', 'json', 'pdf', 'jpeg']

      formats.forEach((format) => {
        publishExportRequest(mockEventBus, format)
      })

      expect(publishSpy).toHaveBeenCalledTimes(5)
      formats.forEach((format, index) => {
        expect(publishSpy.mock.calls[index][0]).toEqual({
          type: AppEventType.ExportRequest,
          payload: { format },
        })
      })
    })

    it('should handle custom export formats', () => {
      const customFormat = 'custom-format-v2'

      publishExportRequest(mockEventBus, customFormat)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ExportRequest,
        payload: { format: customFormat },
      })
    })

    it('should handle empty format string', () => {
      publishExportRequest(mockEventBus, '')

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ExportRequest,
        payload: { format: '' },
      })
    })

    it('should handle format with special characters', () => {
      const specialFormat = 'format.with-special_chars@123'

      publishExportRequest(mockEventBus, specialFormat)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ExportRequest,
        payload: { format: specialFormat },
      })
    })
  })

  describe('publishConfigUpdated', () => {
    it('should publish config updated event with correct structure', () => {
      const config: Partial<CoreConfig> = {
        theme: 'dark',
        language: 'en',
      }

      publishConfigUpdated(mockEventBus, config)

      expect(publishSpy).toHaveBeenCalledTimes(1)
      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ConfigUpdated,
        payload: { config },
      })
    })

    it('should handle empty config object', () => {
      const config: Partial<CoreConfig> = {}

      publishConfigUpdated(mockEventBus, config)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ConfigUpdated,
        payload: { config },
      })
    })

    it('should handle partial config updates', () => {
      const configs = [
        { theme: 'light' },
        { language: 'zh' },
        { autoSave: true },
        { gridSize: 20 },
      ]

      configs.forEach((config) => {
        publishConfigUpdated(mockEventBus, config)
      })

      expect(publishSpy).toHaveBeenCalledTimes(4)
      configs.forEach((config, index) => {
        expect(publishSpy.mock.calls[index][0]).toEqual({
          type: AppEventType.ConfigUpdated,
          payload: { config },
        })
      })
    })

    it('should handle complex config objects', () => {
      const complexConfig: Partial<CoreConfig> = {
        theme: 'dark',
        language: 'en',
        features: {
          enableAdvancedMode: true,
          showDebugInfo: false,
        },
        ui: {
          showToolbar: true,
          showSidebar: false,
        },
      } as any

      publishConfigUpdated(mockEventBus, complexConfig)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ConfigUpdated,
        payload: { config: complexConfig },
      })
    })

    it('should handle config with null values', () => {
      const configWithNulls: Partial<CoreConfig> = {
        theme: null as any,
        language: 'en',
      }

      publishConfigUpdated(mockEventBus, configWithNulls)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ConfigUpdated,
        payload: { config: configWithNulls },
      })
    })

    it('should handle config with undefined values', () => {
      const configWithUndefined: Partial<CoreConfig> = {
        theme: undefined as any,
        language: 'en',
      }

      publishConfigUpdated(mockEventBus, configWithUndefined)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ConfigUpdated,
        payload: { config: configWithUndefined },
      })
    })
  })

  describe('error handling', () => {
    it('should propagate eventBus errors for all publishers', () => {
      const error = new Error('EventBus publish failed')
      publishSpy.mockImplementation(() => {
        throw error
      })

      expect(() => publishHistoryUndo(mockEventBus)).toThrow(error)
      expect(() => publishHistoryRedo(mockEventBus)).toThrow(error)
      expect(() => publishTemplateApply(mockEventBus, 'template')).toThrow(error)
      expect(() => publishExportRequest(mockEventBus, 'png')).toThrow(error)
      expect(() => publishConfigUpdated(mockEventBus, {})).toThrow(error)
    })

    it('should handle eventBus returning false', () => {
      publishSpy.mockReturnValue(false)

      // These should not throw even if eventBus returns false
      expect(() => publishHistoryUndo(mockEventBus)).not.toThrow()
      expect(() => publishHistoryRedo(mockEventBus)).not.toThrow()
      expect(() => publishTemplateApply(mockEventBus, 'template')).not.toThrow()
      expect(() => publishExportRequest(mockEventBus, 'png')).not.toThrow()
      expect(() => publishConfigUpdated(mockEventBus, {})).not.toThrow()
    })
  })

  describe('edge cases', () => {
    it('should handle very long template IDs', () => {
      const longTemplateId = 'a'.repeat(1000)

      publishTemplateApply(mockEventBus, longTemplateId)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.TemplateApply,
        payload: { templateId: longTemplateId },
      })
    })

    it('should handle very long export formats', () => {
      const longFormat = `format-${'x'.repeat(500)}`

      publishExportRequest(mockEventBus, longFormat)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ExportRequest,
        payload: { format: longFormat },
      })
    })

    it('should handle rapid successive calls', () => {
      for (let i = 0; i < 100; i++) {
        publishHistoryUndo(mockEventBus)
      }

      expect(publishSpy).toHaveBeenCalledTimes(100)
    })

    it('should handle mixed publisher calls', () => {
      publishHistoryUndo(mockEventBus)
      publishTemplateApply(mockEventBus, 'template-1')
      publishHistoryRedo(mockEventBus)
      publishExportRequest(mockEventBus, 'svg')
      publishConfigUpdated(mockEventBus, { theme: 'dark' })

      expect(publishSpy).toHaveBeenCalledTimes(5)
      expect(publishSpy.mock.calls[0][0].type).toBe(AppEventType.HistoryUndo)
      expect(publishSpy.mock.calls[1][0].type).toBe(AppEventType.TemplateApply)
      expect(publishSpy.mock.calls[2][0].type).toBe(AppEventType.HistoryRedo)
      expect(publishSpy.mock.calls[3][0].type).toBe(AppEventType.ExportRequest)
      expect(publishSpy.mock.calls[4][0].type).toBe(AppEventType.ConfigUpdated)
    })
  })
})
