/**
 * Text Geometry Component
 *
 * Implements the standardized geometry layout for Text elements
 * according to the design document specifications.
 *
 * Layout:
 * - Position (2 columns): Position X, Position Y
 * - Scale (2 columns): Bounding Box Width, Bounding Box Height
 * - Transform (1 column): Rotation
 * - Typography (1 column): Font Size
 * - Content (1 column): Text Content
 * - Calculation & Cost (unified layout)
 *
 * Note: Font Family, Font Style, and Text Alignment have been moved to the Appearance tab
 * to better separate geometric properties from visual appearance properties.
 *
 * Supports both TEXT and TEXT_LABEL element types.
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'

import { Label } from '@/components/ui/label'
import { ElementType } from '@/types/core/elementDefinitions'
import { GeometrySection, PropertyField } from '../shared'

interface TextGeometryProps {
  /** The text element being edited */
  element: ShapeElement
  /** Function to get input values with proper formatting */
  getValueForInput: (path: string) => string
  /** Function to get placeholder values */
  getPlaceholderForInput: (path: string) => string
  /** Function to handle input changes */
  handleInputChange: (path: string, value: string, isNumeric?: boolean, convertToPixels?: boolean) => void
  /** Unit name for display (e.g., "mm", "cm") */
  unitName: string
}

// List of supported text types
const TEXT_TYPES = [
  ElementType.TEXT,
  ElementType.TEXT_LABEL,
] as const

// Type definitions for store access
interface ShapesStore {
  getState: () => {
    shapes: ShapeElement[]
    selectedShapeIds: string[]
  }
  setState: (state: {
    shapes: ShapeElement[]
    selectedShapeIds: string[]
  }) => void
}

// Type guard for window with store
function hasShapesStore(win: Window): win is Window & { __ZUSTAND_SHAPES_STORE__: ShapesStore } {
  return '__ZUSTAND_SHAPES_STORE__' in win && win.__ZUSTAND_SHAPES_STORE__ !== undefined
}

/**
 * Helper function to handle font size updates
 */
function handleFontSizeUpdate(element: ShapeElement, value: string) {
  // 🔧 使用直接store更新，确保字体大小正确更新
  if (typeof window !== 'undefined' && hasShapesStore(window)) {
    const shapesStore = window.__ZUSTAND_SHAPES_STORE__
    const state = shapesStore.getState()

    const numericValue = Number(value)
    if (Number.isNaN(numericValue) || numericValue <= 0) {
      console.warn(`🔧 [TextGeometry] Invalid font size value: ${value}`)
      return
    }

    // 创建更新后的元素
    const updatedElement = {
      ...element,
      fontSize: numericValue,
      properties: {
        ...element.properties,
        fontSize: numericValue,
      },
    }

    // 更新store中的shapes数组
    const newShapes = state.shapes.map((s: ShapeElement) =>
      s.id === element.id ? updatedElement : s,
    )

    // 🔧 关键修复：保持选中状态
    shapesStore.setState({
      ...state,
      shapes: newShapes,
      selectedShapeIds: state.selectedShapeIds, // 保持元素选中
    })
  }
}

/**
 * Helper function to handle text content updates
 */
function handleTextContentUpdate(element: ShapeElement, value: string) {
  // 🔧 使用直接store更新，确保文本内容正确更新
  if (typeof window !== 'undefined' && hasShapesStore(window)) {
    const shapesStore = window.__ZUSTAND_SHAPES_STORE__
    const state = shapesStore.getState()

    // 创建更新后的元素
    const updatedElement = {
      ...element,
      text: value,
      properties: {
        ...element.properties,
        text: value,
      },
    }

    // 更新store中的shapes数组
    const newShapes = state.shapes.map((s: ShapeElement) =>
      s.id === element.id ? updatedElement : s,
    )

    // 🔧 关键修复：保持选中状态
    shapesStore.setState({
      ...state,
      shapes: newShapes,
      selectedShapeIds: state.selectedShapeIds, // 保持元素选中
    })
  }
}

/**
 * TextGeometry component for editing text properties.
 *
 * Provides a standardized layout for text geometry editing
 * with proper grouping and responsive design. Supports both
 * regular text and text label elements.
 */
export function TextGeometry({
  element,
  getValueForInput,
  getPlaceholderForInput,
  handleInputChange,
  unitName,
}: TextGeometryProps) {
  // Verify this is a text element
  if (!TEXT_TYPES.includes(element.type as typeof TEXT_TYPES[number])) {
    return null
  }

  // Get the text type name for display
  const getTextTypeName = (type: typeof TEXT_TYPES[number]): string => {
    switch (type) {
      case ElementType.TEXT_LABEL:
        return 'Text Label'
      case ElementType.TEXT:
      default:
        return 'Text'
    }
  }

  const textTypeName = getTextTypeName(element.type as typeof TEXT_TYPES[number])

  return (
    <>
      {/* Position (2 columns) */}
      <GeometrySection type="position" columns={2}>
        <PropertyField
          label={`Position X (${unitName})`}
          type="number"
          value={getValueForInput('position.x')}
          onChange={value => handleInputChange('position.x', value)}
          placeholder={getPlaceholderForInput('position.x')}
        />
        <PropertyField
          label={`Position Y (${unitName})`}
          type="number"
          value={getValueForInput('position.y')}
          onChange={value => handleInputChange('position.y', value)}
          placeholder={getPlaceholderForInput('position.y')}
        />
      </GeometrySection>

      {/* Scale (2 columns) */}
      <GeometrySection type="dimensions" columns={2}>
        <PropertyField
          label={`Width (${unitName})`}
          type="number"
          value={getValueForInput('width')}
          onChange={value => handleInputChange('width', value)}
          placeholder="0"
          helpText={`${textTypeName} bounding box width`}
        />
        <PropertyField
          label={`Height (${unitName})`}
          type="number"
          value={getValueForInput('height')}
          onChange={value => handleInputChange('height', value)}
          placeholder="0"
          helpText={`${textTypeName} bounding box height`}
        />
      </GeometrySection>

      {/* Transform (1 column) */}
      <GeometrySection type="transform" columns={1}>
        <PropertyField
          label="Rotation (°)"
          type="number"
          value={getValueForInput('rotation')}
          onChange={value => handleInputChange('rotation', value, true, false)}
          placeholder="0"
        />
      </GeometrySection>

      {/* Typography (1 column) - Only Font Size */}
      <GeometrySection type="typography" columns={1}>
        <PropertyField
          label={`Font Size (${unitName})`}
          type="number"
          value={getValueForInput('properties.fontSize') || getValueForInput('fontSize')}
          onChange={value => handleFontSizeUpdate(element, value)}
          placeholder="16"
          helpText="Text font size"
        />
      </GeometrySection>

      {/* Content (1 column) */}
      <GeometrySection type="content" columns={1}>
        <div className="space-y-1">
          <Label htmlFor="textContent" className="text-sm font-medium">Text Content</Label>
          <textarea
            id="textContent"
            className="flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 resize-vertical"
            value={String(getValueForInput('text') || getValueForInput('content') || getValueForInput('properties.text') || getValueForInput('properties.content') || '')}
            onChange={e => handleTextContentUpdate(element, e.target.value)}
            placeholder="Enter your text content here..."
            rows={3}
          />
        </div>
      </GeometrySection>
    </>
  )
}
