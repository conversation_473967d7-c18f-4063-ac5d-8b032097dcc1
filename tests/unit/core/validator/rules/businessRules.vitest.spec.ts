import { describe, expect, it, vi } from 'vitest'
import * as businessRules from '@/core/validator/rules/businessRules'
import { ElementType } from '@/types/core/shape-type'

describe('businessRules', () => {
  describe('sizeRule', () => {
    it('should return empty array for valid shape size', () => {
      const shape: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 50,
      }

      const result = businessRules.sizeRule.apply(shape)
      expect(result).toEqual([])
    })

    it('should return error for rectangle with zero width', () => {
      const shape: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 0,
        height: 50,
      }

      const result = businessRules.sizeRule.apply(shape)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].code).toBe('INVALID_RECTANGLE_WIDTH')
    })

    it('should return error for rectangle with zero height', () => {
      const shape: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 0,
      }

      const result = businessRules.sizeRule.apply(shape)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].code).toBe('INVALID_RECTANGLE_HEIGHT')
    })

    it('should return error for circle with zero radius', () => {
      const shape: any = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
        radius: 0,
      }

      const result = businessRules.sizeRule.apply(shape)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].code).toBe('INVALID_CIRCLE_RADIUS')
    })

    it('should return error for ellipse with zero radiusX', () => {
      const shape: any = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: 0,
        radiusY: 50,
      }

      const result = businessRules.sizeRule.apply(shape)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].code).toBe('INVALID_ELLIPSE_RADIUS_X')
    })

    it('should return error for ellipse with zero radiusY', () => {
      const shape: any = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: 100,
        radiusY: 0,
      }

      const result = businessRules.sizeRule.apply(shape)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].code).toBe('INVALID_ELLIPSE_RADIUS_Y')
    })
  })

  describe('pointCountRule', () => {
    it('should return error for polygon with less than 3 points', () => {
      const shape: any = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          { x: 10, y: 0 },
        ],
      }

      const result = businessRules.pointCountRule.apply(shape)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].code).toBe('INVALID_POINT_COUNT')
    })

    it('should return empty array for polygon with 3 or more points', () => {
      const shape: any = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          { x: 10, y: 0 },
          { x: 10, y: 10 },
        ],
      }

      const result = businessRules.pointCountRule.apply(shape)
      expect(result).toEqual([])
    })

    it('should return empty array for non-polygon shapes', () => {
      const shape: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 50,
      }

      const result = businessRules.pointCountRule.apply(shape)
      expect(result).toEqual([])
    })
  })

  describe('polygonClosureRule', () => {
    it('should return error for non-closed polygon', () => {
      const shape: any = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          { x: 10, y: 0 },
          { x: 10, y: 10 },
          { x: 0, y: 10 }, // Not closed (last point != first point)
        ],
      }

      const result = businessRules.polygonClosureRule.apply(shape)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].code).toBe('POLYGON_NOT_CLOSED')
    })

    it('should return empty array for closed polygon', () => {
      const shape: any = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          { x: 10, y: 0 },
          { x: 10, y: 10 },
          { x: 0, y: 0 }, // Closed (last point == first point)
        ],
      }

      const result = businessRules.polygonClosureRule.apply(shape)
      expect(result).toEqual([])
    })

    it('should return empty array for non-polygon shapes', () => {
      const shape: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 50,
      }

      const result = businessRules.polygonClosureRule.apply(shape)
      expect(result).toEqual([])
    })
  })

  describe('idRule', () => {
    it('should return empty array for valid id', () => {
      const shape: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
      }

      const result = businessRules.idRule.apply(shape)
      expect(result).toEqual([])
    })

    it('should return error for empty id', () => {
      const shape: any = {
        id: '',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
      }

      const result = businessRules.idRule.apply(shape)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].code).toBe('MISSING_ID')
    })

    it('should return error for missing id', () => {
      const shape: any = {
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
      }

      const result = businessRules.idRule.apply(shape)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].code).toBe('MISSING_ID')
    })
  })

  describe('minSizeRule', () => {
    it('should return empty array for valid shape sizes', () => {
      const shape: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 50,
      }

      const result = businessRules.minSizeRule.apply(shape)
      expect(result).toEqual([])
    })

    it('should return error for circle with radius less than minimum', () => {
      const shape: any = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
        radius: 1, // Less than MIN_DIMENSION (5)
      }

      const result = businessRules.minSizeRule.apply(shape)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].code).toBe('RADIUS_TOO_SMALL')
    })

    it('should return error for rectangle with width less than minimum', () => {
      const shape: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 1, // Less than MIN_DIMENSION (5)
        height: 50,
      }

      const result = businessRules.minSizeRule.apply(shape)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].code).toBe('WIDTH_TOO_SMALL')
    })

    it('should return error for rectangle with height less than minimum', () => {
      const shape: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 1, // Less than MIN_DIMENSION (5)
      }

      const result = businessRules.minSizeRule.apply(shape)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].code).toBe('HEIGHT_TOO_SMALL')
    })

    it('should return error for ellipse with radiusX less than minimum', () => {
      const shape: any = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: 1, // Less than MIN_DIMENSION (5)
        radiusY: 50,
      }

      const result = businessRules.minSizeRule.apply(shape)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].code).toBe('RADIUS_X_TOO_SMALL')
    })

    it('should return error for ellipse with radiusY less than minimum', () => {
      const shape: any = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        radiusX: 50,
        radiusY: 1, // Less than MIN_DIMENSION (5)
      }

      const result = businessRules.minSizeRule.apply(shape)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].code).toBe('RADIUS_Y_TOO_SMALL')
    })

    it('should return error for line that is too short', () => {
      const shape: any = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        start: { x: 0, y: 0 },
        end: { x: 1, y: 1 }, // Length is sqrt(2) which is less than MIN_DIMENSION (5)
      }

      const result = businessRules.minSizeRule.apply(shape)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0].code).toBe('LINE_TOO_SHORT')
    })

    it('should return empty array for unsupported shape types', () => {
      const shape: any = {
        id: 'unknown-1',
        type: 'UNKNOWN' as ElementType,
        position: { x: 0, y: 0 },
      }

      const result = businessRules.minSizeRule.apply(shape)
      expect(result).toEqual([])
    })
  })

  describe('applyBusinessRules', () => {
    it('should apply all registered rules', () => {
      const shape: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 50,
      }

      // Mock the rule application to track calls
      const idRuleSpy = vi.spyOn(businessRules.idRule, 'apply')
      const sizeRuleSpy = vi.spyOn(businessRules.sizeRule, 'apply')
      const minSizeRuleSpy = vi.spyOn(businessRules.minSizeRule, 'apply')
      const pointCountRuleSpy = vi.spyOn(businessRules.pointCountRule, 'apply')
      const polygonClosureRuleSpy = vi.spyOn(businessRules.polygonClosureRule, 'apply')

      // All mocks return empty arrays (no errors)
      idRuleSpy.mockReturnValue([])
      sizeRuleSpy.mockReturnValue([])
      minSizeRuleSpy.mockReturnValue([])
      pointCountRuleSpy.mockReturnValue([])
      polygonClosureRuleSpy.mockReturnValue([])

      const result = businessRules.applyBusinessRules(shape)

      // Verify all rules were applied
      expect(idRuleSpy).toHaveBeenCalledWith(shape)
      expect(sizeRuleSpy).toHaveBeenCalledWith(shape)
      expect(minSizeRuleSpy).toHaveBeenCalledWith(shape)
      expect(pointCountRuleSpy).toHaveBeenCalledWith(shape)
      expect(polygonClosureRuleSpy).toHaveBeenCalledWith(shape)

      // Result should be an empty array since all rules return empty arrays
      expect(result).toEqual([])
    })

    it('should collect errors from all rules', () => {
      // Mock the rule application to return errors
      const idRuleSpy = vi.spyOn(businessRules.idRule, 'apply')
      const sizeRuleSpy = vi.spyOn(businessRules.sizeRule, 'apply')

      // Mock errors from different rules
      idRuleSpy.mockReturnValue([{ code: 'MISSING_ID', message: 'Missing ID' }])
      sizeRuleSpy.mockReturnValue([{ code: 'INVALID_RECTANGLE_WIDTH', message: 'Invalid width' }])

      const shape: any = {
        // Missing id
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 0, // Invalid width
        height: 0, // Invalid height
      }

      const result = businessRules.applyBusinessRules(shape)

      // Should have multiple errors
      expect(result.length).toBeGreaterThan(1)

      // Restore mocks
      idRuleSpy.mockRestore()
      sizeRuleSpy.mockRestore()
    })
  })
})
