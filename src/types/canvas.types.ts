import type React from 'react'
import type Point from '@/types/core/element/geometry/point'
import type { ShapeElement } from '@/types/core/elementDefinitions'

export interface CanvasMouseEvent {
  originalEvent: React.MouseEvent<SVGElement> | React.DragEvent<SVGElement> | MouseEvent
  worldPosition: Point
  svgPosition: Point
  d3Data?: ShapeElement | null
  isInsideCanvas?: boolean // 标记鼠标是否在Canvas区域内
}

export type CanvasDimensionChangeCallback = (formattedWidth: string, formattedHeight: string) => void
