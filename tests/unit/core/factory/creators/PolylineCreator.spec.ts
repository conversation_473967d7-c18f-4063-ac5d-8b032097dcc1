import type { CreatePolylineParams } from '@/core/factory/ElementFactory'
import { afterEach, describe, expect, it, vi } from 'vitest'
import { PolylineCreator } from '@/core/factory/creators/PolylineCreator'
import { Point } from '@/types/core/element/geometry/point'
import { ElementType } from '@/types/core/shape-type'

// Mock the Polyline class constructor
const MockPolyline = vi.fn().mockImplementation((id, properties, options) => ({
  id,
  properties,
  options,
  toJson: () => ({
    id,
    type: properties.type,
    properties,
    options,
  }),
}))
vi.mock('@/types/core/element/path/polyline', () => ({
  Polyline: MockPolyline,
}))

// Mock ensurePointInstance
const mockEnsurePointInstance = vi.fn((p) => {
  if (p instanceof Point)
    return p
  if (typeof p === 'object' && p !== null && typeof p.x === 'number' && typeof p.y === 'number') {
    return new Point(p.x, p.y)
  }
  throw new Error('Invalid point format')
})
vi.mock('@/core/utils/geometryUtils', () => ({
  ensurePointInstance: mockEnsurePointInstance,
}))

// Mock PointUtils.center as it's used internally by Polyline constructor (even if we don't pass position)
// Although the constructor calculates it internally, we might mock it if it causes issues
// For now, let's assume the constructor mock handles it implicitly or doesn't rely on the real PointUtils.

describe('polylineCreator', () => {
  const creator = new PolylineCreator()
  const defaultId = 'polyline-123'
  const point1 = { x: 10, y: 20 }
  const point2 = { x: 110, y: 20 }
  const point3 = { x: 110, y: 120 }

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should create a Polyline instance using create method with valid params', async () => {
    const params: CreatePolylineParams = {
      type: ElementType.POLYLINE,
      id: defaultId,
      points: [point1, point2, point3],
      strokeWidth: 2,
      stroke: '#00ff00',
      curved: true,
      tension: 0.7,
      layer: 'poly-layer',
    }

    await creator.create(params)

    expect(MockPolyline).toHaveBeenCalledTimes(1)
    expect(MockPolyline).toHaveBeenCalledWith(
      defaultId,
      expect.objectContaining({
        type: ElementType.POLYLINE,
        points: [point1, point2, point3], // Constructor expects {x,y}
        curved: true,
        tension: 0.7,
        stroke: '#00ff00',
        strokeWidth: 2,
      }),
      expect.objectContaining({
        layer: 'poly-layer',
      }),
    )
  })

  it('should create a Polyline instance using Point instances in params', async () => {
    const pointInstance1 = new Point(10, 20)
    const pointInstance2 = new Point(110, 20)
    const params: CreatePolylineParams = {
      type: ElementType.POLYLINE,
      id: defaultId,
      points: [pointInstance1, pointInstance2],
    }

    await creator.create(params)

    expect(mockEnsurePointInstance).toHaveBeenCalledTimes(2)
    expect(mockEnsurePointInstance).toHaveBeenCalledWith(pointInstance1)
    expect(mockEnsurePointInstance).toHaveBeenCalledWith(pointInstance2)
    expect(MockPolyline).toHaveBeenCalledWith(
      defaultId,
      expect.objectContaining({
        points: [point1, point2], // Creator converts to {x,y}
      }),
      expect.objectContaining({}),
    )
  })

  it('should throw error if create method receives incorrect shape type', async () => {
    const params = {
      type: ElementType.RECTANGLE, // Incorrect type
      id: defaultId,
      points: [point1, point2],
    } as any

    await expect(creator.create(params)).rejects.toThrow('PolylineCreator received incorrect shape type: rectangle')
    expect(MockPolyline).not.toHaveBeenCalled()
  })

  it('should throw error if create method receives less than two points', async () => {
    const params: CreatePolylineParams = {
      type: ElementType.POLYLINE,
      id: defaultId,
      points: [point1],
    }

    await expect(creator.create(params)).rejects.toThrow('Cannot create Polyline: Requires at least two points.')
    expect(MockPolyline).not.toHaveBeenCalled()
  })

  it('should throw error if create method receives invalid points data', async () => {
    const params: CreatePolylineParams = {
      type: ElementType.POLYLINE,
      id: defaultId,
      points: [point1, 'invalid' as any],
    }
    mockEnsurePointInstance.mockImplementationOnce(p => new Point(p.x, p.y)) // Mock first success
    mockEnsurePointInstance.mockImplementationOnce(() => { throw new Error('Bad point') }) // Mock second failure

    await expect(creator.create(params)).rejects.toThrow('Polyline creation requires valid points: Bad point')
    expect(MockPolyline).not.toHaveBeenCalled()
  })

  it('should create a default Polyline instance using createDefault method', async () => {
    const position = new Point(100, 150)
    await creator.createDefault(defaultId, position)

    expect(MockPolyline).toHaveBeenCalledTimes(1)
    expect(MockPolyline).toHaveBeenCalledWith(
      defaultId,
      expect.objectContaining({
        type: ElementType.POLYLINE,
        points: [{ x: 50, y: 150 }, { x: 150, y: 150 }], // Default points as {x,y}
        stroke: '#000000',
        strokeWidth: 1,
      }),
      expect.objectContaining({}),
    )
  })
})
