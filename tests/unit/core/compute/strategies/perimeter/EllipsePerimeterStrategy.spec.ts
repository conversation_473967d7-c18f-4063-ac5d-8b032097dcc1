import { beforeEach, describe, expect, it } from 'vitest'
import { EllipsePerimeterStrategy } from '@/core/compute/strategies/perimeter/EllipsePerimeterStrategy'
import { Point } from '@/types/core/element/geometry/point'
import { Ellipse } from '@/types/core/element/shape/ellipseShapeTypes'

// Mock Ellipse class
class MockEllipse {
  private radiusX: number
  private radiusY: number

  constructor(radiusX: number, radiusY: number) {
    this.radiusX = radiusX
    this.radiusY = radiusY
  }

  getSubType() {
    return 'ellipse'
  }

  getRadiusX() {
    return this.radiusX
  }

  getRadiusY() {
    return this.radiusY
  }
}

// Mock non-ellipse element
class MockCircle {
  getSubType() {
    return 'circle'
  }
}

describe('ellipsePerimeterStrategy', () => {
  let strategy: EllipsePerimeterStrategy

  beforeEach(() => {
    strategy = new EllipsePerimeterStrategy()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('ellipse')
  })

  it('should calculate perimeter correctly', () => {
    const radiusX = 5
    const radiusY = 3
    const ellipse = new MockEllipse(radiusX, radiusY)

    // Using <PERSON><PERSON><PERSON>'s formula for approximation
    const a = radiusX
    const b = radiusY
    const h = ((a - b) / (a + b)) ** 2
    const expectedPerimeter = Math.PI * (a + b) * (1 + (3 * h) / (10 + Math.sqrt(4 - 3 * h)))

    const perimeter = strategy.calculatePerimeter(ellipse as any)

    expect(perimeter).toBe(expectedPerimeter)
  })

  it('should calculate perimeter correctly for circle case (radiusX = radiusY)', () => {
    const radius = 5
    const ellipse = new MockEllipse(radius, radius)

    // For a circle, perimeter = 2πr
    const expectedPerimeter = 2 * Math.PI * radius

    const perimeter = strategy.calculatePerimeter(ellipse as any)

    // Should be very close to 2πr
    expect(Math.abs(perimeter - expectedPerimeter)).toBeLessThan(0.0001)
  })

  it('should throw error if element is not an ellipse', () => {
    const circle = new MockCircle()

    expect(() => strategy.calculatePerimeter(circle as any)).toThrow('Expected ellipse')
  })

  it('should handle real Ellipse instance', () => {
    // Create a mock that looks like a real Ellipse instance
    const realEllipse = {
      id: 'real-ellipse-id',
      constructor: { name: 'Ellipse' },
      getPosition: () => new Point(100, 100),
      getRadiusX: () => 50,
      getRadiusY: () => 30,
      getSubType: () => 'ellipse',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realEllipse, Ellipse.prototype)

    const perimeter = strategy.calculatePerimeter(realEllipse as any)

    // Calculate expected perimeter using the same formula
    const radiusX = 50
    const radiusY = 30
    const a = Math.max(radiusX, radiusY)
    const b = Math.min(radiusX, radiusY)
    const h = ((a - b) / (a + b)) ** 2
    const expectedPerimeter = Math.PI * (a + b) * (1 + (3 * h) / (10 + Math.sqrt(4 - 3 * h)))

    expect(perimeter).toBeCloseTo(expectedPerimeter)
  })

  it('should handle invalid radiusX in real Ellipse instance', () => {
    // Create a mock that looks like a real Ellipse instance with invalid radiusX
    const realEllipse = {
      id: 'real-ellipse-id',
      constructor: { name: 'Ellipse' },
      getPosition: () => new Point(100, 100),
      getRadiusX: () => Number.NaN,
      getRadiusY: () => 30,
      getSubType: () => 'ellipse',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realEllipse, Ellipse.prototype)

    const perimeter = strategy.calculatePerimeter(realEllipse as any)
    expect(isNaN(perimeter)).toBe(true)
  })

  it('should handle invalid radiusY in real Ellipse instance', () => {
    // Create a mock that looks like a real Ellipse instance with invalid radiusY
    const realEllipse = {
      id: 'real-ellipse-id',
      constructor: { name: 'Ellipse' },
      getPosition: () => new Point(100, 100),
      getRadiusX: () => 50,
      getRadiusY: () => Number.NaN,
      getSubType: () => 'ellipse',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realEllipse, Ellipse.prototype)

    const perimeter = strategy.calculatePerimeter(realEllipse as any)
    expect(isNaN(perimeter)).toBe(true)
  })

  it('should handle error when accessing methods on Ellipse', () => {
    // Create a mock that looks like a real Ellipse instance but throws when getRadiusX is called
    const realEllipse = {
      id: 'real-ellipse-id',
      constructor: { name: 'Ellipse' },
      getPosition: () => new Point(100, 100),
      getRadiusX: () => { throw new Error('Test error') },
      getRadiusY: () => 30,
      getSubType: () => 'ellipse',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realEllipse, Ellipse.prototype)

    // Wrap in try-catch to handle the error
    try {
      const perimeter = strategy.calculatePerimeter(realEllipse as any)
      expect(isNaN(perimeter)).toBe(true)
    }
    catch (error) {
      // If it throws, that's also acceptable
      expect(error).toBeDefined()
    }
  })
})
