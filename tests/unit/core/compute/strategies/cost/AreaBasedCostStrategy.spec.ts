import { describe, expect, it, vi } from 'vitest'
import { AreaBasedCostStrategy } from '@/core/compute/strategies/cost/AreaBasedCostStrategy'
import { CoreError } from '@/services/errors'

describe('areaBasedCostStrategy', () => {
  const strategy = new AreaBasedCostStrategy()

  it('should return the correct shape type', () => {
    expect(strategy.getElementType()).toBe('all')
  })

  it('should calculate cost based on area', () => {
    const mockElement = {
      id: 'element-1',
      type: 'shape',
      compute: {
        area: vi.fn().mockReturnValue(20), // 20 square meters
      },
    }

    const unitCost = 50 // 50 per square meter
    const result = strategy.calculateCost(mockElement, unitCost, {
      additionalCost: 100,
      discountRate: 0.1, // 折扣率应该是小数，不是百分比
      taxRate: 0.05, // 税率应该是小数，不是百分比
    })

    // 基本成本：20 * 50 = 1000
    // 额外成本：100
    // 折扣：(1000 + 100) * 0.1 = 110
    // 税前：(1000 + 100) - 110 = 990
    // 税后：990 * 1.05 = 1039.5
    expect(result).toBeCloseTo(1039.5)
  })

  it('should calculate cost with default options', () => {
    const mockElement = {
      id: 'element-1',
      type: 'shape',
      compute: {
        area: vi.fn().mockReturnValue(20), // 20 square meters
      },
    }

    const unitCost = 50 // 50 per square meter
    const result = strategy.calculateCost(mockElement, unitCost)

    // 基本成本：20 * 50 = 1000
    // 无额外成本、折扣或税率
    expect(result).toBe(1000)
  })

  it('should throw error for invalid element type', () => {
    const mockElement = {
      id: 'invalid-1',
      type: 'invalid',
    }

    expect(() => strategy.calculateCost(mockElement, 50)).toThrow(CoreError)
    expect(() => strategy.calculateCost(mockElement, 50)).toThrow('AreaBasedCostStrategy can only calculate cost for shape elements')
  })

  it('should throw error if element has no area calculation method', () => {
    const mockElement = {
      id: 'element-1',
      type: 'shape',
      compute: {},
    }

    expect(() => strategy.calculateCost(mockElement, 50)).toThrow(CoreError)
    expect(() => strategy.calculateCost(mockElement, 50)).toThrow('Failed to calculate area for element')
  })

  it('should throw error if area calculation returns invalid value', () => {
    const mockElement = {
      id: 'element-1',
      type: 'shape',
      compute: {
        area: vi.fn().mockReturnValue(-10), // Negative area
      },
    }

    expect(() => strategy.calculateCost(mockElement, 50)).toThrow(CoreError)
    expect(() => strategy.calculateCost(mockElement, 50)).toThrow('Invalid area value')
  })
})
