/**
 * Utility Functions for Bezier Curve Calculations
 *
 * @remarks
 * This module provides a collection of utility functions for working with both
 * quadratic and cubic Bezier curves. These utilities include:
 * - Sampling points along a Bezier curve.
 * - Calculating the approximate length of a Bezier curve using either point sampling
 *   or Gaussian quadrature for higher accuracy.
 * - Calculating the bounding box of a Bezier curve, with options for accuracy
 *   (sampling vs. finding mathematical extrema).
 *
 * Functions typically take control points (as {@link IPoint} objects) and other
 * relevant parameters (like number of segments for sampling) as input.
 *
 * @module lib/utils/geometry/bezierUtils
 * @see {@link IPoint}
 * @see {@link BoundingBoxClass}
 */

import type { PointData as IPoint } from '../../../types/core/element/geometry/point' // Use PointData and alias
// Removed unused import for PointClass
import { BoundingBoxClass } from './BoundingBoxClass' // Import BoundingBoxClass

/**
 * Samples a specified number of points along a cubic Bezier curve.
 *
 * @param p0 - The start {@link IPoint} of the curve.
 * @param p1 - The first control {@link IPoint}.
 * @param p2 - The second control {@link IPoint}.
 * @param p3 - The end {@link IPoint} of the curve.
 * @param numSegments - The number of segments to divide the curve into for sampling.
 *                      More segments result in a denser sampling and higher accuracy for
 *                      operations like length approximation or drawing. If `numSegments` is 0 or less,
 *                      only the start and end points are returned.
 * @returns An array of {@link IPoint} objects representing the sampled points along the curve,
 *          including the start and end points.
 */
export function sampleCubicBezier(
  p0: IPoint,
  p1: IPoint,
  p2: IPoint,
  p3: IPoint,
  numSegments: number,
): IPoint[] {
  const points: IPoint[] = []
  if (numSegments <= 0) {
    // Return start and end points if no segments
    points.push({ x: p0.x, y: p0.y, z: p0.z })
    points.push({ x: p3.x, y: p3.y, z: p3.z })
    return points
  }

  for (let i = 0; i <= numSegments; i++) {
    const t = i / numSegments
    const mt = 1 - t
    const mt2 = mt * mt
    const mt3 = mt2 * mt
    const t2 = t * t
    const t3 = t2 * t

    const x = mt3 * p0.x + 3 * mt2 * t * p1.x + 3 * mt * t2 * p2.x + t3 * p3.x
    const y = mt3 * p0.y + 3 * mt2 * t * p1.y + 3 * mt * t2 * p2.y + t3 * p3.y
    // For z, we can interpolate if all points have z, otherwise ignore or handle as needed
    let z: number | undefined
    if (p0.z !== undefined && p1.z !== undefined && p2.z !== undefined && p3.z !== undefined) {
      z = mt3 * p0.z + 3 * mt2 * t * p1.z + 3 * mt * t2 * p2.z + t3 * p3.z
    }
    else if (p0.z !== undefined) { // Fallback if only start point has z
      z = p0.z
    }
    points.push({ x, y, z })
  }
  return points
}

/**
 * Samples a specified number of points along a quadratic Bezier curve.
 *
 * @param p0 - The start {@link IPoint} of the curve.
 * @param p1 - The control {@link IPoint}.
 * @param p2 - The end {@link IPoint} of the curve.
 * @param numSegments - The number of segments to divide the curve into for sampling.
 *                      If `numSegments` is 0 or less, only the start and end points are returned.
 * @returns An array of {@link IPoint} objects representing the sampled points along the curve,
 *          including the start and end points.
 */
export function sampleQuadraticBezier(
  p0: IPoint,
  p1: IPoint,
  p2: IPoint,
  numSegments: number,
): IPoint[] {
  const points: IPoint[] = []
  if (numSegments <= 0) {
    points.push({ x: p0.x, y: p0.y, z: p0.z })
    points.push({ x: p2.x, y: p2.y, z: p2.z })
    return points
  }

  for (let i = 0; i <= numSegments; i++) {
    const t = i / numSegments
    const mt = 1 - t
    const mt2 = mt * mt
    const t2 = t * t

    const x = mt2 * p0.x + 2 * mt * t * p1.x + t2 * p2.x
    const y = mt2 * p0.y + 2 * mt * t * p1.y + t2 * p2.y
    let z: number | undefined
    if (p0.z !== undefined && p1.z !== undefined && p2.z !== undefined) {
      z = mt2 * p0.z + 2 * mt * t * p1.z + t2 * p2.z
    }
    else if (p0.z !== undefined) {
      z = p0.z
    }
    points.push({ x, y, z })
  }
  return points
}
/**
 * Calculates the approximate length of a cubic Bezier curve by summing the lengths
 * of line segments between sampled points.
 *
 * @param p0 - The start {@link IPoint}.
 * @param p1 - The first control {@link IPoint}.
 * @param p2 - The second control {@link IPoint}.
 * @param p3 - The end {@link IPoint}.
 * @param numSegments - The number of segments to use for sampling the curve.
 *                      Defaults to 30. Higher values increase accuracy but also computation time.
 * @returns The approximate length of the cubic Bezier curve.
 */
export function calculateCubicBezierLength(p0: IPoint, p1: IPoint, p2: IPoint, p3: IPoint, numSegments: number = 30): number {
  if (numSegments <= 0)
    numSegments = 1
  const points = sampleCubicBezier(p0, p1, p2, p3, numSegments)
  let length = 0
  for (let i = 0; i < points.length - 1; i++) {
    const current = points[i]
    const next = points[i + 1]
    const dx = next.x - current.x
    const dy = next.y - current.y
    const dz = (next.z ?? 0) - (current.z ?? 0)
    length += Math.sqrt(dx * dx + dy * dy + dz * dz)
  }
  return length
}

/**
 * Calculates the approximate length of a cubic Bezier curve using 5-point Gauss-Legendre quadrature.
 *
 * @remarks
 * This method provides a more accurate length approximation for smooth curves compared to
 * simple point sampling, especially with fewer subdivisions. It integrates the arc length
 * formula numerically.
 *
 * @param p0 - The start {@link IPoint}.
 * @param p1 - The first control {@link IPoint}.
 * @param p2 - The second control {@link IPoint}.
 * @param p3 - The end {@link IPoint}.
 * @returns The approximate length of the cubic Bezier curve.
 */
export function calculateCubicBezierLengthGauss(p0: IPoint, p1: IPoint, p2: IPoint, p3: IPoint): number {
  // 5-point Gauss-Legendre quadrature points and weights for interval [-1, 1]
  const gaussPoints = [
    { t: 0.0, w: 0.5688888888888889 },
    { t: -0.5384693101056831, w: 0.4786286704993665 },
    { t: 0.5384693101056831, w: 0.4786286704993665 },
    { t: -0.9061798459386640, w: 0.2369268850561891 },
    { t: 0.9061798459386640, w: 0.2369268850561891 },
  ]

  const dx_dt = (t: number): number => {
    const mt = 1 - t
    return 3 * mt * mt * (p1.x - p0.x) + 6 * mt * t * (p2.x - p1.x) + 3 * t * t * (p3.x - p2.x)
  }

  const dy_dt = (t: number): number => {
    const mt = 1 - t
    return 3 * mt * mt * (p1.y - p0.y) + 6 * mt * t * (p2.y - p1.y) + 3 * t * t * (p3.y - p2.y)
  }

  const dz_dt = (t: number): number => {
    const mt = 1 - t
    const z0 = p0.z ?? 0
    const z1 = p1.z ?? 0
    const z2 = p2.z ?? 0
    const z3 = p3.z ?? 0
    return 3 * mt * mt * (z1 - z0) + 6 * mt * t * (z2 - z1) + 3 * t * t * (z3 - z2)
  }

  let length = 0
  for (const point of gaussPoints) {
    // Transform t from [-1, 1] (Gauss interval) to [0, 1] (Bezier interval)
    const t_bezier = 0.5 * (point.t + 1)
    const dX = dx_dt(t_bezier)
    const dY = dy_dt(t_bezier)
    const dZ = (p0.z !== undefined || p1.z !== undefined || p2.z !== undefined || p3.z !== undefined) ? dz_dt(t_bezier) : 0
    const speed = Math.sqrt(dX * dX + dY * dY + dZ * dZ)
    length += point.w * speed
  }

  // The integral is from 0 to 1, Gauss quadrature is over [-1, 1]
  // So, multiply by (b-a)/2 = (1-0)/2 = 0.5
  return length * 0.5
}

/**
 * Calculates the bounding box of a cubic Bezier curve by sampling points along it.
 *
 * @remarks
 * This method samples points using {@link sampleCubicBezier} and then creates a
 * bounding box that encompasses all sampled points using {@link BoundingBoxClass.fromPointsArray}.
 * The accuracy depends on `numSegments`. For a more precise bounding box, consider
 * {@link calculateCubicBezierBoundingBoxAccurate}.
 *
 * @param p0 - The start {@link IPoint}.
 * @param p1 - The first control {@link IPoint}.
 * @param p2 - The second control {@link IPoint}.
 * @param p3 - The end {@link IPoint}.
 * @param numSegments - The number of segments for sampling. Defaults to 30.
 * @returns A {@link BoundingBoxClass} instance.
 */
export function calculateCubicBezierBoundingBox(p0: IPoint, p1: IPoint, p2: IPoint, p3: IPoint, numSegments: number = 30): BoundingBoxClass {
  if (numSegments <= 0)
    numSegments = 1
  const points = sampleCubicBezier(p0, p1, p2, p3, numSegments)
  return BoundingBoxClass.fromPointsArray(points)
}

/**
 * Calculates the bounding box of a quadratic Bezier curve by sampling points along it.
 *
 * @remarks
 * This method samples points using {@link sampleQuadraticBezier} and then creates a
 * bounding box that encompasses all sampled points using {@link BoundingBoxClass.fromPointsArray}.
 * The accuracy depends on `numSegments`. For a more precise bounding box, consider
 * {@link calculateQuadraticBezierBoundingBoxAccurate}.
 *
 * @param p0 - The start {@link IPoint}.
 * @param p1 - The control {@link IPoint}.
 * @param p2 - The end {@link IPoint}.
 * @param numSegments - The number of segments for sampling. Defaults to 20.
 * @returns A {@link BoundingBoxClass} instance.
 */
export function calculateQuadraticBezierBoundingBox(p0: IPoint, p1: IPoint, p2: IPoint, numSegments: number = 20): BoundingBoxClass {
  if (numSegments <= 0)
    numSegments = 1
  const points = sampleQuadraticBezier(p0, p1, p2, numSegments)
  return BoundingBoxClass.fromPointsArray(points)
}

// --- More Accurate Bounding Box Calculation using Extrema ---

/**
 * Solves a quadratic equation ax^2 + bx + c = 0 for its real roots.
 * Used internally to find t-values where the derivative of a Bezier curve component is zero.
 * @param a - Coefficient of t^2.
 * @param b - Coefficient of t.
 * @param c - Constant term.
 * @returns An array of real roots. Empty if no real roots or if 'a' and 'b' are near zero.
 * @private
 */
function solveQuadraticForExtrema(a: number, b: number, c: number): number[] {
  if (Math.abs(a) < 1e-12) { // Linear equation
    if (Math.abs(b) < 1e-12)
      return [] // No solution or infinite if c is also 0 (constant, no extremum from derivative)
    return [-c / b]
  }
  const discriminant = b * b - 4 * a * c
  if (discriminant < -1e-12)
    return [] // No real roots
  if (Math.abs(discriminant) < 1e-12)
    return [-b / (2 * a)] // One real root

  const sqrtD = Math.sqrt(Math.max(0, discriminant)) // Ensure non-negative for sqrt
  return [(-b + sqrtD) / (2 * a), (-b - sqrtD) / (2 * a)]
}

/**
 * Solves for t where the derivative of one coordinate component of a cubic Bezier is 0.
 * B'(t)/3 = t^2(p0 - 3p1 + 3p2 - p3) + t(2p0 - 4p1 + 2p2) + (p1 - p0) -- this is for B(t) itself, not B'(t)
 * Correct derivative coefficients for B'(t)/3 = At^2 + Bt + C = 0 are:
 * A = (p3 - p0) + 3 * (p1 - p2)  OR A = p0 - 3*p1 + 3*p2 - p3 (using a slightly different formulation for coefficients of t^2, t, const)
 * Let's use the form: a*t^2 + b*t + c = 0 where
 * a = 3 * (-p0 + 3*p1 - 3*p2 + p3)
 * b = 6 * (p0 - 2*p1 + p2)
 * c = 3 * (p1 - p0)
 * We need to solve for t where derivative is 0.
 * B'(t) = 3( (P1-P0)(1-t)^2 + 2(P2-P1)t(1-t) + (P3-P2)t^2 )
 * Let dp0 = P1-P0, dp1 = P2-P1, dp2 = P3-P2
 * B'(t)/3 = dp0(1-2t+t^2) + 2dp1(t-t^2) + dp2(t^2)
 *         = dp0 - 2tdp0 + t^2dp0 + 2tdp1 - 2t^2dp1 + t^2dp2
 *         = t^2(dp0 - 2dp1 + dp2) + t(-2dp0 + 2dp1) + dp0
 * So, the quadratic equation is At^2 + Bt + C = 0, where:
 * A = (dp0 - 2*dp1 + dp2)
 * B = 2 * (dp1 - dp0)  -- Correction: Should be B = 2 * (-dp0 + dp1) or 2 * (dp1 - dp0)
 * C = dp0
 * This function finds the t values (roots of the derivative) for a single coordinate (x, y, or z).
 * @private
 */
function solveCubicCoordinateExtremum(p0_coord: number, p1_coord: number, p2_coord: number, p3_coord: number): number[] {
  const A = (p0_coord - 2 * p1_coord + p2_coord) - (p1_coord - 2 * p2_coord + p3_coord) // (dp0 - dp1) - (dp1 - dp2) = dp0 - 2*dp1 + dp2
  const B = 2 * ((p1_coord - p0_coord) - (p2_coord - p1_coord)) // 2 * (dp0 - dp1)
  const C = (p1_coord - p0_coord) // dp0

  return solveQuadraticForExtrema(A, B, C)
}

/**
 * Evaluates a point on a cubic Bezier curve at a given parameter t.
 * @param p0 - Start point.
 * @param p1 - First control point.
 * @param p2 - Second control point.
 * @param p3 - End point.
 * @param t - Parameter value (usually between 0 and 1).
 * @returns The {@link IPoint} on the curve at parameter t.
 * @private
 */
function evaluateCubicBezierPoint(p0: IPoint, p1: IPoint, p2: IPoint, p3: IPoint, t: number): IPoint {
  const mt = 1 - t
  const mt2 = mt * mt
  const mt3 = mt2 * mt
  const t2 = t * t
  const t3 = t2 * t

  const x = mt3 * p0.x + 3 * mt2 * t * p1.x + 3 * mt * t2 * p2.x + t3 * p3.x
  const y = mt3 * p0.y + 3 * mt2 * t * p1.y + 3 * mt * t2 * p2.y + t3 * p3.y
  let z: number | undefined
  if (p0.z !== undefined && p1.z !== undefined && p2.z !== undefined && p3.z !== undefined) {
    z = mt3 * p0.z + 3 * mt2 * t * p1.z + 3 * mt * t2 * p2.z + t3 * p3.z
  }
  else if (p0.z !== undefined) {
    z = p0.z
  }
  else if (p1.z !== undefined) {
    z = p1.z
  }
  else if (p2.z !== undefined) {
    z = p2.z
  }
  else if (p3.z !== undefined) {
    z = p3.z
  }
  return { x, y, z }
}

/**
 * Calculates a more accurate bounding box of a cubic Bezier curve by finding mathematical extrema.
 *
 * @remarks
 * This method finds the t-values where the derivative of each coordinate (x, y, and z if present)
 * is zero. These t-values correspond to potential extrema (maximum or minimum) of the curve
 * in that dimension. The points on the curve at these t-values (if within the 0-1 range),
 * along with the start and end points of the curve, are used to determine the bounding box.
 * This is generally more accurate than sampling, especially for curves with sharp turns.
 *
 * @param p0 - The start {@link IPoint}.
 * @param p1 - The first control {@link IPoint}.
 * @param p2 - The second control {@link IPoint}.
 * @param p3 - The end {@link IPoint}.
 * @returns A {@link BoundingBoxClass} instance.
 */
export function calculateCubicBezierBoundingBoxAccurate(p0: IPoint, p1: IPoint, p2: IPoint, p3: IPoint): BoundingBoxClass {
  const pointsToCheck: IPoint[] = [p0, p3]

  const txRoots = solveCubicCoordinateExtremum(p0.x, p1.x, p2.x, p3.x)
  for (const t of txRoots) {
    if (t > 0 && t < 1) {
      pointsToCheck.push(evaluateCubicBezierPoint(p0, p1, p2, p3, t))
    }
  }

  const tyRoots = solveCubicCoordinateExtremum(p0.y, p1.y, p2.y, p3.y)
  for (const t of tyRoots) {
    if (t > 0 && t < 1) {
      pointsToCheck.push(evaluateCubicBezierPoint(p0, p1, p2, p3, t))
    }
  }

  if (p0.z !== undefined && p1.z !== undefined && p2.z !== undefined && p3.z !== undefined) {
    const tzRoots = solveCubicCoordinateExtremum(p0.z, p1.z, p2.z, p3.z)
    for (const t of tzRoots) {
      if (t > 0 && t < 1) {
        pointsToCheck.push(evaluateCubicBezierPoint(p0, p1, p2, p3, t))
      }
    }
  }
  return BoundingBoxClass.fromPointsArray(pointsToCheck)
}

/**
 * Solves for the parameter `t` where the derivative of a single coordinate component
 * of a quadratic Bezier curve is zero.
 * The derivative B'(t) = 2(1-t)(P1-P0) + 2t(P2-P1). Setting to 0 gives:
 * t = (P0-P1) / (P0 - 2P1 + P2).
 * @param p0_coord - Coordinate value of the start point.
 * @param p1_coord - Coordinate value of the control point.
 * @param p2_coord - Coordinate value of the end point.
 * @returns An array containing the t-value (if an extremum exists within the curve's domain),
 *          or an empty array if the derivative is constant (denominator is zero).
 * @private
 */
function solveQuadraticCoordinateExtremum(p0_coord: number, p1_coord: number, p2_coord: number): number[] {
  const denominator = p0_coord - 2 * p1_coord + p2_coord
  if (Math.abs(denominator) < 1e-12) { // Derivative is constant, no extremum unless it's flat
    return []
  }
  const t = (p0_coord - p1_coord) / denominator
  return [t]
}

/**
 * Evaluates a point on a quadratic Bezier curve at a given parameter t.
 * @param p0 - Start point.
 * @param p1 - Control point.
 * @param p2 - End point.
 * @param t - Parameter value (usually between 0 and 1).
 * @returns The {@link IPoint} on the curve at parameter t.
 * @private
 */
function evaluateQuadraticBezierPoint(p0: IPoint, p1: IPoint, p2: IPoint, t: number): IPoint {
  const mt = 1 - t
  const mt2 = mt * mt
  const t2 = t * t

  const x = mt2 * p0.x + 2 * mt * t * p1.x + t2 * p2.x
  const y = mt2 * p0.y + 2 * mt * t * p1.y + t2 * p2.y
  let z: number | undefined
  if (p0.z !== undefined && p1.z !== undefined && p2.z !== undefined) {
    z = mt2 * p0.z + 2 * mt * t * p1.z + t2 * p2.z
  }
  else if (p0.z !== undefined) {
    z = p0.z
  }
  else if (p1.z !== undefined) {
    z = p1.z
  }
  else if (p2.z !== undefined) {
    z = p2.z
  }
  return { x, y, z }
}

/**
 * Calculates a more accurate bounding box of a quadratic Bezier curve by finding mathematical extrema.
 *
 * @remarks
 * This method finds the t-values where the derivative of each coordinate (x, y, and z if present)
 * is zero. These t-values correspond to potential extrema. The points on the curve at these
 * t-values (if within the 0-1 range), along with the start and end points, determine the bounding box.
 *
 * @param p0 - The start {@link IPoint}.
 * @param p1 - The control {@link IPoint}.
 * @param p2 - The end {@link IPoint}.
 * @returns A {@link BoundingBoxClass} instance.
 */
export function calculateQuadraticBezierBoundingBoxAccurate(p0: IPoint, p1: IPoint, p2: IPoint): BoundingBoxClass {
  const pointsToCheck: IPoint[] = [p0, p2]

  const txRoots = solveQuadraticCoordinateExtremum(p0.x, p1.x, p2.x)
  for (const t of txRoots) {
    if (t > 0 && t < 1) {
      pointsToCheck.push(evaluateQuadraticBezierPoint(p0, p1, p2, t))
    }
  }

  const tyRoots = solveQuadraticCoordinateExtremum(p0.y, p1.y, p2.y)
  for (const t of tyRoots) {
    if (t > 0 && t < 1) {
      pointsToCheck.push(evaluateQuadraticBezierPoint(p0, p1, p2, t))
    }
  }

  if (p0.z !== undefined && p1.z !== undefined && p2.z !== undefined) {
    const tzRoots = solveQuadraticCoordinateExtremum(p0.z, p1.z, p2.z)
    for (const t of tzRoots) {
      if (t > 0 && t < 1) {
        pointsToCheck.push(evaluateQuadraticBezierPoint(p0, p1, p2, t))
      }
    }
  }
  return BoundingBoxClass.fromPointsArray(pointsToCheck)
}

/**
 * Calculates the approximate length of a quadratic Bezier curve using 5-point Gauss-Legendre quadrature.
 *
 * @remarks
 * This method provides a more accurate length approximation for smooth curves compared to
 * simple point sampling. It integrates the arc length formula numerically.
 *
 * @param p0 - The start {@link IPoint}.
 * @param p1 - The control {@link IPoint}.
 * @param p2 - The end {@link IPoint}.
 * @returns The approximate length of the quadratic Bezier curve.
 */
export function calculateQuadraticBezierLengthGauss(p0: IPoint, p1: IPoint, p2: IPoint): number {
  const gaussPoints = [
    { t: 0.0, w: 0.5688888888888889 },
    { t: -0.5384693101056831, w: 0.4786286704993665 },
    { t: 0.5384693101056831, w: 0.4786286704993665 },
    { t: -0.9061798459386640, w: 0.2369268850561891 },
    { t: 0.9061798459386640, w: 0.2369268850561891 },
  ]

  // Derivative of B(t) = (1-t)^2 P0 + 2t(1-t)P1 + t^2 P2 is B'(t) = 2(1-t)(P1-P0) + 2t(P2-P1)
  const dx_dt = (t: number): number => 2 * (1 - t) * (p1.x - p0.x) + 2 * t * (p2.x - p1.x)
  const dy_dt = (t: number): number => 2 * (1 - t) * (p1.y - p0.y) + 2 * t * (p2.y - p1.y)
  const dz_dt = (t: number): number => {
    const z0 = p0.z ?? 0
    const z1 = p1.z ?? 0
    const z2 = p2.z ?? 0
    return 2 * (1 - t) * (z1 - z0) + 2 * t * (z2 - z1)
  }

  let length = 0
  for (const point of gaussPoints) {
    const t_bezier = 0.5 * (point.t + 1)
    const dX = dx_dt(t_bezier)
    const dY = dy_dt(t_bezier)
    const dZ = (p0.z !== undefined || p1.z !== undefined || p2.z !== undefined) ? dz_dt(t_bezier) : 0
    const speed = Math.sqrt(dX * dX + dY * dY + dZ * dZ)
    length += point.w * speed
  }

  return length * 0.5
}

/**
 * Calculates the approximate length of a quadratic Bezier curve by summing the lengths
 * of line segments between sampled points.
 *
 * @param p0 - The start {@link IPoint}.
 * @param p1 - The control {@link IPoint}.
 * @param p2 - The end {@link IPoint}.
 * @param numSegments - The number of segments to use for sampling the curve.
 *                      Defaults to 20. Higher values increase accuracy.
 * @returns The approximate length of the quadratic Bezier curve.
 */
export function calculateQuadraticBezierLength(p0: IPoint, p1: IPoint, p2: IPoint, numSegments: number = 20): number {
  if (numSegments <= 0)
    numSegments = 1
  const points = sampleQuadraticBezier(p0, p1, p2, numSegments)
  let length = 0
  for (let i = 0; i < points.length - 1; i++) {
    const current = points[i]
    const next = points[i + 1]
    const dx = next.x - current.x
    const dy = next.y - current.y
    const dz = (next.z ?? 0) - (current.z ?? 0)
    length += Math.sqrt(dx * dx + dy * dy + dz * dz)
  }
  return length
}
