import type { AreaCalculatorStrategy } from '@/types/core/compute/areaComputeTypes'
import type { Element } from '@/types/core/elementDefinitions'
import { describe, expect, it } from 'vitest'
import { ElementType } from '@/types/core/elementDefinitions'

describe('area Compute Types Module', () => {
  describe('areaCalculatorStrategy Interface', () => {
    it('should allow implementation of area calculator strategy', () => {
      // Create a mock implementation of AreaCalculatorStrategy
      const rectangleAreaCalculator: AreaCalculatorStrategy = {
        calculateArea: (element: Element): number => {
          // Simple mock implementation for testing
          if (element.type === ElementType.RECTANGLE) {
            // Assume element has width and height properties
            const rectangle = element as unknown as { width: number, height: number }
            return rectangle.width * rectangle.height
          }
          return 0
        },

        getElementType: (): string => {
          return ElementType.RECTANGLE
        },
      }

      // Create a test rectangle element
      const rectangleElement: Element & { width: number, height: number } = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        visible: true,
        locked: false,
        width: 10,
        height: 5,
      }

      // Test the area calculation
      expect(rectangleAreaCalculator.calculateArea(rectangleElement)).toBe(50)
      expect(rectangleAreaCalculator.getElementType()).toBe(ElementType.RECTANGLE)
    })

    it('should allow implementation for multiple element types', () => {
      // Create a mock implementation that handles multiple element types
      const shapeAreaCalculator: AreaCalculatorStrategy = {
        calculateArea: (element: Element): number => {
          // Simple mock implementation for testing
          if (element.type === ElementType.RECTANGLE) {
            const rectangle = element as unknown as { width: number, height: number }
            return rectangle.width * rectangle.height
          }
          else if (element.type === ElementType.CIRCLE) {
            const circle = element as unknown as { radius: number }
            return Math.PI * circle.radius * circle.radius
          }
          return 0
        },

        getElementType: (): string => {
          // In a real implementation, this might return an array of supported types
          return 'SHAPE' // Generic type representing multiple shapes
        },
      }

      // Create test elements
      const rectangleElement: Element & { width: number, height: number } = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        visible: true,
        locked: false,
        width: 10,
        height: 5,
      }

      const circleElement: Element & { radius: number } = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        visible: true,
        locked: false,
        radius: 5,
      }

      // Test the area calculations
      expect(shapeAreaCalculator.calculateArea(rectangleElement)).toBe(50)
      expect(shapeAreaCalculator.calculateArea(circleElement)).toBeCloseTo(78.54, 2) // π * 5²
      expect(shapeAreaCalculator.getElementType()).toBe('SHAPE')
    })

    it('should handle elements with no area', () => {
      // Create a mock implementation for elements with no area
      const lineAreaCalculator: AreaCalculatorStrategy = {
        calculateArea: (element: Element): number => {
          // Lines have no area
          return 0
        },

        getElementType: (): string => {
          return ElementType.LINE
        },
      }

      // Create a test line element
      const lineElement: Element = {
        id: 'line-1',
        type: ElementType.LINE,
        visible: true,
        locked: false,
      }

      // Test the area calculation
      expect(lineAreaCalculator.calculateArea(lineElement)).toBe(0)
      expect(lineAreaCalculator.getElementType()).toBe(ElementType.LINE)
    })
  })
})
