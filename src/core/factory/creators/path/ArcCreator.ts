import type { CreateArcParams } from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point'
import type {
  Path,
} from '@/types/core/elementDefinitions'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
/**
 * Creator for Arc Path Elements
 *
 * @remarks
 * This class extends {@link PathCreator} to specialize in creating
 * Arc ({@link CoreElementType.ARC}) path elements. It uses `createCommonProperties`
 * for base attributes and then adds arc-specific properties.
 *
 * @module core/factory/creators/path/ArcCreator
 */
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'
import { PathCreator } from './PathCreator'
// ensurePointInstance might not be needed if position is PointData

export class ArcCreator extends PathCreator<Path.Arc, CreateArcParams> {
  constructor() {
    super(CoreElementType.ARC)
  }

  /**
   * Creates an Arc {@link Path.Arc} element based on the provided parameters.
   *
   * @param params - A {@link CreateArcParams} object
   *                 containing all necessary information for arc creation.
   * @returns A Promise resolving to the created {@link Path.Arc} element.
   */
  public async create(params: CreateArcParams): Promise<Path.Arc> {
    if (params.type !== CoreElementType.ARC && params.type !== 'arc') {
      throw new CoreError(ErrorType.InvalidPayload, `ArcCreator cannot create type: ${String(params.type)}`)
    }

    const { id, position, radius, startAngle, endAngle } = params
    if (id == null || id === '')
      throw new CoreError(ErrorType.InvalidPayload, 'Arc requires an ID.')
    if (position == null)
      throw new CoreError(ErrorType.InvalidPayload, 'Arc requires a position (center).')
    if (typeof radius !== 'number' || radius <= 0)
      throw new CoreError(ErrorType.InvalidPayload, `Arc requires a positive radius. Received: ${radius}`)
    if (typeof startAngle !== 'number' || typeof endAngle !== 'number')
      throw new CoreError(ErrorType.InvalidPayload, 'Arc requires startAngle and endAngle.')

    // PathCreator.createCommonProperties will use params.position if available.
    const commonProps = this.createCommonProperties(id, params)

    // Generate SVG arc path string if not provided
    let pathData = params.properties?.pathData as string | undefined
    if (pathData == null || pathData === '') {
      // Import and use the arc utility function
      try {
        const { generateArcPathData } = await import('../../../../lib/utils/geometry/arcUtils')
        // 使用相对坐标(0,0)作为圆心，因为commonProps.position已经设置为实际圆心位置
        // SVG group会通过transform移动到正确位置
        const relativeCenter = { x: 0, y: 0, z: 0 }
        pathData = generateArcPathData(
          relativeCenter,
          radius,
          startAngle,
          endAngle,
          params.counterClockwise ?? false,
        )
        // console.warn('[ArcCreator] Generated arc path data:', {
        //   relativeCenter, radius, startAngle, endAngle,
        //   counterClockwise: params.counterClockwise ?? false,
        //   pathData, actualPosition: commonProps.position
        // })
      }
      catch (error) {
        console.warn('[ArcCreator] Failed to generate arc path data:', error)
        pathData = undefined
      }
    }

    const arcElement: Path.Arc = {
      ...commonProps,
      // radius, // Moved to properties
      // startAngle, // Moved to properties
      // endAngle, // Moved to properties
      // closed: params.closed ?? false, // Moved to properties
      properties: {
        // 设置默认的成本相关属性
        costUnitPrice: 1,
        costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1
        costBasis: 'unit',
        // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
        computedAreaStatus: 'none',
        computedPerimeterStatus: 'none',
        computedLengthStatus: 'none',
        // 然后合并其他属性
        ...(params.properties || {}),
        // Center (cx, cy) is implicitly commonProps.position, so in renderer, cx,cy are 0,0 relative to group
        radius, // Store original radius, might be used if rx/ry not given
        rx: (params.properties?.rx as number) ?? radius, // Default rx to radius
        ry: (params.properties?.ry as number) ?? radius, // Default ry to radius
        startAngle,
        endAngle,
        counterClockwise: params.counterClockwise ?? false,
        closed: params.closed ?? false,
        pathData: pathData as string, // Use generated or provided path data
      },
    }

    return arcElement
  }

  /**
   * Creates a default Arc element.
   *
   * @param id - The unique identifier for the default arc element.
   * @param position - The {@link PointData} for the default arc's center.
   * @param majorCategoryOverride - Optional override for majorCategory.
   * @param minorCategoryOverride - Optional override for minorCategory.
   * @param zLevelIdOverride - Optional override for zLevelId.
   * @param isFixedCategoryOverride - Optional override for isFixedCategory.
   * @returns A Promise resolving to the created default {@link Path.Arc}.
   */
  public async createDefault(
    id: string,
    position: PointData, // Center of the arc
    majorCategoryOverride?: MajorCategory,
    minorCategoryOverride?: MinorCategory,
    zLevelIdOverride?: string,
    isFixedCategoryOverride?: boolean,
  ): Promise<Path.Arc> {
    // Get default settings for ARC elements
    const { getDefaultSettingsForElementType } = await import('@/config/defaultElementSettings')
    const defaults = getDefaultSettingsForElementType('ARC' as any)

    const paramsForCreate: CreateArcParams = {
      id,
      type: CoreElementType.ARC,
      majorCategory: majorCategoryOverride ?? MajorCategory.FURNITURE, // Default
      minorCategory: minorCategoryOverride,
      zLevelId: zLevelIdOverride,
      isFixedCategory: isFixedCategoryOverride,
      position,
      metadata: { name: 'Arc' },
      stroke: defaults.stroke ?? '#333333',
      strokeWidth: defaults.strokeWidth ?? 2,
      opacity: defaults.opacity ?? 1,
      visible: true,
      locked: false,
      // 添加圆弧的关键参数
      radius: 50,
      startAngle: 0,
      endAngle: 90,
      closed: false,
      counterClockwise: false,
      properties: {
        // 设置默认的成本相关属性
        costUnitPrice: 1,
        costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1
        costBasis: 'unit', // 默认使用单位计算
        // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
        computedAreaStatus: 'none',
        computedPerimeterStatus: 'none',
        computedLengthStatus: 'none',
      },
    }
    return this.create(paramsForCreate)
  }
}
