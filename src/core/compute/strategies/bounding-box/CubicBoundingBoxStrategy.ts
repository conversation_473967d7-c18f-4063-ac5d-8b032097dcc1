/**
 * Bounding Box Calculation Strategy for Cubic Bezier Curve Elements
 *
 * @remarks
 * This strategy implements the {@link BoundingBoxCalculatorStrategy} for calculating
 * the axis-aligned bounding box of Cubic Bezier curve elements ({@link CoreElementType.CUBIC}).
 *
 * The calculation involves:
 * 1. Retrieving the start point, end point, and two control points of the cubic Bezier curve
 *    from the element's properties.
 * 2. Converting these points (which might be relative to the element's position) to
 *    absolute coordinates.
 * 3. Delegating the core geometric calculation to the `calculateCubicBezierBoundingBoxAccurate`
 *    function from `../../../../lib/utils/geometry/bezierUtils`. This utility function
 *    typically finds the extrema of the curve to determine the min/max x and y values.
 *
 * The strategy validates that the element is of type `CUBIC` and that its
 * defining points are valid.
 *
 * @module core/compute/strategies/bounding-box/CubicBoundingBoxStrategy
 * @see {@link Path.Cubic} for the cubic Bezier curve element type definition.
 * @see {@link BoundingBoxInterface} for the bounding box structure.
 */
import type { BoundingBoxCalculatorStrategy } from '../../../../types/core/compute'
import type { BoundingBox as BoundingBoxInterface } from '../../../../types/core/element/geometry/bounding-box'
import type { PointData as IPoint } from '../../../../types/core/element/geometry/point' // Use PointData, alias as IPoint
import type {
  Element,
  Path,
  ShapeElement,
  // MetadataProperties // Not used
} from '../../../../types/core/elementDefinitions'
import { calculateCubicBezierBoundingBoxAccurate } from '../../../../lib/utils/geometry/bezierUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
  // MetadataProperties // Not used
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

// createBoundingBoxShapeElement helper function is removed.
// Private helper methods for extrema calculation are moved to bezierUtils.

export class CubicBoundingBoxStrategy implements BoundingBoxCalculatorStrategy {
  /**
   * Calculates the axis-aligned bounding box of a Cubic Bezier curve element.
   *
   * @param element - The cubic Bezier curve element, expected to be of type {@link ShapeElement}
   *                  and specifically {@link CoreElementType.CUBIC}, conforming to {@link Path.Cubic}.
   * @returns A {@link BoundingBoxInterface} object representing the bounding box.
   * @throws {@link CoreError} if the provided element is not of type `CUBIC`.
   * @throws {@link CoreError} if the curve's control points (`start`, `control1`, `control2`, `end`)
   *         are invalid or not numbers.
   */
  public calculateBoundingBox(element: Element): BoundingBoxInterface {
    if (element.type !== CoreElementType.CUBIC) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `CubicBoundingBoxStrategy can only calculate bounding box for CUBIC elements, got ${element.type}`,
        undefined,
        { component: 'CubicBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    const cubicElement = element as unknown as Path.Cubic // Added unknown for type safety

    // Points are directly on the cubicElement for Path.Cubic
    const p0_rel = cubicElement.properties.start
    const p1_rel = cubicElement.properties.control1
    const p2_rel = cubicElement.properties.control2
    const p3_rel = cubicElement.properties.end

    if (p0_rel == null || p1_rel == null || p2_rel == null || p3_rel == null
      || typeof p0_rel.x !== 'number' || typeof p0_rel.y !== 'number'
      || typeof p1_rel.x !== 'number' || typeof p1_rel.y !== 'number'
      || typeof p2_rel.x !== 'number' || typeof p2_rel.y !== 'number'
      || typeof p3_rel.x !== 'number' || typeof p3_rel.y !== 'number') {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid cubic curve points for element ID: ${element.id}`,
        undefined,
        { component: 'CubicBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id, metadata: { p0_rel, p1_rel, p2_rel, p3_rel } },
      )
    }

    // Convert relative points to absolute for BBox calculation
    const shapeElement = element as unknown as ShapeElement
    const elPosX = shapeElement.position?.x ?? 0
    const elPosY = shapeElement.position?.y ?? 0

    const p0_abs: IPoint = { x: p0_rel.x + elPosX, y: p0_rel.y + elPosY, z: p0_rel.z }
    const p1_abs: IPoint = { x: p1_rel.x + elPosX, y: p1_rel.y + elPosY, z: p1_rel.z }
    const p2_abs: IPoint = { x: p2_rel.x + elPosX, y: p2_rel.y + elPosY, z: p2_rel.z }
    const p3_abs: IPoint = { x: p3_rel.x + elPosX, y: p3_rel.y + elPosY, z: p3_rel.z }

    // Delegate to the utility function which returns a BoundingBoxClass instance
    return calculateCubicBezierBoundingBoxAccurate(p0_abs, p1_abs, p2_abs, p3_abs)
  }

  // Private helper methods (solveCubicExtremum, solveQuadratic, evaluateCubicBezierPoint) are removed
  // as their logic is now in bezierUtils.ts

  /**
   * Returns the element type that this strategy is responsible for.
   * @returns The element type this strategy handles
   */
  public getElementType(): CoreElementType {
    return CoreElementType.CUBIC
  }
}
