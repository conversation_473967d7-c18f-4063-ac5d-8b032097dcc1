/**
 * @file openingValidator.ts
 * @description Provides validators for opening elements such as doors and windows.
 * This file includes an abstract `OpeningValidator` base class and concrete implementations
 * like `DoorValidator` and `WindowValidator`. These classes extend `BaseDesignValidator`
 * and add specific validation logic for properties unique to openings (e.g., opening type, dimensions, wall attachment).
 *
 * @module core/validator/validators/design/openingValidator
 */

import type {
  OpeningProperties,
} from '@/types/core/element/design/openingDesignTypes'
import type { WallProperties } from '@/types/core/element/design/wallDesignTypes'
// Point is used indirectly through isValidPoint
import type {
  ShapeElement as ShapeModel,
} from '@/types/core/elementDefinitions'
import type { ValidatableShape, ValidationError } from '@/types/core/validator/validator-interface'
import {
  OpeningType,
  // DoorProperties and WindowProperties are not explicitly defined as separate interfaces in openingDesignTypes.ts
  // We'll assume they are OpeningProperties plus any specific fields if needed.
} from '@/types/core/element/design/openingDesignTypes'
import {
  ElementType, // For type alias
} from '@/types/core/elementDefinitions'
import { ValidationErrorCode } from '@/types/core/validator/error-codes'
import { isValidPoint } from '../common/validationUtils'
import { BaseDesignValidator } from './designValidatorBase'

/**
 * Concrete type for what an opening element should look like for validation.
 * @private
 */
type ValidatableOpening = ShapeModel & OpeningProperties

/**
 * Concrete type for what a door element should look like for validation.
 * @private
 */
type ValidatableDoor = ShapeModel & OpeningProperties & {
  swingDirection?: string // Example door-specific property
  isExterior?: boolean
  isAutomatic?: boolean
}

/**
 * Concrete type for what a window element should look like for validation.
 * @private
 */
type ValidatableWindow = ShapeModel & OpeningProperties & {
  operationType?: string // Example window-specific property
  sillHeight?: number
  headHeight?: number
}

/**
 * Base validator for openings (doors and windows).
 *
 * @abstract
 * @extends BaseDesignValidator
 */
abstract class OpeningValidator extends BaseDesignValidator {
  // expectedElementType is abstract in BaseDesignValidator and implemented by Door/WindowValidator

  /**
   * Validates specific opening properties common to doors and windows.
   *
   * @param opening - The opening to validate.
   * @param errors - The array to add validation errors to.
   * @protected
   */
  protected validateSpecificOpeningProperties(opening: ValidatableOpening, errors: ValidationError[]): void {
    if (opening.openingType == null || !Object.values(OpeningType).includes(opening.openingType)) {
      errors.push({
        code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
        message: `Opening requires a valid 'openingType'. Received: ${opening.openingType}`,
        path: 'openingType',
        value: opening.openingType,
      })
    }

    if (typeof opening.width !== 'number' || opening.width <= 0 || !Number.isFinite(opening.width)) {
      errors.push({
        code: ValidationErrorCode.INVALID_DIMENSION,
        message: `Opening 'width' must be a positive finite number. Received: ${opening.width}`,
        path: 'width',
        value: opening.width,
      })
    }

    if (typeof opening.height !== 'number' || opening.height <= 0 || !Number.isFinite(opening.height)) {
      errors.push({
        code: ValidationErrorCode.INVALID_DIMENSION,
        message: `Opening 'height' must be a positive finite number. Received: ${opening.height}`,
        path: 'height',
        value: opening.height,
      })
    }

    if (typeof opening.heightFromFloor !== 'number' || !Number.isFinite(opening.heightFromFloor) || opening.heightFromFloor < 0) {
      errors.push({
        code: ValidationErrorCode.INVALID_DIMENSION,
        message: `Opening 'heightFromFloor' must be a non-negative finite number. Received: ${opening.heightFromFloor}`,
        path: 'heightFromFloor',
        value: opening.heightFromFloor,
      })
    }

    if (typeof opening.wallId !== 'string' || opening.wallId.trim() === '') {
      errors.push({
        code: ValidationErrorCode.MISSING_OR_INVALID_ID,
        message: `Opening requires a non-empty string 'wallId'. Received: ${opening.wallId}`,
        path: 'wallId',
        value: opening.wallId,
      })
    }

    if (typeof opening.wallPosition !== 'number' || !Number.isFinite(opening.wallPosition) || opening.wallPosition < 0 || opening.wallPosition > 1) {
      errors.push({
        code: ValidationErrorCode.INVALID_POSITION,
        message: `Opening 'wallPosition' must be a finite number between 0 and 1. Received: ${opening.wallPosition}`,
        path: 'wallPosition',
        value: opening.wallPosition,
      })
    }

    if (opening.referencePoint !== undefined && !isValidPoint(opening.referencePoint)) {
      errors.push({
        code: ValidationErrorCode.INVALID_POINT,
        message: `Opening 'referencePoint' if provided, must be a valid point. Received: ${JSON.stringify(opening.referencePoint)}`,
        path: 'referencePoint',
        value: opening.referencePoint,
      })
    }
  }

  /**
   * Validates the placement of an opening within a wall.
   *
   * @param opening - The opening to validate.
   * @param wall - The wall the opening is placed in.
   * @param errors - The array to add validation errors to.
   * @protected
   */
  protected validateOpeningPlacementRule(opening: ValidatableOpening, wall: ShapeModel & WallProperties, errors: ValidationError[]): void {
    if (opening.wallId !== wall.id) {
      errors.push({
        code: ValidationErrorCode.VALIDATION_RULE_ERROR, // More generic rule error
        message: `Opening (ID: ${opening.id}) is assigned to wall '${opening.wallId}' but is being validated against wall '${wall.id}'.`,
        path: 'wallId',
      })
    }

    // Check if opening fits within wall height
    // Ensure heightFromFloor and height are numbers before arithmetic
    if (typeof opening.heightFromFloor === 'number' && typeof opening.height === 'number' && typeof wall.height === 'number'
      && (opening.heightFromFloor + opening.height > wall.height)) {
      errors.push({
        code: ValidationErrorCode.INVALID_DIMENSION,
        message: `Opening (height: ${opening.height} at ${opening.heightFromFloor} from floor) exceeds wall height (${wall.height}).`,
        path: 'height, heightFromFloor',
      })
    }
    // More complex placement logic (e.g., not overlapping wall ends) would go here.
  }
}

/**
 * Validator for door elements.
 *
 * @extends OpeningValidator
 */
export class DoorValidator extends OpeningValidator {
  /**
   * The design element category this validator is for.
   * @protected
   * @readonly
   */
  protected readonly expectedElementType = ElementType.DOOR

  /**
   * Validates door-specific properties.
   *
   * @param shape - The shape to validate.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected validateSpecific(shape: ValidatableShape): ValidationError[] {
    const errors: ValidationError[] = []
    const baseTypeErrors = this.validateBaseType(shape)
    if (baseTypeErrors.length > 0)
      return baseTypeErrors

    const door = shape as ValidatableDoor
    this.validateSpecificOpeningProperties(door, errors)

    // Door-specific validations
    if (door.heightFromFloor !== 0) {
      errors.push({
        code: ValidationErrorCode.INVALID_POSITION,
        message: 'Door height from floor should typically be 0.',
        path: 'heightFromFloor',
        value: door.heightFromFloor,
      })
    }
    // Add validation for swingDirection, isExterior, isAutomatic if they are defined on ValidatableDoor
    return errors
  }
}

/**
 * Validator for window elements.
 *
 * @extends OpeningValidator
 */
export class WindowValidator extends OpeningValidator {
  /**
   * The design element category this validator is for.
   * @protected
   * @readonly
   */
  protected readonly expectedElementType = ElementType.WINDOW

  /**
   * Validates window-specific properties.
   *
   * @param shape - The shape to validate.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected validateSpecific(shape: ValidatableShape): ValidationError[] {
    const errors: ValidationError[] = []
    const baseTypeErrors = this.validateBaseType(shape)
    if (baseTypeErrors.length > 0)
      return baseTypeErrors

    const windowEl = shape as ValidatableWindow
    this.validateSpecificOpeningProperties(windowEl, errors)

    // Window-specific validations
    if (windowEl.heightFromFloor !== undefined && windowEl.heightFromFloor <= 0 && windowEl.openingType !== OpeningType.SKYLIGHT) {
      errors.push({
        code: ValidationErrorCode.INVALID_POSITION,
        message: 'Window height from floor should typically be greater than 0 (unless it is a skylight).',
        path: 'heightFromFloor',
        value: windowEl.heightFromFloor,
      })
    }
    // Add validation for operationType, sillHeight, headHeight if defined on ValidatableWindow
    return errors
  }
}
