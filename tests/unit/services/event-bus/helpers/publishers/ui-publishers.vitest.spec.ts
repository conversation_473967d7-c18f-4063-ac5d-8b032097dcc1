import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  publishNotification,
  publishToastNotification,
  publishToolChanged,
  publishViewPanned,
  publishViewZoomed,
} from '@/services/event-bus/helpers/publishers/ui-publishers'
import { AppEventType } from '@/types/services/events'

describe('uI Publishers', () => {
  let mockEventBus: any

  beforeEach(() => {
    mockEventBus = {
      publish: vi.fn(),
    }
  })

  describe('view Events', () => {
    it('should publish view zoomed event correctly', () => {
      const scale = 1.5

      publishViewZoomed(mockEventBus, scale)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.VIEW_ZOOMED,
        payload: { scale },
      })
    })

    it('should publish view panned event correctly', () => {
      const options = { x: 100, y: 200 }

      publishViewPanned(mockEventBus, options)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.VIEW_PANNED,
        payload: { x: options.x, y: options.y },
      })
    })
  })

  describe('tool Events', () => {
    it('should publish tool changed event correctly', () => {
      const tool = 'rectangle'

      publishToolChanged(mockEventBus, tool)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.TOOL_CHANGED,
        payload: { tool },
      })
    })
  })

  describe('notification Events', () => {
    it('should publish toast notification event correctly', () => {
      const message = 'Test message'
      const type = 'success'
      const duration = 5000

      publishToastNotification(mockEventBus, message, type, duration)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.TOAST_SHOW,
        payload: { message, type, duration },
      })
    })

    it('should use default values for toast notification if not provided', () => {
      const message = 'Test message'

      publishToastNotification(mockEventBus, message)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.TOAST_SHOW,
        payload: { message, type: 'info', duration: 3000 },
      })
    })

    it('should publish notification event correctly', () => {
      const message = 'Test message'
      const type = 'error'

      publishNotification(mockEventBus, message, type)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.NOTIFICATION_ADD,
        payload: { message, type },
      })
    })

    it('should use default values for notification if not provided', () => {
      const message = 'Test message'

      publishNotification(mockEventBus, message)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.NOTIFICATION_ADD,
        payload: { message, type: 'info' },
      })
    })
  })
})
