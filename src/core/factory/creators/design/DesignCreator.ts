/**
 * Abstract Base Creator for Design Elements
 *
 * @remarks
 * This abstract class serves as the foundation for all specific design element creators
 * (e.g., `WallCreator`, `DoorCreator`). It implements the {@link IDesignCreator} interface
 * and provides common functionality such as default metadata generation and a basic
 * `create` method structure.
 *
 * Concrete subclasses must implement the `category` and `elementType` properties.
 * The `createDefault` method is typically overridden by subclasses to instantiate
 * the specific design element with its unique properties.
 *
 * @module core/factory/creators/design/DesignCreator
 * @see {@link IDesignCreator}
 * @see {@link ShapeCreator}
 * @see {@link PathCreator}
 */

import type { ShapeCreationParamsUnion } from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point' // Changed to PointData
import type {
  ElementType,
  MetadataProperties,
  ShapeElement,
} from '@/types/core/elementDefinitions'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
import { ensureCompleteMetadata } from '@/lib/utils/element/elementUtils'
import { MajorCategory } from '@/types/core/majorMinorTypes'

/**
 * Defines the contract for a design element creator.
 *
 * @remarks
 * Design elements often have path-like characteristics, so this interface
 * extends {@link PathCreator}. It adds methods to get the specific
 * design element type and its category.
 */
export interface IDesignCreator {
  /** Gets the specific {@link ElementType} this creator produces. */
  getElementType: () => ElementType
  /** Gets the design category string (e.g., "Wall", "Opening"). */
  getCategory: () => string
  /** Creates an element with the given parameters */
  create: (params: ShapeCreationParamsUnion) => Promise<ShapeElement>
  /** Creates a default element at the given position */
  createDefault: (
    id: string,
    position: PointData,
    majorCategoryOverride?: MajorCategory,
    minorCategoryOverride?: MinorCategory,
    zLevelIdOverride?: string,
    isFixedCategoryOverride?: boolean
  ) => Promise<ShapeElement>
}

/**
 * Type for metadata specific to design elements, extending base metadata.
 */
type DesignMetadata = Partial<MetadataProperties> & {
  designType?: ElementType | string // Specific type of the design element
  designCategory?: string // Broader category (e.g., "Furniture", "Structural")
}

/**
 * Abstract base class for all design element creators.
 *
 * @remarks
 * This class provides common functionalities for creating design elements,
 * such as generating default metadata and a basic creation workflow.
 * Subclasses must implement `category` and `elementType` and typically
 * override `createDefault` to provide specific instantiation logic.
 *
 * @implements {IDesignCreator}
 */
export abstract class DesignCreator implements IDesignCreator {
  /**
   * The category of design element this creator produces.
   * @protected
   * @readonly
   */
  protected abstract readonly category: string

  /**
   * The specific element type this creator produces.
   * @protected
   * @readonly
   */
  protected abstract readonly elementType: ElementType

  /**
   * Creates default metadata for design elements.
   *
   * @returns Default design metadata with name, type, and category.
   * @protected
   */
  protected createDefaultDesignMetadata(): DesignMetadata {
    return ensureCompleteMetadata({
      name: `${this.category} Element ${Date.now()}`, // Add timestamp for uniqueness if needed
      designType: this.elementType,
      designCategory: this.category,
    }) as DesignMetadata
  }

  /**
   * Creates common properties for a design element.
   * This method is required by the IDesignCreator interface.
   *
   * @param id - The unique identifier for the element
   * @param params - Parameters for creating the element
   * @returns Common properties for the design element
   */
  protected createCommonProperties(id: string, params: ShapeCreationParamsUnion): Partial<ShapeElement> {
    const position = (params as { position?: PointData }).position ?? { x: 0, y: 0, z: 0 }

    return {
      id,
      type: this.elementType,
      position,
      rotation: (params as { rotation?: number }).rotation ?? 0,
      visible: (params as { visible?: boolean }).visible ?? true,
      locked: (params as { locked?: boolean }).locked ?? false,
      selectable: (params as { selectable?: boolean }).selectable ?? true,
      draggable: (params as { draggable?: boolean }).draggable ?? true,
      showHandles: (params as { showHandles?: boolean }).showHandles ?? true,
      majorCategory: (params as { majorCategory?: MajorCategory }).majorCategory ?? MajorCategory.FURNITURE,
      minorCategory: (params as { minorCategory?: MinorCategory }).minorCategory,
      zLevelId: (params as { zLevelId?: string }).zLevelId,
      isFixedCategory: (params as { isFixedCategory?: boolean }).isFixedCategory,
      metadata: ensureCompleteMetadata((params as { metadata?: Partial<MetadataProperties> }).metadata ?? {}),
      layer: (params as { layer?: string }).layer,
      zIndex: (params as { zIndex?: number }).zIndex,
      fill: (params as { fill?: string }).fill,
      stroke: (params as { stroke?: string }).stroke,
      strokeWidth: (params as { strokeWidth?: number }).strokeWidth,
      opacity: (params as { opacity?: number }).opacity,
      strokeDasharray: (params as { strokeDasharray?: string }).strokeDasharray,
    }
  }

  /**
   * Creates a design element based on the provided parameters.
   *
   * @remarks
   * This method extracts or defaults a position and then calls `createDefault`
   * for the actual element instantiation.
   *
   * @param params - A {@link ShapeCreationParamsUnion} object containing parameters for element creation.
   * @returns A Promise resolving to the created {@link ShapeElement}.
   */
  async create(params: ShapeCreationParamsUnion): Promise<ShapeElement> {
    let position: PointData = { x: 0, y: 0, z: 0 } // Changed to PointData

    const p = params as {
      position?: PointData | [number, number] | [number, number, number]
      id?: string
      majorCategory?: MajorCategory
      minorCategory?: MinorCategory
      zLevelId?: string
      isFixedCategory?: boolean
    }

    if (p.position != null) {
      const pParam = p.position
      if (typeof pParam === 'object' && pParam !== null) {
        if ('x' in pParam && 'y' in pParam && typeof pParam.x === 'number' && typeof pParam.y === 'number') {
          const pointParam = pParam
          position = { x: pointParam.x, y: pointParam.y, z: 'z' in pointParam && typeof pointParam.z === 'number' ? pointParam.z : 0 }
        }
        else if (Array.isArray(pParam) && typeof pParam[0] === 'number' && typeof pParam[1] === 'number') {
          position = { x: pParam[0], y: pParam[1], z: typeof pParam[2] === 'number' ? pParam[2] : 0 }
        }
      }
    }

    const id = p.id ?? `design-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`

    // Extract category information from params
    const majorCategory = p.majorCategory
    const minorCategory = p.minorCategory
    const zLevelId = p.zLevelId
    const isFixedCategory = p.isFixedCategory

    return this.createDefault(id, position, majorCategory, minorCategory, zLevelId, isFixedCategory)
  }

  /**
   * Abstract or overridable method to create a design element with default properties.
   * Subclasses should typically override this to provide specific instantiation logic.
   *
   * @param id - The unique identifier for the new element.
   * @param position - The {@link PointData} representing the element's position.
   * @param majorCategoryOverride - Optional major category override.
   * @param minorCategoryOverride - Optional minor category override.
   * @param zLevelIdOverride - Optional z-level ID override.
   * @param isFixedCategoryOverride - Optional fixed category flag override.
   * @returns A Promise resolving to the created {@link ShapeElement}.
   */
  async createDefault(
    id: string,
    position: PointData,
    majorCategoryOverride?: MajorCategory,
    minorCategoryOverride?: MinorCategory,
    zLevelIdOverride?: string,
    isFixedCategoryOverride?: boolean,
  ): Promise<ShapeElement> {
    // Create element with default values

    const metadataBase = {}
    const specificDesignMetadata = this.createDefaultDesignMetadata()

    const elementName = specificDesignMetadata.name ?? `Design ${this.elementType}`

    const element: ShapeElement = {
      id,
      type: this.elementType,
      position,
      rotation: 0,
      visible: true,
      locked: false,
      selectable: true,
      draggable: true,
      showHandles: true,
      majorCategory: majorCategoryOverride ?? MajorCategory.FURNITURE, // Add majorCategory
      minorCategory: minorCategoryOverride, // Add optional minorCategory
      zLevelId: zLevelIdOverride, // Add optional zLevelId
      isFixedCategory: isFixedCategoryOverride, // Add optional isFixedCategory
      properties: {
        designType: this.elementType,
        designCategory: this.category,
      },
      metadata: ensureCompleteMetadata({
        ...metadataBase,
        ...specificDesignMetadata,
        name: elementName,
      }),
      fill: undefined,
      stroke: '#000000',
      strokeWidth: 1,
      opacity: 1,
      layer: undefined,
      zIndex: 0,
    }
    return element
  }

  /**
   * Gets the element type this creator produces.
   *
   * @returns The element type.
   */
  getElementType(): ElementType {
    return this.elementType
  }

  /**
   * Gets the category of design element this creator produces.
   *
   * @returns The design category.
   */
  getCategory(): string {
    return this.category
  }
}
