/**
 * SVG Export Functionality Interface Definitions
 *
 * This file defines interface types for SVG export functionality hooks,
 * leveraging the existing event types from exportEvents.ts.
 *
 * - Export actions interface (for UI components)
 * - Export service interface (for internal hook implementation)
 */

import type {
  ExportOptions as BaseExportOptions,
  ExportFormat,
} from '@/types/services/events/exportEvents'

/**
 * Re-export ExportOptions from exportEvents.ts for convenience
 */
export type ExportOptions = BaseExportOptions

/**
 * Export actions interface - for UI components to use
 */
export interface ExportActions {
  /**
   * Export as SVG
   * @param options Export options
   */
  exportAsSVG: (options?: Partial<ExportOptions>) => void

  /**
   * Export as PNG
   * @param options Export options
   */
  exportAsPNG: (options?: Partial<ExportOptions>) => void

  /**
   * Export as PDF
   * @param options Export options
   */
  exportAsPDF: (options?: Partial<ExportOptions>) => void

  /**
   * Export as specified format
   * @param format Export format
   * @param options Export options
   */
  exportAs: (format: ExportFormat | string, options?: Partial<ExportOptions>) => void

  /**
   * Open export modal
   */
  openExportModal: () => void
}

/**
 * Export service interface - for internal hook implementation
 */
export interface ExportService {
  /**
   * Download the exported content
   * 接收组员发送的blob对象并触发下载，不需要再发送数据
   * @param blob Blob object - 组员发送的完整文件内容
   * @param fileName Filename (without extension)
   * @param format Export format
   */
  downloadExport: (blob: Blob, fileName?: string, format?: ExportFormat | string) => void

  /**
   * Handle export request
   * 处理导出请求，发送事件到后台
   * @param format Export format
   * @param options Export options
   */
  handleExportRequest: (format: ExportFormat | string, options?: Partial<ExportOptions>) => void

  /**
   * Check if export is in progress
   * @returns Whether export is in progress
   */
  isExportInProgress: () => boolean

  /**
   * Get current export progress
   * @returns Export progress (0-100)
   */
  getExportProgress: () => number

  /**
   * Get export error if any
   * @returns Export error message or null
   */
  getExportError: () => string | null
}
