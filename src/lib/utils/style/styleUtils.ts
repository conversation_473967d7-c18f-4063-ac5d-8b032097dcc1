/**
 * Style Calculation Utility Functions
 *
 * @remarks
 * This module provides a collection of common utility functions for style calculations
 * and manipulations, such as merging CSS class names, creating dynamic visual effects
 * like shadows and gradients, and processing color values.
 *
 * Key features include:
 * - Merging CSS class names (e.g., with `cn` function, intended for use with `clsx` and `tailwind-merge`).
 * - Generating CSS for floating shadows (`createFloatingShadow`).
 * - Creating transparent colors and adjusting color brightness (`createTransparentColor`, `adjustColorBrightness`, `lightenColor`, `darkenColor`).
 * - Generating linear gradient backgrounds (`createGradientBackground`).
 * - Converting numbers to hex strings (`toHex`).
 * - Processing color strings to standard formats (`processColor`).
 * - Creating CSS styles for glassmorphism effects (`createGlassEffect`).
 *
 * @module lib/utils/style/styleUtils
 */

// 假设 ClassValue, twMerge, clsx 将在需要时导入或全局可用。
// import { type ClassValue, clsx } from "clsx";
// import { twMerge } from "tailwind-merge";

/**
 * Merges CSS class names using a simplified approach.
 *
 * @remarks
 * This is a simplified mock implementation. A production-ready version should typically
 * use libraries like `clsx` for conditional class joining and `tailwind-merge` (if using Tailwind CSS)
 * for resolving conflicting Tailwind utility classes.
 *
 * The current implementation filters out falsy values and joins the remaining inputs with a space.
 *
 * @param inputs - An array of class values (strings, arrays, objects). `any[]` is used for simplicity here.
 * @returns A string of combined CSS class names.
 *
 * @example
 * ```typescript
 * cn("class1", "class2", { class3: true, class4: false }, ["class5", "class6"]);
 * // Simplified output: "class1 class2 class3 class5 class6" (if object/array handling was complete)
 * // Current mock output: "class1 class2 [object Object] class5,class6"
 * ```
 */
export function cn(...inputs: any[]): string {
  // For a proper implementation, consider:
  // import { type ClassValue, clsx } from "clsx";
  // import { twMerge } from "tailwind-merge";
  // return twMerge(clsx(inputs));
  return inputs.filter(Boolean).join(' ') // Simplified mock implementation
}

/**
 * Creates a CSS `box-shadow` string for a floating shadow effect.
 *
 * @param intensity - The intensity of the shadow, ranging from 1 to 5. Defaults to 3.
 *                    Higher values produce a more pronounced shadow.
 * @returns A CSS `box-shadow` string.
 * @throws {@link Error} if `intensity` is not a number.
 */
export function createFloatingShadow(intensity = 3): string {
  if (typeof intensity !== 'number') {
    throw new TypeError('Intensity must be a number')
  }
  const clampedIntensity = Math.max(1, Math.min(intensity, 5))
  const yOffset = clampedIntensity * 2
  const blur = clampedIntensity * 4
  const spread = clampedIntensity * -1
  const opacity = 0.1 + clampedIntensity * 0.02
  return `${spread}px ${yOffset}px ${blur}px rgba(0,0,0,${opacity.toFixed(2)})`
}

/**
 * Converts a hexadecimal color string to an `rgba()` string with a specified alpha (transparency).
 *
 * @param color - The hexadecimal color string (e.g., '#FF0000', '#F00').
 * @param alpha - The alpha transparency value, ranging from 0 (fully transparent) to 1 (fully opaque).
 *                Defaults to 0.5.
 * @returns An `rgba()` color string. If the input `color` is not a valid hex format,
 *          a warning is logged, and the original color string is returned.
 * @throws {@link Error} if the `color` parameter is missing.
 */
export function createTransparentColor(color: string, alpha = 0.5): string {
  if (!color) {
    throw new Error('Color parameter is required')
  }
  if (!/^#([0-9A-F]{3}){1,2}$/i.test(color)) {
    console.warn(`createTransparentColor: Non-hex color "${color}" provided. Alpha transparency might not apply as expected.`)
    return color
  }
  const hex = color.replace('#', '')
  const r = Number.parseInt(hex.substring(0, 2), 16)
  const g = Number.parseInt(hex.substring(2, 4), 16)
  const b = Number.parseInt(hex.substring(4, 6), 16)
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}

/**
 * Adjusts the brightness of a hexadecimal color by a given factor.
 *
 * @param color - The hexadecimal color string (e.g., '#FF0000', '#F00').
 * @param factor - The brightness adjustment factor. Values greater than 1 lighten the color,
 *                 values less than 1 darken it. A factor of 1 leaves the color unchanged.
 *                 Negative factors are not recommended and might lead to black.
 * @returns A new hexadecimal color string with adjusted brightness.
 *          If the input `color` is not a valid hex format, a warning is logged,
 *          and the original color string is returned.
 * @throws {@link Error} if the `color` parameter is missing.
 */
export function adjustColorBrightness(color: string, factor: number): string {
  if (!color) {
    throw new Error('Color parameter is required')
  }
  if (!/^#([0-9A-F]{3}){1,2}$/i.test(color)) {
    console.warn(`adjustColorBrightness: Non-hex color "${color}" provided. Brightness adjustment might not apply as expected.`)
    return color
  }
  const hex = color.replace('#', '')
  let r = Number.parseInt(hex.substring(0, 2), 16)
  let g = Number.parseInt(hex.substring(2, 4), 16)
  let b = Number.parseInt(hex.substring(4, 6), 16)

  r = Math.max(0, Math.min(255, Math.round(r * factor)))
  g = Math.max(0, Math.min(255, Math.round(g * factor)))
  b = Math.max(0, Math.min(255, Math.round(b * factor)))

  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
}

/**
 * Lightens a hexadecimal color by a specified amount.
 *
 * @param color - The hexadecimal color string.
 * @param amount - The amount to lighten the color by, as a decimal between 0 and 1 (e.g., 0.2 for 20% lighter).
 *                 Defaults to 0.2. The absolute value of `amount` is used.
 * @returns A new, lightened hexadecimal color string.
 */
export function lightenColor(color: string, amount: number = 0.2): string {
  const positiveAmount = Math.abs(amount) // Ensure amount is positive for lightening
  return adjustColorBrightness(color, 1 + positiveAmount)
}

/**
 * Darkens a hexadecimal color by a specified amount.
 *
 * @param color - The hexadecimal color string.
 * @param amount - The amount to darken the color by, as a decimal between 0 and 1 (e.g., 0.2 for 20% darker).
 *                 Defaults to 0.2. The absolute value of `amount` is used.
 * @returns A new, darkened hexadecimal color string.
 */
export function darkenColor(color: string, amount: number = 0.2): string {
  const positiveAmount = Math.abs(amount) // Ensure amount is positive for darkening factor
  return adjustColorBrightness(color, 1 - positiveAmount)
}

/**
 * Creates a CSS `linear-gradient` background string.
 *
 * @param startColor - The starting color of the gradient.
 * @param endColor - The ending color of the gradient.
 * @param direction - The direction of the gradient in degrees. Defaults to 135.
 * @param opacity - The overall opacity of the gradient layer (0-1). Defaults to 1.
 *                  If less than 1, an `opacity` CSS property might be more appropriate
 *                  unless a semi-transparent gradient overlay is intended.
 *                  The current implementation appends an `opacity` style if `< 1`, which is unusual for `linear-gradient`.
 *                  Consider applying opacity to the element itself or using rgba colors.
 * @returns A CSS `linear-gradient()` string. If opacity is less than 1, it appends an `opacity` style,
 *          which might not be standard for a background image property.
 * @throws {@link Error} if `startColor` or `endColor` parameters are missing.
 */
export function createGradientBackground(
  startColor: string,
  endColor: string,
  direction: number = 135,
  opacity: number = 1,
): string {
  if (!startColor || !endColor) {
    throw new Error('Both startColor and endColor parameters are required')
  }
  const safeOpacity = Math.max(0, Math.min(1, opacity))
  return `linear-gradient(${direction}deg, ${startColor}, ${endColor}) ${safeOpacity < 1 ? `opacity: ${safeOpacity}` : ''}`.trim()
}

/**
 * Converts a number (0-255) to its two-digit hexadecimal string representation.
 * @param c - The number to convert.
 * @returns A two-digit hexadecimal string (e.g., 10 -> "0a", 255 -> "ff").
 */
export function toHex(c: number): string {
  const hex = Math.max(0, Math.min(255, Math.round(c))).toString(16) // Ensure c is in 0-255 range
  return hex.length === 1 ? `0${hex}` : hex
}

/**
 * Processes a color string (hex format) and applies an opacity, returning an `rgba` or hex string.
 *
 * @param color - The color string, expected in hex format (e.g., '#RGB', '#RRGGBB', 'RGB', 'RRGGBB').
 * @param opacity - The opacity level, from 0 (transparent) to 1 (opaque). Defaults to 1.
 * @returns An `rgba(r,g,b,a)` string if `opacity` is less than 1, otherwise a full hex string (e.g., '#RRGGBB').
 *          Returns the original color string with a warning if the input `color` is not a valid hex format.
 */
export function processColor(color: string, opacity: number = 1): string {
  const hex = color.startsWith('#') ? color.substring(1) : color
  const validOpacity = Math.max(0, Math.min(1, opacity))

  if (!/^[0-9A-F]{3}$|^[0-9A-F]{6}$/i.test(hex)) {
    console.warn(`processColor: Invalid hex color format: ${color}. Returning original color.`)
    return color
  }

  let r: number, g: number, b: number

  if (hex.length === 3) {
    r = Number.parseInt(hex[0] + hex[0], 16)
    g = Number.parseInt(hex[1] + hex[1], 16)
    b = Number.parseInt(hex[2] + hex[2], 16)
  }
  else {
    r = Number.parseInt(hex.substring(0, 2), 16)
    g = Number.parseInt(hex.substring(2, 4), 16)
    b = Number.parseInt(hex.substring(4, 6), 16)
  }

  return validOpacity < 1
    ? `rgba(${r}, ${g}, ${b}, ${validOpacity})`
    : `#${hex.length === 3 ? `${hex[0]}${hex[0]}${hex[1]}${hex[1]}${hex[2]}${hex[2]}` : hex}`
}

/**
 * Creates a CSS style object for a glassmorphism effect.
 *
 * @param opacity - The opacity of the background color (value between 0 and 1). Defaults to 0.8.
 * @param blur - The blur radius in pixels for the `backdrop-filter`. Defaults to 4.
 *               Negative values will be converted to positive.
 * @returns A style object compatible with React's `style` prop (or for direct CSS use),
 *          containing `backgroundColor`, `backdropFilter`, `border`, and `boxShadow` properties.
 * @throws {@link Error} if `opacity` or `blur` is not a valid number (e.g., NaN).
 */
export function createGlassEffect(opacity = 0.8, blur = 4): Record<string, string> {
  if (typeof opacity !== 'number') {
    throw new TypeError('Opacity must be a number')
  }
  if (isNaN(opacity)) {
    throw new TypeError('Opacity cannot be NaN')
  }
  const validOpacity = Math.max(0, Math.min(1, opacity))
  if (validOpacity !== opacity) {
    console.warn(`Opacity value ${opacity} was clamped to ${validOpacity} (valid range: 0-1)`)
  }

  if (typeof blur !== 'number') {
    throw new TypeError('Blur must be a number')
  }
  if (isNaN(blur)) {
    throw new TypeError('Blur cannot be NaN')
  }
  if (blur < 0) {
    console.warn(`Negative blur value ${blur} was converted to positive ${Math.abs(blur)}`)
    blur = Math.abs(blur)
  }

  return {
    backgroundColor: `rgba(255, 255, 255, ${validOpacity})`,
    backdropFilter: `blur(${blur}px)`,
    border: '1px solid rgba(255, 255, 255, 0.2)',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.05), 0 2px 8px rgba(0, 0, 0, 0.05)',
  }
}

/**
 * Alias for the {@link createGlassEffect} function.
 */
export const glassEffect = createGlassEffect
