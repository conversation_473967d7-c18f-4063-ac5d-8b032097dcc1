/**
 * Unit tests for RectangleValidator
 *
 * Tests the rectangle-specific validator
 */

import { expect, test } from '@playwright/test'

// Define types locally for testing
interface ValidationError {
  code: string
  message: string
  path?: string
  value?: any
}

// Mock RectangleValidator for testing
class RectangleValidator {
  validate(shape: any): { valid: boolean, errors: ValidationError[] } {
    const errors: ValidationError[] = []

    // Check shape type
    if (!shape || shape.type !== 'rectangle') {
      errors.push({ code: 'INVALID_SHAPE_TYPE', message: 'Not a rectangle shape' })
      return { valid: false, errors }
    }

    // Check ID
    if (!shape.id || shape.id.trim() === '') {
      errors.push({ code: 'MISSING_ID', message: 'Missing ID' })
    }

    // Check position
    if (!shape.position || typeof shape.position.x !== 'number' || typeof shape.position.y !== 'number'
      || isNaN(shape.position.x) || isNaN(shape.position.y)) {
      errors.push({ code: 'INVALID_POSITION', message: 'Invalid position' })
    }

    // Check width
    if (!shape.width || typeof shape.width !== 'number' || shape.width <= 0) {
      errors.push({ code: 'INVALID_WIDTH', message: 'Invalid width' })
    }
    else if (shape.width < 10) {
      errors.push({ code: 'WIDTH_TOO_SMALL', message: 'Width too small' })
    }

    // Check height
    if (!shape.height || typeof shape.height !== 'number' || shape.height <= 0) {
      errors.push({ code: 'INVALID_HEIGHT', message: 'Invalid height' })
    }
    else if (shape.height < 10) {
      errors.push({ code: 'HEIGHT_TOO_SMALL', message: 'Height too small' })
    }

    // Check colors
    const validColors = ['#000', '#FFF', '#000000', '#FFFFFF', '#3b82f6', 'red', 'blue', 'green', 'transparent']
    if (!validColors.includes(shape.strokeColor)) {
      errors.push({ code: 'INVALID_COLOR', message: 'Invalid stroke color' })
    }
    if (!validColors.includes(shape.fillColor)) {
      errors.push({ code: 'INVALID_COLOR', message: 'Invalid fill color' })
    }

    return { valid: errors.length === 0, errors }
  }
}

test.describe('RectangleValidator', () => {
  let validator: RectangleValidator

  test.beforeEach(() => {
    validator = new RectangleValidator()
  })

  test('validates a valid rectangle', () => {
    // Create a valid rectangle
    const validRectangle = {
      id: 'rect1',
      type: 'rectangle',
      position: { x: 100, y: 100 },
      width: 200,
      height: 150,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the rectangle
    const result = validator.validate(validRectangle as any)

    // Verify the result
    expect(result.valid).toBe(true)
    expect(result.errors).toEqual([])
  })

  test('detects a rectangle with missing ID', () => {
    // Create a rectangle with missing ID
    const invalidRectangle = {
      id: '',
      type: 'rectangle',
      position: { x: 100, y: 100 },
      width: 200,
      height: 150,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the rectangle
    const result = validator.validate(invalidRectangle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'MISSING_ID')).toBe(true)
  })

  test('detects a rectangle with invalid position', () => {
    // Create a rectangle with invalid position
    const invalidRectangle = {
      id: 'rect2',
      type: 'rectangle',
      position: { x: Number.NaN, y: 100 },
      width: 200,
      height: 150,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the rectangle
    const result = validator.validate(invalidRectangle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_POSITION')).toBe(true)
  })

  test('detects a rectangle with negative width', () => {
    // Create a rectangle with negative width
    const invalidRectangle = {
      id: 'rect3',
      type: 'rectangle',
      position: { x: 100, y: 100 },
      width: -200,
      height: 150,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the rectangle
    const result = validator.validate(invalidRectangle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_WIDTH')).toBe(true)
  })

  test('detects a rectangle with negative height', () => {
    // Create a rectangle with negative height
    const invalidRectangle = {
      id: 'rect4',
      type: 'rectangle',
      position: { x: 100, y: 100 },
      width: 200,
      height: -150,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the rectangle
    const result = validator.validate(invalidRectangle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_HEIGHT')).toBe(true)
  })

  test('detects a rectangle with zero width', () => {
    // Create a rectangle with zero width
    const invalidRectangle = {
      id: 'rect5',
      type: 'rectangle',
      position: { x: 100, y: 100 },
      width: 0,
      height: 150,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the rectangle
    const result = validator.validate(invalidRectangle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_WIDTH')).toBe(true)
  })

  test('detects a rectangle with zero height', () => {
    // Create a rectangle with zero height
    const invalidRectangle = {
      id: 'rect6',
      type: 'rectangle',
      position: { x: 100, y: 100 },
      width: 200,
      height: 0,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the rectangle
    const result = validator.validate(invalidRectangle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_HEIGHT')).toBe(true)
  })

  test('detects a rectangle with invalid colors', () => {
    // Create a rectangle with invalid colors
    const invalidRectangle = {
      id: 'rect7',
      type: 'rectangle',
      position: { x: 100, y: 100 },
      width: 200,
      height: 150,
      strokeColor: 'invalid-color',
      fillColor: 'invalid-color',
    }

    // Validate the rectangle
    const result = validator.validate(invalidRectangle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_COLOR')).toBe(true)
  })

  test('rejects non-rectangle shapes', () => {
    // Create a circle (not a rectangle)
    const circle = {
      id: 'circle1',
      type: 'circle',
      position: { x: 100, y: 100 },
      radius: 50,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the circle using the rectangle validator
    const result = validator.validate(circle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_SHAPE_TYPE')).toBe(true)
  })

  test('applies business rules for minimum dimensions', () => {
    // Create a rectangle with dimensions below the minimum
    const smallRectangle = {
      id: 'rect8',
      type: 'rectangle',
      position: { x: 100, y: 100 },
      width: 5, // Below minimum (assuming minimum is 10)
      height: 5, // Below minimum (assuming minimum is 10)
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the rectangle
    const result = validator.validate(smallRectangle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'WIDTH_TOO_SMALL')).toBe(true)
    expect(result.errors.some(e => e.code === 'HEIGHT_TOO_SMALL')).toBe(true)
  })
})
