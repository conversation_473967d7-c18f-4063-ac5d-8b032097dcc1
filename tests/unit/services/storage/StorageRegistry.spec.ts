import type { ServiceRegistry } from '@/services/core/registry'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { appEventBus } from '@/services/core/event-bus'
import { registerStorageService } from '@/services/storage/registry'
import { StorageService } from '@/services/storage/storageService'
import { ServiceId } from '@/types/services/core/serviceIdentifier'

// Mock dependencies
vi.mock('@/services/storage/storageService')
vi.mock('@/services/core/event-bus', () => ({
  appEventBus: {
    publish: vi.fn(),
    subscribe: vi.fn(),
    unsubscribe: vi.fn(),
    clear: vi.fn(),
  },
}))

describe('storage Registry', () => {
  let mockRegistry: ServiceRegistry
  let mockLogger: LoggerService
  let mockStorageService: StorageService

  beforeEach(() => {
    mockStorageService = {
      get: vi.fn(),
      set: vi.fn(),
      remove: vi.fn(),
      clear: vi.fn(),
      has: vi.fn(),
      keys: vi.fn(),
      size: vi.fn(),
    } as unknown as StorageService

    mockRegistry = {
      register: vi.fn(),
      get: vi.fn(),
      has: vi.fn(),
      unregister: vi.fn(),
      clear: vi.fn(),
      getAll: vi.fn(),
    }

    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    }

    // Mock StorageService constructor
    vi.mocked(StorageService).mockImplementation(() => mockStorageService)

    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('registerStorageService', () => {
    it('should register storage service successfully', () => {
      const result = registerStorageService(mockRegistry, mockLogger)

      expect(mockLogger.info).toHaveBeenCalledWith('Registering StorageService')
      expect(StorageService).toHaveBeenCalledWith(appEventBus, mockLogger)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.StorageService,
        mockStorageService,
      )
      expect(mockLogger.info).toHaveBeenCalledWith('StorageService registered successfully')
      expect(result).toBe(mockStorageService)
    })

    it('should create StorageService with correct dependencies', () => {
      registerStorageService(mockRegistry, mockLogger)

      expect(StorageService).toHaveBeenCalledWith(appEventBus, mockLogger)
      expect(StorageService).toHaveBeenCalledTimes(1)
    })

    it('should return the created StorageService instance', () => {
      const result = registerStorageService(mockRegistry, mockLogger)

      expect(result).toBe(mockStorageService)
      expect(result).toBeDefined()
    })

    it('should handle StorageService constructor errors', () => {
      const error = new Error('StorageService creation failed')
      vi.mocked(StorageService).mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerStorageService(mockRegistry, mockLogger)
      }).toThrow(error)

      expect(mockLogger.info).toHaveBeenCalledWith('Registering StorageService')
      expect(mockRegistry.register).not.toHaveBeenCalled()
      expect(mockLogger.info).not.toHaveBeenCalledWith('StorageService registered successfully')
    })

    it('should handle registry registration errors', () => {
      const error = new Error('Registry registration failed')
      mockRegistry.register.mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerStorageService(mockRegistry, mockLogger)
      }).toThrow(error)

      expect(StorageService).toHaveBeenCalledWith(appEventBus, mockLogger)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.StorageService,
        mockStorageService,
      )
      expect(mockLogger.info).toHaveBeenCalledWith('Registering StorageService')
      expect(mockLogger.info).not.toHaveBeenCalledWith('StorageService registered successfully')
    })

    it('should handle logger errors gracefully', () => {
      const error = new Error('Logging failed')
      mockLogger.info.mockImplementationOnce(() => {
        throw error
      })

      expect(() => {
        registerStorageService(mockRegistry, mockLogger)
      }).toThrow(error)

      expect(mockLogger.info).toHaveBeenCalledWith('Registering StorageService')
      expect(StorageService).not.toHaveBeenCalled()
      expect(mockRegistry.register).not.toHaveBeenCalled()
    })

    it('should work with different logger implementations', () => {
      const customLogger = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
        trace: vi.fn(), // Additional method
      }

      const result = registerStorageService(mockRegistry, customLogger)

      expect(customLogger.info).toHaveBeenCalledWith('Registering StorageService')
      expect(StorageService).toHaveBeenCalledWith(appEventBus, customLogger)
      expect(customLogger.info).toHaveBeenCalledWith('StorageService registered successfully')
      expect(result).toBe(mockStorageService)
    })

    it('should work with different registry implementations', () => {
      const customRegistry = {
        register: vi.fn(),
        get: vi.fn(),
        has: vi.fn(),
        unregister: vi.fn(),
        clear: vi.fn(),
        getAll: vi.fn(),
        size: vi.fn(), // Additional method
      }

      const result = registerStorageService(customRegistry, mockLogger)

      expect(customRegistry.register).toHaveBeenCalledWith(
        ServiceId.StorageService,
        mockStorageService,
      )
      expect(result).toBe(mockStorageService)
    })

    it('should handle multiple registration calls', () => {
      const result1 = registerStorageService(mockRegistry, mockLogger)
      const result2 = registerStorageService(mockRegistry, mockLogger)

      expect(StorageService).toHaveBeenCalledTimes(2)
      expect(mockRegistry.register).toHaveBeenCalledTimes(2)
      expect(mockLogger.info).toHaveBeenCalledTimes(4) // 2 calls for each registration
      expect(result1).toBe(mockStorageService)
      expect(result2).toBe(mockStorageService)
    })

    it('should create new StorageService instance for each registration', () => {
      const service1 = { get: vi.fn(), id: 'service1' } as unknown as StorageService
      const service2 = { get: vi.fn(), id: 'service2' } as unknown as StorageService

      vi.mocked(StorageService)
        .mockImplementationOnce(() => service1)
        .mockImplementationOnce(() => service2)

      const result1 = registerStorageService(mockRegistry, mockLogger)
      const result2 = registerStorageService(mockRegistry, mockLogger)

      expect(result1).toBe(service1)
      expect(result2).toBe(service2)
      expect(mockRegistry.register).toHaveBeenNthCalledWith(1, ServiceId.StorageService, service1)
      expect(mockRegistry.register).toHaveBeenNthCalledWith(2, ServiceId.StorageService, service2)
    })

    it('should preserve correct execution order', () => {
      const callOrder: string[] = []

      mockLogger.info.mockImplementation((message: string) => {
        callOrder.push(`logger.info: ${message}`)
      })

      vi.mocked(StorageService).mockImplementation(() => {
        callOrder.push('StorageService constructor')
        return mockStorageService
      })

      mockRegistry.register.mockImplementation(() => {
        callOrder.push('registry.register')
      })

      registerStorageService(mockRegistry, mockLogger)

      expect(callOrder).toEqual([
        'logger.info: Registering StorageService',
        'StorageService constructor',
        'registry.register',
        'logger.info: StorageService registered successfully',
      ])
    })

    it('should use correct service identifier', () => {
      registerStorageService(mockRegistry, mockLogger)

      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.StorageService,
        mockStorageService,
      )

      const registrationCall = mockRegistry.register.mock.calls[0]
      expect(registrationCall[0]).toBe(ServiceId.StorageService)
      expect(typeof registrationCall[0]).toBe('string')
    })

    it('should pass appEventBus to StorageService constructor', () => {
      registerStorageService(mockRegistry, mockLogger)

      expect(StorageService).toHaveBeenCalledWith(appEventBus, mockLogger)

      const constructorCall = vi.mocked(StorageService).mock.calls[0]
      expect(constructorCall[0]).toBe(appEventBus)
      expect(constructorCall[1]).toBe(mockLogger)
    })

    it('should handle edge cases with service registration', () => {
      // Test with null logger (should still work if StorageService accepts it)
      const nullLogger = null as unknown as LoggerService

      expect(() => {
        registerStorageService(mockRegistry, nullLogger)
      }).toThrow() // Should throw because logger.info is called

      // Test with minimal registry
      const minimalRegistry = {
        register: vi.fn(),
      } as unknown as ServiceRegistry

      const result = registerStorageService(minimalRegistry, mockLogger)
      expect(minimalRegistry.register).toHaveBeenCalledWith(
        ServiceId.StorageService,
        mockStorageService,
      )
      expect(result).toBe(mockStorageService)
    })

    it('should handle StorageService constructor with different parameters', () => {
      // Test that the function correctly passes the expected parameters
      registerStorageService(mockRegistry, mockLogger)

      const constructorArgs = vi.mocked(StorageService).mock.calls[0]
      expect(constructorArgs).toHaveLength(2)
      expect(constructorArgs[0]).toBe(appEventBus)
      expect(constructorArgs[1]).toBe(mockLogger)
    })

    it('should handle success logging failure', () => {
      mockLogger.info
        .mockImplementationOnce(() => {}) // First call succeeds
        .mockImplementationOnce(() => {
          throw new Error('Success logging failed')
        }) // Second call fails

      expect(() => {
        registerStorageService(mockRegistry, mockLogger)
      }).toThrow('Success logging failed')

      // Service should still be created and registered
      expect(StorageService).toHaveBeenCalledWith(appEventBus, mockLogger)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.StorageService,
        mockStorageService,
      )
    })
  })

  describe('integration scenarios', () => {
    it('should work in a typical application initialization flow', () => {
      const result = registerStorageService(mockRegistry, mockLogger)

      // Verify complete flow
      expect(mockLogger.info).toHaveBeenCalledWith('Registering StorageService')
      expect(StorageService).toHaveBeenCalledWith(appEventBus, mockLogger)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.StorageService,
        mockStorageService,
      )
      expect(mockLogger.info).toHaveBeenCalledWith('StorageService registered successfully')
      expect(result).toBe(mockStorageService)
    })

    it('should handle service replacement scenarios', () => {
      const service1 = { get: vi.fn(), id: 'service1' } as unknown as StorageService
      const service2 = { get: vi.fn(), id: 'service2' } as unknown as StorageService

      vi.mocked(StorageService)
        .mockImplementationOnce(() => service1)
        .mockImplementationOnce(() => service2)

      // Initial registration
      const result1 = registerStorageService(mockRegistry, mockLogger)
      expect(result1).toBe(service1)

      // Service replacement
      const result2 = registerStorageService(mockRegistry, mockLogger)
      expect(result2).toBe(service2)

      expect(mockRegistry.register).toHaveBeenCalledTimes(2)
      expect(mockRegistry.register).toHaveBeenNthCalledWith(1, ServiceId.StorageService, service1)
      expect(mockRegistry.register).toHaveBeenNthCalledWith(2, ServiceId.StorageService, service2)
    })

    it('should work with real-world service dependencies', () => {
      // Simulate more realistic scenario
      const result = registerStorageService(mockRegistry, mockLogger)

      // Verify that the service is properly configured
      expect(result).toBeDefined()
      expect(StorageService).toHaveBeenCalledWith(appEventBus, mockLogger)

      // Verify that the service can be retrieved from registry
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.StorageService,
        result,
      )
    })
  })
})
