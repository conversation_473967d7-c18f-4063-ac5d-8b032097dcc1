import { test, expect } from '@playwright/test';

/**
 * End-to-end test suite for shape interaction workflows.
 * These tests verify that users can interact with shapes through the application's
 * user interface, including selecting, moving, resizing, and styling shapes.
 */
test.describe('Shape Interaction Workflow', () => {
  /**
   * Setup for each test.
   * Navigates to the application and waits for it to load.
   */
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the application to load
    await page.waitForSelector('.canvas-container');
  });

  /**
   * Tests the shape selection workflow.
   * Verifies that users can select and deselect shapes.
   */
  test('Select and deselect shapes', async ({ page }) => {
    // Create a rectangle
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const startX = boundingBox.x + 100;
    const startY = boundingBox.y + 100;
    const endX = startX + 200;
    const endY = startY + 150;
    
    await page.mouse.move(startX, startY);
    await page.mouse.down();
    await page.mouse.move(endX, endY);
    await page.mouse.up();
    
    // Create a circle
    await page.click('#tool-ellipse');
    
    const centerX = boundingBox.x + 300;
    const centerY = boundingBox.y + 300;
    const radiusX = 50;
    const radiusY = 50;
    
    await page.mouse.move(centerX, centerY);
    await page.mouse.down();
    await page.mouse.move(centerX + radiusX, centerY + radiusY);
    await page.mouse.up();
    
    // Switch to select tool
    await page.click('#tool-select');
    
    // Select the rectangle
    await page.mouse.move(startX + 100, startY + 75);
    await page.mouse.down();
    await page.mouse.up();
    
    // Verify that the rectangle is selected
    const selectedRectangle = page.locator('rect.selected');
    await expect(selectedRectangle).toBeVisible();
    
    // Verify that the circle is not selected
    const selectedCircle = page.locator('ellipse.selected');
    await expect(selectedCircle).not.toBeVisible();
    
    // Select the circle (add to selection with Shift key)
    await page.keyboard.down('Shift');
    await page.mouse.move(centerX, centerY);
    await page.mouse.down();
    await page.mouse.up();
    await page.keyboard.up('Shift');
    
    // Verify that both shapes are selected
    await expect(selectedRectangle).toBeVisible();
    await expect(selectedCircle).toBeVisible();
    
    // Deselect all by clicking on empty area
    await page.mouse.move(boundingBox.x + 500, boundingBox.y + 500);
    await page.mouse.down();
    await page.mouse.up();
    
    // Verify that no shapes are selected
    await expect(selectedRectangle).not.toBeVisible();
    await expect(selectedCircle).not.toBeVisible();
  });

  /**
   * Tests the shape movement workflow.
   * Verifies that users can move shapes by dragging them.
   */
  test('Move shapes by dragging', async ({ page }) => {
    // Create a rectangle
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const startX = boundingBox.x + 100;
    const startY = boundingBox.y + 100;
    const endX = startX + 200;
    const endY = startY + 150;
    
    await page.mouse.move(startX, startY);
    await page.mouse.down();
    await page.mouse.move(endX, endY);
    await page.mouse.up();
    
    // Switch to select tool
    await page.click('#tool-select');
    
    // Select the rectangle
    await page.mouse.move(startX + 100, startY + 75);
    await page.mouse.down();
    await page.mouse.up();
    
    // Get the initial position of the rectangle
    const initialRect = await page.locator('rect').boundingBox();
    
    // Move the rectangle
    const moveX = 50;
    const moveY = 30;
    
    await page.mouse.move(startX + 100, startY + 75);
    await page.mouse.down();
    await page.mouse.move(startX + 100 + moveX, startY + 75 + moveY);
    await page.mouse.up();
    
    // Get the new position of the rectangle
    const movedRect = await page.locator('rect').boundingBox();
    
    // Verify that the rectangle was moved
    expect(movedRect.x).toBeCloseTo(initialRect.x + moveX, 0);
    expect(movedRect.y).toBeCloseTo(initialRect.y + moveY, 0);
  });

  /**
   * Tests the shape resizing workflow.
   * Verifies that users can resize shapes using the resize handles.
   */
  test('Resize shapes using handles', async ({ page }) => {
    // Create a rectangle
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const startX = boundingBox.x + 100;
    const startY = boundingBox.y + 100;
    const endX = startX + 200;
    const endY = startY + 150;
    
    await page.mouse.move(startX, startY);
    await page.mouse.down();
    await page.mouse.move(endX, endY);
    await page.mouse.up();
    
    // Switch to select tool
    await page.click('#tool-select');
    
    // Select the rectangle
    await page.mouse.move(startX + 100, startY + 75);
    await page.mouse.down();
    await page.mouse.up();
    
    // Get the initial size of the rectangle
    const initialRect = await page.locator('rect').boundingBox();
    
    // Resize the rectangle using the bottom-right handle
    const handleX = endX;
    const handleY = endY;
    const resizeX = 50;
    const resizeY = 30;
    
    // Wait for the resize handles to appear
    await page.waitForSelector('.resize-handle');
    
    // Find the bottom-right handle
    const bottomRightHandle = page.locator('.resize-handle.bottom-right');
    const handleBox = await bottomRightHandle.boundingBox();
    
    // Drag the handle
    await page.mouse.move(handleBox.x + handleBox.width / 2, handleBox.y + handleBox.height / 2);
    await page.mouse.down();
    await page.mouse.move(handleBox.x + handleBox.width / 2 + resizeX, handleBox.y + handleBox.height / 2 + resizeY);
    await page.mouse.up();
    
    // Get the new size of the rectangle
    const resizedRect = await page.locator('rect').boundingBox();
    
    // Verify that the rectangle was resized
    expect(resizedRect.width).toBeCloseTo(initialRect.width + resizeX, 0);
    expect(resizedRect.height).toBeCloseTo(initialRect.height + resizeY, 0);
  });

  /**
   * Tests the shape rotation workflow.
   * Verifies that users can rotate shapes using the rotation handle.
   */
  test('Rotate shapes using rotation handle', async ({ page }) => {
    // Create a rectangle
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const startX = boundingBox.x + 100;
    const startY = boundingBox.y + 100;
    const endX = startX + 200;
    const endY = startY + 150;
    
    await page.mouse.move(startX, startY);
    await page.mouse.down();
    await page.mouse.move(endX, endY);
    await page.mouse.up();
    
    // Switch to select tool
    await page.click('#tool-select');
    
    // Select the rectangle
    await page.mouse.move(startX + 100, startY + 75);
    await page.mouse.down();
    await page.mouse.up();
    
    // Wait for the rotation handle to appear
    await page.waitForSelector('.rotation-handle');
    
    // Find the rotation handle
    const rotationHandle = page.locator('.rotation-handle');
    const handleBox = await rotationHandle.boundingBox();
    
    // Get the initial transform of the rectangle
    const initialTransform = await page.locator('rect').getAttribute('transform');
    
    // Drag the rotation handle to rotate the rectangle
    await page.mouse.move(handleBox.x + handleBox.width / 2, handleBox.y + handleBox.height / 2);
    await page.mouse.down();
    
    // Move the mouse to rotate by approximately 45 degrees
    const centerX = startX + 100;
    const centerY = startY + 75;
    const radius = 50;
    const angle = 45 * (Math.PI / 180);
    const rotateX = centerX + radius * Math.cos(angle);
    const rotateY = centerY + radius * Math.sin(angle);
    
    await page.mouse.move(rotateX, rotateY);
    await page.mouse.up();
    
    // Get the new transform of the rectangle
    const rotatedTransform = await page.locator('rect').getAttribute('transform');
    
    // Verify that the rectangle was rotated
    expect(rotatedTransform).not.toBe(initialTransform);
    expect(rotatedTransform).toContain('rotate');
  });

  /**
   * Tests the shape styling workflow.
   * Verifies that users can change the style of shapes using the properties panel.
   */
  test('Change shape style using properties panel', async ({ page }) => {
    // Create a rectangle
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const startX = boundingBox.x + 100;
    const startY = boundingBox.y + 100;
    const endX = startX + 200;
    const endY = startY + 150;
    
    await page.mouse.move(startX, startY);
    await page.mouse.down();
    await page.mouse.move(endX, endY);
    await page.mouse.up();
    
    // Switch to select tool
    await page.click('#tool-select');
    
    // Select the rectangle
    await page.mouse.move(startX + 100, startY + 75);
    await page.mouse.down();
    await page.mouse.up();
    
    // Verify that the properties panel is visible
    const propertiesPanel = page.locator('.properties-panel');
    await expect(propertiesPanel).toBeVisible();
    
    // Get the initial fill color of the rectangle
    const initialFill = await page.locator('rect').getAttribute('fill');
    
    // Change the fill color
    await page.locator('.fill-color-picker').click();
    await page.locator('.color-option[data-color="red"]').click();
    
    // Get the new fill color of the rectangle
    const newFill = await page.locator('rect').getAttribute('fill');
    
    // Verify that the fill color was changed
    expect(newFill).not.toBe(initialFill);
    expect(newFill).toBe('red');
    
    // Get the initial stroke width of the rectangle
    const initialStrokeWidth = await page.locator('rect').getAttribute('stroke-width');
    
    // Change the stroke width
    await page.locator('.stroke-width-input').fill('5');
    await page.keyboard.press('Enter');
    
    // Get the new stroke width of the rectangle
    const newStrokeWidth = await page.locator('rect').getAttribute('stroke-width');
    
    // Verify that the stroke width was changed
    expect(newStrokeWidth).not.toBe(initialStrokeWidth);
    expect(newStrokeWidth).toBe('5');
  });

  /**
   * Tests the shape grouping workflow.
   * Verifies that users can group and ungroup shapes.
   */
  test('Group and ungroup shapes', async ({ page }) => {
    // Create a rectangle
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const rect1X = boundingBox.x + 100;
    const rect1Y = boundingBox.y + 100;
    
    await page.mouse.move(rect1X, rect1Y);
    await page.mouse.down();
    await page.mouse.move(rect1X + 100, rect1Y + 100);
    await page.mouse.up();
    
    // Create another rectangle
    const rect2X = boundingBox.x + 300;
    const rect2Y = boundingBox.y + 100;
    
    await page.mouse.move(rect2X, rect2Y);
    await page.mouse.down();
    await page.mouse.move(rect2X + 100, rect2Y + 100);
    await page.mouse.up();
    
    // Switch to select tool
    await page.click('#tool-select');
    
    // Select both rectangles
    await page.mouse.move(rect1X + 50, rect1Y + 50);
    await page.mouse.down();
    await page.mouse.up();
    
    await page.keyboard.down('Shift');
    await page.mouse.move(rect2X + 50, rect2Y + 50);
    await page.mouse.down();
    await page.mouse.up();
    await page.keyboard.up('Shift');
    
    // Verify that both rectangles are selected
    const selectedRectangles = page.locator('rect.selected');
    expect(await selectedRectangles.count()).toBe(2);
    
    // Group the rectangles
    await page.keyboard.press('Control+G');
    
    // Verify that a group was created
    const group = page.locator('g.group');
    await expect(group).toBeVisible();
    
    // Verify that the group contains both rectangles
    const groupedRectangles = group.locator('rect');
    expect(await groupedRectangles.count()).toBe(2);
    
    // Ungroup the rectangles
    await page.keyboard.press('Control+Shift+G');
    
    // Verify that the group was removed
    await expect(group).not.toBeVisible();
    
    // Verify that both rectangles are still there
    const rectangles = page.locator('rect');
    expect(await rectangles.count()).toBe(2);
  });

  /**
   * Tests the shape alignment workflow.
   * Verifies that users can align shapes using the alignment tools.
   */
  test('Align shapes using alignment tools', async ({ page }) => {
    // Create a rectangle
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const rect1X = boundingBox.x + 100;
    const rect1Y = boundingBox.y + 100;
    
    await page.mouse.move(rect1X, rect1Y);
    await page.mouse.down();
    await page.mouse.move(rect1X + 100, rect1Y + 100);
    await page.mouse.up();
    
    // Create another rectangle at a different position
    const rect2X = boundingBox.x + 300;
    const rect2Y = boundingBox.y + 200;
    
    await page.mouse.move(rect2X, rect2Y);
    await page.mouse.down();
    await page.mouse.move(rect2X + 100, rect2Y + 100);
    await page.mouse.up();
    
    // Switch to select tool
    await page.click('#tool-select');
    
    // Select both rectangles
    await page.mouse.move(rect1X + 50, rect1Y + 50);
    await page.mouse.down();
    await page.mouse.up();
    
    await page.keyboard.down('Shift');
    await page.mouse.move(rect2X + 50, rect2Y + 50);
    await page.mouse.down();
    await page.mouse.up();
    await page.keyboard.up('Shift');
    
    // Get the initial positions of the rectangles
    const initialRect1 = await page.locator('rect').nth(0).boundingBox();
    const initialRect2 = await page.locator('rect').nth(1).boundingBox();
    
    // Align the rectangles horizontally
    await page.click('#align-horizontal-center');
    
    // Get the new positions of the rectangles
    const alignedRect1 = await page.locator('rect').nth(0).boundingBox();
    const alignedRect2 = await page.locator('rect').nth(1).boundingBox();
    
    // Verify that the rectangles are aligned horizontally
    expect(alignedRect1.y + alignedRect1.height / 2).toBeCloseTo(alignedRect2.y + alignedRect2.height / 2, 0);
    
    // Verify that the x positions are unchanged
    expect(alignedRect1.x).toBeCloseTo(initialRect1.x, 0);
    expect(alignedRect2.x).toBeCloseTo(initialRect2.x, 0);
  });

  /**
   * Tests the shape distribution workflow.
   * Verifies that users can distribute shapes evenly.
   */
  test('Distribute shapes evenly', async ({ page }) => {
    // Create three rectangles
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    // First rectangle
    const rect1X = boundingBox.x + 100;
    const rect1Y = boundingBox.y + 100;
    
    await page.mouse.move(rect1X, rect1Y);
    await page.mouse.down();
    await page.mouse.move(rect1X + 100, rect1Y + 100);
    await page.mouse.up();
    
    // Second rectangle
    const rect2X = boundingBox.x + 300;
    const rect2Y = boundingBox.y + 100;
    
    await page.mouse.move(rect2X, rect2Y);
    await page.mouse.down();
    await page.mouse.move(rect2X + 100, rect2Y + 100);
    await page.mouse.up();
    
    // Third rectangle
    const rect3X = boundingBox.x + 600;
    const rect3Y = boundingBox.y + 100;
    
    await page.mouse.move(rect3X, rect3Y);
    await page.mouse.down();
    await page.mouse.move(rect3X + 100, rect3Y + 100);
    await page.mouse.up();
    
    // Switch to select tool
    await page.click('#tool-select');
    
    // Select all three rectangles
    await page.mouse.move(rect1X + 50, rect1Y + 50);
    await page.mouse.down();
    await page.mouse.up();
    
    await page.keyboard.down('Shift');
    await page.mouse.move(rect2X + 50, rect2Y + 50);
    await page.mouse.down();
    await page.mouse.up();
    
    await page.mouse.move(rect3X + 50, rect3Y + 50);
    await page.mouse.down();
    await page.mouse.up();
    await page.keyboard.up('Shift');
    
    // Get the initial positions of the rectangles
    const initialRect1 = await page.locator('rect').nth(0).boundingBox();
    const initialRect2 = await page.locator('rect').nth(1).boundingBox();
    const initialRect3 = await page.locator('rect').nth(2).boundingBox();
    
    // Distribute the rectangles horizontally
    await page.click('#distribute-horizontal');
    
    // Get the new positions of the rectangles
    const distributedRect1 = await page.locator('rect').nth(0).boundingBox();
    const distributedRect2 = await page.locator('rect').nth(1).boundingBox();
    const distributedRect3 = await page.locator('rect').nth(2).boundingBox();
    
    // Verify that the first and last rectangles are at their original positions
    expect(distributedRect1.x).toBeCloseTo(initialRect1.x, 0);
    expect(distributedRect3.x).toBeCloseTo(initialRect3.x, 0);
    
    // Verify that the middle rectangle is evenly distributed
    const expectedMiddleX = (initialRect1.x + initialRect3.x) / 2;
    expect(distributedRect2.x).toBeCloseTo(expectedMiddleX, 0);
  });
});
