
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/drawer/BottomAssetDrawer.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">src/components/drawer</a> BottomAssetDrawer.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/733</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/733</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a>
<a name='L618'></a><a href='#L618'>618</a>
<a name='L619'></a><a href='#L619'>619</a>
<a name='L620'></a><a href='#L620'>620</a>
<a name='L621'></a><a href='#L621'>621</a>
<a name='L622'></a><a href='#L622'>622</a>
<a name='L623'></a><a href='#L623'>623</a>
<a name='L624'></a><a href='#L624'>624</a>
<a name='L625'></a><a href='#L625'>625</a>
<a name='L626'></a><a href='#L626'>626</a>
<a name='L627'></a><a href='#L627'>627</a>
<a name='L628'></a><a href='#L628'>628</a>
<a name='L629'></a><a href='#L629'>629</a>
<a name='L630'></a><a href='#L630'>630</a>
<a name='L631'></a><a href='#L631'>631</a>
<a name='L632'></a><a href='#L632'>632</a>
<a name='L633'></a><a href='#L633'>633</a>
<a name='L634'></a><a href='#L634'>634</a>
<a name='L635'></a><a href='#L635'>635</a>
<a name='L636'></a><a href='#L636'>636</a>
<a name='L637'></a><a href='#L637'>637</a>
<a name='L638'></a><a href='#L638'>638</a>
<a name='L639'></a><a href='#L639'>639</a>
<a name='L640'></a><a href='#L640'>640</a>
<a name='L641'></a><a href='#L641'>641</a>
<a name='L642'></a><a href='#L642'>642</a>
<a name='L643'></a><a href='#L643'>643</a>
<a name='L644'></a><a href='#L644'>644</a>
<a name='L645'></a><a href='#L645'>645</a>
<a name='L646'></a><a href='#L646'>646</a>
<a name='L647'></a><a href='#L647'>647</a>
<a name='L648'></a><a href='#L648'>648</a>
<a name='L649'></a><a href='#L649'>649</a>
<a name='L650'></a><a href='#L650'>650</a>
<a name='L651'></a><a href='#L651'>651</a>
<a name='L652'></a><a href='#L652'>652</a>
<a name='L653'></a><a href='#L653'>653</a>
<a name='L654'></a><a href='#L654'>654</a>
<a name='L655'></a><a href='#L655'>655</a>
<a name='L656'></a><a href='#L656'>656</a>
<a name='L657'></a><a href='#L657'>657</a>
<a name='L658'></a><a href='#L658'>658</a>
<a name='L659'></a><a href='#L659'>659</a>
<a name='L660'></a><a href='#L660'>660</a>
<a name='L661'></a><a href='#L661'>661</a>
<a name='L662'></a><a href='#L662'>662</a>
<a name='L663'></a><a href='#L663'>663</a>
<a name='L664'></a><a href='#L664'>664</a>
<a name='L665'></a><a href='#L665'>665</a>
<a name='L666'></a><a href='#L666'>666</a>
<a name='L667'></a><a href='#L667'>667</a>
<a name='L668'></a><a href='#L668'>668</a>
<a name='L669'></a><a href='#L669'>669</a>
<a name='L670'></a><a href='#L670'>670</a>
<a name='L671'></a><a href='#L671'>671</a>
<a name='L672'></a><a href='#L672'>672</a>
<a name='L673'></a><a href='#L673'>673</a>
<a name='L674'></a><a href='#L674'>674</a>
<a name='L675'></a><a href='#L675'>675</a>
<a name='L676'></a><a href='#L676'>676</a>
<a name='L677'></a><a href='#L677'>677</a>
<a name='L678'></a><a href='#L678'>678</a>
<a name='L679'></a><a href='#L679'>679</a>
<a name='L680'></a><a href='#L680'>680</a>
<a name='L681'></a><a href='#L681'>681</a>
<a name='L682'></a><a href='#L682'>682</a>
<a name='L683'></a><a href='#L683'>683</a>
<a name='L684'></a><a href='#L684'>684</a>
<a name='L685'></a><a href='#L685'>685</a>
<a name='L686'></a><a href='#L686'>686</a>
<a name='L687'></a><a href='#L687'>687</a>
<a name='L688'></a><a href='#L688'>688</a>
<a name='L689'></a><a href='#L689'>689</a>
<a name='L690'></a><a href='#L690'>690</a>
<a name='L691'></a><a href='#L691'>691</a>
<a name='L692'></a><a href='#L692'>692</a>
<a name='L693'></a><a href='#L693'>693</a>
<a name='L694'></a><a href='#L694'>694</a>
<a name='L695'></a><a href='#L695'>695</a>
<a name='L696'></a><a href='#L696'>696</a>
<a name='L697'></a><a href='#L697'>697</a>
<a name='L698'></a><a href='#L698'>698</a>
<a name='L699'></a><a href='#L699'>699</a>
<a name='L700'></a><a href='#L700'>700</a>
<a name='L701'></a><a href='#L701'>701</a>
<a name='L702'></a><a href='#L702'>702</a>
<a name='L703'></a><a href='#L703'>703</a>
<a name='L704'></a><a href='#L704'>704</a>
<a name='L705'></a><a href='#L705'>705</a>
<a name='L706'></a><a href='#L706'>706</a>
<a name='L707'></a><a href='#L707'>707</a>
<a name='L708'></a><a href='#L708'>708</a>
<a name='L709'></a><a href='#L709'>709</a>
<a name='L710'></a><a href='#L710'>710</a>
<a name='L711'></a><a href='#L711'>711</a>
<a name='L712'></a><a href='#L712'>712</a>
<a name='L713'></a><a href='#L713'>713</a>
<a name='L714'></a><a href='#L714'>714</a>
<a name='L715'></a><a href='#L715'>715</a>
<a name='L716'></a><a href='#L716'>716</a>
<a name='L717'></a><a href='#L717'>717</a>
<a name='L718'></a><a href='#L718'>718</a>
<a name='L719'></a><a href='#L719'>719</a>
<a name='L720'></a><a href='#L720'>720</a>
<a name='L721'></a><a href='#L721'>721</a>
<a name='L722'></a><a href='#L722'>722</a>
<a name='L723'></a><a href='#L723'>723</a>
<a name='L724'></a><a href='#L724'>724</a>
<a name='L725'></a><a href='#L725'>725</a>
<a name='L726'></a><a href='#L726'>726</a>
<a name='L727'></a><a href='#L727'>727</a>
<a name='L728'></a><a href='#L728'>728</a>
<a name='L729'></a><a href='#L729'>729</a>
<a name='L730'></a><a href='#L730'>730</a>
<a name='L731'></a><a href='#L731'>731</a>
<a name='L732'></a><a href='#L732'>732</a>
<a name='L733'></a><a href='#L733'>733</a>
<a name='L734'></a><a href='#L734'>734</a>
<a name='L735'></a><a href='#L735'>735</a>
<a name='L736'></a><a href='#L736'>736</a>
<a name='L737'></a><a href='#L737'>737</a>
<a name='L738'></a><a href='#L738'>738</a>
<a name='L739'></a><a href='#L739'>739</a>
<a name='L740'></a><a href='#L740'>740</a>
<a name='L741'></a><a href='#L741'>741</a>
<a name='L742'></a><a href='#L742'>742</a>
<a name='L743'></a><a href='#L743'>743</a>
<a name='L744'></a><a href='#L744'>744</a>
<a name='L745'></a><a href='#L745'>745</a>
<a name='L746'></a><a href='#L746'>746</a>
<a name='L747'></a><a href='#L747'>747</a>
<a name='L748'></a><a href='#L748'>748</a>
<a name='L749'></a><a href='#L749'>749</a>
<a name='L750'></a><a href='#L750'>750</a>
<a name='L751'></a><a href='#L751'>751</a>
<a name='L752'></a><a href='#L752'>752</a>
<a name='L753'></a><a href='#L753'>753</a>
<a name='L754'></a><a href='#L754'>754</a>
<a name='L755'></a><a href='#L755'>755</a>
<a name='L756'></a><a href='#L756'>756</a>
<a name='L757'></a><a href='#L757'>757</a>
<a name='L758'></a><a href='#L758'>758</a>
<a name='L759'></a><a href='#L759'>759</a>
<a name='L760'></a><a href='#L760'>760</a>
<a name='L761'></a><a href='#L761'>761</a>
<a name='L762'></a><a href='#L762'>762</a>
<a name='L763'></a><a href='#L763'>763</a>
<a name='L764'></a><a href='#L764'>764</a>
<a name='L765'></a><a href='#L765'>765</a>
<a name='L766'></a><a href='#L766'>766</a>
<a name='L767'></a><a href='#L767'>767</a>
<a name='L768'></a><a href='#L768'>768</a>
<a name='L769'></a><a href='#L769'>769</a>
<a name='L770'></a><a href='#L770'>770</a>
<a name='L771'></a><a href='#L771'>771</a>
<a name='L772'></a><a href='#L772'>772</a>
<a name='L773'></a><a href='#L773'>773</a>
<a name='L774'></a><a href='#L774'>774</a>
<a name='L775'></a><a href='#L775'>775</a>
<a name='L776'></a><a href='#L776'>776</a>
<a name='L777'></a><a href='#L777'>777</a>
<a name='L778'></a><a href='#L778'>778</a>
<a name='L779'></a><a href='#L779'>779</a>
<a name='L780'></a><a href='#L780'>780</a>
<a name='L781'></a><a href='#L781'>781</a>
<a name='L782'></a><a href='#L782'>782</a>
<a name='L783'></a><a href='#L783'>783</a>
<a name='L784'></a><a href='#L784'>784</a>
<a name='L785'></a><a href='#L785'>785</a>
<a name='L786'></a><a href='#L786'>786</a>
<a name='L787'></a><a href='#L787'>787</a>
<a name='L788'></a><a href='#L788'>788</a>
<a name='L789'></a><a href='#L789'>789</a>
<a name='L790'></a><a href='#L790'>790</a>
<a name='L791'></a><a href='#L791'>791</a>
<a name='L792'></a><a href='#L792'>792</a>
<a name='L793'></a><a href='#L793'>793</a>
<a name='L794'></a><a href='#L794'>794</a>
<a name='L795'></a><a href='#L795'>795</a>
<a name='L796'></a><a href='#L796'>796</a>
<a name='L797'></a><a href='#L797'>797</a>
<a name='L798'></a><a href='#L798'>798</a>
<a name='L799'></a><a href='#L799'>799</a>
<a name='L800'></a><a href='#L800'>800</a>
<a name='L801'></a><a href='#L801'>801</a>
<a name='L802'></a><a href='#L802'>802</a>
<a name='L803'></a><a href='#L803'>803</a>
<a name='L804'></a><a href='#L804'>804</a>
<a name='L805'></a><a href='#L805'>805</a>
<a name='L806'></a><a href='#L806'>806</a>
<a name='L807'></a><a href='#L807'>807</a>
<a name='L808'></a><a href='#L808'>808</a>
<a name='L809'></a><a href='#L809'>809</a>
<a name='L810'></a><a href='#L810'>810</a>
<a name='L811'></a><a href='#L811'>811</a>
<a name='L812'></a><a href='#L812'>812</a>
<a name='L813'></a><a href='#L813'>813</a>
<a name='L814'></a><a href='#L814'>814</a>
<a name='L815'></a><a href='#L815'>815</a>
<a name='L816'></a><a href='#L816'>816</a>
<a name='L817'></a><a href='#L817'>817</a>
<a name='L818'></a><a href='#L818'>818</a>
<a name='L819'></a><a href='#L819'>819</a>
<a name='L820'></a><a href='#L820'>820</a>
<a name='L821'></a><a href='#L821'>821</a>
<a name='L822'></a><a href='#L822'>822</a>
<a name='L823'></a><a href='#L823'>823</a>
<a name='L824'></a><a href='#L824'>824</a>
<a name='L825'></a><a href='#L825'>825</a>
<a name='L826'></a><a href='#L826'>826</a>
<a name='L827'></a><a href='#L827'>827</a>
<a name='L828'></a><a href='#L828'>828</a>
<a name='L829'></a><a href='#L829'>829</a>
<a name='L830'></a><a href='#L830'>830</a>
<a name='L831'></a><a href='#L831'>831</a>
<a name='L832'></a><a href='#L832'>832</a>
<a name='L833'></a><a href='#L833'>833</a>
<a name='L834'></a><a href='#L834'>834</a>
<a name='L835'></a><a href='#L835'>835</a>
<a name='L836'></a><a href='#L836'>836</a>
<a name='L837'></a><a href='#L837'>837</a>
<a name='L838'></a><a href='#L838'>838</a>
<a name='L839'></a><a href='#L839'>839</a>
<a name='L840'></a><a href='#L840'>840</a>
<a name='L841'></a><a href='#L841'>841</a>
<a name='L842'></a><a href='#L842'>842</a>
<a name='L843'></a><a href='#L843'>843</a>
<a name='L844'></a><a href='#L844'>844</a>
<a name='L845'></a><a href='#L845'>845</a>
<a name='L846'></a><a href='#L846'>846</a>
<a name='L847'></a><a href='#L847'>847</a>
<a name='L848'></a><a href='#L848'>848</a>
<a name='L849'></a><a href='#L849'>849</a>
<a name='L850'></a><a href='#L850'>850</a>
<a name='L851'></a><a href='#L851'>851</a>
<a name='L852'></a><a href='#L852'>852</a>
<a name='L853'></a><a href='#L853'>853</a>
<a name='L854'></a><a href='#L854'>854</a>
<a name='L855'></a><a href='#L855'>855</a>
<a name='L856'></a><a href='#L856'>856</a>
<a name='L857'></a><a href='#L857'>857</a>
<a name='L858'></a><a href='#L858'>858</a>
<a name='L859'></a><a href='#L859'>859</a>
<a name='L860'></a><a href='#L860'>860</a>
<a name='L861'></a><a href='#L861'>861</a>
<a name='L862'></a><a href='#L862'>862</a>
<a name='L863'></a><a href='#L863'>863</a>
<a name='L864'></a><a href='#L864'>864</a>
<a name='L865'></a><a href='#L865'>865</a>
<a name='L866'></a><a href='#L866'>866</a>
<a name='L867'></a><a href='#L867'>867</a>
<a name='L868'></a><a href='#L868'>868</a>
<a name='L869'></a><a href='#L869'>869</a>
<a name='L870'></a><a href='#L870'>870</a>
<a name='L871'></a><a href='#L871'>871</a>
<a name='L872'></a><a href='#L872'>872</a>
<a name='L873'></a><a href='#L873'>873</a>
<a name='L874'></a><a href='#L874'>874</a>
<a name='L875'></a><a href='#L875'>875</a>
<a name='L876'></a><a href='#L876'>876</a>
<a name='L877'></a><a href='#L877'>877</a>
<a name='L878'></a><a href='#L878'>878</a>
<a name='L879'></a><a href='#L879'>879</a>
<a name='L880'></a><a href='#L880'>880</a>
<a name='L881'></a><a href='#L881'>881</a>
<a name='L882'></a><a href='#L882'>882</a>
<a name='L883'></a><a href='#L883'>883</a>
<a name='L884'></a><a href='#L884'>884</a>
<a name='L885'></a><a href='#L885'>885</a>
<a name='L886'></a><a href='#L886'>886</a>
<a name='L887'></a><a href='#L887'>887</a>
<a name='L888'></a><a href='#L888'>888</a>
<a name='L889'></a><a href='#L889'>889</a>
<a name='L890'></a><a href='#L890'>890</a>
<a name='L891'></a><a href='#L891'>891</a>
<a name='L892'></a><a href='#L892'>892</a>
<a name='L893'></a><a href='#L893'>893</a>
<a name='L894'></a><a href='#L894'>894</a>
<a name='L895'></a><a href='#L895'>895</a>
<a name='L896'></a><a href='#L896'>896</a>
<a name='L897'></a><a href='#L897'>897</a>
<a name='L898'></a><a href='#L898'>898</a>
<a name='L899'></a><a href='#L899'>899</a>
<a name='L900'></a><a href='#L900'>900</a>
<a name='L901'></a><a href='#L901'>901</a>
<a name='L902'></a><a href='#L902'>902</a>
<a name='L903'></a><a href='#L903'>903</a>
<a name='L904'></a><a href='#L904'>904</a>
<a name='L905'></a><a href='#L905'>905</a>
<a name='L906'></a><a href='#L906'>906</a>
<a name='L907'></a><a href='#L907'>907</a>
<a name='L908'></a><a href='#L908'>908</a>
<a name='L909'></a><a href='#L909'>909</a>
<a name='L910'></a><a href='#L910'>910</a>
<a name='L911'></a><a href='#L911'>911</a>
<a name='L912'></a><a href='#L912'>912</a>
<a name='L913'></a><a href='#L913'>913</a>
<a name='L914'></a><a href='#L914'>914</a>
<a name='L915'></a><a href='#L915'>915</a>
<a name='L916'></a><a href='#L916'>916</a>
<a name='L917'></a><a href='#L917'>917</a>
<a name='L918'></a><a href='#L918'>918</a>
<a name='L919'></a><a href='#L919'>919</a>
<a name='L920'></a><a href='#L920'>920</a>
<a name='L921'></a><a href='#L921'>921</a>
<a name='L922'></a><a href='#L922'>922</a>
<a name='L923'></a><a href='#L923'>923</a>
<a name='L924'></a><a href='#L924'>924</a>
<a name='L925'></a><a href='#L925'>925</a>
<a name='L926'></a><a href='#L926'>926</a>
<a name='L927'></a><a href='#L927'>927</a>
<a name='L928'></a><a href='#L928'>928</a>
<a name='L929'></a><a href='#L929'>929</a>
<a name='L930'></a><a href='#L930'>930</a>
<a name='L931'></a><a href='#L931'>931</a>
<a name='L932'></a><a href='#L932'>932</a>
<a name='L933'></a><a href='#L933'>933</a>
<a name='L934'></a><a href='#L934'>934</a>
<a name='L935'></a><a href='#L935'>935</a>
<a name='L936'></a><a href='#L936'>936</a>
<a name='L937'></a><a href='#L937'>937</a>
<a name='L938'></a><a href='#L938'>938</a>
<a name='L939'></a><a href='#L939'>939</a>
<a name='L940'></a><a href='#L940'>940</a>
<a name='L941'></a><a href='#L941'>941</a>
<a name='L942'></a><a href='#L942'>942</a>
<a name='L943'></a><a href='#L943'>943</a>
<a name='L944'></a><a href='#L944'>944</a>
<a name='L945'></a><a href='#L945'>945</a>
<a name='L946'></a><a href='#L946'>946</a>
<a name='L947'></a><a href='#L947'>947</a>
<a name='L948'></a><a href='#L948'>948</a>
<a name='L949'></a><a href='#L949'>949</a>
<a name='L950'></a><a href='#L950'>950</a>
<a name='L951'></a><a href='#L951'>951</a>
<a name='L952'></a><a href='#L952'>952</a>
<a name='L953'></a><a href='#L953'>953</a>
<a name='L954'></a><a href='#L954'>954</a>
<a name='L955'></a><a href='#L955'>955</a>
<a name='L956'></a><a href='#L956'>956</a>
<a name='L957'></a><a href='#L957'>957</a>
<a name='L958'></a><a href='#L958'>958</a>
<a name='L959'></a><a href='#L959'>959</a>
<a name='L960'></a><a href='#L960'>960</a>
<a name='L961'></a><a href='#L961'>961</a>
<a name='L962'></a><a href='#L962'>962</a>
<a name='L963'></a><a href='#L963'>963</a>
<a name='L964'></a><a href='#L964'>964</a>
<a name='L965'></a><a href='#L965'>965</a>
<a name='L966'></a><a href='#L966'>966</a>
<a name='L967'></a><a href='#L967'>967</a>
<a name='L968'></a><a href='#L968'>968</a>
<a name='L969'></a><a href='#L969'>969</a>
<a name='L970'></a><a href='#L970'>970</a>
<a name='L971'></a><a href='#L971'>971</a>
<a name='L972'></a><a href='#L972'>972</a>
<a name='L973'></a><a href='#L973'>973</a>
<a name='L974'></a><a href='#L974'>974</a>
<a name='L975'></a><a href='#L975'>975</a>
<a name='L976'></a><a href='#L976'>976</a>
<a name='L977'></a><a href='#L977'>977</a>
<a name='L978'></a><a href='#L978'>978</a>
<a name='L979'></a><a href='#L979'>979</a>
<a name='L980'></a><a href='#L980'>980</a>
<a name='L981'></a><a href='#L981'>981</a>
<a name='L982'></a><a href='#L982'>982</a>
<a name='L983'></a><a href='#L983'>983</a>
<a name='L984'></a><a href='#L984'>984</a>
<a name='L985'></a><a href='#L985'>985</a>
<a name='L986'></a><a href='#L986'>986</a>
<a name='L987'></a><a href='#L987'>987</a>
<a name='L988'></a><a href='#L988'>988</a>
<a name='L989'></a><a href='#L989'>989</a>
<a name='L990'></a><a href='#L990'>990</a>
<a name='L991'></a><a href='#L991'>991</a>
<a name='L992'></a><a href='#L992'>992</a>
<a name='L993'></a><a href='#L993'>993</a>
<a name='L994'></a><a href='#L994'>994</a>
<a name='L995'></a><a href='#L995'>995</a>
<a name='L996'></a><a href='#L996'>996</a>
<a name='L997'></a><a href='#L997'>997</a>
<a name='L998'></a><a href='#L998'>998</a>
<a name='L999'></a><a href='#L999'>999</a>
<a name='L1000'></a><a href='#L1000'>1000</a>
<a name='L1001'></a><a href='#L1001'>1001</a>
<a name='L1002'></a><a href='#L1002'>1002</a>
<a name='L1003'></a><a href='#L1003'>1003</a>
<a name='L1004'></a><a href='#L1004'>1004</a>
<a name='L1005'></a><a href='#L1005'>1005</a>
<a name='L1006'></a><a href='#L1006'>1006</a>
<a name='L1007'></a><a href='#L1007'>1007</a>
<a name='L1008'></a><a href='#L1008'>1008</a>
<a name='L1009'></a><a href='#L1009'>1009</a>
<a name='L1010'></a><a href='#L1010'>1010</a>
<a name='L1011'></a><a href='#L1011'>1011</a>
<a name='L1012'></a><a href='#L1012'>1012</a>
<a name='L1013'></a><a href='#L1013'>1013</a>
<a name='L1014'></a><a href='#L1014'>1014</a>
<a name='L1015'></a><a href='#L1015'>1015</a>
<a name='L1016'></a><a href='#L1016'>1016</a>
<a name='L1017'></a><a href='#L1017'>1017</a>
<a name='L1018'></a><a href='#L1018'>1018</a>
<a name='L1019'></a><a href='#L1019'>1019</a>
<a name='L1020'></a><a href='#L1020'>1020</a>
<a name='L1021'></a><a href='#L1021'>1021</a>
<a name='L1022'></a><a href='#L1022'>1022</a>
<a name='L1023'></a><a href='#L1023'>1023</a>
<a name='L1024'></a><a href='#L1024'>1024</a>
<a name='L1025'></a><a href='#L1025'>1025</a>
<a name='L1026'></a><a href='#L1026'>1026</a>
<a name='L1027'></a><a href='#L1027'>1027</a>
<a name='L1028'></a><a href='#L1028'>1028</a>
<a name='L1029'></a><a href='#L1029'>1029</a>
<a name='L1030'></a><a href='#L1030'>1030</a>
<a name='L1031'></a><a href='#L1031'>1031</a>
<a name='L1032'></a><a href='#L1032'>1032</a>
<a name='L1033'></a><a href='#L1033'>1033</a>
<a name='L1034'></a><a href='#L1034'>1034</a>
<a name='L1035'></a><a href='#L1035'>1035</a>
<a name='L1036'></a><a href='#L1036'>1036</a>
<a name='L1037'></a><a href='#L1037'>1037</a>
<a name='L1038'></a><a href='#L1038'>1038</a>
<a name='L1039'></a><a href='#L1039'>1039</a>
<a name='L1040'></a><a href='#L1040'>1040</a>
<a name='L1041'></a><a href='#L1041'>1041</a>
<a name='L1042'></a><a href='#L1042'>1042</a>
<a name='L1043'></a><a href='#L1043'>1043</a>
<a name='L1044'></a><a href='#L1044'>1044</a>
<a name='L1045'></a><a href='#L1045'>1045</a>
<a name='L1046'></a><a href='#L1046'>1046</a>
<a name='L1047'></a><a href='#L1047'>1047</a>
<a name='L1048'></a><a href='#L1048'>1048</a>
<a name='L1049'></a><a href='#L1049'>1049</a>
<a name='L1050'></a><a href='#L1050'>1050</a>
<a name='L1051'></a><a href='#L1051'>1051</a>
<a name='L1052'></a><a href='#L1052'>1052</a>
<a name='L1053'></a><a href='#L1053'>1053</a>
<a name='L1054'></a><a href='#L1054'>1054</a>
<a name='L1055'></a><a href='#L1055'>1055</a>
<a name='L1056'></a><a href='#L1056'>1056</a>
<a name='L1057'></a><a href='#L1057'>1057</a>
<a name='L1058'></a><a href='#L1058'>1058</a>
<a name='L1059'></a><a href='#L1059'>1059</a>
<a name='L1060'></a><a href='#L1060'>1060</a>
<a name='L1061'></a><a href='#L1061'>1061</a>
<a name='L1062'></a><a href='#L1062'>1062</a>
<a name='L1063'></a><a href='#L1063'>1063</a>
<a name='L1064'></a><a href='#L1064'>1064</a>
<a name='L1065'></a><a href='#L1065'>1065</a>
<a name='L1066'></a><a href='#L1066'>1066</a>
<a name='L1067'></a><a href='#L1067'>1067</a>
<a name='L1068'></a><a href='#L1068'>1068</a>
<a name='L1069'></a><a href='#L1069'>1069</a>
<a name='L1070'></a><a href='#L1070'>1070</a>
<a name='L1071'></a><a href='#L1071'>1071</a>
<a name='L1072'></a><a href='#L1072'>1072</a>
<a name='L1073'></a><a href='#L1073'>1073</a>
<a name='L1074'></a><a href='#L1074'>1074</a>
<a name='L1075'></a><a href='#L1075'>1075</a>
<a name='L1076'></a><a href='#L1076'>1076</a>
<a name='L1077'></a><a href='#L1077'>1077</a>
<a name='L1078'></a><a href='#L1078'>1078</a>
<a name='L1079'></a><a href='#L1079'>1079</a>
<a name='L1080'></a><a href='#L1080'>1080</a>
<a name='L1081'></a><a href='#L1081'>1081</a>
<a name='L1082'></a><a href='#L1082'>1082</a>
<a name='L1083'></a><a href='#L1083'>1083</a>
<a name='L1084'></a><a href='#L1084'>1084</a>
<a name='L1085'></a><a href='#L1085'>1085</a>
<a name='L1086'></a><a href='#L1086'>1086</a>
<a name='L1087'></a><a href='#L1087'>1087</a>
<a name='L1088'></a><a href='#L1088'>1088</a>
<a name='L1089'></a><a href='#L1089'>1089</a>
<a name='L1090'></a><a href='#L1090'>1090</a>
<a name='L1091'></a><a href='#L1091'>1091</a>
<a name='L1092'></a><a href='#L1092'>1092</a>
<a name='L1093'></a><a href='#L1093'>1093</a>
<a name='L1094'></a><a href='#L1094'>1094</a>
<a name='L1095'></a><a href='#L1095'>1095</a>
<a name='L1096'></a><a href='#L1096'>1096</a>
<a name='L1097'></a><a href='#L1097'>1097</a>
<a name='L1098'></a><a href='#L1098'>1098</a>
<a name='L1099'></a><a href='#L1099'>1099</a>
<a name='L1100'></a><a href='#L1100'>1100</a>
<a name='L1101'></a><a href='#L1101'>1101</a>
<a name='L1102'></a><a href='#L1102'>1102</a>
<a name='L1103'></a><a href='#L1103'>1103</a>
<a name='L1104'></a><a href='#L1104'>1104</a>
<a name='L1105'></a><a href='#L1105'>1105</a>
<a name='L1106'></a><a href='#L1106'>1106</a>
<a name='L1107'></a><a href='#L1107'>1107</a>
<a name='L1108'></a><a href='#L1108'>1108</a>
<a name='L1109'></a><a href='#L1109'>1109</a>
<a name='L1110'></a><a href='#L1110'>1110</a>
<a name='L1111'></a><a href='#L1111'>1111</a>
<a name='L1112'></a><a href='#L1112'>1112</a>
<a name='L1113'></a><a href='#L1113'>1113</a>
<a name='L1114'></a><a href='#L1114'>1114</a>
<a name='L1115'></a><a href='#L1115'>1115</a>
<a name='L1116'></a><a href='#L1116'>1116</a>
<a name='L1117'></a><a href='#L1117'>1117</a>
<a name='L1118'></a><a href='#L1118'>1118</a>
<a name='L1119'></a><a href='#L1119'>1119</a>
<a name='L1120'></a><a href='#L1120'>1120</a>
<a name='L1121'></a><a href='#L1121'>1121</a>
<a name='L1122'></a><a href='#L1122'>1122</a>
<a name='L1123'></a><a href='#L1123'>1123</a>
<a name='L1124'></a><a href='#L1124'>1124</a>
<a name='L1125'></a><a href='#L1125'>1125</a>
<a name='L1126'></a><a href='#L1126'>1126</a>
<a name='L1127'></a><a href='#L1127'>1127</a>
<a name='L1128'></a><a href='#L1128'>1128</a>
<a name='L1129'></a><a href='#L1129'>1129</a>
<a name='L1130'></a><a href='#L1130'>1130</a>
<a name='L1131'></a><a href='#L1131'>1131</a>
<a name='L1132'></a><a href='#L1132'>1132</a>
<a name='L1133'></a><a href='#L1133'>1133</a>
<a name='L1134'></a><a href='#L1134'>1134</a>
<a name='L1135'></a><a href='#L1135'>1135</a>
<a name='L1136'></a><a href='#L1136'>1136</a>
<a name='L1137'></a><a href='#L1137'>1137</a>
<a name='L1138'></a><a href='#L1138'>1138</a>
<a name='L1139'></a><a href='#L1139'>1139</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * Bottom Asset Drawer Component
 *
 * A comprehensive asset management drawer positioned at the bottom of the screen
 * that provides quick access to design elements and drawing tools. The component
 * features both collapsed and expanded states for optimal workflow efficiency.
 *
 * Features:
 * - Collapsed state: Quick access to pinned assets and common tools
 * - Expanded state: Full asset library with categorized browsing
 * - Drag-and-drop functionality for asset placement
 * - Click-to-activate for drawing tools
 * - Context-aware asset filtering based on current module/step
 * - Asset pinning system for personalized quick access
 * - Real-time asset state management and event handling
 * - Responsive design with tooltips and visual feedback
 *
 * States:
 * - Collapsed: Shows up to 10 pinned draggable assets and 10 drawing tools
 * - Expanded: Full categorized asset library with search and filtering
 *
 * Asset Types:
 * - Draggable assets: Shapes, furniture, fixtures that can be placed on canvas
 * - Drawing tools: Line, arc, curve tools that activate drawing modes
 * - Special elements: Context-specific assets based on current workflow step
 *
 * @example
 * ```tsx
 * &lt;BottomAssetDrawer
 *   onAssetDragStart={handleDragStart}
 *   getSettingsForType={getDefaults}
 *   currentModuleId="FURNITURE"
 *   currentStepId="SEATING"
 *   onAssetSelectFromExpanded={handleToolSelect}
 *   onTogglePinAsset={handlePin}
 *   isAssetPinned={checkPinned}
 *   sheetOpen={isExpanded}
 *   onSheetOpenChange={setExpanded}
 *   pinnedAssets={userPinnedAssets}
 * /&gt;
 * ```
 */
&nbsp;
import type { InitialElementProperties } from '@/config/defaultElementSettings'
import type { DesignElement } from '@/hooks/useSpecialDrawerAssets'
import type { PinnedAsset } from '@/types/core/assetTypes'
import type { MajorCategory, MinorCategory } from '@/types/core/majorMinorTypes'
&nbsp;
<span class="cstat-no" title="statement not covered" >import {</span>
  ChartNoAxesGantt,
  ChevronUp,
  Circle,
  HelpCircle,
  Hexagon,
  Image as ImageIcon,
  LoaderCircle,
  Minus,
  Pentagon,
  Pin,
  PinOff,
  RectangleHorizontal,
  Spline,
  Square,
  Triangle,
  Type,
} from 'lucide-react'
<span class="cstat-no" title="statement not covered" >import React, { useCallback, useEffect, useMemo, useState } from 'react'</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >import { Button } from '@/components/ui/button'</span>
<span class="cstat-no" title="statement not covered" >import { ScrollArea } from '@/components/ui/scroll-area'</span>
<span class="cstat-no" title="statement not covered" >import { Sheet, SheetContent, SheetHeader, SheetOverlay, SheetPortal, SheetTitle, SheetTrigger } from '@/components/ui/sheet'</span>
<span class="cstat-no" title="statement not covered" >import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'</span>
<span class="cstat-no" title="statement not covered" >import {</span>
  BASIC_DRAGGABLE_ORDER_COLLAPSED,
  BASIC_DRAWING_TOOL_ORDER_COLLAPSED,
  basicPathsForExpandedConfig,
  basicShapesForExpandedConfig,
  DRAGGABLE_COLLAPSED_TYPES,
  DRAWING_TOOL_COLLAPSED_TYPES,
} from '@/config/assetConfig'
<span class="cstat-no" title="statement not covered" >import { useSpecialDrawerAssets } from '@/hooks/useSpecialDrawerAssets'</span>
<span class="cstat-no" title="statement not covered" >import { cn } from '@/lib/utils'</span>
<span class="cstat-no" title="statement not covered" >import { getIconForPredefinedElement } from '@/lib/utils/assetIconUtils'</span>
<span class="cstat-no" title="statement not covered" >import { eventBus, EventType } from '@/lib/utils/eventBus'</span>
<span class="cstat-no" title="statement not covered" >import { predefinedElements } from '@/types/core/element/definitions/predefinedElements'</span>
<span class="cstat-no" title="statement not covered" >import { ElementType as CoreElementType } from '@/types/core/elementDefinitions'</span>
&nbsp;
/**
 * Interface for individual assets within an asset group
 */
interface AssetInGroup {
  /** Unique identifier for the asset */
  id: string
  /** Display name of the asset */
  name: string
  /** React icon component for the asset */
  icon: React.ReactNode
  /** Element type from the core definitions */
  type: CoreElementType
  /** Whether this is a context-specific asset */
  isSpecific: boolean
  /** Additional data for predefined elements */
  predefinedElementData?: Record&lt;string, unknown&gt;
}
&nbsp;
/**
 * Interface for asset groups in the expanded view
 */
interface AssetGroup {
  /** Unique identifier for the group */
  id: string
  /** Display name of the group (category) */
  name: string
  /** Array of assets in this group */
  assets: AssetInGroup[]
}
&nbsp;
/**
 * Props for the BottomAssetDrawer component
 */
interface BottomAssetDrawerProps {
  /** Callback fired when an asset drag operation starts */
  onAssetDragStart: (
    elementType: CoreElementType,
    properties: InitialElementProperties,
    event: React.DragEvent&lt;HTMLButtonElement | HTMLDivElement&gt;
  ) =&gt; void
  /** Function to get default settings for an element type */
  getSettingsForType: (elementType: CoreElementType) =&gt; InitialElementProperties
&nbsp;
  /** Current major category (module) context */
  currentModuleId: MajorCategory | undefined
  /** Current minor category (step) context */
  currentStepId: MinorCategory | undefined
  /** Callback fired when a drawing tool is selected from expanded view */
  onAssetSelectFromExpanded: (elementType: CoreElementType) =&gt; void
&nbsp;
  /** Callback to toggle asset pin status */
  onTogglePinAsset: (elementType: CoreElementType, isSpecific: boolean, currentModuleId?: string, currentStepId?: string, assetName?: string) =&gt; void
  /** Function to check if an asset is currently pinned */
  isAssetPinned: (elementType: CoreElementType, isSpecific: boolean, currentModuleId?: string, currentStepId?: string, assetName?: string) =&gt; boolean
&nbsp;
  /** Whether the expanded sheet is open */
  sheetOpen: boolean
  /** Callback to control sheet open state */
  onSheetOpenChange: (open: boolean) =&gt; void
  /** Array of currently pinned assets */
  pinnedAssets: PinnedAsset[]
}
&nbsp;
/**
 * BottomAssetDrawer component that provides a comprehensive asset management interface.
 *
 * This component renders a bottom-positioned drawer with both collapsed and expanded states.
 * In collapsed state, it shows quick access to pinned assets. In expanded state, it provides
 * a full asset library with categorized browsing, search, and pinning functionality.
 *
 * The component manages context-aware asset filtering, drag-and-drop operations, and
 * drawing tool activation. It integrates with the pinning system to provide personalized
 * quick access to frequently used assets.
 *
 * @param props - The component props
 * @param props.onAssetDragStart - Callback fired when an asset drag operation starts
 * @param props.getSettingsForType - Function to get default settings for an element type
 * @param props.currentModuleId - Current major category (module) context
 * @param props.currentStepId - Current minor category (step) context
 * @param props.onAssetSelectFromExpanded - Callback fired when a drawing tool is selected from expanded view
 * @param props.onTogglePinAsset - Callback to toggle asset pin status
 * @param props.isAssetPinned - Function to check if an asset is currently pinned
 * @param props.sheetOpen - Whether the expanded sheet is open
 * @param props.onSheetOpenChange - Callback to control sheet open state
 * @param props.pinnedAssets - Array of currently pinned assets
 * @returns The rendered bottom asset drawer component
 */
<span class="cstat-no" title="statement not covered" >const BottomAssetDrawer: React.FC&lt;BottomAssetDrawerProps&gt; = ({</span>
<span class="cstat-no" title="statement not covered" >  onAssetDragStart,</span>
<span class="cstat-no" title="statement not covered" >  getSettingsForType,</span>
<span class="cstat-no" title="statement not covered" >  currentModuleId,</span>
<span class="cstat-no" title="statement not covered" >  currentStepId,</span>
<span class="cstat-no" title="statement not covered" >  onAssetSelectFromExpanded,</span>
<span class="cstat-no" title="statement not covered" >  onTogglePinAsset,</span>
<span class="cstat-no" title="statement not covered" >  isAssetPinned,</span>
<span class="cstat-no" title="statement not covered" >  sheetOpen,</span>
<span class="cstat-no" title="statement not covered" >  onSheetOpenChange,</span>
<span class="cstat-no" title="statement not covered" >  pinnedAssets,</span>
<span class="cstat-no" title="statement not covered" >}) =&gt; {</span>
  // 添加状态来跟踪当前选中的绘制工具
<span class="cstat-no" title="statement not covered" >  const [selectedDrawingTool, setSelectedDrawingTool] = useState&lt;CoreElementType | null&gt;(null)</span>
  /* // Commented out unused variable defaultQuickButtonAssets
  const defaultQuickButtonAssets = useMemo&lt;QuickAsset[]&gt;(() =&gt; [
    { name: 'Square', icon: &lt;Square className="h-5 w-5" /&gt;, type: CoreElementType.SQUARE, isSpecific: false, assetName: 'Square' },
    { name: 'Circle', icon: &lt;Circle className="h-5 w-5" /&gt;, type: CoreElementType.CIRCLE, isSpecific: false, assetName: 'Circle' },
    { name: 'Triangle', icon: &lt;Triangle className="h-5 w-5" /&gt;, type: CoreElementType.TRIANGLE, isSpecific: false, assetName: 'Triangle' },
    { name: 'Hexagon', icon: &lt;Hexagon className="h-5 w-5" /&gt;, type: CoreElementType.HEXAGON, isSpecific: false, assetName: 'Hexagon' },
    { name: 'Line', icon: &lt;MoveRight className="h-5 w-5" /&gt;, type: CoreElementType.LINE, isSpecific: false, assetName: 'Line' },
    { name: 'Text', icon: &lt;Type className="h-5 w-5" /&gt;, type: CoreElementType.TEXT, isSpecific: false, assetName: 'Text' },
    { name: 'Image', icon: &lt;ImageIcon className="h-5 w-5" /&gt;, type: CoreElementType.IMAGE, isSpecific: false, assetName: 'Image' },
  ], [])
  */
&nbsp;
  /* // Commented out unused variable otherGeneralAssetsForPinning
  const otherGeneralAssetsForPinning = useMemo&lt;QuickAsset[]&gt;(() =&gt; [
    { name: 'Rectangle', icon: &lt;Square className="h-5 w-5" /&gt;, type: CoreElementType.RECTANGLE, isSpecific: false, assetName: 'Rectangle' },
    { name: 'Ellipse', icon: &lt;Circle className="h-5 w-5" /&gt;, type: CoreElementType.ELLIPSE, isSpecific: false, assetName: 'Ellipse' },
    { name: 'Quadrilateral', icon: &lt;Square className="h-5 w-5" /&gt;, type: CoreElementType.QUADRILATERAL, isSpecific: false, assetName: 'Quadrilateral' },
    { name: 'Pentagon', icon: &lt;Pentagon className="h-5 w-5" /&gt;, type: CoreElementType.PENTAGON, isSpecific: false, assetName: 'Pentagon' },
    { name: 'Polyline', icon: &lt;Spline className="h-5 w-5" /&gt;, type: CoreElementType.POLYLINE, isSpecific: false, assetName: 'Polyline' },
    { name: 'Arc', icon: &lt;Orbit className="h-5 w-5" /&gt;, type: CoreElementType.ARC, isSpecific: false, assetName: 'Arc' },
    { name: 'Quadratic', icon: &lt;Waypoints className="h-5 w-5" /&gt;, type: CoreElementType.QUADRATIC, isSpecific: false, assetName: 'Quadratic' },
    { name: 'Cubic', icon: &lt;PenTool className="h-5 w-5" /&gt;, type: CoreElementType.CUBIC, isSpecific: false, assetName: 'Cubic' },
  ], [])
  */
&nbsp;
  // Use the new hook to get special elements
<span class="cstat-no" title="statement not covered" >  const specialElements = useSpecialDrawerAssets(currentModuleId, currentStepId)</span>
<span class="cstat-no" title="statement not covered" >  const hasSpecialElements = specialElements.length &gt; 0</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const [isDraggingFromExpandedSheet, setIsDraggingFromExpandedSheet] = useState(false)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const formatStepNameForTab = useCallback((moduleId: string, stepId: string): string =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (moduleId === '' || stepId === '') {</span>
<span class="cstat-no" title="statement not covered" >      return 'Special'</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    const stepNamePart = stepId.split('-').pop()</span>
<span class="cstat-no" title="statement not covered" >    return stepNamePart != null &amp;&amp; stepNamePart !== '' ? stepNamePart.charAt(0).toUpperCase() + stepNamePart.slice(1) : 'Special'</span>
<span class="cstat-no" title="statement not covered" >  }, [])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const dynamicTabLabel = useMemo(() =&gt;</span>
<span class="cstat-no" title="statement not covered" >    formatStepNameForTab(currentModuleId ?? '', currentStepId ?? ''), [currentModuleId, currentStepId, formatStepNameForTab])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const formatModuleName = useCallback((moduleId?: string): string =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (moduleId == null || moduleId === '') {</span>
<span class="cstat-no" title="statement not covered" >      return 'General'</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return moduleId.charAt(0).toUpperCase() + moduleId.slice(1).toLowerCase()</span>
<span class="cstat-no" title="statement not covered" >  }, [])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
    // This effect updates local state based on currentModuleId/currentStepId.
    // Since useSpecialDrawerAssets is a hook, it will re-run when its dependencies (currentModuleId, currentStepId) change,
    // and specialElements will be updated automatically.
    // The original setSpecialElements and setHasSpecialElements calls are no longer needed here.
    // If there were other side effects here, they would need to be preserved.
<span class="cstat-no" title="statement not covered" >  }, [currentModuleId, currentStepId, hasSpecialElements]) // Added hasSpecialElements to log its change</span>
&nbsp;
  // 添加事件监听器，监听路径绘制完成事件
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
    // 订阅路径绘制完成事件
<span class="cstat-no" title="statement not covered" >    const unsubscribe = eventBus.subscribe(EventType.PATH_DRAWING_COMPLETED, (_data) =&gt; {</span>
      // console.warn('[BottomAssetDrawer] Path drawing completed event received:', data)
      // 重置选中的绘制工具
<span class="cstat-no" title="statement not covered" >      setSelectedDrawingTool(null)</span>
<span class="cstat-no" title="statement not covered" >    })</span>
&nbsp;
    // 组件卸载时取消订阅
<span class="cstat-no" title="statement not covered" >    return () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      unsubscribe()</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }, [])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const mapConfigToAssets = useCallback((</span>
<span class="cstat-no" title="statement not covered" >    configs: Array&lt;{ category: string, items: Array&lt;{ name: string, icon: React.ReactNode, type: CoreElementType, isSpecific?: boolean, predefinedElementData?: Record&lt;string, unknown&gt; }&gt; }&gt;,</span>
<span class="cstat-no" title="statement not covered" >    groupName: string,</span>
<span class="cstat-no" title="statement not covered" >  ): AssetGroup[] =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    return configs.map(config =&gt; ({</span>
<span class="cstat-no" title="statement not covered" >      id: `${groupName.toLowerCase()}-${config.category.toLowerCase().replace(/\s+/g, '-')}`,</span>
<span class="cstat-no" title="statement not covered" >      name: config.category,</span>
<span class="cstat-no" title="statement not covered" >      assets: config.items.map(item =&gt; ({</span>
<span class="cstat-no" title="statement not covered" >        id: `${item.type}-${item.name.replace(/\s+/g, '-')}`,</span>
<span class="cstat-no" title="statement not covered" >        name: item.name,</span>
<span class="cstat-no" title="statement not covered" >        icon: item.icon,</span>
<span class="cstat-no" title="statement not covered" >        type: item.type,</span>
<span class="cstat-no" title="statement not covered" >        isSpecific: item.isSpecific || false,</span>
<span class="cstat-no" title="statement not covered" >        predefinedElementData: item.predefinedElementData,</span>
<span class="cstat-no" title="statement not covered" >      })),</span>
<span class="cstat-no" title="statement not covered" >    }))</span>
<span class="cstat-no" title="statement not covered" >  }, []) // No dependencies needed if it only uses its arguments and global constants/imports</span>
&nbsp;
  // Memoize the transformation of config to assets
<span class="cstat-no" title="statement not covered" >  const basicShapesForExpanded = useMemo(</span>
<span class="cstat-no" title="statement not covered" >    () =&gt; mapConfigToAssets(basicShapesForExpandedConfig, 'Shape'),</span>
<span class="cstat-no" title="statement not covered" >    [mapConfigToAssets],</span>
<span class="cstat-no" title="statement not covered" >  )</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const basicPathsForExpanded = useMemo(</span>
<span class="cstat-no" title="statement not covered" >    () =&gt; mapConfigToAssets(basicPathsForExpandedConfig, 'Path'),</span>
<span class="cstat-no" title="statement not covered" >    [mapConfigToAssets],</span>
<span class="cstat-no" title="statement not covered" >  )</span>
&nbsp;
  // Prepare assets for the collapsed view based on pinnedAssets prop, context, sorting, and limits
<span class="cstat-no" title="statement not covered" >  const { draggableItemsCollapsed, drawingToolItemsCollapsed } = useMemo(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    console.warn('[BottomAssetDrawer] Processing pinnedAssets for collapsed view:', pinnedAssets)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const contextuallyRelevantPins = pinnedAssets.filter((asset) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (asset.isSpecific) {</span>
        // For specific assets, they must match the current context
<span class="cstat-no" title="statement not covered" >        const moduleMatches = typeof asset.moduleId === 'string' &amp;&amp; asset.moduleId === currentModuleId</span>
<span class="cstat-no" title="statement not covered" >        const stepMatches = (typeof currentStepId === 'string' &amp;&amp; typeof asset.stepId === 'string' &amp;&amp; asset.stepId === currentStepId)</span>
<span class="cstat-no" title="statement not covered" >          || (currentStepId === undefined &amp;&amp; asset.stepId === undefined)</span>
<span class="cstat-no" title="statement not covered" >        return moduleMatches &amp;&amp; stepMatches</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      return true // Basic assets are always relevant if pinned</span>
<span class="cstat-no" title="statement not covered" >    })</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    console.warn('[BottomAssetDrawer] Contextually relevant pins:', contextuallyRelevantPins)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const draggables: PinnedAsset[] = []</span>
<span class="cstat-no" title="statement not covered" >    const drawTools: PinnedAsset[] = []</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    contextuallyRelevantPins.forEach((asset) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      console.warn('[BottomAssetDrawer] Processing asset:', asset.type, 'isSpecific:', asset.isSpecific)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (DRAGGABLE_COLLAPSED_TYPES.includes(asset.type)) {</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[BottomAssetDrawer] Asset classified as DRAGGABLE:', asset.type)</span>
<span class="cstat-no" title="statement not covered" >        draggables.push(asset)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (DRAWING_TOOL_COLLAPSED_TYPES.includes(asset.type)) {</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[BottomAssetDrawer] Asset classified as DRAWING_TOOL:', asset.type)</span>
<span class="cstat-no" title="statement not covered" >        drawTools.push(asset)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[BottomAssetDrawer] Asset NOT classified (neither draggable nor drawing tool):', asset.type)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    })</span>
&nbsp;
    // Sort draggables
<span class="cstat-no" title="statement not covered" >    draggables.sort((a, b) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      const isABasic = !a.isSpecific</span>
<span class="cstat-no" title="statement not covered" >      const isBBasic = !b.isSpecific</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (isABasic &amp;&amp; !isBBasic)</span>
<span class="cstat-no" title="statement not covered" >        return -1 // Basic items first</span>
<span class="cstat-no" title="statement not covered" >      if (!isABasic &amp;&amp; isBBasic)</span>
<span class="cstat-no" title="statement not covered" >        return 1 // Special items after basic</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (isABasic &amp;&amp; isBBasic) { // Both are basic, sort by predefined order</span>
<span class="cstat-no" title="statement not covered" >        return BASIC_DRAGGABLE_ORDER_COLLAPSED.indexOf(a.type) - BASIC_DRAGGABLE_ORDER_COLLAPSED.indexOf(b.type)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
      // Both are special, sort by assetName (or could be order of pinning if preferred)
<span class="cstat-no" title="statement not covered" >      const aName = a.assetName ?? ''</span>
<span class="cstat-no" title="statement not covered" >      const bName = b.assetName ?? ''</span>
<span class="cstat-no" title="statement not covered" >      return aName.localeCompare(bName)</span>
<span class="cstat-no" title="statement not covered" >    })</span>
&nbsp;
    // Sort drawing tools
<span class="cstat-no" title="statement not covered" >    drawTools.sort((a, b) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      const isABasic = !a.isSpecific</span>
<span class="cstat-no" title="statement not covered" >      const isBBasic = !b.isSpecific</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (isABasic &amp;&amp; !isBBasic)</span>
<span class="cstat-no" title="statement not covered" >        return -1 // Basic items first</span>
<span class="cstat-no" title="statement not covered" >      if (!isABasic &amp;&amp; isBBasic)</span>
<span class="cstat-no" title="statement not covered" >        return 1 // Special items after basic</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (isABasic &amp;&amp; isBBasic) { // Both are basic, sort by predefined order</span>
<span class="cstat-no" title="statement not covered" >        return BASIC_DRAWING_TOOL_ORDER_COLLAPSED.indexOf(a.type) - BASIC_DRAWING_TOOL_ORDER_COLLAPSED.indexOf(b.type)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
      // Both are special, sort by assetName
<span class="cstat-no" title="statement not covered" >      const aName = a.assetName ?? ''</span>
<span class="cstat-no" title="statement not covered" >      const bName = b.assetName ?? ''</span>
<span class="cstat-no" title="statement not covered" >      return aName.localeCompare(bName)</span>
<span class="cstat-no" title="statement not covered" >    })</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return {</span>
<span class="cstat-no" title="statement not covered" >      draggableItemsCollapsed: draggables.slice(0, 10), // Max 10</span>
<span class="cstat-no" title="statement not covered" >      drawingToolItemsCollapsed: drawTools.slice(0, 10), // Max 10</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }, [pinnedAssets, currentModuleId, currentStepId])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleQuickAssetDragStart = (</span>
<span class="cstat-no" title="statement not covered" >    asset: PinnedAsset,</span>
<span class="cstat-no" title="statement not covered" >    event: React.DragEvent&lt;HTMLButtonElement | HTMLDivElement&gt;,</span>
<span class="cstat-no" title="statement not covered" >  ) =&gt; {</span>
    // console.log('[BottomAssetDrawer] handleQuickAssetDragStart: Quick asset dragged', { asset })
<span class="cstat-no" title="statement not covered" >    const assetTypeForDefaults = asset.type</span>
<span class="cstat-no" title="statement not covered" >    let properties: InitialElementProperties = getSettingsForType(assetTypeForDefaults)</span>
<span class="cstat-no" title="statement not covered" >    let dragDataTypeForCanvas = asset.type</span>
<span class="cstat-no" title="statement not covered" >    let elementSpecificData: Record&lt;string, unknown&gt; = {}</span>
<span class="cstat-no" title="statement not covered" >    let effectiveAssetName = asset.assetName</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (asset.predefinedElementData) {</span>
<span class="cstat-no" title="statement not covered" >      dragDataTypeForCanvas = CoreElementType.IMAGE</span>
<span class="cstat-no" title="statement not covered" >      if (asset.type === CoreElementType.OPENING || asset.type === CoreElementType.FLOOR_AREA || asset.type === CoreElementType.WALL) {</span>
<span class="cstat-no" title="statement not covered" >        dragDataTypeForCanvas = asset.type</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      properties = {</span>
<span class="cstat-no" title="statement not covered" >        ...properties,</span>
<span class="cstat-no" title="statement not covered" >        width: asset.predefinedElementData.defaultWidth ?? properties.width,</span>
<span class="cstat-no" title="statement not covered" >        height: asset.predefinedElementData.defaultHeight ?? properties.height,</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      elementSpecificData = {</span>
<span class="cstat-no" title="statement not covered" >        ...(asset.predefinedElementData.attributes || {}),</span>
<span class="cstat-no" title="statement not covered" >        predefinedId: asset.predefinedElementData.id,</span>
<span class="cstat-no" title="statement not covered" >        actualImagePath: asset.predefinedElementData.imagePath,</span>
<span class="cstat-no" title="statement not covered" >        elementName: asset.predefinedElementData.name,</span>
<span class="cstat-no" title="statement not covered" >        majorCategory: asset.predefinedElementData.majorCategory,</span>
<span class="cstat-no" title="statement not covered" >        minorCategory: asset.predefinedElementData.minorCategory,</span>
<span class="cstat-no" title="statement not covered" >        mountingType: asset.predefinedElementData.mountingType,</span>
<span class="cstat-no" title="statement not covered" >        tags: asset.predefinedElementData.tags,</span>
<span class="cstat-no" title="statement not covered" >        description: asset.predefinedElementData.description,</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      effectiveAssetName = asset.predefinedElementData.name</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
      // For basic shapes not from predefinedElements, assetName from PinnedAsset is used.
      // elementSpecificData remains empty or could hold other type-specific info if needed
      // console.log('[BottomAssetDrawer] handleQuickAssetDragStart: Basic asset, using type as name if assetName is empty.', { assetType: asset.type, assetName: asset.assetName })
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const dragData = {</span>
<span class="cstat-no" title="statement not covered" >      elementType: dragDataTypeForCanvas,</span>
<span class="cstat-no" title="statement not covered" >      properties,</span>
<span class="cstat-no" title="statement not covered" >      name: (typeof effectiveAssetName === 'string' &amp;&amp; effectiveAssetName !== '') ? effectiveAssetName : asset.type,</span>
<span class="cstat-no" title="statement not covered" >      from: 'bottom-drawer-quick-asset',</span>
<span class="cstat-no" title="statement not covered" >      elementSpecificData,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    event.dataTransfer.setData('application/json', JSON.stringify(dragData))</span>
<span class="cstat-no" title="statement not covered" >    event.dataTransfer.effectAllowed = 'copy'</span>
    // console.log('[BottomAssetDrawer] handleQuickAssetDragStart: Drag data set', { dragData })
    // onAssetDragStart(dragDataTypeForCanvas, properties, event); // This might be too early or redundant if canvas handles it
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // NEW: Handler for clicking drawing tool assets in collapsed view
<span class="cstat-no" title="statement not covered" >  const handleQuickAssetClick = (asset: PinnedAsset) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    console.warn('[BottomAssetDrawer] handleQuickAssetClick: Quick asset clicked (drawing tool)', { assetType: asset.type })</span>
<span class="cstat-no" title="statement not covered" >    console.warn('[BottomAssetDrawer] handleQuickAssetClick: Before setSelectedDrawingTool, current:', selectedDrawingTool)</span>
&nbsp;
    // 设置当前选中的绘制工具
<span class="cstat-no" title="statement not covered" >    setSelectedDrawingTool(asset.type)</span>
<span class="cstat-no" title="statement not covered" >    console.warn('[BottomAssetDrawer] handleQuickAssetClick: After setSelectedDrawingTool, new:', asset.type)</span>
&nbsp;
    // 调用父组件的回调函数
<span class="cstat-no" title="statement not covered" >    console.warn('[BottomAssetDrawer] handleQuickAssetClick: Calling onAssetSelectFromExpanded with:', asset.type)</span>
<span class="cstat-no" title="statement not covered" >    onAssetSelectFromExpanded(asset.type)</span>
<span class="cstat-no" title="statement not covered" >    console.warn('[BottomAssetDrawer] handleQuickAssetClick: onAssetSelectFromExpanded called')</span>
&nbsp;
    // 注意：不再需要定时器，因为我们现在使用事件系统来重置按钮状态
    // 当路径绘制完成时，会触发 PATH_DRAWING_COMPLETED 事件，然后重置按钮状态
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Helper to get icon for PinnedAsset (considering predefinedElementData)
<span class="cstat-no" title="statement not covered" >  const getIconForPinnedAsset = (asset: PinnedAsset): React.ReactNode =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (asset.isSpecific &amp;&amp; typeof asset.assetName === 'string' &amp;&amp; asset.assetName !== '') {</span>
<span class="cstat-no" title="statement not covered" >      const special = specialElements.find(el =&gt; el.name === asset.assetName)</span>
<span class="cstat-no" title="statement not covered" >      if (special) {</span>
<span class="cstat-no" title="statement not covered" >        if (special.predefinedElementData) {</span>
<span class="cstat-no" title="statement not covered" >          return getIconForPredefinedElement(special.predefinedElementData)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        else if (special.icon != null) { // Check for not null/undefined</span>
<span class="cstat-no" title="statement not covered" >          return special.icon</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
      // console.warn(`[BottomAssetDrawer] Specific pinned asset '${asset.assetName}' not found or has no icon.`)
<span class="cstat-no" title="statement not covered" >      return &lt;HelpCircle className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
<span class="cstat-no" title="statement not covered" >      const assetTypeLower = asset.type.toLowerCase()</span>
<span class="cstat-no" title="statement not covered" >      const toolElement = predefinedElements.find(pe =&gt;</span>
<span class="cstat-no" title="statement not covered" >        pe.id.includes(`_${assetTypeLower}_tool`)</span>
<span class="cstat-no" title="statement not covered" >        || (pe.name.toLowerCase().includes(assetTypeLower) &amp;&amp; pe.tags?.includes('tool')),</span>
<span class="cstat-no" title="statement not covered" >      )</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (toolElement) {</span>
<span class="cstat-no" title="statement not covered" >        return getIconForPredefinedElement(toolElement)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      switch (asset.type) {</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.SQUARE: return &lt;Square className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.RECTANGLE: return &lt;RectangleHorizontal className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.CIRCLE: return &lt;Circle className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.ELLIPSE: return &lt;Circle className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.TRIANGLE: return &lt;Triangle className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.HEXAGON: return &lt;Hexagon className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.PENTAGON: return &lt;Pentagon className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.LINE: return &lt;Minus className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.TEXT: return &lt;Type className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.IMAGE: return &lt;ImageIcon className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.POLYLINE: return &lt;ChartNoAxesGantt className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.ARC: return &lt;LoaderCircle className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.QUADRATIC: return &lt;Spline className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.CUBIC: return &lt;Spline className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.POLYGON:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.QUADRILATERAL:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.HEPTAGON:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.OCTAGON:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.NONAGON:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.DECAGON:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.TEXT_LABEL:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.WALL:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.DOOR:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.WINDOW:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.FURNITURE:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.FIXTURE:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.ROOM:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.LIGHT:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.FLOOR_AREA:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.HANDRAIL:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.ELECTRICAL_OUTLET:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.ROOM_BOUNDARY:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.APPLIANCE:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.GROUP:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.OPENING:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.WALL_PAINT:</span>
<span class="cstat-no" title="statement not covered" >        case CoreElementType.WALL_PAPER:</span>
<span class="cstat-no" title="statement not covered" >          return &lt;HelpCircle className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        default:</span>
          // console.warn(`[BottomAssetDrawer] No direct icon mapping for PinnedAsset type: ${asset.type}`)
<span class="cstat-no" title="statement not covered" >          return &lt;HelpCircle className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const createAssetButtonForExpandedView = useCallback((</span>
<span class="cstat-no" title="statement not covered" >    element: DesignElement | { name: string, icon: React.ReactNode, type: CoreElementType | string },</span>
<span class="cstat-no" title="statement not covered" >    isSpecific: boolean, // True for special elements, false for basic elements from config</span>
<span class="cstat-no" title="statement not covered" >    interactionType: 'drag' | 'click',</span>
<span class="cstat-no" title="statement not covered" >    pinIconSide: 'left' | 'right', // NEW: Parameter for pin icon placement</span>
<span class="cstat-no" title="statement not covered" >  ) =&gt; {</span>
    // console.log('[BottomAssetDrawer] createAssetButtonForExpandedView: Called for element', { elementName: element.name, elementType: element.type, isSpecific }); // Added detailed log
    // Correctly derive coreType, assuming element.type is a string member of CoreElementType
<span class="cstat-no" title="statement not covered" >    const coreType = element.type as CoreElementType</span>
<span class="cstat-no" title="statement not covered" >    const defaultSettings = getSettingsForType(coreType)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    let finalDragProps: InitialElementProperties = { ...defaultSettings }</span>
<span class="cstat-no" title="statement not covered" >    let dragDataTypeForCanvas = coreType</span>
<span class="cstat-no" title="statement not covered" >    let elementSpecificData: Record&lt;string, unknown&gt; = {}</span>
<span class="cstat-no" title="statement not covered" >    const designElement = element as DesignElement // Type assertion for convenience</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (designElement.predefinedElementData) {</span>
<span class="cstat-no" title="statement not covered" >      dragDataTypeForCanvas = CoreElementType.IMAGE</span>
<span class="cstat-no" title="statement not covered" >      finalDragProps = {</span>
<span class="cstat-no" title="statement not covered" >        ...defaultSettings,</span>
<span class="cstat-no" title="statement not covered" >        width: designElement.predefinedElementData.defaultWidth ?? defaultSettings.width,</span>
<span class="cstat-no" title="statement not covered" >        height: designElement.predefinedElementData.defaultHeight ?? defaultSettings.height,</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      elementSpecificData = {</span>
<span class="cstat-no" title="statement not covered" >        ...(designElement.predefinedElementData.attributes || {}),</span>
<span class="cstat-no" title="statement not covered" >        predefinedId: designElement.predefinedElementData.id,</span>
<span class="cstat-no" title="statement not covered" >        actualImagePath: designElement.predefinedElementData.imagePath,</span>
<span class="cstat-no" title="statement not covered" >        elementName: designElement.predefinedElementData.name,</span>
<span class="cstat-no" title="statement not covered" >        majorCategory: designElement.predefinedElementData.majorCategory,</span>
<span class="cstat-no" title="statement not covered" >        minorCategory: designElement.predefinedElementData.minorCategory,</span>
<span class="cstat-no" title="statement not covered" >        mountingType: designElement.predefinedElementData.mountingType,</span>
<span class="cstat-no" title="statement not covered" >        tags: designElement.predefinedElementData.tags,</span>
<span class="cstat-no" title="statement not covered" >        description: designElement.predefinedElementData.description,</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else if (typeof designElement.openingType === 'string' &amp;&amp; designElement.openingType !== '') {</span>
<span class="cstat-no" title="statement not covered" >      dragDataTypeForCanvas = CoreElementType.OPENING</span>
<span class="cstat-no" title="statement not covered" >      elementSpecificData = {</span>
<span class="cstat-no" title="statement not covered" >        openingType: designElement.openingType,</span>
<span class="cstat-no" title="statement not covered" >        doorType: designElement.doorType,</span>
<span class="cstat-no" title="statement not covered" >        windowType: designElement.windowType,</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else if (coreType === CoreElementType.FLOOR_AREA &amp;&amp; designElement.subtypes) {</span>
<span class="cstat-no" title="statement not covered" >      dragDataTypeForCanvas = CoreElementType.FLOOR_AREA</span>
<span class="cstat-no" title="statement not covered" >      elementSpecificData = { roomSubtypes: designElement.subtypes }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else if (coreType === CoreElementType.WALL_PAINT || coreType === CoreElementType.WALL_PAPER || coreType === CoreElementType.WALL) {</span>
<span class="cstat-no" title="statement not covered" >      dragDataTypeForCanvas = coreType // Wall itself is a drawing tool type</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const displayedElementTypeForPinning = designElement.predefinedElementData ? CoreElementType.IMAGE : coreType</span>
<span class="cstat-no" title="statement not covered" >    const assetNameForPinning = designElement.predefinedElementData ? designElement.predefinedElementData.name : designElement.name</span>
&nbsp;
    // console.log('[BottomAssetDrawer] createAssetButtonForExpandedView - Element info:', {
    //   elementName: designElement.name,
    //   elementType: coreType,
    //   displayedElementTypeForPinning,
    //   assetNameForPinning,
    //   isSpecific,
    //   hasPredefinedElementData: !!designElement.predefinedElementData,
    //   currentModuleId,
    //   currentStepId,
    // })
&nbsp;
    // 钉住功能逻辑：基础元素（非特殊元素）都可以被钉住，包括基础的IMAGE元素
<span class="cstat-no" title="statement not covered" >    const canBePinned = !isSpecific</span>
<span class="cstat-no" title="statement not covered" >    const isCurrentlyPinned = canBePinned</span>
<span class="cstat-no" title="statement not covered" >      ? isAssetPinned(</span>
<span class="cstat-no" title="statement not covered" >          displayedElementTypeForPinning,</span>
<span class="cstat-no" title="statement not covered" >          isSpecific,</span>
<span class="cstat-no" title="statement not covered" >          isSpecific ? currentModuleId : undefined,</span>
<span class="cstat-no" title="statement not covered" >          isSpecific ? currentStepId : undefined,</span>
<span class="cstat-no" title="statement not covered" >          assetNameForPinning,</span>
<span class="cstat-no" title="statement not covered" >        )</span>
<span class="cstat-no" title="statement not covered" >      : false</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const buttonKey = (typeof designElement.predefinedId === 'string' &amp;&amp; designElement.predefinedId !== '')</span>
<span class="cstat-no" title="statement not covered" >      ? `${designElement.predefinedId}-${designElement.name}`</span>
<span class="cstat-no" title="statement not covered" >      : `${designElement.name}-${coreType}-${(typeof designElement.openingType === 'string' &amp;&amp; designElement.openingType !== '' ? designElement.openingType : '')}-${isSpecific}`</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const commonButtonProps = {</span>
<span class="cstat-no" title="statement not covered" >      variant: 'outline' as const,</span>
<span class="cstat-no" title="statement not covered" >      className: `h-10 w-40 px-2.5 py-1.5 flex items-center gap-2 text-xs shadow-sm hover:shadow-md transition-shadow text-left`,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const buttonContentAndTrigger = (</span>
<span class="cstat-no" title="statement not covered" >      &lt;TooltipTrigger asChild&gt;</span>
<span class="cstat-no" title="statement not covered" >        {interactionType === 'drag'</span>
          ? (
<span class="cstat-no" title="statement not covered" >              &lt;Button</span>
<span class="cstat-no" title="statement not covered" >                {...commonButtonProps}</span>
<span class="cstat-no" title="statement not covered" >                draggable</span>
<span class="cstat-no" title="statement not covered" >                onDragStart={(e) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                  setIsDraggingFromExpandedSheet(true)</span>
                  // console.log('[BottomAssetDrawer] Expanded Asset DragStart:', { name: designElement.name, type: dragDataTypeForCanvas })
<span class="cstat-no" title="statement not covered" >                  const dragData = {</span>
<span class="cstat-no" title="statement not covered" >                    elementType: dragDataTypeForCanvas,</span>
<span class="cstat-no" title="statement not covered" >                    properties: finalDragProps,</span>
<span class="cstat-no" title="statement not covered" >                    name: designElement.predefinedElementData ? designElement.predefinedElementData.name : designElement.name,</span>
<span class="cstat-no" title="statement not covered" >                    from: 'bottom-drawer-expanded-asset',</span>
<span class="cstat-no" title="statement not covered" >                    elementSpecificData,</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                  onAssetDragStart(dragDataTypeForCanvas, finalDragProps, e as React.DragEvent&lt;HTMLButtonElement | HTMLDivElement&gt;)</span>
<span class="cstat-no" title="statement not covered" >                  e.dataTransfer.setData('application/json', JSON.stringify(dragData))</span>
<span class="cstat-no" title="statement not covered" >                  e.dataTransfer.effectAllowed = 'copy'</span>
                  // console.log('[BottomAssetDrawer] Expanded Asset DragStart: Drag data set', { dragData })
<span class="cstat-no" title="statement not covered" >                }}</span>
<span class="cstat-no" title="statement not covered" >                onDragEnd={() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                  setIsDraggingFromExpandedSheet(false)</span>
<span class="cstat-no" title="statement not covered" >                }}</span>
              &gt;
<span class="cstat-no" title="statement not covered" >                &lt;div className="flex-shrink-0"&gt;{designElement.icon}&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="text-xs break-words whitespace-normal"&gt;{designElement.name}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/Button&gt;</span>
            )
          : ( // interactionType === 'click'
<span class="cstat-no" title="statement not covered" >              &lt;Button</span>
<span class="cstat-no" title="statement not covered" >                {...commonButtonProps}</span>
<span class="cstat-no" title="statement not covered" >                onClick={() =&gt; {</span>
                  // console.log('[BottomAssetDrawer] Expanded Asset Click (drawing tool):', { name: designElement.name, type: dragDataTypeForCanvas })
<span class="cstat-no" title="statement not covered" >                  onAssetSelectFromExpanded(dragDataTypeForCanvas)</span>
<span class="cstat-no" title="statement not covered" >                }}</span>
              &gt;
<span class="cstat-no" title="statement not covered" >                &lt;div className="flex-shrink-0"&gt;{designElement.icon}&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="text-xs break-words whitespace-normal"&gt;{designElement.name}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/Button&gt;</span>
            )}
<span class="cstat-no" title="statement not covered" >      &lt;/TooltipTrigger&gt;</span>
    )
&nbsp;
<span class="cstat-no" title="statement not covered" >    const pinIconElement = (</span>
<span class="cstat-no" title="statement not covered" >      &lt;div</span>
<span class="cstat-no" title="statement not covered" >        className={`flex-shrink-0 ${</span>
<span class="cstat-no" title="statement not covered" >          pinIconSide === 'left' ? 'mr-1' : 'ml-1'</span>
<span class="cstat-no" title="statement not covered" >        }`}</span>
      &gt;
<span class="cstat-no" title="statement not covered" >        &lt;TooltipProvider delayDuration={200}&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;Tooltip&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;TooltipTrigger asChild&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;Button</span>
<span class="cstat-no" title="statement not covered" >                variant="ghost"</span>
<span class="cstat-no" title="statement not covered" >                size="icon"</span>
<span class="cstat-no" title="statement not covered" >                className="h-7 w-7 p-1"</span>
<span class="cstat-no" title="statement not covered" >                onClick={() =&gt; {</span>
                  // console.log('[BottomAssetDrawer] Toggle Pin Clicked:', {
                  //   assetNameForPinning,
                  //   displayedElementTypeForPinning,
                  //   isSpecific,
                  //   isCurrentlyPinned,
                  //   currentModuleId,
                  //   currentStepId,
                  //   elementName: designElement.name,
                  // })
<span class="cstat-no" title="statement not covered" >                  onTogglePinAsset(</span>
<span class="cstat-no" title="statement not covered" >                    displayedElementTypeForPinning,</span>
<span class="cstat-no" title="statement not covered" >                    isSpecific,</span>
<span class="cstat-no" title="statement not covered" >                    isSpecific ? currentModuleId : undefined,</span>
<span class="cstat-no" title="statement not covered" >                    isSpecific ? currentStepId : undefined,</span>
<span class="cstat-no" title="statement not covered" >                    assetNameForPinning,</span>
<span class="cstat-no" title="statement not covered" >                  )</span>
<span class="cstat-no" title="statement not covered" >                }}</span>
              &gt;
<span class="cstat-no" title="statement not covered" >                {isCurrentlyPinned</span>
<span class="cstat-no" title="statement not covered" >                  ? &lt;Pin className="h-4 w-4 text-primary" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                  : &lt;PinOff className="h-4 w-4 text-muted-foreground" /&gt;}</span>
<span class="cstat-no" title="statement not covered" >                &lt;span className="sr-only"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  {isCurrentlyPinned ? 'Unpin asset' : 'Pin asset'}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/TooltipTrigger&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;TooltipContent side={pinIconSide === 'left' ? 'left' : 'right'}&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;p&gt;{isCurrentlyPinned ? 'Unpin from Quick Access' : 'Pin to Quick Access'}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/TooltipContent&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/Tooltip&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/TooltipProvider&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
    )
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >      &lt;div key={buttonKey} className="flex items-center pr-1"&gt;</span>
        {/* Pin icon on the left if specified and element can be pinned */}
<span class="cstat-no" title="statement not covered" >        {pinIconSide === 'left' &amp;&amp; canBePinned &amp;&amp; pinIconElement}</span>
&nbsp;
        {/* Main button content, with its own Tooltip setup */}
<span class="cstat-no" title="statement not covered" >        &lt;TooltipProvider delayDuration={300}&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;Tooltip&gt;</span>
<span class="cstat-no" title="statement not covered" >            {buttonContentAndTrigger}</span>
<span class="cstat-no" title="statement not covered" >            {' '}</span>
<span class="cstat-no" title="statement not covered" >            &lt;TooltipContent&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;p&gt;</span>
<span class="cstat-no" title="statement not covered" >                {interactionType === 'drag' ? 'Drag to add to canvas: ' : 'Click to select for drawing:'}</span>
<span class="cstat-no" title="statement not covered" >                {' '}</span>
<span class="cstat-no" title="statement not covered" >                {(typeof element.name === 'string' &amp;&amp; element.name !== '') ? element.name : element.type}</span>
<span class="cstat-no" title="statement not covered" >              &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              {'description' in element &amp;&amp; typeof element.description === 'string' &amp;&amp; element.description !== '' &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-xs text-muted-foreground pt-1"&gt;{element.description}&lt;/p&gt;</span>
              )}
<span class="cstat-no" title="statement not covered" >            &lt;/TooltipContent&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/Tooltip&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/TooltipProvider&gt;</span>
&nbsp;
        {/* Pin icon on the right if specified and element can be pinned */}
<span class="cstat-no" title="statement not covered" >        {pinIconSide === 'right' &amp;&amp; canBePinned &amp;&amp; pinIconElement}</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
    )
<span class="cstat-no" title="statement not covered" >  }, [getSettingsForType, isAssetPinned, onAssetSelectFromExpanded, onAssetDragStart, onTogglePinAsset, currentModuleId, currentStepId, setIsDraggingFromExpandedSheet])</span>
&nbsp;
  // Filter special elements for draggable vs. drawing tools
<span class="cstat-no" title="statement not covered" >  const draggableSpecialElements = useMemo(() =&gt;</span>
<span class="cstat-no" title="statement not covered" >    specialElements.filter(el =&gt;</span>
<span class="cstat-no" title="statement not covered" >      el.type !== CoreElementType.WALL</span>
<span class="cstat-no" title="statement not covered" >      &amp;&amp; el.type !== CoreElementType.WALL_PAINT</span>
<span class="cstat-no" title="statement not covered" >      &amp;&amp; el.type !== CoreElementType.WALL_PAPER,</span>
      // Add other non-draggable special types if any
<span class="cstat-no" title="statement not covered" >    ), [specialElements])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const drawingSpecialTools = useMemo(() =&gt;</span>
<span class="cstat-no" title="statement not covered" >    specialElements.filter(el =&gt;</span>
<span class="cstat-no" title="statement not covered" >      el.type === CoreElementType.WALL</span>
<span class="cstat-no" title="statement not covered" >      || el.type === CoreElementType.WALL_PAINT</span>
<span class="cstat-no" title="statement not covered" >      || el.type === CoreElementType.WALL_PAPER,</span>
      // Add other drawing-specific special types if any
<span class="cstat-no" title="statement not covered" >    ), [specialElements])</span>
&nbsp;
  // Determine if left and right wings have content for central divider logic
<span class="cstat-no" title="statement not covered" >  const leftWingHasContent = useMemo(() =&gt;</span>
<span class="cstat-no" title="statement not covered" >    (hasSpecialElements &amp;&amp; draggableSpecialElements.length &gt; 0) || basicShapesForExpanded.length &gt; 0, [hasSpecialElements, draggableSpecialElements, basicShapesForExpanded])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const rightWingHasContent = useMemo(() =&gt;</span>
<span class="cstat-no" title="statement not covered" >    basicPathsForExpanded.length &gt; 0 || (hasSpecialElements &amp;&amp; drawingSpecialTools.length &gt; 0), [hasSpecialElements, drawingSpecialTools, basicPathsForExpanded])</span>
&nbsp;
  // The main &lt;Sheet&gt; component should always be rendered to provide context for SheetTrigger.
  // The visibility of the collapsed bar vs. expanded content is handled inside.
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;Sheet open={sheetOpen} onOpenChange={onSheetOpenChange} modal={false}&gt;</span>
      {/* Collapsed View elements - SheetTrigger is a direct child or descendant of Sheet but outside SheetPortal */}
<span class="cstat-no" title="statement not covered" >      {!sheetOpen &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="fixed bottom-0 left-0 right-0 h-14 bg-background border-t z-40 flex items-center px-4 shadow-sm" data-tutorial="bottom-drawer-content"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex flex-grow items-center justify-center gap-x-3"&gt;</span>
            {/* Group 1: Draggable items - Rendered from Right to Left (Center to Left) */}
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center gap-1.5"&gt;</span>
              {/* Text tools section */}
<span class="cstat-no" title="statement not covered" >              &lt;div className="flex items-center gap-1.5" data-tutorial="text-tool"&gt;</span>
<span class="cstat-no" title="statement not covered" >                {[...draggableItemsCollapsed].reverse().filter(asset =&gt; asset.type === CoreElementType.TEXT).map(asset =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                  &lt;TooltipProvider key={`${asset.assetName ?? asset.type}-${asset.type}-${asset.isSpecific}-text`} delayDuration={300}&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;Tooltip&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;TooltipTrigger asChild&gt;</span>
<span class="cstat-no" title="statement not covered" >                        {!asset.isSpecific</span>
                          ? ( // Basic draggable assets: ICON ONLY
<span class="cstat-no" title="statement not covered" >                              &lt;Button</span>
<span class="cstat-no" title="statement not covered" >                                variant="ghost"</span>
<span class="cstat-no" title="statement not covered" >                                size="icon"</span>
<span class="cstat-no" title="statement not covered" >                                draggable</span>
<span class="cstat-no" title="statement not covered" >                                onDragStart={e =&gt; handleQuickAssetDragStart(asset, e)}</span>
<span class="cstat-no" title="statement not covered" >                                className="cursor-grab h-9 w-9 p-1.5"</span>
                              &gt;
<span class="cstat-no" title="statement not covered" >                                {getIconForPinnedAsset(asset)}</span>
<span class="cstat-no" title="statement not covered" >                                &lt;span className="sr-only"&gt;{(typeof asset.assetName === 'string' &amp;&amp; asset.assetName !== '') ? asset.assetName : asset.type}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                              &lt;/Button&gt;</span>
                            )
                          : ( // Special elements: TEXT ONLY
<span class="cstat-no" title="statement not covered" >                              &lt;Button</span>
<span class="cstat-no" title="statement not covered" >                                variant="ghost"</span>
<span class="cstat-no" title="statement not covered" >                                draggable</span>
<span class="cstat-no" title="statement not covered" >                                onDragStart={e =&gt; handleQuickAssetDragStart(asset, e)}</span>
<span class="cstat-no" title="statement not covered" >                                className="px-2.5 py-1.5 text-xs cursor-grab h-9 min-w-[60px] text-center"</span>
                              &gt;
                                {/* No icon, just text for special elements */}
<span class="cstat-no" title="statement not covered" >                                &lt;span&gt;{(typeof asset.assetName === 'string' &amp;&amp; asset.assetName !== '') ? asset.assetName : asset.type}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                              &lt;/Button&gt;</span>
                            )}
<span class="cstat-no" title="statement not covered" >                      &lt;/TooltipTrigger&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;TooltipContent side="top"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p&gt;</span>
                          Drag to add to canvas:
<span class="cstat-no" title="statement not covered" >                          {' '}</span>
<span class="cstat-no" title="statement not covered" >                          {(typeof asset.assetName === 'string' &amp;&amp; asset.assetName !== '') ? asset.assetName : asset.type}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/TooltipContent&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/Tooltip&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/TooltipProvider&gt;</span>
<span class="cstat-no" title="statement not covered" >                ))}</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
&nbsp;
              {/* Basic shapes section - All basic shapes and polygons (excluding images) */}
<span class="cstat-no" title="statement not covered" >              &lt;div className="flex items-center gap-1.5" data-tutorial="basic-shapes"&gt;</span>
<span class="cstat-no" title="statement not covered" >                {[...draggableItemsCollapsed].reverse().filter(asset =&gt;</span>
<span class="cstat-no" title="statement not covered" >                  asset.type === CoreElementType.CIRCLE</span>
<span class="cstat-no" title="statement not covered" >                  || asset.type === CoreElementType.RECTANGLE</span>
<span class="cstat-no" title="statement not covered" >                  || asset.type === CoreElementType.SQUARE</span>
<span class="cstat-no" title="statement not covered" >                  || asset.type === CoreElementType.ELLIPSE</span>
<span class="cstat-no" title="statement not covered" >                  || asset.type === CoreElementType.TRIANGLE</span>
<span class="cstat-no" title="statement not covered" >                  || asset.type === CoreElementType.PENTAGON</span>
<span class="cstat-no" title="statement not covered" >                  || asset.type === CoreElementType.HEXAGON,</span>
<span class="cstat-no" title="statement not covered" >                ).map((asset) =&gt; {</span>
                  // Determine specific data-tutorial attribute based on asset type
<span class="cstat-no" title="statement not covered" >                  let specificDataTutorial = ''</span>
<span class="cstat-no" title="statement not covered" >                  if (asset.type === CoreElementType.CIRCLE) {</span>
<span class="cstat-no" title="statement not covered" >                    specificDataTutorial = 'circle-shape'</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                  else if (asset.type === CoreElementType.RECTANGLE || asset.type === CoreElementType.SQUARE) {</span>
<span class="cstat-no" title="statement not covered" >                    specificDataTutorial = 'rectangle-shape'</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                  else if (asset.type === CoreElementType.ELLIPSE) {</span>
<span class="cstat-no" title="statement not covered" >                    specificDataTutorial = 'ellipse-shape'</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                  else if (asset.type === CoreElementType.TRIANGLE) {</span>
<span class="cstat-no" title="statement not covered" >                    specificDataTutorial = 'triangle-shape'</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                  else if (asset.type === CoreElementType.PENTAGON) {</span>
<span class="cstat-no" title="statement not covered" >                    specificDataTutorial = 'pentagon-shape'</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                  else if (asset.type === CoreElementType.HEXAGON) {</span>
<span class="cstat-no" title="statement not covered" >                    specificDataTutorial = 'hexagon-shape'</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                  else if (asset.type === CoreElementType.IMAGE) {</span>
<span class="cstat-no" title="statement not covered" >                    specificDataTutorial = 'image-element'</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                  return (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;TooltipProvider key={`${asset.assetName ?? asset.type}-${asset.type}-${asset.isSpecific}-draggable`} delayDuration={300}&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;Tooltip&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;TooltipTrigger asChild&gt;</span>
<span class="cstat-no" title="statement not covered" >                          {!asset.isSpecific</span>
                            ? ( // Basic elements: ICON ONLY
<span class="cstat-no" title="statement not covered" >                                &lt;Button</span>
<span class="cstat-no" title="statement not covered" >                                  variant="ghost"</span>
<span class="cstat-no" title="statement not covered" >                                  size="icon"</span>
<span class="cstat-no" title="statement not covered" >                                  draggable</span>
<span class="cstat-no" title="statement not covered" >                                  onDragStart={e =&gt; handleQuickAssetDragStart(asset, e)}</span>
<span class="cstat-no" title="statement not covered" >                                  className="cursor-grab h-9 w-9 p-1.5"</span>
<span class="cstat-no" title="statement not covered" >                                  data-tutorial={specificDataTutorial}</span>
                                &gt;
<span class="cstat-no" title="statement not covered" >                                  {getIconForPinnedAsset(asset)}</span>
<span class="cstat-no" title="statement not covered" >                                  &lt;span className="sr-only"&gt;{(typeof asset.assetName === 'string' &amp;&amp; asset.assetName !== '') ? asset.assetName : asset.type}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/Button&gt;</span>
                              )
                            : ( // Special elements: TEXT ONLY
<span class="cstat-no" title="statement not covered" >                                &lt;Button</span>
<span class="cstat-no" title="statement not covered" >                                  variant="ghost"</span>
<span class="cstat-no" title="statement not covered" >                                  draggable</span>
<span class="cstat-no" title="statement not covered" >                                  onDragStart={e =&gt; handleQuickAssetDragStart(asset, e)}</span>
<span class="cstat-no" title="statement not covered" >                                  className="px-2.5 py-1.5 text-xs cursor-grab h-9 min-w-[60px] text-center"</span>
<span class="cstat-no" title="statement not covered" >                                  data-tutorial={specificDataTutorial}</span>
                                &gt;
                                  {/* No icon, just text for special elements */}
<span class="cstat-no" title="statement not covered" >                                  &lt;span&gt;{(typeof asset.assetName === 'string' &amp;&amp; asset.assetName !== '') ? asset.assetName : asset.type}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/Button&gt;</span>
                              )}
<span class="cstat-no" title="statement not covered" >                        &lt;/TooltipTrigger&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;TooltipContent side="top"&gt;</span>
<span class="cstat-no" title="statement not covered" >                          &lt;p&gt;</span>
                            Drag to add to canvas:
<span class="cstat-no" title="statement not covered" >                            {' '}</span>
<span class="cstat-no" title="statement not covered" >                            {(typeof asset.assetName === 'string' &amp;&amp; asset.assetName !== '') ? asset.assetName : asset.type}</span>
<span class="cstat-no" title="statement not covered" >                          &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/TooltipContent&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/Tooltip&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/TooltipProvider&gt;</span>
                  )
<span class="cstat-no" title="statement not covered" >                })}</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
&nbsp;
              {/* Image elements section - Only images */}
<span class="cstat-no" title="statement not covered" >              &lt;div className="flex items-center gap-1.5" data-tutorial="image-elements"&gt;</span>
<span class="cstat-no" title="statement not covered" >                {[...draggableItemsCollapsed].reverse().filter(asset =&gt;</span>
<span class="cstat-no" title="statement not covered" >                  asset.type === CoreElementType.IMAGE,</span>
<span class="cstat-no" title="statement not covered" >                ).map(asset =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                  &lt;TooltipProvider key={`${asset.assetName ?? asset.type}-${asset.type}-${asset.isSpecific}-image`} delayDuration={300}&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;Tooltip&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;TooltipTrigger asChild&gt;</span>
<span class="cstat-no" title="statement not covered" >                        {!asset.isSpecific</span>
                          ? ( // Basic draggable assets: ICON ONLY
<span class="cstat-no" title="statement not covered" >                              &lt;Button</span>
<span class="cstat-no" title="statement not covered" >                                variant="ghost"</span>
<span class="cstat-no" title="statement not covered" >                                size="icon"</span>
<span class="cstat-no" title="statement not covered" >                                draggable</span>
<span class="cstat-no" title="statement not covered" >                                onDragStart={e =&gt; handleQuickAssetDragStart(asset, e)}</span>
<span class="cstat-no" title="statement not covered" >                                className="cursor-grab h-9 w-9 p-1.5"</span>
<span class="cstat-no" title="statement not covered" >                                data-tutorial="image-element"</span>
                              &gt;
<span class="cstat-no" title="statement not covered" >                                {getIconForPinnedAsset(asset)}</span>
<span class="cstat-no" title="statement not covered" >                                &lt;span className="sr-only"&gt;{(typeof asset.assetName === 'string' &amp;&amp; asset.assetName !== '') ? asset.assetName : asset.type}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                              &lt;/Button&gt;</span>
                            )
                          : ( // Special draggable assets: ICON + TEXT
<span class="cstat-no" title="statement not covered" >                              &lt;Button</span>
<span class="cstat-no" title="statement not covered" >                                variant="ghost"</span>
<span class="cstat-no" title="statement not covered" >                                size="sm"</span>
<span class="cstat-no" title="statement not covered" >                                draggable</span>
<span class="cstat-no" title="statement not covered" >                                onDragStart={e =&gt; handleQuickAssetDragStart(asset, e)}</span>
<span class="cstat-no" title="statement not covered" >                                className="cursor-grab h-9 px-2 py-1.5 flex items-center gap-1.5"</span>
<span class="cstat-no" title="statement not covered" >                                data-tutorial="image-element"</span>
                              &gt;
<span class="cstat-no" title="statement not covered" >                                &lt;div className="flex-shrink-0"&gt;{getIconForPinnedAsset(asset)}&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;span className="text-xs break-words whitespace-normal"&gt;{(typeof asset.assetName === 'string' &amp;&amp; asset.assetName !== '') ? asset.assetName : asset.type}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                              &lt;/Button&gt;</span>
                            )}
<span class="cstat-no" title="statement not covered" >                      &lt;/TooltipTrigger&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;TooltipContent side="top"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p&gt;</span>
                          Drag to canvas:
<span class="cstat-no" title="statement not covered" >                          {(typeof asset.assetName === 'string' &amp;&amp; asset.assetName !== '') ? asset.assetName : asset.type}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/TooltipContent&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/Tooltip&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/TooltipProvider&gt;</span>
<span class="cstat-no" title="statement not covered" >                ))}</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
&nbsp;
            {/* Divider for Collapsed View */}
<span class="cstat-no" title="statement not covered" >            &lt;div className="h-6 w-px bg-border mx-1 self-center"&gt;&lt;/div&gt;</span>
&nbsp;
            {/* Group 2: Drawing tool items - Rendered from Left to Right (Center to Right) */}
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center gap-1.5" data-tutorial="path-tools"&gt;</span>
<span class="cstat-no" title="statement not covered" >              {drawingToolItemsCollapsed.map(asset =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                &lt;TooltipProvider key={`${asset.assetName ?? asset.type}-${asset.type}-${asset.isSpecific}-clickable`} delayDuration={300}&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;Tooltip&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;TooltipTrigger asChild&gt;</span>
<span class="cstat-no" title="statement not covered" >                      {!asset.isSpecific</span>
                        ? ( // Basic drawing tools: ICON ONLY
<span class="cstat-no" title="statement not covered" >                            &lt;Button</span>
<span class="cstat-no" title="statement not covered" >                              variant={selectedDrawingTool === asset.type ? 'default' : 'ghost'}</span>
<span class="cstat-no" title="statement not covered" >                              size="icon"</span>
<span class="cstat-no" title="statement not covered" >                              onClick={() =&gt; handleQuickAssetClick(asset)}</span>
<span class="cstat-no" title="statement not covered" >                              className={`cursor-pointer h-9 w-9 p-1.5 ${selectedDrawingTool === asset.type ? 'bg-primary text-primary-foreground' : ''}`}</span>
                            &gt;
<span class="cstat-no" title="statement not covered" >                              {getIconForPinnedAsset(asset)}</span>
<span class="cstat-no" title="statement not covered" >                              &lt;span className="sr-only"&gt;{(typeof asset.assetName === 'string' &amp;&amp; asset.assetName !== '') ? asset.assetName : asset.type}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/Button&gt;</span>
                          )
                        : ( // Special drawing tools: TEXT ONLY
<span class="cstat-no" title="statement not covered" >                            &lt;Button</span>
<span class="cstat-no" title="statement not covered" >                              variant={selectedDrawingTool === asset.type ? 'default' : 'ghost'}</span>
<span class="cstat-no" title="statement not covered" >                              onClick={() =&gt; handleQuickAssetClick(asset)}</span>
<span class="cstat-no" title="statement not covered" >                              className={`px-2.5 py-1.5 text-xs cursor-pointer h-9 min-w-[60px] text-center ${selectedDrawingTool === asset.type ? 'bg-primary text-primary-foreground' : ''}`}</span>
                            &gt;
                              {/* No icon, just text for special elements */}
<span class="cstat-no" title="statement not covered" >                              &lt;span&gt;{(typeof asset.assetName === 'string' &amp;&amp; asset.assetName !== '') ? asset.assetName : asset.type}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/Button&gt;</span>
                          )}
<span class="cstat-no" title="statement not covered" >                    &lt;/TooltipTrigger&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;TooltipContent side="top"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p&gt;</span>
                        Click to select for drawing:
<span class="cstat-no" title="statement not covered" >                        {' '}</span>
<span class="cstat-no" title="statement not covered" >                        {(typeof asset.assetName === 'string' &amp;&amp; asset.assetName !== '') ? asset.assetName : asset.type}</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/TooltipContent&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/Tooltip&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/TooltipProvider&gt;</span>
<span class="cstat-no" title="statement not covered" >              ))}</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
&nbsp;
          {/* Expand Button - SheetTrigger is now correctly within Sheet context */}
<span class="cstat-no" title="statement not covered" >          &lt;div className="ml-auto flex-shrink-0" data-tutorial="drawer-expand"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;SheetTrigger asChild&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;Button aria-label="Expand asset drawer" variant="ghost" size="icon" className="h-9 w-9 p-1.5"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;ChevronUp className="h-5 w-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/SheetTrigger&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
      )}
&nbsp;
      {/* Expanded View (Sheet Portal, Overlay, and Content) - Conditionally rendered */}
<span class="cstat-no" title="statement not covered" >      {sheetOpen &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >        &lt;SheetPortal&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;SheetOverlay /&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;SheetContent</span>
<span class="cstat-no" title="statement not covered" >            side="bottom"</span>
<span class="cstat-no" title="statement not covered" >            className={cn(</span>
<span class="cstat-no" title="statement not covered" >              'p-0 flex flex-col',</span>
<span class="cstat-no" title="statement not covered" >              'transition-all duration-200 ease-in-out',</span>
<span class="cstat-no" title="statement not covered" >              isDraggingFromExpandedSheet ? 'h-16' : 'h-[calc(var(--vh,1vh)*60)] md:h-96',</span>
<span class="cstat-no" title="statement not covered" >            )}</span>
            // onClick={(e) =&gt; e.stopPropagation()} // Prevent closing on content click if needed
          &gt;
<span class="cstat-no" title="statement not covered" >            &lt;SheetHeader className="p-3 pb-2 border-b flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="flex justify-between items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;SheetTitle className="text-base font-semibold"&gt;Asset Library&lt;/SheetTitle&gt;</span>
                {/* Optional: Add a SheetClose button here if you want an X inside the expanded drawer header */}
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/SheetHeader&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;ScrollArea className="flex-1 min-h-0"&gt;</span>
              {/* Outermost centering container */}
<span class="cstat-no" title="statement not covered" >              &lt;div className="flex justify-center p-4 pt-2"&gt;</span>
                {/* Main Row for wings and divider. Takes full width up to a max. Removed justify-center from here, relying on parent for centering the block. */}
<span class="cstat-no" title="statement not covered" >                &lt;div className="flex flex-row items-start w-full max-w-6xl"&gt;</span>
&nbsp;
                  {/* Column 1 (NEW ORDER): Special Elements (Draggable) - OUTER LEFT - This column is flex-1. Items inside right-aligned. */}
<span class="cstat-no" title="statement not covered" >                  {hasSpecialElements &amp;&amp; draggableSpecialElements.length &gt; 0 &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex flex-col gap-y-4 flex-1 min-w-[160px] md:min-w-[180px]" data-tutorial="special-elements"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;div key="special-draggable-elements-category"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;h3 className="text-sm font-medium mb-2 text-muted-foreground sticky top-0 bg-background pt-1 z-[1] text-right"&gt;</span>
<span class="cstat-no" title="statement not covered" >                          {formatModuleName(currentModuleId)}</span>
<span class="cstat-no" title="statement not covered" >                          {' '}</span>
                          -
<span class="cstat-no" title="statement not covered" >                          {dynamicTabLabel}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;div className="flex flex-col gap-2 items-end"&gt;</span>
<span class="cstat-no" title="statement not covered" >                          {draggableSpecialElements.map(element =&gt;</span>
<span class="cstat-no" title="statement not covered" >                            createAssetButtonForExpandedView(</span>
<span class="cstat-no" title="statement not covered" >                              element,</span>
<span class="cstat-no" title="statement not covered" >                              true, // isSpecific</span>
<span class="cstat-no" title="statement not covered" >                              'drag', // interactionType</span>
<span class="cstat-no" title="statement not covered" >                              'left', // pinIconSide for outer left column</span>
<span class="cstat-no" title="statement not covered" >                            ),</span>
<span class="cstat-no" title="statement not covered" >                          )}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                  )}
                  {/* Conditional placeholder for Column 1 if no special draggable elements */}
<span class="cstat-no" title="statement not covered" >                  {(!hasSpecialElements || draggableSpecialElements.length === 0) &amp;&amp; basicShapesForExpanded.length &gt; 0 &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex-1 min-w-[160px] md:min-w-[180px]"&gt;{/* Placeholder for Col 1 ensures col 2 can align against something */}&lt;/div&gt;</span>
                  )}
&nbsp;
                  {/* Divider between Column 1 and Column 2 */}
<span class="cstat-no" title="statement not covered" >                  {(hasSpecialElements &amp;&amp; draggableSpecialElements.length &gt; 0) &amp;&amp; basicShapesForExpanded.length &gt; 0 &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex-shrink-0 flex justify-center items-stretch px-2 md:px-3 self-stretch"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;div className="w-px bg-border"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                  )}
&nbsp;
                  {/* Column 2 (NEW ORDER): Basic Shapes (Draggable) - INNER LEFT - This column is flex-1. Items inside right-aligned. */}
<span class="cstat-no" title="statement not covered" >                  &lt;div className="flex flex-col gap-y-4 flex-1 min-w-[160px] md:min-w-[180px]"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {basicShapesForExpanded.map((config: AssetGroup) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                      &lt;div key={`shape-cat-${config.name}`}&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;h3 className="text-sm font-medium mb-2 text-muted-foreground sticky top-0 bg-background pt-1 z-[1] text-right"&gt;</span>
<span class="cstat-no" title="statement not covered" >                          {config.name}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;div className="flex flex-col gap-2 items-end"&gt;</span>
<span class="cstat-no" title="statement not covered" >                          {config.assets.map((item: AssetInGroup) =&gt; createAssetButtonForExpandedView(</span>
<span class="cstat-no" title="statement not covered" >                            { name: item.name, icon: item.icon, type: item.type },</span>
<span class="cstat-no" title="statement not covered" >                            false, // isSpecific should be item.isSpecific, but createAssetButtonForExpandedView expects hardcoded false here for basic shapes</span>
<span class="cstat-no" title="statement not covered" >                            'drag', // interactionType</span>
<span class="cstat-no" title="statement not covered" >                            'left', // pinIconSide for inner left column</span>
<span class="cstat-no" title="statement not covered" >                          ))}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    ))}</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
&nbsp;
                  {/* Divider between Column 2 and Column 3 (Central Divider) */}
<span class="cstat-no" title="statement not covered" >                  {leftWingHasContent &amp;&amp; rightWingHasContent &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex-shrink-0 flex justify-center items-stretch px-2 md:px-3 self-stretch"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      {' '}</span>
                      {/* Adjusted to match other dividers */}
<span class="cstat-no" title="statement not covered" >                      &lt;div className="w-px bg-border"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                  )}
&nbsp;
                  {/* Column 3: Basic Paths (Click-to-Prime) - INNER RIGHT - This column is flex-1. Items inside left-aligned (default). */}
<span class="cstat-no" title="statement not covered" >                  {basicPathsForExpanded.length &gt; 0 &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex flex-col gap-y-4 flex-1 min-w-[160px] md:min-w-[180px]"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      {basicPathsForExpanded.map((config: AssetGroup) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                        &lt;div key={`path-cat-${config.name}`}&gt;</span>
<span class="cstat-no" title="statement not covered" >                          &lt;h3 className="text-sm font-medium mb-2 text-muted-foreground sticky top-0 bg-background pt-1 z-[1]"&gt;{config.name}&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                          &lt;div className="flex flex-col gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            {config.assets.map((item: AssetInGroup) =&gt; createAssetButtonForExpandedView(</span>
<span class="cstat-no" title="statement not covered" >                              { name: item.name, icon: item.icon, type: item.type },</span>
<span class="cstat-no" title="statement not covered" >                              false, // isSpecific should be item.isSpecific, but createAssetButtonForExpandedView expects hardcoded false here for basic paths</span>
<span class="cstat-no" title="statement not covered" >                              'click', // interactionType</span>
<span class="cstat-no" title="statement not covered" >                              'right', // pinIconSide</span>
<span class="cstat-no" title="statement not covered" >                            ))}</span>
<span class="cstat-no" title="statement not covered" >                          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                      ))}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                  )}
                  {/* Conditional rendering for Column 3 placeholder if no basic paths */}
<span class="cstat-no" title="statement not covered" >                  {basicPathsForExpanded.length === 0 &amp;&amp; (basicShapesForExpanded.length &gt; 0 || draggableSpecialElements.length &gt; 0) &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex-1 min-w-[160px] md:min-w-[180px]"&gt;{/* Placeholder ensures col 3 can align against something */}&lt;/div&gt;</span>
                  )}
&nbsp;
                  {/* Divider between Column 3 and Column 4 */}
<span class="cstat-no" title="statement not covered" >                  {basicPathsForExpanded.length &gt; 0 &amp;&amp; (hasSpecialElements &amp;&amp; drawingSpecialTools.length &gt; 0) &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex-shrink-0 flex justify-center items-stretch px-2 md:px-3 self-stretch"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;div className="w-px bg-border"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                  )}
&nbsp;
                  {/* Column 4: Special Drawing Tools (Click-to-Prime) - OUTER RIGHT - This column is flex-1. Items inside left-aligned (default). */}
<span class="cstat-no" title="statement not covered" >                  {hasSpecialElements &amp;&amp; drawingSpecialTools.length &gt; 0 &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex flex-col gap-y-4 flex-1 min-w-[160px] md:min-w-[180px]"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;div key="special-drawing-tools-category"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;h3 className="text-sm font-medium mb-2 text-muted-foreground sticky top-0 bg-background pt-1 z-[1]"&gt;</span>
<span class="cstat-no" title="statement not covered" >                          {formatModuleName(currentModuleId)}</span>
<span class="cstat-no" title="statement not covered" >                          {' '}</span>
                          -
<span class="cstat-no" title="statement not covered" >                          {dynamicTabLabel}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;div className="flex flex-col gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                          {drawingSpecialTools.map(element =&gt;</span>
<span class="cstat-no" title="statement not covered" >                            createAssetButtonForExpandedView(</span>
<span class="cstat-no" title="statement not covered" >                              element,</span>
<span class="cstat-no" title="statement not covered" >                              true, // isSpecific</span>
<span class="cstat-no" title="statement not covered" >                              (element.type === CoreElementType.WALL || element.type === CoreElementType.WALL_PAINT || element.type === CoreElementType.WALL_PAPER) ? 'click' : 'drag',</span>
<span class="cstat-no" title="statement not covered" >                              'right', // pinIconSide</span>
<span class="cstat-no" title="statement not covered" >                            ),</span>
<span class="cstat-no" title="statement not covered" >                          )}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                  )}
                  {/* Conditional rendering for Column 4 placeholder if no special drawing tools */}
<span class="cstat-no" title="statement not covered" >                  {(!hasSpecialElements || drawingSpecialTools.length === 0) &amp;&amp; (basicPathsForExpanded.length &gt; 0) &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex-1 min-w-[160px] md:min-w-[180px]"&gt;{/* Placeholder */}&lt;/div&gt;</span>
                  )}
&nbsp;
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/ScrollArea&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/SheetContent&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/SheetPortal&gt;</span>
      )}
<span class="cstat-no" title="statement not covered" >    &lt;/Sheet&gt;</span>
  )
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default BottomAssetDrawer</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:58:01.486Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    