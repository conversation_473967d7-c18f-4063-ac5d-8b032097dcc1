/**
 * Grid Event Publishers Module
 *
 * This module provides helper functions for publishing grid-related events
 * to the application's event bus. It covers grid visibility, size, color,
 * and snapping behavior.
 *
 * All functions follow dependency injection principles by accepting an EventBus
 * instance as their first parameter.
 *
 * @module services/core/event-bus/helpers/publishers/gridPublishers
 */

import type { EventBus } from '@/types/services/events'
import type { GridColorChangeEvent, GridEnableEvent, GridSizeChangeEvent, GridSnapChangeEvent } from '@/types/services/events/gridEvents'
import { AppEventType } from '@/types/services/events'

/**
 * Publish grid enabled event
 *
 * Notifies the system that the grid visibility has been toggled.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param enabled - Boolean indicating whether the grid is now enabled
 */
export function publishGridEnabled(eventBus: EventBus, enabled: boolean): void {
  const event: GridEnableEvent = {
    type: AppEventType.GridEnabled,
    payload: { enabled },
  }
  eventBus.publish(event)
}

/**
 * Publish grid size changed event
 *
 * Notifies the system that the grid cell size has changed.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param size - New grid cell size in pixels
 */
export function publishGridSizeChanged(eventBus: EventBus, size: number): void {
  const event: GridSizeChangeEvent = {
    type: AppEventType.GridSizeChanged,
    payload: { size },
  }
  eventBus.publish(event)
}

/**
 * Publish grid color changed event
 *
 * Notifies the system that the grid color has changed.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param color - New grid color (CSS color string)
 */
export function publishGridColorChanged(eventBus: EventBus, color: string): void {
  const event: GridColorChangeEvent = {
    type: AppEventType.GridColorChanged,
    payload: { color },
  }
  eventBus.publish(event)
}

/**
 * Publish grid snap changed event
 *
 * Notifies the system that the grid snapping setting has changed.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param snapToGrid - Boolean indicating whether snap-to-grid is now enabled
 */
export function publishGridSnapChanged(eventBus: EventBus, snapToGrid: boolean): void {
  const event: GridSnapChangeEvent = {
    type: AppEventType.GridSnapChanged,
    payload: { enabled: snapToGrid },
  }
  eventBus.publish(event)
}
