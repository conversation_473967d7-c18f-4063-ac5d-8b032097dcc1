// Import the main registry types
import type {
  AppEvent, // BaseEvent & AppEventRegistry
  AppEventMap, // Mapping from AppEventType to specific event interface
  AppEventRegistry, // Union of all specific event interfaces
} from '@/types/services/events/eventRegistry'

import type { KeyPressedEvent, ShapeSelectedEvent } from '@/types/services/events/eventRegistry' // Re-exported
import { describe, expect, it } from 'vitest'
// Import some specific event types and AppEventType for testing
import { AppEventType } from '@/types/services/events/eventTypes'

describe('service Event Registry Types', () => {
  it('should allow declaration using AppEventRegistry union type', () => {
    let registryEvent: AppEventRegistry | undefined
    expect(registryEvent).toBeUndefined()

    // Assign a specific event type to the union
    const shapeSelected: ShapeSelectedEvent = {
      type: AppEventType.SHAPE_SELECTED,
      payload: { shape: null },
    }
    registryEvent = shapeSelected
    expect(registryEvent.type).toBe(AppEventType.SHAPE_SELECTED)

    const keyPressed: KeyPressedEvent = {
      type: AppEventType.KEY_PRESSED,
      payload: {
        key: 'a',
        code: 'KeyA',
        modifiers: { ctrlKey: false, shiftKey: false, altKey: false, metaKey: false },
      },
    }
    registryEvent = keyPressed
    expect(registryEvent.type).toBe(AppEventType.KEY_PRESSED)
  })

  it('should allow declaration using AppEvent type', () => {
    let appEvent: AppEvent | undefined
    expect(appEvent).toBeUndefined()

    // AppEvent should be compatible with BaseEvent and specific events
    // const baseExample: BaseEvent = { type: AppEventType.VIEW_ZOOM_IN, payload: {} };
    // appEvent = baseExample; // Removed: BaseEvent is not directly assignable to AppEvent
    // expect(appEvent.type).toBe(AppEventType.VIEW_ZOOM_IN);

    const specificExample: ShapeSelectedEvent = {
      type: AppEventType.SHAPE_SELECTED,
      payload: { shape: { id: 's1' } as any }, // Use 'any' for mock shape
    }
    appEvent = specificExample // Should be assignable from a specific event
    expect(appEvent.type).toBe(AppEventType.SHAPE_SELECTED)
    // Accessing payload requires type guard/assertion because AppEvent is broad
    if (appEvent.type === AppEventType.SHAPE_SELECTED) {
      expect((appEvent.payload as ShapeSelectedEvent['payload']).shape?.id).toBe('s1')
    }
  })

  it('appEventMap should correctly map event types to interfaces', () => {
    // This test primarily relies on TypeScript's compile-time checking.
    // We demonstrate accessing the type via the map.
    type SelectedEventType = AppEventMap[AppEventType.SHAPE_SELECTED]
    type KeyPressedType = AppEventMap[AppEventType.KEY_PRESSED]

    let selectedVar: SelectedEventType | undefined
    expect(selectedVar).toBeUndefined()

    let keyVar: KeyPressedType | undefined
    expect(keyVar).toBeUndefined()

    // Check if the retrieved type matches the imported specific type
    const shapeSelected: ShapeSelectedEvent = { type: AppEventType.SHAPE_SELECTED, payload: { shape: null } }
    selectedVar = shapeSelected // Should be assignable
    expect(selectedVar.type).toBe(AppEventType.SHAPE_SELECTED)

    const keyPressed: KeyPressedEvent = {
      type: AppEventType.KEY_PRESSED,
      payload: {
        key: 'b',
        code: 'KeyB',
        modifiers: { ctrlKey: true, shiftKey: false, altKey: false, metaKey: false },
      },
    }
    keyVar = keyPressed // Should be assignable
    expect(keyVar.payload.key).toBe('b')
  })
})
