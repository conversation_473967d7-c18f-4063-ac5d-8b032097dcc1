/**
 * Defines Types for Element Fill Patterns, Including textures.js Integration
 *
 * @remarks
 * This module provides a comprehensive set of type definitions related to fill patterns
 * that can be applied to visual elements. It includes specific enumerations and interfaces
 * tailored for patterns generated by the `textures.js` library, such as line, circle,
 * path, and hexagon textures.
 *
 * Additionally, it defines a general {@link PatternDefinition} interface that can
 * describe various pattern types, including image-based patterns, gradients, or
 * procedural textures, along with their configuration options.
 *
 * @module types/core/element/elementPatternTypes
 * @see {@link https://riccardoscalco.github.io/textures/ | textures.js official documentation}
 * @see {@link PatternDefinition} for the main pattern configuration interface.
 * @see {@link PatternType} for `textures.js` specific pattern types.
 */

/**
 * Enumerates pattern types that are specifically supported by, or map directly to,
 * the pattern generation capabilities of the `textures.js` library.
 *
 * @remarks
 * Each member of this enum corresponds to a distinct pattern generator available
 * in `textures.js`. We use simplified names for consistency with the UI.
 *
 * @see {@link https://riccardoscalco.github.io/textures/ | textures.js official documentation}
 */
export enum PatternType {
  /** Represents a line-based pattern generated by textures.js. */
  LINES = 'lines',
  /** Represents a circle-based pattern generated by textures.js. */
  CIRCLES = 'circles',
  /** Represents a custom path-based pattern generated by textures.js. */
  PATHS = 'paths',
}

/**
 * Legacy pattern type enum for backward compatibility.
 * @deprecated Use PatternType instead
 */
export enum LegacyPatternType {
  /** @deprecated Use PatternType.LINES */
  TEXTURE_LINES = 'texture-lines',
  /** @deprecated Use PatternType.CIRCLES */
  TEXTURE_CIRCLES = 'texture-circles',
  /** @deprecated Use PatternType.PATHS */
  TEXTURE_PATHS = 'texture-paths',
}

/**
 * Defines configuration options for line-based patterns generated by `textures.js`.
 *
 * @remarks
 * These options allow customization of various aspects of line patterns,
 * such as line spacing (`size`), stroke width, rendering quality (`shapeRendering`),
 * background color, and line orientation(s).
 *
 * @see {@link PatternType.TEXTURE_LINES}
 * @see {@link https://riccardoscalco.github.io/textures/#lines | textures.js lines documentation}
 */
export interface TextureLinesOptions {
  /** The spacing between lines in the pattern. */
  size?: number
  /** The width (stroke thickness) of the lines. */
  strokeWidth?: number
  /**
   * The rendering method for lines, affecting performance versus quality.
   * Options: 'auto', 'optimizeSpeed', 'crispEdges', 'geometricPrecision'.
   */
  shapeRendering?: 'auto' | 'optimizeSpeed' | 'crispEdges' | 'geometricPrecision'
  /** The background color of the pattern area. */
  background?: string
  /** The stroke color of the lines. */
  stroke?: string
  /** The color of the lines (alias for stroke). */
  color?: string
  /** If `true`, uses a complementary color scheme (specifics depend on textures.js implementation). */
  complement?: boolean
  /**
   * An array specifying the orientation of lines (e.g., `['diagonal']`, `['vertical', 'horizontal']`).
   * Refer to textures.js documentation for available orientations.
   */
  orientation?: string[]
  /** A modifier for line weight, such as 'normal', 'heavier', or 'lighter'. */
  weight?: 'normal' | 'heavier' | 'lighter'
}

/**
 * Defines configuration options for circle-based patterns generated by `textures.js`.
 *
 * @remarks
 * These options allow customization of circle patterns, including circle spacing (`size`),
 * radius, rendering quality (`shapeRendering`), background color, and whether the
 * circles are filled (`'solid'`) or outlined (`'hollow'`).
 *
 * @see {@link PatternType.TEXTURE_CIRCLES}
 * @see {@link https://riccardoscalco.github.io/textures/#circles | textures.js circles documentation}
 */
export interface TextureCirclesOptions {
  /** The spacing between circles in the pattern. */
  size?: number
  /** The radius of the circles. */
  radius?: number
  /**
   * The rendering method for circles.
   * Options: 'auto', 'optimizeSpeed', 'crispEdges', 'geometricPrecision'.
   */
  shapeRendering?: 'auto' | 'optimizeSpeed' | 'crispEdges' | 'geometricPrecision'
  /** The background color of the pattern area. */
  background?: string
  /** The stroke color of the circles. */
  stroke?: string
  /** If `true`, uses a complementary color scheme. */
  complement?: boolean
  /** Specifies the fill color of the circles. Can be a color string (e.g., '#FF0000', 'blue') or 'transparent'. */
  fill?: string
}

/**
 * Defines configuration options for custom path-based patterns generated by `textures.js`.
 *
 * @remarks
 * These options enable the creation of patterns using SVG path data (`d` property).
 * Customizations include path instance spacing (`size`), rendering quality (`shapeRendering`),
 * and background color.
 *
 * @see {@link PatternType.TEXTURE_PATHS}
 * @see {@link https://riccardoscalco.github.io/textures/#paths | textures.js paths documentation}
 */
export interface TexturePathsOptions {
  /** The spacing between path instances in the pattern. */
  size?: number
  /**
   * An SVG path data string (the `d` attribute of an SVG `<path>`) defining the shape of the path,
   * or a predefined keyword (e.g., "waves", "crosses", "hexagons").
   */
  d?: string
  /**
   * The rendering method for paths.
   * Options: 'auto', 'optimizeSpeed', 'crispEdges', 'geometricPrecision'.
   */
  shapeRendering?: 'auto' | 'optimizeSpeed' | 'crispEdges' | 'geometricPrecision'
  /** The background color of the pattern area. */
  background?: string
  /** The stroke color of the paths. */
  stroke?: string
  /** The fill color of the paths. */
  fill?: string
  /** If `true`, uses a complementary color scheme. */
  complement?: boolean
}

/**
 * Defines a comprehensive structure for configuring various types of fill patterns.
 *
 * @remarks
 * This interface is designed to be flexible, accommodating different pattern sources
 * and configurations. It includes general properties applicable to most patterns
 * (e.g., `id`, `type`, `source`, `color`, `opacity`) as well as specific options
 * tailored for patterns generated by libraries like `textures.js` (via `textureType`
 * and the corresponding `*Options` properties).
 *
 * It can be used to define:
 * - Image-based patterns (by providing a URL or data URI in `source`).
 * - Gradient patterns (potentially defined via `colors` or a `source` string).
 * - Procedural textures generated by `textures.js` (by setting `textureType` and
 *   the relevant options object like `linesOptions`).
 * - Other custom pattern types.
 *
 * @see {@link PatternType} for `textures.js` specific pattern types.
 * @see {@link TextureLinesOptions}, {@link TextureCirclesOptions}, {@link TexturePathsOptions}
 *      for options specific to `textures.js` patterns.
 */
export interface PatternDefinition {
  /** A unique identifier for this pattern definition, primarily for internal use or referencing. */
  id: string
  /** The general type of the pattern, which can include textures.js types or other custom types. */
  type: PatternType | string // Allow string for other custom pattern types not in PatternType enum
  /**
   * The source of the pattern. This can be a URL to an image file,
   * a data URI, or a definition string for gradients or custom procedural patterns.
   */
  source?: string
  /** A scale factor to be applied to image-based or custom patterns. */
  scale?: number
  /** An array of color strings, typically used for defining gradients. */
  colors?: string[]

  /** The primary color (foreground color) of the pattern. */
  color?: string
  /** The background color for the pattern area. */
  backgroundColor?: string
  /** A general size parameter for the pattern, interpretation depends on the pattern type. */
  size?: number
  /** The rotation angle of the pattern in degrees. */
  angle?: number
  /** The opacity of the pattern, ranging from 0 (fully transparent) to 1 (fully opaque). */
  opacity?: number
  /** An optional explicit ID to be used for the SVG `<pattern>` element if rendered as SVG. */
  patternId?: string

  // textures.js specific properties
  /**
   * Specifies the type of textures.js texture to generate.
   * Should correspond to one of 'lines', 'circles', 'paths', or 'hexagons'.
   */
  textureType?: 'lines' | 'circles' | 'paths' | 'hexagons'
  /** Options specific to textures.js line patterns, used if `textureType` is 'lines'. */
  linesOptions?: TextureLinesOptions
  /** Options specific to textures.js circle patterns, used if `textureType` is 'circles'. */
  circlesOptions?: TextureCirclesOptions
  /** Options specific to textures.js path patterns, used if `textureType` is 'paths'. */
  pathsOptions?: TexturePathsOptions
}
