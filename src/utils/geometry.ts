/**
 * Geometry utility functions for element layout and positioning
 */

import { ElementType } from '@/types/core/elementDefinitions'

/**
 * Utility function to determine the number of columns for dimension controls
 * based on the element type.
 *
 * @param elementType - The type of element
 * @returns The number of columns to use for dimension controls
 */
export function getDimensionColumns(elementType?: ElementType): 1 | 2 | 3 {
  if (elementType === undefined || elementType === null)
    return 2

  switch (elementType) {
    // Single dimension controls (1 column)
    case ElementType.CIRCLE:
      return 1 // Single radius control

    // Two dimension controls (2 columns)
    case ElementType.RECTANGLE:
    case ElementType.ELLIPSE:
    case ElementType.IMAGE:
    case ElementType.TEXT:
    case ElementType.TEXT_LABEL:
      return 2 // Width/Height or RadiusX/RadiusY

    // Path elements don't use standard dimensions
    case ElementType.LINE:
    case ElementType.POLYLINE:
    case ElementType.ARC:
    case ElementType.QUADRATIC:
    case ElementType.CUBIC:
      return 2 // Key points layout

    // Architectural elements - typically 2 columns
    case ElementType.WALL:
    case ElementType.DOOR:
    case ElementType.WINDOW:
    case ElementType.FURNITURE:
    case ElementType.FIXTURE:
    case ElementType.ROOM:
    case ElementType.LIGHT:
    case ElementType.FLOOR_AREA:
    case ElementType.HANDRAIL:
    case ElementType.ELECTRICAL_OUTLET:
    case ElementType.ROOM_BOUNDARY:
    case ElementType.APPLIANCE:
    case ElementType.GROUP:
    case ElementType.OPENING:
    case ElementType.WALL_PAINT:
    case ElementType.WALL_PAPER:
      return 2

    default:
      return 2
  }
}

/**
 * Utility function to determine if an element type supports rotation controls.
 *
 * @param elementType - The type of element
 * @returns Whether the element supports rotation
 */
export function supportsRotation(elementType?: ElementType): boolean {
  if (elementType === undefined || elementType === null)
    return true

  // Elements that don't support rotation based on design document
  switch (elementType) {
    case ElementType.CIRCLE:
      return false // Circle rotation is meaningless
    case ElementType.ARC:
      return false // Arc has start/end angles instead of rotation

    // All other elements support rotation
    case ElementType.RECTANGLE:
    case ElementType.SQUARE:
    case ElementType.ELLIPSE:
    case ElementType.POLYGON:
    case ElementType.TRIANGLE:
    case ElementType.QUADRILATERAL:
    case ElementType.PENTAGON:
    case ElementType.HEXAGON:
    case ElementType.HEPTAGON:
    case ElementType.OCTAGON:
    case ElementType.NONAGON:
    case ElementType.DECAGON:
    case ElementType.LINE:
    case ElementType.POLYLINE:
    case ElementType.QUADRATIC:
    case ElementType.CUBIC:
    case ElementType.TEXT_LABEL:
    case ElementType.WALL:
    case ElementType.DOOR:
    case ElementType.WINDOW:
    case ElementType.FURNITURE:
    case ElementType.FIXTURE:
    case ElementType.ROOM:
    case ElementType.LIGHT:
    case ElementType.FLOOR_AREA:
    case ElementType.HANDRAIL:
    case ElementType.ELECTRICAL_OUTLET:
    case ElementType.ROOM_BOUNDARY:
    case ElementType.APPLIANCE:
    case ElementType.TEXT:
    case ElementType.IMAGE:
    case ElementType.GROUP:
    case ElementType.OPENING:
    case ElementType.WALL_PAINT:
    case ElementType.WALL_PAPER:
      return true

    default:
      return true
  }
}

/**
 * Utility function to determine if an element type supports position controls.
 *
 * @param elementType - The type of element
 * @returns Whether the element supports position controls
 */
export function supportsPosition(elementType?: ElementType): boolean {
  if (elementType === undefined || elementType === null)
    return true

  // Path elements might use key points instead of position
  switch (elementType) {
    case ElementType.LINE:
    case ElementType.POLYLINE:
    case ElementType.ARC:
    case ElementType.QUADRATIC:
    case ElementType.CUBIC:
      return false // These use key points instead

    // All other elements support position
    case ElementType.RECTANGLE:
    case ElementType.SQUARE:
    case ElementType.ELLIPSE:
    case ElementType.CIRCLE:
    case ElementType.POLYGON:
    case ElementType.TRIANGLE:
    case ElementType.QUADRILATERAL:
    case ElementType.PENTAGON:
    case ElementType.HEXAGON:
    case ElementType.HEPTAGON:
    case ElementType.OCTAGON:
    case ElementType.NONAGON:
    case ElementType.DECAGON:
    case ElementType.TEXT_LABEL:
    case ElementType.WALL:
    case ElementType.DOOR:
    case ElementType.WINDOW:
    case ElementType.FURNITURE:
    case ElementType.FIXTURE:
    case ElementType.ROOM:
    case ElementType.LIGHT:
    case ElementType.FLOOR_AREA:
    case ElementType.HANDRAIL:
    case ElementType.ELECTRICAL_OUTLET:
    case ElementType.ROOM_BOUNDARY:
    case ElementType.APPLIANCE:
    case ElementType.TEXT:
    case ElementType.IMAGE:
    case ElementType.GROUP:
    case ElementType.OPENING:
    case ElementType.WALL_PAINT:
    case ElementType.WALL_PAPER:
      return true

    default:
      return true
  }
}
