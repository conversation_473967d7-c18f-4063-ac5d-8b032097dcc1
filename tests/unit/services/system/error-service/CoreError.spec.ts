import type { ErrorContext } from '@/types/services/errors'
import { describe, expect, it } from 'vitest'
import { CoreError, createError } from '@/services/system/error-service/coreError'
import { ErrorSeverity, ErrorType } from '@/types/services/errors'

describe('coreError', () => {
  describe('constructor', () => {
    it('should create a CoreError with required parameters', () => {
      const error = new CoreError(ErrorType.ValidationError, 'Test validation error')

      expect(error).toBeInstanceOf(Error)
      expect(error).toBeInstanceOf(CoreError)
      expect(error.type).toBe(ErrorType.ValidationError)
      expect(error.message).toBe('Test validation error')
      expect(error.severity).toBe(ErrorSeverity.Medium) // Default severity
      expect(error.context).toBeUndefined()
      expect(error.name).toBe('CoreError')
    })

    it('should create a CoreError with all parameters', () => {
      const context: ErrorContext = {
        component: 'TestComponent',
        operation: 'testOperation',
        target: 'testTarget',
      }

      const error = new CoreError(
        ErrorType.SystemError,
        'Test system error',
        ErrorSeverity.Critical,
        context,
      )

      expect(error.type).toBe(ErrorType.SystemError)
      expect(error.message).toBe('Test system error')
      expect(error.severity).toBe(ErrorSeverity.Critical)
      expect(error.context).toEqual(context)
      expect(error.name).toBe('CoreError')
    })

    it('should handle different error types', () => {
      const errorTypes = [
        ErrorType.ValidationError,
        ErrorType.SystemError,
        ErrorType.NetworkError,
        ErrorType.UserError,
      ]

      errorTypes.forEach((type) => {
        const error = new CoreError(type, `Test ${type} error`)
        expect(error.type).toBe(type)
        expect(error.message).toBe(`Test ${type} error`)
      })
    })

    it('should handle different severity levels', () => {
      const severities = [
        ErrorSeverity.Low,
        ErrorSeverity.Medium,
        ErrorSeverity.High,
        ErrorSeverity.Critical,
      ]

      severities.forEach((severity) => {
        const error = new CoreError(ErrorType.SystemError, 'Test error', severity)
        expect(error.severity).toBe(severity)
      })
    })

    it('should set prototype chain correctly', () => {
      const error = new CoreError(ErrorType.ValidationError, 'Test error')

      expect(Object.getPrototypeOf(error)).toBe(CoreError.prototype)
      expect(error instanceof Error).toBe(true)
      expect(error instanceof CoreError).toBe(true)
    })

    it('should handle empty message', () => {
      const error = new CoreError(ErrorType.SystemError, '')

      expect(error.message).toBe('')
      expect(error.type).toBe(ErrorType.SystemError)
    })

    it('should handle context with partial properties', () => {
      const partialContext: ErrorContext = {
        component: 'TestComponent',
        // Missing operation and target
      }

      const error = new CoreError(
        ErrorType.ValidationError,
        'Test error',
        ErrorSeverity.Low,
        partialContext,
      )

      expect(error.context).toEqual(partialContext)
      expect(error.context?.component).toBe('TestComponent')
      expect(error.context?.operation).toBeUndefined()
      expect(error.context?.target).toBeUndefined()
    })

    it('should handle context with all properties', () => {
      const fullContext: ErrorContext = {
        component: 'TestComponent',
        operation: 'testOperation',
        target: 'testTarget',
      }

      const error = new CoreError(
        ErrorType.NetworkError,
        'Network error',
        ErrorSeverity.High,
        fullContext,
      )

      expect(error.context).toEqual(fullContext)
      expect(error.context?.component).toBe('TestComponent')
      expect(error.context?.operation).toBe('testOperation')
      expect(error.context?.target).toBe('testTarget')
    })
  })

  describe('inheritance behavior', () => {
    it('should behave like a native Error', () => {
      const error = new CoreError(ErrorType.SystemError, 'Test error')

      expect(error.toString()).toContain('CoreError: Test error')
      expect(error.stack).toBeDefined()
    })

    it('should be catchable as Error', () => {
      expect(() => {
        throw new CoreError(ErrorType.ValidationError, 'Test error')
      }).toThrow(Error)
    })

    it('should be catchable as CoreError', () => {
      expect(() => {
        throw new CoreError(ErrorType.ValidationError, 'Test error')
      }).toThrow(CoreError)
    })

    it('should maintain stack trace', () => {
      const error = new CoreError(ErrorType.SystemError, 'Test error')

      expect(error.stack).toBeDefined()
      expect(error.stack).toContain('CoreError: Test error')
    })
  })

  describe('property immutability', () => {
    it('should have readonly type property', () => {
      const error = new CoreError(ErrorType.ValidationError, 'Test error')

      // TypeScript should prevent this, but let's test runtime behavior
      expect(() => {
        (error as any).type = ErrorType.SystemError
      }).not.toThrow() // JavaScript allows this, but TypeScript prevents it

      // The property should still be the original value due to readonly
      expect(error.type).toBe(ErrorType.ValidationError)
    })

    it('should have readonly severity property', () => {
      const error = new CoreError(ErrorType.ValidationError, 'Test error', ErrorSeverity.High)

      expect(error.severity).toBe(ErrorSeverity.High)
    })

    it('should have readonly context property', () => {
      const context: ErrorContext = { component: 'TestComponent' }
      const error = new CoreError(ErrorType.ValidationError, 'Test error', ErrorSeverity.Medium, context)

      expect(error.context).toEqual(context)
    })
  })
})

describe('createError factory function', () => {
  it('should create CoreError with required parameters', () => {
    const error = createError(ErrorType.ValidationError, 'Factory test error')

    expect(error).toBeInstanceOf(CoreError)
    expect(error.type).toBe(ErrorType.ValidationError)
    expect(error.message).toBe('Factory test error')
    expect(error.severity).toBe(ErrorSeverity.Medium) // Default
    expect(error.context).toBeUndefined()
  })

  it('should create CoreError with all parameters', () => {
    const context: ErrorContext = {
      component: 'FactoryComponent',
      operation: 'factoryOperation',
      target: 'factoryTarget',
    }

    const error = createError(
      ErrorType.NetworkError,
      'Factory network error',
      ErrorSeverity.Critical,
      context,
    )

    expect(error).toBeInstanceOf(CoreError)
    expect(error.type).toBe(ErrorType.NetworkError)
    expect(error.message).toBe('Factory network error')
    expect(error.severity).toBe(ErrorSeverity.Critical)
    expect(error.context).toEqual(context)
  })

  it('should create CoreError with partial parameters', () => {
    const error = createError(
      ErrorType.UserError,
      'Factory user error',
      ErrorSeverity.Low,
    )

    expect(error).toBeInstanceOf(CoreError)
    expect(error.type).toBe(ErrorType.UserError)
    expect(error.message).toBe('Factory user error')
    expect(error.severity).toBe(ErrorSeverity.Low)
    expect(error.context).toBeUndefined()
  })

  it('should create different error types', () => {
    const errorTypes = [
      ErrorType.ValidationError,
      ErrorType.SystemError,
      ErrorType.NetworkError,
      ErrorType.UserError,
    ]

    errorTypes.forEach((type) => {
      const error = createError(type, `Factory ${type} error`)
      expect(error.type).toBe(type)
      expect(error.message).toBe(`Factory ${type} error`)
    })
  })

  it('should handle empty context', () => {
    const error = createError(
      ErrorType.SystemError,
      'Test error',
      ErrorSeverity.Medium,
      undefined,
    )

    expect(error.context).toBeUndefined()
  })

  it('should handle empty message', () => {
    const error = createError(ErrorType.ValidationError, '')

    expect(error.message).toBe('')
    expect(error.type).toBe(ErrorType.ValidationError)
  })
})
