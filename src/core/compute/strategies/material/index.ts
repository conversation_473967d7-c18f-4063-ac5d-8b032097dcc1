/**
 * Material Calculation Strategies Index
 *
 * @remarks
 * This module serves as a barrel export for all concrete strategy implementations
 * related to material calculation (e.g., paint, flooring, tiles). It re-exports the
 * `MaterialCalculatorStrategy` interface (and other relevant material computation types)
 * from `@/types/core/compute/materialComputeTypes`, and then exports all specific
 * material calculation strategy classes defined within this directory
 * (e.g., {@link CurtainMaterialStrategy}, {@link FlooringMaterialStrategy}).
 *
 * Note: A previous deprecation notice mentioned strategies moved to '@/lib/utils/material'.
 * However, current exports point to strategies within this `core/compute/strategies/material` directory.
 * This file acts as the central access point for these material strategies.
 *
 * @module core/compute/strategies/material/index
 */

// Re-export concrete strategy implementations from their new location
export * from '@/core/compute/strategies/material/CurtainMaterialStrategy'

export * from '@/core/compute/strategies/material/FlooringMaterialStrategy'
export * from '@/core/compute/strategies/material/FloorTileMaterialStrategy'
export * from '@/core/compute/strategies/material/PaintMaterialStrategy'
export * from '@/core/compute/strategies/material/StoneMaterialStrategy'
export * from '@/core/compute/strategies/material/WallPaintMaterialStrategy'
export * from '@/core/compute/strategies/material/WallpaperMaterialStrategy'
export * from '@/core/compute/strategies/material/WoodMaterialStrategy'
// Export interface first for better code organization (remains from original)
export * from '@/types/core/compute/materialComputeTypes' // This exports MaterialCalculatorStrategy etc.
