import { describe, expect, it } from 'vitest'

import { ValidationErrorCode } from '@/types/core/validator/error-codes'
// Import all validator types from the validator module
import * as ValidatorModule from '@/types/core/validator/validator'

describe('validator Module', () => {
  it('should export all validator types', () => {
    // Verify that the module exists
    expect(ValidatorModule).toBeDefined()

    // Verify that the module exports ValidationErrorCode
    expect(ValidatorModule.ValidationErrorCode).toBeDefined()
    expect(ValidatorModule.ValidationErrorCode.MISSING_OR_INVALID_ID).toBe(ValidationErrorCode.MISSING_OR_INVALID_ID)

    // Verify that the module exports ValidationOptions interface
    // We can't directly test the interface, but we can create an object that conforms to it
    const options: ValidatorModule.ValidationOptions = {
      strict: true,
      requiredOnly: false,
    }
    expect(options.strict).toBe(true)
    expect(options.requiredOnly).toBe(false)

    // Verify that the module exports ValidationResult interface
    const result: ValidatorModule.ValidationResult = {
      valid: true,
      message: 'Validation passed',
    }
    expect(result.valid).toBe(true)
    expect(result.message).toBe('Validation passed')
  })
})
