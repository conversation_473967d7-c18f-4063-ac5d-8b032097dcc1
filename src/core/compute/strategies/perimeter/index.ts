/**
 * Perimeter Calculation Strategies Index
 *
 * @remarks
 * This module serves as a barrel export for all concrete strategy implementations
 * related to perimeter or length calculation of elements. It re-exports the
 * `PerimeterCalculatorStrategy` interface (and other relevant compute types) from
 * `@/types/core/compute/perimeterComputeTypes` and `@/types/core/compute`,
 * and then exports all specific perimeter calculation strategy classes defined
 * within this directory (e.g., {@link ArcPerimeterStrategy}, {@link RectanglePerimeterStrategy}).
 *
 * This allows for a centralized import point for accessing various perimeter
 * calculation strategies.
 *
 * @module core/compute/strategies/perimeter/index
 */

// Export concrete strategy implementations from the current directory
export * from './ArcPerimeterStrategy'
export * from './CubicPerimeterStrategy'

export * from './EllipsePerimeterStrategy'
export * from './LinePerimeterStrategy'
export * from './PolygonPerimeterStrategy'
export * from './PolylinePerimeterStrategy'
export * from './QuadraticPerimeterStrategy'
export * from './RectanglePerimeterStrategy'
// Also export the main BoundingBox type if it's used by any perimeter strategies, or general compute types
export * from '@/types/core/compute'
// Export interface first for better code organization
export * from '@/types/core/compute/perimeterComputeTypes' // This exports PerimeterCalculatorStrategy etc.
