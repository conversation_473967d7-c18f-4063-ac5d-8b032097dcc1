import type {
  ShapeCreationRequest,
  ShapeCreationResult,
  ShapeCreationServiceOptions,
  ShapeDeleteRequest,
  ShapeDeleteResult,
  ShapeEditRequest,
  ShapeEditResult,
  ShapeSelectRequest,
  ShapeSelectResult,
} from '@/types/services/shapes'
import { describe, expect, it } from 'vitest'
import { ElementType } from '@/types/core'

describe('shape Service Types', () => {
  describe('shapeCreationServiceOptions', () => {
    it('should allow creating options with all properties', () => {
      const options: ShapeCreationServiceOptions = {
        snapToGrid: true,
        gridSize: 10,
        maintainAspectRatio: false,
      }

      expect(options.snapToGrid).toBe(true)
      expect(options.gridSize).toBe(10)
      expect(options.maintainAspectRatio).toBe(false)
    })
  })

  describe('shapeCreationRequest', () => {
    it('should allow creating a request with minimal properties', () => {
      const request: ShapeCreationRequest = {
        elementType: ElementType.RECTANGLE,
        position: { x: 100, y: 200 },
      }

      expect(request.elementType).toBe(ElementType.RECTANGLE)
      expect(request.position.x).toBe(100)
      expect(request.position.y).toBe(200)
      expect(request.properties).toBeUndefined()
    })

    it('should allow creating a request with additional properties', () => {
      const request: ShapeCreationRequest = {
        elementType: ElementType.ELLIPSE,
        position: { x: 150, y: 250 },
        properties: {
          width: 200,
          height: 100,
          fill: 'blue',
          stroke: 'black',
        },
      }

      expect(request.elementType).toBe(ElementType.ELLIPSE)
      expect(request.position.x).toBe(150)
      expect(request.position.y).toBe(250)
      expect(request.properties).toBeDefined()
      expect(request.properties?.width).toBe(200)
      expect(request.properties?.height).toBe(100)
      expect(request.properties?.fill).toBe('blue')
      expect(request.properties?.stroke).toBe('black')
    })
  })

  describe('shapeCreationResult', () => {
    it('should allow creating a successful result', () => {
      const result: ShapeCreationResult = {
        success: true,
        data: {
          id: 'shape-123',
          type: ElementType.RECTANGLE,
          position: { x: 100, y: 200 },
          width: 200,
          height: 100,
          style: { fill: 'red' },
        } as any, // Using 'as any' to avoid needing to implement the full ShapeElement interface
        timestamp: Date.now(),
      }

      expect(result.success).toBe(true)
      expect(result.data).toBeDefined()
      expect(result.data?.id).toBe('shape-123')
      expect(result.data?.type).toBe(ElementType.RECTANGLE)
      expect(result.timestamp).toBeGreaterThan(0)
    })

    it('should allow creating an error result', () => {
      const result: ShapeCreationResult = {
        success: false,
        error: 'Failed to create shape',
        timestamp: Date.now(),
      }

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to create shape')
      expect(result.timestamp).toBeGreaterThan(0)
      expect(result.data).toBeUndefined()
    })
  })

  describe('shapeEditRequest', () => {
    it('should allow creating a request with minimal properties', () => {
      const request: ShapeEditRequest = {
        id: 'shape-123',
      }

      expect(request.id).toBe('shape-123')
      expect(request.position).toBeUndefined()
      expect(request.style).toBeUndefined()
      expect(request.properties).toBeUndefined()
      expect(request.selectAfterEdit).toBeUndefined()
    })

    it('should allow creating a request with all properties', () => {
      const request: ShapeEditRequest = {
        id: 'shape-456',
        position: { x: 200, y: 300 },
        style: { fill: 'green', stroke: 'black', strokeWidth: 2 },
        properties: { width: 150, height: 75 },
        selectAfterEdit: true,
      }

      expect(request.id).toBe('shape-456')
      expect(request.position?.x).toBe(200)
      expect(request.position?.y).toBe(300)
      expect(request.style?.fill).toBe('green')
      expect(request.style?.stroke).toBe('black')
      expect(request.style?.strokeWidth).toBe(2)
      expect(request.properties?.width).toBe(150)
      expect(request.properties?.height).toBe(75)
      expect(request.selectAfterEdit).toBe(true)
    })
  })

  describe('shapeEditResult', () => {
    it('should allow creating a successful result', () => {
      const result: ShapeEditResult = {
        success: true,
        data: {
          id: 'shape-123',
          type: ElementType.RECTANGLE,
          position: { x: 200, y: 300 },
          width: 150,
          height: 75,
          style: { fill: 'green' },
        } as any, // Using 'as any' to avoid needing to implement the full ShapeElement interface
        timestamp: Date.now(),
      }

      expect(result.success).toBe(true)
      expect(result.data).toBeDefined()
      expect(result.data?.id).toBe('shape-123')
      expect(result.data?.position.x).toBe(200)
      expect(result.data?.position.y).toBe(300)
      expect(result.timestamp).toBeGreaterThan(0)
    })
  })

  describe('shapeDeleteRequest', () => {
    it('should allow creating a delete request', () => {
      const request: ShapeDeleteRequest = {
        id: 'shape-123',
      }

      expect(request.id).toBe('shape-123')
    })
  })

  describe('shapeDeleteResult', () => {
    it('should allow creating a successful result', () => {
      const result: ShapeDeleteResult = {
        success: true,
        data: 'shape-123',
        timestamp: Date.now(),
      }

      expect(result.success).toBe(true)
      expect(result.data).toBe('shape-123')
      expect(result.timestamp).toBeGreaterThan(0)
    })
  })

  describe('shapeSelectRequest', () => {
    it('should allow creating a request with minimal properties', () => {
      const request: ShapeSelectRequest = {
        id: 'shape-123',
      }

      expect(request.id).toBe('shape-123')
      expect(request.multiSelect).toBeUndefined()
    })

    it('should allow creating a request with multiSelect', () => {
      const request: ShapeSelectRequest = {
        id: 'shape-456',
        multiSelect: true,
      }

      expect(request.id).toBe('shape-456')
      expect(request.multiSelect).toBe(true)
    })
  })

  describe('shapeSelectResult', () => {
    it('should allow creating a successful result with single selection', () => {
      const result: ShapeSelectResult = {
        success: true,
        data: ['shape-123'],
        timestamp: Date.now(),
      }

      expect(result.success).toBe(true)
      expect(result.data).toHaveLength(1)
      expect(result.data?.[0]).toBe('shape-123')
      expect(result.timestamp).toBeGreaterThan(0)
    })

    it('should allow creating a successful result with multiple selections', () => {
      const result: ShapeSelectResult = {
        success: true,
        data: ['shape-123', 'shape-456', 'shape-789'],
        timestamp: Date.now(),
      }

      expect(result.success).toBe(true)
      expect(result.data).toHaveLength(3)
      expect(result.data?.[0]).toBe('shape-123')
      expect(result.data?.[1]).toBe('shape-456')
      expect(result.data?.[2]).toBe('shape-789')
      expect(result.timestamp).toBeGreaterThan(0)
    })
  })
})
