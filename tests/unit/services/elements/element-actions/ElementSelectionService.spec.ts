import type { AppEventMap, EventBus } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import type { ElementSelectRequest } from '@/types/services/shapes'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ElementSelectionService } from '@/services/elements/element-actions/elementSelectionService'
import { AppEventType } from '@/types/services/events'
import { SelectionMode } from '@/types/services/shapes'

// Mock dependencies
const mockEventBus = {
  publish: vi.fn(),
  subscribe: vi.fn(),
  unsubscribe: vi.fn(),
  clear: vi.fn(),
} as unknown as EventBus<AppEventMap>

const mockLogger = {
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  debug: vi.fn(),
  setContext: vi.fn(),
  getConfig: vi.fn(),
  setConfig: vi.fn(),
} as unknown as LoggerService

// Mock external dependencies
vi.mock('@/services/core/registry', () => ({
  getService: vi.fn((id) => {
    if (id === 'event-bus' || id.includes('EventBus'))
      return mockEventBus
    if (id === 'logger' || id.includes('Logger'))
      return mockLogger
    throw new Error(`Service ${id} not found`)
  }),
  ServiceId: {
    EventBus: 'event-bus',
    Logger: 'logger',
    ElementSelectionService: 'element-selection-service',
  },
}))

vi.mock('uuid', () => ({
  v4: vi.fn(() => 'mock-uuid-1234'),
}))

describe('elementSelectionService', () => {
  let selectionService: ElementSelectionService

  beforeEach(() => {
    vi.clearAllMocks()
    selectionService = new ElementSelectionService(mockEventBus, mockLogger)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('constructor', () => {
    it('should initialize with dependencies', () => {
      expect(selectionService).toBeDefined()
      expect(selectionService.serviceId).toBe('element-selection-service')
      expect(mockLogger.info).toHaveBeenCalledWith('[ElementSelectionService] Initialized.')
    })
  })

  describe('static create method', () => {
    it('should create instance with provided logger', () => {
      const instance = ElementSelectionService.create(mockLogger)
      expect(instance).toBeInstanceOf(ElementSelectionService)
    })

    it('should create instance without logger (using registry)', () => {
      const instance = ElementSelectionService.create()
      expect(instance).toBeInstanceOf(ElementSelectionService)
    })

    it('should throw error when dependencies cannot be resolved', async () => {
      // Import the mocked module and override the mock temporarily
      const registryModule = await import('@/services/core/registry')
      const mockGetService = vi.mocked(registryModule.getService)

      mockGetService.mockImplementationOnce(() => {
        throw new Error('Service not found')
      })

      expect(() => ElementSelectionService.create()).toThrow(
        /Failed to create ElementSelectionService:/,
      )
    })
  })

  describe('selectElement', () => {
    it('should select a single element successfully', async () => {
      const request: ElementSelectRequest = {
        id: 'element-1',
      }

      const result = await selectionService.selectElement(request)

      expect(result.success).toBe(true)
      expect(result.data).toEqual([])
      expect(result.timestamp).toBeDefined()

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ElementSelectRequest,
        timestamp: expect.any(Number),
        payload: {
          elementIds: ['element-1'],
          selectionMode: 'replace',
          source: 'ElementSelectionService',
        },
      })

      expect(mockLogger.debug).toHaveBeenCalledWith(
        '[ElementSelectionService] selectElement called',
        request,
      )
    })

    it('should select element with multiSelect mode', async () => {
      const request: ElementSelectRequest & { multiSelect: boolean } = {
        id: 'element-1',
        multiSelect: true,
      }

      const result = await selectionService.selectElement(request)

      expect(result.success).toBe(true)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ElementSelectRequest,
        timestamp: expect.any(Number),
        payload: {
          elementIds: ['element-1'],
          selectionMode: 'add',
          source: 'ElementSelectionService',
        },
      })
    })

    it('should select element with explicit selection mode', async () => {
      const request: ElementSelectRequest & { selectionMode: string } = {
        id: 'element-1',
        selectionMode: 'toggle',
      }

      const result = await selectionService.selectElement(request)

      expect(result.success).toBe(true)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ElementSelectRequest,
        timestamp: expect.any(Number),
        payload: {
          elementIds: ['element-1'],
          selectionMode: 'toggle',
          source: 'ElementSelectionService',
        },
      })
    })

    it('should handle errors during selection', async () => {
      const request: ElementSelectRequest = {
        id: 'element-1',
      }

      // Mock eventBus.publish to throw an error
      mockEventBus.publish.mockImplementationOnce(() => {
        throw new Error('Event bus error')
      })

      const result = await selectionService.selectElement(request)

      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
      expect(result.error?.code).toBe('ELEMENT_SELECT_ERROR')
      expect(result.error?.message).toBe('Event bus error')
      expect(result.timestamp).toBeDefined()
    })

    it('should handle unknown errors', async () => {
      const request: ElementSelectRequest = {
        id: 'element-1',
      }

      // Mock eventBus.publish to throw a non-Error object
      mockEventBus.publish.mockImplementationOnce(() => {
        throw 'String error'
      })

      const result = await selectionService.selectElement(request)

      expect(result.success).toBe(false)
      expect(result.error?.message).toBe('Unknown error')
    })
  })

  describe('selectElements', () => {
    it('should select multiple elements with default mode (add)', async () => {
      const elementIds = ['element-1', 'element-2', 'element-3']

      const result = await selectionService.selectElements(elementIds)

      expect(result.success).toBe(true)
      expect(result.data).toEqual([])
      expect(result.timestamp).toBeDefined()

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ElementSelectRequest,
        timestamp: expect.any(Number),
        payload: {
          elementIds,
          selectionMode: 'add',
          source: 'ElementSelectionService',
        },
      })

      expect(mockLogger.debug).toHaveBeenCalledWith(
        '[ElementSelectionService] selectElements called',
        { elementIds, clearExisting: false, selectionMode: undefined },
      )
    })

    it('should select multiple elements with replace mode', async () => {
      const elementIds = ['element-1', 'element-2']

      const result = await selectionService.selectElements(elementIds, true)

      expect(result.success).toBe(true)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ElementSelectRequest,
        timestamp: expect.any(Number),
        payload: {
          elementIds,
          selectionMode: 'replace',
          source: 'ElementSelectionService',
        },
      })
    })

    it('should select multiple elements with explicit selection mode', async () => {
      const elementIds = ['element-1', 'element-2']

      const result = await selectionService.selectElements(elementIds, false, 'toggle')

      expect(result.success).toBe(true)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ElementSelectRequest,
        timestamp: expect.any(Number),
        payload: {
          elementIds,
          selectionMode: 'toggle',
          source: 'ElementSelectionService',
        },
      })
    })

    it('should handle empty array', async () => {
      const result = await selectionService.selectElements([])

      expect(result.success).toBe(true)
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ElementSelectRequest,
        timestamp: expect.any(Number),
        payload: {
          elementIds: [],
          selectionMode: 'add',
          source: 'ElementSelectionService',
        },
      })
    })

    it('should handle errors during multiple selection', async () => {
      const elementIds = ['element-1', 'element-2']

      // Mock eventBus.publish to throw an error
      mockEventBus.publish.mockImplementationOnce(() => {
        throw new Error('Event bus error')
      })

      const result = await selectionService.selectElements(elementIds)

      expect(result.success).toBe(false)
      expect(result.error?.code).toBe('ELEMENT_SELECT_ERROR')
      expect(result.error?.message).toBe('Event bus error')
    })
  })

  describe('clearElementSelection', () => {
    it('should clear selection successfully', async () => {
      const result = await selectionService.clearElementSelection()

      expect(result.success).toBe(true)
      expect(result.timestamp).toBeDefined()

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ElementSelectRequest,
        timestamp: expect.any(Number),
        payload: {
          elementIds: [],
          selectionMode: SelectionMode.Clear,
          source: 'ElementSelectionService',
        },
      })

      expect(mockLogger.debug).toHaveBeenCalledWith(
        '[ElementSelectionService] Publishing ElementSelectRequest to clear selection',
      )
    })

    it('should handle errors during clear selection', async () => {
      // Mock eventBus.publish to throw an error
      mockEventBus.publish.mockImplementationOnce(() => {
        throw new Error('Event bus error')
      })

      const result = await selectionService.clearElementSelection()

      expect(result.success).toBe(false)
      expect(result.error?.code).toBe('ELEMENT_SELECT_ERROR')
      expect(result.error?.message).toBe('Event bus error')
    })
  })

  describe('error handling', () => {
    it('should emit error events correctly', async () => {
      const request: ElementSelectRequest = {
        id: 'element-1',
      }

      // Mock eventBus.publish to throw an error
      mockEventBus.publish.mockImplementationOnce(() => {
        throw new Error('Test error')
      })

      await selectionService.selectElement(request)

      expect(mockLogger.error).toHaveBeenCalledWith(
        '[ELEMENT_SELECT_ERROR] Test error',
        expect.objectContaining({
          errorId: 'mock-uuid-1234',
          request,
        }),
      )

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.EventError,
        timestamp: expect.any(Number),
        payload: {
          error: {
            code: 'ELEMENT_SELECT_ERROR',
            message: '[ELEMENT_SELECT_ERROR] Test error',
            details: { request },
            errorId: 'mock-uuid-1234',
          },
          context: { request },
        },
      })
    })
  })

  describe('edge cases', () => {
    it('should handle very long element ID arrays', async () => {
      const longArray = Array.from({ length: 1000 }, (_, i) => `element-${i}`)

      const result = await selectionService.selectElements(longArray)

      expect(result.success).toBe(true)
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ElementSelectRequest,
        timestamp: expect.any(Number),
        payload: {
          elementIds: longArray,
          selectionMode: 'add',
          source: 'ElementSelectionService',
        },
      })
    })

    it('should handle special characters in element IDs', async () => {
      const specialIds = ['element-@#$%', 'element-with-unicode-🚀', 'element with spaces']

      const result = await selectionService.selectElements(specialIds)

      expect(result.success).toBe(true)
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ElementSelectRequest,
        timestamp: expect.any(Number),
        payload: {
          elementIds: specialIds,
          selectionMode: 'add',
          source: 'ElementSelectionService',
        },
      })
    })

    it('should handle null and undefined element IDs gracefully', async () => {
      const mixedIds = ['element-1', null as any, undefined as any, 'element-2']

      const result = await selectionService.selectElements(mixedIds)

      expect(result.success).toBe(true)
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ElementSelectRequest,
        timestamp: expect.any(Number),
        payload: {
          elementIds: mixedIds,
          selectionMode: 'add',
          source: 'ElementSelectionService',
        },
      })
    })

    it('should handle complex selection mode combinations', async () => {
      const request: ElementSelectRequest & { multiSelect: boolean, selectionMode: string } = {
        id: 'element-1',
        multiSelect: true,
        selectionMode: 'custom-mode',
      }

      const result = await selectionService.selectElement(request)

      expect(result.success).toBe(true)

      // selectionMode should take precedence over multiSelect
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ElementSelectRequest,
        timestamp: expect.any(Number),
        payload: {
          elementIds: ['element-1'],
          selectionMode: 'custom-mode',
          source: 'ElementSelectionService',
        },
      })
    })
  })
})
