import { beforeEach, describe, expect, it, vi } from 'vitest'
import { CoreError, ErrorType } from '@/core/errors'
import { PolylineCreator } from '@/core/factory/creators/PolylineCreator'
import * as geometryUtils from '@/core/utils/geometryUtils'
import { Point } from '@/types/core/element/geometry/point'
import { ElementType } from '@/types/core/shape-type'

// 模拟 console 方法，但不阻止实际输出
vi.spyOn(console, 'error')
vi.spyOn(console, 'warn')
vi.spyOn(console, 'debug')

describe('polylineCreator', () => {
  let creator: PolylineCreator

  beforeEach(() => {
    creator = new PolylineCreator()
    vi.clearAllMocks()
  })

  describe('create', () => {
    it('应该创建折线 ShapeModel', async () => {
      const params = {
        id: 'polyline-1',
        type: ElementType.POLYLINE,
        points: [
          { x: 10, y: 20 },
          { x: 50, y: 50 },
          { x: 100, y: 20 },
        ],
        stroke: 'black',
        strokeWidth: 2,
      }

      const result = await creator.create(params)

      expect(result).toEqual({
        id: 'polyline-1',
        type: ElementType.POLYLINE,
        position: { x: 53.333333333333336, y: 30 }, // 折线的位置是所有点的平均值
        properties: {
          type: ElementType.POLYLINE,
          points: [
            { x: 10, y: 20 },
            { x: 50, y: 50 },
            { x: 100, y: 20 },
          ],
          stroke: 'black',
          strokeWidth: 2,
          opacity: undefined,
          fill: undefined,
          strokeDasharray: undefined,
        },
        selected: false,
        visible: true,
        layer: undefined,
        zIndex: undefined,
        name: undefined,
        label: undefined,
        metadata: expect.objectContaining({
          createdAt: expect.any(Number),
          updatedAt: expect.any(Number),
        }),
      })
    })

    it('应该处理 Point 实例作为点数组', async () => {
      const params = {
        id: 'polyline-2',
        type: ElementType.POLYLINE,
        points: [
          new Point(10, 20),
          new Point(50, 50),
          new Point(100, 20),
        ],
      }

      const result = await creator.create(params)

      expect(result.properties.points).toEqual([
        { x: 10, y: 20 },
        { x: 50, y: 50 },
        { x: 100, y: 20 },
      ])
    })

    it('应该处理数组作为点数组', async () => {
      const params = {
        id: 'polyline-3',
        type: ElementType.POLYLINE,
        points: [
          [10, 20] as [number, number],
          [50, 50] as [number, number],
          [100, 20] as [number, number],
        ],
      }

      const result = await creator.create(params)

      expect(result.properties.points).toEqual([
        { x: 10, y: 20 },
        { x: 50, y: 50 },
        { x: 100, y: 20 },
      ])
    })

    it('当类型不正确时应该抛出错误', async () => {
      const params = {
        id: 'circle-1',
        type: ElementType.CIRCLE as any,
        points: [
          { x: 10, y: 20 },
          { x: 50, y: 50 },
        ],
      }

      await expect(creator.create(params)).rejects.toThrow(CoreError)
    })

    it('当点数组缺失时应该抛出错误', async () => {
      const params = {
        id: 'polyline-4',
        type: ElementType.POLYLINE,
      } as any

      await expect(creator.create(params)).rejects.toThrow(CoreError)
      await expect(creator.create(params)).rejects.toThrow('Polyline creation requires an array of at least two points')
    })

    it('当点数组少于2个点时应该抛出错误', async () => {
      const params = {
        id: 'polyline-5',
        type: ElementType.POLYLINE,
        points: [{ x: 10, y: 20 }],
      }

      await expect(creator.create(params)).rejects.toThrow(CoreError)
      await expect(creator.create(params)).rejects.toThrow('Polyline creation requires an array of at least two points')
    })

    it('当点数组不是数组时应该抛出错误', async () => {
      const params = {
        id: 'polyline-not-array',
        type: ElementType.POLYLINE,
        points: 'not-an-array',
      } as any

      await expect(creator.create(params)).rejects.toThrow(CoreError)
      await expect(creator.create(params)).rejects.toThrow('Polyline creation requires an array of at least two points')
    })

    it('当点数组包含无效点时应该抛出错误', async () => {
      const params = {
        id: 'polyline-invalid-point',
        type: ElementType.POLYLINE,
        points: [
          { x: 10, y: 20 },
          'invalid-point',
        ],
      } as any

      await expect(creator.create(params)).rejects.toThrow(CoreError)
      await expect(creator.create(params)).rejects.toThrow('Polyline creation failed: Invalid data in points array')
    })

    it('当点数组包含无效点且错误没有 message 属性时应该抛出错误', async () => {
      const params = {
        id: 'polyline-invalid-point-no-message',
        type: ElementType.POLYLINE,
        points: [
          { x: 10, y: 20 },
          { x: 30, y: 40 },
        ],
      }

      // 模拟 ensurePointInstance 抛出没有 message 属性的错误
      vi.spyOn(geometryUtils, 'ensurePointInstance').mockImplementation(() => {
        const error = new Error()
        delete error.message
        throw error
      })

      const testPromise = creator.create(params)
      await expect(testPromise).rejects.toThrow(CoreError)
      await expect(testPromise).rejects.toThrow('Polyline creation failed: Invalid data in points array')
    })

    it('应该使用提供的元数据', async () => {
      const metadata = { createdAt: 1000, updatedAt: 2000, customField: 'test' }
      const params = {
        id: 'polyline-6',
        type: ElementType.POLYLINE,
        points: [
          { x: 10, y: 20 },
          { x: 50, y: 50 },
        ],
        metadata,
      }

      const result = await creator.create(params)

      expect(result.metadata).toEqual(metadata)
    })
  })

  describe('createDefault', () => {
    it('应该创建默认折线 ShapeModel', async () => {
      const id = 'default-polyline'
      const position = new Point(0, 0)

      const result = await creator.createDefault(id, position)

      expect(result).toEqual({
        id,
        type: ElementType.POLYLINE,
        position: { x: 0, y: 0 },
        properties: {
          type: ElementType.POLYLINE,
          points: [
            { x: -50, y: 0 },
            { x: 50, y: 0 },
          ],
          curved: undefined,
          tension: undefined,
          stroke: undefined,
          strokeWidth: undefined,
          opacity: undefined,
          fill: undefined,
          strokeDasharray: undefined,
        },
        selected: false,
        visible: true,
        layer: undefined,
        zIndex: undefined,
        name: undefined,
        label: undefined,
        metadata: expect.objectContaining({
          createdAt: expect.any(Number),
          updatedAt: expect.any(Number),
        }),
      })

      // 确保至少有两个点
      expect(result.properties.points.length).toBeGreaterThanOrEqual(2)
    })

    it('应该处理创建过程中的错误', async () => {
      const id = 'error-polyline'
      const position = new Point(0, 0)

      // 模拟 create 方法抛出错误
      vi.spyOn(creator, 'create').mockImplementation(() => {
        throw new Error('Test error')
      })

      await expect(creator.createDefault(id, position)).rejects.toThrow(CoreError)
      await expect(creator.createDefault(id, position)).rejects.toThrow('Failed to create default Polyline ShapeModel')
    })

    it('应该处理创建过程中的错误没有 message 属性', async () => {
      const id = 'error-no-message-polyline'
      const position = new Point(0, 0)

      // 模拟 create 方法抛出没有 message 属性的错误
      vi.spyOn(creator, 'create').mockImplementation(() => {
        const error = new Error()
        delete error.message
        throw error
      })

      await expect(creator.createDefault(id, position)).rejects.toThrow(CoreError)
      await expect(creator.createDefault(id, position)).rejects.toThrow('Failed to create default Polyline ShapeModel')
    })

    it('应该处理默认创建中的无效位置', async () => {
      const id = 'invalid-position-polyline'
      const invalidPosition = 'invalid' as any

      // 模拟 ensurePointInstance 抛出错误
      vi.spyOn(geometryUtils, 'ensurePointInstance').mockImplementation(() => {
        throw new Error('Invalid position format')
      })

      await expect(creator.createDefault(id, invalidPosition)).rejects.toThrow(CoreError)
      await expect(creator.createDefault(id, invalidPosition)).rejects.toThrow('Failed to create default Polyline: Invalid position provided')
    })

    it('应该处理默认创建中的无效位置且错误没有 message 属性', async () => {
      const id = 'invalid-position-no-message-polyline'
      const invalidPosition = 'invalid' as any

      // 模拟 ensurePointInstance 抛出没有 message 属性的错误
      vi.spyOn(geometryUtils, 'ensurePointInstance').mockImplementation(() => {
        const error = new Error()
        delete error.message
        throw error
      })

      await expect(creator.createDefault(id, invalidPosition)).rejects.toThrow(CoreError)
      await expect(creator.createDefault(id, invalidPosition)).rejects.toThrow('Failed to create default Polyline: Invalid position provided')
    })

    it('应该传递 CoreError 类型的错误', async () => {
      const id = 'core-error-polyline'
      const position = new Point(0, 0)

      // 模拟 create 方法抛出 CoreError
      const coreError = new CoreError(ErrorType.INVALID_PAYLOAD, 'Test CoreError')
      vi.spyOn(creator, 'create').mockImplementation(() => {
        throw coreError
      })

      await expect(creator.createDefault(id, position)).rejects.toThrow(coreError)
    })
  })
})
