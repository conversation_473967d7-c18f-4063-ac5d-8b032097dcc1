import { beforeEach, describe, expect, it } from 'vitest'
import { PolylinePathLengthStrategy } from '@/core/compute/strategies/path-length/PolylinePathLengthStrategy'
import { Point } from '@/types/core/element/geometry/point'
import { Polyline } from '@/types/core/element/path/polylinePathTypes'

// Mock Polyline class
class MockPolyline {
  private _points: Array<Point>
  id: string = 'mock-polyline-id'

  constructor(points: Array<{ x: number, y: number }>) {
    this._points = points.map(p => new Point(p.x, p.y))
  }

  getSubType() {
    return 'polyline'
  }

  getPoints() {
    return this._points
  }
}

// Mock non-polyline element
class MockRectangle {
  id: string = 'mock-rectangle'

  getSubType() {
    return 'rectangle'
  }
}

describe('polylinePathLengthStrategy', () => {
  let strategy: PolylinePathLengthStrategy

  beforeEach(() => {
    strategy = new PolylinePathLengthStrategy()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('polyline')
  })

  it('should calculate path length correctly for a horizontal line', () => {
    const points = [
      { x: 0, y: 0 },
      { x: 100, y: 0 },
    ]

    const polyline = new MockPolyline(points)

    // Make instanceof check pass
    Object.setPrototypeOf(polyline, Polyline.prototype)

    const length = strategy.calculatePerimeter(polyline as any)

    expect(length).toBe(100)
  })

  it('should calculate path length correctly for a vertical line', () => {
    const points = [
      { x: 0, y: 0 },
      { x: 0, y: 100 },
    ]

    const polyline = new MockPolyline(points)

    // Make instanceof check pass
    Object.setPrototypeOf(polyline, Polyline.prototype)

    const length = strategy.calculatePerimeter(polyline as any)

    expect(length).toBe(100)
  })

  it('should calculate path length correctly for a diagonal line', () => {
    const points = [
      { x: 0, y: 0 },
      { x: 3, y: 4 },
    ]

    const polyline = new MockPolyline(points)

    // Make instanceof check pass
    Object.setPrototypeOf(polyline, Polyline.prototype)

    const length = strategy.calculatePerimeter(polyline as any)

    expect(length).toBe(5) // 3-4-5 triangle
  })

  it('should calculate path length correctly for a multi-segment polyline', () => {
    const points = [
      { x: 0, y: 0 },
      { x: 100, y: 0 },
      { x: 100, y: 100 },
      { x: 0, y: 100 },
    ]

    const polyline = new MockPolyline(points)

    // Make instanceof check pass
    Object.setPrototypeOf(polyline, Polyline.prototype)

    const length = strategy.calculatePerimeter(polyline as any)

    expect(length).toBe(300) // 100 + 100 + 100
  })

  it('should handle polyline with fewer than 2 points', () => {
    const points = [{ x: 0, y: 0 }]

    const polyline = new MockPolyline(points)

    // Make instanceof check pass
    Object.setPrototypeOf(polyline, Polyline.prototype)

    const length = strategy.calculatePerimeter(polyline as any)

    expect(length).toBe(0)
  })

  it('should handle empty points array', () => {
    const points: Array<{ x: number, y: number }> = []

    const polyline = new MockPolyline(points)

    // Make instanceof check pass
    Object.setPrototypeOf(polyline, Polyline.prototype)

    const length = strategy.calculatePerimeter(polyline as any)

    expect(length).toBe(0)
  })

  it('should handle non-polyline elements', () => {
    const rectangle = new MockRectangle()

    const length = strategy.calculatePerimeter(rectangle as any)
    expect(isNaN(length)).toBe(true)
  })

  it('should handle invalid points', () => {
    // Create a mock that looks like a real Polyline instance with invalid points
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => [
        new Point(0, 0),
        new Point(100, 0),
        null,
        new Point(100, 100),
      ],
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const length = strategy.calculatePerimeter(realPolyline as any)

    // Should return NaN for invalid points
    expect(isNaN(length)).toBe(true)
  })

  it('should handle real Polyline instance', () => {
    // Create a mock that looks like a real Polyline instance
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => [
        new Point(0, 0),
        new Point(100, 0),
        new Point(100, 100),
      ],
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const length = strategy.calculatePerimeter(realPolyline as any)
    expect(length).toBe(200) // 100 + 100
  })

  it('should handle null points array in real Polyline instance', () => {
    // Create a mock that looks like a real Polyline instance with null points array
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => null,
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const length = strategy.calculatePerimeter(realPolyline as any)
    expect(length).toBe(0)
  })

  it('should handle error when accessing methods on Polyline', () => {
    // Create a mock that looks like a real Polyline instance but throws when getPoints is called
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => { throw new Error('Test error') },
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    // Wrap in try-catch to handle the error
    try {
      const length = strategy.calculatePerimeter(realPolyline as any)
      expect(isNaN(length)).toBe(true)
    }
    catch (error) {
      // If it throws, that's also acceptable
      expect(error).toBeDefined()
    }
  })
})
