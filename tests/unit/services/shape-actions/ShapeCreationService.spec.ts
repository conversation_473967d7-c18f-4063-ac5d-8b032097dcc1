import type { ElementFactory } from '@/core/factory'
import type { ShapeRepository } from '@/core/state/ShapeRepository'
import type { ShapeModel } from '@/types/core/models'
import type { EventBus, ShapeCreateEvent } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ErrorType } from '@/core/errors/CoreError'
import * as geometryUtilsModule from '@/core/utils/geometryUtils'
import * as validationUtilsModule from '@/core/utils/validationUtils'
import * as validatorModule from '@/core/validator'
import { ShapeCreationService } from '@/services/shape-actions/ShapeCreationService'
import { ElementType } from '@/types/core/shape-type'
import { AppEventType } from '@/types/services/events'

describe('shapeCreationService', () => {
  // Test dependencies
  let factory: ElementFactory
  let repository: ShapeRepository
  let eventBus: EventBus
  let logger: LoggerService
  let service: ShapeCreationService

  // Mock shape model
  const mockShapeModel: ShapeModel = {
    id: 'rectangle-123',
    type: ElementType.RECTANGLE,
    position: { x: 100, y: 100 },
    properties: {
      width: 200,
      height: 100,
    },
  }

  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks()

    // Mock ElementValidator.validateElement
    vi.spyOn(validatorModule.ElementValidator, 'validateElement').mockResolvedValue({
      isValid: true,
      errors: [],
    })

    // Mock handleValidationResultAndPublishError
    vi.spyOn(validationUtilsModule, 'handleValidationResultAndPublishError').mockReturnValue(true)

    // Mock ensurePointInstance
    vi.spyOn(geometryUtilsModule, 'ensurePointInstance').mockImplementation(point => ({
      x: point.x,
      y: point.y,
      toJson: () => ({ x: point.x, y: point.y }),
    }))

    // Create mock dependencies
    factory = {
      createShape: vi.fn().mockResolvedValue(mockShapeModel),
    } as unknown as ElementFactory

    repository = {
      add: vi.fn().mockResolvedValue(undefined),
    } as unknown as ShapeRepository

    eventBus = {
      publish: vi.fn(),
    } as unknown as EventBus

    logger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    } as unknown as LoggerService

    // Create service instance
    service = new ShapeCreationService(factory, repository, eventBus, logger)
  })

  describe('handleRequest', () => {
    it('should create a shape and publish success event when valid payload is provided', async () => {
      // Arrange
      const createEvent: ShapeCreateEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 200,
            height: 100,
          },
        },
      }

      // Act
      await service.handleRequest(createEvent)

      // Assert
      expect(validatorModule.ElementValidator.validateElement).toHaveBeenCalled()
      expect(factory.createShape).toHaveBeenCalledWith(ElementType.RECTANGLE, expect.objectContaining({
        type: ElementType.RECTANGLE,
        position: expect.objectContaining({ x: 100, y: 100 }),
      }))
      expect(repository.add).toHaveBeenCalledWith(mockShapeModel)
      expect(eventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        type: AppEventType.SHAPE_CREATE_COMPLETE,
        payload: expect.objectContaining({
          shapeId: mockShapeModel.id,
          ElementType: mockShapeModel.type,
        }),
      }))
    })

    it('should publish error event when payload is missing', async () => {
      // Arrange
      const invalidEvent: ShapeCreateEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: undefined,
      }

      // Act
      await service.handleRequest(invalidEvent)

      // Assert
      expect(eventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          code: ErrorType.INVALID_PAYLOAD,
        }),
      }))
      expect(factory.createShape).not.toHaveBeenCalled()
      expect(repository.add).not.toHaveBeenCalled()
    })

    it('should publish error event when validation fails', async () => {
      // Arrange
      const createEvent: ShapeCreateEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 200,
            height: 100,
          },
        },
      }

      // Mock validation failure
      vi.spyOn(validatorModule.ElementValidator, 'validateElement').mockResolvedValueOnce({
        isValid: false,
        errors: [{ message: 'Validation error', code: 'INVALID_SHAPE' }],
      })

      // Mock handleValidationResultAndPublishError to return false for this test
      vi.spyOn(validationUtilsModule, 'handleValidationResultAndPublishError').mockImplementationOnce((eventBus, validationResult, errorContext) => {
        // Simulate publishing an error event
        eventBus.publish({
          type: AppEventType.ERROR_OCCURRED,
          payload: {
            code: ErrorType.VALIDATION_FAILED,
            message: 'Validation failed',
          },
        })
        return false
      })

      // Act
      await service.handleRequest(createEvent)

      // Assert
      expect(validatorModule.ElementValidator.validateElement).toHaveBeenCalled()
      expect(eventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        type: AppEventType.ERROR_OCCURRED,
      }))
      expect(factory.createShape).not.toHaveBeenCalled()
      expect(repository.add).not.toHaveBeenCalled()
    })

    it('should handle factory creation failure', async () => {
      // Arrange
      const createEvent: ShapeCreateEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 200,
            height: 100,
          },
        },
      }

      // Mock factory failure
      const factoryError = new Error('Factory error')
      vi.mocked(factory.createShape).mockRejectedValue(factoryError)

      // Act
      await service.handleRequest(createEvent)

      // Assert
      expect(validatorModule.ElementValidator.validateElement).toHaveBeenCalled()
      expect(factory.createShape).toHaveBeenCalled()
      expect(eventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          code: ErrorType.FACTORY_CREATION_FAILED,
        }),
      }))
      expect(repository.add).not.toHaveBeenCalled()
    })

    it('should handle repository addition failure', async () => {
      // Arrange
      const createEvent: ShapeCreateEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 200,
            height: 100,
          },
        },
      }

      // Mock repository failure
      const repoError = new Error('Repository error')
      vi.mocked(repository.add).mockRejectedValue(repoError)

      // Act
      await service.handleRequest(createEvent)

      // Assert
      expect(validatorModule.ElementValidator.validateElement).toHaveBeenCalled()
      expect(factory.createShape).toHaveBeenCalled()
      expect(repository.add).toHaveBeenCalled()
      expect(eventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          code: ErrorType.COORDINATOR_OPERATION_FAILED,
        }),
      }))
    })
  })
})
