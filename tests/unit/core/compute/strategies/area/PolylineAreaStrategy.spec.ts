import { beforeEach, describe, expect, it, vi } from 'vitest'
import { PolylineAreaStrategy } from '@/core/compute/strategies/area/PolylineAreaStrategy'
import { CoreError } from '@/services/system/error-service'
import { ElementType } from '@/types/core/elementDefinitions'

// Mock the utility functions
vi.mock('@/lib/utils/geometry/polygonUtils', () => ({
  calculateArea: vi.fn((points) => {
    // Simple mock: return area based on number of points
    return points.length * 10
  }),
}))

describe('polylineAreaStrategy', () => {
  let strategy: PolylineAreaStrategy

  beforeEach(() => {
    strategy = new PolylineAreaStrategy()
    vi.clearAllMocks()
  })

  describe('constructor and Basic Properties', () => {
    it('should be defined and instantiated', () => {
      expect(strategy).toBeDefined()
      expect(strategy).toBeInstanceOf(PolylineAreaStrategy)
    })

    it('should return correct element type', () => {
      expect(strategy.getElementType()).toBe(ElementType.POLYLINE)
    })

    it('should have required methods', () => {
      expect(typeof strategy.calculateArea).toBe('function')
      expect(typeof strategy.getElementType).toBe('function')
    })
  })

  describe('area Calculation', () => {
    it('should calculate area for closed polyline correctly', () => {
      const polylineElement = {
        id: 'polyline-1',
        type: ElementType.POLYLINE,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 10, y: 0 },
            { x: 10, y: 10 },
            { x: 0, y: 10 },
          ],
          closed: true,
        },
      }

      const area = strategy.calculateArea(polylineElement as any)
      expect(area).toBe(40) // 4 points * 10 from our mock
    })

    it('should return 0 for open polyline', () => {
      const polylineElement = {
        id: 'polyline-2',
        type: ElementType.POLYLINE,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 10, y: 0 },
            { x: 10, y: 10 },
          ],
          closed: false,
        },
      }

      const area = strategy.calculateArea(polylineElement as any)
      expect(area).toBe(0)
    })

    it('should handle missing closed property (defaults to false)', () => {
      const polylineElement = {
        id: 'polyline-no-closed',
        type: ElementType.POLYLINE,
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 10, y: 0 },
            { x: 10, y: 10 },
          ],
          // closed property missing
        },
      }

      const area = strategy.calculateArea(polylineElement as any)
      expect(area).toBe(0)
    })

    it('should calculate area for triangle polyline', () => {
      const polylineElement = {
        id: 'polyline-triangle',
        type: ElementType.POLYLINE,
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 10, y: 0 },
            { x: 5, y: 10 },
          ],
          closed: true,
        },
      }

      const area = strategy.calculateArea(polylineElement as any)
      expect(area).toBe(30) // 3 points * 10 from our mock
    })

    it('should calculate area for complex polygon', () => {
      const polylineElement = {
        id: 'polyline-complex',
        type: ElementType.POLYLINE,
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 10, y: 0 },
            { x: 15, y: 5 },
            { x: 10, y: 10 },
            { x: 0, y: 10 },
            { x: -5, y: 5 },
          ],
          closed: true,
        },
      }

      const area = strategy.calculateArea(polylineElement as any)
      expect(area).toBe(60) // 6 points * 10 from our mock
    })
  })

  describe('error Handling', () => {
    it('should throw error for non-polyline element', () => {
      const rectangleElement = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        properties: {},
      }

      expect(() => strategy.calculateArea(rectangleElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculateArea(rectangleElement as any))
        .toThrow('PolylineAreaStrategy can only calculate area for POLYLINE elements')
    })

    it('should throw error for closed polyline with fewer than 3 points', () => {
      const polylineElement = {
        id: 'polyline-too-few-points',
        type: ElementType.POLYLINE,
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 10, y: 0 },
          ],
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(polylineElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculateArea(polylineElement as any))
        .toThrow('must have at least 3 points')
    })

    it('should throw error for closed polyline with null points', () => {
      const polylineElement = {
        id: 'polyline-null-points',
        type: ElementType.POLYLINE,
        properties: {
          points: null,
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(polylineElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for closed polyline with undefined points', () => {
      const polylineElement = {
        id: 'polyline-undefined-points',
        type: ElementType.POLYLINE,
        properties: {
          points: undefined,
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(polylineElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for closed polyline with non-array points', () => {
      const polylineElement = {
        id: 'polyline-non-array-points',
        type: ElementType.POLYLINE,
        properties: {
          points: 'not an array',
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(polylineElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for invalid point coordinates', () => {
      const polylineElement = {
        id: 'polyline-invalid-point',
        type: ElementType.POLYLINE,
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 'invalid', y: 0 },
            { x: 10, y: 10 },
          ],
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(polylineElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculateArea(polylineElement as any))
        .toThrow('contains invalid point data at index 1')
    })

    it('should throw error for null point in array', () => {
      const polylineElement = {
        id: 'polyline-null-point',
        type: ElementType.POLYLINE,
        properties: {
          points: [
            { x: 0, y: 0 },
            null,
            { x: 10, y: 10 },
          ],
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(polylineElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for point with missing coordinates', () => {
      const polylineElement = {
        id: 'polyline-missing-coords',
        type: ElementType.POLYLINE,
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 10 }, // missing y
            { x: 10, y: 10 },
          ],
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(polylineElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for point with infinite coordinates', () => {
      const polylineElement = {
        id: 'polyline-infinite-coords',
        type: ElementType.POLYLINE,
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: Infinity, y: 0 },
            { x: 10, y: 10 },
          ],
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(polylineElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for point with NaN coordinates', () => {
      const polylineElement = {
        id: 'polyline-nan-coords',
        type: ElementType.POLYLINE,
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 10, y: Number.NaN },
            { x: 10, y: 10 },
          ],
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(polylineElement as any))
        .toThrow(CoreError)
    })
  })

  describe('edge Cases', () => {
    it('should handle exactly 3 points (minimum for polygon)', () => {
      const polylineElement = {
        id: 'polyline-min-points',
        type: ElementType.POLYLINE,
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 10, y: 0 },
            { x: 5, y: 10 },
          ],
          closed: true,
        },
      }

      const area = strategy.calculateArea(polylineElement as any)
      expect(area).toBe(30) // 3 points * 10 from our mock
    })

    it('should handle points with zero coordinates', () => {
      const polylineElement = {
        id: 'polyline-zero-coords',
        type: ElementType.POLYLINE,
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 0, y: 0 },
            { x: 0, y: 0 },
          ],
          closed: true,
        },
      }

      const area = strategy.calculateArea(polylineElement as any)
      expect(area).toBe(30) // 3 points * 10 from our mock
    })

    it('should handle negative coordinates', () => {
      const polylineElement = {
        id: 'polyline-negative-coords',
        type: ElementType.POLYLINE,
        properties: {
          points: [
            { x: -10, y: -10 },
            { x: 0, y: -10 },
            { x: -5, y: 0 },
          ],
          closed: true,
        },
      }

      const area = strategy.calculateArea(polylineElement as any)
      expect(area).toBe(30) // 3 points * 10 from our mock
    })

    it('should handle very large coordinates', () => {
      const polylineElement = {
        id: 'polyline-large-coords',
        type: ElementType.POLYLINE,
        properties: {
          points: [
            { x: 1000000, y: 1000000 },
            { x: 1000010, y: 1000000 },
            { x: 1000005, y: 1000010 },
          ],
          closed: true,
        },
      }

      const area = strategy.calculateArea(polylineElement as any)
      expect(area).toBe(30) // 3 points * 10 from our mock
    })

    it('should handle very small coordinates', () => {
      const polylineElement = {
        id: 'polyline-small-coords',
        type: ElementType.POLYLINE,
        properties: {
          points: [
            { x: 0.001, y: 0.001 },
            { x: 0.011, y: 0.001 },
            { x: 0.006, y: 0.011 },
          ],
          closed: true,
        },
      }

      const area = strategy.calculateArea(polylineElement as any)
      expect(area).toBe(30) // 3 points * 10 from our mock
    })

    it('should handle many points', () => {
      const points = []
      for (let i = 0; i < 100; i++) {
        const angle = (i / 100) * 2 * Math.PI
        points.push({
          x: Math.cos(angle) * 10,
          y: Math.sin(angle) * 10,
        })
      }

      const polylineElement = {
        id: 'polyline-many-points',
        type: ElementType.POLYLINE,
        properties: {
          points,
          closed: true,
        },
      }

      const area = strategy.calculateArea(polylineElement as any)
      expect(area).toBe(1000) // 100 points * 10 from our mock
    })
  })
})
