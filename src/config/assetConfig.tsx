import { ChartNoAxesGantt, Circle, Hexagon, ImageIcon, LoaderCircle, Minus, Pentagon, RectangleHorizontal, Spline, Square, Triangle, Type } from 'lucide-react'
/** @jsxImportSource react */
import { ElementType } from '@/types/core/elementDefinitions'

export const basicAssetDisplayOrder: ElementType[] = [
  ElementType.RECTANGLE,
  ElementType.SQUARE,
  ElementType.ELLIPSE,
  ElementType.CIRCLE,
  ElementType.TRIANGLE,
  ElementType.QUADRILATERAL,
  ElementType.PENTAGON,
  ElementType.HEXAGON,
  ElementType.LINE,
  ElementType.POLYLINE,
  ElementType.ARC,
  ElementType.QUADRATIC,
  ElementType.CUBIC,
  ElementType.TEXT,
  ElementType.IMAGE,
]

// Define types for classifying assets for the collapsed drawer
export const DRAGGABLE_COLLAPSED_TYPES: ElementType[] = [
  ElementType.FLOOR_AREA,
  ElementType.OPENING,
  ElementType.IMAGE,
  ElementType.RECTANGLE,
  ElementType.SQUARE,
  ElementType.ELLIPSE,
  ElementType.CIRCLE,
  ElementType.TRIANGLE,
  ElementType.QUADRILATERAL,
  ElementType.PENTAGON,
  ElementType.HEXAGON,
  ElementType.TEXT, // 🔧 恢复Text为拖拽生成
]

export const DRAWING_TOOL_COLLAPSED_TYPES: ElementType[] = [
  ElementType.WALL,
  ElementType.LINE,
  ElementType.POLYLINE,
  ElementType.ARC,
  ElementType.QUADRATIC,
  ElementType.CUBIC,
]

// Define the desired order for basic draggable assets in the collapsed drawer
export const BASIC_DRAGGABLE_ORDER_COLLAPSED: ElementType[] = [
  ElementType.SQUARE,
  ElementType.RECTANGLE,
  ElementType.CIRCLE,
  ElementType.ELLIPSE,
  ElementType.TRIANGLE,
  ElementType.QUADRILATERAL,
  ElementType.PENTAGON,
  ElementType.HEXAGON,
  ElementType.TEXT, // 🔧 添加Text到拖拽顺序
  ElementType.IMAGE,
]

// Define the desired order for basic drawing tool assets in the collapsed drawer
export const BASIC_DRAWING_TOOL_ORDER_COLLAPSED: ElementType[] = [
  ElementType.LINE,
  ElementType.POLYLINE,
  ElementType.ARC,
  ElementType.QUADRATIC,
  ElementType.CUBIC, // Assuming CUBIC is a clickable drawing tool
]

// Configuration for Basic Shapes (Draggable) in Expanded View
export const basicShapesForExpandedConfig = [
  { category: 'Shapes', items: [
    { name: 'Rectangle', icon: <RectangleHorizontal className="h-5 w-5" />, type: ElementType.RECTANGLE },
    { name: 'Square', icon: <Square className="h-5 w-5" />, type: ElementType.SQUARE },
    { name: 'Ellipse', icon: <Circle className="h-5 w-5" />, type: ElementType.ELLIPSE },
    { name: 'Circle', icon: <Circle className="h-5 w-5" />, type: ElementType.CIRCLE },
    { name: 'Triangle', icon: <Triangle className="h-5 w-5" />, type: ElementType.TRIANGLE },
    { name: 'Pentagon', icon: <Pentagon className="h-5 w-5" />, type: ElementType.PENTAGON },
    { name: 'Hexagon', icon: <Hexagon className="h-5 w-5" />, type: ElementType.HEXAGON },
  ] },
  { category: 'Utilities', items: [
    { name: 'Text', icon: <Type className="h-5 w-5" />, type: ElementType.TEXT },
    { name: 'Image', icon: <ImageIcon className="h-5 w-5" />, type: ElementType.IMAGE },
  ] },
]

// Configuration for Basic Paths (Click-to-Prime) in Expanded View
export const basicPathsForExpandedConfig = [
  { category: 'Paths', items: [
    { name: 'Line', icon: <Minus className="h-5 w-5 transform rotate-[-45deg]" />, type: ElementType.LINE },
    { name: 'Polyline', icon: <ChartNoAxesGantt className="h-5 w-5" />, type: ElementType.POLYLINE },
  ] },
  { category: 'Curves', items: [
    { name: 'Arc', icon: <LoaderCircle className="h-5 w-5" />, type: ElementType.ARC },
    { name: 'Quadratic Curve', icon: <Spline className="h-5 w-5" />, type: ElementType.QUADRATIC },
    { name: 'Cubic Curve', icon: <Spline className="h-5 w-5" />, type: ElementType.CUBIC },
  ] },
]
