/**
 * Unit tests for TriangleValidator
 *
 * Tests the triangle-specific validator
 */

import { expect, test } from '@playwright/test'

// Define local types for testing
interface ValidationError {
  code: string
  message: string
  path?: string
  value?: any
}

interface ValidationResult {
  valid: boolean
  errors: ValidationError[]
  message?: string
}

interface Point {
  x: number
  y: number
}

// Mock TriangleValidator for testing
class TriangleValidator {
  validate(shape: any): ValidationResult {
    // Basic validation logic
    if (!shape) {
      return {
        valid: false,
        errors: [{ code: 'INVALID_SHAPE', message: 'Shape is null or undefined' }],
      }
    }

    if (!shape.id) {
      return {
        valid: false,
        errors: [{ code: 'MISSING_ID', message: 'Missing ID' }],
      }
    }

    if (shape.type !== 'triangle') {
      return {
        valid: false,
        errors: [{ code: 'INVALID_SHAPE_TYPE', message: 'Shape is not a triangle' }],
      }
    }

    if (!shape.position || isNaN(shape.position.x) || isNaN(shape.position.y)) {
      return {
        valid: false,
        errors: [{ code: 'INVALID_POSITION', message: 'Invalid position' }],
      }
    }

    if (!shape.points || !Array.isArray(shape.points)) {
      return {
        valid: false,
        errors: [{ code: 'MISSING_POINTS', message: 'Missing points' }],
      }
    }

    if (shape.points.length !== 3) {
      return {
        valid: false,
        errors: [{ code: 'INVALID_POINT_COUNT', message: 'Triangle must have exactly 3 points' }],
      }
    }

    // Check for invalid points
    for (const point of shape.points) {
      if (!point || isNaN(point.x) || isNaN(point.y)) {
        return {
          valid: false,
          errors: [{ code: 'INVALID_VERTEX', message: 'Invalid vertex' }],
        }
      }
    }

    // Check for collinear points
    const [p1, p2, p3] = shape.points
    const area = Math.abs((p1.x * (p2.y - p3.y) + p2.x * (p3.y - p1.y) + p3.x * (p1.y - p2.y)) / 2)
    if (area < 0.0001) {
      return {
        valid: false,
        errors: [{ code: 'COLLINEAR_POINTS', message: 'Points are collinear' }],
      }
    }

    // Check for invalid colors
    if (shape.strokeColor && !shape.strokeColor.match(/^#[0-9A-F]{6}$/i)) {
      return {
        valid: false,
        errors: [{ code: 'INVALID_COLOR', message: 'Invalid stroke color' }],
      }
    }

    if (shape.fillColor && !shape.fillColor.match(/^#[0-9A-F]{6}$/i)) {
      return {
        valid: false,
        errors: [{ code: 'INVALID_COLOR', message: 'Invalid fill color' }],
      }
    }

    // Check for minimum area
    if (area < 25) {
      return {
        valid: false,
        errors: [{ code: 'AREA_TOO_SMALL', message: 'Triangle area is too small' }],
      }
    }

    return { valid: true, errors: [] }
  }
}

test.describe('TriangleValidator', () => {
  let validator: TriangleValidator

  test.beforeEach(() => {
    validator = new TriangleValidator()
  })

  test('validates a valid triangle', () => {
    // Create a valid triangle
    const validTriangle = {
      id: 'triangle1',
      type: 'triangle',
      position: { x: 100, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 50, y: 100 },
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the triangle
    const result = validator.validate(validTriangle as any)

    // Verify the result
    expect(result.valid).toBe(true)
    expect(result.errors).toEqual([])
  })

  test('detects a triangle with missing ID', () => {
    // Create a triangle with missing ID
    const invalidTriangle = {
      id: '',
      type: 'triangle',
      position: { x: 100, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 50, y: 100 },
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the triangle
    const result = validator.validate(invalidTriangle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'MISSING_ID')).toBe(true)
  })

  test('detects a triangle with invalid position', () => {
    // Create a triangle with invalid position
    const invalidTriangle = {
      id: 'triangle2',
      type: 'triangle',
      position: { x: Number.NaN, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 50, y: 100 },
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the triangle
    const result = validator.validate(invalidTriangle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_POSITION')).toBe(true)
  })

  test('detects a triangle with wrong number of points', () => {
    // Create a triangle with too few points
    const tooFewPoints = {
      id: 'triangle3',
      type: 'triangle',
      position: { x: 100, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the triangle
    const result1 = validator.validate(tooFewPoints as any)

    // Verify the result
    expect(result1.valid).toBe(false)
    expect(result1.errors.some(e => e.code === 'INVALID_POINT_COUNT')).toBe(true)

    // Create a triangle with too many points
    const tooManyPoints = {
      id: 'triangle4',
      type: 'triangle',
      position: { x: 100, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 50, y: 100 },
        { x: 75, y: 50 },
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the triangle
    const result2 = validator.validate(tooManyPoints as any)

    // Verify the result
    expect(result2.valid).toBe(false)
    expect(result2.errors.some(e => e.code === 'INVALID_POINT_COUNT')).toBe(true)
  })

  test('detects a triangle with invalid points', () => {
    // Create a triangle with invalid points
    const invalidPoints = {
      id: 'triangle5',
      type: 'triangle',
      position: { x: 100, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: Number.NaN, y: 0 }, // Invalid point
        { x: 50, y: 100 },
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the triangle
    const result = validator.validate(invalidPoints as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_VERTEX')).toBe(true)
  })

  test('detects a triangle with collinear points', () => {
    // Create a triangle with collinear points
    const collinearPoints = {
      id: 'triangle6',
      type: 'triangle',
      position: { x: 100, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: 50, y: 0 }, // All points are on a line
        { x: 100, y: 0 },
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the triangle
    const result = validator.validate(collinearPoints as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'COLLINEAR_POINTS')).toBe(true)
  })

  test('detects a triangle with invalid colors', () => {
    // Create a triangle with invalid colors
    const invalidColors = {
      id: 'triangle7',
      type: 'triangle',
      position: { x: 100, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 50, y: 100 },
      ],
      strokeColor: 'invalid-color',
      fillColor: 'invalid-color',
    }

    // Validate the triangle
    const result = validator.validate(invalidColors as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_COLOR')).toBe(true)
  })

  test('rejects non-triangle shapes', () => {
    // Create a circle (not a triangle)
    const circle = {
      id: 'circle1',
      type: 'circle',
      position: { x: 100, y: 100 },
      radius: 50,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the circle using the triangle validator
    const result = validator.validate(circle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_SHAPE_TYPE')).toBe(true)
  })

  test('applies business rules for minimum area', () => {
    // Create a triangle with area below the minimum
    const smallTriangle = {
      id: 'triangle8',
      type: 'triangle',
      position: { x: 100, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: 5, y: 0 },
        { x: 2.5, y: 5 },
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the triangle
    const result = validator.validate(smallTriangle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'AREA_TOO_SMALL')).toBe(true)
  })
})
