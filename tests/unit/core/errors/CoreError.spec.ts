import { describe, expect, it } from 'vitest'
import { CoreError, createError, ErrorType } from '@/core/errors/CoreError'

describe('coreError', () => {
  describe('coreError class', () => {
    it('should create a CoreError instance with the correct properties', () => {
      const error = new CoreError(ErrorType.UNKNOWN, 'Test error message', { foo: 'bar' })

      expect(error).toBeInstanceOf(CoreError)
      expect(error).toBeInstanceOf(Error)
      expect(error.type).toBe(ErrorType.UNKNOWN)
      expect(error.message).toBe('Test error message')
      expect(error.metadata).toEqual({ foo: 'bar' })
      expect(error.name).toBe('CoreError')
    })

    it('should create a CoreError instance without metadata', () => {
      const error = new CoreError(ErrorType.CONFIGURATION, 'Config error')

      expect(error).toBeInstanceOf(CoreError)
      expect(error.type).toBe(ErrorType.CONFIGURATION)
      expect(error.message).toBe('Config error')
      expect(error.metadata).toBeUndefined()
    })

    it('should maintain proper instanceof behavior', () => {
      const error = new CoreError(ErrorType.UNKNOWN, 'Test error')

      expect(error instanceof CoreError).toBe(true)
      expect(error instanceof Error).toBe(true)
    })
  })

  describe('createError function', () => {
    it('should create a CoreError instance with the correct properties', () => {
      const error = createError(ErrorType.INVALID_PAYLOAD, 'Invalid data', { data: 'invalid' })

      expect(error).toBeInstanceOf(CoreError)
      expect(error.type).toBe(ErrorType.INVALID_PAYLOAD)
      expect(error.message).toBe('Invalid data')
      expect(error.metadata).toEqual({ data: 'invalid' })
    })

    it('should create a CoreError instance without metadata', () => {
      const error = createError(ErrorType.NOT_FOUND, 'Entity not found')

      expect(error).toBeInstanceOf(CoreError)
      expect(error.type).toBe(ErrorType.NOT_FOUND)
      expect(error.message).toBe('Entity not found')
      expect(error.metadata).toBeUndefined()
    })
  })

  describe('errorType enum', () => {
    it('should have the expected error types', () => {
      // General errors
      expect(ErrorType.UNKNOWN).toBe('UNKNOWN')
      expect(ErrorType.INITIALIZATION).toBe('INITIALIZATION')
      expect(ErrorType.CONFIGURATION).toBe('CONFIGURATION')
      expect(ErrorType.INVALID_PAYLOAD).toBe('INVALID_PAYLOAD')
      expect(ErrorType.NOT_FOUND).toBe('NOT_FOUND')

      // Factory errors
      expect(ErrorType.FACTORY_INVALID_TYPE).toBe('FACTORY_INVALID_TYPE')
      expect(ErrorType.FACTORY_CREATION_FAILED).toBe('FACTORY_CREATION_FAILED')

      // Validator errors
      expect(ErrorType.VALIDATION_FAILED).toBe('VALIDATION_FAILED')
      expect(ErrorType.VALIDATION_TYPE_MISMATCH).toBe('VALIDATION_TYPE_MISMATCH')
      expect(ErrorType.VALIDATION_REQUIRED_PROP).toBe('VALIDATION_REQUIRED_PROP')
      expect(ErrorType.VALIDATION_RANGE).toBe('VALIDATION_RANGE')
      expect(ErrorType.VALIDATOR_NOT_FOUND).toBe('VALIDATOR_NOT_FOUND')

      // Compute errors
      expect(ErrorType.COMPUTE_TRANSFORM_FAILED).toBe('COMPUTE_TRANSFORM_FAILED')
      expect(ErrorType.COMPUTE_BOUNDS_ERROR).toBe('COMPUTE_BOUNDS_ERROR')
      expect(ErrorType.STRATEGY_NOT_FOUND).toBe('STRATEGY_NOT_FOUND')

      // Coordinator/State errors
      expect(ErrorType.COORDINATOR_SHAPE_NOT_FOUND).toBe('COORDINATOR_SHAPE_NOT_FOUND')
      expect(ErrorType.COORDINATOR_OPERATION_FAILED).toBe('COORDINATOR_OPERATION_FAILED')
    })
  })
})
