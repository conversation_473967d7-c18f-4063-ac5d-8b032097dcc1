# State Management: Shapes Store (`shapesStore.ts`)

`shapesStore.ts` is a cornerstone of the state management system in RenoPilot.JS.Shapes2, specifically responsible for managing the state of all drawable shapes on the canvas. It utilizes Zustand, a small, fast, and scalable bearbones state-management solution.

## Key Responsibilities

1.  **Storing Shape Data:**
    *   Maintains an array or map of all shape objects currently present on the canvas. Each shape object contains its properties (e.g., type, position, size, color, rotation, custom attributes).
    *   The `shapes` state is typically an array: `Shape[]`.

2.  **Managing Selected Shapes:**
    *   Keeps track of which shapes are currently selected by the user.
    *   The `selectedShapeIds` state is usually an array of shape IDs: `string[]`.

3.  **Providing Actions for Shape Manipulation:**
    *   Exposes actions (functions) to modify the shapes and selection state. These actions are the primary way other parts of the application (UI components, services) interact with and update shape data.
    *   Common actions include:
        *   `addShape(shape: Shape)`: Adds a new shape.
        *   `addShapes(shapes: Shape[])`: Adds multiple shapes.
        *   `updateShape(shapeId: string, updates: Partial<Shape>)`: Updates properties of an existing shape.
        *   `updateShapes(updates: Array<{ id: string; changes: Partial<Shape> }>)`: Updates multiple shapes.
        *   `deleteShape(shapeId: string)`: Deletes a shape.
        *   `deleteShapes(shapeIds: string[])`: Deletes multiple shapes.
        *   `clearShapes()`: Removes all shapes.
        *   `selectShape(shapeId: string)`: Selects a single shape (often deselecting others).
        *   `addShapeToSelection(shapeId: string)`: Adds a shape to the current selection.
        *   `removeShapeFromSelection(shapeId: string)`: Removes a shape from the current selection.
        *   `setSelectedShapes(shapeIds: string[])`: Sets the selection to a specific set of shapes.
        *   `clearSelection()`: Deselects all shapes.
        *   `setShapes(shapes: Shape[])`: Replaces all shapes with a new set (e.g., when loading a project).

4.  **Handling Z-Ordering (Intra-Layer):**
    *   While a separate `layerStore` might manage inter-layer ordering, `shapesStore` can be involved in managing the z-index or drawing order of shapes *within the same layer*.
    *   Actions like `bringForward`, `sendBackward`, `bringToFront`, `sendToBack` for selected shapes would modify the order of shapes in the `shapes` array or update a z-index property.
    *   It publishes events to the `EventBus` (e.g., `SHAPES_Z_ORDER_CHANGED`) to notify other parts of the application (like the canvas renderer) about changes in drawing order.

5.  **Persistence (Optional but common):**
    *   Often integrates with Zustand's persistence middleware (`persist`) to save and rehydrate the shapes state to/from `localStorage` or another storage mechanism.
    *   This allows users to retain their work across browser sessions.
    *   The implementation includes `ShapesStateStorage` for custom serialization/deserialization if needed, especially for complex objects like `Path2D` which are not directly JSON-serializable.

6.  **Singleton Instance and Hot Module Replacement (HMR):**
    *   The store is typically implemented as a singleton to ensure a single source of truth for shape data.
    *   May include HMR support for a better development experience, allowing state to be preserved during hot reloads.

## Zustand Implementation Details

*   **`create<ShapesStoreState>()(...)`:** The store is created using Zustand's `create` function, which takes a setup function.
*   **Setup Function `(set, get) => ({ ... })`:**
    *   `set`: A function to update the store's state. It can take a partial state object or a function that receives the current state and returns a partial state update.
    *   `get`: A function to get the current store's state. Useful for actions that need to read the state before updating it.
*   **State Interface (`ShapesStoreState`):** Defines the structure of the store's state, including `shapes`, `selectedShapeIds`, and all the action methods.

```typescript
// src/store/shapesStore.ts
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { appEventBus, AppEvent } from '@/services/system/eventBus';
import type { Shape, ElementType } from '@/types'; // Assuming Shape type definition

// Helper for local storage to handle non-serializable parts if any (e.g. Path2D)
// For simple JSON data, this might not be strictly necessary
const ShapesStateStorage = {
  getItem: (name: string): Promise<string | null> => {
    const str = localStorage.getItem(name);
    // Potentially add custom deserialization logic here if shapes have complex, non-JSON types
    return Promise.resolve(str);
  },
  setItem: (name: string, value: string): Promise<void> => {
    // Potentially add custom serialization logic here
    localStorage.setItem(name, value);
    return Promise.resolve();
  },
  removeItem: (name: string): Promise<void> => {
    localStorage.removeItem(name);
    return Promise.resolve();
  },
};

export interface ShapesStoreState {
  shapes: Shape[];
  selectedShapeIds: string[];
  // --- Actions ---
  addShape: (shape: Shape) => void;
  addShapes: (shapes: Shape[]) => void;
  updateShape: (shapeId: string, updates: Partial<Shape>) => void;
  updateShapes: (updates: Array<{ id: string; changes: Partial<Shape> }>) => void;
  deleteShape: (shapeId: string) => void;
  deleteShapes: (shapeIds: string[]) => void;
  clearShapes: () => void;
  selectShape: (shapeId: string) => void;
  addShapeToSelection: (shapeId: string) => void;
  removeShapeFromSelection: (shapeId: string) => void;
  setSelectedShapes: (shapeIds: string[]) => void;
  clearSelection: () => void;
  setShapes: (shapes: Shape[]) => void;
  // Z-ordering actions
  bringForward: (shapeId: string) => void;
  sendBackward: (shapeId: string) => void;
  bringToFront: (shapeId: string) => void;
  sendToBack: (shapeId: string) => void;
}

const useShapesStore = create<ShapesStoreState>()(
  persist(
    (set, get) => ({
      shapes: [],
      selectedShapeIds: [],

      addShape: (shape) =>
        set((state) => ({ shapes: [...state.shapes, shape] })),
      addShapes: (newShapes) =>
        set((state) => ({ shapes: [...state.shapes, ...newShapes] })),
      
      updateShape: (shapeId, updates) =>
        set((state) => ({
          shapes: state.shapes.map((s) =>
            s.id === shapeId ? { ...s, ...updates, id: s.id } : s
          ),
        })),
      updateShapes: (updates) =>
        set((state) => ({
          shapes: state.shapes.map((s) => {
            const update = updates.find(u => u.id === s.id);
            return update ? { ...s, ...update.changes, id: s.id } : s;
          }),
        })),

      deleteShape: (shapeId) =>
        set((state) => ({
          shapes: state.shapes.filter((s) => s.id !== shapeId),
          selectedShapeIds: state.selectedShapeIds.filter((id) => id !== shapeId),
        })),
      deleteShapes: (shapeIds) =>
        set((state) => ({
          shapes: state.shapes.filter((s) => !shapeIds.includes(s.id)),
          selectedShapeIds: state.selectedShapeIds.filter((id) => !shapeIds.includes(id)),
        })),
      
      clearShapes: () => set({ shapes: [], selectedShapeIds: [] }),

      selectShape: (shapeId) => set({ selectedShapeIds: [shapeId] }),
      addShapeToSelection: (shapeId) =>
        set((state) => ({
          selectedShapeIds: state.selectedShapeIds.includes(shapeId)
            ? state.selectedShapeIds
            : [...state.selectedShapeIds, shapeId],
        })),
      removeShapeFromSelection: (shapeId) =>
        set((state) => ({
          selectedShapeIds: state.selectedShapeIds.filter((id) => id !== shapeId),
        })),
      setSelectedShapes: (shapeIds) => set({ selectedShapeIds: shapeIds }),
      clearSelection: () => set({ selectedShapeIds: [] }),

      setShapes: (newShapes) => set({ shapes: newShapes, selectedShapeIds: [] }),

      // Z-Ordering (simplified example, actual implementation might be more complex)
      _moveShape: (shapeId: string, newIndexCallback: (currentIndex: number, shapes: Shape[]) => number) => {
        const currentShapes = get().shapes;
        const currentIndex = currentShapes.findIndex(s => s.id === shapeId);
        if (currentIndex === -1) return;

        const newIndex = newIndexCallback(currentIndex, currentShapes);
        const newShapes = [...currentShapes];
        const [shapeToMove] = newShapes.splice(currentIndex, 1);
        newShapes.splice(newIndex, 0, shapeToMove);
        
        set({ shapes: newShapes });
        appEventBus.publish(AppEvent.SHAPES_Z_ORDER_CHANGED, { shapeIds: newShapes.map(s => s.id) });
      },

      bringForward: (shapeId) => get()._moveShape(shapeId, (idx, arr) => Math.min(idx + 1, arr.length - 1)),
      sendBackward: (shapeId) => get()._moveShape(shapeId, (idx) => Math.max(idx - 1, 0)),
      bringToFront: (shapeId) => get()._moveShape(shapeId, (_, arr) => arr.length - 1),
      sendToBack: (shapeId) => get()._moveShape(shapeId, () => 0),
    }),
    {
      name: 'renopilot-shapes-storage', // Name for localStorage key
      storage: createJSONStorage(() => ShapesStateStorage), // Use custom storage if needed
      // partialize: (state) => ({ shapes: state.shapes }), // Optionally persist only part of the state
    }
  )
);

// Singleton pattern and HMR support
let storeInstance = useShapesStore;
if (import.meta.env.MODE === 'development' && (import.meta as any).hot) {
  if (!(globalThis as any).__RENOPILOT_SHAPES_STORE__) {
    (globalThis as any).__RENOPILOT_SHAPES_STORE__ = useShapesStore;
  }
  storeInstance = (globalThis as any).__RENOPILOT_SHAPES_STORE__;
}

export default storeInstance;

```

## Significance

The `shapesStore` is critical for:-

*   **Single Source of Truth:** Providing a centralized and predictable state for all shape data.
*   **Decoupling:** Separating the concern of shape data management from UI components and business logic services. Components subscribe to the store and re-render when relevant data changes, rather than managing this complex state themselves.
*   **Reactivity:** Enabling a reactive UI that automatically updates when shapes are added, modified, or deleted.
*   **Testability:** Store actions can be tested in isolation.
*   **Developer Experience:** Zustand's simplicity and features like persistence and HMR improve development workflow.

Understanding `shapesStore` is fundamental to understanding how shape data is managed, manipulated, and rendered in the RenoPilot.JS.Shapes2 application.