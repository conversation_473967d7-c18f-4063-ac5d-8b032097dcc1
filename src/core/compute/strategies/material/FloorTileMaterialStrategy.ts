/**
 * Material Calculation Strategy for Floor Tiles
 *
 * @remarks
 * This strategy implements the {@link MaterialCalculatorStrategy} to calculate
 * the required amount of floor tiles for a given area.
 *
 * It first calculates the area of the input `element` based on its type. Supported
 * element types for area calculation include `RECTANGLE`, `SQUARE`, `CIRCLE`, `ELLIPSE`,
 * `<PERSON><PERSON>YGON` (and its variants like `TRIANGLE`, `HEXAGON`), `POLYLINE` (if closed),
 * and `ARC` (if a closed sector). Area calculation for basic shapes uses direct utility
 * functions, while for `POLYLINE` and `ARC`, it delegates to their respective area strategies
 * (`PolylineAreaStrategy`, `ArcAreaStrategy`).
 *
 * Once the area is determined, the strategy uses the `calculateTileCount` utility function
 * from `./materialUtils.ts` to compute the number of tiles, total area including wastage,
 * and optionally the number of boxes. This utility requires `tileWidth` and `tileHeight`
 * (from `options.unitSize`) and can consider `wastageRate`, `includeJoints`, `jointWidth`,
 * and `unitsPerBox` from {@link MaterialCalculationOptions}.
 *
 * The strategy validates that the `materialType` is 'tile' and that `options.unitSize`
 * is provided with positive dimensions.
 *
 * The `getElementType()` method returns a generic string 'tileAbleAreaShape', indicating
 * its applicability to any shape for which an area can be determined and tiling is relevant.
 *
 * @module core/compute/strategies/material/FloorTileMaterialStrategy
 * @see {@link MaterialCalculatorStrategy}
 * @see {@link MaterialCalculationOptions}
 * @see {@link MaterialCalculationResult}
 * @see {@link calculateTileCount}
 */
import type {
  MaterialCalculationOptions,
  MaterialCalculationResult,
  MaterialCalculatorStrategy, // Renamed IMaterialCalculatorStrategy for consistency
} from '@/types/core/compute' // Corrected import path
// IPoint interface
import type {
  Element,
  Shape,
} from '@/types/core/elementDefinitions'
// Import area strategies from their new location via the re-exporting index
import { ArcAreaStrategy, PolylineAreaStrategy } from '@/core/compute/strategies/area'
import { calculateTileCount } from '@/core/compute/strategies/material/materialUtils'
import { calculateCircleArea, calculateEllipseArea } from '@/lib/utils/geometry/ellipseUtils'

import { calculateArea as calculatePolygonArea } from '@/lib/utils/geometry/polygonUtils'
// Import area calculation utilities
import { calculateRectangleArea } from '@/lib/utils/geometry/rectangleUtils' // calculateArea is exported as calculateRectangleArea from rectangleUtils
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'

export class FloorTileMaterialStrategy implements MaterialCalculatorStrategy {
  // Renamed from IMaterialCalculatorStrategy for consistency
  private polylineAreaStrategy: PolylineAreaStrategy
  private arcAreaStrategy: ArcAreaStrategy

  constructor() {
    this.polylineAreaStrategy = new PolylineAreaStrategy()
    this.arcAreaStrategy = new ArcAreaStrategy()
  }

  /**
   * Calculates the amount of floor tile material required for a given element's area.
   *
   * @param element - The element (e.g., room, floor area) for which to calculate tile materials.
   *                  Its area will be calculated based on its type.
   * @param materialType - The type of material, expected to be 'tile'.
   * @param options - Optional {@link MaterialCalculationOptions}. Must include `unitSize` (tile dimensions).
   *                  Can also include `wastageRate`, `jointWidth`, `unitsPerBox`.
   * @returns A {@link MaterialCalculationResult} detailing the required tile amount, count, and wastage.
   * @throws {@link CoreError} if `materialType` is not 'tile', if `options.unitSize` is missing or invalid,
   *         or if the element's area cannot be determined or is invalid.
   */
  public calculateMaterialAmount(
    element: Element,
    materialType: string,
    options?: MaterialCalculationOptions,
  ): MaterialCalculationResult {
    if (materialType.toLowerCase() !== 'tile') {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `FloorTileMaterialStrategy only supports 'tile' material type, got '${materialType}'`,
      )
    }

    if (!options?.unitSize || options.unitSize.width <= 0 || options.unitSize.height <= 0) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        'Tile size (options.unitSize with positive width and height) must be provided for FloorTileMaterialStrategy.',
      )
    }
    const tileWidth = options.unitSize.width
    const tileHeight = options.unitSize.height

    let area: number
    try {
      switch (element.type) {
        case CoreElementType.RECTANGLE:
        case CoreElementType.SQUARE: {
          const rect = element as unknown as Shape.Rectangle // Added unknown
          const width = typeof rect.properties?.width === 'number' ? rect.properties.width : 0
          const height = typeof rect.properties?.height === 'number' ? rect.properties.height : 0
          area = calculateRectangleArea(width, height)
          break
        }
        case CoreElementType.CIRCLE: {
          const circle = element as unknown as Shape.Circle // Added unknown
          const radius = typeof circle.properties?.radius === 'number' ? circle.properties.radius : 0
          area = calculateCircleArea(radius)
          break
        }
        case CoreElementType.ELLIPSE: {
          const ellipse = element as unknown as Shape.Ellipse // Added unknown
          const radiusX = typeof ellipse.properties?.radiusX === 'number' ? ellipse.properties.radiusX : 0
          const radiusY = typeof ellipse.properties?.radiusY === 'number' ? ellipse.properties.radiusY : 0
          area = calculateEllipseArea(radiusX, radiusY)
          break
        }
        case CoreElementType.POLYGON:
        case CoreElementType.TRIANGLE:
        case CoreElementType.HEXAGON: {
          const polygon = element as unknown as Shape.Polygon // Added unknown
          if (polygon.points === null || polygon.points === undefined || !Array.isArray(polygon.points) || polygon.points.length < 3) { // Area calculation needs at least 3 points
            throw new CoreError(ErrorType.InvalidParameter, `Polygon (ID: ${element.id}) has insufficient points for area calculation.`)
          }
          area = calculatePolygonArea(polygon.points)
          break
        }
        case CoreElementType.POLYLINE: // Area of a polyline is usually 0 unless it's closed and treated as a polygon
          area = this.polylineAreaStrategy.calculateArea(element)
          break
        case CoreElementType.ARC: // Area of an arc is usually 0 unless it's a closed sector
          area = this.arcAreaStrategy.calculateArea(element)
          break
        default:
          throw new CoreError(
            ErrorType.InvalidElementType,
            `FloorTileMaterialStrategy cannot calculate area for element type: ${element.type}`,
          )
      }
    }
    catch (error) {
      throw new CoreError(
        ErrorType.ComputationError,
        `Failed to calculate area for element (ID: ${element.id}): ${error instanceof Error ? error.message : String(error)}`,
      )
    }

    if (typeof area !== 'number' || Number.isNaN(area) || area < 0) { // Area can be 0
      throw new CoreError(ErrorType.ComputationError, `Invalid or negative area calculated: ${area} for element ID: ${element.id}`)
    }
    if (area === 0) {
      console.warn(`[FloorTileMaterialStrategy] Calculated area for element ${element.id} is 0. No tiles needed.`)
      return { amount: 0, unit: 'm²', unitCount: 0, unitType: 'tile', amountWithWastage: 0 }
    }

    const tileOptions = {
      wastageRate: options?.wastageRate,
      includeJoints: options?.includeJoints,
      jointWidth: options?.jointWidth,
      unitsPerBox: options?.unitsPerBox,
    }

    return calculateTileCount(area, tileWidth, tileHeight, tileOptions)
  }

  /**
   * Returns a generic string identifier for this strategy.
   * @remarks This strategy applies to any shape for which an area can be calculated
   *          and tiling is a relevant material application.
   * @returns The string 'tileAbleAreaShape'.
   */
  public getElementType(): string {
    return 'tileAbleAreaShape' // Generic identifier indicating applicability to any shape whose area can be tiled
  }

  // getMaterialType is not part of the MaterialCalculatorStrategy interface.
  // The specific material type (e.g., "ceramic_tile", "porcelain_tile") would be
  // passed in the `materialType` argument of `calculateMaterialAmount`.
  // This strategy specifically handles the generic 'tile' materialType logic.
}
