import type { ServiceEvent, ServiceEventHandler } from '@/types/services/core/serviceEvent'
import { describe, expect, it, vi } from 'vitest'

describe('service Event Types', () => {
  describe('serviceEvent', () => {
    it('should allow creating a basic event', () => {
      const event: ServiceEvent = {
        type: 'TEST_EVENT',
        source: 'test-service',
        timestamp: Date.now(),
      }

      expect(event.type).toBe('TEST_EVENT')
      expect(event.source).toBe('test-service')
      expect(event.timestamp).toBeGreaterThan(0)
      expect(event.data).toBeUndefined()
    })

    it('should allow creating an event with data', () => {
      const event: ServiceEvent = {
        type: 'DATA_EVENT',
        source: 'data-service',
        timestamp: Date.now(),
        data: { id: 'test-123', value: 42 },
      }

      expect(event.type).toBe('DATA_EVENT')
      expect(event.source).toBe('data-service')
      expect(event.timestamp).toBeGreaterThan(0)
      expect(event.data).toBeDefined()
      expect((event.data as any).id).toBe('test-123')
      expect((event.data as any).value).toBe(42)
    })

    it('should allow different data types', () => {
      // String data
      const stringEvent: ServiceEvent = {
        type: 'STRING_EVENT',
        source: 'string-service',
        timestamp: Date.now(),
        data: 'string data',
      }
      expect(stringEvent.data).toBe('string data')

      // Number data
      const numberEvent: ServiceEvent = {
        type: 'NUMBER_EVENT',
        source: 'number-service',
        timestamp: Date.now(),
        data: 42,
      }
      expect(numberEvent.data).toBe(42)

      // Array data
      const arrayEvent: ServiceEvent = {
        type: 'ARRAY_EVENT',
        source: 'array-service',
        timestamp: Date.now(),
        data: [1, 2, 3],
      }
      expect(Array.isArray(arrayEvent.data)).toBe(true)
      expect((arrayEvent.data as number[]).length).toBe(3)
    })
  })

  describe('serviceEventHandler', () => {
    it('should handle synchronous event handling', () => {
      const mockHandler: ServiceEventHandler = vi.fn()
      const event: ServiceEvent = {
        type: 'TEST_EVENT',
        source: 'test-service',
        timestamp: Date.now(),
      }

      mockHandler(event)

      expect(mockHandler).toHaveBeenCalledWith(event)
    })

    it('should handle asynchronous event handling', async () => {
      const mockAsyncHandler: ServiceEventHandler = vi.fn().mockResolvedValue(undefined)
      const event: ServiceEvent = {
        type: 'ASYNC_EVENT',
        source: 'async-service',
        timestamp: Date.now(),
        data: { async: true },
      }

      await mockAsyncHandler(event)

      expect(mockAsyncHandler).toHaveBeenCalledWith(event)
    })
  })
})
