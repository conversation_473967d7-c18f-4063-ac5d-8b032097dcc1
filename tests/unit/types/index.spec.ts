// Import various types/constants from the main entry point
import type {
  RectangleProperties,
} from '@/types'

import type { BoundingBox, ComputeResult, ElementShape, EventBus, IElement, ModelShape, ShapeModel, ValidationResult } from '@/types'
import { describe, expect, it } from 'vitest'
import { // from ./core
  // Explicitly import ComputeResult as type
  Element,
  // from ./core/element
  // from ./services
  // from ./core/element
  // from ./core/models
  Path,
  Point, // from ./core/config
  // from ./core/element
  UNIT, // from ./constants
  // from ./core/element/validator
} from '@/types'

// Import ElementTypeValues directly from its source file
import { ElementTypeValues } from '@/types/core/shape-type'

describe('types Root Index File', () => {
  it('should export values (classes, constants) from various modules', () => {
    // Check runtime values
    expect(UNIT).toBeDefined()
    expect(Point).toBeDefined()
    expect(Element).toBeDefined()
    expect(Path).toBeDefined()
    // We cannot check types like BoundingBox, ElementShape, ModelShape at runtime with toBeDefined
  })

  it('should allow type usage (compile-time check simulation)', () => {
    // This test primarily serves to ensure the types *can* be referenced
    // The real check is TypeScript compilation
    const bbox: BoundingBox | undefined = undefined
    expect(bbox).toBeUndefined()

    let elementShape: ElementShape | undefined
    expect(elementShape).toBeUndefined()

    let modelShape: ModelShape | undefined
    expect(modelShape).toBeUndefined()

    let elementInstance: IElement | undefined
    expect(elementInstance).toBeUndefined()

    let shapeModelInstance: ShapeModel | undefined
    expect(shapeModelInstance).toBeUndefined()

    let validationResultInstance: ValidationResult | undefined
    expect(validationResultInstance).toBeUndefined()

    let computeResultInstance: ComputeResult<unknown> | undefined
    expect(computeResultInstance).toBeUndefined()

    let eventBusInstance: EventBus | undefined
    expect(eventBusInstance).toBeUndefined()

    let rectPropsInstance: RectangleProperties | undefined
    expect(rectPropsInstance).toBeUndefined()
  })

  it('should re-export constants correctly', () => {
    expect(UNIT).toBe(100) // From constants.ts
  })

  it('should re-export classes correctly', () => {
    // Check if classes can be instantiated (basic check)
    expect(() => new Point(0, 0)).not.toThrow()
    // Cannot easily check abstract classes like Element/Path without concrete implementations
  })

  it('should confirm ElementType is not exported from root but exists', () => {
    // Check that ElementType is NOT available via the root index
    // (This requires checking the compiled output or relying on previous test failure)
    // We can check that the values imported directly work
    expect(ElementTypeValues.RECTANGLE).toBe('rectangle')
  })
})
