/**
 * Handles Renderer Component
 *
 * This component is responsible for rendering and managing the manipulation handles
 * for selected shapes on the canvas. Handles allow users to resize, rotate, or
 * otherwise transform shapes through direct manipulation.
 *
 * The renderer dynamically creates appropriate handles based on the type of
 * selected shapes, manages drag interactions, and applies transformations to
 * the shapes in real-time during manipulation.
 *
 * Key features:
 * - Shape-specific handle generation (corners, edges, control points)
 * - Dragging behavior for handles
 * - Live preview of shape changes during manipulation
 * - Publishing shape update events when manipulation completes
 * - Zoom-aware handle sizing and positioning
 * - Coordinate transformation between screen and world space
 *
 * This component uses D3.js for DOM manipulation and drag behavior management,
 * which provides smooth, efficient interaction even with complex shapes.
 *
 * @module components/canvas/HandlesRenderer
 */

import type { ShapeElement as ShapeModel } from '@types_core/elementDefinitions' // Corrected import
import type { D3DragEvent } from 'd3-drag' // Import drag and D3DragEvent explicitly
import type { ZoomTransform } from 'd3-zoom'
import type { AppEventMap, EventBus } from '@/types/services/events'
import { ElementType } from '@types_core/elementDefinitions' // Corrected import

import * as d3 from 'd3'
// Corrected import order and style
import { drag } from 'd3-drag'
// For d3.select and selection types
import { pointer } from 'd3-selection' // For d3.pointer

import React, { useCallback, useEffect } from 'react'
import {
  getLineProps,
  getPolygonProps,
  isLine,
  isPolygon,
} from '@/lib/utils/element/elementModelUtils' // Corrected path for shape guards

import { calculateCircleBoundingBox, calculateEllipseBoundingBox } from '@/lib/utils/geometry/ellipseUtils'
// Rectangle/Square BBox can be derived directly from position and properties.
import { PointClass as Point } from '@/lib/utils/geometry/PointClass' // Corrected import
import { AppEventType } from '@/types/services/events' // Import AppEventMap
// import { calculateResizeChanges } from '@/lib/utils/element/transformUtils'; // calculateResizeChanges is part of useHandleResizeTransformHandler logic

// Constants for handle styling
// const selectedStrokeWidth = 3
// const selectedStrokeColor = '#007bff'

// Types for drag handler callbacks -- All Unused, removing them
// type HandleDragStartCallback = (event: any, handleData: { id: string; x: number; y: number; cursor: string }, shape: ShapeModel) => void;
// type HandleDragCallback = (_event: any) => void;
// type HandleDragEndCallback = (event: any) => void;

// Type for drag handle state
export type DragHandleInitialState = {
  shape: ShapeModel
  initialMouseWorldPos: Point
  handleId: string
  latestChanges?: Partial<Omit<ShapeModel, 'id' | 'type'>>
} | null

// Define the type for handle data points
export interface HandleType {
  id: string
  x: number
  y: number
  cursor: string
}

interface HandlesRendererProps {
  /**
   * The D3 selection of the handles layer group element
   */
  handlesLayer: d3.Selection<SVGGElement, unknown, null, undefined>

  /**
   * IDs of selected shapes
   */
  selectedIds: string[]

  /**
   * Array of selected shapes
   */
  selectedShapes: ShapeModel[]

  /**
   * Current zoom level
   */
  currentZoom: number

  /**
   * SVG element reference
   */
  svgRef: React.RefObject<SVGSVGElement | null>

  /**
   * Current transform state from D3 zoom behavior
   */
  currentTransform: ZoomTransform | null

  /**
   * Event bus for publishing shape updates
   */
  eventBus: EventBus<AppEventMap> // Use EventBus<AppEventMap>
}

/**
 * Component for rendering selection handles for selected shapes
 */
export const HandlesRenderer: React.FC<HandlesRendererProps> = ({
  handlesLayer,
  selectedIds,
  selectedShapes,
  currentZoom,
  svgRef,
  currentTransform,
  eventBus,
}: HandlesRendererProps) => {
  // Ref to track the current drag operation
  const dragHandleStateRef = React.useRef<DragHandleInitialState>(null)

  // Callback to update shape preview during drag
  const updatePreview = useCallback((id: string, changes: Partial<Omit<ShapeModel, 'id' | 'type'>>) => {
    // Apply preview changes to the shape visually
    // This could update a preview layer or modify the shape directly
    // console.log('[HandlesRenderer] Update preview for shape:', id, changes)

    if (changes === undefined || changes === null)
      return

    // Find all elements with this shape's ID and update them
    // In this implementation we're using class names to identify shapes
    handlesLayer.select(`g.shape-${id}`)
      .each(function () {
        const shapeElement = d3.select(this)

        // Apply position changes if present
        if (changes.position !== undefined) {
          shapeElement
            .attr('transform', `translate(${changes.position.x}, ${changes.position.y})`)
        }

        // Apply property changes based on shape type
        // This is a simplified version - would need expansion for all shape types
        if (changes.properties !== undefined) {
          const mainElement = shapeElement.select('rect, circle, ellipse, line, polygon')
          if (mainElement.empty() === false) { // Explicitly check for not empty
            // Update basic properties like width, height, etc.
            Object.entries(changes.properties).forEach(([key, value]) => {
              // Value is 'any' from ShapeModel properties, converting to string for d3 attr.
              // This might need refinement if specific attributes require number types.
              mainElement.attr(key, String(value))
            })
          }
        }
      })
  }, [handlesLayer])

  // Handle drag start for resize handles
  const handleDragStart = useCallback((
    event: D3DragEvent<SVGCircleElement, HandleType, HandleType>,
    handleData: HandleType,
    shape: ShapeModel,
  ) => {
    if (svgRef.current === null || currentTransform === null)
      return

    if (event.sourceEvent !== undefined) {
      (event.sourceEvent as Event).stopPropagation();

      (event.sourceEvent as Event).preventDefault()
    }

    const pt = svgRef.current.createSVGPoint()

    const [pointerX, pointerY] = pointer(event.sourceEvent, svgRef.current)
    pt.x = pointerX
    pt.y = pointerY
    const svgPoint = pt.matrixTransform(svgRef.current.getScreenCTM()?.inverse())
    const [worldX, worldY] = currentTransform.invert([svgPoint.x, svgPoint.y])
    const initialMouseWorldPos = new Point(worldX, worldY)

    // console.log('[HandlesRenderer] Start drag', {
    //   handleId: handleData.id,
    //   shapeId: shape.id,
    //   initialMouseWorldPos,
    // })

    dragHandleStateRef.current = {
      shape,
      initialMouseWorldPos,
      handleId: handleData.id,
    }
  }, [svgRef, currentTransform])

  // Handle drag movement for resize handles
  const handleDrag = useCallback((_event: D3DragEvent<SVGCircleElement, HandleType, HandleType>, _d: HandleType) => {
    if (dragHandleStateRef.current === null || svgRef.current === null || currentTransform === null)
      return

    const { shape } = dragHandleStateRef.current

    // Import calculateResizeChanges here or use your own implementation
    // The resize logic is now primarily handled within useHandleResizeTransformHandler's handleMouseMove.
    // This component (HandlesRenderer) calls that hook's methods.
    // If calculateResizeChanges was a utility for that hook, it's internal to it.
    // For now, assuming the hook's internal logic is sufficient and this direct call might be redundant or misplaced.
    // If specific preview logic is needed here beyond what the hook provides through its state/callbacks,
    // that would need to be re-evaluated.
    // For now, we assume `updatePreview` is driven by the hook's state changes or direct calls from the hook.
    // The `dragHandleStateRef.current.latestChanges` is set by the hook's `handleDrag` (mousemove).
    const changes = dragHandleStateRef.current?.latestChanges

    if (changes !== undefined) {
      // console.log('[HandlesRenderer] Applying preview changes', changes)
      dragHandleStateRef.current.latestChanges = changes
      updatePreview(shape.id, changes)
    }
  }, [svgRef, currentTransform, updatePreview])

  // Handle drag end for resize handles
  const handleDragEnd = useCallback((_event: D3DragEvent<SVGCircleElement, HandleType, HandleType>, _d: HandleType) => {
    if (dragHandleStateRef.current === null)
      return

    if (_event.sourceEvent !== undefined) {
      (_event.sourceEvent as Event).stopPropagation()
    }
    const { shape, latestChanges } = dragHandleStateRef.current

    if (latestChanges !== undefined && shape !== undefined) {
      // console.log('[HandlesRenderer] Publishing SHAPE_UPDATE_REQUEST', {
      //   id: shape.id,
      //   changes: latestChanges,
      // })

      // 根据ShapeUpdateRequestEvent接口的定义发送正确的payload结构
      eventBus.publish({
        type: AppEventType.ShapeUpdateRequest, // Corrected case
        payload: {
          shapeId: shape.id,
          updates: latestChanges,
        },
      })
    }

    dragHandleStateRef.current = null
  }, [eventBus])

  // Effect for rendering the selection handles
  useEffect(() => {
    // console.log('[HandlesRenderer] Rendering handles for', selectedIds.length, 'selected shapes')
    handlesLayer.selectAll('*').remove() // Clear previous handles

    if (selectedIds === undefined || selectedIds === null || selectedIds.length === 0 || currentZoom <= 0) {
      return
    }

    const handleBaseSize = 8 // Base size in pixels
    const handleStrokeWidth = 1
    const handleSize = handleBaseSize / currentZoom
    // const handleOffset = handleSize / 2 // This seems unused now after large code removal

    selectedShapes.forEach((shape: ShapeModel) => {
      let handlesData: HandleType[] = []

      // Generate handle data based on shape type
      switch (shape.type) {
        case ElementType.RECTANGLE:
        case ElementType.SQUARE: {
          // For Rectangle and Square, BBox is directly from position and properties
          if (shape.properties !== null && shape.properties !== undefined && 'width' in shape.properties && 'height' in shape.properties) {
            const props = shape.properties as { width: number, height: number }
            const bbox = { x: shape.position.x, y: shape.position.y, width: props.width, height: props.height }
            handlesData = [
              { id: 'nw', x: bbox.x, y: bbox.y, cursor: 'nwse-resize' },
              { id: 'ne', x: bbox.x + bbox.width, y: bbox.y, cursor: 'nesw-resize' },
              { id: 'sw', x: bbox.x, y: bbox.y + bbox.height, cursor: 'nesw-resize' },
              { id: 'se', x: bbox.x + bbox.width, y: bbox.y + bbox.height, cursor: 'nwse-resize' },
              // 只保留左右下中点
              { id: 'e', x: bbox.x + bbox.width, y: bbox.y + bbox.height / 2, cursor: 'ew-resize' },
              { id: 's', x: bbox.x + bbox.width / 2, y: bbox.y + bbox.height, cursor: 'ns-resize' },
              { id: 'w', x: bbox.x, y: bbox.y + bbox.height / 2, cursor: 'ew-resize' },
            ]
          }
          break
        }
        case ElementType.CIRCLE:
        case ElementType.ELLIPSE: {
          if (shape.type === ElementType.CIRCLE && shape.properties !== null && shape.properties !== undefined && 'radius' in shape.properties) {
            const props = shape.properties as { radius: number }
            const bbox = calculateCircleBoundingBox(shape.position, props.radius)
            if (bbox !== null && bbox !== undefined) {
              handlesData = [
                // 只保留左右下中点
                { id: 'e', x: bbox.position.x + bbox.width, y: bbox.position.y + bbox.height / 2, cursor: 'ew-resize' },
                { id: 's', x: bbox.position.x + bbox.width / 2, y: bbox.position.y + bbox.height, cursor: 'ns-resize' },
                { id: 'w', x: bbox.position.x, y: bbox.position.y + bbox.height / 2, cursor: 'ew-resize' },
              ]
            }
          }
          else if (shape.type === ElementType.ELLIPSE && shape.properties != null && 'radiusX' in shape.properties && 'radiusY' in shape.properties) {
            const props = shape.properties as { radiusX: number, radiusY: number }
            const bbox = calculateEllipseBoundingBox(shape.position, props.radiusX, props.radiusY)
            if (bbox != null) {
              handlesData = [
                // 只保留左右下中点
                { id: 'e', x: bbox.position.x + bbox.width, y: bbox.position.y + bbox.height / 2, cursor: 'ew-resize' },
                { id: 's', x: bbox.position.x + bbox.width / 2, y: bbox.position.y + bbox.height, cursor: 'ns-resize' },
                { id: 'w', x: bbox.position.x, y: bbox.position.y + bbox.height / 2, cursor: 'ew-resize' },
              ]
            }
          }
          break
        }
        case ElementType.LINE: {
          // For lines, we want handles at the endpoints
          if (isLine(shape)) {
            const lineProps = getLineProps(shape) // getLineProps should provide start and end points relative to shape.position if necessary
            if (lineProps?.start && lineProps?.end) {
              handlesData = [
                { id: 'start', x: lineProps.start.x, y: lineProps.start.y, cursor: 'move' },
                { id: 'end', x: lineProps.end.x, y: lineProps.end.y, cursor: 'move' },
              ]
            }
          }
          break
        }
        case ElementType.POLYGON:
        case ElementType.TRIANGLE:
        case ElementType.HEXAGON: {
          // For polygons, we need each vertex as a handle
          if (isPolygon(shape)) {
            const polygonProps = getPolygonProps(shape)
            if (polygonProps?.points && polygonProps.points.length > 0) {
              handlesData = polygonProps.points.map((point, index) => ({
                id: `vertex-${index}`,
                x: point.x,
                y: point.y,
                cursor: 'move',
              }))
            }
          }
          break
        }
        // Add other shape types as needed
      }

      // 生成 handle 数据后，声明 bbox 变量用于过滤顶部正中点
      let bbox: { x: number, y: number, width: number, height: number } | { position: { x: number, y: number }, width: number, height: number } | null = null
      switch (shape.type) {
        case ElementType.RECTANGLE:
        case ElementType.SQUARE: {
          if (shape.properties != null && 'width' in shape.properties && 'height' in shape.properties) {
            const props = shape.properties as { width: number, height: number }
            bbox = { x: shape.position.x, y: shape.position.y, width: props.width, height: props.height }
          }
          break
        }
        case ElementType.CIRCLE: {
          if (shape.properties != null && 'radius' in shape.properties) {
            const props = shape.properties as { radius: number }
            const b = calculateCircleBoundingBox(shape.position, props.radius)
            if (b != null)
              bbox = b
          }
          break
        }
        case ElementType.ELLIPSE: {
          if (shape.properties != null && 'radiusX' in shape.properties && 'radiusY' in shape.properties) {
            const props = shape.properties as { radiusX: number, radiusY: number }
            const b = calculateEllipseBoundingBox(shape.position, props.radiusX, props.radiusY)
            if (b != null)
              bbox = b
          }
          break
        }
      }
      // 过滤掉顶部正中点（防止和旋转手柄重叠）
      const handlesDataFiltered = handlesData.filter((hd) => {
        if (hd == null)
          return false
        if (
          (hd.id === 'n')
          || (typeof hd.y === 'number' && typeof hd.x === 'number'
            && bbox && (
          // 顶部正中点（矩形/正方形）
            ('x' in bbox && 'y' in bbox && hd.y === bbox.y && hd.x === bbox.x + bbox.width / 2)
            // 顶部正中点（圆/椭圆）
            || ('position' in bbox && hd.y === bbox.position.y && hd.x === bbox.position.x + bbox.width / 2)
          )
          )
        ) {
          return false
        }
        return true
      })

      const group = handlesLayer.append('g')
        .attr('class', `shape-handles shape-${shape.id}`)
        .attr('transform', `translate(${shape.position.x}, ${shape.position.y}) rotate(${shape.rotation || 0})`)

      // Define the drag behavior with explicit types
      const dragBehavior = drag<SVGCircleElement, HandleType, HandleType>() // Use imported drag
        .on('start', (event: D3DragEvent<SVGCircleElement, HandleType, HandleType>, d: HandleType) => {
          // 'd' is the datum of the dragged handle, which is HandleType
          handleDragStart(event, d, shape)
        })
        .on('drag', (event: D3DragEvent<SVGCircleElement, HandleType, HandleType>, d: HandleType) => { // Add datum 'd' to handleDrag signature
          handleDrag(event, d) // Pass it along
        })
        .on('end', (event: D3DragEvent<SVGCircleElement, HandleType, HandleType>, d: HandleType) => { // Add datum 'd' to handleDragEnd signature
          handleDragEnd(event, d)
        })

      group.selectAll<SVGCircleElement, HandleType>('circle.handle') // Specify element and datum type for selection
        .data(handlesDataFiltered, (d: HandleType) => d.id)
        .join('circle')
        .classed('handle', true)
        .attr('id', (d: HandleType) => d.id)
        .attr('cx', (d: HandleType) => d.x - shape.position.x) // Adjust handle x to be relative to group
        .attr('cy', (d: HandleType) => d.y - shape.position.y) // Adjust handle y to be relative to group
        .attr('r', handleSize / 2)
        .attr('fill', '#007bff')
        .attr('stroke', '#ffffff')
        .attr('stroke-width', handleStrokeWidth / currentZoom)
        .style('cursor', (d: HandleType) => d.cursor)
        .call(dragBehavior) // Removed 'as any'
    })
  }, [
    selectedIds,
    selectedShapes,
    handlesLayer,
    currentZoom,
    eventBus,
    svgRef,
    currentTransform, // Added missing dependencies
    handleDragStart,
    handleDrag,
    handleDragEnd, // Added drag handlers as dependencies
  ])

  return null // This component only renders via D3 into the passed layer
}

// Export as named export only, no default export
