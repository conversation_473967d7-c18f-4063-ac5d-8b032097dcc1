/**
 * @file CoreError.additional.spec.ts
 * @description Additional unit tests for CoreError class to improve coverage
 */

import { describe, expect, it } from 'vitest'
import { CoreError, createError, ErrorType } from '@/core/errors'

describe('coreError - Additional Tests', () => {
  describe('constructor', () => {
    it('should create a CoreError with type and message', () => {
      // Act
      const error = new CoreError(ErrorType.VALIDATION_FAILED, 'Test error message')

      // Assert
      expect(error).toBeInstanceOf(CoreError)
      expect(error).toBeInstanceOf(Error)
      expect(error.message).toBe('Test error message')
      expect(error.type).toBe(ErrorType.VALIDATION_FAILED)
    })

    it('should create a CoreError with type, message, and metadata', () => {
      // Arrange
      const metadata = { cause: new Error('Original error'), id: 'test-id' }

      // Act
      const error = new CoreError(ErrorType.VALIDATION_FAILED, 'Test error message', metadata)

      // Assert
      expect(error).toBeInstanceOf(CoreError)
      expect(error.message).toBe('Test error message')
      expect(error.type).toBe(ErrorType.VALIDATION_FAILED)
      expect(error.metadata).toBe(metadata)
    })
  })

  describe('createError', () => {
    it('should create a CoreError with type and message', () => {
      // Act
      const error = createError(ErrorType.VALIDATION_FAILED, 'Test error message')

      // Assert
      expect(error).toBeInstanceOf(CoreError)
      expect(error.message).toBe('Test error message')
      expect(error.type).toBe(ErrorType.VALIDATION_FAILED)
    })

    it('should create a CoreError with type, message, and metadata', () => {
      // Arrange
      const metadata = { cause: new Error('Original error'), id: 'test-id' }

      // Act
      const error = createError(ErrorType.VALIDATION_FAILED, 'Test error message', metadata)

      // Assert
      expect(error).toBeInstanceOf(CoreError)
      expect(error.message).toBe('Test error message')
      expect(error.type).toBe(ErrorType.VALIDATION_FAILED)
      expect(error.metadata).toBe(metadata)
    })
  })

  describe('toString', () => {
    it('should return a string representation of the error', () => {
      // Act
      const error = new CoreError(ErrorType.VALIDATION_FAILED, 'Test error message')

      // Assert
      expect(error.toString()).toContain('CoreError: Test error message')
    })

    it('should include name and message in string representation', () => {
      // Act
      const error = new CoreError(ErrorType.FACTORY_FAILED, 'Factory creation failed')

      // Assert
      expect(error.toString()).toContain('CoreError')
      expect(error.toString()).toContain('Factory creation failed')
    })
  })

  describe('errorType enum', () => {
    it('should have all expected error types', () => {
      // Assert
      expect(ErrorType.UNKNOWN).toBe('UNKNOWN')
      expect(ErrorType.VALIDATION_FAILED).toBe('VALIDATION_FAILED')
      expect(ErrorType.FACTORY_FAILED).toBe('FACTORY_FAILED')
      expect(ErrorType.STRATEGY_NOT_FOUND).toBe('STRATEGY_NOT_FOUND')
      expect(ErrorType.NOT_FOUND).toBe('NOT_FOUND')
      expect(ErrorType.INVALID_PAYLOAD).toBe('INVALID_PAYLOAD')
      expect(ErrorType.INITIALIZATION).toBe('INITIALIZATION')
    })
  })
})
