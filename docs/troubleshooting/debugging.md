# Debugging Guide

This guide provides advice and techniques for debugging issues within the RenoPilot.JS.Shapes2 application. It's primarily aimed at developers or advanced users comfortable with browser developer tools.

## 1. Browser Developer Tools

Modern web browsers come with powerful built-in developer tools. These are your primary resource for debugging client-side JavaScript applications.

*   **Accessing Developer Tools:** Typically, press `F12` or right-click on the page and select "Inspect" or "Inspect Element." Common shortcuts:
    *   Chrome/Edge: `Ctrl+Shift+I` (Windows/Linux), `Cmd+Option+I` (macOS)
    *   Firefox: `Ctrl+Shift+I` (Windows/Linux), `Cmd+Option+I` (macOS)
    *   Safari: Enable Develop menu first (Preferences > Advanced > Show Develop menu in menu bar), then `Cmd+Option+I`.

### Key Developer Tool Panels

*   **Console:**
    *   **View Logs:** Displays `console.log()`, `console.warn()`, `console.error()` messages from the application, as well as browser-generated errors and warnings.
    *   **JavaScript Errors:** Critical for identifying runtime errors, their location in the code, and the call stack.
    *   **Interactive JS Execution:** You can type JavaScript expressions directly into the console to inspect variables, call functions, or test small code snippets in the current page's context.

*   **Sources (or Debugger in Firefox):**
    *   **View Source Code:** Browse the application's source files (often including original source maps for TypeScript/TSX if configured correctly in development builds).
    *   **Set Breakpoints:** Click on a line number to set a breakpoint. When the code execution reaches a breakpoint, it will pause, allowing you to inspect variables, step through code, and understand the application's state at that moment.
        *   **Step Over (`F10`):** Execute the current line and move to the next line in the same function.
        *   **Step Into (`F11`):** If the current line is a function call, step into that function's code.
        *   **Step Out (`Shift+F11`):** Continue execution until the current function returns.
        *   **Resume (`F8`):** Continue execution until the next breakpoint or the end of the script.
    *   **Watch Expressions:** Add variables or expressions to a watch list to monitor their values as you step through code.
    *   **Call Stack:** Shows the sequence of function calls that led to the current point of execution.
    *   **Scope:** Displays all variables (local, closure, global) accessible in the current scope.

*   **Elements (or Inspector in Firefox):**
    *   **Inspect DOM:** View the live HTML structure of the page.
    *   **Modify HTML/CSS:** Edit HTML attributes and CSS rules in real-time to test layout or style changes.
    *   **Event Listeners:** Inspect event listeners attached to DOM elements.

*   **Network:**
    *   **Monitor Requests:** Track all network requests made by the application (e.g., API calls, loading assets).
    *   **Inspect Headers & Payloads:** View request/response headers, parameters, and data payloads.
    *   **Check Status Codes:** Identify failed requests (e.g., 404 Not Found, 500 Internal Server Error).

*   **Application (or Storage in Firefox):**
    *   **Inspect Local Storage/Session Storage:** View, add, edit, or delete data stored in the browser's local or session storage. Useful for debugging state persistence.
    *   **Cookies, IndexedDB, Cache Storage:** Inspect other browser storage mechanisms if used by the application.

## 2. Debugging Techniques

*   **`console.log()` Driven Debugging:**
    *   Strategically place `console.log()` statements in your code to output variable values, object states, or messages indicating code flow.
    *   Example: `console.log('User clicked shape:', shapeId, selectedShapeObject);`
    *   While simple, it's often the quickest way to get insights, especially for less complex issues.

*   **Using Breakpoints:**
    *   Identify a section of code where you suspect an issue.
    *   Set a breakpoint just before or within that section.
    *   When execution pauses, inspect variables in the "Scope" panel, hover over variables in the "Sources" panel, or add them to "Watch."
    *   Step through the code line by line to understand its behavior.

*   **Conditional Breakpoints:**
    *   Right-click on a line number in the "Sources" panel and select "Add conditional breakpoint..."
    *   Enter a JavaScript expression. The debugger will only pause if the expression evaluates to `true`.
    *   Example: `shape.id === 'specific-problematic-id'`

*   **Logpoints:**
    *   Similar to conditional breakpoints, but instead of pausing, they log a message to the console. Useful for logging information from a specific line without modifying the source code directly.

*   **React Developer Tools (Browser Extension):**
    *   If you are working with React (as RenoPilot.JS.Shapes2 does), the React Developer Tools extension is invaluable.
    *   **Components Tab:** Inspect the React component hierarchy, view component props and state, and modify them in real-time.
    *   **Profiler Tab:** Analyze rendering performance to identify bottlenecks.
    *   Available for Chrome, Firefox, and Edge.

*   **Redux DevTools (Browser Extension):**
    *   If the project uses Redux for state management (the current structure uses Zustand, but if Redux were used), this extension allows you to inspect the Redux store, dispatched actions, and state changes over time.
    *   For Zustand, while there isn't a dedicated official browser extension as feature-rich as Redux DevTools, you can use its middleware like `devtools` from `zustand/middleware` to connect to Redux DevTools. See Zustand documentation for setup.

## 3. Debugging State (Zustand)

*   **Zustand Devtools Middleware:** Zustand provides a `devtools` middleware that can connect to the Redux DevTools browser extension.
    ```typescript
    // Example in your store setup (e.g., src/store/useShapeStore.ts)
    import { create } from 'zustand';
    import { devtools } from 'zustand/middleware';

    interface MyState { // ... your state type }

    export const useMyStore = create<MyState>()(
      devtools(
        (set) => ({
          // ... your store initial state and actions
        }),
        { name: 'MyZustandStore' } // Optional: name for the store in DevTools
      )
    );
    ```
    This allows you to inspect state changes, dispatched actions (if you model them as such), and time-travel debug your Zustand store using Redux DevTools.

*   **Logging State Changes:** You can subscribe to state changes within your components or directly in the store definition to log them:
    ```typescript
    // In a component
    useEffect(() => {
      const unsubscribe = useMyStore.subscribe(
        (newState, prevState) => {
          console.log('Zustand state changed:', newState, 'from:', prevState);
        }
      );
      return unsubscribe; // Cleanup subscription on unmount
    }, []);
    ```

## 4. Debugging Tests (Vitest, Playwright)

*   **Vitest:**
    *   **`debugger` statement:** Place a `debugger;` statement in your test code. Run your tests with Node.js inspector attached (e.g., `npx vitest --inspect-brk`). You can then connect a debugger (like Chrome DevTools for Node) to step through your test code.
    *   **IDE Debugger:** Most IDEs (like VS Code) have built-in debuggers that can run and debug Vitest tests directly.
    *   **Vitest UI:** Use `vitest --ui` for a visual interface to run, view, and debug tests.

*   **Playwright:**
    *   **`await page.pause();`:** Add this line to your Playwright test script. When the test reaches this line, it will pause execution and open the Playwright Inspector, allowing you to step through commands, see selectors, and inspect the page.
    *   **Playwright Inspector:** Run tests with `npx playwright test --debug`.
    *   **VS Code Extension:** The official Playwright VS Code extension provides excellent debugging capabilities, including running tests with a visual debugger, picking selectors, and recording tests.

## 5. Common Pitfalls & Tips

*   **Source Maps:** Ensure source maps are correctly configured and generated in your development build (`vite.config.ts` usually handles this by default for Vite projects). Source maps allow you to debug your original TypeScript/TSX code instead of the compiled JavaScript.
*   **Async/Await:** Be mindful of asynchronous operations. Use breakpoints or logging within `async` functions and `.then()`/`.catch()` blocks to trace their execution.
*   **Caching:** Aggressive browser caching or service worker caching (if PWA features are used) can sometimes serve outdated code. Try a hard refresh (`Ctrl+Shift+R` or `Cmd+Shift+R`) or clear cache.
*   **Isolate the Problem:** If you encounter a complex bug, try to create a minimal reproducible example. Simplify the code or the scenario until the bug still occurs but with fewer moving parts.

By using these tools and techniques, you should be able to effectively diagnose and resolve most issues in RenoPilot.JS.Shapes2.