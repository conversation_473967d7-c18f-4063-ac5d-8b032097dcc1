// Import models directly
import type {
  ArcProperties,
  BaseStyleProperties,
  EllipseProperties,
  LineProperties,
  PolygonProperties,
  RectangleProperties,
  ShapeModel,
} from '@/types/core/models'
import type { ShapeElement } from '@/types/core/elementDefinitions'

import { describe, expect, it } from 'vitest'
// 这些类型已经在 models.ts 中导入或定义，不需要再单独导入

describe('core Model Types', () => {
  it('should allow declaration of variables using the imported model interfaces', () => {
    // Attempt to declare variables of these types.
    // TypeScript's compiler checks if they are valid and exported.
    const baseStyle: BaseStyleProperties | undefined = undefined
    const rectProps: RectangleProperties | undefined = undefined
    // 只使用已导入的类型
    const ellipseProps: EllipseProperties | undefined = undefined
    const polyProps: PolygonProperties | undefined = undefined
    const lineProps: LineProperties | undefined = undefined
    const arcProps: ArcProperties | undefined = undefined
    const shapeModel: ShapeModel | undefined = undefined

    // Basic runtime checks
    expect(baseStyle).toBeUndefined()
    expect(rectProps).toBeUndefined()
    expect(ellipseProps).toBeUndefined()
    expect(polyProps).toBeUndefined()
    expect(lineProps).toBeUndefined()
    expect(arcProps).toBeUndefined()
    expect(shapeModel).toBeUndefined()
  })

  // Optional: Add a test demonstrating the discriminated union
  it('should support discriminated union for ShapeModel properties', () => {
    // This test primarily serves as a demonstration and relies on TS compile-time checks.
    const rectangleExample: ShapeModel = {
      id: 'rect-1',
      type: 'rectangle', // Discriminator
      position: { x: 10, y: 10 },
      properties: {
        type: 'rectangle', // Required again inside properties for the union
        width: 100,
        height: 50,
        fill: '#ff0000', // Style property
      },
      layer: 'layer1',
    }

    const lineExample: ShapeElement = {
      id: 'line-1',
      type: 'line', // Discriminator
      position: { x: 0, y: 0 }, // Position might be less relevant for line, but part of base model
      properties: {
        type: 'line', // Required again
        start: { x: 10, y: 10 },
        end: { x: 110, y: 10 },
        stroke: '#0000ff', // Style property
        strokeWidth: 3,
      },
    }

    // Runtime checks on the examples
    expect(rectangleExample.type).toBe('rectangle')
    // Type assertion needed to access specific properties after checking the discriminator
    if (rectangleExample.properties.type === 'rectangle') {
      expect(rectangleExample.properties.width).toBe(100)
    }

    expect(lineExample.type).toBe('line')
    if (lineExample.properties.type === 'line') {
      expect(lineExample.properties.start).toEqual({ x: 10, y: 10 })
      expect(lineExample.properties.strokeWidth).toBe(3)
    }
  })

  it('shapeModel should allow assignment with discriminated union properties', () => {
    // Example Rectangle
    const rectangleModel: ShapeElement = {
      id: 'rect1',
      type: 'rectangle',
      position: { x: 10, y: 10 },
      visible: true,
      properties: {
        type: 'rectangle',
        width: 100,
        height: 50,
        cornerRadius: 5,
        opacity: 1, // BaseStyle property mixed in
        fill: 'red', // BaseStyle property mixed in
      },
      metadata: { createdAt: Date.now(), updatedAt: Date.now() },
    }
    expect(rectangleModel.type).toBe('rectangle')
    if (rectangleModel.properties.type === 'rectangle') {
      expect(rectangleModel.properties.width).toBe(100)
      expect(rectangleModel.properties.opacity).toBe(1) // Check opacity inside properties
      expect(rectangleModel.properties.fill).toBe('red')
    }

    // Example Ellipse
    const ellipseModel: ShapeModel = {
      id: 'ellipse1',
      type: 'ellipse',
      position: { x: 20, y: 20 },
      visible: true,
      properties: {
        type: 'ellipse',
        radiusX: 60,
        radiusY: 40,
        opacity: 0.8,
      },
      metadata: { createdAt: Date.now(), updatedAt: Date.now() },
    }
    expect(ellipseModel.type).toBe('ellipse')
    if (ellipseModel.properties.type === 'ellipse') {
      expect(ellipseModel.properties.radiusX).toBe(60)
      expect(ellipseModel.properties.opacity).toBe(0.8)
    }

    // Example Polygon
    const polygonModel: ShapeElement = {
      id: 'poly1',
      type: 'polygon',
      position: { x: 50, y: 50 },
      visible: true,
      properties: {
        type: 'polygon',
        points: [{ x: 0, y: 0 }, { x: 10, y: 0 }, { x: 5, y: 10 }],
        fill: 'blue',
        opacity: 1,
      },
      metadata: { createdAt: Date.now(), updatedAt: Date.now() },
    }
    expect(polygonModel.type).toBe('polygon')
    if (polygonModel.properties.type === 'polygon') {
      expect(polygonModel.properties.points.length).toBe(3)
      expect(polygonModel.properties.fill).toBe('blue')
      expect(polygonModel.properties.opacity).toBe(1)
    }

    // Example Line
    const lineModel: ShapeModel = {
      id: 'line1',
      type: 'line',
      position: { x: 0, y: 0 },
      visible: true,
      properties: {
        type: 'line',
        start: { x: -10, y: -10 },
        end: { x: 10, y: 10 },
        opacity: 1,
        stroke: 'black',
        strokeWidth: 2,
      },
      metadata: { createdAt: Date.now(), updatedAt: Date.now() },
    }
    expect(lineModel.type).toBe('line')
    if (lineModel.properties.type === 'line') {
      expect(lineModel.properties.start.x).toBe(-10)
      expect(lineModel.properties.opacity).toBe(1)
    }

    // Example Arc
    const arcModel: ShapeModel = {
      id: 'arc1',
      type: 'arc',
      position: { x: 100, y: 100 },
      visible: true,
      properties: {
        type: 'arc',
        radius: 50,
        startAngle: 0,
        endAngle: 90,
        opacity: 1,
      },
      metadata: { createdAt: Date.now(), updatedAt: Date.now() },
    }
    expect(arcModel.type).toBe('arc')
    if (arcModel.properties.type === 'arc') {
      expect(arcModel.properties.radius).toBe(50)
      expect(arcModel.properties.opacity).toBe(1)
    }
  })

  it('should allow basic assignment for property types', () => {
    const rectProps: RectangleProperties = {
      width: 10,
      height: 10,
    }
    expect(rectProps.width).toBe(10)

    const ellipseProps: EllipseProperties = {
      radiusX: 5,
      radiusY: 5,
    }
    expect(ellipseProps.radiusX).toBe(5)

    const baseStyle: BaseStyleProperties = {
      fill: '#fff',
      stroke: '#000',
      strokeWidth: 1,
      opacity: 1,
    }
    expect(baseStyle.strokeWidth).toBe(1)
    expect(baseStyle.opacity).toBe(1)
  })
})
