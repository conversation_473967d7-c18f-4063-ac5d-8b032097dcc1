import type { ElementFactory } from '@/core/factory'
import type { ShapeRepository } from '@/core/state/ShapeRepository'
import type { ServiceRegistry } from '@/services/core/registry'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { getServiceFactory } from '@/services/core/registry'
import { registerElementServices } from '@/services/elements/element-actions'
import { ServiceId } from '@/types/services/core/serviceIdentifier'

// Mock dependencies
vi.mock('@/services/core/registry', () => ({
  getServiceFactory: vi.fn(),
}))

describe('element Services Registry', () => {
  let mockRegistry: ServiceRegistry
  let mockLogger: LoggerService
  let mockElementFactory: ElementFactory
  let mockShapeRepository: ShapeRepository
  let mockServiceFactory: any
  let mockEventBus: any
  let mockServices: any

  beforeEach(() => {
    mockEventBus = {
      publish: vi.fn(),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
      clear: vi.fn(),
    }

    mockServices = {
      creationService: { create: vi.fn() },
      editService: { edit: vi.fn() },
      deleteService: { delete: vi.fn() },
      selectionService: { select: vi.fn() },
    }

    mockServiceFactory = {
      createEventBus: vi.fn().mockReturnValue(mockEventBus),
      createElementCreationService: vi.fn().mockReturnValue(mockServices.creationService),
      createElementEditService: vi.fn().mockReturnValue(mockServices.editService),
      createElementDeleteService: vi.fn().mockReturnValue(mockServices.deleteService),
      createElementSelectionService: vi.fn().mockReturnValue(mockServices.selectionService),
    }

    mockRegistry = {
      register: vi.fn(),
      get: vi.fn(),
      has: vi.fn(),
      unregister: vi.fn(),
      clear: vi.fn(),
      getAll: vi.fn(),
    }

    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    }

    mockElementFactory = {
      createElement: vi.fn(),
      createShape: vi.fn(),
    } as unknown as ElementFactory

    mockShapeRepository = {
      add: vi.fn(),
      remove: vi.fn(),
      get: vi.fn(),
      getAll: vi.fn(),
    } as unknown as ShapeRepository

    vi.mocked(getServiceFactory).mockReturnValue(mockServiceFactory)
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('registerElementServices', () => {
    it('should register all element services successfully', () => {
      const result = registerElementServices(
        mockRegistry,
        mockElementFactory,
        mockShapeRepository,
        mockLogger,
      )

      expect(getServiceFactory).toHaveBeenCalled()
      expect(mockServiceFactory.createEventBus).toHaveBeenCalled()
      expect(mockServiceFactory.createElementCreationService).toHaveBeenCalledWith(
        mockElementFactory,
        mockLogger,
      )
      expect(mockServiceFactory.createElementEditService).toHaveBeenCalledWith(
        mockEventBus,
        mockLogger,
      )
      expect(mockServiceFactory.createElementDeleteService).toHaveBeenCalledWith(
        mockEventBus,
        mockLogger,
      )
      expect(mockServiceFactory.createElementSelectionService).toHaveBeenCalledWith(
        mockEventBus,
        mockLogger,
      )

      expect(mockRegistry.register).toHaveBeenCalledTimes(4)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ElementCreationService,
        mockServices.creationService,
      )
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ElementEditService,
        mockServices.editService,
      )
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ElementDeleteService,
        mockServices.deleteService,
      )
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ElementSelectionService,
        mockServices.selectionService,
      )

      expect(mockLogger.info).toHaveBeenCalledWith('Element services registered successfully')

      expect(result).toEqual({
        creationService: mockServices.creationService,
        editService: mockServices.editService,
        deleteService: mockServices.deleteService,
        selectionService: mockServices.selectionService,
      })
    })

    it('should create services with correct dependencies', () => {
      registerElementServices(
        mockRegistry,
        mockElementFactory,
        mockShapeRepository,
        mockLogger,
      )

      expect(mockServiceFactory.createElementCreationService).toHaveBeenCalledWith(
        mockElementFactory,
        mockLogger,
      )
      expect(mockServiceFactory.createElementEditService).toHaveBeenCalledWith(
        mockEventBus,
        mockLogger,
      )
      expect(mockServiceFactory.createElementDeleteService).toHaveBeenCalledWith(
        mockEventBus,
        mockLogger,
      )
      expect(mockServiceFactory.createElementSelectionService).toHaveBeenCalledWith(
        mockEventBus,
        mockLogger,
      )
    })

    it('should return all created services', () => {
      const result = registerElementServices(
        mockRegistry,
        mockElementFactory,
        mockShapeRepository,
        mockLogger,
      )

      expect(result).toHaveProperty('creationService', mockServices.creationService)
      expect(result).toHaveProperty('editService', mockServices.editService)
      expect(result).toHaveProperty('deleteService', mockServices.deleteService)
      expect(result).toHaveProperty('selectionService', mockServices.selectionService)
    })

    it('should handle service factory errors gracefully', () => {
      const error = new Error('Factory creation failed')
      mockServiceFactory.createElementCreationService.mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerElementServices(
          mockRegistry,
          mockElementFactory,
          mockShapeRepository,
          mockLogger,
        )
      }).toThrow(error)

      expect(getServiceFactory).toHaveBeenCalled()
      expect(mockServiceFactory.createEventBus).toHaveBeenCalled()
      expect(mockRegistry.register).not.toHaveBeenCalled()
      expect(mockLogger.info).not.toHaveBeenCalled()
    })

    it('should handle registry registration errors gracefully', () => {
      const error = new Error('Registration failed')
      mockRegistry.register.mockImplementationOnce(() => {
        throw error
      })

      expect(() => {
        registerElementServices(
          mockRegistry,
          mockElementFactory,
          mockShapeRepository,
          mockLogger,
        )
      }).toThrow(error)

      expect(mockServiceFactory.createElementCreationService).toHaveBeenCalled()
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ElementCreationService,
        mockServices.creationService,
      )
      expect(mockLogger.info).not.toHaveBeenCalled()
    })

    it('should handle getServiceFactory errors gracefully', () => {
      const error = new Error('Service factory not available')
      vi.mocked(getServiceFactory).mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerElementServices(
          mockRegistry,
          mockElementFactory,
          mockShapeRepository,
          mockLogger,
        )
      }).toThrow(error)

      expect(getServiceFactory).toHaveBeenCalled()
      expect(mockRegistry.register).not.toHaveBeenCalled()
      expect(mockLogger.info).not.toHaveBeenCalled()
    })

    it('should work with different logger implementations', () => {
      const customLogger = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
        trace: vi.fn(), // Additional method
      }

      const result = registerElementServices(
        mockRegistry,
        mockElementFactory,
        mockShapeRepository,
        customLogger,
      )

      expect(mockServiceFactory.createElementCreationService).toHaveBeenCalledWith(
        mockElementFactory,
        customLogger,
      )
      expect(customLogger.info).toHaveBeenCalledWith('Element services registered successfully')
      expect(result).toBeDefined()
    })

    it('should work with different registry implementations', () => {
      const customRegistry = {
        register: vi.fn(),
        get: vi.fn(),
        has: vi.fn(),
        unregister: vi.fn(),
        clear: vi.fn(),
        getAll: vi.fn(),
        size: vi.fn(), // Additional method
      }

      const result = registerElementServices(
        customRegistry,
        mockElementFactory,
        mockShapeRepository,
        mockLogger,
      )

      expect(customRegistry.register).toHaveBeenCalledTimes(4)
      expect(result).toBeDefined()
    })

    it('should handle multiple registration calls', () => {
      const result1 = registerElementServices(
        mockRegistry,
        mockElementFactory,
        mockShapeRepository,
        mockLogger,
      )
      const result2 = registerElementServices(
        mockRegistry,
        mockElementFactory,
        mockShapeRepository,
        mockLogger,
      )

      expect(mockServiceFactory.createEventBus).toHaveBeenCalledTimes(2)
      expect(mockRegistry.register).toHaveBeenCalledTimes(8) // 4 services × 2 calls
      expect(mockLogger.info).toHaveBeenCalledTimes(2)
      expect(result1).toBeDefined()
      expect(result2).toBeDefined()
    })

    it('should create new service instances for each registration', () => {
      const services1 = {
        creationService: { create: vi.fn(), id: 'creation1' },
        editService: { edit: vi.fn(), id: 'edit1' },
        deleteService: { delete: vi.fn(), id: 'delete1' },
        selectionService: { select: vi.fn(), id: 'selection1' },
      }

      const services2 = {
        creationService: { create: vi.fn(), id: 'creation2' },
        editService: { edit: vi.fn(), id: 'edit2' },
        deleteService: { delete: vi.fn(), id: 'delete2' },
        selectionService: { select: vi.fn(), id: 'selection2' },
      }

      mockServiceFactory.createElementCreationService
        .mockReturnValueOnce(services1.creationService)
        .mockReturnValueOnce(services2.creationService)
      mockServiceFactory.createElementEditService
        .mockReturnValueOnce(services1.editService)
        .mockReturnValueOnce(services2.editService)
      mockServiceFactory.createElementDeleteService
        .mockReturnValueOnce(services1.deleteService)
        .mockReturnValueOnce(services2.deleteService)
      mockServiceFactory.createElementSelectionService
        .mockReturnValueOnce(services1.selectionService)
        .mockReturnValueOnce(services2.selectionService)

      const result1 = registerElementServices(
        mockRegistry,
        mockElementFactory,
        mockShapeRepository,
        mockLogger,
      )
      const result2 = registerElementServices(
        mockRegistry,
        mockElementFactory,
        mockShapeRepository,
        mockLogger,
      )

      expect(result1.creationService).toBe(services1.creationService)
      expect(result2.creationService).toBe(services2.creationService)
      expect(result1.editService).toBe(services1.editService)
      expect(result2.editService).toBe(services2.editService)
    })

    it('should preserve correct execution order', () => {
      const callOrder: string[] = []

      vi.mocked(getServiceFactory).mockImplementation(() => {
        callOrder.push('getServiceFactory')
        return mockServiceFactory
      })

      mockServiceFactory.createEventBus.mockImplementation(() => {
        callOrder.push('createEventBus')
        return mockEventBus
      })

      mockServiceFactory.createElementCreationService.mockImplementation(() => {
        callOrder.push('createElementCreationService')
        return mockServices.creationService
      })

      mockServiceFactory.createElementEditService.mockImplementation(() => {
        callOrder.push('createElementEditService')
        return mockServices.editService
      })

      mockServiceFactory.createElementDeleteService.mockImplementation(() => {
        callOrder.push('createElementDeleteService')
        return mockServices.deleteService
      })

      mockServiceFactory.createElementSelectionService.mockImplementation(() => {
        callOrder.push('createElementSelectionService')
        return mockServices.selectionService
      })

      mockRegistry.register.mockImplementation(() => {
        callOrder.push('register')
      })

      mockLogger.info.mockImplementation(() => {
        callOrder.push('info')
      })

      registerElementServices(
        mockRegistry,
        mockElementFactory,
        mockShapeRepository,
        mockLogger,
      )

      expect(callOrder).toEqual([
        'getServiceFactory',
        'createEventBus',
        'createElementCreationService',
        'createElementEditService',
        'createElementDeleteService',
        'createElementSelectionService',
        'register',
        'register',
        'register',
        'register',
        'info',
      ])
    })

    it('should use correct service identifiers', () => {
      registerElementServices(
        mockRegistry,
        mockElementFactory,
        mockShapeRepository,
        mockLogger,
      )

      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ElementCreationService,
        mockServices.creationService,
      )
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ElementEditService,
        mockServices.editService,
      )
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ElementDeleteService,
        mockServices.deleteService,
      )
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ElementSelectionService,
        mockServices.selectionService,
      )

      const registrationCalls = mockRegistry.register.mock.calls
      registrationCalls.forEach(([serviceId]) => {
        expect(typeof serviceId).toBe('string')
      })
    })

    it('should handle logger method failures gracefully', () => {
      const faultyLogger = {
        info: vi.fn().mockImplementation(() => {
          throw new Error('Logging failed')
        }),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
      }

      expect(() => {
        registerElementServices(
          mockRegistry,
          mockElementFactory,
          mockShapeRepository,
          faultyLogger,
        )
      }).toThrow('Logging failed')

      // Services should still be created and registered even if logging fails
      expect(mockRegistry.register).toHaveBeenCalledTimes(4)
    })

    it('should handle event bus creation errors', () => {
      const error = new Error('Event bus creation failed')
      mockServiceFactory.createEventBus.mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerElementServices(
          mockRegistry,
          mockElementFactory,
          mockShapeRepository,
          mockLogger,
        )
      }).toThrow(error)

      expect(mockServiceFactory.createEventBus).toHaveBeenCalled()
      expect(mockServiceFactory.createElementCreationService).not.toHaveBeenCalled()
    })

    it('should pass shape repository parameter (even if unused)', () => {
      const customShapeRepository = {
        add: vi.fn(),
        remove: vi.fn(),
        get: vi.fn(),
        getAll: vi.fn(),
        customMethod: vi.fn(),
      } as unknown as ShapeRepository

      const result = registerElementServices(
        mockRegistry,
        mockElementFactory,
        customShapeRepository,
        mockLogger,
      )

      // Even though shape repository is unused, function should still work
      expect(result).toBeDefined()
      expect(mockLogger.info).toHaveBeenCalledWith('Element services registered successfully')
    })
  })

  describe('integration scenarios', () => {
    it('should work in a typical application initialization flow', () => {
      const result = registerElementServices(
        mockRegistry,
        mockElementFactory,
        mockShapeRepository,
        mockLogger,
      )

      // Verify the complete flow
      expect(getServiceFactory).toHaveBeenCalled()
      expect(mockServiceFactory.createEventBus).toHaveBeenCalled()
      expect(mockRegistry.register).toHaveBeenCalledTimes(4)
      expect(mockLogger.info).toHaveBeenCalledWith('Element services registered successfully')
      expect(result).toEqual({
        creationService: mockServices.creationService,
        editService: mockServices.editService,
        deleteService: mockServices.deleteService,
        selectionService: mockServices.selectionService,
      })
    })

    it('should handle service replacement scenarios', () => {
      // Initial registration
      const result1 = registerElementServices(
        mockRegistry,
        mockElementFactory,
        mockShapeRepository,
        mockLogger,
      )

      // Service replacement
      const newServices = {
        creationService: { create: vi.fn(), id: 'new-creation' },
        editService: { edit: vi.fn(), id: 'new-edit' },
        deleteService: { delete: vi.fn(), id: 'new-delete' },
        selectionService: { select: vi.fn(), id: 'new-selection' },
      }

      mockServiceFactory.createElementCreationService.mockReturnValue(newServices.creationService)
      mockServiceFactory.createElementEditService.mockReturnValue(newServices.editService)
      mockServiceFactory.createElementDeleteService.mockReturnValue(newServices.deleteService)
      mockServiceFactory.createElementSelectionService.mockReturnValue(newServices.selectionService)

      const result2 = registerElementServices(
        mockRegistry,
        mockElementFactory,
        mockShapeRepository,
        mockLogger,
      )

      expect(mockRegistry.register).toHaveBeenCalledTimes(8) // 4 services × 2 registrations
      expect(result1.creationService).toBe(mockServices.creationService)
      expect(result2.creationService).toBe(newServices.creationService)
    })
  })
})
