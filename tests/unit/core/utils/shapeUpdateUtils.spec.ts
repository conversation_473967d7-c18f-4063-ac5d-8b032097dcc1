import type { ShapeModel } from '@/types/core/models'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import * as geometryUtils from '@/core/utils/geometryUtils'
import { updatePolygonPropertiesFromRadiusSides } from '@/core/utils/shapeUpdateUtils'
import * as libUtils from '@/lib/utils'

// Mock dependencies
vi.mock('@/lib/utils', () => ({
  calculatePolygonPoints: vi.fn(),
}))

// Mock console methods to avoid actual logging
const originalConsoleWarn = console.warn
const originalConsoleError = console.error

beforeEach(() => {
  console.warn = vi.fn()
  console.error = vi.fn()
})

afterEach(() => {
  console.warn = originalConsoleWarn
  console.error = originalConsoleError
  vi.clearAllMocks()
})

describe('shapeUpdateUtils', () => {
  // We don't need consoleSpy anymore since we're directly mocking console methods

  describe('updatePolygonPropertiesFromRadiusSides', () => {
    // Sample polygon shape for testing
    const createPolygonShape = (): ShapeModel => ({
      id: 'polygon-1',
      type: 'polygon',
      position: { x: 100, y: 100 },
      properties: {
        points: [
          { x: 150, y: 100 },
          { x: 125, y: 150 },
          { x: 75, y: 150 },
          { x: 50, y: 100 },
          { x: 75, y: 50 },
          { x: 125, y: 50 },
        ],
      },
    })

    it('should return unchanged object if shape is not a polygon', async () => {
      const nonPolygonShape = {
        id: 'rect-1',
        type: 'rectangle',
        position: { x: 100, y: 100 },
        properties: { width: 100, height: 50 },
      }

      const changes = { properties: { width: 200 } }
      const result = await updatePolygonPropertiesFromRadiusSides(nonPolygonShape as any, changes)

      expect(result).toBe(changes)
    })

    it('should return unchanged object if shape has no properties', async () => {
      const invalidShape = {
        id: 'polygon-1',
        type: 'polygon',
        position: { x: 100, y: 100 },
      }

      const changes = { properties: { sides: 4 } }
      const result = await updatePolygonPropertiesFromRadiusSides(invalidShape as any, changes)

      expect(result).toBe(changes)
    })

    it('should return unchanged object if properties has no points', async () => {
      const invalidShape = {
        id: 'polygon-1',
        type: 'polygon',
        position: { x: 100, y: 100 },
        properties: { fill: 'red' },
      }

      const changes = { properties: { sides: 4 } }
      const result = await updatePolygonPropertiesFromRadiusSides(invalidShape as any, changes)

      expect(result).toBe(changes)
    })

    it('should return unchanged object if polygon has no points', async () => {
      const emptyPointsShape = {
        id: 'polygon-1',
        type: 'polygon',
        position: { x: 100, y: 100 },
        properties: { points: [] },
      }

      const changes = { properties: { sides: 4 } }
      const result = await updatePolygonPropertiesFromRadiusSides(emptyPointsShape as any, changes)

      expect(result).toBe(changes)
      expect(console.warn).toHaveBeenCalledWith(
        '[updatePolygonProperties] Polygon has no points to calculate from.',
        { shapeId: 'polygon-1' },
      )
    })

    it('should update polygon points and position when sides change', async () => {
      const polygonShape = createPolygonShape()
      const changes = { properties: { sides: 4 } }

      // Mock the calculatePolygonPoints function
      const newPoints = [
        { x: 150, y: 100 },
        { x: 100, y: 150 },
        { x: 50, y: 100 },
        { x: 100, y: 50 },
      ]

      vi.mocked(libUtils.calculatePolygonPoints).mockReturnValue(newPoints)

      // Spy on ensurePointInstance to make sure it's called correctly
      const ensurePointSpy = vi.spyOn(geometryUtils, 'ensurePointInstance')

      const result = await updatePolygonPropertiesFromRadiusSides(polygonShape, changes)

      // Verify ensurePointInstance was called for position and points
      expect(ensurePointSpy).toHaveBeenCalledWith(polygonShape.position, 'polygon center')
      expect(ensurePointSpy).toHaveBeenCalledWith(polygonShape.properties.points[0], 'polygon point')

      // Verify calculatePolygonPoints was called with correct params
      expect(libUtils.calculatePolygonPoints).toHaveBeenCalledWith(
        expect.any(Object), // center point
        expect.any(Number), // radius
        4, // sides
      )

      // Verify the changes object was updated correctly
      expect(result.properties.points).toEqual(newPoints)
      expect(result.position).toEqual({ x: 100, y: 100 }) // Assuming center doesn't change

      // Verify radius and sides were removed from properties
      expect(result.properties.sides).toBeUndefined()
    })

    it('should update polygon points and position when radius changes', async () => {
      const polygonShape = createPolygonShape()
      const changes = { properties: { radius: 75 } }

      // Mock the calculatePolygonPoints function
      const newPoints = [
        { x: 175, y: 100 },
        { x: 137.5, y: 175 },
        { x: 62.5, y: 175 },
        { x: 25, y: 100 },
        { x: 62.5, y: 25 },
        { x: 137.5, y: 25 },
      ]

      vi.mocked(libUtils.calculatePolygonPoints).mockReturnValue(newPoints)

      const result = await updatePolygonPropertiesFromRadiusSides(polygonShape, changes)

      // Verify calculatePolygonPoints was called with correct params
      expect(libUtils.calculatePolygonPoints).toHaveBeenCalledWith(
        expect.any(Object), // center point
        75, // radius
        6, // sides (from original shape)
      )

      // Verify the changes object was updated correctly
      expect(result.properties.points).toEqual(newPoints)
      expect(result.position).toEqual({ x: 100, y: 100 }) // Assuming center doesn't change

      // Verify radius was removed from properties
      expect(result.properties.radius).toBeUndefined()
    })

    it('should update polygon when both sides and radius change', async () => {
      const polygonShape = createPolygonShape()
      const changes = { properties: { sides: 3, radius: 60 } }

      // Mock the calculatePolygonPoints function
      const newPoints = [
        { x: 160, y: 100 },
        { x: 70, y: 152 },
        { x: 70, y: 48 },
      ]

      vi.mocked(libUtils.calculatePolygonPoints).mockReturnValue(newPoints)

      const result = await updatePolygonPropertiesFromRadiusSides(polygonShape, changes)

      // Verify calculatePolygonPoints was called with correct params
      expect(libUtils.calculatePolygonPoints).toHaveBeenCalledWith(
        expect.any(Object), // center point
        60, // radius
        3, // sides
      )

      // Verify the changes object was updated correctly
      expect(result.properties.points).toEqual(newPoints)

      // Verify radius and sides were removed from properties
      expect(result.properties.radius).toBeUndefined()
      expect(result.properties.sides).toBeUndefined()
    })

    it('should create properties object if it does not exist in changes', async () => {
      const polygonShape = createPolygonShape()
      const changes = {} // No properties object

      // Mock the calculatePolygonPoints function to return the same points
      vi.mocked(libUtils.calculatePolygonPoints).mockReturnValue([...polygonShape.properties.points])

      const result = await updatePolygonPropertiesFromRadiusSides(polygonShape, changes)

      // Verify properties object was created
      expect(result.properties).toBeDefined()
      expect(result.properties.points).toEqual(polygonShape.properties.points)
    })

    it('should handle invalid sides (less than 3)', async () => {
      const polygonShape = createPolygonShape()
      const changes = { properties: { sides: 2 } }

      const result = await updatePolygonPropertiesFromRadiusSides(polygonShape, changes)

      // Verify error was logged and published
      expect(console.error).toHaveBeenCalledWith(
        '[updatePolygonProperties] Invalid sides or radius for polygon update',
        expect.any(Object),
      )

      // We're not mocking errorHelpers.publishError anymore, so we don't check it

      // Verify changes were not modified
      expect(result).toBe(changes)
    })

    it('should handle invalid radius (less than or equal to 0)', async () => {
      const polygonShape = createPolygonShape()
      const changes = { properties: { radius: 0 } }

      const result = await updatePolygonPropertiesFromRadiusSides(polygonShape, changes)

      // Verify error was logged and published
      expect(console.error).toHaveBeenCalledWith(
        '[updatePolygonProperties] Invalid sides or radius for polygon update',
        expect.any(Object),
      )

      // We're not mocking errorHelpers.publishError anymore, so we don't check it

      // Verify changes were not modified
      expect(result).toBe(changes)
    })

    it('should handle exceptions during calculation', async () => {
      const polygonShape = createPolygonShape()
      const changes = { properties: { sides: 5 } }

      // Mock calculatePolygonPoints to throw an error
      const errorMessage = 'Calculation failed'
      vi.mocked(libUtils.calculatePolygonPoints).mockImplementation(() => {
        throw new Error(errorMessage)
      })

      const result = await updatePolygonPropertiesFromRadiusSides(polygonShape, changes)

      // Verify error was logged and published
      expect(console.error).toHaveBeenCalledWith(
        '[updatePolygonProperties] Error during polygon update:',
        expect.any(Error),
      )

      // We're not mocking errorHelpers.publishError anymore, so we don't check it

      // Verify changes were not modified
      expect(result).toBe(changes)
    })
  })
})
