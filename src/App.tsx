import type { Template } from './types/template'
import React, { useEffect, useState } from 'react'
import { useSyncShapesStoreToRepository } from '@/store/shapesStore'
import EditorLayout from './components/layout/EditorLayout'
import { TemplateSelectionPage } from './components/template/TemplateSelectionPage'

import { TutorialProvider } from './components/tutorial/TutorialProvider'
import { useTemplateIntegration } from './hooks/useTemplate'
import { useTutorial } from './hooks/useTutorial'
import { appEventBus } from './services/core/event-bus'
import { templateService } from './services/template/templateService'
import './services/template/templateIntegrationService' // Import to initialize the service
import './styles/main.css'
import 'driver.js/dist/driver.css'

/**
 * Main application component
 */
const App: React.FC = () => {
  useSyncShapesStoreToRepository()

  const [showTemplateSelection, setShowTemplateSelection] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const { isTemplateMode } = useTemplateIntegration()
  const { startTutorial } = useTutorial()

  /**
   * Handle starting with blank canvas
   */
  const handleStartBlank = async () => {
    setIsLoading(true)
    try {
      // Clear any existing state and start fresh
      appEventBus.emit({
        type: 'template:start-blank',
        payload: {},
        timestamp: Date.now()
      })
      setShowTemplateSelection(false)

      // Start tutorial after a short delay to ensure UI is ready
      setTimeout(() => {
        startTutorial('app-overview')
      }, 1000)
    }
    catch (error) {
      console.error('Failed to start blank canvas:', error)
    }
    finally {
      setIsLoading(false)
    }
  }

  /**
   * Handle template selection
   */
  const handleSelectTemplate = async (template: Template) => {
    setIsLoading(true)
    try {
      await templateService.loadTemplate(template)
      setShowTemplateSelection(false)

      // Start tutorial after template is loaded

      // Only start if user hasn't seen tutorial before
      const hasSeenTutorial = localStorage.getItem('renopilot-has-seen-tutorial')
      if (!hasSeenTutorial) {
        setTimeout(() => {
          startTutorial('app-overview')
          localStorage.setItem('renopilot-has-seen-tutorial', 'true')
        }, 1500)
      }
      setTimeout(() => {
        startTutorial('app-overview')
      }, 1000)
    }
    catch (error) {
      console.error('Failed to load template:', error)
    }
    finally {
      setIsLoading(false)
    }
  }

  /**
   * Check if user has previously made a choice
   */
  useEffect(() => {
    const hasSeenTemplateSelection = localStorage.getItem('renopilot-has-seen-template-selection')
    if (hasSeenTemplateSelection) {
      setShowTemplateSelection(false)
    }
  }, [])

  /**
   * Save template selection preference
   */
  const saveTemplateSelectionPreference = () => {
    localStorage.setItem('renopilot-has-seen-template-selection', 'true')
  }

  /**
   * Handle template selection with preference saving
   */
  const handleTemplateSelectionWithPreference = async (template: Template) => {
    saveTemplateSelectionPreference()
    await handleSelectTemplate(template)
  }

  /**
   * Handle blank canvas with preference saving
   */
  const handleBlankCanvasWithPreference = async () => {
    saveTemplateSelectionPreference()
    await handleStartBlank()
  }

  if (showTemplateSelection || isTemplateMode) {
    return (
      <TutorialProvider>
        <TemplateSelectionPage
          onStartBlank={handleBlankCanvasWithPreference}
          onSelectTemplate={handleTemplateSelectionWithPreference}
          isVisible={true}
        />
      </TutorialProvider>
    )
  }

  return (
    <TutorialProvider>
      <div className="app">
        <EditorLayout />

        {isLoading && (
          <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
            <div className="flex flex-col items-center gap-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
              <p className="text-lg font-medium">Loading...</p>
            </div>
          </div>
        )}
      </div>
    </TutorialProvider>
  )
}

export default App
