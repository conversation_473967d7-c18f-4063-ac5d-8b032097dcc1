/**
 * Defines reusable business rule validations applicable to various shape types.
 *
 * @remarks
 * These rules are created using `createValidationRule` and applied by `applyBusinessRules`.
 */

import type { IValidationRule } from './ruleInterface'
import type { PointData } from '@/types/core/element/geometry/point' // Import PointData
import type { ValidationError, ValidatableShape as ValidatorShape } from '@/types/core/validator/validator-interface'
import { ElementType } from '@/types/core/elementDefinitions' // Import ElementType only
import { ValidationErrorCode } from '@/types/core/validator/error-codes'
import { isPolygonClosed, isValidPoint, isValueInRange } from '../common/validationUtils' // Assuming this path is correct
import { createValidationRule } from './ruleInterface' // Assuming this path is correct

/**
 * Minimum size (radius, width, height) allowed for shapes.
 */
const MIN_SIZE = 1
/**
 * Maximum size (radius, width, height) allowed for shapes.
 */
const MAX_SIZE = 10000 // Increased for practical use
/**
 * Minimum number of points allowed for a polygon.
 */
const MIN_POINTS = 3
/**
 * Maximum number of points allowed for a polygon.
 */
const MAX_POINTS = 1000 // Increased for practical use

/**
 * Minimum dimension (width, height, radius) or length (for lines) allowed.
 */
const MIN_DIMENSION = 1 // Adjusted to be more practical

/**
 * Validation Rule: Checks if shape dimensions (radius, width, height) are within the allowed range.
 *
 * @remarks
 * Applies to circles, rectangles, squares, and ellipses.
 */
export const sizeRule = createValidationRule(
  'size',
  (shape: ValidatorShape): ValidationError[] => {
    const errors: ValidationError[] = []

    switch (shape.type) {
      case ElementType.CIRCLE: {
        const circleProps = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined
        const circleRadius = circleProps?.radius as number | undefined
        if (!isValueInRange(circleRadius, MIN_SIZE, MAX_SIZE)) {
          errors.push({
            code: ValidationErrorCode.INVALID_RADIUS,
            message: `Circle radius must be between ${MIN_SIZE} and ${MAX_SIZE}. Received: ${circleRadius}`,
            path: 'properties.radius',
            value: circleRadius,
          })
        }
        break
      }

      case ElementType.RECTANGLE:
      case ElementType.SQUARE: { // Squares are also rectangles
        const rectProps = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined
        const rectWidth = rectProps?.width as number | undefined
        const rectHeight = rectProps?.height as number | undefined
        if (!isValueInRange(rectWidth, MIN_SIZE, MAX_SIZE)) {
          errors.push({
            code: ValidationErrorCode.INVALID_DIMENSION,
            message: `Rectangle width must be between ${MIN_SIZE} and ${MAX_SIZE}. Received: ${rectWidth}`,
            path: 'properties.width',
            value: rectWidth,
          })
        }
        if (!isValueInRange(rectHeight, MIN_SIZE, MAX_SIZE)) {
          errors.push({
            code: ValidationErrorCode.INVALID_DIMENSION,
            message: `Rectangle height must be between ${MIN_SIZE} and ${MAX_SIZE}. Received: ${rectHeight}`,
            path: 'properties.height',
            value: rectHeight,
          })
        }
        break
      }

      case ElementType.ELLIPSE: {
        const ellipseProps = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined
        const ellipseRadiusX = ellipseProps?.radiusX as number | undefined
        const ellipseRadiusY = ellipseProps?.radiusY as number | undefined
        if (!isValueInRange(ellipseRadiusX, MIN_SIZE, MAX_SIZE)) {
          errors.push({
            code: ValidationErrorCode.INVALID_RADIUS,
            message: `Ellipse X radius must be between ${MIN_SIZE} and ${MAX_SIZE}. Received: ${ellipseRadiusX}`,
            path: 'properties.radiusX',
            value: ellipseRadiusX,
          })
        }
        if (!isValueInRange(ellipseRadiusY, MIN_SIZE, MAX_SIZE)) {
          errors.push({
            code: ValidationErrorCode.INVALID_RADIUS,
            message: `Ellipse Y radius must be between ${MIN_SIZE} and ${MAX_SIZE}. Received: ${ellipseRadiusY}`,
            path: 'properties.radiusY',
            value: ellipseRadiusY,
          })
        }
        break
      }
    }
    return errors
  },
)

/**
 * Validation Rule: Checks if a polygon's point count is within the allowed range.
 *
 * @remarks
 * Applies to polygons, triangles, hexagons, and polylines.
 */
export const pointCountRule = createValidationRule(
  'pointCount',
  (shape: ValidatorShape): ValidationError[] => {
    const errors: ValidationError[] = []
    // This rule applies to polygons and polylines
    if (shape.type === ElementType.POLYGON || shape.type === ElementType.POLYLINE
      || shape.type === ElementType.TRIANGLE || shape.type === ElementType.HEXAGON /* add other polygon types */) {
      // Access points from the generic properties object
      const props = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined
      const points = props?.points as PointData[] | undefined // Access points via properties

      if (points == null || !Array.isArray(points)) {
        errors.push({ code: ValidationErrorCode.MISSING_PROPERTY, message: `${shape.type} requires a points array in its properties.`, path: 'properties.points' })
        return errors
      }
      const pointCount = points.length
      const minPointsForType = (shape.type === ElementType.POLYLINE) ? 2 : MIN_POINTS
      if (pointCount < minPointsForType || pointCount > MAX_POINTS) {
        errors.push({
          code: ValidationErrorCode.INSUFFICIENT_POINTS, // Or more generic code
          message: `${shape.type} point count must be between ${minPointsForType} and ${MAX_POINTS}. Received: ${pointCount}`,
          path: 'properties.points.length',
          value: pointCount,
        })
      }
    }
    return errors
  },
)

/**
 * Validation Rule: Checks if a polygon is closed.
 *
 * @remarks
 * Applies to polygons, triangles, and hexagons.
 */
export const polygonClosureRule = createValidationRule(
  'polygonClosure',
  (shape: ValidatorShape): ValidationError[] => {
    const errors: ValidationError[] = []
    if (shape.type === ElementType.POLYGON || shape.type === ElementType.TRIANGLE || shape.type === ElementType.HEXAGON /* add other polygon types */) {
      // Access points from the generic properties object
      const props = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined
      const points = props?.points as PointData[] | undefined // Access points via properties

      if (points == null || points.length < MIN_POINTS) {
        // This should be caught by pointCountRule, but good to be defensive
        return errors
      }
      // Pass the correct points array to isPolygonClosed
      if (!isPolygonClosed(points)) {
        errors.push({
          code: ValidationErrorCode.POLYGON_NOT_CLOSED,
          message: 'Polygon must be closed (first and last points should be coincident).',
          path: 'properties.points',
        })
      }
    }
    return errors
  },
)

/**
 * Validation Rule: Checks if the shape has a non-empty `id` property.
 *
 * @remarks
 * Applies to all shape types.
 */
export const idRule = createValidationRule(
  'id',
  (shape: ValidatorShape): ValidationError[] => {
    const errors: ValidationError[] = []
    if (!shape.id || typeof shape.id !== 'string' || shape.id.trim() === '') {
      errors.push({
        code: ValidationErrorCode.MISSING_OR_INVALID_ID,
        message: 'Element requires a non-empty string ID.',
        path: 'id',
        value: shape.id,
      })
    }
    return errors
  },
)

/**
 * Validation Rule: Checks if shape dimensions or length meet the minimum requirement.
 *
 * @remarks
 * Applies to circles, ellipses, rectangles, squares, and lines.
 */
export const minSizeRule = createValidationRule(
  'minSize',
  (shape: ValidatorShape): ValidationError[] => {
    const errors: ValidationError[] = []
    switch (shape.type) {
      case ElementType.CIRCLE:
        {
          const circleProps = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined
          const circleRadius = circleProps?.radius as number | undefined
          if (typeof circleRadius === 'number' && circleRadius < MIN_DIMENSION) {
            errors.push({ code: ValidationErrorCode.INVALID_RADIUS, message: `Circle radius must be at least ${MIN_DIMENSION}. Received: ${circleRadius}`, path: 'properties.radius', value: circleRadius })
          }
        }
        break
      case ElementType.ELLIPSE:
        {
          const ellipseProps = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined
          const ellipseRadiusX = ellipseProps?.radiusX as number | undefined
          const ellipseRadiusY = ellipseProps?.radiusY as number | undefined
          if (typeof ellipseRadiusX === 'number' && ellipseRadiusX < MIN_DIMENSION) {
            errors.push({ code: ValidationErrorCode.INVALID_RADIUS, message: `Ellipse radiusX must be at least ${MIN_DIMENSION}. Received: ${ellipseRadiusX}`, path: 'properties.radiusX', value: ellipseRadiusX })
          }
          if (typeof ellipseRadiusY === 'number' && ellipseRadiusY < MIN_DIMENSION) {
            errors.push({ code: ValidationErrorCode.INVALID_RADIUS, message: `Ellipse radiusY must be at least ${MIN_DIMENSION}. Received: ${ellipseRadiusY}`, path: 'properties.radiusY', value: ellipseRadiusY })
          }
        }
        break
      case ElementType.RECTANGLE:
      case ElementType.SQUARE:
        {
          const rectProps = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined
          const rectWidth = rectProps?.width as number | undefined
          const rectHeight = rectProps?.height as number | undefined
          if (typeof rectWidth === 'number' && rectWidth < MIN_DIMENSION) {
            errors.push({ code: ValidationErrorCode.INVALID_DIMENSION, message: `Rectangle width must be at least ${MIN_DIMENSION}. Received: ${rectWidth}`, path: 'properties.width', value: rectWidth })
          }
          if (typeof rectHeight === 'number' && rectHeight < MIN_DIMENSION) {
            errors.push({ code: ValidationErrorCode.INVALID_DIMENSION, message: `Rectangle height must be at least ${MIN_DIMENSION}. Received: ${rectHeight}`, path: 'properties.height', value: rectHeight })
          }
        }
        break
      case ElementType.LINE:
        {
          const lineProps = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined
          const startPt = lineProps?.start as { x: number, y: number } | undefined
          const endPt = lineProps?.end as { x: number, y: number } | undefined
          if (startPt != null && endPt != null && isValidPoint(startPt) && isValidPoint(endPt)) {
            const dx = endPt.x - startPt.x
            const dy = endPt.y - startPt.y
            const length = Math.sqrt(dx * dx + dy * dy)
            if (length < MIN_DIMENSION) {
              errors.push({ code: ValidationErrorCode.INVALID_DIMENSION, message: `Line length must be at least ${MIN_DIMENSION}. Received: ${length.toFixed(2)}`, path: 'properties.start,properties.end', value: length })
            }
          }
          else {
            errors.push({ code: ValidationErrorCode.INVALID_POINT, message: `Line has invalid start or end points in properties for length calculation.`, path: 'properties.start,properties.end' })
          }
        }
        break
    }
    return errors
  },
)

/**
 * Array containing all defined business rules.
 *
 * @remarks
 * Add new rules to this array to have them automatically applied by `applyBusinessRules`.
 * The order might matter if rules depend on each other, but typically they should be independent.
 */
const allRules: IValidationRule[] = [
  idRule,
  sizeRule,
  pointCountRule,
  polygonClosureRule,
  minSizeRule,
  // Add other rules here as needed
]

/**
 * Applies all defined business rules to a given shape.
 *
 * @remarks
 * Iterates through `allRules` and calls the rule's `apply` method for each,
 * collecting any returned errors. Includes error handling for individual rule application.
 * Each rule is responsible for checking the `shape.type` internally to determine if it applies.
 *
 * @param shape - The shape data object to validate against business rules.
 * @returns An array containing all validation errors resulting from the applied business rules.
 */
export function applyBusinessRules(shape: ValidatorShape): ValidationError[] {
  const errors: ValidationError[] = []

  allRules.forEach((rule) => {
    // Each rule's apply method should check the shape.type internally
    try {
      const ruleErrors = rule.apply(shape)
      if (ruleErrors.length > 0) {
        errors.push(...ruleErrors)
      }
    }
    catch (error: unknown) {
      console.error(`[applyBusinessRules ERROR] Error applying rule ${rule.name} to ${shape.type} (ID: ${shape.id}):`, error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      errors.push({
        code: ValidationErrorCode.VALIDATION_RULE_ERROR,
        message: `Rule ${rule.name} threw an error: ${errorMessage}`,
        path: 'businessRules', // General path for rule execution errors
      })
    }
  })

  return errors
}
