/**
 * Core Type Definitions for Shape Manipulation Handles
 *
 * @remarks
 * This module defines the core types and interfaces related to interactive manipulation
 * handles for shapes on a canvas. These handles are visual controls that allow users
 * to perform actions like resizing, rotating, or moving elements.
 *
 * Key definitions include:
 * - {@link HandleActionType}: A string literal union type specifying the set of possible
 *   actions a handle can perform (e.g., 'resize-corner-tl', 'rotate').
 * - {@link HandleModel}: An interface describing the data structure for a single handle,
 *   including its ID, associated shape ID, action type, position, and optional state.
 *
 * These types are essential for implementing interactive shape editing features.
 *
 * @module types/core/handleTypes
 * @see {@link ./elementDefinitions} for related element type definitions.
 * @see {@link ./canvasTypes} for canvas interaction and layer types.
 * @see {@link ./element/geometry/point} for the `Point` (PointData) type definition.
 */

import type Point from './element/geometry/point'

/**
 * Defines the set of possible actions that a shape manipulation handle can perform.
 * Each string literal represents a specific type of interactive transformation.
 *
 * @remarks
 * - `resize-corner-*`: Handles for resizing from a corner (e.g., top-left, bottom-right).
 * - `resize-edge-*`: Handles for resizing from an edge (e.g., top, left).
 * - `move-endpoint-*`: Handles for moving the start or end point of a line or path.
 * - `rotate`: Handle for rotating the shape.
 * - `move`: Handle for moving the entire shape (though often the shape itself is draggable).
 * - `custom`: For any other specialized handle behavior.
 */
export type HandleActionType =
  | 'resize-corner-tl'
  | 'resize-corner-tr'
  | 'resize-corner-bl'
  | 'resize-corner-br'
  | 'resize-edge-t'
  | 'resize-edge-r'
  | 'resize-edge-b'
  | 'resize-edge-l'
  | 'move-endpoint-start'
  | 'move-endpoint-end'
  | 'rotate'
  | 'move'
  | 'custom'

/**
 * Defines the data model for an interactive manipulation handle associated with a shape.
 *
 * @remarks
 * Handles are visual cues that allow users to modify shapes (e.g., resize, rotate).
 * This interface describes the properties of such a handle.
 *
 * @see {@link HandleActionType} for the types of actions a handle can perform.
 * @see {@link Point} (which is an alias for `PointData` from `./element/geometry/point`) for the position type.
 */
export interface HandleModel {
  /** A unique identifier for this specific handle instance. */
  id: string
  /** The identifier of the shape to which this handle is attached. */
  shapeId: string
  /** The {@link HandleActionType} that this handle triggers when interacted with. */
  action: HandleActionType
  /** The current position of the handle on the canvas, represented as a {@link Point}. */
  position: Point
  /** An optional flag indicating whether the handle is currently visible. Defaults to `true` if not specified. */
  visible?: boolean
  /** An optional flag indicating whether the handle is currently active (e.g., being dragged by the user). */
  active?: boolean
  /** An optional record for storing any custom data associated with this handle, allowing for extensibility. */
  data?: Record<string, unknown>
}
