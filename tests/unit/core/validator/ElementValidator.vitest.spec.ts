import type { ValidationResult, ValidatorShape } from '@/types/core/element/validator'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { CoreError, ErrorType } from '@/core/errors'
import { BaseShapeValidator } from '@/core/validator/common/validatorBase'
import { ElementValidator } from '@/core/validator/ElementValidator'
import { ElementType } from '@/types/core/shape-type'

// Mock the dynamic imports
vi.mock('@/core/validator/shapes/circleValidator', () => {
  return {
    default: class MockCircleValidator extends BaseShapeValidator {
      validate(element: ValidatorShape): ValidationResult {
        return { valid: element.id !== 'invalid-circle' }
      }
    },
  }
}, { virtual: true })

vi.mock('@/core/validator/shapes/rectangleValidator', () => {
  return {
    default: class MockRectangleValidator extends BaseShapeValidator {
      validate(element: ValidatorShape): ValidationResult {
        return { valid: true }
      }
    },
  }
}, { virtual: true })

vi.mock('@/core/validator/shapes/polygonValidator', () => {
  return {
    default: class MockPolygonValidator extends BaseShapeValidator {
      validate(element: ValidatorShape): ValidationResult {
        if (element.id === 'invalid-polygon') {
          return {
            valid: false,
            errors: [{ code: 'INVALID_POLYGON', message: 'Invalid polygon', path: 'properties.points' }],
          }
        }
        return { valid: true }
      }
    },
  }
}, { virtual: true })

vi.mock('@/core/validator/shapes/lineValidator', () => {
  return {
    default: class MockLineValidator extends BaseShapeValidator {
      validate(element: ValidatorShape): ValidationResult {
        return { valid: true }
      }
    },
  }
}, { virtual: true })

vi.mock('@/core/validator/shapes/ellipseValidator', () => {
  return {
    default: class MockEllipseValidator extends BaseShapeValidator {
      validate(element: ValidatorShape): ValidationResult {
        if (element.id === 'throw-error') {
          throw new Error('Validation error')
        }
        if (element.id === 'throw-core-error') {
          throw new CoreError(ErrorType.INVALID_PAYLOAD, 'Core validation error')
        }
        return { valid: true }
      }
    },
  }
}, { virtual: true })

// Mock logError function
vi.mock('@/core/errors', async (importOriginal) => {
  const actual = await importOriginal()
  return {
    ...actual,
    logError: vi.fn(),
  }
})

describe('elementValidator', () => {
  let consoleSpy: any

  beforeEach(() => {
    // Spy on console methods
    consoleSpy = {
      debug: vi.spyOn(console, 'debug').mockImplementation(() => {}),
      error: vi.spyOn(console, 'error').mockImplementation(() => {}),
    }
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('registerValidator', () => {
    it('should register a custom validator for a shape type', async () => {
      // Create a custom validator
      const customValidator = new class extends BaseShapeValidator {
        validate(element: ValidatorShape): ValidationResult {
          return { valid: element.id === 'custom-valid' }
        }
      }()

      // Register the custom validator
      ElementValidator.registerValidator(ElementType.RECTANGLE, customValidator)

      // Test with a valid element
      const validElement: ValidatorShape = {
        id: 'custom-valid',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: {},
      }

      const result = await ElementValidator.validateElement(validElement)
      expect(result.valid).toBe(true)

      // Test with an invalid element
      const invalidElement: ValidatorShape = {
        id: 'custom-invalid',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: {},
      }

      const invalidResult = await ElementValidator.validateElement(invalidElement)
      expect(invalidResult.valid).toBe(false)
    })
  })

  describe('validateElement', () => {
    it('should validate a circle element', async () => {
      const validCircle: ValidatorShape = {
        id: 'valid-circle',
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
        properties: { radius: 10 },
      }

      const result = await ElementValidator.validateElement(validCircle)
      expect(result.valid).toBe(true)
      expect(consoleSpy.debug).toHaveBeenCalledWith(expect.stringContaining('Validating element ID: valid-circle'))
    })

    it('should return invalid result for an invalid circle', async () => {
      const invalidCircle: ValidatorShape = {
        id: 'invalid-circle',
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
        properties: { radius: 10 },
      }

      const result = await ElementValidator.validateElement(invalidCircle)
      expect(result.valid).toBe(false)
    })

    it('should validate a rectangle element', async () => {
      // Mock validateElement to return valid=true for this test
      vi.spyOn(ElementValidator, 'getValidator').mockResolvedValueOnce({
        validate: () => ({ valid: true }),
      } as any)

      const validRectangle: ValidatorShape = {
        id: 'valid-rectangle',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: { width: 100, height: 50 },
      }

      const result = await ElementValidator.validateElement(validRectangle)
      expect(result.valid).toBe(true)
    })

    it('should validate a polygon element', async () => {
      const validPolygon: ValidatorShape = {
        id: 'valid-polygon',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 10, y: 0 },
            { x: 10, y: 10 },
          ],
        },
      }

      const result = await ElementValidator.validateElement(validPolygon)
      expect(result.valid).toBe(true)
    })

    it('should return validation errors for an invalid polygon', async () => {
      const invalidPolygon: ValidatorShape = {
        id: 'invalid-polygon',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 10, y: 0 },
          ], // Not enough points
        },
      }

      const result = await ElementValidator.validateElement(invalidPolygon)
      expect(result.valid).toBe(false)
      expect(result.errors).toBeDefined()
      expect(result.errors?.[0].code).toBe('INVALID_POLYGON')
      expect(result.errors?.[0].path).toBe('properties.points')
    })

    it('should handle errors thrown during validation', async () => {
      const errorElement: ValidatorShape = {
        id: 'throw-error',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        properties: { radiusX: 10, radiusY: 5 },
      }

      const result = await ElementValidator.validateElement(errorElement)
      expect(result.valid).toBe(false)
      expect(result.errors).toBeDefined()
      expect(result.errors?.[0].message).toBe('Validation error')
      expect(consoleSpy.error).toHaveBeenCalledWith(
        expect.stringContaining('Error during validation for element ID: throw-error'),
        expect.any(Error),
      )
    })

    it('should handle CoreError thrown during validation', async () => {
      const errorElement: ValidatorShape = {
        id: 'throw-core-error',
        type: ElementType.ELLIPSE,
        position: { x: 0, y: 0 },
        properties: { radiusX: 10, radiusY: 5 },
      }

      const result = await ElementValidator.validateElement(errorElement)
      expect(result.valid).toBe(false)
      expect(result.errors).toBeDefined()
      expect(result.errors?.[0].code).toBe(ErrorType.INVALID_PAYLOAD)
      expect(result.errors?.[0].message).toBe('Core validation error')
    })

    it('should handle unsupported shape types', async () => {
      // Create a shape with an unsupported type
      const unsupportedShape: ValidatorShape = {
        id: 'unsupported',
        type: 'unsupported-type' as ElementType,
        position: { x: 0, y: 0 },
        properties: {},
      }

      const result = await ElementValidator.validateElement(unsupportedShape)
      expect(result.valid).toBe(false)
      expect(result.errors?.[0].message).toContain('Validator module not found')
    })
  })

  describe('validateElements', () => {
    it('should validate multiple elements', async () => {
      // Mock validateElement to return valid=true for all elements
      vi.spyOn(ElementValidator, 'validateElement').mockResolvedValue({ valid: true })

      const elements: ValidatorShape[] = [
        {
          id: 'valid-circle',
          type: ElementType.CIRCLE,
          position: { x: 0, y: 0 },
          properties: { radius: 10 },
        },
        {
          id: 'valid-rectangle',
          type: ElementType.RECTANGLE,
          position: { x: 0, y: 0 },
          properties: { width: 100, height: 50 },
        },
      ]

      const results = await ElementValidator.validateElements(elements)
      expect(results).toHaveLength(2)
      expect(results[0].valid).toBe(true)
      expect(results[1].valid).toBe(true)
      expect(consoleSpy.debug).toHaveBeenCalledWith(expect.stringContaining('Validating batch of 2 elements'))
    })

    it('should validate mixed valid and invalid elements', async () => {
      // Mock validateElement to return different results for each element
      const validateElementSpy = vi.spyOn(ElementValidator, 'validateElement')
      validateElementSpy.mockResolvedValueOnce({ valid: true }) // First call returns valid
      validateElementSpy.mockResolvedValueOnce({ valid: false }) // Second call returns invalid

      const elements: ValidatorShape[] = [
        {
          id: 'valid-circle',
          type: ElementType.CIRCLE,
          position: { x: 0, y: 0 },
          properties: { radius: 10 },
        },
        {
          id: 'invalid-circle',
          type: ElementType.CIRCLE,
          position: { x: 0, y: 0 },
          properties: { radius: 10 },
        },
      ]

      const results = await ElementValidator.validateElements(elements)
      expect(results).toHaveLength(2)
      expect(results[0].valid).toBe(true)
      expect(results[1].valid).toBe(false)
    })
  })

  describe('areAllElementsValid', () => {
    it('should return true when all elements are valid', async () => {
      // Mock validateElement to return valid=true for all elements
      vi.spyOn(ElementValidator, 'validateElement').mockResolvedValue({ valid: true })

      const elements: ValidatorShape[] = [
        {
          id: 'valid-circle',
          type: ElementType.CIRCLE,
          position: { x: 0, y: 0 },
          properties: { radius: 10 },
        },
        {
          id: 'valid-rectangle',
          type: ElementType.RECTANGLE,
          position: { x: 0, y: 0 },
          properties: { width: 100, height: 50 },
        },
      ]

      const result = await ElementValidator.areAllElementsValid(elements)
      expect(result).toBe(true)
      expect(consoleSpy.debug).toHaveBeenCalledWith(expect.stringContaining('Checking validity of 2 elements'))
      expect(consoleSpy.debug).toHaveBeenCalledWith(expect.stringContaining('All 2 elements passed validity check'))
    })

    it('should return false when any element is invalid', async () => {
      // Mock validateElement to return valid=false for the second element
      const validateElementSpy = vi.spyOn(ElementValidator, 'validateElement')
      validateElementSpy.mockResolvedValueOnce({ valid: true }) // First call returns valid
      validateElementSpy.mockResolvedValueOnce({ valid: false }) // Second call returns invalid

      const elements: ValidatorShape[] = [
        {
          id: 'valid-circle',
          type: ElementType.CIRCLE,
          position: { x: 0, y: 0 },
          properties: { radius: 10 },
        },
        {
          id: 'invalid-rectangle',
          type: ElementType.RECTANGLE,
          position: { x: 0, y: 0 },
          properties: { width: 100, height: 50 },
        },
      ]

      const result = await ElementValidator.areAllElementsValid(elements)
      expect(result).toBe(false)
      expect(consoleSpy.debug).toHaveBeenCalledWith(expect.stringContaining('Validity check failed for element: invalid-rectangle'))
    })

    it('should short-circuit on the first invalid element', async () => {
      // Mock validateElement to return valid=false for the first element
      const validateElementSpy = vi.spyOn(ElementValidator, 'validateElement')
      validateElementSpy.mockResolvedValueOnce({ valid: false }) // First call returns invalid

      const elements: ValidatorShape[] = [
        {
          id: 'invalid-circle',
          type: ElementType.CIRCLE,
          position: { x: 0, y: 0 },
          properties: { radius: 10 },
        },
        {
          id: 'valid-rectangle',
          type: ElementType.RECTANGLE,
          position: { x: 0, y: 0 },
          properties: { width: 100, height: 50 },
        },
      ]

      const result = await ElementValidator.areAllElementsValid(elements)
      expect(result).toBe(false)
      // Should only validate the first element since it's invalid
      expect(validateElementSpy).toHaveBeenCalledTimes(1)
    })
  })
})
