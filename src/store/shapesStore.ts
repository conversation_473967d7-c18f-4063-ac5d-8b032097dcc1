// Zustand types
import type { <PERSON><PERSON><PERSON>, UseBoundStore } from 'zustand'
import type { ShapeRepository } from '@/core/state/ShapeRepository'
import type { ShapeElement } from '@/types/core/elementDefinitions'
import type { BaseEvent, EventBus } from '@/types/services/events'
import type { AppEventMap } from '@/types/services/events/eventRegistry'
import type { SelectionChangedEvent } from '@/types/services/events/shapeEvents'
// React and external libraries
import { useEffect } from 'react'
import { create } from 'zustand'
import { createJSONStorage, devtools, persist, subscribeWithSelector } from 'zustand/middleware'
// Project imports
import { appEventBus } from '@/services/core/event-bus'
import { getService } from '@/services/core/registry'
import { ServiceId } from '@/types/services/core/serviceIdentifier'
import { AppEventType } from '@/types/services/events/eventTypes'
// Augment the Window interface for TypeScript to recognize custom global properties
declare global {
  interface Window {
    __ZUSTAND_SHAPES_STORE__?: UseBoundStore<StoreApi<ShapesStoreState>>
    __ZUSTAND_SHAPES_STORE_DEBUG_ID__?: string
  }
}

// Define types for modes used in the editor
export enum EditorMode {
  VIEW = 'VIEW',
  DRAW = 'DRAW',
  EDIT = 'EDIT',
  SELECT = 'SELECT',
  PAN = 'PAN',
}

// Explicit type for the store's state and actions
export interface ShapesStoreState {
  shapes: ShapeElement[]
  selectedShapeIds: string[]
  setShapesFromExternal: (shapes: ShapeElement[], selectedShapeIds?: string[]) => void
  addShape: (shape: ShapeElement) => void
  clearShapes: () => void
  selectShape: (id: string, multiSelect?: boolean) => void
  selectShapes: (ids: string[], clearExisting?: boolean) => void
  deselectShape: (id: string) => void
  clearSelection: () => void
  // New actions for intra-layer z-ordering
  bringToFrontInLayer: (elementId: string) => void
  sendToBackInLayer: (elementId: string) => void
  // TODO: Consider adding updateElementProperties for generic updates including intraLayerZIndex if needed elsewhere
}

// Renamed for clarity in the new singleton pattern
function createActualShapesStore() {
  return create<ShapesStoreState>()(
    persist(
      devtools(
        subscribeWithSelector(
          set => ({
            shapes: [],
            selectedShapeIds: [],
            setShapesFromExternal: (shapes, selectedShapeIds = []) => {
              set({ shapes, selectedShapeIds }, false, 'setShapesFromExternal')
            },
            addShape: (shape) => {
              set(
                state => ({
                  shapes: [...state.shapes, shape],
                }),
                false,
                'addShape',
              )
            },
            clearShapes: () => {
              set(
                { shapes: [], selectedShapeIds: [] },
                false,
                'clearShapes',
              )
            },
            selectShape: (id, multiSelect = false) => {
              set(
                state => ({
                  selectedShapeIds: multiSelect
                    ? Array.from(new Set([...state.selectedShapeIds, id]))
                    : [id],
                }),
                false,
                'selectShape',
              )
            },
            selectShapes: (ids, clearExisting = true) => {
              set(
                state => ({
                  selectedShapeIds: clearExisting ? [...ids] : Array.from(new Set([...state.selectedShapeIds, ...ids])),
                }),
                false,
                'selectShapes',
              )
            },
            deselectShape: (id) => {
              set(
                state => ({
                  selectedShapeIds: state.selectedShapeIds.filter(sid => sid !== id),
                }),
                false,
                'deselectShape',
              )
            },
            clearSelection: () => {
              set({ selectedShapeIds: [] }, false, 'clearSelection')
            },
            // Implementations for new intra-layer z-ordering actions
            bringToFrontInLayer: (elementId) => {
              console.warn(`[ShapesStore] bringToFrontInLayer called for element: ${elementId}`)
              const eventBus = getService<EventBus<AppEventMap>>(ServiceId.EventBus)
              if (eventBus !== null && eventBus !== undefined && typeof eventBus.publish === 'function') {
                eventBus.publish({
                  type: AppEventType.ShapeBringToFrontRequest,
                  timestamp: Date.now(),
                  payload: {
                    shapeId: elementId,
                    source: 'ShapesStore',
                  },
                })
              }
            },
            sendToBackInLayer: (elementId) => {
              console.warn(`[ShapesStore] sendToBackInLayer called for element: ${elementId}`)
              const eventBus = getService<EventBus<AppEventMap>>(ServiceId.EventBus)
              if (eventBus !== null && eventBus !== undefined && typeof eventBus.publish === 'function') {
                eventBus.publish({
                  type: AppEventType.ShapeSendToBackRequest,
                  timestamp: Date.now(),
                  payload: {
                    shapeId: elementId,
                    source: 'ShapesStore',
                  },
                })
              }
            },
          }),
        ),
        { name: 'ShapesStore' },
      ),
      {
        name: 'reno-pilot-shapes-storage', // localStorage key
        storage: createJSONStorage(() => localStorage),
        partialize: state => ({
          shapes: state.shapes,
          selectedShapeIds: state.selectedShapeIds,
        }),
        // Add merge function to handle data loading
        merge: (persistedState, currentState) => {
          return {
            ...currentState,
            ...(persistedState || {}),
          }
        },
        // Add version for migration if needed
        version: 1,
      },
    ),
  )
}

let shapesStoreSingleton: UseBoundStore<StoreApi<ShapesStoreState>>

if (typeof window !== 'undefined') {
  if (!window.__ZUSTAND_SHAPES_STORE__) {
    window.__ZUSTAND_SHAPES_STORE__ = createActualShapesStore()
    window.__ZUSTAND_SHAPES_STORE_DEBUG_ID__ = Math.random().toString(36).slice(2)
    console.warn('[shapesStore] Singleton debug id:', window.__ZUSTAND_SHAPES_STORE_DEBUG_ID__)
  }
  shapesStoreSingleton = window.__ZUSTAND_SHAPES_STORE__
}
else {
  // Fallback for non-window environments (e.g., SSR, testing)
  shapesStoreSingleton = createActualShapesStore()
}

export const useShapesStore = shapesStoreSingleton

// Vite hot reload protection
if (import.meta?.hot !== null && import.meta?.hot !== undefined) {
  (import.meta.hot as { accept: (callback: () => void) => void }).accept(() => {
    console.warn('[shapesStore] Hot module replacement detected.')
  })
}

export function useShapesStoreEventSync() {
  const { setShapesFromExternal } = useShapesStore()

  useEffect(() => {
    const onDataUpdated = (event: BaseEvent) => {
      if (event.type === AppEventType.DataUpdated) {
        // 只处理 shapes 数据
        const payload = event.payload as { type?: string, data?: ShapeElement[] }
        if (payload !== null && payload !== undefined && payload.type === 'shapes' && Array.isArray(payload.data)) {
          setShapesFromExternal(payload.data)
        }
      }
    }

    const onSelectionChanged = (event: BaseEvent) => {
      if (
        event.type === AppEventType.SelectionChanged
        && event.payload !== null
        && event.payload !== undefined
        && typeof event.payload === 'object'
        && 'selectedIds' in event.payload
        && Array.isArray((event.payload as SelectionChangedEvent['payload']).selectedIds)
      ) {
        const payload = event.payload as SelectionChangedEvent['payload']
        const currentShapes = useShapesStore.getState().shapes
        const validSelectedIds = payload.selectedIds.filter((id): id is string => typeof id === 'string')
        useShapesStore.getState().setShapesFromExternal(currentShapes, validSelectedIds)
      }
    }

    const onZOrderChange = (event: BaseEvent) => {
      if (
        event.type === AppEventType.ShapeBringToFrontComplete
        || event.type === AppEventType.ShapeSendToBackComplete
      ) {
        console.warn(`[useShapesStoreEventSync] Received ${event.type} event:`, event)
        if (event.payload !== null && event.payload !== undefined && typeof event.payload === 'object' && 'shape' in event.payload) {
          // 获取当前store状态
          const currentState = useShapesStore.getState()
          const updatedShape = JSON.parse(JSON.stringify((event.payload as { shape: ShapeElement }).shape)) as ShapeElement

          // 更新store里的指定形状
          const updatedShapes = currentState.shapes.map(shape =>
            shape.id === updatedShape.id ? updatedShape : shape,
          )

          // 保持选择状态
          let selectedIds = currentState.selectedShapeIds
          if ('selectedIds' in event.payload && Array.isArray((event.payload as { selectedIds: string[] }).selectedIds)) {
            selectedIds = (event.payload as { selectedIds: string[] }).selectedIds
          }

          console.warn(`[useShapesStoreEventSync] Updating store with ${updatedShapes.length} shapes and ${selectedIds.length} selected IDs`)
          console.warn(`[useShapesStoreEventSync] Updated shape ${updatedShape.id} intraLayerZIndex: ${updatedShape.intraLayerZIndex}`)

          // 设置更新后的状态，使用深拷贝避免引用问题
          setShapesFromExternal(JSON.parse(JSON.stringify(updatedShapes)) as ShapeElement[], [...selectedIds])
        }
      }
    }

    // ... existing code ...

    // Add event subscriptions
    appEventBus.subscribe(AppEventType.DataUpdated, onDataUpdated)
    appEventBus.subscribe(AppEventType.SelectionChanged, onSelectionChanged)

    // 添加Z序变更事件订阅
    appEventBus.subscribe(AppEventType.ShapeBringToFrontComplete, onZOrderChange)
    appEventBus.subscribe(AppEventType.ShapeSendToBackComplete, onZOrderChange)

    return () => {
      // Remove event subscriptions
      appEventBus.unsubscribe(AppEventType.DataUpdated, onDataUpdated)
      appEventBus.unsubscribe(AppEventType.SelectionChanged, onSelectionChanged)

      // 移除Z序变更事件订阅
      appEventBus.unsubscribe(AppEventType.ShapeBringToFrontComplete, onZOrderChange)
      appEventBus.unsubscribe(AppEventType.ShapeSendToBackComplete, onZOrderChange)
    }
  }, [setShapesFromExternal])

  return null
}

// 在文件末尾添加自动同步逻辑
export function useSyncShapesStoreToRepository() {
  const shapes = useShapesStore(state => state.shapes)
  const selectedShapeIds = useShapesStore(state => state.selectedShapeIds)

  useEffect(() => {
    const repo = getService<ShapeRepository>(ServiceId.ShapeRepository)
    if (repo !== null && repo !== undefined && typeof repo.setShapesFromExternal === 'function') {
      repo.setShapesFromExternal(shapes, selectedShapeIds)
    }
  }, [shapes, selectedShapeIds])
}
