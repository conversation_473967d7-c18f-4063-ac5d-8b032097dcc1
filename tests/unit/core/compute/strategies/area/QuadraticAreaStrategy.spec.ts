import { beforeEach, describe, expect, it, vi } from 'vitest'
import { QuadraticAreaStrategy } from '@/core/compute/strategies/area/QuadraticAreaStrategy'
import { CoreError } from '@/services/system/error-service'
import { ElementType } from '@/types/core/elementDefinitions'

// Mock the utility functions
vi.mock('@/lib/utils/geometry/bezierUtils', () => ({
  sampleQuadraticBezier: vi.fn((p0, p1, p2, numSegments) => {
    // Simple mock: return a triangle-like polygon
    return [
      { x: 0, y: 0 },
      { x: 10, y: 5 },
      { x: 20, y: 0 },
    ]
  }),
}))

vi.mock('@/lib/utils/geometry/polygonUtils', () => ({
  calculateArea: vi.fn((points) => {
    // Simple mock: return area of 50 for the triangle
    return 50
  }),
}))

describe('quadraticAreaStrategy', () => {
  let strategy: QuadraticAreaStrategy

  beforeEach(() => {
    strategy = new QuadraticAreaStrategy()
    vi.clearAllMocks()
  })

  describe('constructor and Basic Properties', () => {
    it('should be defined and instantiated', () => {
      expect(strategy).toBeDefined()
      expect(strategy).toBeInstanceOf(QuadraticAreaStrategy)
    })

    it('should return correct element type', () => {
      expect(strategy.getElementType()).toBe(ElementType.QUADRATIC)
    })

    it('should have required methods', () => {
      expect(typeof strategy.calculateArea).toBe('function')
      expect(typeof strategy.getElementType).toBe('function')
    })
  })

  describe('area Calculation', () => {
    it('should calculate area for closed quadratic curve correctly', () => {
      const quadraticElement = {
        id: 'quadratic-1',
        type: ElementType.QUADRATIC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: -10 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      const area = strategy.calculateArea(quadraticElement as any)
      expect(area).toBe(50) // From our mock
    })

    it('should return 0 for open quadratic curve', () => {
      const quadraticElement = {
        id: 'quadratic-2',
        type: ElementType.QUADRATIC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: -10 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      const area = strategy.calculateArea(quadraticElement as any)
      expect(area).toBe(0)
    })

    it('should handle missing closed property (defaults to false)', () => {
      const quadraticElement = {
        id: 'quadratic-no-closed',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: -10 },
          end: { x: 20, y: 0 },
          // closed property missing
        },
      }

      const area = strategy.calculateArea(quadraticElement as any)
      expect(area).toBe(0)
    })

    it('should call bezier sampling with correct parameters', () => {
      const { sampleQuadraticBezier } = require('@/lib/utils/geometry/bezierUtils')

      const quadraticElement = {
        id: 'quadratic-test',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: -10 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      strategy.calculateArea(quadraticElement as any)

      expect(sampleQuadraticBezier).toHaveBeenCalledWith(
        { x: 0, y: 0 },
        { x: 10, y: -10 },
        { x: 20, y: 0 },
        20, // numSegments
      )
    })
  })

  describe('error Handling', () => {
    it('should throw error for non-quadratic element', () => {
      const rectangleElement = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        properties: {},
      }

      expect(() => strategy.calculateArea(rectangleElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculateArea(rectangleElement as any))
        .toThrow('QuadraticAreaStrategy can only calculate area for QUADRATIC elements')
    })

    it('should throw error for missing start point', () => {
      const quadraticElement = {
        id: 'quadratic-no-start',
        type: ElementType.QUADRATIC,
        properties: {
          start: null,
          control: { x: 10, y: -10 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(quadraticElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculateArea(quadraticElement as any))
        .toThrow('Invalid quadratic curve points')
    })

    it('should throw error for invalid start point coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-invalid-start',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 'invalid', y: 0 },
          control: { x: 10, y: -10 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(quadraticElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for missing control point', () => {
      const quadraticElement = {
        id: 'quadratic-no-control',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: undefined,
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(quadraticElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for invalid control point coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-invalid-control',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: Number.NaN },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(quadraticElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for missing end point', () => {
      const quadraticElement = {
        id: 'quadratic-no-end',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: -10 },
          end: null,
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(quadraticElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for invalid end point coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-invalid-end',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: -10 },
          end: { x: Infinity, y: 0 },
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(quadraticElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for points with missing coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-missing-coords',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0 }, // missing y
          control: { x: 10, y: -10 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(quadraticElement as any))
        .toThrow(CoreError)
    })
  })

  describe('edge Cases', () => {
    it('should handle points with zero coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-zero-coords',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 0, y: 0 },
          end: { x: 0, y: 0 },
          closed: true,
        },
      }

      const area = strategy.calculateArea(quadraticElement as any)
      expect(area).toBe(50) // From our mock
    })

    it('should handle negative coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-negative-coords',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: -10, y: -10 },
          control: { x: 0, y: -20 },
          end: { x: 10, y: -10 },
          closed: true,
        },
      }

      const area = strategy.calculateArea(quadraticElement as any)
      expect(area).toBe(50) // From our mock
    })

    it('should handle very large coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-large-coords',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 1000000, y: 1000000 },
          control: { x: 1000010, y: 999990 },
          end: { x: 1000020, y: 1000000 },
          closed: true,
        },
      }

      const area = strategy.calculateArea(quadraticElement as any)
      expect(area).toBe(50) // From our mock
    })

    it('should handle very small coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-small-coords',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0.001, y: 0.001 },
          control: { x: 0.010, y: -0.009 },
          end: { x: 0.020, y: 0.001 },
          closed: true,
        },
      }

      const area = strategy.calculateArea(quadraticElement as any)
      expect(area).toBe(50) // From our mock
    })

    it('should handle collinear points', () => {
      const quadraticElement = {
        id: 'quadratic-collinear',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: 0 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      const area = strategy.calculateArea(quadraticElement as any)
      expect(area).toBe(50) // From our mock
    })
  })
})
