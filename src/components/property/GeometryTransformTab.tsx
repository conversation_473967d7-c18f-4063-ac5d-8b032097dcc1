/**
 * Geometry Transform Tab Component
 *
 * A comprehensive property editor for geometric transformations
 * of selected elements. This component provides controls for position, dimensions,
 * and rotation.
 *
 * Features:
 * - Position controls (X, Y coordinates)
 * - Dimension controls (width, height) with unit conversion
 * - Rotation controls for supported elements
 * - Corner radius controls for rectangles and squares
 * - Bezier curve control point editing
 * - Multi-element selection support with mixed value handling
 * - Element-specific property visibility
 *
 * Supported Elements:
 * - Rectangles/Squares: Full dimension and corner radius controls
 * - Circles/Ellipses: Dimension controls
 * - Polygons: Basic geometry controls
 * - Text: Position and rotation controls only
 * - Images: Dimension controls with upload functionality
 * - Bezier curves: Control point editing
 *
 * Unit Conversion:
 * - Automatic conversion between pixels (internal) and mm/cm/m (display)
 * - Smart unit selection based on value magnitude
 * - Consistent precision handling with proper rounding
 *
 * @example
 * ```tsx
 * <GeometryTransformTab
 *   selectedElements={selectedShapes}
 *   getCommonValue={getPropertyValue}
 *   updateProperty={updateShapeProperty}
 *   pixelsPerMM={0.08}
 * />
 * ```
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'
import { useCallback } from 'react'

import { ElementType } from '@/types/core/elementDefinitions'
import { CostCalculationPanel } from './cost'
import { GeometryInfoDisplay, GeometrySelector } from './geometry'
import { GeometrySection, PropertyField, UnifiedGeometryLayout } from './shared'
import { supportsPosition, supportsRotation } from '@/utils/geometry'

/**
 * Props for the GeometryTransformTab component
 */
interface GeometryTransformTabProps {
  /** Array of currently selected elements */
  selectedElements: ShapeElement[]
  /** Function to get common property values across selected elements */
  getCommonValue: (propertyPath: string) => unknown
  /** Function to update property values for selected elements */
  updateProperty: (propertyPath: string, value: unknown) => void
  /** Function to update multiple properties for selected elements in a single batch */
  updateProperties?: (updates: Record<string, unknown>) => void
  /** Conversion factor from pixels to millimeters */
  pixelsPerMM: number
  // zoom: number; // Zoom might not be needed here if all values are in base canvas units
}

/** Default unit name for display */
const UNIT_NAME = 'mm'
/** Fallback conversion factor if pixelsPerMM prop is not provided - matches CANVAS_PIXELS_PER_MM */
const PIXELS_PER_MM_FALLBACK = 1.0

/**
 * 生成arc路径（与ShapeRenderer中的generateArcPath函数保持一致）
 */
function generateArcPath(center: { x: number, y: number }, radius: number, startAngle: number, endAngle: number, counterClockwise: boolean = false): string {
  // Convert angles to radians
  const startRad = (startAngle * Math.PI) / 180
  const endRad = (endAngle * Math.PI) / 180

  // Calculate start and end points relative to center
  const startX = center.x + radius * Math.cos(startRad)
  const startY = center.y + radius * Math.sin(startRad)
  const endX = center.x + radius * Math.cos(endRad)
  const endY = center.y + radius * Math.sin(endRad)

  // Calculate sweep angle with proper direction handling
  let sweepAngle = endAngle - startAngle

  // Handle angle wrapping and direction
  if (counterClockwise) {
    // For counterclockwise, ensure negative sweep
    if (sweepAngle > 0)
      sweepAngle -= 360
    sweepAngle = Math.abs(sweepAngle) // Make positive for flag calculation
  }
  else {
    // For clockwise, ensure positive sweep
    if (sweepAngle < 0)
      sweepAngle += 360
  }

  // Determine if this is a large arc (> 180 degrees)
  const largeArcFlag = Math.abs(sweepAngle) > 180 ? 1 : 0

  // Sweep direction flag (0 = counterclockwise, 1 = clockwise)
  const sweepFlag = counterClockwise ? 0 : 1

  // Generate SVG arc path
  return `M ${startX} ${startY} A ${radius} ${radius} 0 ${largeArcFlag} ${sweepFlag} ${endX} ${endY}`
}

/**
 * GeometryTransformTab component that provides comprehensive geometric property editing.
 *
 * This component renders a tabbed interface for editing position, dimensions, rotation,
 * and other geometric properties of selected elements. It handles unit conversion
 * and multi-element selection.
 *
 * @param props - The component props
 * @param props.selectedElements - Array of currently selected shape elements
 * @param props.getCommonValue - Function to get common property values across selected elements
 * @param props.updateProperty - Function to update a single property of selected elements
 * @param props.updateProperties - Function to update multiple properties at once
 * @param props.pixelsPerMM - Conversion ratio from pixels to millimeters
 * @returns The rendered geometry transform tab interface
 */
export function GeometryTransformTab({
  selectedElements,
  getCommonValue,
  updateProperty,
  updateProperties,
  pixelsPerMM,
}: GeometryTransformTabProps) {
  console.warn(`[GeometryTransformTab] 组件渲染开始`, {
    selectedElementsCount: selectedElements.length,
    selectedElementTypes: selectedElements.map(el => el.type),
    selectedElementIds: selectedElements.map(el => el.id),
    pixelsPerMM,
  })
  /**
   * Converts internal pixel values to display units (mm/cm/m).
   *
   * This function handles the conversion from the internal pixel-based coordinate
   * system to user-friendly display units. It includes proper error handling for
   * invalid values and maintains precision with appropriate decimal places.
   *
   * @param internalValue - The value in internal units (pixels)
   * @returns The converted value in display units or 'N/A' for invalid values
   */
  const toDisplayUnit = useCallback((internalValue: unknown): string | number => {
    if (typeof internalValue === 'number') {
      if (Number.isNaN(internalValue) || !Number.isFinite(internalValue)) {
        return 'N/A'
      }
      const effectivePixelsPerMM = pixelsPerMM || PIXELS_PER_MM_FALLBACK
      const displayVal = internalValue / effectivePixelsPerMM

      // 添加调试日志 - 包含更多信息
      // Debug information for unit conversion (removed console.log for production)

      if (Number.isNaN(displayVal) || !Number.isFinite(displayVal)) {
        return 'N/A'
      }
      // 保留两位小数，避免舍入问题
      return Number(displayVal.toFixed(2))
    }
    if (typeof internalValue === 'string') {
      return internalValue
    }
    return ''
  }, [pixelsPerMM])

  /**
   * Converts display unit values to internal pixel values.
   *
   * This function handles the conversion from user-entered display units back to
   * the internal pixel-based coordinate system used by the application.
   *
   * @param displayValue - The value in display units
   * @returns The converted value in internal units (pixels) or undefined for invalid values
   */
  const toInternalUnit = useCallback((displayValue: unknown): number | undefined => {
    const numericValue = typeof displayValue === 'string' ? Number.parseFloat(displayValue) : displayValue
    if (typeof numericValue === 'number' && !Number.isNaN(numericValue)) {
      const effectivePixelsPerMM = pixelsPerMM || PIXELS_PER_MM_FALLBACK
      const result = numericValue * effectivePixelsPerMM

      // 添加调试日志
      // Debug information for unit conversion (removed console.log for production)

      return (Number.isFinite(result) ? result : undefined) // Check result before returning
    }
    return undefined
  }, [pixelsPerMM])

  // Define element early to avoid use-before-define errors
  const element = selectedElements.length > 0 ? selectedElements[0] : null

  // Define store interface for type safety
  interface ShapesStoreInterface {
    getState: () => { shapes: ShapeElement[], selectedShapeIds: string[] }
    setState: (state: { shapes: ShapeElement[], selectedShapeIds: string[] }) => void
  }

  // Type guard functions for safe property access
  function isPoint(value: unknown): value is { x: number, y: number, z?: number } {
    return (
      typeof value === 'object'
      && value !== null
      && typeof (value as { x: unknown }).x === 'number'
      && typeof (value as { y: unknown }).y === 'number'
    )
  }

  function isShapesStore(value: unknown): value is ShapesStoreInterface {
    return (
      typeof value === 'object'
      && value !== null
      && typeof (value as { getState: unknown }).getState === 'function'
      && typeof (value as { setState: unknown }).setState === 'function'
    )
  }

  const getPointProperty = useCallback((properties: Record<string, unknown> | undefined, key: string, defaultValue: { x: number, y: number, z?: number }): { x: number, y: number, z?: number } => {
    const value = properties?.[key]
    return isPoint(value) ? value : defaultValue
  }, [])

  const getPosition = useCallback((element: ShapeElement): { x: number, y: number, z?: number } => {
    return isPoint(element.position) ? element.position : { x: 0, y: 0 }
  }, [])

  const handleInputChange = (
    propertyPath: string,
    value: string | number | boolean,
    isNumeric: boolean = true,
    isDimension: boolean = true, // Indicates if this property should be converted
  ) => {
    // Debug information for input changes (removed console.log for production)

    // 🔧 新增：特殊处理LINE元素的x1, y1, x2, y2路径
    if (element?.type === ElementType.LINE && ['x1', 'y1', 'x2', 'y2'].includes(propertyPath)) {
      const numericValue = Number.parseFloat(String(value))
      if (Number.isNaN(numericValue)) {
        console.warn(`[GeometryTransformTab] Invalid numeric value for ${propertyPath}: ${value}`)
        return
      }

      const pixelValue = toInternalUnit(numericValue) ?? 0
      // 🔧 修复：使用类型安全的属性访问
      const startPoint = getPointProperty(element.properties, 'start', { x: 0, y: 0, z: 0 })
      const endPoint = getPointProperty(element.properties, 'end', { x: 100, y: 0, z: 0 })
      const position = getPosition(element)

      const updatedStart = { ...startPoint }
      const updatedEnd = { ...endPoint }

      switch (propertyPath) {
        case 'x1': // Start X (absolute) -> convert to relative
          updatedStart.x = pixelValue - position.x
          // Debug: x1 calculation completed
          break
        case 'y1': // Start Y (absolute) -> convert to relative
          updatedStart.y = pixelValue - position.y
          // Debug: y1 calculation completed
          break
        case 'x2': // End X (absolute) -> convert to relative
          updatedEnd.x = pixelValue - position.x
          // Debug: x2 calculation completed
          break
        case 'y2': // End Y (absolute) -> convert to relative
          updatedEnd.y = pixelValue - position.y
          // Debug: y2 calculation completed
          break
      }

      // console.log(`[GeometryTransformTab] Updating LINE ${propertyPath}:`, { pixelValue, updatedStart, updatedEnd })

      // 🔧 修复：使用正确的路径来触发LINE元素的特殊处理逻辑
      if (updateProperties) {
        // 使用批量更新函数，使用 properties.start 和 properties.end 路径
        // 这将触发 updateNestedProperty 中的特殊处理逻辑（第539-544行）
        const lineUpdates = {
          'properties.start': updatedStart,
          'properties.end': updatedEnd,
        }
        // Debug: LINE batch update completed
        updateProperties(lineUpdates)
      }
      else {
        // 回退到单独更新（向后兼容）
        // Debug: LINE individual update completed
        updateProperty('properties.start', updatedStart)
        updateProperty('properties.end', updatedEnd)
      }

      // 添加延迟检查，确认更新是否生效
      setTimeout(() => {
        // Debug: Delayed check completed
      }, 200)

      return
    }

    // 🔧 新增：特殊处理POLYLINE元素的points路径
    if (element?.type === ElementType.POLYLINE) {
      const points = (element.properties?.points as Array<{ x: number, y: number }>) ?? []

      // 处理 points.N.x 和 points.N.y 路径
      const pointsMatch = propertyPath.match(/^points\.(\d+)\.(x|y)$/)
      if (pointsMatch) {
        const pointIndex = Number.parseInt(pointsMatch[1], 10)
        const coordinate = pointsMatch[2] as 'x' | 'y'

        const numericValue = Number.parseFloat(String(value))
        if (Number.isNaN(numericValue)) {
          console.warn(`[GeometryTransformTab] Invalid numeric value for ${propertyPath}: ${value}`)
          return
        }

        const pixelValue = toInternalUnit(numericValue) ?? 0
        const position = getPosition(element)

        if (pointIndex < points.length) {
          // 创建更新后的points数组
          const updatedPoints = [...points]
          const updatedPoint = { ...updatedPoints[pointIndex] }

          // 转换绝对坐标为相对坐标
          updatedPoint[coordinate] = pixelValue - position[coordinate]
          updatedPoints[pointIndex] = updatedPoint

          // Debug: POLYLINE update completed

          // 更新points属性
          updateProperty('properties.points', updatedPoints)
        }
        return
      }

      // 处理传统的 x1, y1, x2, y2... 路径（向后兼容）
      const traditionalMatch = propertyPath.match(/^([xy])(\d+)$/)
      if (traditionalMatch) {
        const coordinate = traditionalMatch[1] as 'x' | 'y'
        const pointIndex = Number.parseInt(traditionalMatch[2], 10) - 1 // x1 -> index 0

        const numericValue = Number.parseFloat(String(value))
        if (Number.isNaN(numericValue)) {
          console.warn(`[GeometryTransformTab] Invalid numeric value for ${propertyPath}: ${value}`)
          return
        }

        const pixelValue = toInternalUnit(numericValue) ?? 0
        const position = getPosition(element)

        if (pointIndex < points.length) {
          // 创建更新后的points数组
          const updatedPoints = [...points]
          const updatedPoint = { ...updatedPoints[pointIndex] }

          // 转换绝对坐标为相对坐标
          updatedPoint[coordinate] = pixelValue - position[coordinate]
          updatedPoints[pointIndex] = updatedPoint

          // Debug: POLYLINE traditional update completed

          // 更新points属性
          updateProperty('properties.points', updatedPoints)
        }
        return
      }
    }

    // 🔧 新增：特殊处理CUBIC元素的控制点路径
    if (element?.type === ElementType.CUBIC) {
      // 处理 properties.start.x, properties.start.y, properties.control1.x, etc.
      const cubicMatch = propertyPath.match(/^properties\.(start|control1|control2|end)\.(x|y)$/)
      if (cubicMatch) {
        const pointType = cubicMatch[1] as 'start' | 'control1' | 'control2' | 'end'
        const coordinate = cubicMatch[2] as 'x' | 'y'

        const numericValue = Number.parseFloat(String(value))
        if (Number.isNaN(numericValue)) {
          console.warn(`[GeometryTransformTab] Invalid numeric value for ${propertyPath}: ${value}`)
          return
        }

        const pixelValue = toInternalUnit(numericValue) ?? 0
        const position = getPosition(element)

        // 获取当前点数据
        const currentPoint = getPointProperty(element.properties, pointType, { x: 0, y: 0, z: 0 })

        // 创建更新后的点（转换绝对坐标为相对坐标）
        const updatedPoint = { ...currentPoint }
        updatedPoint[coordinate] = pixelValue - position[coordinate]

        // Debug: CUBIC update completed

        // 更新对应的控制点
        updateProperty(`properties.${pointType}`, updatedPoint)

        return
      }
    }

    // 🔧 新增：特殊处理QUADRATIC元素的控制点路径
    if (element?.type === ElementType.QUADRATIC) {
      // 处理 properties.start.x, properties.start.y, properties.control.x, properties.control.y, properties.end.x, properties.end.y
      const quadraticMatch = propertyPath.match(/^properties\.(start|control|end)\.(x|y)$/)
      if (quadraticMatch) {
        const pointType = quadraticMatch[1] as 'start' | 'control' | 'end'
        const coordinate = quadraticMatch[2] as 'x' | 'y'

        const numericValue = Number.parseFloat(String(value))
        if (Number.isNaN(numericValue)) {
          console.warn(`[GeometryTransformTab] Invalid numeric value for ${propertyPath}: ${value}`)
          return
        }

        const pixelValue = toInternalUnit(numericValue) ?? 0
        const position = getPosition(element)

        // 获取当前点数据
        const currentPoint = getPointProperty(element.properties, pointType, { x: 0, y: 0, z: 0 })

        // 创建更新后的点（转换绝对坐标为相对坐标）
        const updatedPoint = { ...currentPoint }
        updatedPoint[coordinate] = pixelValue - position[coordinate]

        // Debug: QUADRATIC update completed

        // 更新对应的控制点
        updateProperty(`properties.${pointType}`, updatedPoint)

        return
      }
    }

    // 🔧 新增：特殊处理ARC元素的属性路径
    if (element?.type === ElementType.ARC) {
      const arcPaths = ['cx', 'cy', 'radius', 'startAngle', 'endAngle']
      if (arcPaths.includes(propertyPath)) {
        const numericValue = Number.parseFloat(String(value))
        if (Number.isNaN(numericValue)) {
          console.warn(`[GeometryTransformTab] Invalid numeric value for ${propertyPath}: ${value}`)
          return
        }

        // Debug: ARC update started

        // 🔧 关键修复：同时更新pathData以确保正确渲染
        const updateArcPathData = (newRadius?: number, newStartAngle?: number, newEndAngle?: number) => {
          // 使用传入的新值或当前值
          const currentRadius = newRadius ?? (element.properties?.radius as number || 0)
          const currentStartAngle = newStartAngle ?? (element.properties?.startAngle as number || 0)
          const currentEndAngle = newEndAngle ?? (element.properties?.endAngle as number || 0)
          const currentCounterClockwise = element.properties?.counterClockwise as boolean || false

          // 生成新的pathData
          const newPathData = generateArcPath(
            { x: 0, y: 0 },
            currentRadius,
            currentStartAngle,
            currentEndAngle,
            currentCounterClockwise,
          )

          // Debug: ARC pathData update completed

          updateProperty('properties.pathData', newPathData)
        }

        switch (propertyPath) {
          case 'cx': // Center X
            {
              const pixelValue = toInternalUnit(numericValue) ?? 0
              const currentPosition = getPosition(element)
              const newPosition = {
                x: pixelValue,
                y: currentPosition.y,
                z: currentPosition.z ?? 0,
              }
              updateProperty('position', newPosition)
              // 位置变化不影响pathData，因为pathData使用相对坐标(0,0)
            }
            break
          case 'cy': // Center Y
            {
              const pixelValue = toInternalUnit(numericValue) ?? 0
              const currentPosition = getPosition(element)
              const newPosition = {
                x: currentPosition.x,
                y: pixelValue,
                z: currentPosition.z ?? 0,
              }
              updateProperty('position', newPosition)
              // 位置变化不影响pathData，因为pathData使用相对坐标(0,0)
            }
            break
          case 'radius':
            {
              const pixelValue = toInternalUnit(numericValue) ?? 0
              updateProperty('properties.radius', pixelValue)
              // 延迟更新pathData，确保radius属性先更新
              setTimeout(() => {
                updateArcPathData(pixelValue)
              }, 10)
            }
            break
          case 'startAngle':
            updateProperty('properties.startAngle', numericValue)
            // 延迟更新pathData，确保startAngle属性先更新
            setTimeout(() => {
              updateArcPathData(undefined, numericValue)
            }, 10)
            break
          case 'endAngle':
            updateProperty('properties.endAngle', numericValue)
            // 延迟更新pathData，确保endAngle属性先更新
            setTimeout(() => {
              updateArcPathData(undefined, undefined, numericValue)
            }, 10)
            break
        }

        return
      }
    }

    // 🔧 新增：特殊处理ELLIPSE元素的radiusX和radiusY路径
    if (element?.type === ElementType.ELLIPSE && (propertyPath === 'radiusX' || propertyPath === 'radiusY')) {
      const numericValue = Number.parseFloat(String(value))
      if (Number.isNaN(numericValue)) {
        console.warn(`[GeometryTransformTab] Invalid numeric value for ${propertyPath}: ${value}`)
        return
      }

      const pixelValue = toInternalUnit(numericValue) ?? 0

      console.warn(`[GeometryTransformTab] Updating ELLIPSE ${propertyPath}:`, {
        displayValue: numericValue,
        pixelValue,
        elementId: element.id,
      })

      // 🔧 使用直接store更新模式，类似于LINE组件
      const windowWithStore = window as Window & { __ZUSTAND_SHAPES_STORE__?: unknown }
      if (typeof window !== 'undefined' && windowWithStore.__ZUSTAND_SHAPES_STORE__ !== undefined) {
        const shapesStore = windowWithStore.__ZUSTAND_SHAPES_STORE__
        if (isShapesStore(shapesStore)) {
          const state = shapesStore.getState()
          const currentElement = state.shapes.find((s: ShapeElement) => s.id === element.id)

          if (currentElement !== undefined && currentElement !== null) {
            // 获取当前的半径值，确保类型安全
            const radiusXFromProps = currentElement.properties?.radiusX
            const radiusYFromProps = currentElement.properties?.radiusY
            const radiusXFromElement = (currentElement as unknown as Record<string, unknown>).radiusX
            const radiusYFromElement = (currentElement as unknown as Record<string, unknown>).radiusY

            const currentRadiusX = typeof radiusXFromProps === 'number'
              ? radiusXFromProps
              : typeof radiusXFromElement === 'number' ? radiusXFromElement : 120
            const currentRadiusY = typeof radiusYFromProps === 'number'
              ? radiusYFromProps
              : typeof radiusYFromElement === 'number' ? radiusYFromElement : 80

            // 创建更新后的元素，确保类型安全
            const updatedElement = { ...currentElement } as ShapeElement & {
              radiusX?: number
              radiusY?: number
              width?: number
              height?: number
            }

            if (propertyPath === 'radiusX') {
              updatedElement.properties = {
                ...(currentElement.properties ?? {}),
                radiusX: pixelValue,
                radiusY: currentRadiusY, // 🔧 明确保留radiusY
                width: pixelValue * 2,
                height: currentRadiusY * 2,
              }
              updatedElement.radiusX = pixelValue
              updatedElement.radiusY = currentRadiusY
              updatedElement.width = pixelValue * 2
              updatedElement.height = currentRadiusY * 2
            }
            else if (propertyPath === 'radiusY') {
              updatedElement.properties = {
                ...(currentElement.properties ?? {}),
                radiusX: currentRadiusX, // 🔧 明确保留radiusX
                radiusY: pixelValue,
                width: currentRadiusX * 2,
                height: pixelValue * 2,
              }
              updatedElement.radiusX = currentRadiusX
              updatedElement.radiusY = pixelValue
              updatedElement.width = currentRadiusX * 2
              updatedElement.height = pixelValue * 2
            }

            // 更新store中的shapes数组
            const newShapes = state.shapes.map((s: ShapeElement) =>
              s.id === element.id ? updatedElement : s,
            )

            // 保持选中状态
            shapesStore.setState({
              shapes: newShapes,
              selectedShapeIds: [element.id],
            })

            console.warn(`[GeometryTransformTab] ELLIPSE ${propertyPath} direct store update completed:`, {
              propertyPath,
              newValue: pixelValue,
              preservedRadiusX: propertyPath === 'radiusY' ? currentRadiusX : pixelValue,
              preservedRadiusY: propertyPath === 'radiusX' ? currentRadiusY : pixelValue,
            })
          }
        }
      }
      else {
        // 回退到updateProperty
        console.warn(`[GeometryTransformTab] ELLIPSE ${propertyPath} fallback to updateProperty`)
        updateProperty(propertyPath, pixelValue)
      }

      return
    }

    if (isNumeric) {
      if (value === '') {
        console.warn(`[GeometryTransformTab] Setting ${propertyPath} to undefined (empty value)`)
        updateProperty(propertyPath, undefined)
      }
      else {
        const numericValue = typeof value === 'string' ? Number.parseFloat(value) : value
        if (!Number.isNaN(numericValue)) {
          const internalValue = isDimension ? toInternalUnit(numericValue) : numericValue
          console.warn(`[GeometryTransformTab] Setting ${propertyPath} to ${internalValue} (converted from ${numericValue})`)
          updateProperty(propertyPath, internalValue)
        }
        else {
          console.warn(`[GeometryTransformTab] Invalid numeric value for ${propertyPath}: ${value}`)
        }
      }
    }
    else {
      console.warn(`[GeometryTransformTab] Setting ${propertyPath} to ${value} (non-numeric)`)
      updateProperty(propertyPath, value) // For non-numeric values
    }
  }

  const getValueForInput = useCallback((path: string) => {
    try {
      // 🔧 新增：特殊处理LINE元素的x1, y1, x2, y2路径
      if (element?.type === ElementType.LINE) {
        // 🔧 修复：直接使用顶层的 start 和 end，因为这些是绝对坐标
        // 如果顶层没有，则使用 position + properties 计算
        // 🔧 修复：使用类型安全的属性访问
        const startPoint = getPointProperty(element.properties, 'start', { x: 0, y: 0, z: 0 })
        const endPoint = getPointProperty(element.properties, 'end', { x: 100, y: 0, z: 0 })
        const position = getPosition(element)

        const startAbsolute = {
          x: position.x + startPoint.x,
          y: position.y + startPoint.y,
        }
        const endAbsolute = {
          x: position.x + endPoint.x,
          y: position.y + endPoint.y,
        }

        console.warn(`[getValueForInput] LINE ${path} detailed calculation:`, {
          elementId: element.id,
          position,
          startPoint,
          endPoint,
          startAbsolute,
          endAbsolute,
        })

        // console.log(`[getValueForInput] LINE ${path} calculation:`, { elementId: element.id, startAbsolute, endAbsolute })

        switch (path) {
          case 'x1': {
            const displayValue = toDisplayUnit(startAbsolute.x)
            return typeof displayValue === 'number' ? displayValue.toFixed(2) : '0.00'
          }
          case 'y1': {
            const displayValue = toDisplayUnit(startAbsolute.y)
            return typeof displayValue === 'number' ? displayValue.toFixed(2) : '0.00'
          }
          case 'x2': {
            const displayValue = toDisplayUnit(endAbsolute.x)
            return typeof displayValue === 'number' ? displayValue.toFixed(2) : '0.00'
          }
          case 'y2': {
            const displayValue = toDisplayUnit(endAbsolute.y)
            return typeof displayValue === 'number' ? displayValue.toFixed(2) : '0.00'
          }
        }
      }

      // 🔧 新增：特殊处理POLYLINE元素的points路径
      if (element?.type === ElementType.POLYLINE) {
        const points = (element.properties?.points as Array<{ x: number, y: number }>) ?? []

        // 处理 points.N.x 和 points.N.y 路径
        const pointsMatch = path.match(/^points\.(\d+)\.(x|y)$/)
        if (pointsMatch) {
          const pointIndex = Number.parseInt(pointsMatch[1], 10)
          const coordinate = pointsMatch[2] as 'x' | 'y'

          if (pointIndex < points.length) {
            const point = points[pointIndex]
            const absoluteValue = (element.position?.[coordinate] ?? 0) + (point[coordinate] ?? 0)

            console.warn(`[getValueForInput] POLYLINE ${path} calculation:`, {
              elementId: element.id,
              pointIndex,
              coordinate,
              position: element.position,
              relativeValue: point[coordinate],
              absoluteValue,
            })

            const displayValue = toDisplayUnit(absoluteValue)
            return typeof displayValue === 'number' ? displayValue.toFixed(2) : '0.00'
          }
        }

        // 处理传统的 x1, y1, x2, y2... 路径（向后兼容）
        const traditionalMatch = path.match(/^([xy])(\d+)$/)
        if (traditionalMatch) {
          const coordinate = traditionalMatch[1] as 'x' | 'y'
          const pointIndex = Number.parseInt(traditionalMatch[2], 10) - 1 // x1 -> index 0

          if (pointIndex < points.length) {
            const point = points[pointIndex]
            const absoluteValue = (element.position?.[coordinate] ?? 0) + (point[coordinate] ?? 0)

            console.warn(`[getValueForInput] POLYLINE traditional ${path} calculation:`, {
              elementId: element.id,
              pointIndex,
              coordinate,
              absoluteValue,
            })

            const displayValue = toDisplayUnit(absoluteValue)
            return typeof displayValue === 'number' ? displayValue.toFixed(2) : '0.00'
          }
        }
      }

      // 🔧 新增：特殊处理CUBIC元素的控制点路径
      if (element?.type === ElementType.CUBIC) {
        // 处理 properties.start.x, properties.start.y, properties.control1.x, etc.
        const cubicMatch = path.match(/^properties\.(start|control1|control2|end)\.(x|y)$/)
        if (cubicMatch) {
          const pointType = cubicMatch[1] as 'start' | 'control1' | 'control2' | 'end'
          const coordinate = cubicMatch[2] as 'x' | 'y'

          // 获取当前点数据
          const currentPoint = getPointProperty(element.properties, pointType, { x: 0, y: 0, z: 0 })
          const position = getPosition(element)

          // 计算绝对坐标（position + relative point）
          const absoluteValue = position[coordinate] + currentPoint[coordinate]

          console.warn(`[getValueForInput] CUBIC ${path} calculation:`, {
            elementId: element.id,
            pointType,
            coordinate,
            position,
            currentPoint,
            absoluteValue,
          })

          const displayValue = toDisplayUnit(absoluteValue)
          return typeof displayValue === 'number' ? displayValue.toFixed(2) : '0.00'
        }
      }

      // 🔧 新增：特殊处理QUADRATIC元素的控制点路径
      if (element?.type === ElementType.QUADRATIC) {
        // 处理 properties.start.x, properties.start.y, properties.control.x, properties.control.y, properties.end.x, properties.end.y
        const quadraticMatch = path.match(/^properties\.(start|control|end)\.(x|y)$/)
        if (quadraticMatch) {
          const pointType = quadraticMatch[1] as 'start' | 'control' | 'end'
          const coordinate = quadraticMatch[2] as 'x' | 'y'

          // 获取当前点数据
          const currentPoint = getPointProperty(element.properties, pointType, { x: 0, y: 0, z: 0 })
          const position = getPosition(element)

          // 计算绝对坐标（position + relative point）
          const absoluteValue = position[coordinate] + currentPoint[coordinate]

          console.warn(`[getValueForInput] QUADRATIC ${path} calculation:`, {
            elementId: element.id,
            pointType,
            coordinate,
            position,
            currentPoint,
            absoluteValue,
          })

          const displayValue = toDisplayUnit(absoluteValue)
          return typeof displayValue === 'number' ? displayValue.toFixed(2) : '0.00'
        }
      }

      // 🔧 新增：特殊处理ARC元素的属性路径
      if (element?.type === ElementType.ARC) {
        const radius = element.properties?.radius as number | undefined
        const startAngle = element.properties?.startAngle as number | undefined
        const endAngle = element.properties?.endAngle as number | undefined

        console.warn(`[getValueForInput] ARC ${path} calculation:`, {
          elementId: element.id,
          position: element.position,
          radius,
          startAngle,
          endAngle,
        })

        switch (path) {
          case 'cx': { // Center X
            const displayValue = toDisplayUnit(element.position?.x || 0)
            return typeof displayValue === 'number' ? displayValue.toFixed(2) : '0.00'
          }
          case 'cy': { // Center Y
            const displayValue = toDisplayUnit(element.position?.y || 0)
            return typeof displayValue === 'number' ? displayValue.toFixed(2) : '0.00'
          }
          case 'radius': {
            const displayValue = toDisplayUnit(radius ?? 0)
            return typeof displayValue === 'number' ? displayValue.toFixed(2) : '0.00'
          }
          case 'startAngle':
            return String((startAngle ?? 0).toFixed(1))
          case 'endAngle':
            return String((endAngle ?? 0).toFixed(1))
        }
      }

      const val = getCommonValue(path)

      // 🔧 修复：改进值获取逻辑，优先显示实际值
      let actualValue = val

      // 如果是mixed，对于单个元素选择，直接从element获取值
      if (val === 'mixed' && selectedElements.length === 1 && element) {
        // 处理嵌套路径，如 'position.x'
        if (path.includes('.')) {
          const pathParts = path.split('.')
          let currentValue: unknown = element
          for (const part of pathParts) {
            if (currentValue !== null && typeof currentValue === 'object') {
              currentValue = (currentValue as Record<string, unknown>)[part]
            }
            else {
              currentValue = undefined
              break
            }
            if (currentValue === undefined || currentValue === null)
              break
          }
          actualValue = currentValue
        }
        else {
          // 简单路径，先检查properties，再检查顶层
          actualValue = element.properties?.[path] ?? (element as unknown as Record<string, unknown>)[path]
        }
      }

      // 如果getCommonValue返回null/undefined，尝试直接从element获取值
      if ((val === null || val === undefined) && element) {
        // 处理嵌套路径，如 'position.x'
        if (path.includes('.')) {
          const pathParts = path.split('.')
          let currentValue: unknown = element
          for (const part of pathParts) {
            if (currentValue !== null && typeof currentValue === 'object') {
              currentValue = (currentValue as Record<string, unknown>)[part]
            }
            else {
              currentValue = undefined
              break
            }
            if (currentValue === undefined || currentValue === null)
              break
          }
          actualValue = currentValue
        }
        else {
          // 简单路径，先检查properties，再检查顶层
          actualValue = element.properties?.[path] ?? (element as unknown as Record<string, unknown>)[path]
        }
      }

      // 如果仍然没有值，返回空字符串
      if (actualValue === null || actualValue === undefined) {
        return ''
      }

      // 如果是mixed且有多个元素，返回空字符串
      if (actualValue === 'mixed' && selectedElements.length > 1) {
        return ''
      }

      // 特殊处理LINE元素的rotation - 显示计算出的角度
      if (path.includes('rotation') && element?.type === ElementType.LINE) {
        // 🔧 修复：始终使用 properties 中的相对坐标，确保一致性
        const startPoint = getPointProperty(element.properties, 'start', { x: 0, y: 0, z: 0 })
        const endPoint = getPointProperty(element.properties, 'end', { x: 100, y: 0, z: 0 })

        const angle = Math.atan2(endPoint.y - startPoint.y, endPoint.x - startPoint.x) * 180 / Math.PI
        return String(angle.toFixed(1))
      }

      // 对于旋转属性，直接返回数值
      if (path.includes('rotation')) {
        return String(actualValue)
      }

      // 🔧 修复：对于字体相关属性，直接返回字符串值，不进行单位转换
      if (path.includes('fontFamily') || path.includes('fontStyle') || path.includes('fontWeight')
        || path.includes('textAlign') || path.includes('text') || path.includes('content')) {
        return String(actualValue ?? '')
      }

      // 对于尺寸属性，进行单位转换
      const displayValue = toDisplayUnit(actualValue)
      return typeof displayValue === 'number' ? displayValue.toFixed(2) : String(displayValue)
    }
    catch (error) {
      console.warn(`[GeometryTransformTab] Error getting value for path "${path}":`, error)
      return ''
    }
  }, [getCommonValue, toDisplayUnit, element, getPointProperty, getPosition, selectedElements.length])

  const getPlaceholderForInput = useCallback((path: string, exampleValue?: number | string) => {
    const val = getCommonValue(path)

    // 对于多个元素且值不同的情况，显示"Mixed"
    if (val === 'mixed' && selectedElements.length > 1) {
      return 'Mixed'
    }

    // 对于单个元素或值相同的情况，如果没有值则显示默认placeholder
    if (val === null || val === undefined || val === 'mixed') {
      // 根据路径提供合适的默认placeholder
      if (path === 'width' || path === 'height') {
        return '0'
      }
      if (path.includes('position.')) {
        return '0'
      }
      if (path === 'cornerRadius') {
        return '0'
      }
      if (path === 'rotation') {
        return '0'
      }

      // 如果有提供示例值，使用示例值
      if (exampleValue !== undefined && exampleValue !== null) {
        return exampleValue.toString()
      }

      return '0' // 默认placeholder
    }

    // 如果有实际值，不显示placeholder（让输入框显示实际值）
    return undefined
  }, [getCommonValue, selectedElements.length])

  // 不再需要单位选择状态

  // Early return only if no elements are selected
  if (selectedElements.length === 0) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        <p>No elements selected</p>
        <p className="text-xs mt-2">Please select one or more elements to edit their geometry properties.</p>
      </div>
    )
  }

  return (
    <UnifiedGeometryLayout elementType={element?.type as ElementType}>

      {/* Use new GeometrySelector for all element types */}
      {(element?.type === ElementType.RECTANGLE
        || element?.type === ElementType.SQUARE
        || element?.type === ElementType.CIRCLE
        || element?.type === ElementType.ELLIPSE
        || element?.type === ElementType.POLYGON
        || element?.type === ElementType.TRIANGLE
        || element?.type === ElementType.QUADRILATERAL
        || element?.type === ElementType.PENTAGON
        || element?.type === ElementType.HEXAGON
        || element?.type === ElementType.HEPTAGON
        || element?.type === ElementType.OCTAGON
        || element?.type === ElementType.NONAGON
        || element?.type === ElementType.DECAGON
        || element?.type === ElementType.TEXT
        || element?.type === ElementType.TEXT_LABEL
        || element?.type === ElementType.IMAGE
        || element?.type === ElementType.LINE
        || element?.type === ElementType.ARC
        || element?.type === ElementType.POLYLINE
        || element?.type === ElementType.QUADRATIC
        || element?.type === ElementType.CUBIC) && (
        <GeometrySelector
          element={element}
          getValueForInput={getValueForInput}
          getPlaceholderForInput={getPlaceholderForInput}
          handleInputChange={handleInputChange}
          unitName={UNIT_NAME}
          toDisplayUnit={(value: number) => Number(toDisplayUnit(value))}
          toInternalUnit={toInternalUnit}
          updateProperty={updateProperty}
        />
      )}

      {/* Legacy controls are no longer needed - all element types now use GeometrySelector */}
      {!(element?.type === ElementType.RECTANGLE
        || element?.type === ElementType.SQUARE
        || element?.type === ElementType.CIRCLE
        || element?.type === ElementType.ELLIPSE
        || element?.type === ElementType.POLYGON
        || element?.type === ElementType.TRIANGLE
        || element?.type === ElementType.QUADRILATERAL
        || element?.type === ElementType.PENTAGON
        || element?.type === ElementType.HEXAGON
        || element?.type === ElementType.HEPTAGON
        || element?.type === ElementType.OCTAGON
        || element?.type === ElementType.NONAGON
        || element?.type === ElementType.DECAGON
        || element?.type === ElementType.TEXT
        || element?.type === ElementType.TEXT_LABEL
        || element?.type === ElementType.IMAGE
        || element?.type === ElementType.LINE
        || element?.type === ElementType.ARC
        || element?.type === ElementType.POLYLINE
        || element?.type === ElementType.QUADRATIC
        || element?.type === ElementType.CUBIC) && (
        <>
          {/* Position Controls - for elements that support standard position */}
          {supportsPosition(element?.type as ElementType) && (
            <GeometrySection type="position" columns={2}>
              <PropertyField
                label={`Position X (${UNIT_NAME})`}
                type="number"
                value={getValueForInput('position.x')}
                onChange={value => handleInputChange('position.x', value)}
                placeholder={getPlaceholderForInput('position.x')}
              />
              <PropertyField
                label={`Position Y (${UNIT_NAME})`}
                type="number"
                value={getValueForInput('position.y')}
                onChange={value => handleInputChange('position.y', value)}
                placeholder={getPlaceholderForInput('position.y')}
              />
            </GeometrySection>
          )}

          {/* Basic Dimension Controls - removed ELLIPSE as it's now handled by GeometrySelector */}

          {/* Basic Rotation Control */}
          {supportsRotation(element?.type as ElementType) && (
            <GeometrySection type="transform" columns={1}>
              <PropertyField
                label="Rotation (°)"
                type="number"
                value={getValueForInput('rotation')}
                onChange={value => handleInputChange('rotation', value, true, false)}
                placeholder="0"
              />
            </GeometrySection>
          )}
        </>
      )}

      {/* LINE元素现在通过GeometrySelector使用LineGeometry组件处理 */}

      {/* Legacy Dimensions Section - Removed: All elements now use GeometrySelector */}
      {/* Legacy Element Properties Section - Removed: Corner Radius now handled by GeometrySelector */}

      {/* Legacy Transform Section - Removed: Rotation now handled by GeometrySelector */}

      {/* Geometry Information Display */}
      {element && (
        <GeometryInfoDisplay
          element={element}
          pixelsPerMM={pixelsPerMM}
          getCommonValue={getCommonValue}
        />
      )}

      {/* Cost Calculation Panel */}
      {element && (
        <CostCalculationPanel
          element={element}
          getCommonValue={getCommonValue}
          updateProperty={updateProperty}
        />
      )}

    </UnifiedGeometryLayout>
  )
}
export default GeometryTransformTab
