import { describe, expect, it } from 'vitest'

import { AppEventType } from '@/types/services/events/eventTypes'

describe('application Event Type Enum', () => {
  it('appEventType enum should contain correct string values for various categories', () => {
    // System
    expect(AppEventType.EVENT_REGISTER).toBe('event.register')
    // Shape
    expect(AppEventType.SHAPE_CREATE_REQUEST).toBe('shape.create.request')
    expect(AppEventType.SHAPE_SELECTED).toBe('shape.selected')
    // UI
    expect(AppEventType.TOOL_CHANGED).toBe('tool.changed')
    expect(AppEventType.VIEW_ZOOM_IN).toBe('view.zoom.in')
    // History
    expect(AppEventType.HISTORY_UNDO).toBe('history.undo')
    // Render
    expect(AppEventType.RENDER_COMPLETE).toBe('render.complete')
    // Layer
    expect(AppEventType.LAYER_CREATE).toBe('layer.create')
    // File
    expect(AppEventType.FILE_IMPORTED).toBe('file.imported')
    // Template
    expect(AppEventType.TEMPLATE_APPLY).toBe('template.apply')
    // Compute
    expect(AppEventType.COMPUTE_REQUEST).toBe('compute.request')
    expect(AppEventType.VALIDATE_SHAPE).toBe('validate.shape')
    // State
    expect(AppEventType.STATE_UPDATED).toBe('state.updated')
    expect(AppEventType.DATA_LOAD_COMPLETE).toBe('data.load.complete')
    // Grid
    expect(AppEventType.GRID_ENABLED).toBe('grid.enabled')
    // Sidebar
    expect(AppEventType.SIDEBAR_LEFT_TOGGLE).toBe('sidebar.left.toggle')
    // Keyboard
    expect(AppEventType.KEY_PRESSED).toBe('keyboard.key.pressed')
  })

  it('appEventType should be usable as a type', () => {
    const myEvent: AppEventType = AppEventType.SHAPE_DELETE_COMPLETE
    expect(myEvent).toBe('shape.delete.complete')

    // TS should prevent assigning invalid strings
    // let invalidEvent: AppEventType = 'invalid-event';
  })

  it('should have unique values for all enum members', () => {
    const values = Object.values(AppEventType)
    const uniqueValues = new Set(values)
    expect(values.length).toBe(uniqueValues.size)
  })
})
