/**
 * Application-Wide Constants and Enumerations
 *
 * @remarks
 * This file defines constants and enumerations that are used throughout the application.
 * It serves as a central repository for default values, settings, identifiers,
 * and other constant data to ensure consistency and maintainability.
 *
 * Key groups of constants include:
 * - Default timing values (debounce, throttle, animation).
 * - Z-index layering for UI elements.
 * - Keyboard event key codes.
 * - CSS cursor styles.
 * - Common string literals (application name, version).
 *
 * @module types/constants
 */

/**
 * Default debounce time for events, in milliseconds.
 * @defaultValue 50
 */
export const DEFAULT_DEBOUNCE_TIME = 50

/**
 * Default throttle time for events, in milliseconds.
 * @defaultValue 100
 */
export const DEFAULT_THROTTLE_TIME = 100

/**
 * Default duration for animations, in milliseconds.
 * @defaultValue 300
 */
export const DEFAULT_ANIMATION_DURATION = 300

/**
 * Defines standard z-index values for managing the stacking order of UI elements.
 */
export enum ZIndex {
  /** The base layer for the main canvas where shapes are drawn. */
  CANVAS = 10,
  /** The layer for displaying the grid lines on the canvas. */
  GRID = 20,
  /** The layer where interactive elements (shapes, paths) are rendered. */
  ELEMENTS = 30,
  /** The layer for selection highlights and manipulation handles. */
  SELECTION = 40,
  /** The layer for UI controls, toolbars, and other interface components. */
  CONTROLS = 50,
  /** The highest layer, typically used for modal dialogs and overlays. */
  MODAL = 100,
}

/**
 * Defines common key codes for keyboard event handling.
 * Values correspond to `event.key` property.
 */
export enum KeyCode {
  ENTER = 'Enter',
  ESC = 'Escape',
  DELETE = 'Delete',
  BACKSPACE = 'Backspace',
  SPACE = ' ',
  TAB = 'Tab',

  // Arrow keys
  UP = 'ArrowUp',
  DOWN = 'ArrowDown',
  LEFT = 'ArrowLeft',
  RIGHT = 'ArrowRight',

  // Modifier keys
  SHIFT = 'Shift',
  CTRL = 'Control',
  ALT = 'Alt',
  META = 'Meta',
}

/**
 * Defines standard CSS cursor styles for different interaction modes within the application.
 */
export enum CursorStyle {
  DEFAULT = 'default',
  POINTER = 'pointer',
  MOVE = 'move',
  GRAB = 'grab',
  GRABBING = 'grabbing',
  CROSSHAIR = 'crosshair',
  NOT_ALLOWED = 'not-allowed',
  RESIZE_NS = 'ns-resize',
  RESIZE_EW = 'ew-resize',
  RESIZE_NESW = 'nesw-resize',
  RESIZE_NWSE = 'nwse-resize',
}

/**
 * Cursor type for resize handles
 * NESW = North-East-South-West (compass directions for cursor position)
 */
export enum ResizeCursor {
  /** Northwest resize handle */
  NW = 'nw-resize',
  /** Northeast resize handle */
  NE = 'ne-resize',
  /** Southwest resize handle */
  SW = 'sw-resize',
  /** Southeast resize handle */
  SE = 'se-resize',
  /** North resize handle */
  N = 'n-resize',
  /** East resize handle */
  E = 'e-resize',
  /** South resize handle */
  S = 's-resize',
  /** West resize handle */
  W = 'w-resize',
}

/**
 * Common string constants
 */
export const STRINGS = {
  APP_NAME: 'RenoPilot.JS.Shapes2',
  VERSION: '1.0.0',
  COPYRIGHT: '© 2025 RenoPilot',
  AUTHOR: 'RenoPilot Team',
}
