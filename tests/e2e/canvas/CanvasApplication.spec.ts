import { test, expect } from '@playwright/test';

/**
 * End-to-end test suite for the canvas application.
 * These tests verify that the canvas application works correctly as a whole.
 */
test.describe('Canvas Application', () => {
  /**
   * Setup for each test.
   * Navigates to the canvas application page.
   */
  test.beforeEach(async ({ page }) => {
    // Navigate to the canvas application page
    await page.goto('/');
    
    // Wait for the canvas to be loaded
    await page.waitForSelector('#canvas-container svg');
  });

  /**
   * Tests creating a rectangle.
   * Verifies that a rectangle can be created and appears on the canvas.
   */
  test('Create rectangle', async ({ page }) => {
    // Click the rectangle tool
    await page.click('#rectangle-tool');
    
    // Draw a rectangle on the canvas
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Verify that a rectangle was created
    const rectangle = await page.locator('rect');
    await expect(rectangle).toBeVisible();
    
    // Verify rectangle attributes
    const x = await rectangle.getAttribute('x');
    const y = await rectangle.getAttribute('y');
    const width = await rectangle.getAttribute('width');
    const height = await rectangle.getAttribute('height');
    
    expect(parseInt(x)).toBeLessThanOrEqual(100);
    expect(parseInt(y)).toBeLessThanOrEqual(100);
    expect(parseInt(width)).toBeGreaterThanOrEqual(200);
    expect(parseInt(height)).toBeGreaterThanOrEqual(150);
  });

  /**
   * Tests creating an ellipse.
   * Verifies that an ellipse can be created and appears on the canvas.
   */
  test('Create ellipse', async ({ page }) => {
    // Click the ellipse tool
    await page.click('#ellipse-tool');
    
    // Draw an ellipse on the canvas
    await page.mouse.move(200, 200);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Verify that an ellipse was created
    const ellipse = await page.locator('ellipse');
    await expect(ellipse).toBeVisible();
    
    // Verify ellipse attributes
    const cx = await ellipse.getAttribute('cx');
    const cy = await ellipse.getAttribute('cy');
    const rx = await ellipse.getAttribute('rx');
    const ry = await ellipse.getAttribute('ry');
    
    expect(parseInt(cx)).toBeGreaterThan(0);
    expect(parseInt(cy)).toBeGreaterThan(0);
    expect(parseInt(rx)).toBeGreaterThan(0);
    expect(parseInt(ry)).toBeGreaterThan(0);
  });

  /**
   * Tests creating a line.
   * Verifies that a line can be created and appears on the canvas.
   */
  test('Create line', async ({ page }) => {
    // Click the line tool
    await page.click('#line-tool');
    
    // Draw a line on the canvas
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 300);
    await page.mouse.up();
    
    // Verify that a line was created
    const line = await page.locator('line');
    await expect(line).toBeVisible();
    
    // Verify line attributes
    const x1 = await line.getAttribute('x1');
    const y1 = await line.getAttribute('y1');
    const x2 = await line.getAttribute('x2');
    const y2 = await line.getAttribute('y2');
    
    expect(parseInt(x1)).toBeLessThanOrEqual(100);
    expect(parseInt(y1)).toBeLessThanOrEqual(100);
    expect(parseInt(x2)).toBeGreaterThanOrEqual(300);
    expect(parseInt(y2)).toBeGreaterThanOrEqual(300);
  });

  /**
   * Tests selecting and moving a shape.
   * Verifies that a shape can be selected and moved.
   */
  test('Select and move shape', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Switch to the select tool
    await page.click('#select-tool');
    
    // Select the rectangle
    await page.mouse.move(200, 175);
    await page.mouse.down();
    await page.mouse.up();
    
    // Verify that the rectangle is selected
    const selectedRectangle = await page.locator('rect.selected');
    await expect(selectedRectangle).toBeVisible();
    
    // Get the initial position
    const initialX = await selectedRectangle.getAttribute('x');
    const initialY = await selectedRectangle.getAttribute('y');
    
    // Move the rectangle
    await page.mouse.move(200, 175);
    await page.mouse.down();
    await page.mouse.move(300, 275);
    await page.mouse.up();
    
    // Verify that the rectangle was moved
    const finalX = await selectedRectangle.getAttribute('x');
    const finalY = await selectedRectangle.getAttribute('y');
    
    expect(parseInt(finalX)).toBeGreaterThan(parseInt(initialX));
    expect(parseInt(finalY)).toBeGreaterThan(parseInt(initialY));
  });

  /**
   * Tests resizing a shape.
   * Verifies that a shape can be resized.
   */
  test('Resize shape', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Switch to the select tool
    await page.click('#select-tool');
    
    // Select the rectangle
    await page.mouse.move(200, 175);
    await page.mouse.down();
    await page.mouse.up();
    
    // Verify that the rectangle is selected
    const selectedRectangle = await page.locator('rect.selected');
    await expect(selectedRectangle).toBeVisible();
    
    // Get the initial size
    const initialWidth = await selectedRectangle.getAttribute('width');
    const initialHeight = await selectedRectangle.getAttribute('height');
    
    // Resize the rectangle using the bottom-right handle
    await page.mouse.move(300, 250); // Move to the bottom-right corner
    await page.mouse.down();
    await page.mouse.move(400, 350);
    await page.mouse.up();
    
    // Verify that the rectangle was resized
    const finalWidth = await selectedRectangle.getAttribute('width');
    const finalHeight = await selectedRectangle.getAttribute('height');
    
    expect(parseInt(finalWidth)).toBeGreaterThan(parseInt(initialWidth));
    expect(parseInt(finalHeight)).toBeGreaterThan(parseInt(initialHeight));
  });

  /**
   * Tests changing a shape's style.
   * Verifies that a shape's style can be changed.
   */
  test('Change shape style', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Switch to the select tool
    await page.click('#select-tool');
    
    // Select the rectangle
    await page.mouse.move(200, 175);
    await page.mouse.down();
    await page.mouse.up();
    
    // Verify that the rectangle is selected
    const selectedRectangle = await page.locator('rect.selected');
    await expect(selectedRectangle).toBeVisible();
    
    // Get the initial style
    const initialFill = await selectedRectangle.getAttribute('fill');
    const initialStroke = await selectedRectangle.getAttribute('stroke');
    
    // Change the fill color
    await page.click('#fill-color-picker');
    await page.fill('#fill-color-input', '#0000ff');
    await page.press('#fill-color-input', 'Enter');
    
    // Change the stroke color
    await page.click('#stroke-color-picker');
    await page.fill('#stroke-color-input', '#00ff00');
    await page.press('#stroke-color-input', 'Enter');
    
    // Change the stroke width
    await page.fill('#stroke-width-input', '5');
    await page.press('#stroke-width-input', 'Enter');
    
    // Verify that the rectangle's style was changed
    const finalFill = await selectedRectangle.getAttribute('fill');
    const finalStroke = await selectedRectangle.getAttribute('stroke');
    const finalStrokeWidth = await selectedRectangle.getAttribute('stroke-width');
    
    expect(finalFill).not.toBe(initialFill);
    expect(finalStroke).not.toBe(initialStroke);
    expect(finalStrokeWidth).toBe('5');
  });

  /**
   * Tests deleting a shape.
   * Verifies that a shape can be deleted.
   */
  test('Delete shape', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Verify that a rectangle was created
    const rectangle = await page.locator('rect');
    await expect(rectangle).toBeVisible();
    
    // Switch to the select tool
    await page.click('#select-tool');
    
    // Select the rectangle
    await page.mouse.move(200, 175);
    await page.mouse.down();
    await page.mouse.up();
    
    // Delete the rectangle
    await page.keyboard.press('Delete');
    
    // Verify that the rectangle was deleted
    await expect(page.locator('rect')).toHaveCount(0);
  });

  /**
   * Tests creating a group.
   * Verifies that shapes can be grouped.
   */
  test('Create group', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Create an ellipse
    await page.click('#ellipse-tool');
    await page.mouse.move(400, 300);
    await page.mouse.down();
    await page.mouse.move(500, 350);
    await page.mouse.up();
    
    // Switch to the select tool
    await page.click('#select-tool');
    
    // Select both shapes using a selection rectangle
    await page.mouse.move(50, 50);
    await page.mouse.down();
    await page.mouse.move(550, 400);
    await page.mouse.up();
    
    // Group the shapes
    await page.keyboard.press('Control+G');
    
    // Verify that a group was created
    const group = await page.locator('g.group');
    await expect(group).toBeVisible();
    
    // Verify that the shapes are in the group
    const rectInGroup = await page.locator('g.group rect');
    const ellipseInGroup = await page.locator('g.group ellipse');
    
    await expect(rectInGroup).toBeVisible();
    await expect(ellipseInGroup).toBeVisible();
  });

  /**
   * Tests ungrouping shapes.
   * Verifies that shapes can be ungrouped.
   */
  test('Ungroup shapes', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Create an ellipse
    await page.click('#ellipse-tool');
    await page.mouse.move(400, 300);
    await page.mouse.down();
    await page.mouse.move(500, 350);
    await page.mouse.up();
    
    // Switch to the select tool
    await page.click('#select-tool');
    
    // Select both shapes using a selection rectangle
    await page.mouse.move(50, 50);
    await page.mouse.down();
    await page.mouse.move(550, 400);
    await page.mouse.up();
    
    // Group the shapes
    await page.keyboard.press('Control+G');
    
    // Verify that a group was created
    const group = await page.locator('g.group');
    await expect(group).toBeVisible();
    
    // Select the group
    await page.mouse.move(300, 200);
    await page.mouse.down();
    await page.mouse.up();
    
    // Ungroup the shapes
    await page.keyboard.press('Control+Shift+G');
    
    // Verify that the group was removed
    await expect(page.locator('g.group')).toHaveCount(0);
    
    // Verify that the shapes still exist
    await expect(page.locator('rect')).toBeVisible();
    await expect(page.locator('ellipse')).toBeVisible();
  });

  /**
   * Tests clearing the canvas.
   * Verifies that the canvas can be cleared.
   */
  test('Clear canvas', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Create an ellipse
    await page.click('#ellipse-tool');
    await page.mouse.move(400, 300);
    await page.mouse.down();
    await page.mouse.move(500, 350);
    await page.mouse.up();
    
    // Verify that shapes were created
    await expect(page.locator('rect')).toBeVisible();
    await expect(page.locator('ellipse')).toBeVisible();
    
    // Clear the canvas
    await page.click('#clear-canvas-button');
    
    // Confirm the clear action in the dialog
    await page.click('#confirm-clear-button');
    
    // Verify that the canvas was cleared
    await expect(page.locator('rect')).toHaveCount(0);
    await expect(page.locator('ellipse')).toHaveCount(0);
  });

  /**
   * Tests exporting the canvas.
   * Verifies that the canvas can be exported.
   */
  test('Export canvas', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Export the canvas as SVG
    const downloadPromise = page.waitForEvent('download');
    await page.click('#export-svg-button');
    const download = await downloadPromise;
    
    // Verify that a file was downloaded
    expect(download.suggestedFilename()).toContain('.svg');
  });

  /**
   * Tests importing a canvas.
   * Verifies that a canvas can be imported.
   */
  test('Import canvas', async ({ page }) => {
    // Create a file input for importing
    await page.setInputFiles('#import-file-input', 'path/to/test-canvas.svg');
    
    // Click the import button
    await page.click('#import-button');
    
    // Verify that shapes were imported
    await expect(page.locator('svg > *')).toHaveCount.greaterThan(0);
  });

  /**
   * Tests undo and redo functionality.
   * Verifies that actions can be undone and redone.
   */
  test('Undo and redo', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Verify that a rectangle was created
    await expect(page.locator('rect')).toHaveCount(1);
    
    // Undo the creation
    await page.keyboard.press('Control+Z');
    
    // Verify that the rectangle was removed
    await expect(page.locator('rect')).toHaveCount(0);
    
    // Redo the creation
    await page.keyboard.press('Control+Y');
    
    // Verify that the rectangle was restored
    await expect(page.locator('rect')).toHaveCount(1);
  });

  /**
   * Tests keyboard shortcuts.
   * Verifies that keyboard shortcuts work correctly.
   */
  test('Keyboard shortcuts', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Switch to the select tool using the keyboard shortcut
    await page.keyboard.press('v');
    
    // Select the rectangle
    await page.mouse.move(200, 175);
    await page.mouse.down();
    await page.mouse.up();
    
    // Verify that the rectangle is selected
    const selectedRectangle = await page.locator('rect.selected');
    await expect(selectedRectangle).toBeVisible();
    
    // Delete the rectangle using the keyboard shortcut
    await page.keyboard.press('Delete');
    
    // Verify that the rectangle was deleted
    await expect(page.locator('rect')).toHaveCount(0);
    
    // Undo the deletion using the keyboard shortcut
    await page.keyboard.press('Control+Z');
    
    // Verify that the rectangle was restored
    await expect(page.locator('rect')).toHaveCount(1);
  });

  /**
   * Tests the responsiveness of the canvas.
   * Verifies that the canvas is responsive to window size changes.
   */
  test('Canvas responsiveness', async ({ page }) => {
    // Get the initial canvas size
    const initialCanvasWidth = await page.evaluate(() => {
      const svg = document.querySelector('#canvas-container svg');
      return svg.clientWidth;
    });
    
    const initialCanvasHeight = await page.evaluate(() => {
      const svg = document.querySelector('#canvas-container svg');
      return svg.clientHeight;
    });
    
    // Resize the window
    await page.setViewportSize({ width: 800, height: 600 });
    
    // Get the new canvas size
    const newCanvasWidth = await page.evaluate(() => {
      const svg = document.querySelector('#canvas-container svg');
      return svg.clientWidth;
    });
    
    const newCanvasHeight = await page.evaluate(() => {
      const svg = document.querySelector('#canvas-container svg');
      return svg.clientHeight;
    });
    
    // Verify that the canvas size changed
    expect(newCanvasWidth).not.toBe(initialCanvasWidth);
    expect(newCanvasHeight).not.toBe(initialCanvasHeight);
  });
});
