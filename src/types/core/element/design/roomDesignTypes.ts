import type { MaterialType } from '@/types/core/element/elementMaterialTypes'
import type { Ellipse } from '@/types/core/element/shape/ellipseShapeTypes'
import type { Polygon } from '@/types/core/element/shape/polygonShapeTypes'
import type { Rectangle } from '@/types/core/element/shape/rectangleShapeTypes'

/**
 * Defines types for room elements in interior design.
 *
 * @remarks
 * This module provides definitions for various room types and their associated properties,
 * crucial for representing and managing spaces within an interior design project.
 *
 * @module types/core/element/design/roomDesignTypes
 * @see {@link OpeningType} for related opening element types often found within rooms.
 * @see {@link WallProperties} for properties of walls that typically define room boundaries.
 */

/**
 * Enumerates standard room types used in residential and commercial interior design.
 *
 * @remarks
 * This enum covers a comprehensive list of common room classifications.
 * The `CUSTOM` value allows for user-defined room types not explicitly listed.
 *
 * @see {@link RoomProperties} for how these types are used in room element definitions.
 */
export enum RoomType {
  /** A primary communal space for relaxation and socializing. */
  LIVING_ROOM = 'living-room',
  /** A space designated for eating meals. */
  DINING_ROOM = 'dining-room',
  /** A space equipped for cooking and food preparation. */
  KITCHEN = 'kitchen',
  /** A room primarily used for sleeping. */
  BEDROOM = 'bedroom',
  /** A room containing a toilet and a sink, and often a bathtub or shower. */
  BATHROOM = 'bathroom',
  /** A room designated as a workspace, typically for professional or academic tasks. */
  OFFICE = 'office',
  /** A passage or corridor connecting different parts of a building. */
  HALLWAY = 'hallway',
  /** An area at the entrance of a building or home. */
  ENTRYWAY = 'entryway',
  /** A room equipped for washing and drying clothes. */
  LAUNDRY = 'laundry',
  /** A building or part of a building used to store a vehicle or vehicles. */
  GARAGE = 'garage',
  /** The floor of a building partly or entirely below ground level. */
  BASEMENT = 'basement',
  /** An outdoor platform enclosed by a wall or balustrade on the outside of a building, with access from an upper-floor window or door. */
  BALCONY = 'balcony',
  /** A paved outdoor area adjoining a house. */
  PATIO = 'patio',
  /** A room designed or designated for children. */
  CHILDREN = 'children',
  /** A room designed with considerations for elderly occupants, focusing on accessibility and safety. */
  ELDERLY_FRIENDLY = 'elderly-friendly',
  /** A room designed to serve multiple functions (e.g., guest room/office). */
  MULTIFUNCTION = 'multifunction',
  /** A room or space designated for storing items. */
  STORAGE = 'storage',
  /** A room used for quiet work or reading; a home office. */
  STUDY = 'study',
  /** A user-defined or non-standard room type. */
  CUSTOM = 'custom',
}

/**
 * Defines the properties specific to a room element in an interior design project.
 *
 * @remarks
 * This interface represents the complete set of attributes needed to define a room,
 * including its type, name, dimensions (like ceiling height), materials for surfaces,
 * and references to contained elements like walls, openings, furniture, and fixtures.
 *
 * @see {@link MaterialType} for definitions of material types.
 * @see {@link OpeningProperties} for properties of opening elements within the room.
 */
export interface RoomProperties {
  /**
   * A string identifier for the type of this element, e.g., 'room'.
   * This helps in distinguishing it from other element types.
   */
  type: string

  /** The classification of the room, as defined by the {@link RoomType} enum. */
  roomType: RoomType

  /** A user-defined name for the room (e.g., "Master Bedroom", "Living Area"). */
  name: string

  /**
   * The floor level on which the room is located.
   * For example, 0 could represent the ground floor, 1 the first floor, -1 a basement level.
   */
  floorLevel: number

  /** The height of the ceiling in the room, typically measured in millimeters. */
  ceilingHeight: number

  /**
   * The material used for the floor surfaces of the room.
   * Can be a {@link MaterialType} enum member or a custom string identifier.
   */
  floorMaterial: MaterialType | string

  /**
   * The material used for the wall surfaces of the room.
   * Can be a {@link MaterialType} enum member or a custom string identifier.
   */
  wallMaterial: MaterialType | string

  /**
   * The material used for the ceiling surface of the room.
   * Can be a {@link MaterialType} enum member or a custom string identifier.
   */
  ceilingMaterial: MaterialType | string

  /** An array of unique string identifiers for the wall elements that define the boundaries of this room. */
  wallIds: string[]

  /** An array of unique string identifiers for opening elements (doors, windows) contained within this room's walls. */
  openingIds: string[]

  /** An array of unique string identifiers for furniture elements placed within this room. */
  furnitureIds: string[]

  /** An array of unique string identifiers for fixture elements installed in this room. */
  fixtureIds: string[]

  /**
   * The geometric shape defining the boundary of the room.
   * This can be a Rectangle, Ellipse, or Polygon.
   */
  shape: Rectangle | Ellipse | Polygon

  /** Optional: Indicates if the room is an outdoor space (e.g., balcony, patio). Defaults to false. */
  isOutdoor?: boolean
}
