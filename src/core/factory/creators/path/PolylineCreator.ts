import type { CreatePolylineParams } from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point'
import type {
  Path,
} from '@/types/core/elementDefinitions'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
/**
 * Creator for Polyline Path Elements
 *
 * @remarks
 * This class extends {@link PathCreator} to specialize in creating
 * Polyline ({@link CoreElementType.POLYLINE}) path elements. It uses `createCommonProperties`
 * for base attributes and then adds polyline-specific properties (points, curved, tension).
 *
 * @module core/factory/creators/path/PolylineCreator
 */
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'
import { PathCreator } from './PathCreator'

// Removed EnrichedCreatePolylineParams as commonProps handles position now

// normalizeToPointData can be removed if all points are already PointData or handled by params
// For simplicity, assuming points in CreatePolylineParams are already PointData[] or will be normalized before this create.

export class PolylineCreator extends PathCreator<Path.Polyline, CreatePolylineParams> {
  constructor() {
    super(CoreElementType.POLYLINE)
  }

  /**
   * Creates a Polyline {@link Path.Polyline} element based on the provided parameters.
   *
   * @param params - A {@link CreatePolylineParams} object
   *                 containing all necessary information for polyline creation.
   * @returns A Promise resolving to the created {@link Path.Polyline} element.
   */
  public async create(params: CreatePolylineParams): Promise<Path.Polyline> {
    if (params.type !== CoreElementType.POLYLINE && params.type !== 'polyline') {
      throw new CoreError(ErrorType.InvalidPayload, `PolylineCreator cannot create type: ${String(params.type)}`)
    }

    const { id, points } = params
    if (!id)
      throw new CoreError(ErrorType.InvalidPayload, 'Polyline element requires an ID.')
    if (points == null || !Array.isArray(points) || points.length < 2) {
      throw new CoreError(ErrorType.InvalidPayload, 'Polyline creation requires at least 2 points.')
    }

    // The first point of the polyline can be its nominal position.
    // PathCreator.createCommonProperties will use params.position if available, or the first point from params.points.
    // If params.points[0] is to be the position, ensure params.position is not set or is set to params.points[0].
    // For this refactor, we assume createCommonProperties handles position correctly based on params.
    const commonProps = this.createCommonProperties(id, params)

    const polylineElement: Path.Polyline = {
      ...commonProps,
      // type: CoreElementType.POLYLINE, // from commonProps
      // position: commonProps.position, // from commonProps
      properties: {
        // 设置默认的成本相关属性
        costUnitPrice: 1,
        costMultiplierOrCount: 0,
        costBasis: 'unit',
        // 然后合并其他属性
        ...(params.properties || {}),
        points: points.map(p => ({ x: p.x, y: p.y, z: p.z ?? 0 })),
        curved: params.curved ?? false,
        tension: params.tension ?? 0.5,
        // `closed` property for polyline was not explicitly handled here before, assuming it might be in params.properties if needed
        // If `closed` is a direct param in CreatePolylineParams and needs to be in properties, add it:
        // closed: params.closed ?? false,
      },
    }

    return polylineElement
  }

  /**
   * Creates a default Polyline element.
   *
   * @param id - The unique identifier for the default polyline element.
   * @param position - The {@link PointData} for the default polyline's start position.
   * @param majorCategoryOverride - Optional override for majorCategory.
   * @param minorCategoryOverride - Optional override for minorCategory.
   * @param zLevelIdOverride - Optional override for zLevelId.
   * @param isFixedCategoryOverride - Optional override for isFixedCategory.
   * @returns A Promise resolving to the created default {@link Path.Polyline}.
   */
  public async createDefault(
    id: string,
    position: PointData, // Used as the basis for the first point
    majorCategoryOverride?: MajorCategory,
    minorCategoryOverride?: MinorCategory,
    zLevelIdOverride?: string,
    isFixedCategoryOverride?: boolean,
  ): Promise<Path.Polyline> {
    const defaultPoints: PointData[] = [
      position, // First point at the given position
      { x: position.x + 80, y: position.y + 40, z: position.z },
    ]

    // Get default settings for POLYLINE elements
    const { getDefaultSettingsForElementType } = await import('@/config/defaultElementSettings')
    const defaults = getDefaultSettingsForElementType('POLYLINE' as any)

    const params: CreatePolylineParams = {
      id,
      type: CoreElementType.POLYLINE,
      majorCategory: majorCategoryOverride ?? MajorCategory.FURNITURE, // Default
      minorCategory: minorCategoryOverride,
      zLevelId: zLevelIdOverride,
      isFixedCategory: isFixedCategoryOverride,
      points: defaultPoints,
      // position: position, // commonProps will derive from points[0] or params.position if set
      metadata: { name: 'Polyline' },
      stroke: defaults.stroke ?? '#333333',
      strokeWidth: defaults.strokeWidth ?? 2,
      opacity: defaults.opacity ?? 1,
      visible: true,
      locked: false,
      properties: {
        // 设置默认的成本相关属性
        costUnitPrice: 1,
        costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1
        costBasis: 'unit', // 默认使用单位计算
        // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
        computedAreaStatus: 'none',
        computedPerimeterStatus: 'none',
        computedLengthStatus: 'none',
      },
    }
    return this.create(params)
  }
}
