/**
 * Appearance Tab Component
 *
 * A modular property panel tab for managing visual appearance of selected elements
 * following the Geometry Tab design pattern.
 *
 * Features:
 * - Modular sections with consistent grid layouts
 * - Color controls positioned on the left side
 * - Grid-based responsive layout for other properties
 * - Fill and stroke color controls
 * - Pattern/texture system with multiple types (lines, circles, paths)
 * - Dynamic pattern options based on selected pattern type
 * - Unit conversion support for dimensional properties
 * - Multi-element selection support with mixed value handling
 * - Real-time preview of appearance changes
 *
 * @example
 * ```tsx
 * <AppearanceTab
 *   selectedElements={selectedElements}
 *   getCommonValue={getCommonValue}
 *   updateProperty={updateProperty}
 *   commonPattern={commonPattern}
 *   isPatternActive={isPatternActive}
 *   currentPatternSelectValue={currentPatternSelectValue}
 *   handlePatternChange={handlePatternChange}
 *   handlePatternOptionChange={handlePatternOptionChange}
 *   pixelsPerMM={pixelsPerMM}
 * />
 * ```
 */

import type React from 'react'
import type {
  PatternDefinition,
  TextureCirclesOptions,
  TextureLinesOptions,
  TexturePathsOptions,
} from '@/types/core/element/elementPatternTypes'
import type { ShapeElement as ShapeModel } from '@/types/core/elementDefinitions'
import { useCallback, useEffect, useState } from 'react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
// import { Checkbox } from "@/components/ui/checkbox"; // Unused import
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useUnitConversion } from '@/hooks/useUnitConversion'
import { appEventBus } from '@/services/core/event-bus'
import { useShapesStore } from '@/store/shapesStore'
import { ElementType } from '@/types/core/elementDefinitions'
import { AppEventType } from '@/types/services/events/eventTypes'
import { GeometrySection, PropertyField, ValueSlider } from './shared'

// Define texture types for the Select component iteration
const TEXTURE_TYPES: Array<PatternDefinition['textureType'] & string> = ['lines', 'circles', 'paths']

// 🔧 修复：统一类型定义，与PropertySidebar保持一致
type ResolvedPatternType = 'lines' | 'circles' | 'paths' | 'none'



const UNIT_NAME = 'mm'

/**
 * Type-safe helper to get string property from options object
 */
function getStringOption<T>(options: Partial<T>, key: keyof T, defaultValue: string = ''): string {
  const value = options[key]
  return typeof value === 'string' ? value : defaultValue
}

/**
 * Type-safe helper to get number property from options object
 */
function getNumberOption<T>(options: Partial<T>, key: keyof T, defaultValue: number = 0): number {
  const value = options[key]
  return typeof value === 'number' ? value : defaultValue
}

/**
 * Interface for props passed to specific pattern option components
 * @template T - The type of pattern options (e.g., TextureLinesOptions, TextureCirclesOptions)
 */
interface PatternOptionComponentProps<T> {
  /** Pattern options specific to the pattern type */
  _options: T // Prefixed with underscore
  /** Common pattern definition containing textureType and other shared properties */
  commonPattern: PatternDefinition
  /** Callback to handle changes to pattern option properties */
  handlePatternOptionChange: (optionPath: string, value: unknown) => void
  /** Function to get common value across selected elements for a property path */
  getCommonValue: (propertyPath: string) => unknown
  /** Conversion factor from pixels to millimeters */
  pixelsPerMM: number
  /** Function to convert internal values to display units */
  toDisplayUnit: (internalValue: number | string | null | undefined | 'mixed') => string
  /** Function to convert display values to internal units */
  toInternalUnit: (displayValue: string | number) => number | undefined
  /** Helper function to handle dimension changes with proper conversion */
  handleDimensionChange: (updateFn: (path: string, value: unknown) => void, propertyPath: string, value: string | number) => void
  /** Function to get formatted value for input fields */
  getValueForInput: (path: string, isDimension?: boolean) => string
  /** Function to get placeholder text for input fields */
  getPlaceholderForInput: (path: string, exampleValue?: number | string, isDimension?: boolean) => string | undefined
}

// --- LinePatternOptions ---
const LinePatternOptions: React.FC<Omit<PatternOptionComponentProps<Partial<TextureLinesOptions>>, 'getValueForInput' | 'handleDimensionChange' | 'toInternalUnit' | 'toDisplayUnit' | 'getPlaceholderForInput' | 'getCommonValue' | 'pixelsPerMM'> & { updateProperty: (propertyPath: string, value: unknown) => void }> = ({
  _options,
  commonPattern,
  updateProperty,
}) => {
  let selectOrientationValue: string | undefined = 'horizontal'
  if (typeof _options.orientation === 'string') {
    selectOrientationValue = _options.orientation
  }
  else if (
    Array.isArray(_options.orientation)
    && _options.orientation.length > 0
    && typeof _options.orientation[0] === 'string'
  ) {
    selectOrientationValue = _options.orientation[0]
  }

  const handleLineOptionChange = (key: string, value: number | string) => {
    if (!commonPattern?.linesOptions) {
      return
    }

    const newPattern = {
      ...commonPattern,
      linesOptions: {
        ...commonPattern.linesOptions,
        [key]: value,
      },
    }
    updateProperty('pattern', newPattern)
  }

  return (
    <div className="space-y-3">
      {/* 第一行：Lines Fill Color */}
      <div className="space-y-1">
        <Label htmlFor="line-stroke">Lines Fill Color</Label>
        <Input
          id="line-stroke"
          type="color"
          value={getStringOption(_options, 'color', '#333333')}
          onChange={(e) => {
            const newPatternColor = e.target.value
            // 同时设置渲染引擎使用的属性和UI显示的属性
            handleLineOptionChange('stroke', newPatternColor) // 渲染引擎使用
            handleLineOptionChange('color', newPatternColor) // UI显示使用
          }}
          className="h-10 w-full"
          title="Pattern line color - automatically contrasts with fill color when fill changes"
        />
      </div>
      {/* 第二行：Orientation */}
      <div className="space-y-1">
        <Label htmlFor="line-orientation">Orientation</Label>
        <Select
          value={selectOrientationValue}
          onValueChange={(value: string) => handleLineOptionChange('orientation', value)}
        >
          <SelectTrigger id="line-orientation">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="horizontal">Horizontal</SelectItem>
            <SelectItem value="vertical">Vertical</SelectItem>
            <SelectItem value="diagonal">Diagonal (↘)</SelectItem>
            <SelectItem value="diagonal-sw-ne">Diagonal (↗)</SelectItem>
            <SelectItem value="radial">Radial</SelectItem>
          </SelectContent>
        </Select>
      </div>
      {/* 第三行：Stroke Width 和 Size */}
      <div className="grid grid-cols-2 gap-x-4 gap-y-3">
        <div className="space-y-1">
          <Label htmlFor="line-strokeWidth">Stroke Width (mm)</Label>
          <Input
            id="line-strokeWidth"
            type="number"
            value={getNumberOption(_options, 'strokeWidth') || ''}
            placeholder={getNumberOption(_options, 'strokeWidth') ? String(getNumberOption(_options, 'strokeWidth')) : '1'}
            onChange={e => handleLineOptionChange('strokeWidth', Math.max(0.1, Number(e.target.value)))}
            min={0.1}
          />
        </div>
        <div className="space-y-1">
          <Label htmlFor="line-size">Size (mm)</Label>
          <Input
            id="line-size"
            type="number"
            value={getNumberOption(_options, 'size') || ''}
            placeholder={getNumberOption(_options, 'size') ? String(getNumberOption(_options, 'size')) : 'e.g., 4'}
            onChange={e => handleLineOptionChange('size', Math.max(1, Math.round(Number(e.target.value))))}
            min={1}
            step={1}
          />
        </div>
      </div>
    </div>
  )
}

// Type definitions for pattern options
interface CircleOptions {
  radius?: number
  size?: number
  [key: string]: unknown
}

interface PathOptions {
  d?: string
  size?: number
  fill?: string
  [key: string]: unknown
}

// --- CirclePatternOptions ---
const CirclePatternOptions: React.FC<Omit<PatternOptionComponentProps<Partial<TextureCirclesOptions>>, 'getValueForInput' | 'handleDimensionChange' | 'toInternalUnit' | 'toDisplayUnit' | 'getPlaceholderForInput' | 'getCommonValue' | 'pixelsPerMM'> & { updateProperty: (propertyPath: string, value: unknown) => void }> = ({
  _options,
  commonPattern,
  updateProperty,
}) => {
  const handleCircleOptionChange = (key: keyof TextureCirclesOptions, value: number | string) => {
    if (!commonPattern?.circlesOptions) {
      return
    }

    const newPattern = {
      ...commonPattern,
      circlesOptions: {
        ...commonPattern.circlesOptions,
        [key]: value,
      },
    }
    updateProperty('pattern', newPattern)
  }

  // Type-safe option access
  const circleOptions = _options as CircleOptions

  return (
    <div className="space-y-3">
      {/* 第一行：Circles Fill Color */}
      <div className="space-y-1">
        <Label htmlFor="circle-fill">Circles Fill Color</Label>
        <Input
          id="circle-fill"
          type="color"
          value={getStringOption(_options, 'fill', '#333333')}
          onChange={(e) => {
            const newPatternColor = e.target.value
            // 只设置渲染引擎使用的属性
            handleCircleOptionChange('fill', newPatternColor)
          }}
          className="h-10 w-full"
        />
      </div>
      {/* 第二行：Radius、Spacing */}
      <div className="grid grid-cols-2 gap-x-4 gap-y-3">
        <div className="space-y-1">
          <Label htmlFor="circle-radius">Radius (mm)</Label>
          <Input
            id="circle-radius"
            type="number"
            value={circleOptions.radius ?? ''}
            placeholder={typeof circleOptions.radius === 'number' ? String(circleOptions.radius) : 'e.g., 2'}
            onChange={e => handleCircleOptionChange('radius', Math.max(1, Math.round(Number(e.target.value))))}
            min={1}
            step={1}
          />
        </div>
        <div className="space-y-1">
          <Label htmlFor="circle-spacing">Spacing (mm)</Label>
          <Input
            id="circle-spacing"
            type="number"
            value={circleOptions.size ?? ''}
            placeholder={typeof circleOptions.size === 'number' ? String(circleOptions.size) : 'e.g., 5'}
            onChange={e => handleCircleOptionChange('size', Math.max(1, Math.round(Number(e.target.value))))}
            min={1}
            step={1}
          />
        </div>
      </div>
    </div>
  )
}

// --- PathPatternOptions ---
const PathPatternOptions: React.FC<Omit<PatternOptionComponentProps<Partial<TexturePathsOptions>>, 'getValueForInput' | 'handleDimensionChange' | 'toInternalUnit' | 'toDisplayUnit' | 'getPlaceholderForInput' | 'getCommonValue' | 'pixelsPerMM'> & { updateProperty: (propertyPath: string, value: unknown) => void }> = ({
  _options,
  commonPattern,
  updateProperty,
}) => {
  const handlePathOptionChange = (key: string, value: number | string) => {
    if (!commonPattern?.pathsOptions)
      return

    const newPattern = {
      ...commonPattern,
      pathsOptions: {
        ...commonPattern.pathsOptions,
        [key]: value,
      },
    }
    updateProperty('pattern', newPattern)
  }

  const handlePathKeywordChange = (value: string) => {
    handlePathOptionChange('d', value)
  }

  // Type-safe option access
  const pathOptions = _options as PathOptions
  const currentDPathValue = pathOptions.d ?? 'squares'
  const knownKeywords = ['squares', 'waves', 'woven', 'crosses', 'caps', 'hexes']
  const isKnownKeyword = knownKeywords.includes(currentDPathValue)
  const selectDisplayValue = isKnownKeyword && typeof currentDPathValue === 'string' ? currentDPathValue : knownKeywords[0]

  return (
    <div className="space-y-3">
      {/* 第一行：Path Fill Color */}
      <div className="space-y-1">
        <Label htmlFor="path-fill">Path Fill Color</Label>
        <Input
          id="path-fill"
          type="color"
          value={getStringOption(pathOptions, 'fill', '#333333')}
          onChange={(e) => {
            const newPatternColor = e.target.value
            // 只设置渲染引擎使用的属性
            handlePathOptionChange('fill', newPatternColor)
          }}
          className="h-10 w-full"
        />
      </div>
      {/* 第二行：Path Types */}
      <div className="space-y-1">
        <Label htmlFor="path-d-select">Path Type</Label>
        <Select
          value={selectDisplayValue}
          onValueChange={handlePathKeywordChange}
        >
          <SelectTrigger id="path-d-select">
            <SelectValue placeholder="Select path shape or keyword" />
          </SelectTrigger>
          <SelectContent>
            {knownKeywords.map(k => (
              <SelectItem key={k} value={k}>{k.charAt(0).toUpperCase() + k.slice(1)}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      {/* 第三行：Spacing */}
      <div className="space-y-1">
        <Label htmlFor="path-spacing">Path Spacing (mm)</Label>
        <Input
          id="path-spacing"
          type="number"
          value={pathOptions.size ?? ''}
          placeholder={typeof pathOptions.size === 'number' ? String(pathOptions.size) : 'e.g., 5'}
          onChange={e => handlePathOptionChange('size', Math.max(1, Math.round(Number(e.target.value))))}
          min={1}
          step={1}
        />
      </div>
    </div>
  )
}

/**
 * Props for the AppearanceTab component
 */
interface AppearanceTabProps {
  /** Array of currently selected shape elements */
  selectedElements: ShapeModel[]
  /** Function to get common value across selected elements for a property path */
  getCommonValue: (propertyPath: string) => unknown
  /** Function to update a property value for selected elements */
  updateProperty: (propertyPath: string, value: unknown) => void
  /** Function to update multiple properties for selected elements in a single batch */
  updateProperties?: (updates: Record<string, unknown>) => void
  /** Common pattern definition across selected elements, or 'mixed' if different patterns */
  commonPattern: PatternDefinition | 'mixed' | undefined
  /** Whether any pattern is currently active on selected elements */
  isPatternActive: boolean
  /** Current pattern type selection value for the dropdown */
  currentPatternSelectValue: ResolvedPatternType
  /** Callback to handle pattern type changes */
  handlePatternChange: (newPattern: { textureType?: string }, fillColorLocal?: string) => void
  /** Callback to handle changes to specific pattern options */
  handlePatternOptionChange: (optionPath: string, value: unknown) => void
  /** Conversion factor from pixels to millimeters for unit conversion */
  pixelsPerMM: number
}

/**
 * AppearanceTab component for managing visual appearance properties of selected elements.
 *
 * This component provides a comprehensive interface for editing visual properties including:
 * - Fill and stroke colors
 * - Stroke width and opacity
 * - Advanced pattern/texture options (lines, circles, paths)
 * - Pattern-specific configuration options
 *
 * The component handles multi-element selection by showing common values and
 * indicating when values are mixed across selected elements.
 *
 * @param props - The component props
 * @param props.selectedElements - Array of currently selected shape elements
 * @param props.getCommonValue - Function to get common value across selected elements for a property path
 * @param props.updateProperty - Function to update a property value for selected elements
 * @param props.updateProperties - Function to update multiple properties for selected elements in a single batch
 * @param props.commonPattern - Common pattern definition across selected elements, or 'mixed' if different patterns
 * @param props.isPatternActive - Whether any pattern is currently active on selected elements
 * @param props.currentPatternSelectValue - Current pattern type selection value for the dropdown
 * @param props.handlePatternChange - Callback to handle pattern type changes
 * @param props.handlePatternOptionChange - Callback to handle changes to specific pattern options
 * @param props.pixelsPerMM - Conversion factor from pixels to millimeters for unit conversion
 * @returns The rendered appearance tab component
 */
const AppearanceTab: React.FC<AppearanceTabProps> = ({
  selectedElements,
  getCommonValue,
  updateProperty,
  updateProperties: _updateProperties,
  commonPattern,
  isPatternActive,
  currentPatternSelectValue,
  handlePatternChange,
  handlePatternOptionChange,
  pixelsPerMM,
}) => {
  const { toDisplayUnit, toInternalUnit } = useUnitConversion(pixelsPerMM)

  // 🔧 修复：添加本地状态来立即反映用户的Pattern Type选择
  const [localPatternSelectValue, setLocalPatternSelectValue] = useState<ResolvedPatternType | null>(null)
  const [lastSelectedElementId, setLastSelectedElementId] = useState<string | null>(null)

  // 🔧 修复：当切换到不同元素时，重置本地状态，但要考虑元素的实际pattern状态
  useEffect(() => {
    const timeoutIds: NodeJS.Timeout[] = []

    if (selectedElements.length === 1) {
      const currentElementId = selectedElements[0].id
      const currentElement = selectedElements[0]

      if (currentElementId !== lastSelectedElementId) {
        // 根据元素的实际pattern状态来设置本地状态
        const hasPattern = currentElement.pattern?.textureType !== undefined
          && currentElement.pattern?.textureType !== null

        if (!hasPattern) {
          // 如果元素没有pattern，设置为'none'以确保UI显示正确
          const timeoutId1 = setTimeout(() => setLocalPatternSelectValue('none'), 0)
          timeoutIds.push(timeoutId1)
        }
        else {
          // 如果元素有pattern，重置为null让它使用currentPatternSelectValue
          const timeoutId2 = setTimeout(() => setLocalPatternSelectValue(null), 0)
          timeoutIds.push(timeoutId2)
        }
        const timeoutId3 = setTimeout(() => setLastSelectedElementId(currentElementId), 0)
        timeoutIds.push(timeoutId3)
      }
      else {
        // 即使是同一个元素，也要检查pattern状态是否与本地状态一致
        const hasPattern = currentElement.pattern?.textureType !== undefined
          && currentElement.pattern?.textureType !== null

        if (!hasPattern && localPatternSelectValue !== 'none') {
          // Use a timeout to avoid direct setState call in useEffect
          const timeoutId4 = setTimeout(() => setLocalPatternSelectValue('none'), 0)
          timeoutIds.push(timeoutId4)
        }
        else if (hasPattern && localPatternSelectValue === 'none') {
          // Use a timeout to avoid direct setState call in useEffect
          const timeoutId5 = setTimeout(() => setLocalPatternSelectValue(null), 0)
          timeoutIds.push(timeoutId5)
        }
      }
    }
    else if (selectedElements.length === 0) {
      // 没有选中元素时也重置状态
      const timeoutId6 = setTimeout(() => {
        setLocalPatternSelectValue(null)
        setLastSelectedElementId(null)
      }, 0)
      timeoutIds.push(timeoutId6)
    }
    else {
      // 多选时重置状态
      const timeoutId7 = setTimeout(() => {
        setLocalPatternSelectValue(null)
        setLastSelectedElementId(null)
      }, 0)
      timeoutIds.push(timeoutId7)
    }

    return () => {
      timeoutIds.forEach(id => clearTimeout(id))
    }
  }, [selectedElements, lastSelectedElementId, localPatternSelectValue])

  // 🔧 修复：计算实际显示的pattern select value，优先使用本地状态
  const displayPatternSelectValue = localPatternSelectValue !== null ? localPatternSelectValue : currentPatternSelectValue

  // 调试日志

  const handleDimensionChange = useCallback((
    updateFn: (path: string, value: unknown) => void,
    propertyPath: string,
    value: string | number,
  ) => {
    const numericValue = typeof value === 'string' ? Number(value) : value
    if (!Number.isNaN(numericValue)) {
      updateFn(propertyPath, numericValue)
    }
  }, [])

  // 🔧 修复：专门的fontFamily值获取函数，防止在更新其他属性时被重置
  const getFontFamilyValue = useCallback((): string => {
    try {
      // 优先从getCommonValue获取
      const commonVal = getCommonValue('fontFamily')
      if (typeof commonVal === 'string' && commonVal !== 'mixed') {
        return commonVal
      }

      // 如果getCommonValue失败，直接从selectedElements获取
      if (selectedElements.length > 0) {
        const element = selectedElements[0]
        const fontFamily = element.properties?.fontFamily
        if (typeof fontFamily === 'string') {
          return fontFamily
        }
      }

      // 最后才使用默认值
      return 'Inter, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
    }
    catch {
      return 'Inter, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
    }
  }, [getCommonValue, selectedElements])

  // 🔧 修复：专门的textAlign值获取函数，防止在更新其他属性时被重置
  const getTextAlignValue = useCallback((): string => {
    try {
      // 优先从getCommonValue获取
      const commonVal = getCommonValue('textAlign')
      if (typeof commonVal === 'string' && commonVal !== 'mixed') {
        return commonVal
      }

      // 如果getCommonValue失败，直接从selectedElements获取
      if (selectedElements.length > 0) {
        const element = selectedElements[0]
        const textAlign = element.properties?.textAlign
        if (typeof textAlign === 'string') {
          return textAlign
        }
      }

      // 最后才使用默认值
      return 'left'
    }
    catch {
      return 'left'
    }
  }, [getCommonValue, selectedElements])

  const getValueForInput = useCallback((path: string): string => {
    try {
      const val = getCommonValue(path)
      if (path.endsWith('background') && (val === 'mixed' || val === null || val === undefined || val === '')) {
        return '#ffffff'
      }
      if (val === 'mixed' || val === null || val === undefined) {
        return ''
      }
      return String(val)
    }
    catch (error) {
      console.warn(`[AppearanceTab] Error getting value for path "${path}":`, error)
      return ''
    }
  }, [getCommonValue])

  const getPlaceholderForInput = useCallback((path: string, exampleValue?: number | string, isDimension: boolean = true) => {
    try {
      const commonVal = getCommonValue(path)
      if (commonVal === 'mixed') {
        return 'Mixed'
      }
      if (exampleValue !== undefined) {
        const displayValue = isDimension && typeof exampleValue === 'number' ? toDisplayUnit(exampleValue) : exampleValue
        return `e.g., ${displayValue}${isDimension ? ` ${UNIT_NAME}` : ''}`
      }
      return isDimension ? UNIT_NAME : undefined
    }
    catch {
      return isDimension ? UNIT_NAME : undefined
    }
  }, [getCommonValue, toDisplayUnit])

  // Helper to render pattern options based on type
  const renderPatternOptions = () => {
    if (!isPatternActive || commonPattern === 'mixed' || !commonPattern?.textureType) {
      return null
    }

    const patternProps = {
      commonPattern,
      handlePatternOptionChange,
      getCommonValue,
      pixelsPerMM,
      toDisplayUnit,
      toInternalUnit,
      handleDimensionChange,
      getValueForInput,
      getPlaceholderForInput,
    }

    switch (commonPattern.textureType) {
      case 'lines':
        return <LinePatternOptions {...patternProps} _options={commonPattern.linesOptions || {}} commonPattern={commonPattern} updateProperty={updateProperty} />
      case 'circles':
        return <CirclePatternOptions {...patternProps} _options={commonPattern.circlesOptions || {}} commonPattern={commonPattern} updateProperty={updateProperty} />
      case 'paths':
        return <PathPatternOptions {...patternProps} _options={commonPattern.pathsOptions || {}} commonPattern={commonPattern} updateProperty={updateProperty} />
      case 'hexagons':
        // hexagons 已被移除，兜底返回 null
        return null
      default:
        return null
    }
  }

  // 类型安全获取 pattern background
  function getPatternBackgroundValue(
    commonPattern: PatternDefinition | 'mixed' | undefined,
    isPatternActive: boolean,
    fillColorLocal: string,
  ): string {
    if (
      isPatternActive
      && commonPattern !== 'mixed'
      && typeof commonPattern === 'object'
      && commonPattern?.textureType !== undefined
      && (commonPattern.textureType === 'lines' || commonPattern.textureType === 'circles' || commonPattern.textureType === 'paths')
    ) {
      const optRaw = (commonPattern as unknown as Record<string, unknown>)[`${commonPattern.textureType}Options`]
      if (
        typeof optRaw === 'object'
        && optRaw !== null
        && 'background' in optRaw
        && typeof (optRaw as { background?: unknown }).background === 'string'
      ) {
        return (optRaw as { background: string }).background
      }
      return '#ffffff'
    }
    return fillColorLocal
  }

  const [fillColorLocal, setFillColorLocal] = useState<string>('')

  const handlePatternChangeInternal = (newPatternProps: Partial<PatternDefinition>) => {
    const { textureType } = newPatternProps

    if (!textureType || String(textureType) === 'none') {
      // 立即更新本地状态以反映用户选择
      setLocalPatternSelectValue('none')

      // 当切换到None时，保留当前的填充颜色
      const currentFill = fillColorLocal || '#ffffff'

      updateProperty('pattern', undefined)

      // 确保fill属性被设置为当前的填充颜色
      updateProperty('fill', currentFill)

      // 确保本地状态持久保持'none'，即使在状态更新后
      setTimeout(() => {
        setLocalPatternSelectValue('none')
      }, 100)

      setTimeout(() => {
        setLocalPatternSelectValue('none')
      }, 1000)

      return
    }

    // 立即更新本地状态以反映用户选择
    setLocalPatternSelectValue(textureType as ResolvedPatternType)

    // 通过 props 传递 fillColorLocal
    handlePatternChange({ textureType }, fillColorLocal)

    // 🔧 修复：不再自动清除本地状态，让它持续保持用户的选择
  }

  useEffect(() => {
    // When Pattern is active, update local state from pattern background
    if (isPatternActive && commonPattern !== 'mixed' && commonPattern?.textureType) {
      const v = getValueForInput(`pattern.${commonPattern.textureType}Options.background`)
      // Use a timeout to avoid direct setState call in useEffect
      const timeoutId = setTimeout(() => setFillColorLocal(v === '' ? '#ffffff' : v), 0)
      return () => clearTimeout(timeoutId)
    }
  }, [isPatternActive, commonPattern, getValueForInput])

  useEffect(() => {
    // When Pattern is NOT active, update local state from element fill
    if (!isPatternActive && selectedElements.length === 1) {
      // Use a timeout to avoid direct setState call in useEffect
      const timeoutId = setTimeout(() => setFillColorLocal(selectedElements[0].fill ?? '#ffffff'), 0)
      return () => clearTimeout(timeoutId)
    }
  }, [isPatternActive, selectedElements])

  return (
    <div className="space-y-4 p-1">
      {/* Text Elements Appearance */}
      {selectedElements.length > 0 && selectedElements.every(el => el.type === ElementType.TEXT || el.type === ElementType.TEXT_LABEL) && (
        <>
          {/* Colors Section */}
          <GeometrySection type="colors" columns={2}>
            <PropertyField
              label="Text Color"
              type="color"
              value={getValueForInput('fill') || '#000000'}
              onChange={value => updateProperty('fill', value)}
            />
            <ValueSlider
              label="Opacity"
              value={Number(getCommonValue('opacity') ?? 1)}
              onChange={value => updateProperty('opacity', value)}
              min={0}
              max={1}
              step={0.01}
              showInput={true}
            />
          </GeometrySection>

          {/* Typography Section */}
          <GeometrySection type="typography" columns={2}>
            <div className="space-y-1">
              <Label htmlFor="fontFamily" className="text-sm font-medium">Font Family</Label>
              <Select
                value={getFontFamilyValue()}
                onValueChange={(value) => {
                  updateProperty('fontFamily', value)
                  updateProperty('properties.fontFamily', value)
                }}
              >
                <SelectTrigger id="fontFamily">
                  <SelectValue placeholder="Select font family" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='Inter, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'>Inter (Modern)</SelectItem>
                  <SelectItem value="Arial, sans-serif">Arial</SelectItem>
                  <SelectItem value="Helvetica, Arial, sans-serif">Helvetica</SelectItem>
                  <SelectItem value="'Times New Roman', Times, serif">Times New Roman</SelectItem>
                  <SelectItem value='Georgia, "Times New Roman", serif'>Georgia</SelectItem>
                  <SelectItem value="'Courier New', Courier, monospace">Courier New</SelectItem>
                  <SelectItem value="Verdana, Geneva, sans-serif">Verdana</SelectItem>
                  <SelectItem value="'Trebuchet MS', Helvetica, sans-serif">Trebuchet MS</SelectItem>
                  <SelectItem value="Impact, Charcoal, sans-serif">Impact</SelectItem>
                  <SelectItem value="'Lucida Console', Monaco, monospace">Lucida Console</SelectItem>
                  <SelectItem value="'Comic Sans MS', cursive">Comic Sans MS</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-1">
              <Label htmlFor="textAlignment" className="text-sm font-medium">Text Alignment</Label>
              <Select
                value={getTextAlignValue()}
                onValueChange={(value) => {
                  updateProperty('textAlign', value)
                  updateProperty('properties.textAlign', value)
                }}
              >
                <SelectTrigger id="textAlignment">
                  <SelectValue placeholder="Select text alignment" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="left">Align Left</SelectItem>
                  <SelectItem value="center">Align Center</SelectItem>
                  <SelectItem value="right">Align Right</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </GeometrySection>

        </>
      )}

      {/* Image and Special Elements Appearance */}
      {selectedElements.length > 0 && selectedElements.every(el =>
        el.type === ElementType.IMAGE
        || el.type === ElementType.FURNITURE
        || el.type === ElementType.FIXTURE
        || el.type === ElementType.LIGHT
        || el.type === ElementType.FLOOR_AREA
        || el.type === ElementType.WALL_PAINT
        || el.type === ElementType.WALL_PAPER
        || el.type === ElementType.OPENING
        || el.type === ElementType.APPLIANCE
        || el.type === ElementType.HANDRAIL
        || el.type === ElementType.ELECTRICAL_OUTLET
        || el.type === ElementType.ROOM_BOUNDARY,
      ) && (
        <GeometrySection type="appearance" columns={1}>
          {/* Image Upload Section - Only for IMAGE elements */}
          {selectedElements.length > 0 && selectedElements.every(el => el.type === ElementType.IMAGE) && (
            <div className="space-y-2">
              <Label htmlFor="imageUpload">Upload Image</Label>
              {/* 显示当前图片状态 */}
              <div className="text-sm text-muted-foreground">
                {(() => {
                  const element = selectedElements[0]
                  const src = element?.properties?.src
                  if (typeof src === 'string' && src !== '/public/icon/image.svg') {
                    return `Current: ${src.startsWith('data:') ? 'Custom uploaded image' : src}`
                  }
                  return 'No image uploaded. Click to select an image file.'
                })()}
              </div>
              <div className="flex items-center gap-2">
                <Input
                  id="imageUpload"
                  type="file"
                  accept="image/*"
                  className="flex-1"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) {
                      const reader = new FileReader()
                      reader.onload = (event) => {
                        const result = event.target?.result
                        if (typeof result === 'string') {
                          // 更新图片源
                          updateProperty('properties.src', result)

                          // 确保图片数据被保存到Store中
                          const element = selectedElements[0]
                          if (element?.id) {
                            // 1. 触发计算请求
                            appEventBus.publish({
                              type: AppEventType.ComputeRequest,
                              timestamp: Date.now(),
                              payload: {
                                shapeId: element.id,
                                operation: 'all',
                                source: 'AppearanceTab',
                              },
                            })

                            // 2. 触发数据更新事件，确保数据流向Store
                            const updatedProperties = {
                              ...element.properties,
                              src: result,
                            }

                            appEventBus.publish({
                              type: AppEventType.ShapeEditRequest,
                              timestamp: Date.now(),
                              payload: {
                                shapeId: element.id,
                                changes: {
                                  properties: updatedProperties,
                                },
                                source: 'AppearanceTab',
                              },
                            })

                            // 3. 获取当前所有形状并触发数据更新事件
                            const currentShapes = useShapesStore.getState().shapes
                            const updatedShapes = currentShapes.map(shape =>
                              shape.id === element.id
                                ? { ...shape, properties: { ...shape.properties, src: result } }
                                : shape,
                            )

                            appEventBus.publish({
                              type: AppEventType.DataUpdated,
                              timestamp: Date.now(),
                              payload: {
                                type: 'shapes',
                                data: updatedShapes,
                                source: 'AppearanceTab',
                              },
                            })

                            // 4. 触发存储请求，确保数据被持久化
                            setTimeout(() => {
                              const currentShapes = useShapesStore.getState().shapes

                              // 直接使用localStorage保存当前状态
                              localStorage.setItem('reno-pilot-shapes-storage', JSON.stringify({
                                shapes: currentShapes,
                                selectedShapeIds: useShapesStore.getState().selectedShapeIds,
                              }))

                              appEventBus.publish({
                                type: AppEventType.StorageSaveRequest,
                                timestamp: Date.now(),
                                payload: {
                                  operation: 'save',
                                  designId: 'current_design',
                                  data: {
                                    shapes: currentShapes,
                                    projectName: 'current_design',
                                  },
                                  source: 'AppearanceTab',
                                },
                              })
                            }, 500)
                          }
                        }
                      }
                      reader.readAsDataURL(file)
                    }
                  }}
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // 更新图片源为默认图片
                    updateProperty('properties.src', '/public/icon/image.svg')

                    // 确保图片数据被保存到Store中
                    const element = selectedElements[0]
                    if (element?.id) {
                      // 1. 触发计算请求
                      appEventBus.publish({
                        type: AppEventType.ComputeRequest,
                        timestamp: Date.now(),
                        payload: {
                          shapeId: element.id,
                          operation: 'all',
                          source: 'AppearanceTab',
                        },
                      })

                      // 2. 触发数据更新事件，确保数据流向Store
                      appEventBus.publish({
                        type: AppEventType.ShapeEditRequest,
                        timestamp: Date.now(),
                        payload: {
                          shapeId: element.id,
                          changes: {
                            properties: {
                              ...element.properties,
                              src: '/public/icon/image.svg',
                            },
                          },
                          source: 'AppearanceTab',
                        },
                      })

                      // 3. 触发存储请求，确保数据被持久化
                      setTimeout(() => {
                        const currentShapes = useShapesStore.getState().shapes

                        // 直接使用localStorage保存当前状态
                        localStorage.setItem('reno-pilot-shapes-storage', JSON.stringify({
                          shapes: currentShapes,
                          selectedShapeIds: useShapesStore.getState().selectedShapeIds,
                        }))

                        appEventBus.publish({
                          type: AppEventType.StorageSaveRequest,
                          timestamp: Date.now(),
                          payload: {
                            operation: 'save',
                            designId: 'current_design',
                            data: {
                              shapes: currentShapes,
                              projectName: 'current_design',
                            },
                            source: 'AppearanceTab',
                          },
                        })
                      }, 500)
                    }
                  }}
                >
                  Reset to Default
                </Button>
              </div>
            </div>
          )}

          {/* Opacity Control for all Image and Special Elements */}
          <ValueSlider
            label="Opacity"
            value={(() => {
              const opacity = getCommonValue('opacity')
              const propsOpacity = getCommonValue('properties.opacity')
              return Number(opacity ?? propsOpacity ?? 1)
            })()}
            onChange={(value) => {
              updateProperty('opacity', value)
              updateProperty('properties.opacity', value)
            }}
            min={0}
            max={1}
            step={0.01}
          />
        </GeometrySection>
      )}

      {/* Shape Elements Appearance */}
      {selectedElements.length > 0 && selectedElements.every(el =>
        el.type !== ElementType.IMAGE
        && el.type !== ElementType.TEXT
        && el.type !== ElementType.TEXT_LABEL
        && el.type !== ElementType.LINE
        && el.type !== ElementType.POLYLINE
        && el.type !== ElementType.ARC
        && el.type !== ElementType.QUADRATIC
        && el.type !== ElementType.CUBIC
        && !([ElementType.FURNITURE, ElementType.FIXTURE, ElementType.LIGHT, ElementType.FLOOR_AREA, ElementType.WALL_PAINT, ElementType.WALL_PAPER, ElementType.OPENING, ElementType.APPLIANCE, ElementType.HANDRAIL, ElementType.ELECTRICAL_OUTLET, ElementType.ROOM_BOUNDARY] as string[]).includes(el.type),
      ) && (
        <>
          {/* Color Controls */}
          <GeometrySection type="colors" columns={2}>
            <PropertyField
              label="Fill Color"
              type="color"
              value={getPatternBackgroundValue(commonPattern, isPatternActive, fillColorLocal)}
              onChange={(value) => {
                setFillColorLocal(value)
                updateProperty('fill', value)

                // Update pattern background if pattern is active
                if (selectedElements.length === 1) {
                  const el = selectedElements[0]
                  const pattern = el.pattern
                  if (pattern) {
                    const updatedPattern = { ...pattern }
                    if (updatedPattern.linesOptions)
                      updatedPattern.linesOptions.background = value
                    if (updatedPattern.circlesOptions)
                      updatedPattern.circlesOptions.background = value
                    if (updatedPattern.pathsOptions)
                      updatedPattern.pathsOptions.background = value
                    updateProperty('pattern', updatedPattern)
                  }
                }
              }}
            />
            <PropertyField
              label="Stroke Color"
              type="color"
              value={getValueForInput('stroke') || '#000000'}
              onChange={value => updateProperty('stroke', value)}
            />
          </GeometrySection>

          {/* Other Properties */}
          <GeometrySection type="appearance" columns={2}>
            <PropertyField
              label="Stroke Width"
              type="number"
              value={getValueForInput('strokeWidth')}
              onChange={value => updateProperty('strokeWidth', Number(value))}
              min={0}
              step={0.1}
              unit={UNIT_NAME}
            />
            <ValueSlider
              label="Opacity"
              value={(() => {
                const opacity = getCommonValue('opacity')
                return Number(opacity ?? 1)
              })()}
              onChange={value => updateProperty('opacity', value)}
              min={0}
              max={1}
              step={0.01}
              showInput={true}
            />
          </GeometrySection>
        </>
      )}

      {/* Path Elements Appearance */}
      {selectedElements.length > 0 && selectedElements.every(el =>
        el.type === ElementType.LINE
        || el.type === ElementType.POLYLINE
        || el.type === ElementType.ARC
        || el.type === ElementType.QUADRATIC
        || el.type === ElementType.CUBIC,
      ) && (
        <>
          {/* Color Controls */}
          <GeometrySection type="colors" columns={1}>
            <PropertyField
              label="Stroke Color"
              type="color"
              value={getValueForInput('stroke') || getValueForInput('properties.stroke') || '#000000'}
              onChange={(value) => {
                updateProperty('stroke', value)
                updateProperty('properties.stroke', value)
              }}
            />
          </GeometrySection>

          {/* Other Properties */}
          <GeometrySection type="appearance" columns={2}>
            <PropertyField
              label="Stroke Width"
              type="number"
              value={getValueForInput('strokeWidth')}
              onChange={(value) => {
                const numValue = Number(value)
                if (!Number.isNaN(numValue) && numValue >= 0) {
                  updateProperty('strokeWidth', numValue)
                  updateProperty('properties.strokeWidth', numValue)
                }
              }}
              min={0}
              step={0.1}
              unit={UNIT_NAME}
            />
            <ValueSlider
              label="Opacity"
              value={(() => {
                const opacity = getCommonValue('opacity')
                const propsOpacity = getCommonValue('properties.opacity')
                return Number(opacity ?? propsOpacity ?? 1)
              })()}
              onChange={(value) => {
                updateProperty('opacity', value)
                updateProperty('properties.opacity', value)
              }}
              min={0}
              max={1}
              step={0.01}
            />
          </GeometrySection>
        </>
      )}

      {/* Design Elements Appearance */}
      {selectedElements.length > 0 && selectedElements.every(el =>
        el.type === ElementType.WALL
        || el.type === ElementType.DOOR
        || el.type === ElementType.WINDOW
        || el.type === ElementType.ROOM,
      ) && (
        <GeometrySection type="appearance" columns={1}>
          <ValueSlider
            label="Opacity"
            value={(() => {
              const opacity = getCommonValue('opacity')
              const propsOpacity = getCommonValue('properties.opacity')
              return Number(opacity ?? propsOpacity ?? 1)
            })()}
            onChange={(value) => {
              updateProperty('opacity', value)
              updateProperty('properties.opacity', value)
            }}
            min={0}
            max={1}
            step={0.01}
          />
        </GeometrySection>
      )}

      {/* Pattern Configuration for Shape Elements */}
      {selectedElements.length > 0 && selectedElements.every(el =>
        el.type !== ElementType.IMAGE
        && el.type !== ElementType.TEXT
        && el.type !== ElementType.TEXT_LABEL
        && el.type !== ElementType.LINE
        && el.type !== ElementType.POLYLINE
        && el.type !== ElementType.ARC
        && el.type !== ElementType.QUADRATIC
        && el.type !== ElementType.CUBIC
        && !([ElementType.FURNITURE, ElementType.FIXTURE, ElementType.LIGHT, ElementType.FLOOR_AREA, ElementType.WALL_PAINT, ElementType.WALL_PAPER, ElementType.OPENING, ElementType.APPLIANCE, ElementType.HANDRAIL, ElementType.ELECTRICAL_OUTLET, ElementType.ROOM_BOUNDARY] as string[]).includes(el.type),
      ) && (
        <GeometrySection type="patterns" columns={1} title="Pattern Configuration">
          {/* Pattern Type Selection */}
          <div className="space-y-1">
            <Label htmlFor="patternType">Pattern Type</Label>
            <Select
              key={`pattern-select-${selectedElements[0]?.id}-${displayPatternSelectValue}-${localPatternSelectValue}-${currentPatternSelectValue}`}
              value={displayPatternSelectValue}
              onValueChange={(value: ResolvedPatternType) => {
                // 🔧 修复：使用handlePatternChangeInternal来处理所有情况
                handlePatternChangeInternal({ textureType: value === 'none' ? undefined : value })
              }}
              disabled={selectedElements.length === 0 || getCommonValue('pattern') === 'mixed'}
            >
              <SelectTrigger id="patternType">
                <SelectValue placeholder={getCommonValue('pattern') === 'mixed' ? 'Mixed Patterns' : 'Select Pattern'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None (Solid Color Fill)</SelectItem>
                {TEXTURE_TYPES.map((type: PatternDefinition['textureType'] & string) => (
                  <SelectItem key={type} value={type}>{type.charAt(0).toUpperCase() + type.slice(1)}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Pattern Options */}
          {isPatternActive && typeof commonPattern === 'object' && commonPattern !== null && (
            <div key={commonPattern.textureType || 'none'}>
              {renderPatternOptions()}
            </div>
          )}
        </GeometrySection>
      )}
    </div>
  )
}

export default AppearanceTab
