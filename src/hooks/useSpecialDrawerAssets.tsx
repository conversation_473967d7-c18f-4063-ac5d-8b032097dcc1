/**
 * Special Drawer Assets Hook
 *
 * This module provides a React hook for managing special assets in the drawer.
 * It handles predefined elements, architectural components, and provides
 * icon mapping and categorization for specialized design elements.
 *
 * @module hooks/useSpecialDrawerAssets
 */

import type { FixtureMountingType, PredefinedElement } from '@/types/core/element/definitions/predefinedElements'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
import {
  DoorOpen,
  Home,
  LayoutGrid,
  Palette,
  Square,
  Wallpaper,
} from 'lucide-react'
import React, { useCallback, useMemo } from 'react'
import { getIconForPredefinedElement } from '@/lib/utils/assetIconUtils.tsx'
import { predefinedElements } from '@/types/core/element/definitions/predefinedElements'
import { RoomType } from '@/types/core/element/design/roomDesignTypes'
import { ElementType as CoreElementType } from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'

// Copied from BottomAssetDrawer.tsx - consider moving to a shared types file
export interface DesignElement {
  name: string
  icon: React.ReactNode
  type: CoreElementType | string // Base type for getSettingsForType

  predefinedId?: string
  imagePath?: string
  majorCategory?: MajorCategory
  minorCategory?: MinorCategory
  mountingType?: FixtureMountingType
  defaultWidth?: number
  defaultDepth?: number
  defaultHeight?: number
  description?: string
  tags?: string[]
  attributes?: Record<string, unknown>
  predefinedElementData?: PredefinedElement

  openingType?: string
  doorType?: string
  windowType?: string

  subtypes?: unknown[]
  stepId?: string
}

export function useSpecialDrawerAssets(
  currentModuleId: MajorCategory | null | undefined,
  currentStepId: MinorCategory | null | undefined,
): DesignElement[] {
  const getSpecialElements = useCallback((): DesignElement[] => {
    const elements: DesignElement[] = []
    if (currentModuleId === null || currentModuleId === undefined || currentStepId === null || currentStepId === undefined) {
      return []
    }

    const currentMajorCat = currentModuleId
    const currentMinorCat = currentStepId

    predefinedElements
      .filter(pe => pe.majorCategory === currentMajorCat && pe.minorCategory === currentMinorCat)
      .forEach((pe) => {
        elements.push({
          name: pe.name,
          icon: getIconForPredefinedElement(pe),
          type: CoreElementType.IMAGE, // Assuming predefined elements are dragged as images or a generic type
          predefinedElementData: pe,
          stepId: currentMinorCat,
          predefinedId: pe.id,
          imagePath: pe.imagePath,
          majorCategory: pe.majorCategory,
          minorCategory: pe.minorCategory,
          mountingType: pe.mountingType,
          defaultWidth: pe.defaultWidth,
          defaultDepth: pe.defaultDepth,
          defaultHeight: pe.defaultHeight,
          description: pe.description,
          tags: pe.tags,
          attributes: pe.attributes,
        })
      })

    if (currentMajorCat === MajorCategory.BASE && currentMinorCat === 'architecture') {
      const specialArchitecturalAndFinishTools: DesignElement[] = [
        { name: 'Room Definition', icon: <Home className="h-5 w-5" />, type: CoreElementType.FLOOR_AREA, subtypes: Object.values(RoomType), stepId: currentMinorCat },
        { name: 'Wall Structure', icon: <LayoutGrid className="h-5 w-5" />, type: CoreElementType.WALL, stepId: currentMinorCat },
        { name: 'Door (Single Swing)', icon: <DoorOpen className="h-5 w-5" />, type: CoreElementType.OPENING, openingType: 'DOOR', doorType: 'HINGED_DOOR', stepId: currentMinorCat },
        { name: 'Door (Sliding)', icon: <DoorOpen className="h-5 w-5" />, type: CoreElementType.OPENING, openingType: 'DOOR', doorType: 'SLIDING_DOOR', stepId: currentMinorCat },
        { name: 'Window (Fixed)', icon: <Square className="h-5 w-5" />, type: CoreElementType.OPENING, openingType: 'WINDOW', windowType: 'FIXED_WINDOW', stepId: currentMinorCat },
        { name: 'Window (Sliding)', icon: <Square className="h-5 w-5" />, type: CoreElementType.OPENING, openingType: 'WINDOW', windowType: 'SLIDING_WINDOW', stepId: currentMinorCat },
        { name: 'Paint Can', icon: <Palette className="h-5 w-5" />, type: CoreElementType.WALL_PAINT, stepId: currentMinorCat },
        { name: 'Wallpaper Roll', icon: <Wallpaper className="h-5 w-5" />, type: CoreElementType.WALL_PAPER, stepId: currentMinorCat },
      ]
      elements.unshift(...specialArchitecturalAndFinishTools)
    }
    return elements
  }, [currentModuleId, currentStepId]) // getIconForPredefinedElement is stable if it's a pure function

  const specialElements = useMemo(() => {
    return getSpecialElements()
  }, [getSpecialElements])

  return specialElements
}
