/**
 * Utility Functions for Design Elements
 *
 * @remarks
 * This module provides a collection of utility functions specifically for working
 * with design elements such as Rooms and Walls (as defined by {@link ShapeModel}).
 * These utilities assist in calculations related to their geometric properties
 * (e.g., area of a room, length of a wall), cost estimations, and bounding boxes.
 *
 * The functions are exposed as static methods of the {@link DesignElementUtils} class
 * and are also re-exported directly and via the {@link DesignUtils} namespace object
 * for convenient access.
 *
 * Key functionalities include:
 * - `calculateRoomArea`: Computes the area of a room based on its boundary points.
 * - `calculateWallLength`: Calculates the length of a wall based on its start and end points.
 * - `calculateWallElementCost`: Estimates the cost of a wall given its dimensions and cost per square meter.
 * - `calculateWallBoundingBox`: Determines the bounding box of a wall element.
 *
 * @module lib/utils/element/designElementUtils
 * @see {@link ShapeModel}
 * @see {@link BoundingBox}
 */

import type { ShapeElement as ShapeModel } from '@/types/core/elementDefinitions' // Use type import

import { BoundingBoxClass as BoundingBox } from '../geometry/BoundingBoxClass' // Use BoundingBoxClass and alias
// Removed incorrect import for calculateBoundingBox as it's a static method of BoundingBox class
import { calculateArea } from '../geometry/polygonUtils' // Corrected path
// import type { PointData as IPoint } from '../../../types/core/element/geometry/point'; // Unused directly
import { points } from './elementUtils' // Corrected import path for points
// Removed import for calculateWallCost as it's not found and logic will be inlined.
// Removed unused imports: calculateFloorCost, calculateWallpaperRolls, calculateFlooringAmount, RoomType, calculateRoomLayout
// calculateFloorCost is not used in this file.
// calculateWallpaperRolls, calculateFlooringAmount are not used in this file.
// RoomType and calculateRoomLayout are not used in this file.

/**
 * Provides static utility methods for calculations related to design elements.
 */
export class DesignElementUtils {
  /**
   * Calculates the area of a room element.
   *
   * @remarks
   * This function computes the area of a room, assuming the room's boundary is defined
   * by a set of points in its `properties.points`. It uses the `points` utility
   * (from `./elementUtils`) to get these points (which are relative to the element's
   * `position`) and then delegates to the `calculateArea` utility (from `../geometry/polygonUtils`).
   *
   * @param element - The room element ({@link ShapeModel}) for which to calculate the area.
   *                  Expected to have `properties.points` defining its boundary.
   * @returns The calculated area of the room. Units depend on the units of the input points
   *          and the `calculateArea` utility's implementation (e.g., square meters if points are in meters).
   * @throws {@link Error} if the element is invalid, or if its `properties.points` are missing or insufficient (less than 3 points).
   *
   * @example
   * ```typescript
   * const roomElement = {
   *   id: 'room1',
   *   type: 'ROOM', // Assuming ElementType.ROOM
   *   position: { x: 0, y: 0 }, // Position of the room's origin
   *   properties: {
   *     points: [ { x: 0, y: 0 }, { x: 5000, y: 0 }, { x: 5000, y: 4000 }, { x: 0, y: 4000 } ] // Points in mm
   *   },
   *   // ... other ShapeModel properties
   * };
   * const area = DesignElementUtils.calculateRoomArea(roomElement);
   * // If calculateArea converts mm^2 to m^2, area might be 20.
   * ```
   */
  static calculateRoomArea(element: ShapeModel): number {
    if (!element?.properties || !element.position) {
      throw new Error('Valid room element must be provided')
    }

    if ('points' in element.properties && Array.isArray(element.properties.points)) {
      if (element.properties.points.length < 3) {
        throw new Error('Room must have at least 3 points to calculate area')
      }

      // 使用公共工具函数替代冗余实现
      // 原始函数: // 使用公共工具函数替代冗余实现
      // 原始函数: // 使用公共工具函数替代冗余实现
      // 原始函数: // 使用公共工具函数替代冗余实现
      // 原始函数: // 使用公共工具函数替代冗余实现
      // The `points` function from commonUtils (now elementUtils) already returns these relative points.
      // No need to manually add element.position to each point here.
      // 已移至: src/lib/utils/core/common/commonUtils.ts
      // Area calculation should use points relative to the element's origin.
      // The `points` function from commonUtils already returns these relative points.
      const relativePoints = points(element)
      if (!relativePoints || relativePoints.length < 3) {
        // This check might be redundant if points() already handles it or if the earlier check is sufficient.
        // However, keeping it for safety if points() could return less than 3 points for some reason.
        throw new Error('Room must have at least 3 points to calculate area after processing.')
      }
      return calculateArea(relativePoints)
    }
    // If properties are not points (e.g. rectangle width/height), this path might be taken.
    // However, the function is named calculateRoomArea, implying polygonal rooms.
    // For now, returning 0 if not a point-based shape that calculateArea can handle.
    // A more robust solution might involve type guards for different shape properties if this function is meant to be generic.
    console.warn('[DesignElementUtils.calculateRoomArea] Element does not have point properties for area calculation.')
    return 0
  }

  /**
   * Calculates the length of a wall element.
   *
   * @remarks
   * Assumes the wall's geometry is defined by `start` and `end` points within its
   * `properties`. These points are considered relative to the wall element's `position`.
   *
   * @param element - The wall element ({@link ShapeModel}) for which to calculate the length.
   *                  Expected to have `properties.start` and `properties.end` points.
   * @returns The length of the wall. Returns 0 if start/end points are not valid.
   *          Units depend on the units of the input points.
   */
  static calculateWallLength(element: ShapeModel): number {
    if (element.properties && 'start' in element.properties && 'end' in element.properties) {
      // Type assertion for start and end points
      const startPoint = element.properties.start as { x: number, y: number }
      const endPoint = element.properties.end as { x: number, y: number }

      const start = {
        x: startPoint.x + element.position.x,
        y: startPoint.y + element.position.y,
      }
      const end = {
        x: endPoint.x + element.position.x,
        y: endPoint.y + element.position.y,
      }

      const dx = end.x - start.x
      const dy = end.y - start.y
      return Math.sqrt(dx * dx + dy * dy)
    }
    return 0
  }

  /**
   * Calculates the estimated cost of a wall element.
   *
   * @param element - The wall element ({@link ShapeModel}).
   * @param wallHeight - The height of the wall (in units consistent with wall length and `costPerSquareMeter`).
   * @param costPerSquareMeter - The cost per square unit of wall area.
   * @returns The estimated cost of the wall. Returns 0 if length, height, or cost are invalid.
   */
  static calculateWallElementCost(
    element: ShapeModel,
    wallHeight: number,
    costPerSquareMeter: number,
  ): number {
    const length = DesignElementUtils.calculateWallLength(element)
    if (length === 0 || wallHeight <= 0 || costPerSquareMeter < 0) {
      return 0 // Or throw an error for invalid parameters
    }
    return length * wallHeight * costPerSquareMeter
  }

  /**
   * Calculates the bounding box of a wall element.
   *
   * @remarks
   * Assumes the wall's geometry is defined by `start` and `end` points within its
   * `properties`, relative to the wall element's `position`.
   *
   * @param element - The wall element ({@link ShapeModel}).
   * @returns A {@link BoundingBox} instance representing the wall's bounding box,
   *          or `null` if the start/end points are not valid.
   */
  static calculateWallBoundingBox(element: ShapeModel): BoundingBox | null {
    if (element.properties && 'start' in element.properties && 'end' in element.properties) {
      // Type assertion for start and end points
      const startPoint = element.properties.start as { x: number, y: number }
      const endPoint = element.properties.end as { x: number, y: number }

      const start = {
        x: startPoint.x + element.position.x,
        y: startPoint.y + element.position.y,
      }
      const end = {
        x: endPoint.x + element.position.x,
        y: endPoint.y + element.position.y,
      }

      const minX = Math.min(start.x, end.x)
      const minY = Math.min(start.y, end.y)
      const maxX = Math.max(start.x, end.x)
      const maxY = Math.max(start.y, end.y)

      return new BoundingBox(
        minX,
        minY,
        maxX - minX,
        maxY - minY,
      )
    }
    return null
  }
}

// Export all static methods for direct use
export const {
  calculateRoomArea,
  // calculateRoomWallLength, // Not defined in DesignElementUtils
  // calculateRoomCost, // Not defined in DesignElementUtils
  // calculateRoomMaterialRequirements, // Not defined in DesignElementUtils
  // calculateRoomFurnitureLayout, // Not defined in DesignElementUtils
  // calculateRoomBoundingBox, // Not defined in DesignElementUtils
  calculateWallLength,
  calculateWallElementCost,
  calculateWallBoundingBox,
} = DesignElementUtils

/**
 * Namespace object for convenient access to design element utility functions.
 *
 * @remarks
 * This object groups the static methods from {@link DesignElementUtils} under a
 * single export, allowing for usage like `DesignUtils.calculateRoomArea(...)`.
 */
export const DesignUtils = {
  // Room calculations
  calculateRoomArea,
  // calculateRoomWallLength, // Not defined in DesignElementUtils
  // calculateRoomCost, // Not defined in DesignElementUtils
  // calculateRoomMaterialRequirements, // Not defined in DesignElementUtils
  // calculateRoomFurnitureLayout, // Not defined in DesignElementUtils
  // calculateRoomBoundingBox, // Not defined in DesignElementUtils

  // Wall calculations
  calculateWallLength,
  calculateWallElementCost,
  calculateWallBoundingBox,
}
