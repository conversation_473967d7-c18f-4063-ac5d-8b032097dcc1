import { beforeEach, describe, expect, it } from 'vitest'
import { serviceFactory, ServiceRegistry } from '@/services/core/registry'

describe('serviceRegistry', () => {
  let registry: ServiceRegistry

  beforeEach(() => {
    registry = new ServiceRegistry()
  })

  describe('constructor', () => {
    it('should create an empty registry', () => {
      expect(registry).toBeDefined()
      expect(() => registry.getById('nonexistent')).toThrow()
    })
  })

  describe('register', () => {
    it('should register a service with an id', () => {
      const mockService = { name: 'test-service' }
      registry.register('test-id', mockService)

      const retrieved = registry.getById('test-id')
      expect(retrieved).toBe(mockService)
    })

    it('should overwrite existing service with same id', () => {
      const service1 = { name: 'service1' }
      const service2 = { name: 'service2' }

      registry.register('same-id', service1)
      registry.register('same-id', service2)

      const retrieved = registry.getById('same-id')
      expect(retrieved).toBe(service2)
    })

    it('should handle null services', () => {
      registry.register('null-service', null)

      expect(registry.getById('null-service')).toBeNull()
    })

    it('should handle undefined services differently', () => {
      // undefined services are treated as not registered
      registry.register('undefined-service', undefined)

      // This will throw because undefined is treated as not registered
      expect(() => registry.getById('undefined-service')).toThrow(
        'Service with id \'undefined-service\' not registered',
      )
    })
  })

  describe('getById', () => {
    it('should retrieve registered service', () => {
      const mockService = { test: true }
      registry.register('test', mockService)

      const result = registry.getById<typeof mockService>('test')
      expect(result).toBe(mockService)
      expect(result.test).toBe(true)
    })

    it('should throw error for unregistered service', () => {
      expect(() => registry.getById('nonexistent')).toThrow(
        'Service with id \'nonexistent\' not registered',
      )
    })

    it('should throw TypeError specifically', () => {
      expect(() => registry.getById('missing')).toThrow(TypeError)
    })

    it('should handle type casting correctly', () => {
      interface TestService {
        method: () => string
      }

      const mockService: TestService = {
        method: () => 'test',
      }

      registry.register('typed-service', mockService)
      const result = registry.getById<TestService>('typed-service')

      expect(result.method()).toBe('test')
    })
  })

  describe('service existence checking', () => {
    it('should be able to check if service exists by trying to get it', () => {
      registry.register('exists', {})

      // Service exists - should not throw
      expect(() => registry.getById('exists')).not.toThrow()

      // Service doesn't exist - should throw
      expect(() => registry.getById('does-not-exist')).toThrow()
    })
  })

  describe('service management', () => {
    it('should allow registering and retrieving services', () => {
      const service = { name: 'test-service' }
      registry.register('test', service)

      expect(registry.getById('test')).toBe(service)
    })

    it('should handle service replacement', () => {
      const service1 = { name: 'service1' }
      const service2 = { name: 'service2' }

      registry.register('test', service1)
      registry.register('test', service2)

      expect(registry.getById('test')).toBe(service2)
    })
  })

  describe('clear', () => {
    it('should remove all registered services', () => {
      registry.register('service1', {})
      registry.register('service2', {})
      registry.register('service3', {})

      // Services should exist before clear
      expect(() => registry.getById('service1')).not.toThrow()
      expect(() => registry.getById('service2')).not.toThrow()
      expect(() => registry.getById('service3')).not.toThrow()

      registry.clear()

      // Services should not exist after clear
      expect(() => registry.getById('service1')).toThrow()
      expect(() => registry.getById('service2')).toThrow()
      expect(() => registry.getById('service3')).toThrow()
    })

    it('should work on empty registry', () => {
      expect(() => registry.clear()).not.toThrow()
    })
  })

  describe('registry behavior', () => {
    it('should maintain service references correctly', () => {
      const service1 = { name: 'service1' }
      const service2 = { name: 'service2' }

      registry.register('id1', service1)
      registry.register('id2', service2)

      expect(registry.getById('id1')).toBe(service1)
      expect(registry.getById('id2')).toBe(service2)
    })

    it('should handle multiple registrations and retrievals', () => {
      for (let i = 0; i < 10; i++) {
        const service = { id: i, name: `service-${i}` }
        registry.register(`service-${i}`, service)
      }

      for (let i = 0; i < 10; i++) {
        const retrieved = registry.getById(`service-${i}`)
        expect(retrieved.id).toBe(i)
        expect(retrieved.name).toBe(`service-${i}`)
      }
    })
  })
})

describe('serviceFactory', () => {
  it('should have all required factory methods', () => {
    expect(typeof serviceFactory.createEventBus).toBe('function')
    expect(typeof serviceFactory.createLogger).toBe('function')
    expect(typeof serviceFactory.createErrorService).toBe('function')
    expect(typeof serviceFactory.createValidationService).toBe('function')
    expect(typeof serviceFactory.createElementCreationService).toBe('function')
    expect(typeof serviceFactory.createElementEditService).toBe('function')
  })

  describe('createLogger', () => {
    it('should create a logger service', () => {
      const logger = serviceFactory.createLogger()
      expect(logger).toBeDefined()
      expect(typeof logger.info).toBe('function')
      expect(typeof logger.warn).toBe('function')
      expect(typeof logger.error).toBe('function')
      expect(typeof logger.debug).toBe('function')
    })
  })

  describe('createEventBus', () => {
    it('should create an event bus', () => {
      const eventBus = serviceFactory.createEventBus()
      expect(eventBus).toBeDefined()
      expect(typeof eventBus.subscribe).toBe('function')
      expect(typeof eventBus.publish).toBe('function')
    })
  })
})
