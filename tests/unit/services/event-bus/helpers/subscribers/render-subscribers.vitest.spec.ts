import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  subscribeToCanvasClearEvents,
  subscribeToCanvasEvents,
  subscribeToCanvasResizeEvents,
  subscribeToLayerLockChangeEvents,
  subscribeToLayerOrderChangeEvents,
  subscribeToLayerVisibilityChangeEvents,
  subscribeToRenderEvents,
  subscribeToViewEvents,
} from '@/services/event-bus/helpers/subscribers/render-subscribers'
import * as utils from '@/services/event-bus/helpers/subscribers/utils'
import { AppEventType } from '@/types/services/events'

describe('render Subscribers', () => {
  let mockEventBus: any
  let mockHandler: any
  let mockOptions: any
  let typedSubscribeSpy: any

  beforeEach(() => {
    mockEventBus = {
      subscribe: vi.fn().mockReturnValue(() => {}),
    }
    mockHandler = vi.fn()
    mockOptions = { once: true }

    // Spy on the typedSubscribe function
    typedSubscribeSpy = vi.spyOn(utils, 'typedSubscribe').mockReturnValue(() => {})

    // Spy on the utility functions
    vi.spyOn(utils, 'getRenderEventType').mockImplementation((phase) => {
      switch (phase) {
        case 'trigger': return AppEventType.RENDER_TRIGGER
        case 'start': return AppEventType.RENDER_START
        case 'complete': return AppEventType.RENDER_COMPLETE
        case 'error': return AppEventType.RENDER_ERROR
        default: throw new Error(`Invalid phase: ${phase}`)
      }
    })
  })

  describe('subscribeToRenderEvents', () => {
    it('should call getRenderEventType and typedSubscribe with correct parameters', () => {
      const phase = 'trigger'
      const unsubscribe = subscribeToRenderEvents(
        mockEventBus,
        mockHandler,
        phase,
        mockOptions,
      )

      expect(utils.getRenderEventType).toHaveBeenCalledWith(phase)
      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.RENDER_TRIGGER,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should work with different phases', () => {
      const phases: ('trigger' | 'start' | 'complete' | 'error')[] = ['start', 'complete', 'error']

      phases.forEach((phase) => {
        subscribeToRenderEvents(mockEventBus, mockHandler, phase, mockOptions)
        expect(utils.getRenderEventType).toHaveBeenCalledWith(phase)
      })
    })
  })

  describe('subscribeToLayerVisibilityChangeEvents', () => {
    it('should call typedSubscribe with correct parameters', () => {
      const unsubscribe = subscribeToLayerVisibilityChangeEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.LAYER_VISIBILITY_CHANGE,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('subscribeToLayerLockChangeEvents', () => {
    it('should call typedSubscribe with correct parameters', () => {
      const unsubscribe = subscribeToLayerLockChangeEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.LAYER_LOCK_CHANGE,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('subscribeToLayerOrderChangeEvents', () => {
    it('should call typedSubscribe with correct parameters', () => {
      const unsubscribe = subscribeToLayerOrderChangeEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.LAYER_ORDER_CHANGE,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('subscribeToCanvasEvents', () => {
    it('should call typedSubscribe with correct parameters for mousemove event', () => {
      const unsubscribe = subscribeToCanvasEvents(
        mockEventBus,
        mockHandler,
        'mousemove',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.CANVAS_MOUSE_MOVE,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for mousedown event', () => {
      const unsubscribe = subscribeToCanvasEvents(
        mockEventBus,
        mockHandler,
        'mousedown',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.CANVAS_MOUSE_DOWN,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for mouseup event', () => {
      const unsubscribe = subscribeToCanvasEvents(
        mockEventBus,
        mockHandler,
        'mouseup',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.CANVAS_MOUSE_UP,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for click event', () => {
      const unsubscribe = subscribeToCanvasEvents(
        mockEventBus,
        mockHandler,
        'click',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.CANVAS_CLICK,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for dblclick event', () => {
      const unsubscribe = subscribeToCanvasEvents(
        mockEventBus,
        mockHandler,
        'dblclick',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.CANVAS_DBLCLICK,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should throw error for invalid event type', () => {
      expect(() => subscribeToCanvasEvents(
        mockEventBus,
        mockHandler,
        'invalid' as any,
        mockOptions,
      )).toThrow('Invalid canvas event type: invalid')
    })
  })

  describe('subscribeToViewEvents', () => {
    it('should call typedSubscribe with correct parameters for zoom event', () => {
      const unsubscribe = subscribeToViewEvents(
        mockEventBus,
        mockHandler,
        'zoom',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.VIEW_ZOOMED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for pan event', () => {
      const unsubscribe = subscribeToViewEvents(
        mockEventBus,
        mockHandler,
        'pan',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.VIEW_PANNED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for reset event', () => {
      const unsubscribe = subscribeToViewEvents(
        mockEventBus,
        mockHandler,
        'reset',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.VIEW_RESET,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should throw error for invalid event type', () => {
      expect(() => subscribeToViewEvents(
        mockEventBus,
        mockHandler,
        'invalid' as any,
        mockOptions,
      )).toThrow('Invalid view event type: invalid')
    })
  })

  describe('subscribeToCanvasResizeEvents', () => {
    it('should call typedSubscribe with correct parameters', () => {
      const unsubscribe = subscribeToCanvasResizeEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.CANVAS_RESIZED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('subscribeToCanvasClearEvents', () => {
    it('should call typedSubscribe with correct parameters', () => {
      const unsubscribe = subscribeToCanvasClearEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.CANVAS_CLEARED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })
})
