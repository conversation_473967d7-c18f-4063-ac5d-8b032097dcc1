import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import * as eventHandlersModule from '@/services/event-bus/handlers/EventHandlers'
// Now import the mocked modules
import * as eventBusModule from '@/services/event-bus/index'

import { cleanupEventSystem, initializeEventSystem } from '@/services/event-bus/initialize'
import { AppEventType } from '@/types/services/events'

// Mock the modules before importing
vi.mock('@/services/event-bus/index', () => {
  const mockEventBus = {
    reset: vi.fn(),
    subscribe: vi.fn(),
    publish: vi.fn(),
    unsubscribe: vi.fn(),
    getSubscriptionCount: vi.fn(),
    setConfig: vi.fn(),
    getConfig: vi.fn(),
  }

  return {
    getEventBus: vi.fn().mockReturnValue(mockEventBus),
    appEventBus: mockEventBus,
  }
})

vi.mock('@/services/event-bus/handlers/EventHandlers', () => ({
  setupEventHandlers: vi.fn(),
}))

describe('event System Initialization', () => {
  let mockEventBus: any
  let originalConsoleLog: any
  let originalNodeEnv: string | undefined

  beforeEach(() => {
    // Save original console.log
    originalConsoleLog = console.log
    console.log = vi.fn()

    // Save original NODE_ENV
    originalNodeEnv = process.env.NODE_ENV

    // Get the mock event bus from the mocked module
    mockEventBus = eventBusModule.appEventBus;

    // Set up getEventBus to return our mock
    (eventBusModule.getEventBus as jest.Mock).mockReturnValue(mockEventBus)
  })

  afterEach(() => {
    // Restore console.log
    console.log = originalConsoleLog

    // Restore NODE_ENV
    process.env.NODE_ENV = originalNodeEnv

    // Clear all mocks
    vi.clearAllMocks()
  })

  describe('initializeEventSystem', () => {
    it('should reset the event bus', () => {
      initializeEventSystem()

      expect(mockEventBus.reset).toHaveBeenCalled()
    })

    it('should set up event handlers', () => {
      initializeEventSystem()

      expect(eventHandlersModule.setupEventHandlers).toHaveBeenCalled()
    })

    it('should log initialization messages', () => {
      initializeEventSystem()

      expect(console.log).toHaveBeenCalledWith('Initializing event system...')
      expect(console.log).toHaveBeenCalledWith('Event system initialized with CoreCoordinator integration')
    })

    it('should set up development logging in development mode', () => {
      // Set NODE_ENV to development
      process.env.NODE_ENV = 'development'

      initializeEventSystem()

      // Should subscribe to all event types
      expect(mockEventBus.subscribe).toHaveBeenCalled()
    })

    it('should not set up development logging in production mode', () => {
      // Set NODE_ENV to production
      process.env.NODE_ENV = 'production'

      initializeEventSystem()

      // Should not subscribe to any event types
      expect(mockEventBus.subscribe).not.toHaveBeenCalled()
    })
  })

  describe('cleanupEventSystem', () => {
    it('should reset the event bus', () => {
      cleanupEventSystem()

      expect(mockEventBus.reset).toHaveBeenCalled()
    })

    it('should log cleanup messages', () => {
      cleanupEventSystem()

      expect(console.log).toHaveBeenCalledWith('Cleaning up event system...')
      expect(console.log).toHaveBeenCalledWith('Event system cleaned up')
    })
  })

  describe('setupDevelopmentLogging (private function)', () => {
    it('should subscribe to all event types in development mode', () => {
      // Set NODE_ENV to development
      process.env.NODE_ENV = 'development'

      // Call initializeEventSystem which will call setupDevelopmentLogging internally
      initializeEventSystem()

      // Should have called subscribe for each event type
      const eventTypeCount = Object.values(AppEventType).length
      expect(mockEventBus.subscribe).toHaveBeenCalledTimes(eventTypeCount)

      // Verify the options passed to subscribe
      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(Function),
        { priority: -100 },
      )
    })

    it('should log events when the subscription handler is called', () => {
      // Set NODE_ENV to development
      process.env.NODE_ENV = 'development'

      // Call initializeEventSystem which will call setupDevelopmentLogging internally
      initializeEventSystem()

      // Get the subscription handler (the second argument to subscribe)
      const subscribeCall = (mockEventBus.subscribe as jest.Mock).mock.calls[0]
      const handler = subscribeCall[1]

      // Create a mock event
      const mockEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle' },
      }

      // Call the handler with the mock event
      handler(mockEvent)

      // Verify that console.log was called with the event details
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('[EVENT]'),
        expect.stringContaining(AppEventType.SHAPE_CREATE_REQUEST),
        expect.objectContaining({ ElementType: 'rectangle' }),
      )
    })

    it('should not log events with sensitive data', () => {
      // Set NODE_ENV to development
      process.env.NODE_ENV = 'development'

      // Call initializeEventSystem which will call setupDevelopmentLogging internally
      initializeEventSystem()

      // Get the subscription handler (the second argument to subscribe)
      const subscribeCall = (mockEventBus.subscribe as jest.Mock).mock.calls[0]
      const handler = subscribeCall[1]

      // Create a mock event with sensitive data
      const mockEvent = {
        type: AppEventType.USER_LOGIN,
        payload: { username: 'user', password: process.env.ENV_VARIABLE },
      }

      // Call the handler with the mock event
      handler(mockEvent)

      // Verify that console.log was called but password was redacted
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('[EVENT]'),
        expect.stringContaining(AppEventType.USER_LOGIN),
        expect.objectContaining({
          username: 'user',
          password: process.env.ENV_VARIABLE,
        }),
      )
    })
  })

  describe('event system configuration', () => {
    it('should configure the event bus with default settings', () => {
      initializeEventSystem()

      expect(mockEventBus.setConfig).toHaveBeenCalledWith(
        expect.objectContaining({
          defaultDebounceTime: expect.any(Number),
          defaultThrottleTime: expect.any(Number),
          validateEvents: true,
        }),
      )
    })

    it('should allow custom configuration', () => {
      const customConfig = {
        defaultDebounceTime: 500,
        defaultThrottleTime: 300,
        validateEvents: false,
      }

      initializeEventSystem(customConfig)

      expect(mockEventBus.setConfig).toHaveBeenCalledWith(
        expect.objectContaining(customConfig),
      )
    })

    it('should merge custom configuration with defaults', () => {
      const partialConfig = {
        defaultDebounceTime: 500,
      }

      initializeEventSystem(partialConfig)

      expect(mockEventBus.setConfig).toHaveBeenCalledWith(
        expect.objectContaining({
          defaultDebounceTime: 500,
          defaultThrottleTime: expect.any(Number),
          validateEvents: true,
        }),
      )
    })
  })

  describe('integration with event handlers', () => {
    it('should pass the event bus to setupEventHandlers', () => {
      initializeEventSystem()

      expect(eventHandlersModule.setupEventHandlers).toHaveBeenCalledWith(
        mockEventBus,
      )
    })

    it('should handle errors from setupEventHandlers', () => {
      // Mock setupEventHandlers to throw an error
      (eventHandlersModule.setupEventHandlers as jest.Mock).mockImplementationOnce(() => {
        throw new Error('Handler setup error')
      })

      // Should not throw
      expect(() => initializeEventSystem()).not.toThrow()

      // Should log the error
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Error setting up event handlers'),
        expect.any(Error),
      )
    })
  })

  describe('system state', () => {
    it('should publish system ready event after initialization', () => {
      initializeEventSystem()

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SYSTEM_READY,
        payload: {
          timestamp: expect.any(Number),
        },
      })
    })

    it('should publish system shutdown event during cleanup', () => {
      cleanupEventSystem()

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SYSTEM_SHUTDOWN,
        payload: {
          timestamp: expect.any(Number),
        },
      })
    })
  })
})
