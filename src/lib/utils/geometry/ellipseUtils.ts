/**
 * Utility Functions for Ellipse and Circle Geometry
 *
 * @remarks
 * This module provides utility functions for calculating geometric properties of
 * ellipses and circles, such as their area, perimeter (circumference for circles),
 * and bounding boxes. Circles are often treated as a special case of ellipses.
 *
 * It uses the {@link IPoint} (aliased from `PointData`) type for point representations
 * and {@link BoundingBoxClass} for bounding box results.
 *
 * @module lib/utils/geometry/ellipseUtils
 */

import type { PointData as IPoint } from '../../../types/core/element/geometry/point' // Use PointData and alias
import { BoundingBoxClass } from './BoundingBoxClass' // Import BoundingBoxClass
// import { PointClass } from './PointClass';

// === Circle Functions ===

/**
 * Calculates the area of a circle.
 * @param radius - The radius of the circle. Must be a non-negative number.
 * @returns The area of the circle. Returns `NaN` if the radius is negative or not a number.
 */
export function calculateCircleArea(radius: number): number {
  if (typeof radius !== 'number' || isNaN(radius) || radius < 0) {
    console.warn('calculateCircleArea: Radius must be a non-negative number. Returning NaN.')
    return Number.NaN
  }
  return Math.PI * radius * radius
}

/**
 * Calculates the perimeter (circumference) of a circle.
 * @param radius - The radius of the circle. Can be any number (absolute value is used).
 * @returns The circumference of the circle. Returns `NaN` if the radius is not a valid number.
 */
export function calculateCirclePerimeter(radius: number): number {
  if (typeof radius !== 'number' || isNaN(radius)) {
    console.warn('calculateCirclePerimeter: Radius must be a valid number. Returning NaN.')
    return Number.NaN
  }
  const absRadius = Math.abs(radius)
  return 2 * Math.PI * absRadius
}

/**
 * Calculates the axis-aligned bounding box of a circle.
 * @param center - The center {@link IPoint} of the circle.
 * @param radius - The radius of the circle. Must be a non-negative number.
 * @returns A {@link BoundingBoxClass} instance. Returns a 0x0 BBox at origin if parameters are invalid.
 */
export function calculateCircleBoundingBox(center: IPoint, radius: number): BoundingBoxClass {
  if (!center || typeof center.x !== 'number' || typeof center.y !== 'number'
    || typeof radius !== 'number' || isNaN(radius) || radius < 0) { // Added radius < 0 check
    console.warn('calculateCircleBoundingBox: Invalid parameters. Returning 0x0 BBox at origin.')
    return new BoundingBoxClass(0, 0, 0, 0)
  }
  const absRadius = Math.abs(radius)
  return new BoundingBoxClass(center.x - absRadius, center.y - absRadius, 2 * absRadius, 2 * absRadius)
}

// === Ellipse Functions ===

/**
 * Calculates the area of an ellipse.
 * @param radiusX - The radius of the ellipse along the x-axis (semi-major or semi-minor axis). Must be non-negative.
 * @param radiusY - The radius of the ellipse along the y-axis (semi-major or semi-minor axis). Must be non-negative.
 * @returns The area of the ellipse. Returns `NaN` if either radius is negative or not a number.
 */
export function calculateEllipseArea(radiusX: number, radiusY: number): number {
  if (typeof radiusX !== 'number' || typeof radiusY !== 'number' || isNaN(radiusX) || isNaN(radiusY) || radiusX < 0 || radiusY < 0) {
    console.warn('calculateEllipseArea: Radii must be non-negative numbers. Returning NaN.')
    return Number.NaN
  }
  return Math.PI * radiusX * radiusY
}

/**
 * Calculates the approximate perimeter (circumference) of an ellipse using Ramanujan's second approximation.
 *
 * @remarks
 * Ramanujan's approximation: P ≈ π [ (a+b) + (3(a-b)² / (10(a+b) + √(a²+14ab+b²))) ]
 * Simplified form used: P ≈ π(a+b)(1 + 3h / (10 + √(4-3h))) where h = ((a-b)/(a+b))²
 * This provides a good balance of accuracy and computational simplicity.
 *
 * @param radiusX - The radius of the ellipse along one axis (e.g., semi-major). Must be non-negative.
 * @param radiusY - The radius of the ellipse along the other axis (e.g., semi-minor). Must be non-negative.
 * @returns The approximate perimeter of the ellipse. Returns `NaN` if radii are negative or not numbers.
 *          Returns 0 if both radii are 0. Returns `2 * max(radiusX, radiusY)` if one radius is 0 (a line).
 */
export function calculateEllipsePerimeter(radiusX: number, radiusY: number): number {
  if (typeof radiusX !== 'number' || typeof radiusY !== 'number' || isNaN(radiusX) || isNaN(radiusY) || radiusX < 0 || radiusY < 0) {
    console.warn('calculateEllipsePerimeter: Radii must be non-negative numbers. Returning NaN.')
    return Number.NaN
  }
  if (radiusX === 0 && radiusY === 0)
    return 0

  const a = Math.max(radiusX, radiusY)
  const b = Math.min(radiusX, radiusY)

  if (b === 0) { // Line ellipse
    return 2 * a
  }

  const h = ((a - b) / (a + b)) ** 2
  // Ramanujan's approximation
  return Math.PI * (a + b) * (1 + (3 * h) / (10 + Math.sqrt(Math.max(0, 4 - 3 * h))))
}

/**
 * Calculates the axis-aligned bounding box of an ellipse.
 *
 * @remarks
 * If `radiusX` and `radiusY` are equal, it delegates to {@link calculateCircleBoundingBox}.
 *
 * @param center - The center {@link IPoint} of the ellipse.
 * @param radiusX - The radius of the ellipse along the x-axis. Must be non-negative.
 * @param radiusY - The radius of the ellipse along the y-axis. Must be non-negative.
 * @returns A {@link BoundingBoxClass} instance. Returns a 0x0 BBox at origin if parameters are invalid.
 */
export function calculateEllipseBoundingBox(center: IPoint, radiusX: number, radiusY: number): BoundingBoxClass {
  if (!center || typeof center.x !== 'number' || typeof center.y !== 'number'
    || typeof radiusX !== 'number' || typeof radiusY !== 'number'
    || isNaN(radiusX) || isNaN(radiusY) || radiusX < 0 || radiusY < 0) {
    console.warn('calculateEllipseBoundingBox: Invalid parameters. Returning 0x0 BBox at origin.')
    return new BoundingBoxClass(0, 0, 0, 0)
  }
  const absRadiusX = Math.abs(radiusX) // Already done in commonUtils, but good to ensure here too
  const absRadiusY = Math.abs(radiusY)
  if (absRadiusX === absRadiusY) { // If it's a circle, delegate
    return calculateCircleBoundingBox(center, absRadiusX)
  }
  return new BoundingBoxClass(center.x - absRadiusX, center.y - absRadiusY, 2 * absRadiusX, 2 * absRadiusY)
}
