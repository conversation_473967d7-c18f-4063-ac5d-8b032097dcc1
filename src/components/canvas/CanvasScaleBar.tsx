/**
 * Canvas Scale Bar Component
 *
 * A dynamic scale bar component that displays the current scale of the canvas
 * with automatic unit conversion and responsive positioning. The scale bar
 * adapts to different zoom levels and provides visual reference for measurements.
 *
 * Features:
 * - Automatic unit conversion (mm, cm, m) based on scale
 * - Responsive positioning with smooth animations
 * - SVG-based rendering for crisp display at any zoom level
 * - Adaptive sizing based on canvas dimensions
 * - Support for multiple positioning modes (left, right, sidebar)
 * - Smooth transitions when repositioning
 *
 * @example
 * ```tsx
 * <CanvasScaleBar
 *   zoom={1.5}
 *   pixelsPerMM={0.08}
 *   canvasWidth={800}
 *   position="right"
 *   offset={{ x: 20, y: 20 }}
 * />
 * ```
 */

import React from 'react'
import { cn } from '@/lib/utils'
import { calculateScaleBarInfo } from '@/lib/utils/canvas/scaleBarUtils'

/**
 * Props for the internal ScaleBar component
 */
interface ScaleBarProps {
  /** Current zoom level of the canvas */
  zoom: number
  /** Conversion factor from pixels to millimeters */
  pixelsPerMM: number
  /** Width of the canvas in pixels */
  canvasWidth: number
  // canvasHeight: number // Unused
}

/**
 * Internal ScaleBar component that renders the actual scale bar SVG.
 *
 * This component creates an SVG-based scale bar with main bar, end ticks,
 * and optional middle tick for longer bars. It automatically calculates
 * the appropriate scale and units based on the current zoom level.
 *
 * @param props - The component props
 * @param props.zoom - Current zoom level of the canvas
 * @param props.pixelsPerMM - Conversion factor from pixels to millimeters
 * @param props.canvasWidth - Width of the canvas in pixels
 * @returns The rendered scale bar SVG with label
 */
const ScaleBar: React.FC<ScaleBarProps> = ({
  zoom,
  pixelsPerMM,
  canvasWidth,
  // canvasHeight, // Unused
}: ScaleBarProps) => {
  const { pixelLength, unitValue, unit } = calculateScaleBarInfo(zoom, pixelsPerMM, canvasWidth)

  // SVG tick/bar parameters
  const TICK_WIDTH = 2
  const SVG_HEIGHT = 16
  const BAR_Y = 8
  const BAR_STROKE = 3
  const TICK_Y1 = 3
  const TICK_Y2 = 13
  const MID_TICK_Y1 = 5
  const MID_TICK_Y2 = 11
  const MID_TICK_STROKE = 1.5
  const COLOR = '#222'

  return (
    <div className="flex flex-col items-center w-full" style={{ width: `${pixelLength + 2 * TICK_WIDTH}px` }}>
      <svg
        width={pixelLength + 2 * TICK_WIDTH}
        height={SVG_HEIGHT}
        style={{ display: 'block' }}
      >
        {/* Main bar */}
        <line
          x1={TICK_WIDTH}
          x2={pixelLength + TICK_WIDTH}
          y1={BAR_Y}
          y2={BAR_Y}
          stroke={COLOR}
          strokeWidth={BAR_STROKE}
          strokeLinecap="round"
        />
        {/* Left tick */}
        <line
          x1={TICK_WIDTH}
          x2={TICK_WIDTH}
          y1={TICK_Y1}
          y2={TICK_Y2}
          stroke={COLOR}
          strokeWidth={TICK_WIDTH}
        />
        {/* Right tick */}
        <line
          x1={pixelLength + TICK_WIDTH}
          x2={pixelLength + TICK_WIDTH}
          y1={TICK_Y1}
          y2={TICK_Y2}
          stroke={COLOR}
          strokeWidth={TICK_WIDTH}
        />
        {/* Middle tick */}
        {pixelLength > 40 && (
          <line
            x1={pixelLength / 2 + TICK_WIDTH}
            x2={pixelLength / 2 + TICK_WIDTH}
            y1={MID_TICK_Y1}
            y2={MID_TICK_Y2}
            stroke={COLOR}
            strokeWidth={MID_TICK_STROKE}
          />
        )}
      </svg>
      <div
        className="w-full text-xs text-center mt-1 font-medium text-black/80 flex justify-center"
        style={{ textShadow: '0px 1px 1px rgba(255,255,255,0.4)', width: `${pixelLength + 2 * TICK_WIDTH}px` }}
      >
        <span>
          {unitValue}
          {' '}
          {unit}
        </span>
      </div>
    </div>
  )
}

/**
 * Props for the main CanvasScaleBar component
 */
interface CanvasScaleBarProps {
  /** Current zoom level of the canvas */
  zoom: number
  /** Conversion factor from pixels to millimeters */
  pixelsPerMM: number
  /** Width of the canvas in pixels */
  canvasWidth: number
  // canvasHeight: number // Unused
  /** Positioning mode for the scale bar */
  position?: 'left' | 'right' | 'sidebar'
  /** Absolute positioning offset from the specified position */
  offset?: { x: number, y: number }
}

// Animation duration is handled by CSS transition

/**
 * CanvasScaleBar component that displays a dynamic scale reference on the canvas.
 *
 * This component automatically positions itself based on the specified position mode
 * and adapts to zoom level changes. It provides smooth animations when repositioning
 * and automatically adjusts its scale representation based on the current view.
 *
 * The scale bar is particularly useful for design applications where accurate
 * measurements are important, providing immediate visual feedback about the
 * current scale of the canvas.
 *
 * @param props - The component props
 * @param props.zoom - Current zoom level of the canvas
 * @param props.pixelsPerMM - Conversion factor for unit calculations
 * @param props.canvasWidth - Canvas width for scale calculations
 * @param props.position - Where to position the scale bar (default: 'right')
 * @param props.offset - Optional absolute positioning offset
 * @returns The rendered scale bar component
 */
const CanvasScaleBar: React.FC<CanvasScaleBarProps> = ({
  zoom,
  pixelsPerMM,
  canvasWidth,
  position = 'right',
  offset,
}: CanvasScaleBarProps) => {
  // Positioning logic is now handled by parent (foreignObject in Canvas)
  // Animate the scale bar's own content for smoothness
  let justify = 'flex-end'
  if (position === 'left') {
    justify = 'flex-start'
  }
  if (position === 'sidebar') {
    justify = 'flex-end'
  }

  // Calculate the scale bar info to determine its actual width
  const { pixelLength } = calculateScaleBarInfo(zoom, pixelsPerMM, canvasWidth)
  const actualWidth = pixelLength + 2 * 2 // Add TICK_WIDTH (2) on both sides

  // Absolute positioning style
  const absStyle: React.CSSProperties = offset !== undefined
    ? {
        position: 'absolute',
        left: offset.x,
        top: offset.y,
        zIndex: 45, // 在左右边栏(z-50/z-60)之后，画布网格之前
        pointerEvents: 'none', // 修复：不阻挡Canvas的鼠标事件
        transition: 'left 0.3s cubic-bezier(0.4,0,0.2,1), width 0.3s cubic-bezier(0.4,0,0.2,1)', // Apply transition whenever position changes
        width: `${actualWidth}px`,
        height: 'auto',
        display: 'flex',
        justifyContent: justify,
        alignItems: 'flex-end',
        // 移除maxWidth和overflow限制，允许比例尺完全显示
      }
    : {
        width: '100%',
        height: '100%',
        justifyContent: justify,
        display: 'flex',
        // 移除maxWidth和overflow限制
      }

  return (
    <div
      className={cn(
        // 'transition-all duration-300 ease-in-out',
      )}
      style={absStyle}
    >
      <div className="flex-shrink-0">
        <div className="w-auto">
          <ScaleBar
            zoom={zoom}
            pixelsPerMM={pixelsPerMM}
            canvasWidth={canvasWidth}
          />
        </div>
      </div>
    </div>
  )
}

export default CanvasScaleBar
