import type { LoggerService } from '@/types/services/logging'
import { describe, expect, it, vi } from 'vitest'

describe('logging Types', () => {
  describe('loggerService', () => {
    it('should define the required methods', () => {
      // Create a mock implementation of LoggerService to verify the interface
      const mockLoggerService: LoggerService = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
      }

      expect(typeof mockLoggerService.info).toBe('function')
      expect(typeof mockLoggerService.warn).toBe('function')
      expect(typeof mockLoggerService.error).toBe('function')
      expect(typeof mockLoggerService.debug).toBe('function')
    })

    it('should allow calling info method with parameters', () => {
      const mockLoggerService: LoggerService = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
      }

      mockLoggerService.info('Test info message', { data: 123 })
      expect(mockLoggerService.info).toHaveBeenCalledWith('Test info message', { data: 123 })
    })

    it('should allow calling warn method with parameters', () => {
      const mockLoggerService: LoggerService = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
      }

      mockLoggerService.warn('Test warning message', { data: 456 })
      expect(mockLoggerService.warn).toHaveBeenCalledWith('Test warning message', { data: 456 })
    })

    it('should allow calling error method with parameters', () => {
      const mockLoggerService: LoggerService = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
      }

      const error = new Error('Test error')
      mockLoggerService.error('Test error message', error)
      expect(mockLoggerService.error).toHaveBeenCalledWith('Test error message', error)
    })

    it('should allow debug method to be optional', () => {
      const mockLoggerService: LoggerService = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        // debug is optional and not defined here
      }

      expect(mockLoggerService.debug).toBeUndefined()
      expect(typeof mockLoggerService.info).toBe('function')
      expect(typeof mockLoggerService.warn).toBe('function')
      expect(typeof mockLoggerService.error).toBe('function')
    })

    it('should allow calling debug method when defined', () => {
      const mockLoggerService: LoggerService = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
      }

      if (mockLoggerService.debug) {
        mockLoggerService.debug('Test debug message', { data: 789 })
        expect(mockLoggerService.debug).toHaveBeenCalledWith('Test debug message', { data: 789 })
      }
    })
  })
})
