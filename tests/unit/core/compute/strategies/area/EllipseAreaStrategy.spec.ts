import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { EllipseAreaStrategy } from '@/core/compute/strategies/area/EllipseAreaStrategy'
import { Ellipse } from '@/types/core/element/shape/ellipseShapeTypes'

// Mock Ellipse class
class MockEllipse {
  properties = {
    type: 'ellipse',
    radiusX: 0,
    radiusY: 0,
  }

  id = 'mock-ellipse'

  constructor(radiusX: number, radiusY: number) {
    this.properties.radiusX = radiusX
    this.properties.radiusY = radiusY
  }

  getSubType() {
    return 'ellipse'
  }

  getRadiusX() {
    return this.properties.radiusX
  }

  getRadiusY() {
    return this.properties.radiusY
  }
}

// Mock non-ellipse element
class MockRectangle {
  id = 'mock-rectangle'
  getSubType() {
    return 'rectangle'
  }
}

describe('ellipseAreaStrategy', () => {
  let strategy: EllipseAreaStrategy

  beforeEach(() => {
    strategy = new EllipseAreaStrategy()
    // Spy on console methods
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(console, 'debug').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('ellipse')
  })

  it('should calculate area correctly', () => {
    const radiusX = 5
    const radiusY = 3
    const ellipse = new MockEllipse(radiusX, radiusY)

    const area = strategy.calculateArea(ellipse as any)

    // Ellipse area = π * rx * ry
    expect(area).toBeCloseTo(Math.PI * radiusX * radiusY)
  })

  it('should handle zero radiusX', () => {
    const ellipse = new MockEllipse(0, 5)
    const area = strategy.calculateArea(ellipse as any)

    expect(area).toBe(0)
  })

  it('should handle zero radiusY', () => {
    const ellipse = new MockEllipse(5, 0)
    const area = strategy.calculateArea(ellipse as any)

    expect(area).toBe(0)
  })

  it('should handle negative radius', () => {
    const ellipse = new MockEllipse(-5, 3)
    const area = strategy.calculateArea(ellipse as any)

    expect(area).toBe(0)
  })

  it('should throw error if element is not an ellipse', () => {
    const rectangle = new MockRectangle()

    expect(() => strategy.calculateArea(rectangle as any)).toThrow('EllipseAreaStrategy can only calculate area for Ellipse elements')
  })

  it('should handle real Ellipse instance', () => {
    // Create a mock that looks like a real Ellipse instance
    const realEllipse = {
      id: 'real-ellipse-id',
      constructor: { name: 'Ellipse' },
      getRadiusX: () => 10,
      getRadiusY: () => 5,
      getSubType: () => 'ellipse',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realEllipse, Ellipse.prototype)

    const area = strategy.calculateArea(realEllipse as any)
    const expectedArea = Math.PI * 10 * 5
    expect(area).toBeCloseTo(expectedArea)
  })

  it('should return NaN for invalid radiusX in real Ellipse instance', () => {
    // Create a mock that looks like a real Ellipse instance with invalid radiusX
    const realEllipse = {
      id: 'real-ellipse-id',
      constructor: { name: 'Ellipse' },
      getRadiusX: () => Number.NaN,
      getRadiusY: () => 5,
      getSubType: () => 'ellipse',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realEllipse, Ellipse.prototype)

    const area = strategy.calculateArea(realEllipse as any)
    expect(isNaN(area)).toBe(true)
  })

  it('should return NaN for invalid radiusY in real Ellipse instance', () => {
    // Create a mock that looks like a real Ellipse instance with invalid radiusY
    const realEllipse = {
      id: 'real-ellipse-id',
      constructor: { name: 'Ellipse' },
      getRadiusX: () => 10,
      getRadiusY: () => Number.NaN,
      getSubType: () => 'ellipse',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realEllipse, Ellipse.prototype)

    const area = strategy.calculateArea(realEllipse as any)
    expect(isNaN(area)).toBe(true)
  })

  it('should return NaN for negative radiusX in real Ellipse instance', () => {
    // Create a mock that looks like a real Ellipse instance with negative radiusX
    const realEllipse = {
      id: 'real-ellipse-id',
      constructor: { name: 'Ellipse' },
      getRadiusX: () => -10, // Negative radius
      getRadiusY: () => 5,
      getSubType: () => 'ellipse',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realEllipse, Ellipse.prototype)

    const area = strategy.calculateArea(realEllipse as any)
    expect(isNaN(area)).toBe(true)
  })
  it('should handle null element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    const result = strategy.calculateArea(null)

    expect(isNaN(result)).toBe(true)
    expect(console.error).toHaveBeenCalledWith(
      expect.stringContaining('Invalid element provided (null)'),
    )
  })

  it('should handle undefined element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    const result = strategy.calculateArea(undefined)

    expect(isNaN(result)).toBe(true)
    expect(console.error).toHaveBeenCalledWith(
      expect.stringContaining('Invalid element provided (undefined)'),
    )
  })

  it('should handle element with missing constructor', () => {
    const elementWithoutConstructor = {
      id: 'strange-element',
    }

    const result = strategy.calculateArea(elementWithoutConstructor as any)

    expect(isNaN(result)).toBe(true)
    expect(console.error).toHaveBeenCalledWith(
      expect.stringContaining('Expected an element of type Ellipse, but received type: Object'),
    )
  })

  it('should handle error when accessing radii methods', () => {
    // Create a mock that throws an error when getRadiusX is called
    const realEllipse = {
      id: 'real-ellipse-id',
      constructor: { name: 'Ellipse' },
      getRadiusX: () => { throw new Error('Test error') },
      getRadiusY: () => 5,
      getSubType: () => 'ellipse',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realEllipse, Ellipse.prototype)

    const result = strategy.calculateArea(realEllipse as any)

    expect(isNaN(result)).toBe(true)
    expect(console.error).toHaveBeenCalledWith(
      expect.stringContaining('Error accessing radii methods on element real-ellipse-id'),
      expect.any(Error),
    )
  })
})
