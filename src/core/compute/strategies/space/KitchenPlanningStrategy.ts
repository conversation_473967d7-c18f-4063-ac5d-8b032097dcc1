import type {
  ErgonomicsEvaluationR<PERSON>ult,
  PathwayCheckResult,
  SpacePlanningStrategy,
  SpaceType,
} from '@/types/core/compute'
import type Point from '@/types/core/element/geometry/point' // Point interface (IPoint)
/**
 * Kitchen Space Planning Strategy
 * Provides planning functionality for kitchen spaces, including work triangle check, pathway width check, etc.
 */
import type {
  Element,
  Shape,
  // Path, // Unused
  ShapeElement, // For accessing properties
} from '@/types/core/elementDefinitions'
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  ElementType as CoreElementType, // For accessing properties
} from '@/types/core/elementDefinitions'
// import {
//     calculateSpaceUtilization,
//     checkPathwaysWidth,
//     checkWorkTriangle as checkWorkTriangleUtil // Renamed to avoid conflict
// } from '@/lib/utils/space/spacePlanning'; // Module not found
// import { BoundingBoxClass } from '@/lib/utils/geometry'; // Unused
// import { PointClass } from '@/lib/utils/geometry'; // PointClass is exported from here // Unused

// Helper to get BoundingBoxClass from an Element (Unused in this file)
// function getElementBoundingBoxClass(element: Element): BoundingBoxClass | null {
//     if (!element) return null;
//     if (element.type === CoreElementType.RECTANGLE || element.type === CoreElementType.SQUARE) {
//         const shape = element as Shape.Rectangle;
//         if (shape.position && typeof shape.width === 'number' && typeof shape.height === 'number') {
//             return new BoundingBoxClass(shape.position.x - shape.width / 2, shape.position.y - shape.height / 2, shape.width, shape.height);
//         }
//     }
//      if (element.type === CoreElementType.CIRCLE) {
//         const shape = element as Shape.Circle;
//         if (shape.position && typeof shape.radius === 'number') {
//             return new BoundingBoxClass(shape.position.x - shape.radius, shape.position.y - shape.radius, shape.radius * 2, shape.radius * 2);
//         }
//     }
//     console.warn(`[KitchenPlanningStrategy] getElementBoundingBoxClass: Bounding box for type ${element.type} not fully implemented here.`);
//     return null;
// }

/**
 * Kitchen Space Planning Strategy
 */
export class KitchenPlanningStrategy implements SpacePlanningStrategy {
  public calculateSpaceUtilization(/* elements: Element[], roomBoundaryElement: Element */): number { // Params unused due to commented out body
    // const roomBBoxClass = getElementBoundingBoxClass(roomBoundaryElement);
    // if (!roomBBoxClass) {
    //   throw new CoreError( ErrorType.INVALID_PARAMETER, 'Room boundary must be an element from which a bounding box can be derived.');
    // }
    // return calculateSpaceUtilization(elements, roomBBoxClass);
    console.warn('[KitchenPlanningStrategy] calculateSpaceUtilization is temporarily disabled due to missing dependencies.')
    return 0
  }

  public checkPathwayWidth(/* elements: Element[], pathways: Path.Line[], minWidth: number */): PathwayCheckResult[] { // Params unused due to commented out body
    // if (!Array.isArray(elements)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Elements must be an array');
    // if (!Array.isArray(pathways)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Pathways must be an array');
    // if (typeof minWidth !== 'number' || minWidth <= 0) {
    //   throw new CoreError(ErrorType.INVALID_PARAMETER, 'Minimum width must be a positive number');
    // }

    // const formattedPathways = pathways.map((path, index) => {
    //     const lineForUtil = {
    //         id: path.id ?? `pathway-${index}`,
    //         start: path.start as Point,
    //         end: path.end as Point,
    //         type: CoreElementType.LINE,
    //         getSubType: () => CoreElementType.LINE,
    //     } as any;
    //     return { id: path.id ?? `pathway-${index + 1}`, line: lineForUtil as import('@/lib/utils/element/path/lineImplementation').Line };
    // });

    // return checkPathwaysWidth(elements, formattedPathways, minWidth);
    console.warn('[KitchenPlanningStrategy] checkPathwayWidth is temporarily disabled due to missing dependencies.')
    return []
  }

  public checkWorkTriangle(
    /* elements: Element[], sinkElement: Element, stoveElement: Element, refrigeratorElement: Element */
  ): { isValid: boolean, perimeter: number, message: string } { // Params unused due to commented out body
    // if (!sinkElement || !stoveElement || !refrigeratorElement) {
    //   throw new CoreError(ErrorType.INVALID_PARAMETER, 'Sink, stove, and refrigerator elements must be provided.');
    // }

    // const sinkPos = (sinkElement as ShapeElement).position as Point; // Assuming these are ShapeElements with a position
    // const stovePos = (stoveElement as ShapeElement).position as Point;
    // const fridgePos = (refrigeratorElement as ShapeElement).position as Point;

    // if (!sinkPos || !stovePos || !fridgePos) {
    //     throw new CoreError(ErrorType.INVALID_PARAMETER, 'One or more work triangle elements lack a position.');
    // }

    // // The utility function expects PointClass instances
    // return checkWorkTriangleUtil(
    //   new PointClass(sinkPos.x, sinkPos.y),
    //   new PointClass(stovePos.x, stovePos.y),
    //   new PointClass(fridgePos.x, fridgePos.y),
    //   elements
    // );
    console.warn('[KitchenPlanningStrategy] checkWorkTriangle is temporarily disabled due to missing dependencies.')
    return { isValid: false, perimeter: 0, message: 'Work triangle check disabled.' }
  }

  public evaluateErgonomics(
    elements: Element[],
    deskElement: Element, // In kitchen context, this might be a primary work counter
    // chairElement: Element // Or a stool // Parameter 'chairElement' is declared but its value is never read.
  ): ErgonomicsEvaluationResult {
    const issues: string[] = []
    const recommendations: string[] = []

    if (!deskElement?.type) { // chairElement might be optional for some kitchen workspaces
      throw new CoreError(ErrorType.InvalidParameter, 'Desk/work counter element must be provided and have a type.')
    }

    const workSurface = deskElement as unknown as Shape.Rectangle // Assuming work surface is rectangular
    const counterHeight = workSurface.height ?? 0.9 // Default to 0.9m if not specified

    if (counterHeight < 0.85 || counterHeight > 0.95) {
      issues.push(`Counter height of ${counterHeight.toFixed(2)}m is outside the recommended range (0.85-0.95m).`)
    }
    else {
      recommendations.push(`Counter height of ${counterHeight.toFixed(2)}m is within standard range.`)
    }

    const triangleElements = this.findWorkTriangleElements(elements)
    if (triangleElements.sink && triangleElements.stove && triangleElements.refrigerator) {
      const workSurfaceShapeElement = workSurface as unknown as ShapeElement
      const workSurfacePos = workSurfaceShapeElement.position
      if (workSurfacePos !== null && workSurfacePos !== undefined) {
        const sinkPos = (triangleElements.sink as ShapeElement).position
        const stovePos = (triangleElements.stove as ShapeElement).position
        const fridgePos = (triangleElements.refrigerator as ShapeElement).position

        if (this.isPointInTriangle(workSurfacePos, sinkPos, stovePos, fridgePos)) {
          issues.push('Primary workspace is within the main kitchen work triangle, potentially causing workflow disruption.')
          recommendations.push('Consider positioning primary workspace adjacent to, but not inside, the work triangle.')
        }
      }
    }

    recommendations.push('Ensure adequate lighting for all work surfaces.')
    recommendations.push('Consider toe-kick space under base cabinets (approx. 10cm high, 7.5cm deep).')

    return {
      isValid: issues.length === 0,
      issues,
      recommendations,
    }
  }

  private findWorkTriangleElements(elements: Element[]): {
    sink?: Element
    stove?: Element
    refrigerator?: Element
  } {
    let sink, stove, refrigerator
    for (const element of elements) {
      const el = element as ShapeElement // Assume these are ShapeElements with properties
      const elementType = el.type as CoreElementType
      const elementCategory = el.properties?.category as string || ''
      const customType = el.properties?.customType as string || ''

      if (elementType === CoreElementType.FIXTURE || elementCategory === 'fixture' || customType === 'sink')
        sink = element
      if (elementType === CoreElementType.APPLIANCE || elementCategory === 'appliance' || customType === 'stove' || customType === 'cooktop')
        stove = element
      if (elementType === CoreElementType.APPLIANCE || elementCategory === 'appliance' || customType === 'refrigerator')
        refrigerator = element
    }
    return { sink, stove, refrigerator }
  }

  private isPointInTriangle(p: Point, a: Point, b: Point, c: Point): boolean {
    const areaABC = Math.abs((a.x * (b.y - c.y) + b.x * (c.y - a.y) + c.x * (a.y - b.y))) // Doubled area
    const areaPAB = Math.abs((p.x * (a.y - b.y) + a.x * (b.y - p.y) + b.x * (p.y - a.y)))
    const areaPBC = Math.abs((p.x * (b.y - c.y) + b.x * (c.y - p.y) + c.x * (p.y - b.y)))
    const areaPCA = Math.abs((p.x * (c.y - a.y) + c.x * (a.y - p.y) + a.x * (p.y - c.y)))
    return Math.abs(areaABC - (areaPAB + areaPBC + areaPCA)) < 0.001 // Check if sum of small triangle areas equals big one
  }

  public getSpaceType(): SpaceType {
    return 'kitchen'
  }
}
