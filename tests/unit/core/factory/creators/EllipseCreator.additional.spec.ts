/**
 * @file EllipseCreator.additional.spec.ts
 * @description Additional unit tests for EllipseCreator class to improve coverage
 */

import { beforeEach, describe, expect, it, vi } from 'vitest'
import { CoreError } from '@/core/errors'
import { EllipseCreator } from '@/core/factory/creators/EllipseCreator'
import { Point } from '@/types/core/element/geometry/point'
import { Ellipse } from '@/types/core/element/shape/ellipseShapeTypes'
import { ElementType } from '@/types/core/shape-type'

// Mock the Ellipse class
vi.mock('@/types/core/element/shape/ellipse')

describe('ellipseCreator - Additional Tests', () => {
  let creator: EllipseCreator

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()

    // Create a new instance for each test
    creator = new EllipseCreator();

    // Mock the Ellipse constructor
    (Ellipse as any).mockImplementation((id, position, initialProperties, options) => {
      return {
        id,
        type: initialProperties.type,
        position,
        ...(initialProperties.type === ElementType.ELLIPSE
          ? {
              radiusX: initialProperties.radiusX,
              radiusY: initialProperties.radiusY,
            }
          : {
              radius: initialProperties.radius,
            }),
        ...(options?.metadata ? { metadata: options.metadata } : {}),
        toJson: () => ({
          id,
          type: initialProperties.type,
          position,
          ...(initialProperties.type === ElementType.ELLIPSE
            ? {
                radiusX: initialProperties.radiusX,
                radiusY: initialProperties.radiusY,
              }
            : {
                radius: initialProperties.radius,
              }),
          ...(options?.metadata ? { metadata: options.metadata } : {}),
        }),
      }
    })
  })

  describe('create', () => {
    it('should create an ellipse with metadata', async () => {
      // Arrange
      const params = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: new Point(100, 100),
        radiusX: 50,
        radiusY: 30,
        metadata: {
          color: 'blue',
          opacity: 0.5,
        },
      }

      // Act
      const ellipse = await creator.create(params)

      // Assert
      expect(Ellipse).toHaveBeenCalledWith(
        'ellipse-1',
        expect.objectContaining({ x: 100, y: 100 }),
        expect.objectContaining({
          type: ElementType.ELLIPSE,
          radiusX: 50,
          radiusY: 30,
        }),
        expect.objectContaining({
          metadata: { color: 'blue', opacity: 0.5 },
        }),
      )
      expect(ellipse).toEqual(expect.objectContaining({
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: expect.objectContaining({ x: 100, y: 100 }),
        radiusX: 50,
        radiusY: 30,
        metadata: { color: 'blue', opacity: 0.5 },
      }))
    })

    it('should create a circle with metadata', async () => {
      // Arrange
      const params = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: new Point(100, 100),
        radius: 50,
        metadata: {
          color: 'red',
          opacity: 0.7,
        },
      }

      // Act
      const circle = await creator.create(params)

      // Assert
      expect(Ellipse).toHaveBeenCalledWith(
        'circle-1',
        expect.objectContaining({ x: 100, y: 100 }),
        expect.objectContaining({
          type: ElementType.CIRCLE,
          radius: 50,
        }),
        expect.objectContaining({
          metadata: { color: 'red', opacity: 0.7 },
        }),
      )
      expect(circle).toEqual(expect.objectContaining({
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: expect.objectContaining({ x: 100, y: 100 }),
        radius: 50,
        metadata: { color: 'red', opacity: 0.7 },
      }))
    })

    it('should handle different position formats for ellipse', async () => {
      // Arrange - Using array for position
      const paramsWithArrayPosition = {
        id: 'ellipse-array',
        type: ElementType.ELLIPSE,
        position: [150, 150],
        radiusX: 60,
        radiusY: 40,
      }

      // Act
      const ellipseWithArrayPosition = await creator.create(paramsWithArrayPosition)

      // Assert
      expect(Ellipse).toHaveBeenCalledWith(
        'ellipse-array',
        expect.objectContaining({ x: 150, y: 150 }),
        expect.objectContaining({
          type: ElementType.ELLIPSE,
          radiusX: 60,
          radiusY: 40,
        }),
        expect.any(Object),
      )

      // Arrange - Using object for position
      const paramsWithObjectPosition = {
        id: 'ellipse-object',
        type: ElementType.ELLIPSE,
        position: { x: 200, y: 200 },
        radiusX: 70,
        radiusY: 50,
      }

      // Act
      const ellipseWithObjectPosition = await creator.create(paramsWithObjectPosition)

      // Assert
      expect(Ellipse).toHaveBeenCalledWith(
        'ellipse-object',
        expect.objectContaining({ x: 200, y: 200 }),
        expect.objectContaining({
          type: ElementType.ELLIPSE,
          radiusX: 70,
          radiusY: 50,
        }),
        expect.any(Object),
      )
    })

    it('should handle different position formats for circle', async () => {
      // Arrange - Using array for position
      const paramsWithArrayPosition = {
        id: 'circle-array',
        type: ElementType.CIRCLE,
        position: [150, 150],
        radius: 60,
      }

      // Act
      const circleWithArrayPosition = await creator.create(paramsWithArrayPosition)

      // Assert
      expect(Ellipse).toHaveBeenCalledWith(
        'circle-array',
        expect.objectContaining({ x: 150, y: 150 }),
        expect.objectContaining({
          type: ElementType.CIRCLE,
          radius: 60,
        }),
        expect.any(Object),
      )

      // Arrange - Using object for position
      const paramsWithObjectPosition = {
        id: 'circle-object',
        type: ElementType.CIRCLE,
        position: { x: 200, y: 200 },
        radius: 70,
      }

      // Act
      const circleWithObjectPosition = await creator.create(paramsWithObjectPosition)

      // Assert
      expect(Ellipse).toHaveBeenCalledWith(
        'circle-object',
        expect.objectContaining({ x: 200, y: 200 }),
        expect.objectContaining({
          type: ElementType.CIRCLE,
          radius: 70,
        }),
        expect.any(Object),
      )
    })

    it('should throw an error for unsupported shape type', async () => {
      // Arrange
      const params = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
      }

      // Act & Assert
      await expect(creator.create(params)).rejects.toThrow(CoreError)
      await expect(creator.create(params)).rejects.toThrow('EllipseCreator cannot create type: rectangle')
    })

    it('should throw an error if required parameters are missing for ellipse', async () => {
      // Arrange - Missing radiusX
      const paramsWithoutRadiusX = {
        id: 'ellipse-missing-radiusX',
        type: ElementType.ELLIPSE,
        position: new Point(100, 100),
        radiusY: 30,
      }

      // Act & Assert
      await expect(creator.create(paramsWithoutRadiusX)).rejects.toThrow(CoreError)
      await expect(creator.create(paramsWithoutRadiusX)).rejects.toThrow('Ellipse creation requires valid positive radiusX and radiusY')

      // Arrange - Missing radiusY
      const paramsWithoutRadiusY = {
        id: 'ellipse-missing-radiusY',
        type: ElementType.ELLIPSE,
        position: new Point(100, 100),
        radiusX: 50,
      }

      // Act & Assert
      await expect(creator.create(paramsWithoutRadiusY)).rejects.toThrow(CoreError)
      await expect(creator.create(paramsWithoutRadiusY)).rejects.toThrow('Ellipse creation requires valid positive radiusX and radiusY')
    })

    it('should throw an error if required parameters are missing for circle', async () => {
      // Arrange - Missing radius
      const paramsWithoutRadius = {
        id: 'circle-missing-radius',
        type: ElementType.CIRCLE,
        position: new Point(100, 100),
      }

      // Act & Assert
      await expect(creator.create(paramsWithoutRadius)).rejects.toThrow(CoreError)
      await expect(creator.create(paramsWithoutRadius)).rejects.toThrow('Circle creation requires a valid positive radius')
    })
  })

  describe('createDefault', () => {
    it('should create a default ellipse', async () => {
      // Arrange
      const id = 'default-ellipse'
      const position = new Point(100, 100)

      // Act
      const ellipse = await creator.createDefault(id, position)

      // Assert
      expect(Ellipse).toHaveBeenCalledWith(
        'default-ellipse',
        expect.objectContaining({ x: 100, y: 100 }),
        expect.objectContaining({
          type: ElementType.ELLIPSE,
          radiusX: expect.any(Number),
          radiusY: expect.any(Number),
        }),
        expect.any(Object),
      )
      expect(ellipse).toEqual(expect.objectContaining({
        id: 'default-ellipse',
        type: ElementType.ELLIPSE,
        position: expect.objectContaining({ x: 100, y: 100 }),
      }))
    })

    it('should handle different position formats for default ellipse', async () => {
      // Arrange - Using array for position
      const arrayPosition = [150, 150]

      // Act
      const ellipseWithArrayPosition = await creator.createDefault('ellipse-array', arrayPosition)

      // Assert
      expect(Ellipse).toHaveBeenCalledWith(
        'ellipse-array',
        expect.objectContaining({ x: 150, y: 150 }),
        expect.objectContaining({
          type: ElementType.ELLIPSE,
          radiusX: expect.any(Number),
          radiusY: expect.any(Number),
        }),
        expect.any(Object),
      )

      // Arrange - Using object for position
      const objectPosition = { x: 200, y: 200 }

      // Act
      const ellipseWithObjectPosition = await creator.createDefault('ellipse-object', objectPosition)

      // Assert
      expect(Ellipse).toHaveBeenCalledWith(
        'ellipse-object',
        expect.objectContaining({ x: 200, y: 200 }),
        expect.objectContaining({
          type: ElementType.ELLIPSE,
          radiusX: expect.any(Number),
          radiusY: expect.any(Number),
        }),
        expect.any(Object),
      )
    })

    it('should handle errors during ellipse creation', async () => {
      // Arrange
      const id = 'error-ellipse'
      const position = new Point(100, 100);

      // Mock Ellipse constructor to throw an error
      (Ellipse as any).mockImplementationOnce(() => {
        throw new Error('Test error')
      })

      // Act & Assert
      await expect(creator.createDefault(id, position)).rejects.toThrow('Test error')
    })
  })
})
