import type { ServiceIdType } from '@/types/services/core/serviceIdentifier'
import { describe, expect, it } from 'vitest'
import { ServiceId } from '@/types/services/core/serviceIdentifier'

describe('service Identifier Types', () => {
  describe('serviceId', () => {
    it('should define all expected service identifiers', () => {
      // Core services
      expect(ServiceId.EVENT_BUS).toBeDefined()
      expect(ServiceId.LOGGER).toBeDefined()
      expect(ServiceId.ERROR_SERVICE).toBeDefined()
      expect(ServiceId.VALIDATION_SERVICE).toBeDefined()

      // Shape action services
      expect(ServiceId.SHAPE_CREATION_SERVICE).toBeDefined()
      expect(ServiceId.SHAPE_EDIT_SERVICE).toBeDefined()
      expect(ServiceId.SHAPE_DELETE_SERVICE).toBeDefined()
      expect(ServiceId.SHAPE_SELECTION_SERVICE).toBeDefined()

      // Other services
      expect(ServiceId.KEYBOARD_SERVICE).toBeDefined()
      expect(ServiceId.HISTORY_SERVICE).toBeDefined()
      expect(ServiceId.STORAGE_SERVICE).toBeDefined()
    })

    it('should have the correct string values', () => {
      // Core services
      expect(ServiceId.EVENT_BUS).toBe('event-bus')
      expect(ServiceId.LOGGER).toBe('logger')
      expect(ServiceId.ERROR_SERVICE).toBe('error-service')
      expect(ServiceId.VALIDATION_SERVICE).toBe('validation-service')

      // Shape action services
      expect(ServiceId.SHAPE_CREATION_SERVICE).toBe('shape-creation-service')
      expect(ServiceId.SHAPE_EDIT_SERVICE).toBe('shape-edit-service')
      expect(ServiceId.SHAPE_DELETE_SERVICE).toBe('shape-delete-service')
      expect(ServiceId.SHAPE_SELECTION_SERVICE).toBe('shape-selection-service')

      // Other services
      expect(ServiceId.KEYBOARD_SERVICE).toBe('keyboard-service')
      expect(ServiceId.HISTORY_SERVICE).toBe('history-service')
      expect(ServiceId.STORAGE_SERVICE).toBe('storage-service')
    })

    it('should be usable as a type', () => {
      // Create a function that accepts ServiceId
      const getServiceDescription = (serviceId: ServiceId): string => {
        switch (serviceId) {
          case ServiceId.EVENT_BUS:
            return 'Event bus service'
          case ServiceId.LOGGER:
            return 'Logger service'
          case ServiceId.ERROR_SERVICE:
            return 'Error handling service'
          default:
            return 'Unknown service'
        }
      }

      // Test the function with different service IDs
      expect(getServiceDescription(ServiceId.EVENT_BUS)).toBe('Event bus service')
      expect(getServiceDescription(ServiceId.LOGGER)).toBe('Logger service')
      expect(getServiceDescription(ServiceId.ERROR_SERVICE)).toBe('Error handling service')
    })

    it('should have unique values', () => {
      const serviceIdValues = Object.values(ServiceId)
      const uniqueValues = new Set(serviceIdValues)

      // If there are duplicates, the Set size will be smaller than the array length
      expect(uniqueValues.size).toBe(serviceIdValues.length)
    })
  })

  describe('serviceIdType', () => {
    it('should be usable as a branded string type', () => {
      // We can't directly test the brand, but we can test that it's assignable from ServiceId
      const eventBusId: ServiceIdType = ServiceId.EVENT_BUS as ServiceIdType
      expect(eventBusId).toBe('event-bus')

      // Create a function that accepts ServiceIdType
      const getServiceById = (id: ServiceIdType): string => {
        return `Service: ${id}`
      }

      // Test with a value from ServiceId
      expect(getServiceById(ServiceId.LOGGER as ServiceIdType)).toBe('Service: logger')
    })
  })
})
