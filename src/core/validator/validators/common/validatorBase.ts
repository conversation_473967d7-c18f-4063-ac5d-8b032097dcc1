/**
 * @file validatorBase.ts
 * @description Provides an abstract base class for all shape-specific validators.
 * This class implements a template validation method (`validate`) that orchestrates common checks
 * (e.g., ID, colors), delegates to shape-specific property validation (`validateSpecific`),
 * and applies business rules (`applyAllRules`).
 *
 * @module core/validator/validators/common/validatorBase
 * @implements {IBaseShapeValidator}
 */

import type {
  ValidationError,
  ValidationResult,
  ValidatableShape as ValidatorShape,
} from '@/types/core/validator/validator-interface'
import { ValidationErrorCode } from '@/types/core/validator/error-codes'
import { applyBusinessRules } from '../rules/businessRules'
import { isValidColor } from './validationUtils'

/**
 * Interface defining the contract for shape validators.
 *
 * @remarks
 * All shape validators must implement this interface, which provides
 * the standard methods for validating shapes of different types.
 */
export interface IBaseShapeValidator {
  /**
   * Validates a shape object and returns the validation result.
   *
   * @param shape - The shape data object to validate.
   * @returns A result object containing validity status and any errors.
   */
  validate: (shape: ValidatorShape) => ValidationResult
}

/**
 * Abstract base class for all shape-specific validators.
 *
 * @remarks
 * Provides a common structure and validation flow for different shape types.
 * Defines a template method `validate` that orchestrates the validation process.
 * Subclasses **must** implement the abstract `validateSpecific` method to check properties
 * unique to their shape type.
 * Subclasses **can** override the `applySpecificRules` method to add shape-specific business logic validation.
 *
 * @implements {IBaseShapeValidator}
 */
export abstract class BaseShapeValidator implements IBaseShapeValidator {
  /**
   * Template method for validating a shape object.
   *
   * @remarks
   * This method orchestrates the validation process and should not typically be overridden by subclasses.
   *
   * The validation process is as follows:
   * 1. Performs basic structural checks common to all shapes (e.g., presence and format of `id`, optional `strokeColor`, `fillColor`).
   * 2. Calls the abstract `validateSpecific` method (implemented by subclasses) to validate properties unique to the specific shape type.
   * 3. If steps 1 and 2 produce no errors, it calls `applyAllRules` to check the shape against common and specific business rules.
   *
   * @param shape - The shape data object to validate.
   * @returns A ValidationResult object containing the overall validity status and an array of any validation errors found.
   * @final Subclasses should not override this method directly.
   */
  public validate(shape: ValidatorShape): ValidationResult {
    const errors: ValidationError[] = []

    // Step 1: Basic structural/property validation common to all shapes
    if (!shape.id || typeof shape.id !== 'string' || shape.id.trim() === '') {
      errors.push({
        code: ValidationErrorCode.MISSING_OR_INVALID_ID,
        message: 'Shape requires a non-empty string ID.',
        path: 'id',
        value: shape.id,
      })
    }
    // Add validation for common style properties if they are expected on ValidatorShape
    // Note: ValidatorShape might not include style props, depending on its definition.
    // Check if properties exist before validating.
    if ('strokeColor' in shape && shape.strokeColor != null && shape.strokeColor !== '' && !isValidColor(shape.strokeColor)) {
      errors.push({
        code: ValidationErrorCode.INVALID_STROKE_COLOR,
        message: `Invalid stroke color format. Received: ${String(shape.strokeColor)}`,
        path: 'strokeColor',
        value: shape.strokeColor,
      })
    }
    if ('fillColor' in shape && shape.fillColor != null && shape.fillColor !== '' && !isValidColor(shape.fillColor)) {
      errors.push({
        code: ValidationErrorCode.INVALID_FILL_COLOR,
        message: `Invalid fill color format. Received: ${String(shape.fillColor)}`,
        path: 'fillColor',
        value: shape.fillColor,
      })
    }
    // TODO: Add checks for other common properties like position, layer, etc. if they are part of a base ValidatorShape definition.

    // Step 2: Shape-specific property format validation (delegated to subclass)
    try {
      const specificErrors = this.validateSpecific(shape)
      errors.push(...specificErrors)
    }
    catch (error: unknown) {
      console.error(`[BaseShapeValidator] Error during validateSpecific for ${shape.type} (ID: ${shape.id}):`, error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      errors.push({
        code: ValidationErrorCode.VALIDATION_SPECIFIC_ERROR,
        message: `Error during specific validation: ${errorMessage}`,
        path: '?',
      })
    }

    // Step 3: Apply business rules (common and specific)
    // Only apply rules if basic structural and specific property validation passes
    if (errors.length === 0) {
      try {
        const ruleErrors = this.applyAllRules(shape)
        errors.push(...ruleErrors)
      }
      catch (error: unknown) {
        console.error(`[BaseShapeValidator] Error during applyAllRules for ${shape.type} (ID: ${shape.id}):`, error)
        const errorMessage = error instanceof Error ? error.message : String(error)
        errors.push({
          code: ValidationErrorCode.VALIDATION_RULE_ERROR,
          message: `Error applying validation rules: ${errorMessage}`,
          path: '?',
        })
      }
    }

    // Determine final validity and return result
    const isValid = errors.length === 0
    if (!isValid) {
      console.warn(`[BaseShapeValidator] Validation failed for ${shape.type} (ID: ${shape.id}). Errors:`, errors)
    }
    return { valid: isValid, errors }
  }

  /**
   * Abstract method for validating shape-specific properties.
   *
   * @remarks
   * Subclasses **must** implement this method to check the format, constraints, and validity
   * of properties unique to the specific shape type they handle (e.g., `radius` for Circle, `points` for Polygon).
   * This method should focus on the structural correctness of the properties, not on cross-property business logic (which belongs in `applySpecificRules`).
   *
   * @param shape - The shape data object to validate. The subclass can safely cast this to its specific type based on the `type` property.
   * @returns An array of validation errors describing any specific property validation failures. Return an empty array if all specific properties are valid.
   * @protected
   * @abstract
   */
  protected abstract validateSpecific(shape: ValidatorShape): ValidationError[]

  /**
   * Applies all relevant business rules to the shape, combining common and specific rules.
   *
   * @remarks
   * It first calls the common `applyBusinessRules` function (imported from `../rules/businessRules`)
   * and then calls the shape-specific `applySpecificRules` method (which can be overridden by subclasses).
   * This method is called by the main `validate` method only if basic structural and specific property validation has passed.
   *
   * @param shape - The shape data object to validate against business rules.
   * @returns An array containing all validation errors resulting from both common and specific business rule checks.
   * @protected
   */
  protected applyAllRules(shape: ValidatorShape): ValidationError[] {
    const errors: ValidationError[] = []
    // Apply common business rules first
    // These rules might check things applicable to most shapes, like minimum size, ID format etc.
    errors.push(...applyBusinessRules(shape))

    // Apply specific business rules (implemented by subclass, defaults to empty array)
    // These rules handle logic specific to the shape type, like aspect ratio, closure, etc.
    errors.push(...this.applySpecificRules(shape))
    return errors
  }

  /**
   * Virtual method for applying shape-specific business rules.
   *
   * @remarks
   * Subclasses **can** override this method to implement validation logic that goes beyond
   * simple property format checks and is specific to the shape type.
   * Examples: Checking for self-intersections in a polygon, enforcing a maximum aspect ratio for a rectangle,
   * ensuring a line's start and end points meet certain criteria relative to other elements.
   * The default implementation returns an empty array, indicating no specific rules.
   *
   * @param _shape - The shape data object to validate against business rules.
   * @returns An array of validation errors specific to the business rules of this shape type. Return an empty array if no rules are violated or no specific rules are implemented.
   * @protected
   * @virtual Can be overridden by subclasses.
   */
  protected applySpecificRules(_shape: ValidatorShape): ValidationError[] {
    // Default implementation: No shape-specific business rules defined in the base class.
    // Parameter is prefixed with underscore to indicate it's intentionally unused
    return []
  }
}
