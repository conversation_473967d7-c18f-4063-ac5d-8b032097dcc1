import type { OpeningProperties } from '@/types/core/element/design/openingDesignTypes'
import { describe, expect, it } from 'vitest'
import { OpeningType } from '@/types/core/element/design/openingDesignTypes'

describe('opening Types Module', () => {
  describe('openingType Enum', () => {
    it('should define standard door types', () => {
      // Verify that the enum exists and has the expected door values
      expect(OpeningType).toBeDefined()

      // Test door types
      expect(OpeningType.HINGED_DOOR).toBe('hinged-door')
      expect(OpeningType.SLIDING_DOOR).toBe('sliding-door')
      expect(OpeningType.POCKET_DOOR).toBe('pocket-door')
      expect(OpeningType.BIFOLD_DOOR).toBe('bifold-door')
      expect(OpeningType.FRENCH_DOOR).toBe('french-door')
      expect(OpeningType.BARN_DOOR).toBe('barn-door')
      expect(OpeningType.REVOLVING_DOOR).toBe('revolving-door')
    })

    it('should define standard window types', () => {
      // Verify that the enum has the expected window values
      expect(OpeningType.CASEMENT_WINDOW).toBe('casement-window')
      expect(OpeningType.AWNING_WINDOW).toBe('awning-window')
      expect(OpeningType.HOPPER_WINDOW).toBe('hopper-window')
      expect(OpeningType.SLIDING_WINDOW).toBe('sliding-window')
      expect(OpeningType.DOUBLE_HUNG_WINDOW).toBe('double-hung-window')
      expect(OpeningType.FIXED_WINDOW).toBe('fixed-window')
      expect(OpeningType.BAY_WINDOW).toBe('bay-window')
      expect(OpeningType.BOW_WINDOW).toBe('bow-window')
    })

    it('should define other opening types', () => {
      // Verify that the enum has other opening values
      expect(OpeningType.ARCHWAY).toBe('archway')
      expect(OpeningType.PASS_THROUGH).toBe('pass-through')
      expect(OpeningType.SKYLIGHT).toBe('skylight')
    })

    it('should be usable in switch statements', () => {
      // Define a function that uses the enum in a switch statement
      function getOpeningCategory(openingType: OpeningType): string {
        switch (openingType) {
          case OpeningType.HINGED_DOOR:
          case OpeningType.SLIDING_DOOR:
          case OpeningType.POCKET_DOOR:
          case OpeningType.BIFOLD_DOOR:
          case OpeningType.FRENCH_DOOR:
          case OpeningType.BARN_DOOR:
          case OpeningType.REVOLVING_DOOR:
            return 'door'

          case OpeningType.CASEMENT_WINDOW:
          case OpeningType.AWNING_WINDOW:
          case OpeningType.HOPPER_WINDOW:
          case OpeningType.SLIDING_WINDOW:
          case OpeningType.DOUBLE_HUNG_WINDOW:
          case OpeningType.FIXED_WINDOW:
          case OpeningType.BAY_WINDOW:
          case OpeningType.BOW_WINDOW:
          case OpeningType.SKYLIGHT:
            return 'window'

          default:
            return 'other'
        }
      }

      // Test the function with different enum values
      expect(getOpeningCategory(OpeningType.HINGED_DOOR)).toBe('door')
      expect(getOpeningCategory(OpeningType.CASEMENT_WINDOW)).toBe('window')
      expect(getOpeningCategory(OpeningType.ARCHWAY)).toBe('other')
    })

    it('should be usable as object keys', () => {
      // Create an object using enum values as keys
      const openingWidths: Record<OpeningType, number> = {
        [OpeningType.HINGED_DOOR]: 900,
        [OpeningType.SLIDING_DOOR]: 1200,
        [OpeningType.POCKET_DOOR]: 800,
        [OpeningType.BIFOLD_DOOR]: 1500,
        [OpeningType.FRENCH_DOOR]: 1600,
        [OpeningType.BARN_DOOR]: 1000,
        [OpeningType.REVOLVING_DOOR]: 2000,
        [OpeningType.CASEMENT_WINDOW]: 600,
        [OpeningType.AWNING_WINDOW]: 800,
        [OpeningType.HOPPER_WINDOW]: 600,
        [OpeningType.SLIDING_WINDOW]: 1200,
        [OpeningType.DOUBLE_HUNG_WINDOW]: 900,
        [OpeningType.FIXED_WINDOW]: 1000,
        [OpeningType.BAY_WINDOW]: 1800,
        [OpeningType.BOW_WINDOW]: 2000,
        [OpeningType.ARCHWAY]: 1200,
        [OpeningType.PASS_THROUGH]: 600,
        [OpeningType.SKYLIGHT]: 800,
      }

      // Verify that we can access values using enum keys
      expect(openingWidths[OpeningType.HINGED_DOOR]).toBe(900)
      expect(openingWidths[OpeningType.CASEMENT_WINDOW]).toBe(600)
      expect(openingWidths[OpeningType.BAY_WINDOW]).toBe(1800)
    })
  })

  describe('openingProperties Interface', () => {
    it('should define a valid door opening with required properties', () => {
      // Create a door opening that conforms to the OpeningProperties interface
      const door: OpeningProperties = {
        type: 'opening',
        openingType: OpeningType.HINGED_DOOR,
        width: 900,
        height: 2100,
        heightFromFloor: 0,
        wallId: 'wall-1',
        wallPosition: 0.5,
      }

      // Verify the door has the expected properties
      expect(door.type).toBe('opening')
      expect(door.openingType).toBe(OpeningType.HINGED_DOOR)
      expect(door.width).toBe(900)
      expect(door.height).toBe(2100)
      expect(door.heightFromFloor).toBe(0)
      expect(door.wallId).toBe('wall-1')
      expect(door.wallPosition).toBe(0.5)
    })

    it('should define a valid window opening with required properties', () => {
      // Create a window opening that conforms to the OpeningProperties interface
      const window: OpeningProperties = {
        type: 'opening',
        openingType: OpeningType.CASEMENT_WINDOW,
        width: 800,
        height: 1200,
        heightFromFloor: 900,
        wallId: 'wall-2',
        wallPosition: 0.3,
      }

      // Verify the window has the expected properties
      expect(window.type).toBe('opening')
      expect(window.openingType).toBe(OpeningType.CASEMENT_WINDOW)
      expect(window.width).toBe(800)
      expect(window.height).toBe(1200)
      expect(window.heightFromFloor).toBe(900)
      expect(window.wallId).toBe('wall-2')
      expect(window.wallPosition).toBe(0.3)
    })

    it('should be usable in arrays and collections', () => {
      // Create an array of openings
      const openings: OpeningProperties[] = [
        {
          type: 'opening',
          openingType: OpeningType.HINGED_DOOR,
          width: 900,
          height: 2100,
          heightFromFloor: 0,
          wallId: 'wall-1',
          wallPosition: 0.5,
        },
        {
          type: 'opening',
          openingType: OpeningType.CASEMENT_WINDOW,
          width: 800,
          height: 1200,
          heightFromFloor: 900,
          wallId: 'wall-1',
          wallPosition: 0.8,
        },
        {
          type: 'opening',
          openingType: OpeningType.SLIDING_WINDOW,
          width: 1500,
          height: 1000,
          heightFromFloor: 1000,
          wallId: 'wall-2',
          wallPosition: 0.4,
        },
      ]

      // Verify array operations
      expect(openings.length).toBe(3)

      // Test array methods
      const doorOpenings = openings.filter(opening =>
        opening.openingType === OpeningType.HINGED_DOOR
        || opening.openingType === OpeningType.SLIDING_DOOR
        || opening.openingType === OpeningType.POCKET_DOOR
        || opening.openingType === OpeningType.BIFOLD_DOOR
        || opening.openingType === OpeningType.FRENCH_DOOR
        || opening.openingType === OpeningType.BARN_DOOR
        || opening.openingType === OpeningType.REVOLVING_DOOR,
      )
      expect(doorOpenings.length).toBe(1)

      const windowOpenings = openings.filter(opening =>
        opening.openingType !== OpeningType.HINGED_DOOR
        && opening.openingType !== OpeningType.SLIDING_DOOR
        && opening.openingType !== OpeningType.POCKET_DOOR
        && opening.openingType !== OpeningType.BIFOLD_DOOR
        && opening.openingType !== OpeningType.FRENCH_DOOR
        && opening.openingType !== OpeningType.BARN_DOOR
        && opening.openingType !== OpeningType.REVOLVING_DOOR
        && opening.openingType !== OpeningType.ARCHWAY
        && opening.openingType !== OpeningType.PASS_THROUGH,
      )
      expect(windowOpenings.length).toBe(2)

      const openingsInWall1 = openings.filter(opening => opening.wallId === 'wall-1')
      expect(openingsInWall1.length).toBe(2)
    })

    it('should be usable as function parameters and return values', () => {
      // Define a function that takes an OpeningProperties parameter
      function calculateOpeningArea(opening: OpeningProperties): number {
        return opening.width * opening.height
      }

      // Define a function that returns an OpeningProperties object
      function createDoor(wallId: string, wallPosition: number, width: number = 900): OpeningProperties {
        return {
          type: 'opening',
          openingType: OpeningType.HINGED_DOOR,
          width,
          height: 2100,
          heightFromFloor: 0,
          wallId,
          wallPosition,
        }
      }

      // Test the functions
      const door = createDoor('wall-3', 0.6, 1000)
      expect(door.openingType).toBe(OpeningType.HINGED_DOOR)
      expect(door.width).toBe(1000)
      expect(door.wallId).toBe('wall-3')
      expect(door.wallPosition).toBe(0.6)

      const area = calculateOpeningArea(door)
      expect(area).toBe(1000 * 2100) // 2,100,000 sq mm or 2.1 sq m

      // Test with default width
      const standardDoor = createDoor('wall-4', 0.5)
      expect(standardDoor.width).toBe(900)

      const standardArea = calculateOpeningArea(standardDoor)
      expect(standardArea).toBe(900 * 2100) // 1,890,000 sq mm or 1.89 sq m
    })

    it('should handle wallPosition as a normalized value', () => {
      // Create openings with different wall positions
      const openingStart: OpeningProperties = {
        type: 'opening',
        openingType: OpeningType.HINGED_DOOR,
        width: 900,
        height: 2100,
        heightFromFloor: 0,
        wallId: 'wall-5',
        wallPosition: 0, // Start of the wall
      }

      const openingMiddle: OpeningProperties = {
        type: 'opening',
        openingType: OpeningType.CASEMENT_WINDOW,
        width: 800,
        height: 1200,
        heightFromFloor: 900,
        wallId: 'wall-5',
        wallPosition: 0.5, // Middle of the wall
      }

      const openingEnd: OpeningProperties = {
        type: 'opening',
        openingType: OpeningType.SLIDING_WINDOW,
        width: 1000,
        height: 1000,
        heightFromFloor: 1000,
        wallId: 'wall-5',
        wallPosition: 1, // End of the wall
      }

      // Verify the wall positions
      expect(openingStart.wallPosition).toBe(0)
      expect(openingMiddle.wallPosition).toBe(0.5)
      expect(openingEnd.wallPosition).toBe(1)

      // Define a function that calculates the actual position along a wall
      function calculateActualPosition(opening: OpeningProperties, wallLength: number): number {
        return opening.wallPosition * wallLength
      }

      // Test with a 4000mm wall
      const wallLength = 4000
      expect(calculateActualPosition(openingStart, wallLength)).toBe(0)
      expect(calculateActualPosition(openingMiddle, wallLength)).toBe(2000)
      expect(calculateActualPosition(openingEnd, wallLength)).toBe(4000)
    })
  })
})
