/**
 * System Event Type Definitions
 *
 * This file defines event types related to core system operations,
 * such as event bus interactions and factory registrations.
 *
 * @module types/services/events
 */
import type { AppEventType } from './eventTypes'

/**
 * Represents an event related to the event bus itself, such as
 * registration or unregistration of event handlers, or internal errors.
 */
export interface EventBusEvent {
  /** The type of event bus event. */
  type: AppEventType.EventRegister | AppEventType.EventUnregister | AppEventType.EventError
  /** The payload associated with the event bus event. */
  payload: {
    /** The specific event type string this event pertains to (e.g., 'shape.create.request'). */
    eventType: string
    /** The event handler function, if applicable (e.g., for register/unregister events). */
    handler?: (...args: unknown[]) => unknown
    /** An error message, if the event represents an error. */
    error?: string
  }
}

/**
 * Represents an event related to the shape factory, such as registration
 * of new shape creators or factory-level errors.
 */
export interface ShapeFactoryEvent {
  /** The type of shape factory event. */
  type: AppEventType.FactoryRegister | AppEventType.FactoryUnregister | AppEventType.FactoryError
  /** The payload associated with the shape factory event. */
  payload: {
    /** The type of element this factory event pertains to (e.g., 'Line', 'Rectangle'). */
    elementType: string // Corrected from ElementType to elementType
    /** Factory-specific data, such as creator and validator functions, if applicable. */
    factory?: {
      /** The function responsible for creating an instance of the element. */
      creator: (...args: unknown[]) => unknown
      /** The function responsible for validating parameters for the element. */
      validator: (...args: unknown[]) => unknown
    }
    /** An error message, if the event represents an error. */
    error?: string
  }
}
