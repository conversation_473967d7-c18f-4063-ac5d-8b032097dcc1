import { describe, expect, it } from 'vitest'
import * as HistoryTypesModule from '@/types/services/features/historyTypes'
import * as HistoryModule from '@/types/services/history'

describe('history Types Feature Module', () => {
  it('should be defined as a module', () => {
    // Verify the module exists
    expect(HistoryTypesModule).toBeDefined()
  })

  it('should re-export types from history module', () => {
    // Check that the module has the same exports as the history module
    const historyTypesExports = Object.keys(HistoryTypesModule)
    const historyExports = Object.keys(HistoryModule)

    // Verify that all exports from history module are re-exported
    for (const exportName of historyExports) {
      expect(historyTypesExports).toContain(exportName)
    }
  })
})
