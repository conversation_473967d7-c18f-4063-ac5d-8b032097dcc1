import type {
  DataLoadEvent,
  ExportEvent,
  HistoryEvent,
  StorageEvent,
  TemplateEvent,
} from '@/types/services/events/dataEvents'
import { describe, expect, it } from 'vitest'
import { AppEventType } from '@/types/services/events/eventTypes'

describe('dataEvents', () => {
  describe('historyEvent', () => {
    it('should create undo event correctly', () => {
      const event: HistoryEvent = {
        type: AppEventType.HISTORY_UNDO,
        payload: {},
      }

      expect(event.type).toBe(AppEventType.HISTORY_UNDO)
      expect(event.payload).toEqual({})
    })

    it('should create redo event correctly', () => {
      const event: HistoryEvent = {
        type: AppEventType.HISTORY_REDO,
        payload: {},
      }

      expect(event.type).toBe(AppEventType.HISTORY_REDO)
      expect(event.payload).toEqual({})
    })

    it('should create checkpoint event correctly', () => {
      const event: HistoryEvent = {
        type: AppEventType.HISTORY_CHECKPOINT,
        payload: {
          description: 'Created rectangle',
        },
      }

      expect(event.type).toBe(AppEventType.HISTORY_CHECKPOINT)
      expect(event.payload.description).toBe('Created rectangle')
    })

    it('should create error event correctly', () => {
      const error = new Error('History operation failed')
      const event: HistoryEvent = {
        type: AppEventType.HISTORY_ERROR,
        payload: {
          error,
        },
      }

      expect(event.type).toBe(AppEventType.HISTORY_ERROR)
      expect(event.payload.error).toBe(error)
    })
  })

  describe('templateEvent', () => {
    it('should create template apply event correctly', () => {
      const event: TemplateEvent = {
        type: AppEventType.TEMPLATE_APPLY,
        payload: {
          templateId: 'template-123',
        },
      }

      expect(event.type).toBe(AppEventType.TEMPLATE_APPLY)
      expect(event.payload.templateId).toBe('template-123')
    })

    it('should create template apply request event correctly', () => {
      const event: TemplateEvent = {
        type: AppEventType.TEMPLATE_APPLY_REQUEST,
        payload: {
          templateId: 'template-123',
          options: {
            scale: 1.5,
            position: { x: 100, y: 100 },
          },
        },
      }

      expect(event.type).toBe(AppEventType.TEMPLATE_APPLY_REQUEST)
      expect(event.payload.templateId).toBe('template-123')
      expect(event.payload.options).toEqual({
        scale: 1.5,
        position: { x: 100, y: 100 },
      })
    })

    it('should create template apply complete event correctly', () => {
      const event: TemplateEvent = {
        type: AppEventType.TEMPLATE_APPLY_COMPLETE,
        payload: {
          templateId: 'template-123',
          createdShapeIds: ['shape-1', 'shape-2', 'shape-3'],
        },
      }

      expect(event.type).toBe(AppEventType.TEMPLATE_APPLY_COMPLETE)
      expect(event.payload.templateId).toBe('template-123')
      expect(event.payload.createdShapeIds).toEqual(['shape-1', 'shape-2', 'shape-3'])
    })

    it('should create template apply error event correctly', () => {
      const error = new Error('Template application failed')
      const event: TemplateEvent = {
        type: AppEventType.TEMPLATE_APPLY_ERROR,
        payload: {
          templateId: 'template-123',
          error,
        },
      }

      expect(event.type).toBe(AppEventType.TEMPLATE_APPLY_ERROR)
      expect(event.payload.templateId).toBe('template-123')
      expect(event.payload.error).toBe(error)
    })
  })

  describe('exportEvent', () => {
    it('should create export request event correctly', () => {
      const event: ExportEvent = {
        type: AppEventType.EXPORT_REQUEST,
        payload: {
          format: 'png',
        },
      }

      expect(event.type).toBe(AppEventType.EXPORT_REQUEST)
      expect(event.payload.format).toBe('png')
    })

    it('should create export request event with options', () => {
      const event: ExportEvent = {
        type: AppEventType.EXPORT_REQUEST,
        payload: {
          format: 'svg',
          options: {
            quality: 'high',
            includeBackground: true,
          },
        },
      }

      expect(event.type).toBe(AppEventType.EXPORT_REQUEST)
      expect(event.payload.format).toBe('svg')
      expect(event.payload.options).toEqual({
        quality: 'high',
        includeBackground: true,
      })
    })

    it('should create export progress event correctly', () => {
      const event: ExportEvent = {
        type: AppEventType.EXPORT_PROGRESS,
        payload: {
          format: 'png',
          progress: 0.5,
        },
      }

      expect(event.type).toBe(AppEventType.EXPORT_PROGRESS)
      expect(event.payload.format).toBe('png')
      expect(event.payload.progress).toBe(0.5)
    })

    it('should create export complete event correctly', () => {
      const event: ExportEvent = {
        type: AppEventType.EXPORT_COMPLETE,
        payload: {
          format: 'png',
          data: 'base64-encoded-data',
          filename: 'export.png',
        },
      }

      expect(event.type).toBe(AppEventType.EXPORT_COMPLETE)
      expect(event.payload.format).toBe('png')
      expect(event.payload.data).toBe('base64-encoded-data')
      expect(event.payload.filename).toBe('export.png')
    })

    it('should create export error event correctly', () => {
      const error = new Error('Export failed')
      const event: ExportEvent = {
        type: AppEventType.EXPORT_ERROR,
        payload: {
          format: 'png',
          error,
        },
      }

      expect(event.type).toBe(AppEventType.EXPORT_ERROR)
      expect(event.payload.format).toBe('png')
      expect(event.payload.error).toBe(error)
    })
  })

  describe('dataLoadEvent', () => {
    it('should create data load request event correctly', () => {
      const event: DataLoadEvent = {
        type: AppEventType.DATA_LOAD_REQUEST,
        payload: {
          source: 'file',
          sourceId: 'file-123',
        },
      }

      expect(event.type).toBe(AppEventType.DATA_LOAD_REQUEST)
      expect(event.payload.source).toBe('file')
      expect(event.payload.sourceId).toBe('file-123')
    })

    it('should create data load progress event correctly', () => {
      const event: DataLoadEvent = {
        type: AppEventType.DATA_LOAD_PROGRESS,
        payload: {
          source: 'file',
          sourceId: 'file-123',
          progress: 0.75,
        },
      }

      expect(event.type).toBe(AppEventType.DATA_LOAD_PROGRESS)
      expect(event.payload.source).toBe('file')
      expect(event.payload.sourceId).toBe('file-123')
      expect(event.payload.progress).toBe(0.75)
    })

    it('should create data load complete event correctly', () => {
      const event: DataLoadEvent = {
        type: AppEventType.DATA_LOAD_COMPLETE,
        payload: {
          source: 'file',
          sourceId: 'file-123',
          data: {
            shapes: [
              { id: 'shape-1', type: 'rectangle' },
              { id: 'shape-2', type: 'circle' },
            ],
          },
        },
      }

      expect(event.type).toBe(AppEventType.DATA_LOAD_COMPLETE)
      expect(event.payload.source).toBe('file')
      expect(event.payload.sourceId).toBe('file-123')
      expect(event.payload.data).toEqual({
        shapes: [
          { id: 'shape-1', type: 'rectangle' },
          { id: 'shape-2', type: 'circle' },
        ],
      })
    })

    it('should create data load error event correctly', () => {
      const error = new Error('Data load failed')
      const event: DataLoadEvent = {
        type: AppEventType.DATA_LOAD_ERROR,
        payload: {
          source: 'file',
          sourceId: 'file-123',
          error,
        },
      }

      expect(event.type).toBe(AppEventType.DATA_LOAD_ERROR)
      expect(event.payload.source).toBe('file')
      expect(event.payload.sourceId).toBe('file-123')
      expect(event.payload.error).toBe(error)
    })
  })

  describe('storageEvent', () => {
    it('should create storage save request event correctly', () => {
      const event: StorageEvent = {
        type: AppEventType.STORAGE_SAVE_REQUEST,
        payload: {
          key: 'document-123',
          data: {
            shapes: [
              { id: 'shape-1', type: 'rectangle' },
              { id: 'shape-2', type: 'circle' },
            ],
          },
        },
      }

      expect(event.type).toBe(AppEventType.STORAGE_SAVE_REQUEST)
      expect(event.payload.key).toBe('document-123')
      expect(event.payload.data).toEqual({
        shapes: [
          { id: 'shape-1', type: 'rectangle' },
          { id: 'shape-2', type: 'circle' },
        ],
      })
    })

    it('should create storage save complete event correctly', () => {
      const event: StorageEvent = {
        type: AppEventType.STORAGE_SAVE_COMPLETE,
        payload: {
          key: 'document-123',
          timestamp: Date.now(),
        },
      }

      expect(event.type).toBe(AppEventType.STORAGE_SAVE_COMPLETE)
      expect(event.payload.key).toBe('document-123')
      expect(event.payload.timestamp).toBeDefined()
    })

    it('should create storage load request event correctly', () => {
      const event: StorageEvent = {
        type: AppEventType.STORAGE_LOAD_REQUEST,
        payload: {
          key: 'document-123',
        },
      }

      expect(event.type).toBe(AppEventType.STORAGE_LOAD_REQUEST)
      expect(event.payload.key).toBe('document-123')
    })

    it('should create storage load complete event correctly', () => {
      const event: StorageEvent = {
        type: AppEventType.STORAGE_LOAD_COMPLETE,
        payload: {
          key: 'document-123',
          data: {
            shapes: [
              { id: 'shape-1', type: 'rectangle' },
              { id: 'shape-2', type: 'circle' },
            ],
          },
        },
      }

      expect(event.type).toBe(AppEventType.STORAGE_LOAD_COMPLETE)
      expect(event.payload.key).toBe('document-123')
      expect(event.payload.data).toEqual({
        shapes: [
          { id: 'shape-1', type: 'rectangle' },
          { id: 'shape-2', type: 'circle' },
        ],
      })
    })

    it('should create storage error event correctly', () => {
      const error = new Error('Storage operation failed')
      const event: StorageEvent = {
        type: AppEventType.STORAGE_ERROR,
        payload: {
          key: 'document-123',
          operation: 'save',
          error,
        },
      }

      expect(event.type).toBe(AppEventType.STORAGE_ERROR)
      expect(event.payload.key).toBe('document-123')
      expect(event.payload.operation).toBe('save')
      expect(event.payload.error).toBe(error)
    })
  })
})
