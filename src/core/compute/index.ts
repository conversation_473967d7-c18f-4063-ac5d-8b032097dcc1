/**
 * Core Computation Module Index
 *
 * @remarks
 * This file serves as the entry point and barrel export for the core computation module.
 * It re-exports the primary classes, types, and interfaces related to geometric and
 * other element-based computations. Key exports include:
 * - {@link ComputeFacade}: The main class for requesting various computations.
 * - {@link StrategyRegistry}: The class responsible for managing and providing different
 *   computation algorithms (strategies).
 * - Various strategy interfaces (e.g., {@link AreaCalculatorStrategy},
 *   {@link PerimeterCalculatorStrategy}) defining contracts for specific calculation types.
 * - Core computation-related types (e.g., {@link ComputeOperation}, {@link ComputeResult},
 *   {@link BoundingBox}, {@link TransformOptions}) imported from `@/types/core/compute`
 *   and `@/types/core`.
 *
 * This module adheres to Dependency Inversion principles by not creating or exporting
 * singleton instances directly. Instead, it provides the necessary building blocks
 * for the application to compose and inject dependencies as needed.
 *
 * It also re-exports all strategies from the `./strategies` submodule.
 *
 * @module core/compute/index
 */

// Export ComputeFacade facade class, passing in StrategyRegistry
// Export computation types from the correct location
import type { BoundingBox } from '@/types/core'

export { ComputeFacade } from './ComputeFacade'

// Export strategies
export * from './strategies'

// Export StrategyRegistry
export { StrategyRegistry } from './StrategyRegistry'

// Export BoundingBox type
export type { BoundingBox }

// Export all strategy interfaces
export type {
  AreaCalculatorStrategy,
  CostCalculatorStrategy,
  DistanceCalculatorStrategy,
  MaterialCalculatorStrategy,
  PerimeterCalculatorStrategy,
  SpacePlanningStrategy,
} from '@/types/core/compute'

// Export core computation types
export {
  ComputeOperation,
  ComputeOperationType,
} from '@/types/core/compute'

export type {
  ComputeOptions,
  ComputeResult,
  TransformOptions,
} from '@/types/core/compute'
