import type {
  MaterialCalculationOptions,
  MaterialCalculationResult,
  MaterialCalculatorStrategy,
} from '@/types/core/compute/materialComputeTypes'

import type { Element } from '@/types/core/element/element'
import { describe, expect, it } from 'vitest'

describe('material Calculator Strategy Interface', () => {
  it('should define the required methods for material calculation strategies', () => {
    // Create a mock implementation of MaterialCalculatorStrategy
    class MockMaterialCalculator implements MaterialCalculatorStrategy {
      calculateMaterialAmount(element: Element, materialType: string, options?: MaterialCalculationOptions): MaterialCalculationResult {
        // Simple mock implementation
        return {
          amount: 100,
          unit: 'm²',
        }
      }

      getElementType(): string {
        return 'mock-element'
      }
    }

    const calculator = new MockMaterialCalculator()

    // Test the interface implementation
    expect(typeof calculator.calculateMaterialAmount).toBe('function')
    expect(typeof calculator.getElementType).toBe('function')

    // Test the mock implementation
    const mockElement = { id: 'test-element' } as Element
    const result = calculator.calculateMaterialAmount(mockElement, 'paint')
    expect(result.amount).toBe(100)
    expect(result.unit).toBe('m²')
    expect(calculator.getElementType()).toBe('mock-element')
  })

  it('should allow implementation for different material types', () => {
    // Create a mock paint material calculator
    class MockPaintCalculator implements MaterialCalculatorStrategy {
      calculateMaterialAmount(element: Element, materialType: string, options?: MaterialCalculationOptions): MaterialCalculationResult {
        // Mock implementation for paint
        const area = 50 // Simulated area
        const coats = options?.coats || 2
        const coverage = options?.coverage || 10 // m² per liter

        const amount = (area * coats) / coverage

        return {
          amount,
          unit: 'liters',
          coats,
        }
      }

      getElementType(): string {
        return 'rectangle'
      }
    }

    // Create a mock tile material calculator
    class MockTileCalculator implements MaterialCalculatorStrategy {
      calculateMaterialAmount(element: Element, materialType: string, options?: MaterialCalculationOptions): MaterialCalculationResult {
        // Mock implementation for tiles
        const area = 30 // Simulated area
        const tileSize = options?.unitSize || { width: 0.3, height: 0.3 } // Default 30x30cm
        const tileArea = tileSize.width * tileSize.height
        const wastageRate = options?.wastageRate || 0.1 // 10% wastage

        const tilesNeeded = Math.ceil((area * (1 + wastageRate)) / tileArea)

        return {
          amount: area,
          unit: 'm²',
          unitCount: tilesNeeded,
          unitType: 'tiles',
          amountWithWastage: area * (1 + wastageRate),
        }
      }

      getElementType(): string {
        return 'rectangle'
      }
    }

    const paintCalculator = new MockPaintCalculator()
    const tileCalculator = new MockTileCalculator()

    // Test the implementations
    const mockElement = { id: 'test-element' } as Element

    // Test paint calculator with default options
    let result = paintCalculator.calculateMaterialAmount(mockElement, 'paint')
    expect(result.amount).toBe(10) // (50 * 2) / 10 = 10 liters
    expect(result.unit).toBe('liters')
    expect(result.coats).toBe(2)

    // Test paint calculator with custom options
    result = paintCalculator.calculateMaterialAmount(mockElement, 'paint', { coats: 3, coverage: 8 })
    expect(result.amount).toBe(18.75) // (50 * 3) / 8 = 18.75 liters
    expect(result.coats).toBe(3)

    // Test tile calculator with default options
    result = tileCalculator.calculateMaterialAmount(mockElement, 'tile')
    expect(result.amount).toBe(30)
    expect(result.unit).toBe('m²')
    expect(result.unitCount).toBe(367) // Math.ceil((30 * 1.1) / (0.3 * 0.3)) = 367
    expect(result.unitType).toBe('tiles')
    expect(result.amountWithWastage).toBe(33) // 30 * 1.1 = 33

    // Test tile calculator with custom options
    result = tileCalculator.calculateMaterialAmount(mockElement, 'tile', {
      unitSize: { width: 0.5, height: 0.5 }, // 50x50cm tiles
      wastageRate: 0.15, // 15% wastage
    })
    expect(result.unitCount).toBe(138) // Math.ceil((30 * 1.15) / (0.5 * 0.5)) = 138
    expect(result.amountWithWastage).toBe(34.5) // 30 * 1.15 = 34.5
  })

  it('should handle material calculation options correctly', () => {
    // Create a mock material calculator that uses all options
    class MockComprehensiveMaterialCalculator implements MaterialCalculatorStrategy {
      calculateMaterialAmount(element: Element, materialType: string, options?: MaterialCalculationOptions): MaterialCalculationResult {
        // Get element area (mock value)
        const area = 40 // m²

        // Default values
        const unitSize = options?.unitSize || { width: 0.3, height: 0.3 } // 30x30cm
        const wastageRate = options?.wastageRate || 0.1 // 10% wastage
        const includeJoints = options?.includeJoints || false
        const jointWidth = options?.jointWidth || 0.002 // 2mm
        const coverage = options?.coverage || 10 // 10 m²/liter
        const coats = options?.coats || 2

        // Calculate based on material type
        if (materialType === 'paint') {
          const paintAmount = (area * coats) / coverage
          return {
            amount: paintAmount,
            unit: 'liters',
            coats,
          }
        }
        else if (materialType === 'tile') {
          // Calculate effective tile area including joints if needed
          let effectiveTileArea = unitSize.width * unitSize.height
          if (includeJoints) {
            const effectiveWidth = unitSize.width + jointWidth
            const effectiveHeight = unitSize.height + jointWidth
            effectiveTileArea = effectiveWidth * effectiveHeight
          }

          // Calculate tiles needed with wastage
          const tilesNeeded = Math.ceil((area * (1 + wastageRate)) / effectiveTileArea)

          return {
            amount: area,
            unit: 'm²',
            unitCount: tilesNeeded,
            unitType: 'tiles',
            amountWithWastage: area * (1 + wastageRate),
          }
        }
        else {
          // Generic material calculation
          return {
            amount: area,
            unit: 'm²',
          }
        }
      }

      getElementType(): string {
        return 'comprehensive'
      }
    }

    const comprehensiveCalculator = new MockComprehensiveMaterialCalculator()
    const mockElement = { id: 'test-element' } as Element

    // Test with paint material
    let result = comprehensiveCalculator.calculateMaterialAmount(mockElement, 'paint', {
      coverage: 8,
      coats: 3,
    })
    expect(result.amount).toBe(15) // (40 * 3) / 8 = 15 liters
    expect(result.unit).toBe('liters')
    expect(result.coats).toBe(3)

    // Test with tile material and no joints
    result = comprehensiveCalculator.calculateMaterialAmount(mockElement, 'tile', {
      unitSize: { width: 0.4, height: 0.4 }, // 40x40cm tiles
      wastageRate: 0.15, // 15% wastage
    })
    expect(result.amount).toBe(40)
    expect(result.unit).toBe('m²')
    expect(result.unitCount).toBe(288) // Math.ceil((40 * 1.15) / (0.4 * 0.4)) = 288
    expect(result.unitType).toBe('tiles')
    expect(result.amountWithWastage).toBe(46) // 40 * 1.15 = 46

    // Test with tile material and joints
    result = comprehensiveCalculator.calculateMaterialAmount(mockElement, 'tile', {
      unitSize: { width: 0.4, height: 0.4 }, // 40x40cm tiles
      wastageRate: 0.15, // 15% wastage
      includeJoints: true,
      jointWidth: 0.005, // 5mm joints
    })
    expect(result.unitCount).toBe(281) // Math.ceil((40 * 1.15) / ((0.4 + 0.005) * (0.4 + 0.005))) = 281
  })

  it('should validate MaterialCalculationResult interface', () => {
    // Create valid result objects of different types
    const basicResult: MaterialCalculationResult = {
      amount: 50,
      unit: 'm²',
    }

    const detailedResult: MaterialCalculationResult = {
      amount: 50,
      unit: 'm²',
      unitCount: 200,
      unitType: 'tiles',
      amountWithWastage: 55,
      boxes: 20,
      coats: 2,
    }

    // Verify the result objects are valid
    expect(basicResult.amount).toBe(50)
    expect(basicResult.unit).toBe('m²')
    expect(basicResult.unitCount).toBeUndefined()

    expect(detailedResult.amount).toBe(50)
    expect(detailedResult.unit).toBe('m²')
    expect(detailedResult.unitCount).toBe(200)
    expect(detailedResult.unitType).toBe('tiles')
    expect(detailedResult.amountWithWastage).toBe(55)
    expect(detailedResult.boxes).toBe(20)
    expect(detailedResult.coats).toBe(2)
  })

  it('should validate MaterialCalculationOptions interface', () => {
    // Create valid options objects
    const tileOptions: MaterialCalculationOptions = {
      unitSize: { width: 0.3, height: 0.3 },
      wastageRate: 0.1,
      includeJoints: true,
      jointWidth: 0.002,
    }

    const paintOptions: MaterialCalculationOptions = {
      coverage: 8,
      coats: 3,
    }

    // Verify the options objects are valid
    expect(tileOptions.unitSize).toEqual({ width: 0.3, height: 0.3 })
    expect(tileOptions.wastageRate).toBe(0.1)
    expect(tileOptions.includeJoints).toBe(true)
    expect(tileOptions.jointWidth).toBe(0.002)

    expect(paintOptions.coverage).toBe(8)
    expect(paintOptions.coats).toBe(3)
  })
})
