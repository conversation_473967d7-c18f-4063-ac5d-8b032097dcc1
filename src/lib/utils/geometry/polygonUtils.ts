/**
 * Utility Functions for Polygon Geometry
 *
 * @remarks
 * This module provides a collection of utility functions for working with polygons,
 * defined by an array of vertices (points). These utilities include:
 * - Creating vertices for regular polygons.
 * - Calculating area (using the <PERSON><PERSON><PERSON> formula) and perimeter.
 * - Finding the geometric center (centroid).
 * - Performing transformations like scaling and rotation.
 * - Checking if a point is inside a polygon (using the ray casting algorithm).
 * - Calculating the bounding box of a polygon.
 * - Calculating the length of an open polyline (sum of segment lengths).
 *
 * Functions typically expect input points as an array of {@link IPoint} (aliased from `PointData`)
 * and may return new {@link PointClass} instances or arrays thereof.
 *
 * @module lib/utils/geometry/polygonUtils
 */

import type { PointData as IPoint } from '../../../types/core/element/geometry/point' // Use PointData and alias
import { BoundingBoxClass } from './BoundingBoxClass'
import { PointClass } from './PointClass'

/**
 * Creates an array of {@link PointClass} instances representing the vertices of a regular polygon.
 *
 * @param center - The center {@link IPoint} of the polygon.
 * @param radius - The distance from the center to each vertex (circumradius).
 *                 Will be treated as its absolute value if negative.
 * @param sides - The number of sides for the polygon (must be 3 or more).
 * @returns An array of {@link PointClass} instances representing the vertices.
 * @throws {@link Error} if `sides` is less than 3.
 */
export function createRegularPolygon(center: IPoint, radius: number, sides: number): PointClass[] {
  if (sides < 3) {
    throw new Error('A polygon must have at least 3 sides.')
  }
  if (radius < 0) {
    console.warn(`createRegularPolygon: Negative radius (${radius}) provided. Using absolute value.`)
    radius = Math.abs(radius)
  }

  const points: PointClass[] = []
  const angleStep = (Math.PI * 2) / sides

  for (let i = 0; i < sides; i++) {
    // Start from 0 angle (positive x-axis) or adjust if a specific orientation is needed
    const angle = i * angleStep
    const x = center.x + radius * Math.cos(angle)
    const y = center.y + radius * Math.sin(angle)
    points.push(new PointClass(x, y))
  }
  return points
}

/**
 * Calculates the area of a polygon using the Shoelace formula.
 *
 * @remarks
 * The vertices (`points`) must be ordered (either clockwise or counter-clockwise).
 * The formula calculates twice the signed area; the absolute value is taken and halved.
 *
 * @param points - An array of {@link IPoint} objects representing the polygon's vertices.
 * @returns The area of the polygon. Returns `0` if fewer than 3 points are provided.
 */
export function calculateArea(points: IPoint[]): number {
  if (points.length < 3) {
    return 0 // A polygon needs at least 3 vertices
  }

  // 确保所有点都有有效的坐标
  for (const point of points) {
    if (typeof point.x !== 'number'
      || typeof point.y !== 'number'
      || Number.isNaN(point.x)
      || Number.isNaN(point.y)
      || !Number.isFinite(point.x)
      || !Number.isFinite(point.y)) {
      console.warn('Invalid point coordinates detected in calculateArea:', point)
      return 0
    }
  }

  let area = 0
  for (let i = 0; i < points.length; i++) {
    const p1 = points[i]
    const p2 = points[(i + 1) % points.length] // Next point, wraps around
    area += (p1.x * p2.y) - (p2.x * p1.y)
  }

  const result = Math.abs(area / 2)

  // 记录计算结果，便于调试
  console.warn(`[polygonUtils] calculateArea: points=${JSON.stringify(points)}, result=${result}`)

  return result
}

/**
 * Calculates the perimeter of a polygon (or the length of a polyline if not closed by context).
 *
 * @remarks
 * If `points` has 2 elements, it calculates the distance between them (length of a line segment).
 * If `points` has 3 or more elements, it sums the lengths of the segments connecting
 * consecutive points, including the segment from the last point back to the first.
 *
 * @param points - An array of {@link IPoint} objects representing the vertices in order.
 * @returns The perimeter of the polygon. Returns `0` if fewer than 2 points are provided.
 */
export function calculatePerimeter(points: IPoint[]): number {
  if (points.length < 2) {
    return 0
  }

  // 确保所有点都有有效的坐标
  for (const point of points) {
    if (typeof point.x !== 'number'
      || typeof point.y !== 'number'
      || Number.isNaN(point.x)
      || Number.isNaN(point.y)
      || !Number.isFinite(point.x)
      || !Number.isFinite(point.y)) {
      console.warn('Invalid point coordinates detected in calculatePerimeter:', point)
      return 0
    }
  }

  if (points.length === 2) { // A line
    const dx = points[1].x - points[0].x
    const dy = points[1].y - points[0].y
    const result = Math.sqrt(dx * dx + dy * dy)
    console.warn(`[polygonUtils] calculatePerimeter (line): points=${JSON.stringify(points)}, result=${result}`)
    return result
  }

  let perimeter = 0
  for (let i = 0; i < points.length; i++) {
    const p1 = points[i]
    const p2 = points[(i + 1) % points.length] // Next point, wraps around
    const dx = p2.x - p1.x
    const dy = p2.y - p1.y
    perimeter += Math.sqrt(dx * dx + dy * dy)
  }

  console.warn(`[polygonUtils] calculatePerimeter (polygon): points=${JSON.stringify(points)}, result=${perimeter}`)
  return perimeter
}

/**
 * Calculates the geometric center (centroid) of a polygon.
 *
 * @remarks
 * For a simple (non-self-intersecting) polygon, the centroid is the average of its vertices' coordinates.
 *
 * @param points - An array of {@link IPoint} objects representing the polygon's vertices.
 * @returns A new {@link PointClass} instance representing the centroid.
 *          Returns `PointClass(0,0)` if the `points` array is empty.
 */
export function calculateCenter(points: IPoint[]): PointClass {
  if (points.length === 0) {
    return new PointClass(0, 0)
  }
  let sumX = 0
  let sumY = 0
  for (const p of points) {
    sumX += p.x
    sumY += p.y
  }
  return new PointClass(sumX / points.length, sumY / points.length)
}

/**
 * Scales a polygon relative to a specified origin point (or its centroid if no origin is provided).
 *
 * @param points - An array of {@link IPoint} objects representing the polygon's vertices.
 * @param scaleFactor - The factor by which to scale the polygon (e.g., 2 for double size, 0.5 for half size).
 * @param origin - Optional: The {@link IPoint} to scale around. If not provided,
 *                 the polygon's centroid (calculated using {@link calculateCenter}) is used as the origin.
 * @returns A new array of {@link PointClass} instances representing the vertices of the scaled polygon.
 *          Returns an empty array if the input `points` array is empty.
 */
export function scalePolygon(points: IPoint[], scaleFactor: number, origin?: IPoint): PointClass[] {
  if (points.length === 0) {
    return []
  }
  const actualOrigin = origin || calculateCenter(points)
  return points.map((p) => {
    const dx = p.x - actualOrigin.x
    const dy = p.y - actualOrigin.y
    return new PointClass(actualOrigin.x + dx * scaleFactor, actualOrigin.y + dy * scaleFactor, p.z)
  })
}

/**
 * Rotates a polygon around a specified origin point (or its centroid if no origin is provided).
 *
 * @param points - An array of {@link IPoint} objects representing the polygon's vertices.
 * @param angleRadians - The angle of rotation in radians.
 * @param origin - Optional: The {@link IPoint} to rotate around. If not provided,
 *                 the polygon's centroid (calculated using {@link calculateCenter}) is used as the origin.
 * @returns A new array of {@link PointClass} instances representing the vertices of the rotated polygon.
 *          Returns an empty array if the input `points` array is empty.
 */
export function rotatePolygon(points: IPoint[], angleRadians: number, origin?: IPoint): PointClass[] {
  if (points.length === 0) {
    return []
  }
  const actualOrigin = origin || calculateCenter(points)
  const cosA = Math.cos(angleRadians)
  const sinA = Math.sin(angleRadians)
  return points.map((p) => {
    const dx = p.x - actualOrigin.x
    const dy = p.y - actualOrigin.y
    const newX = actualOrigin.x + dx * cosA - dy * sinA
    const newY = actualOrigin.y + dx * sinA + dy * cosA
    return new PointClass(newX, newY, p.z)
  })
}

/**
 * Checks if a given point is inside a polygon using the ray casting algorithm.
 *
 * @remarks
 * This algorithm counts the number of times a ray extending from the point in a fixed
 * direction (typically positive x-axis) intersects the edges of the polygon.
 * An odd number of intersections means the point is inside; an even number means outside.
 * This method works for simple polygons (non-self-intersecting).
 *
 * @param point - The {@link IPoint} to check.
 * @param polygonVertices - An array of {@link IPoint} objects representing the polygon's vertices, in order.
 *                          Must have at least 3 vertices.
 * @returns `true` if the point is inside the polygon, `false` otherwise or if the polygon is invalid.
 */
export function isPointInside(point: IPoint, polygonVertices: IPoint[]): boolean {
  if (polygonVertices.length < 3) {
    return false // Not a polygon
  }

  // 确保所有点都有有效的坐标
  if (typeof point.x !== 'number'
    || typeof point.y !== 'number'
    || Number.isNaN(point.x)
    || Number.isNaN(point.y)
    || !Number.isFinite(point.x)
    || !Number.isFinite(point.y)) {
    console.warn('Invalid point coordinates detected in isPointInside:', point)
    return false
  }

  for (const vertex of polygonVertices) {
    if (typeof vertex.x !== 'number'
      || typeof vertex.y !== 'number'
      || Number.isNaN(vertex.x)
      || Number.isNaN(vertex.y)
      || !Number.isFinite(vertex.x)
      || !Number.isFinite(vertex.y)) {
      console.warn('Invalid vertex coordinates detected in isPointInside:', vertex)
      return false
    }
  }

  let crossings = 0
  const numVertices = polygonVertices.length

  for (let i = 0; i < numVertices; i++) {
    const p1 = polygonVertices[i]
    const p2 = polygonVertices[(i + 1) % numVertices]

    // Check if the ray crosses the edge (p1, p2)
    if (((p1.y <= point.y && point.y < p2.y) || (p2.y <= point.y && point.y < p1.y))
      && (point.x < (p2.x - p1.x) * (point.y - p1.y) / (p2.y - p1.y) + p1.x)) {
      crossings++
    }
  }
  return crossings % 2 === 1 // Odd number of crossings means inside
}

/**
 * Calculates the axis-aligned bounding box of a polygon defined by an array of points.
 *
 * @remarks
 * This function delegates to {@link BoundingBoxClass.fromPointsArray}.
 *
 * @param points - An array of {@link IPoint} objects representing the polygon's vertices.
 * @returns A {@link BoundingBoxClass} instance.
 *          Returns a 0x0 BBox at origin if the `points` array is empty or null.
 */
export function calculateBoundingBox(points: IPoint[]): BoundingBoxClass {
  return BoundingBoxClass.fromPointsArray(points)
}

/**
 * Calculates the total length of an open polyline (sum of the lengths of its segments).
 *
 * @remarks
 * This function does not connect the last point back to the first, making it suitable
 * for open paths rather than closed polygon perimeters (for which {@link calculatePerimeter}
 * should be used if a closing segment is implied).
 *
 * @param points - An array of {@link IPoint} objects representing the polyline's vertices in order.
 * @returns The total length of the open polyline. Returns `0` if fewer than 2 points are provided.
 */
export function calculateOpenPolylineLength(points: IPoint[]): number {
  if (points.length < 2) {
    return 0
  }

  // 确保所有点都有有效的坐标
  for (const point of points) {
    if (typeof point.x !== 'number'
      || typeof point.y !== 'number'
      || Number.isNaN(point.x)
      || Number.isNaN(point.y)
      || !Number.isFinite(point.x)
      || !Number.isFinite(point.y)) {
      console.warn('Invalid point coordinates detected in calculateOpenPolylineLength:', point)
      return 0
    }
  }

  let length = 0
  for (let i = 0; i < points.length - 1; i++) {
    const p1 = points[i]
    const p2 = points[i + 1]
    const dx = p2.x - p1.x
    const dy = p2.y - p1.y
    length += Math.sqrt(dx * dx + dy * dy)
  }

  console.warn(`[polygonUtils] calculateOpenPolylineLength: points=${JSON.stringify(points)}, result=${length}`)
  return length
}
