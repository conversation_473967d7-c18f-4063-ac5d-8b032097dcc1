import { describe, expect, it } from 'vitest'

// Test basic math utility functions
describe('math Utils - Basic Tests', () => {
  it('should handle basic math operations', () => {
    // Test basic arithmetic
    expect(1 + 1).toBe(2)
    expect(2 * 3).toBe(6)
    expect(10 / 2).toBe(5)
    expect(5 - 3).toBe(2)
  })

  it('should handle floating point operations', () => {
    expect(0.1 + 0.2).toBeCloseTo(0.3)
    expect(Math.PI).toBeCloseTo(3.14159, 4)
    expect(Math.sqrt(4)).toBe(2)
    expect(2 ** 3).toBe(8)
  })

  it('should handle trigonometric functions', () => {
    expect(Math.sin(0)).toBe(0)
    expect(Math.cos(0)).toBe(1)
    expect(Math.tan(0)).toBe(0)
    expect(Math.sin(Math.PI / 2)).toBeCloseTo(1)
  })

  it('should handle rounding operations', () => {
    expect(Math.round(4.7)).toBe(5)
    expect(Math.round(4.4)).toBe(4)
    expect(Math.floor(4.7)).toBe(4)
    expect(Math.ceil(4.1)).toBe(5)
  })

  it('should handle min/max operations', () => {
    expect(Math.min(1, 2, 3)).toBe(1)
    expect(Math.max(1, 2, 3)).toBe(3)
    expect(Math.abs(-5)).toBe(5)
    expect(Math.abs(5)).toBe(5)
  })
})
