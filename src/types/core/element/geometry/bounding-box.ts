/**
 * Defines the interface for a Bounding Box shape, typically used to represent the extents of other elements.
 *
 * @remarks
 * This file provides the `BoundingBox` interface, which is often based on a set of control points
 * (e.g., 8 points for a resizable box, or 9 including a center point).
 * A bounding box is the smallest rectangle that completely contains a given shape or group of shapes.
 * It is commonly used for operations like collision detection, layout calculations, selection highlighting,
 * and rendering optimizations.
 *
 * @module types/core/element/geometry/bounding-box
 * @see {@link ShapeElement} as a base interface that a `BoundingBox` might extend if it's also a render-able element.
 * @see {@link Point} for the type definition of control points.
 */

import type Point from '@/types/core/element/geometry/point'
import type { ShapeElement } from '@/types/core/elementDefinitions' // Assuming BoundingBox might be a specialized ShapeElement

/**
 * Represents an axis-aligned bounding box, defined by its control points, width, and height.
 *
 * @remarks
 * This interface extends {@link ShapeElement}, implying it can have common element properties
 * like ID, type, visibility, etc., in addition to its geometric properties.
 * The `points` array typically defines the corners and mid-points of the edges,
 * allowing for precise interaction and manipulation.
 */
export interface BoundingBox extends ShapeElement {
  /**
   * An array of 9 control points defining the bounding box.
   * The order is typically: `[topLeft, topCenter, topRight, middleLeft, center, middleRight, bottomLeft, bottomCenter, bottomRight]`.
   * These points are read-only as they are usually derived from the shape it bounds or its explicit dimensions.
   */
  readonly points: [Point, Point, Point, Point, Point, Point, Point, Point, Point]

  /**
   * The width of the bounding box.
   * This property is read-only as it's derived from the points or the bounded shape.
   */
  readonly width: number

  /**
   * The height of the bounding box.
   * This property is read-only as it's derived from the points or the bounded shape.
   */
  readonly height: number
}
