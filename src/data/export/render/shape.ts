/**
 * Shape SVG Renderers
 *
 * This module provides rendering functions for basic closed shapes,
 * including rectangles (and squares), ellipses (and circles), and polygons.
 *
 * - `renderRectangle` is used for both rectangles and squares.
 * - `renderEllipse` is used for both ellipses and circles.
 * - `renderPolygon` is used for all other polygons
 *   (triangles, quadrilaterals, pentagons, etc.).
 *
 * These functions generate SVG markup for the corresponding shape elements.
 *
 * @module render/shape
 */
import type { Ellipse, Polygon, Rectangle } from '@/types/core'
import type Point from '@/types/core/element/geometry/point'
import { getShapeStyle, getTransform } from './general'

export async function renderRectangle(e: Rectangle): Promise<string> {
  const pos = e.position
  const width = e.properties!.width! as number
  const height = e.properties!.height! as number
  const x = pos.x - width / 2
  const y = pos.y - height / 2

  const { fill, stroke, strokeWidth, patternStr } = getShapeStyle(e)
  const { cornerRadius, rotation } = e
  const transform = getTransform(rotation, pos)

  return (
    `${patternStr}<rect x="${x}" y="${y}" width="${width}" height="${height}"${
      cornerRadius ? ` rx="${cornerRadius}" ry="${cornerRadius}"` : ''
    } fill="${fill}" stroke="${stroke}" stroke-width="${strokeWidth}"`
    + `${transform} />`
  )
}

export async function renderEllipse(e: Ellipse): Promise<string> {
  const pos = e.position
  const rx = (e.properties.radiusX ?? e.properties.radius!) as number
  const ry = (e.properties.radiusY ?? e.properties.radius!) as number

  const { fill, stroke, strokeWidth, patternStr } = getShapeStyle(e)
  const rotation = e.rotation
  const transform = getTransform(rotation, pos)

  return (
    `${patternStr}<ellipse cx="${pos.x}" cy="${pos.y}" rx="${rx}" ry="${ry}"`
    + ` fill="${fill}" stroke="${stroke}" stroke-width="${strokeWidth}"`
    + `${transform} />`
  )
}

export async function renderPolygon(e: Polygon): Promise<string> {
  const zoom = (e.properties!.radius! as number) / 100
  const pos = e.position
  const points = (e.points || []).map((p: Point) =>
    `${p.x * zoom + pos.x},${p.y * zoom + pos.y}`).join(' ')

  const { fill, stroke, strokeWidth, patternStr } = getShapeStyle(e)
  const rotation = e.rotation
  const transform = getTransform(rotation, pos)

  return (
    `${patternStr}<polygon points="${points}"`
    + ` fill="${fill}" stroke="${stroke}" stroke-width="${strokeWidth}"`
    + `${transform} />`
  )
}
