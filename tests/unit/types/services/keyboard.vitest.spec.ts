import type { KeyboardService, KeyEventData, KeyModifiers } from '@/types/services/keyboard'
import { describe, expect, it } from 'vitest'

describe('keyboard Types', () => {
  describe('keyModifiers', () => {
    it('should create valid key modifiers', () => {
      const modifiers: KeyModifiers = {
        altKey: true,
        ctrlKey: false,
        shiftKey: true,
        metaKey: false,
      }

      expect(modifiers.altKey).toBe(true)
      expect(modifiers.ctrlKey).toBe(false)
      expect(modifiers.shiftKey).toBe(true)
      expect(modifiers.metaKey).toBe(false)
    })
  })

  describe('keyEventData', () => {
    it('should create valid key event data', () => {
      const keyEventData: KeyEventData = {
        key: 'a',
        code: 'KeyA',
        modifiers: {
          altKey: false,
          ctrlKey: true,
          shiftKey: false,
          metaKey: false,
        },
      }

      expect(keyEventData.key).toBe('a')
      expect(keyEventData.code).toBe('KeyA')
      expect(keyEventData.modifiers.ctrlKey).toBe(true)
      expect(keyEventData.modifiers.altKey).toBe(false)
      expect(keyEventData.modifiers.shiftKey).toBe(false)
      expect(keyEventData.modifiers.metaKey).toBe(false)
    })

    it('should allow optional originalEvent', () => {
      const keyEventData: KeyEventData = {
        key: 'a',
        code: 'KeyA',
        modifiers: {
          altKey: false,
          ctrlKey: true,
          shiftKey: false,
          metaKey: false,
        },
        originalEvent: undefined,
      }

      expect(keyEventData.originalEvent).toBeUndefined()
    })
  })

  describe('keyboardService', () => {
    it('should define the required methods', () => {
      // Create a mock implementation of KeyboardService to verify the interface
      const mockKeyboardService: KeyboardService = {
        initialize: () => {},
        cleanup: () => {},
        registerKeyBinding: () => () => {},
        unregisterKeyBinding: () => {},
        getKeyBindings: () => new Map(),
        addKeyDownListener: () => () => {},
        addKeyUpListener: () => () => {},
      }

      expect(typeof mockKeyboardService.initialize).toBe('function')
      expect(typeof mockKeyboardService.cleanup).toBe('function')
      expect(typeof mockKeyboardService.registerKeyBinding).toBe('function')
      expect(typeof mockKeyboardService.unregisterKeyBinding).toBe('function')
      expect(typeof mockKeyboardService.getKeyBindings).toBe('function')
      expect(typeof mockKeyboardService.addKeyDownListener).toBe('function')
      expect(typeof mockKeyboardService.addKeyUpListener).toBe('function')
    })
  })
})
