/**
 * Material Calculation Strategy for Stone (e.g., Tiles, Slabs)
 *
 * @remarks
 * This strategy implements the {@link IMaterialCalculatorStrategy} to calculate
 * the required amount of stone material (like marble, granite, tiles) for a given area.
 *
 * It first determines the area of the input `element` (currently assuming the element
 * has a `getArea()` or `compute.area()` method - marked as TODO for refactoring).
 *
 * Key inputs from {@link MaterialCalculationOptions} include:
 * - `stoneType`: Affects default wastage factors if `wastageRate` is not provided.
 * - `unitSize` (or `tileWidth`, `tileLength` as fallback): Dimensions of individual stone pieces.
 * - `thickness`: For volume and weight calculations.
 * - `patternMatching`: Influences default wastage.
 * - `jointWidth`: If `includeJoints` is true.
 * - `wastageRate`: Overrides default wastage factors.
 * - `density`: For weight calculation.
 * - `adhesivePerSqm`, `groutPerSqm`, `sealantPerSqm`: For calculating auxiliary materials.
 *
 * The strategy calculates the number of tiles needed, total stone area (including wastage),
 * volume, weight, and quantities of adhesive, grout, and sealant.
 *
 * The `calculateMaterialAmount` method returns a summarized {@link MaterialCalculationResult},
 * while a private `calculateDetailedMaterial` method provides a more granular breakdown.
 *
 * @module core/compute/strategies/material/StoneMaterialStrategy
 * @see {@link IMaterialCalculatorStrategy}
 * @see {@link MaterialCalculationOptions}
 * @see {@link MaterialCalculationResult}
 */
import type {
  MaterialCalculatorStrategy as IMaterialCalculatorStrategy, // Renamed for consistency
  MaterialCalculationOptions,
  MaterialCalculationResult,
} from '@/types/core/compute' // Corrected import path
import type { Element } from '@/types/core/elementDefinitions'
import { CoreError, ErrorType } from '@/services/system/error-service'
import { ElementType as CoreElementType } from '@/types/core/elementDefinitions'

export class StoneMaterialStrategy implements IMaterialCalculatorStrategy { // Renamed IMaterialCalculatorStrategy for consistency
  /**
   * Calculates the amount of stone material required.
   *
   * @param element - The element (e.g., floor, countertop) for which stone material is being calculated.
   *                  Expected to have a method to compute its area.
   * @param materialType - The type of material, expected to be 'stone'.
   * @param options - Optional {@link MaterialCalculationOptions} to customize the calculation.
   * @returns A {@link MaterialCalculationResult} summarizing the stone material quantity.
   * @throws {@link CoreError} if `materialType` is not 'stone', or if area calculation fails or is invalid,
   *         or if essential options like tile dimensions (if applicable) are missing or invalid.
   */
  public calculateMaterialAmount(element: Element, materialType: string, options?: MaterialCalculationOptions): MaterialCalculationResult {
    const detailedResult = this.calculateDetailedMaterial(element, materialType, options)
    // Ensure the returned object matches MaterialCalculationResult structure
    return {
      amount: detailedResult.totalArea, // This is totalStoneAreaWithWastage
      unit: 'm²', // Standard symbol for Square meters
      unitCount: detailedResult.tiles,
      unitType: 'tile',
      amountWithWastage: detailedResult.totalArea, // totalArea from detailedResult already includes wastage
      // Optionally, other details like volume or weight could be added if MaterialCalculationResult is extended
      // For now, they are part of the internal detailedResult but not the primary MaterialCalculationResult.
    }
  }

  /**
   * Calculates a detailed breakdown of stone and related installation materials.
   * @private
   * @param element - The element to be covered with stone.
   * @param materialType - Expected to be 'stone'.
   * @param options - Optional {@link MaterialCalculationOptions}.
   * @returns A record containing quantities for tiles, total area, volume, weight, adhesive, etc.
   * @throws {@link CoreError} if `materialType` is not 'stone', or if area calculation fails or is invalid,
   *         or if essential options like tile dimensions are missing or invalid.
   */
  private calculateDetailedMaterial(element: Element, materialType: string, options?: MaterialCalculationOptions): Record<string, number> {
    if (materialType.toLowerCase() !== 'stone') {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `StoneMaterialStrategy can only calculate for 'stone' material type, got ${materialType}`,
      )
    }

    // TODO: Refactor to avoid direct access to a potential 'getArea' or 'compute' property.
    const computableElement = element as unknown as { getArea?: () => number, compute?: { area?: () => number } }
    let area: number
    if (computableElement.getArea !== null && computableElement.getArea !== undefined && typeof computableElement.getArea === 'function') {
      area = computableElement.getArea()
    }
    else if (computableElement.compute?.area && typeof computableElement.compute.area === 'function') {
      area = computableElement.compute.area()
    }
    else {
      throw new CoreError(ErrorType.ComputationError, `Element ${element.id} does not have a method to calculate area.`)
    }

    if (typeof area !== 'number' || area < 0 || !Number.isFinite(area)) { // Area can be 0
      throw new CoreError(ErrorType.ComputationError, `Invalid area for element ${element.id}: ${area}`)
    }
    if (area === 0) {
      console.warn(`[StoneMaterialStrategy] Area for element ${element.id} is 0. No stone needed.`)
      return { tiles: 0, totalArea: 0, volume: 0, weight: 0, adhesive: 0, grout: 0, sealant: 0, wasteFactor: 0 }
    }

    const materialOptions = options ?? {}
    const stoneType = (materialOptions.stoneType !== null && materialOptions.stoneType !== undefined && materialOptions.stoneType !== '') ? materialOptions.stoneType : 'marble'
    // Use unitSize from options if available, otherwise default
    const tileWidth = materialOptions.unitSize?.width ?? materialOptions.tileWidth ?? 0.6
    const tileLength = materialOptions.unitSize?.height ?? materialOptions.tileLength ?? 0.6
    const thickness = (materialOptions.thickness !== null && materialOptions.thickness !== undefined && materialOptions.thickness !== 0) ? materialOptions.thickness : 0.02
    const patternMatching = materialOptions.patternMatching ?? false
    const jointWidth = (materialOptions.jointWidth !== null && materialOptions.jointWidth !== undefined && materialOptions.jointWidth !== 0) ? materialOptions.jointWidth : 0.003

    let wasteFactor = materialOptions.wastageRate !== undefined ? materialOptions.wastageRate / 100 : 0.1 // Use wastageRate from options if present (as percentage)

    if (materialOptions.wastageRate === null || materialOptions.wastageRate === undefined) { // Only apply default logic if wastageRate is not in options
      if (patternMatching) {
        switch (stoneType) {
          case 'marble':
            wasteFactor = 0.20
            break
          case 'granite':
            wasteFactor = 0.15
            break
          case 'travertine':
            wasteFactor = 0.18
            break
          case 'quartz':
            wasteFactor = 0.12
            break
          default:
            wasteFactor = 0.15
        }
      }
      else {
        switch (stoneType) {
          case 'marble':
            wasteFactor = 0.12
            break
          case 'granite':
            wasteFactor = 0.10
            break
          case 'travertine':
            wasteFactor = 0.12
            break
          case 'quartz':
            wasteFactor = 0.08
            break
          default:
            wasteFactor = 0.10
        }
      }
    }

    const effectiveTileWidth = tileWidth - (materialOptions.includeJoints ? jointWidth : 0)
    const effectiveTileLength = tileLength - (materialOptions.includeJoints ? jointWidth : 0)

    if (effectiveTileWidth <= 0 || effectiveTileLength <= 0) {
      throw new CoreError(ErrorType.InvalidParameter, 'Effective tile dimensions must be positive after considering joint width.')
    }
    const effectiveTileArea = effectiveTileWidth * effectiveTileLength

    const totalStoneAreaRaw = area // Raw area before wastage
    const totalStoneAreaWithWastage = totalStoneAreaRaw * (1 + wasteFactor)

    const tilesNeeded = Math.ceil(totalStoneAreaWithWastage / effectiveTileArea)
    const totalVolume = totalStoneAreaWithWastage * thickness
    const density = (materialOptions.density !== null && materialOptions.density !== undefined && materialOptions.density !== 0) ? materialOptions.density : 2700
    const totalWeight = totalVolume * density
    const adhesivePerSqm = (materialOptions.adhesivePerSqm !== null && materialOptions.adhesivePerSqm !== undefined && materialOptions.adhesivePerSqm !== 0) ? materialOptions.adhesivePerSqm : 5
    const adhesiveNeeded = totalStoneAreaRaw * adhesivePerSqm // Base adhesive on raw area
    const groutPerSqm = (materialOptions.groutPerSqm !== null && materialOptions.groutPerSqm !== undefined && materialOptions.groutPerSqm !== 0) ? materialOptions.groutPerSqm : 0.5
    const groutNeeded = totalStoneAreaRaw * groutPerSqm // Base grout on raw area
    const sealantPerSqm = (materialOptions.sealantPerSqm !== null && materialOptions.sealantPerSqm !== undefined && materialOptions.sealantPerSqm !== 0) ? materialOptions.sealantPerSqm : 0.1
    const sealantNeeded = totalStoneAreaRaw * sealantPerSqm // Base sealant on raw area

    return {
      tiles: tilesNeeded,
      totalArea: totalStoneAreaWithWastage, // This is the area including wastage
      volume: totalVolume,
      weight: totalWeight,
      adhesive: adhesiveNeeded,
      grout: groutNeeded,
      sealant: sealantNeeded,
      wasteFactor: wasteFactor * 100, // Return as percentage
    }
  }

  /**
   * Returns the primary element type this strategy is typically associated with.
   * @remarks Stone materials are often applied to rectangular or polygonal areas like floors or counter tops.
   * @returns {@link CoreElementType.RECTANGLE} (as a common example).
   */
  public getElementType(): string {
    return CoreElementType.RECTANGLE // Could also be POLYGON or a generic 'surface' type
  }

  /**
   * Returns the specific material type this strategy calculates.
   * @returns The string 'stone'.
   */
  public getMaterialType(): string {
    return 'stone'
  }
}
