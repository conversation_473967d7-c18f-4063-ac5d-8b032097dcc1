// Import all relevant types/interfaces
import type {
  <PERSON>hapeValidator,
  ValidatableShape,
  ValidationError,
  ValidationOptions,
  ValidationResult,
  ValidationWarning,
  ValidatorArc,
  ValidatorCircle,
  ValidatorCustom,
  ValidatorEllipse,
  ValidatorHexagon,
  ValidatorLine,
  ValidatorPath,
  ValidatorPolygon,
  ValidatorQuadratic,
  ValidatorRectangle,
  ValidatorShape, // Union type
  ValidatorTrapezium,
  ValidatorTriangle,
} from '@/types/core/element/validator'

// Import ElementType for interface checks
import type { ElementType } from '@/types/core/shape-type'

import { describe, expect, it } from 'vitest'

describe('core Element Validator Types', () => {
  it('should allow declaration using validation configuration/result interfaces', () => {
    const options: ValidationOptions | undefined = undefined
    const result: ValidationResult | undefined = undefined
    const error: ValidationError | undefined = undefined
    const warning: ValidationWarning | undefined = undefined

    expect(options).toBeUndefined()
    expect(result).toBeUndefined()
    expect(error).toBeUndefined()
    expect(warning).toBeUndefined()
  })

  it('should allow declaration using specific Validator* interfaces', () => {
    const validatable: ValidatableShape | undefined = undefined
    const circle: ValidatorCircle | undefined = undefined
    const ellipse: ValidatorEllipse | undefined = undefined
    const rect: ValidatorRectangle | undefined = undefined
    const poly: ValidatorPolygon | undefined = undefined
    const tri: ValidatorTriangle | undefined = undefined
    const hex: ValidatorHexagon | undefined = undefined
    const trap: ValidatorTrapezium | undefined = undefined
    const line: ValidatorLine | undefined = undefined
    const path: ValidatorPath | undefined = undefined
    const arc: ValidatorArc | undefined = undefined
    const quad: ValidatorQuadratic | undefined = undefined
    const custom: ValidatorCustom | undefined = undefined

    expect(validatable).toBeUndefined()
    expect(circle).toBeUndefined()
    expect(ellipse).toBeUndefined()
    expect(rect).toBeUndefined()
    expect(poly).toBeUndefined()
    expect(tri).toBeUndefined()
    expect(hex).toBeUndefined()
    expect(trap).toBeUndefined()
    expect(line).toBeUndefined()
    expect(path).toBeUndefined()
    expect(arc).toBeUndefined()
    expect(quad).toBeUndefined()
    expect(custom).toBeUndefined()
  })

  it('validatorShape union type should accept specific validator types', () => {
    // Example: Assign a ValidatorRectangle to the union type
    const rectExample: ValidatorRectangle = {
      id: 'rect-1',
      type: 'rectangle',
      width: 10,
      height: 20,
    }
    let unionShape: ValidatorShape = rectExample
    expect(unionShape.type).toBe('rectangle')

    // Example: Assign a ValidatorLine
    const lineExample: ValidatorLine = {
      id: 'line-1',
      type: 'line',
      start: { x: 0, y: 0 },
      end: { x: 1, y: 1 },
    }
    unionShape = lineExample
    expect(unionShape.type).toBe('line')
  })

  it('shapeValidator interface should define expected methods', () => {
    // Check if a mock object satisfies the interface
    const mockValidator: ShapeValidator = {
      validate: (shape: ValidatableShape, _options?: ValidationOptions): ValidationResult => {
        return { valid: shape.type !== 'custom' } // Dummy logic
      },
      validateType: (type: ElementType, _options?: ValidationOptions): ValidationResult => {
        return { valid: type === 'rectangle' } // Dummy logic
      },
    }

    // Call methods to ensure structure is correct
    expect(mockValidator.validate({ id: 'rect-1', type: 'rectangle' })).toEqual({ valid: true })
    expect(mockValidator.validate({ id: 'custom-1', type: 'custom' })).toEqual({ valid: false })
    expect(mockValidator.validateType('rectangle')).toEqual({ valid: true })
    expect(mockValidator.validateType('ellipse')).toEqual({ valid: false })
  })
})
