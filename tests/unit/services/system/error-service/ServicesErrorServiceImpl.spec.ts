import type { AppEventMap, EventBus } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ServicesErrorService } from '@/services/system/error-service/servicesErrorService'
import { ErrorSeverity, ErrorType } from '@/types/services/errors'
import { AppEventType } from '@/types/services/events'

describe('servicesErrorService', () => {
  let servicesErrorService: ServicesErrorService
  let mockEventBus: EventBus<AppEventMap>
  let mockLogger: LoggerService
  let publishSpy: ReturnType<typeof vi.fn>

  beforeEach(() => {
    publishSpy = vi.fn()

    mockEventBus = {
      publish: publishSpy,
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
      clear: vi.fn(),
    } as unknown as EventBus<AppEventMap>

    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    } as unknown as LoggerService

    servicesErrorService = new ServicesErrorService(mockEventBus, mockLogger)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('constructor', () => {
    it('should initialize with dependencies and log initialization', () => {
      expect(mockLogger.info).toHaveBeenCalledWith('[ServicesErrorService] Initialized.')
    })

    it('should extend ErrorService', () => {
      expect(servicesErrorService).toBeInstanceOf(ServicesErrorService)
    })
  })

  describe('handleServiceError', () => {
    it('should handle service error with all parameters', () => {
      const serviceName = 'TestService'
      const error = new Error('Test error message')
      const operation = 'testOperation'
      const metadata = { userId: '123', action: 'create' }

      servicesErrorService.handleServiceError(serviceName, error, operation, metadata)

      expect(publishSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: AppEventType.ErrorOccurred,
          payload: expect.objectContaining({
            message: 'Test error message',
            code: ErrorType.Runtime.toString(),
            severity: ErrorSeverity.High,
          }),
        }),
      )
    })

    it('should handle service error without metadata', () => {
      const serviceName = 'TestService'
      const error = new Error('Test error message')
      const operation = 'testOperation'

      servicesErrorService.handleServiceError(serviceName, error, operation)

      expect(publishSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: AppEventType.ErrorOccurred,
          payload: expect.objectContaining({
            message: 'Test error message',
          }),
        }),
      )
    })

    it('should handle error with custom error code', () => {
      const serviceName = 'TestService'
      const error = new Error('Test error message') as Error & { code?: string }
      error.code = 'CUSTOM_ERROR_CODE'
      const operation = 'testOperation'

      servicesErrorService.handleServiceError(serviceName, error, operation)

      expect(publishSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          payload: expect.objectContaining({
            code: 'CUSTOM_ERROR_CODE',
          }),
        }),
      )
    })

    it('should handle different service names', () => {
      const serviceNames = ['UserService', 'DataService', 'ValidationService']
      const error = new Error('Test error')
      const operation = 'test'

      serviceNames.forEach((serviceName) => {
        servicesErrorService.handleServiceError(serviceName, error, operation)
      })

      expect(publishSpy).toHaveBeenCalledTimes(3)
    })

    it('should handle empty service name', () => {
      const error = new Error('Test error')
      const operation = 'test'

      servicesErrorService.handleServiceError('', error, operation)

      expect(publishSpy).toHaveBeenCalledTimes(1)
    })

    it('should handle errors with valid error objects', () => {
      const serviceName = 'TestService'
      const error = new Error('Test error')
      const operation = 'test'

      servicesErrorService.handleServiceError(serviceName, error, operation)

      expect(publishSpy).toHaveBeenCalledTimes(1)
      expect(publishSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: AppEventType.ErrorOccurred,
        }),
      )
    })
  })

  describe('logServiceWarning', () => {
    it('should log service warning with all parameters', () => {
      const serviceName = 'TestService'
      const message = 'Warning message'
      const operation = 'testOperation'
      const metadata = { warningType: 'performance' }

      servicesErrorService.logServiceWarning(serviceName, message, operation, metadata)

      expect(mockLogger.warn).toHaveBeenCalledWith(
        `Service Log [${ErrorType.Warning.toString()}][${ErrorSeverity.Medium}]: Warning message`,
        expect.objectContaining({
          component: 'Service:TestService',
          operation: 'testOperation',
          metadata: { warningType: 'performance' },
        }),
      )
    })

    it('should log service warning without metadata', () => {
      const serviceName = 'TestService'
      const message = 'Warning message'
      const operation = 'testOperation'

      servicesErrorService.logServiceWarning(serviceName, message, operation)

      expect(mockLogger.warn).toHaveBeenCalledWith(
        `Service Log [${ErrorType.Warning.toString()}][${ErrorSeverity.Medium}]: Warning message`,
        expect.objectContaining({
          component: 'Service:TestService',
          operation: 'testOperation',
          metadata: undefined,
        }),
      )
    })

    it('should handle different warning messages', () => {
      const serviceName = 'TestService'
      const operation = 'test'
      const warnings = [
        'Performance warning',
        'Deprecation warning',
        'Configuration warning',
      ]

      warnings.forEach((message) => {
        servicesErrorService.logServiceWarning(serviceName, message, operation)
      })

      expect(mockLogger.warn).toHaveBeenCalledTimes(3)
      warnings.forEach((message, index) => {
        expect(mockLogger.warn.mock.calls[index][0]).toContain(message)
      })
    })

    it('should handle empty warning message', () => {
      const serviceName = 'TestService'
      const operation = 'test'

      servicesErrorService.logServiceWarning(serviceName, '', operation)

      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('Service Log'),
        expect.any(Object),
      )
    })

    it('should handle special characters in service name and operation', () => {
      const serviceName = 'Test-Service_v2.0'
      const message = 'Warning with special chars: @#$%^&*()'
      const operation = 'test-operation_with.dots'

      servicesErrorService.logServiceWarning(serviceName, message, operation)

      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining(message),
        expect.objectContaining({
          component: 'Service:Test-Service_v2.0',
          operation: 'test-operation_with.dots',
        }),
      )
    })
  })

  describe('error handling edge cases', () => {
    it('should handle very long error messages', () => {
      const serviceName = 'TestService'
      const longMessage = 'A'.repeat(1000)
      const error = new Error(longMessage)
      const operation = 'test'

      servicesErrorService.handleServiceError(serviceName, error, operation)

      expect(publishSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          payload: expect.objectContaining({
            message: longMessage,
          }),
        }),
      )
    })

    it('should handle circular references in metadata gracefully', () => {
      const serviceName = 'TestService'
      const error = new Error('Test error')
      const operation = 'test'
      const circularMetadata: any = { name: 'test' }
      circularMetadata.self = circularMetadata

      // Should not throw even with circular references
      expect(() => {
        servicesErrorService.handleServiceError(serviceName, error, operation, circularMetadata)
      }).not.toThrow()
    })
  })

  describe('integration scenarios', () => {
    it('should handle multiple service errors in sequence', () => {
      const services = ['Service1', 'Service2', 'Service3']
      const operations = ['op1', 'op2', 'op3']

      services.forEach((service, index) => {
        const error = new Error(`Error ${index + 1}`)
        servicesErrorService.handleServiceError(service, error, operations[index])
      })

      expect(publishSpy).toHaveBeenCalledTimes(3)
      // Logger.info is called once for initialization
      expect(mockLogger.info).toHaveBeenCalledWith('[ServicesErrorService] Initialized.')
    })

    it('should handle mixed error handling and warning logging', () => {
      const serviceName = 'TestService'
      const error = new Error('Test error')
      const operation = 'test'

      servicesErrorService.handleServiceError(serviceName, error, operation)
      servicesErrorService.logServiceWarning(serviceName, 'Test warning', operation)

      expect(publishSpy).toHaveBeenCalledTimes(1)
      // Logger.warn may be called multiple times (base class + service class)
      expect(mockLogger.warn).toHaveBeenCalled()
    })

    it('should maintain service context in error calls', () => {
      const serviceName = 'ConsistentService'
      const operation = 'consistentOperation'
      const metadata = { sessionId: 'session-123' }

      servicesErrorService.handleServiceError(serviceName, new Error('Error 1'), operation, metadata)
      servicesErrorService.logServiceWarning(serviceName, 'Warning 1', operation, metadata)

      expect(publishSpy).toHaveBeenCalledTimes(1)
      // Logger.warn may be called multiple times (base class + service class)
      expect(mockLogger.warn).toHaveBeenCalled()

      // Find the warning call from logServiceWarning (should contain "Service Log")
      const warningCalls = mockLogger.warn.mock.calls
      const serviceWarningCall = warningCalls.find(call =>
        call[0].includes('Service Log') && call[0].includes('Warning 1'),
      )
      expect(serviceWarningCall).toBeDefined()
      if (serviceWarningCall) {
        expect(serviceWarningCall[1].component).toBe('Service:ConsistentService')
        expect(serviceWarningCall[1].operation).toBe('consistentOperation')
      }
    })
  })
})
