import type { Element } from '@/types/core/elementDefinitions'
import { describe, expect, it } from 'vitest'
import { EllipseAreaStrategy } from '@/core/compute/strategies/area/EllipseAreaStrategy'
import { CoreError } from '@/services/system/error-service/coreError'
import { ElementType } from '@/types/core/elementDefinitions'

/**
 * Additional unit tests for the EllipseAreaStrategy class to improve coverage
 */
describe('ellipseAreaStrategy - Additional Tests', () => {
  let strategy: EllipseAreaStrategy

  beforeEach(() => {
    strategy = new EllipseAreaStrategy()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe(ElementType.ELLIPSE)
  })

  it('should calculate area correctly for a circle (equal radii)', () => {
    const radius = 5
    const ellipse: any = {
      id: 'circle-test',
      type: ElementType.ELLIPSE,
      visible: true,
      locked: false,
      metadata: {},
      radiusX: radius,
      radiusY: radius,
    }

    const area = strategy.calculateArea(ellipse)
    const expectedArea = Math.PI * radius * radius

    expect(area).toBeCloseTo(expectedArea, 5)
  })

  it('should calculate area correctly for an ellipse', () => {
    const radiusX = 8
    const radiusY = 3
    const ellipse: any = {
      id: 'ellipse-test',
      type: ElementType.ELLIPSE,
      visible: true,
      locked: false,
      metadata: {},
      radiusX,
      radiusY,
    }

    const area = strategy.calculateArea(ellipse)
    const expectedArea = Math.PI * radiusX * radiusY

    expect(area).toBeCloseTo(expectedArea, 5)
  })

  it('should handle very small radii', () => {
    const ellipse: any = {
      id: 'small-ellipse',
      type: ElementType.ELLIPSE,
      visible: true,
      locked: false,
      metadata: {},
      radiusX: 0.001,
      radiusY: 0.002,
    }

    const area = strategy.calculateArea(ellipse)
    const expectedArea = Math.PI * 0.001 * 0.002

    expect(area).toBeCloseTo(expectedArea, 9)
  })

  it('should handle large radii', () => {
    const ellipse: any = {
      id: 'large-ellipse',
      type: ElementType.ELLIPSE,
      visible: true,
      locked: false,
      metadata: {},
      radiusX: 1000,
      radiusY: 500,
    }

    const area = strategy.calculateArea(ellipse)
    const expectedArea = Math.PI * 1000 * 500

    expect(area).toBeCloseTo(expectedArea, 5)
  })

  it('should throw error for invalid radiusX', () => {
    const ellipse: any = {
      id: 'invalid-ellipse',
      type: ElementType.ELLIPSE,
      visible: true,
      locked: false,
      metadata: {},
      radiusX: Number.NaN,
      radiusY: 5,
    }

    expect(() => strategy.calculateArea(ellipse)).toThrow(CoreError)
    expect(() => strategy.calculateArea(ellipse)).toThrow('[EllipseAreaStrategy] Invalid radii (rx: NaN, ry: 5) for Ellipse ID: invalid-ellipse.')
  })

  it('should throw error for invalid radiusY', () => {
    const ellipse: any = {
      id: 'invalid-ellipse',
      type: ElementType.ELLIPSE,
      visible: true,
      locked: false,
      metadata: {},
      radiusX: 5,
      radiusY: Number.NaN,
    }

    expect(() => strategy.calculateArea(ellipse)).toThrow(CoreError)
    expect(() => strategy.calculateArea(ellipse)).toThrow('[EllipseAreaStrategy] Invalid radii (rx: 5, ry: NaN) for Ellipse ID: invalid-ellipse.')
  })

  it('should throw error for negative radiusX', () => {
    const ellipse: any = {
      id: 'negative-ellipse',
      type: ElementType.ELLIPSE,
      visible: true,
      locked: false,
      metadata: {},
      radiusX: -5,
      radiusY: 3,
    }

    expect(() => strategy.calculateArea(ellipse)).toThrow(CoreError)
    expect(() => strategy.calculateArea(ellipse)).toThrow('[EllipseAreaStrategy] Invalid radii (rx: -5, ry: 3) for Ellipse ID: negative-ellipse.')
  })

  it('should throw error for negative radiusY', () => {
    const ellipse: any = {
      id: 'negative-ellipse',
      type: ElementType.ELLIPSE,
      visible: true,
      locked: false,
      metadata: {},
      radiusX: 5,
      radiusY: -3,
    }

    expect(() => strategy.calculateArea(ellipse)).toThrow(CoreError)
    expect(() => strategy.calculateArea(ellipse)).toThrow('[EllipseAreaStrategy] Invalid radii (rx: 5, ry: -3) for Ellipse ID: negative-ellipse.')
  })

  it('should throw error for wrong element type', () => {
    const rectangle: Element = {
      id: 'not-ellipse',
      type: ElementType.RECTANGLE,
      visible: true,
      locked: false,
      metadata: {},
    }

    expect(() => strategy.calculateArea(rectangle)).toThrow(CoreError)
    expect(() => strategy.calculateArea(rectangle)).toThrow('EllipseAreaStrategy can only calculate area for Ellipse or Circle elements, got RECTANGLE')
  })

  it('should throw error for null element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    expect(() => strategy.calculateArea(null)).toThrow(CoreError)
    expect(() => strategy.calculateArea(null)).toThrow('[EllipseAreaStrategy] Error: Invalid element provided (null or undefined).')
  })

  it('should throw error for undefined element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    expect(() => strategy.calculateArea(undefined)).toThrow(CoreError)
    expect(() => strategy.calculateArea(undefined)).toThrow('[EllipseAreaStrategy] Error: Invalid element provided (null or undefined).')
  })

  it('should throw error for zero radiusX', () => {
    const ellipse: any = {
      id: 'zero-ellipse',
      type: ElementType.ELLIPSE,
      visible: true,
      locked: false,
      metadata: {},
      radiusX: 0,
      radiusY: 5,
    }

    expect(() => strategy.calculateArea(ellipse)).toThrow(CoreError)
    expect(() => strategy.calculateArea(ellipse)).toThrow('[EllipseAreaStrategy] Invalid radii (rx: 0, ry: 5) for Ellipse ID: zero-ellipse.')
  })

  it('should throw error for both zero radii', () => {
    const ellipse: any = {
      id: 'zero-ellipse',
      type: ElementType.ELLIPSE,
      visible: true,
      locked: false,
      metadata: {},
      radiusX: 0,
      radiusY: 0,
    }

    expect(() => strategy.calculateArea(ellipse)).toThrow(CoreError)
    expect(() => strategy.calculateArea(ellipse)).toThrow('[EllipseAreaStrategy] Invalid radii (rx: 0, ry: 0) for Ellipse ID: zero-ellipse.')
  })
})
