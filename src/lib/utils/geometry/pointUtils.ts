/**
 * Utility Functions for Point and Coordinate Operations
 *
 * @remarks
 * This module provides a collection of utility functions for performing common
 * operations related to 2D and 3D points (represented by {@link IPoint} or {@link PointClass}).
 * These operations include:
 * - Calculating the angle between two points.
 * - Checking for equality between two points (within a tolerance).
 * - Rotating and scaling points around a center.
 * - Calculating the midpoint between two points.
 * - Calculating Euclidean distances (point-to-point, point-to-circle, point-to-ellipse).
 * - Ensuring a given point data object is an instance of {@link PointClass}.
 *
 * @module lib/utils/geometry/pointUtils
 */

import type { PointData as IPoint } from '../../../types/core/element/geometry/point' // Use PointData and alias
import { PointClass } from './PointClass'
// import { BoundingBoxClass } from './BoundingBoxClass'; // Not needed for these functions
// import type { IBoundingBox } from '../../../types/core/element/geometry/bounding-box';

/**
 * Calculates the angle (in radians) of the vector from point `p1` to point `p2`
 * relative to the positive x-axis.
 *
 * @param p1 - The first {@link IPoint} (origin of the vector).
 * @param p2 - The second {@link IPoint} (end of the vector).
 * @returns The angle in radians. Returns `0` if either point is invalid.
 */
export function calculateAngle(p1: IPoint, p2: IPoint): number {
  if (!p1 || !p2 || typeof p1.x !== 'number' || typeof p1.y !== 'number' || typeof p2.x !== 'number' || typeof p2.y !== 'number') {
    console.warn('calculateAngle: Invalid points provided.')
    return 0
  }
  return Math.atan2(p2.y - p1.y, p2.x - p1.x)
}

/**
 * Checks if two points are approximately equal within a specified tolerance.
 *
 * @remarks
 * Compares the `x` and `y` coordinates. Does not consider `z` for equality in this version.
 *
 * @param p1 - The first {@link IPoint}.
 * @param p2 - The second {@link IPoint}.
 * @param tolerance - The maximum allowed difference for each coordinate to be considered equal. Defaults to `1e-10`.
 * @returns `true` if the points are considered equal within the tolerance, `false` otherwise or if points are invalid.
 */
export function arePointsEqual(
  p1: IPoint,
  p2: IPoint,
  tolerance: number = 1e-10,
): boolean {
  if (!p1 || !p2 || typeof p1.x !== 'number' || typeof p1.y !== 'number' || typeof p2.x !== 'number' || typeof p2.y !== 'number') {
    console.warn('arePointsEqual: Invalid points provided.')
    return false
  }
  return Math.abs(p1.x - p2.x) < tolerance && Math.abs(p1.y - p2.y) < tolerance
}

/**
 * Rotates a given point around a specified center point by a given angle in radians.
 *
 * @param point - The {@link IPoint} to rotate.
 * @param center - The {@link IPoint} representing the center of rotation.
 * @param angleRadians - The angle of rotation in radians.
 * @returns A new {@link PointClass} instance representing the rotated point.
 *          Returns `PointClass(0,0,z)` or throws if parameters are invalid.
 */
export function rotatePoint(
  point: IPoint,
  center: IPoint,
  angleRadians: number,
): PointClass {
  if (
    !point || typeof point.x !== 'number' || typeof point.y !== 'number'
    || !center || typeof center.x !== 'number' || typeof center.y !== 'number'
    || typeof angleRadians !== 'number'
  ) {
    console.warn('rotatePoint: Invalid parameters provided. Returning origin.')
    return new PointClass(0, 0, point?.z) // Or throw error
  }

  const s = Math.sin(angleRadians)
  const c = Math.cos(angleRadians)
  const px = point.x - center.x
  const py = point.y - center.y
  const newX = px * c - py * s
  const newY = px * s + py * c
  return new PointClass(newX + center.x, newY + center.y, point.z)
}

/**
 * Scales a given point relative to a specified center point by given scale factors.
 *
 * @param point - The {@link IPoint} to scale.
 * @param center - The {@link IPoint} representing the center of scaling.
 * @param scaleX - The scaling factor along the x-axis.
 * @param scaleY - The scaling factor along the y-axis (defaults to `scaleX` if not provided).
 * @returns A new {@link PointClass} instance representing the scaled point.
 *          Returns `PointClass(0,0,z)` or throws if parameters are invalid.
 */
export function scalePoint(
  point: IPoint,
  center: IPoint,
  scaleX: number,
  scaleY: number = scaleX,
): PointClass {
  if (
    !point || typeof point.x !== 'number' || typeof point.y !== 'number'
    || !center || typeof center.x !== 'number' || typeof center.y !== 'number'
    || typeof scaleX !== 'number' || typeof scaleY !== 'number'
  ) {
    console.warn('scalePoint: Invalid parameters provided. Returning origin.')
    return new PointClass(0, 0, point?.z) // Or throw error
  }

  const px = point.x - center.x
  const py = point.y - center.y
  const newX = px * scaleX
  const newY = py * scaleY
  return new PointClass(newX + center.x, newY + center.y, point.z)
}

/**
 * Calculates the midpoint between two points.
 *
 * @remarks
 * If both points have `z` coordinates, the z-coordinate of the midpoint is their average.
 * If only one has `z`, that `z` is used. If neither, `z` is undefined.
 *
 * @param p1 - The first {@link IPoint}.
 * @param p2 - The second {@link IPoint}.
 * @returns A new {@link PointClass} instance representing the midpoint.
 *          Returns `PointClass(0,0)` or throws if points are invalid.
 */
export function calculateMidpoint(p1: IPoint, p2: IPoint): PointClass {
  if (!p1 || typeof p1.x !== 'number' || typeof p1.y !== 'number'
    || !p2 || typeof p2.x !== 'number' || typeof p2.y !== 'number') {
    console.warn('calculateMidpoint: Invalid points provided. Returning origin.')
    return new PointClass(0, 0) // Or throw error
  }
  const z = (p1.z !== undefined && p2.z !== undefined) ? (p1.z + p2.z) / 2 : (p1.z ?? p2.z)
  return new PointClass((p1.x + p2.x) / 2, (p1.y + p2.y) / 2, z)
}

/**
 * Calculates the Euclidean distance between two points in 2D or 3D space.
 *
 * @remarks
 * If both points have `z` coordinates, the 3D distance is calculated.
 * Otherwise, the 2D distance (ignoring `z` or treating missing `z` as 0) is calculated.
 *
 * @param p1 - The first {@link IPoint}.
 * @param p2 - The second {@link IPoint}.
 * @returns The Euclidean distance between the two points. Returns `NaN` if points are invalid.
 */
export function calculateDistance(p1: IPoint, p2: IPoint): number {
  if (
    !p1 || typeof p1.x !== 'number' || typeof p1.y !== 'number'
    || !p2 || typeof p2.x !== 'number' || typeof p2.y !== 'number'
  ) {
    console.warn('calculateDistance: Invalid points provided.')
    return Number.NaN
  }
  const dx = p2.x - p1.x
  const dy = p2.y - p1.y
  const dz = (p2.z ?? 0) - (p1.z ?? 0) // Consider Z-coordinate if present
  return Math.sqrt(dx * dx + dy * dy + dz * dz)
}

/**
 * Alias for {@link calculateDistance}.
 */
export const calculatePointToPointDistance = calculateDistance

/**
 * Calculates the signed distance from a point to the circumference of a circle.
 *
 * @remarks
 * - If the point is outside the circle, the distance is positive.
 * - If the point is on the circumference, the distance is 0.
 * - If the point is inside the circle, the distance is negative (representing the shortest distance to escape the circle).
 *
 * @param point - The {@link IPoint} to measure from.
 * @param center - The center {@link IPoint} of the circle.
 * @param radius - The radius of the circle. Must be non-negative.
 * @returns The signed distance from the point to the circle's circumference.
 * @throws {@link Error} if any parameter is invalid or radius is negative.
 */
export function calculatePointToCircleDistance(point: IPoint, center: IPoint, radius: number): number {
  if (!point || !center || typeof point.x !== 'number' || typeof point.y !== 'number'
    || typeof center.x !== 'number' || typeof center.y !== 'number'
    || typeof radius !== 'number' || isNaN(radius) || radius < 0) {
    throw new Error('Point, circle center, and radius must be valid and radius non-negative.')
  }
  const distToCenter = calculateDistance(point, center)
  return distToCenter - radius
}

/**
 * Calculates a normalized value indicating the position of a point relative to an ellipse.
 *
 * @remarks
 * The function calculates `sqrt(((point.x - center.x)/radiusX)^2 + ((point.y - center.y)/radiusY)^2)`.
 * - A value of `1` means the point is on the ellipse boundary.
 * - A value `< 1` means the point is inside the ellipse.
 * - A value `> 1` means the point is outside the ellipse.
 * The returned value minus 1 gives a signed "distance" relative to the ellipse's normalized form.
 * This is not the true Euclidean distance to the ellipse edge but a useful metric for containment.
 *
 * @param point - The {@link IPoint} to check.
 * @param center - The center {@link IPoint} of the ellipse.
 * @param radiusX - The radius of the ellipse along the x-axis. Must be positive.
 * @param radiusY - The radius of the ellipse along the y-axis. Must be positive.
 * @returns A normalized value. `(value - 1)` can be interpreted as a relative signed distance.
 * @throws {@link Error} if any parameter is invalid or radii are not positive.
 */
export function calculatePointToEllipseDistance(point: IPoint, center: IPoint, radiusX: number, radiusY: number): number {
  if (!point || !center || typeof point.x !== 'number' || typeof point.y !== 'number'
    || typeof center.x !== 'number' || typeof center.y !== 'number'
    || typeof radiusX !== 'number' || typeof radiusY !== 'number'
    || isNaN(radiusX) || isNaN(radiusY) || radiusX <= 0 || radiusY <= 0) {
    throw new Error('Point, ellipse center, and radii must be valid and radii positive.')
  }
  const dx = point.x - center.x
  const dy = point.y - center.y
  // Normalized distance calculation
  const normX = dx / radiusX
  const normY = dy / radiusY
  // For a point (px, py) on the ellipse, (px-cx)^2/rx^2 + (py-cy)^2/ry^2 = 1
  // So, sqrt(normX^2 + normY^2) gives a measure relative to the ellipse "unit circle"
  const distToNormalizedShape = Math.sqrt(normX * normX + normY * normY)
  return distToNormalizedShape - 1 // 0 if on edge, <0 inside, >0 outside
}

/**
 * Ensures that the input is an instance of {@link PointClass}.
 *
 * @remarks
 * If the input `point` is already a `PointClass` instance, it is returned directly.
 * If `point` is an object conforming to the {@link IPoint} structure (i.e., has `x` and `y` number properties),
 * a new `PointClass` instance is created from it.
 * Otherwise, a warning is logged, and an {@link Error} is thrown.
 *
 * @param point - The point data, which can be an {@link IPoint}-compatible object or a `PointClass` instance.
 * @returns A {@link PointClass} instance.
 * @throws {@link Error} if the input `point` data cannot be converted to a `PointClass` instance.
 */
export function ensurePointInstance(point: IPoint | PointClass | { x: number, y: number, z?: number }): PointClass {
  if (point instanceof PointClass) {
    return point
  }
  if (point && typeof point.x === 'number' && typeof point.y === 'number') {
    // Ensure z is handled correctly, even if undefined on the input object
    return new PointClass(point.x, point.y, point.z)
  }
  console.warn('ensurePointInstance: Could not convert to Point instance. Input was:', point)
  throw new Error('Invalid point data for ensurePointInstance. Expected an object with x and y properties or a PointClass instance.')
}
