# Project Understanding Document

## Project Overview
- Project Name: RenoPilot.JS.Shapes2
- Project Introduction: A modern, interactive 2D vector graphics editor built with React, TypeScript, and Vite. RenoPilot.JS.Shapes2 allows users to create, manipulate, and manage various geometric shapes and paths with a focus on ease of use and extensibility.
- Open Source License: MIT License
- Main Features:
    - Shape Creation: Draw various shapes like rectangles, ellipses, polygons, lines, and freehand paths.
    - Element Manipulation: Move, resize, rotate, and group elements.
    - Property Editing: Modify fill color, stroke color, stroke width, opacity, and other visual attributes.
    - Layer Management: Organize elements into layers for better control and visibility.
    - Undo/Redo: Robust undo and redo functionality for non-destructive editing.
    - Zoom & Pan: Navigate the canvas efficiently.
    - Templating: Start new projects from predefined templates or a blank canvas.
    - Export: (Functionality to be detailed based on implementation - e.g., SVG, PNG, JSON)
    - Interactive Tutorial: Guided tours to help users learn the application.

## Tech Stack

### Programming Languages
- Main Language: TypeScript (Version 5)
- Other Languages: JavaScript (inherent in the ecosystem)

### Frameworks and Libraries
- Frontend Framework: React (Version 19)
- State Management: Zustand (Version 5)
- Styling: Tailwind CSS (Version 3), PostCSS
- UI Components: Radix UI, Lucide React Icons
- Drag and Drop: @dnd-kit
- Graphics & Interaction: D3.js (potentially for complex calculations or SVG manipulation), Textures.js
- Utilities: Lodash-es, clsx, tailwind-merge, uuid

### Development Tools
- Build Tool: Vite (Version 6)
- Testing Frameworks: Playwright, Vitest
- Code Quality Tools: ESLint, Prettier
- Version Control Hooks: Husky, lint-staged

## Project Architecture

### Directory Structure
```
RenoPilot.JS.Shapes2
├── .github/         # GitHub specific workflows and templates
├── public/          # Static assets
├── scripts/         # Build and utility scripts
├── src/
│   ├── App.tsx      # Main application component, handles template selection and layout
│   ├── main.tsx     # Application entry point, initializes services and renders App
│   ├── components/  # React UI components (canvas, dialogs, layout, toolbar, etc.)
│   ├── config/      # Application configuration (default settings, environment)
│   ├── core/        # Core application logic, independent of UI
│   ├── data/        # Data handling (export, storage, versioning)
│   ├── hooks/       # Custom React hooks for reusable logic
│   ├── lib/         # General utility functions
│   ├── services/    # Business logic services (elements, input, storage, etc.)
│   ├── store/       # Zustand state management stores (canvas, shapes, UI)
│   ├── styles/      # Global CSS styles
│   └── types/       # TypeScript type definitions
├── tests/
│   ├── e2e/         # End-to-end tests (Playwright)
│   ├── integration/ # Integration tests (Playwright)
│   └── unit/        # Unit tests (Playwright/Vitest)
├── vite.config.ts   # Vite build and development server configuration
├── package.json     # Project metadata and dependencies
└── README.md        # Project README
```

### Architectural Design
- Overall Architectural Pattern: Component-based architecture with separation of concerns.
- Layered Structure:
    - UI Layer (`src/components`, `src/App.tsx`)
    - State Management Layer (`src/store` - Zustand)
    - Service Layer (`src/services`)
    - Core Logic Layer (`src/core`)
    - Configuration (`src/config`)
- Core Modules:
    - `src/core/CoreCoordinator.ts`: Central orchestrator for core logic.
    - `src/core/factory/ElementFactory.ts`: Handles creation of shape elements.
    - `src/core/compute/ComputeFacade.ts`: Manages calculation strategies (area, perimeter, etc.).
    - `src/core/state/ShapeRepository.ts`: In-memory data store for shapes.
    - `src/store/shapesStore.ts`: Zustand store for managing shape states, including undo/redo.
    - `src/store/uiStore.ts`: Zustand store for UI-related state.
    - `src/services/core/appEventBus.ts`: Event bus for decoupled communication.
- Module Relationships: (To be detailed further with diagrams)
    - UI components interact with Zustand stores and services.
    - Services can utilize core logic modules and the event bus.
    - Core logic modules are designed to be framework-agnostic.
    - Zustand stores are the single source of truth for their respective domains.

### System Architecture Diagram
```mermaid
graph TB
    subgraph UserInterface[UI Layer]
        direction LR
        Toolbar[Toolbar]
        Canvas[Canvas]
        PropertiesPanel[Properties Panel]
        LayersPanel[Layers Panel]
        Dialogs[Dialogs]
    end

    subgraph StateManagement[State Management Layer - Zustand]
        direction LR
        ShapesStore[shapesStore.ts]
        UIStore[uiStore.ts]
        CanvasStore[canvasStore.ts]
    end

    subgraph ServicesLayer[Service Layer]
        direction LR
        ElementServices[Element Services src/services/elements]
        TemplateServices[Template Services src/services/template]
        SystemServices[System Services src/services/system]
        CoreServices[Core Services src/services/core/appEventBus.ts]
    end

    subgraph CoreLogic[Core Logic Layer]
        direction LR
        CoreCoordinator[CoreCoordinator.ts]
        ElementFactory[ElementFactory.ts]
        ComputeFacade[ComputeFacade.ts]
        ShapeRepository[ShapeRepository.ts]
        Validators[Validators src/core/validator]
    end

    subgraph ConfigAndUtils[Configuration & Utilities]
        direction LR
        AppConfig[src/config]
        Hooks[src/hooks]
        LibUtils[src/lib/utils]
        Types[src/types]
    end

    UserInterface --> StateManagement
    UserInterface --> ServicesLayer
    ServicesLayer --> StateManagement
    ServicesLayer --> CoreLogic
    ServicesLayer --> CoreServices
    CoreLogic --> ShapeRepository
    StateManagement -.-> UserInterface

    CoreCoordinator --> ElementFactory
    CoreCoordinator --> ComputeFacade
    CoreCoordinator --> ShapeRepository
    CoreCoordinator --> Validators

    %% Interactions
    Toolbar --> ShapesStore
    Canvas --> ShapesStore
    PropertiesPanel --> ShapesStore
    ShapesStore --> Canvas
    ShapesStore --> PropertiesPanel

    ElementServices --> CoreCoordinator
    TemplateServices --> ShapesStore

    CoreServices --> ShapesStore
    CoreServices --> UIStore
```

### Data Flow Diagram
```mermaid
flowchart LR
    User[User Interaction] --> Components[UI Components src/components]
    Components --> Handlers[Event Handlers / Hooks src/hooks]
    Handlers -->|Action Dispatch / Service Call| StoreActions[Zustand Actions src/store]
    StoreActions --> Stores[Zustand Stores shapesStore, uiStore]
    Handlers -->|Service Call| Services[Service Layer src/services]
    Services --> CoreLogic[Core Logic src/core/CoreCoordinator]
    CoreLogic --> ShapeRepo[ShapeRepository src/core/state]
    ShapeRepo --> Stores
    Stores -->|State Update| Components
    Services --> EventBus[Event Bus appEventBus]
    EventBus --> Services
    EventBus --> Stores
```

### Module Dependency Diagram
```mermaid
graph TD
    App[App.tsx] --> MainLayout[components/layout/MainLayout]
    App --> EditorLayout[components/layout/EditorLayout]
    MainLayout --> Toolbar[components/toolbar/Toolbar]
    MainLayout --> PropertySidebar[components/property/PropertySidebar]
    MainLayout --> Canvas[components/canvas/Canvas]

    Toolbar --> uiStore[store/uiStore]
    Toolbar --> shapesStore[store/shapesStore]
    PropertySidebar --> shapesStore
    Canvas --> shapesStore
    Canvas --> canvasStore[store/canvasStore]

    shapesStore --> CoreCoordinator[core/CoreCoordinator]
    shapesStore --> ElementFactory[core/factory/ElementFactory]
    uiStore --> ElementServices[services/elements/elementCreationService]

    CoreCoordinator --> ComputeFacade[core/compute/ComputeFacade]
    CoreCoordinator --> ShapeRepository[core/state/ShapeRepository]
    CoreCoordinator --> ElementValidator[core/validator/ElementValidator]

    ComputeFacade --> ComputeStrategies[core/compute/strategies]
    ElementFactory --> ElementCreators[core/factory/creators]

    services_elements[services/elements] --> shapesStore
    services_elements --> CoreCoordinator
    services_core[services/core/appEventBus] --> shapesStore
    services_core --> uiStore
```

### API Call Flow Diagram (Example: Creating a Shape)
```mermaid
sequenceDiagram
    participant User
    participant ToolbarComponent
    participant UIStore
    participant ElementCreationService
    participant ShapesStore
    participant CoreCoordinator
    participant ElementFactory
    participant ShapeRepository

    User->>ToolbarComponent: Selects Shape Tool (e.g., Rectangle)
    ToolbarComponent->>UIStore: Update selected tool state
    User->>CanvasComponent: Clicks and drags to draw
    CanvasComponent->>ElementCreationService: Initiate shape creation (with points, type)
    ElementCreationService->>CoreCoordinator: Create element (type, properties)
    CoreCoordinator->>ElementFactory: Get new element instance
    ElementFactory-->>CoreCoordinator: Returns new element object
    CoreCoordinator->>ShapeRepository: Add element to repository
    ShapeRepository-->>CoreCoordinator: Confirms addition
    CoreCoordinator-->>ElementCreationService: Element created successfully
    ElementCreationService->>ShapesStore: Add new shape to state
    ShapesStore-->>CanvasComponent: Re-render with new shape
```

### 3.2. Core Functional Modules

This section provides a high-level list of the core functional modules. For a more detailed breakdown of each module category, their responsibilities, and key interactions, please refer to the [Module Design Document](./modules.md).

For in-depth explanations of specific key components, services, and concepts, please consult the [Core Concepts](../core-concepts/) directory.

1.  **UI Components (`src/components`)**: Manages all visual elements and user interactions. (See also: [Editor Layout](../core-concepts/editor-layout.md), [App Component](../core-concepts/app-component.md))
2.  **State Management (`src/store`)**: Centralized state handling using Zustand. (See also: [Shapes Store](../core-concepts/state-management-shapes.md))
3.  **Service Layer (`src/services`)**: Encapsulates business logic and orchestrates operations. (See also: [Element Action Services](../core-concepts/element-action-services.md))
4.  **Core Logic Layer (`src/core`)**: Contains fundamental algorithms and data structures. (See also: [CoreCoordinator](../core-concepts/core-coordinator.md))
5.  **Application Entry Point (`src/main.tsx`)**: Initializes and bootstraps the application. (See also: [Application Entry Point](../core-concepts/application-entry-point.md))
6.  **Custom Hooks (`src/hooks`)**: Reusable React hooks. (See also: [Custom Hooks](../core-concepts/custom-hooks.md))
7.  **Configuration (`src/config`)**: Application-wide settings and constants.
8.  **Utilities (`src/lib`)**: Reusable functions.
9.  **Types (`src/types`)**: Global TypeScript definitions.

### API Interface Analysis

**REST API**
- **Endpoint List:** Not applicable (client-side application, no backend REST API identified from project structure for core functionality).
- **Authentication:** Not applicable.
- **Data Format:** Not applicable.

**Other Interfaces**
- **GraphQL API:** None identified.
- **WebSocket API:** None identified.
- **Internal Service Interfaces:** The application uses internal JavaScript/TypeScript interfaces and function calls between its modules (`src/services`, `src/core`, `src/store`). These are not external APIs but define the contracts for internal communication.
    - Example: `shapesStore` actions, `elementCreationService` methods, `CoreCoordinator` methods.

### Configuration and Deployment

**Environment Configuration**
- **Development Environment Requirements:** Node.js (Version 18+), npm/Yarn.
- **Configuration File Explanation:**
    - `vite.config.ts`: Build, dev server, plugins.
    - `tsconfig.json` (and related): TypeScript compiler options.
    - `tailwind.config.js`, `postcss.config.cjs`: Styling configuration.
    - `eslint.config.ts`: Linting rules.
    - `src/config/environment.ts`: Application-specific environment settings (e.g., feature flags, API endpoints if any were used externally).
    - `src/config/defaultElementSettings.ts`: Default properties for new elements.
- **Environment Variables:** Primarily managed via `src/config/environment.ts`. If `.env` files were used, `VITE_` prefixed variables would be exposed.

**Deployment Methods**
- **Local Deployment:** `npm run dev` for development server.
- **Production Deployment:** `npm run build` generates static assets in the `dist/` folder, which can then be served by any static web server (e.g., Nginx, Apache, Vercel, Netlify).
- **Containerization (If any):** No Dockerfile or containerization setup identified in the project structure.

### Development Guide

**Quick Start**
- **Installation Steps:** (From README.md)
    1. `git clone <repository-url>`
    2. `cd RenoPilot.JS.Shapes2`
    3. `npm install` (or `yarn install`)
- **Running Commands:**
    - `npm run dev` (start development server)
    - `npm run build` (build for production)
    - `npm run lint` (lint code)
    - `npm test` (run Playwright tests)
    - `npm run test:vitest` (run Vitest tests)
- **Testing Methods:** (From README.md & package.json)
    - End-to-end and Integration Tests: Playwright (`npm test`)
    - Unit Tests: Vitest (`npm run test:vitest`)

**Development Standards**
- **Code Style:** Enforced by Prettier and ESLint. Configuration in `.prettierrc` (or `package.json`) and `eslint.config.ts`.
- **Commit Convention:** Likely Conventional Commits (implied by `commitlint.config.ts`).
- **Testing Requirements:** Unit tests for core logic and services, E2E tests for user workflows. Test files located in `tests/`.

## Project Features and Highlights
- **Technical Highlights:**
    - Modern tech stack (React 19, TypeScript 5, Vite 6).
    - Robust state management with Zustand, including undo/redo middleware (`zundo`).
    - Efficient rendering and interaction, leveraging React's reconciliation.
    - Strategy pattern for extensible computation logic (`src/core/compute`).
    - Factory pattern for element creation (`src/core/factory`).
    - Decoupled architecture using an event bus.
- **Design Features:**
    - Intuitive and interactive user interface.
    - Component-based UI with reusable elements (Radix UI, custom components).
    - Clear separation of concerns in the architecture.
    - Templating system for starting new designs.
    - Interactive tutorial for new users.
- **Performance Optimizations:** (Potential, based on typical React/Vite practices)
    - Vite's fast HMR and optimized builds.
    - React's virtual DOM and efficient updates.
    - Code splitting (default with Vite).
    - Memoization for components and selectors where applicable.
- **Security Measures:** (Client-side focus)
    - Standard web security practices (e.g., avoiding XSS by proper data handling in React).
    - No backend, so server-side security concerns are minimal for the core app.
    - Dependencies are managed via npm, vulnerability scanning can be done via `npm audit`.

## Potential Improvement Points
- **Code Quality:**
    - Increase test coverage, especially for complex interaction logic and core computations.
    - Refine type definitions for stricter type safety in some areas.
- **Architectural Optimization:**
    - Further modularization of large components or services if they grow.
    - Evaluate performance of state selectors and event handling for very large numbers of shapes.
- **Performance Improvements:**
    - Optimize SVG rendering for extremely complex scenes (e.g., virtualization of off-screen elements if needed).
    - Profile and optimize critical path computations.
- **Documentation Enhancement:**
    - Detailed API documentation for internal services and core modules (JSDoc or TypeDoc).
    - More comprehensive user guides for advanced features.
    - In-code comments for complex logic sections.

## Summary
- **Overall Project Assessment:** RenoPilot.JS.Shapes2 is a well-structured, modern web application for 2D vector graphics editing. It leverages a strong technology stack and sound architectural principles, making it a solid foundation for further development and feature enhancement. The separation of concerns and use of patterns like Strategy and Factory contribute to its maintainability and extensibility.
- **Applicable Scenarios:**
    - Simple diagramming and flowchart creation.
    - Basic graphic design tasks.
    - Educational tool for learning about vector graphics.
    - A base platform for more specialized 2D design applications (e.g., floor plan design, UI mockups with extensions).
- **Learning Value:**
    - Demonstrates best practices in modern React and TypeScript development.
    - Excellent example of using Zustand for complex state management.
    - Shows how to structure a client-side application with clear separation of UI, state, services, and core logic.
    - Implementation of design patterns in a real-world application.
- **Future Prospects:**
    - Integration with backend services for saving/loading projects to the cloud.
    - Advanced features like real-time collaboration.
    - Support for more complex shape types and manipulations.
    - Plugin system for extensibility.
    - Enhanced export/import capabilities (e.g., more formats, better SVG compatibility).