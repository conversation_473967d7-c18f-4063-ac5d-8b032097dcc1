/**
 * Export Template Types
 *
 * @remarks
 * This module defines types related to exporting templates and data from the application.
 * These types are used for configuring export operations and defining export formats.
 *
 * @module types/export/ExportTemplateTypes
 */

/**
 * Export format options
 */
export type ExportFormat = 'json' | 'svg' | 'png' | 'pdf' | 'dxf'

/**
 * Export quality settings
 */
export interface ExportQuality {
  /** Resolution for raster formats (DPI) */
  dpi?: number
  /** Compression level (0-100) */
  compression?: number
  /** Anti-aliasing enabled */
  antiAliasing?: boolean
}

/**
 * Export options configuration
 */
export interface ExportOptions {
  /** The format to export to */
  format: ExportFormat
  /** Include metadata in the export */
  includeMetadata?: boolean
  /** Quality settings for the export */
  quality?: ExportQuality
  /** Custom filename for the export */
  filename?: string
  /** Scale factor for the export */
  scale?: number
  /** Include hidden elements */
  includeHidden?: boolean
  /** Export specific layers only */
  layerIds?: string[]
  /** Export specific elements only */
  elementIds?: string[]
}

/**
 * Export result information
 */
export interface ExportResult {
  /** Whether the export was successful */
  success: boolean
  /** The exported data (base64 for binary formats, string for text formats) */
  data?: string
  /** The filename used for the export */
  filename?: string
  /** Error message if export failed */
  error?: string
  /** File size in bytes */
  fileSize?: number
  /** Export timestamp */
  timestamp: Date
}

/**
 * Export template configuration
 */
export interface ExportTemplate {
  /** Unique identifier for the template */
  id: string
  /** Display name for the template */
  name: string
  /** Description of the template */
  description?: string
  /** Default export options for this template */
  defaultOptions: ExportOptions
  /** Whether this template is built-in or user-defined */
  isBuiltIn: boolean
  /** Creation timestamp */
  createdAt: Date
  /** Last modified timestamp */
  modifiedAt: Date
}
