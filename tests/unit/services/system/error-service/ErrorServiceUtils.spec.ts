import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ensureError as ensureErrorUtil } from '@/lib/utils/errorUtils'
import { CoreError } from '@/services/system/error-service/coreError'
import {
  createErrorContext,
  ensureCoreError,
  ensureError,
  extractStackTrace,
  formatErrorMessage,
} from '@/services/system/error-service/utils'

import { ErrorSeverity, ErrorType } from '@/types/services/errors'

// Mock dependencies
vi.mock('@/lib/utils/errorUtils', () => ({
  ensureError: vi.fn(),
}))

describe('error Service Utils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('createErrorContext', () => {
    it('should create error context with required parameters', () => {
      const result = createErrorContext('TestComponent', 'testOperation')

      expect(result).toEqual({
        component: 'TestComponent',
        operation: 'testOperation',
        timestamp: expect.any(Number),
      })
      expect(result.timestamp).toBeGreaterThan(0)
    })

    it('should create error context with optional parameters', () => {
      const options = {
        metadata: { key: 'value' },
        stack: 'test stack trace',
      }

      const result = createErrorContext('TestComponent', 'testOperation', options)

      expect(result).toEqual({
        component: 'TestComponent',
        operation: 'testOperation',
        timestamp: expect.any(Number),
        metadata: { key: 'value' },
        stack: 'test stack trace',
      })
    })

    it('should handle empty component and operation', () => {
      const result = createErrorContext('', '')

      expect(result).toEqual({
        component: '',
        operation: '',
        timestamp: expect.any(Number),
      })
    })

    it('should handle special characters in component and operation', () => {
      const result = createErrorContext('Test-Component_123', 'test.operation@domain')

      expect(result.component).toBe('Test-Component_123')
      expect(result.operation).toBe('test.operation@domain')
    })

    it('should override timestamp if provided in options', () => {
      const customTimestamp = 1234567890
      const result = createErrorContext('TestComponent', 'testOperation', {
        timestamp: customTimestamp,
      })

      expect(result.timestamp).toBe(customTimestamp)
    })

    it('should handle complex metadata objects', () => {
      const complexMetadata = {
        nested: { object: true },
        array: [1, 2, 3],
        function: () => 'test',
      }

      const result = createErrorContext('TestComponent', 'testOperation', {
        metadata: complexMetadata,
      })

      expect(result.metadata).toBe(complexMetadata)
    })
  })

  describe('formatErrorMessage', () => {
    it('should return error message when available', () => {
      const error = new Error('Test error message')
      const result = formatErrorMessage(error)

      expect(result).toBe('Test error message')
    })

    it('should return default message when error message is empty', () => {
      const error = new Error('')
      const result = formatErrorMessage(error)

      expect(result).toBe('Operation failed')
    })

    it('should return custom default message', () => {
      const error = new Error('')
      const result = formatErrorMessage(error, 'Custom default message')

      expect(result).toBe('Custom default message')
    })

    it('should handle error without message property', () => {
      const error = {} as Error
      const result = formatErrorMessage(error)

      expect(result).toBe('Operation failed')
    })

    it('should handle null or undefined error message', () => {
      const error = { message: null } as unknown as Error
      const result = formatErrorMessage(error)

      expect(result).toBe('Operation failed')
    })

    it('should handle very long error messages', () => {
      const longMessage = 'A'.repeat(1000)
      const error = new Error(longMessage)
      const result = formatErrorMessage(error)

      expect(result).toBe(longMessage)
      expect(result.length).toBe(1000)
    })

    it('should handle error messages with special characters', () => {
      const specialMessage = 'Error: 测试错误 with émojis 🚨 and symbols @#$%'
      const error = new Error(specialMessage)
      const result = formatErrorMessage(error)

      expect(result).toBe(specialMessage)
    })
  })

  describe('extractStackTrace', () => {
    it('should return stack trace when available', () => {
      const error = new Error('Test error')
      const result = extractStackTrace(error)

      expect(result).toBe(error.stack)
      expect(result).toContain('Error: Test error')
    })

    it('should return empty string when stack is null', () => {
      const error = new Error('Test error')
      error.stack = null as unknown as string
      const result = extractStackTrace(error)

      expect(result).toBe('')
    })

    it('should return empty string when stack is undefined', () => {
      const error = new Error('Test error')
      error.stack = undefined as unknown as string
      const result = extractStackTrace(error)

      expect(result).toBe('')
    })

    it('should return empty string when stack is empty', () => {
      const error = new Error('Test error')
      error.stack = ''
      const result = extractStackTrace(error)

      expect(result).toBe('')
    })

    it('should handle custom stack traces', () => {
      const error = new Error('Test error')
      error.stack = 'Custom stack trace\n  at function1\n  at function2'
      const result = extractStackTrace(error)

      expect(result).toBe('Custom stack trace\n  at function1\n  at function2')
    })

    it('should handle errors without stack property', () => {
      const error = {} as Error
      const result = extractStackTrace(error)

      expect(result).toBe('')
    })
  })

  describe('ensureError', () => {
    it('should call ensureErrorUtil with the provided value', () => {
      const testValue = 'test error'
      const mockError = new Error('Mock error')
      vi.mocked(ensureErrorUtil).mockReturnValue(mockError)

      const result = ensureError(testValue)

      expect(ensureErrorUtil).toHaveBeenCalledWith(testValue)
      expect(result).toBe(mockError)
    })

    it('should handle different types of values', () => {
      const testValues = [
        'string error',
        123,
        { message: 'object error' },
        new Error('actual error'),
        null,
        undefined,
      ]

      testValues.forEach((value, index) => {
        const mockError = new Error(`Mock error ${index}`)
        vi.mocked(ensureErrorUtil).mockReturnValue(mockError)

        const result = ensureError(value)

        expect(ensureErrorUtil).toHaveBeenCalledWith(value)
        expect(result).toBe(mockError)
      })
    })
  })

  describe('ensureCoreError', () => {
    beforeEach(() => {
      vi.mocked(ensureErrorUtil).mockImplementation((value) => {
        if (value instanceof Error)
          return value
        return new Error(String(value))
      })
    })

    it('should return existing CoreError instance', () => {
      const coreError = new CoreError(ErrorType.ValidationError, 'Test error', ErrorSeverity.High)
      const result = ensureCoreError(coreError)

      expect(result).toBe(coreError)
    })

    it('should wrap regular Error in CoreError', () => {
      const error = new Error('Test error')
      const result = ensureCoreError(error)

      expect(result).toBeInstanceOf(CoreError)
      expect(result.message).toBe('Test error')
      expect(result.type).toBe(ErrorType.UnknownError)
      expect(result.severity).toBe(ErrorSeverity.Medium)
    })

    it('should use custom default message', () => {
      const error = new Error('Original message')
      const result = ensureCoreError(error, 'Custom default message')

      expect(result.message).toBe('Custom default message')
    })

    it('should preserve error type and severity if available', () => {
      const error = new Error('Test error') as Error & { type?: ErrorType, severity?: ErrorSeverity }
      error.type = ErrorType.Validation
      error.severity = ErrorSeverity.High

      const result = ensureCoreError(error)

      expect(result.type).toBe(ErrorType.Validation)
      expect(result.severity).toBe(ErrorSeverity.High)
    })

    it('should handle non-Error values', () => {
      const result = ensureCoreError('string error')

      expect(result).toBeInstanceOf(CoreError)
      expect(result.message).toBe('string error')
      expect(result.type).toBe(ErrorType.UnknownError)
      expect(result.severity).toBe(ErrorSeverity.Medium)
    })

    it('should handle null and undefined values', () => {
      const resultNull = ensureCoreError(null)
      const resultUndefined = ensureCoreError(undefined)

      expect(resultNull).toBeInstanceOf(CoreError)
      expect(resultUndefined).toBeInstanceOf(CoreError)
    })

    it('should preserve original error in metadata', () => {
      const originalError = new Error('Original error')
      const result = ensureCoreError(originalError)

      expect(result.context?.metadata?.originalError).toBe(originalError)
    })

    it('should preserve stack trace', () => {
      const error = new Error('Test error')
      const result = ensureCoreError(error)

      expect(result.context?.stack).toBe(error.stack)
    })

    it('should handle Error-like objects with type and severity', () => {
      const errorLike = {
        name: 'TestError',
        message: 'Test message',
        type: ErrorType.ComputationError,
        severity: ErrorSeverity.Low,
        stack: 'test stack',
      } as Error & { type: ErrorType, severity: ErrorSeverity }

      // Make it an instance of Error
      Object.setPrototypeOf(errorLike, Error.prototype)

      const result = ensureCoreError(errorLike)

      expect(result).toBeInstanceOf(CoreError)
      expect(result.type).toBe(ErrorType.ComputationError)
      expect(result.severity).toBe(ErrorSeverity.Low)
    })

    it('should handle complex error objects', () => {
      const complexError = {
        message: 'Complex error',
        code: 'ERR_COMPLEX',
        details: { nested: true },
        timestamp: Date.now(),
      }

      // Mock ensureErrorUtil to return an Error with the message
      vi.mocked(ensureErrorUtil).mockReturnValue(new Error('Complex error'))

      const result = ensureCoreError(complexError)

      expect(result).toBeInstanceOf(CoreError)
      expect(result.message).toBe('Complex error')
      expect(result.context?.metadata?.originalError).toEqual(new Error('Complex error'))
    })
  })

  describe('integration scenarios', () => {
    it('should work together in error handling pipeline', () => {
      // Mock ensureErrorUtil for this test
      vi.mocked(ensureErrorUtil).mockReturnValue(new Error('Processing failed'))

      // Create error context
      const context = createErrorContext('TestService', 'processData', {
        metadata: { userId: '123' },
      })

      // Create an error
      const originalError = new Error('Processing failed')

      // Ensure it's a CoreError
      const coreError = ensureCoreError(originalError)

      // Format the message
      const formattedMessage = formatErrorMessage(coreError)

      // Extract stack trace
      const stackTrace = extractStackTrace(coreError)

      expect(context.component).toBe('TestService')
      expect(context.operation).toBe('processData')
      expect(coreError).toBeInstanceOf(CoreError)
      expect(formattedMessage).toBe('Processing failed')
      expect(stackTrace).toBeTruthy()
    })

    it('should handle error transformation chain', () => {
      const originalValue = 'Something went wrong'
      const mockError = new Error('Something went wrong')

      // Mock ensureErrorUtil for both calls
      vi.mocked(ensureErrorUtil).mockReturnValue(mockError)

      // Ensure it's an error
      const error = ensureError(originalValue)

      // Ensure it's a CoreError
      const coreError = ensureCoreError(error, 'Default message')

      // Create context for it
      const context = createErrorContext('ErrorHandler', 'transform')

      expect(coreError).toBeInstanceOf(CoreError)
      expect(context.component).toBe('ErrorHandler')
      expect(context.operation).toBe('transform')
    })
  })
})
