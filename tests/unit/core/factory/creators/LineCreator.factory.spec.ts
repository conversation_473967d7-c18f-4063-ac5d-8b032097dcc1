import { beforeEach, describe, expect, it, vi } from 'vitest'
import { CoreError, ErrorType } from '@/core/errors'
import { LineCreator } from '@/core/factory/creators/LineCreator'
import * as geometryUtils from '@/core/utils/geometryUtils'
import { Point } from '@/types/core/element/geometry/point'
import { ElementType } from '@/types/core/shape-type'

// 模拟 console 方法，但不阻止实际输出
vi.spyOn(console, 'error')
vi.spyOn(console, 'warn')
vi.spyOn(console, 'debug')

describe('lineCreator', () => {
  let creator: LineCreator

  beforeEach(() => {
    creator = new LineCreator()
    vi.clearAllMocks()
  })

  describe('create', () => {
    it('应该创建线段 ShapeModel', async () => {
      const params = {
        id: 'line-1',
        type: ElementType.LINE,
        start: { x: 10, y: 20 },
        end: { x: 100, y: 200 },
        stroke: 'black',
        strokeWidth: 2,
      }

      const result = await creator.create(params)

      expect(result).toEqual({
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 55, y: 110 }, // 线段的位置是起点和终点的中点
        properties: {
          type: ElementType.LINE,
          start: { x: 10, y: 20 },
          end: { x: 100, y: 200 },
          stroke: 'black',
          strokeWidth: 2,
          opacity: undefined,
          fill: undefined,
          strokeDasharray: undefined,
        },
        selected: false,
        visible: true,
        layer: undefined,
        zIndex: undefined,
        name: undefined,
        label: undefined,
        metadata: expect.objectContaining({
          createdAt: expect.any(Number),
          updatedAt: expect.any(Number),
        }),
      })
    })

    it('应该处理 Point 实例作为起点和终点', async () => {
      const params = {
        id: 'line-2',
        type: ElementType.LINE,
        start: new Point(10, 20),
        end: new Point(100, 200),
      }

      const result = await creator.create(params)

      expect(result.properties.start).toEqual({ x: 10, y: 20 })
      expect(result.properties.end).toEqual({ x: 100, y: 200 })
    })

    it('应该处理数组作为起点和终点', async () => {
      const params = {
        id: 'line-3',
        type: ElementType.LINE,
        start: [10, 20] as [number, number],
        end: [100, 200] as [number, number],
      }

      const result = await creator.create(params)

      expect(result.properties.start).toEqual({ x: 10, y: 20 })
      expect(result.properties.end).toEqual({ x: 100, y: 200 })
    })

    it('当类型不正确时应该抛出错误', async () => {
      const params = {
        id: 'circle-1',
        type: ElementType.CIRCLE as any,
        start: { x: 10, y: 20 },
        end: { x: 100, y: 200 },
      }

      await expect(creator.create(params)).rejects.toThrow(CoreError)
      await expect(creator.create(params)).rejects.toThrow('LineCreator cannot create type')
    })

    // 注意：LineCreator 目前不检查起点和终点是否相同
    it('应该处理起点和终点相同的情况', async () => {
      const params = {
        id: 'line-same-points',
        type: ElementType.LINE,
        start: { x: 10, y: 20 },
        end: { x: 10, y: 20 },
      }

      const result = await creator.create(params)

      expect(result.id).toBe('line-same-points')
      expect(result.properties.start).toEqual({ x: 10, y: 20 })
      expect(result.properties.end).toEqual({ x: 10, y: 20 })
    })

    it('当起点缺失时应该抛出错误', async () => {
      const params = {
        id: 'line-4',
        type: ElementType.LINE,
        end: { x: 100, y: 200 },
      } as any

      await expect(creator.create(params)).rejects.toThrow(CoreError)
      await expect(creator.create(params)).rejects.toThrow('Line creation requires valid start and end points')
    })

    it('当终点缺失时应该抛出错误', async () => {
      const params = {
        id: 'line-5',
        type: ElementType.LINE,
        start: { x: 10, y: 20 },
      } as any

      await expect(creator.create(params)).rejects.toThrow(CoreError)
      await expect(creator.create(params)).rejects.toThrow('Line creation requires valid start and end points')
    })

    it('当起点无效且错误没有 message 属性时应该抛出错误', async () => {
      const params = {
        id: 'line-invalid-start',
        type: ElementType.LINE,
        start: 'invalid' as any,
        end: { x: 100, y: 200 },
      }

      // 模拟 ensurePointInstance 抛出没有 message 属性的错误
      vi.spyOn(geometryUtils, 'ensurePointInstance').mockImplementationOnce(() => {
        const error = new Error()
        delete error.message
        throw error
      })

      await expect(creator.create(params)).rejects.toThrow(CoreError)
      await expect(creator.create(params)).rejects.toThrow('Line creation requires valid start and end points')
    })

    it('应该使用提供的元数据', async () => {
      const metadata = { createdAt: 1000, updatedAt: 2000, customField: 'test' }
      const params = {
        id: 'line-6',
        type: ElementType.LINE,
        start: { x: 10, y: 20 },
        end: { x: 100, y: 200 },
        metadata,
      }

      const result = await creator.create(params)

      expect(result.metadata).toEqual(metadata)
    })
  })

  describe('createDefault', () => {
    it('应该创建默认线段 ShapeModel', async () => {
      const id = 'default-line'
      const position = new Point(0, 0)

      const result = await creator.createDefault(id, position)

      expect(result).toEqual({
        id,
        type: ElementType.LINE,
        position: { x: 0, y: 0 },
        properties: {
          type: ElementType.LINE,
          start: { x: -50, y: 0 },
          end: { x: 50, y: 0 }, // 默认水平线
          fill: undefined,
          stroke: undefined,
          strokeWidth: undefined,
          opacity: undefined,
          strokeDasharray: undefined,
        },
        selected: false,
        visible: true,
        layer: undefined,
        zIndex: undefined,
        name: undefined,
        label: undefined,
        metadata: expect.objectContaining({
          createdAt: expect.any(Number),
          updatedAt: expect.any(Number),
        }),
      })
    })

    it('应该处理创建过程中的错误', async () => {
      const id = 'error-line'
      const position = new Point(0, 0)

      // 模拟 create 方法抛出错误
      vi.spyOn(creator, 'create').mockImplementation(() => {
        throw new Error('Test error')
      })

      await expect(creator.createDefault(id, position)).rejects.toThrow(CoreError)
      await expect(creator.createDefault(id, position)).rejects.toThrow('Failed to create default Line ShapeModel')
    })

    it('应该处理创建过程中的错误没有 message 属性', async () => {
      const id = 'error-no-message-line'
      const position = new Point(0, 0)

      // 模拟 create 方法抛出没有 message 属性的错误
      vi.spyOn(creator, 'create').mockImplementation(() => {
        const error = new Error()
        delete error.message
        throw error
      })

      await expect(creator.createDefault(id, position)).rejects.toThrow(CoreError)
      await expect(creator.createDefault(id, position)).rejects.toThrow('Failed to create default Line ShapeModel')
    })

    it('应该传递 CoreError 类型的错误', async () => {
      const id = 'core-error-line'
      const position = new Point(0, 0)

      // 模拟 create 方法抛出 CoreError
      const coreError = new CoreError(ErrorType.INVALID_PAYLOAD, 'Test CoreError')
      vi.spyOn(creator, 'create').mockImplementation(() => {
        throw coreError
      })

      await expect(creator.createDefault(id, position)).rejects.toThrow(coreError)
    })

    it('应该处理无效的位置格式', async () => {
      const id = 'invalid-position-line'
      const invalidPosition = 'invalid' as any

      // 模拟 ensurePointInstance 抛出错误
      vi.spyOn(geometryUtils, 'ensurePointInstance').mockImplementation(() => {
        throw new Error('Invalid position format')
      })

      try {
        await expect(creator.createDefault(id, invalidPosition)).rejects.toThrow(CoreError)
        await expect(creator.createDefault(id, invalidPosition)).rejects.toThrow('Failed to create default Line: Invalid position provided')
      }
      finally {
        // 恢复原始函数
        vi.spyOn(geometryUtils, 'ensurePointInstance').mockRestore()
      }
    })

    it('应该处理无效的位置格式且错误没有 message 属性', async () => {
      const id = 'invalid-position-no-message-line'
      const invalidPosition = 'invalid' as any

      // 模拟 ensurePointInstance 抛出没有 message 属性的错误
      vi.spyOn(geometryUtils, 'ensurePointInstance').mockImplementation(() => {
        const error = new Error()
        delete error.message
        throw error
      })

      try {
        await expect(creator.createDefault(id, invalidPosition)).rejects.toThrow(CoreError)
        await expect(creator.createDefault(id, invalidPosition)).rejects.toThrow('Failed to create default Line: Invalid position provided')
      }
      finally {
        // 恢复原始函数
        vi.spyOn(geometryUtils, 'ensurePointInstance').mockRestore()
      }
    })
  })
})
