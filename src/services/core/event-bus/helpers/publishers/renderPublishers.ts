/**
 * Render Event Publishers Module
 *
 * This module provides helper functions for publishing rendering-related events
 * to the application's event bus. It covers rendering triggers, layer management,
 * and other rendering-related operations.
 *
 * All functions follow dependency injection principles by accepting an EventBus
 * instance as their first parameter.
 *
 * @module services/core/event-bus/helpers/publishers/renderPublishers
 */

import type { ShapeElement as ShapeModel } from '@/types/core/elementDefinitions' // Import actual ShapeModel
import type { EventBus } from '@/types/services/events'
import type { TypedEvent } from '@/types/services/events/eventCore'
import type { RenderEvent } from '@/types/services/events/renderEvents'
import { AppEventType } from '@/types/services/events'
// Removed local ShapeModel definition
// Removed incorrect comment about TypedEvent import

/**
 * Publish render trigger event
 *
 * Requests a rendering update for the specified shapes.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param shapes - Array of shapes to render
 * @param reason - Optional reason for the render trigger
 */
export function publishRenderTrigger(
  eventBus: EventBus,
  shapes: ShapeModel[],
  reason: string = 'manual_trigger',
): void {
  const event: RenderEvent = {
    type: AppEventType.RenderTrigger,
    payload: {
      options: {
        elementIds: shapes.map(shape => shape.id),
        highPerformance: reason === 'dragging',
      },
    },
  }
  eventBus.publish(event)
}

/**
 * Publish data updated event
 *
 * Notifies the system that shape data has been updated.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param shapes - Array of updated shapes
 */
export function publishDataUpdated(eventBus: EventBus, shapes: ShapeModel[]): void {
  const event: TypedEvent<{ type: string, data: ShapeModel[] }> = {
    type: AppEventType.DataUpdated,
    payload: { type: 'shapes', data: shapes },
  }
  eventBus.publish(event)
}
