```mermaid
graph TB
    subgraph UserInterface["User Interface (React + Tailwind CSS)"]
        direction LR
        Toolbar["Toolbar (src/components/Toolbar)"]
        Canvas["Canvas (src/components/Canvas)"]
        PropertiesPanel["Properties Panel (src/components/PropertiesPanel)"]
        ModalDialogs["Modal Dialogs (src/components/Modal)"]
    end

    subgraph StateManagement["State Management (Zustand)"]
        direction LR
        ShapeStore["Shape Store (src/store/useShapeStore)"]
        UIStore["UI Store (src/store/useUIStore)"]
        HistoryStore["History Store (Undo/Redo - src/store/useHistoryStore)"]
    end

    subgraph CoreLogic["Core Logic (TypeScript)"]
        direction LR
        ShapeEngine["Shape Engine (src/core/shapes)"]
        TransformationEngine["Transformation Engine (src/core/transformations)"]
        EventEngine["Event Engine (src/core/events)"]
        UndoRedoService["Undo/Redo Service (src/services/historyService)"]
    end

    subgraph Services["Services Layer"]
        direction LR
        LocalStorageService["Local Storage Service (src/services/storageService)"]
        ExportService["Export Service (src/services/exportService)"]
    end

    subgraph UtilsAndHooks["Utilities & Hooks"]
        direction LR
        UtilsLib["General Utilities (src/lib)"]
        CustomHooks["Custom React Hooks (src/hooks)"]
    end

    subgraph ConfigAndTypes["Configuration & Types"]
        direction LR
        AppConfig["App Configuration (src/config)"]
        TSDefs["TypeScript Definitions (src/types)"]
    end

    BrowserAPI["Browser APIs (DOM, Events, LocalStorage)"]

    UserInterface --> StateManagement
    UserInterface --> CoreLogic
    UserInterface -- "Uses" --> UtilsAndHooks
    StateManagement --> CoreLogic
    CoreLogic --> Services
    CoreLogic -- "Uses" --> TSDefs
    Services --> BrowserAPI
    UtilsAndHooks --> BrowserAPI
    UserInterface --> BrowserAPI
    StateManagement -- "Persists/Loads via" --> LocalStorageService

    ShapeStore -.-> HistoryStore
    UIStore -.-> UserInterface
    HistoryStore -.-> UndoRedoService
    UndoRedoService -.-> ShapeStore

    %% Styling
    classDef react fill:#61DAFB,stroke:#333,stroke-width:2px;
    classDef zustand fill:#FFD700,stroke:#333,stroke-width:2px;
    classDef typescript fill:#3178C6,stroke:#FFF,stroke-width:2px,color:#FFF;
    classDef browser fill:#E44D26,stroke:#333,stroke-width:2px;
    classDef service fill:#4CAF50,stroke:#333,stroke-width:2px;
    classDef util fill:#9C27B0,stroke:#FFF,stroke-width:2px,color:#FFF;
    classDef config fill:#795548,stroke:#FFF,stroke-width:2px,color:#FFF;

    class UserInterface react;
    class StateManagement zustand;
    class CoreLogic typescript;
    class BrowserAPI browser;
    class Services service;
    class UtilsAndHooks util;
    class ConfigAndTypes config;
```