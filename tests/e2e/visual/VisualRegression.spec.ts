import { test, expect } from '@playwright/test';

/**
 * Visual regression test suite for the canvas application.
 * These tests verify that the visual appearance of the canvas application is correct.
 */
test.describe('Visual Regression', () => {
  /**
   * Setup for each test.
   * Navigates to the canvas application page.
   */
  test.beforeEach(async ({ page }) => {
    // Navigate to the canvas application page
    await page.goto('/');
    
    // Wait for the canvas to be loaded
    await page.waitForSelector('#canvas-container svg');
  });

  /**
   * Tests the visual appearance of the canvas.
   * Verifies that the canvas looks correct.
   */
  test('Canvas appearance', async ({ page }) => {
    // Take a screenshot of the canvas
    await expect(page.locator('#canvas-container')).toHaveScreenshot('empty-canvas.png');
  });

  /**
   * Tests the visual appearance of the toolbar.
   * Verifies that the toolbar looks correct.
   */
  test('Toolbar appearance', async ({ page }) => {
    // Take a screenshot of the toolbar
    await expect(page.locator('#toolbar')).toHaveScreenshot('toolbar.png');
  });

  /**
   * Tests the visual appearance of the properties panel.
   * Verifies that the properties panel looks correct.
   */
  test('Properties panel appearance', async ({ page }) => {
    // Take a screenshot of the properties panel
    await expect(page.locator('#properties-panel')).toHaveScreenshot('properties-panel.png');
  });

  /**
   * Tests the visual appearance of a rectangle.
   * Verifies that a rectangle looks correct.
   */
  test('Rectangle appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Take a screenshot of the rectangle
    await expect(page.locator('rect')).toHaveScreenshot('rectangle.png');
  });

  /**
   * Tests the visual appearance of an ellipse.
   * Verifies that an ellipse looks correct.
   */
  test('Ellipse appearance', async ({ page }) => {
    // Create an ellipse
    await page.click('#ellipse-tool');
    await page.mouse.move(200, 200);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Take a screenshot of the ellipse
    await expect(page.locator('ellipse')).toHaveScreenshot('ellipse.png');
  });

  /**
   * Tests the visual appearance of a line.
   * Verifies that a line looks correct.
   */
  test('Line appearance', async ({ page }) => {
    // Create a line
    await page.click('#line-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 300);
    await page.mouse.up();
    
    // Take a screenshot of the line
    await expect(page.locator('line')).toHaveScreenshot('line.png');
  });

  /**
   * Tests the visual appearance of a selected shape.
   * Verifies that a selected shape looks correct.
   */
  test('Selected shape appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Switch to the select tool
    await page.click('#select-tool');
    
    // Select the rectangle
    await page.mouse.move(200, 175);
    await page.mouse.down();
    await page.mouse.up();
    
    // Take a screenshot of the selected rectangle
    await expect(page.locator('rect.selected')).toHaveScreenshot('selected-rectangle.png');
  });

  /**
   * Tests the visual appearance of a group.
   * Verifies that a group looks correct.
   */
  test('Group appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Create an ellipse
    await page.click('#ellipse-tool');
    await page.mouse.move(400, 300);
    await page.mouse.down();
    await page.mouse.move(500, 350);
    await page.mouse.up();
    
    // Switch to the select tool
    await page.click('#select-tool');
    
    // Select both shapes using a selection rectangle
    await page.mouse.move(50, 50);
    await page.mouse.down();
    await page.mouse.move(550, 400);
    await page.mouse.up();
    
    // Group the shapes
    await page.keyboard.press('Control+G');
    
    // Take a screenshot of the group
    await expect(page.locator('g.group')).toHaveScreenshot('group.png');
  });

  /**
   * Tests the visual appearance of a shape with a custom style.
   * Verifies that a shape with a custom style looks correct.
   */
  test('Custom style appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Switch to the select tool
    await page.click('#select-tool');
    
    // Select the rectangle
    await page.mouse.move(200, 175);
    await page.mouse.down();
    await page.mouse.up();
    
    // Change the fill color
    await page.click('#fill-color-picker');
    await page.fill('#fill-color-input', '#0000ff');
    await page.press('#fill-color-input', 'Enter');
    
    // Change the stroke color
    await page.click('#stroke-color-picker');
    await page.fill('#stroke-color-input', '#00ff00');
    await page.press('#stroke-color-input', 'Enter');
    
    // Change the stroke width
    await page.fill('#stroke-width-input', '5');
    await page.press('#stroke-width-input', 'Enter');
    
    // Take a screenshot of the styled rectangle
    await expect(page.locator('rect')).toHaveScreenshot('styled-rectangle.png');
  });

  /**
   * Tests the visual appearance of a shape with a transformation.
   * Verifies that a shape with a transformation looks correct.
   */
  test('Transformed shape appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Switch to the select tool
    await page.click('#select-tool');
    
    // Select the rectangle
    await page.mouse.move(200, 175);
    await page.mouse.down();
    await page.mouse.up();
    
    // Rotate the rectangle
    await page.click('#rotate-button');
    await page.mouse.move(200, 175);
    await page.mouse.down();
    await page.mouse.move(250, 125);
    await page.mouse.up();
    
    // Take a screenshot of the rotated rectangle
    await expect(page.locator('rect')).toHaveScreenshot('rotated-rectangle.png');
  });

  /**
   * Tests the visual appearance of the canvas with multiple shapes.
   * Verifies that the canvas with multiple shapes looks correct.
   */
  test('Multiple shapes appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Create an ellipse
    await page.click('#ellipse-tool');
    await page.mouse.move(400, 300);
    await page.mouse.down();
    await page.mouse.move(500, 350);
    await page.mouse.up();
    
    // Create a line
    await page.click('#line-tool');
    await page.mouse.move(100, 300);
    await page.mouse.down();
    await page.mouse.move(300, 100);
    await page.mouse.up();
    
    // Take a screenshot of the canvas with multiple shapes
    await expect(page.locator('#canvas-container')).toHaveScreenshot('multiple-shapes.png');
  });

  /**
   * Tests the visual appearance of the canvas in different viewport sizes.
   * Verifies that the canvas looks correct in different viewport sizes.
   */
  test('Responsive appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Take a screenshot of the canvas in the default viewport size
    await expect(page.locator('#canvas-container')).toHaveScreenshot('canvas-default-viewport.png');
    
    // Resize the viewport to a smaller size
    await page.setViewportSize({ width: 800, height: 600 });
    
    // Take a screenshot of the canvas in the smaller viewport size
    await expect(page.locator('#canvas-container')).toHaveScreenshot('canvas-small-viewport.png');
    
    // Resize the viewport to a larger size
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // Take a screenshot of the canvas in the larger viewport size
    await expect(page.locator('#canvas-container')).toHaveScreenshot('canvas-large-viewport.png');
  });

  /**
   * Tests the visual appearance of the canvas in different themes.
   * Verifies that the canvas looks correct in different themes.
   */
  test('Theme appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Take a screenshot of the canvas in the default theme
    await expect(page.locator('#canvas-container')).toHaveScreenshot('canvas-default-theme.png');
    
    // Switch to the dark theme
    await page.click('#theme-toggle');
    
    // Take a screenshot of the canvas in the dark theme
    await expect(page.locator('#canvas-container')).toHaveScreenshot('canvas-dark-theme.png');
  });

  /**
   * Tests the visual appearance of the canvas with a complex drawing.
   * Verifies that the canvas with a complex drawing looks correct.
   */
  test('Complex drawing appearance', async ({ page }) => {
    // Create a complex drawing
    
    // Create a rectangle
    await page.click('#rectangle-tool');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Create an ellipse
    await page.click('#ellipse-tool');
    await page.mouse.move(400, 300);
    await page.mouse.down();
    await page.mouse.move(500, 350);
    await page.mouse.up();
    
    // Create a line
    await page.click('#line-tool');
    await page.mouse.move(100, 300);
    await page.mouse.down();
    await page.mouse.move(300, 100);
    await page.mouse.up();
    
    // Create a polygon
    await page.click('#polygon-tool');
    await page.mouse.click(500, 100);
    await page.mouse.click(600, 150);
    await page.mouse.click(550, 200);
    await page.mouse.click(500, 100);
    
    // Create a text element
    await page.click('#text-tool');
    await page.mouse.click(350, 400);
    await page.keyboard.type('Hello, World!');
    
    // Take a screenshot of the canvas with a complex drawing
    await expect(page.locator('#canvas-container')).toHaveScreenshot('complex-drawing.png');
  });
});
