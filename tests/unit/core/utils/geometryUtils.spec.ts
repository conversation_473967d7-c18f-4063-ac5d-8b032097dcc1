import { describe, expect, it } from 'vitest'
import { CoreError, ErrorType } from '@/core/errors'
import { ensurePointInstance } from '@/core/utils/geometryUtils'
import { Point } from '@/types/core/element/geometry/point'

describe('geometryUtils', () => {
  describe('ensurePointInstance', () => {
    it('should return the same Point instance if a valid Point is provided', () => {
      const point = new Point(10, 20)
      const result = ensurePointInstance(point)
      expect(result).toBe(point)
      expect(result.x).toBe(10)
      expect(result.y).toBe(20)
    })

    it('should create a Point instance from a valid object with x and y properties', () => {
      const obj = { x: 30, y: 40 }
      const result = ensurePointInstance(obj)
      expect(result).toBeInstanceOf(Point)
      expect(result.x).toBe(30)
      expect(result.y).toBe(40)
    })

    it('should create a Point instance from a valid array with two numbers', () => {
      const arr = [50, 60]
      const result = ensurePointInstance(arr)
      expect(result).toBeInstanceOf(Point)
      expect(result.x).toBe(50)
      expect(result.y).toBe(60)
    })

    it('should throw an error for null input', () => {
      expect(() => ensurePointInstance(null)).toThrow(CoreError)
      try {
        ensurePointInstance(null)
      }
      catch (error) {
        expect(error instanceof CoreError).toBe(true)
        expect((error as CoreError).type).toBe(ErrorType.INVALID_PAYLOAD)
        expect((error as CoreError).message).toContain('Missing position data')
      }
    })

    it('should throw an error for undefined input', () => {
      expect(() => ensurePointInstance(undefined)).toThrow(CoreError)
      try {
        ensurePointInstance(undefined)
      }
      catch (error) {
        expect(error instanceof CoreError).toBe(true)
        expect((error as CoreError).type).toBe(ErrorType.INVALID_PAYLOAD)
        expect((error as CoreError).message).toContain('Missing position data')
      }
    })

    it('should throw an error for a Point instance with non-finite coordinates', () => {
      const invalidPoint = new Point(Number.NaN, 20)
      expect(() => ensurePointInstance(invalidPoint)).toThrow(CoreError)
      try {
        ensurePointInstance(invalidPoint)
      }
      catch (error) {
        expect(error instanceof CoreError).toBe(true)
        expect((error as CoreError).type).toBe(ErrorType.INVALID_PAYLOAD)
        expect((error as CoreError).message).toContain('Invalid coordinates in provided Point instance')
      }
    })

    it('should throw an error for an object with non-numeric x or y properties', () => {
      const invalidObj = { x: 'not a number', y: 40 }
      expect(() => ensurePointInstance(invalidObj)).toThrow(CoreError)
      try {
        ensurePointInstance(invalidObj)
      }
      catch (error) {
        expect(error instanceof CoreError).toBe(true)
        expect((error as CoreError).type).toBe(ErrorType.INVALID_PAYLOAD)
        expect((error as CoreError).message).toContain('Invalid coordinates in object')
      }
    })

    it('should throw an error for an array with non-numeric elements', () => {
      const invalidArr = ['not a number', 60]
      expect(() => ensurePointInstance(invalidArr)).toThrow(CoreError)
      try {
        ensurePointInstance(invalidArr)
      }
      catch (error) {
        expect(error instanceof CoreError).toBe(true)
        expect((error as CoreError).type).toBe(ErrorType.INVALID_PAYLOAD)
        expect((error as CoreError).message).toContain('Invalid coordinates in array')
      }
    })

    it('should throw an error for an array with incorrect length', () => {
      const invalidArr = [10, 20, 30]
      expect(() => ensurePointInstance(invalidArr)).toThrow(CoreError)
      try {
        ensurePointInstance(invalidArr)
      }
      catch (error) {
        expect(error instanceof CoreError).toBe(true)
        expect((error as CoreError).type).toBe(ErrorType.INVALID_PAYLOAD)
        expect((error as CoreError).message).toContain('Invalid position format')
      }
    })

    it('should throw an error for an unsupported type (string)', () => {
      const invalidInput = 'not a point'
      expect(() => ensurePointInstance(invalidInput)).toThrow(CoreError)
      try {
        ensurePointInstance(invalidInput)
      }
      catch (error) {
        expect(error instanceof CoreError).toBe(true)
        expect((error as CoreError).type).toBe(ErrorType.INVALID_PAYLOAD)
        expect((error as CoreError).message).toContain('Invalid position format')
      }
    })

    it('should throw an error for an unsupported type (number)', () => {
      const invalidInput = 42
      expect(() => ensurePointInstance(invalidInput)).toThrow(CoreError)
      try {
        ensurePointInstance(invalidInput)
      }
      catch (error) {
        expect(error instanceof CoreError).toBe(true)
        expect((error as CoreError).type).toBe(ErrorType.INVALID_PAYLOAD)
        expect((error as CoreError).message).toContain('Invalid position format')
      }
    })

    it('should throw an error for an object with non-finite coordinates', () => {
      const invalidObj = { x: Infinity, y: 40 }
      expect(() => ensurePointInstance(invalidObj)).toThrow(CoreError)
      try {
        ensurePointInstance(invalidObj)
      }
      catch (error) {
        expect(error instanceof CoreError).toBe(true)
        expect((error as CoreError).type).toBe(ErrorType.INVALID_PAYLOAD)
        expect((error as CoreError).message).toContain('Invalid coordinates in object')
      }
    })

    it('should throw an error for an array with non-finite elements', () => {
      const invalidArr = [50, Number.NaN]
      expect(() => ensurePointInstance(invalidArr)).toThrow(CoreError)
      try {
        ensurePointInstance(invalidArr)
      }
      catch (error) {
        expect(error instanceof CoreError).toBe(true)
        expect((error as CoreError).type).toBe(ErrorType.INVALID_PAYLOAD)
        expect((error as CoreError).message).toContain('Invalid coordinates in array')
      }
    })

    it('should handle objects with constructor property', () => {
      const objWithConstructor = { x: 10, y: 20, constructor: { name: 'CustomPoint' } }
      const result = ensurePointInstance(objWithConstructor)
      expect(result).toBeInstanceOf(Point)
      expect(result.x).toBe(10)
      expect(result.y).toBe(20)
    })

    it('should handle objects without constructor property', () => {
      const objWithoutConstructor = Object.create(null)
      objWithoutConstructor.x = 10
      objWithoutConstructor.y = 20
      const result = ensurePointInstance(objWithoutConstructor)
      expect(result).toBeInstanceOf(Point)
      expect(result.x).toBe(10)
      expect(result.y).toBe(20)
    })

    it('should use the provided context in error messages', () => {
      try {
        ensurePointInstance(null, 'center point')
      }
      catch (error) {
        expect((error as CoreError).message).toContain('Missing center point data')
      }
    })
  })
})
