import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { LineAreaStrategy } from '@/core/compute/strategies/area/LineAreaStrategy'
import { Point } from '@/types/core/element/geometry/point'
import { Line } from '@/types/core/element/path/linePathTypes'

// Mock Line class
class MockLine {
  id: string = 'mock-line'

  constructor() {}

  getSubType() {
    return 'line'
  }
}

// Make MockLine instances pass the instanceof Line check
Object.setPrototypeOf(MockLine.prototype, Line.prototype)

// Mock non-line element
class MockRectangle {
  id: string = 'mock-rectangle'

  getSubType() {
    return 'rectangle'
  }
}

describe('lineAreaStrategy', () => {
  let strategy: LineAreaStrategy

  beforeEach(() => {
    strategy = new LineAreaStrategy()
    // Spy on console methods
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(console, 'debug').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('line')
  })

  it('should calculate area correctly (always 0)', () => {
    const line = new MockLine()
    const area = strategy.calculateArea(line as any)
    expect(area).toBe(0)
  })

  it('should return NaN if element is not a line', () => {
    const rectangle = new MockRectangle()
    const area = strategy.calculateArea(rectangle as any)
    expect(isNaN(area)).toBe(true)
  })

  it('should handle real Line instance', () => {
    // Create a mock that looks like a real Line instance
    const realLine = {
      id: 'real-line-id',
      constructor: { name: 'Line' },
      getStartPoint: () => new Point(0, 0),
      getEndPoint: () => new Point(100, 100),
      getSubType: () => 'line',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realLine, Line.prototype)

    const area = strategy.calculateArea(realLine as any)
    expect(area).toBe(0)
  })

  it('should handle line with different lengths', () => {
    // Create a mock that looks like a real Line instance with different start and end points
    const realLine = {
      id: 'real-line-id',
      constructor: { name: 'Line' },
      getStartPoint: () => new Point(0, 0),
      getEndPoint: () => new Point(200, 200),
      getSubType: () => 'line',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realLine, Line.prototype)

    const area = strategy.calculateArea(realLine as any)
    expect(area).toBe(0)
  })

  it('should handle horizontal line', () => {
    // Create a mock that looks like a real Line instance with horizontal orientation
    const realLine = {
      id: 'real-line-id',
      constructor: { name: 'Line' },
      getStartPoint: () => new Point(0, 0),
      getEndPoint: () => new Point(100, 0),
      getSubType: () => 'line',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realLine, Line.prototype)

    const area = strategy.calculateArea(realLine as any)
    expect(area).toBe(0)
  })

  it('should handle vertical line', () => {
    // Create a mock that looks like a real Line instance with vertical orientation
    const realLine = {
      id: 'real-line-id',
      constructor: { name: 'Line' },
      getStartPoint: () => new Point(0, 0),
      getEndPoint: () => new Point(0, 100),
      getSubType: () => 'line',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realLine, Line.prototype)

    const area = strategy.calculateArea(realLine as any)
    expect(area).toBe(0)
  })
  it('should handle null element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    const result = strategy.calculateArea(null)

    expect(isNaN(result)).toBe(true)
    expect(console.error).toHaveBeenCalledWith(
      expect.stringContaining('Invalid element provided (null)'),
    )
  })

  it('should handle undefined element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    const result = strategy.calculateArea(undefined)

    expect(isNaN(result)).toBe(true)
    expect(console.error).toHaveBeenCalledWith(
      expect.stringContaining('Invalid element provided (undefined)'),
    )
  })

  it('should handle element with missing constructor', () => {
    const elementWithoutConstructor = {
      id: 'strange-element',
    }

    const result = strategy.calculateArea(elementWithoutConstructor as any)

    expect(isNaN(result)).toBe(true)
    expect(console.error).toHaveBeenCalledWith(
      expect.stringContaining('Expected an element of type Line, but received type: Object'),
    )
  })
})
