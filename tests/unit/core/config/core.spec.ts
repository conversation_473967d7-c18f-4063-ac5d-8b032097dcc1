import type { CoreConfig } from '@/core/config/core'
import { describe, expect, it } from 'vitest'
import { createConfig, defaultCoreConfig } from '@/core/config/core'

describe('core Configuration', () => {
  describe('defaultCoreConfig', () => {
    it('should have the expected default values', () => {
      // Factory defaults
      expect(defaultCoreConfig.factory.defaultShapeSize).toBe(100)
      expect(defaultCoreConfig.factory.idPrefix).toBe('shape_')
      expect(defaultCoreConfig.factory.generateUniqueId).toBe(true)

      // Validator defaults
      expect(defaultCoreConfig.validator.strictMode).toBe(false)
      expect(defaultCoreConfig.validator.maxShapeSize).toBe(5000)
      expect(defaultCoreConfig.validator.minShapeSize).toBe(1)

      // Compute defaults
      expect(defaultCoreConfig.compute.transformPrecision).toBe(2)
      expect(defaultCoreConfig.compute.useHardwareAcceleration).toBe(true)
      expect(defaultCoreConfig.compute.batchOperations).toBe(true)

      // Render defaults
      expect(defaultCoreConfig.render.defaultStrokeWidth).toBe(1)
      expect(defaultCoreConfig.render.defaultFillColor).toBe('#cccccc')
      expect(defaultCoreConfig.render.defaultStrokeColor).toBe('#333333')
      expect(defaultCoreConfig.render.highlightColor).toBe('#007bff')
      expect(defaultCoreConfig.render.selectionColor).toBe('#007bff')
      expect(defaultCoreConfig.render.renderQuality).toBe('high')

      // Units defaults
      expect(defaultCoreConfig.units.drawingScale).toBe(1)
      expect(defaultCoreConfig.units.unitType).toBe('px')
    })
  })

  describe('createConfig', () => {
    it('should return the default config when no partial config is provided', () => {
      const config = createConfig()
      expect(config).toEqual(defaultCoreConfig)
    })

    it('should override top-level properties when provided', () => {
      const partialConfig: Partial<CoreConfig> = {
        factory: {
          defaultShapeSize: 200,
          idPrefix: 'custom_',
          generateUniqueId: false,
        },
      }

      const config = createConfig(partialConfig)

      // Check that factory properties were overridden
      expect(config.factory.defaultShapeSize).toBe(200)
      expect(config.factory.idPrefix).toBe('custom_')
      expect(config.factory.generateUniqueId).toBe(false)

      // Check that other properties remain at default values
      expect(config.validator).toEqual(defaultCoreConfig.validator)
      expect(config.compute).toEqual(defaultCoreConfig.compute)
      expect(config.render).toEqual(defaultCoreConfig.render)
      expect(config.units).toEqual(defaultCoreConfig.units)
    })

    it('should perform a deep merge for nested objects', () => {
      const partialConfig: Partial<CoreConfig> = {
        validator: {
          strictMode: true,
          // Only override strictMode, leaving other validator properties at default
        },
        render: {
          defaultFillColor: '#ff0000',
          // Only override defaultFillColor, leaving other render properties at default
        },
      }

      const config = createConfig(partialConfig)

      // Check that specified properties were overridden
      expect(config.validator.strictMode).toBe(true)
      expect(config.render.defaultFillColor).toBe('#ff0000')

      // Check that unspecified properties within the same objects remain at default values
      expect(config.validator.maxShapeSize).toBe(defaultCoreConfig.validator.maxShapeSize)
      expect(config.validator.minShapeSize).toBe(defaultCoreConfig.validator.minShapeSize)
      expect(config.render.defaultStrokeWidth).toBe(defaultCoreConfig.render.defaultStrokeWidth)
      expect(config.render.defaultStrokeColor).toBe(defaultCoreConfig.render.defaultStrokeColor)
      expect(config.render.highlightColor).toBe(defaultCoreConfig.render.highlightColor)
      expect(config.render.selectionColor).toBe(defaultCoreConfig.render.selectionColor)
      expect(config.render.renderQuality).toBe(defaultCoreConfig.render.renderQuality)

      // Check that completely unspecified objects remain at default values
      expect(config.factory).toEqual(defaultCoreConfig.factory)
      expect(config.compute).toEqual(defaultCoreConfig.compute)
      expect(config.units).toEqual(defaultCoreConfig.units)
    })

    it('should handle undefined nested objects gracefully', () => {
      const partialConfig: Partial<CoreConfig> = {
        factory: undefined,
        validator: undefined,
        compute: undefined,
        render: undefined,
        units: undefined,
      }

      const config = createConfig(partialConfig)

      // All properties should be at default values
      expect(config).toEqual(defaultCoreConfig)
    })

    it('should handle partial nested objects correctly', () => {
      const partialConfig = {
        factory: {
          defaultShapeSize: 300,
          // idPrefix and generateUniqueId not specified
        },
        validator: {
          maxShapeSize: 10000,
          // strictMode and minShapeSize not specified
        },
      }

      const config = createConfig(partialConfig)

      // Check that specified properties were overridden
      expect(config.factory.defaultShapeSize).toBe(300)
      expect(config.validator.maxShapeSize).toBe(10000)

      // Check that unspecified properties within the same objects remain at default values
      expect(config.factory.idPrefix).toBe(defaultCoreConfig.factory.idPrefix)
      expect(config.factory.generateUniqueId).toBe(defaultCoreConfig.factory.generateUniqueId)
      expect(config.validator.strictMode).toBe(defaultCoreConfig.validator.strictMode)
      expect(config.validator.minShapeSize).toBe(defaultCoreConfig.validator.minShapeSize)
    })

    it('should handle complex overrides across multiple nested objects', () => {
      const partialConfig: Partial<CoreConfig> = {
        factory: {
          defaultShapeSize: 150,
        },
        validator: {
          strictMode: true,
          minShapeSize: 5,
        },
        compute: {
          transformPrecision: 4,
        },
        render: {
          defaultStrokeWidth: 2,
          defaultFillColor: '#ffffff',
        },
        units: {
          drawingScale: 0.5,
          unitType: 'mm',
        },
      }

      const config = createConfig(partialConfig)

      // Check factory overrides
      expect(config.factory.defaultShapeSize).toBe(150)
      expect(config.factory.idPrefix).toBe(defaultCoreConfig.factory.idPrefix)
      expect(config.factory.generateUniqueId).toBe(defaultCoreConfig.factory.generateUniqueId)

      // Check validator overrides
      expect(config.validator.strictMode).toBe(true)
      expect(config.validator.minShapeSize).toBe(5)
      expect(config.validator.maxShapeSize).toBe(defaultCoreConfig.validator.maxShapeSize)

      // Check compute overrides
      expect(config.compute.transformPrecision).toBe(4)
      expect(config.compute.useHardwareAcceleration).toBe(defaultCoreConfig.compute.useHardwareAcceleration)
      expect(config.compute.batchOperations).toBe(defaultCoreConfig.compute.batchOperations)

      // Check render overrides
      expect(config.render.defaultStrokeWidth).toBe(2)
      expect(config.render.defaultFillColor).toBe('#ffffff')
      expect(config.render.defaultStrokeColor).toBe(defaultCoreConfig.render.defaultStrokeColor)
      expect(config.render.highlightColor).toBe(defaultCoreConfig.render.highlightColor)
      expect(config.render.selectionColor).toBe(defaultCoreConfig.render.selectionColor)
      expect(config.render.renderQuality).toBe(defaultCoreConfig.render.renderQuality)

      // Check units overrides
      expect(config.units.drawingScale).toBe(0.5)
      expect(config.units.unitType).toBe('mm')
    })
  })
})
