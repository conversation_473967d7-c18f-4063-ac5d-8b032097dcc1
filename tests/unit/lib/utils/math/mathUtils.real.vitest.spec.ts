import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  toDegrees,
  toRadians,
  clamp,
  lerp,
  map,
  roundToDecimals,
  getRandomInt,
  getRandomFloat,
  areNumbersEqual,
  normalize,
  percentage,
} from '@/lib/utils/math/mathUtils'

describe('Math Utils (Real Implementation)', () => {
  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Angle Conversions', () => {
    it('should convert radians to degrees', () => {
      expect(toDegrees(Math.PI)).toBe(180)
      expect(toDegrees(Math.PI / 2)).toBe(90)
      expect(toDegrees(0)).toBe(0)
      expect(toDegrees(2 * Math.PI)).toBe(360)
    })

    it('should convert degrees to radians', () => {
      expect(toRadians(180)).toBe(Math.PI)
      expect(toRadians(90)).toBe(Math.PI / 2)
      expect(toRadians(0)).toBe(0)
      expect(toRadians(360)).toBe(2 * Math.PI)
    })

    it('should be reversible', () => {
      const degrees = 45
      const radians = toRadians(degrees)
      const backToDegrees = toDegrees(radians)
      expect(backToDegrees).toBeCloseTo(degrees, 10)
    })

    it('should throw error for non-numeric input', () => {
      expect(() => toDegrees('invalid' as any)).toThrow('Input must be a number')
      expect(() => toRadians('invalid' as any)).toThrow('Input must be a number')
    })
  })

  describe('Clamp Function', () => {
    it('should clamp value within range', () => {
      expect(clamp(5, 0, 10)).toBe(5)
      expect(clamp(-5, 0, 10)).toBe(0)
      expect(clamp(15, 0, 10)).toBe(10)
    })

    it('should handle edge cases', () => {
      expect(clamp(0, 0, 10)).toBe(0)
      expect(clamp(10, 0, 10)).toBe(10)
      expect(clamp(5, 5, 5)).toBe(5)
    })

    it('should handle negative ranges', () => {
      expect(clamp(-5, -10, -1)).toBe(-5)
      expect(clamp(-15, -10, -1)).toBe(-10)
      expect(clamp(5, -10, -1)).toBe(-1)
    })

    it('should throw error for non-numeric input', () => {
      expect(() => clamp('invalid' as any, 0, 10)).toThrow('All parameters must be numbers')
      expect(() => clamp(5, 'invalid' as any, 10)).toThrow('All parameters must be numbers')
      expect(() => clamp(5, 0, 'invalid' as any)).toThrow('All parameters must be numbers')
    })
  })

  describe('Linear Interpolation', () => {
    it('should interpolate between values', () => {
      expect(lerp(0, 10, 0.5)).toBe(5)
      expect(lerp(0, 10, 0)).toBe(0)
      expect(lerp(0, 10, 1)).toBe(10)
    })

    it('should handle negative values', () => {
      expect(lerp(-10, 10, 0.5)).toBe(0)
      expect(lerp(-5, -1, 0.5)).toBe(-3)
    })

    it('should extrapolate beyond range', () => {
      expect(lerp(0, 10, 1.5)).toBe(15)
      expect(lerp(0, 10, -0.5)).toBe(-5)
    })

    it('should throw error for non-numeric input', () => {
      expect(() => lerp('invalid' as any, 10, 0.5)).toThrow('All parameters must be numbers')
      expect(() => lerp(0, 'invalid' as any, 0.5)).toThrow('All parameters must be numbers')
      expect(() => lerp(0, 10, 'invalid' as any)).toThrow('All parameters must be numbers')
    })
  })

  describe('Map Function', () => {
    it('should map values between ranges', () => {
      expect(map(5, 0, 10, 0, 100)).toBe(50)
      expect(map(0, 0, 10, 0, 100)).toBe(0)
      expect(map(10, 0, 10, 0, 100)).toBe(100)
    })

    it('should handle negative ranges', () => {
      expect(map(0, -10, 10, 0, 100)).toBe(50)
      expect(map(-5, -10, 10, 0, 100)).toBe(25)
    })

    it('should handle equal input range', () => {
      expect(map(5, 5, 5, 0, 100)).toBe(0)
    })

    it('should throw error for non-numeric input', () => {
      expect(() => map('invalid' as any, 0, 10, 0, 100)).toThrow('All parameters must be numbers')
    })
  })

  describe('Round to Decimals', () => {
    it('should round to specified decimal places', () => {
      expect(roundToDecimals(3.14159, 2)).toBe(3.14)
      expect(roundToDecimals(3.14159, 4)).toBe(3.1416)
      expect(roundToDecimals(3.14159, 0)).toBe(3)
    })

    it('should handle negative numbers', () => {
      expect(roundToDecimals(-3.14159, 2)).toBe(-3.14)
      expect(roundToDecimals(-3.16159, 1)).toBe(-3.2)
    })

    it('should handle edge cases', () => {
      expect(roundToDecimals(0, 2)).toBe(0)
      expect(roundToDecimals(1.999, 2)).toBe(2)
      expect(roundToDecimals(1.001, 2)).toBe(1)
    })

    it('should throw error for non-numeric input', () => {
      expect(() => roundToDecimals('invalid' as any, 2)).toThrow('Both value and decimals must be numbers')
      expect(() => roundToDecimals(3.14, 'invalid' as any)).toThrow('Both value and decimals must be numbers')
    })
  })

  describe('Random Number Generation', () => {
    it('should generate random integers within range', () => {
      for (let i = 0; i < 100; i++) {
        const result = getRandomInt(1, 10)
        expect(result).toBeGreaterThanOrEqual(1)
        expect(result).toBeLessThanOrEqual(10)
        expect(Number.isInteger(result)).toBe(true)
      }
    })

    it('should generate random floats within range', () => {
      for (let i = 0; i < 100; i++) {
        const result = getRandomFloat(1.0, 10.0)
        expect(result).toBeGreaterThanOrEqual(1.0)
        expect(result).toBeLessThanOrEqual(10.0)
      }
    })

    it('should handle edge cases for random generation', () => {
      expect(getRandomInt(5, 5)).toBe(5)
      expect(getRandomFloat(5.0, 5.0)).toBe(5.0)
    })

    it('should throw error for invalid ranges', () => {
      expect(() => getRandomInt('invalid' as any, 10)).toThrow('Both min and max must be numbers')
      expect(() => getRandomFloat('invalid' as any, 10.0)).toThrow('Both min and max must be numbers')
    })
  })

  describe('Number Equality', () => {
    it('should compare numbers with default epsilon', () => {
      expect(areNumbersEqual(1.0, 1.0001)).toBe(true)
      expect(areNumbersEqual(1.0, 1.01)).toBe(false)
      expect(areNumbersEqual(0, 0.0001)).toBe(true)
    })

    it('should use custom epsilon', () => {
      expect(areNumbersEqual(1.0, 1.1, 0.2)).toBe(true)
      expect(areNumbersEqual(1.0, 1.1, 0.05)).toBe(false)
    })

    it('should handle negative numbers', () => {
      expect(areNumbersEqual(-1.0, -1.0001)).toBe(true)
      expect(areNumbersEqual(-1.0, -0.9999)).toBe(true)
      expect(areNumbersEqual(-1.0, -0.99)).toBe(false)
    })

    it('should throw error for non-numeric input', () => {
      expect(() => areNumbersEqual('invalid' as any, 1.0)).toThrow('Both a and b must be numbers')
      expect(() => areNumbersEqual(1.0, 'invalid' as any)).toThrow('Both a and b must be numbers')
    })
  })

  describe('Normalize Function', () => {
    it('should normalize values to 0-1 range', () => {
      expect(normalize(5, 0, 10)).toBe(0.5)
      expect(normalize(0, 0, 10)).toBe(0)
      expect(normalize(10, 0, 10)).toBe(1)
    })

    it('should handle negative ranges', () => {
      expect(normalize(0, -10, 10)).toBe(0.5)
      expect(normalize(-5, -10, 10)).toBe(0.25)
    })

    it('should handle equal min and max', () => {
      expect(normalize(5, 5, 5)).toBe(0)
    })

    it('should throw error for non-numeric input', () => {
      expect(() => normalize('invalid' as any, 0, 10)).toThrow('All parameters must be numbers')
    })
  })

  describe('Percentage Function', () => {
    it('should calculate percentage of value within range', () => {
      expect(percentage(5, 0, 10)).toBe(50)
      expect(percentage(0, 0, 10)).toBe(0)
      expect(percentage(10, 0, 10)).toBe(100)
    })

    it('should handle negative ranges', () => {
      expect(percentage(0, -10, 10)).toBe(50)
      expect(percentage(-5, -10, 10)).toBe(25)
    })

    it('should handle values outside range', () => {
      expect(percentage(15, 0, 10)).toBe(150)
      expect(percentage(-5, 0, 10)).toBe(-50)
    })

    it('should throw error for non-numeric input', () => {
      expect(() => percentage('invalid' as any, 0, 10)).toThrow('All parameters must be numbers')
    })
  })

  describe('Edge Cases and Error Handling', () => {
    it('should handle NaN values', () => {
      expect(clamp(NaN, 0, 10)).toBeNaN()
      expect(lerp(NaN, 10, 0.5)).toBeNaN()
      expect(map(NaN, 0, 10, 0, 100)).toBeNaN()
    })

    it('should handle Infinity values', () => {
      expect(clamp(Infinity, 0, 10)).toBe(10)
      expect(clamp(-Infinity, 0, 10)).toBe(0)
      expect(lerp(0, Infinity, 0.5)).toBe(Infinity)
    })

    it('should handle very large numbers', () => {
      const large = Number.MAX_SAFE_INTEGER
      expect(clamp(large, 0, large)).toBe(large)
      expect(lerp(0, large, 0.5)).toBe(large / 2)
    })

    it('should handle very small numbers', () => {
      const small = Number.MIN_VALUE
      expect(clamp(small, 0, 1)).toBe(small)
      expect(areNumbersEqual(small, 0, small * 2)).toBe(true)
    })
  })

  describe('Performance', () => {
    it('should handle many calculations efficiently', () => {
      const startTime = Date.now()
      
      for (let i = 0; i < 10000; i++) {
        clamp(i, 0, 1000)
        lerp(0, 100, i / 10000)
        map(i, 0, 10000, 0, 100)
        roundToDecimals(i / 3, 2)
      }
      
      const endTime = Date.now()
      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly
    })
  })
})
