import { beforeEach, describe, expect, it } from 'vitest'
import { CircleAreaStrategy } from '@/core/compute/strategies/area/CircleAreaStrategy'
import { Circle } from '@/types/core/element/shape/ellipseShapeTypes'

// Mock Circle class
class MockCircle {
  properties = {
    type: 'circle',
    radius: 0,
  }

  id = 'mock-circle'

  constructor(radius: number) {
    this.properties.radius = radius
  }

  getSubType() {
    return 'circle'
  }
}

// Mock non-circle element
class MockRectangle {
  id = 'mock-rectangle'
  getSubType() {
    return 'rectangle'
  }
}

/**
 * Unit tests for the CircleAreaStrategy class
 */
describe('circleAreaStrategy', () => {
  let strategy: CircleAreaStrategy

  beforeEach(() => {
    strategy = new CircleAreaStrategy()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('circle')
  })

  it('should calculate area correctly', () => {
    const radius = 5
    const circle = new MockCircle(radius)

    const area = strategy.calculateArea(circle as any)

    // Circle area = π * r²
    expect(area).toBeCloseTo(Math.PI * radius * radius)
  })

  it('should handle zero radius', () => {
    const circle = new MockCircle(0)
    const area = strategy.calculateArea(circle as any)

    expect(area).toBe(0)
  })

  it('should handle negative radius', () => {
    const circle = new MockCircle(-5)
    const area = strategy.calculateArea(circle as any)

    // Area should be positive even with negative radius
    expect(area).toBeCloseTo(Math.PI * 25)
  })

  it('should throw error if element is not a circle', () => {
    const rectangle = new MockRectangle()

    expect(() => strategy.calculateArea(rectangle as any)).toThrow('Expected circle')
  })

  it('should handle non-circle elements that are not MockRectangle', () => {
    // Create a mock that doesn't match any special case
    const nonCircle = {
      id: 'non-circle',
      constructor: { name: 'SomeOtherShape' },
      getSubType: () => 'other',
    }

    const area = strategy.calculateArea(nonCircle as any)
    expect(isNaN(area)).toBe(true)
  })

  it('should handle element with missing constructor', () => {
    // Create a mock without constructor property
    const strangeElement = {
      id: 'strange-element',
      getSubType: () => 'other',
    }

    const area = strategy.calculateArea(strangeElement as any)
    expect(isNaN(area)).toBe(true)
  })

  it('should handle element with missing id', () => {
    // Create a mock without id property
    const noIdElement = {
      constructor: { name: 'NoIdShape' },
      getSubType: () => 'other',
    }

    const area = strategy.calculateArea(noIdElement as any)
    expect(isNaN(area)).toBe(true)
  })

  it('should handle undefined element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    const area = strategy.calculateArea(undefined)
    expect(isNaN(area)).toBe(true)
  })

  it('should handle null element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    const area = strategy.calculateArea(null)
    expect(isNaN(area)).toBe(true)
  })

  it('should handle real Circle instance', () => {
    // Create a mock that looks like a real Circle instance
    const realCircle = {
      id: 'real-circle-id',
      constructor: { name: 'Circle' },
      getRadius: () => 10,
      getSubType: () => 'circle',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realCircle, Circle.prototype)

    const area = strategy.calculateArea(realCircle as any)
    const expectedArea = Math.PI * 10 * 10
    expect(area).toBeCloseTo(expectedArea)
  })

  it('should return NaN for invalid radius in real Circle instance', () => {
    // Create a mock that looks like a real Circle instance with invalid radius
    const realCircle = {
      id: 'real-circle-id',
      constructor: { name: 'Circle' },
      getRadius: () => Number.NaN,
      getSubType: () => 'circle',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realCircle, Circle.prototype)

    const area = strategy.calculateArea(realCircle as any)
    expect(isNaN(area)).toBe(true)
  })

  it('should return NaN for negative radius in real Circle instance', () => {
    // Create a mock that looks like a real Circle instance with negative radius
    const realCircle = {
      id: 'real-circle-id',
      constructor: { name: 'Circle' },
      getRadius: () => -10, // Negative radius
      getSubType: () => 'circle',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realCircle, Circle.prototype)

    const area = strategy.calculateArea(realCircle as any)
    expect(isNaN(area)).toBe(true)
  })
})
