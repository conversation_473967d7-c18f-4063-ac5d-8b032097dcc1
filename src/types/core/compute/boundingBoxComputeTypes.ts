/**
 * Defines the Strategy Interface for Bounding Box Calculation
 *
 * @remarks
 * This module introduces the {@link BoundingBoxCalculatorStrategy} interface,
 * a core component of the strategy design pattern applied to geometric computations.
 * This pattern allows for the encapsulation of different bounding box calculation
 * algorithms, each tailored to specific types of {@link Element | elements}.
 *
 * Concrete implementations of this interface will provide the specific logic for
 * computing the axis-aligned {@link BoundingBox} for one or more element types,
 * promoting flexibility and extensibility in these calculations.
 *
 * @module types/core/compute/boundingBoxComputeTypes
 * @see {@link Element} from '@/types/core/elementDefinitions'
 * @see {@link BoundingBox} from '@/types/core/element/geometry/bounding-box'
 * @see {@link ../computeInterfaces} for other computation strategy interfaces.
 */

import type { BoundingBox } from '@/types/core/element/geometry/bounding-box'
import type { Element } from '@/types/core/elementDefinitions'

/**
 * Defines the contract for a bounding box calculation strategy.
 *
 * @remarks
 * Any class that implements this interface is responsible for providing a specific
 * algorithm to calculate the axis-aligned {@link BoundingBox} of an {@link Element}.
 * Concrete strategies will typically be specialized for one or more
 * {@link ElementType | element types}.
 *
 * @see {@link Element} from '@/types/core/elementDefinitions'
 * @see {@link BoundingBox} from '@/types/core/element/geometry/bounding-box'
 * @see {@link ElementType} from '@/types/core/elementDefinitions'
 */
export interface BoundingBoxCalculatorStrategy {
  /**
   * Calculates the axis-aligned bounding box of the provided element.
   *
   * @param element - The {@link Element} for which to calculate the bounding box.
   *                  The element is expected to have properties necessary for the
   *                  specific strategy's calculation (e.g., position, width, height, points).
   * @returns The calculated {@link BoundingBox}.
   */
  calculateBoundingBox: (element: Element) => BoundingBox

  /**
   * Gets the specific element type (or types) that this strategy is designed to handle.
   *
   * @returns A string identifier (or an array of string identifiers) corresponding to
   *          one or more {@link ElementType | ElementTypes} that this strategy supports.
   * @remarks Consider returning `ElementType` or `ElementType[]` for enhanced type safety in future revisions.
   */
  getElementType: () => string | string[]
}
