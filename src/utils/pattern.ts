/**
 * Pattern utility functions for appearance and texture management
 */

import type { PatternDefinition } from '@/types/core/element/elementPatternTypes'

/**
 * Creates a default pattern definition for a given pattern type
 *
 * @param patternType - The type of pattern to create
 * @param elementId - The ID of the element this pattern belongs to
 * @returns A default pattern definition
 */
export function getDefaultPatternDefinition(
  patternType: 'lines' | 'circles' | 'paths',
  elementId: string,
): PatternDefinition {
  const basePattern: PatternDefinition = {
    id: `pattern-${elementId}-${Date.now()}`,
    type: patternType,
    textureType: patternType,
    linesOptions: {
      size: 10,
      strokeWidth: 1,
      color: '#000000',
    },
    circlesOptions: {
      radius: 5,
      size: 15,
      stroke: '#000000',
      fill: 'transparent',
    },
    pathsOptions: {
      d: 'M0,0 L10,10 M10,0 L0,10',
      size: 20,
      stroke: '#000000',
      fill: 'transparent',
    },
    opacity: 1,
  }

  return basePattern
}
