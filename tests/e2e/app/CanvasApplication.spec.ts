import { test, expect } from '@playwright/test';

/**
 * End-to-end test suite for the Canvas Application.
 * These tests verify the functionality of the entire application from a user's perspective.
 */
test.describe('Canvas Application', () => {
  /**
   * Setup function that runs before each test.
   * Navigates to the application and waits for it to load.
   */
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the canvas to be visible
    await page.waitForSelector('#canvas-container');
  });

  /**
   * Tests creating a rectangle.
   * Verifies that a rectangle can be created on the canvas.
   */
  test('Create a rectangle', async ({ page }) => {
    // Click the rectangle tool
    await page.click('.tool-button[data-tool="rectangle"]');
    
    // Draw a rectangle on the canvas
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Verify that a rectangle was created
    const rectangle = await page.locator('rect');
    await expect(rectangle).toBeVisible();
    
    // Verify rectangle attributes
    const x = await rectangle.getAttribute('x');
    const y = await rectangle.getAttribute('y');
    const width = await rectangle.getAttribute('width');
    const height = await rectangle.getAttribute('height');
    
    expect(parseInt(x)).toBe(100);
    expect(parseInt(y)).toBe(100);
    expect(parseInt(width)).toBe(200); // 300 - 100
    expect(parseInt(height)).toBe(150); // 250 - 100
  });

  /**
   * Tests creating an ellipse.
   * Verifies that an ellipse can be created on the canvas.
   */
  test('Create an ellipse', async ({ page }) => {
    // Click the ellipse tool
    await page.click('.tool-button[data-tool="ellipse"]');
    
    // Draw an ellipse on the canvas
    await page.mouse.move(200, 200);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Verify that an ellipse was created
    const ellipse = await page.locator('ellipse');
    await expect(ellipse).toBeVisible();
    
    // Verify ellipse attributes
    const cx = await ellipse.getAttribute('cx');
    const cy = await ellipse.getAttribute('cy');
    const rx = await ellipse.getAttribute('rx');
    const ry = await ellipse.getAttribute('ry');
    
    expect(parseInt(cx)).toBe(200);
    expect(parseInt(cy)).toBe(200);
    expect(parseInt(rx)).toBe(100); // 300 - 200
    expect(parseInt(ry)).toBe(50); // 250 - 200
  });

  /**
   * Tests selecting and moving a shape.
   * Verifies that a shape can be selected and moved on the canvas.
   */
  test('Select and move a shape', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Switch to the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Select the rectangle
    await page.mouse.click(200, 175); // Click in the middle of the rectangle
    
    // Verify that the rectangle is selected
    const selectedRectangle = await page.locator('rect.selected');
    await expect(selectedRectangle).toBeVisible();
    
    // Move the rectangle
    await page.mouse.move(200, 175); // Move to the middle of the rectangle
    await page.mouse.down();
    await page.mouse.move(300, 275); // Move 100px right and 100px down
    await page.mouse.up();
    
    // Verify that the rectangle was moved
    const x = await selectedRectangle.getAttribute('x');
    const y = await selectedRectangle.getAttribute('y');
    
    expect(parseInt(x)).toBe(200); // 100 + 100
    expect(parseInt(y)).toBe(200); // 100 + 100
  });

  /**
   * Tests resizing a shape.
   * Verifies that a shape can be resized on the canvas.
   */
  test('Resize a shape', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Switch to the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Select the rectangle
    await page.mouse.click(200, 175); // Click in the middle of the rectangle
    
    // Wait for the resize handles to appear
    await page.waitForSelector('.resize-handle');
    
    // Resize the rectangle using the bottom-right handle
    await page.mouse.move(300, 250); // Move to the bottom-right corner
    await page.mouse.down();
    await page.mouse.move(400, 350); // Resize to 300x250
    await page.mouse.up();
    
    // Verify that the rectangle was resized
    const rectangle = await page.locator('rect');
    const width = await rectangle.getAttribute('width');
    const height = await rectangle.getAttribute('height');
    
    expect(parseInt(width)).toBe(300); // 400 - 100
    expect(parseInt(height)).toBe(250); // 350 - 100
  });

  /**
   * Tests deleting a shape.
   * Verifies that a shape can be deleted from the canvas.
   */
  test('Delete a shape', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Verify that a rectangle was created
    let rectangle = await page.locator('rect');
    await expect(rectangle).toBeVisible();
    
    // Switch to the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Select the rectangle
    await page.mouse.click(200, 175); // Click in the middle of the rectangle
    
    // Delete the rectangle
    await page.keyboard.press('Delete');
    
    // Verify that the rectangle was deleted
    rectangle = await page.locator('rect');
    await expect(rectangle).not.toBeVisible();
  });

  /**
   * Tests creating multiple shapes.
   * Verifies that multiple shapes can be created on the canvas.
   */
  test('Create multiple shapes', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Create an ellipse
    await page.click('.tool-button[data-tool="ellipse"]');
    await page.mouse.move(400, 300);
    await page.mouse.down();
    await page.mouse.move(500, 350);
    await page.mouse.up();
    
    // Verify that both shapes were created
    const rectangle = await page.locator('rect');
    const ellipse = await page.locator('ellipse');
    
    await expect(rectangle).toBeVisible();
    await expect(ellipse).toBeVisible();
  });

  /**
   * Tests grouping shapes.
   * Verifies that shapes can be grouped on the canvas.
   */
  test('Group shapes', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Create an ellipse
    await page.click('.tool-button[data-tool="ellipse"]');
    await page.mouse.move(400, 300);
    await page.mouse.down();
    await page.mouse.move(500, 350);
    await page.mouse.up();
    
    // Switch to the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Select both shapes (using shift key for multi-select)
    await page.mouse.click(200, 175); // Click in the middle of the rectangle
    await page.keyboard.down('Shift');
    await page.mouse.click(450, 325); // Click in the middle of the ellipse
    await page.keyboard.up('Shift');
    
    // Group the shapes
    await page.keyboard.press('Control+G');
    
    // Verify that a group was created
    const group = await page.locator('g.group');
    await expect(group).toBeVisible();
    
    // Verify that both shapes are in the group
    const rectInGroup = await page.locator('g.group rect');
    const ellipseInGroup = await page.locator('g.group ellipse');
    
    await expect(rectInGroup).toBeVisible();
    await expect(ellipseInGroup).toBeVisible();
  });

  /**
   * Tests ungrouping shapes.
   * Verifies that shapes can be ungrouped on the canvas.
   */
  test('Ungroup shapes', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Create an ellipse
    await page.click('.tool-button[data-tool="ellipse"]');
    await page.mouse.move(400, 300);
    await page.mouse.down();
    await page.mouse.move(500, 350);
    await page.mouse.up();
    
    // Switch to the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Select both shapes (using shift key for multi-select)
    await page.mouse.click(200, 175); // Click in the middle of the rectangle
    await page.keyboard.down('Shift');
    await page.mouse.click(450, 325); // Click in the middle of the ellipse
    await page.keyboard.up('Shift');
    
    // Group the shapes
    await page.keyboard.press('Control+G');
    
    // Verify that a group was created
    let group = await page.locator('g.group');
    await expect(group).toBeVisible();
    
    // Select the group
    await page.mouse.click(300, 200); // Click somewhere in the group
    
    // Ungroup the shapes
    await page.keyboard.press('Control+Shift+G');
    
    // Verify that the group was removed
    group = await page.locator('g.group');
    await expect(group).not.toBeVisible();
    
    // Verify that the shapes still exist
    const rectangle = await page.locator('rect');
    const ellipse = await page.locator('ellipse');
    
    await expect(rectangle).toBeVisible();
    await expect(ellipse).toBeVisible();
  });

  /**
   * Tests changing shape properties.
   * Verifies that shape properties can be changed using the properties panel.
   */
  test('Change shape properties', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Switch to the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Select the rectangle
    await page.mouse.click(200, 175); // Click in the middle of the rectangle
    
    // Change the fill color in the properties panel
    await page.fill('input[name="fill"]', '#0000ff'); // Blue
    await page.press('input[name="fill"]', 'Enter');
    
    // Change the stroke color in the properties panel
    await page.fill('input[name="stroke"]', '#ff0000'); // Red
    await page.press('input[name="stroke"]', 'Enter');
    
    // Change the stroke width in the properties panel
    await page.fill('input[name="strokeWidth"]', '5');
    await page.press('input[name="strokeWidth"]', 'Enter');
    
    // Verify that the rectangle properties were updated
    const rectangle = await page.locator('rect');
    const fill = await rectangle.getAttribute('fill');
    const stroke = await rectangle.getAttribute('stroke');
    const strokeWidth = await rectangle.getAttribute('stroke-width');
    
    expect(fill).toBe('#0000ff');
    expect(stroke).toBe('#ff0000');
    expect(strokeWidth).toBe('5');
  });

  /**
   * Tests undo and redo functionality.
   * Verifies that actions can be undone and redone.
   */
  test('Undo and redo actions', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Verify that a rectangle was created
    let rectangle = await page.locator('rect');
    await expect(rectangle).toBeVisible();
    
    // Undo the creation
    await page.keyboard.press('Control+Z');
    
    // Verify that the rectangle was removed
    rectangle = await page.locator('rect');
    await expect(rectangle).not.toBeVisible();
    
    // Redo the creation
    await page.keyboard.press('Control+Y');
    
    // Verify that the rectangle was restored
    rectangle = await page.locator('rect');
    await expect(rectangle).toBeVisible();
  });

  /**
   * Tests saving and loading the canvas state.
   * Verifies that the canvas state can be saved and loaded.
   */
  test('Save and load canvas state', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Create an ellipse
    await page.click('.tool-button[data-tool="ellipse"]');
    await page.mouse.move(400, 300);
    await page.mouse.down();
    await page.mouse.move(500, 350);
    await page.mouse.up();
    
    // Verify that both shapes were created
    let rectangle = await page.locator('rect');
    let ellipse = await page.locator('ellipse');
    
    await expect(rectangle).toBeVisible();
    await expect(ellipse).toBeVisible();
    
    // Save the canvas state
    await page.click('#save-button');
    
    // Clear the canvas
    await page.click('#clear-button');
    
    // Verify that the shapes were removed
    rectangle = await page.locator('rect');
    ellipse = await page.locator('ellipse');
    
    await expect(rectangle).not.toBeVisible();
    await expect(ellipse).not.toBeVisible();
    
    // Load the saved state
    await page.click('#load-button');
    
    // Verify that the shapes were restored
    rectangle = await page.locator('rect');
    ellipse = await page.locator('ellipse');
    
    await expect(rectangle).toBeVisible();
    await expect(ellipse).toBeVisible();
  });

  /**
   * Tests exporting the canvas as an image.
   * Verifies that the canvas can be exported as an image.
   */
  test('Export canvas as image', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Click the export button
    await page.click('#export-button');
    
    // Wait for the download dialog
    const downloadPromise = page.waitForEvent('download');
    
    // Click the export as PNG option
    await page.click('#export-png');
    
    // Wait for the download to start
    const download = await downloadPromise;
    
    // Verify that a file was downloaded
    expect(download.suggestedFilename()).toContain('.png');
  });

  /**
   * Tests importing an image to the canvas.
   * Verifies that an image can be imported to the canvas.
   */
  test('Import image to canvas', async ({ page }) => {
    // Click the import button
    await page.click('#import-button');
    
    // Set up file input for the file upload
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('#file-input');
    const fileChooser = await fileChooserPromise;
    
    // Upload a test image
    await fileChooser.setFiles('./tests/fixtures/test-image.png');
    
    // Verify that an image was added to the canvas
    const image = await page.locator('image');
    await expect(image).toBeVisible();
  });

  /**
   * Tests the responsiveness of the canvas.
   * Verifies that the canvas is responsive to window size changes.
   */
  test('Canvas responsiveness', async ({ page }) => {
    // Get the initial canvas size
    const initialCanvasSize = await page.evaluate(() => {
      const canvas = document.getElementById('canvas-container');
      return {
        width: canvas.clientWidth,
        height: canvas.clientHeight
      };
    });
    
    // Resize the window
    await page.setViewportSize({ width: 800, height: 600 });
    
    // Get the new canvas size
    const newCanvasSize = await page.evaluate(() => {
      const canvas = document.getElementById('canvas-container');
      return {
        width: canvas.clientWidth,
        height: canvas.clientHeight
      };
    });
    
    // Verify that the canvas size changed
    expect(newCanvasSize.width).not.toBe(initialCanvasSize.width);
    expect(newCanvasSize.height).not.toBe(initialCanvasSize.height);
  });
});
