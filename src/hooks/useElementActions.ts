/**
 * Element Actions Hook
 *
 * This module provides a React hook for managing core element operations
 * such as creation, editing, deletion, and selection. It serves as the
 * primary interface between UI components and the element management services.
 *
 * @module hooks/useElementActions
 */

import type Point from '@/types/core/element/geometry/point'
import type { ElementType, ShapeElement } from '@/types/core/elementDefinitions'
import type { ElementSelectionService, ElementSelectRequest, ShapeCreationService, ShapeDeleteService, ShapeEditService } from '@/types/services/shapes/shapeService'
import { useCallback } from 'react'
// import { shapeCreationService, shapeEditService, shapeDeleteService, shapeSelectionService } from '@/services/shapes/shape-actions'
import { appEventBus } from '@/services/core/event-bus'
import { publishHistoryRedo, publishHistoryUndo } from '@/services/core/event-bus/helpers/publishers/dataPublishers'
// Import your service layer APIs here
import { getService } from '@/services/core/registry'
import { useShapesStore } from '@/store/shapesStore'
import { ServiceId } from '@/types/services/core/serviceIdentifier'

/**
 * Canonical hook for all element/shape actions in the app.
 * This is the ONLY hook UI/components should use for shape/element operations.
 * All methods here must call service APIs or publish events, NEVER directly manipulate the store.
 */
export function useElementActions() {
  // --- Service accessors ---
  const shapeCreationService = getService<ShapeCreationService>(ServiceId.ElementCreationService as ServiceId)
  const shapeEditService = getService<ShapeEditService>(ServiceId.ElementEditService as ServiceId)
  const shapeDeleteService = getService<ShapeDeleteService>(ServiceId.ElementDeleteService as ServiceId)
  const elementSelectionService = getService<ElementSelectionService>(ServiceId.ElementSelectionService as ServiceId)

  // --- Actions ---

  // Create a shape
  const createShape = useCallback(async (type: ElementType, position: Point, properties: Record<string, unknown> = {}) => {
    if (shapeCreationService === null || shapeCreationService === undefined)
      throw new Error('ShapeCreationService not available')
    return shapeCreationService.createShape({ elementType: type, position, properties })
  }, [shapeCreationService])

  // Update a shape
  const updateShape = useCallback(async (id: string, updates: Partial<ShapeElement>) => {
    if (shapeEditService === null || shapeEditService === undefined)
      throw new Error('ShapeEditService not available')
    return shapeEditService.editShape({ id, ...updates })
  }, [shapeEditService])

  // Delete a shape
  const deleteShape = useCallback(async (id: string) => {
    if (shapeDeleteService === null || shapeDeleteService === undefined)
      throw new Error('ShapeDeleteService not available')
    return shapeDeleteService.deleteShape({ id })
  }, [shapeDeleteService])

  // Select a shape
  const selectElement = useCallback(async (id: string, multi: boolean = false) => {
    console.warn('[useElementActions] selectElement called', id, multi)
    if (elementSelectionService === null || elementSelectionService === undefined)
      throw new Error('ElementSelectionService not available')
    return elementSelectionService.selectElement({ id, multiSelect: multi, selectionMode: multi ? 'add' : 'replace' } as ElementSelectRequest & { selectionMode?: string })
  }, [elementSelectionService])

  // Clear selection
  const clearElementSelection = useCallback(async () => {
    if (elementSelectionService === null || elementSelectionService === undefined)
      throw new Error('ElementSelectionService not available')
    return elementSelectionService.clearElementSelection()
  }, [elementSelectionService])

  // Move/transform shape (position only)
  const moveShape = useCallback(async (id: string, position: Point) => {
    if (shapeEditService === null || shapeEditService === undefined)
      throw new Error('ShapeEditService not available')
    return shapeEditService.editShape({ id, position })
  }, [shapeEditService])

  // Undo/Redo
  const undo = useCallback(() => {
    publishHistoryUndo(appEventBus as never)
  }, [])
  const redo = useCallback(() => {
    publishHistoryRedo(appEventBus as never)
  }, [])

  // Batch update elements (property editing, multi-select)
  const updateElements = useCallback(async (ids: string[], updates: Record<string, unknown>) => {
    if (shapeEditService === null || shapeEditService === undefined)
      throw new Error('ShapeEditService not available')
    console.warn('[useElementActions.updateElements] received ids:', ids, 'updates:', JSON.stringify(updates, null, 2))
    return shapeEditService.transformShapes(ids, updates)
  }, [shapeEditService])

  // Select all elements (get all shape IDs from store, then select)
  const selectElements = useCallback(async (allElementIds: string[]) => {
    console.warn('[useElementActions] selectElements called', allElementIds)
    if (elementSelectionService === null || elementSelectionService === undefined)
      throw new Error('ElementSelectionService not available')
    return elementSelectionService.selectElements(allElementIds, true)
  }, [elementSelectionService])

  // Batch delete shapes
  const deleteShapes = useCallback(async (ids: string[]) => {
    if (shapeDeleteService === null || shapeDeleteService === undefined)
      throw new Error('ShapeDeleteService not available')
    return shapeDeleteService.deleteShapes(ids)
  }, [shapeDeleteService])

  // --- Selection state (subscribe to selection changes) ---
  const selectedElementIds = useShapesStore((state: { selectedShapeIds: string[] }) => state.selectedShapeIds)

  return {
    createShape,
    updateShape,
    deleteShape,
    deleteShapes,
    selectElement,
    clearElementSelection,
    moveShape,
    undo,
    redo,
    selectedElementIds,
    selectElements,
    updateElements,
    // ...add more actions as needed
  }
}
