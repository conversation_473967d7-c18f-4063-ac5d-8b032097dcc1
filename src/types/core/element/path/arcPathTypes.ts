/**
 * Defines the type for an arc path element.
 *
 * @remarks
 * This module provides the `Arc` interface, which extends {@link ShapeElement}
 * to include properties specific to defining an arc, such as its radius,
 * start and end angles, and center point.
 *
 * @module types/core/element/path/arcPathTypes
 */
import type { ShapeElement } from '../../elementDefinitions' // Corrected import path

/**
 * Represents an arc path element with its geometric properties.
 *
 * @remarks
 * An arc is a segment of a circle or an ellipse. This interface extends the base
 * {@link ShapeElement} to include arc-specific attributes.
 *
 * @example
 * ```typescript
 * const myArc: Arc = {
 *   // Properties from ShapeElement (id, type, visible, etc.)
 *   id: 'arc-001',
 *   type: 'arc', // Or a more specific ElementType if defined
 *   visible: true,
 *   locked: false,
 *   position: { x: 50, y: 50 }, // Often the center point, but ShapeElement has position
 *   rotation: 0,
 *   scale: { x: 1, y: 1 },
 *   selectable: true,
 *   draggable: true,
 *   showHandles: true,
 *   // Arc-specific properties
 *   center: { x: 50, y: 50 },
 *   radius: 100,
 *   startAngle: 0,  // degrees
 *   endAngle: 90    // degrees
 * };
 * ```
 *
 * @see {@link ShapeElement} for base shape properties.
 * @see {@link Point} for the structure of the center point coordinates.
 * @see {@link ../../../lib/utils/element/path/arcImplementation.ts} for potential implementation details.
 */
export interface Arc extends ShapeElement {
  /**
   * Properties specific to an Arc element.
   */
  properties: ShapeElement['properties'] & {
    // cx, cy would be derived from shapeData.position (the group's origin, 0,0 locally)
    // For elliptical arcs, rx and ry are needed. For circular, rx = ry = radius.
    radius?: number // Kept for simple circular arc definition
    rx?: number // For elliptical arc, or use radius if circular
    ry?: number // For elliptical arc, or use radius if circular
    startAngle: number // In degrees
    endAngle: number // In degrees
    counterClockwise?: boolean
    closed?: boolean // For pie slices
    pathData?: string // Preferred: pre-calculated SVG 'd' attribute string
  }
}
