// import Point from '@/types/core/element/geometry/point'; // Point interface (IPoint) // Unused
import type {
  ErgonomicsEvaluationResult,
  PathwayCheckResult,
  SpacePlanningStrategy,
  SpaceType,
} from '@/types/core/compute'
/**
 * Multifunction Space Planning Strategy
 * Provides planning functionality for multifunctional spaces, including compatibility between different functions,
 * space transformation cost and difficulty, space layout optimization, etc.
 */
import type {
  Element,
  Shape,
  // Path, // Unused
  ShapeElement, // For properties access
} from '@/types/core/elementDefinitions'
// import {
//     calculateSpaceUtilization,
//     checkPathwaysWidth,
//     // checkWorkTriangle is used internally by a specific method here
// } from '@/lib/utils/space/spacePlanning'; // Module not found
import { BoundingBoxClass } from '@/lib/utils/geometry' // BoundingBoxClass is exported from here
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  ElementType as CoreElementType, // For properties access
} from '@/types/core/elementDefinitions'
// import { PointClass } from '@/lib/utils/geometry'; // PointClass is exported from here // Not used after commenting out methods

// Helper to get BoundingBoxClass from an Element
function getElementBoundingBoxClass(element: Element): BoundingBoxClass | null {
  if (element === null || element === undefined)
    return null
  if (element.type === CoreElementType.RECTANGLE || element.type === CoreElementType.SQUARE) {
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement.position !== null && shapeElement.position !== undefined && shapeElement.properties !== null && shapeElement.properties !== undefined
      && typeof shapeElement.properties.width === 'number'
      && typeof shapeElement.properties.height === 'number') {
      return new BoundingBoxClass(
        shapeElement.position.x - shapeElement.properties.width / 2,
        shapeElement.position.y - shapeElement.properties.height / 2,
        shapeElement.properties.width,
        shapeElement.properties.height,
      )
    }
  }
  if (element.type === CoreElementType.CIRCLE) {
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement.position !== null && shapeElement.position !== undefined && shapeElement.properties !== null && shapeElement.properties !== undefined
      && typeof shapeElement.properties.radius === 'number') {
      return new BoundingBoxClass(
        shapeElement.position.x - shapeElement.properties.radius,
        shapeElement.position.y - shapeElement.properties.radius,
        shapeElement.properties.radius * 2,
        shapeElement.properties.radius * 2,
      )
    }
  }
  console.warn(`[MultifunctionRoomPlanningStrategy] getElementBoundingBoxClass: Bounding box for type ${element.type} not fully implemented or properties missing.`)
  return null
}

/**
 * Multifunction Space Planning Strategy
 */
export class MultifunctionRoomPlanningStrategy implements SpacePlanningStrategy {
  public calculateSpaceUtilization(/* elements: Element[], roomBoundaryElement: Element */): number { // Params unused due to commented out body
    // const roomBBoxClass = getElementBoundingBoxClass(roomBoundaryElement);
    // if (!roomBBoxClass) {
    //   throw new CoreError( ErrorType.INVALID_PARAMETER, 'Room boundary must be an element from which a bounding box can be derived.');
    // }
    // return calculateSpaceUtilization(elements, roomBBoxClass);
    console.warn('[MultifunctionRoomPlanningStrategy] calculateSpaceUtilization is temporarily disabled due to missing dependencies.')
    return 0
  }

  public checkPathwayWidth(/* elements: Element[], pathways: Path.Line[], minWidth: number */): PathwayCheckResult[] { // Params unused due to commented out body
    // if (!Array.isArray(elements)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Elements must be an array');
    // if (!Array.isArray(pathways)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Pathways must be an array');
    // if (typeof minWidth !== 'number' || minWidth <= 0) {
    //   throw new CoreError(ErrorType.INVALID_PARAMETER, 'Minimum width must be a positive number');
    // }

    // const formattedPathways = pathways.map((path, index) => {
    //     const lineForUtil = {
    //         id: path.id ?? `pathway-${index}`,
    //         start: path.start as Point,
    //         end: path.end as Point,
    //         type: CoreElementType.LINE,
    //         getSubType: () => CoreElementType.LINE,
    //     } as any;
    //     return { id: path.id ?? `pathway-${index + 1}`, line: lineForUtil as import('@/lib/utils/element/path/lineImplementation').Line };
    // });

    // return checkPathwaysWidth(elements, formattedPathways, minWidth);
    console.warn('[MultifunctionRoomPlanningStrategy] checkPathwayWidth is temporarily disabled due to missing dependencies.')
    return []
  }

  public evaluateErgonomics(
    elements: Element[],
    deskElement: Element, // Can be a desk, dining table, etc.
    chairElement: Element, // Can be an office chair, dining chair, sofa
  ): ErgonomicsEvaluationResult {
    const issues: string[] = []
    const recommendations: string[] = []

    if (!deskElement?.type || !chairElement?.type) {
      throw new CoreError(ErrorType.InvalidParameter, 'Desk/surface and chair/seating elements must be provided and have a type.')
    }

    const functions = this.identifyRoomFunctions(elements)
    const workSurface = deskElement as unknown as Shape.Rectangle // Assuming it's a rectangular surface
    const seating = chairElement as unknown as Shape.Rectangle // Assuming it's a rectangular seating

    const surfaceHeight = workSurface.height ?? 0.75 // Default if not specified
    const seatingShapeElement = seating as unknown as ShapeElement
    const seatingProperties = seatingShapeElement.properties as { seatHeight?: number, isAdjustable?: boolean } | undefined
    const seatHeight = seatingProperties?.seatHeight ?? (seating.height ? seating.height * 0.6 : 0.45)

    if (functions.includes('office')) {
      if (surfaceHeight < 0.7 || surfaceHeight > 0.78) {
        issues.push(`For office work, surface height of ${surfaceHeight.toFixed(2)}m is outside recommended range (0.7-0.78m).`)
      }
      if (seatHeight < 0.42 || seatHeight > 0.55) {
        issues.push(`For office work, chair seat height of ${seatHeight.toFixed(2)}m is outside recommended range (0.42-0.55m).`)
      }
      if (!seatingProperties?.isAdjustable) { // Corrected to use seatingProperties
        recommendations.push('An adjustable office chair is highly recommended for multifunction office use.')
      }
    }
    if (functions.includes('dining')) {
      if (surfaceHeight < 0.72 || surfaceHeight > 0.78) { // Dining table height
        issues.push(`For dining, table height of ${surfaceHeight.toFixed(2)}m is outside typical range (0.72-0.78m).`)
      }
      // Dining chair seat height is usually 0.45-0.5m
      if (seatHeight < 0.43 || seatHeight > 0.5) {
        issues.push(`For dining, chair seat height of ${seatHeight.toFixed(2)}m is outside typical range (0.43-0.5m).`)
      }
    }

    recommendations.push('Ensure adequate lighting for all intended functions.')
    recommendations.push('Consider furniture that can be easily moved or transformed.')

    return { isValid: issues.length === 0, issues, recommendations }
  }

  private identifyRoomFunctions(elements: Element[]): string[] {
    const functions: string[] = []
    elements.forEach((element) => {
      const el = element as ShapeElement // Assume properties are on ShapeElement
      const type = el.type as CoreElementType
      const category = el.properties?.category as string || ''
      const customType = el.properties?.customType as string || ''

      if (customType === 'sofa' || category === 'seating_living' || (type === CoreElementType.FURNITURE && category === 'sofa'))
        functions.push('living')
      if (customType === 'dining_table' || (type === CoreElementType.FURNITURE && category === 'dining_table'))
        functions.push('dining')
      if (customType === 'desk' || category === 'office_furniture' || (type === CoreElementType.FURNITURE && category === 'desk'))
        functions.push('office')
      if (customType === 'bed' || (type === CoreElementType.FURNITURE && category === 'bed'))
        functions.push('bedroom')
      if (customType === 'sofa_bed' || (type === CoreElementType.FURNITURE && category === 'sofa_bed'))
        functions.push('guest')
        // Add more mappings based on your ElementType and properties
    })
    if (functions.includes('living') === false && elements.some(el => (el as ShapeElement).properties?.isLivingArea === true))
      functions.push('living')

    return functions.length > 0 ? [...new Set(functions)] : ['general']
  }

  public evaluateFunctionCompatibility(_elements: Element[], functions: string[]): { compatibilityScore: number, issues: string[], recommendations: string[] } {
    if (!Array.isArray(functions) || functions.length < 2) {
      throw new CoreError(ErrorType.InvalidParameter, 'At least two space functions must be provided for compatibility check.')
    }
    // ... (rest of the method remains largely the same, ensure getFunctionName is correct)
    const issues: string[] = []
    const recommendations: string[] = []
    const compatibilityMatrix: Record<string, Record<string, number>> = { /* ... as before ... */ }
    let totalCompatibilityScore = 0
    let pairCount = 0

    for (let i = 0; i < functions.length; i++) {
      for (let j = i + 1; j < functions.length; j++) {
        const func1 = functions[i]
        const func2 = functions[j]
        let score = 0.5 // Default
        if (compatibilityMatrix[func1]?.[func2] !== undefined)
          score = compatibilityMatrix[func1][func2]
        else if (compatibilityMatrix[func2]?.[func1] !== undefined)
          score = compatibilityMatrix[func2][func1]

        totalCompatibilityScore += score
        pairCount++
        if (score < 0.6)
          issues.push(`${this.getFunctionName(func1)} & ${this.getFunctionName(func2)} low compatibility.`)
      }
    }
    const compatibilityScore = pairCount > 0 ? totalCompatibilityScore / pairCount : 0
    if (compatibilityScore < 0.6)
      recommendations.push(`Overall compatibility is low. Consider reducing functions or using highly adaptable furniture.`)
    return { compatibilityScore, issues, recommendations }
  }

  public evaluateTransformationDifficulty(_elements: Element[], fromFunction: string, toFunction: string): { difficultyScore: number, timeEstimate: number, recommendations: string[] } {
    if (!fromFunction || !toFunction)
      throw new CoreError(ErrorType.InvalidParameter, 'Must provide from and to functions.')
    // ... (rest of the method remains largely the same)
    const difficultyMatrix: Record<string, Record<string, number>> = { /* ... as before ... */ }
    let difficultyScore = 0.5
    if (difficultyMatrix[fromFunction]?.[toFunction] !== undefined) {
      difficultyScore = difficultyMatrix[fromFunction][toFunction]
    }
    const timeEstimate = Math.round(difficultyScore * 20)
    const recommendations: string[] = [`Transformation from ${this.getFunctionName(fromFunction)} to ${this.getFunctionName(toFunction)} has a difficulty score of ${difficultyScore.toFixed(2)}.`]
    return { difficultyScore, timeEstimate, recommendations }
  }

  public optimizeLayout(elements: Element[], functions: string[]): { zoneRecommendations: Record<string, string[]>, generalRecommendations: string[] } {
    if (!Array.isArray(functions) || functions.length === 0) { // Allow single function for general recommendations
      throw new CoreError(ErrorType.InvalidParameter, 'At least one space function must be provided.')
    }
    // ... (rest of the method, ensure calculateOverallBoundingBox is used if roomElement not found)
    const zoneRecommendations: Record<string, string[]> = {}
    const generalRecommendations: string[] = []
    const roomBBox = this.calculateOverallBoundingBox(elements) // Use helper
    const roomArea = roomBBox ? roomBBox.width * roomBBox.height : 0

    if (roomArea <= 0) {
      generalRecommendations.push('Cannot optimize layout without a defined room area.')
      return { zoneRecommendations, generalRecommendations }
    }
    // ... (rest of area allocation and recommendations)
    functions.forEach((func) => {
      zoneRecommendations[func] = [`Define clear zone for ${this.getFunctionName(func)}.`]
    })
    generalRecommendations.push('Use multifunctional furniture and vertical storage.')
    return { zoneRecommendations, generalRecommendations }
  }

  private getFunctionName(functionCode: string): string {
    const names: Record<string, string> = {
      living: 'Living Area',
      dining: 'Dining Area',
      office: 'Office/Workspace',
      guest: 'Guest Area',
      entertainment: 'Entertainment Zone',
      exercise: 'Exercise Zone',
      bedroom: 'Bedroom Area',
      storage: 'Storage Solutions',
      general: 'General Purpose',
    }
    return names[functionCode.toLowerCase()] || functionCode
  }

  private calculateOverallBoundingBox(elements: Element[]): BoundingBoxClass | null {
    if (elements === null || elements === undefined || elements.length === 0)
      return null
    let minX = Infinity
    let minY = Infinity
    let maxX = -Infinity
    let maxY = -Infinity
    let hasBBox = false
    for (const element of elements) {
      const bbox = getElementBoundingBoxClass(element) // Use the helper
      if (bbox) {
        hasBBox = true
        minX = Math.min(minX, bbox.position.x)
        minY = Math.min(minY, bbox.position.y)
        maxX = Math.max(maxX, bbox.position.x + bbox.width)
        maxY = Math.max(maxY, bbox.position.y + bbox.height)
      }
    }
    return hasBBox ? new BoundingBoxClass(minX, minY, maxX - minX, maxY - minY) : null
  }

  public getSpaceType(): SpaceType {
    return 'multifunction'
  }
}
