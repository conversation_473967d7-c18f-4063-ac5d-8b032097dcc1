# Development Environment Setup

This guide provides instructions for setting up the development environment for RenoPilot.JS.Shapes2.

## Prerequisites

Before you begin, ensure you have the following installed on your system:

*   **Node.js:** Version 18.x or later. You can download it from [nodejs.org](https://nodejs.org/).
    *   We recommend using a version manager like [nvm](https://github.com/nvm-sh/nvm) to manage Node.js versions.
*   **npm (Node Package Manager):** Version 8.x or later (usually comes with Node.js).
    *   Alternatively, you can use **Yarn** (Version 1.x or later).
*   **Git:** For cloning the repository. Download from [git-scm.com](https://git-scm.com/).

## Installation Steps

1.  **Clone the Repository:**
    Open your terminal and run the following command to clone the project:
    ```bash
    git clone https://github.com/your-username/RenoPilot.JS.Shapes2.git
    ```
    (Replace `https://github.com/your-username/RenoPilot.JS.Shapes2.git` with the actual repository URL if different.)

2.  **Navigate to the Project Directory:**
    ```bash
    cd RenoPilot.JS.Shapes2
    ```

3.  **Install Dependencies:**
    Using npm:
    ```bash
    npm install
    ```
    Or, if you prefer Yarn:
    ```bash
    yarn install
    ```
    This command will download and install all the project dependencies listed in `package.json`.

## Running the Development Server

Once the installation is complete, you can start the development server:

Using npm:
```bash
npm run dev
```
Or, with Yarn:
```bash
yarn dev
```
This command will start the Vite development server. By default, it should be accessible at `http://localhost:5173` (or another port if 5173 is in use - check the terminal output).
The application will automatically reload if you make changes to the source files.

## Building for Production

To create an optimized production build:

Using npm:
```bash
npm run build
```
Or, with Yarn:
```bash
yarn build
```
This will generate static assets in the `dist/` directory. These files are ready to be deployed to any static web hosting service.

## Running Tests

The project uses Playwright for end-to-end and integration tests, and Vitest for unit tests.

*   **Run all Playwright tests:**
    ```bash
    npm test
    # or
    yarn test
    ```

*   **Run Vitest unit tests:**
    ```bash
    npm run test:vitest
    # or
    yarn test:vitest
    ```
    To run Vitest in watch mode:
    ```bash
    npm run test:vitest -- --watch
    # or
    yarn test:vitest --watch
    ```

## Linting and Formatting

The project uses ESLint for linting and Prettier for code formatting.

*   **Run ESLint:**
    ```bash
    npm run lint
    # or
    yarn lint
    ```
    To automatically fix linting issues:
    ```bash
    npm run lint -- --fix
    # or
    yarn lint --fix
    ```

*   **Format code with Prettier:**
    Prettier is often integrated with ESLint and run via `lint --fix`. It's also recommended to set up your code editor to format on save using Prettier. See [Editor Integration](https://prettier.io/docs/en/editors.html).
    To check formatting:
    ```bash
    npm run format:check
    # or
    yarn format:check
    ```
    To apply formatting:
    ```bash
    npm run format
    # or
    yarn format
    ```

## Recommended Editor Setup

*   **Visual Studio Code (VS Code)** is recommended.
*   **Extensions:**
    *   [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)
    *   [Prettier - Code formatter](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)
    *   [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss)
    *   [Vitest](https://marketplace.visualstudio.com/items?itemName=ZixuanChen.vitest-explorer) (for running Vitest tests from the editor)
*   **Settings (VS Code `.vscode/settings.json`):**
    ```json
    {
      "editor.formatOnSave": true,
      "editor.defaultFormatter": "esbenp.prettier-vscode",
      "eslint.validate": [
        "javascript",
        "javascriptreact",
        "typescript",
        "typescriptreact"
      ],
      "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit"
      }
    }
    ```
    Ensure you have a `.vscode` directory in your project root with this `settings.json` file, or configure these settings globally in your VS Code.

## Troubleshooting

*   **Node.js Version Issues:** If you encounter errors related to Node.js versions, ensure you are using a compatible version (18+). Use `nvm` to switch if necessary.
*   **Dependency Conflicts:** If `npm install` fails, try deleting `node_modules` and `package-lock.json` (or `yarn.lock`) and running the install command again.
    ```bash
    rm -rf node_modules package-lock.json
    npm install
    ```
*   **Port in Use:** If the development server fails to start because the port is in use, Vite will usually try the next available port. You can also specify a port:
    ```bash
    npm run dev -- --port 3001
    ```

This setup should get you started with developing RenoPilot.JS.Shapes2. Happy coding!