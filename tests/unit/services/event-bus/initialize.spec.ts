import { expect, test } from '@playwright/test'
import { appEventBus } from '@/services/event-bus/AppEventBus'
import { cleanupEventSystem, initializeEventSystem } from '@/services/event-bus/initialize'
import { AppEventType } from '@/types/services/events'

test.describe('Event System Initialization', () => {
  let originalConsoleLog: typeof console.log
  let originalConsoleDebug: typeof console.debug
  let logMessages: string[]
  let debugMessages: string[]

  test.beforeEach(async () => {
    // 保存原始的 console 方法
    originalConsoleLog = console.log
    originalConsoleDebug = console.debug
    logMessages = []
    debugMessages = []

    // 模拟 console 方法
    console.log = (message: string) => {
      logMessages.push(message)
    }
    console.debug = (message: string, event?: any) => {
      debugMessages.push(message)
    }

    // 重置事件总线
    await appEventBus.reset()
  })

  test.afterEach(() => {
    // 恢复原始的 console 方法
    console.log = originalConsoleLog
    console.debug = originalConsoleDebug
  })

  test('should initialize event system correctly', async () => {
    // 执行初始化
    await initializeEventSystem()

    // 验证日志输出
    expect(logMessages).toContain('Initializing event system...')
    expect(logMessages).toContain('Event system initialized with CoreCoordinator integration')

    // 验证事件总线是否被重置
    const subscriptions = appEventBus.getSubscriptions()
    expect(subscriptions.size).toBeGreaterThan(0) // 应该有开发环境的日志订阅
  })

  test('should clean up event system correctly', async () => {
    // 先初始化系统
    await initializeEventSystem()

    // 执行清理
    await cleanupEventSystem()

    // 验证日志输出
    expect(logMessages).toContain('Cleaning up event system...')
    expect(logMessages).toContain('Event system cleaned up')

    // 验证事件总线是否被重置
    const subscriptions = appEventBus.getSubscriptions()
    expect(subscriptions.size).toBe(0)
  })

  test('should set up development logging in development environment', async () => {
    // 设置开发环境
    const originalNodeEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'development'

    // 执行初始化
    await initializeEventSystem()

    // 发布一个测试事件
    await appEventBus.publish({
      type: Object.values(AppEventType)[0],
      payload: { test: 'data' },
    })

    // 验证是否有日志输出
    expect(debugMessages.length).toBeGreaterThan(0)

    // 恢复环境设置
    process.env.NODE_ENV = originalNodeEnv
  })

  test('should not set up development logging in production environment', async () => {
    // 设置生产环境
    const originalNodeEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'production'

    // 执行初始化
    await initializeEventSystem()

    // 发布一个测试事件
    await appEventBus.publish({
      type: Object.values(AppEventType)[0],
      payload: { test: 'data' },
    })

    // 验证没有日志输出
    expect(debugMessages.length).toBe(0)

    // 恢复环境设置
    process.env.NODE_ENV = originalNodeEnv
  })

  test('should handle multiple initializations correctly', async () => {
    // 连续执行多次初始化
    await initializeEventSystem()
    await initializeEventSystem()

    // 验证系统状态正常
    const subscriptions = appEventBus.getSubscriptions()
    expect(subscriptions.size).toBeGreaterThan(0)

    // 验证日志输出正确
    const initCount = logMessages.filter(msg =>
      msg === 'Initializing event system...',
    ).length
    expect(initCount).toBe(2)
  })
})
