/**
 * Property Row Component
 *
 * A flexible row component for organizing property fields in single or double column layouts.
 * Provides consistent spacing and responsive behavior for property panels.
 *
 * Features:
 * - Single and double column layouts
 * - Responsive grid system
 * - Consistent gap spacing
 * - Flexible content arrangement
 * - Support for mixed content types
 *
 * @example
 * ```tsx
 * <PropertyRow columns={2}>
 *   <PropertyField label="X Position" value={x} onChange={setX} />
 *   <PropertyField label="Y Position" value={y} onChange={setY} />
 * </PropertyRow>
 *
 * <PropertyRow columns={1}>
 *   <PropertyField label="Description" value={desc} onChange={setDesc} />
 * </PropertyRow>
 * ```
 */

import React from 'react'
import { cn } from '@/lib/utils'

/**
 * Props for the PropertyRow component
 */
interface PropertyRowProps {
  /** Number of columns (1 or 2) */
  columns?: 1 | 2
  /** Child components to render in the row */
  children: React.ReactNode
  /** Additional CSS classes */
  className?: string
  /** Custom gap size override */
  gap?: 'sm' | 'md' | 'lg'
  /** Whether to align items to the top */
  alignTop?: boolean
}

/**
 * PropertyRow component for organizing property fields in flexible layouts.
 *
 * This component provides a standardized way to arrange property fields
 * in single or double column layouts with consistent spacing.
 *
 * @param props - The component props
 * @param props.columns - Number of columns in the grid layout
 * @param props.children - Child elements to render in the row
 * @param props.className - Additional CSS classes
 * @param props.gap - Gap size between grid items
 * @param props.alignTop - Whether to align items to the top
 * @returns The rendered property row
 */
export function PropertyRow({
  columns = 2,
  children,
  className,
  gap = 'md',
  alignTop = false,
}: PropertyRowProps) {
  const gapClasses = {
    sm: 'gap-x-2 gap-y-1',
    md: 'gap-x-3 gap-y-2',
    lg: 'gap-x-4 gap-y-3',
  }

  if (columns === 1) {
    return (
      <div className={cn('space-y-2', className)}>
        {children}
      </div>
    )
  }

  return (
    <div
      className={cn(
        'grid grid-cols-2',
        gapClasses[gap],
        alignTop && 'items-start',
        className,
      )}
    >
      {children}
    </div>
  )
}
