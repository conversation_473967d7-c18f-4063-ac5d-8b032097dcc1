/**
 * Bottom Asset Drawer Component
 *
 * A comprehensive asset management drawer positioned at the bottom of the screen
 * that provides quick access to design elements and drawing tools. The component
 * features both collapsed and expanded states for optimal workflow efficiency.
 *
 * Features:
 * - Collapsed state: Quick access to pinned assets and common tools
 * - Expanded state: Full asset library with categorized browsing
 * - Drag-and-drop functionality for asset placement
 * - Click-to-activate for drawing tools
 * - Context-aware asset filtering based on current module/step
 * - Asset pinning system for personalized quick access
 * - Real-time asset state management and event handling
 * - Responsive design with tooltips and visual feedback
 *
 * States:
 * - Collapsed: Shows up to 10 pinned draggable assets and 10 drawing tools
 * - Expanded: Full categorized asset library with search and filtering
 *
 * Asset Types:
 * - Draggable assets: Shapes, furniture, fixtures that can be placed on canvas
 * - Drawing tools: Line, arc, curve tools that activate drawing modes
 * - Special elements: Context-specific assets based on current workflow step
 *
 * @example
 * ```tsx
 * <BottomAssetDrawer
 *   onAssetDragStart={handleDragStart}
 *   getSettingsForType={getDefaults}
 *   currentModuleId="FURNITURE"
 *   currentStepId="SEATING"
 *   onAssetSelectFromExpanded={handleToolSelect}
 *   onTogglePinAsset={handlePin}
 *   isAssetPinned={checkPinned}
 *   sheetOpen={isExpanded}
 *   onSheetOpenChange={setExpanded}
 *   pinnedAssets={userPinnedAssets}
 * />
 * ```
 */

import type { InitialElementProperties } from '@/config/defaultElementSettings'
import type { DesignElement } from '@/hooks/useSpecialDrawerAssets'
import type { PinnedAsset } from '@/types/core/assetTypes'
import type { MajorCategory, MinorCategory } from '@/types/core/majorMinorTypes'

import {
  ChartNoAxesGantt,
  ChevronUp,
  Circle,
  HelpCircle,
  Hexagon,
  Image as ImageIcon,
  LoaderCircle,
  Minus,
  Pentagon,
  Pin,
  PinOff,
  RectangleHorizontal,
  Spline,
  Square,
  Triangle,
  Type,
} from 'lucide-react'
import React, { useCallback, useEffect, useMemo, useState } from 'react'

import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Sheet, SheetContent, SheetHeader, SheetOverlay, SheetPortal, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import {
  BASIC_DRAGGABLE_ORDER_COLLAPSED,
  BASIC_DRAWING_TOOL_ORDER_COLLAPSED,
  basicPathsForExpandedConfig,
  basicShapesForExpandedConfig,
  DRAGGABLE_COLLAPSED_TYPES,
  DRAWING_TOOL_COLLAPSED_TYPES,
} from '@/config/assetConfig'
import { useSpecialDrawerAssets } from '@/hooks/useSpecialDrawerAssets'
import { cn } from '@/lib/utils'
import { getIconForPredefinedElement } from '@/lib/utils/assetIconUtils'
import { eventBus, EventType } from '@/lib/utils/eventBus'
import { predefinedElements } from '@/types/core/element/definitions/predefinedElements'
import { ElementType as CoreElementType } from '@/types/core/elementDefinitions'

/**
 * Interface for individual assets within an asset group
 */
interface AssetInGroup {
  /** Unique identifier for the asset */
  id: string
  /** Display name of the asset */
  name: string
  /** React icon component for the asset */
  icon: React.ReactNode
  /** Element type from the core definitions */
  type: CoreElementType
  /** Whether this is a context-specific asset */
  isSpecific: boolean
  /** Additional data for predefined elements */
  predefinedElementData?: Record<string, unknown>
}

/**
 * Interface for asset groups in the expanded view
 */
interface AssetGroup {
  /** Unique identifier for the group */
  id: string
  /** Display name of the group (category) */
  name: string
  /** Array of assets in this group */
  assets: AssetInGroup[]
}

/**
 * Props for the BottomAssetDrawer component
 */
interface BottomAssetDrawerProps {
  /** Callback fired when an asset drag operation starts */
  onAssetDragStart: (
    elementType: CoreElementType,
    properties: InitialElementProperties,
    event: React.DragEvent<HTMLButtonElement | HTMLDivElement>
  ) => void
  /** Function to get default settings for an element type */
  getSettingsForType: (elementType: CoreElementType) => InitialElementProperties

  /** Current major category (module) context */
  currentModuleId: MajorCategory | undefined
  /** Current minor category (step) context */
  currentStepId: MinorCategory | undefined
  /** Callback fired when a drawing tool is selected from expanded view */
  onAssetSelectFromExpanded: (elementType: CoreElementType) => void

  /** Callback to toggle asset pin status */
  onTogglePinAsset: (elementType: CoreElementType, isSpecific: boolean, currentModuleId?: string, currentStepId?: string, assetName?: string) => void
  /** Function to check if an asset is currently pinned */
  isAssetPinned: (elementType: CoreElementType, isSpecific: boolean, currentModuleId?: string, currentStepId?: string, assetName?: string) => boolean

  /** Whether the expanded sheet is open */
  sheetOpen: boolean
  /** Callback to control sheet open state */
  onSheetOpenChange: (open: boolean) => void
  /** Array of currently pinned assets */
  pinnedAssets: PinnedAsset[]
}

/**
 * BottomAssetDrawer component that provides a comprehensive asset management interface.
 *
 * This component renders a bottom-positioned drawer with both collapsed and expanded states.
 * In collapsed state, it shows quick access to pinned assets. In expanded state, it provides
 * a full asset library with categorized browsing, search, and pinning functionality.
 *
 * The component manages context-aware asset filtering, drag-and-drop operations, and
 * drawing tool activation. It integrates with the pinning system to provide personalized
 * quick access to frequently used assets.
 *
 * @param props - The component props
 * @param props.onAssetDragStart - Callback fired when an asset drag operation starts
 * @param props.getSettingsForType - Function to get default settings for an element type
 * @param props.currentModuleId - Current major category (module) context
 * @param props.currentStepId - Current minor category (step) context
 * @param props.onAssetSelectFromExpanded - Callback fired when a drawing tool is selected from expanded view
 * @param props.onTogglePinAsset - Callback to toggle asset pin status
 * @param props.isAssetPinned - Function to check if an asset is currently pinned
 * @param props.sheetOpen - Whether the expanded sheet is open
 * @param props.onSheetOpenChange - Callback to control sheet open state
 * @param props.pinnedAssets - Array of currently pinned assets
 * @returns The rendered bottom asset drawer component
 */
const BottomAssetDrawer: React.FC<BottomAssetDrawerProps> = ({
  onAssetDragStart,
  getSettingsForType,
  currentModuleId,
  currentStepId,
  onAssetSelectFromExpanded,
  onTogglePinAsset,
  isAssetPinned,
  sheetOpen,
  onSheetOpenChange,
  pinnedAssets,
}) => {
  // 添加状态来跟踪当前选中的绘制工具
  const [selectedDrawingTool, setSelectedDrawingTool] = useState<CoreElementType | null>(null)
  /* // Commented out unused variable defaultQuickButtonAssets
  const defaultQuickButtonAssets = useMemo<QuickAsset[]>(() => [
    { name: 'Square', icon: <Square className="h-5 w-5" />, type: CoreElementType.SQUARE, isSpecific: false, assetName: 'Square' },
    { name: 'Circle', icon: <Circle className="h-5 w-5" />, type: CoreElementType.CIRCLE, isSpecific: false, assetName: 'Circle' },
    { name: 'Triangle', icon: <Triangle className="h-5 w-5" />, type: CoreElementType.TRIANGLE, isSpecific: false, assetName: 'Triangle' },
    { name: 'Hexagon', icon: <Hexagon className="h-5 w-5" />, type: CoreElementType.HEXAGON, isSpecific: false, assetName: 'Hexagon' },
    { name: 'Line', icon: <MoveRight className="h-5 w-5" />, type: CoreElementType.LINE, isSpecific: false, assetName: 'Line' },
    { name: 'Text', icon: <Type className="h-5 w-5" />, type: CoreElementType.TEXT, isSpecific: false, assetName: 'Text' },
    { name: 'Image', icon: <ImageIcon className="h-5 w-5" />, type: CoreElementType.IMAGE, isSpecific: false, assetName: 'Image' },
  ], [])
  */

  /* // Commented out unused variable otherGeneralAssetsForPinning
  const otherGeneralAssetsForPinning = useMemo<QuickAsset[]>(() => [
    { name: 'Rectangle', icon: <Square className="h-5 w-5" />, type: CoreElementType.RECTANGLE, isSpecific: false, assetName: 'Rectangle' },
    { name: 'Ellipse', icon: <Circle className="h-5 w-5" />, type: CoreElementType.ELLIPSE, isSpecific: false, assetName: 'Ellipse' },
    { name: 'Quadrilateral', icon: <Square className="h-5 w-5" />, type: CoreElementType.QUADRILATERAL, isSpecific: false, assetName: 'Quadrilateral' },
    { name: 'Pentagon', icon: <Pentagon className="h-5 w-5" />, type: CoreElementType.PENTAGON, isSpecific: false, assetName: 'Pentagon' },
    { name: 'Polyline', icon: <Spline className="h-5 w-5" />, type: CoreElementType.POLYLINE, isSpecific: false, assetName: 'Polyline' },
    { name: 'Arc', icon: <Orbit className="h-5 w-5" />, type: CoreElementType.ARC, isSpecific: false, assetName: 'Arc' },
    { name: 'Quadratic', icon: <Waypoints className="h-5 w-5" />, type: CoreElementType.QUADRATIC, isSpecific: false, assetName: 'Quadratic' },
    { name: 'Cubic', icon: <PenTool className="h-5 w-5" />, type: CoreElementType.CUBIC, isSpecific: false, assetName: 'Cubic' },
  ], [])
  */

  // Use the new hook to get special elements
  const specialElements = useSpecialDrawerAssets(currentModuleId, currentStepId)
  const hasSpecialElements = specialElements.length > 0

  const [isDraggingFromExpandedSheet, setIsDraggingFromExpandedSheet] = useState(false)

  const formatStepNameForTab = useCallback((moduleId: string, stepId: string): string => {
    if (moduleId === '' || stepId === '') {
      return 'Special'
    }
    const stepNamePart = stepId.split('-').pop()
    return stepNamePart != null && stepNamePart !== '' ? stepNamePart.charAt(0).toUpperCase() + stepNamePart.slice(1) : 'Special'
  }, [])

  const dynamicTabLabel = useMemo(() =>
    formatStepNameForTab(currentModuleId ?? '', currentStepId ?? ''), [currentModuleId, currentStepId, formatStepNameForTab])

  const formatModuleName = useCallback((moduleId?: string): string => {
    if (moduleId == null || moduleId === '') {
      return 'General'
    }
    return moduleId.charAt(0).toUpperCase() + moduleId.slice(1).toLowerCase()
  }, [])

  useEffect(() => {
    // This effect updates local state based on currentModuleId/currentStepId.
    // Since useSpecialDrawerAssets is a hook, it will re-run when its dependencies (currentModuleId, currentStepId) change,
    // and specialElements will be updated automatically.
    // The original setSpecialElements and setHasSpecialElements calls are no longer needed here.
    // If there were other side effects here, they would need to be preserved.
  }, [currentModuleId, currentStepId, hasSpecialElements]) // Added hasSpecialElements to log its change

  // 添加事件监听器，监听路径绘制完成事件
  useEffect(() => {
    // 订阅路径绘制完成事件
    const unsubscribe = eventBus.subscribe(EventType.PATH_DRAWING_COMPLETED, (_data) => {
      // console.warn('[BottomAssetDrawer] Path drawing completed event received:', data)
      // 重置选中的绘制工具
      setSelectedDrawingTool(null)
    })

    // 组件卸载时取消订阅
    return () => {
      unsubscribe()
    }
  }, [])

  const mapConfigToAssets = useCallback((
    configs: Array<{ category: string, items: Array<{ name: string, icon: React.ReactNode, type: CoreElementType, isSpecific?: boolean, predefinedElementData?: Record<string, unknown> }> }>,
    groupName: string,
  ): AssetGroup[] => {
    return configs.map(config => ({
      id: `${groupName.toLowerCase()}-${config.category.toLowerCase().replace(/\s+/g, '-')}`,
      name: config.category,
      assets: config.items.map(item => ({
        id: `${item.type}-${item.name.replace(/\s+/g, '-')}`,
        name: item.name,
        icon: item.icon,
        type: item.type,
        isSpecific: item.isSpecific || false,
        predefinedElementData: item.predefinedElementData,
      })),
    }))
  }, []) // No dependencies needed if it only uses its arguments and global constants/imports

  // Memoize the transformation of config to assets
  const basicShapesForExpanded = useMemo(
    () => mapConfigToAssets(basicShapesForExpandedConfig, 'Shape'),
    [mapConfigToAssets],
  )

  const basicPathsForExpanded = useMemo(
    () => mapConfigToAssets(basicPathsForExpandedConfig, 'Path'),
    [mapConfigToAssets],
  )

  // Prepare assets for the collapsed view based on pinnedAssets prop, context, sorting, and limits
  const { draggableItemsCollapsed, drawingToolItemsCollapsed } = useMemo(() => {
    console.warn('[BottomAssetDrawer] Processing pinnedAssets for collapsed view:', pinnedAssets)

    const contextuallyRelevantPins = pinnedAssets.filter((asset) => {
      if (asset.isSpecific) {
        // For specific assets, they must match the current context
        const moduleMatches = typeof asset.moduleId === 'string' && asset.moduleId === currentModuleId
        const stepMatches = (typeof currentStepId === 'string' && typeof asset.stepId === 'string' && asset.stepId === currentStepId)
          || (currentStepId === undefined && asset.stepId === undefined)
        return moduleMatches && stepMatches
      }
      return true // Basic assets are always relevant if pinned
    })

    console.warn('[BottomAssetDrawer] Contextually relevant pins:', contextuallyRelevantPins)

    const draggables: PinnedAsset[] = []
    const drawTools: PinnedAsset[] = []

    contextuallyRelevantPins.forEach((asset) => {
      console.warn('[BottomAssetDrawer] Processing asset:', asset.type, 'isSpecific:', asset.isSpecific)

      if (DRAGGABLE_COLLAPSED_TYPES.includes(asset.type)) {
        console.warn('[BottomAssetDrawer] Asset classified as DRAGGABLE:', asset.type)
        draggables.push(asset)
      }
      else if (DRAWING_TOOL_COLLAPSED_TYPES.includes(asset.type)) {
        console.warn('[BottomAssetDrawer] Asset classified as DRAWING_TOOL:', asset.type)
        drawTools.push(asset)
      }
      else {
        console.warn('[BottomAssetDrawer] Asset NOT classified (neither draggable nor drawing tool):', asset.type)
      }
    })

    // Sort draggables
    draggables.sort((a, b) => {
      const isABasic = !a.isSpecific
      const isBBasic = !b.isSpecific

      if (isABasic && !isBBasic)
        return -1 // Basic items first
      if (!isABasic && isBBasic)
        return 1 // Special items after basic

      if (isABasic && isBBasic) { // Both are basic, sort by predefined order
        return BASIC_DRAGGABLE_ORDER_COLLAPSED.indexOf(a.type) - BASIC_DRAGGABLE_ORDER_COLLAPSED.indexOf(b.type)
      }
      // Both are special, sort by assetName (or could be order of pinning if preferred)
      const aName = a.assetName ?? ''
      const bName = b.assetName ?? ''
      return aName.localeCompare(bName)
    })

    // Sort drawing tools
    drawTools.sort((a, b) => {
      const isABasic = !a.isSpecific
      const isBBasic = !b.isSpecific

      if (isABasic && !isBBasic)
        return -1 // Basic items first
      if (!isABasic && isBBasic)
        return 1 // Special items after basic

      if (isABasic && isBBasic) { // Both are basic, sort by predefined order
        return BASIC_DRAWING_TOOL_ORDER_COLLAPSED.indexOf(a.type) - BASIC_DRAWING_TOOL_ORDER_COLLAPSED.indexOf(b.type)
      }
      // Both are special, sort by assetName
      const aName = a.assetName ?? ''
      const bName = b.assetName ?? ''
      return aName.localeCompare(bName)
    })

    return {
      draggableItemsCollapsed: draggables.slice(0, 10), // Max 10
      drawingToolItemsCollapsed: drawTools.slice(0, 10), // Max 10
    }
  }, [pinnedAssets, currentModuleId, currentStepId])

  const handleQuickAssetDragStart = (
    asset: PinnedAsset,
    event: React.DragEvent<HTMLButtonElement | HTMLDivElement>,
  ) => {
    // console.log('[BottomAssetDrawer] handleQuickAssetDragStart: Quick asset dragged', { asset })
    const assetTypeForDefaults = asset.type
    let properties: InitialElementProperties = getSettingsForType(assetTypeForDefaults)
    let dragDataTypeForCanvas = asset.type
    let elementSpecificData: Record<string, unknown> = {}
    let effectiveAssetName = asset.assetName

    if (asset.predefinedElementData) {
      dragDataTypeForCanvas = CoreElementType.IMAGE
      if (asset.type === CoreElementType.OPENING || asset.type === CoreElementType.FLOOR_AREA || asset.type === CoreElementType.WALL) {
        dragDataTypeForCanvas = asset.type
      }

      properties = {
        ...properties,
        width: asset.predefinedElementData.defaultWidth ?? properties.width,
        height: asset.predefinedElementData.defaultHeight ?? properties.height,
      }
      elementSpecificData = {
        ...(asset.predefinedElementData.attributes || {}),
        predefinedId: asset.predefinedElementData.id,
        actualImagePath: asset.predefinedElementData.imagePath,
        elementName: asset.predefinedElementData.name,
        majorCategory: asset.predefinedElementData.majorCategory,
        minorCategory: asset.predefinedElementData.minorCategory,
        mountingType: asset.predefinedElementData.mountingType,
        tags: asset.predefinedElementData.tags,
        description: asset.predefinedElementData.description,
      }
      effectiveAssetName = asset.predefinedElementData.name
    }
    else {
      // For basic shapes not from predefinedElements, assetName from PinnedAsset is used.
      // elementSpecificData remains empty or could hold other type-specific info if needed
      // console.log('[BottomAssetDrawer] handleQuickAssetDragStart: Basic asset, using type as name if assetName is empty.', { assetType: asset.type, assetName: asset.assetName })
    }

    const dragData = {
      elementType: dragDataTypeForCanvas,
      properties,
      name: (typeof effectiveAssetName === 'string' && effectiveAssetName !== '') ? effectiveAssetName : asset.type,
      from: 'bottom-drawer-quick-asset',
      elementSpecificData,
    }
    event.dataTransfer.setData('application/json', JSON.stringify(dragData))
    event.dataTransfer.effectAllowed = 'copy'
    // console.log('[BottomAssetDrawer] handleQuickAssetDragStart: Drag data set', { dragData })
    // onAssetDragStart(dragDataTypeForCanvas, properties, event); // This might be too early or redundant if canvas handles it
  }

  // NEW: Handler for clicking drawing tool assets in collapsed view
  const handleQuickAssetClick = (asset: PinnedAsset) => {
    console.warn('[BottomAssetDrawer] handleQuickAssetClick: Quick asset clicked (drawing tool)', { assetType: asset.type })
    console.warn('[BottomAssetDrawer] handleQuickAssetClick: Before setSelectedDrawingTool, current:', selectedDrawingTool)

    // 设置当前选中的绘制工具
    setSelectedDrawingTool(asset.type)
    console.warn('[BottomAssetDrawer] handleQuickAssetClick: After setSelectedDrawingTool, new:', asset.type)

    // 调用父组件的回调函数
    console.warn('[BottomAssetDrawer] handleQuickAssetClick: Calling onAssetSelectFromExpanded with:', asset.type)
    onAssetSelectFromExpanded(asset.type)
    console.warn('[BottomAssetDrawer] handleQuickAssetClick: onAssetSelectFromExpanded called')

    // 注意：不再需要定时器，因为我们现在使用事件系统来重置按钮状态
    // 当路径绘制完成时，会触发 PATH_DRAWING_COMPLETED 事件，然后重置按钮状态
  }

  // Helper to get icon for PinnedAsset (considering predefinedElementData)
  const getIconForPinnedAsset = (asset: PinnedAsset): React.ReactNode => {
    if (asset.isSpecific && typeof asset.assetName === 'string' && asset.assetName !== '') {
      const special = specialElements.find(el => el.name === asset.assetName)
      if (special) {
        if (special.predefinedElementData) {
          return getIconForPredefinedElement(special.predefinedElementData)
        }
        else if (special.icon != null) { // Check for not null/undefined
          return special.icon
        }
      }
      // console.warn(`[BottomAssetDrawer] Specific pinned asset '${asset.assetName}' not found or has no icon.`)
      return <HelpCircle className="h-5 w-5" />
    }
    else {
      const assetTypeLower = asset.type.toLowerCase()
      const toolElement = predefinedElements.find(pe =>
        pe.id.includes(`_${assetTypeLower}_tool`)
        || (pe.name.toLowerCase().includes(assetTypeLower) && pe.tags?.includes('tool')),
      )

      if (toolElement) {
        return getIconForPredefinedElement(toolElement)
      }

      switch (asset.type) {
        case CoreElementType.SQUARE: return <Square className="h-5 w-5" />
        case CoreElementType.RECTANGLE: return <RectangleHorizontal className="h-5 w-5" />
        case CoreElementType.CIRCLE: return <Circle className="h-5 w-5" />
        case CoreElementType.ELLIPSE: return <Circle className="h-5 w-5" />
        case CoreElementType.TRIANGLE: return <Triangle className="h-5 w-5" />
        case CoreElementType.HEXAGON: return <Hexagon className="h-5 w-5" />
        case CoreElementType.PENTAGON: return <Pentagon className="h-5 w-5" />
        case CoreElementType.LINE: return <Minus className="h-5 w-5" />
        case CoreElementType.TEXT: return <Type className="h-5 w-5" />
        case CoreElementType.IMAGE: return <ImageIcon className="h-5 w-5" />
        case CoreElementType.POLYLINE: return <ChartNoAxesGantt className="h-5 w-5" />
        case CoreElementType.ARC: return <LoaderCircle className="h-5 w-5" />
        case CoreElementType.QUADRATIC: return <Spline className="h-5 w-5" />
        case CoreElementType.CUBIC: return <Spline className="h-5 w-5" />
        case CoreElementType.POLYGON:
        case CoreElementType.QUADRILATERAL:
        case CoreElementType.HEPTAGON:
        case CoreElementType.OCTAGON:
        case CoreElementType.NONAGON:
        case CoreElementType.DECAGON:
        case CoreElementType.TEXT_LABEL:
        case CoreElementType.WALL:
        case CoreElementType.DOOR:
        case CoreElementType.WINDOW:
        case CoreElementType.FURNITURE:
        case CoreElementType.FIXTURE:
        case CoreElementType.ROOM:
        case CoreElementType.LIGHT:
        case CoreElementType.FLOOR_AREA:
        case CoreElementType.HANDRAIL:
        case CoreElementType.ELECTRICAL_OUTLET:
        case CoreElementType.ROOM_BOUNDARY:
        case CoreElementType.APPLIANCE:
        case CoreElementType.GROUP:
        case CoreElementType.OPENING:
        case CoreElementType.WALL_PAINT:
        case CoreElementType.WALL_PAPER:
          return <HelpCircle className="h-5 w-5" />
        default:
          // console.warn(`[BottomAssetDrawer] No direct icon mapping for PinnedAsset type: ${asset.type}`)
          return <HelpCircle className="h-5 w-5" />
      }
    }
  }

  const createAssetButtonForExpandedView = useCallback((
    element: DesignElement | { name: string, icon: React.ReactNode, type: CoreElementType | string },
    isSpecific: boolean, // True for special elements, false for basic elements from config
    interactionType: 'drag' | 'click',
    pinIconSide: 'left' | 'right', // NEW: Parameter for pin icon placement
  ) => {
    // console.log('[BottomAssetDrawer] createAssetButtonForExpandedView: Called for element', { elementName: element.name, elementType: element.type, isSpecific }); // Added detailed log
    // Correctly derive coreType, assuming element.type is a string member of CoreElementType
    const coreType = element.type as CoreElementType
    const defaultSettings = getSettingsForType(coreType)

    let finalDragProps: InitialElementProperties = { ...defaultSettings }
    let dragDataTypeForCanvas = coreType
    let elementSpecificData: Record<string, unknown> = {}
    const designElement = element as DesignElement // Type assertion for convenience

    if (designElement.predefinedElementData) {
      dragDataTypeForCanvas = CoreElementType.IMAGE
      finalDragProps = {
        ...defaultSettings,
        width: designElement.predefinedElementData.defaultWidth ?? defaultSettings.width,
        height: designElement.predefinedElementData.defaultHeight ?? defaultSettings.height,
      }
      elementSpecificData = {
        ...(designElement.predefinedElementData.attributes || {}),
        predefinedId: designElement.predefinedElementData.id,
        actualImagePath: designElement.predefinedElementData.imagePath,
        elementName: designElement.predefinedElementData.name,
        majorCategory: designElement.predefinedElementData.majorCategory,
        minorCategory: designElement.predefinedElementData.minorCategory,
        mountingType: designElement.predefinedElementData.mountingType,
        tags: designElement.predefinedElementData.tags,
        description: designElement.predefinedElementData.description,
      }
    }
    else if (typeof designElement.openingType === 'string' && designElement.openingType !== '') {
      dragDataTypeForCanvas = CoreElementType.OPENING
      elementSpecificData = {
        openingType: designElement.openingType,
        doorType: designElement.doorType,
        windowType: designElement.windowType,
      }
    }
    else if (coreType === CoreElementType.FLOOR_AREA && designElement.subtypes) {
      dragDataTypeForCanvas = CoreElementType.FLOOR_AREA
      elementSpecificData = { roomSubtypes: designElement.subtypes }
    }
    else if (coreType === CoreElementType.WALL_PAINT || coreType === CoreElementType.WALL_PAPER || coreType === CoreElementType.WALL) {
      dragDataTypeForCanvas = coreType // Wall itself is a drawing tool type
    }

    const displayedElementTypeForPinning = designElement.predefinedElementData ? CoreElementType.IMAGE : coreType
    const assetNameForPinning = designElement.predefinedElementData ? designElement.predefinedElementData.name : designElement.name

    // console.log('[BottomAssetDrawer] createAssetButtonForExpandedView - Element info:', {
    //   elementName: designElement.name,
    //   elementType: coreType,
    //   displayedElementTypeForPinning,
    //   assetNameForPinning,
    //   isSpecific,
    //   hasPredefinedElementData: !!designElement.predefinedElementData,
    //   currentModuleId,
    //   currentStepId,
    // })

    // 钉住功能逻辑：基础元素（非特殊元素）都可以被钉住，包括基础的IMAGE元素
    const canBePinned = !isSpecific
    const isCurrentlyPinned = canBePinned
      ? isAssetPinned(
          displayedElementTypeForPinning,
          isSpecific,
          isSpecific ? currentModuleId : undefined,
          isSpecific ? currentStepId : undefined,
          assetNameForPinning,
        )
      : false

    const buttonKey = (typeof designElement.predefinedId === 'string' && designElement.predefinedId !== '')
      ? `${designElement.predefinedId}-${designElement.name}`
      : `${designElement.name}-${coreType}-${(typeof designElement.openingType === 'string' && designElement.openingType !== '' ? designElement.openingType : '')}-${isSpecific}`

    const commonButtonProps = {
      variant: 'outline' as const,
      className: `h-10 w-40 px-2.5 py-1.5 flex items-center gap-2 text-xs shadow-sm hover:shadow-md transition-shadow text-left`,
    }

    const buttonContentAndTrigger = (
      <TooltipTrigger asChild>
        {interactionType === 'drag'
          ? (
              <Button
                {...commonButtonProps}
                draggable
                onDragStart={(e) => {
                  setIsDraggingFromExpandedSheet(true)
                  // console.log('[BottomAssetDrawer] Expanded Asset DragStart:', { name: designElement.name, type: dragDataTypeForCanvas })
                  const dragData = {
                    elementType: dragDataTypeForCanvas,
                    properties: finalDragProps,
                    name: designElement.predefinedElementData ? designElement.predefinedElementData.name : designElement.name,
                    from: 'bottom-drawer-expanded-asset',
                    elementSpecificData,
                  }
                  onAssetDragStart(dragDataTypeForCanvas, finalDragProps, e as React.DragEvent<HTMLButtonElement | HTMLDivElement>)
                  e.dataTransfer.setData('application/json', JSON.stringify(dragData))
                  e.dataTransfer.effectAllowed = 'copy'
                  // console.log('[BottomAssetDrawer] Expanded Asset DragStart: Drag data set', { dragData })
                }}
                onDragEnd={() => {
                  setIsDraggingFromExpandedSheet(false)
                }}
              >
                <div className="flex-shrink-0">{designElement.icon}</div>
                <span className="text-xs break-words whitespace-normal">{designElement.name}</span>
              </Button>
            )
          : ( // interactionType === 'click'
              <Button
                {...commonButtonProps}
                onClick={() => {
                  // console.log('[BottomAssetDrawer] Expanded Asset Click (drawing tool):', { name: designElement.name, type: dragDataTypeForCanvas })
                  onAssetSelectFromExpanded(dragDataTypeForCanvas)
                }}
              >
                <div className="flex-shrink-0">{designElement.icon}</div>
                <span className="text-xs break-words whitespace-normal">{designElement.name}</span>
              </Button>
            )}
      </TooltipTrigger>
    )

    const pinIconElement = (
      <div
        className={`flex-shrink-0 ${
          pinIconSide === 'left' ? 'mr-1' : 'ml-1'
        }`}
      >
        <TooltipProvider delayDuration={200}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 p-1"
                onClick={() => {
                  // console.log('[BottomAssetDrawer] Toggle Pin Clicked:', {
                  //   assetNameForPinning,
                  //   displayedElementTypeForPinning,
                  //   isSpecific,
                  //   isCurrentlyPinned,
                  //   currentModuleId,
                  //   currentStepId,
                  //   elementName: designElement.name,
                  // })
                  onTogglePinAsset(
                    displayedElementTypeForPinning,
                    isSpecific,
                    isSpecific ? currentModuleId : undefined,
                    isSpecific ? currentStepId : undefined,
                    assetNameForPinning,
                  )
                }}
              >
                {isCurrentlyPinned
                  ? <Pin className="h-4 w-4 text-primary" />
                  : <PinOff className="h-4 w-4 text-muted-foreground" />}
                <span className="sr-only">
                  {isCurrentlyPinned ? 'Unpin asset' : 'Pin asset'}
                </span>
              </Button>
            </TooltipTrigger>
            <TooltipContent side={pinIconSide === 'left' ? 'left' : 'right'}>
              <p>{isCurrentlyPinned ? 'Unpin from Quick Access' : 'Pin to Quick Access'}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    )

    return (
      <div key={buttonKey} className="flex items-center pr-1">
        {/* Pin icon on the left if specified and element can be pinned */}
        {pinIconSide === 'left' && canBePinned && pinIconElement}

        {/* Main button content, with its own Tooltip setup */}
        <TooltipProvider delayDuration={300}>
          <Tooltip>
            {buttonContentAndTrigger}
            {' '}
            <TooltipContent>
              <p>
                {interactionType === 'drag' ? 'Drag to add to canvas: ' : 'Click to select for drawing:'}
                {' '}
                {(typeof element.name === 'string' && element.name !== '') ? element.name : element.type}
              </p>
              {'description' in element && typeof element.description === 'string' && element.description !== '' && (
                <p className="text-xs text-muted-foreground pt-1">{element.description}</p>
              )}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* Pin icon on the right if specified and element can be pinned */}
        {pinIconSide === 'right' && canBePinned && pinIconElement}
      </div>
    )
  }, [getSettingsForType, isAssetPinned, onAssetSelectFromExpanded, onAssetDragStart, onTogglePinAsset, currentModuleId, currentStepId, setIsDraggingFromExpandedSheet])

  // Filter special elements for draggable vs. drawing tools
  const draggableSpecialElements = useMemo(() =>
    specialElements.filter(el =>
      el.type !== CoreElementType.WALL
      && el.type !== CoreElementType.WALL_PAINT
      && el.type !== CoreElementType.WALL_PAPER,
      // Add other non-draggable special types if any
    ), [specialElements])

  const drawingSpecialTools = useMemo(() =>
    specialElements.filter(el =>
      el.type === CoreElementType.WALL
      || el.type === CoreElementType.WALL_PAINT
      || el.type === CoreElementType.WALL_PAPER,
      // Add other drawing-specific special types if any
    ), [specialElements])

  // Determine if left and right wings have content for central divider logic
  const leftWingHasContent = useMemo(() =>
    (hasSpecialElements && draggableSpecialElements.length > 0) || basicShapesForExpanded.length > 0, [hasSpecialElements, draggableSpecialElements, basicShapesForExpanded])

  const rightWingHasContent = useMemo(() =>
    basicPathsForExpanded.length > 0 || (hasSpecialElements && drawingSpecialTools.length > 0), [hasSpecialElements, drawingSpecialTools, basicPathsForExpanded])

  // The main <Sheet> component should always be rendered to provide context for SheetTrigger.
  // The visibility of the collapsed bar vs. expanded content is handled inside.
  return (
    <Sheet open={sheetOpen} onOpenChange={onSheetOpenChange} modal={false}>
      {/* Collapsed View elements - SheetTrigger is a direct child or descendant of Sheet but outside SheetPortal */}
      {!sheetOpen && (
        <div className="fixed bottom-0 left-0 right-0 h-14 bg-background border-t z-40 flex items-center px-4 shadow-sm" data-tutorial="bottom-drawer-content">
          <div className="flex flex-grow items-center justify-center gap-x-3">
            {/* Group 1: Draggable items - Rendered from Right to Left (Center to Left) */}
            <div className="flex items-center gap-1.5">
              {/* Text tools section */}
              <div className="flex items-center gap-1.5" data-tutorial="text-tool">
                {[...draggableItemsCollapsed].reverse().filter(asset => asset.type === CoreElementType.TEXT).map(asset => (
                  <TooltipProvider key={`${asset.assetName ?? asset.type}-${asset.type}-${asset.isSpecific}-text`} delayDuration={300}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        {!asset.isSpecific
                          ? ( // Basic draggable assets: ICON ONLY
                              <Button
                                variant="ghost"
                                size="icon"
                                draggable
                                onDragStart={e => handleQuickAssetDragStart(asset, e)}
                                className="cursor-grab h-9 w-9 p-1.5"
                              >
                                {getIconForPinnedAsset(asset)}
                                <span className="sr-only">{(typeof asset.assetName === 'string' && asset.assetName !== '') ? asset.assetName : asset.type}</span>
                              </Button>
                            )
                          : ( // Special elements: TEXT ONLY
                              <Button
                                variant="ghost"
                                draggable
                                onDragStart={e => handleQuickAssetDragStart(asset, e)}
                                className="px-2.5 py-1.5 text-xs cursor-grab h-9 min-w-[60px] text-center"
                              >
                                {/* No icon, just text for special elements */}
                                <span>{(typeof asset.assetName === 'string' && asset.assetName !== '') ? asset.assetName : asset.type}</span>
                              </Button>
                            )}
                      </TooltipTrigger>
                      <TooltipContent side="top">
                        <p>
                          Drag to add to canvas:
                          {' '}
                          {(typeof asset.assetName === 'string' && asset.assetName !== '') ? asset.assetName : asset.type}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ))}
              </div>

              {/* Basic shapes section - All basic shapes and polygons (excluding images) */}
              <div className="flex items-center gap-1.5" data-tutorial="basic-shapes">
                {[...draggableItemsCollapsed].reverse().filter(asset =>
                  asset.type === CoreElementType.CIRCLE
                  || asset.type === CoreElementType.RECTANGLE
                  || asset.type === CoreElementType.SQUARE
                  || asset.type === CoreElementType.ELLIPSE
                  || asset.type === CoreElementType.TRIANGLE
                  || asset.type === CoreElementType.PENTAGON
                  || asset.type === CoreElementType.HEXAGON,
                ).map((asset) => {
                  // Determine specific data-tutorial attribute based on asset type
                  let specificDataTutorial = ''
                  if (asset.type === CoreElementType.CIRCLE) {
                    specificDataTutorial = 'circle-shape'
                  }
                  else if (asset.type === CoreElementType.RECTANGLE || asset.type === CoreElementType.SQUARE) {
                    specificDataTutorial = 'rectangle-shape'
                  }
                  else if (asset.type === CoreElementType.ELLIPSE) {
                    specificDataTutorial = 'ellipse-shape'
                  }
                  else if (asset.type === CoreElementType.TRIANGLE) {
                    specificDataTutorial = 'triangle-shape'
                  }
                  else if (asset.type === CoreElementType.PENTAGON) {
                    specificDataTutorial = 'pentagon-shape'
                  }
                  else if (asset.type === CoreElementType.HEXAGON) {
                    specificDataTutorial = 'hexagon-shape'
                  }
                  else if (asset.type === CoreElementType.IMAGE) {
                    specificDataTutorial = 'image-element'
                  }

                  return (
                    <TooltipProvider key={`${asset.assetName ?? asset.type}-${asset.type}-${asset.isSpecific}-draggable`} delayDuration={300}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          {!asset.isSpecific
                            ? ( // Basic elements: ICON ONLY
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  draggable
                                  onDragStart={e => handleQuickAssetDragStart(asset, e)}
                                  className="cursor-grab h-9 w-9 p-1.5"
                                  data-tutorial={specificDataTutorial}
                                >
                                  {getIconForPinnedAsset(asset)}
                                  <span className="sr-only">{(typeof asset.assetName === 'string' && asset.assetName !== '') ? asset.assetName : asset.type}</span>
                                </Button>
                              )
                            : ( // Special elements: TEXT ONLY
                                <Button
                                  variant="ghost"
                                  draggable
                                  onDragStart={e => handleQuickAssetDragStart(asset, e)}
                                  className="px-2.5 py-1.5 text-xs cursor-grab h-9 min-w-[60px] text-center"
                                  data-tutorial={specificDataTutorial}
                                >
                                  {/* No icon, just text for special elements */}
                                  <span>{(typeof asset.assetName === 'string' && asset.assetName !== '') ? asset.assetName : asset.type}</span>
                                </Button>
                              )}
                        </TooltipTrigger>
                        <TooltipContent side="top">
                          <p>
                            Drag to add to canvas:
                            {' '}
                            {(typeof asset.assetName === 'string' && asset.assetName !== '') ? asset.assetName : asset.type}
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )
                })}
              </div>

              {/* Image elements section - Only images */}
              <div className="flex items-center gap-1.5" data-tutorial="image-elements">
                {[...draggableItemsCollapsed].reverse().filter(asset =>
                  asset.type === CoreElementType.IMAGE,
                ).map(asset => (
                  <TooltipProvider key={`${asset.assetName ?? asset.type}-${asset.type}-${asset.isSpecific}-image`} delayDuration={300}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        {!asset.isSpecific
                          ? ( // Basic draggable assets: ICON ONLY
                              <Button
                                variant="ghost"
                                size="icon"
                                draggable
                                onDragStart={e => handleQuickAssetDragStart(asset, e)}
                                className="cursor-grab h-9 w-9 p-1.5"
                                data-tutorial="image-element"
                              >
                                {getIconForPinnedAsset(asset)}
                                <span className="sr-only">{(typeof asset.assetName === 'string' && asset.assetName !== '') ? asset.assetName : asset.type}</span>
                              </Button>
                            )
                          : ( // Special draggable assets: ICON + TEXT
                              <Button
                                variant="ghost"
                                size="sm"
                                draggable
                                onDragStart={e => handleQuickAssetDragStart(asset, e)}
                                className="cursor-grab h-9 px-2 py-1.5 flex items-center gap-1.5"
                                data-tutorial="image-element"
                              >
                                <div className="flex-shrink-0">{getIconForPinnedAsset(asset)}</div>
                                <span className="text-xs break-words whitespace-normal">{(typeof asset.assetName === 'string' && asset.assetName !== '') ? asset.assetName : asset.type}</span>
                              </Button>
                            )}
                      </TooltipTrigger>
                      <TooltipContent side="top">
                        <p>
                          Drag to canvas:
                          {(typeof asset.assetName === 'string' && asset.assetName !== '') ? asset.assetName : asset.type}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ))}
              </div>
            </div>

            {/* Divider for Collapsed View */}
            <div className="h-6 w-px bg-border mx-1 self-center"></div>

            {/* Group 2: Drawing tool items - Rendered from Left to Right (Center to Right) */}
            <div className="flex items-center gap-1.5" data-tutorial="path-tools">
              {drawingToolItemsCollapsed.map(asset => (
                <TooltipProvider key={`${asset.assetName ?? asset.type}-${asset.type}-${asset.isSpecific}-clickable`} delayDuration={300}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      {!asset.isSpecific
                        ? ( // Basic drawing tools: ICON ONLY
                            <Button
                              variant={selectedDrawingTool === asset.type ? 'default' : 'ghost'}
                              size="icon"
                              onClick={() => handleQuickAssetClick(asset)}
                              className={`cursor-pointer h-9 w-9 p-1.5 ${selectedDrawingTool === asset.type ? 'bg-primary text-primary-foreground' : ''}`}
                            >
                              {getIconForPinnedAsset(asset)}
                              <span className="sr-only">{(typeof asset.assetName === 'string' && asset.assetName !== '') ? asset.assetName : asset.type}</span>
                            </Button>
                          )
                        : ( // Special drawing tools: TEXT ONLY
                            <Button
                              variant={selectedDrawingTool === asset.type ? 'default' : 'ghost'}
                              onClick={() => handleQuickAssetClick(asset)}
                              className={`px-2.5 py-1.5 text-xs cursor-pointer h-9 min-w-[60px] text-center ${selectedDrawingTool === asset.type ? 'bg-primary text-primary-foreground' : ''}`}
                            >
                              {/* No icon, just text for special elements */}
                              <span>{(typeof asset.assetName === 'string' && asset.assetName !== '') ? asset.assetName : asset.type}</span>
                            </Button>
                          )}
                    </TooltipTrigger>
                    <TooltipContent side="top">
                      <p>
                        Click to select for drawing:
                        {' '}
                        {(typeof asset.assetName === 'string' && asset.assetName !== '') ? asset.assetName : asset.type}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ))}
            </div>
          </div>

          {/* Expand Button - SheetTrigger is now correctly within Sheet context */}
          <div className="ml-auto flex-shrink-0" data-tutorial="drawer-expand">
            <SheetTrigger asChild>
              <Button aria-label="Expand asset drawer" variant="ghost" size="icon" className="h-9 w-9 p-1.5">
                <ChevronUp className="h-5 w-5" />
              </Button>
            </SheetTrigger>
          </div>
        </div>
      )}

      {/* Expanded View (Sheet Portal, Overlay, and Content) - Conditionally rendered */}
      {sheetOpen && (
        <SheetPortal>
          <SheetOverlay />
          <SheetContent
            side="bottom"
            className={cn(
              'p-0 flex flex-col',
              'transition-all duration-200 ease-in-out',
              isDraggingFromExpandedSheet ? 'h-16' : 'h-[calc(var(--vh,1vh)*60)] md:h-96',
            )}
            // onClick={(e) => e.stopPropagation()} // Prevent closing on content click if needed
          >
            <SheetHeader className="p-3 pb-2 border-b flex-shrink-0">
              <div className="flex justify-between items-center">
                <SheetTitle className="text-base font-semibold">Asset Library</SheetTitle>
                {/* Optional: Add a SheetClose button here if you want an X inside the expanded drawer header */}
              </div>
            </SheetHeader>
            <ScrollArea className="flex-1 min-h-0">
              {/* Outermost centering container */}
              <div className="flex justify-center p-4 pt-2">
                {/* Main Row for wings and divider. Takes full width up to a max. Removed justify-center from here, relying on parent for centering the block. */}
                <div className="flex flex-row items-start w-full max-w-6xl">

                  {/* Column 1 (NEW ORDER): Special Elements (Draggable) - OUTER LEFT - This column is flex-1. Items inside right-aligned. */}
                  {hasSpecialElements && draggableSpecialElements.length > 0 && (
                    <div className="flex flex-col gap-y-4 flex-1 min-w-[160px] md:min-w-[180px]" data-tutorial="special-elements">
                      <div key="special-draggable-elements-category">
                        <h3 className="text-sm font-medium mb-2 text-muted-foreground sticky top-0 bg-background pt-1 z-[1] text-right">
                          {formatModuleName(currentModuleId)}
                          {' '}
                          -
                          {dynamicTabLabel}
                        </h3>
                        <div className="flex flex-col gap-2 items-end">
                          {draggableSpecialElements.map(element =>
                            createAssetButtonForExpandedView(
                              element,
                              true, // isSpecific
                              'drag', // interactionType
                              'left', // pinIconSide for outer left column
                            ),
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                  {/* Conditional placeholder for Column 1 if no special draggable elements */}
                  {(!hasSpecialElements || draggableSpecialElements.length === 0) && basicShapesForExpanded.length > 0 && (
                    <div className="flex-1 min-w-[160px] md:min-w-[180px]">{/* Placeholder for Col 1 ensures col 2 can align against something */}</div>
                  )}

                  {/* Divider between Column 1 and Column 2 */}
                  {(hasSpecialElements && draggableSpecialElements.length > 0) && basicShapesForExpanded.length > 0 && (
                    <div className="flex-shrink-0 flex justify-center items-stretch px-2 md:px-3 self-stretch">
                      <div className="w-px bg-border"></div>
                    </div>
                  )}

                  {/* Column 2 (NEW ORDER): Basic Shapes (Draggable) - INNER LEFT - This column is flex-1. Items inside right-aligned. */}
                  <div className="flex flex-col gap-y-4 flex-1 min-w-[160px] md:min-w-[180px]">
                    {basicShapesForExpanded.map((config: AssetGroup) => (
                      <div key={`shape-cat-${config.name}`}>
                        <h3 className="text-sm font-medium mb-2 text-muted-foreground sticky top-0 bg-background pt-1 z-[1] text-right">
                          {config.name}
                        </h3>
                        <div className="flex flex-col gap-2 items-end">
                          {config.assets.map((item: AssetInGroup) => createAssetButtonForExpandedView(
                            { name: item.name, icon: item.icon, type: item.type },
                            false, // isSpecific should be item.isSpecific, but createAssetButtonForExpandedView expects hardcoded false here for basic shapes
                            'drag', // interactionType
                            'left', // pinIconSide for inner left column
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Divider between Column 2 and Column 3 (Central Divider) */}
                  {leftWingHasContent && rightWingHasContent && (
                    <div className="flex-shrink-0 flex justify-center items-stretch px-2 md:px-3 self-stretch">
                      {' '}
                      {/* Adjusted to match other dividers */}
                      <div className="w-px bg-border"></div>
                    </div>
                  )}

                  {/* Column 3: Basic Paths (Click-to-Prime) - INNER RIGHT - This column is flex-1. Items inside left-aligned (default). */}
                  {basicPathsForExpanded.length > 0 && (
                    <div className="flex flex-col gap-y-4 flex-1 min-w-[160px] md:min-w-[180px]">
                      {basicPathsForExpanded.map((config: AssetGroup) => (
                        <div key={`path-cat-${config.name}`}>
                          <h3 className="text-sm font-medium mb-2 text-muted-foreground sticky top-0 bg-background pt-1 z-[1]">{config.name}</h3>
                          <div className="flex flex-col gap-2">
                            {config.assets.map((item: AssetInGroup) => createAssetButtonForExpandedView(
                              { name: item.name, icon: item.icon, type: item.type },
                              false, // isSpecific should be item.isSpecific, but createAssetButtonForExpandedView expects hardcoded false here for basic paths
                              'click', // interactionType
                              'right', // pinIconSide
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  {/* Conditional rendering for Column 3 placeholder if no basic paths */}
                  {basicPathsForExpanded.length === 0 && (basicShapesForExpanded.length > 0 || draggableSpecialElements.length > 0) && (
                    <div className="flex-1 min-w-[160px] md:min-w-[180px]">{/* Placeholder ensures col 3 can align against something */}</div>
                  )}

                  {/* Divider between Column 3 and Column 4 */}
                  {basicPathsForExpanded.length > 0 && (hasSpecialElements && drawingSpecialTools.length > 0) && (
                    <div className="flex-shrink-0 flex justify-center items-stretch px-2 md:px-3 self-stretch">
                      <div className="w-px bg-border"></div>
                    </div>
                  )}

                  {/* Column 4: Special Drawing Tools (Click-to-Prime) - OUTER RIGHT - This column is flex-1. Items inside left-aligned (default). */}
                  {hasSpecialElements && drawingSpecialTools.length > 0 && (
                    <div className="flex flex-col gap-y-4 flex-1 min-w-[160px] md:min-w-[180px]">
                      <div key="special-drawing-tools-category">
                        <h3 className="text-sm font-medium mb-2 text-muted-foreground sticky top-0 bg-background pt-1 z-[1]">
                          {formatModuleName(currentModuleId)}
                          {' '}
                          -
                          {dynamicTabLabel}
                        </h3>
                        <div className="flex flex-col gap-2">
                          {drawingSpecialTools.map(element =>
                            createAssetButtonForExpandedView(
                              element,
                              true, // isSpecific
                              (element.type === CoreElementType.WALL || element.type === CoreElementType.WALL_PAINT || element.type === CoreElementType.WALL_PAPER) ? 'click' : 'drag',
                              'right', // pinIconSide
                            ),
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                  {/* Conditional rendering for Column 4 placeholder if no special drawing tools */}
                  {(!hasSpecialElements || drawingSpecialTools.length === 0) && (basicPathsForExpanded.length > 0) && (
                    <div className="flex-1 min-w-[160px] md:min-w-[180px]">{/* Placeholder */}</div>
                  )}

                </div>
              </div>
            </ScrollArea>
          </SheetContent>
        </SheetPortal>
      )}
    </Sheet>
  )
}

export default BottomAssetDrawer
