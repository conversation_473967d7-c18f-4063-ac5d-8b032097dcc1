/**
 * Index file for the validator module.
 *
 * @remarks
 * This file re-exports all validator-related components, including the main
 * ElementValidator, individual shape/path/design validators, and relevant type definitions.
 *
 * @module core/validator
 *
 * @see {@link ElementValidator} For the main validator class.
 * @see {@link IElementValidator} For the validator interface.
 * @see {@link module:core/validator/validators} For individual validator implementations.
 */

import type { ValidationError, ValidationResult, ValidatableShape as ValidatorShape } from '@/types/core/validator/validator-interface'
import { ValidationErrorCode } from '@/types/core/validator/error-codes'
import { ElementValidator } from './ElementValidator'

export * from './ElementValidator'
// Export common validator and validator interfaces
export * from './IElementValidator'

// Re-export types
export type { ValidationError, ValidationResult, ValidatorShape }
export { ValidationErrorCode }

// Export validator-specific model aliases
export type { ValidatorShape as Validatable<PERSON>hape }

// Export singleton instance
export const validator = new ElementValidator()

// Export specific validator implementations
export * from './validators'

// Export common validation utility functions
export { isPolygonClosed, isValidColor, isValidPoint } from './validators/common/validationUtils'
// Export validator base classes and interfaces
export { BaseShapeValidator } from './validators/common/validatorBase'

export type { IBaseShapeValidator } from './validators/common/validatorBase'

// Export common business rules and the function to apply them
export {
  applyBusinessRules,
  idRule,
  minSizeRule,
  pointCountRule,
  polygonClosureRule,
  sizeRule,
} from './validators/rules/businessRules'
// Export validation rule interface and factory function
export type { IValidationRule } from './validators/rules/ruleInterface'

export { createValidationRule } from './validators/rules/ruleInterface'
