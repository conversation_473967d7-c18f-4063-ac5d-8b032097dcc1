import { describe, expect, it } from 'vitest'

import { ValidationErrorCode } from '@/types/core/validator/error-codes'

describe('validation Error Codes', () => {
  it('should define general validation error codes', () => {
    // General validation errors
    expect(ValidationErrorCode.MISSING_OR_INVALID_ID).toBe('MISSING_OR_INVALID_ID')
    expect(ValidationErrorCode.INVALID_STROKE_COLOR).toBe('INVALID_STROKE_COLOR')
    expect(ValidationErrorCode.INVALID_FILL_COLOR).toBe('INVALID_FILL_COLOR')
    expect(ValidationErrorCode.VALIDATION_SPECIFIC_ERROR).toBe('VALIDATION_SPECIFIC_ERROR')
    expect(ValidationErrorCode.VALIDATION_RULE_ERROR).toBe('VALIDATION_RULE_ERROR')
  })

  it('should define shape type error codes', () => {
    // Shape type errors
    expect(ValidationErrorCode.INVALID_SHAPE_TYPE).toBe('INVALID_SHAPE_TYPE')
  })

  it('should define dimension and property error codes', () => {
    // Dimension and property errors
    expect(ValidationErrorCode.INVALID_DIMENSION).toBe('INVALID_DIMENSION')
    expect(ValidationErrorCode.INVALID_RADIUS).toBe('INVALID_RADIUS')
    expect(ValidationErrorCode.INVALID_POSITION).toBe('INVALID_POSITION')
    expect(ValidationErrorCode.INVALID_PROPERTY_VALUE).toBe('INVALID_PROPERTY_VALUE')
    expect(ValidationErrorCode.INVALID_SIDES).toBe('INVALID_SIDES')
  })

  it('should define polygon-specific error codes', () => {
    // Polygon specific errors
    expect(ValidationErrorCode.INSUFFICIENT_POINTS).toBe('INSUFFICIENT_POINTS')
    expect(ValidationErrorCode.INVALID_POINT).toBe('INVALID_POINT')
    expect(ValidationErrorCode.POLYGON_NOT_CLOSED).toBe('POLYGON_NOT_CLOSED')
    expect(ValidationErrorCode.DUPLICATE_POINTS).toBe('DUPLICATE_POINTS')
    expect(ValidationErrorCode.MISSING_POLYGON_DEFINITION).toBe('MISSING_POLYGON_DEFINITION')
  })

  it('should define design element error codes', () => {
    // Design element errors
    expect(ValidationErrorCode.INVALID_CATEGORY).toBe('INVALID_CATEGORY')
    expect(ValidationErrorCode.MISSING_PROPERTY).toBe('MISSING_PROPERTY')
  })

  it('should allow use in switch statements', () => {
    // Test using the enum in a switch statement
    const getErrorMessage = (code: ValidationErrorCode): string => {
      switch (code) {
        case ValidationErrorCode.MISSING_OR_INVALID_ID:
          return 'ID is missing or invalid'
        case ValidationErrorCode.INVALID_DIMENSION:
          return 'Dimension is invalid'
        case ValidationErrorCode.INSUFFICIENT_POINTS:
          return 'Polygon has insufficient points'
        default:
          return 'Unknown error'
      }
    }

    expect(getErrorMessage(ValidationErrorCode.MISSING_OR_INVALID_ID)).toBe('ID is missing or invalid')
    expect(getErrorMessage(ValidationErrorCode.INVALID_DIMENSION)).toBe('Dimension is invalid')
    expect(getErrorMessage(ValidationErrorCode.INSUFFICIENT_POINTS)).toBe('Polygon has insufficient points')
    expect(getErrorMessage(ValidationErrorCode.INVALID_CATEGORY)).toBe('Unknown error')
  })

  it('should allow use as object keys', () => {
    // Test using the enum values as object keys
    const errorMessages: Record<ValidationErrorCode, string> = {
      [ValidationErrorCode.MISSING_OR_INVALID_ID]: 'ID is missing or invalid',
      [ValidationErrorCode.INVALID_STROKE_COLOR]: 'Stroke color is invalid',
      [ValidationErrorCode.INVALID_FILL_COLOR]: 'Fill color is invalid',
      [ValidationErrorCode.VALIDATION_SPECIFIC_ERROR]: 'Validation specific error',
      [ValidationErrorCode.VALIDATION_RULE_ERROR]: 'Validation rule error',
      [ValidationErrorCode.INVALID_SHAPE_TYPE]: 'Shape type is invalid',
      [ValidationErrorCode.INVALID_DIMENSION]: 'Dimension is invalid',
      [ValidationErrorCode.INVALID_RADIUS]: 'Radius is invalid',
      [ValidationErrorCode.INVALID_POSITION]: 'Position is invalid',
      [ValidationErrorCode.INVALID_PROPERTY_VALUE]: 'Property value is invalid',
      [ValidationErrorCode.INVALID_SIDES]: 'Number of sides is invalid',
      [ValidationErrorCode.INSUFFICIENT_POINTS]: 'Polygon has insufficient points',
      [ValidationErrorCode.INVALID_POINT]: 'Point coordinates are invalid',
      [ValidationErrorCode.POLYGON_NOT_CLOSED]: 'Polygon is not closed',
      [ValidationErrorCode.DUPLICATE_POINTS]: 'Polygon contains duplicate points',
      [ValidationErrorCode.MISSING_POLYGON_DEFINITION]: 'Polygon definition is missing',
      [ValidationErrorCode.INVALID_CATEGORY]: 'Category is invalid',
      [ValidationErrorCode.MISSING_PROPERTY]: 'Required property is missing',
    }

    expect(errorMessages[ValidationErrorCode.MISSING_OR_INVALID_ID]).toBe('ID is missing or invalid')
    expect(errorMessages[ValidationErrorCode.INVALID_DIMENSION]).toBe('Dimension is invalid')
    expect(errorMessages[ValidationErrorCode.INSUFFICIENT_POINTS]).toBe('Polygon has insufficient points')
  })
})
