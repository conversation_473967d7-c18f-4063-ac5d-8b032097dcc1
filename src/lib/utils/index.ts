/**
 * Utility Functions and Classes Index
 *
 * @remarks
 * This module serves as the central entry point for various utility functions and
 * helper classes used throughout the application. It consolidates and re-exports
 * utilities from individual modules within the `lib/utils` directory, covering areas such as:
 * - Geometry calculations and geometric object classes (e.g., {@link PointClass}, {@link GeometryUtils}).
 * - Element type checking and model property accessors (e.g., {@link getElementCategory}, {@link getRectangleProps}).
 * - Coordinate transformations (e.g., {@link clientToSvgPoint}).
 * - Mathematical helpers (e.g., {@link clamp}).
 * - Style generation and manipulation (e.g., {@link cn}, {@link createFloatingShadow}).
 * - Canvas, error handling, event, and validation utilities.
 *
 * While this module primarily aims to export utility functions, it currently also
 * re-exports some core type definitions and classes from other parts of the application
 * (e.g., from `../../types/core` and `../../core/compute`). Ideally, such core
 * definitions should be imported directly from their source or a dedicated types/core index.
 * Future refactoring may address this to strictly adhere to "utils only export utility functions/classes".
 *
 * @module lib/utils/index
 */

import { BoundingBoxClass } from './geometry/BoundingBoxClass'
// Geometry classes defined within utils
import { PointClass } from './geometry/PointClass'
// TODO: Create VectorClass and RectangleClass if they are needed as classes with methods

// -----------------------------------------------------------------------------
// Geometry Utility Functions and Classes
// -----------------------------------------------------------------------------

// Export geometry utility classes
export { PointClass }
export { BoundingBoxClass }
// Aliases for convenience
export { PointClass as Point }
export { BoundingBoxClass as GeometryBoundingBox }

// REMOVED: Re-export of core types (CoreIPoint, CoreIVector, CoreIRectangle, CoreIBoundingBox and their aliases)
// These should be imported directly from '@/types/core/...'

// Export other utility categories from their respective sub-directory's index.ts file
// export * from './business'; // Removed as directory is empty
export * from './canvas'

// Export element specific utilities from ./element (including points and ensureCompleteMetadata)
export { ensureCompleteMetadata, points } from './element' // Add other element utils as needed, or use export *

// Comments regarding previously removed path/shape implementations are now updated above.

// Export element model utilities
export {
  getCircleProps,
  getDoorProps,
  getEllipseProps,
  getLineProps,
  getPolygonProps,
  getRectangleProps,
  getRoomProps,
  getStyleProps,
  getWallProps,
  getWindowProps,
  isCircle,
  isDoor,
  isEllipse,
  isLine,
  isPolygon,
  isRectangle,
  isRoom,
  isWall,
  isWindow,
} from './element/elementModelUtils'

// Geometry calculation functions are now exported in the consolidated block above.

// Polyline functions are temporarily removed

// No longer exported from point.ts, now from coordinates.ts
// Distance calculation functions are now exported from ./core/common
// (calculatePointToPointDistance is from ./core/common)
// (calculatePointToLineDistance and its alias pointToLineDistance are from ./geometry/coordinates)

// -----------------------------------------------------------------------------
// Coordinate Transformation Functions
// -----------------------------------------------------------------------------

// Export element type utilities
export {
  getElementCategory,
  isElementType,
  // isSpecialType, // Removed as it's not exported from elementTypeUtils
  isInteriorDesignType,
  isPathType,
} from './element/elementTypeUtils'

// export * from './cost'; // Removed as directory is empty
export * from './errorUtils'

// Export geometry calculation namespace
export {
  calculateCircleArea,
  calculateCircleBoundingBox,
  calculateCirclePerimeter,
  calculateEllipseArea,
  calculateEllipseBoundingBox,
  calculateEllipsePerimeter,
  calculateLineLength,
  calculatePolygonArea,
  calculatePointsBoundingBox as calculatePolygonBoundingBox,
  calculatePolygonPerimeter,
  // ensurePointInstance, // Will be exported separately from pointUtils
  calculateRectangleBoundingBox,
  GeometryUtils,
} from './geometry'
// Export Point/Coordinate related utils from ./geometry (which exports from ./geometry/pointUtils)
export {
  arePointsEqual,
  calculateAngle,
  calculateDistance,
  calculateMidpoint,
  calculatePointToCircleDistance,
  calculatePointToEllipseDistance,
  calculatePointToPointDistance,
  rotatePoint,
  scalePoint,
} from './geometry'

// Export remaining common utilities from ./core/common (should be empty now or only truly generic, non-domain specific utils)
// export {
// } from './core/common'; // commonUtils should be empty of these domain/geometry specific utils now

// Export coordinate transformation functions (defined in ./geometry/coordinates.ts)
export {
  calculatePointToLineDistance, // Exporting from coordinates.ts
  clientToSvgPoint,
  mouseEventToSvgPoint,
  pointToLineDistance, // Alias exported from coordinates.ts
  rotatePointDegrees,
  svgToClientPoint,
  translatePoint,
} from './geometry/coordinates'

export { ensurePointInstance } from './geometry/pointUtils' // Export directly from pointUtils

// Export Math utilities from ./math
export {
  areNumbersEqual, // Also in mathUtils
  clamp,
  getRandomFloat,
  getRandomInt,
  lerp,
  map,
  normalize,
  percentage,
  roundToDecimals,
  toDegrees,
  toRadians,
} from './math'
// Export Style utilities from ./style (assuming style/index.ts exports them from styleUtils.ts)
export {
  adjustColorBrightness,
  cn,
  createFloatingShadow,
  createGlassEffect,
  createGradientBackground,
  createTransparentColor,
  darkenColor,
  createFloatingShadow as floatingShadow,
  glassEffect,
  lightenColor,
  processColor,
  toHex,
  createTransparentColor as transparentize,
} from './style'
// export * from './events'; // Removed, module does not exist
// REMOVED: export * from '../../core/compute/strategies/material';
// This exports a module, not utility functions. Import directly if needed.

// export * from './models'; // Removed as its contents were moved to core/common or are no longer needed.
// export * from './space'; // Removed as directory is empty
// export * from './svg'; // Removed as directory is empty
export * from './validationUtils'
// 'system' and 'ui' directories were mentioned in the old comment but do not exist in the provided structure.

// -----------------------------------------------------------------------------
// Compute Utility Functions - REMOVED
// -----------------------------------------------------------------------------
// REMOVED: export { ComputeFacade, StrategyRegistry, ComputeOperation, ComputeOperationType } from '../../core/compute';
// REMOVED: export type { AreaCalculatorStrategy, PerimeterCalculatorStrategy, DistanceCalculatorStrategy, CostCalculatorStrategy, MaterialCalculatorStrategy, SpacePlanningStrategy, ComputeOptions, ComputeResult, TransformOptions, BoundingBox as CoreBoundingBoxType } from '../../core/compute';
// These are core application logic/types, not general utilities. Import directly from '@/core/compute' or '@/types/core/compute'.
