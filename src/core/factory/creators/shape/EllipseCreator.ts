import type {
  C<PERSON><PERSON><PERSON>cleParams,
  CreateEllipseParams,
  // BaseElementCreationParams // Commented out as unused
} from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point'
import type {
  Shape,
} from '@/types/core/elementDefinitions'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
/**
 * Creator for Ellipse and Circle Shape Elements
 *
 * @remarks
 * This class implements the {@link ShapeCreator} interface to specialize in creating
 * Ellipse ({@link CoreElementType.ELLIPSE}) and Circle ({@link CoreElementType.CIRCLE})
 * shape elements. Circles are handled as a special case of ellipses where `radiusX`
 * and `radiusY` are equal (represented by a single `radius` property for circles).
 *
 * The `create` method takes {@link CreateEllipseParams} or {@link CreateCircleParams}
 * and constructs a {@link ShapeElement} that conforms to the {@link Shape.Ellipse} or
 * {@link Shape.Circle} interface respectively. It ensures all necessary properties,
 * including those inherited from `ShapeElement` and `BaseStyleProperties`, are correctly
 * initialized by leveraging `createCommonProperties` from the base `ShapeCreator`.
 *
 * The `createDefault` method provides a simple way to create a default ellipse.
 *
 * @module core/factory/creators/shape/EllipseCreator
 * @see {@link ShapeCreator}
 * @see {@link CoreElementType.ELLIPSE}
 * @see {@link CoreElementType.CIRCLE}
 * @see {@link Shape.Ellipse}
 * @see {@link Shape.Circle}
 * @see {@link CreateEllipseParams}
 * @see {@link CreateCircleParams}
 */
import { CoreError, ErrorType } from '@/services/system/error-service'
import { // Retain for context, though Shape.Ellipse/Circle are used
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'
import { ShapeCreator } from './ShapeCreator'
// ensureCompleteMetadata is handled by createCommonProperties
// ensurePointInstance is not strictly needed if position is already PointData

/**
 * Creator class for instantiating Ellipse and Circle shape elements.
 * It implements the {@link ShapeCreator} interface.
 *
 * @implements {ShapeCreator}
 */
export class EllipseCreator extends ShapeCreator<Shape.Ellipse | Shape.Circle, CreateEllipseParams | CreateCircleParams> {
  constructor() {
    super(CoreElementType.ELLIPSE) // Or handle dynamically if it can create CIRCLE too
  }

  /**
   * Creates an Ellipse or Circle {@link ShapeElement} based on the provided parameters.
   *
   * @param params - A {@link CreateEllipseParams} or {@link CreateCircleParams} object
   *                 containing all necessary information for creation.
   * @returns A Promise resolving to the created {@link Shape.Ellipse} or {@link Shape.Circle}.
   * @throws {@link CoreError} if `params.type` is not 'ellipse' or 'circle', or if essential parameters
   *         (id, position, radii) are missing or invalid.
   */
  public async create(params: CreateEllipseParams | CreateCircleParams): Promise<Shape.Ellipse | Shape.Circle> {
    const { id, type: elementType } = params

    if (elementType !== CoreElementType.ELLIPSE && elementType !== CoreElementType.CIRCLE
      && elementType !== 'ellipse' && elementType !== 'circle') {
      throw new CoreError(ErrorType.InvalidPayload, `EllipseCreator cannot create type: ${String(elementType)}`)
    }
    if (!id)
      throw new CoreError(ErrorType.InvalidPayload, 'Ellipse/Circle requires an ID.')

    const inputPosition = (params as unknown as { position?: PointData }).position
    if (inputPosition == null || typeof inputPosition.x !== 'number' || typeof inputPosition.y !== 'number') {
      throw new CoreError(ErrorType.InvalidPayload, 'Ellipse/Circle requires a valid position.')
    }

    const commonProps = this.createCommonProperties(id, params)

    console.warn('[EllipseCreator create] commonProps RECEIVED:', {
      id: commonProps.id,
      type: commonProps.type,
      majorCategory: commonProps.majorCategory,
      minorCategory: commonProps.minorCategory,
      zLevelId: commonProps.zLevelId,
      isFixedCategory: commonProps.isFixedCategory,
      positionX: commonProps.position.x,
      positionY: commonProps.position.y,
    })

    const paramsWithStyle = params as unknown as { fill?: string, stroke?: string, strokeWidth?: number, opacity?: number }
    const fill = paramsWithStyle.fill ?? '#CCCCCC'
    const stroke = paramsWithStyle.stroke ?? '#333333'
    const strokeWidth = paramsWithStyle.strokeWidth ?? 1
    const opacity = paramsWithStyle.opacity ?? 1

    // Initialize internalProperties with default cost properties first
    const internalProperties: Partial<Shape.Ellipse['properties'] & Shape.Circle['properties']> = {
      // 设置默认的成本相关属性
      costUnitPrice: 1,
      costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1
      costBasis: 'unit', // 🔧 修复：默认使用单位计算，而不是面积
      // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
      computedAreaStatus: 'none',
      computedPerimeterStatus: 'none',
      computedLengthStatus: 'none',
      // 然后合并其他属性
      ...(params.properties || {}),
    }
    internalProperties.fill = fill
    internalProperties.stroke = stroke
    internalProperties.strokeWidth = strokeWidth
    internalProperties.opacity = opacity
    let specificGeometricProps: { points: [PointData, PointData, PointData], radiusX: number, radiusY: number, radius?: number }

    if (elementType === CoreElementType.ELLIPSE || elementType === 'ellipse') {
      const ellipseParams = params
      const { radiusX, radiusY } = ellipseParams
      if (typeof radiusX !== 'number' || radiusX <= 0 || !Number.isFinite(radiusX)
        || typeof radiusY !== 'number' || radiusY <= 0 || !Number.isFinite(radiusY)) {
        throw new CoreError(ErrorType.InvalidPayload, `Ellipse requires positive finite radiusX and radiusY. Received: rx=${radiusX}, ry=${radiusY}`)
      }
      specificGeometricProps = {
        points: [commonProps.position, { x: commonProps.position.x + radiusX, y: commonProps.position.y, z: commonProps.position.z }, { x: commonProps.position.x, y: commonProps.position.y + radiusY, z: commonProps.position.z }],
        radiusX,
        radiusY,
      }
      internalProperties.width = radiusX * 2
      internalProperties.height = radiusY * 2
    }
    else { // Circle
      const circleParams = params as CreateCircleParams
      const { radius } = circleParams
      if (typeof radius !== 'number' || radius <= 0 || !Number.isFinite(radius)) {
        throw new CoreError(ErrorType.InvalidPayload, `Circle requires a positive finite radius. Received: ${radius}`)
      }
      specificGeometricProps = {
        points: [commonProps.position, { x: commonProps.position.x + radius, y: commonProps.position.y, z: commonProps.position.z }, { x: commonProps.position.x, y: commonProps.position.y + radius, z: commonProps.position.z }],
        radiusX: radius,
        radiusY: radius,
        radius,
      }
      internalProperties.radius = radius
      internalProperties.width = radius * 2
      internalProperties.height = radius * 2
    }

    if (elementType === CoreElementType.CIRCLE || elementType === 'circle') {
      const circleElement: Shape.Circle = {
        ...commonProps,
        fill,
        stroke,
        strokeWidth,
        opacity,
        type: CoreElementType.CIRCLE,
        points: specificGeometricProps.points,
        properties: internalProperties as Shape.Circle['properties'],
      }
      console.warn('[EllipseCreator create] FINAL CIRCLE ELEMENT:', {
        id: circleElement.id,
        type: circleElement.type,
        majorCategory: circleElement.majorCategory,
        minorCategory: circleElement.minorCategory,
        zLevelId: circleElement.zLevelId,
        isFixedCategory: circleElement.isFixedCategory,
        propertiesZLevelId: (circleElement.properties as unknown as { zLevelId?: string }).zLevelId,
        propertiesMajorCategory: (circleElement.properties as unknown as { majorCategory?: string }).majorCategory,
        propertiesMinorCategory: (circleElement.properties as unknown as { minorCategory?: string }).minorCategory,
      })
      return circleElement
    }
    else {
      const ellipseElement: Shape.Ellipse = {
        ...commonProps,
        fill,
        stroke,
        strokeWidth,
        opacity,
        type: CoreElementType.ELLIPSE,
        points: specificGeometricProps.points,
        properties: internalProperties as Shape.Ellipse['properties'],
      }
      console.warn('[EllipseCreator create] FINAL ELLIPSE ELEMENT:', {
        id: ellipseElement.id,
        type: ellipseElement.type,
        majorCategory: ellipseElement.majorCategory,
        minorCategory: ellipseElement.minorCategory,
        zLevelId: ellipseElement.zLevelId,
        isFixedCategory: ellipseElement.isFixedCategory,
        propertiesZLevelId: (ellipseElement.properties as unknown as { zLevelId?: string }).zLevelId,
        propertiesMajorCategory: (ellipseElement.properties as unknown as { majorCategory?: string }).majorCategory,
        propertiesMinorCategory: (ellipseElement.properties as unknown as { minorCategory?: string }).minorCategory,
      })
      return ellipseElement
    }
  }

  /**
   * Creates a default Ellipse element.
   *
   * @param id - The unique identifier for the default ellipse.
   * @param position - The center {@link PointData} for the default ellipse.
   * @param majorCategoryOverride - Optional override for majorCategory.
   * @param minorCategoryOverride - Optional override for minorCategory.
   * @param zLevelIdOverride - Optional override for zLevelId.
   * @param isFixedCategoryOverride - Optional override for isFixedCategory.
   * @returns A Promise resolving to the created default {@link Shape.Ellipse}.
   */
  public async createDefault(
    id: string,
    position: PointData,
    majorCategoryOverride?: MajorCategory,
    minorCategoryOverride?: MinorCategory,
    zLevelIdOverride?: string,
    isFixedCategoryOverride?: boolean,
  ): Promise<Shape.Ellipse> {
    const params: CreateEllipseParams = {
      id,
      type: CoreElementType.ELLIPSE,
      majorCategory: majorCategoryOverride ?? MajorCategory.FURNITURE,
      minorCategory: minorCategoryOverride,
      zLevelId: zLevelIdOverride,
      isFixedCategory: isFixedCategoryOverride,
      position,
      radiusX: 50,
      radiusY: 30,
      metadata: { name: `Default Ellipse ${id}` }, // This is Partial<MetadataProperties>
      fill: '#D9D9D9',
      stroke: '#333333',
      strokeWidth: 1,
      visible: true,
      locked: false,
      rotation: 0,
      selectable: true,
      draggable: true,
      showHandles: true,
      properties: {
        // 设置默认的成本相关属性
        costUnitPrice: 1,
        costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1
        costBasis: 'unit', // 🔧 修复：默认使用单位计算，而不是面积
        // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
        computedAreaStatus: 'none',
        computedPerimeterStatus: 'none',
        computedLengthStatus: 'none',
      },
    }
    return this.create(params) as Promise<Shape.Ellipse>
  }
}
