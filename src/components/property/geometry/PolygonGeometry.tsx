/**
 * Polygon Geometry Component
 *
 * Implements the standardized geometry layout for Polygon elements
 * according to the design document specifications.
 *
 * Layout:
 * - Position (2 columns): Position X, Position Y
 * - Scale (1 column): Size (circumradius)
 * - Transform (1 column): Rotation
 * - Calculation & Cost (unified layout)
 *
 * Supports all polygon types: POLYGON, TRIANGLE, QUADRILATERAL, PENTAGON,
 * HEXAGON, HEPTAGON, OCTAGON, NONAGON, DECAGON
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'

import { ElementType } from '@/types/core/elementDefinitions'
import { GeometrySection, PropertyField } from '../shared'

interface PolygonGeometryProps {
  /** The polygon element being edited */
  element: ShapeElement
  /** Function to get input values with proper formatting */
  getValueForInput: (path: string) => string
  /** Function to get placeholder values */
  getPlaceholderForInput: (path: string) => string
  /** Function to handle input changes */
  handleInputChange: (path: string, value: string, isNumeric?: boolean, convertToPixels?: boolean) => void
  /** Unit name for display (e.g., "mm", "cm") */
  unitName: string
}

// List of supported polygon types
const POLYGON_TYPES = [
  ElementType.POLYGON,
  ElementType.TRIANGLE,
  ElementType.QUADRILATERAL,
  ElementType.PENTAGON,
  ElementType.HEXAGON,
  ElementType.HEPTAGON,
  ElementType.OCTAGON,
  ElementType.NONAGON,
  ElementType.DECAGON,
]

/**
 * PolygonGeometry component for editing polygon properties.
 *
 * Provides a standardized layout for polygon geometry editing
 * with proper grouping and responsive design. Uses a single
 * size control representing the circumradius (outer circle radius).
 */
export function PolygonGeometry({
  element,
  getValueForInput,
  getPlaceholderForInput,
  handleInputChange,
  unitName,
}: PolygonGeometryProps) {
  // Verify this is a polygon element
  if (!POLYGON_TYPES.includes(element.type as ElementType)) {
    return null
  }

  // Get the polygon type name for display
  const getPolygonTypeName = (type: typeof POLYGON_TYPES[number]): string => {
    const polygonTypeMap: Partial<Record<ElementType, string>> = {
      [ElementType.TRIANGLE]: 'Triangle',
      [ElementType.QUADRILATERAL]: 'Quadrilateral',
      [ElementType.PENTAGON]: 'Pentagon',
      [ElementType.HEXAGON]: 'Hexagon',
      [ElementType.HEPTAGON]: 'Heptagon',
      [ElementType.OCTAGON]: 'Octagon',
      [ElementType.NONAGON]: 'Nonagon',
      [ElementType.DECAGON]: 'Decagon',
      [ElementType.POLYGON]: 'Polygon',
    }

    return polygonTypeMap[type] ?? 'Polygon'
  }

  const polygonName = getPolygonTypeName(element.type as typeof POLYGON_TYPES[number])

  return (
    <>
      {/* Position (2 columns) */}
      <GeometrySection type="position" columns={2}>
        <PropertyField
          label={`Position X (${unitName})`}
          type="number"
          value={getValueForInput('position.x')}
          onChange={value => handleInputChange('position.x', value)}
          placeholder={getPlaceholderForInput('position.x')}
        />
        <PropertyField
          label={`Position Y (${unitName})`}
          type="number"
          value={getValueForInput('position.y')}
          onChange={value => handleInputChange('position.y', value)}
          placeholder={getPlaceholderForInput('position.y')}
        />
      </GeometrySection>

      {/* Scale (1 column) */}
      <GeometrySection type="dimensions" columns={1}>
        <PropertyField
          label={`Size (${unitName})`}
          type="number"
          value={getValueForInput('radius')}
          onChange={value => handleInputChange('radius', value)}
          placeholder="0"
          helpText={`${polygonName} circumradius (outer circle radius)`}
        />
      </GeometrySection>

      {/* Transform (1 column) */}
      <GeometrySection type="transform" columns={1}>
        <PropertyField
          label="Rotation (°)"
          type="number"
          value={getValueForInput('rotation')}
          onChange={value => handleInputChange('rotation', value, true, false)}
          placeholder="0"
        />
      </GeometrySection>
    </>
  )
}
