import { describe, expect, it } from 'vitest'
import { MaterialType } from '@/types/core/element/elementMaterialTypes'

describe('materialType Enum', () => {
  it('should define standard material types', () => {
    // Verify that the enum exists and has the expected values
    expect(MaterialType).toBeDefined()

    // Test wall materials
    expect(MaterialType.BRICK).toBe('brick')
    expect(MaterialType.CONCRETE).toBe('concrete')
    expect(MaterialType.DRYWALL).toBe('drywall')

    // Test flooring materials
    expect(MaterialType.HARDWOOD).toBe('hardwood')
    expect(MaterialType.LAMINATE).toBe('laminate')
    expect(MaterialType.TILE).toBe('tile')
    expect(MaterialType.CARPET).toBe('carpet')
    expect(MaterialType.VINYL).toBe('vinyl')

    // Test general materials
    expect(MaterialType.WOOD).toBe('wood')
    expect(MaterialType.GLASS).toBe('glass')
    expect(MaterialType.STONE).toBe('stone')
    expect(MaterialType.METAL).toBe('metal')
    expect(MaterialType.PLASTIC).toBe('plastic')
    expect(MaterialType.FABRIC).toBe('fabric')
    expect(MaterialType.LEATHER).toBe('leather')

    // Test custom option
    expect(MaterialType.CUSTOM).toBe('custom')
  })

  it('should be usable in switch statements', () => {
    // Define a function that uses the enum in a switch statement
    function getMaterialCategory(material: MaterialType): string {
      switch (material) {
        case MaterialType.BRICK:
        case MaterialType.CONCRETE:
        case MaterialType.DRYWALL:
          return 'wall'

        case MaterialType.HARDWOOD:
        case MaterialType.LAMINATE:
        case MaterialType.TILE:
        case MaterialType.CARPET:
        case MaterialType.VINYL:
          return 'flooring'

        case MaterialType.WOOD:
        case MaterialType.GLASS:
        case MaterialType.STONE:
        case MaterialType.METAL:
        case MaterialType.PLASTIC:
        case MaterialType.FABRIC:
        case MaterialType.LEATHER:
          return 'general'

        case MaterialType.CUSTOM:
          return 'custom'

        default:
          return 'unknown'
      }
    }

    // Test the function with different enum values
    expect(getMaterialCategory(MaterialType.BRICK)).toBe('wall')
    expect(getMaterialCategory(MaterialType.HARDWOOD)).toBe('flooring')
    expect(getMaterialCategory(MaterialType.METAL)).toBe('general')
    expect(getMaterialCategory(MaterialType.CUSTOM)).toBe('custom')
  })

  it('should be usable as object keys', () => {
    // Create an object using enum values as keys
    const materialCosts: Record<MaterialType, number> = {
      [MaterialType.BRICK]: 50,
      [MaterialType.CONCRETE]: 30,
      [MaterialType.DRYWALL]: 20,
      [MaterialType.WOOD]: 40,
      [MaterialType.GLASS]: 60,
      [MaterialType.HARDWOOD]: 80,
      [MaterialType.LAMINATE]: 35,
      [MaterialType.TILE]: 45,
      [MaterialType.CARPET]: 25,
      [MaterialType.VINYL]: 15,
      [MaterialType.STONE]: 90,
      [MaterialType.METAL]: 70,
      [MaterialType.PLASTIC]: 10,
      [MaterialType.FABRIC]: 20,
      [MaterialType.LEATHER]: 100,
      [MaterialType.CUSTOM]: 0,
    }

    // Verify that we can access values using enum keys
    expect(materialCosts[MaterialType.BRICK]).toBe(50)
    expect(materialCosts[MaterialType.HARDWOOD]).toBe(80)
    expect(materialCosts[MaterialType.LEATHER]).toBe(100)
  })

  it('should be usable in arrays and collections', () => {
    // Create an array of materials
    const wallMaterials: MaterialType[] = [
      MaterialType.BRICK,
      MaterialType.CONCRETE,
      MaterialType.DRYWALL,
    ]

    // Verify array operations
    expect(wallMaterials.length).toBe(3)
    expect(wallMaterials.includes(MaterialType.BRICK)).toBe(true)
    expect(wallMaterials.includes(MaterialType.CARPET)).toBe(false)

    // Test array methods
    const materialNames = wallMaterials.map(material => material.toString())
    expect(materialNames).toEqual(['brick', 'concrete', 'drywall'])
  })

  it('should be compatible with string operations', () => {
    // Test string operations on enum values
    const material = MaterialType.HARDWOOD

    expect(material.toUpperCase()).toBe('HARDWOOD')
    expect(material.includes('wood')).toBe(true)
    expect(`Floor material: ${material}`).toBe('Floor material: hardwood')
  })
})
