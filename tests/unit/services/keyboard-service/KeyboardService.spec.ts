import type { EventBus } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
// Import the mocked functions for assertions
import { publishKeyPressed, publishKeyReleased } from '@/services/event-bus/helpers/publishers/input-publishers'

import { cleanupKeyboardService, getKeyboardService, initializeKeyboardService } from '@/services/keyboard-service'

// Mock the event bus publishers
vi.mock('@/services/event-bus/helpers/publishers/input-publishers', () => ({
  publishKeyPressed: vi.fn(),
  publishKeyReleased: vi.fn(),
}))

describe('keyboardService', () => {
  // Test dependencies
  let eventBus: EventBus
  let logger: LoggerService
  let addEventListenerSpy: any
  let removeEventListenerSpy: any

  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks()

    // Create mock dependencies
    eventBus = {
      publish: vi.fn(),
    } as unknown as EventBus

    logger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    } as unknown as LoggerService

    // Spy on document event listeners
    addEventListenerSpy = vi.spyOn(document, 'addEventListener')
    removeEventListenerSpy = vi.spyOn(document, 'removeEventListener')

    // Initialize keyboard service
    initializeKeyboardService(eventBus, logger)
  })

  afterEach(() => {
    // Clean up keyboard service
    cleanupKeyboardService()
  })

  describe('initialization and cleanup', () => {
    it('should add event listeners on initialization', () => {
      expect(addEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function))
      expect(addEventListenerSpy).toHaveBeenCalledWith('keyup', expect.any(Function))
      expect(logger.info).toHaveBeenCalledWith('Keyboard service initialized')
    })

    it('should remove event listeners on cleanup', () => {
      cleanupKeyboardService()

      expect(removeEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function))
      expect(removeEventListenerSpy).toHaveBeenCalledWith('keyup', expect.any(Function))
      expect(logger.info).toHaveBeenCalledWith('Keyboard service cleaned up')
    })

    it('should warn if trying to initialize an already initialized service', () => {
      // Initialize again
      initializeKeyboardService(eventBus, logger)

      expect(logger.warn).toHaveBeenCalledWith('Keyboard service already initialized.')
    })

    it('should warn if trying to clean up a service that is not initialized', () => {
      // Spy on console.warn
      const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      // Clean up
      cleanupKeyboardService()
      // Clean up again
      cleanupKeyboardService()

      // Check if console.warn was called with the expected message
      expect(consoleWarnSpy).toHaveBeenCalledWith('Keyboard service not initialized or already cleaned up.')

      // Restore the original console.warn
      consoleWarnSpy.mockRestore()
    })
  })

  describe('key binding registration', () => {
    it('should register a key binding', () => {
      const service = getKeyboardService()
      const callback = vi.fn()

      service?.registerKeyBinding('ctrl+s', callback)

      const bindings = service?.getKeyBindings()
      expect(bindings?.has('ctrl+s')).toBe(true)
      expect(bindings?.get('ctrl+s')?.callback).toBe(callback)
    })

    it('should normalize key strings to lowercase', () => {
      const service = getKeyboardService()
      const callback = vi.fn()

      service?.registerKeyBinding('CTRL+S', callback)

      const bindings = service?.getKeyBindings()
      expect(bindings?.has('ctrl+s')).toBe(true)
    })

    it('should unregister a key binding', () => {
      const service = getKeyboardService()
      const callback = vi.fn()

      service?.registerKeyBinding('ctrl+s', callback)
      service?.unregisterKeyBinding('ctrl+s')

      const bindings = service?.getKeyBindings()
      expect(bindings?.has('ctrl+s')).toBe(false)
    })

    it('should return an unregister function when registering a binding', () => {
      const service = getKeyboardService()
      const callback = vi.fn()

      const unregister = service?.registerKeyBinding('ctrl+s', callback)

      expect(typeof unregister).toBe('function')

      // Call the unregister function
      unregister?.()

      const bindings = service?.getKeyBindings()
      expect(bindings?.has('ctrl+s')).toBe(false)
    })

    it('should register a key binding with custom options', () => {
      const service = getKeyboardService()
      const callback = vi.fn()

      service?.registerKeyBinding('ctrl+s', callback, {
        preventDefault: false,
        stopPropagation: true,
        onKeyUp: true,
        description: 'Save document',
      })

      const bindings = service?.getKeyBindings()
      const binding = bindings?.get('ctrl+s')

      expect(binding?.options.preventDefault).toBe(false)
      expect(binding?.options.stopPropagation).toBe(true)
      expect(binding?.options.onKeyUp).toBe(true)
      expect(binding?.options.description).toBe('Save document')
    })
  })

  describe('key event listeners', () => {
    it('should add a key down listener', () => {
      const service = getKeyboardService()
      const listener = vi.fn()

      const unsubscribe = service?.addKeyDownListener(listener)

      expect(typeof unsubscribe).toBe('function')
      expect(document.addEventListener).toHaveBeenCalledWith('keydown', expect.any(Function))
    })

    it('should add a key up listener', () => {
      const service = getKeyboardService()
      const listener = vi.fn()

      const unsubscribe = service?.addKeyUpListener(listener)

      expect(typeof unsubscribe).toBe('function')
      expect(document.addEventListener).toHaveBeenCalledWith('keyup', expect.any(Function))
    })

    it('should remove a key down listener when unsubscribing', () => {
      const service = getKeyboardService()
      const listener = vi.fn()

      const unsubscribe = service?.addKeyDownListener(listener)
      unsubscribe?.()

      expect(document.removeEventListener).toHaveBeenCalledWith('keydown', expect.any(Function))
    })

    it('should remove a key up listener when unsubscribing', () => {
      const service = getKeyboardService()
      const listener = vi.fn()

      const unsubscribe = service?.addKeyUpListener(listener)
      unsubscribe?.()

      expect(document.removeEventListener).toHaveBeenCalledWith('keyup', expect.any(Function))
    })
  })

  describe('key event handling', () => {
    it('should handle keydown events and publish them', () => {
      // Create a mock event
      const mockEvent = new KeyboardEvent('keydown', {
        key: 'a',
        code: 'KeyA',
        ctrlKey: true,
        altKey: false,
        shiftKey: false,
        metaKey: false,
      })

      // Dispatch the event
      document.dispatchEvent(mockEvent)

      // Check if the event was published
      expect(publishKeyPressed).toHaveBeenCalledWith(
        eventBus,
        'a',
        'KeyA',
        expect.objectContaining({
          ctrlKey: true,
          altKey: false,
          shiftKey: false,
          metaKey: false,
        }),
        mockEvent,
      )
    })

    it('should handle keyup events and publish them', () => {
      // Create a mock event
      const mockEvent = new KeyboardEvent('keyup', {
        key: 'a',
        code: 'KeyA',
        ctrlKey: true,
        altKey: false,
        shiftKey: false,
        metaKey: false,
      })

      // Dispatch the event
      document.dispatchEvent(mockEvent)

      // Check if the event was published
      expect(publishKeyReleased).toHaveBeenCalledWith(
        eventBus,
        'a',
        'KeyA',
        expect.objectContaining({
          ctrlKey: true,
          altKey: false,
          shiftKey: false,
          metaKey: false,
        }),
        mockEvent,
      )
    })

    it('should trigger registered key binding on keydown', () => {
      const service = getKeyboardService()
      const callback = vi.fn()

      // Register a key binding
      service?.registerKeyBinding('ctrl+a', callback)

      // Create a mock event
      const mockEvent = new KeyboardEvent('keydown', {
        key: 'a',
        code: 'KeyA',
        ctrlKey: true,
        altKey: false,
        shiftKey: false,
        metaKey: false,
      })

      // Dispatch the event
      document.dispatchEvent(mockEvent)

      // Check if the callback was called
      expect(callback).toHaveBeenCalled()
    })

    it('should trigger registered key binding on keyup when onKeyUp is true', () => {
      const service = getKeyboardService()
      const callback = vi.fn()

      // Register a key binding with onKeyUp=true
      service?.registerKeyBinding('ctrl+a', callback, { onKeyUp: true })

      // Create a mock event
      const mockEvent = new KeyboardEvent('keyup', {
        key: 'a',
        code: 'KeyA',
        ctrlKey: true,
        altKey: false,
        shiftKey: false,
        metaKey: false,
      })

      // Dispatch the event
      document.dispatchEvent(mockEvent)

      // Check if the callback was called
      expect(callback).toHaveBeenCalled()
    })

    it('should prevent default when preventDefault is true', () => {
      const service = getKeyboardService()
      const callback = vi.fn()

      // Register a key binding with preventDefault=true
      service?.registerKeyBinding('ctrl+a', callback, { preventDefault: true })

      // Create a mock event with preventDefault spy
      const mockEvent = new KeyboardEvent('keydown', {
        key: 'a',
        code: 'KeyA',
        ctrlKey: true,
        altKey: false,
        shiftKey: false,
        metaKey: false,
      })
      const preventDefaultSpy = vi.spyOn(mockEvent, 'preventDefault')

      // Dispatch the event
      document.dispatchEvent(mockEvent)

      // Check if preventDefault was called
      expect(preventDefaultSpy).toHaveBeenCalled()
    })

    it('should stop propagation when stopPropagation is true', () => {
      const service = getKeyboardService()
      const callback = vi.fn()

      // Register a key binding with stopPropagation=true
      service?.registerKeyBinding('ctrl+a', callback, { stopPropagation: true })

      // Create a mock event with stopPropagation spy
      const mockEvent = new KeyboardEvent('keydown', {
        key: 'a',
        code: 'KeyA',
        ctrlKey: true,
        altKey: false,
        shiftKey: false,
        metaKey: false,
      })
      const stopPropagationSpy = vi.spyOn(mockEvent, 'stopPropagation')

      // Dispatch the event
      document.dispatchEvent(mockEvent)

      // Check if stopPropagation was called
      expect(stopPropagationSpy).toHaveBeenCalled()
    })
  })
})
