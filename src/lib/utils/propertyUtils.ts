/**
 * Gets a common value for a deeply nested property from an array of objects.
 *
 * @param items - An array of objects to inspect.
 * @param propertyPath - A dot-separated path to the property (e.g., 'foo.bar.baz').
 * @param defaultValue - The value to return if the property is not found or if values are mixed.
 * @returns The common value if all items have the same value for the path, otherwise the defaultValue.
 */
export function getCommonDeepValue<T extends Record<string, any>>(
  items: T[],
  propertyPath: string,
  defaultValue: any = 'mixed',
): any {
  if (!items || items.length === 0) {
    return defaultValue
  }

  const parts = propertyPath.split('.')

  const values = items.map((item) => {
    let value: any = item
    for (const part of parts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part]
      }
      else {
        value = undefined
        break
      }
    }
    return value
  })

  const firstValue = values[0]
  const allSame = values.every(v => JSON.stringify(v) === JSON.stringify(firstValue))

  if (allSame) {
    return firstValue !== undefined ? firstValue : ''
  }

  return defaultValue
}
