/**
 * Event Utilities
 *
 * This file provides type definitions for event utilities.
 * Implementation moved to src/lib/utils/events/eventUtils.ts
 */

import type { BaseEvent } from './eventCore'
import type { AppEventType } from './eventTypes'

/**
 * Create a new event with the specified type and payload
 *
 * @param type - The event type
 * @param payload - The event payload
 * @param timestamp - Optional timestamp (defaults to current time)
 * @returns A new event object
 */
export function createEvent<T>(
  type: AppEventType,
  payload: T,
  timestamp: number = Date.now(),
): BaseEvent & { id: string, payload: T } {
  return {
    type,
    payload,
    timestamp,
    id: `event-${timestamp}-${Math.random().toString(36).substring(2, 9)}`,
  }
}
