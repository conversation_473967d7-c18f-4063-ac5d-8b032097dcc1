/**
 * Layer Progress Component
 *
 * A comprehensive layer management panel that provides project progress tracking,
 * task module organization, and layer (Z-level) management functionality.
 *
 * Features:
 * - Project progress visualization with completion percentage
 * - Tabbed interface for different task modules (e.g., Floor, Furniture, Ceiling)
 * - Step-by-step task completion tracking
 * - Layer (Z-level) management with visibility controls
 * - Real-time progress updates and next task suggestions
 * - Layer editing capabilities (rename, add, delete)
 * - Responsive slide-in/out panel behavior
 *
 * @example
 * ```tsx
 * <SpecificLogicLayerPanel
 *   instanceRenderId="layer-panel-1"
 *   isLayerPanelOpen={true}
 * />
 * ```
 */

import type { TaskStep } from '@/types/core/layerPanelTypes'
import type { MajorCategory, MinorCategory } from '@/types/core/majorMinorTypes'

// cspell:ignore nums, SPLP
// import { AlertCircle } from 'lucide-react' // Unused
import { CheckCircle2, Edit2, Eye, EyeOff, Plus, Sparkles, Trash2 } from 'lucide-react'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { cn } from '@/lib/utils'
import { useLayerStore } from '@/store/layerStore'
import { TaskStatus } from '@/types/core/layerPanelTypes'

/**
 * Placeholder component for empty states in the layer panel
 * @param props - The component props
 * @param props.message - The message to display when no content is available
 */
const PlaceholderComponent: React.FC<{ message: string, instanceRenderId?: string }> = ({ message }) => (
  <div className="flex items-center justify-center h-full p-4 text-center text-muted-foreground">
    <p>{message}</p>
  </div>
)

/**
 * Props for the SpecificLogicLayerPanel component
 */
export interface SpecificLogicLayerPanelProps {
  /** Optional instance identifier for debugging and unique rendering */
  instanceRenderId?: string
  /** Controls whether the layer panel is visible/open */
  isLayerPanelOpen?: boolean
  /** Controls whether the bottom drawer is open (affects panel height) */
  isBottomDrawerOpen?: boolean
}

/**
 * Main Layer Panel component that provides comprehensive project and layer management.
 *
 * This component serves as the primary interface for managing project progress,
 * task modules, and individual layers (Z-levels). It integrates with the Zustand
 * layer store to provide real-time updates and state management.
 *
 * Key functionality:
 * - Displays overall project progress with completion percentage
 * - Organizes tasks into modules (Floor, Furniture, Ceiling)
 * - Manages individual task steps with completion tracking
 * - Provides layer (Z-level) management with visibility controls
 * - Supports layer editing (rename, add, delete operations)
 * - Shows next suggested tasks and completion celebrations
 *
 * @param props - The component props
 * @param props.instanceRenderId - Optional identifier for debugging and unique rendering
 * @param props.isLayerPanelOpen - Controls panel visibility and slide animation
 * @returns The rendered layer panel component
 */
export const SpecificLogicLayerPanel: React.FC<SpecificLogicLayerPanelProps> = (props) => {
  const { instanceRenderId, isBottomDrawerOpen = false } = props
  // console.log(`[SPLP RENDER TEST - ${instanceRenderId || 'NO_ID'}] isLayerPanelOpen: ${props.isLayerPanelOpen}`);

  // Calculate dynamic height based on bottom drawer state
  // Use useEffect to get actual toolbar height instead of hardcoded value
  const [actualToolbarHeight, setActualToolbarHeight] = useState(56) // fallback to 56px
  const bottomDrawerHeight = isBottomDrawerOpen ? 384 : 56 // 384px (md:h-96) when expanded, 56px when collapsed
  const verticalGap = 8 // 8px gap on top and bottom (total 16px)
  const dynamicHeight = `calc(100vh - ${actualToolbarHeight}px - ${bottomDrawerHeight}px - ${verticalGap * 2}px)`

  // Get actual toolbar height on mount and window resize
  const updateToolbarHeight = useCallback(() => {
    const toolbar = document.querySelector('.toolbar')
    if (toolbar) {
      const rect = toolbar.getBoundingClientRect()
      const newHeight = rect.height
      // Use setTimeout to avoid direct setState call in useEffect
      setTimeout(() => {
        setActualToolbarHeight(prevHeight => prevHeight !== newHeight ? newHeight : prevHeight)
      }, 0)
    }
  }, [])

  useEffect(() => {
    updateToolbarHeight()
    window.addEventListener('resize', updateToolbarHeight)
    return () => window.removeEventListener('resize', updateToolbarHeight)
  }, [updateToolbarHeight])

  // Zustand store selectors and actions - individual selection
  const modules = useLayerStore(state => state.modules)
  const currentModuleId = useLayerStore(state => state.currentModuleId)
  const currentStepId = useLayerStore(state => state.currentStepId)
  const currentZLevelId = useLayerStore(state => state.currentZLevelId)

  const selectLayerIdentifiers = useLayerStore(state => state.selectLayerIdentifiers)
  const updateStepStatus = useLayerStore(state => state.updateStepStatus)
  const toggleZLevelActive = useLayerStore(state => state.toggleZLevelActive)
  const setCurrentZLevelFocus = useLayerStore(state => state.setCurrentZLevelFocus)
  const deleteZLevel = useLayerStore(state => state.deleteZLevel)
  const addZLevel = useLayerStore(state => state.addZLevel)
  const renameZLevel = useLayerStore(state => state.renameZLevel)

  // State for editing layer names (local to this component)
  const [editingLayerId, setEditingLayerId] = useState<string | null>(null)
  const [editedName, setEditedName] = useState('')
  const editInputRef = useRef<HTMLInputElement>(null)

  // Calculate total progress
  const totalSteps = modules.reduce((acc, module) => acc + module.steps.length, 0)
  const completedSteps = modules.reduce((acc, module) => {
    return acc + module.steps.filter(step => step.status === TaskStatus.COMPLETED).length
  }, 0)
  const totalProgress = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0

  // Sort modules
  const sortedModules = [...modules].sort((a, b) => a.order - b.order)

  // Check if all modules in the entire project are complete
  const isTheEntireProjectComplete = sortedModules.every(m =>
    m.steps.every(step => step.status === TaskStatus.COMPLETED),
  )

  // NEW: Find the first module that is not yet complete to be the suggested next one
  const trulyNextModuleId = sortedModules.find(m =>
    !m.steps.every(step => step.status === TaskStatus.COMPLETED),
  )?.id

  // Find the first pending step across all modules for the "Next" badge on a step
  const firstPendingStepAcrossAllModules = sortedModules
    .flatMap(module =>
      module.steps
        .filter(step => step.status !== TaskStatus.COMPLETED)
        .sort((a, b) => a.order - b.order)
        .map(step => ({ ...step, moduleId: module.id })),
    )[0]

  // Handlers using store actions
  const handleSelectModule = (moduleId: string) => {
    selectLayerIdentifiers(moduleId as MajorCategory)
  }

  const handleSelectStep = (moduleId: string, stepId: string) => {
    selectLayerIdentifiers(moduleId as MajorCategory, stepId as MinorCategory)
  }

  const handleCompleteStep = (moduleId: string, stepId: string, isComplete: boolean) => {
    const status = isComplete ? TaskStatus.COMPLETED : TaskStatus.PENDING
    updateStepStatus(moduleId as MajorCategory, stepId as MinorCategory, status)
  }

  const handleToggleZLevel = (moduleId: string, stepId: string, zLevelId: string) => {
    toggleZLevelActive(moduleId as MajorCategory, stepId as MinorCategory, zLevelId)
  }

  const handleSetFocusZLevel = (moduleId: string, stepId: string, zLevelId: string) => {
    setCurrentZLevelFocus(moduleId as MajorCategory, stepId as MinorCategory, zLevelId)
  }

  const handleDeleteZLevelInternal = (moduleId: string, stepId: string, zLevelId: string) => {
    const module = modules.find(m => m.id === moduleId)
    const step = module?.steps.find(s => s.id === stepId)
    if (step && step.zLevels.length <= 1) {
      console.warn('Cannot delete the last layer. At least one layer must remain.')
      return
    }
    deleteZLevel(moduleId as MajorCategory, stepId as MinorCategory, zLevelId)
  }

  const handleAddZLevelInternal = (moduleId: string, stepId: string) => {
    // Type casting for safety, assuming moduleId and stepId are valid categories.
    addZLevel(moduleId as MajorCategory, stepId as MinorCategory)
  }

  const startEditing = (layerId: string, currentName: string) => {
    setEditingLayerId(layerId)
    setEditedName(currentName)
    // eslint-disable-next-line style/max-statements-per-line
    setTimeout(() => { editInputRef.current?.focus() }, 50)
  }

  const saveLayerName = (moduleId: string, stepId: string, layerId: string) => {
    renameZLevel(moduleId as MajorCategory, stepId as MinorCategory, layerId, editedName)
    setEditingLayerId(null)
  }

  const handleKeyPressOnEdit = (e: React.KeyboardEvent, moduleId: string, stepId: string, layerId: string) => {
    if (e.key === 'Enter') {
      saveLayerName(moduleId, stepId, layerId)
    }
    else if (e.key === 'Escape') {
      setEditingLayerId(null)
    }
  }

  // Determine effective IDs for rendering, defaulting if necessary
  let effectiveCurrentModuleId = currentModuleId
  if (effectiveCurrentModuleId == null || !sortedModules.find(m => m.id === effectiveCurrentModuleId)) {
    effectiveCurrentModuleId = sortedModules[0]?.id
  }

  const currentModuleData = sortedModules.find(m => m.id === effectiveCurrentModuleId)
  let effectiveCurrentStepId = currentStepId
  if (currentModuleData && (!effectiveCurrentStepId || !currentModuleData.steps.find(s => s.id === effectiveCurrentStepId))) {
    effectiveCurrentStepId = currentModuleData.steps.sort((a, b) => a.order - b.order)[0]?.id
  }
  const currentStepDetails = currentModuleData?.steps.find(s => s.id === effectiveCurrentStepId)

  if (sortedModules.length === 0) {
    return (
      <Card
        className={cn(
          'fixed left-2 z-50 w-[300px]', // Explicit width
          'transition-transform duration-300 ease-in-out',
          'bg-background border border-border rounded-md',
          'select-none',
          props.isLayerPanelOpen ? 'translate-x-0' : '-translate-x-[calc(100%+0.5rem)]',
          !props.isLayerPanelOpen && instanceRenderId?.startsWith('EditorLayoutDLP_detached') ? 'translate-x-0' : '',
        )}
        style={{
          top: `${actualToolbarHeight + verticalGap}px`,
          height: dynamicHeight,
        }}
      >
        <CardContent className="p-2 flex flex-col h-full">
          <PlaceholderComponent message="No task modules available." instanceRenderId={instanceRenderId} />
        </CardContent>
      </Card>
    )
  }

  if (effectiveCurrentModuleId == null) {
    return (
      <Card
        className={cn(
          'fixed left-2 z-50 w-[300px]', // Explicit width
          'transition-transform duration-300 ease-in-out',
          'bg-background border border-border rounded-md',
          'select-none',
          props.isLayerPanelOpen ? 'translate-x-0' : '-translate-x-[calc(100%+0.5rem)]',
          !props.isLayerPanelOpen && instanceRenderId?.startsWith('EditorLayoutDLP_detached') ? 'translate-x-0' : '',
        )}
        style={{
          top: `${actualToolbarHeight + verticalGap}px`,
          height: dynamicHeight,
        }}
      >
        <CardContent className="p-2 flex flex-col h-full">
          <PlaceholderComponent message="No module selected or available." instanceRenderId={instanceRenderId} />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card
      className={cn(
        'fixed left-2 z-50 w-[300px]', // Explicit width
        'transition-transform duration-300 ease-in-out',
        'bg-background border border-border rounded-md',
        'select-none',
        props.isLayerPanelOpen ? 'translate-x-0' : '-translate-x-[calc(100%+0.5rem)]',
        !props.isLayerPanelOpen && instanceRenderId?.startsWith('EditorLayoutDLP_detached') ? 'translate-x-0' : '',
      )}
      style={{
        top: `${actualToolbarHeight + verticalGap}px`,
        height: dynamicHeight,
      }}
    >
      <CardContent className="p-2 flex flex-col h-full" data-tutorial="layer-panel-content">
        <div className="my-4" data-tutorial="project-progress-overview">
          <h3 className="text-lg font-semibold flex items-center">
            <span>Project Progress</span>
            {isTheEntireProjectComplete && <Sparkles className="ml-2 h-5 w-5 text-yellow-500" />}
          </h3>
          <p className="text-xs text-muted-foreground mt-2">
            {completedSteps}
            {' '}
            of
            {' '}
            {totalSteps}
            {' '}
            steps completed (
            {totalProgress}
            %)
          </p>
          <Progress value={totalProgress} className="w-full h-2 mt-2" />
        </div>

        <Tabs
          defaultValue={effectiveCurrentModuleId}
          value={effectiveCurrentModuleId}
          onValueChange={handleSelectModule}
          className="w-full flex flex-col flex-1 min-h-0"
        >
          <TabsList className="grid w-full grid-cols-3" data-tutorial="module-tabs">
            {sortedModules.map((module) => {
              const isModuleComplete = module.steps.every(step => step.status === TaskStatus.COMPLETED)
              const isNextSuggestedModule = !isModuleComplete && module.id === trulyNextModuleId

              return (
                <TabsTrigger
                  key={module.id}
                  value={module.id}
                  className={cn(
                    'text-xs px-1 flex items-center justify-center gap-1',
                    isNextSuggestedModule && 'font-bold text-indigo-600 animate-pulse',
                  )}
                >
                  {isModuleComplete && <CheckCircle2 className="h-3 w-3 text-green-500 flex-shrink-0" />}
                  <span>
                    {module.name}
                  </span>
                  {isNextSuggestedModule && <Sparkles className="h-3 w-3 text-indigo-500 flex-shrink-0" />}
                </TabsTrigger>
              )
            })}
          </TabsList>

          {sortedModules.map((module) => {
            const isThisModuleFullyComplete = module.steps.every(step => step.status === TaskStatus.COMPLETED)
            let nextModuleInSequence: { id: string, name: string } | undefined
            if (isThisModuleFullyComplete) {
              const currentModuleIdx = sortedModules.findIndex(m => m.id === module.id)
              if (currentModuleIdx !== -1 && currentModuleIdx < sortedModules.length - 1) {
                nextModuleInSequence = {
                  id: sortedModules[currentModuleIdx + 1].id,
                  name: sortedModules[currentModuleIdx + 1].name,
                }
              }
            }

            return (
              <TabsContent key={module.id} value={module.id} className="flex-1 order-1 overflow-y-auto mt-2 min-h-0">
                {/* Always render the list of steps */}
                <div className="flex flex-col-reverse gap-1" data-tutorial="step-list">
                  {module.steps
                    .sort((a, b) => a.order - b.order)
                    .map((step: TaskStep) => {
                      const isActiveStep = step.id === effectiveCurrentStepId && module.id === effectiveCurrentModuleId
                      const isCompleted = step.status === TaskStatus.COMPLETED
                      // const isNextStep = step.id === firstPendingStepAcrossAllModules?.id && module.id === firstPendingStepAcrossAllModules?.moduleId; // Ensure this is commented or removed

                      return (
                        <div
                          key={step.id}
                          className={cn(
                            'relative border rounded-md p-2 transition-colors',
                            isActiveStep ? 'bg-primary/10 border-primary shadow-md' : 'border-muted hover:bg-muted/10',
                            isCompleted ? 'bg-green-500/10 border-green-500/30' : '',
                          )}
                        >
                          <div
                            className="flex justify-between items-center cursor-pointer"
                            onClick={() => handleSelectStep(module.id, step.id)}
                          >
                            <div className="flex items-center w-full">
                              <div className="flex items-center gap-2 flex-grow min-w-0 mr-2">
                                <Checkbox
                                  id={`check-${step.id}`}
                                  checked={isCompleted}
                                  onCheckedChange={checked => handleCompleteStep(module.id, step.id, checked === true)}
                                  className={cn(
                                    'flex-shrink-0',
                                    isCompleted && 'border-green-600 data-[state=checked]:bg-green-500', // Added comma
                                  )}
                                />
                                <span
                                  className={cn(
                                    'text-sm truncate',
                                    isCompleted ? 'line-through text-muted-foreground/70' : 'font-medium',
                                    isActiveStep ? 'text-primary' : 'text-foreground', // Added comma
                                  )}
                                  title={step.name}
                                >
                                  {step.name}
                                </span>
                              </div>

                              <div className="flex items-center ml-auto pl-1 flex-shrink-0">
                                {step.zLevels.length > 0 && (
                                  <span className="text-xs text-muted-foreground mr-1 flex-shrink-0 whitespace-nowrap">
                                    {step.zLevels.length}
                                    {' '}
                                    layer
                                    {step.zLevels.length > 1 ? 's' : ''}
                                    {/* Intentionally kept on one line to see if formatter handles it, but linter might complain */}
                                  </span>
                                )}
                                {firstPendingStepAcrossAllModules?.id === step.id && firstPendingStepAcrossAllModules.moduleId === module.id && (
                                  <span className="text-xs px-1.5 py-0.5 rounded-sm bg-purple-600 text-white flex items-center flex-shrink-0 whitespace-nowrap">
                                    <Sparkles className="h-3 w-3 mr-1 flex-shrink-0" />
                                    Next
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Z-Levels (Layers) within the step - Try to fix indentation for this block */}
                          {isActiveStep && step.zLevels.length > 0 && (
                            <div className="mt-1 p-1 border-t border-dashed border-border space-y-1" data-tutorial="z-levels">
                              {currentStepDetails !== undefined && currentStepDetails !== null && currentStepDetails.zLevels.length > 0
                                ? (
                                    <div className="space-y-0.5 mt-1 flex-1 overflow-y-auto scrollbar-thin pb-2">
                                      <div className="flex justify-between items-center my-1 px-1">
                                        <h5 className="text-xs font-semibold text-foreground/90">Layers</h5>
                                        <Button
                                          variant="ghost"
                                          size="icon"
                                          className="h-auto w-auto p-1"
                                          title="Add New Layer Above Selected"
                                          onClick={() => {
                                            if (currentModuleData !== undefined && currentModuleData !== null && currentStepDetails !== undefined && currentStepDetails !== null) {
                                              handleAddZLevelInternal(currentModuleData.id, currentStepDetails.id)
                                            }
                                          }}
                                        >
                                          <Plus size={14} />
                                        </Button>
                                      </div>
                                      {[...currentStepDetails.zLevels]
                                        .sort((a, b) => b.zIndex - a.zIndex)
                                        .map((zLevel) => {
                                          const isCurrentActiveLayer = zLevel.id === currentZLevelId
                                          return (
                                            <div
                                              key={zLevel.id}
                                              className={cn(
                                                'group flex items-center justify-between py-1.5 px-2 rounded-md hover:bg-muted/80',
                                                isCurrentActiveLayer && 'bg-primary/10 hover:bg-primary/20',
                                              )}
                                            >
                                              <div className="flex items-center flex-grow min-w-0">
                                                {editingLayerId === zLevel.id
                                                  ? (
                                                      <Input
                                                        ref={editInputRef}
                                                        type="text"
                                                        value={editedName}
                                                        onChange={e => setEditedName(e.target.value)}
                                                        onBlur={() => {
                                                          if (currentModuleData !== undefined && currentModuleData !== null && currentStepDetails !== undefined && currentStepDetails !== null) {
                                                            saveLayerName(currentModuleData.id, currentStepDetails.id, zLevel.id)
                                                          }
                                                        }}
                                                        onKeyDown={(e) => {
                                                          if (currentModuleData !== undefined && currentModuleData !== null && currentStepDetails !== undefined && currentStepDetails !== null) {
                                                            handleKeyPressOnEdit(e, currentModuleData.id, currentStepDetails.id, zLevel.id)
                                                          }
                                                        }}
                                                        className="h-7 px-1 py-0.5 text-xs flex-grow"
                                                      />
                                                    )
                                                  : (
                                                      <span
                                                        className="flex-grow cursor-pointer truncate pr-1 font-medium"
                                                        title={zLevel.name}
                                                        onClick={() => {
                                                          if (currentModuleData !== undefined && currentModuleData !== null && currentStepDetails !== undefined && currentStepDetails !== null) {
                                                            handleSetFocusZLevel(currentModuleData.id, currentStepDetails.id, zLevel.id)
                                                          }
                                                        }}
                                                      >
                                                        {zLevel.name}
                                                      </span>
                                                    )}
                                              </div>
                                              <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 flex-shrink-0">
                                                <Button
                                                  variant="ghost"
                                                  size="icon"
                                                  className="h-auto w-auto p-1"
                                                  title={zLevel.active ? 'Hide Layer' : 'Show Layer'}
                                                  onClick={() => {
                                                    if (currentModuleData != null) {
                                                      handleToggleZLevel(currentModuleData.id, currentStepDetails.id, zLevel.id)
                                                    }
                                                  }}
                                                >
                                                  {zLevel.active ? <Eye size={14} /> : <EyeOff size={14} />}
                                                </Button>
                                                <Button
                                                  variant="ghost"
                                                  size="icon"
                                                  className="h-auto w-auto p-1"
                                                  title="Edit Layer Name"
                                                  onClick={() => startEditing(zLevel.id, zLevel.name)}
                                                >
                                                  <Edit2 size={14} />
                                                </Button>
                                                <Button
                                                  variant="ghost"
                                                  size="icon"
                                                  className="h-auto w-auto p-1 text-destructive hover:text-destructive/80 disabled:text-muted-foreground/50"
                                                  title="Delete Layer"
                                                  disabled={currentStepDetails.zLevels.length <= 1}
                                                  onClick={() => {
                                                    if (currentModuleData != null) {
                                                      handleDeleteZLevelInternal(currentModuleData.id, currentStepDetails.id, zLevel.id)
                                                    }
                                                  }}
                                                >
                                                  <Trash2 size={14} />
                                                </Button>
                                              </div>
                                            </div>
                                          )
                                        })}
                                    </div>
                                  )
                                : (
                                    <PlaceholderComponent message="No layers in this step." instanceRenderId={instanceRenderId} />
                                  )}
                            </div>
                          )}
                        </div>
                      )
                    })}
                </div>

                {/* Append "Module Complete!" Section if this module is the active one and is fully complete */}
                {isThisModuleFullyComplete && module.id === effectiveCurrentModuleId && (
                  <div className="p-4 mt-4 text-center border-t border-dashed">
                    <CheckCircle2 className="h-12 w-12 text-green-500 mx-auto mb-2" />
                    <h4 className="text-lg font-semibold mb-1">
                      {module.name}
                      {' '}
                      Complete!
                    </h4>
                    <p className="text-sm text-muted-foreground mb-3">
                      Well done! All steps in this module are finished.
                    </p>
                    {nextModuleInSequence
                      ? (
                          <Button
                            onClick={() => {
                              if (nextModuleInSequence != null) {
                                handleSelectModule(nextModuleInSequence.id)
                              }
                            }}
                            className="bg-indigo-600 hover:bg-indigo-700 text-white"
                          >
                            Proceed to
                            {' '}
                            {nextModuleInSequence.name}
                            <Sparkles className="ml-2 h-4 w-4" />
                          </Button>
                        )
                      : (isTheEntireProjectComplete
                          ? (
                              <p className="text-sm text-muted-foreground">You've completed all available modules!</p>
                            )
                          : null
                        )}
                  </div>
                )}
              </TabsContent>
            )
          })}
        </Tabs>
      </CardContent>
    </Card>
  )
}

export default SpecificLogicLayerPanel
