/**
 * Template Service
 *
 * Service for managing template functionality including loading,
 * filtering, and applying templates to the application state.
 */

import type { MajorCategory, MinorCategory, ShapeElement } from '@/types/core/elementDefinitions'
import type {
  Template,
  TemplateCategory,
  TemplateFilter,
  TemplateMetadata,
  TemplateService,
} from '@/types/template'
import { appEventBus } from '@/services/core/event-bus'

/**
 * Convert template data to ensure compatibility with ShapeElement interface
 */
function normalizeTemplateShape(shape: any): ShapeElement {
  // Map old minorCategory values to new ones
  const minorCategoryMap: Record<string, MinorCategory> = {
    TABLE: 'tables',
    CHAIR: 'seating',
    BED: 'beds',
    STORAGE: 'storage',
    APPLIANCE: 'appliances',
    architecture: 'architecture',
    coverings: 'coverings',
    decor: 'decor',
    utilities: 'utilities',
    lighting: 'lighting',
    tables: 'tables',
    seating: 'seating',
    beds: 'beds',
    storage: 'storage',
    appliances: 'appliances',
  }

  // Ensure majorCategory is valid
  const validMajorCategories = ['BASE', 'CEILING', 'FURNITURE']
  const majorCategory = validMajorCategories.includes(shape.majorCategory)
    ? shape.majorCategory as MajorCategory
    : 'BASE' as MajorCategory

  // Map minorCategory
  const minorCategory = shape.minorCategory
    ? minorCategoryMap[shape.minorCategory] || 'architecture' as MinorCategory
    : 'architecture' as MinorCategory

  // Ensure required fields exist
  const normalizedShape: ShapeElement = {
    ...shape,
    majorCategory,
    minorCategory,
    // Ensure required fields have defaults
    position: shape.position || { x: 0, y: 0, z: 0 },
    rotation: shape.rotation || 0,
    visible: shape.visible !== undefined ? shape.visible : true,
    locked: shape.locked !== undefined ? shape.locked : false,
    selectable: shape.selectable !== undefined ? shape.selectable : true,
    draggable: shape.draggable !== undefined ? shape.draggable : true,
    showHandles: shape.showHandles !== undefined ? shape.showHandles : true,
    metadata: shape.metadata || {
      createdAt: Date.now(),
      updatedAt: Date.now(),
    },
  }

  return normalizedShape
}

/**
import { appEventBus } from '@/services/core/event-bus'

/**
 * Template service implementation
 */
class TemplateServiceImpl implements TemplateService {
  private templates: Template[] = []
  private isInitialized = false

  /**
   * Get all available templates
   */
  getTemplates = async (): Promise<Template[]> => {
    if (!this.isInitialized) {
      await this.loadTemplatesFromConfig()
    }
    return [...this.templates]
  }

  /**
   * Get template by ID
   */
  getTemplate = async (id: string): Promise<Template | null> => {
    const templates = await this.getTemplates()
    return templates.find(template => template.metadata.id === id) || null
  }

  /**
   * Get filtered templates
   */
  getFilteredTemplates = async (filter: TemplateFilter): Promise<Template[]> => {
    const templates = await this.getTemplates()

    return templates.filter((template) => {
      // Category filter
      if (filter.category && template.metadata.category !== filter.category) {
        return false
      }

      // Tags filter
      if (filter.tags && filter.tags.length > 0) {
        const hasMatchingTag = filter.tags.some(tag =>
          template.metadata.tags.includes(tag),
        )
        if (!hasMatchingTag)
          return false
      }

      // Difficulty filter
      if (filter.difficulty && filter.difficulty.length > 0) {
        if (!filter.difficulty.includes(template.metadata.difficulty)) {
          return false
        }
      }

      // Featured filter
      if (filter.featured !== undefined) {
        if (template.metadata.featured !== filter.featured) {
          return false
        }
      }

      // Search filter
      if (filter.search) {
        const searchLower = filter.search.toLowerCase()
        const matchesName = template.metadata.name.toLowerCase().includes(searchLower)
        const matchesDescription = template.metadata.description.toLowerCase().includes(searchLower)
        const matchesTags = template.metadata.tags.some(tag =>
          tag.toLowerCase().includes(searchLower),
        )

        if (!matchesName && !matchesDescription && !matchesTags) {
          return false
        }
      }

      return true
    })
  }

  /**
   * Load template into application
   */
  loadTemplate = async (template: Template): Promise<void> => {
    try {
      // Emit event to notify application of template loading
      appEventBus.emit({
        type: 'template:loading',
        payload: { template },
        timestamp: Date.now()
      })

      // Normalize template shapes to ensure compatibility
      const normalizedShapes = template.state.shapes.map(shape => normalizeTemplateShape(shape))

      // Store template data directly in reno-pilot-shapes-storage
      const shapesData = {
        shapes: normalizedShapes,
        selectedShapeIds: template.state.selectedShapeIds || [],
      }

      // Save directly to the main shapes storage
      localStorage.setItem('reno-pilot-shapes-storage', JSON.stringify(shapesData))

      // Force shapesStore to update with template data
      if (typeof window !== 'undefined' && (window as any).__ZUSTAND_SHAPES_STORE__) {
        const shapesStore = (window as any).__ZUSTAND_SHAPES_STORE__

        // Directly update the store with the normalized shapes
        shapesStore.setState({
          shapes: normalizedShapes,
          selectedShapeIds: template.state.selectedShapeIds || [],
        })
      }

      // Also store template metadata for reference
      // Store template data in localStorage for immediate access

      const templateData = {
        shapes: template.state.shapes,
        selectedShapeIds: template.state.selectedShapeIds || [],
        metadata: template.metadata,
        timestamp: new Date().toISOString(),
      }
      localStorage.setItem('renopilot-current-template', JSON.stringify(templateData))
      localStorage.setItem('renopilot-template-loaded', 'true')

      // Emit event that the application can listen to
      appEventBus.emit({
        type: 'template:apply',
        payload: {
          shapes: template.state.shapes,
          selectedShapeIds: template.state.selectedShapeIds || [],
        },
        timestamp: Date.now()
      })

      appEventBus.emit({
        type: 'template:loaded',
        payload: { template },
        timestamp: Date.now()
      })

      console.log('Template loaded and stored in localStorage:', template.metadata.name)
    }
    catch (error) {
      console.error('Failed to load template:', error)
      throw new Error(`Failed to load template: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Create new template from current state
   */
  saveAsTemplate = async (metadata: Omit<TemplateMetadata, 'id' | 'createdAt' | 'updatedAt'>): Promise<Template> => {
    // This would typically get the current application state
    // For now, we'll create a placeholder implementation
    const now = new Date()
    const id = `template-${Date.now()}`

    const template: Template = {
      metadata: {
        ...metadata,
        id,
        createdAt: now,
        updatedAt: now,
      },
      state: {
        shapes: [], // Would get from current application state
        selectedShapeIds: [],
      },
      version: 1,
    }

    // Add to templates list
    this.templates.push(template)

    // Emit event
    appEventBus.emit({
      type: 'template:created',
      payload: { template },
      timestamp: Date.now()
    })

    return template
  }

  /**
   * Delete template
   */
  deleteTemplate = async (id: string): Promise<void> => {
    const index = this.templates.findIndex(template => template.metadata.id === id)
    if (index === -1) {
      throw new Error(`Template with id '${id}' not found`)
    }

    this.templates.splice(index, 1)
    appEventBus.emit({
      type: 'template:deleted',
      payload: { templateId: id },
      timestamp: Date.now()
    })
  }

  /**
   * Get template categories
   */
  getCategories = (): TemplateCategory[] => {
    return [
      'residential',
      'commercial',
      'office',
      'retail',
      'hospitality',
      'healthcare',
      'education',
      'industrial',
      'outdoor',
      'custom',
    ]
  }

  /**
   * Load templates from configuration files
   */
  private loadTemplatesFromConfig = async (): Promise<void> => {
    try {
      // Load template configurations from the public/templates directory
      const templateConfigs = [
        'single-person-1',
        'single-person-2',
        'single-person-3',
        'two-person-1',
        'two-person-2',
        'two-person-3',
        'three-person-1',
        'three-person-2',
        'three-person-3',
        'multi-person-1',
        'multi-person-2',
        'multi-person-3',
      ]

      const templates: Template[] = []

      for (const configName of templateConfigs) {
        try {
          // Fetch the JSON configuration from public folder
          const response = await fetch(`/templates/${configName}.json`)
          if (!response.ok) {
            throw new Error(`Failed to fetch ${configName}.json`)
          }
          const config = await response.json()

          // Create template metadata based on filename
          const metadata: TemplateMetadata = {
            id: configName,
            name: this.generateTemplateName(configName),
            description: this.generateTemplateDescription(configName),
            category: this.getCategoryFromName(configName),
            tags: this.getTagsFromName(configName),
            previewImage: `/templates/${this.getPreviewImage(configName)}`,
            author: 'RenoPilot',
            createdAt: new Date(),
            updatedAt: new Date(),
            version: '1.0.0',
            featured: configName.includes('single-person-1'),
            difficulty: 'beginner',
            estimatedTime: this.getEstimatedTime(configName),
          }

          const template: Template = {
            metadata,
            state: config.state,
            version: config.version || 1,
          }

          templates.push(template)
        }
        catch (error) {
          console.warn(`Failed to load template config: ${configName}`, error)
        }
      }

      this.templates = templates
      this.isInitialized = true
    }
    catch (error) {
      console.error('Failed to load templates from config:', error)
      this.isInitialized = true // Set to true even on error to prevent infinite retries
    }
  }

  /**
   * Generate template name from config name
   */
  private generateTemplateName(configName: string): string {
    const parts = configName.split('-')
    const personCount = parts[0]
    const variant = parts[2]

    const personCountMap: Record<string, string> = {
      single: 'Single Person',
      two: 'Two Person',
      three: 'Three Person',
      multi: 'Multi Person',
    }

    return `${personCountMap[personCount] || personCount} Layout ${variant}`
  }

  /**
   * Generate template description from config name
   */
  private generateTemplateDescription(configName: string): string {
    const parts = configName.split('-')
    const personCount = parts[0]

    const descriptions: Record<string, string> = {
      single: 'Compact layout design suitable for single person living',
      two: 'Comfortable layout design suitable for two person living',
      three: 'Spacious layout design suitable for three person family',
      multi: 'Large layout design suitable for multi person living',
    }

    return descriptions[personCount] || 'Layout design template'
  }

  /**
   * Get category from template name
   */
  private getCategoryFromName(_configName: string): TemplateCategory {
    return 'residential'
  }

  /**
   * Get tags from template name
   */
  private getTagsFromName(configName: string): string[] {
    const parts = configName.split('-')
    const tags = ['layout', 'residential']

    if (parts[0] === 'single')
      tags.push('single person', 'compact')
    if (parts[0] === 'two')
      tags.push('two person', 'medium')
    if (parts[0] === 'three')
      tags.push('three person', 'spacious')
    if (parts[0] === 'multi')
      tags.push('multi person', 'large')

    return tags
  }

  /**
   * Get preview image filename
   */
  private getPreviewImage(configName: string): string {
    // Map config names to preview images with new naming
    const imageMap: Record<string, string> = {
      'single-person-1': 'single-person-1.jpg',
      'single-person-2': 'single-person-2.jpg',
      'single-person-3': 'single-person-3.jpg',
      'two-person-1': 'two-person-1.jpg',
      'two-person-2': 'two-person-2.jpg',
      'two-person-3': 'two-person-3.jpg',
      'three-person-1': 'three-person-1.jpg',
      'three-person-2': 'three-person-2.jpg',
      'three-person-3': 'three-person-3.jpg',
      'multi-person-1': 'multi-person-1.jpg',
      'multi-person-2': 'multi-person-2.jpg',
      'multi-person-3': 'multi-person-3.jpg',
    }

    return imageMap[configName] || 'single-person-1.jpg'
  }

  /**
   * Get estimated completion time
   */
  private getEstimatedTime(configName: string): number {
    const parts = configName.split('-')
    const personCount = parts[0]

    const timeMap: Record<string, number> = {
      single: 30,
      two: 45,
      three: 60,
      multi: 90,
    }

    return timeMap[personCount] || 45
  }
}

// Export singleton instance
export const templateService = new TemplateServiceImpl()
