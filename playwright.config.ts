/**
 * Playwright Configuration File
 * Configured according to Playwright best practices
 * @see https://playwright.dev/docs/test-configuration
 */

import process from 'node:process'
import { defineConfig, devices } from '@playwright/test'

// Environment variable helper for type safety
const isCI = Boolean(process.env.CI)

export default defineConfig({
  // Add path aliases for TypeScript
  webServer: {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: !isCI,
  },
  testDir: './tests/e2e', // Point to e2e tests
  // globalSetup: './tests/global-setup.ts', // Keep commented unless Playwright-specific setup is needed
  fullyParallel: true,
  forbidOnly: isCI,
  retries: isCI ? 2 : 0,
  workers: isCI ? 1 : undefined,
  reporter: [['html'], ['list']],
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'on-first-retry',
  },
  projects: [
    // Remove 'unit' project, handled by Vitest
    // Remove 'setup' project, seems Vitest-related
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],

      },
    },
    {
      name: 'firefox',
      use: {
        ...devices['Desktop Firefox'],

      },
    },
    {
      name: 'webkit',
      use: {
        ...devices['Desktop Safari'],

      },
    },
    {
      name: 'Mobile Chrome',
      use: {
        ...devices['Pixel 5'],

      },
    },
    {
      name: 'Mobile Safari',
      use: {
        ...devices['iPhone 12'],

      },
    },
  ],
})
