import type { BaseStyleProperties, CircleProperties, RectangleProperties } from '@/types/core/models'
import type { ShapeElement as ShapeModel } from '@/types/core/elementDefinitions'
import type { LoggerService } from '@/types/services/logging'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ShapeRepository } from '@/core/state/ShapeRepository'
import { ElementType } from '@/types/core/elementDefinitions'

function createRectangleShape(overrides: Partial<RectangleProperties & BaseStyleProperties> = {}): ShapeModel {
  return {
    id: 'shape1',
    type: ElementType.RECTANGLE,
    position: { x: 10, y: 20 },
    properties: {
      type: 'rectangle',
      width: 100,
      height: 50,
      cornerRadius: 0,
      fill: 'red',
      stroke: 'black',
      strokeWidth: 1,
      ...overrides,
    },
    metadata: { createdAt: 1000, updatedAt: 1000 },
  }
}

function createCircleShape(overrides: Partial<CircleProperties & BaseStyleProperties> = {}): ShapeModel {
  return {
    id: 'shape2',
    type: ElementType.CIRCLE,
    position: { x: 30, y: 40 },
    properties: {
      type: 'circle',
      radius: 25,
      fill: 'blue',
      stroke: 'black',
      strokeWidth: 1,
      ...overrides,
    },
    metadata: { createdAt: 2000, updatedAt: 2000 },
  }
}

describe('shapeRepository', () => {
  let repository: ShapeRepository
  let mockLogger: LoggerService
  let mockEventBus: any

  // Sample shape models for testing
  const sampleShape1: ShapeModel = {
    id: 'shape1',
    type: ElementType.RECTANGLE,
    position: { x: 10, y: 20 },
    properties: {
      type: 'rectangle',
      width: 100,
      height: 50,
      cornerRadius: 0,
      fill: 'red',
      stroke: 'black',
      strokeWidth: 1,
    },
    metadata: {
      createdAt: 1000,
      updatedAt: 1000,
    },
  }

  const sampleShape2: ShapeModel = {
    id: 'shape2',
    type: ElementType.ELLIPSE,
    position: { x: 50, y: 60 },
    properties: {
      type: 'ellipse',
      radiusX: 30,
      radiusY: 20,
      fill: 'blue',
      stroke: 'green',
      strokeWidth: 2,
    },
    metadata: {
      createdAt: 2000,
      updatedAt: 2000,
    },
  }

  beforeEach(() => {
    // Create a mock logger
    mockLogger = {
      info: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
    }

    // Create a mock event bus
    mockEventBus = {
      publish: vi.fn(),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
    }

    // Create a new repository instance before each test
    repository = new ShapeRepository(mockLogger, mockEventBus)
  })

  describe('repository Initialization', () => {
    it('should initialize with an empty repository', () => {
      expect(repository.getAll()).toHaveLength(0)
      expect(repository.getSelectedIds().size).toBe(0)
    })
  })

  describe('add and Retrieve Shapes', () => {
    it('should add a shape and retrieve it by ID', () => {
      const rectangleShape = createRectangleShape()
      repository.add(rectangleShape)
      const retrievedShape = repository.getById(rectangleShape.id)
      expect(retrievedShape).toEqual(rectangleShape)
    })

    it('should throw an error when retrieving a non-existent shape', () => {
      expect(() => repository.getById('nonexistent')).toThrow('Shape not found')
    })
  })

  describe('shape Selection', () => {
    it('should select a single shape by ID', () => {
      const rectangleShape = createRectangleShape()
      repository.add(rectangleShape)
      repository.setSelectedIds([rectangleShape.id])
      expect(repository.getSelectedIds().has(rectangleShape.id)).toBe(true)
    })

    it('should select multiple shapes', () => {
      const rectangleShape = createRectangleShape()
      const circleShape = createCircleShape()
      repository.add(rectangleShape)
      repository.add(circleShape)
      repository.setSelectedIds([rectangleShape.id, circleShape.id])
      expect(repository.getSelectedIds().size).toBe(2)
    })

    it('should clear selection', () => {
      const rectangleShape = createRectangleShape()
      repository.add(rectangleShape)
      repository.setSelectedIds([rectangleShape.id])
      repository.setSelectedIds([])
      expect(repository.getSelectedIds().size).toBe(0)
    })
  })

  describe('shape Removal', () => {
    it('should remove a shape by ID', () => {
      const rectangleShape = createRectangleShape()
      const circleShape = createCircleShape()
      repository.add(rectangleShape)
      repository.add(circleShape)
      repository.remove(rectangleShape.id)
      expect(repository.getAll()).toHaveLength(1)
      expect(repository.getById(circleShape.id)).toBeDefined()
    })

    it('should throw an error when removing a non-existent shape', () => {
      expect(() => repository.remove('nonexistent')).toThrow('Shape not found')
    })

    it('should log an info message when removing a shape', () => {
      const rectangleShape = createRectangleShape()
      repository.add(rectangleShape)
      repository.remove(rectangleShape.id)
      expect(mockLogger.info).toHaveBeenCalledWith(expect.stringContaining('Shape removed'))
    })
  })

  describe('shape Update', () => {
    it('should update an existing shape with partial properties', () => {
      const rectangleShape = createRectangleShape()
      repository.add(rectangleShape)
      const updateResult = repository.update(rectangleShape.id, {
        properties: {
          type: 'rectangle',
          width: 100,
          height: 50,
          cornerRadius: 0,
          fill: 'green',
          stroke: 'black',
          strokeWidth: 1,
        },
      })
      expect(updateResult).toBe(true)
      const retrievedShape = repository.getById(rectangleShape.id)
      expect(retrievedShape.properties.fill).toBe('green')
    })

    it('should update shape metadata', () => {
      const rectangleShape = createRectangleShape()
      repository.add(rectangleShape)
      const updateResult = repository.update(rectangleShape.id, {
        metadata: {
          updatedAt: 5000,
        },
      })
      expect(updateResult).toBe(true)
      const retrievedShape = repository.getById(rectangleShape.id)
      expect(retrievedShape.metadata.updatedAt).toBe(5000)
    })

    it('should return false when updating a non-existent shape', () => {
      const updateResult = repository.update('nonexistent', {
        properties: { type: 'rectangle', width: 10, height: 10, fill: 'blue' },
      })
      expect(updateResult).toBe(false)
    })

    it('should update shape position', () => {
      const rectangleShape = createRectangleShape()
      repository.add(rectangleShape)
      const updateResult = repository.update(rectangleShape.id, {
        position: { x: 100, y: 200 },
      })
      expect(updateResult).toBe(true)
      const retrievedShape = repository.getById(rectangleShape.id)
      expect(retrievedShape.position).toEqual({ x: 100, y: 200 })
    })
  })

  describe('repository Logging', () => {
    it('should log info when adding a shape', () => {
      const rectangleShape = createRectangleShape()
      repository.add(rectangleShape)
      expect(mockLogger.info).toHaveBeenCalledWith(expect.stringContaining('Shape added'))
    })

    it('should log info when removing a shape', () => {
      const rectangleShape = createRectangleShape()
      repository.add(rectangleShape)
      repository.remove(rectangleShape.id)
      expect(mockLogger.info).toHaveBeenCalledWith(expect.stringContaining('Shape removed'))
    })
  })

  describe('constructor', () => {
    it('should initialize with empty shapes and selection', () => {
      expect(repository.getAll()).toEqual([])
      expect(repository.getSelectedIds().size).toBe(0)
      expect(mockLogger.info).toHaveBeenCalledWith('[ShapeRepository] Initialized.')
    })
  })

  describe('add', () => {
    it('should add a valid shape to the repository', () => {
      repository.add(sampleShape1)
      expect(repository.getById(sampleShape1.id)).toEqual(sampleShape1)
      expect(repository.getAll()).toHaveLength(1)
    })

    it('should overwrite an existing shape with the same ID', () => {
      repository.add(sampleShape1)
      const updatedShape: ShapeModel = {
        ...sampleShape1,
        properties: {
          type: 'rectangle',
          width: 200,
          height: 100,
          cornerRadius: 5,
          fill: 'green',
          stroke: 'black',
          strokeWidth: 2,
        },
      }
      repository.add(updatedShape)
      expect(repository.getById(sampleShape1.id)).toEqual(updatedShape)
      expect(repository.getAll()).toHaveLength(1)
    })

    it('should log an error and not add a shape without an ID', () => {
      const invalidShape = { ...sampleShape1, id: undefined } as ShapeModel
      repository.add(invalidShape)
      expect(repository.getAll()).toHaveLength(0)
      expect(mockLogger.error).toHaveBeenCalled()
    })

    it('should log an error and not add a null or undefined shape', () => {
      repository.add(null as any)
      repository.add(undefined as any)
      expect(repository.getAll()).toHaveLength(0)
      expect(mockLogger.error).toHaveBeenCalledTimes(2)
    })
  })

  describe('getById', () => {
    it('should return the shape with the given ID if it exists', () => {
      repository.add(sampleShape1)
      repository.add(sampleShape2)

      expect(repository.getById(sampleShape1.id)).toEqual(sampleShape1)
      expect(repository.getById(sampleShape2.id)).toEqual(sampleShape2)
    })

    it('should return undefined if the shape does not exist', () => {
      expect(repository.getById('nonexistent')).toBeUndefined()

      repository.add(sampleShape1)
      expect(repository.getById('nonexistent')).toBeUndefined()
    })
  })

  describe('getAll', () => {
    it('should return an empty array when no shapes exist', () => {
      expect(repository.getAll()).toEqual([])
    })

    it('should return all shapes in the repository', () => {
      repository.add(sampleShape1)
      repository.add(sampleShape2)

      const allShapes = repository.getAll()
      expect(allShapes).toHaveLength(2)
      expect(allShapes).toContainEqual(sampleShape1)
      expect(allShapes).toContainEqual(sampleShape2)
    })

    it('should return a copy of the shapes array that does not affect the repository', () => {
      repository.add(sampleShape1)
      repository.add(sampleShape2)

      const allShapes = repository.getAll()
      allShapes.pop() // Remove a shape from the returned array

      // Repository should still have both shapes
      expect(repository.getAll()).toHaveLength(2)
    })
  })

  describe('update', () => {
    it('should update an existing shape with the provided changes', () => {
      repository.add(sampleShape1)

      const changes = {
        position: { x: 15, y: 25 },
        properties: { width: 120, height: 60 },
      }

      const result = repository.update(sampleShape1.id, changes)
      const updatedShape = repository.getById(sampleShape1.id)

      expect(result).toBe(true)
      expect(updatedShape?.position).toEqual(changes.position)
      expect(updatedShape?.properties).toEqual(changes.properties)
      expect(updatedShape?.id).toBe(sampleShape1.id) // ID should remain unchanged
    })

    it('should return false if the shape does not exist', () => {
      const result = repository.update('nonexistent', { position: { x: 0, y: 0 } })
      expect(result).toBe(false)
    })

    it('should update the metadata.updatedAt timestamp', () => {
      repository.add(sampleShape1)

      // Mock Date.now() to return a fixed timestamp
      const mockTimestamp = 3000
      const originalNow = Date.now
      Date.now = vi.fn(() => mockTimestamp)

      repository.update(sampleShape1.id, { position: { x: 15, y: 25 } })
      const updatedShape = repository.getById(sampleShape1.id)

      expect(updatedShape?.metadata?.updatedAt).toBe(mockTimestamp)

      // Restore original Date.now
      Date.now = originalNow
    })

    it('should merge metadata objects', () => {
      repository.add(sampleShape1)

      const newMetadata = {
        customField: 'custom value',
        // updatedAt will be overwritten by the update method
      }

      repository.update(sampleShape1.id, { metadata: newMetadata })
      const updatedShape = repository.getById(sampleShape1.id)

      expect(updatedShape?.metadata?.createdAt).toBe(sampleShape1.metadata?.createdAt)
      expect(updatedShape?.metadata?.customField).toBe(newMetadata.customField)
    })

    it('should merge properties objects', () => {
      repository.add(sampleShape1)

      const newProperties = {
        width: 120,
        customProp: 'custom value',
      }

      repository.update(sampleShape1.id, { properties: newProperties })
      const updatedShape = repository.getById(sampleShape1.id)

      expect(updatedShape?.properties.width).toBe(newProperties.width)
      expect(updatedShape?.properties.height).toBe(sampleShape1.properties.height)
      expect(updatedShape?.properties.customProp).toBe(newProperties.customProp)
    })
  })

  describe('remove', () => {
    it('should remove an existing shape and return true', () => {
      repository.add(sampleShape1)
      repository.add(sampleShape2)

      const result = repository.remove(sampleShape1.id)

      expect(result).toBe(true)
      expect(repository.getById(sampleShape1.id)).toBeUndefined()
      expect(repository.getAll()).toHaveLength(1)
      expect(repository.getAll()[0]).toEqual(sampleShape2)
    })

    it('should return false if the shape does not exist', () => {
      const result = repository.remove('nonexistent')
      expect(result).toBe(false)
    })

    it('should remove the shape from selection if it was selected', () => {
      repository.add(sampleShape1)
      repository.setSelectedIds([sampleShape1.id])

      repository.remove(sampleShape1.id)

      expect(repository.getSelectedIds().size).toBe(0)
    })
  })

  describe('clearAll', () => {
    it('should remove all shapes from the repository', () => {
      repository.add(sampleShape1)
      repository.add(sampleShape2)

      expect(repository.getAll()).toHaveLength(2)

      repository.clearAll()

      expect(repository.getAll()).toHaveLength(0)
    })

    it('should clear the selection', () => {
      repository.add(sampleShape1)
      repository.setSelectedIds([sampleShape1.id])

      repository.clearAll()

      expect(repository.getSelectedIds().size).toBe(0)
    })
  })

  describe('add different shape types', () => {
    it('should add a square shape', () => {
      const squareShape: ShapeModel = {
        id: 'shape3',
        type: ElementType.SQUARE,
        position: { x: 50, y: 50 },
        properties: {
          type: 'square',
          width: 100,
          height: 100,
          fill: 'green',
          stroke: 'black',
          strokeWidth: 2,
        },
      }
      repository.add(squareShape)
      expect(repository.getById(squareShape.id)).toEqual(squareShape)
    })

    it('should add an ellipse shape', () => {
      const ellipseShape: ShapeModel = {
        id: 'shape4',
        type: ElementType.ELLIPSE,
        position: { x: 100, y: 100 },
        properties: {
          type: 'ellipse',
          radiusX: 50,
          radiusY: 30,
          fill: 'purple',
          stroke: 'white',
          strokeWidth: 1,
        },
      }
      repository.add(ellipseShape)
      expect(repository.getById(ellipseShape.id)).toEqual(ellipseShape)
    })

    it('should add a polygon shape', () => {
      const polygonShape: ShapeModel = {
        id: 'shape5',
        type: ElementType.POLYGON,
        position: { x: 150, y: 150 },
        properties: {
          type: 'polygon',
          points: [
            { x: 0, y: 0 },
            { x: 50, y: 0 },
            { x: 25, y: 50 },
          ],
          fill: 'orange',
          stroke: 'brown',
          strokeWidth: 3,
        },
      }
      repository.add(polygonShape)
      expect(repository.getById(polygonShape.id)).toEqual(polygonShape)
    })

    it('should add a line shape', () => {
      const lineShape: ShapeModel = {
        id: 'shape6',
        type: ElementType.LINE,
        position: { x: 200, y: 200 },
        properties: {
          type: 'line',
          start: { x: 0, y: 0 },
          end: { x: 100, y: 100 },
          stroke: 'red',
          strokeWidth: 2,
        },
      }
      repository.add(lineShape)
      expect(repository.getById(lineShape.id)).toEqual(lineShape)
    })

    it('should add a path shape', () => {
      const pathShape: ShapeModel = {
        id: 'shape7',
        type: ElementType.PATH,
        position: { x: 250, y: 250 },
        properties: {
          type: 'path',
          points: [
            { x: 0, y: 0 },
            { x: 50, y: 50 },
            { x: 100, y: 0 },
          ],
          closed: true,
          fill: 'cyan',
          stroke: 'magenta',
          strokeWidth: 1,
        },
      }
      repository.add(pathShape)
      expect(repository.getById(pathShape.id)).toEqual(pathShape)
    })

    it('should add a svg-icon shape', () => {
      const svgIconShape: ShapeModel = {
        id: 'shape8',
        type: ElementType.SVG_ICON,
        position: { x: 300, y: 300 },
        properties: {
          type: 'svg-icon',
          svg: '<svg>...</svg>',
          width: 50,
          height: 50,
          fill: 'gray',
        },
      }
      repository.add(svgIconShape)
      expect(repository.getById(svgIconShape.id)).toEqual(svgIconShape)
    })

    it('should add a text-label shape', () => {
      const textLabelShape: ShapeModel = {
        id: 'shape9',
        type: ElementType.TEXT_LABEL,
        position: { x: 350, y: 350 },
        properties: {
          type: 'text-label',
          text: 'Hello, World!',
          fontFamily: 'Arial',
          fontSize: 16,
          fontWeight: 'bold',
          textAlign: 'center',
          fill: 'black',
        },
      }
      repository.add(textLabelShape)
      expect(repository.getById(textLabelShape.id)).toEqual(textLabelShape)
    })
  })

  describe('selection methods', () => {
    beforeEach(() => {
      // Add sample shapes for selection tests
      repository.add(sampleShape1)
      repository.add(sampleShape2)
    })

    describe('setSelectedIds', () => {
      it('should set the selection to the provided IDs', () => {
        repository.setSelectedIds([sampleShape1.id])

        expect(repository.getSelectedIds().size).toBe(1)
        expect(repository.isSelected(sampleShape1.id)).toBe(true)
        expect(repository.isSelected(sampleShape2.id)).toBe(false)
      })

      it('should replace any previous selection', () => {
        repository.setSelectedIds([sampleShape1.id])
        repository.setSelectedIds([sampleShape2.id])

        expect(repository.getSelectedIds().size).toBe(1)
        expect(repository.isSelected(sampleShape1.id)).toBe(false)
        expect(repository.isSelected(sampleShape2.id)).toBe(true)
      })

      it('should accept a Set of IDs', () => {
        const idSet = new Set([sampleShape1.id, sampleShape2.id])
        repository.setSelectedIds(idSet)

        expect(repository.getSelectedIds().size).toBe(2)
        expect(repository.isSelected(sampleShape1.id)).toBe(true)
        expect(repository.isSelected(sampleShape2.id)).toBe(true)
      })

      it('should handle empty arrays or sets', () => {
        repository.setSelectedIds([sampleShape1.id])
        repository.setSelectedIds([])

        expect(repository.getSelectedIds().size).toBe(0)
      })
    })

    describe('addToSelection', () => {
      it('should add the provided IDs to the current selection', () => {
        repository.setSelectedIds([sampleShape1.id])
        repository.addToSelection([sampleShape2.id])

        expect(repository.getSelectedIds().size).toBe(2)
        expect(repository.isSelected(sampleShape1.id)).toBe(true)
        expect(repository.isSelected(sampleShape2.id)).toBe(true)
      })

      it('should not duplicate IDs that are already selected', () => {
        repository.setSelectedIds([sampleShape1.id])
        repository.addToSelection([sampleShape1.id])

        expect(repository.getSelectedIds().size).toBe(1)
      })

      it('should accept a Set of IDs', () => {
        const idSet = new Set([sampleShape1.id, sampleShape2.id])
        repository.addToSelection(idSet)

        expect(repository.getSelectedIds().size).toBe(2)
      })
    })

    describe('removeFromSelection', () => {
      it('should remove the provided IDs from the current selection', () => {
        repository.setSelectedIds([sampleShape1.id, sampleShape2.id])
        repository.removeFromSelection([sampleShape1.id])

        expect(repository.getSelectedIds().size).toBe(1)
        expect(repository.isSelected(sampleShape1.id)).toBe(false)
        expect(repository.isSelected(sampleShape2.id)).toBe(true)
      })

      it('should do nothing for IDs that are not selected', () => {
        repository.setSelectedIds([sampleShape1.id])
        repository.removeFromSelection([sampleShape2.id])

        expect(repository.getSelectedIds().size).toBe(1)
        expect(repository.isSelected(sampleShape1.id)).toBe(true)
      })

      it('should accept a Set of IDs', () => {
        repository.setSelectedIds([sampleShape1.id, sampleShape2.id])

        const idSet = new Set([sampleShape1.id])
        repository.removeFromSelection(idSet)

        expect(repository.getSelectedIds().size).toBe(1)
        expect(repository.isSelected(sampleShape2.id)).toBe(true)
      })
    })

    describe('clearSelection', () => {
      it('should remove all IDs from the selection', () => {
        repository.setSelectedIds([sampleShape1.id, sampleShape2.id])
        repository.clearSelection()

        expect(repository.getSelectedIds().size).toBe(0)
      })

      it('should do nothing if the selection is already empty', () => {
        repository.clearSelection()
        expect(repository.getSelectedIds().size).toBe(0)
      })
    })

    describe('getSelectedIds', () => {
      it('should return a Set containing the selected IDs', () => {
        repository.setSelectedIds([sampleShape1.id, sampleShape2.id])

        const selectedIds = repository.getSelectedIds()
        expect(selectedIds.size).toBe(2)
        expect(selectedIds.has(sampleShape1.id)).toBe(true)
        expect(selectedIds.has(sampleShape2.id)).toBe(true)
      })

      it('should return a copy of the selection that does not affect the repository', () => {
        repository.setSelectedIds([sampleShape1.id, sampleShape2.id])

        const selectedIds = repository.getSelectedIds()
        selectedIds.delete(sampleShape1.id)

        // Repository's selection should remain unchanged
        expect(repository.getSelectedIds().size).toBe(2)
      })
    })

    describe('isSelected', () => {
      it('should return true if the ID is in the current selection', () => {
        repository.setSelectedIds([sampleShape1.id])

        expect(repository.isSelected(sampleShape1.id)).toBe(true)
        expect(repository.isSelected(sampleShape2.id)).toBe(false)
      })

      it('should return false for non-existent shape', () => {
        expect(repository.isSelected('nonexistent')).toBe(false)
      })
    })
  })
})
