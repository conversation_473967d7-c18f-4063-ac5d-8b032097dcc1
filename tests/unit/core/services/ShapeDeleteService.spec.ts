/**
 * @file ShapeDeleteService.spec.ts
 * @description Unit tests for ShapeDeleteService class
 */

import type { LoggerService } from '@/core/services/logger/LoggerService'
import type { ShapeRepository } from '@/core/state/ShapeRepository'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ShapeDeleteService } from '@/core/services/ShapeDeleteService'
import { Point } from '@/types/core/element/geometry/point'
import { ElementType } from '@/types/core/shape-type'

describe('shapeDeleteService', () => {
  let deleteService: ShapeDeleteService
  let mockRepository: ShapeRepository
  let mockLogger: LoggerService

  beforeEach(() => {
    // Create mock repository
    mockRepository = {
      remove: vi.fn().mockReturnValue(true),
      getById: vi.fn(),
      getAll: vi.fn().mockReturnValue([]),
      getSelectedIds: vi.fn().mockReturnValue(new Set()),
      clearSelection: vi.fn(),
    } as unknown as ShapeRepository

    // Create mock logger
    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    } as unknown as LoggerService

    // Create service instance
    deleteService = new ShapeDeleteService(mockRepository, mockLogger)
  })

  describe('deleteShape', () => {
    it('should delete a shape by ID', () => {
      // Arrange
      const shapeId = 'rect-1'
      const mockShape = {
        id: shapeId,
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
      };
      (mockRepository.getById as any).mockReturnValue(mockShape)

      // Act
      const result = deleteService.deleteShape(shapeId)

      // Assert
      expect(result).toBe(true)
      expect(mockRepository.remove).toHaveBeenCalledWith(shapeId)
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining(`Deleted shape ${shapeId}`),
      )
    })

    it('should return false if shape does not exist', () => {
      // Arrange
      const shapeId = 'non-existent';
      (mockRepository.getById as any).mockReturnValue(undefined)

      // Act
      const result = deleteService.deleteShape(shapeId)

      // Assert
      expect(result).toBe(false)
      expect(mockRepository.remove).not.toHaveBeenCalled()
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining(`Shape ${shapeId} not found`),
      )
    })

    it('should return false if repository fails to remove the shape', () => {
      // Arrange
      const shapeId = 'rect-1'
      const mockShape = {
        id: shapeId,
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
      };
      (mockRepository.getById as any).mockReturnValue(mockShape);
      (mockRepository.remove as any).mockReturnValue(false)

      // Act
      const result = deleteService.deleteShape(shapeId)

      // Assert
      expect(result).toBe(false)
      expect(mockRepository.remove).toHaveBeenCalledWith(shapeId)
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(`Failed to delete shape ${shapeId}`),
      )
    })

    it('should handle errors during deletion', () => {
      // Arrange
      const shapeId = 'rect-1'
      const mockShape = {
        id: shapeId,
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
      };
      (mockRepository.getById as any).mockReturnValue(mockShape);
      (mockRepository.remove as any).mockImplementation(() => {
        throw new Error('Test error')
      })

      // Act
      const result = deleteService.deleteShape(shapeId)

      // Assert
      expect(result).toBe(false)
      expect(mockRepository.remove).toHaveBeenCalledWith(shapeId)
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(`Error deleting shape ${shapeId}`),
      )
    })
  })

  describe('deleteSelectedShapes', () => {
    it('should delete all selected shapes', () => {
      // Arrange
      const selectedIds = new Set(['rect-1', 'circle-1']);
      (mockRepository.getSelectedIds as any).mockReturnValue(selectedIds)

      // Act
      const result = deleteService.deleteSelectedShapes()

      // Assert
      expect(result).toBe(true)
      expect(mockRepository.remove).toHaveBeenCalledTimes(2)
      expect(mockRepository.remove).toHaveBeenCalledWith('rect-1')
      expect(mockRepository.remove).toHaveBeenCalledWith('circle-1')
      expect(mockRepository.clearSelection).toHaveBeenCalled()
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining('Deleted 2 selected shapes'),
      )
    })

    it('should return false if no shapes are selected', () => {
      // Arrange
      const selectedIds = new Set();
      (mockRepository.getSelectedIds as any).mockReturnValue(selectedIds)

      // Act
      const result = deleteService.deleteSelectedShapes()

      // Assert
      expect(result).toBe(false)
      expect(mockRepository.remove).not.toHaveBeenCalled()
      expect(mockRepository.clearSelection).not.toHaveBeenCalled()
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('No shapes selected for deletion'),
      )
    })

    it('should handle errors during deletion of selected shapes', () => {
      // Arrange
      const selectedIds = new Set(['rect-1', 'circle-1']);
      (mockRepository.getSelectedIds as any).mockReturnValue(selectedIds);
      (mockRepository.remove as any).mockImplementation(() => {
        throw new Error('Test error')
      })

      // Act
      const result = deleteService.deleteSelectedShapes()

      // Assert
      expect(result).toBe(false)
      expect(mockRepository.remove).toHaveBeenCalledTimes(1) // Should stop after first error
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error deleting selected shapes'),
      )
    })

    it('should continue deleting if some shapes fail to delete', () => {
      // Arrange
      const selectedIds = new Set(['rect-1', 'circle-1', 'polygon-1']);
      (mockRepository.getSelectedIds as any).mockReturnValue(selectedIds);
      (mockRepository.remove as any)
        .mockReturnValueOnce(true) // rect-1 succeeds
        .mockReturnValueOnce(false) // circle-1 fails
        .mockReturnValueOnce(true) // polygon-1 succeeds

      // Act
      const result = deleteService.deleteSelectedShapes()

      // Assert
      expect(result).toBe(true) // Overall success if at least one shape is deleted
      expect(mockRepository.remove).toHaveBeenCalledTimes(3)
      expect(mockRepository.clearSelection).toHaveBeenCalled()
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining('Deleted 2 selected shapes'),
      )
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('Failed to delete 1 selected shapes'),
      )
    })
  })

  describe('deleteAllShapes', () => {
    it('should delete all shapes', () => {
      // Arrange
      const allShapes = [
        {
          id: 'rect-1',
          type: ElementType.RECTANGLE,
        },
        {
          id: 'circle-1',
          type: ElementType.CIRCLE,
        },
      ];
      (mockRepository.getAll as any).mockReturnValue(allShapes)

      // Act
      const result = deleteService.deleteAllShapes()

      // Assert
      expect(result).toBe(true)
      expect(mockRepository.remove).toHaveBeenCalledTimes(2)
      expect(mockRepository.remove).toHaveBeenCalledWith('rect-1')
      expect(mockRepository.remove).toHaveBeenCalledWith('circle-1')
      expect(mockRepository.clearSelection).toHaveBeenCalled()
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining('Deleted all 2 shapes'),
      )
    })

    it('should return false if no shapes exist', () => {
      // Arrange
      const allShapes: any[] = [];
      (mockRepository.getAll as any).mockReturnValue(allShapes)

      // Act
      const result = deleteService.deleteAllShapes()

      // Assert
      expect(result).toBe(false)
      expect(mockRepository.remove).not.toHaveBeenCalled()
      expect(mockRepository.clearSelection).not.toHaveBeenCalled()
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('No shapes to delete'),
      )
    })

    it('should handle errors during deletion of all shapes', () => {
      // Arrange
      const allShapes = [
        {
          id: 'rect-1',
          type: ElementType.RECTANGLE,
        },
        {
          id: 'circle-1',
          type: ElementType.CIRCLE,
        },
      ];
      (mockRepository.getAll as any).mockReturnValue(allShapes);
      (mockRepository.remove as any).mockImplementation(() => {
        throw new Error('Test error')
      })

      // Act
      const result = deleteService.deleteAllShapes()

      // Assert
      expect(result).toBe(false)
      expect(mockRepository.remove).toHaveBeenCalledTimes(1) // Should stop after first error
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error deleting all shapes'),
      )
    })

    it('should continue deleting if some shapes fail to delete', () => {
      // Arrange
      const allShapes = [
        {
          id: 'rect-1',
          type: ElementType.RECTANGLE,
        },
        {
          id: 'circle-1',
          type: ElementType.CIRCLE,
        },
        {
          id: 'polygon-1',
          type: ElementType.POLYGON,
        },
      ];
      (mockRepository.getAll as any).mockReturnValue(allShapes);
      (mockRepository.remove as any)
        .mockReturnValueOnce(true) // rect-1 succeeds
        .mockReturnValueOnce(false) // circle-1 fails
        .mockReturnValueOnce(true) // polygon-1 succeeds

      // Act
      const result = deleteService.deleteAllShapes()

      // Assert
      expect(result).toBe(true) // Overall success if at least one shape is deleted
      expect(mockRepository.remove).toHaveBeenCalledTimes(3)
      expect(mockRepository.clearSelection).toHaveBeenCalled()
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining('Deleted all 2 shapes'),
      )
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('Failed to delete 1 shapes'),
      )
    })
  })
})
