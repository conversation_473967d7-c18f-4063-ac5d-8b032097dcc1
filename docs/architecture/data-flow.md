# Data Flow

This document describes the data flow within the RenoPilot.JS.Shapes2 application.

## Overview

The primary data flow revolves around user interactions, state management, service layers, and the core logic responsible for shape manipulation and computation.

## Main Data Flow Diagram

A visual representation of the data flow can be found in the [System Architecture Overview](./overview.md#data-flow-diagram).

```mermaid
flowchart LR
    User[User Interaction] --> Components[UI Components src/components]
    Components --> Handlers[Event Handlers / Hooks src/hooks]
    Handlers -->|Action Dispatch / Service Call| StoreActions[Zustand Actions src/store]
    StoreActions --> Stores[Zustand Stores shapesStore, uiStore]
    Handlers -->|Service Call| Services[Service Layer src/services]
    Services --> CoreLogic[Core Logic src/core/CoreCoordinator]
    CoreLogic --> ShapeRepo[ShapeRepository src/core/state]
    ShapeRepo --> Stores
    Stores -->|State Update| Components
    Services --> EventBus[Event Bus appEventBus]
    EventBus --> Services
    EventBus --> Stores
```

## Detailed Flow Descriptions

(This section can be expanded with detailed textual descriptions of specific data flow scenarios, e.g., shape creation, property update, undo/redo actions, etc.)

### 1. User Interaction to State Update
1.  **User Action:** The user interacts with a UI element (e.g., clicks a button on the `Toolbar`, drags on the `Canvas`).
2.  **Component Handler:** The corresponding React component's event handler or a custom hook is triggered.
3.  **Service Call / Store Action:**
    *   The handler might directly call an action on a Zustand store (e.g., `uiStore.setSelectedTool()`).
    *   Alternatively, it might call a method on a service (e.g., `elementCreationService.startShape()`).
4.  **Service Logic (if applicable):** Services encapsulate business logic. They might interact with:
    *   Core logic modules (`src/core`) for complex operations.
    *   Other services.
    *   Zustand stores to dispatch further actions or read data.
    *   The application event bus (`appEventBus`) to publish events.
5.  **Core Logic (if applicable):** Core modules perform fundamental tasks (e.g., `ElementFactory` creates a shape, `ComputeFacade` calculates properties).
    *   The `ShapeRepository` might be used to store or retrieve element data internally within the core.
6.  **State Mutation:** Zustand store actions update the application state immutably.
7.  **Component Re-render:** Components subscribed to the modified parts of the Zustand store automatically re-render to reflect the new state.

### 2. Data Flow for Undo/Redo
1.  **User Action:** User clicks undo/redo button.
2.  **Component Handler:** Calls `shapesStore.undo()` or `shapesStore.redo()`.
3.  **Zundo Middleware:** The `zundo` middleware in `shapesStore` handles reverting to or re-applying a previous state snapshot of the shapes.
4.  **State Update:** `shapesStore` state is updated to the past/future state.
5.  **Component Re-render:** Canvas and other relevant components re-render.

### 3. Event-Driven Data Flow (via `appEventBus`)
1.  **Event Publishing:** A service or module publishes an event to `appEventBus` (e.g., `appEventBus.publish('shapeSelected', { id })`).
2.  **Event Subscription:** Other services or store listeners subscribed to this event type receive the event and its payload.
3.  **Reactive Logic:** Subscribers execute their logic in response to the event, which might involve updating their own state, calling other services, or dispatching store actions.

(Further specific flows will be added as the system evolves and more detailed analysis is performed.)