/**
 * Bounding Box Calculation Strategies Index
 *
 * @remarks
 * This module serves as a barrel export for all concrete strategy implementations
 * related to bounding box calculation. It re-exports the `BoundingBoxCalculatorStrategy`
 * interface and the `BoundingBox` type (from `@/types/core/compute`), and then
 * exports all specific bounding box calculation strategy classes defined within this
 * directory (e.g., {@link ArcBoundingBoxStrategy}, {@link RectangleBoundingBoxStrategy}).
 *
 * This allows for a centralized import point for accessing various bounding box
 * calculation strategies.
 *
 * @module core/compute/strategies/bounding-box/index
 */

// Export concrete strategy implementations from the current directory
export * from './ArcBoundingBoxStrategy'
export * from './CubicBoundingBoxStrategy'
export * from './EllipseBoundingBoxStrategy'
export * from './ImageBoundingBoxStrategy'
export * from './LineBoundingBoxStrategy'
export * from './PolygonBoundingBoxStrategy'
export * from './PolylineBoundingBoxStrategy'
export * from './QuadraticBoundingBoxStrategy'
export * from './RectangleBoundingBoxStrategy'
export * from './TextBoundingBoxStrategy'
// Export interface first for better code organization
// This typically exports BoundingBoxCalculatorStrategy and BoundingBox type from compute types
export * from '@/types/core/compute' // Ensure this exports BoundingBoxCalculatorStrategy and the BoundingBox interface used by strategies
