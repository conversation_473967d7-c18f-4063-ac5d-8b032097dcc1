import { beforeEach, describe, expect, it, vi } from 'vitest'
import { WallpaperMaterialStrategy } from '@/core/compute/strategies/material/WallpaperMaterialStrategy'
import { CoreError } from '@/services/errors'

describe('wallpaperMaterialStrategy', () => {
  let strategy: WallpaperMaterialStrategy
  let mockElement: any

  beforeEach(() => {
    strategy = new WallpaperMaterialStrategy()

    // 创建模拟元素
    mockElement = {
      id: 'test-wall',
      type: 'wall',
      getSubType: () => 'rectangle',

      getArea: vi.fn().mockReturnValue(20), // 20平方米
      getPerimeter: vi.fn().mockReturnValue(15.6), // 15.6米周长
      getBoundingBox: vi.fn().mockReturnValue({
        x: 0,
        y: 0,
        width: 5,
        height: 2.8,
      }),
      height: 2.8, // 墙高2.8米
      width: 5, // 墙宽5米
    }
  })

  it('should be defined', () => {
    expect(strategy).toBeDefined()
  })

  it('should return the correct shape type', () => {
    expect(strategy.getElementType()).toBe('rectangle')
  })

  it('should calculate wallpaper amount correctly with default options', () => {
    const result = strategy.calculateMaterialAmount(mockElement, 'wallpaper')

    expect(result).toBeDefined()
    expect(result.amount).toBe(79.2) // 壁纸总面积
    expect(result.unit).toBe('平方米')
    expect(result.amountWithWastage).toBeGreaterThan(20) // 考虑损耗
  })

  it('should calculate wallpaper amount correctly with pattern repeat', () => {
    const result = strategy.calculateMaterialAmount(mockElement, 'wallpaper', {
      patternRepeat: 0.5, // 图案重复高度0.5米
      rollWidth: 0.53, // 壁纸宽度0.53米
      rollLength: 10, // 壁纸长度10米
      wastageRate: 0.15, // 15%损耗率
    })

    expect(result).toBeDefined()
    expect(result.amount).toBe(82.5) // 壁纸总面积
    expect(result.unit).toBe('平方米')
    expect(result.amountWithWastage).toBeGreaterThan(20) // 考虑损耗

    expect(result.unitCount).toBeGreaterThan(0) // 需要的卷数
  })

  it('should calculate wallpaper amount correctly with seam allowance', () => {
    const result = strategy.calculateMaterialAmount(mockElement, 'wallpaper', {
      rollWidth: 0.53, // 壁纸宽度0.53米
      rollLength: 10, // 壁纸长度10米
      wastageRate: 0.1, // 10%损耗率
      seamAllowance: 0.03, // 3厘米接缝余量
    })

    expect(result).toBeDefined()
    expect(result.amount).toBe(79.2) // 壁纸总面积
    expect(result.unit).toBe('平方米')
    expect(result.amountWithWastage).toBeGreaterThan(20) // 考虑损耗

    expect(result.unitCount).toBeGreaterThan(0) // 需要的卷数
  })

  it('should throw error for invalid element type', () => {
    const invalidElement = { ...mockElement, type: 'invalid' }

    expect(() => {
      strategy.calculateMaterialAmount(invalidElement, 'standard')
    }).toThrow(CoreError)
  })

  it('should throw error for unsupported material type', () => {
    expect(() => {
      strategy.calculateMaterialAmount(mockElement, 'unsupported')
    }).toThrow(CoreError)
  })

  it('should handle different wall heights correctly', () => {
    // 创建不同高度的墙
    const tallWall = {
      ...mockElement,
      height: 3.5, // 3.5米高
      getBoundingBox: vi.fn().mockReturnValue({
        x: 0,
        y: 0,
        width: 5,
        height: 3.5,
      }),
      compute: {
        getArea: vi.fn().mockReturnValue(5 * 3.5), // 17.5平方米
      },
      getArea: vi.fn().mockReturnValue(5 * 3.5), // 17.5平方米
      getPerimeter: vi.fn().mockReturnValue(17), // 17米周长
    }

    const result = strategy.calculateMaterialAmount(tallWall, 'wallpaper', {
      rollWidth: 0.53,
      rollLength: 10,
      wastageRate: 0.1,
    })

    expect(result).toBeDefined()
    expect(result.amount).toBe(87.12) // 壁纸总面积
    expect(result.unitCount).toBeGreaterThan(0) // 需要的卷数
  })
})
