/**
 * @file ElementFactory.additional3.spec.ts
 * @description Additional unit tests for ElementFactory to improve coverage
 */

import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ElementFactory } from '@/core/factory/ElementFactory'
import { Point } from '@/types/core/element/geometry/point'
import { ElementType } from '@/types/core/shape'

// Mock creators
const mockRectangleCreator = {
  create: vi.fn(),
  createDefault: vi.fn(),
}

const mockCircleCreator = {
  create: vi.fn(),
  createDefault: vi.fn(),
}

const mockLineCreator = {
  create: vi.fn(),
  createDefault: vi.fn(),
}

const mockPolygonCreator = {
  create: vi.fn(),
  createDefault: vi.fn(),
}

const mockEllipseCreator = {
  create: vi.fn(),
  createDefault: vi.fn(),
}

// Mock path factories
const mockArcPathFactory = vi.fn()
const mockBezierPathFactory = vi.fn()
const mockQuadraticPathFactory = vi.fn()

describe('elementFactory - Additional Tests 3', () => {
  let factory: ElementFactory

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()

    // Create mock return values
    mockRectangleCreator.create.mockResolvedValue({ id: 'rect1', type: ElementType.RECTANGLE })
    mockRectangleCreator.createDefault.mockResolvedValue({ id: 'rect1', type: ElementType.RECTANGLE })

    mockCircleCreator.create.mockResolvedValue({ id: 'circle1', type: ElementType.CIRCLE })
    mockCircleCreator.createDefault.mockResolvedValue({ id: 'circle1', type: ElementType.CIRCLE })

    mockLineCreator.create.mockResolvedValue({ id: 'line1', type: ElementType.LINE })
    mockLineCreator.createDefault.mockResolvedValue({ id: 'line1', type: ElementType.LINE })

    mockPolygonCreator.create.mockResolvedValue({ id: 'polygon1', type: ElementType.POLYGON })
    mockPolygonCreator.createDefault.mockResolvedValue({ id: 'polygon1', type: ElementType.POLYGON })

    mockEllipseCreator.create.mockResolvedValue({ id: 'ellipse1', type: ElementType.ELLIPSE })
    mockEllipseCreator.createDefault.mockResolvedValue({ id: 'ellipse1', type: ElementType.ELLIPSE })

    mockArcPathFactory.mockResolvedValue({ id: 'arc1', type: 'arc' })
    mockBezierPathFactory.mockResolvedValue({ id: 'bezier1', type: 'bezier' })
    mockQuadraticPathFactory.mockResolvedValue({ id: 'quadratic1', type: 'quadratic' })

    // Create factory with mocked creators
    factory = new ElementFactory();

    // Register mock creators
    (factory as any).creators = new Map();
    (factory as any).creators.set(ElementType.RECTANGLE, mockRectangleCreator);
    (factory as any).creators.set(ElementType.CIRCLE, mockCircleCreator);
    (factory as any).creators.set(ElementType.LINE, mockLineCreator);
    (factory as any).creators.set(ElementType.POLYGON, mockPolygonCreator);
    (factory as any).creators.set(ElementType.ELLIPSE, mockEllipseCreator);

    // Register mock path factories
    (factory as any).pathFactories = new Map();
    (factory as any).pathFactories.set('arc', mockArcPathFactory);
    (factory as any).pathFactories.set('bezier', mockBezierPathFactory);
    (factory as any).pathFactories.set('quadratic', mockQuadraticPathFactory)
  })

  describe('constructor and Default Creators', () => {
    it('should register default creators when instantiated', () => {
      // Create a new factory to test the constructor
      const newFactory = new ElementFactory()

      // Access private creators map
      const creators = (newFactory as any).creators

      // Verify default creators are registered
      expect(creators.has(ElementType.RECTANGLE)).toBe(true)
      expect(creators.has(ElementType.SQUARE)).toBe(true)
      expect(creators.has(ElementType.ELLIPSE)).toBe(true)
      expect(creators.has(ElementType.CIRCLE)).toBe(true)
      expect(creators.has(ElementType.POLYGON)).toBe(true)
      expect(creators.has(ElementType.TRIANGLE)).toBe(true)
      expect(creators.has(ElementType.HEXAGON)).toBe(true)
      expect(creators.has(ElementType.LINE)).toBe(true)
      expect(creators.has(ElementType.POLYLINE)).toBe(true)
    })

    it('should map alias types to appropriate creators', () => {
      // Create a new factory to test the constructor
      const newFactory = new ElementFactory()

      // Access private creators map
      const creators = (newFactory as any).creators

      // Verify aliases are mapped correctly
      expect(creators.get(ElementType.SQUARE)).toBe(creators.get(ElementType.RECTANGLE))
      expect(creators.get(ElementType.CIRCLE)).toBe(creators.get(ElementType.ELLIPSE))
      expect(creators.get(ElementType.TRIANGLE)).toBe(creators.get(ElementType.POLYGON))
      expect(creators.get(ElementType.HEXAGON)).toBe(creators.get(ElementType.POLYGON))
    })
  })

  describe('createDefaultShape', () => {
    it('should handle Point instance for position', async () => {
      // Arrange
      const id = 'rect-point'
      const position = new Point(10, 20)

      // Act
      await factory.createDefaultShape(ElementType.RECTANGLE, id, position)

      // Assert
      expect(mockRectangleCreator.createDefault).toHaveBeenCalledWith(id, position)
    })

    it('should handle object with x,y for position', async () => {
      // Arrange
      const id = 'rect-obj'
      const position = { x: 10, y: 20 }

      // Act
      await factory.createDefaultShape(ElementType.RECTANGLE, id, position)

      // Assert
      expect(mockRectangleCreator.createDefault).toHaveBeenCalledWith(
        id,
        expect.objectContaining({ x: 10, y: 20 }),
      )
    })

    it('should handle array [x,y] for position', async () => {
      // Arrange
      const id = 'rect-array'
      const position = [10, 20]

      // Act
      await factory.createDefaultShape(ElementType.RECTANGLE, id, position)

      // Assert
      expect(mockRectangleCreator.createDefault).toHaveBeenCalledWith(
        id,
        expect.objectContaining({ x: 10, y: 20 }),
      )
    })
  })

  describe('convenience Methods with Single Parameter Object', () => {
    it('should handle createRectangle with single parameter object', async () => {
      // Arrange
      const params = {
        id: 'rect-single',
        position: { x: 10, y: 20 },
        width: 100,
        height: 50,
        fill: '#ff0000',
      }

      // Act
      await factory.createRectangle(params)

      // Assert
      expect(mockRectangleCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'rect-single',
          type: ElementType.RECTANGLE,
          position: { x: 10, y: 20 },
          width: 100,
          height: 50,
          fill: '#ff0000',
        }),
      )
    })

    it('should handle createSquare with single parameter object', async () => {
      // Arrange
      const params = {
        id: 'square-single',
        position: { x: 20, y: 20 },
        size: 100,
        fill: '#00ff00',
      }

      // Act
      await factory.createSquare(params)

      // Assert
      expect(mockRectangleCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'square-single',
          type: ElementType.SQUARE,
          position: { x: 20, y: 20 },
          width: 100,
          height: 100,
          fill: '#00ff00',
        }),
      )
    })

    it('should handle createEllipse with single parameter object', async () => {
      // Arrange
      const params = {
        id: 'ellipse-single',
        position: { x: 30, y: 30 },
        radiusX: 100,
        radiusY: 50,
        fill: '#0000ff',
      }

      // Act
      await factory.createEllipse(params)

      // Assert
      expect(mockEllipseCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'ellipse-single',
          type: ElementType.ELLIPSE,
          position: { x: 30, y: 30 },
          radiusX: 100,
          radiusY: 50,
          fill: '#0000ff',
        }),
      )
    })

    it('should handle createCircle with single parameter object', async () => {
      // Arrange
      const params = {
        id: 'circle-single',
        position: { x: 40, y: 40 },
        radius: 50,
        fill: '#ffff00',
      }

      // Act
      await factory.createCircle(params)

      // Assert
      expect(mockCircleCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'circle-single',
          type: ElementType.CIRCLE,
          position: { x: 40, y: 40 },
          radius: 50,
          fill: '#ffff00',
        }),
      )
    })

    it('should handle createLine with single parameter object', async () => {
      // Arrange
      const params = {
        id: 'line-single',
        start: { x: 0, y: 0 },
        end: { x: 100, y: 100 },
        stroke: '#ff00ff',
        strokeWidth: 2,
      }

      // Act
      await factory.createLine(params)

      // Assert
      expect(mockLineCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'line-single',
          type: ElementType.LINE,
          start: { x: 0, y: 0 },
          end: { x: 100, y: 100 },
          stroke: '#ff00ff',
          strokeWidth: 2,
        }),
      )
    })

    it('should handle createRegularPolygon with single parameter object', async () => {
      // Arrange
      const params = {
        id: 'regpoly-single',
        center: { x: 50, y: 50 },
        sides: 6,
        radius: 40,
        fill: '#00ffff',
      }

      // Act
      await factory.createRegularPolygon(params)

      // Assert
      expect(mockPolygonCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'regpoly-single',
          type: ElementType.POLYGON,
          center: { x: 50, y: 50 },
          sides: 6,
          radius: 40,
          points: [],
          fill: '#00ffff',
        }),
      )
    })

    it('should handle createCustomPolygon with single parameter object', async () => {
      // Arrange
      const params = {
        id: 'custpoly-single',
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 50, y: 100 },
        ],
        fill: '#ff00ff',
      }

      // Act
      await factory.createCustomPolygon(params)

      // Assert
      expect(mockPolygonCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'custpoly-single',
          type: ElementType.POLYGON,
          points: [
            { x: 0, y: 0 },
            { x: 100, y: 0 },
            { x: 50, y: 100 },
          ],
          fill: '#ff00ff',
        }),
      )
    })
  })

  describe('edge Cases', () => {
    it('should handle empty params for createShape', async () => {
      // Arrange
      const emptyParams = {}

      // Act
      await factory.createShape(ElementType.RECTANGLE, emptyParams)

      // Assert
      expect(mockRectangleCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          type: ElementType.RECTANGLE,
          id: expect.any(String),
        }),
      )
    })

    it('should handle empty params for createPath', async () => {
      // Arrange
      const emptyParams = {}

      // Act
      await factory.createPath('arc', emptyParams)

      // Assert
      expect(mockArcPathFactory).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'arc',
          id: expect.any(String),
        }),
      )
    })

    it('should handle null or undefined params for convenience methods', async () => {
      // Act & Assert - These should not throw errors
      await factory.createRectangle(null as any)
      expect(mockRectangleCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          type: ElementType.RECTANGLE,
          id: expect.any(String),
        }),
      )

      await factory.createCircle(undefined as any)
      expect(mockCircleCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          type: ElementType.CIRCLE,
          id: expect.any(String),
        }),
      )
    })
  })

  describe('overloaded Methods', () => {
    it('should handle createRectangle with separate parameters', async () => {
      // Act
      await factory.createRectangle('rect-sep', { x: 10, y: 20 }, 100, 50, { fill: '#ff0000' })

      // Assert
      expect(mockRectangleCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'rect-sep',
          type: ElementType.RECTANGLE,
          position: { x: 10, y: 20 },
          width: 100,
          height: 50,
          fill: '#ff0000',
        }),
      )
    })

    it('should handle createSquare with separate parameters', async () => {
      // Act
      await factory.createSquare('square-sep', { x: 20, y: 20 }, 100, { fill: '#00ff00' })

      // Assert
      expect(mockRectangleCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'square-sep',
          type: ElementType.SQUARE,
          position: { x: 20, y: 20 },
          width: 100,
          height: 100,
          fill: '#00ff00',
        }),
      )
    })

    it('should handle createEllipse with separate parameters', async () => {
      // Act
      await factory.createEllipse('ellipse-sep', { x: 30, y: 30 }, 100, 50, { fill: '#0000ff' })

      // Assert
      expect(mockEllipseCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'ellipse-sep',
          type: ElementType.ELLIPSE,
          position: { x: 30, y: 30 },
          radiusX: 100,
          radiusY: 50,
          fill: '#0000ff',
        }),
      )
    })

    it('should handle createCircle with separate parameters', async () => {
      // Act
      await factory.createCircle('circle-sep', { x: 40, y: 40 }, 50, { fill: '#ffff00' })

      // Assert
      expect(mockCircleCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'circle-sep',
          type: ElementType.CIRCLE,
          position: { x: 40, y: 40 },
          radius: 50,
          fill: '#ffff00',
        }),
      )
    })

    it('should handle createLine with separate parameters', async () => {
      // Act
      await factory.createLine('line-sep', { x: 0, y: 0 }, { x: 100, y: 100 }, { stroke: '#ff00ff' })

      // Assert
      expect(mockLineCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'line-sep',
          type: ElementType.LINE,
          start: { x: 0, y: 0 },
          end: { x: 100, y: 100 },
          stroke: '#ff00ff',
        }),
      )
    })

    it('should handle createRegularPolygon with separate parameters', async () => {
      // Act
      await factory.createRegularPolygon('regpoly-sep', { x: 50, y: 50 }, 6, 40, { fill: '#00ffff' })

      // Assert
      expect(mockPolygonCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'regpoly-sep',
          type: ElementType.POLYGON,
          center: { x: 50, y: 50 },
          sides: 6,
          radius: 40,
          points: [],
          fill: '#00ffff',
        }),
      )
    })

    it('should handle createCustomPolygon with separate parameters', async () => {
      // Act
      await factory.createCustomPolygon('custpoly-sep', [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 50, y: 100 },
      ], { fill: '#ff00ff' })

      // Assert
      expect(mockPolygonCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'custpoly-sep',
          type: ElementType.POLYGON,
          points: [
            { x: 0, y: 0 },
            { x: 100, y: 0 },
            { x: 50, y: 100 },
          ],
          fill: '#ff00ff',
        }),
      )
    })
  })
})
