/**
 * Core Type Definitions for Material Calculation Strategies
 *
 * @remarks
 * This module defines the interfaces and types necessary for implementing material
 * estimation and calculation strategies within the application. It follows the
 * strategy design pattern to allow for flexible and interchangeable algorithms
 * for determining material quantities based on element type, material properties,
 * and various calculation options.
 *
 * Key definitions include:
 * - {@link MaterialCalculationOptions}: An interface for specifying detailed parameters
 *   that influence material calculations, such as unit sizes, wastage rates,
 *   coverage for liquid materials, and specific options for different material types
 *   (e.g., curtains, paint, stone).
 * - {@link MaterialCalculationResult}: An interface structuring the output of material
 *   estimations, including total amounts, units, and wastage considerations.
 * - {@link MaterialCalculatorStrategy}: The core strategy interface that all concrete
 *   material calculation strategies must implement.
 *
 * @module types/core/compute/materialComputeTypes
 * @see {@link Element} from '@/types/core'
 * @see {@link ../computeInterfaces} for other computation strategy interfaces.
 */

import type { Element } from '@/types/core' // Assuming Element is from @/types/core not @/types/core/element

/**
 * Defines a comprehensive set of configurable parameters for material calculation operations.
 *
 * @remarks
 * This interface is utilized by {@link MaterialCalculatorStrategy} implementations to allow
 * for detailed customization of how material quantities are estimated for various
 * {@link Element | elements}. It supports different material characteristics and
 * application methods, including options for unit sizes, wastage, joints, liquid material
 * coverage, and specific parameters for materials like curtains, paint, stone, wallpaper, and wood.
 */
export interface MaterialCalculationOptions {
  /**
   * The size of a single unit of material, e.g., dimensions of a tile or plank.
   * Useful for calculating how many units are needed to cover an area.
   */
  unitSize?: { width: number, height: number }

  /**
   * The anticipated wastage rate for the material, expressed as a decimal (e.g., 0.1 for 10% wastage).
   * This accounts for cuts, errors, and unusable portions of material.
   */
  wastageRate?: number

  /**
   * A boolean indicating whether to include the width of joints (e.g., grout lines for tiles)
   * in the material calculation.
   */
  includeJoints?: boolean

  /**
   * The width of joints between material units, if `includeJoints` is true.
   * Measured in the same units as the element dimensions.
   */
  jointWidth?: number

  /**
   * The area that one liter (or other specified unit) of a liquid material (e.g., paint, sealant)
   * can cover. Typically measured in square meters per liter.
   */
  coverage?: number

  /**
   * The number of coats or layers of a liquid material to be applied.
   * Used in conjunction with `coverage` to determine total liquid material needed.
   */
  coats?: number

  // Curtain specific options
  /** Optional: Type of curtain (e.g., 'standard', 'roman', 'venetian', 'roller'). */
  curtainType?: string
  /** Optional: Width of the fabric roll in meters. */
  fabricWidth?: number
  /** Optional: Fullness factor for curtain pleats (e.g., 2.0 for double fullness). */
  fullness?: number
  /** Optional: Top hem allowance in meters. */
  hemTop?: number
  /** Optional: Bottom hem allowance in meters. */
  hemBottom?: number
  /** Optional: Extra height added to the curtain (e.g., for pooling) in meters. */
  extraHeight?: number
  /** Optional: Extra width added to each side of the window for curtain coverage, in meters. Total extra width will be this value * 2. */
  extraWidthPerSide?: number // Renamed for clarity from extraWidth
  /** Optional: Spacing between curtain hooks in meters. */
  hooksSpacing?: number
  /** Optional: Number of tieback pairs. */
  tiebackPairs?: number

  /** Optional: Number of units (e.g., tiles, planks) per box or package. */
  unitsPerBox?: number

  // Properties used by PaintMaterialStrategy
  /** Optional: Type of paint (e.g., 'latex', 'oil-based'). */
  paintType?: string
  /** Optional: Type of surface being painted (e.g., 'drywall', 'concrete', 'wood'). */
  surfaceType?: string
  /** Optional: Coverage of paint per liter (m²/L). Overrides default if provided. */
  coveragePerLiter?: number // Note: 'coverage' is already for general liquid material
  /** Optional: Amount of thinner needed per liter of paint (ratio). */
  thinnerPerLiter?: number
  /** Optional: Size of a standard paint can in liters. */
  canSize?: number

  // Properties used by StoneMaterialStrategy
  /** Optional: Type of stone (e.g., 'marble', 'granite'). */
  stoneType?: string
  /** Optional: Width of a single stone tile/slab in meters. (Consider renaming if unitSize.width can be used) */
  tileWidth?: number
  /** Optional: Length of a single stone tile/slab in meters. (Consider renaming if unitSize.height can be used) */
  tileLength?: number
  /** Optional: Thickness of the stone material in meters. */
  thickness?: number
  /** Optional: Whether pattern matching is required for the stone. */
  patternMatching?: boolean
  // jointWidth is already defined
  /** Optional: Density of the stone in kg/m³. */
  density?: number
  /** Optional: Amount of adhesive needed per square meter in kg. */
  adhesivePerSqm?: number
  /** Optional: Amount of grout needed per square meter in kg. */
  groutPerSqm?: number
  /** Optional: Amount of sealant needed per square meter in liters. */
  sealantPerSqm?: number

  // Properties used by WallpaperMaterialStrategy
  /** Optional: Width of a wallpaper roll in meters. */
  rollWidth?: number
  /** Optional: Length of a wallpaper roll in meters. */
  rollLength?: number
  /** Optional: Height of the pattern repeat for wallpaper in meters. */
  patternRepeat?: number
  /** Optional: Height of the room, used for wallpaper calculation, in meters. */
  roomHeight?: number
  /** Optional: Amount of adhesive needed per square meter for wallpaper in kg. */
  adhesivePerSqmWallpaper?: number // Renamed to avoid conflict with stone's adhesivePerSqm

  // Properties used by WoodMaterialStrategy
  /** Optional: Width of a single wood board/plank in meters. (Consider if unitSize.width can be used) */
  boardWidth?: number
  /** Optional: Length of a single wood board/plank in meters. (Consider if unitSize.height can be used) */
  boardLength?: number
  // thickness is already in MaterialCalculationOptions (from StoneMaterialStrategy, can be generic)
  /** Optional: Installation pattern for wood (e.g., 'straight', 'herringbone'). */
  installationPattern?: string
  /** Optional: Expansion gap allowance for wood, as a decimal (e.g., 0.02 for 2%). */
  expansion?: number
  // adhesivePerSqm is already in MaterialCalculationOptions (from StoneMaterialStrategy, can be generic)
  /** Optional: Amount of finish needed per square meter in liters. */
  finishPerSqm?: number
}

/**
 * Represents the result of a material calculation.
 *
 * @remarks
 * This interface structures the output of a material estimation, providing details
 * such as the total amount of material required, its unit of measure, the count of
 * individual units (if applicable), and amounts adjusted for wastage.
 */
export interface MaterialCalculationResult {
  /**
   * The total calculated amount of material needed (e.g., area in m², length in m, or quantity of items).
   * This is often the raw calculated amount before considering wastage or packaging.
   */
  amount: number

  /**
   * The unit of measure for the `amount` (e.g., "m^2", "m", "pieces", "liters").
   */
  unit: string

  /**
   * The number of discrete units of material needed, if applicable (e.g., number of tiles, planks).
   * This might be derived from `amount` and `unitSize`.
   */
  unitCount?: number

  /**
   * A string describing the type of unit, if `unitCount` is used (e.g., "tiles", "boards", "rolls").
   */
  unitType?: string

  /**
   * The total amount of material needed, including any specified wastage allowance.
   * Calculated as `amount * (1 + wastageRate)`.
   */
  amountWithWastage?: number

  /**
   * The estimated number of boxes or packages of material required, if applicable.
   * This would depend on how the material is sold (e.g., tiles per box).
   */
  boxes?: number

  /**
   * The number of coats applied, relevant for liquid materials like paint.
   * This is often an input option but can be included in results for clarity.
   */
  coats?: number
}

/**
 * Defines the contract for a material calculation strategy.
 *
 * @remarks
 * Any class implementing this interface is responsible for providing a specific
 * algorithm to estimate the quantity of a given material required for an {@link Element}.
 * Concrete strategies will typically be specialized for different {@link ElementType | element types}
 * and/or material types, utilizing the provided {@link MaterialCalculationOptions}.
 *
 * @see {@link Element} from '@/types/core'
 * @see {@link ElementType} from '@/types/core/elementDefinitions'
 * @see {@link MaterialCalculationOptions}
 * @see {@link MaterialCalculationResult}
 */
export interface MaterialCalculatorStrategy {
  /**
   * Calculates the amount of a specific material needed for a given element.
   *
   * @param element - The {@link Element} for which to calculate material requirements.
   * @param materialType - A string identifying the type of material being calculated
   *                       (e.g., "paint", "wood_flooring", "ceramic_tile"). This helps the
   *                       strategy adapt if it handles multiple materials for the same element type.
   * @param options - Optional {@link MaterialCalculationOptions} to customize the calculation
   *                  (e.g., wastage rates, unit sizes, number of coats).
   * @returns A {@link MaterialCalculationResult} object detailing the estimated material quantities.
   */
  calculateMaterialAmount: (element: Element, materialType: string, options?: MaterialCalculationOptions) => MaterialCalculationResult

  /**
   * Gets the specific element type (or types) that this strategy is designed to handle.
   *
   * @returns A string identifier (or an array of string identifiers) corresponding to
   *          one or more {@link ElementType | ElementTypes} that this strategy supports.
   * @remarks Consider returning `ElementType` or `ElementType[]` for enhanced type safety in future revisions.
   */
  getElementType: () => string | string[]
}
