import { beforeEach, describe, expect, it, vi } from 'vitest'
import { KeyboardService } from '@/services/keyboard-service/KeyboardService'
import { AppEventType } from '@/types/services/events'
import { KeyboardEventType } from '@/types/services/keyboard'

describe('keyboardService', () => {
  let keyboardService: KeyboardService
  let mockEventBus: any
  let mockLogger: any
  let mockDocument: Document
  let mockAddEventListener: any
  let mockRemoveEventListener: any

  beforeEach(() => {
    // Mock event bus
    mockEventBus = {
      publish: vi.fn(),
    }

    // Mock logger
    mockLogger = {
      info: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    }

    // Mock document event listeners
    mockAddEventListener = vi.fn()
    mockRemoveEventListener = vi.fn()

    // Create a mock document object
    mockDocument = {
      addEventListener: mockAddEventListener,
      removeEventListener: mockRemoveEventListener,
    } as unknown as Document

    // Create keyboard service with mocks
    keyboardService = new KeyboardService(mockEventBus, mockLogger, mockDocument)
  })

  describe('initialization and cleanup', () => {
    it('should initialize correctly', () => {
      keyboardService.initialize()

      expect(mockAddEventListener).toHaveBeenCalledWith('keydown', expect.any(Function), false)
      expect(mockAddEventListener).toHaveBeenCalledWith('keyup', expect.any(Function), false)
      expect(mockLogger.info).toHaveBeenCalledWith(expect.stringContaining('initialized'))
    })

    it('should clean up correctly', () => {
      keyboardService.initialize()
      keyboardService.cleanup()

      expect(mockRemoveEventListener).toHaveBeenCalledWith('keydown', expect.any(Function), false)
      expect(mockRemoveEventListener).toHaveBeenCalledWith('keyup', expect.any(Function), false)
      expect(mockLogger.info).toHaveBeenCalledWith(expect.stringContaining('cleaned up'))
    })

    it('should warn if trying to initialize an already initialized service', () => {
      keyboardService.initialize()
      keyboardService.initialize()

      expect(mockLogger.warn).toHaveBeenCalledWith(expect.stringContaining('already initialized'))
    })

    it('should warn if trying to clean up a service that is not initialized', () => {
      keyboardService.cleanup()

      expect(mockLogger.warn).toHaveBeenCalledWith(expect.stringContaining('not initialized'))
    })
  })

  describe('event handling', () => {
    it('should handle keydown events and publish them', () => {
      keyboardService.initialize()

      // Get the keydown handler that was registered
      const keydownHandler = mockAddEventListener.mock.calls.find(
        call => call[0] === 'keydown',
      )[1]

      // Create a mock keyboard event
      const mockEvent = {
        key: 'a',
        code: 'KeyA',
        altKey: false,
        ctrlKey: false,
        shiftKey: false,
        metaKey: false,
        preventDefault: vi.fn(),
        stopPropagation: vi.fn(),
      } as unknown as KeyboardEvent

      // Call the handler with the mock event
      keydownHandler(mockEvent)

      // Verify the event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        type: AppEventType.KEYBOARD_EVENT,
        payload: expect.objectContaining({
          eventType: KeyboardEventType.KEY_DOWN,
          key: 'a',
          code: 'KeyA',
          altKey: false,
          ctrlKey: false,
          shiftKey: false,
          metaKey: false,
        }),
      }))
    })

    it('should handle keyup events and publish them', () => {
      keyboardService.initialize()

      // Get the keyup handler that was registered
      const keyupHandler = mockAddEventListener.mock.calls.find(
        call => call[0] === 'keyup',
      )[1]

      // Create a mock keyboard event
      const mockEvent = {
        key: 'a',
        code: 'KeyA',
        altKey: false,
        ctrlKey: false,
        shiftKey: false,
        metaKey: false,
        preventDefault: vi.fn(),
        stopPropagation: vi.fn(),
      } as unknown as KeyboardEvent

      // Call the handler with the mock event
      keyupHandler(mockEvent)

      // Verify the event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        type: AppEventType.KEYBOARD_EVENT,
        payload: expect.objectContaining({
          eventType: KeyboardEventType.KEY_UP,
          key: 'a',
          code: 'KeyA',
        }),
      }))
    })
  })
})
