import { describe, expect, it } from 'vitest'
import {
  ComputeError,
  ElementNotFoundError,
  FactoryError,
  UnsupportedElementTypeError,
  UnsupportedOperationError,
  ValidationResultError,
  ValidatorError,
} from '@/services/system/error-service/classes'
import { CoreError } from '@/services/system/error-service/coreError'
import { ErrorSeverity, ErrorType } from '@/types/services/errors'

describe('error Classes', () => {
  describe('factoryError', () => {
    it('should create FactoryError with correct properties', () => {
      const message = 'Factory operation failed'
      const error = new FactoryError(message)

      expect(error).toBeInstanceOf(CoreError)
      expect(error).toBeInstanceOf(Error)
      expect(error.message).toBe(message)
      expect(error.name).toBe('FactoryError')
      expect(error.type).toBe(ErrorType.Runtime)
      expect(error.severity).toBe(ErrorSeverity.High)
    })

    it('should create FactoryError with context', () => {
      const message = 'Factory operation failed'
      const context = { component: 'TestFactory', operation: 'create' }
      const error = new FactoryError(message, context)

      expect(error.message).toBe(message)
      expect(error.context).toEqual(context)
      expect(error.name).toBe('FactoryError')
    })
  })

  describe('validatorError', () => {
    it('should create ValidatorError with correct properties', () => {
      const message = 'Validation failed'
      const error = new ValidatorError(message)

      expect(error).toBeInstanceOf(CoreError)
      expect(error.message).toBe(message)
      expect(error.name).toBe('ValidatorError')
      expect(error.type).toBe(ErrorType.Validation)
      expect(error.severity).toBe(ErrorSeverity.Medium)
    })

    it('should create ValidatorError with context', () => {
      const message = 'Validation failed'
      const context = { component: 'TestValidator', operation: 'validate' }
      const error = new ValidatorError(message, context)

      expect(error.message).toBe(message)
      expect(error.context).toEqual(context)
    })
  })

  describe('computeError', () => {
    it('should create ComputeError with correct properties', () => {
      const message = 'Computation failed'
      const error = new ComputeError(message)

      expect(error).toBeInstanceOf(CoreError)
      expect(error.message).toBe(message)
      expect(error.name).toBe('ComputeError')
      expect(error.type).toBe(ErrorType.Runtime)
      expect(error.severity).toBe(ErrorSeverity.Medium)
    })

    it('should create ComputeError with context', () => {
      const message = 'Computation failed'
      const context = { component: 'Calculator', operation: 'compute' }
      const error = new ComputeError(message, context)

      expect(error.message).toBe(message)
      expect(error.context).toEqual(context)
    })
  })

  describe('validationResultError', () => {
    it('should create ValidationResultError with validation errors', () => {
      const message = 'Validation result failed'
      const validationErrors = ['Error 1', 'Error 2']
      const error = new ValidationResultError(message, validationErrors)

      expect(error).toBeInstanceOf(CoreError)
      expect(error.message).toBe(message)
      expect(error.validationErrors).toEqual(validationErrors)
      expect(error.name).toBe('ValidationResultError')
      expect(error.type).toBe(ErrorType.Validation)
      expect(error.severity).toBe(ErrorSeverity.Medium)
    })

    it('should handle empty validation errors', () => {
      const message = 'Validation result failed'
      const validationErrors: unknown[] = []
      const error = new ValidationResultError(message, validationErrors)

      expect(error.validationErrors).toEqual([])
      expect(error.validationErrors).toHaveLength(0)
    })
  })

  describe('unsupportedElementTypeError', () => {
    it('should create UnsupportedElementTypeError with element type', () => {
      const elementType = 'UNKNOWN_TYPE'
      const error = new UnsupportedElementTypeError(elementType)

      expect(error).toBeInstanceOf(CoreError)
      expect(error.message).toBe(`Element type '${elementType}' is not supported`)
      expect(error.name).toBe('UnsupportedElementTypeError')
      expect(error.type).toBe(ErrorType.Runtime)
      expect(error.severity).toBe(ErrorSeverity.High)
    })

    it('should handle special characters in element type', () => {
      const elementType = 'TYPE_WITH-SPECIAL.CHARS@123'
      const error = new UnsupportedElementTypeError(elementType)

      expect(error.message).toBe(`Element type '${elementType}' is not supported`)
    })
  })

  describe('unsupportedOperationError', () => {
    it('should create UnsupportedOperationError with operation', () => {
      const operation = 'UNKNOWN_OPERATION'
      const error = new UnsupportedOperationError(operation)

      expect(error).toBeInstanceOf(CoreError)
      expect(error.message).toBe(`Operation '${operation}' is not supported`)
      expect(error.name).toBe('UnsupportedOperationError')
      expect(error.type).toBe(ErrorType.Runtime)
      expect(error.severity).toBe(ErrorSeverity.High)
    })

    it('should handle special characters in operation', () => {
      const operation = 'operation-with_special.chars@123'
      const error = new UnsupportedOperationError(operation)

      expect(error.message).toBe(`Operation '${operation}' is not supported`)
    })
  })

  describe('elementNotFoundError', () => {
    it('should create ElementNotFoundError with element ID', () => {
      const elementId = 'element-123'
      const error = new ElementNotFoundError(elementId)

      expect(error).toBeInstanceOf(CoreError)
      expect(error.message).toBe(`Element with ID '${elementId}' not found`)
      expect(error.name).toBe('ElementNotFoundError')
      expect(error.type).toBe(ErrorType.Runtime)
      expect(error.severity).toBe(ErrorSeverity.Medium)
    })

    it('should handle empty element ID', () => {
      const elementId = ''
      const error = new ElementNotFoundError(elementId)

      expect(error.message).toBe(`Element with ID '' not found`)
    })
  })

  describe('error inheritance and behavior', () => {
    it('should all extend CoreError', () => {
      const errors = [
        new FactoryError('test'),
        new ValidatorError('test'),
        new ComputeError('test'),
        new ValidationResultError('test', []),
        new UnsupportedElementTypeError('test'),
        new UnsupportedOperationError('test'),
        new ElementNotFoundError('test'),
      ]

      errors.forEach((error) => {
        expect(error).toBeInstanceOf(CoreError)
        expect(error).toBeInstanceOf(Error)
      })
    })

    it('should be catchable as Error', () => {
      const errors = [
        new FactoryError('test'),
        new ValidatorError('test'),
        new ComputeError('test'),
        new ValidationResultError('test', []),
        new UnsupportedElementTypeError('test'),
        new UnsupportedOperationError('test'),
        new ElementNotFoundError('test'),
      ]

      errors.forEach((error) => {
        try {
          throw error
        }
        catch (caught) {
          expect(caught).toBeInstanceOf(Error)
          expect(caught).toBeInstanceOf(CoreError)
        }
      })
    })

    it('should maintain stack traces', () => {
      const errors = [
        new FactoryError('test'),
        new ValidatorError('test'),
        new ComputeError('test'),
        new ValidationResultError('test', []),
        new UnsupportedElementTypeError('test'),
        new UnsupportedOperationError('test'),
        new ElementNotFoundError('test'),
      ]

      errors.forEach((error) => {
        expect(error.stack).toBeDefined()
        expect(error.stack).toContain(error.name)
      })
    })

    it('should have correct error types and severities', () => {
      const testCases = [
        { error: new FactoryError('test'), expectedType: ErrorType.Runtime, expectedSeverity: ErrorSeverity.High },
        { error: new ValidatorError('test'), expectedType: ErrorType.Validation, expectedSeverity: ErrorSeverity.Medium },
        { error: new ComputeError('test'), expectedType: ErrorType.Runtime, expectedSeverity: ErrorSeverity.Medium },
        { error: new ValidationResultError('test', []), expectedType: ErrorType.Validation, expectedSeverity: ErrorSeverity.Medium },
        { error: new UnsupportedElementTypeError('test'), expectedType: ErrorType.Runtime, expectedSeverity: ErrorSeverity.High },
        { error: new UnsupportedOperationError('test'), expectedType: ErrorType.Runtime, expectedSeverity: ErrorSeverity.High },
        { error: new ElementNotFoundError('test'), expectedType: ErrorType.Runtime, expectedSeverity: ErrorSeverity.Medium },
      ]

      testCases.forEach(({ error, expectedType, expectedSeverity }) => {
        expect(error.type).toBe(expectedType)
        expect(error.severity).toBe(expectedSeverity)
      })
    })

    it('should have unique names for each error type', () => {
      const errors = [
        new FactoryError('test'),
        new ValidatorError('test'),
        new ComputeError('test'),
        new ValidationResultError('test', []),
        new UnsupportedElementTypeError('test'),
        new UnsupportedOperationError('test'),
        new ElementNotFoundError('test'),
      ]

      const expectedNames = [
        'FactoryError',
        'ValidatorError',
        'ComputeError',
        'ValidationResultError',
        'UnsupportedElementTypeError',
        'UnsupportedOperationError',
        'ElementNotFoundError',
      ]

      errors.forEach((error, index) => {
        expect(error.name).toBe(expectedNames[index])
      })
    })
  })
})
