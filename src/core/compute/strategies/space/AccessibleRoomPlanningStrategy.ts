/**
 * Space Planning Strategy for Accessible Rooms
 *
 * @remarks
 * This strategy implements the {@link SpacePlanningStrategy} to evaluate and provide
 * recommendations for designing accessible room layouts. It focuses on aspects crucial
 * for users with mobility impairments, such as:
 * - **Wheelchair Accessibility**: Checks for adequate pathway widths, turning diameters,
 *   and accessible door widths based on predefined `ACCESSIBLE_STANDARDS`.
 * - **Ergonomics**: Placeholder for evaluating desk and chair setups (currently basic).
 * - **Assistive Features**: Placeholder for evaluating features like grab bars, handrail
 *   placement, and control heights.
 * - **Space Utilization**: Placeholder for calculating space efficiency (currently disabled
 *   due to missing dependencies).
 *
 * The strategy uses a local helper `getElementBoundingBoxClass` to obtain bounding boxes
 * for various element types, which is marked as a TODO for refactoring to use a
 * centralized BBox calculation service.
 *
 * Several methods like `calculateSpaceUtilization` and `checkPathwayWidth` are
 * temporarily disabled or simplified due to missing utility function dependencies
 * (e.g., `LineImplementationClass`, `calculateSpaceUtilization`, `checkPathwaysWidth`).
 *
 * **FIXME:** Many helper functions for geometric analysis (e.g., `identifyCorridors`,
 * `identifyTurningSpaces`) are currently placeholders and require proper implementation.
 *
 * @module core/compute/strategies/space/AccessibleRoomPlanningStrategy
 * @see {@link SpacePlanningStrategy}
 * @see {@link PathwayCheckResult}
 * @see {@link ErgonomicsEvaluationResult}
 */
import type {
  ErgonomicsEvaluationResult,
  PathwayCheckResult,
  SpacePlanningStrategy,
  SpaceType,
  // SpacePlanningStandard removed as it's not used
} from '@/types/core/compute' // Corrected path
// ElementPropertiesUnion is not found easily, will use a more direct approach for lineProperties
import type Point from '@/types/core/element/geometry/point' // Correct import for Point interface (IPoint)
// TODO: ROO_ASSIST - Revisit type errors for LineImplementationClass instantiation (around line 164) and Shape.Door type casting (around line 252).
// ElementPropertiesUnion import was also problematic.
import type {
  Element,
  Shape,
  ShapeElement, // Added for properties access
  // IPoint removed
} from '@/types/core/elementDefinitions'
// import { Line as LineImplementationClass } from '@/lib/utils/element/path/lineImplementation'; // Module not found
// import { CoreError, ErrorType } from '@/services/system/error-service'; // Commented out as unused for now
import { BoundingBoxClass } from '@/lib/utils/geometry' // BoundingBoxClass is exported from here
import {
  ElementType as CoreElementType, // Added for properties access
  // IPoint removed
} from '@/types/core/elementDefinitions'
// import {
//     calculateSpaceUtilization,
//     checkPathwaysWidth,
//     // Other space planning utils might be imported here if used
// } from '@/lib/utils/space/spacePlanning'; // Module not found

/**
 * Constants for accessible design standards.
 * @private
 */
const ACCESSIBLE_STANDARDS = {
  MIN_ACCESSIBLE_PATH_WIDTH: 0.9, // meters
  RECOMMENDED_MAIN_PATH_WIDTH: 1.2, // meters
  WHEELCHAIR_TURNING_DIAMETER: 1.5, // meters
  MIN_DOOR_WIDTH_ACCESSIBLE: 0.8, // meters
  ACCESSIBLE_DESK_MIN_HEIGHT: 0.7, // meters (example)
  ACCESSIBLE_DESK_KNEE_CLEARANCE_WIDTH: 0.8, // meters (example)
  ACCESSIBLE_DESK_KNEE_CLEARANCE_DEPTH: 0.5, // meters (example)
  HANDRAIL_HEIGHT_MIN: 0.85, // meters
  HANDRAIL_HEIGHT_MAX: 0.95, // meters
  SWITCH_REACH_MIN: 0.9, // meters from floor
  SWITCH_REACH_MAX: 1.2, // meters from floor
}

/**
 * Helper to get BoundingBoxClass from an Element.
 *
 * @remarks
 * TODO: This should ideally use a centralized BoundingBox calculation (e.g., via ComputeFacade)
 * or the element itself should provide its BoundingBox.
 *
 * @param element - The element to get the bounding box for.
 * @returns A BoundingBoxClass instance or null if the element type is not supported.
 * @private
 */
function getElementBoundingBoxClass(element: Element): BoundingBoxClass | null {
  const computableElement = element as unknown as { compute?: { boundingBox?: () => { x: number, y: number, width: number, height: number } } } // Use proper typing

  if (computableElement.compute && typeof computableElement.compute.boundingBox === 'function') {
    const bbox = computableElement.compute.boundingBox()
    if (bbox !== null && bbox !== undefined && typeof bbox.x === 'number' && typeof bbox.y === 'number' && typeof bbox.width === 'number' && typeof bbox.height === 'number') {
      return new BoundingBoxClass(bbox.x, bbox.y, bbox.width, bbox.height)
    }
  }

  // Fallback for simple shapes if no compute.boundingBox
  if (element.type === CoreElementType.RECTANGLE || element.type === CoreElementType.SQUARE) {
    const shape = element as unknown as Shape.Rectangle // Added unknown
    const shapeElement = element as ShapeElement // Cast to ShapeElement to access position
    if (shapeElement.position !== null && shapeElement.position !== undefined && typeof shape.width === 'number' && typeof shape.height === 'number') {
      // Assuming position is top-left for Shape.Rectangle as per ShapeElement.position doc
      const x = shapeElement.position.x
      const y = shapeElement.position.y
      return new BoundingBoxClass(x, y, shape.width, shape.height)
    }
  }
  else if (element.type === CoreElementType.CIRCLE) {
    const shape = element as unknown as Shape.Circle // Added unknown
    const shapeElement = element as ShapeElement // Cast to ShapeElement to access position
    if (shapeElement.position !== null && shapeElement.position !== undefined && typeof shape.radius === 'number') {
      const r = shape.radius
      // For Circle, ShapeElement.position is center. BoundingBoxClass expects top-left.
      return new BoundingBoxClass(shapeElement.position.x - r, shapeElement.position.y - r, r * 2, r * 2)
    }
  }
  else if (element.type === CoreElementType.ELLIPSE) {
    const shape = element as unknown as Shape.Ellipse // Added unknown
    const shapeElement = element as ShapeElement // Cast to ShapeElement to access position
    if (shapeElement.position !== null && shapeElement.position !== undefined && typeof shape.radiusX === 'number' && typeof shape.radiusY === 'number') {
      // For Ellipse, ShapeElement.position is center. BoundingBoxClass expects top-left.
      return new BoundingBoxClass(shapeElement.position.x - shape.radiusX, shapeElement.position.y - shape.radiusY, shape.radiusX * 2, shape.radiusY * 2)
    }
  }
  else if ((element.type === CoreElementType.POLYGON || element.type === CoreElementType.POLYLINE
    || element.type === CoreElementType.TRIANGLE || element.type === CoreElementType.HEXAGON) // Added other polygon types
    && (element as unknown as { properties?: { points?: Point[] } }).properties?.points) { // Added unknown
    const elementWithPoints = element as unknown as { properties?: { points?: Point[] } }
    const points = elementWithPoints.properties?.points
    if (points && points.length > 0) {
      return BoundingBoxClass.fromPointsArray(points) // Removed unnecessary type assertion
    }
  }

  console.warn(`[AccessibleRoomPlanningStrategy] getElementBoundingBoxClass: Could not derive BBox for type ${element.type} (ID: ${element.id}).`)
  return null // Ensure null is returned if no BBox can be derived
}

/**
 * Implements the {@link SpacePlanningStrategy} interface to provide planning and
 * evaluation for accessible room layouts.
 *
 * @implements SpacePlanningStrategy
 */
export class AccessibleRoomPlanningStrategy implements SpacePlanningStrategy {
  /**
   * Calculates how efficiently the accessible room space is being utilized.
   *
   * @returns A utilization percentage between 0 and 100.
   * @throws {CoreError} If the room boundary element is invalid.
   */
  public calculateSpaceUtilization(/* elements: Element[], roomBoundaryElement: Element */): number { // Params unused due to commented out body
    // const roomBBoxClass = getElementBoundingBoxClass(roomBoundaryElement);
    // if (!roomBBoxClass) {
    //   throw new CoreError( ErrorType.INVALID_PARAMETER, 'Room boundary must be an element from which a bounding box can be derived.');
    // }
    // // Ensure elements passed to calculateSpaceUtilization are appropriate (e.g., have BBoxes)
    // const elementsWithBBoxes = elements.map(el => getElementBoundingBoxClass(el)).filter(bbox => bbox !== null) as BoundingBoxClass[];
    // return calculateSpaceUtilization(elementsWithBBoxes, roomBBoxClass); // Pass BoundingBoxClass instances
    console.warn('[AccessibleRoomPlanningStrategy] calculateSpaceUtilization is temporarily disabled due to missing dependencies.')
    return 0
  }

  /**
   * Checks if pathways in the accessible room have sufficient width for wheelchair movement.
   *
   * @returns An array of pathway check results.
   * @throws {CoreError} If any parameters are invalid.
   */
  public checkPathwayWidth(/* elements: Element[], pathways: Path.Line[], minWidth: number */): PathwayCheckResult[] { // Params unused due to commented out body
    // if (!Array.isArray(elements)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Elements must be an array');
    // if (!Array.isArray(pathways)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Pathways must be an array');

    // const accessibleMinWidth = Math.max(minWidth, ACCESSIBLE_STANDARDS.MIN_ACCESSIBLE_PATH_WIDTH);

    // const formattedPathways = pathways.map((path, index) => {
    //     // Create an instance of LineImplementationClass
    //     // This assumes LineImplementationClass constructor takes (id, metadata, start, end, type, other optional props)
    //     // We might need to mock metadata or other properties if they are strictly required by the constructor
    //     const lineId = path.id ?? `pathway-line-${index}`;

    //     // The LineImplementationClass constructor expects an object compatible with LineProperties.
    //     // Path.Line should be compatible if we ensure 'type' is 'line'.
    //     const lineProperties = {
    //         ...path, // Spread existing Path.Line properties
    //         id: lineId,
    //         type: 'line' as const, // Ensure 'type' is the literal 'line'
    //         // Ensure all required properties for Line constructor's second argument are present
    //         // Path.Line already includes start, end, metadata, visible, locked, etc. from ShapeElement/Element
    //     };

    //     // The constructor of Line expects `Extract<ElementPropertiesUnion, { type: 'line' }>`
    //     // which effectively means an object that looks like LineProperties.
    //     // We cast to `any` here as a workaround if `ElementPropertiesUnion` is hard to import/define precisely here,
    //     // relying on the `lineProperties` object being structurally compatible.
    //     const lineInstance = new LineImplementationClass(
    //         lineId,
    //         lineProperties as any
    //     );
    //     return {
    //         id: path.id ?? `pathway-${index + 1}`,
    //         line: lineInstance
    //     };
    // });

    // const obstacleBBoxes = elements.map(el => getElementBoundingBoxClass(el)).filter(bbox => bbox !== null) as BoundingBoxClass[];
    // return checkPathwaysWidth(obstacleBBoxes, formattedPathways, accessibleMinWidth);
    console.warn('[AccessibleRoomPlanningStrategy] checkPathwayWidth is temporarily disabled due to missing dependencies.')
    return []
  }

  /**
   * Evaluates the ergonomics of the accessible room layout, particularly for desk and chair setups.
   *
   * @param _elements - The elements within the accessible room space.
   * @param deskElement - Optional desk element for workspace evaluation.
   * @param chairElement - Optional chair element for workspace evaluation.
   * @returns An ergonomics evaluation result with issues and recommendations.
   */
  public evaluateErgonomics(_elements: Element[], deskElement?: Element, chairElement?: Element): ErgonomicsEvaluationResult {
    console.warn('[AccessibleRoomPlanningStrategy] evaluateErgonomics not implemented.')
    // Basic checks for accessible desk/chair if provided
    const issues: string[] = []
    if (deskElement) {
      // Check desk height, knee clearance etc. (requires properties on deskElement)
      // e.g. if ((deskElement as any).properties?.height < ACCESSIBLE_STANDARDS.ACCESSIBLE_DESK_MIN_HEIGHT) issues.push("Desk height too low");
    }
    if (chairElement) {
      // Check chair properties for accessibility
    }
    return { isValid: issues.length === 0, issues, recommendations: ['Ensure desk provides adequate knee clearance (0.7m H x 0.8m W x 0.5m D).', 'Chair should be adjustable and stable.'] }
  }

  /**
   * Gets the type of space this strategy is designed for.
   *
   * @returns The space type identifier ('accessible').
   */
  public getSpaceType(): SpaceType {
    return 'accessible'
  }

  /**
   * Evaluates the wheelchair accessibility of the room layout.
   *
   * @param elements - The elements within the accessible room space.
   * @param roomBoundary - The element representing the room boundary.
   * @returns An object with validation result, issues, and recommendations.
   */
  public checkWheelchairAccessibility(elements: Element[], roomBoundary: Element): { isValid: boolean, issues: string[], recommendations: string[] } {
    const issues: string[] = []
    const recommendations: string[] = []
    const minTurningDiameter = ACCESSIBLE_STANDARDS.WHEELCHAIR_TURNING_DIAMETER
    const minDoorWidth = ACCESSIBLE_STANDARDS.MIN_DOOR_WIDTH_ACCESSIBLE

    const corridors = this.identifyCorridors(elements, roomBoundary)
    if (corridors.length === 0 && elements.length > 1) { // if there are elements, there should be paths
      issues.push('No clear pathways identified for wheelchair accessibility.')
    }
    else {
      corridors.forEach((corridor) => {
        if (corridor.width < ACCESSIBLE_STANDARDS.MIN_ACCESSIBLE_PATH_WIDTH) {
          issues.push(`Pathway (ID: ${corridor.id}) width of ${corridor.width.toFixed(2)}m is less than the recommended ${ACCESSIBLE_STANDARDS.MIN_ACCESSIBLE_PATH_WIDTH}m.`)
        }
        else if (corridor.isMain && corridor.width < ACCESSIBLE_STANDARDS.RECOMMENDED_MAIN_PATH_WIDTH) {
          issues.push(`Main pathway (ID: ${corridor.id}) width of ${corridor.width.toFixed(2)}m is less than the recommended ${ACCESSIBLE_STANDARDS.RECOMMENDED_MAIN_PATH_WIDTH}m.`)
        }
      })
    }

    const turningSpaces = this.identifyTurningSpaces(elements, roomBoundary)
    const hasAdequateTurningSpace = turningSpaces.some(space => space.width >= minTurningDiameter && space.height >= minTurningDiameter)
    if (!hasAdequateTurningSpace && elements.length > 0) { // Check only if there are elements that might obstruct
      // Try to find if any large open area exists within roomBoundary
      const roomBBox = getElementBoundingBoxClass(roomBoundary)
      if (roomBBox && (roomBBox.width < minTurningDiameter || roomBBox.height < minTurningDiameter) && elements.length > 0) {
        // If room itself is too small and has items, it's an issue.
      }
      else if (roomBBox && (roomBBox.width >= minTurningDiameter && roomBBox.height >= minTurningDiameter)) {
        // Room is large enough, but specific turning spaces might be obstructed.
        // This requires more complex free space analysis. For now, we assume if no dedicated space, it's an issue.
        if (turningSpaces.length === 0 && elements.filter(el => el.type !== CoreElementType.ROOM_BOUNDARY).length > 0) {
          issues.push(`No dedicated wheelchair turning space of at least ${minTurningDiameter}m x ${minTurningDiameter}m detected.`)
          recommendations.push(`Ensure a clear ${minTurningDiameter}m diameter turning space is available.`)
        }
      }
    }

    const doors = elements.filter(el => el.type === CoreElementType.DOOR) as unknown as (ShapeElement & { properties?: { openingWidth?: number, clearWidth?: number }, width?: number })[] // Added unknown
    if (doors.length > 0) {
      doors.forEach((door) => {
        // Prioritize specific properties if they exist
        const doorOpeningWidth = door.properties?.openingWidth ?? door.properties?.clearWidth ?? door.width ?? 0
        if (doorOpeningWidth < minDoorWidth) {
          issues.push(`Door (ID: ${door.id}) clear width of ${doorOpeningWidth.toFixed(2)}m is less than the recommended ${minDoorWidth}m.`)
        }
      })
    }
    else if (elements.some(el => el.type === CoreElementType.WALL)) { // If there are walls, expect doors
      issues.push('No door elements detected to assess accessible widths.')
    }

    recommendations.push(`Ensure all doors have at least ${minDoorWidth}m clear width.`)
    recommendations.push(`Main pathways should be at least ${ACCESSIBLE_STANDARDS.MIN_ACCESSIBLE_PATH_WIDTH}m wide, ideally ${ACCESSIBLE_STANDARDS.RECOMMENDED_MAIN_PATH_WIDTH}m.`)

    return { isValid: issues.length === 0, issues, recommendations }
  }

  /**
   * Evaluates the assistive features in the room layout.
   *
   * @param _elements - The elements within the accessible room space.
   * @returns An object with validation result, issues, and recommendations.
   */
  public evaluateAssistiveFeatures(_elements: Element[]): { isValid: boolean, issues: string[], recommendations: string[] } {
    const issues: string[] = []
    const recommendations: string[] = []
    // Example check: presence of grab bars if it's a bathroom context
    // const hasGrabBars = elements.some(el => (el as any).properties?.isGrabBar === true);
    // if (this.getSpaceType() === 'bathroom' && !hasGrabBars) { // Assuming getSpaceType() could be context-aware
    //    issues.push("Grab bars not found in bathroom area.");
    // }
    recommendations.push(`Consider handrail placement at appropriate heights (${ACCESSIBLE_STANDARDS.HANDRAIL_HEIGHT_MIN}m-${ACCESSIBLE_STANDARDS.HANDRAIL_HEIGHT_MAX}m).`)
    recommendations.push(`Ensure light switches and controls are within accessible reach (${ACCESSIBLE_STANDARDS.SWITCH_REACH_MIN}m-${ACCESSIBLE_STANDARDS.SWITCH_REACH_MAX}m height).`)
    recommendations.push('For bathrooms/kitchens, verify accessible sink/counter heights and clearances.')
    return { isValid: issues.length === 0, issues, recommendations } // Simplified
  }

  /**
   * Identifies corridors within the room layout.
   *
   * @param _elements - The elements within the accessible room space.
   * @param roomBoundary - The element representing the room boundary.
   * @returns An array of corridor objects with dimensions and properties.
   * @private
   */
  private identifyCorridors(_elements: Element[], roomBoundary: Element): { id: string, width: number, length: number, isMain: boolean }[] {
    // Placeholder logic - requires actual geometric analysis of free space
    // This might involve creating a navigation mesh or analyzing space between bounding boxes.
    // For now, returns a mock corridor if space allows.
    const roomBBox = getElementBoundingBoxClass(roomBoundary)
    if (!roomBBox || roomBBox.width < 0.9 || roomBBox.height < 0.9)
      return []
    return [{ id: 'main_corridor_mock', width: Math.min(roomBBox.width, roomBBox.height), length: Math.max(roomBBox.width, roomBBox.height), isMain: true }]
  }

  /**
   * Identifies wheelchair turning spaces within the room layout.
   *
   * @param elements - The elements within the accessible room space.
   * @param roomBoundary - The element representing the room boundary.
   * @returns An array of bounding boxes representing potential turning spaces.
   * @private
   */
  private identifyTurningSpaces(elements: Element[], roomBoundary: Element): BoundingBoxClass[] {
    // Placeholder logic - requires actual geometric analysis of free space
    const roomBBox = getElementBoundingBoxClass(roomBoundary)
    if (roomBBox && roomBBox.width >= 1.5 && roomBBox.height >= 1.5) {
      // Check if this space is obstructed by other elements
      const obstructingElements = elements.filter(el => el.id !== roomBoundary.id && getElementBoundingBoxClass(el) !== null)
      const isObstructed = false
      for (const obsEl of obstructingElements) {
        const obsBBox = getElementBoundingBoxClass(obsEl)
        if (obsBBox && roomBBox.intersects(obsBBox)) { // Assuming BoundingBoxClass has an intersects method
          // This is a very simple check. True obstruction analysis is complex.
          // isObstructed = true; break;
        }
      }
      if (!isObstructed)
        return [new BoundingBoxClass(roomBBox.position.x, roomBBox.position.y, roomBBox.width, roomBBox.height)]
    }
    return []
  }
}
