import type { ShapeElement } from '@/types/core/element/element'
import type { Circle, Ellipse } from '@/types/core/element/shape/ellipseShapeTypes'
import { describe, expect, it } from 'vitest'

describe('ellipse Interface', () => {
  it('should define an ellipse with required properties', () => {
    // Create a valid Ellipse object
    const ellipse: Ellipse = {
      id: 'ellipse-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 100, y: 100 }, // center
        { x: 150, y: 100 }, // x-radius point
        { x: 100, y: 150 }, // y-radius point
      ],
      getRadiusX: () => 50,
      getRadiusY: () => 50,
    }

    // Verify the object is valid
    expect(ellipse.id).toBe('ellipse-1')
    expect(ellipse.type).toBe('shape')
    expect(ellipse.points.length).toBe(3)
    expect(ellipse.getRadiusX()).toBe(50)
    expect(ellipse.getRadiusY()).toBe(50)
  })

  it('should extend ShapeElement interface', () => {
    // Create an ellipse
    const ellipse: Ellipse = {
      id: 'ellipse-2',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 100, y: 100 }, // center
        { x: 200, y: 100 }, // x-radius point
        { x: 100, y: 150 }, // y-radius point
      ],
      getRadiusX: () => 100,
      getRadiusY: () => 50,
    }

    // Verify the ellipse extends ShapeElement
    const shapeElement: ShapeElement = ellipse
    expect(shapeElement.id).toBe('ellipse-2')
    expect(shapeElement.type).toBe('shape')
    expect(shapeElement.visible).toBe(true)
    expect(shapeElement.locked).toBe(false)
    expect(shapeElement.position.x).toBe(100)
    expect(shapeElement.position.y).toBe(100)
  })

  it('should allow creating ellipses with different dimensions', () => {
    // Create ellipses with different dimensions
    const circularEllipse: Ellipse = {
      id: 'circular',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 100, y: 100 }, // center
        { x: 150, y: 100 }, // x-radius point
        { x: 100, y: 150 }, // y-radius point
      ],
      getRadiusX: () => 50,
      getRadiusY: () => 50,
    }

    const wideEllipse: Ellipse = {
      id: 'wide',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 100, y: 100 }, // center
        { x: 200, y: 100 }, // x-radius point
        { x: 100, y: 125 }, // y-radius point
      ],
      getRadiusX: () => 100,
      getRadiusY: () => 25,
    }

    const tallEllipse: Ellipse = {
      id: 'tall',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 100, y: 100 }, // center
        { x: 125, y: 100 }, // x-radius point
        { x: 100, y: 200 }, // y-radius point
      ],
      getRadiusX: () => 25,
      getRadiusY: () => 100,
    }

    // Verify all ellipses are valid
    expect(circularEllipse.getRadiusX()).toBe(50)
    expect(circularEllipse.getRadiusY()).toBe(50)

    expect(wideEllipse.getRadiusX()).toBe(100)
    expect(wideEllipse.getRadiusY()).toBe(25)

    expect(tallEllipse.getRadiusX()).toBe(25)
    expect(tallEllipse.getRadiusY()).toBe(100)
  })

  it('should be usable in arrays and collections', () => {
    // Create an array of ellipses
    const ellipses: Ellipse[] = [
      {
        id: 'ellipse-3',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 100, y: 100 },
        rotation: 0,
        scale: { x: 1, y: 1 },
        selectable: true,
        draggable: true,
        showHandles: true,
        points: [
          { x: 100, y: 100 }, // center
          { x: 150, y: 100 }, // x-radius point
          { x: 100, y: 150 }, // y-radius point
        ],
        getRadiusX: () => 50,
        getRadiusY: () => 50,
      },
      {
        id: 'ellipse-4',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 200, y: 200 },
        rotation: 0,
        scale: { x: 1, y: 1 },
        selectable: true,
        draggable: true,
        showHandles: true,
        points: [
          { x: 200, y: 200 }, // center
          { x: 300, y: 200 }, // x-radius point
          { x: 200, y: 250 }, // y-radius point
        ],
        getRadiusX: () => 100,
        getRadiusY: () => 50,
      },
    ]

    // Verify the array is valid
    expect(ellipses.length).toBe(2)
    expect(ellipses[0].id).toBe('ellipse-3')
    expect(ellipses[0].getRadiusX()).toBe(50)
    expect(ellipses[0].getRadiusY()).toBe(50)

    expect(ellipses[1].id).toBe('ellipse-4')
    expect(ellipses[1].getRadiusX()).toBe(100)
    expect(ellipses[1].getRadiusY()).toBe(50)
  })

  it('should be usable in functions that require Ellipse', () => {
    // Define a function that uses Ellipse
    function calculateEllipseArea(ellipse: Ellipse): number {
      return Math.PI * ellipse.getRadiusX() * ellipse.getRadiusY()
    }

    // Create an ellipse
    const ellipse: Ellipse = {
      id: 'ellipse-5',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 100, y: 100 }, // center
        { x: 110, y: 100 }, // x-radius point
        { x: 100, y: 120 }, // y-radius point
      ],
      getRadiusX: () => 10,
      getRadiusY: () => 20,
    }

    // Test the function
    const area = calculateEllipseArea(ellipse)
    expect(area).toBeCloseTo(Math.PI * 10 * 20)
  })

  it('should be compatible with JSON serialization/deserialization', () => {
    // Create an ellipse
    const ellipse: Ellipse & { radiusX: number, radiusY: number } = {
      id: 'ellipse-6',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 100, y: 100 }, // center
        { x: 150, y: 100 }, // x-radius point
        { x: 100, y: 150 }, // y-radius point
      ],
      radiusX: 50,
      radiusY: 50,
      getRadiusX() { return this.radiusX },
      getRadiusY() { return this.radiusY },
    }

    // Serialize to JSON and deserialize
    const serialized = JSON.stringify(ellipse)
    const deserialized = JSON.parse(serialized)

    // Verify the deserialized object has the correct properties
    // Note: methods are lost during serialization
    expect(deserialized.id).toBe('ellipse-6')
    expect(deserialized.type).toBe('shape')
    expect(deserialized.points.length).toBe(3)
    expect(deserialized.points[0].x).toBe(100)
    expect(deserialized.points[0].y).toBe(100)
    expect(deserialized.points[1].x).toBe(150)
    expect(deserialized.points[1].y).toBe(100)
    expect(deserialized.points[2].x).toBe(100)
    expect(deserialized.points[2].y).toBe(150)
    expect(deserialized.radiusX).toBe(50)
    expect(deserialized.radiusY).toBe(50)
  })
})

describe('circle Interface', () => {
  it('should define a circle with required properties', () => {
    // Create a valid Circle object
    const circle: Circle = {
      id: 'circle-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 100, y: 100 }, // center
        { x: 150, y: 100 }, // x-radius point
        { x: 100, y: 150 }, // y-radius point
      ],
      getRadiusX: () => 50,
      getRadiusY: () => 50,
      getRadius: () => 50,
    }

    // Verify the object is valid
    expect(circle.id).toBe('circle-1')
    expect(circle.type).toBe('shape')
    expect(circle.points.length).toBe(3)
    expect(circle.getRadiusX()).toBe(50)
    expect(circle.getRadiusY()).toBe(50)
    expect(circle.getRadius()).toBe(50)
  })

  it('should extend Ellipse interface', () => {
    // Create a circle
    const circle: Circle = {
      id: 'circle-2',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 100, y: 100 }, // center
        { x: 150, y: 100 }, // x-radius point
        { x: 100, y: 150 }, // y-radius point
      ],
      getRadiusX: () => 50,
      getRadiusY: () => 50,
      getRadius: () => 50,
    }

    // Verify the circle extends Ellipse
    const ellipse: Ellipse = circle
    expect(ellipse.id).toBe('circle-2')
    expect(ellipse.getRadiusX()).toBe(50)
    expect(ellipse.getRadiusY()).toBe(50)
  })

  it('should be usable in functions that require Circle', () => {
    // Define a function that uses Circle
    function calculateCircleArea(circle: Circle): number {
      return Math.PI * circle.getRadius() ** 2
    }

    // Create a circle
    const circle: Circle = {
      id: 'circle-3',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 100, y: 100 }, // center
        { x: 110, y: 100 }, // x-radius point
        { x: 100, y: 110 }, // y-radius point
      ],
      getRadiusX: () => 10,
      getRadiusY: () => 10,
      getRadius: () => 10,
    }

    // Test the function
    const area = calculateCircleArea(circle)
    expect(area).toBeCloseTo(Math.PI * 100) // π * 10²
  })

  it('should be compatible with JSON serialization/deserialization', () => {
    // Create a circle with additional properties to store radius
    const circle: Circle & { radius: number } = {
      id: 'circle-4',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 100, y: 100 }, // center
        { x: 150, y: 100 }, // x-radius point
        { x: 100, y: 150 }, // y-radius point
      ],
      radius: 50,
      getRadiusX() { return this.radius },
      getRadiusY() { return this.radius },
      getRadius() { return this.radius },
    }

    // Serialize to JSON and deserialize
    const serialized = JSON.stringify(circle)
    const deserialized = JSON.parse(serialized)

    // Verify the deserialized object has the correct properties
    // Note: methods are lost during serialization
    expect(deserialized.id).toBe('circle-4')
    expect(deserialized.type).toBe('shape')
    expect(deserialized.points.length).toBe(3)
    expect(deserialized.points[0].x).toBe(100)
    expect(deserialized.points[0].y).toBe(100)
    expect(deserialized.points[1].x).toBe(150)
    expect(deserialized.points[1].y).toBe(100)
    expect(deserialized.points[2].x).toBe(100)
    expect(deserialized.points[2].y).toBe(150)
    expect(deserialized.radius).toBe(50)
  })
})
