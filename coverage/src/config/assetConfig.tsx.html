
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/config/assetConfig.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">src/config</a> assetConfig.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/86</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/86</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import { ChartNoAxesGantt, Circle, Hexagon, ImageIcon, LoaderCircle, Minus, Pentagon, RectangleHorizontal, Spline, Square, Triangle, Type } from 'lucide-react'<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
/** @jsxImportSource react */
<span class="cstat-no" title="statement not covered" >import { ElementType } from '@/types/core/elementDefinitions'</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const basicAssetDisplayOrder: ElementType[] = [</span>
<span class="cstat-no" title="statement not covered" >  ElementType.RECTANGLE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.SQUARE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.ELLIPSE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.CIRCLE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.TRIANGLE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.QUADRILATERAL,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.PENTAGON,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.HEXAGON,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.LINE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.POLYLINE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.ARC,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.QUADRATIC,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.CUBIC,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.TEXT,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.IMAGE,</span>
<span class="cstat-no" title="statement not covered" >]</span>
&nbsp;
// Define types for classifying assets for the collapsed drawer
<span class="cstat-no" title="statement not covered" >export const DRAGGABLE_COLLAPSED_TYPES: ElementType[] = [</span>
<span class="cstat-no" title="statement not covered" >  ElementType.FLOOR_AREA,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.OPENING,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.IMAGE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.RECTANGLE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.SQUARE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.ELLIPSE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.CIRCLE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.TRIANGLE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.QUADRILATERAL,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.PENTAGON,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.HEXAGON,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.TEXT, // 🔧 恢复Text为拖拽生成</span>
<span class="cstat-no" title="statement not covered" >]</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const DRAWING_TOOL_COLLAPSED_TYPES: ElementType[] = [</span>
<span class="cstat-no" title="statement not covered" >  ElementType.WALL,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.LINE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.POLYLINE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.ARC,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.QUADRATIC,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.CUBIC,</span>
<span class="cstat-no" title="statement not covered" >]</span>
&nbsp;
// Define the desired order for basic draggable assets in the collapsed drawer
<span class="cstat-no" title="statement not covered" >export const BASIC_DRAGGABLE_ORDER_COLLAPSED: ElementType[] = [</span>
<span class="cstat-no" title="statement not covered" >  ElementType.SQUARE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.RECTANGLE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.CIRCLE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.ELLIPSE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.TRIANGLE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.QUADRILATERAL,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.PENTAGON,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.HEXAGON,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.TEXT, // 🔧 添加Text到拖拽顺序</span>
<span class="cstat-no" title="statement not covered" >  ElementType.IMAGE,</span>
<span class="cstat-no" title="statement not covered" >]</span>
&nbsp;
// Define the desired order for basic drawing tool assets in the collapsed drawer
<span class="cstat-no" title="statement not covered" >export const BASIC_DRAWING_TOOL_ORDER_COLLAPSED: ElementType[] = [</span>
<span class="cstat-no" title="statement not covered" >  ElementType.LINE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.POLYLINE,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.ARC,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.QUADRATIC,</span>
<span class="cstat-no" title="statement not covered" >  ElementType.CUBIC, // Assuming CUBIC is a clickable drawing tool</span>
<span class="cstat-no" title="statement not covered" >]</span>
&nbsp;
// Configuration for Basic Shapes (Draggable) in Expanded View
<span class="cstat-no" title="statement not covered" >export const basicShapesForExpandedConfig = [</span>
<span class="cstat-no" title="statement not covered" >  { category: 'Shapes', items: [</span>
<span class="cstat-no" title="statement not covered" >    { name: 'Rectangle', icon: &lt;RectangleHorizontal className="h-5 w-5" /&gt;, type: ElementType.RECTANGLE },</span>
<span class="cstat-no" title="statement not covered" >    { name: 'Square', icon: &lt;Square className="h-5 w-5" /&gt;, type: ElementType.SQUARE },</span>
<span class="cstat-no" title="statement not covered" >    { name: 'Ellipse', icon: &lt;Circle className="h-5 w-5" /&gt;, type: ElementType.ELLIPSE },</span>
<span class="cstat-no" title="statement not covered" >    { name: 'Circle', icon: &lt;Circle className="h-5 w-5" /&gt;, type: ElementType.CIRCLE },</span>
<span class="cstat-no" title="statement not covered" >    { name: 'Triangle', icon: &lt;Triangle className="h-5 w-5" /&gt;, type: ElementType.TRIANGLE },</span>
<span class="cstat-no" title="statement not covered" >    { name: 'Pentagon', icon: &lt;Pentagon className="h-5 w-5" /&gt;, type: ElementType.PENTAGON },</span>
<span class="cstat-no" title="statement not covered" >    { name: 'Hexagon', icon: &lt;Hexagon className="h-5 w-5" /&gt;, type: ElementType.HEXAGON },</span>
<span class="cstat-no" title="statement not covered" >  ] },</span>
<span class="cstat-no" title="statement not covered" >  { category: 'Utilities', items: [</span>
<span class="cstat-no" title="statement not covered" >    { name: 'Text', icon: &lt;Type className="h-5 w-5" /&gt;, type: ElementType.TEXT },</span>
<span class="cstat-no" title="statement not covered" >    { name: 'Image', icon: &lt;ImageIcon className="h-5 w-5" /&gt;, type: ElementType.IMAGE },</span>
<span class="cstat-no" title="statement not covered" >  ] },</span>
<span class="cstat-no" title="statement not covered" >]</span>
&nbsp;
// Configuration for Basic Paths (Click-to-Prime) in Expanded View
<span class="cstat-no" title="statement not covered" >export const basicPathsForExpandedConfig = [</span>
<span class="cstat-no" title="statement not covered" >  { category: 'Paths', items: [</span>
<span class="cstat-no" title="statement not covered" >    { name: 'Line', icon: &lt;Minus className="h-5 w-5 transform rotate-[-45deg]" /&gt;, type: ElementType.LINE },</span>
<span class="cstat-no" title="statement not covered" >    { name: 'Polyline', icon: &lt;ChartNoAxesGantt className="h-5 w-5" /&gt;, type: ElementType.POLYLINE },</span>
<span class="cstat-no" title="statement not covered" >  ] },</span>
<span class="cstat-no" title="statement not covered" >  { category: 'Curves', items: [</span>
<span class="cstat-no" title="statement not covered" >    { name: 'Arc', icon: &lt;LoaderCircle className="h-5 w-5" /&gt;, type: ElementType.ARC },</span>
<span class="cstat-no" title="statement not covered" >    { name: 'Quadratic Curve', icon: &lt;Spline className="h-5 w-5" /&gt;, type: ElementType.QUADRATIC },</span>
<span class="cstat-no" title="statement not covered" >    { name: 'Cubic Curve', icon: &lt;Spline className="h-5 w-5" /&gt;, type: ElementType.CUBIC },</span>
<span class="cstat-no" title="statement not covered" >  ] },</span>
<span class="cstat-no" title="statement not covered" >]</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:58:01.486Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    