/**
 * Distance Calculation Strategy for Arc Elements
 *
 * @remarks
 * This strategy implements the {@link DistanceCalculatorStrategy} for calculating
 * the distance between an Arc element ({@link CoreElementType.ARC}) and another
 * {@link ShapeElement}.
 *
 * It currently supports calculating the distance between:
 * - An Arc and a Line ({@link CoreElementType.LINE}).
 * - An Arc and another Arc ({@link CoreElementType.ARC}).
 *
 * The calculations are delegated to utility functions `calculateArcLineDistance`
 * and `calculateArcArcDistance` from `../../../../lib/utils/geometry/arcUtils`.
 *
 * The strategy validates the types of the input elements and their geometric
 * properties (e.g., radius, angles, points, position).
 * For Line elements, it converts relative start/end points to absolute coordinates
 * before passing them to the utility function.
 *
 * @module core/compute/strategies/distance/ArcDistanceStrategy
 * @see {@link Path.Arc}
 * @see {@link Path.Line}
 * @see {@link DistanceCalculatorStrategy}
 */
import type { DistanceCalculatorStrategy } from '../../../../types/core/compute'
import type { PointData as IPoint } from '../../../../types/core/element/geometry/point' // Use PointData, alias as IPoint
import type {
  Element,
  Path,
  ShapeElement,
} from '../../../../types/core/elementDefinitions'
import {
  calculateArcArcDistance,
  calculateArcLineDistance,
  // calculateArcPointDistance, // If needed for distance to a raw point
} from '../../../../lib/utils/geometry/arcUtils' // Corrected path
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

export class ArcDistanceStrategy implements DistanceCalculatorStrategy {
  /**
   * Calculates the distance between an Arc element and another {@link ShapeElement}.
   *
   * @param element1 - The first shape element, which must be of type {@link CoreElementType.ARC}.
   * @param element2 - The second shape element. Currently supports {@link CoreElementType.LINE} and {@link CoreElementType.ARC}.
   * @returns The calculated distance between the two elements.
   * @throws {@link CoreError} if `element1` is not an ARC.
   * @throws {@link CoreError} if `element2` is of an unsupported type for distance calculation with an ARC.
   * @throws {@link CoreError} if either element has invalid or missing geometric properties required for the calculation.
   */
  public calculateDistance(element1: Element, element2: Element): number {
    // Cast to ShapeElement to access position and other shape properties
    const shapeElement1 = element1 as ShapeElement
    const shapeElement2 = element2 as ShapeElement

    if (shapeElement1.type !== CoreElementType.ARC) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `ArcDistanceStrategy's first element must be an ARC, got ${shapeElement1.type}`,
        undefined,
        { component: 'ArcDistanceStrategy', operation: 'calculateDistance', target: shapeElement1.id },
      )
    }

    const arcElement1 = shapeElement1 as unknown as Path.Arc // Added unknown for type safety

    // Validate arcElement1 properties (radius, startAngle, endAngle are in properties)
    if (typeof arcElement1.properties?.radius !== 'number' || (arcElement1.properties?.radius ?? 0) <= 0 || !Number.isFinite(arcElement1.properties?.radius ?? 0)
      || typeof arcElement1.properties?.startAngle !== 'number' || !Number.isFinite(arcElement1.properties?.startAngle ?? 0)
      || typeof arcElement1.properties?.endAngle !== 'number' || !Number.isFinite(arcElement1.properties?.endAngle ?? 0)
    // Access position from shapeElement1
      || shapeElement1.position == null || typeof shapeElement1.position.x !== 'number' || typeof shapeElement1.position.y !== 'number') {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid arc parameters for element ID ${element1.id}`,
        undefined,
        { component: 'ArcDistanceStrategy', operation: 'calculateDistance', target: element1.id, metadata: { arc: arcElement1 } },
      )
    }

    switch (shapeElement2.type) {
      case CoreElementType.LINE: {
        const lineElement = shapeElement2 as unknown as Path.Line // Added unknown for type safety
        const lineStart = lineElement.properties?.start ?? lineElement.start // Try properties first, fallback to direct access
        const lineEnd = lineElement.properties?.end ?? lineElement.end

        if (lineStart == null || lineEnd == null
          || typeof lineStart.x !== 'number' || typeof lineStart.y !== 'number' || !Number.isFinite(lineStart.x) || !Number.isFinite(lineStart.y)
          || typeof lineEnd.x !== 'number' || typeof lineEnd.y !== 'number' || !Number.isFinite(lineEnd.x) || !Number.isFinite(lineEnd.y)
        // Access position from shapeElement2
          || shapeElement2.position == null || typeof shapeElement2.position.x !== 'number' || typeof shapeElement2.position.y !== 'number') {
          throw new CoreError(
            ErrorType.InvalidParameter,
            `Target line element (ID: ${shapeElement2.id}) has invalid start/end points or position.`,
            undefined,
            { component: 'ArcDistanceStrategy', operation: 'calculateDistance', target: shapeElement2.id, metadata: { line: lineElement } },
          )
        }
        // Convert line points to absolute if they are relative to shapeElement2.position
        // arcUtils expects ArcProperties which has angles in degrees and position as center.
        // calculateArcLineDistance expects absolute line points.
        // Access position from shapeElement2
        const linePos = shapeElement2.position
        const absLineStart: IPoint = { x: lineStart.x + linePos.x, y: lineStart.y + linePos.y, z: lineStart.z }
        const absLineEnd: IPoint = { x: lineEnd.x + linePos.x, y: lineEnd.y + linePos.y, z: lineEnd.z }

        // calculateArcLineDistance from arcUtils expects (lineStart, lineEnd, arc)
        return calculateArcLineDistance(absLineStart, absLineEnd, arcElement1)
      }

      case CoreElementType.ARC: {
        const arcElement2 = shapeElement2 as unknown as Path.Arc // Added unknown for type safety
        if (typeof arcElement2.properties?.radius !== 'number' || (arcElement2.properties?.radius ?? 0) <= 0 || !Number.isFinite(arcElement2.properties?.radius ?? 0)
          || typeof arcElement2.properties?.startAngle !== 'number' || !Number.isFinite(arcElement2.properties?.startAngle ?? 0)
          || typeof arcElement2.properties?.endAngle !== 'number' || !Number.isFinite(arcElement2.properties?.endAngle ?? 0)
        // Access position from shapeElement2
          || shapeElement2.position == null || typeof shapeElement2.position.x !== 'number' || typeof shapeElement2.position.y !== 'number') {
          throw new CoreError(
            ErrorType.InvalidParameter,
            `Target arc element (ID: ${shapeElement2.id}) has invalid parameters.`,
            undefined,
            { component: 'ArcDistanceStrategy', operation: 'calculateDistance', target: shapeElement2.id, metadata: { arc: arcElement2 } },
          )
        }
        // calculateArcArcDistance from arcUtils expects (arc1, arc2)
        return calculateArcArcDistance(arcElement1, arcElement2)
      }

      default:
        // console.warn(`[ArcDistanceStrategy] Distance calculation between ARC and ${shapeElement2.type} is not directly supported. Element1 ID: ${shapeElement1.id}, Element2 ID: ${shapeElement2.id}`);
        throw new CoreError(
          ErrorType.InvalidElementType,
          `Distance calculation between ARC and ${shapeElement2.type} is not supported by ArcDistanceStrategy.`,
          undefined,
          { component: 'ArcDistanceStrategy', operation: 'calculateDistance', metadata: { type1: shapeElement1.type, type2: shapeElement2.type } },
        )
    }
  }

  /**
   * Returns the primary element type that this strategy handles as the first argument.
   * @returns The element type {@link CoreElementType.ARC}
   */
  public getElementType(): CoreElementType {
    return CoreElementType.ARC
  }
}
