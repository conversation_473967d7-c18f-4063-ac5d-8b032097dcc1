/**
 * Shape Service Types
 *
 * This file defines the types for shape-related service functionality.
 * It references element types from the core module to avoid duplication.
 *
 * @module types/services/shapes
 */

import type { ServiceResult } from '../core/serviceResult'
import type { ElementType, ShapeElement } from '@/types/core'

/**
 * Shape creation service options interface
 */
export interface ShapeCreationServiceOptions {
  /** Whether to snap shapes to grid */
  snapToGrid: boolean
  /** Grid size for snapping */
  gridSize: number
  /** Whether to maintain aspect ratio during operations */
  maintainAspectRatio: boolean
}

/**
 * Shape creation request parameters
 */
export interface ShapeCreationRequest {
  /** Element type to create */
  elementType: ElementType
  /** Initial position */
  position: { x: number, y: number }
  /** Additional properties */
  properties?: Record<string, unknown>
  /** Optional ID for the new element */
  id?: string
}

/**
 * Shape creation result type
 */
export type ShapeCreationResult = ServiceResult<ShapeElement>

/**
 * Shape edit request parameters
 */
export interface ShapeEditRequest {
  /** Shape ID to edit */
  id: string
  /** New position */
  position?: { x: number, y: number }
  /** New style properties */
  style?: Record<string, unknown>
  /** Other property updates */
  properties?: Record<string, unknown>
  /** Whether to select the shape after editing */
  selectAfterEdit?: boolean
  /** Major category for layer organization (e.g., 'floor', 'ceiling', 'furniture') */
  majorCategory?: string
  /** Minor category within the major category (e.g., 'coverings', 'lighting') */
  minorCategory?: string
  /** Z-level identifier for ordering within a layer */
  zLevelId?: string
}

/**
 * Shape edit result type
 */
export type ShapeEditResult = ServiceResult<ShapeElement>

/**
 * Shape delete request parameters
 */
export interface ShapeDeleteRequest {
  /** Shape ID to delete */
  id: string
}

/**
 * Shape delete result type
 */
export type ShapeDeleteResult = ServiceResult<string>

/**
 * Shape selection request parameters
 */
export interface ShapeSelectRequest {
  /** Shape ID to select */
  id: string
  /** Whether to allow multiple selection */
  multiSelect?: boolean
}

/**
 * Shape selection result type
 */
export type ShapeSelectResult = ServiceResult<string[]>

/**
 * Element selection request parameters
 */
export interface ElementSelectRequest {
  /** Element ID to select */
  id: string
  /** Whether to allow multiple selection */
  multiSelect?: boolean
}

/**
 * Element selection result type
 */
export type ElementSelectResult = ServiceResult<string[]>

/**
 * Element edit request parameters
 */
export interface ElementEditRequest {
  /** Element ID to edit */
  id: string
  /** New position */
  position?: { x: number, y: number }
  /** New style properties */
  style?: Record<string, unknown>
  /** Other property updates */
  properties?: Record<string, unknown>
  /** Whether to select the element after editing */
  selectAfterEdit?: boolean
  /** Major category for layer organization (e.g., 'floor', 'ceiling', 'furniture') */
  majorCategory?: string
  /** Minor category within the major category (e.g., 'coverings', 'lighting') */
  minorCategory?: string
  /** Z-level identifier for ordering within a layer */
  zLevelId?: string
}

/**
 * Element edit result type
 */
export type ElementEditResult = ServiceResult<ShapeElement>

/**
 * Defines the modes for shape selection.
 */
export enum SelectionMode {
  /** Replace the current selection with the new one. */
  Replace = 'REPLACE',
  /** Add the new shapes to the current selection. */
  Add = 'ADD',
  /** Remove the specified shapes from the current selection. */
  Remove = 'REMOVE',
  /** Clear the entire current selection. */
  Clear = 'CLEAR',
}

/**
 * Element creation request parameters
 */
export interface ElementCreationRequest {
  /** Element type to create */
  elementType: ElementType
  /** Initial position */
  position: { x: number, y: number }
  /** Additional properties */
  properties?: Record<string, unknown>
  /** Optional ID for the new element */
  id?: string
}

/**
 * Element creation result type
 */
export type ElementCreationResult = ServiceResult<ShapeElement>

/**
 * Element delete request parameters
 */
export interface ElementDeleteRequest {
  /** Element ID to delete */
  id: string
}

/**
 * Element delete result type
 */
export type ElementDeleteResult = ServiceResult<string>

/**
 * Element creation service interface
 */
export interface ElementCreationService {
  /** Service identifier */
  readonly serviceId: string
  /**
   * Creates a new element
   * @param request Element creation parameters
   * @returns Result containing the created element or error
   */
  createShape: (request: ElementCreationRequest) => Promise<ElementCreationResult>
  /**
   * Duplicates an existing element
   * @param shapeId ID of the element to duplicate
   * @param offset Position offset for the duplicate
   * @returns Result containing the duplicated element or error
   */
  duplicateShape: (shapeId: string, offset?: { x: number, y: number }) => Promise<ElementCreationResult>
}

/**
 * Element delete service interface
 */
export interface ElementDeleteService {
  /** Service identifier */
  readonly serviceId: string
  /**
   * Deletes an element
   * @param request Element delete parameters
   * @returns Result indicating success or error
   */
  deleteShape: (request: ElementDeleteRequest) => Promise<ElementDeleteResult>
  /**
   * Deletes multiple elements
   * @param shapeIds Array of element IDs to delete
   * @returns Result indicating success or error
   */
  deleteShapes: (shapeIds: string[]) => Promise<ServiceResult<string[]>>
}

// Export service interfaces
export * from './shapeService'
