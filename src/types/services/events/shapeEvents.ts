/**
 * Shape Events Type Definitions
 *
 * This file defines the event types related to shape operations,
 * including shape creation, modification, selection, and deletion.
 *
 * @module types/services/events
 */

import type { BaseEvent } from './eventCore'
import type { AppEventType } from './eventTypes'

/**
 * Basic shape interface (temporary definition)
 */
export interface Shape {
  id: string
  type: string
  x: number
  y: number
  width?: number
  height?: number
  rotation?: number
  [key: string]: unknown
}

/**
 * Transformation options interface
 */
export interface TransformOptions {
  x?: number
  y?: number
  width?: number
  height?: number
  rotation?: number
  scaleX?: number
  scaleY?: number
}

/**
 * Shape creation event
 */
export interface ShapeCreateEvent extends BaseEvent {
  type: AppEventType.ShapeCreateRequest |
    AppEventType.ShapeCreateValidate |
    AppEventType.ShapeCreateComplete |
    AppEventType.ShapeCreateError
  payload: {
    /** Type of the element to create (string, will be cast to ElementType enum) */
    ElementType?: string // Added for ShapeCreateRequest
    /** Initial position for the new shape */
    position?: { x: number, y: number } | unknown // Allow PointClass, Added for ShapeCreateRequest
    /** Optional additional properties for the shape */
    properties?: Record<string, unknown> // Added for ShapeCreateRequest

    /** Shape data (used by ShapeCreateComplete) */
    shape?: Shape
    /** Validation result (for SHAPE_CREATE_VALIDATE) */
    validationResult?: {
      valid: boolean
      errors?: string[]
    }
    /** Error message (for SHAPE_CREATE_ERROR) */
    error?: string
  }
}

/**
 * Shape edit event
 */
export interface ShapeEditEvent extends BaseEvent {
  type: AppEventType.ShapeEditRequest |
    AppEventType.ShapeEditCompute |
    AppEventType.ShapeEditComplete |
    AppEventType.ShapeEditError
  payload: {
    /** Shape ID */
    shapeId: string
    /** Updated shape properties (legacy) */
    properties?: Partial<Shape>
    /** Computed properties (for SHAPE_EDIT_COMPUTE) */
    computed?: Record<string, unknown>
    /** Error message (for SHAPE_EDIT_ERROR) */
    error?: string
    /** The full set of changes to apply (replaces properties) */
    changes?: Record<string, unknown>
    /** Source of the edit request (service or component name) */
    source?: string
  }
}

/**
 * Shape delete event
 */
export interface ShapeDeleteEvent extends BaseEvent {
  type: AppEventType.ShapeDeleteRequest |
    AppEventType.ShapeDeleteComplete |
    AppEventType.ShapeDeleteError
  payload: {
    /** Shape ID or array of shape IDs */
    shapeId: string | string[]
    /** Error message (for SHAPE_DELETE_ERROR) */
    error?: string
  }
}

/**
 * Shape selection event
 */
export interface ShapeSelectionEvent extends BaseEvent {
  type: AppEventType.ShapeSelectRequest |
    AppEventType.ShapeSelectionSuccess |
    AppEventType.ShapeSelected
  payload: {
    /** Selected shape ID or array of shape IDs */
    shapeId: string | string[]
    /** Whether to add to existing selection */
    addToSelection?: boolean
    /** Whether selection was done via a group */
    viaGroup?: boolean
  }
}

/**
 * Shape transform event
 */
export interface ShapeTransformEvent extends BaseEvent {
  type: AppEventType.ShapeTransformRequest |
    AppEventType.ShapeTransformUpdate |
    AppEventType.ShapeTransformComplete
  payload: {
    /** Shape ID or array of shape IDs */
    shapeId: string | string[]
    /** Transform options */
    transform: TransformOptions
    /** Whether this is the final transform in a sequence */
    isFinal?: boolean
  }
}

/**
 * Shape selected event
 * Event published after a shape is selected
 */
export interface ShapeSelectedEvent extends BaseEvent {
  type: AppEventType.ShapeSelected
  payload: {
    /** Selected shape data */
    shape: Shape | null
  }
}

/**
 * Shape duplicate event
 */
export interface ShapeDuplicateEvent extends BaseEvent {
  type: AppEventType.ShapeDuplicateRequest | AppEventType.ShapeDuplicateComplete
  payload: {
    /** Original shape ID */
    originalId: string
    /** Position for the duplicated shape */
    position: { x: number, y: number }
    /** Duplicated shape (for SHAPE_DUPLICATE_COMPLETE) */
    duplicate?: Shape
  }
}

/**
 * Shape add event
 */
export interface ShapeAddEvent extends BaseEvent {
  type: AppEventType.ShapeAdd
  payload: Shape
}

/**
 * Selection changed event payload
 */
export interface SelectionChangedPayload {
  /** IDs of currently selected shapes */
  selectedIds: string[]
}

/**
 * Selection changed event
 * Event published when the set of selected shapes changes
 */
export interface SelectionChangedEvent extends BaseEvent {
  type: AppEventType.SelectionChanged
  payload: SelectionChangedPayload
}

/**
 * Shape select request payload
 */
export interface ShapeSelectRequestPayload {
  /** Optional: Select specific shapes by ID */
  shapeIds?: string[]
  /** Optional: Select shapes within an area */
  area?: { x: number, y: number, x2: number, y2: number }
  /** Optional: Select shape at a point */
  point?: { x: number, y: number }
  /** Optional: Selection mode (Replace, Add, Clear) */
  selectionMode?: string
}

/**
 * Shape select request event
 */
export interface ShapeSelectRequestEvent extends BaseEvent {
  type: AppEventType.ShapeSelectRequest
  payload: ShapeSelectRequestPayload
}

/**
 * Shape update request payload
 */
export interface ShapeUpdateRequestPayload {
  /** Shape ID to update */
  shapeId: string
  /** Updates to apply to the shape */
  updates: Partial<Shape>
}

/**
 * Shape update request event
 */
export interface ShapeUpdateRequestEvent extends BaseEvent {
  type: AppEventType.ShapeUpdateRequest
  payload: ShapeUpdateRequestPayload
}

/**
 * Shape update success payload
 */
export interface ShapeUpdateSuccessPayload {
  /** Shape ID that was updated */
  shapeId: string
  /** Updated shape data */
  updatedShape: Shape
}

/**
 * Shape update success event
 */
export interface ShapeUpdateSuccessEvent extends BaseEvent {
  type: AppEventType.ShapeUpdateSuccess
  payload: ShapeUpdateSuccessPayload
}
