/**
 * Perimeter/Length Calculation Strategy for Arc Elements
 *
 * @remarks
 * This strategy implements the {@link PerimeterCalculatorStrategy} for calculating
 * the perimeter (or length) of Arc elements ({@link CoreElementType.ARC}).
 *
 * The calculation involves:
 * 1. Determining the arc length using its `radius` and the angular difference
 *    between `startAngle` and `endAngle`. This part is delegated to the
 *    `calculateArcLengthUtil` function from `../../../../lib/utils/geometry/arcUtils`.
 * 2. If the arc is marked as `closed` (i.e., it forms a circular sector or "pie slice"),
 *    the lengths of the two radii connecting the arc's endpoints to its center
 *    are added to the arc length to get the total perimeter of the sector.
 * 3. If the arc is not `closed`, its "perimeter" is considered to be just its length.
 *
 * The strategy validates that the element is of type `ARC` and that its
 * defining properties (radius, angles) are valid.
 *
 * @module core/compute/strategies/perimeter/ArcPerimeterStrategy
 * @see {@link Path.Arc} for the arc element type definition.
 * @see {@link PerimeterCalculatorStrategy}
 * @see {@link calculateArcLengthUtil}
 */
import type { PerimeterCalculatorStrategy } from '../../../../types/core/compute'
import type {
  Element,
  Path,
} from '../../../../types/core/elementDefinitions'
import { calculateArcLength as calculateArcLengthUtil } from '../../../../lib/utils/geometry/arcUtils'
import { toRadians } from '../../../../lib/utils/math'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

// Local degreesToRadians helper removed

/**
 * Implements the {@link PerimeterCalculatorStrategy} for {@link CoreElementType.ARC} elements.
 * It computes the length of the arc segment and, if the arc is 'closed',
 * adds the lengths of the two radii to form the perimeter of the sector.
 */
export class ArcPerimeterStrategy implements PerimeterCalculatorStrategy {
  /**
   * Calculates the perimeter of an Arc element.
   *
   * @remarks
   * If the arc is `closed` (forming a sector), the perimeter is the arc length plus twice the radius.
   * If the arc is open, the perimeter is simply the arc length.
   *
   * @param element - The arc element, expected to conform to {@link Path.Arc} and have type {@link CoreElementType.ARC}.
   * @returns The calculated perimeter.
   * @throws {@link CoreError} if the provided element is not of type `ARC`.
   * @throws {@link CoreError} if the arc's `radius`, `startAngle`, or `endAngle` properties are invalid.
   * @throws {@link CoreError} if the utility function `calculateArcLengthUtil` returns `NaN`.
   */
  public calculatePerimeter(element: Element): number {
    console.warn(`[ArcPerimeterStrategy] 🚀 calculatePerimeter 开始执行`, {
      elementId: element.id,
      elementType: element.type,
      element,
    })

    if (element.type !== CoreElementType.ARC) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `ArcPerimeterStrategy can only calculate perimeter for ARC elements, got ${element.type}`,
        undefined,
        { component: 'ArcPerimeterStrategy', operation: 'calculatePerimeter', target: element.id },
      )
    }

    const arcElement = element as unknown as Path.Arc // Added unknown for type safety

    const radius = arcElement.properties.radius
    const startAngleDegrees = arcElement.properties.startAngle
    const endAngleDegrees = arcElement.properties.endAngle
    const closed = arcElement.properties.closed === true

    console.warn(`[ArcPerimeterStrategy] 圆弧参数:`, {
      radius,
      startAngleDegrees,
      endAngleDegrees,
      closed,
      properties: arcElement.properties,
    })

    if (typeof radius !== 'number' || radius <= 0 || !Number.isFinite(radius)) { // Arc length calculation needs positive radius
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid radius: ${radius} for Arc ID: ${element.id}. Radius must be a positive finite number.`,
        undefined,
        { component: 'ArcPerimeterStrategy', operation: 'calculatePerimeter', target: element.id, metadata: { radius } },
      )
    }
    if (typeof startAngleDegrees !== 'number' || !Number.isFinite(startAngleDegrees)
      || typeof endAngleDegrees !== 'number' || !Number.isFinite(endAngleDegrees)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid angles for Arc ID: ${element.id}. Original degrees: start=${startAngleDegrees}, end=${endAngleDegrees}`,
        undefined,
        { component: 'ArcPerimeterStrategy', operation: 'calculatePerimeter', target: element.id, metadata: { startAngleDegrees, endAngleDegrees } },
      )
    }

    const startAngleRad = toRadians(startAngleDegrees)
    const endAngleRad = toRadians(endAngleDegrees)

    let angleDiffRad = endAngleRad - startAngleRad
    // Normalize angle difference to be between 0 and 2*PI for arc length calculation
    angleDiffRad = angleDiffRad % (2 * Math.PI)
    if (angleDiffRad < 0) {
      angleDiffRad += 2 * Math.PI
    }

    const arcLength = calculateArcLengthUtil(radius, angleDiffRad)
    console.warn(`[ArcPerimeterStrategy] 计算弧长:`, {
      radius,
      angleDiffRad,
      arcLength,
      isNaN: Number.isNaN(arcLength),
    })

    if (Number.isNaN(arcLength)) {
      throw new CoreError(
        ErrorType.ComputationError, // More specific if util failed with valid-seeming inputs
        `Arc element (ID: ${element.id}) resulted in invalid arc length with radius: ${radius}, angleDiffRad: ${angleDiffRad}`,
        undefined,
        { component: 'ArcPerimeterStrategy', operation: 'calculatePerimeter', target: element.id, metadata: { radius, angleDiffRad } },
      )
    }

    let finalResult: number
    if (closed) {
      finalResult = arcLength + 2 * radius // Arc length + two radii for a pie slice
    }
    else {
      finalResult = arcLength // Length of the arc segment itself
    }

    console.warn(`[ArcPerimeterStrategy] ✅ 计算完成:`, {
      arcLength,
      closed,
      finalResult,
    })

    return finalResult
  }

  /**
   * Returns the element type that this strategy is responsible for.
   * @returns {@link CoreElementType.ARC} - The arc element type that this strategy handles
   */
  public getElementType(): CoreElementType {
    return CoreElementType.ARC
  }
}
