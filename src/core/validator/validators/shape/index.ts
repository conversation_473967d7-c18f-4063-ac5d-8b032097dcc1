/**
 * Centralized export for shape validator classes.
 *
 * @remarks
 * This index file serves as the public entry point for the shape validator implementations
 * within the `@/core/validator/validators/shape` directory. It exports the concrete
 * implementations for each basic shape type (`RectangleValidator`, `EllipseValidator`, etc.).
 *
 * @module core/validator/validators/shape
 */

export { EllipseValidator } from './ellipseValidator' // Handles Ellipse and Circle
export { PolygonValidator } from './polygonValidator' // Handles Polygon, Triangle, Hexagon
// Export concrete shape validator implementations
export { RectangleValidator } from './rectangleValidator' // Handles Rectangle and Square
