/**
 * Shape Event Publishers Module
 *
 * This module provides helper functions for publishing shape-related events
 * to the application's event bus. It covers shape creation, editing, deletion,
 * selection, and duplication events.
 *
 * All functions follow dependency injection principles by accepting an EventBus
 * instance as their first parameter.
 *
 * @module services/core/event-bus/helpers/publishers/shapePublishers
 */

import type Point from '@/types/core/element/geometry/point' // Corrected import for Point
import type { ElementType } from '@/types/core/elementDefinitions'
import type { EventBus } from '@/types/services/events'
import type { SelectionChangedEvent, Shape, ShapeCreateEvent, ShapeDeleteEvent, ShapeDuplicateEvent, ShapeEditEvent, ShapeSelectRequestPayload } from '@/types/services/events/shapeEvents'
import { AppEventType } from '@/types/services/events'
import { SelectionMode } from '@/types/services/shapes' // Import SelectionMode

// Removed local definitions of ElementType, Point, ShapeModel, and SelectionMode

/**
 * Publish shape create request event
 *
 * Triggers the creation of a new shape in the system with the specified
 * type, position, and properties.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param elementType - Type of shape to create (rectangle, circle, etc.)
 * @param position - Initial position of the shape
 * @param properties - Additional shape-specific properties
 */
export function publishShapeCreateRequest(
  eventBus: EventBus,
  elementType: ElementType,
  position: Point,
  properties: Record<string, unknown>,
): void {
  const event: ShapeCreateEvent = {
    type: AppEventType.ShapeCreateRequest,
    payload: {
      ElementType: elementType.toString(),
      position: { x: position.x, y: position.y },
      properties,
    },
  }
  eventBus.publish(event)
}

/**
 * Publish shape edit request event
 *
 * Requests modification of an existing shape's properties or position.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param shapeId - ID of the shape to edit
 * @param changes - Changes to apply to the shape (partial properties or position)
 */
export function publishShapeEditRequest(
  eventBus: EventBus,
  shapeId: string,
  changes: Partial<Record<string, unknown>> & { position?: Point },
): void {
  const eventProperties: Partial<Shape> = {}

  for (const key in changes) {
    if (key !== 'position' && Object.prototype.hasOwnProperty.call(changes, key)) {
      eventProperties[key] = (changes as Record<string, unknown>)[key]
    }
  }

  if (changes.position) {
    eventProperties.x = changes.position.x
    eventProperties.y = changes.position.y
    if (typeof changes.position.z === 'number') {
      eventProperties.z = changes.position.z
    }
  }

  const event: ShapeEditEvent = {
    type: AppEventType.ShapeEditRequest,
    timestamp: Date.now(),
    payload: {
      shapeId,
      properties: eventProperties,
    },
  }
  eventBus.publish(event)
}

/**
 * Publish shape delete request event
 *
 * Requests deletion of one or more shapes from the system.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param shapeIds - Array of shape IDs to delete
 */
export function publishShapeDeleteRequest(eventBus: EventBus, shapeIds: string[]): void {
  const event: ShapeDeleteEvent = {
    type: AppEventType.ShapeDeleteRequest,
    payload: {
      shapeId: shapeIds,
    },
  }
  eventBus.publish(event)
}

/**
 * Publish shape duplication request event
 *
 * Requests duplication of an existing shape at a new position.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param originalId - ID of the shape to duplicate
 * @param position - Position for the duplicated shape
 */
export function publishShapeDuplicationRequest(eventBus: EventBus, originalId: string, position: Point): void {
  const event: ShapeDuplicateEvent = {
    type: AppEventType.ShapeDuplicateRequest,
    payload: { originalId, position },
  }
  eventBus.publish(event)
}

/**
 * Publish selection changed event
 *
 * Notifies the system that the selection of shapes has changed.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param selectedShapeIds - Array of currently selected shape IDs
 */
export function publishSelectionChanged(eventBus: EventBus, selectedShapeIds: string[]): void {
  const event: SelectionChangedEvent = {
    type: AppEventType.SelectionChanged,
    payload: { selectedIds: selectedShapeIds },
  }
  eventBus.publish(event)
}

/**
 * Publish shape select request event
 *
 * Requests a change in the current shape selection using the specified mode.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param shapeIds - Array of shape IDs to select
 * @param mode - Selection mode (replace, add, remove, clear)
 */
export function publishShapeSelectRequest(
  eventBus: EventBus,
  shapeIds: string[],
  mode: SelectionMode = SelectionMode.Replace, // Use PascalCase for enum member
): void {
  eventBus.publish({
    type: AppEventType.ShapeSelectRequest,
    payload: {
      shapeIds,
      selectionMode: mode,
    } as ShapeSelectRequestPayload & { selectionMode: SelectionMode },
  })
}

/**
 * Publish shape deselected event
 *
 * Notifies the system that all shapes have been deselected.
 * This is a convenience method that publishes a SELECTION_CHANGED event with an empty array.
 *
 * @param eventBus - The EventBus instance to use for publishing
 */
export function publishShapeDeselected(eventBus: EventBus): void {
  publishSelectionChanged(eventBus, [])
}
