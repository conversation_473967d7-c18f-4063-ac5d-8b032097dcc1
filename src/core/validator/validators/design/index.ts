/**
 * @file index.ts
 * @description Centralized export for all design element validators (e.g., Wall, Room, Opening).
 * This file re-exports specific validator classes from the `design` subdirectory,
 * making them easily accessible for use by the `ElementValidator` or other parts of the application
 * that require validation of design-related elements.
 *
 * @module core/validator/validators/design
 */

export * from './designValidatorBase'
export * from './openingValidator'
export * from './roomValidator'
export * from './wallValidator'
// export * from './imageValidator'; // Removed as it does not exist here and is exported from media path

// TODO: Add other design element validators as they are created (e.g., RoofValidator, LandscapeValidator)
