import { describe, expect, it, vi } from 'vitest'
import { WoodMaterialStrategy } from '@/core/compute/strategies/material/WoodMaterialStrategy'
import { CoreError } from '@/services/errors'

describe('woodMaterialStrategy', () => {
  const strategy = new WoodMaterialStrategy()

  it('should return the correct shape type', () => {
    expect(strategy.getElementType()).toBe('rectangle')
  })

  it('should return the correct material type', () => {
    expect(strategy.getMaterialType()).toBe('wood')
  })

  it('should calculate wood material amount correctly', () => {
    const mockElement = {
      id: 'floor-1',
      type: 'shape',
      getArea: vi.fn().mockReturnValue(20), // 20 square meters
    }

    const result = strategy.calculateMaterialAmount(mockElement, 'wood', {
      boardWidth: 0.15,
      boardLength: 1.2,
      thickness: 0.02,
      installationPattern: 'straight',
      expansion: 0.02,
    })

    expect(result.amount).toBeGreaterThan(20) // 考虑了膨胀和浪费
    expect(result.unitCount).toBeGreaterThan(0)
    expect((result as any).volume).toBeGreaterThan(0)
    expect((result as any).adhesive).toBeGreaterThan(0)
    expect((result as any).finish).toBeGreaterThan(0)
    expect((result as any).wasteFactor).toBe(0.1) // 直铺浪费率为10%
  })

  it('should adjust waste factor based on installation pattern', () => {
    const mockElement = {
      id: 'floor-1',
      type: 'shape',
      getArea: vi.fn().mockReturnValue(20), // 20 square meters
    }

    // 测试人字形安装方式
    const herringboneResult = strategy.calculateMaterialAmount(mockElement, 'wood', {
      installationPattern: 'herringbone',
    })
    expect((herringboneResult as any).wasteFactor).toBe(0.15) // 人字形浪费率为15%

    // 测试斜铺安装方式
    const diagonalResult = strategy.calculateMaterialAmount(mockElement, 'wood', {
      installationPattern: 'diagonal',
    })
    expect((diagonalResult as any).wasteFactor).toBe(0.18) // 斜铺浪费率为18%

    // 测试拼花安装方式
    const parquetResult = strategy.calculateMaterialAmount(mockElement, 'wood', {
      installationPattern: 'parquet',
    })
    expect((parquetResult as any).wasteFactor).toBe(0.12) // 拼花浪费率为12%
  })

  it('should use default values when options are not provided', () => {
    const mockElement = {
      id: 'floor-1',
      type: 'shape',
      getArea: vi.fn().mockReturnValue(20), // 20 square meters
    }

    const result = strategy.calculateMaterialAmount(mockElement, 'wood')

    expect(result.amount).toBeGreaterThan(20)
    expect(result.unitCount).toBeGreaterThan(0)
    expect((result as any).volume).toBeGreaterThan(0)
    expect((result as any).adhesive).toBeGreaterThan(0)
    expect((result as any).finish).toBeGreaterThan(0)
    expect((result as any).wasteFactor).toBe(0.1) // 默认浪费率为10%
  })

  it('should throw error for invalid element type', () => {
    const mockElement = {
      id: 'invalid-1',
      type: 'invalid',
    }

    expect(() => strategy.calculateMaterialAmount(mockElement, 'wood')).toThrow(CoreError)
    expect(() => strategy.calculateMaterialAmount(mockElement, 'wood')).toThrow('Invalid area for element invalid-1')
  })

  it('should throw error for invalid material type', () => {
    const mockElement = {
      id: 'floor-1',
      type: 'shape',
      getArea: vi.fn().mockReturnValue(20), // 20 square meters
    }

    expect(() => strategy.calculateMaterialAmount(mockElement, 'invalid')).toThrow(CoreError)
    expect(() => strategy.calculateMaterialAmount(mockElement, 'invalid')).toThrow('WoodMaterialStrategy can only calculate for \'wood\' material type')
  })

  it('should throw error if element has no area calculation method', () => {
    const mockElement = {
      id: 'floor-1',
      type: 'shape',
      // 没有 getArea 方法
    }

    expect(() => strategy.calculateMaterialAmount(mockElement, 'wood')).toThrow(CoreError)
    expect(() => strategy.calculateMaterialAmount(mockElement, 'wood')).toThrow('Invalid area for element floor-1')
  })

  it('should throw error if area calculation returns invalid value', () => {
    const mockElement = {
      id: 'floor-1',
      type: 'shape',
      getArea: vi.fn().mockReturnValue(-10), // Negative area
    }

    expect(() => strategy.calculateMaterialAmount(mockElement, 'wood')).toThrow(CoreError)
    expect(() => strategy.calculateMaterialAmount(mockElement, 'wood')).toThrow('Invalid area for element floor-1')
  })
})
