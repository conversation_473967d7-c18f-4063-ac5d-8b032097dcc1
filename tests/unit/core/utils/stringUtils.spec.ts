/**
 * @file stringUtils.spec.ts
 * @description Unit tests for string utility functions
 */

import { describe, expect, it } from 'vitest'
import {
  camelCase,
  capitalize,
  generateUUID,
  isUUID,
  kebabCase,
  pascalCase,
  slugify,
  snakeCase,
  truncate,
} from '@/core/utils/stringUtils'

describe('stringUtils', () => {
  describe('capitalize', () => {
    it('should capitalize the first letter of a string', () => {
      expect(capitalize('hello')).toBe('Hello')
      expect(capitalize('world')).toBe('World')
    })

    it('should handle empty strings', () => {
      expect(capitalize('')).toBe('')
    })

    it('should handle strings that are already capitalized', () => {
      expect(capitalize('Hello')).toBe('Hello')
    })

    it('should handle single character strings', () => {
      expect(capitalize('a')).toBe('A')
      expect(capitalize('Z')).toBe('Z')
    })

    it('should handle strings with non-letter characters at the beginning', () => {
      expect(capitalize('123abc')).toBe('123abc')
      expect(capitalize(' hello')).toBe(' hello')
    })
  })

  describe('camelCase', () => {
    it('should convert a string to camelCase', () => {
      expect(camelCase('hello world')).toBe('helloWorld')
      expect(camelCase('Hello World')).toBe('helloWorld')
      expect(camelCase('hello-world')).toBe('helloWorld')
      expect(camelCase('hello_world')).toBe('helloWorld')
    })

    it('should handle empty strings', () => {
      expect(camelCase('')).toBe('')
    })

    it('should handle single word strings', () => {
      expect(camelCase('hello')).toBe('hello')
      expect(camelCase('Hello')).toBe('hello')
    })

    it('should handle strings with numbers', () => {
      expect(camelCase('hello 123')).toBe('hello123')
      expect(camelCase('hello-123')).toBe('hello123')
    })

    it('should handle strings with special characters', () => {
      expect(camelCase('hello!world')).toBe('helloWorld')
      expect(camelCase('hello.world')).toBe('helloWorld')
    })
  })

  describe('kebabCase', () => {
    it('should convert a string to kebab-case', () => {
      expect(kebabCase('hello world')).toBe('hello-world')
      expect(kebabCase('Hello World')).toBe('hello-world')
      expect(kebabCase('helloWorld')).toBe('hello-world')
      expect(kebabCase('hello_world')).toBe('hello-world')
    })

    it('should handle empty strings', () => {
      expect(kebabCase('')).toBe('')
    })

    it('should handle single word strings', () => {
      expect(kebabCase('hello')).toBe('hello')
      expect(kebabCase('Hello')).toBe('hello')
    })

    it('should handle strings with numbers', () => {
      expect(kebabCase('hello 123')).toBe('hello-123')
      expect(kebabCase('hello123')).toBe('hello-123')
    })

    it('should handle strings with special characters', () => {
      expect(kebabCase('hello!world')).toBe('hello-world')
      expect(kebabCase('hello.world')).toBe('hello-world')
    })
  })

  describe('snakeCase', () => {
    it('should convert a string to snake_case', () => {
      expect(snakeCase('hello world')).toBe('hello_world')
      expect(snakeCase('Hello World')).toBe('hello_world')
      expect(snakeCase('helloWorld')).toBe('hello_world')
      expect(snakeCase('hello-world')).toBe('hello_world')
    })

    it('should handle empty strings', () => {
      expect(snakeCase('')).toBe('')
    })

    it('should handle single word strings', () => {
      expect(snakeCase('hello')).toBe('hello')
      expect(snakeCase('Hello')).toBe('hello')
    })

    it('should handle strings with numbers', () => {
      expect(snakeCase('hello 123')).toBe('hello_123')
      expect(snakeCase('hello123')).toBe('hello_123')
    })

    it('should handle strings with special characters', () => {
      expect(snakeCase('hello!world')).toBe('hello_world')
      expect(snakeCase('hello.world')).toBe('hello_world')
    })
  })

  describe('pascalCase', () => {
    it('should convert a string to PascalCase', () => {
      expect(pascalCase('hello world')).toBe('HelloWorld')
      expect(pascalCase('Hello World')).toBe('HelloWorld')
      expect(pascalCase('helloWorld')).toBe('HelloWorld')
      expect(pascalCase('hello-world')).toBe('HelloWorld')
      expect(pascalCase('hello_world')).toBe('HelloWorld')
    })

    it('should handle empty strings', () => {
      expect(pascalCase('')).toBe('')
    })

    it('should handle single word strings', () => {
      expect(pascalCase('hello')).toBe('Hello')
      expect(pascalCase('Hello')).toBe('Hello')
    })

    it('should handle strings with numbers', () => {
      expect(pascalCase('hello 123')).toBe('Hello123')
      expect(pascalCase('hello123')).toBe('Hello123')
    })

    it('should handle strings with special characters', () => {
      expect(pascalCase('hello!world')).toBe('HelloWorld')
      expect(pascalCase('hello.world')).toBe('HelloWorld')
    })
  })

  describe('truncate', () => {
    it('should truncate a string to the specified length', () => {
      expect(truncate('Hello, world!', 5)).toBe('Hello...')
      expect(truncate('Hello', 10)).toBe('Hello')
    })

    it('should use the provided suffix', () => {
      expect(truncate('Hello, world!', 5, '...')).toBe('Hello...')
      expect(truncate('Hello, world!', 5, '---')).toBe('Hello---')
    })

    it('should handle empty strings', () => {
      expect(truncate('', 5)).toBe('')
    })

    it('should handle length less than or equal to 0', () => {
      expect(truncate('Hello', 0)).toBe('...')
      expect(truncate('Hello', -5)).toBe('...')
    })

    it('should handle length less than suffix length', () => {
      expect(truncate('Hello', 2, '...')).toBe('...')
    })
  })

  describe('slugify', () => {
    it('should convert a string to a URL-friendly slug', () => {
      expect(slugify('Hello World')).toBe('hello-world')
      expect(slugify('Hello, World!')).toBe('hello-world')
      expect(slugify('Hello   World')).toBe('hello-world')
    })

    it('should handle empty strings', () => {
      expect(slugify('')).toBe('')
    })

    it('should handle strings with special characters', () => {
      expect(slugify('Hello & World')).toBe('hello-and-world')
      expect(slugify('Hello @ World')).toBe('hello-at-world')
    })

    it('should handle strings with accented characters', () => {
      expect(slugify('Héllö Wörld')).toBe('hello-world')
      expect(slugify('Café')).toBe('cafe')
    })

    it('should handle strings with numbers', () => {
      expect(slugify('Hello 123')).toBe('hello-123')
      expect(slugify('123 Hello')).toBe('123-hello')
    })
  })

  describe('generateUUID', () => {
    it('should generate a valid UUID', () => {
      const uuid = generateUUID()
      expect(uuid).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)
    })

    it('should generate unique UUIDs', () => {
      const uuid1 = generateUUID()
      const uuid2 = generateUUID()
      expect(uuid1).not.toBe(uuid2)
    })
  })

  describe('isUUID', () => {
    it('should return true for valid UUIDs', () => {
      expect(isUUID('123e4567-e89b-12d3-a456-************')).toBe(true)
      expect(isUUID('00000000-0000-0000-0000-000000000000')).toBe(true)
    })

    it('should return false for invalid UUIDs', () => {
      expect(isUUID('not-a-uuid')).toBe(false)
      expect(isUUID('123e4567-e89b-12d3-a456-42661417400')).toBe(false) // Too short
      expect(isUUID('123e4567-e89b-12d3-a456-************0')).toBe(false) // Too long
      expect(isUUID('123e4567-e89b-12d3-a456-42661417400g')).toBe(false) // Invalid character
    })

    it('should handle empty strings', () => {
      expect(isUUID('')).toBe(false)
    })

    it('should handle null and undefined', () => {
      expect(isUUID(null as any)).toBe(false)
      expect(isUUID(undefined as any)).toBe(false)
    })
  })
})
