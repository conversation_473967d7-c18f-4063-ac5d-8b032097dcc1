import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ConsoleLoggerService } from '@/services/logging/ConsoleLoggerService'

describe('consoleLoggerService', () => {
  let logger: ConsoleLoggerService

  // Spy on console methods
  beforeEach(() => {
    // Create spies for all console methods
    vi.spyOn(console, 'info').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(console, 'debug').mockImplementation(() => {})

    // Create a new logger instance for each test
    logger = new ConsoleLoggerService()
  })

  afterEach(() => {
    // Restore original console methods
    vi.restoreAllMocks()
  })

  describe('info method', () => {
    it('should call console.info with the correct prefix and message', () => {
      const message = 'Test info message'
      logger.info(message)

      expect(console.info).toHaveBeenCalledWith('[INFO] Test info message')
    })

    it('should pass additional parameters to console.info', () => {
      const message = 'Test info message'
      const additionalParam = { userId: '123', role: 'admin' }

      logger.info(message, additionalParam)

      expect(console.info).toHaveBeenCalledWith('[INFO] Test info message', additionalParam)
    })

    it('should handle multiple additional parameters', () => {
      const message = 'Test info message'
      const param1 = { userId: '123' }
      const param2 = { role: 'admin' }

      logger.info(message, param1, param2)

      expect(console.info).toHaveBeenCalledWith('[INFO] Test info message', param1, param2)
    })
  })

  describe('warn method', () => {
    it('should call console.warn with the correct prefix and message', () => {
      const message = 'Test warning message'
      logger.warn(message)

      expect(console.warn).toHaveBeenCalledWith('[WARN] Test warning message')
    })

    it('should pass additional parameters to console.warn', () => {
      const message = 'Test warning message'
      const additionalParam = { endpoint: '/users', responseTime: 3500 }

      logger.warn(message, additionalParam)

      expect(console.warn).toHaveBeenCalledWith('[WARN] Test warning message', additionalParam)
    })
  })

  describe('error method', () => {
    it('should call console.error with the correct prefix and message', () => {
      const message = 'Test error message'
      logger.error(message)

      expect(console.error).toHaveBeenCalledWith('[ERROR] Test error message')
    })

    it('should pass additional parameters to console.error', () => {
      const message = 'Test error message'
      const error = new Error('Something went wrong')
      const additionalParam = { documentId: '456' }

      logger.error(message, error, additionalParam)

      expect(console.error).toHaveBeenCalledWith('[ERROR] Test error message', error, additionalParam)
    })
  })

  describe('debug method', () => {
    it('should call console.debug with the correct prefix and message', () => {
      const message = 'Test debug message'
      logger.debug(message)

      expect(console.debug).toHaveBeenCalledWith('[DEBUG] Test debug message')
    })

    it('should pass additional parameters to console.debug', () => {
      const message = 'Test debug message'
      const additionalParam = { shapeId: '789', vertices: 8 }

      logger.debug(message, additionalParam)

      expect(console.debug).toHaveBeenCalledWith('[DEBUG] Test debug message', additionalParam)
    })

    it('should handle complex objects in debug messages', () => {
      const message = 'Processing data'
      const complexObject = {
        id: 'abc123',
        nested: {
          property: 'value',
          array: [1, 2, 3],
        },
      }

      logger.debug(message, complexObject)

      expect(console.debug).toHaveBeenCalledWith('[DEBUG] Processing data', complexObject)
    })
  })
})
