import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import type { BaseEvent, EventSubscriptionOptions } from '@/types/services/events'

// Mock the event bus
const mockEventBus = {
  subscribe: vi.fn(),
  publish: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
  configure: vi.fn(),
  getSubscriptions: vi.fn().mockReturnValue(new Map()),
  reset: vi.fn(),
  createEvent: vi.fn(),
}

vi.mock('@/services/core/event-bus', () => ({
  appEventBus: mockEventBus,
}))

// Import after mocking
import { useEventBus } from '@/hooks/core/useEventBus'

describe('useEventBus (Real Implementation)', () => {
  beforeEach(() => {
    mockEventBus.subscribe.mockReturnValue(() => {}) // Mock unsubscribe function
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Hook Initialization', () => {
    it('should return subscribe and publish functions', () => {
      const { result } = renderHook(() => useEventBus())

      expect(typeof result.current.subscribe).toBe('function')
      expect(typeof result.current.publish).toBe('function')
      expect(typeof result.current.publishEvent).toBe('function')
      expect(typeof result.current.cleanup).toBe('function')
    })

    it('should initialize with empty subscriptions', () => {
      const { result } = renderHook(() => useEventBus())

      // The hook should start with no active subscriptions
      expect(result.current).toBeDefined()
    })
  })

  describe('Event Subscription', () => {
    it('should subscribe to events', () => {
      const { result } = renderHook(() => useEventBus())
      const handler = vi.fn()

      act(() => {
        result.current.subscribe('SHAPE_CREATED', handler)
      })

      expect(mockEventBus.subscribe).toHaveBeenCalledWith('SHAPE_CREATED', handler, undefined)
    })

    it('should subscribe with options', () => {
      const { result } = renderHook(() => useEventBus())
      const handler = vi.fn()
      const options: EventSubscriptionOptions = { priority: 1 }

      act(() => {
        result.current.subscribe('SHAPE_CREATED', handler, options)
      })

      expect(mockEventBus.subscribe).toHaveBeenCalledWith('SHAPE_CREATED', handler, options)
    })

    it('should return unsubscribe function', () => {
      const { result } = renderHook(() => useEventBus())
      const handler = vi.fn()
      const mockUnsubscribe = vi.fn()
      mockEventBus.subscribe.mockReturnValue(mockUnsubscribe)

      let unsubscribe: (() => void) | undefined

      act(() => {
        unsubscribe = result.current.subscribe('SHAPE_CREATED', handler)
      })

      expect(typeof unsubscribe).toBe('function')

      act(() => {
        unsubscribe?.()
      })

      expect(mockUnsubscribe).toHaveBeenCalled()
    })

    it('should handle multiple subscriptions', () => {
      const { result } = renderHook(() => useEventBus())
      const handler1 = vi.fn()
      const handler2 = vi.fn()

      act(() => {
        result.current.subscribe('SHAPE_CREATED', handler1)
        result.current.subscribe('SHAPE_DELETED', handler2)
      })

      expect(mockEventBus.subscribe).toHaveBeenCalledTimes(2)
      expect(mockEventBus.subscribe).toHaveBeenCalledWith('SHAPE_CREATED', handler1, undefined)
      expect(mockEventBus.subscribe).toHaveBeenCalledWith('SHAPE_DELETED', handler2, undefined)
    })

    it('should track subscriptions for cleanup', () => {
      const { result } = renderHook(() => useEventBus())
      const handler = vi.fn()

      act(() => {
        result.current.subscribe('SHAPE_CREATED', handler)
      })

      // The hook should track this subscription internally
      expect(mockEventBus.subscribe).toHaveBeenCalled()
    })
  })

  describe('Event Publishing', () => {
    it('should publish events using publish method', () => {
      const { result } = renderHook(() => useEventBus())
      const event: BaseEvent = {
        type: 'SHAPE_CREATED',
        payload: { shapeId: 'test-shape' },
      }

      act(() => {
        result.current.publish(event)
      })

      expect(mockEventBus.publish).toHaveBeenCalledWith(event)
    })

    it('should publish events using publishEvent method', () => {
      const { result } = renderHook(() => useEventBus())
      const payload = { shapeId: 'test-shape' }

      act(() => {
        result.current.publishEvent('SHAPE_CREATED', payload)
      })

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: 'SHAPE_CREATED',
        payload,
      })
    })

    it('should handle events without payload', () => {
      const { result } = renderHook(() => useEventBus())

      act(() => {
        result.current.publishEvent('CANVAS_CLEARED', undefined)
      })

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: 'CANVAS_CLEARED',
        payload: undefined,
      })
    })

    it('should handle null payload', () => {
      const { result } = renderHook(() => useEventBus())

      act(() => {
        result.current.publishEvent('SHAPE_CREATED', null)
      })

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: 'SHAPE_CREATED',
        payload: null,
      })
    })
  })

  describe('Subscription Management', () => {
    it('should clean up subscriptions on unsubscribe', () => {
      const { result } = renderHook(() => useEventBus())
      const handler = vi.fn()
      const mockUnsubscribe = vi.fn()
      mockEventBus.subscribe.mockReturnValue(mockUnsubscribe)

      let unsubscribe: (() => void) | undefined

      act(() => {
        unsubscribe = result.current.subscribe('SHAPE_CREATED', handler)
      })

      act(() => {
        unsubscribe?.()
      })

      expect(mockUnsubscribe).toHaveBeenCalled()
    })

    it('should handle cleanup of all subscriptions', () => {
      const { result } = renderHook(() => useEventBus())
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      const mockUnsubscribe1 = vi.fn()
      const mockUnsubscribe2 = vi.fn()

      mockEventBus.subscribe
        .mockReturnValueOnce(mockUnsubscribe1)
        .mockReturnValueOnce(mockUnsubscribe2)

      act(() => {
        result.current.subscribe('SHAPE_CREATED', handler1)
        result.current.subscribe('SHAPE_DELETED', handler2)
      })

      act(() => {
        result.current.cleanup()
      })

      expect(mockUnsubscribe1).toHaveBeenCalled()
      expect(mockUnsubscribe2).toHaveBeenCalled()
    })

    it('should handle duplicate subscriptions', () => {
      const { result } = renderHook(() => useEventBus())
      const handler = vi.fn()

      act(() => {
        result.current.subscribe('SHAPE_CREATED', handler)
        result.current.subscribe('SHAPE_CREATED', handler) // Same handler again
      })

      expect(mockEventBus.subscribe).toHaveBeenCalledTimes(2)
    })

    it('should handle unsubscribe of non-existent subscription', () => {
      const { result } = renderHook(() => useEventBus())
      const handler = vi.fn()
      const mockUnsubscribe = vi.fn()
      mockEventBus.subscribe.mockReturnValue(mockUnsubscribe)

      let unsubscribe: (() => void) | undefined

      act(() => {
        unsubscribe = result.current.subscribe('SHAPE_CREATED', handler)
      })

      act(() => {
        unsubscribe?.()
        unsubscribe?.() // Call again
      })

      expect(mockUnsubscribe).toHaveBeenCalledTimes(2)
    })
  })

  describe('Hook Lifecycle', () => {
    it('should maintain subscriptions across re-renders', () => {
      const { result, rerender } = renderHook(() => useEventBus())
      const handler = vi.fn()

      act(() => {
        result.current.subscribe('SHAPE_CREATED', handler)
      })

      rerender()

      // Should still be able to publish events
      act(() => {
        result.current.publishEvent('SHAPE_CREATED', { shapeId: 'test' })
      })

      expect(mockEventBus.publish).toHaveBeenCalled()
    })

    it('should clean up on unmount', () => {
      const { result, unmount } = renderHook(() => useEventBus())
      const handler = vi.fn()
      const mockUnsubscribe = vi.fn()
      mockEventBus.subscribe.mockReturnValue(mockUnsubscribe)

      act(() => {
        result.current.subscribe('SHAPE_CREATED', handler)
      })

      unmount()

      // Cleanup should be called automatically
      // Note: This depends on the implementation using useEffect cleanup
    })
  })

  describe('Error Handling', () => {
    it('should handle subscription errors gracefully', () => {
      const { result } = renderHook(() => useEventBus())
      const handler = vi.fn()
      mockEventBus.subscribe.mockImplementation(() => {
        throw new Error('Subscription failed')
      })

      expect(() => {
        act(() => {
          result.current.subscribe('SHAPE_CREATED', handler)
        })
      }).not.toThrow()
    })

    it('should handle publish errors gracefully', () => {
      const { result } = renderHook(() => useEventBus())
      mockEventBus.publish.mockImplementation(() => {
        throw new Error('Publish failed')
      })

      expect(() => {
        act(() => {
          result.current.publishEvent('SHAPE_CREATED', { shapeId: 'test' })
        })
      }).not.toThrow()
    })

    it('should handle unsubscribe errors gracefully', () => {
      const { result } = renderHook(() => useEventBus())
      const handler = vi.fn()
      const mockUnsubscribe = vi.fn(() => {
        throw new Error('Unsubscribe failed')
      })
      mockEventBus.subscribe.mockReturnValue(mockUnsubscribe)

      let unsubscribe: (() => void) | undefined

      act(() => {
        unsubscribe = result.current.subscribe('SHAPE_CREATED', handler)
      })

      expect(() => {
        act(() => {
          unsubscribe?.()
        })
      }).not.toThrow()
    })
  })

  describe('Type Safety', () => {
    it('should handle typed event subscriptions', () => {
      const { result } = renderHook(() => useEventBus())
      const handler = vi.fn()

      act(() => {
        result.current.subscribe('SHAPE_CREATED', handler)
      })

      expect(mockEventBus.subscribe).toHaveBeenCalledWith('SHAPE_CREATED', handler, undefined)
    })

    it('should handle typed event publishing', () => {
      const { result } = renderHook(() => useEventBus())

      act(() => {
        result.current.publishEvent('SHAPE_CREATED', { shapeId: 'test', shapeType: 'rectangle' })
      })

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: 'SHAPE_CREATED',
        payload: { shapeId: 'test', shapeType: 'rectangle' },
      })
    })
  })

  describe('Performance', () => {
    it('should handle many subscriptions efficiently', () => {
      const { result } = renderHook(() => useEventBus())
      const handlers = Array.from({ length: 100 }, () => vi.fn())

      const startTime = Date.now()

      act(() => {
        handlers.forEach((handler, index) => {
          result.current.subscribe(`EVENT_${index}`, handler)
        })
      })

      const endTime = Date.now()

      expect(mockEventBus.subscribe).toHaveBeenCalledTimes(100)
      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly
    })

    it('should handle many publications efficiently', () => {
      const { result } = renderHook(() => useEventBus())

      const startTime = Date.now()

      act(() => {
        for (let i = 0; i < 100; i++) {
          result.current.publishEvent('SHAPE_CREATED', { shapeId: `shape-${i}` })
        }
      })

      const endTime = Date.now()

      expect(mockEventBus.publish).toHaveBeenCalledTimes(100)
      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly
    })
  })
})
