/**
 * Perimeter/Length Calculation Strategy for Cubic Bezier Curve Elements
 *
 * @remarks
 * This strategy implements the {@link PerimeterCalculatorStrategy} for calculating
 * the perimeter (length) of Cubic Bezier curve elements ({@link CoreElementType.CUBIC}).
 *
 * The length of the curve itself is approximated using Gaussian quadrature via the
 * `calculateCubicBezierLengthGauss` utility function from
 * `../../../../lib/utils/geometry/bezierUtils`.
 *
 * If the `closed` property of the cubic Bezier curve element is `true`, this strategy
 * also calculates the straight-line distance between the curve's start and end points
 * (using `calculateDistance` from `../../../../lib/utils/geometry/pointUtils`)
 * and adds this to the curve length to form the total perimeter of the closed shape.
 *
 * The strategy validates that the element is of type `CUBIC` and that its
 * defining points (start, control1, control2, end) are valid.
 *
 * @module core/compute/strategies/perimeter/CubicPerimeterStrategy
 * @see {@link Path.Cubic} for the cubic Bezier curve element type definition.
 * @see {@link PerimeterCalculatorStrategy}
 * @see {@link calculateCubicBezierLengthGauss}
 * @see {@link calculateDistance}
 */
import type { PerimeterCalculatorStrategy } from '../../../../types/core/compute'
import type { PointData as IPoint } from '../../../../types/core/element/geometry/point' // Use PointData, alias as IPoint
import type {
  Element,
  Path,
  ShapeElement,
} from '../../../../types/core/elementDefinitions'
import { calculateCubicBezierLengthGauss } from '../../../../lib/utils/geometry/bezierUtils'
import { calculateDistance } from '../../../../lib/utils/geometry/pointUtils' // For closing segment
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

/**
 * Implements the {@link PerimeterCalculatorStrategy} for {@link CoreElementType.CUBIC} elements.
 * It uses Gaussian quadrature for accurate curve length approximation and adds the
 * closing segment length if the curve is marked as `closed`.
 */
export class CubicPerimeterStrategy implements PerimeterCalculatorStrategy {
  /**
   * Calculates the perimeter of a Cubic Bezier curve element.
   *
   * @remarks
   * The curve length is calculated using Gaussian quadrature. If the curve is `closed`,
   * the straight-line distance between its start and end points is added to the curve length.
   *
   * @param element - The cubic Bezier curve element, expected to be of type {@link ShapeElement}
   *                  and specifically {@link CoreElementType.CUBIC}, conforming to {@link Path.Cubic}.
   * @returns The calculated perimeter.
   * @throws {@link CoreError} if the provided element is not of type `CUBIC`.
   * @throws {@link CoreError} if the curve's control points (`start`, `control1`, `control2`, `end`) are invalid.
   */
  public calculatePerimeter(element: Element): number {
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement.type !== CoreElementType.CUBIC) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `CubicPerimeterStrategy can only calculate perimeter for CUBIC elements, got ${shapeElement.type}`,
        undefined,
        { component: 'CubicPerimeterStrategy', operation: 'calculatePerimeter', target: shapeElement.id },
      )
    }

    const cubicElement = shapeElement as unknown as Path.Cubic // Added unknown for type safety

    const p0 = cubicElement.properties?.start as IPoint | undefined
    const p1 = cubicElement.properties?.control1 as IPoint | undefined
    const p2 = cubicElement.properties?.control2 as IPoint | undefined
    const p3 = cubicElement.properties?.end as IPoint | undefined
    const closed = cubicElement.properties?.closed === true

    if (p0 === null || p0 === undefined || p1 === null || p1 === undefined || p2 === null || p2 === undefined || p3 === null || p3 === undefined
      || typeof p0.x !== 'number' || typeof p0.y !== 'number' || !Number.isFinite(p0.x) || !Number.isFinite(p0.y)
      || typeof p1.x !== 'number' || typeof p1.y !== 'number' || !Number.isFinite(p1.x) || !Number.isFinite(p1.y)
      || typeof p2.x !== 'number' || typeof p2.y !== 'number' || !Number.isFinite(p2.x) || !Number.isFinite(p2.y)
      || typeof p3.x !== 'number' || typeof p3.y !== 'number' || !Number.isFinite(p3.x) || !Number.isFinite(p3.y)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid cubic curve points for element ID ${shapeElement.id}`,
        undefined,
        { component: 'CubicPerimeterStrategy', operation: 'calculatePerimeter', target: shapeElement.id, metadata: { p0, p1, p2, p3 } },
      )
    }

    // Delegate to the utility function for curve length
    const length = calculateCubicBezierLengthGauss(p0, p1, p2, p3)

    if (closed) {
      // Calculate distance between relative start and end points for closing segment
      const closingLength = calculateDistance(p0, p3)
      return length + closingLength
    }

    return length
  }

  // private calculateCubicCurveLengthInternal method removed

  /**
   * Returns the element type that this strategy is responsible for.
   * @returns {@link CoreElementType.CUBIC} - The cubic Bezier curve element type
   */
  public getElementType(): CoreElementType {
    return CoreElementType.CUBIC
  }
}
