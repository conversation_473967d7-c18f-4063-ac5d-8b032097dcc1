import type {
  ExportActions,
  ExportOptions,
  ExportService,
} from '@/types/hooks/export'
import { useCallback, useEffect, useState } from 'react'
import { appEventBus } from '@/services/core/event-bus'
import {
  ExportEventTypes,
  ExportFormat,
} from '@/types/services/events/exportEvents'
import { useEventBus } from './core/useEventBus'

export function useExport(): ExportActions & Partial<ExportService> {
  const { subscribe } = useEventBus()
  console.log('[useExport] Hook initialized')

  // 添加缺少的状态定义
  const [isExporting, setIsExporting] = useState(false)
  const [exportProgress, setExportProgress] = useState(0)
  const [exportError, setExportError] = useState<string | null>(null)

  useEffect(() => {
    console.log('[useExport] Setting up event subscriptions')

    const unsubscribeProgress = subscribe(
      ExportEventTypes.ExportProgress,
      (event) => {
        console.log('[useExport] Received ExportProgress:', event)
        setExportProgress(event.payload.progress || 0)
      },
    )

    const unsubscribeComplete = subscribe(
      ExportEventTypes.ExportComplete,
      (event: { payload: { data?: Blob, fileName?: string, format?: ExportFormat | string } }) => {
        console.log('[useExport] Received ExportComplete:', event)
        setIsExporting(false)
        setExportProgress(100)

        console.log('[useExport] ExportComplete payload:', event.payload)

        if (event.payload.data) {
          console.log('[useExport] Triggering download with:', event.payload)
          downloadExport(
            event.payload.data,
            event.payload.fileName,
            event.payload.format,
          )
        }
        else {
          console.log('[useExport] ExportComplete missing data or fileName!', event.payload)
        }
      },
    )

    const unsubscribeError = subscribe(
      ExportEventTypes.ExportError,
      (event) => {
        console.log('[useExport] Received ExportError:', event)
        setIsExporting(false)
        setExportError(event.payload.error || 'Unknown export error')
      },
    )

    return () => {
      console.log('[useExport] Cleaning up subscriptions')
      unsubscribeProgress()
      unsubscribeComplete()
      unsubscribeError()
    }
  }, []) // 修改为空数组，只在组件挂载时执行一次

  const handleExportRequest = useCallback((format: ExportFormat | string, options?: Partial<ExportOptions>) => {
    console.log('[useExport] handleExportRequest called with format:', format, 'options:', options)
    setIsExporting(true)
    setExportProgress(0)
    setExportError(null)

    try {
      const payload = {
        format,
        ...options,
      }
      console.log('[useExport] Publishing ExportRequest with payload:', payload)

      appEventBus.publish({
        type: ExportEventTypes.ExportRequest,
        payload,
      })
    }
    catch (e) {
      console.error('[useExport] ExportRequest publish error:', e)
    }
  }, [])

  // 导出方法保持不变，它们会把 options 传给 handleExportRequest
  const exportAsSVG = useCallback((options?: Partial<ExportOptions>) => {
    console.log('[useExport] exportAsSVG called with options:', options)
    handleExportRequest(ExportFormat.Svg, options)
  }, [handleExportRequest])

  const exportAsPNG = useCallback((options?: Partial<ExportOptions>) => {
    console.log('[useExport] exportAsPNG called with options:', options)
    handleExportRequest(ExportFormat.Png, options)
  }, [handleExportRequest])

  const exportAsPDF = useCallback((options?: Partial<ExportOptions>) => {
    console.log('[useExport] exportAsPDF called with options:', options)
    handleExportRequest(ExportFormat.Pdf, options)
  }, [handleExportRequest])

  const exportAs = useCallback((format: ExportFormat | string, options?: Partial<ExportOptions>) => {
    handleExportRequest(format, options)
  }, [handleExportRequest])

  const openExportModal = useCallback(() => {
    // Implementation depends on UI framework
    console.log('Open export modal')
    // TODO: Implement modal opening logic
  }, [])

  const downloadExport = useCallback((blob: Blob, fileName?: string, format?: ExportFormat | string) => {
    console.log('[downloadExport] start', blob, fileName, format)
    // 使用传入的 fileName，不要自己猜测或生成
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 使用传入的 fileName 和 format
    const downloadFileName = `${fileName || 'export'}.${format || ExportFormat.Svg}`
    link.download = downloadFileName

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }, [])

  // 这个函数定义是正确的，保持不变
  const isExportInProgress = useCallback(() => {
    return isExporting
  }, [isExporting])

  const getExportProgress = useCallback(() => {
    return exportProgress
  }, [exportProgress])

  const getExportError = useCallback(() => {
    return exportError
  }, [exportError])

  return {
    exportAsSVG,
    exportAsPNG,
    exportAsPDF,
    exportAs,
    openExportModal,

    downloadExport,
    handleExportRequest,
    isExportInProgress,
    getExportProgress,
    getExportError,
  }
}

export default useExport
