/**
 * Subscriber Utilities
 *
 * This module provides common utility functions for subscribing to events
 * on the application's event bus.
 *
 * @module event-bus/helpers/subscribers/utils
 */

import type { BaseEvent, EventBus, EventHandler, EventSubscriptionOptions } from '@/types/services/events'
import { AppEventType } from '@/types/services/events'
// import { AppEventMap } from '@/types/services/events/eventRegistry'; // No longer used after removing cast in typedSubscribe

/**
 * Generic event subscription helper with proper type casting
 * This function handles the common subscription pattern with proper typing
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param eventType - The event type to subscribe to
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function typedSubscribe<E extends BaseEvent>(
  eventBus: EventBus,
  eventType: AppEventType,
  handler: (event: E) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return eventBus.subscribe(
    eventType, // Removed 'as keyof AppEventMap' assertion
    // Use type assertion to ensure compatibility with Event<PERSON>andler<BaseEvent>
    handler as EventHandler<BaseEvent>,
    options,
  )
}

/**
 * Get the appropriate event type for shape create events
 * @param phase - The phase of the process
 * @returns The corresponding AppEventType
 */
export function getShapeCreateEventType(phase: string): AppEventType {
  switch (phase) {
    case 'request': return AppEventType.ShapeCreateRequest
    case 'validate': return AppEventType.ShapeCreateValidate
    case 'complete': return AppEventType.ShapeCreateComplete
    case 'error': return AppEventType.ShapeCreateError
    default: throw new Error(`Invalid phase: ${phase}`)
  }
}

/**
 * Get the appropriate event type for shape edit events
 * @param phase - The phase of the process
 * @returns The corresponding AppEventType
 */
export function getShapeEditEventType(phase: string): AppEventType {
  switch (phase) {
    case 'request': return AppEventType.ShapeEditRequest
    case 'compute': return AppEventType.ShapeEditCompute
    case 'complete': return AppEventType.ShapeEditComplete
    case 'error': return AppEventType.ShapeEditError
    default: throw new Error(`Invalid phase: ${phase}`)
  }
}

/**
 * Get the appropriate event type for shape delete events
 * @param phase - The phase of the process
 * @returns The corresponding AppEventType
 */
export function getShapeDeleteEventType(phase: string): AppEventType {
  switch (phase) {
    case 'request': return AppEventType.ShapeDeleteRequest
    case 'complete': return AppEventType.ShapeDeleteComplete
    case 'error': return AppEventType.ShapeDeleteError
    default: throw new Error(`Invalid phase: ${phase}`)
  }
}

/**
 * Get the appropriate event type for render events
 * @param phase - The phase of the process
 * @returns The corresponding AppEventType
 */
export function getRenderEventType(phase: string): AppEventType {
  switch (phase) {
    case 'trigger': return AppEventType.RenderTrigger
    case 'start': return AppEventType.RenderStart
    case 'complete': return AppEventType.RenderComplete
    case 'error': return AppEventType.RenderError
    default: throw new Error(`Invalid phase: ${phase}`)
  }
}
