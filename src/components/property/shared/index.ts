/**
 * Shared Property Components
 *
 * This module exports all shared components used across property panels
 * to ensure consistent styling and behavior throughout the application.
 *
 * Components:
 * - PropertySection: Organized sections with optional collapsible behavior
 * - PropertyField: Standardized input fields with labels and validation
 * - PropertyRow: Flexible row layouts for organizing fields
 * - ElementTypeBadge: Visual element type identification
 * - ValueSlider: Combined slider and numeric input controls
 *
 * @module components/property/shared
 */

export { ElementTypeBadge } from './ElementTypeBadge'
export { PropertyField } from './PropertyField'
export { PropertyRow } from './PropertyRow'
export { PropertySection } from './PropertySection'
export { GeometrySection, UnifiedGeometryLayout } from './UnifiedGeometryLayout'
export { ValueSlider } from './ValueSlider'

// Re-export commonly used types
export type { ElementType } from '@/types/core/elementDefinitions'
