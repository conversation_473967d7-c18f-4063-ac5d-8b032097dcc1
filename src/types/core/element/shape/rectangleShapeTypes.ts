/**
 * Defines types for rectangle and square shape elements.
 *
 * @remarks
 * This module provides the `Rectangle` interface, which extends {@link ShapeElement}
 * to include properties specific to defining a rectangle, such as its corner points,
 * width, and height. A square can be considered a special case of a rectangle.
 *
 * @module types/core/element/shape/rectangleShapeTypes
 */
import type { ShapeElement } from '../../elementDefinitions' // Corrected import path
import type Point from '@/types/core/element/geometry/point'

/**
 * Represents a rectangular shape, defined by its four corner points, width, and height.
 * It can also have optional rounded corners (though not explicitly in this interface,
 * it might be handled by a `cornerRadius` property in a concrete implementation or extended interface).
 *
 * @remarks
 * This interface defines the structure for rectangle shapes within the system.
 * It extends the base {@link ShapeElement} interface and adds rectangle-specific geometric properties.
 *
 * @example
 * ```typescript
 * const myRectangle: Rectangle = {
 *   // Properties from ShapeElement (id, type, visible, etc.)
 *   id: 'rect-001',
 *   type: 'rectangle', // Or a more specific ElementType if defined
 *   visible: true,
 *   // ... other ShapeElement properties
 *   // Rectangle-specific properties
 *   points: [ // Typically top-left, top-right, bottom-right, bottom-left
 *     { x: 10, y: 10 },
 *     { x: 110, y: 10 },
 *     { x: 110, y: 60 },
 *     { x: 10, y: 60 }
 *   ],
 *   width: 100,
 *   height: 50
 *   // cornerRadius: 5 // This property was in the original example but not in the interface
 * };
 * ```
 *
 * @see {@link ShapeElement} for base shape properties.
 * @see {@link Point} for the structure of point coordinates.
 */
export interface Rectangle extends ShapeElement {
  /**
   * An array of four {@link Point} objects representing the corner points of the rectangle.
   * @remarks
   * The points are typically ordered, for example: [top-left, top-right, bottom-right, bottom-left].
   * @see {@link Point} for the definition of a point.
   */
  points: [Point, Point, Point, Point]

  /**
   * The width of the rectangle, usually in pixels or the project's default unit.
   * @remarks
   * This value must be a positive number greater than 0.
   * @throws {Error} Implementations might throw an error if a non-positive value is set.
   */
  width: number

  /**
   * The height of the rectangle, usually in pixels or the project's default unit.
   * @remarks
   * This value must be a positive number greater than 0.
   * @throws {Error} Implementations might throw an error if a non-positive value is set.
   */
  height: number
  /** Optional: The radius of the corners if the rectangle is rounded. Defaults to 0 (sharp corners). */
  cornerRadius?: number
}
