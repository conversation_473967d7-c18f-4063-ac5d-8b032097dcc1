import type { Line } from '../../../../../src/types/core/element/path/linePathTypes'
import type { Ellipse } from '../../../../../src/types/core/element/shape/ellipseShapeTypes'
import type { Rectangle } from '../../../../../src/types/core/element/shape/rectangleShapeTypes'
import type { Polygon } from '../../../../../src/types/core/element/shape/shape'
import { describe, expect, it } from 'vitest'
import {
  calculateBoundingBox,
  calculateEllipseBoundingBox,
  calculateLineBoundingBox,
  calculatePointsBoundingBox,
  calculatePolygonBoundingBox,
  calculateRectangleBoundingBox,
} from '../../../../../src/core/compute/properties/boundingBox'
import { Point } from '../../../../../src/types/core/element/geometry/point'

describe('bounding Box Calculation Module', () => {
  describe('calculateBoundingBox', () => {
    it('should return zero-size box for null or undefined element', () => {
      const expected = { x: 0, y: 0, width: 0, height: 0 }
      expect(calculateBoundingBox(null as any)).toEqual(expected)
      expect(calculateBoundingBox(undefined as any)).toEqual(expected)
    })

    it('should return zero-size box at position for element with no getSubType method', () => {
      const element = {
        id: 'test-element',
        getPosition: () => ({ x: 10, y: 20 }),
      } as any

      expect(calculateBoundingBox(element)).toEqual({ x: 10, y: 20, width: 0, height: 0 })
    })

    it('should calculate rectangle bounding box', () => {
      const rectangle = {
        getSubType: () => 'Rectangle',
        getPosition: () => ({ x: 50, y: 50 }),
        width: 100,
        height: 80,
      } as Rectangle

      expect(calculateBoundingBox(rectangle)).toEqual({ x: 0, y: 10, width: 100, height: 80 })
    })

    it('should calculate ellipse bounding box', () => {
      const ellipse = {
        getSubType: () => 'Ellipse',
        getPosition: () => ({ x: 50, y: 50 }),
        getRadiusX: () => 30,
        getRadiusY: () => 20,
      } as Ellipse

      expect(calculateBoundingBox(ellipse)).toEqual({ x: 20, y: 30, width: 60, height: 40 })
    })

    it('should calculate polygon bounding box', () => {
      const polygon = {
        getSubType: () => 'Polygon',
        getPoints: () => [
          new Point(10, 10),
          new Point(50, 10),
          new Point(50, 40),
          new Point(10, 40),
        ],
      } as Polygon

      expect(calculateBoundingBox(polygon)).toEqual({ x: 10, y: 10, width: 40, height: 30 })
    })

    it('should calculate line bounding box', () => {
      const line = {
        getSubType: () => 'Line',
        start: new Point(10, 10),
        end: new Point(50, 40),
      } as Line

      expect(calculateBoundingBox(line)).toEqual({ x: 10, y: 10, width: 40, height: 30 })
    })

    it('should return zero-size box at position for unknown element types', () => {
      const unknown = {
        getSubType: () => 'Unknown',
        getPosition: () => ({ x: 10, y: 20 }),
      } as any

      expect(calculateBoundingBox(unknown)).toEqual({ x: 10, y: 20, width: 0, height: 0 })
    })

    it('should return zero-size box at origin for unknown element with no position', () => {
      const unknown = {
        getSubType: () => 'Unknown',
      } as any

      expect(calculateBoundingBox(unknown)).toEqual({ x: 0, y: 0, width: 0, height: 0 })
    })
  })

  describe('calculateRectangleBoundingBox', () => {
    it('should calculate rectangle bounding box correctly', () => {
      const rectangle = {
        getPosition: () => ({ x: 50, y: 50 }),
        width: 100,
        height: 80,
      } as Rectangle

      expect(calculateRectangleBoundingBox(rectangle)).toEqual({ x: 0, y: 10, width: 100, height: 80 })
    })

    it('should handle zero dimensions', () => {
      const rectangle = {
        getPosition: () => ({ x: 50, y: 50 }),
        width: 0,
        height: 0,
      } as Rectangle

      expect(calculateRectangleBoundingBox(rectangle)).toEqual({ x: 50, y: 50, width: 0, height: 0 })
    })

    it('should handle negative dimensions', () => {
      const rectangle = {
        getPosition: () => ({ x: 50, y: 50 }),
        width: -100,
        height: 80,
      } as Rectangle

      // Negative width means the box extends to the right of the center
      expect(calculateRectangleBoundingBox(rectangle)).toEqual({ x: 100, y: 10, width: -100, height: 80 })
    })
  })

  describe('calculateEllipseBoundingBox', () => {
    it('should calculate ellipse bounding box correctly', () => {
      const ellipse = {
        getPosition: () => ({ x: 50, y: 50 }),
        getRadiusX: () => 30,
        getRadiusY: () => 20,
      } as Ellipse

      expect(calculateEllipseBoundingBox(ellipse)).toEqual({ x: 20, y: 30, width: 60, height: 40 })
    })

    it('should handle zero radii', () => {
      const ellipse = {
        getPosition: () => ({ x: 50, y: 50 }),
        getRadiusX: () => 0,
        getRadiusY: () => 0,
      } as Ellipse

      expect(calculateEllipseBoundingBox(ellipse)).toEqual({ x: 50, y: 50, width: 0, height: 0 })
    })

    it('should handle negative radii', () => {
      const ellipse = {
        getPosition: () => ({ x: 50, y: 50 }),
        getRadiusX: () => -30,
        getRadiusY: () => 20,
      } as Ellipse

      // Negative radius is treated as extending in the opposite direction
      expect(calculateEllipseBoundingBox(ellipse)).toEqual({ x: 80, y: 30, width: -60, height: 40 })
    })
  })

  describe('calculatePolygonBoundingBox', () => {
    it('should calculate polygon bounding box correctly', () => {
      const polygon = {
        getPoints: () => [
          new Point(10, 10),
          new Point(50, 10),
          new Point(50, 40),
          new Point(10, 40),
        ],
      } as Polygon

      expect(calculatePolygonBoundingBox(polygon)).toEqual({ x: 10, y: 10, width: 40, height: 30 })
    })

    it('should delegate to calculatePointsBoundingBox', () => {
      const points = [
        new Point(10, 10),
        new Point(50, 10),
        new Point(50, 40),
        new Point(10, 40),
      ]

      const polygon = {
        getPoints: () => points,
      } as Polygon

      const expected = calculatePointsBoundingBox(points)
      expect(calculatePolygonBoundingBox(polygon)).toEqual(expected)
    })
  })

  describe('calculateLineBoundingBox', () => {
    it('should calculate line bounding box correctly', () => {
      const line = {
        start: new Point(10, 10),
        end: new Point(50, 40),
      } as Line

      expect(calculateLineBoundingBox(line)).toEqual({ x: 10, y: 10, width: 40, height: 30 })
    })

    it('should handle horizontal line', () => {
      const line = {
        start: new Point(10, 20),
        end: new Point(50, 20),
      } as Line

      expect(calculateLineBoundingBox(line)).toEqual({ x: 10, y: 20, width: 40, height: 0 })
    })

    it('should handle vertical line', () => {
      const line = {
        start: new Point(30, 10),
        end: new Point(30, 50),
      } as Line

      expect(calculateLineBoundingBox(line)).toEqual({ x: 30, y: 10, width: 0, height: 40 })
    })

    it('should handle zero-length line', () => {
      const line = {
        start: new Point(30, 30),
        end: new Point(30, 30),
      } as Line

      expect(calculateLineBoundingBox(line)).toEqual({ x: 30, y: 30, width: 0, height: 0 })
    })

    it('should delegate to calculatePointsBoundingBox', () => {
      const start = new Point(10, 10)
      const end = new Point(50, 40)

      const line = {
        start,
        end,
      } as Line

      const expected = calculatePointsBoundingBox([start, end])
      expect(calculateLineBoundingBox(line)).toEqual(expected)
    })
  })

  describe('calculatePointsBoundingBox', () => {
    it('should calculate bounding box for multiple points', () => {
      const points = [
        new Point(10, 10),
        new Point(50, 10),
        new Point(30, 40),
        new Point(20, 30),
      ]

      expect(calculatePointsBoundingBox(points)).toEqual({ x: 10, y: 10, width: 40, height: 30 })
    })

    it('should handle single point', () => {
      const points = [new Point(30, 40)]

      expect(calculatePointsBoundingBox(points)).toEqual({ x: 30, y: 40, width: 0, height: 0 })
    })

    it('should return zero-size box at origin for empty points array', () => {
      expect(calculatePointsBoundingBox([])).toEqual({ x: 0, y: 0, width: 0, height: 0 })
    })

    it('should return zero-size box at origin for null or undefined points', () => {
      expect(calculatePointsBoundingBox(null as any)).toEqual({ x: 0, y: 0, width: 0, height: 0 })
      expect(calculatePointsBoundingBox(undefined as any)).toEqual({ x: 0, y: 0, width: 0, height: 0 })
    })

    it('should handle points with negative coordinates', () => {
      const points = [
        new Point(-10, -20),
        new Point(30, -5),
        new Point(10, 15),
      ]

      expect(calculatePointsBoundingBox(points)).toEqual({ x: -10, y: -20, width: 40, height: 35 })
    })
  })
})
