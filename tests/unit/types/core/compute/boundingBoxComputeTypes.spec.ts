import type { BoundingBoxCalculatorStrategy } from '@/types/core/compute/boundingBoxComputeTypes'
import type { BoundingBox } from '@/types/core/element/geometry/bounding-box'
import type { Element } from '@/types/core/elementDefinitions'
import { describe, expect, it } from 'vitest'
import { ElementType } from '@/types/core/elementDefinitions'

describe('bounding Box Compute Types Module', () => {
  describe('boundingBoxCalculatorStrategy Interface', () => {
    it('should allow implementation of bounding box calculator strategy', () => {
      // Create a mock implementation of BoundingBoxCalculatorStrategy
      const rectangleBoundingBoxCalculator: BoundingBoxCalculatorStrategy = {
        calculateBoundingBox: (element: Element): BoundingBox => {
          // Simple mock implementation for testing
          if (element.type === ElementType.RECTANGLE) {
            // Assume element has x, y, width, and height properties
            const rectangle = element as unknown as { x: number, y: number, width: number, height: number }
            return {
              x: rectangle.x,
              y: rectangle.y,
              width: rectangle.width,
              height: rectangle.height,
            }
          }
          return { x: 0, y: 0, width: 0, height: 0 }
        },

        getElementType: (): string => {
          return ElementType.RECTANGLE
        },
      }

      // Create a test rectangle element
      const rectangleElement: Element & { x: number, y: number, width: number, height: number } = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        visible: true,
        locked: false,
        x: 10,
        y: 20,
        width: 100,
        height: 50,
      }

      // Test the bounding box calculation
      const boundingBox = rectangleBoundingBoxCalculator.calculateBoundingBox(rectangleElement)
      expect(boundingBox.x).toBe(10)
      expect(boundingBox.y).toBe(20)
      expect(boundingBox.width).toBe(100)
      expect(boundingBox.height).toBe(50)
      expect(rectangleBoundingBoxCalculator.getElementType()).toBe(ElementType.RECTANGLE)
    })

    it('should allow implementation for multiple element types', () => {
      // Create a mock implementation that handles multiple element types
      const shapeBoundingBoxCalculator: BoundingBoxCalculatorStrategy = {
        calculateBoundingBox: (element: Element): BoundingBox => {
          // Simple mock implementation for testing
          if (element.type === ElementType.RECTANGLE) {
            const rectangle = element as unknown as { x: number, y: number, width: number, height: number }
            return {
              x: rectangle.x,
              y: rectangle.y,
              width: rectangle.width,
              height: rectangle.height,
            }
          }
          else if (element.type === ElementType.CIRCLE) {
            const circle = element as unknown as { x: number, y: number, radius: number }
            return {
              x: circle.x - circle.radius,
              y: circle.y - circle.radius,
              width: circle.radius * 2,
              height: circle.radius * 2,
            }
          }
          return { x: 0, y: 0, width: 0, height: 0 }
        },

        getElementType: (): string => {
          // In a real implementation, this might return an array of supported types
          return 'SHAPE' // Generic type representing multiple shapes
        },
      }

      // Create test elements
      const rectangleElement: Element & { x: number, y: number, width: number, height: number } = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        visible: true,
        locked: false,
        x: 10,
        y: 20,
        width: 100,
        height: 50,
      }

      const circleElement: Element & { x: number, y: number, radius: number } = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        visible: true,
        locked: false,
        x: 50,
        y: 50,
        radius: 25,
      }

      // Test the bounding box calculations
      const rectBoundingBox = shapeBoundingBoxCalculator.calculateBoundingBox(rectangleElement)
      expect(rectBoundingBox.x).toBe(10)
      expect(rectBoundingBox.y).toBe(20)
      expect(rectBoundingBox.width).toBe(100)
      expect(rectBoundingBox.height).toBe(50)

      const circleBoundingBox = shapeBoundingBoxCalculator.calculateBoundingBox(circleElement)
      expect(circleBoundingBox.x).toBe(25)
      expect(circleBoundingBox.y).toBe(25)
      expect(circleBoundingBox.width).toBe(50)
      expect(circleBoundingBox.height).toBe(50)

      expect(shapeBoundingBoxCalculator.getElementType()).toBe('SHAPE')
    })

    it('should handle complex elements with rotation', () => {
      // Create a mock implementation that handles rotated elements
      const rotatedBoundingBoxCalculator: BoundingBoxCalculatorStrategy = {
        calculateBoundingBox: (element: Element): BoundingBox => {
          // Simple mock implementation for testing
          // In a real implementation, this would calculate the bounding box of a rotated element
          // For this test, we'll just return a predefined bounding box
          return {
            x: 5,
            y: 5,
            width: 110,
            height: 60,
          }
        },

        getElementType: (): string => {
          return ElementType.RECTANGLE
        },
      }

      // Create a test rotated rectangle element
      const rotatedRectangleElement: Element & { x: number, y: number, width: number, height: number, rotation: number } = {
        id: 'rect-2',
        type: ElementType.RECTANGLE,
        visible: true,
        locked: false,
        x: 10,
        y: 20,
        width: 100,
        height: 50,
        rotation: 45,
      }

      // Test the bounding box calculation
      const boundingBox = rotatedBoundingBoxCalculator.calculateBoundingBox(rotatedRectangleElement)
      expect(boundingBox.x).toBe(5)
      expect(boundingBox.y).toBe(5)
      expect(boundingBox.width).toBe(110)
      expect(boundingBox.height).toBe(60)
    })

    it('should handle elements with zero dimensions', () => {
      // Create a mock implementation for elements with zero dimensions
      const pointBoundingBoxCalculator: BoundingBoxCalculatorStrategy = {
        calculateBoundingBox: (element: Element): BoundingBox => {
          // For a point, the bounding box has zero width and height
          const point = element as unknown as { x: number, y: number }
          return {
            x: point.x,
            y: point.y,
            width: 0,
            height: 0,
          }
        },

        getElementType: (): string => {
          return 'POINT' // Not a standard ElementType, just for testing
        },
      }

      // Create a test point element
      const pointElement: Element & { x: number, y: number } = {
        id: 'point-1',
        type: 'POINT',
        visible: true,
        locked: false,
        x: 30,
        y: 40,
      }

      // Test the bounding box calculation
      const boundingBox = pointBoundingBoxCalculator.calculateBoundingBox(pointElement)
      expect(boundingBox.x).toBe(30)
      expect(boundingBox.y).toBe(40)
      expect(boundingBox.width).toBe(0)
      expect(boundingBox.height).toBe(0)
    })
  })
})
