/**
 * Unit tests for ValidatorRegistry
 *
 * Tests the validator registry class
 */

import { expect, test } from '@playwright/test'

// Define types locally for testing
interface ValidationError {
  code: string
  message: string
  path?: string
  value?: any
}

// Mock BaseShapeValidator to avoid namespace issues
class BaseShapeValidator {
  validate(shape: any): { valid: boolean, errors: ValidationError[] } {
    return { valid: true, errors: [] }
  }

  protected validateSpecific(_shape: any): ValidationError[] {
    return []
  }

  protected applySpecificRules(_shape: any): ValidationError[] {
    return []
  }
}

// Mock ValidatorRegistry to avoid namespace issues
class ValidatorRegistry {
  private validators: Map<string, BaseShapeValidator> = new Map()

  registerValidator(ElementType: string, validator: BaseShapeValidator): void {
    this.validators.set(ElementType, validator)
  }

  getValidator(ElementType: string): BaseShapeValidator | undefined {
    return this.validators.get(ElementType)
  }

  hasValidator(ElementType: string): boolean {
    return this.validators.has(ElementType)
  }

  removeValidator(ElementType: string): boolean {
    return this.validators.delete(ElementType)
  }

  getRegisteredElementTypes(): string[] {
    return Array.from(this.validators.keys())
  }

  clear(): void {
    this.validators.clear()
  }
}

// Create a concrete implementation of BaseShapeValidator for testing
class TestValidator extends BaseShapeValidator {
  protected validateSpecific(_shape: any): ValidationError[] {
    return []
  }

  protected applySpecificRules(_shape: any): ValidationError[] {
    return []
  }
}

// Create another validator for testing multiple registrations
class AnotherTestValidator extends BaseShapeValidator {
  protected validateSpecific(_shape: any): ValidationError[] {
    return []
  }

  protected applySpecificRules(_shape: any): ValidationError[] {
    return []
  }
}

test.describe('ValidatorRegistry', () => {
  let registry: ValidatorRegistry
  let testValidator: TestValidator
  let anotherTestValidator: AnotherTestValidator

  test.beforeEach(() => {
    registry = new ValidatorRegistry()
    testValidator = new TestValidator()
    anotherTestValidator = new AnotherTestValidator()
  })

  test('registerValidator registers a validator', () => {
    // Register a validator
    registry.registerValidator('test-shape', testValidator)

    // Verify that the validator is registered
    expect(registry.hasValidator('test-shape')).toBe(true)
  })

  test('getValidator retrieves a registered validator', () => {
    // Register a validator
    registry.registerValidator('test-shape', testValidator)

    // Retrieve the validator
    const validator = registry.getValidator('test-shape')

    // Verify that the correct validator is retrieved
    expect(validator).toBe(testValidator)
  })

  test('getValidator returns undefined for unregistered shape types', () => {
    // Retrieve a validator for an unregistered shape type
    const validator = registry.getValidator('unregistered-shape')

    // Verify that undefined is returned
    expect(validator).toBeUndefined()
  })

  test('hasValidator checks if a validator is registered', () => {
    // Register a validator
    registry.registerValidator('test-shape', testValidator)

    // Check if validators are registered
    expect(registry.hasValidator('test-shape')).toBe(true)
    expect(registry.hasValidator('unregistered-shape')).toBe(false)
  })

  test('removeValidator removes a registered validator', () => {
    // Register a validator
    registry.registerValidator('test-shape', testValidator)

    // Verify that the validator is registered
    expect(registry.hasValidator('test-shape')).toBe(true)

    // Remove the validator
    const result = registry.removeValidator('test-shape')

    // Verify that the validator is removed
    expect(result).toBe(true)
    expect(registry.hasValidator('test-shape')).toBe(false)
  })

  test('removeValidator returns false for unregistered shape types', () => {
    // Remove a validator for an unregistered shape type
    const result = registry.removeValidator('unregistered-shape')

    // Verify that false is returned
    expect(result).toBe(false)
  })

  test('getRegisteredElementTypes returns all registered shape types', () => {
    // Register multiple validators
    registry.registerValidator('shape1', testValidator)
    registry.registerValidator('shape2', anotherTestValidator)
    registry.registerValidator('shape3', testValidator)

    // Get registered shape types
    const ElementTypes = registry.getRegisteredElementTypes()

    // Verify that all shape types are returned
    expect(ElementTypes).toHaveLength(3)
    expect(ElementTypes).toContain('shape1')
    expect(ElementTypes).toContain('shape2')
    expect(ElementTypes).toContain('shape3')
  })

  test('getRegisteredElementTypes returns empty array when no validators are registered', () => {
    // Get registered shape types
    const ElementTypes = registry.getRegisteredElementTypes()

    // Verify that an empty array is returned
    expect(ElementTypes).toEqual([])
  })

  test('clear removes all registered validators', () => {
    // Register multiple validators
    registry.registerValidator('shape1', testValidator)
    registry.registerValidator('shape2', anotherTestValidator)
    registry.registerValidator('shape3', testValidator)

    // Verify that validators are registered
    expect(registry.hasValidator('shape1')).toBe(true)
    expect(registry.hasValidator('shape2')).toBe(true)
    expect(registry.hasValidator('shape3')).toBe(true)

    // Clear the registry
    registry.clear()

    // Verify that all validators are removed
    expect(registry.hasValidator('shape1')).toBe(false)
    expect(registry.hasValidator('shape2')).toBe(false)
    expect(registry.hasValidator('shape3')).toBe(false)
    expect(registry.getRegisteredElementTypes()).toEqual([])
  })

  test('registerValidator overwrites existing validator for the same shape type', () => {
    // Register a validator
    registry.registerValidator('test-shape', testValidator)

    // Verify that the validator is registered
    expect(registry.getValidator('test-shape')).toBe(testValidator)

    // Register another validator for the same shape type
    registry.registerValidator('test-shape', anotherTestValidator)

    // Verify that the validator is overwritten
    expect(registry.getValidator('test-shape')).toBe(anotherTestValidator)
    expect(registry.getValidator('test-shape')).not.toBe(testValidator)
  })
})
