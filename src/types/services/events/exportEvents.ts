/**
 * Export Events Type Definitions
 *
 * This module defines the event types and interfaces related to export operations,
 * including file export, preview generation, history tracking, and batch processing.
 *
 * @module types/services/events/exportEvents
 */

import type { TypedEvent } from './eventCore'
import type { AppEventType } from './eventTypes'

/**
 * Export task status types.
 * Represents the possible states of an export operation.
 */
export type ExportTaskStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'paused' | 'canceled'

/**
 * Supported export formats.
 */
export enum ExportFormat {
  /** Scalable Vector Graphics format. */
  Svg = 'svg',
  /** Portable Network Graphics format. */
  Png = 'png',
  /** Portable Document Format. */
  Pdf = 'pdf',
  /** JavaScript Object Notation format. */
  Json = 'json',
}

/**
 * Export configuration options.
 */
export interface ExportOptions {
  /** Output file format. */
  format: ExportFormat
  /** Name of the output file. */
  fileName?: string
  /** Quality setting (0-1) for lossy formats. */
  quality?: number
  /** Scaling factor for the output. */
  scale?: number
  /** Whether to export only selected elements. */
  selectedOnly?: boolean
  /** Whether to include the canvas background. */
  includeBackground?: boolean
  /** Background color (when includeBackground is true). */
  backgroundColor?: string
  /** Whether to include metadata in the export. */
  includeMetadata?: boolean
  /** Additional metadata to include. */
  metadata?: Record<string, unknown>
  /** Output dimensions. */
  dimensions?: {
    width: number
    height: number
  }
  /** Watermark configuration. */
  watermark?: {
    enabled: boolean
    text?: string
    position?: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight' | 'center'
    rotation?: number
    opacity?: number
  }
}

/**
 * Export result data.
 */
export interface ExportResult {
  /** Name of the exported file. */
  fileName: string
  /** Format of the exported file. */
  format: ExportFormat
  /** URL to the exported file. */
  fileUrl: string
  /** Additional metadata. */
  metadata?: Record<string, unknown>
}

/**
 * Basic export event payload.
 * @deprecated Use more specific payload types instead.
 */
export interface ExportPayload {
  /** The format of the export. */
  format: ExportFormat
  /** The exported data as a Blob, if applicable. */
  data?: Blob
  /** The progress of the export operation (0-100). */
  progress?: number
  /** An error message if the export failed. */
  error?: string
}

/**
 * Export event type constants.
 * These are used as keys in the AppEventMap and for event type discrimination.
 */
export enum ExportEventTypes {
  // Basic export events
  /** Event type for requesting an export. */
  ExportRequest = 'export.request',
  /** Event type for preparing an export. */
  ExportPrepare = 'export.prepare',
  /** Event type for export progress updates. */
  ExportProgress = 'export.progress',
  /** Event type for export completion. */
  ExportComplete = 'export.complete',
  /** Event type for export errors. */
  ExportError = 'export.error',

  // Preview events
  /** Event type for requesting an export preview. */
  ExportPreviewRequest = 'export.preview.request',
  /** Event type for generating an export preview. */
  ExportPreviewGenerate = 'export.preview.generate',
  /** Event type for export preview completion. */
  ExportPreviewComplete = 'export.preview.complete',
  /** Event type for export preview errors. */
  ExportPreviewError = 'export.preview.error',

  // History events
  /** Event type for updating export history. */
  ExportHistoryUpdate = 'export.history.update',
  /** Event type for clearing export history. */
  ExportHistoryClear = 'export.history.clear',

  // Template events (related to export templates)
  /** Event type for creating an export template. */
  ExportTemplateCreated = 'export.template.created',
  /** Event type for updating an export template. */
  ExportTemplateUpdated = 'export.template.updated',
  /** Event type for deleting an export template. */
  ExportTemplateDeleted = 'export.template.deleted',

  // Batch export events
  /** Event type for starting a batch export. */
  ExportBatchStart = 'export.batch.start',
  /** Event type for batch export progress updates. */
  ExportBatchProgress = 'export.batch.progress',
  /** Event type for batch export completion. */
  ExportBatchComplete = 'export.batch.complete',
  /** Event type for batch export errors. */
  ExportBatchError = 'export.batch.error',

  // Queue events
  /** Event type for updating the export queue. */
  ExportQueueUpdate = 'export.queue.update',
  /** Event type for starting an export queue task. */
  ExportQueueTaskStart = 'export.queue.task.start',
  /** Event type for completing an export queue task. */
  ExportQueueTaskComplete = 'export.queue.task.complete',
  /** Event type for errors in an export queue task. */
  ExportQueueTaskError = 'export.queue.task.error',
}

/**
 * Export progress event payload.
 */
export interface ExportProgressPayload {
  /** Export format. */
  format: ExportFormat
  /** Progress percentage (0-100). */
  progress: number
  /** Current processing stage. */
  stage?: string
}

/**
 * Export result event payload.
 */
export interface ExportResultPayload {
  /** Name of the exported file. */
  fileName: string
  /** Format of the exported file. */
  format: ExportFormat
  /** URL to the exported file. */
  fileUrl?: string
  /** Additional metadata. */
  metadata?: Record<string, unknown>
}

/**
 * Export preview event payload.
 */
export interface ExportPreviewPayload extends Record<string, unknown> {
  /** Export format. */
  format: ExportFormat
  /** URL to the preview image. */
  previewUrl?: string
  /** Export options used. */
  options?: Partial<ExportOptions>
  /** Error message if preview generation failed. */
  error?: string
}

/**
 * Export history event payload.
 */
export interface ExportHistoryPayload extends Record<string, unknown> {
  /** History items. */
  items?: Array<{
    /** Unique identifier for the history item. */
    id: string
    /** Date of the export. */
    date: Date
    /** Format of the export. */
    format: ExportFormat
    /** Name of the exported file. */
    fileName: string
    /** URL to the preview image. */
    previewUrl?: string
    /** Additional metadata associated with the export. */
    metadata?: Record<string, unknown>
  }>
  /** Type of history operation ('add', 'clear', 'remove'). */
  operation: 'add' | 'clear' | 'remove'
  /** Identifier of the item to be removed (for 'remove' operation). */
  itemId?: string
}

/**
 * Export template event payload.
 */
export interface ExportTemplatePayload extends Record<string, unknown> {
  /** Template identifier. */
  templateId: string
  /** Template name. */
  name: string
  /** Whether this is the default template. */
  isDefault?: boolean
}

/**
 * Batch export event payload.
 */
export interface ExportBatchPayload extends Record<string, unknown> {
  /** Batch identifier. */
  batchId: string
  /** Export formats to process in the batch. */
  formats: ExportFormat[]
  /** Overall progress percentage of the batch. */
  progress?: number
  /** Individual operation results within the batch. */
  operations: Array<{
    /** Type of the individual operation (e.g., an ExportFormat). */
    type: ExportFormat // Or a more specific operation type
    /** Result or error of the individual operation. */
    payload: unknown
  }>
  /** Array of results for each successful export in the batch. */
  results?: ExportResult[]
  /** Error message if the batch operation failed. */
  error?: string
}

/**
 * Export queue event payload.
 */
export interface ExportQueuePayload extends Record<string, unknown> {
  /** Current queue length. */
  queueLength: number
  /** Number of completed tasks in the queue. */
  completedTasks: number
  /** Overall progress percentage of the queue. */
  progress: number
  /** Current task identifier being processed. */
  taskId?: string
  /** Type of action performed on the queue. */
  action?: 'added' | 'cleared' | 'canceled' | 'paused' | 'resumed'
  /** Details of the current task. */
  task?: {
    /** Task identifier. */
    id: string
    /** Export format for the task. */
    format: ExportFormat
    /** Current status of the task. */
    status: ExportTaskStatus
  }
  /** Error message if a queue operation failed. */
  error?: string
}

/**
 * Extended export event interface, representing various specific export-related events.
 * The payload type is a union based on the specific event type.
 */
export interface ExtendedExportEvent {
  /** The specific type of export event, from ExportEventTypes. */
  type: ExportEventTypes[keyof ExportEventTypes] // More precise: typeof ExportEventTypes[keyof typeof ExportEventTypes] is for values
  /** Event payload, varying by the event type. */
  payload:
    | { format: ExportFormat } // For EXPORT_PREPARE
    | ExportProgressPayload // For EXPORT_PROGRESS
    | ExportResultPayload // For EXPORT_COMPLETE
    | { format: string, error: string } // For EXPORT_ERROR
    | ExportBatchPayload // For EXPORT_BATCH_*
    | ExportTemplatePayload // For EXPORT_TEMPLATE_*
    | ExportQueuePayload // For EXPORT_QUEUE_*
    | ExportPreviewPayload // For EXPORT_PREVIEW_*
    | ExportHistoryPayload // For EXPORT_HISTORY_*
}

// Add these event types to AppEventMap type declaration enhancement
declare module './eventRegistry' {
  interface AppEventMap {
    [ExportEventTypes.ExportBatchStart]: TypedEvent<ExportBatchPayload>
    [ExportEventTypes.ExportBatchProgress]: TypedEvent<ExportBatchPayload>
    [ExportEventTypes.ExportBatchComplete]: TypedEvent<ExportBatchPayload>
    [ExportEventTypes.ExportTemplateCreated]: TypedEvent<ExportTemplatePayload>
    [ExportEventTypes.ExportTemplateUpdated]: TypedEvent<ExportTemplatePayload>
    [ExportEventTypes.ExportTemplateDeleted]: TypedEvent<ExportTemplatePayload>
    // Add other ExportEventTypes as needed
  }
}

/**
 * Extended event type declaration for AppEventType compatibility.
 * This ensures that events defined in ExportEventTypes can also be handled
 * through the main AppEventType system if necessary.
 */
declare module './eventTypes' {
  // This AppEventMap is augmenting the one in eventRegistry.ts for the EventBus
  // It should ideally be defined once in eventRegistry.ts or a central place.
  // For now, ensuring these specific events are mapped.
  interface AppEventMap { // This refers to the AppEventMap in eventRegistry.ts
    [ExportEventTypes.ExportPreviewRequest]: TypedEvent<ExportPreviewPayload>
    [ExportEventTypes.ExportPreviewGenerate]: TypedEvent<ExportPreviewPayload>
    [ExportEventTypes.ExportPreviewComplete]: TypedEvent<ExportPreviewPayload>
    [ExportEventTypes.ExportPreviewError]: TypedEvent<ExportPreviewPayload>
    [ExportEventTypes.ExportHistoryUpdate]: TypedEvent<ExportHistoryPayload>
    [ExportEventTypes.ExportHistoryClear]: TypedEvent<ExportHistoryPayload>
    // [ExportEventTypes.ExportTemplateCreated]: TypedEvent<ExportTemplatePayload>; // Already in ./eventRegistry augmentation
    // [ExportEventTypes.ExportTemplateUpdated]: TypedEvent<ExportTemplatePayload>; // Already in ./eventRegistry augmentation
    // [ExportEventTypes.ExportTemplateDeleted]: TypedEvent<ExportTemplatePayload>; // Already in ./eventRegistry augmentation
    // [ExportEventTypes.ExportBatchStart]: TypedEvent<ExportBatchPayload>; // Already in ./eventRegistry augmentation
    // [ExportEventTypes.ExportBatchProgress]: TypedEvent<ExportBatchPayload>; // Already in ./eventRegistry augmentation
    // [ExportEventTypes.ExportBatchComplete]: TypedEvent<ExportBatchPayload>; // Already in ./eventRegistry augmentation
    [ExportEventTypes.ExportBatchError]: TypedEvent<ExportBatchPayload>
    [ExportEventTypes.ExportQueueUpdate]: TypedEvent<ExportQueuePayload>
    [ExportEventTypes.ExportQueueTaskStart]: TypedEvent<ExportQueuePayload>
    [ExportEventTypes.ExportQueueTaskComplete]: TypedEvent<ExportQueuePayload>
    [ExportEventTypes.ExportQueueTaskError]: TypedEvent<ExportQueuePayload>
  }
}

/**
 * Represents a union of all possible export-related events,
 * combining general AppEventType export events with specific ExportEventTypes.
 * @deprecated Consider using specific event types or ExtendedExportEvent.
 */
export interface ExportEvents { // This interface might be too broad or redundant
  /** Event type, can be a general AppEventType or a specific ExportEventType. */
  type: AppEventType.ExportRequest |
    AppEventType.ExportPrepare |
    AppEventType.ExportProgress |
    AppEventType.ExportComplete |
    AppEventType.ExportError |
    ExportEventTypes.ExportRequest | // Redundant if AppEventType covers these
    ExportEventTypes.ExportPrepare |
    ExportEventTypes.ExportProgress |
    ExportEventTypes.ExportComplete |
    ExportEventTypes.ExportError |
    ExportEventTypes.ExportPreviewRequest |
    ExportEventTypes.ExportPreviewGenerate |
    ExportEventTypes.ExportPreviewComplete |
    ExportEventTypes.ExportPreviewError |
    ExportEventTypes.ExportHistoryUpdate |
    ExportEventTypes.ExportHistoryClear |
    ExportEventTypes.ExportBatchStart |
    ExportEventTypes.ExportBatchProgress |
    ExportEventTypes.ExportBatchComplete |
    ExportEventTypes.ExportBatchError |
    ExportEventTypes.ExportQueueUpdate |
    ExportEventTypes.ExportQueueTaskStart |
    ExportEventTypes.ExportQueueTaskComplete |
    ExportEventTypes.ExportQueueTaskError |
    ExportEventTypes.ExportTemplateCreated |
    ExportEventTypes.ExportTemplateUpdated |
    ExportEventTypes.ExportTemplateDeleted
  /** Event payload, specific to the event type. */
  payload: ExportPayload | ExportPreviewPayload | ExportHistoryPayload | ExportBatchPayload | ExportQueuePayload | ExportTemplatePayload
}
