import type { ServiceRegistry } from '@/services/core/registry'
import type { LoggerService } from '@/types/services/logging'

import { appEventBus } from '@/services/core/event-bus'
import { ServiceId } from '@/types/services/core/serviceIdentifier'
import { StorageService } from './storageService'

/**
 * Registers the StorageService with the service registry.
 *
 * @param registry - The service registry to register with
 * @param logger - The logger service for logging
 * @returns The registered StorageService instance
 *
 * @module services/storage/registry
 */
export function registerStorageService(
  registry: ServiceRegistry,
  logger: LoggerService,
): StorageService {
  logger.info('Registering StorageService')

  const storageService = new StorageService(appEventBus, logger)

  registry.register(ServiceId.StorageService, storageService)

  logger.info('StorageService registered successfully')

  return storageService
}
