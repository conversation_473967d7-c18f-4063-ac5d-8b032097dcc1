import type { <PERSON><PERSON><PERSON> } from '@/types/core/element/element'

import type { <PERSON><PERSON><PERSON>Model } from '@/types/core/models'

// Import other types for payload checks
import type { ElementType } from '@/types/core/shape-type'
// Import the interfaces and type alias
import type {
  ShapeAddEvent,
  ShapeCreateEvent,
  ShapeDeleteEvent,
  ShapeDuplicateEvent,
  ShapeEditEvent,
  ShapeEditRequestPayload,
  ShapeId,
  ShapeSelectedEvent,
  ShapeUpdateRequestPayload,
} from '@/types/services/events/shapeEvents'
import { describe, expect, it } from 'vitest'
import { Point } from '@/types/core/element/geometry'
// Import AppEventType for type checking
import { AppEventType } from '@/types/services/events/eventTypes'

describe('shape Event Types', () => {
  it('should allow declaration using ShapeId type alias', () => {
    const id: ShapeId = 'shape-123'
    expect(typeof id).toBe('string')
  })

  it('should allow declaration using ShapeCreateEvent interface', () => {
    const event: <PERSON>hapeCreateEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: ShapeCreateEvent = {
      type: AppEventType.SHAPE_CREATE_REQUEST,
      payload: {
        ElementType: 'rectangle' as ElementType,
        properties: { width: 10, height: 10 },
        position: { x: 0, y: 0 },
      },
    }
    expect(example.type).toBe(AppEventType.SHAPE_CREATE_REQUEST)
    expect(example.payload.ElementType).toBe('rectangle')
  })

  it('should allow declaration using ShapeEditEvent interface', () => {
    const event: ShapeEditEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: ShapeEditEvent = {
      type: AppEventType.SHAPE_EDIT_REQUEST,
      payload: {
        shapeId: 's1',
        changes: { position: { x: 5, y: 5 } },
      },
    }
    expect(example.type).toBe(AppEventType.SHAPE_EDIT_REQUEST)
    expect(example.payload.changes.position).toEqual({ x: 5, y: 5 })
  })

  it('should allow declaration using ShapeDeleteEvent interface', () => {
    const event: ShapeDeleteEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: ShapeDeleteEvent = {
      type: AppEventType.SHAPE_DELETE_COMPLETE,
      payload: { shapeIds: ['s1', 's2'] },
    }
    expect(example.type).toBe(AppEventType.SHAPE_DELETE_COMPLETE)
    expect(example.payload.shapeIds).toHaveLength(2)
  })

  it('should allow declaration using ShapeSelectedEvent interface', () => {
    const event: ShapeSelectedEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: ShapeSelectedEvent = {
      type: AppEventType.SHAPE_SELECTED,
      payload: { shape: { id: 's3', type: 'ellipse' } }, // Using Record<string, unknown>
    }
    expect(example.type).toBe(AppEventType.SHAPE_SELECTED)
    expect(example.payload.shape?.id).toBe('s3')
  })

  it('should allow declaration using ShapeDuplicateEvent interface', () => {
    const event: ShapeDuplicateEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: ShapeDuplicateEvent = {
      type: AppEventType.SHAPE_DUPLICATE_COMPLETE,
      payload: {
        originalId: 's4',
        position: { x: 10, y: 10 },
        duplicate: { id: 's5' } as Shape, // Mock Shape
      },
    }
    expect(example.type).toBe(AppEventType.SHAPE_DUPLICATE_COMPLETE)
    expect(example.payload.duplicate?.id).toBe('s5')
  })

  it('should allow declaration using ShapeAddEvent interface', () => {
    const event: ShapeAddEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: ShapeAddEvent = {
      type: AppEventType.SHAPE_ADD,
      payload: { id: 's6', type: 'polygon' } as ShapeModel, // Mock ShapeModel
    }
    expect(example.type).toBe(AppEventType.SHAPE_ADD)
    expect(example.payload.id).toBe('s6')
  })

  it('should allow declaration using ShapeEditRequestPayload interface', () => {
    const payload: ShapeEditRequestPayload | undefined = undefined
    expect(payload).toBeUndefined()
    const example: ShapeEditRequestPayload = {
      id: 's7',
      changes: { name: 'New Name' }, // Partial<Omit<ShapeModel, 'id'>>
    }
    expect(example.id).toBe('s7')
    expect(example.changes.name).toBe('New Name')
  })

  it('should allow declaration using ShapeUpdateRequestPayload interface', () => {
    const payload: ShapeUpdateRequestPayload | undefined = undefined
    expect(payload).toBeUndefined()
    const example: ShapeUpdateRequestPayload = {
      id: 's8',
      changes: { position: new Point(100, 100) },
    }
    expect(example.id).toBe('s8')
    expect(example.changes.position?.x).toBe(100)
  })
})
