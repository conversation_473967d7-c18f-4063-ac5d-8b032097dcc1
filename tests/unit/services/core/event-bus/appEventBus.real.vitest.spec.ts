import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { appEventBus } from '@/services/core/event-bus/appEventBus'
import type { AppEventMap } from '@/types/services/events/eventRegistry'
import type { BaseEvent } from '@/types/services/events'

describe('appEventBus (Real Implementation)', () => {
  beforeEach(() => {
    // Reset the event bus before each test
    appEventBus.reset()
  })

  afterEach(() => {
    // Clean up after each test
    appEventBus.reset()
    vi.clearAllMocks()
  })

  describe('Event Subscription', () => {
    it('should subscribe to events', () => {
      const handler = vi.fn()
      
      const unsubscribe = appEventBus.subscribe('SHAPE_CREATED', handler)
      
      expect(typeof unsubscribe).toBe('function')
      expect(appEventBus.getSubscriptions().has('SHAPE_CREATED')).toBe(true)
    })

    it('should allow multiple subscriptions to same event', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      
      appEventBus.subscribe('SHAPE_CREATED', handler1)
      appEventBus.subscribe('SHAPE_CREATED', handler2)
      
      const subscriptions = appEventBus.getSubscriptions()
      const shapeCreatedHandlers = subscriptions.get('SHAPE_CREATED')
      expect(shapeCreatedHandlers?.length).toBe(2)
    })

    it('should return unsubscribe function', () => {
      const handler = vi.fn()
      
      const unsubscribe = appEventBus.subscribe('SHAPE_CREATED', handler)
      expect(appEventBus.getSubscriptions().has('SHAPE_CREATED')).toBe(true)
      
      unsubscribe()
      const subscriptions = appEventBus.getSubscriptions()
      const shapeCreatedHandlers = subscriptions.get('SHAPE_CREATED')
      expect(shapeCreatedHandlers?.length).toBe(0)
    })

    it('should handle subscription to different events', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      
      appEventBus.subscribe('SHAPE_CREATED', handler1)
      appEventBus.subscribe('SHAPE_DELETED', handler2)
      
      const subscriptions = appEventBus.getSubscriptions()
      expect(subscriptions.has('SHAPE_CREATED')).toBe(true)
      expect(subscriptions.has('SHAPE_DELETED')).toBe(true)
    })

    it('should support on/off methods', () => {
      const handler = vi.fn()
      
      appEventBus.on('SHAPE_CREATED', handler)
      expect(appEventBus.getSubscriptions().has('SHAPE_CREATED')).toBe(true)
      
      appEventBus.off('SHAPE_CREATED', handler)
      const subscriptions = appEventBus.getSubscriptions()
      const shapeCreatedHandlers = subscriptions.get('SHAPE_CREATED')
      expect(shapeCreatedHandlers?.length).toBe(0)
    })
  })

  describe('Event Publishing', () => {
    it('should publish events to subscribers', () => {
      const handler = vi.fn()
      const payload = { shapeId: 'test-shape', shapeType: 'rectangle' }
      
      appEventBus.subscribe('SHAPE_CREATED', handler)
      appEventBus.publish('SHAPE_CREATED', payload)
      
      expect(handler).toHaveBeenCalledWith(payload)
      expect(handler).toHaveBeenCalledTimes(1)
    })

    it('should publish to multiple subscribers', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      const payload = { shapeId: 'test-shape', shapeType: 'rectangle' }
      
      appEventBus.subscribe('SHAPE_CREATED', handler1)
      appEventBus.subscribe('SHAPE_CREATED', handler2)
      appEventBus.publish('SHAPE_CREATED', payload)
      
      expect(handler1).toHaveBeenCalledWith(payload)
      expect(handler2).toHaveBeenCalledWith(payload)
    })

    it('should not affect other event types', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      
      appEventBus.subscribe('SHAPE_CREATED', handler1)
      appEventBus.subscribe('SHAPE_DELETED', handler2)
      appEventBus.publish('SHAPE_CREATED', { shapeId: 'test' })
      
      expect(handler1).toHaveBeenCalledWith({ shapeId: 'test' })
      expect(handler2).not.toHaveBeenCalled()
    })

    it('should handle events with no payload', () => {
      const handler = vi.fn()
      
      appEventBus.subscribe('CANVAS_CLEARED', handler)
      appEventBus.publish('CANVAS_CLEARED')
      
      expect(handler).toHaveBeenCalledWith(undefined)
    })

    it('should handle events with null payload', () => {
      const handler = vi.fn()
      
      appEventBus.subscribe('SHAPE_CREATED', handler)
      appEventBus.publish('SHAPE_CREATED', null)
      
      expect(handler).toHaveBeenCalledWith(null)
    })

    it('should support createEvent method', () => {
      const event = appEventBus.createEvent('SHAPE_CREATED', { shapeId: 'test' })
      
      expect(event).toEqual({
        type: 'SHAPE_CREATED',
        payload: { shapeId: 'test' },
        timestamp: expect.any(Number),
      })
    })
  })

  describe('Event Unsubscription', () => {
    it('should unsubscribe using returned function', () => {
      const handler = vi.fn()
      
      const unsubscribe = appEventBus.subscribe('SHAPE_CREATED', handler)
      appEventBus.publish('SHAPE_CREATED', { shapeId: 'before' })
      
      unsubscribe()
      appEventBus.publish('SHAPE_CREATED', { shapeId: 'after' })
      
      expect(handler).toHaveBeenCalledTimes(1)
      expect(handler).toHaveBeenCalledWith({ shapeId: 'before' })
    })

    it('should unsubscribe using off method', () => {
      const handler = vi.fn()
      
      appEventBus.on('SHAPE_CREATED', handler)
      appEventBus.publish('SHAPE_CREATED', { shapeId: 'before' })
      
      appEventBus.off('SHAPE_CREATED', handler)
      appEventBus.publish('SHAPE_CREATED', { shapeId: 'after' })
      
      expect(handler).toHaveBeenCalledTimes(1)
      expect(handler).toHaveBeenCalledWith({ shapeId: 'before' })
    })

    it('should only unsubscribe specific handler', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      
      const unsubscribe1 = appEventBus.subscribe('SHAPE_CREATED', handler1)
      appEventBus.subscribe('SHAPE_CREATED', handler2)
      
      unsubscribe1()
      appEventBus.publish('SHAPE_CREATED', { shapeId: 'test' })
      
      expect(handler1).not.toHaveBeenCalled()
      expect(handler2).toHaveBeenCalledWith({ shapeId: 'test' })
    })

    it('should handle unsubscribing non-existent handler', () => {
      const handler = vi.fn()
      
      expect(() => {
        appEventBus.off('SHAPE_CREATED', handler)
      }).not.toThrow()
    })
  })

  describe('Configuration', () => {
    it('should support configuration', () => {
      const config = {
        enableLogging: true,
        maxListeners: 100,
      }
      
      expect(() => {
        appEventBus.configure(config)
      }).not.toThrow()
    })

    it('should handle invalid configuration gracefully', () => {
      expect(() => {
        appEventBus.configure(null as any)
      }).not.toThrow()
      
      expect(() => {
        appEventBus.configure(undefined as any)
      }).not.toThrow()
    })
  })

  describe('Error Handling', () => {
    it('should handle errors in event handlers', () => {
      const errorHandler = vi.fn(() => {
        throw new Error('Handler error')
      })
      const normalHandler = vi.fn()
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      appEventBus.subscribe('SHAPE_CREATED', errorHandler)
      appEventBus.subscribe('SHAPE_CREATED', normalHandler)
      
      appEventBus.publish('SHAPE_CREATED', { shapeId: 'test' })
      
      expect(errorHandler).toHaveBeenCalled()
      expect(normalHandler).toHaveBeenCalled()
      
      consoleSpy.mockRestore()
    })

    it('should handle publishing to non-existent event type', () => {
      expect(() => {
        appEventBus.publish('NON_EXISTENT_EVENT' as any, { data: 'test' })
      }).not.toThrow()
    })

    it('should handle multiple unsubscriptions', () => {
      const handler = vi.fn()
      
      const unsubscribe = appEventBus.subscribe('SHAPE_CREATED', handler)
      unsubscribe()
      
      expect(() => {
        unsubscribe() // Second call should not throw
      }).not.toThrow()
    })
  })

  describe('State Management', () => {
    it('should provide subscription information', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      
      appEventBus.subscribe('SHAPE_CREATED', handler1)
      appEventBus.subscribe('SHAPE_DELETED', handler2)
      
      const subscriptions = appEventBus.getSubscriptions()
      expect(subscriptions.has('SHAPE_CREATED')).toBe(true)
      expect(subscriptions.has('SHAPE_DELETED')).toBe(true)
    })

    it('should reset all subscriptions', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      
      appEventBus.subscribe('SHAPE_CREATED', handler1)
      appEventBus.subscribe('SHAPE_DELETED', handler2)
      
      appEventBus.reset()
      
      const subscriptions = appEventBus.getSubscriptions()
      expect(subscriptions.size).toBe(0)
    })
  })

  describe('Complex Event Scenarios', () => {
    it('should handle rapid subscribe/unsubscribe cycles', () => {
      const handler = vi.fn()
      
      for (let i = 0; i < 100; i++) {
        const unsubscribe = appEventBus.subscribe('SHAPE_CREATED', handler)
        unsubscribe()
      }
      
      appEventBus.publish('SHAPE_CREATED', { shapeId: 'test' })
      expect(handler).not.toHaveBeenCalled()
    })

    it('should handle subscription during event handling', () => {
      const handler1 = vi.fn(() => {
        appEventBus.subscribe('SHAPE_CREATED', handler2)
      })
      const handler2 = vi.fn()
      
      appEventBus.subscribe('SHAPE_CREATED', handler1)
      appEventBus.publish('SHAPE_CREATED', { shapeId: 'test' })
      
      expect(handler1).toHaveBeenCalled()
      const subscriptions = appEventBus.getSubscriptions()
      const shapeCreatedHandlers = subscriptions.get('SHAPE_CREATED')
      expect(shapeCreatedHandlers?.length).toBe(2)
    })

    it('should handle unsubscription during event handling', () => {
      let unsubscribe2: (() => void) | undefined
      
      const handler1 = vi.fn(() => {
        if (unsubscribe2) unsubscribe2()
      })
      const handler2 = vi.fn()
      
      appEventBus.subscribe('SHAPE_CREATED', handler1)
      unsubscribe2 = appEventBus.subscribe('SHAPE_CREATED', handler2)
      
      appEventBus.publish('SHAPE_CREATED', { shapeId: 'test' })
      
      expect(handler1).toHaveBeenCalled()
      // handler2 behavior depends on implementation details
      const subscriptions = appEventBus.getSubscriptions()
      const shapeCreatedHandlers = subscriptions.get('SHAPE_CREATED')
      expect(shapeCreatedHandlers?.length).toBe(1)
    })
  })

  describe('Performance', () => {
    it('should handle many subscribers efficiently', () => {
      const handlers = Array.from({ length: 1000 }, () => vi.fn())
      
      const startTime = Date.now()
      
      handlers.forEach(handler => {
        appEventBus.subscribe('SHAPE_CREATED', handler)
      })
      
      appEventBus.publish('SHAPE_CREATED', { shapeId: 'test' })
      
      const endTime = Date.now()
      
      handlers.forEach(handler => {
        expect(handler).toHaveBeenCalledWith({ shapeId: 'test' })
      })
      
      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly
    })

    it('should handle many events efficiently', () => {
      const handler = vi.fn()
      appEventBus.subscribe('SHAPE_CREATED', handler)
      
      const startTime = Date.now()
      
      for (let i = 0; i < 1000; i++) {
        appEventBus.publish('SHAPE_CREATED', { shapeId: `shape-${i}` })
      }
      
      const endTime = Date.now()
      
      expect(handler).toHaveBeenCalledTimes(1000)
      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly
    })
  })
})
