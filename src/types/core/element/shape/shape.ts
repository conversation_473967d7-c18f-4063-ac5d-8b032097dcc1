/**
 * Exports type definitions for all standard shape elements in the application.
 *
 * @remarks
 * This module serves as a barrel file, re-exporting interfaces and types for common
 * geometric shapes such as rectangles, ellipses, and polygons. Each shape type typically
 * extends the base {@link ShapeElement} interface and defines its own specific
 * geometric properties and behaviors. All shape types are expected to implement
 * the base `ShapeElement` interface.
 *
 * @module types/core/element/shape
 * @see {@link ShapeElement} for the base shape element interface.
 * @see {@link Point} for the core geometry type used by shapes.
 * @see {@link ./rectangle} for rectangle shape type definitions.
 * @see {@link ./ellipse} for ellipse and circle shape type definitions.
 * @see {@link ./polygon} for polygon shape type definitions.
 */

/** Re-exports all types from the `./ellipse` module, including {@link Ellipse} and {@link Circle}. */
export * from './ellipseShapeTypes'
/** Re-exports all types from the `./polygon` module, including {@link Polygon}. */
// Removed circular export: export * from './shape';
// ShapeModel and other base shape types should be defined and exported from a more appropriate location,
// or this file should define them if it's meant to be the source of truth for base shape definitions.
// For now, assuming ShapeModel is defined elsewhere and re-exported by a higher-level barrel file like element.ts or models/index.ts.
export * from './polygonShapeTypes' // Assuming this was the intended export instead of a self-reference.
/** Re-exports all types from the `./rectangle` module, including {@link Rectangle} and {@link Square}. */
export * from './rectangleShapeTypes'
// If polygonShapeTypes.ts doesn't exist or is wrong, this needs adjustment.
// Based on other exports, it seems specific shape types are in their own files.
// If ShapeModel is a base type, it should not be in polygonShapeTypes.
// Let's stick to removing the circular dependency for now.
