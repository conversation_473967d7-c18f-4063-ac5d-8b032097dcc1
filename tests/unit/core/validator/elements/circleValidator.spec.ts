/**
 * Unit tests for Circle validation using EllipseValidator
 *
 * Tests the circle validation functionality in EllipseValidator
 */

import { expect, test } from '@playwright/test'
import { EllipseValidator } from '@/core/validator/elements/ellipseValidator'
import { ElementType } from '@/types/core/shape-type'

test.describe('Circle Validation (using EllipseValidator)', () => {
  let validator: EllipseValidator

  test.beforeEach(() => {
    validator = new EllipseValidator()
  })

  test('validates a valid circle', () => {
    // Create a valid circle
    const validCircle = {
      id: 'circle1',
      type: ElementType.CIRCLE,
      position: { x: 100, y: 100 },
      radius: 50,
    }

    // Validate the circle
    const result = validator.validate(validCircle)

    // Verify the result
    expect(result.valid).toBe(true)
    expect(result.errors).toEqual([])
  })

  test('detects a circle with missing ID', () => {
    // Create a circle with missing ID
    const invalidCircle = {
      id: '',
      type: ElementType.CIRCLE,
      position: { x: 100, y: 100 },
      radius: 50,
    }

    // Validate the circle
    const result = validator.validate(invalidCircle)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'MISSING_OR_INVALID_ID')).toBe(true)
  })

  test('detects a circle with invalid position', () => {
    // Create a circle with invalid position
    const invalidCircle = {
      id: 'circle2',
      type: ElementType.CIRCLE,
      position: null,
      radius: 50,
    }

    // Validate the circle
    const result = validator.validate(invalidCircle)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_POSITION')).toBe(true)
  })

  test('detects a circle with negative radius', () => {
    // Create a circle with negative radius
    const invalidCircle = {
      id: 'circle3',
      type: ElementType.CIRCLE,
      position: { x: 100, y: 100 },
      radius: -50,
    }

    // Validate the circle
    const result = validator.validate(invalidCircle)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_RADIUS')).toBe(true)
  })

  test('detects a circle with zero radius', () => {
    // Create a circle with zero radius
    const invalidCircle = {
      id: 'circle4',
      type: ElementType.CIRCLE,
      position: { x: 100, y: 100 },
      radius: 0,
    }

    // Validate the circle
    const result = validator.validate(invalidCircle)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_RADIUS')).toBe(true)
  })

  test('detects multiple validation errors', () => {
    // Create a circle with multiple issues
    const invalidCircle = {
      id: '', // Missing ID
      type: ElementType.CIRCLE,
      position: null, // Invalid position
      radius: 0, // Invalid radius
    }

    // Validate the circle
    const result = validator.validate(invalidCircle)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.length).toBeGreaterThan(2)
    expect(result.errors.some(e => e.code === 'MISSING_OR_INVALID_ID')).toBe(true)
    expect(result.errors.some(e => e.code === 'INVALID_POSITION')).toBe(true)
    expect(result.errors.some(e => e.code === 'INVALID_RADIUS')).toBe(true)
  })
})
