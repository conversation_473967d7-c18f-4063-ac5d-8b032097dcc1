import type { StrategyRegistry } from '@/core/compute/StrategyRegistry'
import type { ElementFactory } from '@/core/factory/ElementFactory'
import type { ShapeRepository } from '@/core/state/ShapeRepository'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ComputeFacade } from '@/core/compute/ComputeFacade'
import { CoreError, ErrorType } from '@/services/errors'
import { ComputeOperation } from '@/types/core/element/compute'

// 模拟策略
const mockAreaStrategy = {
  getElementType: () => 'rectangle',
  calculateArea: vi.fn().mockReturnValue(100),
}

const mockPerimeterStrategy = {
  getElementType: () => 'rectangle',
  calculatePerimeter: vi.fn().mockReturnValue(40),
}

const mockBoundingBoxStrategy = {
  getElementType: () => 'rectangle',
  calculateBoundingBox: vi.fn().mockReturnValue({ x: 0, y: 0, width: 10, height: 10 }),
}

const mockDistanceStrategy = {
  getElementType: () => 'rectangle',
  calculateDistance: vi.fn().mockReturnValue(5),
}

const mockCostStrategy = {
  getElementType: () => 'rectangle',
  calculateCost: vi.fn().mockReturnValue(1000),
}

const mockMaterialStrategy = {
  getElementType: () => 'rectangle',
  calculateMaterialAmount: vi.fn().mockReturnValue({ amount: 10, unit: 'm²' }),
}

const mockSpacePlanningStrategy = {
  getElementType: () => 'rectangle',
  calculateSpaceUtilization: vi.fn().mockReturnValue({ utilization: 0.75 }),
  checkPathwayWidth: vi.fn().mockReturnValue({ pathways: [{ id: 'path1', width: 1.2, isValid: true }] }),
}

// 模拟元素
const mockElement = {
  id: 'test-rectangle',
  type: 'shape',
  getSubType: () => 'rectangle',
  getArea: () => 100,
  getPerimeter: () => 40,
}

describe('computeFacade - Basic Tests', () => {
  let computeFacade: ComputeFacade
  let strategyRegistry: StrategyRegistry
  let shapeRepository: ShapeRepository
  let elementFactory: ElementFactory
  let consoleSpy: { log: any, warn: any, debug: any, error: any }

  beforeEach(() => {
    // 创建模拟对象
    strategyRegistry = {
      getAreaStrategy: vi.fn().mockReturnValue(mockAreaStrategy),
      getPerimeterStrategy: vi.fn().mockReturnValue(mockPerimeterStrategy),
      getBoundingBoxStrategy: vi.fn().mockReturnValue(mockBoundingBoxStrategy),
      getDistanceStrategy: vi.fn().mockReturnValue(mockDistanceStrategy),
      getCostStrategy: vi.fn().mockReturnValue(mockCostStrategy),
      getMaterialStrategy: vi.fn().mockReturnValue(mockMaterialStrategy),
      getSpacePlanningStrategy: vi.fn().mockReturnValue(mockSpacePlanningStrategy),
      hasStrategy: vi.fn().mockReturnValue(true),
      getSupportedElementTypes: vi.fn().mockReturnValue(['rectangle', 'circle']),
      registerAreaStrategy: vi.fn(),
      registerPerimeterStrategy: vi.fn(),
      registerBoundingBoxStrategy: vi.fn(),
      registerDistanceStrategy: vi.fn(),
      registerCostStrategy: vi.fn(),
      registerMaterialStrategy: vi.fn(),
      registerSpacePlanningStrategy: vi.fn(),
      registerStrategies: vi.fn(),
    } as unknown as StrategyRegistry

    shapeRepository = {
      getById: vi.fn().mockReturnValue({ id: 'test-rectangle', type: 'rectangle' }),
    } as unknown as ShapeRepository

    elementFactory = {
      createShape: vi.fn().mockResolvedValue(mockElement),
    } as unknown as ElementFactory

    // 创建 ComputeFacade 实例
    computeFacade = new ComputeFacade(strategyRegistry, shapeRepository, elementFactory)

    // 监视控制台输出
    consoleSpy = {
      log: vi.spyOn(console, 'log').mockImplementation(() => {}),
      warn: vi.spyOn(console, 'warn').mockImplementation(() => {}),
      debug: vi.spyOn(console, 'debug').mockImplementation(() => {}),
      error: vi.spyOn(console, 'error').mockImplementation(() => {}),
    }
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('constructor', () => {
    it('should create an instance with injected dependencies', () => {
      expect(computeFacade).toBeDefined()
      expect(consoleSpy.log).toHaveBeenCalledWith(expect.stringContaining('Instance created with injected dependencies'))
    })
  })

  describe('computeArea', () => {
    it('should compute area for a valid shape', async () => {
      const result = await computeFacade.computeArea('test-rectangle')
      expect(result).toBe(100)
      expect(strategyRegistry.getAreaStrategy).toHaveBeenCalledWith('rectangle')
      expect(mockAreaStrategy.calculateArea).toHaveBeenCalledWith(mockElement)
    })

    it('should throw error if shape is not found', async () => {
      shapeRepository.getById = vi.fn().mockReturnValue(undefined)
      await expect(computeFacade.computeArea('non-existent')).rejects.toThrow(CoreError)
      await expect(computeFacade.computeArea('non-existent')).rejects.toThrow(ErrorType.COORDINATOR_SHAPE_NOT_FOUND)
    })

    it('should throw error if element creation fails', async () => {
      elementFactory.createShape = vi.fn().mockResolvedValue(undefined)
      await expect(computeFacade.computeArea('test-rectangle')).rejects.toThrow(CoreError)
      await expect(computeFacade.computeArea('test-rectangle')).rejects.toThrow(ErrorType.FACTORY_FAILED)
    })

    it('should propagate strategy errors', async () => {
      mockAreaStrategy.calculateArea = vi.fn().mockImplementation(() => {
        throw new Error('Strategy error')
      })
      await expect(computeFacade.computeArea('test-rectangle')).rejects.toThrow(CoreError)
      await expect(computeFacade.computeArea('test-rectangle')).rejects.toThrow('Strategy error')
    })
  })

  describe('computePerimeter', () => {
    it('should compute perimeter for a valid shape', async () => {
      const result = await computeFacade.computePerimeter('test-rectangle')
      expect(result).toBe(40)
      expect(strategyRegistry.getPerimeterStrategy).toHaveBeenCalledWith('rectangle')
      expect(mockPerimeterStrategy.calculatePerimeter).toHaveBeenCalledWith(mockElement)
    })

    it('should throw error if shape is not found', async () => {
      shapeRepository.getById = vi.fn().mockReturnValue(undefined)
      await expect(computeFacade.computePerimeter('non-existent')).rejects.toThrow(CoreError)
      await expect(computeFacade.computePerimeter('non-existent')).rejects.toThrow(ErrorType.COORDINATOR_SHAPE_NOT_FOUND)
    })
  })

  describe('computeBoundingBox', () => {
    it('should compute bounding box for a valid shape', async () => {
      const result = await computeFacade.computeBoundingBox('test-rectangle')
      expect(result).toEqual({ x: 0, y: 0, width: 10, height: 10 })
      expect(strategyRegistry.getBoundingBoxStrategy).toHaveBeenCalledWith('rectangle')
      expect(mockBoundingBoxStrategy.calculateBoundingBox).toHaveBeenCalledWith(mockElement)
    })
  })

  describe('computeDistance', () => {
    it('should compute distance between two shapes', async () => {
      const result = await computeFacade.computeDistance('test-rectangle', 'test-rectangle-2')
      expect(result).toBe(5)
      expect(strategyRegistry.getDistanceStrategy).toHaveBeenCalledWith('rectangle')
      expect(mockDistanceStrategy.calculateDistance).toHaveBeenCalledWith(mockElement, mockElement)
    })

    it('should throw error if first shape is not found', async () => {
      shapeRepository.getById = vi.fn().mockImplementation((id) => {
        if (id === 'non-existent')
          return undefined
        return { id: 'test-rectangle-2', type: 'rectangle' }
      })
      await expect(computeFacade.computeDistance('non-existent', 'test-rectangle-2')).rejects.toThrow(CoreError)
      await expect(computeFacade.computeDistance('non-existent', 'test-rectangle-2')).rejects.toThrow(ErrorType.COORDINATOR_SHAPE_NOT_FOUND)
    })

    it('should throw error if second shape is not found', async () => {
      shapeRepository.getById = vi.fn().mockImplementation((id) => {
        if (id === 'non-existent')
          return undefined
        return { id: 'test-rectangle', type: 'rectangle' }
      })
      await expect(computeFacade.computeDistance('test-rectangle', 'non-existent')).rejects.toThrow(CoreError)
      await expect(computeFacade.computeDistance('test-rectangle', 'non-existent')).rejects.toThrow(ErrorType.COORDINATOR_SHAPE_NOT_FOUND)
    })
  })

  describe('isPointInside', () => {
    it('should throw error indicating the method is deprecated', async () => {
      await expect(computeFacade.isPointInside('test-rectangle', 5, 5)).rejects.toThrow(CoreError)
      await expect(computeFacade.isPointInside('test-rectangle', 5, 5)).rejects.toThrow(ErrorType.UNSUPPORTED_OPERATION)
      await expect(computeFacade.isPointInside('test-rectangle', 5, 5)).rejects.toThrow('deprecated')
    })
  })

  describe('computeCost', () => {
    it('should compute cost for a valid shape', async () => {
      const result = await computeFacade.computeCost('test-rectangle', 50)
      expect(result).toBe(1000)
      expect(strategyRegistry.getCostStrategy).toHaveBeenCalledWith('rectangle')
      expect(mockCostStrategy.calculateCost).toHaveBeenCalledWith(mockElement, 50, undefined)
    })

    it('should pass options to the strategy', async () => {
      const options = { additionalCost: 100, discountRate: 0.1 }
      await computeFacade.computeCost('test-rectangle', 50, options)
      expect(mockCostStrategy.calculateCost).toHaveBeenCalledWith(mockElement, 50, options)
    })
  })

  describe('computeMaterial', () => {
    it('should compute material amount for a valid shape', async () => {
      const result = await computeFacade.computeMaterial('test-rectangle', 'wood')
      expect(result).toEqual({ amount: 10, unit: 'm²' })
      expect(strategyRegistry.getMaterialStrategy).toHaveBeenCalledWith('rectangle')
      expect(mockMaterialStrategy.calculateMaterialAmount).toHaveBeenCalledWith(mockElement, 'wood', undefined)
    })

    it('should pass options to the strategy', async () => {
      const options = { wastageRate: 10 }
      await computeFacade.computeMaterial('test-rectangle', 'wood', options)
      expect(mockMaterialStrategy.calculateMaterialAmount).toHaveBeenCalledWith(mockElement, 'wood', options)
    })
  })

  describe('computeSpacePlanning', () => {
    it('should compute space utilization', async () => {
      const result = await computeFacade.computeSpacePlanning(['test-rectangle', 'test-rectangle-2'], 'livingRoom', 'utilization')
      expect(result).toEqual({ utilization: 0.75 })
      expect(strategyRegistry.getSpacePlanningStrategy).toHaveBeenCalledWith('livingRoom')
      expect(mockSpacePlanningStrategy.calculateSpaceUtilization).toHaveBeenCalled()
    })

    it('should check pathway width', async () => {
      const options = { pathways: [{ start: { x: 0, y: 0 }, end: { x: 10, y: 10 } }], minWidth: 1.0 }
      const result = await computeFacade.computeSpacePlanning(['test-rectangle', 'test-rectangle-2'], 'livingRoom', 'pathwayCheck', options)
      expect(result).toEqual({ pathways: [{ id: 'path1', width: 1.2, isValid: true }] })
      expect(strategyRegistry.getSpacePlanningStrategy).toHaveBeenCalledWith('livingRoom')
      expect(mockSpacePlanningStrategy.checkPathwayWidth).toHaveBeenCalled()
    })

    it('should throw error for unknown operation', async () => {
      await expect(computeFacade.computeSpacePlanning(['test-rectangle'], 'livingRoom', 'unknown')).rejects.toThrow(CoreError)
      await expect(computeFacade.computeSpacePlanning(['test-rectangle'], 'livingRoom', 'unknown')).rejects.toThrow(ErrorType.INVALID_PAYLOAD)
    })

    it('should throw error if pathways and minWidth are missing for pathway check', async () => {
      await expect(computeFacade.computeSpacePlanning(['test-rectangle'], 'livingRoom', 'pathwayCheck')).rejects.toThrow(CoreError)
      await expect(computeFacade.computeSpacePlanning(['test-rectangle'], 'livingRoom', 'pathwayCheck')).rejects.toThrow(ErrorType.INVALID_PAYLOAD)
    })
  })

  describe('registerOperation', () => {
    it('should register a custom operation', () => {
      const customOperation = vi.fn().mockResolvedValue({ custom: 'result' })
      computeFacade.registerOperation('customOp', customOperation)
      expect(consoleSpy.log).toHaveBeenCalledWith(expect.stringContaining('Registered custom operation: customOp'))
    })

    it('should warn when overwriting an existing operation', () => {
      const customOperation = vi.fn().mockResolvedValue({ custom: 'result' })
      computeFacade.registerOperation('customOp', customOperation)
      computeFacade.registerOperation('customOp', customOperation)
      expect(consoleSpy.warn).toHaveBeenCalledWith(expect.stringContaining('Overwriting existing standard or custom operation'))
    })
  })

  describe('isComputationSupported', () => {
    it('should return true for registered custom operations', () => {
      const customOperation = vi.fn().mockResolvedValue({ custom: 'result' })
      computeFacade.registerOperation('customOp', customOperation)
      expect(computeFacade.isComputationSupported('customOp', 'rectangle')).toBe(true)
    })

    it('should check strategy registry for standard operations', () => {
      expect(computeFacade.isComputationSupported(ComputeOperation.AREA, 'rectangle')).toBe(true)
      expect(strategyRegistry.hasStrategy).toHaveBeenCalledWith('rectangle', 'area')
    })

    it('should return false for IS_POINT_INSIDE operation', () => {
      expect(computeFacade.isComputationSupported(ComputeOperation.IS_POINT_INSIDE, 'rectangle')).toBe(false)
    })

    it('should return true for TRANSFORM operation', () => {
      expect(computeFacade.isComputationSupported(ComputeOperation.TRANSFORM, 'rectangle')).toBe(true)
    })

    it('should warn for unrecognized operations', () => {
      expect(computeFacade.isComputationSupported('unknownOp', 'rectangle')).toBe(false)
      expect(consoleSpy.warn).toHaveBeenCalledWith(expect.stringContaining('not a recognized standard or registered custom operation'))
    })
  })

  describe('compute', () => {
    it('should throw error if no shape IDs are provided', async () => {
      await expect(computeFacade.compute(ComputeOperation.AREA, [])).rejects.toThrow(CoreError)
      await expect(computeFacade.compute(ComputeOperation.AREA, [])).rejects.toThrow(ErrorType.INVALID_PAYLOAD)
    })

    it('should execute custom operations', async () => {
      const customOperation = vi.fn().mockResolvedValue({ custom: 'result' })
      computeFacade.registerOperation('customOp', customOperation)

      const result = await computeFacade.compute('customOp', ['test-rectangle'])

      expect(customOperation).toHaveBeenCalledWith(['test-rectangle'], undefined)
      expect(result.operation).toBe('customOp')
      expect(result.result).toEqual({ custom: 'result' })
      expect(result.metadata.executionTime).toBeGreaterThan(0)
    })

    it('should throw error for unsupported operations', async () => {
      await expect(computeFacade.compute('unknownOp', ['test-rectangle'])).rejects.toThrow(CoreError)
      await expect(computeFacade.compute('unknownOp', ['test-rectangle'])).rejects.toThrow(ErrorType.COORDINATOR_OPERATION_FAILED)
    })

    it('should execute AREA operation', async () => {
      const result = await computeFacade.compute(ComputeOperation.AREA, ['test-rectangle'])
      expect(result.operation).toBe(ComputeOperation.AREA)
      expect(result.result).toBe(100)
      expect(result.metadata.shapeId).toBe('test-rectangle')
    })

    it('should execute PERIMETER operation', async () => {
      const result = await computeFacade.compute(ComputeOperation.PERIMETER, ['test-rectangle'])
      expect(result.operation).toBe(ComputeOperation.PERIMETER)
      expect(result.result).toBe(40)
    })

    it('should execute BOUNDING_BOX operation', async () => {
      const result = await computeFacade.compute(ComputeOperation.BOUNDING_BOX, ['test-rectangle'])
      expect(result.operation).toBe(ComputeOperation.BOUNDING_BOX)
      expect(result.result).toEqual({ x: 0, y: 0, width: 10, height: 10 })
    })

    it('should throw error for IS_POINT_INSIDE operation', async () => {
      await expect(computeFacade.compute(ComputeOperation.IS_POINT_INSIDE, ['test-rectangle'], { x: 5, y: 5 })).rejects.toThrow(CoreError)
      await expect(computeFacade.compute(ComputeOperation.IS_POINT_INSIDE, ['test-rectangle'], { x: 5, y: 5 })).rejects.toThrow(ErrorType.UNSUPPORTED_OPERATION)
    })

    it('should throw error for IS_POINT_INSIDE operation without coordinates', async () => {
      await expect(computeFacade.compute(ComputeOperation.IS_POINT_INSIDE, ['test-rectangle'])).rejects.toThrow(CoreError)
      await expect(computeFacade.compute(ComputeOperation.IS_POINT_INSIDE, ['test-rectangle'])).rejects.toThrow(ErrorType.INVALID_PAYLOAD)
    })

    it('should execute DISTANCE operation', async () => {
      const result = await computeFacade.compute(ComputeOperation.DISTANCE, ['test-rectangle'], { targetShapeId: 'test-rectangle-2' })
      expect(result.operation).toBe(ComputeOperation.DISTANCE)
      expect(result.result).toBe(5)
    })

    it('should throw error for DISTANCE operation without target shape ID', async () => {
      await expect(computeFacade.compute(ComputeOperation.DISTANCE, ['test-rectangle'])).rejects.toThrow(CoreError)
      await expect(computeFacade.compute(ComputeOperation.DISTANCE, ['test-rectangle'])).rejects.toThrow(ErrorType.INVALID_PAYLOAD)
    })

    it('should execute COST operation', async () => {
      const result = await computeFacade.compute(ComputeOperation.COST, ['test-rectangle'], { unitCost: 50 })
      expect(result.operation).toBe(ComputeOperation.COST)
      expect(result.result).toBe(1000)
    })

    it('should throw error for COST operation without unit cost', async () => {
      await expect(computeFacade.compute(ComputeOperation.COST, ['test-rectangle'])).rejects.toThrow(CoreError)
      await expect(computeFacade.compute(ComputeOperation.COST, ['test-rectangle'])).rejects.toThrow(ErrorType.INVALID_PAYLOAD)
    })

    it('should execute MATERIAL operation', async () => {
      const result = await computeFacade.compute(ComputeOperation.MATERIAL, ['test-rectangle'], { materialType: 'wood' })
      expect(result.operation).toBe(ComputeOperation.MATERIAL)
      expect(result.result).toEqual({ amount: 10, unit: 'm²' })
    })

    it('should throw error for MATERIAL operation without material type', async () => {
      await expect(computeFacade.compute(ComputeOperation.MATERIAL, ['test-rectangle'])).rejects.toThrow(CoreError)
      await expect(computeFacade.compute(ComputeOperation.MATERIAL, ['test-rectangle'])).rejects.toThrow(ErrorType.INVALID_PAYLOAD)
    })

    it('should execute SPACE_UTILIZATION operation', async () => {
      const result = await computeFacade.compute(ComputeOperation.SPACE_UTILIZATION, ['test-rectangle', 'test-rectangle-2'], { spaceType: 'livingRoom' })
      expect(result.operation).toBe(ComputeOperation.SPACE_UTILIZATION)
      expect(result.result).toEqual({ utilization: 0.75 })
    })

    it('should throw error for SPACE_UTILIZATION operation without space type', async () => {
      await expect(computeFacade.compute(ComputeOperation.SPACE_UTILIZATION, ['test-rectangle'])).rejects.toThrow(CoreError)
      await expect(computeFacade.compute(ComputeOperation.SPACE_UTILIZATION, ['test-rectangle'])).rejects.toThrow(ErrorType.INVALID_PAYLOAD)
    })

    it('should execute PATHWAY_CHECK operation', async () => {
      const options = {
        spaceType: 'livingRoom',
        spacePlanningOptions: {
          pathways: [{ start: { x: 0, y: 0 }, end: { x: 10, y: 10 } }],
          minWidth: 1.0,
        },
      }
      const result = await computeFacade.compute(ComputeOperation.PATHWAY_CHECK, ['test-rectangle', 'test-rectangle-2'], options)
      expect(result.operation).toBe(ComputeOperation.PATHWAY_CHECK)
      expect(result.result).toEqual({ pathways: [{ id: 'path1', width: 1.2, isValid: true }] })
    })

    it('should handle TRANSFORM operation', async () => {
      const result = await computeFacade.compute(ComputeOperation.TRANSFORM, ['test-rectangle'])
      expect(result.operation).toBe(ComputeOperation.TRANSFORM)
      expect(result.result).toEqual({ message: expect.stringContaining('Transform operations are handled by D3.js and SVG') })
      expect(consoleSpy.warn).toHaveBeenCalledWith(expect.stringContaining('Transform operation is now handled by D3.js and SVG'))
    })

    it('should warn when multiple shape IDs are provided for single-shape operations', async () => {
      await computeFacade.compute(ComputeOperation.AREA, ['test-rectangle', 'test-rectangle-2'])
      expect(consoleSpy.warn).toHaveBeenCalledWith(expect.stringContaining('typically uses only the first shape ID provided'))
    })
  })
})
