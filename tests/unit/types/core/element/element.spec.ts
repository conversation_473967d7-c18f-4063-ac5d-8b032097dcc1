import { describe, expect, it } from 'vitest'

// Import all element types from the element module
import * as ElementModule from '@/types/core/element/element'

describe('element Module', () => {
  it('should export all element types', () => {
    // Verify that the module exists
    expect(ElementModule).toBeDefined()

    // The element module is primarily a barrel file that re-exports from other modules
    // We can't directly test the exports without circular dependencies,
    // but we can verify that the module itself is defined
    expect(typeof ElementModule).toBe('object')
  })
})
