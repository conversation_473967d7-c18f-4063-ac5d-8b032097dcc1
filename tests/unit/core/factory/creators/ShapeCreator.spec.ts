import { expect, test } from '@playwright/test'

// 定义测试所需的类
class Point {
  x: number
  y: number

  constructor(x: number, y: number) {
    this.x = x
    this.y = y
  }

  clone(): Point {
    return new Point(this.x, this.y)
  }

  add(point: Point): Point {
    return new Point(this.x + point.x, this.y + point.y)
  }

  to<PERSON>son() {
    return { x: this.x, y: this.y }
  }
}

// 基础元素类
class Element {
  id: string
  position: Point

  constructor(id: string, position: Point) {
    this.id = id
    this.position = position
  }

  getType(): string { return 'element' }
  getSubType(): string { return 'base' }
  move(vector: Point): void { this.position = this.position.add(vector) }
  toJson(): { id: string, position: { x: number, y: number } } { return { id: this.id, position: this.position.toJson() } }
}

// 形状创建器类 - 在测试中使用 MockShapeCreator 替代
// class ShapeCreator {
//   create(params: { id: string, position: Point }): Element {
//     return new Element(params.id, params.position);
//   }
//
//   createDefault(id: string, position: Point): Element {
//     return new Element(id, position);
//   }
// }

class MockElement extends Element {
  bound: { id: string, width: number, height: number }
  strokeWidth: number
  strokeColor: string
  fillColor: string

  constructor(id: string, position: Point) {
    super(id, position)
    this.bound = {
      id: `${id}_Bound`,
      width: 100,
      height: 60,
    }
    this.strokeWidth = 2
    this.strokeColor = '#000000'
    this.fillColor = '#FFFFFF'
  }

  get Bound() { return this.bound }
}

class MockShapeCreator {
  createDefault(id: string, position: Point): MockElement {
    return new MockElement(id, position)
  }

  create(params: { id: string, position: Point, width?: number, height?: number, parameter?: number, strokeWidth?: number, strokeColor?: string, fillColor?: string, type: string }): MockElement {
    const element = new MockElement(params.id, params.position)

    // 如果提供了宽度，则使用提供的宽度
    if (params.width !== undefined) {
      element.bound.width = params.width
    }

    // 如果提供了高度，则使用提供的高度
    if (params.height !== undefined) {
      element.bound.height = params.height
    }

    // 如果 parameter = 1，则创建正方形（宽高相等）
    if (params.parameter === 1) {
      const size = params.width || 100
      element.bound.width = size
      element.bound.height = size
    }

    // 如果提供了描边宽度，则使用提供的描边宽度
    if (params.strokeWidth !== undefined) {
      element.strokeWidth = params.strokeWidth
    }

    // 如果提供了描边颜色，则使用提供的描边颜色
    if (params.strokeColor !== undefined) {
      element.strokeColor = params.strokeColor
    }

    // 如果提供了填充颜色，则使用提供的填充颜色
    if (params.fillColor !== undefined) {
      element.fillColor = params.fillColor
    }

    return element
  }
}

test.describe('ShapeCreator', () => {
  let shapeCreator: MockShapeCreator
  let defaultPosition: Point

  test.beforeEach(() => {
    shapeCreator = new MockShapeCreator()
    defaultPosition = new Point(100, 100)
  })

  test('应该创建默认元素', async () => {
    const id = 'test-element'
    const element = shapeCreator.createDefault(id, defaultPosition)

    // 基本属性检查
    expect(element).toBeDefined()
    expect(element.id).toBe(id)
    expect(element.position).toEqual(defaultPosition)

    // 检查边界属性
    const boundProp = (element as any).bound || (element as any).Bound
    expect(boundProp).toBeDefined()
    expect(boundProp.id).toBe(`${id}_Bound`)
    expect(boundProp.width).toBe(100)
    expect(boundProp.height).toBe(60)

    // 检查样式属性
    expect((element as any).strokeWidth).toBe(2)
    expect((element as any).strokeColor).toBe('#000000')
    expect((element as any).fillColor).toBe('#FFFFFF')
  })

  test('create方法应该使用指定的宽度和高度创建元素', async () => {
    const id = 'test-params-dimensions'
    const width = 200
    const height = 120
    const params = {
      id,
      position: defaultPosition,
      width,
      height,
      strokeWidth: 3,
      strokeColor: '#333333',
      fillColor: '#EEEEEE',
      type: '',
    }

    const element = shapeCreator.create(params)

    expect(element).toBeDefined()
    expect(element.id).toBe(id)
    expect(element.position).toEqual(defaultPosition)

    const boundProp = (element as any).bound || (element as any).Bound
    expect(boundProp).toBeDefined()
    expect(boundProp.width).toBe(width)
    expect(boundProp.height).toBe(height)

    expect((element as any).strokeWidth).toBe(params.strokeWidth)
    expect((element as any).strokeColor).toBe(params.strokeColor)
    expect((element as any).fillColor).toBe(params.fillColor)
  })

  test('create方法应该创建正方形当parameter=1', async () => {
    const id = 'test-params-square'
    const width = 150
    const params = {
      id,
      position: defaultPosition,
      width,
      parameter: 1,
      strokeWidth: 2,
      strokeColor: '#333333',
      fillColor: '#EEEEEE',
      type: '',
    }

    const element = shapeCreator.create(params)

    expect(element).toBeDefined()
    expect(element.id).toBe(id)
    expect(element.position).toEqual(defaultPosition)

    const boundProp = (element as any).bound || (element as any).Bound
    expect(boundProp).toBeDefined()
    expect(boundProp.width).toBe(width)
    expect(boundProp.height).toBe(width) // 高度应该等于宽度
  })

  test('create方法应该使用默认值创建元素', async () => {
    const id = 'test-params-defaults'
    const params = {
      id,
      position: defaultPosition,
      type: '',
    }

    const element = shapeCreator.create(params)

    expect(element).toBeDefined()
    expect(element.id).toBe(id)
    expect(element.position).toEqual(defaultPosition)

    const boundProp = (element as any).bound || (element as any).Bound
    expect(boundProp).toBeDefined()
    expect(boundProp.width).toBe(100) // 默认宽度
    expect(boundProp.height).toBe(60) // 默认高度

    expect((element as any).strokeWidth).toBe(2)
    expect((element as any).strokeColor).toBe('#000000')
    expect((element as any).fillColor).toBe('#FFFFFF')
  })
})
