/**
 * Element Factory Module Index
 *
 * @remarks
 * This file serves as the main entry point and barrel export for the element factory module.
 * It re-exports all factory-related components, including:
 * - The main {@link ElementFactory} class.
 * - All individual creator classes for shapes, paths, and design elements from the
 *   `./creators` submodule (see {@link module:core/factory/creators}).
 * - Key interfaces related to the factory, such as {@link IElementFactory},
 *   {@link PathCreationOptionsUnion}, and various `ShapeCreationParamsUnion` types,
 *   which are typically defined within or alongside `ElementFactory.ts`.
 * - The core {@link ElementType} enumeration from `@/types/core/elementDefinitions`.
 *
 * This centralized export simplifies importing factory functionalities into other
 * parts of the application.
 *
 * @module core/factory/index
 * @see {@link ElementFactory} For the main factory class.
 * @see {@link module:core/factory/creators} For individual creator implementations.
 */

// Export creator interfaces and all creators from the reorganized structure
export * from './creators'

// Export the main factory class
export { ElementFactory } from './ElementFactory'

// Export factory interfaces
export type {
  IElementFactory,
  PathCreationOptionsUnion,
} from './ElementFactory'

// Export shape creation params type
export type {
  CreateCircleParams,
  CreateEllipseParams,
  CreateLineParams,
  CreatePolygonParams,
  CreatePolylineParams,
  CreateRectangleParams,
  ShapeCreationParamsUnion,
} from './ElementFactory'

// Re-export from elementTypes
export type { ElementType } from '@/types/core/elementDefinitions'

// Removed re-export of PathType from validator
// // Export shape types from element validator
// export type {
//   PathType
// } from '../../types/core/element/validator';

// Removed local, redundant ShapeProperties and ShapeOptions interfaces
// // Define shape properties and options types locally
// export interface ShapeProperties { ... }
// export interface ShapeOptions { ... }
// export interface ShapeProperties { ... }
// export interface ShapeOptions { ... }
// export interface ShapeProperties { ... }
// export interface ShapeOptions { ... }
