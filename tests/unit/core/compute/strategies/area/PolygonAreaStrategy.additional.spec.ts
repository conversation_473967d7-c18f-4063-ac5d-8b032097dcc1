import type { Element } from '@/types/core/elementDefinitions'
import { describe, expect, it } from 'vitest'
import { PolygonAreaStrategy } from '@/core/compute/strategies/area/PolygonAreaStrategy'
import { CoreError } from '@/services/system/error-service/coreError'
import { ElementType } from '@/types/core/elementDefinitions'

/**
 * Additional unit tests for the PolygonAreaStrategy class to improve coverage
 */
describe('polygonAreaStrategy - Additional Tests', () => {
  let strategy: PolygonAreaStrategy

  beforeEach(() => {
    strategy = new PolygonAreaStrategy()
  })

  it('should return correct shape types', () => {
    const types = strategy.getElementType()
    expect(types).toContain(ElementType.POLYGON)
    expect(types).toContain(ElementType.TRIANGLE)
    expect(Array.isArray(types)).toBe(true)
  })

  it('should calculate area correctly for a triangle', () => {
    const triangle: any = {
      id: 'triangle-test',
      type: ElementType.POLYGON,
      visible: true,
      locked: false,
      metadata: {},
      points: [
        { x: 0, y: 0 },
        { x: 4, y: 0 },
        { x: 2, y: 3 },
      ],
      position: { x: 0, y: 0 },
    }

    const area = strategy.calculateArea(triangle)
    // Triangle area = 0.5 * base * height = 0.5 * 4 * 3 = 6
    expect(area).toBeCloseTo(6, 5)
  })

  it('should calculate area correctly for a square', () => {
    const square: any = {
      id: 'square-test',
      type: ElementType.POLYGON,
      visible: true,
      locked: false,
      metadata: {},
      points: [
        { x: 0, y: 0 },
        { x: 5, y: 0 },
        { x: 5, y: 5 },
        { x: 0, y: 5 },
      ],
      position: { x: 0, y: 0 },
    }

    const area = strategy.calculateArea(square)
    // Square area = 5 * 5 = 25
    expect(area).toBeCloseTo(25, 5)
  })

  it('should calculate area correctly for a rectangle', () => {
    const rectangle: any = {
      id: 'rectangle-test',
      type: ElementType.POLYGON,
      visible: true,
      locked: false,
      metadata: {},
      points: [
        { x: 0, y: 0 },
        { x: 8, y: 0 },
        { x: 8, y: 3 },
        { x: 0, y: 3 },
      ],
      position: { x: 0, y: 0 },
    }

    const area = strategy.calculateArea(rectangle)
    // Rectangle area = 8 * 3 = 24
    expect(area).toBeCloseTo(24, 5)
  })

  it('should throw error for polygon with fewer than 3 points', () => {
    const invalidPolygon: any = {
      id: 'invalid-polygon',
      type: ElementType.POLYGON,
      visible: true,
      locked: false,
      metadata: {},
      points: [
        { x: 0, y: 0 },
        { x: 5, y: 0 },
      ],
      position: { x: 0, y: 0 },
    }

    expect(() => strategy.calculateArea(invalidPolygon)).toThrow(CoreError)
    expect(() => strategy.calculateArea(invalidPolygon)).toThrow('Polygon element (ID: invalid-polygon) must have at least 3 points. Found: 2')
  })

  it('should throw error for polygon with no points', () => {
    const emptyPolygon: any = {
      id: 'empty-polygon',
      type: ElementType.POLYGON,
      visible: true,
      locked: false,
      metadata: {},
      points: [],
      position: { x: 0, y: 0 },
    }

    expect(() => strategy.calculateArea(emptyPolygon)).toThrow(CoreError)
    expect(() => strategy.calculateArea(emptyPolygon)).toThrow('Polygon element (ID: empty-polygon) must have at least 3 points. Found: 0')
  })

  it('should throw error for polygon with null points', () => {
    const nullPolygon: any = {
      id: 'null-polygon',
      type: ElementType.POLYGON,
      visible: true,
      locked: false,
      metadata: {},
      points: null,
      position: { x: 0, y: 0 },
    }

    expect(() => strategy.calculateArea(nullPolygon)).toThrow(CoreError)
    expect(() => strategy.calculateArea(nullPolygon)).toThrow('Polygon element (ID: null-polygon) must have at least 3 points. Found: 0')
  })

  it('should throw error for polygon with invalid coordinates', () => {
    const invalidPolygon: any = {
      id: 'invalid-coords-polygon',
      type: ElementType.POLYGON,
      visible: true,
      locked: false,
      metadata: {},
      points: [
        { x: 0, y: 0 },
        { x: Number.NaN, y: 0 },
        { x: 5, y: Number.NaN },
      ],
      position: { x: 0, y: 0 },
    }

    expect(() => strategy.calculateArea(invalidPolygon)).toThrow(CoreError)
    expect(() => strategy.calculateArea(invalidPolygon)).toThrow('Polygon element (ID: invalid-coords-polygon) contains invalid point data at index 1.')
  })

  it('should calculate area for complex polygon', () => {
    const hexagon: any = {
      id: 'hexagon-test',
      type: ElementType.POLYGON,
      visible: true,
      locked: false,
      metadata: {},
      points: [
        { x: 1, y: 0 },
        { x: 2, y: 0 },
        { x: 2.5, y: Math.sqrt(3) / 2 },
        { x: 2, y: Math.sqrt(3) },
        { x: 1, y: Math.sqrt(3) },
        { x: 0.5, y: Math.sqrt(3) / 2 },
      ],
      position: { x: 0, y: 0 },
    }

    const area = strategy.calculateArea(hexagon)
    // Regular hexagon with side length 1 has area = 3√3/2 ≈ 2.598
    expect(area).toBeCloseTo(2.598, 2)
  })

  it('should throw error for wrong element type', () => {
    const rectangle: Element = {
      id: 'not-polygon',
      type: ElementType.RECTANGLE,
      visible: true,
      locked: false,
      metadata: {},
    }

    expect(() => strategy.calculateArea(rectangle)).toThrow(CoreError)
    expect(() => strategy.calculateArea(rectangle)).toThrow('PolygonAreaStrategy can only calculate area for polygon-like elements, got RECTANGLE')
  })

  it('should throw error for null element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    expect(() => strategy.calculateArea(null)).toThrow(CoreError)
    expect(() => strategy.calculateArea(null)).toThrow('Invalid element: null or undefined')
  })

  it('should throw error for undefined element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    expect(() => strategy.calculateArea(undefined)).toThrow(CoreError)
    expect(() => strategy.calculateArea(undefined)).toThrow('Invalid element: null or undefined')
  })

  it('should handle triangle element type', () => {
    const triangle: any = {
      id: 'triangle-element',
      type: ElementType.TRIANGLE,
      visible: true,
      locked: false,
      metadata: {},
      points: [
        { x: 0, y: 0 },
        { x: 3, y: 0 },
        { x: 1.5, y: 2 },
      ],
      position: { x: 0, y: 0 },
    }

    const area = strategy.calculateArea(triangle)
    // Triangle area = 0.5 * base * height = 0.5 * 3 * 2 = 3
    expect(area).toBeCloseTo(3, 5)
  })

  it('should throw error for square element type (not supported)', () => {
    const square: any = {
      id: 'square-element',
      type: ElementType.SQUARE,
      visible: true,
      locked: false,
      metadata: {},
      points: [
        { x: 0, y: 0 },
        { x: 4, y: 0 },
        { x: 4, y: 4 },
        { x: 0, y: 4 },
      ],
      position: { x: 0, y: 0 },
    }

    expect(() => strategy.calculateArea(square)).toThrow(CoreError)
    expect(() => strategy.calculateArea(square)).toThrow('PolygonAreaStrategy can only calculate area for polygon-like elements, got SQUARE')
  })
})
