/**
 * Error Service Registration
 *
 * This module provides functions for registering the error service with the service registry.
 * It includes an adapter to ensure compatibility between different error service interface versions if necessary.
 *
 * @module services/system/error-service
 */

import type { ErrorService as ErrorServiceImpl } from './errorService' // Renamed to avoid conflict with interface
import type { ServiceRegistry } from '@/services/core/registry'
import type { ErrorDetails as ErrorDetailsType, ErrorService as ErrorServiceInterface } from '@/types/services/core/errorService' // ErrorContext removed as unused
// import { EventBus } from '@/types/services/events'; // Unused import
import type { LoggerService } from '@/types/services/logging'
import { getServiceFactory } from '@/services/core/registry'
import { ServiceId } from '@/types/services/core/serviceIdentifier'

/**
 * Adapter class to make the concrete ErrorServiceImpl compatible with the ErrorServiceInterface.
 * This can be useful if the implemented service has a slightly different signature
 * or needs to be adapted for a more generic interface.
 */
class ErrorServiceAdapter implements ErrorServiceInterface {
  /**
   * Creates an instance of ErrorServiceAdapter.
   * @param {ErrorServiceImpl} errorService - The concrete error service implementation to adapt.
   */
  constructor(private errorService: ErrorServiceImpl) {}

  /**
   * Creates a new standard Error object from ErrorDetails.
   * @param {ErrorDetailsType} details - The details to create the error from.
   * @returns {Error} A new Error object.
   */
  createError(details: ErrorDetailsType): Error {
    const error = new Error(details.message)
    error.name = (details.code !== null && details.code !== undefined && details.code !== '') ? details.code : 'Error'
    // Note: This adapter's createError might not produce a CoreError instance directly.
    // The underlying errorService.handleError will likely convert it.
    return error
  }

  /**
   * Logs an error or error details through the adapted error service.
   * @param {Error | ErrorDetailsType} errorOrDetails - The error object or details to log.
   */
  logError(errorOrDetails: Error | ErrorDetailsType): void {
    // The adapted errorService's handleError method is expected to handle logging.
    // This adapter ensures the input is passed correctly.
    this.errorService.logError(errorOrDetails) // Delegate to the adapted service's logError
  }

  /**
   * Reports an error, typically by logging it.
   * @param {Error | ErrorDetailsType} errorOrDetails - The error object or details to report.
   */
  reportError(errorOrDetails: Error | ErrorDetailsType): void {
    // For this adapter, reporting is treated the same as logging.
    this.logError(errorOrDetails)
  }

  /**
   * Handles an error or error details through the adapted error service.
   * @param {Error | ErrorDetailsType} errorOrDetails - The error object or details to handle.
   */
  handleError(errorOrDetails: Error | ErrorDetailsType): void {
    // This method directly calls the handleError of the wrapped ErrorServiceImpl.
    // The ErrorServiceImpl is responsible for the full handling logic (logging, event publishing).
    this.errorService.handleError(errorOrDetails)
  }
  // Removed unused private method errorDetailsToRecord
}

/**
 * Registers the error service with the service registry.
 * It uses a factory to create the concrete error service implementation and
 * wraps it with an adapter if necessary to conform to the {@link ErrorServiceInterface}.
 *
 * @param {ServiceRegistry} registry - The service registry instance.
 * @param {LoggerService} logger - The logger service instance, a dependency for the error service.
 * @returns {ErrorServiceInterface} The registered error service instance (potentially adapted).
 */
export function registerErrorService(
  registry: ServiceRegistry,
  logger: LoggerService,
): ErrorServiceInterface {
  const factory = getServiceFactory()
  // factory.createErrorService is expected to return the concrete ErrorServiceImpl
  const errorServiceImpl = factory.createErrorService(logger)

  // The adapter is used here to ensure the registered service strictly adheres to ErrorServiceInterface.
  // If ErrorServiceImpl already perfectly implements ErrorServiceInterface, this adapter might be simplified or removed.
  const adaptedErrorService = new ErrorServiceAdapter(errorServiceImpl)

  registry.register(ServiceId.ErrorService, adaptedErrorService)

  logger.info('Error service registered successfully.')

  return adaptedErrorService
}
