/**
 * Utility Functions for Circular Arcs
 *
 * @remarks
 * This module provides utility functions for calculations related to circular arcs,
 * such as calculating arc length, distances involving arcs, and their bounding boxes.
 * It uses the {@link ArcProperties} type (an alias for `Path.Arc` from core types)
 * to define arc parameters.
 *
 * Note: Some distance calculation functions (`calculateArcPointDistance`,
 * `calculateArcLineDistance`, `calculateArcArcDistance`) are currently
 * placeholder implementations and require full implementation.
 *
 * @module lib/utils/geometry/arcUtils
 */

import type { PointData as IPoint } from '../../../types/core/element/geometry/point'
import type { Arc as ArcProperties } from '../../../types/core/element/path/arcPathTypes' // Import Arc type
import type { ShapeElement } from '../../../types/core/elementDefinitions' // Import ShapeElement
import { BoundingBoxClass } from './BoundingBoxClass' // Import BoundingBoxClass
// import { PointClass } from './PointClass';
// import { Line } from './lineUtils'; // Assuming a Line type/interface might be needed

// Local IArc interface removed, will use ArcProperties (aliased as IArc for less code change initially if preferred)

/**
 * Converts an angle from degrees to radians.
 * @param degrees - The angle in degrees.
 * @returns The angle in radians.
 * @private
 */
function degreesToRadians(degrees: number): number {
  return degrees * (Math.PI / 180)
}

/**
 * Converts radians to degrees.
 * @param radians - The angle in radians.
 * @returns The angle in degrees.
 * @private
 */
function radiansToDegrees(radians: number): number {
  return (radians * 180) / Math.PI
}

/**
 * Calculates arc parameters from three points: center, start, and end.
 *
 * @param center - The center point of the arc
 * @param startPoint - The starting point of the arc
 * @param endPoint - The ending point of the arc
 * @returns Arc parameters including radius, start angle, end angle, and sweep angle
 */
export function calculateArcFromThreePoints(
  center: IPoint,
  startPoint: IPoint,
  endPoint: IPoint,
): {
    radius: number
    startAngle: number
    endAngle: number
    sweepAngle: number
  } {
  // Calculate radius from center to start point
  const dx1 = startPoint.x - center.x
  const dy1 = startPoint.y - center.y
  const radius = Math.sqrt(dx1 * dx1 + dy1 * dy1)

  // Calculate start angle
  const startAngle = Math.atan2(dy1, dx1)

  // Calculate end angle
  const dx2 = endPoint.x - center.x
  const dy2 = endPoint.y - center.y
  const endAngle = Math.atan2(dy2, dx2)

  // Calculate sweep angle (always positive, going counterclockwise)
  let sweepAngle = endAngle - startAngle
  if (sweepAngle < 0) {
    sweepAngle += 2 * Math.PI
  }

  return {
    radius,
    startAngle: radiansToDegrees(startAngle),
    endAngle: radiansToDegrees(endAngle),
    sweepAngle: radiansToDegrees(sweepAngle),
  }
}

/**
 * Generates SVG path data for an arc.
 *
 * @param center - The center point of the arc
 * @param radius - The radius of the arc
 * @param startAngle - The start angle in degrees
 * @param endAngle - The end angle in degrees
 * @param counterClockwise - Whether the arc goes counterclockwise (default: false)
 * @returns SVG path data string
 */
export function generateArcPathData(
  center: IPoint,
  radius: number,
  startAngle: number,
  endAngle: number,
  counterClockwise: boolean = false,
): string {
  // Convert angles to radians
  const startRad = degreesToRadians(startAngle)
  const endRad = degreesToRadians(endAngle)

  // Calculate start and end points
  const startX = center.x + radius * Math.cos(startRad)
  const startY = center.y + radius * Math.sin(startRad)
  const endX = center.x + radius * Math.cos(endRad)
  const endY = center.y + radius * Math.sin(endRad)

  // Calculate sweep angle
  let sweepAngle = endAngle - startAngle
  if (counterClockwise) {
    if (sweepAngle > 0) {
      sweepAngle -= 360
    }
  }
  else {
    if (sweepAngle < 0) {
      sweepAngle += 360
    }
  }

  // Determine if this is a large arc (> 180 degrees)
  const largeArcFlag = Math.abs(sweepAngle) > 180 ? 1 : 0

  // Sweep direction flag (0 = counterclockwise, 1 = clockwise)
  const sweepFlag = counterClockwise ? 0 : 1

  // Generate SVG arc path
  return `M ${startX} ${startY} A ${radius} ${radius} 0 ${largeArcFlag} ${sweepFlag} ${endX} ${endY}`
}

/**
 * Calculates the approximate distance from a point to a circular arc.
 * @remarks Placeholder implementation. Currently returns the absolute difference
 *          between the point's distance to the arc's center and the arc's radius.
 *          A more accurate implementation would consider the arc's sweep.
 * @param point - The {@link IPoint} to calculate distance from.
 * @param arc - The {@link ArcProperties} defining the arc (center is `arc.position`, angles in degrees).
 * @returns The approximate distance from the point to the arc.
 */
export function calculateArcPointDistance(point: IPoint, arc: ArcProperties): number {
  console.warn('calculateArcPointDistance is not fully implemented.')
  // ArcProperties (Path.Arc) extends ShapeElement, so it should have a position.
  // Use double assertion if direct access or single assertion fails.
  const arcPos = (arc as unknown as ShapeElement).position
  const dx = point.x - arcPos.x
  const dy = point.y - arcPos.y
  const distToCenter = Math.sqrt(dx * dx + dy * dy)
  const radius = arc.properties.radius || 0
  return Math.abs(distToCenter - radius)
}

/**
 * Calculates the distance from a line segment to a circular arc.
 * @remarks Placeholder implementation. Returns `Number.MAX_VALUE`.
 *          A full implementation would be complex, involving checks for intersections
 *          and distances from line segment endpoints to arc, and arc endpoints to line segment.
 * @param _lineStart - The start {@link IPoint} of the line segment.
 * @param _lineEnd - The end {@link IPoint} of the line segment.
 * @param _arc - The {@link ArcProperties} defining the arc.
 * @returns Currently `Number.MAX_VALUE` as it's not implemented.
 */
export function calculateArcLineDistance(_lineStart: IPoint, _lineEnd: IPoint, _arc: ArcProperties): number {
  console.warn('calculateArcLineDistance is not fully implemented.')
  return Number.MAX_VALUE
}

/**
 * Calculates the distance between two circular arcs.
 * @remarks Placeholder implementation. Returns `Number.MAX_VALUE`.
 *          A full implementation would be very complex.
 * @param _arc1 - The {@link ArcProperties} of the first arc.
 * @param _arc2 - The {@link ArcProperties} of the second arc.
 * @returns Currently `Number.MAX_VALUE` as it's not implemented.
 */
export function calculateArcArcDistance(_arc1: ArcProperties, _arc2: ArcProperties): number {
  console.warn('calculateArcArcDistance is not fully implemented.')
  return Number.MAX_VALUE
}

// TODO: Implement actual arc calculations, including:
// - Point on arc at a given angle
// - Intersection points with other shapes (if not handled by a general intersection utility)

/**
 * Calculates the length of a circular arc segment.
 *
 * @param radius - The radius of the circle from which the arc is derived. Must be non-negative.
 * @param angleRadians - The sweep angle of the arc in radians. Must be non-negative.
 * @returns The length of the arc. Returns `NaN` if `radius` or `angleRadians` is negative or not a number.
 */
export function calculateArcLength(radius: number, angleRadians: number): number {
  if (typeof radius !== 'number' || isNaN(radius) || radius < 0
    || typeof angleRadians !== 'number' || isNaN(angleRadians) || angleRadians < 0) {
    console.warn('calculateArcLength: Radius and angleRadians must be non-negative numbers.')
    return Number.NaN
  }
  return radius * angleRadians
}
/**
 * Calculates the axis-aligned bounding box of a circular arc.
 *
 * @remarks
 * The calculation considers the arc's start and end points, its center (if closed),
 * and any cardinal points (0, 90, 180, 270 degrees) that fall within the arc's sweep.
 * Angles in `arc.startAngle` and `arc.endAngle` are expected in degrees.
 *
 * @param arc - The {@link ArcProperties} object defining the arc.
 *              `arc.position` is the center, `arc.radius`, `arc.startAngle`, `arc.endAngle`, `arc.closed`.
 * @returns A {@link BoundingBoxClass} instance. Returns a 0x0 BBox at the center if the radius is invalid.
 */
export function calculateArcBoundingBox(
  arc: ArcProperties,
): BoundingBoxClass {
  const arcShapeElement = arc as unknown as ShapeElement // Use double assertion
  const center = arcShapeElement.position
  const radius = arc.properties.radius || 0
  const startAngleRad = degreesToRadians(arc.properties.startAngle)
  const endAngleRad = degreesToRadians(arc.properties.endAngle)
  const closed = arc.properties.closed

  if (radius < 0) {
    console.warn('calculateArcBoundingBox: Negative radius provided. Returning BBox at center.')
    return new BoundingBoxClass(center.x, center.y, 0, 0)
  }
  if (radius === 0) {
    return new BoundingBoxClass(center.x, center.y, 0, 0)
  }

  const pointsToCheck: IPoint[] = []

  // Add start and end points of the arc
  pointsToCheck.push({
    x: center.x + radius * Math.cos(startAngleRad),
    y: center.y + radius * Math.sin(startAngleRad),
    z: center.z,
  })
  pointsToCheck.push({
    x: center.x + radius * Math.cos(endAngleRad),
    y: center.y + radius * Math.sin(endAngleRad),
    z: center.z,
  })

  // If it's a closed arc (pie slice), include the center point
  if (closed) {
    pointsToCheck.push(center)
  }

  // Normalize angles for sweep check: ensure endAngle is greater than startAngle for a single sweep
  let normalizedStart = startAngleRad % (2 * Math.PI)
  let normalizedEnd = endAngleRad % (2 * Math.PI)

  if (normalizedStart < 0)
    normalizedStart += 2 * Math.PI
  if (normalizedEnd < 0)
    normalizedEnd += 2 * Math.PI

  // If the sweep crosses the 0-radian line (e.g., from 330deg to 30deg)
  // or if it's a full circle or more.
  if (normalizedEnd < normalizedStart || (normalizedEnd - normalizedStart) >= (2 * Math.PI - 1e-9)) {
    // For full circles or sweeps crossing 0, all cardinal points are relevant if covered by radius
    pointsToCheck.push({ x: center.x + radius, y: center.y, z: center.z }) // 0 rad
    pointsToCheck.push({ x: center.x, y: center.y + radius, z: center.z }) // PI/2 rad
    pointsToCheck.push({ x: center.x - radius, y: center.y, z: center.z }) // PI rad
    pointsToCheck.push({ x: center.x, y: center.y - radius, z: center.z }) // 3PI/2 rad
  }
  else {
    // Check cardinal points (0, PI/2, PI, 3PI/2) only if they fall within the specific arc sweep
    const cardinalAngles = [0, Math.PI / 2, Math.PI, (3 * Math.PI) / 2]
    for (const angle of cardinalAngles) {
      // Check if this cardinal angle lies within the arc's sweep
      if (angle >= normalizedStart - 1e-9 && angle <= normalizedEnd + 1e-9) {
        pointsToCheck.push({ x: center.x + radius * Math.cos(angle), y: center.y + radius * Math.sin(angle), z: center.z })
      }
    }
  }
  return BoundingBoxClass.fromPointsArray(pointsToCheck)
}
