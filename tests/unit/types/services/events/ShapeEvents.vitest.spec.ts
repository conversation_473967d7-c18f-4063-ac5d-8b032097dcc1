import type {
  <PERSON>hape<PERSON>reate<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>DeleteEvent,
  <PERSON>hapeEditEvent,
  ShapeSelectionEvent,
  ShapeTransformEvent,
} from '@/types/services/events/shapeEvents'
import { describe, expect, it } from 'vitest'
import { Point } from '@/types/core/element/geometry/point'
import { ElementType } from '@/types/core/shape-type'
import { AppEventType } from '@/types/services/events/eventTypes'

describe('shapeEvents', () => {
  describe('shapeCreateEvent', () => {
    it('should have correct structure', () => {
      const event: ShapeCreateEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 200,
            height: 150,
          },
        },
      }

      expect(event.type).toBe(AppEventType.SHAPE_CREATE_REQUEST)
      expect(event.payload.ElementType).toBe(ElementType.RECTANGLE)
      expect(event.payload.position).toEqual({ x: 100, y: 100 })
      expect(event.payload.properties).toEqual({ width: 200, height: 150 })
    })

    it('should allow optional properties', () => {
      const event: ShapeCreateEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.CIRCLE,
          position: { x: 100, y: 100 },
        },
      }

      expect(event.type).toBe(AppEventType.SHAPE_CREATE_REQUEST)
      expect(event.payload.ElementType).toBe(ElementType.CIRCLE)
      expect(event.payload.position).toEqual({ x: 100, y: 100 })
      expect(event.payload.properties).toBeUndefined()
    })

    it('should work with Point instance', () => {
      const position = new Point(100, 100)
      const event: ShapeCreateEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position,
        },
      }

      expect(event.payload.position).toBe(position)
    })
  })

  describe('shapeEditEvent', () => {
    it('should have correct structure', () => {
      const event: ShapeEditEvent = {
        type: AppEventType.SHAPE_EDIT_REQUEST,
        payload: {
          shapeId: 'shape-123',
          changes: {
            position: { x: 200, y: 200 },
            properties: {
              width: 300,
              height: 250,
            },
          },
        },
      }

      expect(event.type).toBe(AppEventType.SHAPE_EDIT_REQUEST)
      expect(event.payload.shapeId).toBe('shape-123')
      expect(event.payload.changes.position).toEqual({ x: 200, y: 200 })
      expect(event.payload.changes.properties).toEqual({ width: 300, height: 250 })
    })

    it('should allow partial changes', () => {
      const event: ShapeEditEvent = {
        type: AppEventType.SHAPE_EDIT_REQUEST,
        payload: {
          shapeId: 'shape-123',
          changes: {
            position: { x: 200, y: 200 },
          },
        },
      }

      expect(event.type).toBe(AppEventType.SHAPE_EDIT_REQUEST)
      expect(event.payload.shapeId).toBe('shape-123')
      expect(event.payload.changes.position).toEqual({ x: 200, y: 200 })
      expect(event.payload.changes.properties).toBeUndefined()
    })

    it('should work with Point instance for position changes', () => {
      const position = new Point(200, 200)
      const event: ShapeEditEvent = {
        type: AppEventType.SHAPE_EDIT_REQUEST,
        payload: {
          shapeId: 'shape-123',
          changes: {
            position,
          },
        },
      }

      expect(event.payload.changes.position).toBe(position)
    })
  })

  describe('shapeDeleteEvent', () => {
    it('should handle single shape ID', () => {
      const event: ShapeDeleteEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: {
          shapeIds: 'shape-123',
        },
      }

      expect(event.type).toBe(AppEventType.SHAPE_DELETE_REQUEST)
      expect(event.payload.shapeIds).toBe('shape-123')
    })

    it('should handle multiple shape IDs', () => {
      const event: ShapeDeleteEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: {
          shapeIds: ['shape-123', 'shape-456', 'shape-789'],
        },
      }

      expect(event.type).toBe(AppEventType.SHAPE_DELETE_REQUEST)
      expect(event.payload.shapeIds).toEqual(['shape-123', 'shape-456', 'shape-789'])
    })
  })

  describe('shapeSelectionEvent', () => {
    it('should handle shape selection', () => {
      const event: ShapeSelectionEvent = {
        type: AppEventType.SELECTION_CHANGED,
        payload: {
          shapeId: 'shape-123',
        },
      }

      expect(event.type).toBe(AppEventType.SELECTION_CHANGED)
      expect(event.payload.shapeId).toBe('shape-123')
    })

    it('should handle shape deselection', () => {
      const event: ShapeSelectionEvent = {
        type: AppEventType.SELECTION_CHANGED,
        payload: {
          shapeId: null,
        },
      }

      expect(event.type).toBe(AppEventType.SELECTION_CHANGED)
      expect(event.payload.shapeId).toBeNull()
    })
  })

  describe('shapeTransformEvent', () => {
    it('should have correct structure', () => {
      const event: ShapeTransformEvent = {
        type: AppEventType.SHAPE_TRANSFORM_REQUEST,
        payload: {
          shapeId: 'shape-123',
          transformType: 'resize',
          transformData: {
            width: 300,
            height: 250,
          },
        },
      }

      expect(event.type).toBe(AppEventType.SHAPE_TRANSFORM_REQUEST)
      expect(event.payload.shapeId).toBe('shape-123')
      expect(event.payload.transformType).toBe('resize')
      expect(event.payload.transformData).toEqual({ width: 300, height: 250 })
    })

    it('should handle rotation transform', () => {
      const event: ShapeTransformEvent = {
        type: AppEventType.SHAPE_TRANSFORM_REQUEST,
        payload: {
          shapeId: 'shape-123',
          transformType: 'rotate',
          transformData: {
            angle: 45,
          },
        },
      }

      expect(event.type).toBe(AppEventType.SHAPE_TRANSFORM_REQUEST)
      expect(event.payload.transformType).toBe('rotate')
      expect(event.payload.transformData).toEqual({ angle: 45 })
    })

    it('should handle multiple shapes', () => {
      const event: ShapeTransformEvent = {
        type: AppEventType.SHAPE_TRANSFORM_REQUEST,
        payload: {
          shapeId: ['shape-123', 'shape-456'],
          transformType: 'move',
          transformData: {
            dx: 10,
            dy: 20,
          },
        },
      }

      expect(event.type).toBe(AppEventType.SHAPE_TRANSFORM_REQUEST)
      expect(event.payload.shapeId).toEqual(['shape-123', 'shape-456'])
      expect(event.payload.transformType).toBe('move')
      expect(event.payload.transformData).toEqual({ dx: 10, dy: 20 })
    })
  })
})
