import type { EventBus } from '@/types/services/events'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { CoreError, ErrorType } from '@/core/errors/CoreError'
import { publishCoreError, publishError, publishValidationError } from '@/services/event-bus/helpers/error-helpers'
import { AppEventType } from '@/types/services/events'

describe('error-helpers', () => {
  let eventBus: EventBus
  let consoleWarnSpy: jest.SpyInstance
  let consoleErrorSpy: jest.SpyInstance

  beforeEach(() => {
    // Mock event bus
    eventBus = {
      publish: vi.fn(),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
      unsubscribeAll: vi.fn(),
      clear: vi.fn(),
      reset: vi.fn(),
      getSubscriptions: vi.fn(),
      configure: vi.fn(),
    }

    // Spy on console methods
    consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
    consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  describe('publishError', () => {
    it('should publish an error event with the correct payload', () => {
      // Arrange
      const code = ErrorType.VALIDATION_FAILED
      const message = 'Validation failed'
      const details = { field: 'name', value: '' }

      // Act
      publishError(eventBus, code, message, details)

      // Assert
      expect(eventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: {
          message,
          code,
          details,
          critical: false,
          stack: undefined,
        },
      })
    })

    it('should log to console by default', () => {
      // Arrange
      const code = ErrorType.VALIDATION_FAILED
      const message = 'Validation failed'

      // Act
      publishError(eventBus, code, message)

      // Assert
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringContaining(message),
        undefined,
      )
    })

    it('should use console.error for critical errors', () => {
      // Arrange
      const code = ErrorType.UNKNOWN
      const message = 'Critical error'
      const options = { critical: true }

      // Act
      publishError(eventBus, code, message, null, options)

      // Assert
      expect(consoleErrorSpy).toHaveBeenCalled()
      expect(consoleWarnSpy).not.toHaveBeenCalled()
    })

    it('should not log to console when logToConsole is false', () => {
      // Arrange
      const code = ErrorType.VALIDATION_FAILED
      const message = 'Validation failed'
      const options = { logToConsole: false }

      // Act
      publishError(eventBus, code, message, null, options)

      // Assert
      expect(consoleWarnSpy).not.toHaveBeenCalled()
      expect(consoleErrorSpy).not.toHaveBeenCalled()
    })

    it('should include stack trace when details is an Error', () => {
      // Arrange
      const code = ErrorType.UNKNOWN
      const message = 'An error occurred'
      const error = new Error('Original error')

      // Act
      publishError(eventBus, code, message, error)

      // Assert
      expect(eventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        payload: expect.objectContaining({
          stack: error.stack,
        }),
      }))
    })
  })

  describe('publishCoreError', () => {
    it('should publish a CoreError with correct payload', () => {
      // Arrange
      const error = new CoreError(ErrorType.NOT_FOUND, 'Resource not found')
      error.metadata = { resourceId: '123' }
      const additionalContext = { userId: 'user1' }

      // Act
      publishCoreError(eventBus, error, additionalContext)

      // Assert
      expect(eventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          message: error.message,
          code: error.type,
          details: {
            resourceId: '123',
            userId: 'user1',
          },
        }),
      })
    })

    it('should handle CoreError without metadata', () => {
      // Arrange
      const error = new CoreError(ErrorType.NOT_FOUND, 'Resource not found')

      // Act
      publishCoreError(eventBus, error)

      // Assert
      expect(eventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          message: error.message,
          code: error.type,
          details: {},
        }),
      })
    })

    it('should pass options to publishError', () => {
      // Arrange
      const error = new CoreError(ErrorType.UNKNOWN, 'Critical error')
      const options = { critical: true, logToConsole: true }

      // Act
      publishCoreError(eventBus, error, null, options)

      // Assert
      expect(consoleErrorSpy).toHaveBeenCalled()
      expect(eventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        payload: expect.objectContaining({
          critical: true,
        }),
      }))
    })
  })

  describe('publishValidationError', () => {
    it('should publish a validation error with correct payload', () => {
      // Arrange
      const message = 'Validation failed'
      const validationErrors = ['Field is required', 'Value is too short']
      const context = { formData: { name: '' } }

      // Act
      publishValidationError(eventBus, message, validationErrors, context)

      // Assert
      expect(eventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          message,
          code: ErrorType.VALIDATION_FAILED,
          details: {
            validationErrors,
            context,
          },
        }),
      })
    })
  })
})
