/**
 * Index for Core Computation Type Definitions
 *
 * @remarks
 * This module serves as the central barrel file for all type definitions related to
 * computation operations within the `types/core/compute` directory. It re-exports
 * key interfaces, enums, and types from various specialized sub-modules, providing
 * a unified access point for the rest of the application.
 *
 * Key re-exported or defined entities include:
 * - Core computation interfaces like {@link TransformOptions}, {@link ComputeOptions},
 *   {@link ComputeResult}, and {@link ComputeShape} from {@link ./computeInterfaces}.
 * - Enumerations for computation operations: {@link ComputeOperationType} (lowercase strings)
 *   and {@link ComputeOperation} (uppercase string constants).
 * - A general {@link ComputeValue} type for flexible data handling in computations.
 * - Various strategy interfaces for specific calculations (e.g., {@link AreaCalculatorStrategy},
 *   {@link BoundingBoxCalculatorStrategy}, {@link CostCalculatorStrategy}) re-exported
 *   from their respective type definition files (e.g., `areaComputeTypes.ts`).
 *
 * This centralized export structure simplifies imports and promotes consistency when
 * dealing with computation-related types.
 *
 * @module types/core/compute/index
 */

/**
 * Re-exports various computation strategy interfaces and related types from their respective modules.
 * This provides a single point of import for all core computation strategy interfaces.
 */
export type { BoundingBox } from '../element/geometry/bounding-box'

/**
 * Defines the available computation operations as lowercase string literals.
 *
 * @remarks
 * These string literal values are typically used for:
 * - Runtime type checking or discrimination in `switch` statements.
 * - Dynamic selection of computation strategies.
 * - Keys in configuration objects or maps related to computations.
 *
 * @enum {string}
 * @see {@link ComputeOperation} for an equivalent enum with uppercase constant names.
 */
export enum ComputeOperationType {
  /** Represents an area calculation operation. */
  AREA = 'area',
  /** Represents a perimeter or length calculation operation. */
  PERIMETER = 'perimeter',
  /** Represents a bounding box calculation operation. */
  BOUNDING_BOX = 'boundingBox',
  /** Represents a distance calculation operation (e.g., between elements or points). */
  DISTANCE = 'distance',
  /** Represents a material requirements estimation operation. */
  MATERIAL = 'material',
  /** Represents a cost calculation operation (e.g., construction or material costs). */
  COST = 'cost',
  /** Represents a space analysis or planning operation. */
  SPACE = 'space',
}

/**
 * Defines the available computation operations as uppercase string constants.
 *
 * @remarks
 * This enum provides a set of named constants for various computation operations.
 * It includes general geometric calculations as well as more specialized operations
 * like those for space planning. Using uppercase constants is a common convention
 * for representing fixed, well-defined operation identifiers.
 *
 * @enum {string}
 * @see {@link ComputeOperationType} for an equivalent enum with lowercase string values.
 */
export enum ComputeOperation {
  /** An operation to calculate the area of an element. */
  AREA = 'AREA',
  /** An operation to calculate the perimeter or length of an element. */
  PERIMETER = 'PERIMETER',
  /** An operation to calculate the bounding box of an element. */
  BOUNDING_BOX = 'BOUNDING_BOX',
  /** An operation to calculate the distance between elements or to a point. */
  DISTANCE = 'DISTANCE',
  /** An operation to estimate material requirements for an element or design. */
  MATERIAL = 'MATERIAL',
  /** An operation to calculate the construction or material costs. */
  COST = 'COST',
  /** A general operation related to space analysis or planning. */
  SPACE = 'SPACE',

  // Space planning specific operations
  /** An operation to calculate the efficiency of space utilization. */
  SPACE_UTILIZATION = 'SPACE_UTILIZATION',
  /** An operation to check if pathways are clear and meet accessibility standards. */
  PATHWAY_CHECK = 'PATHWAY_CHECK',
  /** An operation to calculate an overall quality score for a layout design. */
  LAYOUT_SCORE = 'LAYOUT_SCORE',
}

/**
 * Represents a union of possible primitive and simple object types that can be
 * used as values within computation options or results.
 *
 * @remarks
 * This type provides flexibility for generic computation structures, allowing for
 * various kinds of data to be passed or returned.
 */
export type ComputeValue =
  | number
  | string
  | boolean
  | null
  | { x: number, y: number } // Represents a Point-like structure
  | number[]
  | string[]
  | Record<string, number | string | boolean> // Simple key-value store

// Note: The `ComputeOptions`, `ComputeResult`, and `TransformOptions` interfaces
// were previously defined here but are now imported from './computeInterfaces.ts'
// to avoid duplication and maintain a single source of truth.
// If specific variations are needed in this module, they should be distinctly named
// or extend the base interfaces from './computeInterfaces.ts'.

export type { AreaCalculatorStrategy } from './areaComputeTypes'
export type { BoundingBoxCalculatorStrategy } from './boundingBoxComputeTypes'
/**
 * Re-exports all interfaces and types from {@link ./computeInterfaces}.
 * This includes {@link TransformOptions}, {@link ComputeOptions}, {@link ComputeResult}, and {@link ComputeShape}.
 */
export * from './computeInterfaces'
export type { CostCalculationOptions, CostCalculatorStrategy } from './costComputeTypes'
export type { DistanceCalculatorStrategy } from './distanceComputeTypes'
export type { MaterialCalculationOptions, MaterialCalculationResult, MaterialCalculatorStrategy } from './materialComputeTypes'
export type { PerimeterCalculatorStrategy } from './perimeterComputeTypes'
export type {
  ErgonomicsEvaluationResult,
  PathwayCheckResult,
  SpacePlanningStandard,
  SpacePlanningStrategy,
  SpaceType,
} from './spaceComputeTypes'
