import { describe, expect, it } from 'vitest'
import { AppEventType } from '@/types/services/events/eventTypes'

describe('appEventType', () => {
  it('should have all expected event types', () => {
    // Shape events
    expect(AppEventType.SHAPE_CREATE_REQUEST).toBeDefined()
    expect(AppEventType.SHAPE_CREATE_COMPLETE).toBeDefined()
    expect(AppEventType.SHAPE_DELETE_REQUEST).toBeDefined()
    expect(AppEventType.SHAPE_DELETE_COMPLETE).toBeDefined()
    expect(AppEventType.SHAPE_EDIT_REQUEST).toBeDefined()
    expect(AppEventType.SHAPE_EDIT_COMPLETE).toBeDefined()

    // Error events
    expect(AppEventType.ERROR_OCCURRED).toBeDefined()

    // Canvas events
    expect(AppEventType.CANVAS_CLEARED).toBeDefined()
    expect(AppEventType.CANVAS_RESIZED).toBeDefined()
    expect(AppEventType.CANVAS_MOUSE_MOVE).toBeDefined()

    // Tool events
    expect(AppEventType.TOOL_CHANGED).toBeDefined()

    // History events
    expect(AppEventType.HISTORY_CHECKPOINT).toBeDefined()
    expect(AppEventType.HISTORY_UNDO).toBeDefined()
    expect(AppEventType.HISTORY_REDO).toBeDefined()

    // Layer events
    expect(AppEventType.LAYER_VISIBILITY_CHANGE).toBeDefined()
    expect(AppEventType.LAYER_LOCK_CHANGE).toBeDefined()
    expect(AppEventType.LAYER_ORDER_CHANGE).toBeDefined()

    // File events
    expect(AppEventType.FILE_IMPORTED).toBeDefined()
    expect(AppEventType.FILE_EXPORTED).toBeDefined()

    // View events
    expect(AppEventType.VIEW_ZOOMED).toBeDefined()
    expect(AppEventType.VIEW_PANNED).toBeDefined()

    // Template events
    expect(AppEventType.TEMPLATE_APPLY).toBeDefined()

    // Notification events
    expect(AppEventType.NOTIFICATION_ADD).toBeDefined()
    expect(AppEventType.TOAST_SHOW).toBeDefined()

    // Sidebar events
    expect(AppEventType.SIDEBAR_LEFT_TOGGLE).toBeDefined()
    expect(AppEventType.SIDEBAR_RIGHT_TOGGLE).toBeDefined()

    // Compute events
    expect(AppEventType.COMPUTE_REQUEST).toBeDefined()
    expect(AppEventType.COMPUTE_COMPLETE).toBeDefined()
    expect(AppEventType.COMPUTE_PROGRESS).toBeDefined()
    expect(AppEventType.COMPUTE_ERROR).toBeDefined()

    // Data events
    expect(AppEventType.DATA_LOAD_REQUEST).toBeDefined()
    expect(AppEventType.DATA_LOAD_COMPLETE).toBeDefined()
    expect(AppEventType.STORAGE_SAVE_REQUEST).toBeDefined()
    expect(AppEventType.STORAGE_SAVE_COMPLETE).toBeDefined()

    // Grid events
    expect(AppEventType.GRID_ENABLED).toBeDefined()
    expect(AppEventType.GRID_SIZE_CHANGED).toBeDefined()
    expect(AppEventType.GRID_SNAP_CHANGED).toBeDefined()

    // Input events
    expect(AppEventType.KEY_PRESSED).toBeDefined()
    expect(AppEventType.KEY_RELEASED).toBeDefined()
    expect(AppEventType.CANVAS_MOUSE_DOWN).toBeDefined()
    expect(AppEventType.CANVAS_MOUSE_UP).toBeDefined()

    // Render events
    expect(AppEventType.RENDER_START).toBeDefined()
    expect(AppEventType.RENDER_COMPLETE).toBeDefined()
  })

  it('should have correct string values for event types', () => {
    // Test a sample of event types to ensure they have the expected string values
    expect(AppEventType.SHAPE_CREATE_REQUEST).toBe('shape.create.request')
    expect(AppEventType.SHAPE_CREATE_COMPLETE).toBe('shape.create.complete')
    expect(AppEventType.ERROR_OCCURRED).toBe('error.occurred')
    expect(AppEventType.CANVAS_CLEARED).toBe('canvas.cleared')
    expect(AppEventType.TOOL_CHANGED).toBe('tool.changed')
    expect(AppEventType.HISTORY_UNDO).toBe('history.undo')
    expect(AppEventType.LAYER_VISIBILITY_CHANGE).toBe('layer.visibility.change')
    expect(AppEventType.FILE_IMPORTED).toBe('file.imported')
    expect(AppEventType.VIEW_ZOOMED).toBe('view.zoomed')
    expect(AppEventType.TEMPLATE_APPLY).toBe('template.apply')
    expect(AppEventType.NOTIFICATION_ADD).toBe('notification.add')
    expect(AppEventType.SIDEBAR_LEFT_TOGGLE).toBe('sidebar.left.toggle')
    expect(AppEventType.COMPUTE_REQUEST).toBe('compute.request')
    expect(AppEventType.DATA_LOAD_REQUEST).toBe('data.load.request')
    expect(AppEventType.GRID_ENABLED).toBe('grid.enabled')
    expect(AppEventType.KEY_PRESSED).toBe('keyboard.key.pressed')
    expect(AppEventType.RENDER_START).toBe('render.start')
  })

  it('should have unique values for all event types', () => {
    // Get all values from AppEventType
    const eventValues = Object.values(AppEventType)

    // Create a Set from the values (which will remove duplicates)
    const uniqueValues = new Set(eventValues)

    // If there are duplicates, the Set size will be smaller than the array length
    expect(uniqueValues.size).toBe(eventValues.length)
  })
})
