/**
 * Material Calculation Strategy for Paint
 *
 * @remarks
 * This strategy implements the {@link IMaterialCalculatorStrategy} to calculate
 * the required amount of paint for a given element (typically a surface area).
 *
 * It considers several factors from {@link MaterialCalculationOptions}:
 * - `paintType`: Affects coverage factor.
 * - `surfaceType`: Affects absorption factor.
 * - `coats`: Number of paint coats to apply.
 * - `coveragePerLiter`: Base coverage rate of the paint.
 * - `wastageRate`: Percentage for paint wastage.
 * - `thinnerPerLiter`: Ratio of thinner needed per liter of paint.
 * - `canSize`: Standard size of paint cans for calculating can count.
 *
 * The strategy first determines the area of the element (currently assuming the
 * element has a `getArea()` or `compute.area()` method - marked as TODO for refactoring).
 * It then calculates the effective coverage based on paint type and surface absorption,
 * computes the raw paint needed, applies wastage, and estimates primer and thinner quantities.
 * Finally, it calculates the number of paint and primer cans required.
 *
 * The `calculateMaterialAmount` method returns a summarized {@link MaterialCalculationResult},
 * while a private `calculateDetailedMaterial` method provides a more granular breakdown.
 *
 * @module core/compute/strategies/material/PaintMaterialStrategy
 * @see {@link IMaterialCalculatorStrategy}
 * @see {@link MaterialCalculationOptions}
 * @see {@link MaterialCalculationResult}
 */
import type {
  MaterialCalculatorStrategy as IMaterialCalculatorStrategy, // Renamed for consistency
  MaterialCalculationOptions,
  MaterialCalculationResult,
} from '@/types/core/compute' // Corrected import path
import type { Element } from '@/types/core/elementDefinitions'
import { CoreError, ErrorType } from '@/services/system/error-service'
import { ElementType as CoreElementType } from '@/types/core/elementDefinitions'

export class PaintMaterialStrategy implements IMaterialCalculatorStrategy { // Renamed IMaterialCalculatorStrategy for consistency
  /**
   * Calculates the amount of paint material required.
   *
   * @param element - The element (e.g., wall, surface) for which paint is being calculated.
   *                  Expected to have a method to compute its area.
   * @param materialType - The type of material, expected to be 'paint'.
   * @param options - Optional {@link MaterialCalculationOptions} to customize the calculation.
   * @returns A {@link MaterialCalculationResult} summarizing the paint quantity.
   * @throws {@link CoreError} if `materialType` is not 'paint', or if area calculation fails or is invalid.
   */
  public calculateMaterialAmount(element: Element, materialType: string, options?: MaterialCalculationOptions): MaterialCalculationResult {
    const detailedResult = this.calculateDetailedMaterial(element, materialType, options)
    return {
      amount: detailedResult.paint, // This is paintNeededWithWastage (liters)
      unit: 'L', // Standard symbol for Liters
      unitCount: detailedResult.paintCans,
      unitType: 'can',
      amountWithWastage: detailedResult.paint, // paint from detailedResult already includes wastage
      coats: detailedResult.coats,
    }
  }

  /**
   * Calculates a detailed breakdown of paint and related materials.
   * @private
   * @param element - The element to be painted.
   * @param materialType - Expected to be 'paint'.
   * @param options - Optional {@link MaterialCalculationOptions}.
   * @returns A record containing quantities for paint, primer, thinner, cans, etc.
   * @throws {@link CoreError} if `materialType` is not 'paint', or if area calculation fails or is invalid.
   */
  private calculateDetailedMaterial(element: Element, materialType: string, options?: MaterialCalculationOptions): Record<string, number> {
    if (materialType.toLowerCase() !== 'paint') {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `PaintMaterialStrategy can only calculate for 'paint' material type, got ${materialType}`,
      )
    }

    // TODO: Refactor to avoid direct access to a potential 'getArea' or 'compute' property.
    const computableElement = element as unknown as { getArea?: () => number, compute?: { area?: () => number } }
    let area: number
    if (computableElement.getArea !== null && computableElement.getArea !== undefined && typeof computableElement.getArea === 'function') {
      area = computableElement.getArea()
    }
    else if (computableElement.compute?.area && typeof computableElement.compute.area === 'function') {
      area = computableElement.compute.area()
    }
    else {
      throw new CoreError(ErrorType.ComputationError, `Element ${element.id} does not have a method to calculate area.`)
    }

    if (typeof area !== 'number' || area < 0 || !Number.isFinite(area)) { // Area can be 0
      throw new CoreError(ErrorType.ComputationError, `Invalid area for element ${element.id}: ${area}`)
    }
    if (area === 0) {
      console.warn(`[PaintMaterialStrategy] Area for element ${element.id} is 0. No paint needed.`)
      return { paint: 0, primer: 0, thinner: 0, paintCans: 0, primerCans: 0, coats: (options?.coats !== null && options?.coats !== undefined) ? options.coats : 2, effectiveCoverage: 0 }
    }

    const materialOptions = options ?? {}
    const paintType = (materialOptions.paintType !== null && materialOptions.paintType !== undefined && materialOptions.paintType !== '') ? materialOptions.paintType : 'latex'
    const surfaceType = (materialOptions.surfaceType !== null && materialOptions.surfaceType !== undefined && materialOptions.surfaceType !== '') ? materialOptions.surfaceType : 'drywall'
    const coats = (materialOptions.coats !== null && materialOptions.coats !== undefined && materialOptions.coats !== 0) ? materialOptions.coats : 2
    const coveragePerLiter = (materialOptions.coveragePerLiter !== null && materialOptions.coveragePerLiter !== undefined && materialOptions.coveragePerLiter !== 0) ? materialOptions.coveragePerLiter : 10

    let absorptionFactor = 1.0
    switch (surfaceType) {
      case 'drywall':
        absorptionFactor = 1.0
        break
      case 'concrete':
        absorptionFactor = 1.3
        break
      case 'wood':
        absorptionFactor = 1.2
        break
      case 'metal':
        absorptionFactor = 0.9
        break
      case 'previously-painted':
        absorptionFactor = 0.8
        break
      default:
        absorptionFactor = 1.0
    }

    let coverageFactor = 1.0
    switch (paintType) {
      case 'latex':
        coverageFactor = 1.0
        break
      case 'oil-based':
        coverageFactor = 0.9
        break
      case 'acrylic':
        coverageFactor = 1.1
        break
      case 'enamel':
        coverageFactor = 0.95
        break
      case 'eco-friendly':
        coverageFactor = 0.85
        break
      default:
        coverageFactor = 1.0
    }

    const effectiveCoverage = coveragePerLiter * coverageFactor / absorptionFactor
    if (effectiveCoverage <= 0) {
      throw new CoreError(ErrorType.InvalidParameter, `Effective coverage per liter must be positive. Calculated: ${effectiveCoverage}`)
    }
    const paintNeededRaw = (area * coats) / effectiveCoverage

    // Wastage should be applied here if not implicitly handled by coverage factors
    const wastageRate = materialOptions.wastageRate ?? 10 // Default 10%
    const paintNeededWithWastage = paintNeededRaw * (1 + wastageRate / 100)

    const primerNeededRaw = area / (coveragePerLiter * 0.9) // Assuming primer coverage is 90% of paint
    const primerNeededWithWastage = primerNeededRaw * (1 + wastageRate / 100)

    const thinnerPerLiter = (materialOptions.thinnerPerLiter !== null && materialOptions.thinnerPerLiter !== undefined && materialOptions.thinnerPerLiter !== 0) ? materialOptions.thinnerPerLiter : 0.1
    const thinnerNeeded = paintNeededWithWastage * thinnerPerLiter

    const standardCanSize = (materialOptions.canSize !== null && materialOptions.canSize !== undefined && materialOptions.canSize !== 0) ? materialOptions.canSize : 4
    const cansNeeded = Math.ceil(paintNeededWithWastage / standardCanSize)
    const primerCansNeeded = Math.ceil(primerNeededWithWastage / standardCanSize)

    return {
      paint: paintNeededWithWastage,
      primer: primerNeededWithWastage,
      thinner: thinnerNeeded,
      paintCans: cansNeeded,
      primerCans: primerCansNeeded,
      coats,
      effectiveCoverage,
    }
  }

  /**
   * Returns the primary element type this strategy is typically associated with.
   * @remarks Paint is often applied to rectangular surfaces like walls.
   * @returns {@link CoreElementType.RECTANGLE} - The rectangle element type typically used for painted surfaces
   */
  public getElementType(): string {
    // This strategy is typically applied to surfaces that can be painted, often rectangular.
    return CoreElementType.RECTANGLE
  }

  /**
   * Returns the specific material type this strategy calculates.
   * @returns The string 'paint'.
   */
  public getMaterialType(): string {
    return 'paint'
  }
}
