import {
  renderArc,
  renderCubic,
  renderLine,
  renderPolyline,
  renderQuadratic,
} from './path'
/**
 * SVG Renderers Map
 *
 * This object maps shape and path type names to their corresponding SVG renderer functions.
 * Only `svgRenderersMap` is exported from this module.
 *
 * @module render/index
 */
import { renderEllipse, renderPolygon, renderRectangle } from './shape'
import { renderImage, renderText } from './utility'

export const svgRenderersMap = {
  // RECTANGLE
  RECTANGLE: renderRectangle,
  SQUARE: renderRectangle,
  // ELLIPSES
  ELLIPSE: renderEllipse,
  CIRCLE: renderEllipse,
  // POLYGONS
  POLYGON: renderPolygon,
  TRIANGLE: renderPolygon,
  QUADRILATERAL: renderPolygon,
  PENTAGON: renderPolygon,
  HEXAGON: renderPolygon,
  HEPTAGON: renderPolygon,
  OCTAGON: renderPolygon,
  NONAGON: renderPolygon,
  DECAGON: renderPolygon,
  // PATHS
  LINE: renderLine,
  POLYLINE: renderPolyline,
  ARC: renderArc,
  QUADRATIC: renderQuadratic,
  CUBIC: renderCubic,
  // UTILITIES
  TEXT: renderText,
  IMAGE: renderImage,
}
