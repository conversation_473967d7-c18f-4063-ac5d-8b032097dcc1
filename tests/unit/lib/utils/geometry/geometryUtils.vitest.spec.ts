import { describe, it, expect } from 'vitest'

// Mock geometry utilities
const geometryUtils = {
  calculateRectangleArea: (width: number, height: number): number => {
    return width * height
  },
  
  calculateRectanglePerimeter: (width: number, height: number): number => {
    return 2 * (width + height)
  },
  
  calculateCircleArea: (radius: number): number => {
    return Math.PI * radius * radius
  },
  
  calculateCircleCircumference: (radius: number): number => {
    return 2 * Math.PI * radius
  },
  
  calculateEllipseArea: (radiusX: number, radiusY: number): number => {
    return Math.PI * radiusX * radiusY
  },
  
  isPointInRectangle: (
    pointX: number, 
    pointY: number, 
    rectX: number, 
    rectY: number, 
    width: number, 
    height: number
  ): boolean => {
    return pointX >= rectX && 
           pointX <= rectX + width && 
           pointY >= rectY && 
           pointY <= rectY + height
  },
  
  isPointInCircle: (
    pointX: number, 
    pointY: number, 
    centerX: number, 
    centerY: number, 
    radius: number
  ): boolean => {
    const dx = pointX - centerX
    const dy = pointY - centerY
    return (dx * dx + dy * dy) <= (radius * radius)
  },
  
  getBoundingBox: (points: Array<{ x: number; y: number }>): {
    x: number; y: number; width: number; height: number
  } => {
    if (points.length === 0) {
      return { x: 0, y: 0, width: 0, height: 0 }
    }
    
    let minX = points[0].x
    let maxX = points[0].x
    let minY = points[0].y
    let maxY = points[0].y
    
    for (const point of points) {
      minX = Math.min(minX, point.x)
      maxX = Math.max(maxX, point.x)
      minY = Math.min(minY, point.y)
      maxY = Math.max(maxY, point.y)
    }
    
    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    }
  },
  
  rotatePoint: (
    x: number, 
    y: number, 
    centerX: number, 
    centerY: number, 
    angle: number
  ): { x: number; y: number } => {
    const cos = Math.cos(angle)
    const sin = Math.sin(angle)
    const dx = x - centerX
    const dy = y - centerY
    
    return {
      x: centerX + dx * cos - dy * sin,
      y: centerY + dx * sin + dy * cos
    }
  }
}

describe('Geometry Utils', () => {
  describe('Rectangle calculations', () => {
    it('should calculate rectangle area', () => {
      expect(geometryUtils.calculateRectangleArea(10, 5)).toBe(50)
      expect(geometryUtils.calculateRectangleArea(0, 5)).toBe(0)
      expect(geometryUtils.calculateRectangleArea(3.5, 2.5)).toBe(8.75)
    })

    it('should calculate rectangle perimeter', () => {
      expect(geometryUtils.calculateRectanglePerimeter(10, 5)).toBe(30)
      expect(geometryUtils.calculateRectanglePerimeter(0, 5)).toBe(10)
      expect(geometryUtils.calculateRectanglePerimeter(3.5, 2.5)).toBe(12)
    })

    it('should handle negative dimensions', () => {
      expect(geometryUtils.calculateRectangleArea(-10, 5)).toBe(-50)
      expect(geometryUtils.calculateRectanglePerimeter(-10, 5)).toBe(-10)
    })
  })

  describe('Circle calculations', () => {
    it('should calculate circle area', () => {
      expect(geometryUtils.calculateCircleArea(1)).toBeCloseTo(Math.PI, 5)
      expect(geometryUtils.calculateCircleArea(2)).toBeCloseTo(4 * Math.PI, 5)
      expect(geometryUtils.calculateCircleArea(0)).toBe(0)
    })

    it('should calculate circle circumference', () => {
      expect(geometryUtils.calculateCircleCircumference(1)).toBeCloseTo(2 * Math.PI, 5)
      expect(geometryUtils.calculateCircleCircumference(2)).toBeCloseTo(4 * Math.PI, 5)
      expect(geometryUtils.calculateCircleCircumference(0)).toBe(0)
    })

    it('should handle negative radius', () => {
      expect(geometryUtils.calculateCircleArea(-5)).toBeCloseTo(25 * Math.PI, 5)
      expect(geometryUtils.calculateCircleCircumference(-5)).toBeCloseTo(-10 * Math.PI, 5)
    })
  })

  describe('Ellipse calculations', () => {
    it('should calculate ellipse area', () => {
      expect(geometryUtils.calculateEllipseArea(2, 3)).toBeCloseTo(6 * Math.PI, 5)
      expect(geometryUtils.calculateEllipseArea(1, 1)).toBeCloseTo(Math.PI, 5) // Circle
      expect(geometryUtils.calculateEllipseArea(0, 5)).toBe(0)
    })

    it('should handle negative radii', () => {
      expect(geometryUtils.calculateEllipseArea(-2, 3)).toBeCloseTo(-6 * Math.PI, 5)
      expect(geometryUtils.calculateEllipseArea(2, -3)).toBeCloseTo(-6 * Math.PI, 5)
    })
  })

  describe('Point in shape detection', () => {
    describe('Point in rectangle', () => {
      it('should detect point inside rectangle', () => {
        expect(geometryUtils.isPointInRectangle(5, 5, 0, 0, 10, 10)).toBe(true)
        expect(geometryUtils.isPointInRectangle(0, 0, 0, 0, 10, 10)).toBe(true) // Corner
        expect(geometryUtils.isPointInRectangle(10, 10, 0, 0, 10, 10)).toBe(true) // Corner
      })

      it('should detect point outside rectangle', () => {
        expect(geometryUtils.isPointInRectangle(15, 5, 0, 0, 10, 10)).toBe(false)
        expect(geometryUtils.isPointInRectangle(5, 15, 0, 0, 10, 10)).toBe(false)
        expect(geometryUtils.isPointInRectangle(-1, 5, 0, 0, 10, 10)).toBe(false)
      })

      it('should handle negative coordinates', () => {
        expect(geometryUtils.isPointInRectangle(-5, -5, -10, -10, 10, 10)).toBe(true)
        expect(geometryUtils.isPointInRectangle(-15, -5, -10, -10, 10, 10)).toBe(false)
      })
    })

    describe('Point in circle', () => {
      it('should detect point inside circle', () => {
        expect(geometryUtils.isPointInCircle(0, 0, 0, 0, 5)).toBe(true) // Center
        expect(geometryUtils.isPointInCircle(3, 4, 0, 0, 5)).toBe(true) // Distance = 5
        expect(geometryUtils.isPointInCircle(2, 2, 0, 0, 5)).toBe(true)
      })

      it('should detect point outside circle', () => {
        expect(geometryUtils.isPointInCircle(6, 0, 0, 0, 5)).toBe(false)
        expect(geometryUtils.isPointInCircle(4, 4, 0, 0, 5)).toBe(false)
      })

      it('should handle edge cases', () => {
        expect(geometryUtils.isPointInCircle(5, 0, 0, 0, 5)).toBe(true) // On edge
        expect(geometryUtils.isPointInCircle(0, 5, 0, 0, 5)).toBe(true) // On edge
      })
    })
  })

  describe('Bounding box calculation', () => {
    it('should calculate bounding box for multiple points', () => {
      const points = [
        { x: 0, y: 0 },
        { x: 10, y: 5 },
        { x: -5, y: 15 },
        { x: 8, y: -3 }
      ]
      
      const bbox = geometryUtils.getBoundingBox(points)
      expect(bbox).toEqual({
        x: -5,
        y: -3,
        width: 15, // 10 - (-5)
        height: 18  // 15 - (-3)
      })
    })

    it('should handle single point', () => {
      const points = [{ x: 5, y: 10 }]
      const bbox = geometryUtils.getBoundingBox(points)
      expect(bbox).toEqual({
        x: 5,
        y: 10,
        width: 0,
        height: 0
      })
    })

    it('should handle empty array', () => {
      const bbox = geometryUtils.getBoundingBox([])
      expect(bbox).toEqual({
        x: 0,
        y: 0,
        width: 0,
        height: 0
      })
    })

    it('should handle points with same coordinates', () => {
      const points = [
        { x: 5, y: 5 },
        { x: 5, y: 5 },
        { x: 5, y: 5 }
      ]
      
      const bbox = geometryUtils.getBoundingBox(points)
      expect(bbox).toEqual({
        x: 5,
        y: 5,
        width: 0,
        height: 0
      })
    })
  })

  describe('Point rotation', () => {
    it('should rotate point around center', () => {
      // Rotate (1, 0) by 90 degrees around origin
      const result = geometryUtils.rotatePoint(1, 0, 0, 0, Math.PI / 2)
      expect(result.x).toBeCloseTo(0, 5)
      expect(result.y).toBeCloseTo(1, 5)
    })

    it('should rotate point around custom center', () => {
      // Rotate (2, 1) by 90 degrees around (1, 1)
      const result = geometryUtils.rotatePoint(2, 1, 1, 1, Math.PI / 2)
      expect(result.x).toBeCloseTo(1, 5)
      expect(result.y).toBeCloseTo(2, 5)
    })

    it('should handle 180 degree rotation', () => {
      const result = geometryUtils.rotatePoint(1, 1, 0, 0, Math.PI)
      expect(result.x).toBeCloseTo(-1, 5)
      expect(result.y).toBeCloseTo(-1, 5)
    })

    it('should handle 360 degree rotation', () => {
      const result = geometryUtils.rotatePoint(3, 4, 0, 0, 2 * Math.PI)
      expect(result.x).toBeCloseTo(3, 5)
      expect(result.y).toBeCloseTo(4, 5)
    })

    it('should handle negative angles', () => {
      const result1 = geometryUtils.rotatePoint(1, 0, 0, 0, -Math.PI / 2)
      const result2 = geometryUtils.rotatePoint(1, 0, 0, 0, 3 * Math.PI / 2)
      
      expect(result1.x).toBeCloseTo(result2.x, 5)
      expect(result1.y).toBeCloseTo(result2.y, 5)
    })
  })

  describe('Edge cases and error handling', () => {
    it('should handle zero dimensions', () => {
      expect(geometryUtils.calculateRectangleArea(0, 0)).toBe(0)
      expect(geometryUtils.calculateCircleArea(0)).toBe(0)
      expect(geometryUtils.calculateEllipseArea(0, 0)).toBe(0)
    })

    it('should handle very large numbers', () => {
      const large = 1e10
      expect(geometryUtils.calculateRectangleArea(large, large)).toBe(large * large)
      expect(geometryUtils.isPointInRectangle(large / 2, large / 2, 0, 0, large, large)).toBe(true)
    })

    it('should handle very small numbers', () => {
      const small = 1e-10
      expect(geometryUtils.calculateRectangleArea(small, small)).toBe(small * small)
      expect(geometryUtils.calculateCircleArea(small)).toBeCloseTo(Math.PI * small * small, 15)
    })

    it('should handle NaN and Infinity', () => {
      expect(geometryUtils.calculateRectangleArea(NaN, 5)).toBeNaN()
      expect(geometryUtils.calculateCircleArea(Infinity)).toBe(Infinity)
      expect(geometryUtils.isPointInRectangle(NaN, 5, 0, 0, 10, 10)).toBe(false)
    })
  })

  describe('Performance', () => {
    it('should handle many calculations efficiently', () => {
      const startTime = Date.now()
      
      for (let i = 0; i < 10000; i++) {
        geometryUtils.calculateRectangleArea(i, i + 1)
        geometryUtils.calculateCircleArea(i)
        geometryUtils.isPointInRectangle(i, i, 0, 0, 100, 100)
        geometryUtils.rotatePoint(i, i, 0, 0, i / 1000)
      }
      
      const endTime = Date.now()
      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly
    })
  })
})
