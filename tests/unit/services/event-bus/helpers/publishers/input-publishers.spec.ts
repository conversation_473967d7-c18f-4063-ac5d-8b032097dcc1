import type { KeyModifiers } from '@/types/services/keyboard'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  publishCanvasClicked,
  publishCanvasMouseDown,
  publishCanvasMouseMove,
  publishCanvasMouseUp,
  publishKeyPressed,
  publishKeyReleased,
} from '@/services/event-bus/helpers/publishers/input-publishers'
import { AppEventType } from '@/types/services/events'

describe('input Publishers', () => {
  let mockEventBus: any

  beforeEach(() => {
    mockEventBus = {
      publish: vi.fn(),
    }
  })

  describe('keyboard Event Publishers', () => {
    it('should publish key pressed event correctly', () => {
      const key = 'a'
      const code = 'KeyA'
      const modifiers: KeyModifiers = {
        altKey: false,
        ctrlKey: true,
        shiftKey: false,
        metaKey: false,
      }
      const originalEvent = new KeyboardEvent('keydown')

      publishKeyPressed(mockEventBus, key, code, modifiers, originalEvent)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.KEY_PRESSED,
        payload: {
          key,
          code,
          modifiers,
          originalEvent,
        },
      })
    })

    it('should publish key released event correctly', () => {
      const key = 'a'
      const code = 'KeyA'
      const modifiers: KeyModifiers = {
        altKey: false,
        ctrlKey: true,
        shiftKey: false,
        metaKey: false,
      }
      const originalEvent = new KeyboardEvent('keyup')

      publishKeyReleased(mockEventBus, key, code, modifiers, originalEvent)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.KEY_RELEASED,
        payload: {
          key,
          code,
          modifiers,
          originalEvent,
        },
      })
    })

    it('should publish key pressed event without original event', () => {
      const key = 'a'
      const code = 'KeyA'
      const modifiers: KeyModifiers = {
        altKey: false,
        ctrlKey: true,
        shiftKey: false,
        metaKey: false,
      }

      publishKeyPressed(mockEventBus, key, code, modifiers)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.KEY_PRESSED,
        payload: {
          key,
          code,
          modifiers,
          originalEvent: undefined,
        },
      })
    })

    it('should publish key released event without original event', () => {
      const key = 'a'
      const code = 'KeyA'
      const modifiers: KeyModifiers = {
        altKey: false,
        ctrlKey: true,
        shiftKey: false,
        metaKey: false,
      }

      publishKeyReleased(mockEventBus, key, code, modifiers)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.KEY_RELEASED,
        payload: {
          key,
          code,
          modifiers,
          originalEvent: undefined,
        },
      })
    })
  })

  describe('canvas Mouse Event Publishers', () => {
    it('should publish canvas clicked event correctly', () => {
      const options = {
        x: 100,
        y: 200,
        button: 0,
        altKey: false,
        ctrlKey: false,
        shiftKey: false,
        metaKey: false,
      }

      publishCanvasClicked(mockEventBus, options)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.CANVAS_CLICKED,
        payload: options,
      })
    })

    it('should publish canvas mouse down event correctly', () => {
      const options = {
        x: 100,
        y: 200,
        button: 0,
        altKey: false,
        ctrlKey: false,
        shiftKey: false,
        metaKey: false,
      }

      publishCanvasMouseDown(mockEventBus, options)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.CANVAS_MOUSE_DOWN,
        payload: options,
      })
    })

    it('should publish canvas mouse move event correctly', () => {
      const x = 100
      const y = 200

      publishCanvasMouseMove(mockEventBus, x, y)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.CANVAS_MOUSE_MOVE,
        payload: { x, y },
      })
    })

    it('should publish canvas mouse up event correctly', () => {
      const options = {
        x: 100,
        y: 200,
        button: 0,
        altKey: false,
        ctrlKey: false,
        shiftKey: false,
        metaKey: false,
      }

      publishCanvasMouseUp(mockEventBus, options)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.CANVAS_MOUSE_UP,
        payload: options,
      })
    })
  })
})
