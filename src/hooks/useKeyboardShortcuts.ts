/**
 * Unified Keyboard Shortcuts Hook
 *
 * This module provides a comprehensive React hook for managing all keyboard shortcuts
 * throughout the application. It consolidates all shortcut functionality into a single
 * centralized system with support for both legacy and modern configuration patterns.
 *
 * Features:
 * - Centralized shortcut management
 * - Cross-platform compatibility (Mac/PC)
 * - Selection shortcuts (single/multi-select)
 * - Canvas navigation shortcuts
 * - Tool shortcuts
 * - File operation shortcuts
 * - UI toggle shortcuts
 * - Configurable shortcut definitions
 *
 * @module hooks/useKeyboardShortcuts
 */

import { useCallback, useEffect } from 'react'
import {
  extractKeyboardModifiers,
  isMultiSelectKeyPressed,
} from '@/lib/utils/canvas/selectionUtils'

// 快捷键配置类型
export interface ShortcutConfig {
  key: string
  ctrl?: boolean
  shift?: boolean
  alt?: boolean
  meta?: boolean
  callback?: () => void
  preventDefault?: boolean
  description?: string
  category?: string
}

// 选择相关快捷键配置
export interface SelectionShortcuts {
  // 全选
  onSelectAll?: () => void
  // 清除选择
  onClearSelection?: () => void
  // 反选
  onInvertSelection?: () => void
  // 选择相同类型
  onSelectSameType?: () => void
}

// 工具相关快捷键配置
export interface ToolShortcuts {
  // 选择工具
  onSelectTool?: () => void
  // 平移工具
  onPanTool?: () => void
  // 绘制工具
  onDrawTool?: () => void
  // 文本工具
  onTextTool?: () => void
  // 矩形工具
  onRectangleTool?: () => void
  // 圆形工具
  onCircleTool?: () => void
  // 线条工具
  onLineTool?: () => void
}

// 文件操作快捷键配置
export interface FileShortcuts {
  // 新建
  onNew?: () => void
  // 打开
  onOpen?: () => void
  // 保存
  onSave?: () => void
  // 另存为
  onSaveAs?: () => void
  // 导出
  onExport?: () => void
  // 导入
  onImport?: () => void
}

// UI 切换快捷键配置
export interface UIShortcuts {
  // 切换左侧面板
  onToggleLeftPanel?: () => void
  // 切换右侧面板
  onToggleRightPanel?: () => void
  // 切换网格
  onToggleGrid?: () => void
  // 切换标尺
  onToggleRuler?: () => void
  // 切换全屏
  onToggleFullscreen?: () => void
  // 打开设置
  onOpenSettings?: () => void
  // 打开快捷键指南
  onOpenShortcutsGuide?: () => void
}

// 编辑操作快捷键配置
export interface EditShortcuts {
  // 撤销
  onUndo: () => void
  // 重做
  onRedo: () => void
  // 复制
  onCopy?: () => void
  // 粘贴
  onPaste?: () => void
  // 剪切
  onCut?: () => void
  // 删除
  onDelete: () => void
  // 复制
  onDuplicate?: () => void
}

// 视图操作快捷键配置
export interface ViewShortcuts {
  // 放大
  onZoomIn: () => void
  // 缩小
  onZoomOut: () => void
  // 重置缩放
  onResetZoom: () => void
  // 适应窗口
  onFitToWindow?: () => void
  // 实际大小
  onActualSize?: () => void
  // 切换平移模式
  onTogglePanMode: () => void
}

// 完整的快捷键配置接口
export interface KeyboardShortcutOptions extends
  EditShortcuts,
  ViewShortcuts,
  SelectionShortcuts,
  ToolShortcuts,
  FileShortcuts,
  UIShortcuts {
}

/**
 * 预定义的快捷键配置
 * 只包含实际实现的快捷键功能
 */
export const DEFAULT_SHORTCUTS: ShortcutConfig[] = [
  // === Edit Operations ===
  { key: 'z', ctrl: true, description: 'Undo', category: 'edit' },
  { key: 'z', ctrl: true, shift: true, description: 'Redo', category: 'edit' },
  { key: 'Delete/Backspace', description: 'Delete Selected Elements', category: 'edit' },

  // === Selection Operations ===
  { key: 'a', ctrl: true, description: 'Select All', category: 'selection' },
  { key: 'Escape', description: 'Clear Selection', category: 'selection' },

  // === View Operations ===
  { key: '+/=', ctrl: true, description: 'Zoom In', category: 'view' },
  { key: '-', ctrl: true, description: 'Zoom Out', category: 'view' },
  { key: '0', ctrl: true, description: 'Reset Zoom', category: 'view' },
  { key: 'Space', description: 'Toggle Pan Mode', category: 'view' },

  // === File Operations ===
  { key: 'n', ctrl: true, description: 'New Canvas', category: 'file' },
  { key: 's', ctrl: true, description: 'Save Canvas', category: 'file' },
  { key: 'e', ctrl: true, description: 'Export Canvas', category: 'file' },

  // === Tool Selection ===
  { key: 'v', description: 'Select Tool', category: 'tool' },
  { key: 'h', description: 'Pan Tool', category: 'tool' },

  // === UI Toggle ===
  { key: 'l', ctrl: true, shift: true, description: 'Toggle Left Panel', category: 'ui' },
  { key: 'r', ctrl: true, shift: true, description: 'Toggle Right Panel', category: 'ui' },
  { key: 'g', ctrl: true, description: 'Toggle Grid', category: 'ui' },
  { key: '/', ctrl: true, description: 'Open Shortcuts Guide', category: 'ui' },
  { key: ',', ctrl: true, description: 'Open Settings', category: 'ui' },
]

/**
 * 统一的快捷键处理 Hook
 *
 * 支持两种使用方式：
 * 1. 配置数组模式：useKeyboardShortcuts([{ key: 'z', ctrl: true, callback: ... }])
 * 2. 选项对象模式：useKeyboardShortcuts({ onUndo: ..., onRedo: ... })
 *
 * @param configsOrOptions 快捷键配置数组或选项对象
 */
export function useKeyboardShortcuts(
  configsOrOptions: ShortcutConfig[] | KeyboardShortcutOptions,
) {
  const isConfigArray = Array.isArray(configsOrOptions)

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const { key, ctrlKey, metaKey, shiftKey } = event
    const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.userAgent)
    const isCtrl = isMac ? metaKey : ctrlKey

    // 提取键盘修饰键
    // Note: modifiers available but not currently used in this implementation
    // const modifiers = extractKeyboardModifiers(event)

    // 检查是否在输入框内
    const activeElement = document.activeElement
    const isInputFocused = (
      activeElement instanceof HTMLInputElement
      || activeElement instanceof HTMLTextAreaElement
      || activeElement?.hasAttribute('contenteditable')
    )

    // 在输入框内时，只处理特定的快捷键（如 Ctrl+Z, Ctrl+Y 等）
    if (isInputFocused) {
      const allowedInInput = [
        'z',
        'y',
        'a',
        'c',
        'v',
        'x',
        's',
        'n',
        'o',
        'e', // 编辑和文件操作
        'l',
        'r', // UI 切换（左右面板）
        'F11',
        'Escape', // 全屏和取消
      ]
      if (!allowedInInput.includes(key) && !isCtrl) {
        return
      }
    }

    if (isConfigArray) {
      // 配置数组模式：处理自定义快捷键配置
      for (const conf of configsOrOptions) {
        if (isShortcutMatch(conf, event, isMac)) {
          if (conf.preventDefault !== false) {
            event.preventDefault()
          }
          if (conf.callback) {
            conf.callback()
          }
          return
        }
      }
    }
    else {
      // 选项对象模式：处理预定义的快捷键
      const opts = configsOrOptions

      // === 编辑操作 ===
      // 撤销: Ctrl/Cmd + Z
      if (isCtrl && key === 'z' && !shiftKey) {
        event.preventDefault()
        opts.onUndo()
        return
      }

      // 重做: Ctrl/Cmd + Shift + Z 或 Ctrl/Cmd + Y
      if ((isCtrl && key === 'z' && shiftKey) || (isCtrl && key === 'y')) {
        event.preventDefault()
        opts.onRedo()
        return
      }

      // 复制: Ctrl/Cmd + C
      if (isCtrl && key === 'c' && opts.onCopy) {
        event.preventDefault()
        opts.onCopy()
        return
      }

      // 粘贴: Ctrl/Cmd + V
      if (isCtrl && key === 'v' && opts.onPaste) {
        event.preventDefault()
        opts.onPaste()
        return
      }

      // 剪切: Ctrl/Cmd + X
      if (isCtrl && key === 'x' && opts.onCut) {
        event.preventDefault()
        opts.onCut()
        return
      }

      // 复制: Ctrl/Cmd + D
      if (isCtrl && key === 'd' && opts.onDuplicate) {
        event.preventDefault()
        opts.onDuplicate()
        return
      }

      // 删除: Delete 或 Backspace
      if ((key === 'Delete' || key === 'Backspace') && !isInputFocused) {
        event.preventDefault()
        opts.onDelete()
        return
      }

      // === 选择操作 ===
      // 全选: Ctrl/Cmd + A
      if (isCtrl && key === 'a' && opts.onSelectAll) {
        event.preventDefault()
        opts.onSelectAll()
        return
      }

      // 清除选择: Escape
      if (key === 'Escape' && opts.onClearSelection) {
        event.preventDefault()
        opts.onClearSelection()
        return
      }

      // === 视图操作 ===
      // 放大: Ctrl/Cmd + = 或 Ctrl/Cmd + +
      if (isCtrl && (key === '=' || key === '+')) {
        event.preventDefault()
        opts.onZoomIn()
        return
      }

      // 缩小: Ctrl/Cmd + -
      if (isCtrl && key === '-') {
        event.preventDefault()
        opts.onZoomOut()
        return
      }

      // 重置缩放: Ctrl/Cmd + 0
      if (isCtrl && key === '0') {
        event.preventDefault()
        opts.onResetZoom()
        return
      }

      // 适应窗口: Ctrl/Cmd + 1
      if (isCtrl && key === '1' && opts.onFitToWindow) {
        event.preventDefault()
        opts.onFitToWindow()
        return
      }

      // 实际大小: Ctrl/Cmd + 2
      if (isCtrl && key === '2' && opts.onActualSize) {
        event.preventDefault()
        opts.onActualSize()
        return
      }

      // 切换平移模式: Space
      if (key === ' ' && !isInputFocused) {
        event.preventDefault()
        opts.onTogglePanMode()
        return
      }

      // === 文件操作 ===
      // 新建: Ctrl/Cmd + N
      if (isCtrl && key === 'n' && opts.onNew) {
        event.preventDefault()
        opts.onNew()
        return
      }

      // 打开: Ctrl/Cmd + O
      if (isCtrl && key === 'o' && opts.onOpen) {
        event.preventDefault()
        opts.onOpen()
        return
      }

      // 保存: Ctrl/Cmd + S
      if (isCtrl && key === 's' && !shiftKey && opts.onSave) {
        event.preventDefault()
        opts.onSave()
        return
      }

      // 另存为: Ctrl/Cmd + Shift + S
      if (isCtrl && key === 's' && shiftKey && opts.onSaveAs) {
        event.preventDefault()
        opts.onSaveAs()
        return
      }

      // 导出: Ctrl/Cmd + E
      if (isCtrl && key === 'e' && opts.onExport) {
        event.preventDefault()
        opts.onExport()
        return
      }

      // === 工具选择 ===
      // 选择工具: V
      if (key === 'v' && !isCtrl && !isInputFocused && opts.onSelectTool) {
        event.preventDefault()
        opts.onSelectTool()
        return
      }

      // 平移工具: H
      if (key === 'h' && !isCtrl && !isInputFocused && opts.onPanTool) {
        event.preventDefault()
        opts.onPanTool()
        return
      }

      // 文本工具: T
      if (key === 't' && !isCtrl && !isInputFocused && opts.onTextTool) {
        event.preventDefault()
        opts.onTextTool()
        return
      }

      // 矩形工具: R
      if (key === 'r' && !isCtrl && !isInputFocused && opts.onRectangleTool) {
        event.preventDefault()
        opts.onRectangleTool()
        return
      }

      // 圆形工具: C (只有在不是 Ctrl+C 的情况下)
      if (key === 'c' && !isCtrl && !isInputFocused && opts.onCircleTool) {
        event.preventDefault()
        opts.onCircleTool()
        return
      }

      // 线条工具: L
      if (key === 'l' && !isCtrl && !isInputFocused && opts.onLineTool) {
        event.preventDefault()
        opts.onLineTool()
        return
      }

      // === UI 切换 ===
      // 切换左侧面板: Ctrl/Cmd + Shift + L
      if (isCtrl && shiftKey && key === 'l' && opts.onToggleLeftPanel) {
        event.preventDefault()
        opts.onToggleLeftPanel()
        return
      }

      // 切换右侧面板: Ctrl/Cmd + Shift + R
      if (isCtrl && shiftKey && key === 'r' && opts.onToggleRightPanel) {
        event.preventDefault()
        opts.onToggleRightPanel()
        return
      }

      // 切换网格: Ctrl/Cmd + G
      if (isCtrl && key === 'g' && opts.onToggleGrid) {
        event.preventDefault()
        opts.onToggleGrid()
        return
      }

      // 切换全屏: F11
      if (key === 'F11' && opts.onToggleFullscreen) {
        event.preventDefault()
        opts.onToggleFullscreen()
        return
      }

      // 打开设置: Ctrl/Cmd + ,
      if (isCtrl && key === ',' && opts.onOpenSettings) {
        event.preventDefault()
        opts.onOpenSettings()
        return
      }

      // 打开快捷键指南: Ctrl/Cmd + /
      if (isCtrl && key === '/' && opts.onOpenShortcutsGuide) {
        event.preventDefault()
        opts.onOpenShortcutsGuide()
      }
    }
  }, [configsOrOptions, isConfigArray])

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleKeyDown])
}

/**
 * 检查快捷键是否匹配
 * @param config 快捷键配置
 * @param event 键盘事件
 * @param isMac 是否为 Mac 系统
 * @returns 是否匹配
 */
function isShortcutMatch(config: ShortcutConfig, event: KeyboardEvent, isMac: boolean): boolean {
  const { key, ctrlKey, metaKey, shiftKey, altKey } = event

  // 处理 Ctrl/Cmd 键的跨平台兼容性
  const isCtrl = isMac ? metaKey : ctrlKey
  const expectedCtrl = config.ctrl || config.meta

  // 检查主键
  if (key.toLowerCase() !== config.key.toLowerCase()) {
    return false
  }

  // 检查修饰键
  const ctrlMatch = expectedCtrl ? isCtrl : !isCtrl
  const shiftMatch = config.shift ? shiftKey : !shiftKey
  const altMatch = config.alt ? altKey : !altKey

  return ctrlMatch && shiftMatch && altMatch
}

/**
 * 获取快捷键的显示文本
 * @param config 快捷键配置
 * @param isMac 是否为 Mac 系统
 * @returns 快捷键显示文本
 */
export function getShortcutDisplayText(config: ShortcutConfig, isMac?: boolean): string {
  const platform = isMac ?? /Mac|iPod|iPhone|iPad/.test(navigator.userAgent)
  const parts: string[] = []

  if (config.ctrl || config.meta) {
    parts.push(platform ? '⌘' : 'Ctrl')
  }

  if (config.shift) {
    parts.push(platform ? '⇧' : 'Shift')
  }

  if (config.alt) {
    parts.push(platform ? '⌥' : 'Alt')
  }

  // 处理特殊键名
  let keyName = config.key
  switch (config.key.toLowerCase()) {
    case ' ':
      keyName = 'Space'
      break
    case 'escape':
      keyName = 'Esc'
      break
    case 'delete':
      keyName = 'Del'
      break
    case 'backspace':
      keyName = '⌫'
      break
    case 'enter':
      keyName = '↵'
      break
    case 'tab':
      keyName = '⇥'
      break
    default:
      keyName = config.key.toUpperCase()
  }

  parts.push(keyName)

  return parts.join(platform ? '' : '+')
}

/**
 * 根据类别获取快捷键列表
 * @param category 快捷键类别
 * @returns 该类别的快捷键配置列表
 */
export function getShortcutsByCategory(category: string): ShortcutConfig[] {
  return DEFAULT_SHORTCUTS.filter(shortcut => shortcut.category === category)
}

/**
 * 获取所有快捷键类别
 * @returns 快捷键类别列表
 */
export function getShortcutCategories(): string[] {
  const categories = new Set(DEFAULT_SHORTCUTS.map(shortcut => shortcut.category))
  return Array.from(categories).filter(Boolean) as string[]
}

/**
 * 创建快捷键配置的便捷函数
 * @param key 按键
 * @param modifiers 修饰键
 * @param callback 回调函数
 * @param options 其他选项
 * @returns 快捷键配置
 */
export function createShortcut(
  key: string,
  modifiers: {
    ctrl?: boolean
    shift?: boolean
    alt?: boolean
    meta?: boolean
  },
  callback: () => void,
  options?: {
    preventDefault?: boolean
    description?: string
    category?: string
  },
): ShortcutConfig {
  return {
    key,
    ...modifiers,
    callback,
    preventDefault: options?.preventDefault ?? true,
    description: options?.description,
    category: options?.category,
  }
}

/**
 * 工具栏快捷键提示工具函数
 */
export const ToolbarShortcutUtils = {
  /**
   * 获取快捷键提示文本
   * @param shortcutKey 快捷键标识
   * @param isMac 是否为 Mac 系统
   * @returns 快捷键提示文本
   */
  getTooltipText: (shortcutKey: string, isMac?: boolean): string => {
    const platform = isMac ?? /Mac|iPod|iPhone|iPad/.test(navigator.userAgent)
    const ctrlKey = platform ? '⌘' : 'Ctrl'
    const shiftKey = platform ? '⇧' : 'Shift'

    const shortcuts: Record<string, string> = {
      // File operations
      new: `${ctrlKey}+N`,
      save: `${ctrlKey}+S`,
      export: `${ctrlKey}+E`,

      // Edit operations
      undo: `${ctrlKey}+Z`,
      redo: platform ? `${ctrlKey}+${shiftKey}+Z` : `${ctrlKey}+Y`,

      // View operations
      zoomIn: `${ctrlKey}++`,
      zoomOut: `${ctrlKey}+-`,
      resetZoom: `${ctrlKey}+0`,
      panMode: 'Space',

      // UI toggles
      leftPanel: `${ctrlKey}+${shiftKey}+L`,
      rightPanel: `${ctrlKey}+${shiftKey}+R`,
      grid: `${ctrlKey}+G`,
      shortcuts: `${ctrlKey}+/`,
      settings: `${ctrlKey}+,`,
    }

    return shortcuts[shortcutKey] || ''
  },

  /**
   * 生成完整的工具提示文本
   * @param action 操作名称
   * @param shortcutKey 快捷键标识
   * @param isMac 是否为 Mac 系统
   * @returns 完整的工具提示文本
   */
  getFullTooltipText: (action: string, shortcutKey: string, isMac?: boolean): string => {
    const shortcut = ToolbarShortcutUtils.getTooltipText(shortcutKey, isMac)
    return shortcut ? `${action} (${shortcut})` : action
  },
}

/**
 * 多选相关的快捷键工具函数
 */
export const SelectionShortcutUtils = {
  /**
   * 检查是否按下了多选键
   * @param event 键盘事件
   * @returns 是否按下多选键
   */
  isMultiSelectPressed: (event: KeyboardEvent): boolean => {
    const modifiers = extractKeyboardModifiers(event)
    return isMultiSelectKeyPressed(modifiers)
  },

  /**
   * 获取多选键的显示文本
   * @param isMac 是否为 Mac 系统
   * @returns 多选键显示文本
   */
  getMultiSelectKeyText: (isMac?: boolean): string => {
    const platform = isMac ?? /Mac|iPod|iPhone|iPad/.test(navigator.userAgent)
    return platform ? '⌘/⇧' : 'Ctrl/Shift'
  },

  /**
   * 创建选择相关的快捷键配置
   * @param callbacks 选择操作回调函数
   * @returns 选择快捷键配置数组
   */
  createSelectionShortcuts: (callbacks: SelectionShortcuts): ShortcutConfig[] => {
    const shortcuts: ShortcutConfig[] = []

    if (callbacks.onSelectAll) {
      shortcuts.push(createShortcut('a', { ctrl: true }, callbacks.onSelectAll, {
        description: '全选',
        category: 'selection',
      }))
    }

    if (callbacks.onClearSelection) {
      shortcuts.push(createShortcut('Escape', {}, callbacks.onClearSelection, {
        description: '清除选择',
        category: 'selection',
      }))
    }

    if (callbacks.onInvertSelection) {
      shortcuts.push(createShortcut('i', { ctrl: true, shift: true }, callbacks.onInvertSelection, {
        description: '反选',
        category: 'selection',
      }))
    }

    return shortcuts
  },
}

export default useKeyboardShortcuts
