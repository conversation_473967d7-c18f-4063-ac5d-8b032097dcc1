import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import type { Mocked } from 'vitest'
import { CoreCoordinator } from '@/core/CoreCoordinator'
import type { ComputeFacade } from '@/core/compute/ComputeFacade'
import type { ElementFactory } from '@/core/factory/ElementFactory'
import type { ShapeRepository } from '@/core/state/ShapeRepository'
import type { ElementValidator } from '@/core/validator/ElementValidator'
import type { EventBus } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import type { ErrorService } from '@/services/system/error-service/errorService'
import type { AppEventMap } from '@/types/services/events/eventRegistry'
import { ElementType } from '@/types/core/elementDefinitions'

// Mock all dependencies
vi.mock('@/core/compute/ComputeFacade')
vi.mock('@/core/factory/ElementFactory')
vi.mock('@/core/state/ShapeRepository')
vi.mock('@/core/validator/ElementValidator')
vi.mock('@/services/system/error-service/errorService')

describe('CoreCoordinator (Real Implementation)', () => {
  let coordinator: CoreCoordinator
  let mockEventBus: Mocked<EventBus<AppEventMap>>
  let mockShapeRepository: Mocked<ShapeRepository>
  let mockValidator: Mocked<ElementValidator>
  let mockElementFactory: Mocked<ElementFactory>
  let mockLogger: Mocked<LoggerService>
  let mockErrorService: Mocked<ErrorService>
  let mockComputeFacade: Mocked<ComputeFacade>

  beforeEach(() => {
    // Mock EventBus
    mockEventBus = {
      publish: vi.fn(),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
      on: vi.fn(),
      off: vi.fn(),
      configure: vi.fn(),
      getSubscriptions: vi.fn().mockReturnValue(new Map()),
      reset: vi.fn(),
      createEvent: vi.fn(),
    } as unknown as Mocked<EventBus<AppEventMap>>

    // Mock ShapeRepository
    mockShapeRepository = {
      add: vi.fn(),
      getById: vi.fn(),
      getAll: vi.fn().mockReturnValue([]),
      update: vi.fn(),
      remove: vi.fn(),
      clear: vi.fn(),
      getSelectedIds: vi.fn().mockReturnValue([]),
      setSelectedIds: vi.fn(),
      addToSelection: vi.fn(),
      removeFromSelection: vi.fn(),
      clearSelection: vi.fn(),
      setShapesFromExternal: vi.fn(),
    } as unknown as Mocked<ShapeRepository>

    // Mock ElementValidator
    mockValidator = {
      validateElement: vi.fn().mockReturnValue({ valid: true, errors: [] }),
      validateElements: vi.fn().mockResolvedValue([]),
      validateElementAsync: vi.fn().mockResolvedValue({ valid: true, errors: [] }),
      formatErrorMessage: vi.fn().mockReturnValue('Test error'),
    } as unknown as Mocked<ElementValidator>

    // Mock ElementFactory
    mockElementFactory = {
      createShape: vi.fn().mockResolvedValue({
        id: 'test-shape',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: { width: 100, height: 50 },
      }),
      createElement: vi.fn(),
      createDefaultElement: vi.fn(),
      registerCreator: vi.fn(),
      normalizePositionInput: vi.fn().mockReturnValue({ x: 0, y: 0 }),
    } as unknown as Mocked<ElementFactory>

    // Mock Logger
    mockLogger = {
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      setContext: vi.fn(),
      getConfig: vi.fn(),
      setConfig: vi.fn(),
    } as unknown as Mocked<LoggerService>

    // Mock ErrorService
    mockErrorService = {
      handleError: vi.fn(),
      reportError: vi.fn(),
      getLastError: vi.fn(),
      createError: vi.fn(),
    } as unknown as Mocked<ErrorService>

    // Mock ComputeFacade
    mockComputeFacade = {
      computeArea: vi.fn().mockResolvedValue(100),
      computePerimeter: vi.fn().mockResolvedValue(40),
      computeBoundingBox: vi.fn().mockReturnValue({
        x: 0, y: 0, width: 100, height: 50,
      }),
      isPointInside: vi.fn().mockReturnValue(false),
      transformElement: vi.fn(),
      compute: vi.fn(),
      hasStrategy: vi.fn().mockReturnValue(true),
    } as unknown as Mocked<ComputeFacade>

    // Create CoreCoordinator instance
    coordinator = new CoreCoordinator(
      mockEventBus,
      mockShapeRepository,
      mockValidator,
      mockElementFactory,
      mockLogger,
      mockErrorService,
      undefined, // config
      mockComputeFacade,
    )
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Constructor', () => {
    it('should create CoreCoordinator instance with all dependencies', () => {
      expect(coordinator).toBeDefined()
      expect(coordinator).toBeInstanceOf(CoreCoordinator)
    })

    it('should initialize with provided dependencies', () => {
      // Access public properties if available
      expect(coordinator.shapeRepository).toBe(mockShapeRepository)
      expect(coordinator.validator).toBe(mockValidator)
      expect(coordinator.elementFactory).toBe(mockElementFactory)
      expect(coordinator.computeFacade).toBe(mockComputeFacade)
    })

    it('should set up event subscriptions during initialization', () => {
      // The constructor should call subscribe on the event bus
      expect(mockEventBus.subscribe).toHaveBeenCalled()
    })

    it('should log initialization', () => {
      // The constructor should log its creation
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('[CoreCoordinator] constructor called')
      )
    })
  })

  describe('Event Handling', () => {
    it('should subscribe to required events', () => {
      // Verify that the coordinator subscribes to necessary events
      expect(mockEventBus.subscribe).toHaveBeenCalled()
      
      // Check for specific event subscriptions if the implementation exposes them
      const subscribeCallArgs = mockEventBus.subscribe.mock.calls
      expect(subscribeCallArgs.length).toBeGreaterThan(0)
    })

    it('should handle event subscription errors gracefully', () => {
      mockEventBus.subscribe.mockImplementation(() => {
        throw new Error('Subscription failed')
      })

      // Creating a new coordinator should not throw even if subscription fails
      expect(() => {
        new CoreCoordinator(
          mockEventBus,
          mockShapeRepository,
          mockValidator,
          mockElementFactory,
          mockLogger,
          mockErrorService,
        )
      }).not.toThrow()
    })
  })

  describe('Dependency Access', () => {
    it('should provide access to shape repository', () => {
      expect(coordinator.shapeRepository).toBe(mockShapeRepository)
    })

    it('should provide access to element validator', () => {
      expect(coordinator.validator).toBe(mockValidator)
    })

    it('should provide access to element factory', () => {
      expect(coordinator.elementFactory).toBe(mockElementFactory)
    })

    it('should provide access to compute facade when provided', () => {
      expect(coordinator.computeFacade).toBe(mockComputeFacade)
    })
  })

  describe('Configuration', () => {
    it('should handle undefined initial config', () => {
      const coordinatorWithoutConfig = new CoreCoordinator(
        mockEventBus,
        mockShapeRepository,
        mockValidator,
        mockElementFactory,
        mockLogger,
        mockErrorService,
      )

      expect(coordinatorWithoutConfig).toBeDefined()
    })

    it('should handle partial initial config', () => {
      const partialConfig = { enableDebugMode: true }
      
      const coordinatorWithConfig = new CoreCoordinator(
        mockEventBus,
        mockShapeRepository,
        mockValidator,
        mockElementFactory,
        mockLogger,
        mockErrorService,
        partialConfig,
      )

      expect(coordinatorWithConfig).toBeDefined()
    })
  })

  describe('Error Handling', () => {
    it('should handle dependency injection errors', () => {
      // Test with null dependencies
      expect(() => {
        new CoreCoordinator(
          null as any,
          mockShapeRepository,
          mockValidator,
          mockElementFactory,
          mockLogger,
          mockErrorService,
        )
      }).not.toThrow()
    })

    it('should handle logger errors gracefully', () => {
      mockLogger.warn.mockImplementation(() => {
        throw new Error('Logger error')
      })

      // Should not throw even if logger fails
      expect(() => {
        new CoreCoordinator(
          mockEventBus,
          mockShapeRepository,
          mockValidator,
          mockElementFactory,
          mockLogger,
          mockErrorService,
        )
      }).not.toThrow()
    })
  })

  describe('Internal State Management', () => {
    it('should initialize internal update flag', () => {
      // The coordinator should have an internal flag to prevent feedback loops
      // This is a private property, so we test its behavior indirectly
      expect(coordinator).toBeDefined()
    })

    it('should handle concurrent operations', () => {
      // Test that multiple operations don't interfere with each other
      const operations = [
        () => coordinator.shapeRepository.getAll(),
        () => coordinator.validator.validateElement({} as any),
        () => coordinator.elementFactory.normalizePositionInput({ x: 0, y: 0 }),
      ]

      expect(() => {
        operations.forEach(op => op())
      }).not.toThrow()
    })
  })

  describe('Integration Points', () => {
    it('should integrate with all required services', () => {
      // Verify that all injected services are accessible
      expect(coordinator.shapeRepository).toBeDefined()
      expect(coordinator.validator).toBeDefined()
      expect(coordinator.elementFactory).toBeDefined()
      expect(coordinator.computeFacade).toBeDefined()
    })

    it('should handle optional compute facade', () => {
      const coordinatorWithoutCompute = new CoreCoordinator(
        mockEventBus,
        mockShapeRepository,
        mockValidator,
        mockElementFactory,
        mockLogger,
        mockErrorService,
      )

      expect(coordinatorWithoutCompute).toBeDefined()
      // computeFacade should be undefined when not provided
      expect(coordinatorWithoutCompute.computeFacade).toBeUndefined()
    })
  })
})
