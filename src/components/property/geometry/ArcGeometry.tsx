/**
 * Arc Geometry Component
 *
 * Implements the standardized geometry layout for Arc elements
 * according to the design document specifications.
 *
 * Layout:
 * - Center Point: Canvas position editing for arc center
 * - Arc Properties: Radius and angle controls with real-time path generation
 * - Calculation & Cost (unified layout)
 *
 * Note: Arc elements use center point and angles instead of standard position controls.
 * They don't support rotation as the arc orientation is defined by the angles.
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ElementType } from '@/types/core/elementDefinitions'
import { GeometrySection, PropertyField } from '../shared'

// Type definitions for store access
interface WindowWithStore {
  __ZUSTAND_SHAPES_STORE__?: {
    getState: () => {
      shapes: ShapeElement[]
      selectedShapeIds: string[]
    }
    setState: (state: {
      shapes: ShapeElement[]
      selectedShapeIds: string[]
    }) => void
  }
}

// Stable function references to avoid infinite render loops
const identityFunction = (value: number) => value

interface ArcGeometryProps {
  /** The arc element being edited */
  element: ShapeElement
  /** Function to get input values with proper formatting */
  getValueForInput: (path: string) => string
  /** Function to get placeholder values */
  getPlaceholderForInput: (path: string) => string
  /** Function to handle input changes */
  handleInputChange: (path: string, value: string, isNumeric?: boolean, convertToPixels?: boolean) => void
  /** Unit name for display (e.g., "mm", "cm") */
  unitName: string
  /** Function to convert internal units to display units */
  toDisplayUnit?: (value: number) => number
  /** Function to convert display units to internal units */
  toInternalUnit?: (value: number) => number
  /** Function to update element properties directly */
  updateProperty?: (path: string, value: unknown) => void
}

/**
 * ArcGeometry component for editing arc properties.
 *
 * Provides a standardized layout for arc geometry editing
 * with proper grouping and responsive design. Arcs use
 * center point, radius, and angle controls with real-time path generation.
 */
export function ArcGeometry({
  element,
  getValueForInput: _getValueForInput,
  getPlaceholderForInput: _getPlaceholderForInput,
  handleInputChange,
  unitName,
  toDisplayUnit = identityFunction,
  toInternalUnit = identityFunction,
  updateProperty,
}: ArcGeometryProps) {
  // Verify this is an arc element
  if (element.type !== ElementType.ARC) {
    return null
  }

  // Get arc properties
  const arcPosition = element.position ?? { x: 0, y: 0, z: 0 }
  const radius = (element.properties?.radius as number) || 50
  const startAngle = (element.properties?.startAngle as number) || 0
  const endAngle = (element.properties?.endAngle as number) || 90

  // Helper function to update arc with path regeneration using direct store update
  const updateArcProperty = async (propertyPath: string, value: unknown) => {
    console.warn(`[ArcGeometry] updateArcProperty called: ${propertyPath} = ${String(value)}`)

    // 🔧 使用与LINE相同的直接store更新模式
    const windowWithStore = window as WindowWithStore
    if (typeof window !== 'undefined' && windowWithStore.__ZUSTAND_SHAPES_STORE__ !== undefined) {
      const shapesStore = windowWithStore.__ZUSTAND_SHAPES_STORE__
      const state = shapesStore.getState()

      try {
        // For radius and angle changes, we need to regenerate the path data
        if (propertyPath.includes('radius') || propertyPath.includes('Angle')) {
          console.warn('[ArcGeometry] Regenerating path data for:', propertyPath)

          // Import the arc utils dynamically
          const { generateArcPathData } = await import('@/lib/utils/geometry/arcUtils')

          // Get current values and apply the update
          let newRadius = radius
          let newStartAngle = startAngle
          let newEndAngle = endAngle

          if (propertyPath.includes('radius')) {
            newRadius = typeof value === 'number' ? value : Number(value) || 0
          }
          else if (propertyPath.includes('startAngle')) {
            newStartAngle = typeof value === 'number' ? value : Number(value) || 0
          }
          else if (propertyPath.includes('endAngle')) {
            newEndAngle = typeof value === 'number' ? value : Number(value) || 0
          }

          console.warn('[ArcGeometry] New arc parameters:', { newRadius, newStartAngle, newEndAngle })

          // Generate new path data with relative center (0,0)
          const relativeCenter = { x: 0, y: 0, z: 0 }
          const newPathData = generateArcPathData(
            relativeCenter,
            newRadius,
            newStartAngle,
            newEndAngle,
            false,
          )

          console.warn('[ArcGeometry] Generated new path data:', newPathData)

          // Create updated element with new properties
          const updatedElement = { ...element }

          if (propertyPath === 'position') {
            updatedElement.position = value as { x: number, y: number, z: number }
          }
          else if (propertyPath.includes('radius')) {
            updatedElement.properties = {
              ...element.properties,
              radius: newRadius,
              rx: newRadius,
              ry: newRadius,
              pathData: newPathData,
            }
          }
          else if (propertyPath.includes('startAngle')) {
            updatedElement.properties = {
              ...element.properties,
              startAngle: newStartAngle,
              pathData: newPathData,
            }
          }
          else if (propertyPath.includes('endAngle')) {
            updatedElement.properties = {
              ...element.properties,
              endAngle: newEndAngle,
              pathData: newPathData,
            }
          }

          // Update store中的shapes数组
          const newShapes = state.shapes.map((s: ShapeElement) =>
            s.id === element.id ? updatedElement : s,
          )

          // 保持选中状态
          shapesStore.setState({
            ...state,
            shapes: newShapes,
            selectedShapeIds: [element.id],
          })

          console.warn('[ArcGeometry] Direct store update completed successfully')
        }
        else {
          // For position changes, just update the property
          console.warn('[ArcGeometry] Simple property update for:', propertyPath)

          const updatedElement = { ...element }
          if (propertyPath === 'position') {
            updatedElement.position = value as { x: number, y: number, z: number }
          }

          const newShapes = state.shapes.map((s: ShapeElement) =>
            s.id === element.id ? updatedElement : s,
          )

          shapesStore.setState({
            ...state,
            shapes: newShapes,
            selectedShapeIds: [element.id],
          })
        }
      }
      catch (error) {
        console.warn('[ArcGeometry] Failed to update arc with direct store:', error)
        // Fallback to updateProperty if available
        if (updateProperty) {
          updateProperty(propertyPath, value)
        }
        else {
          handleInputChange(propertyPath, String(value))
        }
      }
    }
    else {
      // Fallback to updateProperty or handleInputChange
      console.warn('[ArcGeometry] Store not available, using fallback')
      if (updateProperty) {
        updateProperty(propertyPath, value)
      }
      else {
        handleInputChange(propertyPath, String(value))
      }
    }
  }

  return (
    <>
      {/* Center Position */}
      <GeometrySection type="special" columns={2} title="Center Position">
        <PropertyField
          label={`Center X (${unitName})`}
          type="number"
          value={Number(toDisplayUnit(arcPosition.x)).toFixed(2)}
          onChange={(value) => {
            const newCanvasValue = Number.parseFloat(value) || 0
            const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
            const newPosition = { ...arcPosition, x: newCanvasPixelValue }
            void updateArcProperty('position', newPosition)
          }}
          placeholder="X"
        />
        <PropertyField
          label={`Center Y (${unitName})`}
          type="number"
          value={Number(toDisplayUnit(arcPosition.y)).toFixed(2)}
          onChange={(value) => {
            const newCanvasValue = Number.parseFloat(value) || 0
            const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
            const newPosition = { ...arcPosition, y: newCanvasPixelValue }
            void updateArcProperty('position', newPosition)
          }}
          placeholder="Y"
        />
      </GeometrySection>

      {/* Arc Properties - Combined with dimensions */}
      <GeometrySection type="dimensions" columns={1}>
        <div className="space-y-3">
          {/* Radius */}
          <div className="space-y-1">
            <Label className="text-sm font-medium">
              Radius (
              {unitName}
              )
            </Label>
            <Input
              type="number"
              value={Number(toDisplayUnit(radius)).toFixed(2)}
              onChange={(e) => {
                const newValue = Number.parseFloat(e.target.value) || 0
                const pixelValue = toInternalUnit(newValue) || 0
                void updateArcProperty('properties.radius', pixelValue)
              }}
              placeholder="50"
              className="h-10"
              min={1}
            />
          </div>

          {/* Angle Controls */}
          <div className="grid grid-cols-2 gap-x-3 gap-y-2">
            <div className="space-y-1">
              <Label className="text-sm font-medium">Start Angle (°)</Label>
              <Input
                type="number"
                value={startAngle.toFixed(1)}
                onChange={(e) => {
                  const newStartAngle = Number.parseFloat(e.target.value) || 0
                  void updateArcProperty('properties.startAngle', newStartAngle)
                }}
                placeholder="0"
                className="h-10"
                step={1}
              />
            </div>
            <div className="space-y-1">
              <Label className="text-sm font-medium">End Angle (°)</Label>
              <Input
                type="number"
                value={endAngle.toFixed(1)}
                onChange={(e) => {
                  const newEndAngle = Number.parseFloat(e.target.value) || 0
                  void updateArcProperty('properties.endAngle', newEndAngle)
                }}
                placeholder="90"
                className="h-10"
                step={1}
              />
            </div>
          </div>
        </div>
      </GeometrySection>

      {/* Note: No standard Position or Rotation sections for arcs */}
      {/* Arc position and orientation are fully defined by center point and angles */}
    </>
  )
}
