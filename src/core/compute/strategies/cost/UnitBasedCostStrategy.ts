/**
 * Cost Calculation Strategy Based on Number of Units
 *
 * @remarks
 * This strategy implements the {@link CostCalculatorStrategy} to calculate costs
 * for elements that are typically priced per unit (e.g., doors, windows, fixtures,
 * appliances).
 *
 * The cost is determined by multiplying the `quantity` of units by a `unitCost`.
 * The `quantity` can be provided directly in the {@link CostCalculationOptions} or,
 * ideally, should be derivable from the element itself (though this part is marked as a TODO).
 * If not provided, it currently defaults to `1` with a warning.
 *
 * The core calculation logic (once quantity and unit cost are determined) is intended
 * to be delegated to a `calculateUnitBasedCost` utility function, which would also
 * handle optional cost adjustments from {@link CostCalculationOptions} (like
 * `installationCost`, `additionalCost`, `discountRate`, `taxRate`).
 * **Note:** The import for `calculateUnitBasedCost` from `../../../../lib/utils/cost/costUtils`
 * is currently commented out as the module was not found. A placeholder calculation is used.
 *
 * The `getElementType()` method returns a generic string 'unit_based_cost', indicating
 * its applicability to any element type priced per unit. The {@link StrategyRegistry}
 * would map specific {@link ElementType}s (e.g., `DOOR`, `WINDOW`) to this strategy.
 *
 * @module core/compute/strategies/cost/UnitBasedCostStrategy
 * @see {@link CostCalculatorStrategy}
 * @see {@link CostCalculationOptions}
 */
import type { CostCalculationOptions, CostCalculatorStrategy } from '../../../../types/core/compute'
import type { Element } from '../../../../types/core/elementDefinitions'
// TODO: Cannot find module '../../../../lib/utils/cost/costUtils' or its corresponding type declarations.
// import { calculateUnitBasedCost } from '../../../../lib/utils/cost/costUtils';
import { CoreError } from '../../../../services/system/error-service/coreError'
import { ErrorType } from '../../../../types/services/errors'

export class UnitBasedCostStrategy implements CostCalculatorStrategy {
  /**
   * Calculates the cost for an element based on the number of units and a per-unit cost.
   *
   * @param element - The element for which to calculate the cost. Used primarily for context (ID, type) in error messages.
   * @param unitCost - The cost for a single unit of the element.
   * @param options - Optional {@link CostCalculationOptions}. Key properties include:
   *                  `quantity` (number of units; defaults to 1 if not provided or derivable),
   *                  `installationCost` (per unit or total, context-dependent),
   *                  `additionalCost`, `discountRate`, `taxRate`.
   * @returns The total calculated cost.
   * @throws {@link CoreError} if `unitCost` is invalid (e.g., negative).
   * @throws {@link CoreError} if `quantity` (from options or default) is invalid (e.g., zero or negative).
   */
  public calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
    if (typeof unitCost !== 'number' || unitCost < 0) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Unit cost must be a non-negative number, got ${unitCost}`,
        undefined,
        { component: 'UnitBasedCostStrategy', operation: 'calculateCost', target: element.id, metadata: { unitCost } },
      )
    }

    let quantity: number

    if (options?.quantity !== undefined && typeof options.quantity === 'number') {
      quantity = options.quantity
    }
    else {
      // 尝试从元素的 properties 中获取 costMultiplierOrCount
      const el = element as { properties?: Record<string, unknown>, costMultiplierOrCount?: number }
      const props = el.properties ?? {}

      if (typeof props.costMultiplierOrCount === 'number') {
        quantity = props.costMultiplierOrCount
      }
      else if (typeof el.costMultiplierOrCount === 'number') {
        quantity = el.costMultiplierOrCount
      }
      else {
        // 默认为1，确保有合理的初始乘数
        quantity = 1
      }
    }

    if (quantity < 0 || !Number.isFinite(quantity)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Quantity must be a non-negative finite number, got ${quantity} for element ${element.id}`,
        undefined,
        { component: 'UnitBasedCostStrategy', operation: 'calculateCost', target: element.id, metadata: { quantity } },
      )
    }

    // return calculateUnitBasedCost(quantity, unitCost, {
    //   installationCost: options?.installationCost,
    //   additionalCost: options?.additionalCost,
    //   discountRate: options?.discountRate,
    //   taxRate: options?.taxRate,
    // });
    // Placeholder implementation:
    let totalCost = quantity * unitCost
    if (options?.installationCost != null && typeof options.installationCost === 'number') { // Assuming installationCost is per unit if not specified otherwise
      totalCost += quantity * options.installationCost
    }
    if (options?.additionalCost != null && typeof options.additionalCost === 'number') {
      totalCost += options.additionalCost
    }
    if (options?.discountRate != null && typeof options.discountRate === 'number') {
      totalCost *= (1 - (options.discountRate / 100)) // Assuming discountRate is a percentage
    }
    if (options?.taxRate != null && typeof options.taxRate === 'number') {
      totalCost *= (1 + (options.taxRate / 100)) // Assuming taxRate is a percentage
    }
    return totalCost
  }

  /**
   * Returns a string identifier indicating the general applicability of this strategy.
   *
   * @remarks
   * This strategy is designed for elements whose cost is primarily determined by
   * their quantity (e.g., doors, windows, fixtures). It returns a generic identifier
   * 'unit_based_cost'. The {@link StrategyRegistry} would be responsible for mapping
   * specific {@link ElementType}s to this strategy.
   *
   * @returns The string 'unit_based_cost'.
   */
  public getElementType(): string {
    // This strategy is generic for elements that are costed per unit.
    return 'unit_based_cost'
  }
}
