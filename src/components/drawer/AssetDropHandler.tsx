/**
 * Asset Drop Handler Component
 *
 * A context provider component that manages the drag-and-drop functionality for assets
 * from the sidebar to the canvas. This component handles the complete workflow from
 * asset drop to shape creation, including event publishing and state management.
 *
 * Features:
 * - React Context API for drag-and-drop state management
 * - Event-driven architecture for shape creation
 * - Type-safe asset to shape conversion
 * - Integration with the shapes store for state persistence
 * - Comprehensive error handling and validation
 * - Custom hook for easy context consumption
 *
 * Workflow:
 * 1. User drags an asset from the sidebar
 * 2. Asset is dropped at a specific canvas position
 * 3. Component publishes a ShapeCreateRequest event
 * 4. Listens for ShapeCreateComplete event
 * 5. Validates and adds the created shape to the store
 *
 * @example
 * ```tsx
 * <AssetDropHandler>
 *   <Canvas />
 *   <Sidebar />
 * </AssetDropHandler>
 *
 * // In a child component:
 * const handleDrop = useAssetDrop();
 * handleDrop(asset, { x: 100, y: 200, z: 0 });
 * ```
 */

import type { SidebarAsset } from '@/types/core/assetTypes'
import type Point from '@/types/core/element/geometry/point'
import type { ShapeElement } from '@/types/core/elementDefinitions'
import type { AppEventMap, EventBus } from '@/types/services/events'
// Used for context, but guard input is unknown
import React, { useCallback, useEffect } from 'react'
import { getService, ServiceId } from '@/services/core/registry'
import { useShapesStore } from '@/store/shapesStore'
import { AppEventType } from '@/types/services/events/eventTypes'

/**
 * Type definition for the asset drop context function
 */
export type AssetDropContextType = (asset: SidebarAsset, position: Point) => void

/**
 * React context for providing asset drop functionality throughout the component tree
 */
// eslint-disable-next-line react-refresh/only-export-components
export const AssetDropContext = React.createContext<AssetDropContextType | undefined>(
  undefined,
)

/**
 * Props for the AssetDropHandler component
 */
interface AssetDropHandlerProps {
  /** Child components that will have access to the asset drop context */
  children: React.ReactNode
}

/**
 * Type guard function to validate if an unknown object is a valid ShapeElement.
 *
 * This function performs comprehensive runtime type checking to ensure that
 * objects received from events or external sources conform to the ShapeElement
 * interface before they are processed or added to the store.
 *
 * @param obj - The object to validate
 * @returns True if the object is a valid ShapeElement, false otherwise
 */
function isShapeElement(obj: unknown): obj is ShapeElement {
  if (typeof obj !== 'object' || obj === null) {
    return false
  }

  const shape = obj as Record<string, unknown>

  // Basic Shape properties (from Shape interface in shapeEvents.ts)
  if (
    typeof shape.id !== 'string'
    || typeof shape.type !== 'string' // ElementType is a string enum
    || typeof shape.x !== 'number'
    || typeof shape.y !== 'number'
    // width, height, rotation are optional in Shape interface
  ) {
    return false
  }

  // Additional ShapeElement specific properties (verify based on ShapeElement definition)
  if (
    typeof shape.name !== 'string'
    || typeof shape.rotation !== 'number' // rotation is also in Shape, but might be stricter in ShapeElement
    || typeof shape.locked !== 'boolean'
    || typeof shape.visible !== 'boolean'
    || typeof shape.isGenerated !== 'boolean'
    || typeof shape.canTransform !== 'boolean'
    || typeof shape.canSelect !== 'boolean'
    || typeof shape.properties !== 'object' || shape.properties === null
    // zLevelId and zIndex are optional in ShapeElement, so check if present or undefined
    // No need to check typeof undefined explicitly, (typeof shape.zLevelId === 'string' || shape.zLevelId === undefined)
  ) {
    return false
  }

  // Optional properties check (if they exist, they must be of the correct type)
  if (Object.prototype.hasOwnProperty.call(shape, 'zLevelId') && typeof shape.zLevelId !== 'string') {
    return false
  }
  if (Object.prototype.hasOwnProperty.call(shape, 'zIndex') && typeof shape.zIndex !== 'number') {
    return false
  }
  // Example for a potentially nested mandatory property if 'properties' had a strict structure
  // if (typeof shape.properties.someMandatoryProp !== 'expectedType') return false;

  return true
}

/**
 * AssetDropHandler component that provides drag-and-drop functionality for assets.
 *
 * This component serves as a context provider that manages the complete asset drop
 * workflow. It handles event publishing for shape creation requests and listens
 * for completion events to update the application state.
 *
 * The component integrates with the event bus system and shapes store to provide
 * a seamless drag-and-drop experience from the asset sidebar to the canvas.
 *
 * @param props - The component props
 * @param props.children - Child components that will have access to drop functionality
 * @returns The context provider with asset drop capabilities
 */
const AssetDropHandler: React.FC<AssetDropHandlerProps> = ({ children }) => {
  const eventBus = (() => {
    try {
      return getService<EventBus<AppEventMap>>(ServiceId.EventBus)
    }
    catch {
      return null
    }
  })()
  const addShape = useShapesStore(state => state.addShape)

  const handleAssetDrop = useCallback((asset: SidebarAsset, position: Point) => {
    if (asset == null || position == null || eventBus == null) {
      return
    }
    const payload = {
      elementType: asset.elementType,
      position,
      properties: asset.properties ?? {},
    }
    eventBus.publish({
      type: AppEventType.ShapeCreateRequest,
      timestamp: Date.now(),
      payload,
    })
  }, [eventBus])

  useEffect(() => {
    if (eventBus == null) {
      return
    }
    const unsubscribe = eventBus.subscribe(AppEventType.ShapeCreateComplete, (event) => {
      // event.payload.shape is of type Shape | undefined from shapeEvents.ts
      const receivedShape = event.payload.shape

      if (receivedShape != null && isShapeElement(receivedShape)) {
        // receivedShape is now safely typed as ShapeElement
        addShape(receivedShape)
      }
      else {
        console.error('[AssetDropHandler] Invalid or incomplete shape object received in ShapeCreateComplete event:', receivedShape)
      }
    })

    return () => {
      if (typeof unsubscribe === 'function') {
        unsubscribe()
      }
    }
  }, [eventBus, addShape])

  return (
    <AssetDropContext value={handleAssetDrop}>
      {children}
    </AssetDropContext>
  )
}

/**
 * Custom hook to access the asset drop functionality from the AssetDropContext.
 *
 * This hook provides a convenient way for child components to access the asset
 * drop handler function. It includes proper error handling to ensure the hook
 * is only used within the appropriate context provider.
 *
 * @throws {Error} If used outside of an AssetDropHandler provider
 * @returns The asset drop handler function
 *
 * @example
 * ```tsx
 * function CanvasComponent() {
 *   const handleAssetDrop = useAssetDrop();
 *
 *   const onDrop = (event) => {
 *     const asset = getAssetFromEvent(event);
 *     const position = getPositionFromEvent(event);
 *     handleAssetDrop(asset, position);
 *   };
 *
 *   return <div onDrop={onDrop}>Canvas</div>;
 * }
 * ```
 */
// eslint-disable-next-line react-refresh/only-export-components
export function useAssetDrop(): AssetDropContextType {
  // eslint-disable-next-line react/no-use-context
  const context = React.useContext(AssetDropContext)
  if (context === undefined) {
    throw new Error('useAssetDrop must be used within an AssetDropHandler')
  }
  return context
}

export default AssetDropHandler
