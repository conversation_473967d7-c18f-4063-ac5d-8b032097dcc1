import type {
  ValidationError,
  ValidationOptions,
  ValidationResult,
  ValidationRule,
  ValidationService,
} from '@/types/services/validation'
import { describe, expect, it, vi } from 'vitest'

describe('validation Types', () => {
  describe('validationRule', () => {
    it('should allow creating a validation rule', () => {
      const rule: ValidationRule = {
        id: 'required-field',
        description: 'Field is required',
        validate: vi.fn((value) => {
          if (value === undefined || value === null || value === '') {
            return {
              valid: false,
              errors: [{ message: 'Field is required' }],
            }
          }
          return { valid: true }
        }),
      }

      expect(rule.id).toBe('required-field')
      expect(rule.description).toBe('Field is required')
      expect(typeof rule.validate).toBe('function')

      // Test the validation function
      const invalidResult = rule.validate('')
      expect(invalidResult.valid).toBe(false)
      expect(invalidResult.errors).toHaveLength(1)
      expect(invalidResult.errors?.[0].message).toBe('Field is required')

      const validResult = rule.validate('test')
      expect(validResult.valid).toBe(true)
      expect(validResult.errors).toBeUndefined()
    })

    it('should allow creating a rule with a category', () => {
      const rule: ValidationRule = {
        id: 'positive-number',
        description: 'Number must be positive',
        category: 'number-validation',
        validate: vi.fn((value) => {
          if (typeof value !== 'number' || value <= 0) {
            return {
              valid: false,
              errors: [{ message: 'Number must be positive' }],
            }
          }
          return { valid: true }
        }),
      }

      expect(rule.id).toBe('positive-number')
      expect(rule.description).toBe('Number must be positive')
      expect(rule.category).toBe('number-validation')

      // Test the validation function
      const invalidResult = rule.validate(-5)
      expect(invalidResult.valid).toBe(false)
      expect(invalidResult.errors).toHaveLength(1)

      const validResult = rule.validate(10)
      expect(validResult.valid).toBe(true)
    })
  })

  describe('validationResult', () => {
    it('should allow creating a valid result', () => {
      const result: ValidationResult = {
        valid: true,
      }

      expect(result.valid).toBe(true)
      expect(result.errors).toBeUndefined()
    })

    it('should allow creating an invalid result with errors', () => {
      const result: ValidationResult = {
        valid: false,
        errors: [
          { message: 'Field is required' },
          { message: 'Value must be a number', field: 'age' },
        ],
      }

      expect(result.valid).toBe(false)
      expect(result.errors).toHaveLength(2)
      expect(result.errors?.[0].message).toBe('Field is required')
      expect(result.errors?.[1].message).toBe('Value must be a number')
      expect(result.errors?.[1].field).toBe('age')
    })
  })

  describe('validationError', () => {
    it('should allow creating a basic error', () => {
      const error: ValidationError = {
        message: 'Validation failed',
      }

      expect(error.message).toBe('Validation failed')
      expect(error.field).toBeUndefined()
      expect(error.code).toBeUndefined()
      expect(error.details).toBeUndefined()
    })

    it('should allow creating a detailed error', () => {
      const error: ValidationError = {
        message: 'Invalid email format',
        field: 'email',
        code: 'INVALID_FORMAT',
        details: { format: 'email', value: 'test' },
      }

      expect(error.message).toBe('Invalid email format')
      expect(error.field).toBe('email')
      expect(error.code).toBe('INVALID_FORMAT')
      expect(error.details).toBeDefined()
      expect(error.details?.format).toBe('email')
      expect(error.details?.value).toBe('test')
    })
  })

  describe('validationOptions', () => {
    it('should allow creating options with all properties', () => {
      const options: ValidationOptions = {
        stopOnFirstError: true,
        context: { mode: 'strict' },
        rules: ['required', 'format', 'range'],
      }

      expect(options.stopOnFirstError).toBe(true)
      expect(options.context).toBeDefined()
      expect(options.context?.mode).toBe('strict')
      expect(options.rules).toHaveLength(3)
      expect(options.rules?.[0]).toBe('required')
      expect(options.rules?.[1]).toBe('format')
      expect(options.rules?.[2]).toBe('range')
    })

    it('should allow creating options with minimal properties', () => {
      const options: ValidationOptions = {}

      expect(options.stopOnFirstError).toBeUndefined()
      expect(options.context).toBeUndefined()
      expect(options.rules).toBeUndefined()
    })
  })

  describe('validationService', () => {
    it('should define all required methods', () => {
      // Create a mock implementation of ValidationService
      const mockValidationService: ValidationService = {
        registerRule: vi.fn(),
        unregisterRule: vi.fn(),
        validate: vi.fn(),
        validateField: vi.fn(),
        getRegisteredRules: vi.fn(),
      }

      expect(typeof mockValidationService.registerRule).toBe('function')
      expect(typeof mockValidationService.unregisterRule).toBe('function')
      expect(typeof mockValidationService.validate).toBe('function')
      expect(typeof mockValidationService.validateField).toBe('function')
      expect(typeof mockValidationService.getRegisteredRules).toBe('function')
    })

    it('should allow calling validate method', () => {
      const mockValidationService: ValidationService = {
        registerRule: vi.fn(),
        unregisterRule: vi.fn(),
        validate: vi.fn().mockReturnValue({ valid: true }),
        validateField: vi.fn(),
        getRegisteredRules: vi.fn(),
      }

      const data = { name: 'Test', age: 25 }
      const options: ValidationOptions = { stopOnFirstError: true }

      const result = mockValidationService.validate(data, options)

      expect(mockValidationService.validate).toHaveBeenCalledWith(data, options)
      expect(result.valid).toBe(true)
    })

    it('should allow calling validateField method', () => {
      const mockValidationService: ValidationService = {
        registerRule: vi.fn(),
        unregisterRule: vi.fn(),
        validate: vi.fn(),
        validateField: vi.fn().mockReturnValue({ valid: false, errors: [{ message: 'Invalid value' }] }),
        getRegisteredRules: vi.fn(),
      }

      const value = -5
      const rules = ['positive-number']
      const options: ValidationOptions = { context: { mode: 'strict' } }

      const result = mockValidationService.validateField(value, rules, options)

      expect(mockValidationService.validateField).toHaveBeenCalledWith(value, rules, options)
      expect(result.valid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors?.[0].message).toBe('Invalid value')
    })
  })
})
