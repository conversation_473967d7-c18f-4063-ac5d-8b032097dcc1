import type { ServiceRegistry } from '@/services/core/registry'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { getServiceFactory } from '@/services/core/registry'
import { registerValidationService } from '@/services/core/validation/registry'
import { ServiceId } from '@/types/services/core/serviceIdentifier'

// Mock dependencies
vi.mock('@/services/core/registry', () => ({
  getServiceFactory: vi.fn(),
}))

describe('validation Registry', () => {
  let mockRegistry: ServiceRegistry
  let mockLogger: LoggerService
  let mockServiceFactory: any
  let mockValidationService: any

  beforeEach(() => {
    mockValidationService = {
      validateElement: vi.fn(),
      validateElements: vi.fn(),
      initializeValidatorModules: vi.fn(),
    }

    mockServiceFactory = {
      createValidationService: vi.fn().mockReturnValue(mockValidationService),
    }

    mockRegistry = {
      register: vi.fn(),
      get: vi.fn(),
      has: vi.fn(),
      unregister: vi.fn(),
      clear: vi.fn(),
      getAll: vi.fn(),
    }

    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    }

    vi.mocked(getServiceFactory).mockReturnValue(mockServiceFactory)
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('registerValidationService', () => {
    it('should register validation service successfully', () => {
      registerValidationService(mockRegistry, mockLogger)

      expect(getServiceFactory).toHaveBeenCalled()
      expect(mockServiceFactory.createValidationService).toHaveBeenCalledWith(mockLogger)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ValidationService,
        mockValidationService,
      )
      expect(mockLogger.info).toHaveBeenCalledWith('验证服务注册成功')
    })

    it('should create validation service with provided logger', () => {
      const customLogger = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
      }

      registerValidationService(mockRegistry, customLogger)

      expect(mockServiceFactory.createValidationService).toHaveBeenCalledWith(customLogger)
      expect(customLogger.info).toHaveBeenCalledWith('验证服务注册成功')
    })

    it('should handle service factory errors gracefully', () => {
      const error = new Error('Factory creation failed')
      mockServiceFactory.createValidationService.mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerValidationService(mockRegistry, mockLogger)
      }).toThrow(error)

      expect(getServiceFactory).toHaveBeenCalled()
      expect(mockServiceFactory.createValidationService).toHaveBeenCalledWith(mockLogger)
      expect(mockRegistry.register).not.toHaveBeenCalled()
      expect(mockLogger.info).not.toHaveBeenCalled()
    })

    it('should handle registry registration errors gracefully', () => {
      const error = new Error('Registration failed')
      mockRegistry.register.mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerValidationService(mockRegistry, mockLogger)
      }).toThrow(error)

      expect(mockServiceFactory.createValidationService).toHaveBeenCalledWith(mockLogger)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ValidationService,
        mockValidationService,
      )
      expect(mockLogger.info).not.toHaveBeenCalled()
    })

    it('should handle getServiceFactory errors gracefully', () => {
      const error = new Error('Service factory not available')
      vi.mocked(getServiceFactory).mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerValidationService(mockRegistry, mockLogger)
      }).toThrow(error)

      expect(getServiceFactory).toHaveBeenCalled()
      expect(mockRegistry.register).not.toHaveBeenCalled()
      expect(mockLogger.info).not.toHaveBeenCalled()
    })

    it('should work with different logger implementations', () => {
      const loggers = [
        {
          info: vi.fn(),
          warn: vi.fn(),
          error: vi.fn(),
          debug: vi.fn(),
        },
        {
          info: vi.fn(),
          warn: vi.fn(),
          error: vi.fn(),
          debug: vi.fn(),
          trace: vi.fn(), // Additional method
        },
      ]

      loggers.forEach((logger, index) => {
        vi.clearAllMocks()

        registerValidationService(mockRegistry, logger)

        expect(mockServiceFactory.createValidationService).toHaveBeenCalledWith(logger)
        expect(logger.info).toHaveBeenCalledWith('验证服务注册成功')
      })
    })

    it('should work with different registry implementations', () => {
      const registries = [
        {
          register: vi.fn(),
          get: vi.fn(),
          has: vi.fn(),
          unregister: vi.fn(),
          clear: vi.fn(),
          getAll: vi.fn(),
        },
        {
          register: vi.fn(),
          get: vi.fn(),
          has: vi.fn(),
          unregister: vi.fn(),
          clear: vi.fn(),
          getAll: vi.fn(),
          size: vi.fn(), // Additional method
        },
      ]

      registries.forEach((registry, index) => {
        vi.clearAllMocks()

        registerValidationService(registry, mockLogger)

        expect(registry.register).toHaveBeenCalledWith(
          ServiceId.ValidationService,
          mockValidationService,
        )
        expect(mockLogger.info).toHaveBeenCalledWith('验证服务注册成功')
      })
    })

    it('should handle multiple registration calls', () => {
      // First registration
      registerValidationService(mockRegistry, mockLogger)

      expect(mockRegistry.register).toHaveBeenCalledTimes(1)
      expect(mockLogger.info).toHaveBeenCalledTimes(1)

      // Second registration (should work independently)
      registerValidationService(mockRegistry, mockLogger)

      expect(mockRegistry.register).toHaveBeenCalledTimes(2)
      expect(mockLogger.info).toHaveBeenCalledTimes(2)
      expect(mockServiceFactory.createValidationService).toHaveBeenCalledTimes(2)
    })

    it('should create new validation service instance for each registration', () => {
      const service1 = { validateElement: vi.fn(), id: 'service1' }
      const service2 = { validateElement: vi.fn(), id: 'service2' }

      mockServiceFactory.createValidationService
        .mockReturnValueOnce(service1)
        .mockReturnValueOnce(service2)

      registerValidationService(mockRegistry, mockLogger)
      expect(mockRegistry.register).toHaveBeenCalledWith(ServiceId.ValidationService, service1)

      registerValidationService(mockRegistry, mockLogger)
      expect(mockRegistry.register).toHaveBeenCalledWith(ServiceId.ValidationService, service2)
    })

    it('should preserve service creation order', () => {
      const callOrder: string[] = []

      vi.mocked(getServiceFactory).mockImplementation(() => {
        callOrder.push('getServiceFactory')
        return mockServiceFactory
      })

      mockServiceFactory.createValidationService.mockImplementation((logger: LoggerService) => {
        callOrder.push('createValidationService')
        return mockValidationService
      })

      mockRegistry.register.mockImplementation(() => {
        callOrder.push('register')
      })

      mockLogger.info.mockImplementation(() => {
        callOrder.push('info')
      })

      registerValidationService(mockRegistry, mockLogger)

      expect(callOrder).toEqual([
        'getServiceFactory',
        'createValidationService',
        'register',
        'info',
      ])
    })

    it('should handle edge cases with service identifiers', () => {
      registerValidationService(mockRegistry, mockLogger)

      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ValidationService,
        mockValidationService,
      )

      // Verify the service ID is the correct one
      const registrationCall = mockRegistry.register.mock.calls[0]
      expect(registrationCall[0]).toBe(ServiceId.ValidationService)
      expect(typeof registrationCall[0]).toBe('string')
    })

    it('should handle logger method failures gracefully', () => {
      const faultyLogger = {
        info: vi.fn().mockImplementation(() => {
          throw new Error('Logging failed')
        }),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
      }

      expect(() => {
        registerValidationService(mockRegistry, faultyLogger)
      }).toThrow('Logging failed')

      // Service should still be registered even if logging fails
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ValidationService,
        mockValidationService,
      )
    })

    it('should work with minimal logger interface', () => {
      const minimalLogger = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
      }

      registerValidationService(mockRegistry, minimalLogger)

      expect(mockServiceFactory.createValidationService).toHaveBeenCalledWith(minimalLogger)
      expect(minimalLogger.info).toHaveBeenCalledWith('验证服务注册成功')
    })

    it('should handle service factory returning null or undefined', () => {
      mockServiceFactory.createValidationService.mockReturnValue(null)

      registerValidationService(mockRegistry, mockLogger)

      expect(mockRegistry.register).toHaveBeenCalledWith(ServiceId.ValidationService, null)
      expect(mockLogger.info).toHaveBeenCalledWith('验证服务注册成功')

      // Test with undefined
      vi.clearAllMocks()
      mockServiceFactory.createValidationService.mockReturnValue(undefined)

      registerValidationService(mockRegistry, mockLogger)

      expect(mockRegistry.register).toHaveBeenCalledWith(ServiceId.ValidationService, undefined)
      expect(mockLogger.info).toHaveBeenCalledWith('验证服务注册成功')
    })

    it('should handle complex validation service objects', () => {
      const complexValidationService = {
        validateElement: vi.fn(),
        validateElements: vi.fn(),
        initializeValidatorModules: vi.fn(),
        getValidatorConfig: vi.fn(),
        setValidatorConfig: vi.fn(),
        metadata: {
          version: '1.0.0',
          capabilities: ['element', 'batch'],
        },
      }

      mockServiceFactory.createValidationService.mockReturnValue(complexValidationService)

      registerValidationService(mockRegistry, mockLogger)

      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ValidationService,
        complexValidationService,
      )
      expect(mockLogger.info).toHaveBeenCalledWith('验证服务注册成功')
    })
  })

  describe('integration scenarios', () => {
    it('should work in a typical application initialization flow', () => {
      // Simulate application startup
      registerValidationService(mockRegistry, mockLogger)

      // Verify the complete flow
      expect(getServiceFactory).toHaveBeenCalled()
      expect(mockServiceFactory.createValidationService).toHaveBeenCalledWith(mockLogger)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.ValidationService,
        mockValidationService,
      )
      expect(mockLogger.info).toHaveBeenCalledWith('验证服务注册成功')
    })

    it('should handle service replacement scenarios', () => {
      // Initial registration
      registerValidationService(mockRegistry, mockLogger)

      const initialService = mockServiceFactory.createValidationService.mock.results[0].value

      // Service replacement
      const newService = { validateElement: vi.fn(), id: 'new-service' }
      mockServiceFactory.createValidationService.mockReturnValue(newService)

      registerValidationService(mockRegistry, mockLogger)

      expect(mockRegistry.register).toHaveBeenCalledTimes(2)
      expect(mockRegistry.register).toHaveBeenNthCalledWith(1, ServiceId.ValidationService, initialService)
      expect(mockRegistry.register).toHaveBeenNthCalledWith(2, ServiceId.ValidationService, newService)
    })
  })
})
