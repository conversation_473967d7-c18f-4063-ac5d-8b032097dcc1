import type Point from '@/types/core/element/geometry/point'
import type { HandleActionType, HandleModel } from '@/types/core/handleTypes'
import { describe, expect, it } from 'vitest'

describe('handle Types Module', () => {
  describe('handleActionType', () => {
    it('should define all standard handle action types', () => {
      // Define a function that uses the HandleActionType
      function getHandleDescription(action: HandleActionType): string {
        switch (action) {
          case 'resize-corner-tl':
            return 'Resize from top-left corner'
          case 'resize-corner-tr':
            return 'Resize from top-right corner'
          case 'resize-corner-bl':
            return 'Resize from bottom-left corner'
          case 'resize-corner-br':
            return 'Resize from bottom-right corner'
          case 'resize-edge-t':
            return 'Resize from top edge'
          case 'resize-edge-r':
            return 'Resize from right edge'
          case 'resize-edge-b':
            return 'Resize from bottom edge'
          case 'resize-edge-l':
            return 'Resize from left edge'
          case 'move-endpoint-start':
            return 'Move start point'
          case 'move-endpoint-end':
            return 'Move end point'
          case 'rotate':
            return 'Rotate shape'
          case 'move':
            return 'Move entire shape'
          case 'custom':
            return 'Custom action'
          default:
            return 'Unknown action'
        }
      }

      // Test all action types
      expect(getHandleDescription('resize-corner-tl')).toBe('Resize from top-left corner')
      expect(getHandleDescription('resize-corner-tr')).toBe('Resize from top-right corner')
      expect(getHandleDescription('resize-corner-bl')).toBe('Resize from bottom-left corner')
      expect(getHandleDescription('resize-corner-br')).toBe('Resize from bottom-right corner')
      expect(getHandleDescription('resize-edge-t')).toBe('Resize from top edge')
      expect(getHandleDescription('resize-edge-r')).toBe('Resize from right edge')
      expect(getHandleDescription('resize-edge-b')).toBe('Resize from bottom edge')
      expect(getHandleDescription('resize-edge-l')).toBe('Resize from left edge')
      expect(getHandleDescription('move-endpoint-start')).toBe('Move start point')
      expect(getHandleDescription('move-endpoint-end')).toBe('Move end point')
      expect(getHandleDescription('rotate')).toBe('Rotate shape')
      expect(getHandleDescription('move')).toBe('Move entire shape')
      expect(getHandleDescription('custom')).toBe('Custom action')
    })

    it('should be usable as object keys', () => {
      // Create an object using HandleActionType values as keys
      const handlePriorities: Record<HandleActionType, number> = {
        'resize-corner-tl': 1,
        'resize-corner-tr': 2,
        'resize-corner-bl': 3,
        'resize-corner-br': 4,
        'resize-edge-t': 5,
        'resize-edge-r': 6,
        'resize-edge-b': 7,
        'resize-edge-l': 8,
        'move-endpoint-start': 9,
        'move-endpoint-end': 10,
        'rotate': 11,
        'move': 12,
        'custom': 13,
      }

      // Verify that we can access values using HandleActionType keys
      expect(handlePriorities['resize-corner-tl']).toBe(1)
      expect(handlePriorities['resize-corner-tr']).toBe(2)
      expect(handlePriorities['resize-corner-bl']).toBe(3)
      expect(handlePriorities['resize-corner-br']).toBe(4)
      expect(handlePriorities['resize-edge-t']).toBe(5)
      expect(handlePriorities['resize-edge-r']).toBe(6)
      expect(handlePriorities['resize-edge-b']).toBe(7)
      expect(handlePriorities['resize-edge-l']).toBe(8)
      expect(handlePriorities['move-endpoint-start']).toBe(9)
      expect(handlePriorities['move-endpoint-end']).toBe(10)
      expect(handlePriorities.rotate).toBe(11)
      expect(handlePriorities.move).toBe(12)
      expect(handlePriorities.custom).toBe(13)
    })

    it('should be usable in arrays', () => {
      // Create an array of HandleActionType values
      const cornerActions: HandleActionType[] = [
        'resize-corner-tl',
        'resize-corner-tr',
        'resize-corner-bl',
        'resize-corner-br',
      ]

      const edgeActions: HandleActionType[] = [
        'resize-edge-t',
        'resize-edge-r',
        'resize-edge-b',
        'resize-edge-l',
      ]

      const endpointActions: HandleActionType[] = [
        'move-endpoint-start',
        'move-endpoint-end',
      ]

      const otherActions: HandleActionType[] = [
        'rotate',
        'move',
        'custom',
      ]

      // Verify array contents
      expect(cornerActions).toHaveLength(4)
      expect(cornerActions).toContain('resize-corner-tl')
      expect(cornerActions).toContain('resize-corner-tr')
      expect(cornerActions).toContain('resize-corner-bl')
      expect(cornerActions).toContain('resize-corner-br')

      expect(edgeActions).toHaveLength(4)
      expect(edgeActions).toContain('resize-edge-t')
      expect(edgeActions).toContain('resize-edge-r')
      expect(edgeActions).toContain('resize-edge-b')
      expect(edgeActions).toContain('resize-edge-l')

      expect(endpointActions).toHaveLength(2)
      expect(endpointActions).toContain('move-endpoint-start')
      expect(endpointActions).toContain('move-endpoint-end')

      expect(otherActions).toHaveLength(3)
      expect(otherActions).toContain('rotate')
      expect(otherActions).toContain('move')
      expect(otherActions).toContain('custom')
    })
  })

  describe('handleModel Interface', () => {
    it('should define a valid handle model with required properties', () => {
      // Create a point for the handle position
      const position: Point = { x: 100, y: 100 }

      // Create a handle model with required properties
      const handle: HandleModel = {
        id: 'handle-1',
        shapeId: 'shape-1',
        action: 'resize-corner-tl',
        position,
      }

      // Verify the handle has the expected properties
      expect(handle.id).toBe('handle-1')
      expect(handle.shapeId).toBe('shape-1')
      expect(handle.action).toBe('resize-corner-tl')
      expect(handle.position).toBe(position)
      expect(handle.position.x).toBe(100)
      expect(handle.position.y).toBe(100)
    })

    it('should define a valid handle model with all properties', () => {
      // Create a point for the handle position
      const position: Point = { x: 200, y: 200 }

      // Create a handle model with all properties
      const handle: HandleModel = {
        id: 'handle-2',
        shapeId: 'shape-2',
        action: 'rotate',
        position,
        visible: true,
        active: false,
        data: {
          rotationCenter: { x: 150, y: 150 },
          rotationAngle: 45,
        },
      }

      // Verify the handle has the expected properties
      expect(handle.id).toBe('handle-2')
      expect(handle.shapeId).toBe('shape-2')
      expect(handle.action).toBe('rotate')
      expect(handle.position).toBe(position)
      expect(handle.position.x).toBe(200)
      expect(handle.position.y).toBe(200)
      expect(handle.visible).toBe(true)
      expect(handle.active).toBe(false)
      expect(handle.data).toEqual({
        rotationCenter: { x: 150, y: 150 },
        rotationAngle: 45,
      })
    })

    it('should support optional properties', () => {
      // Create a point for the handle position
      const position: Point = { x: 300, y: 300 }

      // Create a handle model with only required properties
      const handle: HandleModel = {
        id: 'handle-3',
        shapeId: 'shape-3',
        action: 'move',
        position,
      }

      // Verify the handle has the expected properties
      expect(handle.id).toBe('handle-3')
      expect(handle.shapeId).toBe('shape-3')
      expect(handle.action).toBe('move')
      expect(handle.position).toBe(position)
      expect(handle.visible).toBeUndefined()
      expect(handle.active).toBeUndefined()
      expect(handle.data).toBeUndefined()
    })

    it('should be usable in collections', () => {
      // Create points for handle positions
      const position1: Point = { x: 100, y: 100 }
      const position2: Point = { x: 200, y: 100 }
      const position3: Point = { x: 100, y: 200 }
      const position4: Point = { x: 200, y: 200 }

      // Create an array of handle models
      const handles: HandleModel[] = [
        {
          id: 'handle-tl',
          shapeId: 'rectangle-1',
          action: 'resize-corner-tl',
          position: position1,
        },
        {
          id: 'handle-tr',
          shapeId: 'rectangle-1',
          action: 'resize-corner-tr',
          position: position2,
        },
        {
          id: 'handle-bl',
          shapeId: 'rectangle-1',
          action: 'resize-corner-bl',
          position: position3,
        },
        {
          id: 'handle-br',
          shapeId: 'rectangle-1',
          action: 'resize-corner-br',
          position: position4,
        },
      ]

      // Verify the array has the expected handles
      expect(handles).toHaveLength(4)
      expect(handles[0].id).toBe('handle-tl')
      expect(handles[0].action).toBe('resize-corner-tl')
      expect(handles[1].id).toBe('handle-tr')
      expect(handles[1].action).toBe('resize-corner-tr')
      expect(handles[2].id).toBe('handle-bl')
      expect(handles[2].action).toBe('resize-corner-bl')
      expect(handles[3].id).toBe('handle-br')
      expect(handles[3].action).toBe('resize-corner-br')

      // All handles should be for the same shape
      expect(handles.every(h => h.shapeId === 'rectangle-1')).toBe(true)
    })

    it('should be usable in functions that require HandleModel', () => {
      // Create a function that uses HandleModel
      function isCornerHandle(handle: HandleModel): boolean {
        return handle.action.startsWith('resize-corner')
      }

      function isEdgeHandle(handle: HandleModel): boolean {
        return handle.action.startsWith('resize-edge')
      }

      function isEndpointHandle(handle: HandleModel): boolean {
        return handle.action.startsWith('move-endpoint')
      }

      // Create test handles
      const cornerHandle: HandleModel = {
        id: 'corner-handle',
        shapeId: 'shape-1',
        action: 'resize-corner-tl',
        position: { x: 100, y: 100 },
      }

      const edgeHandle: HandleModel = {
        id: 'edge-handle',
        shapeId: 'shape-1',
        action: 'resize-edge-t',
        position: { x: 150, y: 100 },
      }

      const endpointHandle: HandleModel = {
        id: 'endpoint-handle',
        shapeId: 'shape-2',
        action: 'move-endpoint-start',
        position: { x: 200, y: 200 },
      }

      const otherHandle: HandleModel = {
        id: 'other-handle',
        shapeId: 'shape-3',
        action: 'rotate',
        position: { x: 300, y: 300 },
      }

      // Test the functions
      expect(isCornerHandle(cornerHandle)).toBe(true)
      expect(isCornerHandle(edgeHandle)).toBe(false)
      expect(isCornerHandle(endpointHandle)).toBe(false)
      expect(isCornerHandle(otherHandle)).toBe(false)

      expect(isEdgeHandle(cornerHandle)).toBe(false)
      expect(isEdgeHandle(edgeHandle)).toBe(true)
      expect(isEdgeHandle(endpointHandle)).toBe(false)
      expect(isEdgeHandle(otherHandle)).toBe(false)

      expect(isEndpointHandle(cornerHandle)).toBe(false)
      expect(isEndpointHandle(edgeHandle)).toBe(false)
      expect(isEndpointHandle(endpointHandle)).toBe(true)
      expect(isEndpointHandle(otherHandle)).toBe(false)
    })
  })
})
