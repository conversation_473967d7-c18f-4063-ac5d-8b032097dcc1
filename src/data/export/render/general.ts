/**
 * General utility functions for SVG shape rendering.
 *
 * This module provides common helpers for extracting shape styles
 * and generating SVG transform attributes, which are shared by
 * various shape renderers.
 *
 * @module render/general
 */
import type { ShapeElement } from '@/types/core'
import type Point from '@/types/core/element/geometry/point'
import { getPattern } from './pattern'

export function getShapeStyle(e: ShapeElement) {
  let fill = e.fill || '#ccc'
  const stroke = e.stroke || '#333'
  const strokeWidth = e.strokeWidth || 1
  let patternStr: string = ''

  if (e.pattern) {
    const pattern = e.pattern
    const patternId = pattern.id
    if (patternId) {
      fill = `url(#${patternId})`
      patternStr = `${getPattern(pattern)}\n`
    }
  }

  return { fill, stroke, strokeWidth, patternStr }
}

export function getTransform(rotation: number, pos: Point) {
  return rotation ? ` transform="rotate(${rotation},${pos.x},${pos.y})"` : ''
}
