import type {
  BaseStyleProperties,
  Element,
  MetadataProperties,
  ShapeElement,
} from '@/types/core/elementDefinitions'
import { describe, expect, it } from 'vitest'
import {
  ElementCategories,
  ElementType,
  getElementCategory,
  isElementType,
  isInteriorDesignType,
  isPathType,
  isTextType,
} from '@/types/core/elementDefinitions'

describe('element Types Module', () => {
  describe('elementType Enum', () => {
    it('should define all basic shape types', () => {
      expect(ElementType.RECTANGLE).toBe('RECTANGLE')
      expect(ElementType.SQUARE).toBe('SQUARE')
      expect(ElementType.ELLIPSE).toBe('ELLIPSE')
      expect(ElementType.CIRCLE).toBe('CIRCLE')
    })

    it('should define all polygon types', () => {
      expect(ElementType.POLYGON).toBe('POLYGON')
      expect(ElementType.TRIANGLE).toBe('TRIANGLE')
      expect(ElementType.QUADRILATERAL).toBe('QUADRILATERAL')
      expect(ElementType.PENTAGON).toBe('PENTAGON')
      expect(ElementType.HEXAGON).toBe('HEXAGON')
      expect(ElementType.HEPTAGON).toBe('HEPTAGON')
      expect(ElementType.OCTAGON).toBe('OCTAGON')
      expect(ElementType.NONAGON).toBe('NONAGON')
      expect(ElementType.DECAGON).toBe('DECAGON')
    })

    it('should define all path types', () => {
      expect(ElementType.LINE).toBe('LINE')
      expect(ElementType.PATH).toBe('PATH')
      expect(ElementType.POLYLINE).toBe('POLYLINE')
      expect(ElementType.ARC).toBe('ARC')
      expect(ElementType.QUADRATIC).toBe('QUADRATIC')
      expect(ElementType.CUBIC).toBe('CUBIC')
    })

    it('should define all text types', () => {
      expect(ElementType.TEXT_LABEL).toBe('TEXT_LABEL')
    })

    it('should define all interior design types', () => {
      expect(ElementType.WALL).toBe('WALL')
      expect(ElementType.DOOR).toBe('DOOR')
      expect(ElementType.WINDOW).toBe('WINDOW')
      expect(ElementType.FURNITURE).toBe('FURNITURE')
      expect(ElementType.FIXTURE).toBe('FIXTURE')
      expect(ElementType.ROOM).toBe('ROOM')
    })
  })

  describe('elementCategories', () => {
    it('should group basic shapes correctly', () => {
      expect(ElementCategories.BASIC_SHAPES).toContain(ElementType.RECTANGLE)
      expect(ElementCategories.BASIC_SHAPES).toContain(ElementType.SQUARE)
      expect(ElementCategories.BASIC_SHAPES).toContain(ElementType.ELLIPSE)
      expect(ElementCategories.BASIC_SHAPES).toContain(ElementType.CIRCLE)
      expect(ElementCategories.BASIC_SHAPES).toHaveLength(4)
    })

    it('should group polygons correctly', () => {
      expect(ElementCategories.POLYGONS).toContain(ElementType.POLYGON)
      expect(ElementCategories.POLYGONS).toContain(ElementType.TRIANGLE)
      expect(ElementCategories.POLYGONS).toContain(ElementType.QUADRILATERAL)
      expect(ElementCategories.POLYGONS).toContain(ElementType.PENTAGON)
      expect(ElementCategories.POLYGONS).toContain(ElementType.HEXAGON)
      expect(ElementCategories.POLYGONS).toContain(ElementType.HEPTAGON)
      expect(ElementCategories.POLYGONS).toContain(ElementType.OCTAGON)
      expect(ElementCategories.POLYGONS).toContain(ElementType.NONAGON)
      expect(ElementCategories.POLYGONS).toContain(ElementType.DECAGON)
      expect(ElementCategories.POLYGONS).toHaveLength(9)
    })

    it('should group paths correctly', () => {
      expect(ElementCategories.PATHS).toContain(ElementType.LINE)
      expect(ElementCategories.PATHS).toContain(ElementType.PATH)
      expect(ElementCategories.PATHS).toContain(ElementType.POLYLINE)
      expect(ElementCategories.PATHS).toContain(ElementType.ARC)
      expect(ElementCategories.PATHS).toContain(ElementType.QUADRATIC)
      expect(ElementCategories.PATHS).toContain(ElementType.CUBIC)
      expect(ElementCategories.PATHS).toHaveLength(6)
    })

    it('should group text correctly', () => {
      expect(ElementCategories.TEXT).toContain(ElementType.TEXT_LABEL)
      expect(ElementCategories.TEXT).toHaveLength(1)
    })

    it('should group interior design elements correctly', () => {
      expect(ElementCategories.INTERIOR_DESIGN).toContain(ElementType.WALL)
      expect(ElementCategories.INTERIOR_DESIGN).toContain(ElementType.DOOR)
      expect(ElementCategories.INTERIOR_DESIGN).toContain(ElementType.WINDOW)
      expect(ElementCategories.INTERIOR_DESIGN).toContain(ElementType.FURNITURE)
      expect(ElementCategories.INTERIOR_DESIGN).toContain(ElementType.FIXTURE)
      expect(ElementCategories.INTERIOR_DESIGN).toContain(ElementType.ROOM)
      expect(ElementCategories.INTERIOR_DESIGN).toHaveLength(6)
    })
  })

  describe('getElementCategory', () => {
    it('should return the correct category for basic shapes', () => {
      expect(getElementCategory(ElementType.RECTANGLE)).toBe('BASIC_SHAPES')
      expect(getElementCategory(ElementType.SQUARE)).toBe('BASIC_SHAPES')
      expect(getElementCategory(ElementType.ELLIPSE)).toBe('BASIC_SHAPES')
      expect(getElementCategory(ElementType.CIRCLE)).toBe('BASIC_SHAPES')
    })

    it('should return the correct category for polygons', () => {
      expect(getElementCategory(ElementType.POLYGON)).toBe('POLYGONS')
      expect(getElementCategory(ElementType.TRIANGLE)).toBe('POLYGONS')
      expect(getElementCategory(ElementType.HEXAGON)).toBe('POLYGONS')
    })

    it('should return the correct category for paths', () => {
      expect(getElementCategory(ElementType.LINE)).toBe('PATHS')
      expect(getElementCategory(ElementType.PATH)).toBe('PATHS')
      expect(getElementCategory(ElementType.ARC)).toBe('PATHS')
    })

    it('should return the correct category for text', () => {
      expect(getElementCategory(ElementType.TEXT_LABEL)).toBe('TEXT')
    })

    it('should return the correct category for interior design elements', () => {
      expect(getElementCategory(ElementType.WALL)).toBe('INTERIOR_DESIGN')
      expect(getElementCategory(ElementType.DOOR)).toBe('INTERIOR_DESIGN')
      expect(getElementCategory(ElementType.ROOM)).toBe('INTERIOR_DESIGN')
    })

    it('should return undefined for unknown element types', () => {
      // @ts-ignore - Testing with invalid input
      expect(getElementCategory('UNKNOWN_TYPE')).toBeUndefined()
    })
  })

  describe('type Checking Functions', () => {
    it('should correctly identify element types with isElementType', () => {
      // Basic shapes should return true
      expect(isElementType(ElementType.RECTANGLE)).toBe(true)
      expect(isElementType(ElementType.CIRCLE)).toBe(true)

      // Polygons should return true
      expect(isElementType(ElementType.POLYGON)).toBe(true)
      expect(isElementType(ElementType.TRIANGLE)).toBe(true)

      // Other types should return false
      expect(isElementType(ElementType.LINE)).toBe(false)
      expect(isElementType(ElementType.TEXT_LABEL)).toBe(false)
      expect(isElementType(ElementType.WALL)).toBe(false)
    })

    it('should correctly identify path types with isPathType', () => {
      // Path types should return true
      expect(isPathType(ElementType.LINE)).toBe(true)
      expect(isPathType(ElementType.PATH)).toBe(true)
      expect(isPathType(ElementType.ARC)).toBe(true)

      // Other types should return false
      expect(isPathType(ElementType.RECTANGLE)).toBe(false)
      expect(isPathType(ElementType.TEXT_LABEL)).toBe(false)
      expect(isPathType(ElementType.WALL)).toBe(false)
    })

    it('should correctly identify text types with isTextType', () => {
      // Text types should return true
      expect(isTextType(ElementType.TEXT_LABEL)).toBe(true)

      // Other types should return false
      expect(isTextType(ElementType.RECTANGLE)).toBe(false)
      expect(isTextType(ElementType.LINE)).toBe(false)
      expect(isTextType(ElementType.WALL)).toBe(false)
    })

    it('should correctly identify interior design types with isInteriorDesignType', () => {
      // Interior design types should return true
      expect(isInteriorDesignType(ElementType.WALL)).toBe(true)
      expect(isInteriorDesignType(ElementType.DOOR)).toBe(true)
      expect(isInteriorDesignType(ElementType.ROOM)).toBe(true)

      // Other types should return false
      expect(isInteriorDesignType(ElementType.RECTANGLE)).toBe(false)
      expect(isInteriorDesignType(ElementType.LINE)).toBe(false)
      expect(isInteriorDesignType(ElementType.TEXT_LABEL)).toBe(false)
    })
  })

  describe('baseStyleProperties Interface', () => {
    it('should define valid style properties', () => {
      // Create a style properties object
      const styleProps: BaseStyleProperties = {
        fill: '#FF0000',
        stroke: '#000000',
        strokeWidth: 2,
        opacity: 0.8,
      }

      // Verify the properties
      expect(styleProps.fill).toBe('#FF0000')
      expect(styleProps.stroke).toBe('#000000')
      expect(styleProps.strokeWidth).toBe(2)
      expect(styleProps.opacity).toBe(0.8)
    })

    it('should allow partial style properties', () => {
      // Create a style properties object with only some properties
      const styleProps: BaseStyleProperties = {
        fill: 'blue',
        opacity: 0.5,
      }

      // Verify the properties
      expect(styleProps.fill).toBe('blue')
      expect(styleProps.opacity).toBe(0.5)
      expect(styleProps.stroke).toBeUndefined()
      expect(styleProps.strokeWidth).toBeUndefined()
    })
  })

  describe('element Interface', () => {
    it('should define valid element with required properties', () => {
      // Create an element with required properties
      const element: Element = {
        id: 'elem-001',
        type: 'RECTANGLE',
        visible: true,
        locked: false,
      }

      // Verify the properties
      expect(element.id).toBe('elem-001')
      expect(element.type).toBe('RECTANGLE')
      expect(element.visible).toBe(true)
      expect(element.locked).toBe(false)
      expect(element.metadata).toBeUndefined()
    })

    it('should define valid element with metadata', () => {
      // Create an element with metadata
      const element: Element = {
        id: 'elem-002',
        type: 'CIRCLE',
        visible: true,
        locked: true,
        metadata: {
          createdAt: 1625097600000, // July 1, 2021
          updatedAt: 1625184000000, // July 2, 2021
          name: 'My Circle',
          tags: ['geometric', 'round'],
        },
      }

      // Verify the properties
      expect(element.id).toBe('elem-002')
      expect(element.type).toBe('CIRCLE')
      expect(element.visible).toBe(true)
      expect(element.locked).toBe(true)
      expect(element.metadata).toBeDefined()
      expect(element.metadata?.name).toBe('My Circle')
      expect(element.metadata?.tags).toContain('geometric')
    })
  })

  describe('shapeElement Interface', () => {
    it('should define valid shape element with required properties', () => {
      // Create a shape element with required properties
      const shapeElement: ShapeElement = {
        id: 'shape-001',
        type: 'RECTANGLE',
        visible: true,
        locked: false,
        position: { x: 100, y: 200 },
        rotation: 0,
        selectable: true,
        draggable: true,
        showHandles: true,
      }

      // Verify the properties
      expect(shapeElement.id).toBe('shape-001')
      expect(shapeElement.type).toBe('RECTANGLE')
      expect(shapeElement.visible).toBe(true)
      expect(shapeElement.locked).toBe(false)
      expect(shapeElement.position).toEqual({ x: 100, y: 200 })
      expect(shapeElement.rotation).toBe(0)
      expect(shapeElement.selectable).toBe(true)
      expect(shapeElement.draggable).toBe(true)
      expect(shapeElement.showHandles).toBe(true)
    })

    it('should define valid shape element with all properties', () => {
      // Create a shape element with all properties
      const shapeElement: ShapeElement = {
        id: 'shape-002',
        type: 'CIRCLE',
        visible: true,
        locked: false,
        fill: 'blue',
        stroke: 'black',
        strokeWidth: 2,
        opacity: 0.8,
        position: { x: 150, y: 250 },
        rotation: 45,
        selectable: true,
        draggable: true,
        showHandles: true,
        zIndex: 10,
        layer: 'foreground',
        metadata: {
          createdAt: 1625097600000,
          updatedAt: 1625184000000,
          name: 'My Circle',
        },
      }

      // Verify the properties
      expect(shapeElement.id).toBe('shape-002')
      expect(shapeElement.type).toBe('CIRCLE')
      expect(shapeElement.visible).toBe(true)
      expect(shapeElement.locked).toBe(false)
      expect(shapeElement.fill).toBe('blue')
      expect(shapeElement.stroke).toBe('black')
      expect(shapeElement.strokeWidth).toBe(2)
      expect(shapeElement.opacity).toBe(0.8)
      expect(shapeElement.position).toEqual({ x: 150, y: 250 })
      expect(shapeElement.rotation).toBe(45)
      expect(shapeElement.selectable).toBe(true)
      expect(shapeElement.draggable).toBe(true)
      expect(shapeElement.showHandles).toBe(true)
      expect(shapeElement.zIndex).toBe(10)
      expect(shapeElement.layer).toBe('foreground')
      expect(shapeElement.metadata).toBeDefined()
      expect(shapeElement.metadata?.name).toBe('My Circle')
    })
  })

  describe('metadataProperties Interface', () => {
    it('should define valid metadata with required properties', () => {
      // Create metadata with required properties
      const metadata: MetadataProperties = {
        createdAt: 1625097600000, // July 1, 2021
        updatedAt: 1625184000000, // July 2, 2021
      }

      // Verify the properties
      expect(metadata.createdAt).toBe(1625097600000)
      expect(metadata.updatedAt).toBe(1625184000000)
    })

    it('should define valid metadata with all properties', () => {
      // Create metadata with all properties
      const metadata: MetadataProperties = {
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
        createdBy: 'user123',
        name: 'Living Room Wall',
        description: 'North-facing wall in the living room',
        tags: ['wall', 'living-room', 'north'],
        designType: 'modern',
        designCategory: 'residential',
        customProperty1: 'custom value',
        customProperty2: 42,
      }

      // Verify the properties
      expect(metadata.createdAt).toBe(1625097600000)
      expect(metadata.updatedAt).toBe(1625184000000)
      expect(metadata.createdBy).toBe('user123')
      expect(metadata.name).toBe('Living Room Wall')
      expect(metadata.description).toBe('North-facing wall in the living room')
      expect(metadata.tags).toContain('wall')
      expect(metadata.tags).toContain('living-room')
      expect(metadata.designType).toBe('modern')
      expect(metadata.designCategory).toBe('residential')
      expect(metadata.customProperty1).toBe('custom value')
      expect(metadata.customProperty2).toBe(42)
    })

    it('should allow custom properties via index signature', () => {
      // Create metadata with custom properties
      const metadata: MetadataProperties = {
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
        dimensions: { width: 100, height: 200, depth: 10 },
        materials: ['wood', 'glass'],
        cost: 299.99,
        isImported: true,
      }

      // Verify the custom properties
      expect(metadata.dimensions).toEqual({ width: 100, height: 200, depth: 10 })
      expect(metadata.materials).toEqual(['wood', 'glass'])
      expect(metadata.cost).toBe(299.99)
      expect(metadata.isImported).toBe(true)
    })
  })
})
