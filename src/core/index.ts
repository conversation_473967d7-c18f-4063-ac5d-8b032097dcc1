/**
 * Core Business Logic Layer Index
 *
 * @remarks
 * This file serves as the main entry point and barrel export for the core business logic layer.
 * It re-exports public APIs (classes, types, functions, constants) from various sub-modules,
 * including:
 * - `factory`: For creating Element instances.
 * - `validator`: For validating shape data.
 * - `compute`: For performing geometric calculations.
 * - `state`: For managing application state related to shapes (e.g., `ShapeRepository`).
 * - `config`: For core configuration (imported from `@/config`).
 * - `errors`: For custom error types (imported from `@/services/system/error-service`).
 * - `CoreCoordinator`: The central coordinator class.
 *
 * This centralized export allows other parts of the application (hooks, services, UI)
 * to import core functionalities from a single, stable entry point (`@/core`)
 * without needing to know the internal directory structure.
 *
 * Singleton instances that were previously exported from here are being phased out
 * in favor of Dependency Injection initialized at the application root.
 *
 * @module core/index
 */

// Re-export everything from the engine module
// export * from '@/engine'; // Module not found, and this file IS the core/engine entry point.

// Display a deprecation warning when this module is imported
// console.warn(
//   'WARNING: The @/core module is deprecated and will be removed in a future version. ' +
//   'Please update your imports to use @/engine instead.'
// );

// Export CoreCoordinator class (central orchestrator)
export { CoreCoordinator } from './CoreCoordinator'

// Export factory module contents (ElementFactory, Creators, types)
export * from './factory'

// Export ShapeRepository class (central state manager for shapes)
export { ShapeRepository } from './state/ShapeRepository'

// Export validator module contents (ElementValidator, BaseShapeValidator, types)
export * from './validator'

// Export config module contents (CoreConfig interface, default config)
// Use the config from src/config instead of a local config
export * from '@/config'

// Export errors module contents (CoreError, specific errors, ErrorType enum)
export * from '@/services/system/error-service'

// Export core utility functions if they exist and are meant for public use
// Example: export * from './utils'; // Uncomment if utils are added and exported from ./utils/index.ts

// Note: Singletons like `elementFactory`, `coreCoordinator` are no longer exported from here.
// They should be instantiated and managed via Dependency Injection at the application root (`main.tsx` or `App.tsx`).
