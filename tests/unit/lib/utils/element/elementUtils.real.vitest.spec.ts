import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  ElementUtils,
  getElementCategory,
  getElementType,
  isShapeElement,
  isPathElement,
  isTextElement,
  isImageElement,
  isGroupElement,
  getRectangleProps,
  getEllipseProps,
  getLineProps,
  getPolygonProps,
  getTextProps,
  getImageProps,
  points,
  calculateElementsBoundingBox,
  getCenter,
  isVisible,
  distanceBetween,
  doElementsOverlap,
  sortByZIndex,
  groupByLayer,
  filterByType,
  filterByCategory,
} from '@/lib/utils/element/elementUtils'
import type { Element, ShapeElement } from '@/types/core/elementDefinitions'
import { ElementType } from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'
import type { IPoint } from '@/types/core/element/geometry/point'

describe('Element Utils (Real Implementation)', () => {
  let testRectangle: ShapeElement
  let testEllipse: ShapeElement
  let testLine: ShapeElement
  let testPolygon: ShapeElement
  let testElements: Element[]

  beforeEach(() => {
    testRectangle = {
      id: 'rect-1',
      type: ElementType.RECTANGLE,
      position: { x: 0, y: 0 },
      properties: { width: 100, height: 50 },
      majorCategory: MajorCategory.BASE,
      minorCategory: 'architecture',
      intraLayerZIndex: 1,
    }

    testEllipse = {
      id: 'ellipse-1',
      type: ElementType.ELLIPSE,
      position: { x: 50, y: 50 },
      properties: { radiusX: 30, radiusY: 20 },
      majorCategory: MajorCategory.FURNITURE,
      minorCategory: 'seating',
      intraLayerZIndex: 2,
    }

    testLine = {
      id: 'line-1',
      type: ElementType.LINE,
      position: { x: 0, y: 0 },
      properties: { endX: 100, endY: 100 },
      majorCategory: MajorCategory.BASE,
      minorCategory: 'architecture',
      intraLayerZIndex: 0,
    }

    testPolygon = {
      id: 'polygon-1',
      type: ElementType.POLYGON,
      position: { x: 0, y: 0 },
      properties: {
        points: [
          { x: 0, y: 0 },
          { x: 50, y: 0 },
          { x: 25, y: 50 },
        ],
      },
      majorCategory: MajorCategory.BASE,
      minorCategory: 'architecture',
      intraLayerZIndex: 3,
    }

    testElements = [testRectangle, testEllipse, testLine, testPolygon]
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Element Type Detection', () => {
    it('should detect element categories', () => {
      expect(getElementCategory(testRectangle)).toBe(MajorCategory.BASE)
      expect(getElementCategory(testEllipse)).toBe(MajorCategory.FURNITURE)
    })

    it('should get element types', () => {
      expect(getElementType(testRectangle)).toBe(ElementType.RECTANGLE)
      expect(getElementType(testEllipse)).toBe(ElementType.ELLIPSE)
      expect(getElementType(testLine)).toBe(ElementType.LINE)
      expect(getElementType(testPolygon)).toBe(ElementType.POLYGON)
    })

    it('should detect shape elements', () => {
      expect(isShapeElement(testRectangle)).toBe(true)
      expect(isShapeElement(testEllipse)).toBe(true)
      expect(isShapeElement(testLine)).toBe(true)
      expect(isShapeElement(testPolygon)).toBe(true)
    })

    it('should detect path elements', () => {
      const pathElement = {
        ...testRectangle,
        type: ElementType.PATH,
      }
      expect(isPathElement(pathElement)).toBe(true)
      expect(isPathElement(testRectangle)).toBe(false)
    })

    it('should detect text elements', () => {
      const textElement = {
        ...testRectangle,
        type: ElementType.TEXT,
      }
      expect(isTextElement(textElement)).toBe(true)
      expect(isTextElement(testRectangle)).toBe(false)
    })

    it('should detect image elements', () => {
      const imageElement = {
        ...testRectangle,
        type: ElementType.IMAGE,
      }
      expect(isImageElement(imageElement)).toBe(true)
      expect(isImageElement(testRectangle)).toBe(false)
    })

    it('should detect group elements', () => {
      const groupElement = {
        ...testRectangle,
        type: ElementType.GROUP,
      }
      expect(isGroupElement(groupElement)).toBe(true)
      expect(isGroupElement(testRectangle)).toBe(false)
    })
  })

  describe('Property Getters', () => {
    it('should get rectangle properties', () => {
      const props = getRectangleProps(testRectangle)
      expect(props.width).toBe(100)
      expect(props.height).toBe(50)
    })

    it('should get ellipse properties', () => {
      const props = getEllipseProps(testEllipse)
      expect(props.radiusX).toBe(30)
      expect(props.radiusY).toBe(20)
    })

    it('should get line properties', () => {
      const props = getLineProps(testLine)
      expect(props.endX).toBe(100)
      expect(props.endY).toBe(100)
    })

    it('should get polygon properties', () => {
      const props = getPolygonProps(testPolygon)
      expect(props.points).toHaveLength(3)
      expect(props.points[0]).toEqual({ x: 0, y: 0 })
    })

    it('should get text properties', () => {
      const textElement = {
        ...testRectangle,
        type: ElementType.TEXT,
        properties: { text: 'Hello', fontSize: 16 },
      }
      const props = getTextProps(textElement)
      expect(props.text).toBe('Hello')
      expect(props.fontSize).toBe(16)
    })

    it('should get image properties', () => {
      const imageElement = {
        ...testRectangle,
        type: ElementType.IMAGE,
        properties: { src: 'image.jpg', width: 200, height: 150 },
      }
      const props = getImageProps(imageElement)
      expect(props.src).toBe('image.jpg')
      expect(props.width).toBe(200)
      expect(props.height).toBe(150)
    })

    it('should handle missing properties gracefully', () => {
      const elementWithoutProps = {
        ...testRectangle,
        properties: undefined,
      }
      const props = getRectangleProps(elementWithoutProps as any)
      expect(props.width).toBeUndefined()
      expect(props.height).toBeUndefined()
    })
  })

  describe('Points Extraction', () => {
    it('should extract points from polygon', () => {
      const extractedPoints = points(testPolygon)
      expect(extractedPoints).toHaveLength(3)
      expect(extractedPoints[0]).toEqual({ x: 0, y: 0, z: undefined })
    })

    it('should return empty array for elements without points', () => {
      const extractedPoints = points(testRectangle)
      expect(extractedPoints).toEqual([])
    })

    it('should handle invalid elements', () => {
      const extractedPoints = points(null)
      expect(extractedPoints).toEqual([])
    })

    it('should ensure point structure', () => {
      const elementWithIncompletePoints = {
        properties: {
          points: [{ x: 1, y: 2 }, { x: 3, y: 4, z: 5 }],
        },
      }
      const extractedPoints = points(elementWithIncompletePoints)
      expect(extractedPoints).toHaveLength(2)
      expect(extractedPoints[0]).toEqual({ x: 1, y: 2, z: undefined })
      expect(extractedPoints[1]).toEqual({ x: 3, y: 4, z: 5 })
    })
  })

  describe('ElementUtils Static Methods', () => {
    it('should calculate elements bounding box', () => {
      const bbox = calculateElementsBoundingBox(testElements)
      expect(bbox.x).toBe(0)
      expect(bbox.y).toBe(0)
      expect(bbox.width).toBeGreaterThan(0)
      expect(bbox.height).toBeGreaterThan(0)
    })

    it('should get element center', () => {
      const center = getCenter(testRectangle)
      expect(center.x).toBe(50) // 0 + 100/2
      expect(center.y).toBe(25) // 0 + 50/2
    })

    it('should check element visibility', () => {
      expect(isVisible(testRectangle)).toBe(true)
      
      const hiddenElement = {
        ...testRectangle,
        properties: { ...testRectangle.properties, visible: false },
      }
      expect(isVisible(hiddenElement)).toBe(false)
    })

    it('should calculate distance between elements', () => {
      const distance = distanceBetween(testRectangle, testEllipse)
      expect(distance).toBeGreaterThanOrEqual(0)
    })

    it('should check element overlap', () => {
      const overlap = doElementsOverlap(testRectangle, testEllipse)
      expect(typeof overlap).toBe('boolean')
    })

    it('should sort elements by z-index', () => {
      const sorted = sortByZIndex(testElements)
      expect(sorted[0].intraLayerZIndex).toBeLessThanOrEqual(sorted[1].intraLayerZIndex!)
    })

    it('should group elements by layer', () => {
      const grouped = groupByLayer(testElements)
      expect(typeof grouped).toBe('object')
    })

    it('should filter elements by type', () => {
      const rectangles = filterByType(testElements, ElementType.RECTANGLE)
      expect(rectangles).toHaveLength(1)
      expect(rectangles[0].type).toBe(ElementType.RECTANGLE)
    })

    it('should filter elements by category', () => {
      const baseElements = filterByCategory(testElements, MajorCategory.BASE)
      expect(baseElements.length).toBeGreaterThan(0)
      baseElements.forEach(element => {
        expect(element.majorCategory).toBe(MajorCategory.BASE)
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle null elements gracefully', () => {
      expect(() => getElementType(null as any)).not.toThrow()
      expect(() => getElementCategory(null as any)).not.toThrow()
      expect(() => isShapeElement(null as any)).not.toThrow()
    })

    it('should handle undefined properties', () => {
      const elementWithoutProps = {
        id: 'test',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        majorCategory: MajorCategory.BASE,
        minorCategory: 'test',
      } as any

      expect(() => getRectangleProps(elementWithoutProps)).not.toThrow()
      expect(() => getCenter(elementWithoutProps)).not.toThrow()
    })

    it('should handle invalid element arrays', () => {
      expect(() => calculateElementsBoundingBox([])).not.toThrow()
      expect(() => sortByZIndex([])).not.toThrow()
      expect(() => filterByType([], ElementType.RECTANGLE)).not.toThrow()
    })

    it('should handle malformed elements', () => {
      const malformedElement = {
        id: 'malformed',
        // Missing required properties
      } as any

      expect(() => getElementType(malformedElement)).not.toThrow()
      expect(() => isVisible(malformedElement)).not.toThrow()
    })
  })

  describe('Performance', () => {
    it('should handle large element arrays efficiently', () => {
      const largeElementArray = Array.from({ length: 1000 }, (_, i) => ({
        ...testRectangle,
        id: `element-${i}`,
        position: { x: i, y: i },
      }))

      const startTime = Date.now()

      calculateElementsBoundingBox(largeElementArray)
      sortByZIndex(largeElementArray)
      filterByType(largeElementArray, ElementType.RECTANGLE)
      filterByCategory(largeElementArray, MajorCategory.BASE)

      const endTime = Date.now()
      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly
    })

    it('should handle many property extractions efficiently', () => {
      const startTime = Date.now()

      for (let i = 0; i < 10000; i++) {
        getRectangleProps(testRectangle)
        getEllipseProps(testEllipse)
        getLineProps(testLine)
        getPolygonProps(testPolygon)
        getElementType(testRectangle)
        getElementCategory(testRectangle)
      }

      const endTime = Date.now()
      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly
    })
  })

  describe('Edge Cases', () => {
    it('should handle elements with extreme coordinates', () => {
      const extremeElement = {
        ...testRectangle,
        position: { x: Number.MAX_SAFE_INTEGER, y: Number.MIN_SAFE_INTEGER },
      }

      expect(() => getCenter(extremeElement)).not.toThrow()
      expect(() => calculateElementsBoundingBox([extremeElement])).not.toThrow()
    })

    it('should handle elements with zero dimensions', () => {
      const zeroElement = {
        ...testRectangle,
        properties: { width: 0, height: 0 },
      }

      const center = getCenter(zeroElement)
      expect(center.x).toBe(0)
      expect(center.y).toBe(0)
    })

    it('should handle elements with negative dimensions', () => {
      const negativeElement = {
        ...testRectangle,
        properties: { width: -100, height: -50 },
      }

      expect(() => getCenter(negativeElement)).not.toThrow()
      expect(() => calculateElementsBoundingBox([negativeElement])).not.toThrow()
    })

    it('should handle circular references in element arrays', () => {
      const circularArray = [testRectangle, testEllipse]
      // Add circular reference (if the implementation supports it)
      ;(circularArray as any).circular = circularArray

      expect(() => filterByType(circularArray, ElementType.RECTANGLE)).not.toThrow()
      expect(() => sortByZIndex(circularArray)).not.toThrow()
    })
  })
})
