import type { Cubic } from '@/types/core/element/path/cubicPathTypes'
import type { ShapeElement } from '@/types/core/elementDefinitions'

import { describe, expect, it } from 'vitest'

describe('cubic Interface', () => {
  it('should define a cubic bezier curve with required properties', () => {
    // Create a valid Cubic object
    const cubic: Cubic = {
      id: 'cubic-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 0 },
      control1: { x: 50, y: 100 },
      control2: { x: 150, y: 100 },
      end: { x: 200, y: 0 },
    }

    // Verify the object is valid
    expect(cubic.id).toBe('cubic-1')
    expect(cubic.type).toBe('shape')
    expect(cubic.start.x).toBe(0)
    expect(cubic.start.y).toBe(0)
    expect(cubic.control1.x).toBe(50)
    expect(cubic.control1.y).toBe(100)
    expect(cubic.control2.x).toBe(150)
    expect(cubic.control2.y).toBe(100)
    expect(cubic.end.x).toBe(200)
    expect(cubic.end.y).toBe(0)
  })

  it('should extend ShapeElement interface', () => {
    // Create a cubic bezier curve
    const cubic: Cubic = {
      id: 'cubic-2',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 10, y: 10 },
      control1: { x: 60, y: 110 },
      control2: { x: 160, y: 110 },
      end: { x: 210, y: 10 },
    }

    // Verify the cubic extends ShapeElement
    const shapeElement: ShapeElement = cubic
    expect(shapeElement.id).toBe('cubic-2')
    expect(shapeElement.type).toBe('shape')
  })

  it('should allow creating cubic curves with different control points', () => {
    // Create cubic curves with different control points
    const curve1: Cubic = {
      id: 'curve-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 0 },
      control1: { x: 50, y: 100 },
      control2: { x: 150, y: 100 },
      end: { x: 200, y: 0 },
    }

    const curve2: Cubic = {
      id: 'curve-2',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 0 },
      control1: { x: 0, y: 100 },
      control2: { x: 200, y: 100 },
      end: { x: 200, y: 0 },
    }

    const curve3: Cubic = {
      id: 'curve-3',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 0 },
      control1: { x: 100, y: -50 },
      control2: { x: 100, y: -50 },
      end: { x: 200, y: 0 },
    }

    // Verify all curves are valid
    expect(curve1.start.x).toBe(0)
    expect(curve1.start.y).toBe(0)
    expect(curve1.control1.x).toBe(50)
    expect(curve1.control1.y).toBe(100)
    expect(curve1.control2.x).toBe(150)
    expect(curve1.control2.y).toBe(100)
    expect(curve1.end.x).toBe(200)
    expect(curve1.end.y).toBe(0)

    expect(curve2.start.x).toBe(0)
    expect(curve2.start.y).toBe(0)
    expect(curve2.control1.x).toBe(0)
    expect(curve2.control1.y).toBe(100)
    expect(curve2.control2.x).toBe(200)
    expect(curve2.control2.y).toBe(100)
    expect(curve2.end.x).toBe(200)
    expect(curve2.end.y).toBe(0)

    expect(curve3.start.x).toBe(0)
    expect(curve3.start.y).toBe(0)
    expect(curve3.control1.x).toBe(100)
    expect(curve3.control1.y).toBe(-50)
    expect(curve3.control2.x).toBe(100)
    expect(curve3.control2.y).toBe(-50)
    expect(curve3.end.x).toBe(200)
    expect(curve3.end.y).toBe(0)
  })

  it('should be usable in arrays and collections', () => {
    // Create an array of cubic curves
    const curves: Cubic[] = [
      {
        id: 'cubic-3',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 100, y: 100 },
        rotation: 0,

        selectable: true,
        draggable: true,
        showHandles: true,
        start: { x: 0, y: 0 },
        control1: { x: 50, y: 100 },
        control2: { x: 150, y: 100 },
        end: { x: 200, y: 0 },
      },
      {
        id: 'cubic-4',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 100, y: 100 },
        rotation: 0,

        selectable: true,
        draggable: true,
        showHandles: true,
        start: { x: 0, y: 0 },
        control1: { x: 0, y: 100 },
        control2: { x: 200, y: 100 },
        end: { x: 200, y: 0 },
      },
    ]

    // Verify the array is valid
    expect(curves.length).toBe(2)
    expect(curves[0].id).toBe('cubic-3')
    expect(curves[0].start.x).toBe(0)
    expect(curves[0].start.y).toBe(0)
    expect(curves[0].control1.x).toBe(50)
    expect(curves[0].control1.y).toBe(100)

    expect(curves[1].id).toBe('cubic-4')
    expect(curves[1].start.x).toBe(0)
    expect(curves[1].start.y).toBe(0)
    expect(curves[1].control1.x).toBe(0)
    expect(curves[1].control1.y).toBe(100)
  })

  it('should be usable in functions that require Cubic', () => {
    // Define a function that uses Cubic
    function calculateCubicCurveLength(cubic: Cubic): number {
      // Simple approximation of curve length by dividing into segments
      const segments = 10
      let length = 0
      let prevX = cubic.start.x
      let prevY = cubic.start.y

      for (let i = 1; i <= segments; i++) {
        const t = i / segments
        const oneMinusT = 1 - t

        // Cubic Bezier formula
        const x = oneMinusT ** 3 * cubic.start.x
          + 3 * oneMinusT ** 2 * t * cubic.control1.x
          + 3 * oneMinusT * t ** 2 * cubic.control2.x
          + t ** 3 * cubic.end.x

        const y = oneMinusT ** 3 * cubic.start.y
          + 3 * oneMinusT ** 2 * t * cubic.control1.y
          + 3 * oneMinusT * t ** 2 * cubic.control2.y
          + t ** 3 * cubic.end.y

        // Calculate segment length
        const dx = x - prevX
        const dy = y - prevY
        length += Math.sqrt(dx * dx + dy * dy)

        prevX = x
        prevY = y
      }

      return length
    }

    // Create a cubic curve
    const cubic: Cubic = {
      id: 'cubic-5',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 0 },
      control1: { x: 50, y: 100 },
      control2: { x: 150, y: 100 },
      end: { x: 200, y: 0 },
    }

    // Test the function
    const length = calculateCubicCurveLength(cubic)
    expect(length).toBeGreaterThan(0)
  })

  it('should be compatible with JSON serialization/deserialization', () => {
    // Create a cubic curve
    const cubic: Cubic = {
      id: 'cubic-6',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      start: { x: 0, y: 0 },
      control1: { x: 50, y: 100 },
      control2: { x: 150, y: 100 },
      end: { x: 200, y: 0 },
    }

    // Serialize to JSON and deserialize
    const serialized = JSON.stringify(cubic)
    const deserialized = JSON.parse(serialized) as Cubic

    // Verify the deserialized object is valid
    expect(deserialized.id).toBe('cubic-6')
    expect(deserialized.type).toBe('shape')
    expect(deserialized.start.x).toBe(0)
    expect(deserialized.start.y).toBe(0)
    expect(deserialized.control1.x).toBe(50)
    expect(deserialized.control1.y).toBe(100)
    expect(deserialized.control2.x).toBe(150)
    expect(deserialized.control2.y).toBe(100)
    expect(deserialized.end.x).toBe(200)
    expect(deserialized.end.y).toBe(0)
  })
})
