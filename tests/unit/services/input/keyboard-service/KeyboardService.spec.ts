import type { AppEventMap, EventBus } from '@/types/services/events'
import type { KeyboardEventContext, KeyboardShortcut } from '@/types/services/keyboard'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { KeyboardServiceImpl } from '@/services/input/keyboard-service'

// Mock dependencies
const mockEventBus = {
  publish: vi.fn(),
  subscribe: vi.fn(),
  unsubscribe: vi.fn(),
  clear: vi.fn(),
} as unknown as EventBus<AppEventMap>

const mockLogger = {
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  debug: vi.fn(),
  setContext: vi.fn(),
  getConfig: vi.fn(),
  setConfig: vi.fn(),
} as unknown as LoggerService

// Mock DOM event listeners
const mockAddEventListener = vi.fn()
const mockRemoveEventListener = vi.fn()

Object.defineProperty(document, 'addEventListener', {
  value: mockAddEventListener,
})

Object.defineProperty(document, 'removeEventListener', {
  value: mockRemoveEventListener,
})

describe('keyboardServiceImpl', () => {
  let keyboardService: KeyboardServiceImpl
  let keyDownHandler: (event: KeyboardEvent) => void
  let keyUpHandler: (event: KeyboardEvent) => void

  beforeEach(() => {
    vi.clearAllMocks()

    // Capture event handlers when they're registered
    mockAddEventListener.mockImplementation((event: string, handler: Function) => {
      if (event === 'keydown') {
        keyDownHandler = handler as (event: KeyboardEvent) => void
      }
      else if (event === 'keyup') {
        keyUpHandler = handler as (event: KeyboardEvent) => void
      }
    })

    keyboardService = new KeyboardServiceImpl(mockEventBus, mockLogger)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('constructor', () => {
    it('should initialize with dependencies', () => {
      expect(keyboardService).toBeDefined()
      expect(keyboardService.serviceId).toBeDefined()
      expect(mockLogger.info).toHaveBeenCalledWith('[KeyboardServiceImpl] Initialized.')
    })
  })

  describe('initialize', () => {
    it('should add event listeners', () => {
      keyboardService.initialize()

      expect(mockAddEventListener).toHaveBeenCalledWith('keydown', expect.any(Function))
      expect(mockAddEventListener).toHaveBeenCalledWith('keyup', expect.any(Function))
      expect(mockLogger.info).toHaveBeenCalledWith('Keyboard service event listeners initialized.')
    })
  })

  describe('cleanup', () => {
    it('should remove event listeners and clear bindings', () => {
      keyboardService.initialize()
      keyboardService.cleanup()

      expect(mockRemoveEventListener).toHaveBeenCalledWith('keydown', expect.any(Function))
      expect(mockRemoveEventListener).toHaveBeenCalledWith('keyup', expect.any(Function))
      expect(mockLogger.info).toHaveBeenCalledWith('Keyboard service cleaned up.')
    })
  })

  describe('registerShortcut', () => {
    it('should register a new shortcut', () => {
      const shortcut: KeyboardShortcut = {
        id: 'test-shortcut',
        keyCombination: { key: 's', ctrl: true },
        action: vi.fn(),
        description: 'Test shortcut',
        enabled: true,
      }

      const id = keyboardService.registerShortcut(shortcut)

      expect(id).toBe('test-shortcut')
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Registering shortcut: test-shortcut',
        shortcut,
      )
    })

    it('should warn when overwriting existing shortcut', () => {
      const shortcut: KeyboardShortcut = {
        id: 'duplicate-shortcut',
        keyCombination: { key: 's', ctrl: true },
        action: vi.fn(),
        enabled: true,
      }

      keyboardService.registerShortcut(shortcut)
      keyboardService.registerShortcut(shortcut)

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Shortcut with ID duplicate-shortcut already exists. Overwriting.',
      )
    })

    it('should enable shortcut by default when enabled is not specified', () => {
      const shortcut: KeyboardShortcut = {
        id: 'auto-enabled',
        keyCombination: { key: 'a' },
        action: vi.fn(),
      }

      keyboardService.registerShortcut(shortcut)
      const retrieved = keyboardService.getShortcut('auto-enabled')

      expect(retrieved?.enabled).toBe(true)
    })
  })

  describe('unregisterShortcut', () => {
    it('should unregister existing shortcut', () => {
      const shortcut: KeyboardShortcut = {
        id: 'to-remove',
        keyCombination: { key: 'd', ctrl: true },
        action: vi.fn(),
        enabled: true,
      }

      keyboardService.registerShortcut(shortcut)
      const result = keyboardService.unregisterShortcut('to-remove')

      expect(result).toBe(true)
      expect(mockLogger.info).toHaveBeenCalledWith('Unregistering shortcut: to-remove')
    })

    it('should return false for non-existent shortcut', () => {
      const result = keyboardService.unregisterShortcut('non-existent')

      expect(result).toBe(false)
    })
  })

  describe('getShortcut', () => {
    it('should return registered shortcut', () => {
      const shortcut: KeyboardShortcut = {
        id: 'get-test',
        keyCombination: { key: 'g' },
        action: vi.fn(),
        enabled: true,
      }

      keyboardService.registerShortcut(shortcut)
      const retrieved = keyboardService.getShortcut('get-test')

      expect(retrieved).toEqual(expect.objectContaining({
        id: 'get-test',
        keyCombination: { key: 'g' },
      }))
    })

    it('should return undefined for non-existent shortcut', () => {
      const result = keyboardService.getShortcut('non-existent')

      expect(result).toBeUndefined()
    })
  })

  describe('getAllShortcuts', () => {
    it('should return all registered shortcuts', () => {
      const shortcut1: KeyboardShortcut = {
        id: 'shortcut1',
        keyCombination: { key: '1' },
        action: vi.fn(),
        enabled: true,
      }

      const shortcut2: KeyboardShortcut = {
        id: 'shortcut2',
        keyCombination: { key: '2' },
        action: vi.fn(),
        enabled: true,
      }

      keyboardService.registerShortcut(shortcut1)
      keyboardService.registerShortcut(shortcut2)

      const allShortcuts = keyboardService.getAllShortcuts()

      expect(allShortcuts).toHaveLength(2)
      expect(allShortcuts.map(s => s.id)).toContain('shortcut1')
      expect(allShortcuts.map(s => s.id)).toContain('shortcut2')
    })

    it('should return empty array when no shortcuts registered', () => {
      const allShortcuts = keyboardService.getAllShortcuts()

      expect(allShortcuts).toEqual([])
    })
  })

  describe('enableShortcut', () => {
    it('should enable existing shortcut', () => {
      const shortcut: KeyboardShortcut = {
        id: 'to-enable',
        keyCombination: { key: 'e' },
        action: vi.fn(),
        enabled: false,
      }

      keyboardService.registerShortcut(shortcut)
      const result = keyboardService.enableShortcut('to-enable')

      expect(result).toBe(true)
      expect(keyboardService.getShortcut('to-enable')?.enabled).toBe(true)
      expect(mockLogger.info).toHaveBeenCalledWith('Shortcut enabled: to-enable')
    })

    it('should return false for non-existent shortcut', () => {
      const result = keyboardService.enableShortcut('non-existent')

      expect(result).toBe(false)
      expect(mockLogger.warn).toHaveBeenCalledWith('Shortcut not found for enabling: non-existent')
    })
  })

  describe('disableShortcut', () => {
    it('should disable existing shortcut', () => {
      const shortcut: KeyboardShortcut = {
        id: 'to-disable',
        keyCombination: { key: 'd' },
        action: vi.fn(),
        enabled: true,
      }

      keyboardService.registerShortcut(shortcut)
      const result = keyboardService.disableShortcut('to-disable')

      expect(result).toBe(true)
      expect(keyboardService.getShortcut('to-disable')?.enabled).toBe(false)
      expect(mockLogger.info).toHaveBeenCalledWith('Shortcut disabled: to-disable')
    })

    it('should return false for non-existent shortcut', () => {
      const result = keyboardService.disableShortcut('non-existent')

      expect(result).toBe(false)
      expect(mockLogger.warn).toHaveBeenCalledWith('Shortcut not found for disabling: non-existent')
    })
  })

  describe('setContext and getContext', () => {
    it('should set and get keyboard context', () => {
      const context: KeyboardEventContext = {
        source: 'canvas',
        activeElement: 'shape-editor',
        mode: 'edit',
      }

      keyboardService.setContext(context)
      const retrievedContext = keyboardService.getContext()

      expect(retrievedContext).toEqual(expect.objectContaining(context))
      expect(mockLogger.info).toHaveBeenCalledWith('Setting keyboard context:', context)
    })

    it('should merge context with existing context', () => {
      keyboardService.setContext({ source: 'canvas' })
      keyboardService.setContext({ mode: 'edit' })

      const context = keyboardService.getContext()

      expect(context.source).toBe('canvas')
      expect(context.mode).toBe('edit')
    })
  })

  describe('getConfig and setConfig', () => {
    it('should get default configuration', () => {
      const config = keyboardService.getConfig()

      expect(config).toEqual({
        enabled: true,
        preventDefaultForRegistered: true,
        stopPropagationForRegistered: false,
      })
    })

    it('should update configuration', () => {
      keyboardService.setConfig({ enabled: false })

      const config = keyboardService.getConfig()

      expect(config.enabled).toBe(false)
      expect(config.preventDefaultForRegistered).toBe(true) // Should remain unchanged
      expect(mockLogger.info).toHaveBeenCalledWith(
        'KeyboardService config updated:',
        expect.objectContaining({ enabled: false }),
      )
    })
  })

  describe('registerKeyBinding', () => {
    it('should register key binding', () => {
      const callback = vi.fn()
      const unregister = keyboardService.registerKeyBinding('ctrl+s', callback)

      expect(typeof unregister).toBe('function')
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Registered key binding for \'ctrl+s\'',
      )
    })

    it('should register key binding with options', () => {
      const callback = vi.fn()
      const options = {
        preventDefault: false,
        stopPropagation: true,
        onKeyUp: true,
        description: 'Test binding',
      }

      const unregister = keyboardService.registerKeyBinding('alt+f', callback, options)

      expect(typeof unregister).toBe('function')
    })
  })

  describe('unregisterKeyBinding', () => {
    it('should unregister key binding', () => {
      const callback = vi.fn()
      keyboardService.registerKeyBinding('ctrl+z', callback)

      keyboardService.unregisterKeyBinding('ctrl+z')

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Unregistered key binding for \'ctrl+z\'',
      )
    })
  })

  describe('handleKeyEvent', () => {
    beforeEach(() => {
      keyboardService.initialize()
    })

    it('should handle keydown events', () => {
      const mockEvent = new KeyboardEvent('keydown', { key: 's', ctrlKey: true })

      const result = keyboardService.handleKeyEvent(mockEvent)

      expect(result).toBe(true)
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Manually handling KeyEvent:',
        'keydown',
        's',
      )
    })

    it('should handle keyup events', () => {
      const mockEvent = new KeyboardEvent('keyup', { key: 's', ctrlKey: true })

      const result = keyboardService.handleKeyEvent(mockEvent)

      expect(result).toBe(true)
    })

    it('should return false for unsupported event types', () => {
      const mockEvent = { type: 'keypress', key: 's' } as any

      const result = keyboardService.handleKeyEvent(mockEvent)

      expect(result).toBe(false)
    })
  })
})
