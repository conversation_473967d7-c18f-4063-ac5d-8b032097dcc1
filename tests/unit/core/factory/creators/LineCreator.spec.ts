import type { CreateLineParams } from '@/core/factory/ElementFactory'

import { vi } from 'vitest'

// 其他导入
import { afterEach, describe, expect, it } from 'vitest'
import { LineCreator } from '@/core/factory/creators/LineCreator'
// 导入 Point
import { Point } from '@/types/core/element/geometry/point'

// 使用 vi.hoisted 定义模拟函数
const MockLine = vi.fn()
const mockEnsurePointInstance = vi.fn()

// 设置模拟
vi.mock('@/types/core/element/path/line', () => ({
  Line: MockLine,
}))

vi.mock('@/core/utils/geometryUtils', () => ({
  ensurePointInstance: mockEnsurePointInstance,
}))

describe('lineCreator', () => {
  const creator = new LineCreator()
  const defaultId = 'line-123'

  // Clean up mocks after each test
  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should create a Line instance using create method with valid params', async () => {
    const params: CreateLineParams = {
      type: 'line' as const,
      id: defaultId,
      start: new Point(10, 20),
      end: new Point(110, 120),
      strokeWidth: 3,
      stroke: '#ff0000',
      opacity: 0.8,
      layer: 'layer1',
      visible: true,
    }

    await creator.create(params)

    expect(MockLine).toHaveBeenCalledTimes(1)
    expect(MockLine).toHaveBeenCalledWith(
      defaultId,
      expect.objectContaining({
        type: 'line',
        start: expect.any(Point),
        end: expect.any(Point),
        strokeWidth: 3,
        stroke: '#ff0000',
        opacity: 0.8,
      }),
      expect.objectContaining({
        layer: 'layer1',
        visible: true,
        // other base options will be undefined if not in params
      }),
    )
  })

  it('should create a Line instance using create method with Point instances', async () => {
    const startPoint = new Point(10, 20)
    const endPoint = new Point(110, 120)
    const params: CreateLineParams = {
      type: 'line' as const,
      id: defaultId,
      start: startPoint,
      end: endPoint,
    }

    await creator.create(params)

    expect(mockEnsurePointInstance).toHaveBeenCalledWith(startPoint)
    expect(mockEnsurePointInstance).toHaveBeenCalledWith(endPoint)
    expect(MockLine).toHaveBeenCalledWith(
      defaultId,
      expect.objectContaining({
        start: expect.any(Point),
        end: expect.any(Point),
      }),
      expect.objectContaining({}), // Options will be empty/undefined
    )
  })

  it('should throw error if create method receives incorrect shape type', async () => {
    const params = {
      type: 'rectangle' as const, // Incorrect type
      id: defaultId,
      start: new Point(0, 0),
      end: new Point(10, 10),
    } as any // Cast to bypass TS check for test

    await expect(creator.create(params)).rejects.toThrow('LineCreator received incorrect shape type: rectangle')
    expect(MockLine).not.toHaveBeenCalled()
  })

  it('should throw error if create method receives invalid start point', async () => {
    const params: CreateLineParams = {
      type: 'line' as const,
      id: defaultId,
      start: null as any, // Invalid point
      end: new Point(10, 10),
    }

    // Configure mock to throw on the first call (for start point)
    mockEnsurePointInstance.mockImplementationOnce(() => { throw new Error('Invalid point format') })

    await expect(creator.create(params)).rejects.toThrow('Line creation requires valid start and end points: Invalid point format')
    expect(MockLine).not.toHaveBeenCalled()
  })

  it('should throw error if create method receives invalid end point', async () => {
    const params: CreateLineParams = {
      type: 'line' as const,
      id: defaultId,
      start: new Point(0, 0),
      end: 'invalid' as any, // Invalid point
    }

    // Configure mock: succeed for start, throw for end
    mockEnsurePointInstance.mockImplementationOnce((p: any) => new Point(p.x, p.y))
    mockEnsurePointInstance.mockImplementationOnce(() => { throw new Error('Invalid point format') })

    await expect(creator.create(params)).rejects.toThrow('Line creation requires valid start and end points: Invalid point format')
    expect(MockLine).not.toHaveBeenCalled()
  })

  it('should create a default Line instance using createDefault method', async () => {
    const position = new Point(100, 150)
    await creator.createDefault(defaultId, position)

    expect(MockLine).toHaveBeenCalledTimes(1)
    expect(MockLine).toHaveBeenCalledWith(
      defaultId,
      expect.objectContaining({
        type: 'line' as const,
        start: expect.any(Point),
        end: expect.any(Point),
        strokeWidth: 2,
        stroke: '#000000',
      }),
      expect.objectContaining({}), // Default options are empty
    )
  })
})
