// Import the event interfaces
import type {
  BatchEvent,
  ComputeEvent,
  ValidationEvent,
} from '@/types/services/events/computeEvents'

import { describe, expect, it } from 'vitest'

// Import AppEventType for type checking
import { AppEventType } from '@/types/services/events/eventTypes'

describe('compute/Validation/Batch Event Types', () => {
  it('should allow declaration using ComputeEvent interface', () => {
    const event: ComputeEvent | undefined = undefined
    expect(event).toBeUndefined()

    // Example usage
    const exampleEvent: ComputeEvent = {
      type: AppEventType.COMPUTE_REQUEST,
      payload: {
        operation: 'area',
        shapeIds: ['shape-1', 'shape-2'],
        computeConfig: { async: true, precision: 2 },
      },
    }
    expect(exampleEvent.type).toBe(AppEventType.COMPUTE_REQUEST)
    expect(exampleEvent.payload.operation).toBe('area')
    expect(exampleEvent.payload.shapeIds).toHaveLength(2)
  })

  it('should allow declaration using ValidationEvent interface', () => {
    const event: ValidationEvent | undefined = undefined
    expect(event).toBeUndefined()

    // Example usage
    const exampleEvent: ValidationEvent = {
      type: AppEventType.VALIDATE_SHAPE,
      payload: {
        targetId: 'shape-3',
        rules: { minWidth: 10 }, // Using unknown for rules
        context: { type: 'editing' },
      },
    }
    expect(exampleEvent.type).toBe(AppEventType.VALIDATE_SHAPE)
    expect(exampleEvent.payload.targetId).toBe('shape-3')
    expect((exampleEvent.payload.rules as any).minWidth).toBe(10)
  })

  it('should allow declaration using BatchEvent interface', () => {
    const event: BatchEvent | undefined = undefined
    expect(event).toBeUndefined()

    // Example usage
    const exampleEvent: BatchEvent = {
      type: AppEventType.BATCH_START,
      payload: {
        batchId: 'batch-xyz',
        operations: [
          { type: 'translate', payload: { shapeId: 's1', dx: 10, dy: 0 } },
          { type: 'rotate', payload: { shapeId: 's2', angle: 90 } },
        ],
      },
    }
    expect(exampleEvent.type).toBe(AppEventType.BATCH_START)
    expect(exampleEvent.payload.batchId).toBe('batch-xyz')
    expect(exampleEvent.payload.operations).toHaveLength(2)
  })
})
