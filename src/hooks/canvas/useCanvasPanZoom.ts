/**
 * Canvas Pan and Zoom Hook
 *
 * This module provides a React hook for managing canvas pan and zoom operations.
 * It handles mouse wheel events for zooming, drag operations for panning,
 * and provides methods for programmatic zoom and pan control.
 *
 * @module hooks/canvas/useCanvasPanZoom
 */

import type React from 'react'
import type Point from '@/types/core/element/geometry/point'
import { useCallback, useState } from 'react'

interface UseCanvasPanZoomProps {
  initialPan?: Point
  initialZoom?: number
  minZoom?: number
  maxZoom?: number
  onPanChange?: (pan: Point) => void
  onZoomChange?: (zoom: number) => void
}

const DEFAULT_MIN_ZOOM = 0.01
const DEFAULT_MAX_ZOOM = 10
const DEFAULT_ZOOM_SENSITIVITY = 1.1

export function useCanvasPanZoom({
  initialPan = { x: 0, y: 0 },
  initialZoom = 1,
  minZoom = DEFAULT_MIN_ZOOM,
  maxZoom = DEFAULT_MAX_ZOOM,
  onPanChange,
  onZoomChange,
}: UseCanvasPanZoomProps) {
  const [pan, setPanState] = useState<Point>(initialPan)
  const [zoom, setZoomState] = useState<number>(initialZoom)
  const [isPanning, setIsPanning] = useState<boolean>(false)
  const [panStartMousePosition, setPanStartMousePosition] = useState<Point | null>(null)
  const [panStartCoords, setPanStartCoords] = useState<Point | null>(null)

  const handlePanStateChange = useCallback((newPan: Point) => {
    setPanState(newPan)
    if (onPanChange) {
      onPanChange(newPan)
    }
  }, [onPanChange])

  const handleZoomStateChange = useCallback((newZoom: number) => {
    const clampedZoom = Math.max(minZoom, Math.min(newZoom, maxZoom))
    setZoomState(clampedZoom)
    if (onZoomChange) {
      onZoomChange(clampedZoom)
    }
  }, [minZoom, maxZoom, onZoomChange])

  const handleWheel = useCallback((event: React.WheelEvent<SVGElement>) => {
    event.preventDefault()
    const zoomFactor = event.deltaY < 0 ? DEFAULT_ZOOM_SENSITIVITY : 1 / DEFAULT_ZOOM_SENSITIVITY
    const newZoomValue = zoom * zoomFactor
    handleZoomStateChange(newZoomValue)

    // Optional: Pan with Shift/Ctrl + Wheel - this might be better handled by specific keybindings
    // if (event.shiftKey || event.ctrlKey) {
    //   const newPanX = pan.x - (event.shiftKey ? event.deltaY : 0);
    //   const newPanY = pan.y - (event.ctrlKey && !event.shiftKey ? event.deltaY : 0);
    //   if (newPanX !== pan.x || newPanY !== pan.y) {
    //     handlePanStateChange({ x: newPanX, y: newPanY });
    //   }
    // }
  }, [zoom, handleZoomStateChange])

  const startPan = useCallback((event: React.MouseEvent<SVGElement>) => {
    console.warn('[useCanvasPanZoom] startPan called. Setting isPanning to true.')
    setIsPanning(true)
    setPanStartMousePosition({ x: event.clientX, y: event.clientY })
    setPanStartCoords(pan)
  }, [pan])

  const continuePan = useCallback((event: React.MouseEvent<SVGElement>) => {
    if (isPanning && panStartMousePosition && panStartCoords) {
      // console.log('[useCanvasPanZoom] continuePan called.'); // This can be very noisy
      const dx = event.clientX - panStartMousePosition.x
      const dy = event.clientY - panStartMousePosition.y
      handlePanStateChange({
        x: panStartCoords.x + dx,
        y: panStartCoords.y + dy,
      })
    }
  }, [isPanning, panStartMousePosition, panStartCoords, handlePanStateChange])

  const endPan = useCallback(() => {
    console.warn('[useCanvasPanZoom] endPan called. Current isPanning:', isPanning)
    if (isPanning) {
      setIsPanning(false)
      setPanStartMousePosition(null)
      setPanStartCoords(null)
      console.warn('[useCanvasPanZoom] endPan: isPanning set to false.')
    }
    else {
      console.warn('[useCanvasPanZoom] endPan: called but isPanning was already false.')
    }
  }, [isPanning])

  const setManualPan = useCallback((newPan: Point) => {
    handlePanStateChange(newPan)
  }, [handlePanStateChange])

  const setManualZoom = useCallback((newZoom: number) => {
    handleZoomStateChange(newZoom)
  }, [handleZoomStateChange])

  return {
    pan,
    zoom,
    isPanning,
    handleWheel,
    startPan,
    continuePan,
    endPan,
    setPan: setManualPan,
    setZoom: setManualZoom,
  }
}
