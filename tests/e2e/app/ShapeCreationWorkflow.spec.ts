import { test, expect } from '@playwright/test';

/**
 * End-to-end test suite for the shape creation workflow.
 * These tests verify that users can create, edit, and delete shapes
 * through the application's user interface.
 */
test.describe('Shape Creation Workflow', () => {
  /**
   * Setup for each test.
   * Navigates to the application and waits for it to load.
   */
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the application to load
    await page.waitForSelector('.canvas-container');
  });

  /**
   * Tests creating a rectangle.
   * Verifies that a user can select the rectangle tool and create a rectangle on the canvas.
   */
  test('Create a rectangle', async ({ page }) => {
    // Click the rectangle tool button
    await page.click('#tool-rectangle');
    
    // Verify that the rectangle tool is active
    await expect(page.locator('#tool-rectangle')).toHaveClass(/active/);
    
    // Draw a rectangle on the canvas
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    // Calculate start and end points for the rectangle
    const startX = boundingBox.x + 100;
    const startY = boundingBox.y + 100;
    const endX = startX + 200;
    const endY = startY + 150;
    
    // Draw the rectangle
    await page.mouse.move(startX, startY);
    await page.mouse.down();
    await page.mouse.move(endX, endY);
    await page.mouse.up();
    
    // Verify that a rectangle was created
    const rectangle = page.locator('rect');
    await expect(rectangle).toBeVisible();
    
    // Verify rectangle properties
    const x = await rectangle.getAttribute('x');
    const y = await rectangle.getAttribute('y');
    const width = await rectangle.getAttribute('width');
    const height = await rectangle.getAttribute('height');
    
    expect(parseFloat(x)).toBeCloseTo(100, 0);
    expect(parseFloat(y)).toBeCloseTo(100, 0);
    expect(parseFloat(width)).toBeCloseTo(200, 0);
    expect(parseFloat(height)).toBeCloseTo(150, 0);
  });

  /**
   * Tests creating an ellipse.
   * Verifies that a user can select the ellipse tool and create an ellipse on the canvas.
   */
  test('Create an ellipse', async ({ page }) => {
    // Click the ellipse tool button
    await page.click('#tool-ellipse');
    
    // Verify that the ellipse tool is active
    await expect(page.locator('#tool-ellipse')).toHaveClass(/active/);
    
    // Draw an ellipse on the canvas
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    // Calculate start and end points for the ellipse
    const centerX = boundingBox.x + 200;
    const centerY = boundingBox.y + 150;
    const radiusX = 100;
    const radiusY = 50;
    
    // Draw the ellipse
    await page.mouse.move(centerX, centerY);
    await page.mouse.down();
    await page.mouse.move(centerX + radiusX, centerY + radiusY);
    await page.mouse.up();
    
    // Verify that an ellipse was created
    const ellipse = page.locator('ellipse');
    await expect(ellipse).toBeVisible();
    
    // Verify ellipse properties
    const cx = await ellipse.getAttribute('cx');
    const cy = await ellipse.getAttribute('cy');
    const rx = await ellipse.getAttribute('rx');
    const ry = await ellipse.getAttribute('ry');
    
    expect(parseFloat(cx)).toBeCloseTo(200, 0);
    expect(parseFloat(cy)).toBeCloseTo(150, 0);
    expect(parseFloat(rx)).toBeCloseTo(100, 0);
    expect(parseFloat(ry)).toBeCloseTo(50, 0);
  });

  /**
   * Tests selecting and moving a shape.
   * Verifies that a user can select a shape and move it to a new position.
   */
  test('Select and move a shape', async ({ page }) => {
    // Create a rectangle first
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const startX = boundingBox.x + 100;
    const startY = boundingBox.y + 100;
    const endX = startX + 200;
    const endY = startY + 150;
    
    await page.mouse.move(startX, startY);
    await page.mouse.down();
    await page.mouse.move(endX, endY);
    await page.mouse.up();
    
    // Switch to select tool
    await page.click('#tool-select');
    
    // Verify that the select tool is active
    await expect(page.locator('#tool-select')).toHaveClass(/active/);
    
    // Select the rectangle
    await page.mouse.move(startX + 100, startY + 75);
    await page.mouse.down();
    await page.mouse.up();
    
    // Verify that the rectangle is selected
    const selectedRectangle = page.locator('rect.selected');
    await expect(selectedRectangle).toBeVisible();
    
    // Move the rectangle
    const moveX = 50;
    const moveY = 30;
    
    await page.mouse.move(startX + 100, startY + 75);
    await page.mouse.down();
    await page.mouse.move(startX + 100 + moveX, startY + 75 + moveY);
    await page.mouse.up();
    
    // Verify that the rectangle was moved
    const rectangle = page.locator('rect');
    const x = await rectangle.getAttribute('x');
    const y = await rectangle.getAttribute('y');
    
    expect(parseFloat(x)).toBeCloseTo(100 + moveX, 0);
    expect(parseFloat(y)).toBeCloseTo(100 + moveY, 0);
  });

  /**
   * Tests resizing a shape.
   * Verifies that a user can select a shape and resize it.
   */
  test('Resize a shape', async ({ page }) => {
    // Create a rectangle first
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const startX = boundingBox.x + 100;
    const startY = boundingBox.y + 100;
    const endX = startX + 200;
    const endY = startY + 150;
    
    await page.mouse.move(startX, startY);
    await page.mouse.down();
    await page.mouse.move(endX, endY);
    await page.mouse.up();
    
    // Switch to select tool
    await page.click('#tool-select');
    
    // Select the rectangle
    await page.mouse.move(startX + 100, startY + 75);
    await page.mouse.down();
    await page.mouse.up();
    
    // Verify that the rectangle is selected
    const selectedRectangle = page.locator('rect.selected');
    await expect(selectedRectangle).toBeVisible();
    
    // Resize the rectangle using the bottom-right handle
    const handleX = endX;
    const handleY = endY;
    const resizeX = 50;
    const resizeY = 30;
    
    await page.mouse.move(handleX, handleY);
    await page.mouse.down();
    await page.mouse.move(handleX + resizeX, handleY + resizeY);
    await page.mouse.up();
    
    // Verify that the rectangle was resized
    const rectangle = page.locator('rect');
    const width = await rectangle.getAttribute('width');
    const height = await rectangle.getAttribute('height');
    
    expect(parseFloat(width)).toBeCloseTo(200 + resizeX, 0);
    expect(parseFloat(height)).toBeCloseTo(150 + resizeY, 0);
  });

  /**
   * Tests deleting a shape.
   * Verifies that a user can select a shape and delete it.
   */
  test('Delete a shape', async ({ page }) => {
    // Create a rectangle first
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const startX = boundingBox.x + 100;
    const startY = boundingBox.y + 100;
    const endX = startX + 200;
    const endY = startY + 150;
    
    await page.mouse.move(startX, startY);
    await page.mouse.down();
    await page.mouse.move(endX, endY);
    await page.mouse.up();
    
    // Verify that a rectangle was created
    const rectangle = page.locator('rect');
    await expect(rectangle).toBeVisible();
    
    // Switch to select tool
    await page.click('#tool-select');
    
    // Select the rectangle
    await page.mouse.move(startX + 100, startY + 75);
    await page.mouse.down();
    await page.mouse.up();
    
    // Delete the rectangle
    await page.keyboard.press('Delete');
    
    // Verify that the rectangle was deleted
    await expect(page.locator('rect')).not.toBeVisible();
  });

  /**
   * Tests changing shape properties.
   * Verifies that a user can select a shape and change its properties.
   */
  test('Change shape properties', async ({ page }) => {
    // Create a rectangle first
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const startX = boundingBox.x + 100;
    const startY = boundingBox.y + 100;
    const endX = startX + 200;
    const endY = startY + 150;
    
    await page.mouse.move(startX, startY);
    await page.mouse.down();
    await page.mouse.move(endX, endY);
    await page.mouse.up();
    
    // Switch to select tool
    await page.click('#tool-select');
    
    // Select the rectangle
    await page.mouse.move(startX + 100, startY + 75);
    await page.mouse.down();
    await page.mouse.up();
    
    // Verify that the properties panel is visible
    const propertiesPanel = page.locator('.properties-panel');
    await expect(propertiesPanel).toBeVisible();
    
    // Change the fill color
    await page.locator('.fill-color-picker').click();
    await page.locator('.color-option[data-color="red"]').click();
    
    // Verify that the rectangle's fill color was changed
    const rectangle = page.locator('rect');
    const fillColor = await rectangle.getAttribute('fill');
    expect(fillColor).toBe('red');
    
    // Change the stroke width
    await page.locator('.stroke-width-input').fill('5');
    await page.keyboard.press('Enter');
    
    // Verify that the rectangle's stroke width was changed
    const strokeWidth = await rectangle.getAttribute('stroke-width');
    expect(strokeWidth).toBe('5');
  });

  /**
   * Tests undo and redo functionality.
   * Verifies that a user can undo and redo actions.
   */
  test('Undo and redo actions', async ({ page }) => {
    // Create a rectangle
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const startX = boundingBox.x + 100;
    const startY = boundingBox.y + 100;
    const endX = startX + 200;
    const endY = startY + 150;
    
    await page.mouse.move(startX, startY);
    await page.mouse.down();
    await page.mouse.move(endX, endY);
    await page.mouse.up();
    
    // Verify that a rectangle was created
    const rectangle = page.locator('rect');
    await expect(rectangle).toBeVisible();
    
    // Undo the creation
    await page.keyboard.press('Control+Z');
    
    // Verify that the rectangle was removed
    await expect(page.locator('rect')).not.toBeVisible();
    
    // Redo the creation
    await page.keyboard.press('Control+Y');
    
    // Verify that the rectangle was restored
    await expect(page.locator('rect')).toBeVisible();
  });
});
