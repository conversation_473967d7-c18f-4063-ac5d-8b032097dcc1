/**
 * Keyboard Service Registration
 *
 * This module provides functions for registering the keyboard service with the service registry.
 *
 * @module services/input/keyboard-service
 */

import type { ServiceRegistry } from '@/services/core/registry'
import type { KeyboardService } from '@/types/services/keyboard'
// import { EventBus } from '@/types/services/events'; // Unused import
import type { LoggerService } from '@/types/services/logging'
import { getServiceFactory } from '@/services/core/registry' // Added getServiceFactory
import { ServiceId } from '@/types/services/core/serviceIdentifier'
// import { initializeKeyboardService, getKeyboardService } from './index'; // No longer needed
// import { getEventBus } from '@/services/core/event-bus'; // No longer needed

/**
 * 注册键盘服务到服务注册表
 *
 * @param registry 服务注册表
 * @param logger 日志服务
 * @returns 注册的键盘服务实例
 */
export function registerKeyboardService(
  registry: ServiceRegistry,
  logger: LoggerService,
): KeyboardService {
  // Create keyboard service instance using the factory
  const factory = getServiceFactory()
  const keyboardService = factory.createKeyboardService(logger)

  // 注册到注册表
  registry.register(ServiceId.KeyboardService, keyboardService)

  logger.info('键盘服务注册成功')

  return keyboardService
}
