/**
 * StorageService handles saving and loading design data to/from persistent storage.
 *
 * @remarks
 * It listens for storage-related events and manages the persistence of application state.
 * This service provides a unified interface for data persistence operations and abstracts
 * the underlying storage mechanism to provide a consistent API for storing and retrieving
 * application data.
 *
 * The service supports localStorage operations and handles serialization and deserialization
 * of complex objects automatically. It provides error handling for storage operations and
 * publishes events to notify other parts of the application about storage operations.
 *
 * @example
 * ```typescript
 * import { StorageService } from '@/services/storage/storageService';
 *
 * const storage = new StorageService(eventBus, logger);
 * // Storage operations are handled automatically through events
 * ```
 *
 * @module services/storage/storageService
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'
import type { AppEventMap, EventBus } from '@/types/services/events'
import type { DesignData, StorageEvent } from '@/types/services/events/dataEvents'
import type { LoggerService } from '@/types/services/logging'

import { safeExecuteAsync } from '@/lib/utils/errorUtils'
import { useShapesStore } from '@/store/shapesStore'
import { AppEventType } from '@/types/services/events/eventTypes'

/**
 * StorageService handles saving and loading design data to/from persistent storage.
 * It listens for storage-related events and manages the persistence of application state.
 */
export class StorageService {
  private eventBus: EventBus<AppEventMap>
  private logger: LoggerService

  /**
   * Creates a new instance of StorageService.
   *
   * @param eventBus - The event bus for publishing and subscribing to events
   * @param logger - The logger service for logging
   */
  constructor(eventBus: EventBus<AppEventMap>, logger: LoggerService) {
    this.eventBus = eventBus
    this.logger = logger

    // Subscribe to storage events
    this.eventBus.subscribe(AppEventType.StorageSaveRequest, (event) => {
      void this.handleStorageSaveRequest(event)
    })
    this.eventBus.subscribe(AppEventType.StorageLoadRequest, (event) => {
      void this.handleStorageLoadRequest(event)
    })

    this.logger.info('StorageService initialized')
  }

  /**
   * Handles storage save requests.
   *
   * @param event - The storage save request event
   */
  private async handleStorageSaveRequest(event: StorageEvent): Promise<void> {
    await safeExecuteAsync(async () => {
      this.logger.info('StorageService handling StorageSaveRequest:', event?.payload)

      // Get current shapes from store if not provided in the event
      let dataToSave: DesignData

      if (event.payload?.data) {
        dataToSave = event.payload.data
      }
      else {
        // If no data provided, get current state from store
        const currentShapes: ShapeElement[] = useShapesStore.getState().shapes
        dataToSave = {
          id: 'current_design',
          title: 'Current Design',
          shapes: currentShapes as unknown as Record<string, unknown>,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        }
      }

      // 直接保存到localStorage，确保数据持久化
      // 这比依赖Zustand的persist中间件更可靠
      try {
        localStorage.setItem('reno-pilot-shapes-storage', JSON.stringify({
          shapes: dataToSave.shapes,
          selectedShapeIds: useShapesStore.getState().selectedShapeIds,
        }))
        this.logger.info('StorageService saved data to localStorage')
      }
      catch (error) {
        this.logger.error('StorageService failed to save data to localStorage', error)
      }

      // Publish completion event
      this.eventBus.publish({
        type: AppEventType.StorageSaveComplete,
        timestamp: Date.now(),
        payload: {
          operation: 'save',
          data: dataToSave,
          source: 'StorageService',
        },
      })

      this.logger.info('StorageService completed save operation')
    }, {
      component: 'StorageService',
      operation: 'handleStorageSaveRequest',
      metadata: { event },
    })
  }

  /**
   * Handles storage load requests.
   *
   * @param event - The storage load request event
   */
  private async handleStorageLoadRequest(event: StorageEvent): Promise<void> {
    await safeExecuteAsync(async () => {
      this.logger.info('StorageService handling StorageLoadRequest:', event?.payload)

      // For now, we're just using Zustand's built-in persistence
      // This method would be expanded if we implement custom loading logic

      // Publish completion event
      this.eventBus.publish({
        type: AppEventType.StorageLoadComplete,
        timestamp: Date.now(),
        payload: {
          operation: 'load',
          source: 'StorageService',
        },
      })

      this.logger.info('StorageService completed load operation')
    }, {
      component: 'StorageService',
      operation: 'handleStorageLoadRequest',
      metadata: { event },
    })
  }
}
