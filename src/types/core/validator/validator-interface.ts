/**
 * Validator Interface Definitions
 *
 * @remarks
 * This module defines core validation interfaces used throughout the application.
 * It provides type definitions for:
 * - Validation options and results
 * - Error/warning structures
 * - Validatable shapes and validator contracts
 *
 * @see {@link ./error-codes} for validation error code definitions
 * @see {@link ../validators} for validator implementations
 * @module types/core/validator/validator-interface
 */

// import { Element } from '@/types/core/element';
import type { ValidationErrorCode } from './error-codes'

/**
 * Validation options for customizing validation behavior
 *
 * @remarks
 * These options allow fine-grained control over validation:
 * - Strict vs lenient validation modes
 * - Contextual validation data
 * - Recursion depth limits
 */
export interface ValidationOptions {
  /** If `true`, performs strict validation according to all defined rules. Defaults to `false` (lenient). */
  strict?: boolean
  /** If `true`, validates only the required properties of an element or type. Defaults to `false`. */
  requiredOnly?: boolean
  /**
   * An optional record providing additional context for the validation process.
   * This can be used to pass environment-specific data or other relevant information
   * to custom validation rules.
   */
  context?: Record<string, unknown>
  /** If `true`, validation will be performed recursively on nested structures or child elements. Defaults to `false`. */
  recursive?: boolean
  /** Specifies the maximum depth for recursive validation, if `recursive` is `true`. */
  maxDepth?: number
}

/**
 * Represents the outcome of a validation operation.
 *
 * @remarks
 * This interface provides a structured way to report the results of validation,
 * including an overall pass/fail status, a general message, and detailed lists
 * of any errors or warnings encountered.
 */
export interface ValidationResult {
  /** A boolean indicating whether the validation passed (`true`) or failed (`false`). */
  valid: boolean
  /** An optional summary message describing the overall validation outcome. */
  message?: string
  /** An optional array of {@link ValidationError} objects detailing any errors found. */
  errors?: ValidationError[]
  /** An optional array of {@link ValidationWarning} objects detailing any warnings identified. */
  warnings?: ValidationWarning[]
}

/**
 * Describes a specific validation error.
 *
 * @remarks
 * This interface provides a standardized format for reporting validation errors, including:
 * - A machine-readable error code from {@link ValidationErrorCode}.
 * - A human-readable descriptive message.
 * - An optional path to the property or part of the data that caused the error.
 * - The actual value that failed validation, if applicable.
 */
export interface ValidationError {
  /** The specific error code, drawn from the {@link ValidationErrorCode} enum. */
  code: ValidationErrorCode
  /** A human-readable message explaining the validation error. */
  message: string
  /** An optional string representing the path to the property where the error occurred (e.g., 'shape.properties.width'). */
  path?: string
  /** The actual value that caused the validation error, if applicable. */
  value?: unknown
}

/**
 * Describes a specific validation warning.
 *
 * @remarks
 * Warnings represent non-critical issues found during validation.
 * They follow the same structure as {@link ValidationError} but indicate
 * potential problems rather than outright failures.
 */
export interface ValidationWarning {
  /** The specific warning code, typically drawn from the {@link ValidationErrorCode} enum (though some codes might be warning-specific). */
  code: ValidationErrorCode
  /** A human-readable message explaining the validation warning. */
  message: string
  /** An optional string representing the path to the property where the warning occurred. */
  path?: string
  /** The actual value that caused the validation warning, if applicable. */
  value?: unknown
}

/**
 * Defines the basic properties required for an element or shape to be subject to validation.
 *
 * @remarks
 * This interface ensures that any object intended for validation has at least:
 * - A `type` identifier.
 * - A unique `id`.
 * It also includes optional visual properties that might be relevant for certain validation rules.
 */
export interface ValidatableShape {
  /** A string identifier for the type of the element (e.g., 'rectangle', 'wall'). */
  type: string // Consider using ElementType for stricter typing if applicable
  /** A unique identifier for the specific instance of the element. */
  id: string
  /** An optional stroke color string, which might be subject to format validation. */
  strokeColor?: string
  /** An optional fill color string, which might be subject to format validation. */
  fillColor?: string
  /** Optional position of the shape. */
  position?: { x: number, y: number, z?: number } // Add position
  /** Optional record of additional properties specific to the shape type. */
  properties?: Record<string, unknown>
}

/**
 * Defines the contract for validator implementations.
 *
 * @remarks
 * Validators are responsible for checking if shapes or shape types adhere to
 * predefined rules and constraints. This interface specifies methods for:
 * - Validating concrete shape instances (via `validate`).
 * - Validating shape types abstractly (via `validateType`).
 */
export interface ShapeValidator {
  /**
   * Validates a specific shape instance against a set of rules.
   *
   * @param shape - The {@link ValidatableShape} instance to validate.
   * @param options - Optional {@link ValidationOptions} to customize the validation behavior.
   * @returns A {@link ValidationResult} object detailing the outcome of the validation.
   */
  validate: (shape: ValidatableShape, options?: ValidationOptions) => ValidationResult

  /**
   * Validates a shape type abstractly, checking general rules applicable to that type.
   *
   * @param type - A string identifier for the element type to validate (e.g., 'circle', 'polygon').
   * @param options - Optional {@link ValidationOptions} to customize the validation behavior.
   * @returns A {@link ValidationResult} object detailing the outcome of the type validation.
   */
  validateType: (type: string, options?: ValidationOptions) => ValidationResult // Consider using ElementType for `type`
}
