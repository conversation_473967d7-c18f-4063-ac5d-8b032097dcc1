/**
 * Core Type Definitions Index
 *
 * @remarks
 * This module serves as the primary barrel file for all core type definitions
 * within the `types/core` directory. It re-exports types from various sub-modules,
 * providing a centralized and simplified import path for other parts of the application
 * that depend on these core types.
 *
 * Key re-exported domains include:
 * - Canvas interaction types from {@link ./canvasTypes}.
 * - Fundamental element definitions (like {@link Element}, {@link ShapeElement}, {@link ElementType})
 *   from {@link ./elementDefinitions}, which also re-exports specific element interfaces
 *   from sub-namespaces like `Design`, `Shape`, and `Path`.
 * - Shape manipulation handle types from {@link ./handleTypes}.
 * - Types related to computational geometry and element property calculations from {@link ./compute}.
 * - Types for data validation from {@link ./validator}.
 * - Core application constants are typically imported directly from `../constants`.
 *
 * Additionally, some geometric primitive types like {@link PointData}, {@link GeometryBoundingBoxType}
 * are imported and re-exported. Note that `GeometryRectangle` and `Vector` are currently
 * defined locally within this file but are recommended to be moved to their own dedicated
 * files under `./element/geometry/`.
 *
 * @module types/core/index
 */

import type { BoundingBox as GeometryBoundingBoxType } from './element/geometry/bounding-box' // Renamed for clarity
import type { PointData } from './element/geometry/point' // Using named import

// CanvasLayer type is exported from ./canvasTypes.ts through the re-export at the end of this file.

/**
 * Base interfaces for elements.
 *
 * @remarks
 * - {@link ElementBase}: The most fundamental interface for any element, aliased as `Element` upon export.
 * - {@link ShapeElement}: Extends `ElementBase` for visual shapes with styling and transform properties.
 *
 * This section also re-exports namespaces for specific element kinds:
 * {@link Design}, {@link Shape}, {@link PathModule} (aliased from `Path`), {@link Image}, {@link Text}.
 */
import type {
  Element as ElementBase,
  ShapeElement,
} from './elementDefinitions'

/**
 * Core enumerations defining element types (e.g., RECTANGLE, LINE) and their logical categories.
 * These are fundamental to the type system of the application.
 * @see {@link ElementType}
 * @see {@link ElementCategories}
 */
import {
  // Re-exporting namespaces for specific element kinds
  Design, // Renamed to avoid conflict with DOM Element
  ElementCategories,
  ElementType,
  Image,
  Path as PathModule, // Alias to avoid conflict with local Path type if any
  Shape,
  Text,
} from './elementDefinitions'

export { ElementCategories, ElementType }

/**
 * Alias for {@link PointData}, representing a geometric point.
 * @see {@link PointData}
 */
type GeometryPoint = PointData
// GeometryPoint will be exported later.

/**
 * Represents a generic path defined by a sequence of points.
 *
 * @remarks
 * This interface is defined locally.
 * TODO: Consider moving `IPath` to a more specific path module, e.g., `src/types/core/element/path/commonPathTypes.ts`,
 * and ensure its `points` property uses a consistent Point type (e.g., {@link PointData}).
 */
interface IPath {
  /** The unique identifier for this path instance. */
  id: string
  /** The type identifier for the path (e.g., 'line', 'polyline'). */
  type: string
  /** An array of {@link GeometryPoint} objects defining the path's vertices. */
  points: GeometryPoint[]
}

/**
 * Re-exports core element interfaces.
 * - `Element`: Alias for the base {@link ElementBase} interface.
 * - `ShapeElement`: Interface for visual shape elements.
 * - `Path`: Alias for the locally defined {@link IPath} interface.
 */
export type { ElementBase as Element, ShapeElement }
export type { IPath as Path } // Exporting the locally defined IPath

// Re-export Design module types (contains interfaces like WallElement, DoorElement, etc.)
export { Design }

// Re-export Shape module types (contains interfaces like Rectangle, Circle, Polygon, etc.)
export { Shape }

// Re-export Path module types (contains interfaces like Line, Arc, Polyline, etc.)
export { PathModule } // Use the alias to avoid conflict

// Re-export Image module types
export { Image }

// Re-export Text module types
export { Text }

/**
 * Fundamental geometric primitive type definitions.
 *
 * @remarks
 * These types are essential for defining positions, dimensions, and boundaries of elements.
 * - {@link GeometryPoint}: Alias for {@link PointData}.
 * - {@link GeometryBoundingBoxType}: Imported from `./element/geometry/bounding-box` and aliased as `BoundingBox`.
 * - {@link GeometryRectangle}: Locally defined interface for a simple rectangle.
 *   TODO: Move to `src/types/core/element/geometry/rectangle.ts`.
 * - {@link Vector}: Locally defined interface for a 2D vector.
 *   TODO: Move to `src/types/core/element/geometry/vector.ts`.
 */

/**
 * Represents a rectangle defined by its top-left corner (x, y), width, and height.
 *
 * @remarks
 * TODO: Move this interface to `src/types/core/element/geometry/rectangle.ts`.
 */
interface GeometryRectangle {
  /** The x-coordinate of the top-left corner of the rectangle. */
  x: number
  /** The y-coordinate of the top-left corner of the rectangle. */
  y: number
  /** The width of the rectangle. */
  width: number
  /** The height of the rectangle. */
  height: number
}

/**
 * Represents an axis-aligned bounding box.
 * This type is imported from `./element/geometry/bounding-box.ts` and re-exported as `BoundingBox`.
 * @see {@link GeometryBoundingBoxType}
 */
export type { GeometryBoundingBoxType as BoundingBox }

/**
 * Represents a 2D vector with x and y components.
 *
 * @remarks
 * TODO: Move this interface to `src/types/core/element/geometry/vector.ts`.
 */
interface Vector {
  /** The x-component of the vector. */
  x: number
  /** The y-component of the vector. */
  y: number
}

// Note: Empty Utils objects placeholders and redundant I-prefixed interfaces were removed in previous steps.

/**
 * Re-exports core geometric primitive types.
 *
 * @remarks
 * It is recommended to move locally defined types (`GeometryRectangle`, `Vector`)
 * to their own dedicated files within `./element/geometry/`.
 */
export type {
  GeometryPoint, // Re-exporting the alias for PointData
  GeometryRectangle,
  // BoundingBox is re-exported above
  Vector,
}

/**
 * Re-exports all types from `./canvasTypes.ts`.
 * This includes types related to canvas operations, state, and layer management.
 */
export * from './canvasTypes'

/**
 * Re-exports all types from `./compute/index.ts`.
 * This includes types related to computational geometry and element property calculations.
 */
export * from './compute'

/**
 * Re-exports all types from `./element/design/design.ts`.
 * This typically includes design-specific element interfaces like `WallElement`, `DoorElement`, etc.
 */
export * from './element/design/design'

/**
 * Re-exports all types from `./element/elementPatternTypes.ts`.
 * This includes types related to fill patterns, line patterns, etc.
 */
export * from './element/elementPatternTypes'

/**
 * Re-exports all types from `./element/path/path.ts`.
 * This typically includes specific path interfaces like `Line`, `Arc`, `Polyline`, etc.
 */
export * from './element/path/path'

/**
 * Re-exports all types from `./element/shape/shape.ts`.
 * This typically includes specific shape interfaces like `Rectangle`, `Circle`, `Polygon`, etc.
 */
export * from './element/shape/shape'

/**
 * Re-exports all types from `./handleTypes.ts`.
 * This includes types related to element manipulation handles and their interactions.
 */
export * from './handleTypes'

/**
 * Re-exports all types from `./validator/validator.ts` (and potentially other validator files via `./validator/index.ts`).
 * This includes types related to data validation for elements and their properties.
 */
export * from './validator/validator' // Assuming this exports all necessary validator types
