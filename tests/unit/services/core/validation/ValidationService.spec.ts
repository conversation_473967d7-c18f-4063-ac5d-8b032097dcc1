import type { ShapeElement } from '@/types/core'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ElementValidator } from '@/core/validator'
import { ServiceRegistry } from '@/services/core/registry'
import { getValidationService, registerValidationService } from '@/services/core/validation'
import { ValidationErrorCode } from '@/types/core/validator/error-codes'
import { ServiceId } from '@/types/services/core/serviceIdentifier'

// Mock dependencies
const mockLogger = {
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  debug: vi.fn(),
  setContext: vi.fn(),
  getConfig: vi.fn(),
  setConfig: vi.fn(),
} as unknown as LoggerService

// Mock console.warn to avoid noise in tests
const mockConsoleWarn = vi.spyOn(console, 'warn').mockImplementation(() => {})

describe('validationService', () => {
  let registry: ServiceRegistry
  let validationService: ElementValidator

  beforeEach(() => {
    vi.clearAllMocks()
    registry = new ServiceRegistry()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('registerValidationService', () => {
    it('should register validation service in registry', () => {
      registerValidationService(registry, mockLogger)

      expect(registry.has(ServiceId.ValidationService)).toBe(true)
      expect(mockLogger.info).toHaveBeenCalledWith('验证服务注册成功')
    })

    it('should create validation service using factory', () => {
      registerValidationService(registry, mockLogger)

      const service = registry.getById(ServiceId.ValidationService)
      expect(service).toBeDefined()
      expect(service).toBeInstanceOf(ElementValidator)
    })
  })

  describe('getValidationService', () => {
    it('should return service from registry when available', () => {
      registerValidationService(registry, mockLogger)

      // Mock getService to return the registered service
      vi.doMock('@/services/core/registry', () => ({
        getService: vi.fn(() => registry.getById(ServiceId.ValidationService)),
      }))

      const service = getValidationService()
      expect(service).toBeDefined()
      expect(service).toBeInstanceOf(ElementValidator)
    })

    it('should return singleton when service not in registry', () => {
      // Mock getService to throw error
      vi.doMock('@/services/core/registry', () => ({
        getService: vi.fn(() => {
          throw new Error('Service not found')
        }),
      }))

      const service = getValidationService()
      expect(service).toBeDefined()
      expect(service).toBeInstanceOf(ElementValidator)
      // Note: The actual implementation uses console.warn, not our mock
      // This is expected behavior - the service should still work
    })
  })

  describe('elementValidator functionality', () => {
    beforeEach(() => {
      validationService = new ElementValidator()
    })

    describe('validateElement', () => {
      it('should validate valid rectangle element', async () => {
        const validRectangle: ShapeElement = {
          id: 'rect-1',
          type: 'RECTANGLE',
          position: { x: 100, y: 100, z: 0 },
          properties: {
            width: 200,
            height: 150,
          },
        }

        const result = await validationService.validateElement(validRectangle)

        expect(result.valid).toBe(true)
        expect(result.errors).toHaveLength(0)
      })

      it('should validate valid circle element', async () => {
        const validCircle: ShapeElement = {
          id: 'circle-1',
          type: 'CIRCLE',
          position: { x: 150, y: 150, z: 0 },
          properties: {
            radius: 50,
          },
        }

        const result = await validationService.validateElement(validCircle)

        expect(result.valid).toBe(true)
        expect(result.errors).toHaveLength(0)
      })

      it('should validate valid polygon element', async () => {
        const validPolygon: ShapeElement = {
          id: 'polygon-1',
          type: 'POLYGON',
          position: { x: 200, y: 200, z: 0 },
          properties: {
            points: [
              { x: 0, y: 50, z: 0 },
              { x: 43.3, y: 25, z: 0 },
              { x: 43.3, y: -25, z: 0 },
              { x: 0, y: -50, z: 0 },
              { x: -43.3, y: -25, z: 0 },
              { x: -43.3, y: 25, z: 0 },
              { x: 0, y: 50, z: 0 }, // Close the polygon
            ],
            sides: 6,
            isRegular: false,
          },
        }

        const result = await validationService.validateElement(validPolygon)

        expect(result.valid).toBe(true)
        expect(result.errors).toHaveLength(0)
      })

      it('should detect invalid element without id', async () => {
        const invalidElement: any = {
          type: 'RECTANGLE',
          position: { x: 100, y: 100, z: 0 },
          properties: {
            width: 200,
            height: 150,
          },
        }

        const result = await validationService.validateElement(invalidElement)

        expect(result.valid).toBe(false)
        expect(result.errors.length).toBeGreaterThan(0)
        expect(result.errors.some(error =>
          error.code === ValidationErrorCode.MISSING_OR_INVALID_ID,
        )).toBe(true)
      })

      it('should detect invalid element without type', async () => {
        const invalidElement: any = {
          id: 'test-element',
          position: { x: 100, y: 100, z: 0 },
          properties: {
            width: 200,
            height: 150,
          },
        }

        const result = await validationService.validateElement(invalidElement)

        expect(result.valid).toBe(false)
        expect(result.errors.length).toBeGreaterThan(0)
      })

      it('should handle element without position', async () => {
        const elementWithoutPosition: any = {
          id: 'test-element',
          type: 'RECTANGLE',
          properties: {
            width: 200,
            height: 150,
          },
        }

        const result = await validationService.validateElement(elementWithoutPosition)

        // The validator might accept elements without position in some cases
        // This test verifies the validator handles it gracefully
        expect(result).toBeDefined()
        expect(typeof result.valid).toBe('boolean')
        expect(Array.isArray(result.errors)).toBe(true)
      })

      it('should detect rectangle with invalid dimensions', async () => {
        const invalidRectangle: ShapeElement = {
          id: 'rect-invalid',
          type: 'RECTANGLE',
          position: { x: 100, y: 100, z: 0 },
          properties: {
            width: -200, // Invalid negative width
            height: 150,
          },
        }

        const result = await validationService.validateElement(invalidRectangle)

        expect(result.valid).toBe(false)
        expect(result.errors.length).toBeGreaterThan(0)
      })

      it('should detect circle with invalid radius', async () => {
        const invalidCircle: ShapeElement = {
          id: 'circle-invalid',
          type: 'CIRCLE',
          position: { x: 150, y: 150, z: 0 },
          properties: {
            radius: 0, // Invalid zero radius
          },
        }

        const result = await validationService.validateElement(invalidCircle)

        expect(result.valid).toBe(false)
        expect(result.errors.length).toBeGreaterThan(0)
      })

      it('should detect polygon with insufficient points', async () => {
        const invalidPolygon: ShapeElement = {
          id: 'polygon-invalid',
          type: 'POLYGON',
          position: { x: 200, y: 200, z: 0 },
          properties: {
            points: [
              { x: 0, y: 50, z: 0 },
              { x: 43.3, y: 25, z: 0 },
              // Only 2 points, need at least 3
            ],
          },
        }

        const result = await validationService.validateElement(invalidPolygon)

        expect(result.valid).toBe(false)
        expect(result.errors.length).toBeGreaterThan(0)
      })
    })

    describe('validateElements', () => {
      it('should validate multiple valid elements', async () => {
        const elements: ShapeElement[] = [
          {
            id: 'rect-1',
            type: 'RECTANGLE',
            position: { x: 100, y: 100, z: 0 },
            properties: { width: 200, height: 150 },
          },
          {
            id: 'circle-1',
            type: 'CIRCLE',
            position: { x: 300, y: 300, z: 0 },
            properties: { radius: 50 },
          },
        ]

        const results = await validationService.validateElements(elements)

        expect(results).toHaveLength(2)
        expect(results.every(result => result.valid)).toBe(true)
      })

      it('should detect mixed valid and invalid elements', async () => {
        const elements: ShapeElement[] = [
          {
            id: 'rect-valid',
            type: 'RECTANGLE',
            position: { x: 100, y: 100, z: 0 },
            properties: { width: 200, height: 150 },
          },
          {
            id: 'circle-invalid',
            type: 'CIRCLE',
            position: { x: 300, y: 300, z: 0 },
            properties: { radius: -50 }, // Invalid negative radius
          },
        ]

        const results = await validationService.validateElements(elements)

        expect(results).toHaveLength(2)
        expect(results[0].valid).toBe(true)
        expect(results[1].valid).toBe(false)
      })

      it('should handle empty array', async () => {
        const results = await validationService.validateElements([])

        expect(results).toHaveLength(0)
      })
    })

    describe('error handling', () => {
      it('should handle null element gracefully', async () => {
        try {
          const result = await validationService.validateElement(null as any)
          expect(result.valid).toBe(false)
          expect(result.errors.length).toBeGreaterThan(0)
        }
        catch (error) {
          // If it throws an error, that's also acceptable behavior
          expect(error).toBeDefined()
        }
      })

      it('should handle undefined element gracefully', async () => {
        try {
          const result = await validationService.validateElement(undefined as any)
          expect(result.valid).toBe(false)
          expect(result.errors.length).toBeGreaterThan(0)
        }
        catch (error) {
          // If it throws an error, that's also acceptable behavior
          expect(error).toBeDefined()
        }
      })

      it('should handle malformed element gracefully', async () => {
        const malformedElement = {
          id: 123, // Should be string
          type: null, // Should be string
          position: 'invalid', // Should be object
        } as any

        const result = await validationService.validateElement(malformedElement)

        expect(result.valid).toBe(false)
        expect(result.errors.length).toBeGreaterThan(0)
      })
    })
  })
})
