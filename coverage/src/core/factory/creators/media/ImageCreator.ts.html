
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/core/factory/creators/media/ImageCreator.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> / <a href="index.html">src/core/factory/creators/media</a> ImageCreator.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/121</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/121</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// Params types from ElementFactory
import type { CreateImageParams } from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point'
// Corrected import: Image is the main interface for image elements
import type { Image } from '@/types/core/element/image/imageElementTypes'
<span class="cstat-no" title="statement not covered" >import { CoreError, ErrorType } from '@/services/system/error-service'</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >import { ElementType as CoreElementType } from '@/types/core/elementDefinitions'</span>
&nbsp;
/**
 * Creator for Image Media Elements
 *
 * @remarks
 * This class implements the {@link ShapeCreator} interface to specialize in creating
 * Image ({@link CoreElementType.IMAGE}) media elements.
 *
 * @module core/factory/creators/media/ImageCreator
 * @see {@link ShapeCreator}
 * @see {@link CoreElementType.IMAGE}
 * @see {@link CreateImageParams}
 */
<span class="cstat-no" title="statement not covered" >import { ShapeCreator } from '../shape/ShapeCreator'</span>
&nbsp;
// Helper type for the return of getProperties, matching direct props of Image interface
// Ensure this type is defined before its first use.
interface ImageSpecificProps {
  src: string
  width: number
  height: number
  alt?: string
  sourceType: 'url' | 'svg_inline_data'
  // Allow other properties to be merged in
  [key: string]: unknown
}
&nbsp;
<span class="cstat-no" title="statement not covered" >export default class ImageCreator extends ShapeCreator&lt;Image, CreateImageParams&gt; {</span>
<span class="cstat-no" title="statement not covered" >  constructor() {</span>
<span class="cstat-no" title="statement not covered" >    super(CoreElementType.IMAGE)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // This method now prepares the complete 'properties' object for the ShapeModel
<span class="cstat-no" title="statement not covered" >  protected getProperties(params: CreateImageParams, generatedId: string): ImageSpecificProps {</span>
    // console.warn(`[ImageCreator getProperties START] for ID: ${generatedId}. params.properties:`, JSON.stringify(params.properties))
<span class="cstat-no" title="statement not covered" >    const customPropsFromParams = params.properties || {}</span>
    // console.warn(`[ImageCreator getProperties] customPropsFromParams (after params.properties || {}):`, JSON.stringify(customPropsFromParams))
&nbsp;
    // Default to 'url' if sourceType is not provided or invalid
<span class="cstat-no" title="statement not covered" >    const sourceType: 'url' | 'svg_inline_data'</span>
<span class="cstat-no" title="statement not covered" >            = customPropsFromParams.sourceType === 'svg_inline_data'</span>
<span class="cstat-no" title="statement not covered" >              ? 'svg_inline_data' // Check customProps first</span>
<span class="cstat-no" title="statement not covered" >              : params.sourceType === 'svg_inline_data'</span>
<span class="cstat-no" title="statement not covered" >                ? 'svg_inline_data'</span>
<span class="cstat-no" title="statement not covered" >                : 'url' // Then params</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const defaultImageSrc = '/public/icon/image.svg'</span>
<span class="cstat-no" title="statement not covered" >    const inputSrc = (customPropsFromParams.src as string) ?? (params.src as string) // Prioritize from customProps</span>
<span class="cstat-no" title="statement not covered" >    let finalSrc = defaultImageSrc</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (inputSrc != null &amp;&amp; typeof inputSrc === 'string' &amp;&amp; inputSrc.trim() !== '') {</span>
<span class="cstat-no" title="statement not covered" >      finalSrc = inputSrc.trim()</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
      // const idForLog = params.id || generatedId; // Unused variable
      // console.warn is kept for debugging, can be removed if too noisy
      // console.warn(`[ImageCreator getProperties] 'src' for ID '${idForLog}' is empty or invalid. Using default: "${finalSrc}". Input was:`, inputSrc);
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const widthInput = (customPropsFromParams.width as number) ?? (params.width as number) // Prioritize</span>
<span class="cstat-no" title="statement not covered" >    let finalWidth = 100 // Default</span>
<span class="cstat-no" title="statement not covered" >    if (typeof widthInput === 'number' &amp;&amp; widthInput &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      finalWidth = widthInput</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else if (widthInput !== undefined) {</span>
      // const idForLog = params.id || generatedId; // Unused variable
      // console.warn(`[ImageCreator getProperties] Invalid 'width' for ID '${idForLog}': ${widthInput}. Using default ${finalWidth}.`);
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const heightInput = (customPropsFromParams.height as number) ?? (params.height as number) // Prioritize</span>
<span class="cstat-no" title="statement not covered" >    let finalHeight = 100 // Default</span>
<span class="cstat-no" title="statement not covered" >    if (typeof heightInput === 'number' &amp;&amp; heightInput &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      finalHeight = heightInput</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else if (heightInput !== undefined) {</span>
      // const idForLog = params.id || generatedId; // Unused variable
      // console.warn(`[ImageCreator getProperties] Invalid 'height' for ID '${idForLog}': ${heightInput}. Using default ${finalHeight}.`);
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const altText = (customPropsFromParams.alt as string) ?? (params.alt as string) ?? `Image ${(params.id ?? generatedId).substring(0, 5)}`</span>
&nbsp;
    // Merge all properties: customPropsFromParams takes precedence for overlaps,
    // then specific image props, then remaining params.properties (though most are covered)
<span class="cstat-no" title="statement not covered" >    const returnedProps = {</span>
      // 设置默认的成本相关属性
<span class="cstat-no" title="statement not covered" >      costUnitPrice: 1,</span>
<span class="cstat-no" title="statement not covered" >      costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1</span>
<span class="cstat-no" title="statement not covered" >      costBasis: 'unit',</span>
      // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
<span class="cstat-no" title="statement not covered" >      computedAreaStatus: 'none',</span>
<span class="cstat-no" title="statement not covered" >      computedPerimeterStatus: 'none',</span>
<span class="cstat-no" title="statement not covered" >      computedLengthStatus: 'none',</span>
      // 然后合并其他属性
<span class="cstat-no" title="statement not covered" >      ...customPropsFromParams, // Start with all properties passed in params.properties</span>
<span class="cstat-no" title="statement not covered" >      src: finalSrc,</span>
<span class="cstat-no" title="statement not covered" >      sourceType,</span>
<span class="cstat-no" title="statement not covered" >      width: finalWidth,</span>
<span class="cstat-no" title="statement not covered" >      height: finalHeight,</span>
<span class="cstat-no" title="statement not covered" >      alt: altText,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    // console.warn(`[ImageCreator getProperties END] Props to be returned:`, JSON.stringify(returnedProps))
<span class="cstat-no" title="statement not covered" >    return returnedProps</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  public async create(params: CreateImageParams): Promise&lt;Image&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (!params.id) {</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(ErrorType.InvalidPayload, 'ImageCreator requires an ID in params.')</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    const id = params.id</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const commonProps = this.createCommonProperties(id, params) // type, majorCat, minorCat, zLevelId etc. are here</span>
&nbsp;
    // All shape-specific properties, including src, width, height, and custom ones like category,
    // are now prepared by getProperties and will be assigned to the 'properties' field.
<span class="cstat-no" title="statement not covered" >    const allShapeProperties = this.getProperties(params, id)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const imageElement = {</span>
<span class="cstat-no" title="statement not covered" >      ...commonProps, // Common props (id, type, position, rotation, categories, etc.)</span>
<span class="cstat-no" title="statement not covered" >      properties: allShapeProperties, // All other props (src, width, height, category, fill, stroke, etc.)</span>
<span class="cstat-no" title="statement not covered" >    } as Image // Cast to Image, acknowledging the structural difference to be resolved in Image type def</span>
&nbsp;
    // Adjust metadata name if it's the generic default one
    // This check needs to access commonProps.type and id correctly.
<span class="cstat-no" title="statement not covered" >    const defaultNamePattern = `${commonProps.type ?? CoreElementType.IMAGE}-${id.substring(0, 8)}`</span>
<span class="cstat-no" title="statement not covered" >    if (imageElement.metadata &amp;&amp; imageElement.metadata.name === defaultNamePattern) {</span>
      // Use name from params.metadata if available, otherwise from params.properties.name, or a more specific default.
<span class="cstat-no" title="statement not covered" >      imageElement.metadata.name = params.metadata?.name ?? (allShapeProperties.name as string) ?? `${commonProps.type ?? 'Element'} ${id.substring(0, 8)}`</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else if (imageElement.metadata &amp;&amp; params.metadata?.name != null &amp;&amp; params.metadata.name !== '') {</span>
<span class="cstat-no" title="statement not covered" >      imageElement.metadata.name = params.metadata.name // Ensure explicit metadata name is preferred</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else if (imageElement.metadata &amp;&amp; (imageElement.metadata.name == null || imageElement.metadata.name === '') &amp;&amp; allShapeProperties.name != null &amp;&amp; allShapeProperties.name !== '') {</span>
<span class="cstat-no" title="statement not covered" >      imageElement.metadata.name = allShapeProperties.name as string // Fallback to name from properties if metadata.name is initially empty</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // console.warn(`[ImageCreator create] Successfully created imageElement (ID: ${id}):`, JSON.stringify(imageElement, null, 2))
<span class="cstat-no" title="statement not covered" >    return imageElement</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  public async createDefault(id: string, position: PointData): Promise&lt;Image&gt; {</span>
    // console.warn(`[ImageCreator createDefault] Creating default image ID: ${id} at position:`, position)
&nbsp;
<span class="cstat-no" title="statement not covered" >    const now = Date.now()</span>
    // For createDefault, the properties are explicitly defined here.
<span class="cstat-no" title="statement not covered" >    const defaultProperties = {</span>
<span class="cstat-no" title="statement not covered" >      src: '/public/icon/image.svg', // 设置默认图片为image.svg</span>
<span class="cstat-no" title="statement not covered" >      width: 150,</span>
<span class="cstat-no" title="statement not covered" >      height: 150,</span>
<span class="cstat-no" title="statement not covered" >      alt: 'Placeholder Image',</span>
<span class="cstat-no" title="statement not covered" >      sourceType: 'url' as 'url' | 'svg_inline_data', // Ensure type correctness</span>
      // 设置默认的成本相关属性
<span class="cstat-no" title="statement not covered" >      costUnitPrice: 1,</span>
<span class="cstat-no" title="statement not covered" >      costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1</span>
<span class="cstat-no" title="statement not covered" >      costBasis: 'unit',</span>
      // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
<span class="cstat-no" title="statement not covered" >      computedAreaStatus: 'none',</span>
<span class="cstat-no" title="statement not covered" >      computedPerimeterStatus: 'none',</span>
<span class="cstat-no" title="statement not covered" >      computedLengthStatus: 'none',</span>
      // Add any other core properties expected for a default image, like fill/stroke if applicable.
      // For an IMAGE type, fill/stroke might not be standard, but if this creator is used for
      // other types that look like images but need a background, they could be added here.
      // e.g. fill: 'transparent', stroke: 'none', strokeWidth: 0
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const params: CreateImageParams = {</span>
<span class="cstat-no" title="statement not covered" >      id,</span>
<span class="cstat-no" title="statement not covered" >      type: CoreElementType.IMAGE, // This is for a default *IMAGE* element</span>
<span class="cstat-no" title="statement not covered" >      position,</span>
<span class="cstat-no" title="statement not covered" >      properties: defaultProperties, // Pass the defined default properties</span>
<span class="cstat-no" title="statement not covered" >      metadata: {</span>
<span class="cstat-no" title="statement not covered" >        name: `Default Image ${id.substring(0, 8)}`,</span>
<span class="cstat-no" title="statement not covered" >        createdAt: now,</span>
<span class="cstat-no" title="statement not covered" >        updatedAt: now,</span>
<span class="cstat-no" title="statement not covered" >      },</span>
      // Common style and behavior props are typically part of BaseElement/CommonElementParams,
      // so they are set via createCommonProperties or directly in the final assignment.
      // However, if some visual defaults (like opacity) are desired *within* properties for some reason,
      // they could be added to defaultProperties. Otherwise, they are handled by commonProps.
<span class="cstat-no" title="statement not covered" >      visible: true,</span>
<span class="cstat-no" title="statement not covered" >      locked: false,</span>
<span class="cstat-no" title="statement not covered" >      rotation: 0,</span>
<span class="cstat-no" title="statement not covered" >      selectable: true,</span>
<span class="cstat-no" title="statement not covered" >      draggable: true,</span>
<span class="cstat-no" title="statement not covered" >      showHandles: true,</span>
<span class="cstat-no" title="statement not covered" >      opacity: 1,</span>
      // Note: fill, stroke, strokeWidth if they are common and NOT part of specific properties
      // should be passed at the root of params to be picked up by createCommonProperties if it expects them.
      // If they are truly part of the *shape's visual characteristics* (like a text's color),
      // they belong in the properties object. For a generic image, they might be less relevant
      // unless it's an SVG or has a background.
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // Create an object that conforms to the structure ShapeModel expects.
    // Create an object that conforms to the structure ShapeModel expects.
<span class="cstat-no" title="statement not covered" >    const common = this.createCommonProperties(id, params)</span>
<span class="cstat-no" title="statement not covered" >    const finalShape = {</span>
<span class="cstat-no" title="statement not covered" >      ...common,</span>
<span class="cstat-no" title="statement not covered" >      properties: defaultProperties as ImageSpecificProps, // Cast since defaultProperties is simpler than full ImageSpecificProps</span>
<span class="cstat-no" title="statement not covered" >    } as Image // Cast to Image</span>
&nbsp;
    // Ensure metadata name is consistent
<span class="cstat-no" title="statement not covered" >    if (finalShape.metadata) {</span>
<span class="cstat-no" title="statement not covered" >      finalShape.metadata.name = params.metadata?.name ?? `Default Image ${id.substring(0, 8)}`</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // console.warn(`[ImageCreator createDefault] Default imageElement (ID: ${id}):`, JSON.stringify(finalShape, null, 2))
<span class="cstat-no" title="statement not covered" >    return finalShape</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:28:25.559Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    