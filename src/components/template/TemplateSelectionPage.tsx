/**
 * Template Selection Page
 *
 * A landing page component that allows users to choose between
 * starting with a blank canvas or selecting from available templates.
 */

import type { Template } from '@/types/template'
import { Plus } from 'lucide-react'
import React, { useCallback, useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { templateService } from '@/services/template/templateService'

interface TemplateSelectionPageProps {
  /** Callback when user chooses to start with blank canvas */
  onStartBlank: () => void
  /** Callback when user selects a template */
  onSelectTemplate: (template: Template) => void
  /** Whether to show the page */
  isVisible: boolean
}

/**
 * Template selection page component
 */
export const TemplateSelectionPage: React.FC<TemplateSelectionPageProps> = ({
  onStartBlank,
  onSelectTemplate,
  isVisible,
}) => {
  const [templates, setTemplates] = useState<Template[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  /**
   * Load all available templates
   */
  const loadTemplates = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      const loadedTemplates = await templateService.getTemplates()
      setTemplates(loadedTemplates)
    }
    catch (err) {
      setError(err instanceof Error ? err.message : '加载模板失败')
    }
    finally {
      setIsLoading(false)
    }
  }, [])

  /**
   * Load templates on component mount
   */
  useEffect(() => {
    if (isVisible) {
      loadTemplates().catch((error) => {
        console.error('Failed to load templates:', error)
        setError('Failed to load templates. Please try again.')
      })
    }
  }, [isVisible, loadTemplates])

  if (!isVisible) {
    return null
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Welcome to RenoPilot</h1>
          <p className="text-xl text-muted-foreground mb-6">
            Choose a template to start designing, or begin with a blank canvas
          </p>

          {/* Quick Start Options */}
          <div className="flex justify-center gap-4 mb-8">
            <Button
              size="lg"
              onClick={onStartBlank}
              className="flex items-center gap-2 bg-primary text-primary-foreground hover:bg-primary/90"
            >
              <Plus className="w-5 h-5" />
              Blank Canvas
            </Button>
          </div>
        </div>

        {/* Template Section */}
        <div className="mb-6">
          <h2 className="text-2xl font-semibold mb-4 text-center">Design Templates</h2>
        </div>

        {/* Template Grid */}
        <div className="w-full">
          {isLoading
            ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                  <p>Loading templates...</p>
                </div>
              )
            : error !== null && error !== undefined && error.trim() !== ''
              ? (
                  <div className="text-center py-12">
                    <p className="text-destructive mb-4">{error}</p>
                    <Button
                      onClick={() => {
                        loadTemplates().catch((error) => {
                          console.error('Failed to retry loading templates:', error)
                        })
                      }}
                    >
                      Retry
                    </Button>
                  </div>
                )
              : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {templates.map(template => (
                      <Card
                        key={template.metadata.id}
                        className="cursor-pointer transition-all hover:shadow-lg hover:scale-105"
                        onClick={() => onSelectTemplate(template)}
                      >

                        <CardHeader className="p-4">
                          <div className="aspect-video bg-muted rounded-md mb-3 overflow-hidden">
                            <img
                              src={template.metadata.previewImage}
                              alt={template.metadata.name}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement
                                target.src = '/placeholder-template.png'
                              }}
                            />
                          </div>
                          <CardTitle className="text-lg text-center">{template.metadata.name}</CardTitle>
                          <CardDescription className="text-sm text-center">
                            {template.metadata.description}
                          </CardDescription>
                        </CardHeader>
                      </Card>
                    ))}
                  </div>
                )}
        </div>
      </div>
    </div>
  )
}
