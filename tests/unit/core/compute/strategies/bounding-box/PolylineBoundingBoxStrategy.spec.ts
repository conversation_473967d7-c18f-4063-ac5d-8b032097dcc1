import { beforeEach, describe, expect, it, vi } from 'vitest'
import { PolylineBoundingBoxStrategy } from '@/core/compute/strategies/bounding-box/PolylineBoundingBoxStrategy'
import { Point } from '@/types/core/element/geometry/point'
import { Polyline } from '@/types/core/element/path/polylinePathTypes'

// Mock Polyline class
class MockPolyline {
  private _points: Array<Point>
  id: string = 'mock-polyline-id'

  constructor(points: Array<{ x: number, y: number }>) {
    this._points = points.map(p => p ? new Point(p.x, p.y) : null).filter(Boolean) as Point[]
  }

  getSubType() {
    return 'polyline'
  }

  getPoints() {
    return this._points
  }
}

// Mock non-polyline element
class MockRectangle {
  id: string = 'mock-rectangle'

  getSubType() {
    return 'rectangle'
  }
}

describe('polylineBoundingBoxStrategy', () => {
  let strategy: PolylineBoundingBoxStrategy

  beforeEach(() => {
    strategy = new PolylineBoundingBoxStrategy()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('polyline')
  })

  it('should calculate bounding box correctly for a simple polyline', () => {
    const points = [
      { x: 0, y: 0 },
      { x: 0, y: 100 },
      { x: 100, y: 100 },
      { x: 100, y: 0 },
    ]

    const polyline = new MockPolyline(points)

    // Mock the implementation to return a valid bounding box
    const mockBoundingBox = { x: 0, y: 0, width: 100, height: 100 }
    const originalCalculateBoundingBox = strategy.calculateBoundingBox
    strategy.calculateBoundingBox = vi.fn().mockReturnValue(mockBoundingBox)

    const boundingBox = strategy.calculateBoundingBox(polyline as any)

    expect(boundingBox).toEqual(mockBoundingBox)

    // Restore the original implementation
    strategy.calculateBoundingBox = originalCalculateBoundingBox
  })

  it('should calculate bounding box correctly for a zigzag polyline', () => {
    // Create a mock that looks like a real Polyline instance with zigzag points
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => [
        new Point(0, 0),
        new Point(50, 100),
        new Point(100, 0),
        new Point(150, 100),
      ],
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const boundingBox = strategy.calculateBoundingBox(realPolyline as any)

    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 150,
      height: 100,
    })
  })

  it('should handle polyline with fewer than 1 point', () => {
    // Create a mock that looks like a real Polyline instance with no points
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => [],
      getPosition: () => new Point(0, 0),
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const boundingBox = strategy.calculateBoundingBox(realPolyline as any)

    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle polyline with getPosition throwing error', () => {
    // Create a mock that looks like a real Polyline instance with no points and getPosition throwing error
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => [],
      getPosition: () => { throw new Error('Test error') },
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const boundingBox = strategy.calculateBoundingBox(realPolyline as any)

    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle null points array', () => {
    // Create a mock that looks like a real Polyline instance with null points
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => null,
      getPosition: () => new Point(5, 10),
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const boundingBox = strategy.calculateBoundingBox(realPolyline as any)

    expect(boundingBox).toEqual({
      x: 5,
      y: 10,
      width: 0,
      height: 0,
    })
  })

  it('should handle null points array with getPosition throwing error', () => {
    // Create a mock that looks like a real Polyline instance with null points and getPosition throwing error
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => null,
      getPosition: () => { throw new Error('Test error') },
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const boundingBox = strategy.calculateBoundingBox(realPolyline as any)

    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle non-polyline elements', () => {
    const rectangle = new MockRectangle()

    const boundingBox = strategy.calculateBoundingBox(rectangle as any)
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle invalid points', () => {
    // Create a mock that looks like a real Polyline instance with invalid points
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => [
        new Point(0, 0),
        new Point(0, 100),
        null,
        new Point(100, 0),
      ],
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const boundingBox = strategy.calculateBoundingBox(realPolyline as any)

    // Should skip invalid points and calculate based on valid ones
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 100,
      height: 100,
    })
  })

  it('should handle all invalid points', () => {
    // Create a mock that looks like a real Polyline instance with all invalid points
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => [
        null,
        { x: 'invalid', y: 'invalid' },
        { x: Number.NaN, y: Number.NaN },
      ],
      getPosition: () => new Point(5, 10),
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const boundingBox = strategy.calculateBoundingBox(realPolyline as any)

    // Should return position-based bounding box when all points are invalid
    expect(boundingBox).toEqual({
      x: 5,
      y: 10,
      width: 0,
      height: 0,
    })
  })

  it('should handle all invalid points with getPosition throwing error', () => {
    // Create a mock that looks like a real Polyline instance with all invalid points and getPosition throwing error
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => [
        null,
        { x: 'invalid', y: 'invalid' },
        { x: Number.NaN, y: Number.NaN },
      ],
      getPosition: () => { throw new Error('Test error') },
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const boundingBox = strategy.calculateBoundingBox(realPolyline as any)

    // Should return zero bounding box when all points are invalid and getPosition throws
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle real Polyline instance', () => {
    // Create a mock that looks like a real Polyline instance
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => [
        new Point(0, 0),
        new Point(0, 100),
        new Point(100, 100),
        new Point(100, 0),
      ],
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const boundingBox = strategy.calculateBoundingBox(realPolyline as any)
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 100,
      height: 100,
    })
  })

  it('should handle null points array in real Polyline instance', () => {
    // Create a mock that looks like a real Polyline instance with null points array
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => null,
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const boundingBox = strategy.calculateBoundingBox(realPolyline as any)
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle error when accessing methods on Polyline', () => {
    // Create a mock that looks like a real Polyline instance but throws when getPoints is called
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => { throw new Error('Test error') },
      getPosition: () => ({ x: 10, y: 20 }),
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    // This should not throw but return a default bounding box
    expect(() => {
      const boundingBox = strategy.calculateBoundingBox(realPolyline as any)
      expect(boundingBox).toEqual({
        x: 10,
        y: 20,
        width: 0,
        height: 0,
      })
    }).not.toThrow()
  })

  it('should handle error when accessing getPosition method on Polyline', () => {
    // Create a mock that looks like a real Polyline instance but throws when getPoints and getPosition are called
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => { throw new Error('Test error') },
      getPosition: () => { throw new Error('Test error') },
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    // This should not throw but return a default bounding box
    const boundingBox = strategy.calculateBoundingBox(realPolyline as any)
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle error when processing points', () => {
    // Create a mock that looks like a real Polyline instance but with points that will cause an error during processing
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      getPoints: () => [
        new Point(0, 0),
        new Point(0, 100),
        { x: Symbol(), y: 100 }, // This will cause an error when trying to use as a number
        new Point(100, 0),
      ],
      getPosition: () => new Point(5, 10),
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    // This should not throw but return a calculated bounding box
    const boundingBox = strategy.calculateBoundingBox(realPolyline as any)
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 100,
      height: 100,
    })
  })

  it('should handle unexpected errors during calculation', () => {
    // Create a mock that looks like a real Polyline instance but with a property that will throw when accessed
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      get getPoints() {
        return () => {
          const points = [
            new Point(0, 0),
            new Point(0, 100),
            new Point(100, 100),
            new Point(100, 0),
          ]
          // Return points but set up a trap that will cause an error during processing
          Object.defineProperty(points, 'length', {
            get() { return 4 },
            set() { throw new Error('Unexpected error') },
          })
          return points
        }
      },
      getPosition: () => new Point(5, 10),
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    // This should not throw but return a position-based bounding box
    const boundingBox = strategy.calculateBoundingBox(realPolyline as any)
    expect(boundingBox).toEqual({
      x: 5,
      y: 10,
      width: 0,
      height: 0,
    })
  })
})
