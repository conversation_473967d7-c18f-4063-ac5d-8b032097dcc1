{
  "extends": "./tsconfig.paths.json",
  "compilerOptions": {
    "composite": false,
    "target": "ES2022",
    "jsx": "react-jsx",
    "lib": ["ES2023", "DOM", "DOM.Iterable"],
    "moduleDetection": "force",
    "baseUrl": ".",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "strict": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitAny": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noEmit": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    // "paths" is now inherited from tsconfig.paths.json
    "skipLibCheck": true
  },
  "include": [
    "src",
    "src/types/**/*.d.ts",
    "tests",
    "scripts",
    "vite.config.ts",
    "playwright.config.ts",
    "eslint.config.ts",
    "commitlint.config.ts",
    "postcss.config.js",
    "tailwind.config.js",
    "vitest.config.ts"
  ],
  "exclude": ["node_modules"]
}
