import type { Image } from '@/types/core/element/image/imageElementTypes'
/**
 * Utility SVG Renderers
 *
 * This module provides rendering functions for utility elements,
 * including text and images.
 *
 * - `renderText` generates SVG markup for text elements, supporting
 *   position, font, color, and rotation.
 * - `renderImage` generates SVG markup for image elements, supporting
 *   position, size, rotation, and Base64 encoding for image sources.
 *
 * These functions generate SVG markup for the corresponding utility elements.
 *
 * @module render/utility
 */
import type { Text } from '@/types/core/element/text/textElementTypes'
import { getTransform } from './general'

export async function renderText(e: Text): Promise<string> {
  const { position, fontSize, fontFamily, fill, rotation } = e
  const content = e.text ?? 'Text'
  const transform = getTransform(rotation, position)
  return (
    `<text x="${position.x}" y="${position.y}" font-size="${fontSize}" `
    + `font-family='${fontFamily}' fill="${fill}"${transform}>${content}</text>`
  )
}

export async function renderImage(e: Image): Promise<string> {
  const { position, rotation } = e
  const { width, height, src, opacity = 1 } = e.properties!
  const x = position.x - width! / 2
  const y = position.y - height! / 2
  const transform = getTransform(rotation, position)

  // Convert image paths to Base64 data URIs
  const base64 = await fetch(src!)
    .then(async res => res.blob())
    .then(async blob => new Promise<string>((resolve) => {
      const reader = new FileReader()
      reader.onloadend = () => resolve(reader.result as string)
      reader.readAsDataURL(blob)
    }))

  return (
    `<image x="${x}" y="${y}" width="${width}" height="${height}" `
    + `href="${base64}" opacity="${opacity}"${transform} />`
  )
}
