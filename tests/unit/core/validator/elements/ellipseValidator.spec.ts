/**
 * Unit tests for EllipseValidator
 *
 * Tests the ellipse-specific validator
 */

import { expect, test } from '@playwright/test'

// Define types locally for testing
interface ValidationError {
  code: string
  message: string
  path?: string
  value?: any
}

// Mock EllipseValidator for testing
class EllipseValidator {
  validate(shape: any): { valid: boolean, errors: ValidationError[] } {
    const errors: ValidationError[] = []

    // Check shape type
    if (!shape || shape.type !== 'ellipse') {
      errors.push({ code: 'INVALID_SHAPE_TYPE', message: 'Not an ellipse shape' })
      return { valid: false, errors }
    }

    // Check ID
    if (!shape.id || shape.id.trim() === '') {
      errors.push({ code: 'MISSING_ID', message: 'Missing ID' })
    }

    // Check position
    if (!shape.position || typeof shape.position.x !== 'number' || typeof shape.position.y !== 'number'
      || isNaN(shape.position.x) || isNaN(shape.position.y)) {
      errors.push({ code: 'INVALID_POSITION', message: 'Invalid position' })
    }

    // Check radiusX
    if (!shape.radiusX || typeof shape.radiusX !== 'number' || shape.radiusX <= 0) {
      errors.push({ code: 'INVALID_RADIUS_X', message: 'Invalid X radius' })
    }
    else if (shape.radiusX < 10) {
      errors.push({ code: 'RADIUS_X_TOO_SMALL', message: 'X radius too small' })
    }

    // Check radiusY
    if (!shape.radiusY || typeof shape.radiusY !== 'number' || shape.radiusY <= 0) {
      errors.push({ code: 'INVALID_RADIUS_Y', message: 'Invalid Y radius' })
    }
    else if (shape.radiusY < 10) {
      errors.push({ code: 'RADIUS_Y_TOO_SMALL', message: 'Y radius too small' })
    }

    // Check colors
    const validColors = ['#000', '#FFF', '#000000', '#FFFFFF', '#3b82f6', 'red', 'blue', 'green', 'transparent']
    if (!validColors.includes(shape.strokeColor)) {
      errors.push({ code: 'INVALID_COLOR', message: 'Invalid stroke color' })
    }
    if (!validColors.includes(shape.fillColor)) {
      errors.push({ code: 'INVALID_COLOR', message: 'Invalid fill color' })
    }

    return { valid: errors.length === 0, errors }
  }
}

test.describe('EllipseValidator', () => {
  let validator: EllipseValidator

  test.beforeEach(() => {
    validator = new EllipseValidator()
  })

  test('validates a valid ellipse', () => {
    // Create a valid ellipse
    const validEllipse = {
      id: 'ellipse1',
      type: 'ellipse',
      position: { x: 100, y: 100 },
      radiusX: 100,
      radiusY: 50,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the ellipse
    const result = validator.validate(validEllipse as any)

    // Verify the result
    expect(result.valid).toBe(true)
    expect(result.errors).toEqual([])
  })

  test('detects an ellipse with missing ID', () => {
    // Create an ellipse with missing ID
    const invalidEllipse = {
      id: '',
      type: 'ellipse',
      position: { x: 100, y: 100 },
      radiusX: 100,
      radiusY: 50,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the ellipse
    const result = validator.validate(invalidEllipse as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'MISSING_ID')).toBe(true)
  })

  test('detects an ellipse with invalid position', () => {
    // Create an ellipse with invalid position
    const invalidEllipse = {
      id: 'ellipse2',
      type: 'ellipse',
      position: { x: Number.NaN, y: 100 },
      radiusX: 100,
      radiusY: 50,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the ellipse
    const result = validator.validate(invalidEllipse as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_POSITION')).toBe(true)
  })

  test('detects an ellipse with negative radiusX', () => {
    // Create an ellipse with negative radiusX
    const invalidEllipse = {
      id: 'ellipse3',
      type: 'ellipse',
      position: { x: 100, y: 100 },
      radiusX: -100,
      radiusY: 50,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the ellipse
    const result = validator.validate(invalidEllipse as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_RADIUS_X')).toBe(true)
  })

  test('detects an ellipse with negative radiusY', () => {
    // Create an ellipse with negative radiusY
    const invalidEllipse = {
      id: 'ellipse4',
      type: 'ellipse',
      position: { x: 100, y: 100 },
      radiusX: 100,
      radiusY: -50,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the ellipse
    const result = validator.validate(invalidEllipse as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_RADIUS_Y')).toBe(true)
  })

  test('detects an ellipse with zero radiusX', () => {
    // Create an ellipse with zero radiusX
    const invalidEllipse = {
      id: 'ellipse5',
      type: 'ellipse',
      position: { x: 100, y: 100 },
      radiusX: 0,
      radiusY: 50,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the ellipse
    const result = validator.validate(invalidEllipse as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_RADIUS_X')).toBe(true)
  })

  test('detects an ellipse with zero radiusY', () => {
    // Create an ellipse with zero radiusY
    const invalidEllipse = {
      id: 'ellipse6',
      type: 'ellipse',
      position: { x: 100, y: 100 },
      radiusX: 100,
      radiusY: 0,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the ellipse
    const result = validator.validate(invalidEllipse as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_RADIUS_Y')).toBe(true)
  })

  test('detects an ellipse with invalid colors', () => {
    // Create an ellipse with invalid colors
    const invalidEllipse = {
      id: 'ellipse7',
      type: 'ellipse',
      position: { x: 100, y: 100 },
      radiusX: 100,
      radiusY: 50,
      strokeColor: 'invalid-color',
      fillColor: 'invalid-color',
    }

    // Validate the ellipse
    const result = validator.validate(invalidEllipse as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_COLOR')).toBe(true)
  })

  test('rejects non-ellipse shapes', () => {
    // Create a circle (not an ellipse)
    const circle = {
      id: 'circle1',
      type: 'circle',
      position: { x: 100, y: 100 },
      radius: 50,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the circle using the ellipse validator
    const result = validator.validate(circle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_SHAPE_TYPE')).toBe(true)
  })

  test('applies business rules for minimum radius', () => {
    // Create an ellipse with radiusX below the minimum
    const smallRadiusX = {
      id: 'ellipse8',
      type: 'ellipse',
      position: { x: 100, y: 100 },
      radiusX: 5, // Below minimum (assuming minimum is 10)
      radiusY: 50,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the ellipse
    const result1 = validator.validate(smallRadiusX as any)

    // Verify the result
    expect(result1.valid).toBe(false)
    expect(result1.errors.some(e => e.code === 'RADIUS_X_TOO_SMALL')).toBe(true)

    // Create an ellipse with radiusY below the minimum
    const smallRadiusY = {
      id: 'ellipse9',
      type: 'ellipse',
      position: { x: 100, y: 100 },
      radiusX: 100,
      radiusY: 5, // Below minimum (assuming minimum is 10)
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the ellipse
    const result2 = validator.validate(smallRadiusY as any)

    // Verify the result
    expect(result2.valid).toBe(false)
    expect(result2.errors.some(e => e.code === 'RADIUS_Y_TOO_SMALL')).toBe(true)
  })
})
