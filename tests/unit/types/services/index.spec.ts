// Import from the services index file
import type {
  // Core types
  ServiceResult,
} from '@/types/services/index'

import { describe, expect, it } from 'vitest'
import {
  // Direct exports
  AppEventType,
  ErrorTypes,
  EventCore,
  EventRegistry,
  // Namespaces
  EventTypes,
  HistoryTypes,
  KeyboardTypes,
  LoggingTypes,
  ShapeTypes,
  ValidationTypes,
} from '@/types/services/index'

describe('services Root Index File', () => {
  it('should export core service types', () => {
    // Test ServiceResult interface
    const result: ServiceResult<string> = {
      success: true,
      data: 'test',
      timestamp: Date.now(),
    }
    expect(result.success).toBe(true)
    expect(result.data).toBe('test')
  })

  it('should export namespaces', () => {
    // Test EventTypes namespace
    expect(EventTypes).toBeDefined()
    expect(EventTypes.AppEventType).toBeDefined()

    // Test EventCore namespace
    expect(EventCore).toBeDefined()

    // Test EventRegistry namespace
    expect(EventRegistry).toBeDefined()

    // Test ErrorTypes namespace
    expect(ErrorTypes).toBeDefined()

    // Test HistoryTypes namespace
    expect(HistoryTypes).toBeDefined()

    // Test LoggingTypes namespace
    expect(LoggingTypes).toBeDefined()

    // Test ValidationTypes namespace
    expect(ValidationTypes).toBeDefined()

    // Test KeyboardTypes namespace
    expect(KeyboardTypes).toBeDefined()

    // Test ShapeTypes namespace
    expect(ShapeTypes).toBeDefined()
  })

  it('should directly export AppEventType', () => {
    expect(AppEventType).toBeDefined()
  })
})
