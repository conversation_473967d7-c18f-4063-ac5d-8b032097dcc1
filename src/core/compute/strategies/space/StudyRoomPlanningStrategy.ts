// import Point from '@/types/core/element/geometry/point'; // Point interface (IPoint) // Unused
import type {
  ErgonomicsEvaluationResult,
  PathwayCheckResult,
  SpacePlanningStrategy,
  SpaceType,
} from '@/types/core/compute'
/**
 * Study Room Planning Strategy
 * Provides planning functionality for study rooms, including workspace ergonomics, bookshelf capacity, layout, and lighting requirements
 */
import type {
  Element,
  Shape,
  // Path, // Unused
  ShapeElement, // For properties access
} from '@/types/core/elementDefinitions'
// import {
//     calculateSpaceUtilization,
//     checkPathwaysWidth
//     // evaluateFurnitureLayout is not directly used by this strategy's interface methods
// } from '@/lib/utils/space/spacePlanning'; // Module not found
import { BoundingBoxClass } from '@/lib/utils/geometry' // BoundingBoxClass is exported from here
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  ElementType as CoreElementType, // For properties access
} from '@/types/core/elementDefinitions'

// Helper to get BoundingBoxClass from an Element
function getElementBoundingBoxClass(element: Element): BoundingBoxClass | null {
  if (element === null || element === undefined)
    return null
  if (element.type === CoreElementType.RECTANGLE || element.type === CoreElementType.SQUARE) {
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement.position !== null && shapeElement.position !== undefined && shapeElement.properties !== null && shapeElement.properties !== undefined
      && typeof shapeElement.properties.width === 'number'
      && typeof shapeElement.properties.height === 'number') {
      return new BoundingBoxClass(
        shapeElement.position.x - shapeElement.properties.width / 2,
        shapeElement.position.y - shapeElement.properties.height / 2,
        shapeElement.properties.width,
        shapeElement.properties.height,
      )
    }
  }
  if (element.type === CoreElementType.CIRCLE) {
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement.position !== null && shapeElement.position !== undefined && shapeElement.properties !== null && shapeElement.properties !== undefined
      && typeof shapeElement.properties.radius === 'number') {
      return new BoundingBoxClass(
        shapeElement.position.x - shapeElement.properties.radius,
        shapeElement.position.y - shapeElement.properties.radius,
        shapeElement.properties.radius * 2,
        shapeElement.properties.radius * 2,
      )
    }
  }
  console.warn(`[StudyRoomPlanningStrategy] getElementBoundingBoxClass: Bounding box for type ${element.type} not fully implemented or properties missing.`)
  return null
}

/**
 * Study Room Planning Strategy
 */
export class StudyRoomPlanningStrategy implements SpacePlanningStrategy {
  public calculateSpaceUtilization(/* elements: Element[], roomBoundaryElement: Element */): number { // Params unused due to commented out body
    // const roomBBoxClass = getElementBoundingBoxClass(roomBoundaryElement);
    // if (!roomBBoxClass) {
    //   throw new CoreError( ErrorType.INVALID_PARAMETER, 'Room boundary must be an element from which a bounding box can be derived.');
    // }
    // return calculateSpaceUtilization(elements, roomBBoxClass);
    console.warn('[StudyRoomPlanningStrategy] calculateSpaceUtilization is temporarily disabled due to missing dependencies.')
    return 0
  }

  public checkPathwayWidth(/* elements: Element[], pathways: Path.Line[], minWidth: number */): PathwayCheckResult[] { // Params unused due to commented out body
    // if (!Array.isArray(elements)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Elements must be an array');
    // if (!Array.isArray(pathways)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Pathways must be an array');
    // if (typeof minWidth !== 'number' || minWidth <= 0) {
    //   throw new CoreError(ErrorType.INVALID_PARAMETER, 'Minimum width must be a positive number');
    // }

    // const formattedPathways = pathways.map((path, index) => {
    //     const lineForUtil = {
    //         id: path.id ?? `pathway-${index}`,
    //         start: path.start as Point,
    //         end: path.end as Point,
    //         type: CoreElementType.LINE,
    //         getSubType: () => CoreElementType.LINE,
    //     } as any;
    //     return { id: path.id ?? `pathway-${index + 1}`, line: lineForUtil as import('@/lib/utils/element/path/lineImplementation').Line };
    // });

    // return checkPathwaysWidth(elements, formattedPathways, minWidth);
    console.warn('[StudyRoomPlanningStrategy] checkPathwayWidth is temporarily disabled due to missing dependencies.')
    return []
  }

  public evaluateErgonomics(
    elements: Element[],
    deskElement: Element,
    chairElement: Element,
  ): ErgonomicsEvaluationResult {
    if (!deskElement?.type || !chairElement?.type) {
      throw new CoreError(ErrorType.InvalidParameter, 'Must provide desk and chair elements with types.')
    }

    const issues: string[] = []
    const recommendations: string[] = []

    const deskShape = deskElement as unknown as Shape.Rectangle // Assuming desk is rectangular
    const chairShape = chairElement as unknown as Shape.Rectangle // Assuming chair base is rectangular for this check
    const chairShapeElement = chairElement as unknown as ShapeElement

    const deskHeight = deskShape.height ?? 0.75 // Desk surface height
    const deskDepth = deskShape.width ?? 0.6 // Assuming width is depth for a typical desk orientation
    const deskSurfaceWidth = deskShape.width ?? 1.2 // Assuming width is the working surface width

    if (deskHeight < 0.73 || deskHeight > 0.76) {
      issues.push(`Desk height of ${deskHeight.toFixed(2)}m is outside standard ergonomic range (0.73-0.76m).`)
    }
    if (deskDepth < 0.6) {
      issues.push(`Desk depth of ${deskDepth.toFixed(2)}m is less than recommended (min 0.6m).`)
    }
    if (deskSurfaceWidth < 1.0) { // Slightly less than 1.2m for very compact setups
      issues.push(`Desk surface width of ${deskSurfaceWidth.toFixed(2)}m may be insufficient (min 1.0-1.2m recommended).`)
    }

    const chairProperties = chairShapeElement.properties as { seatHeight?: number, isAdjustable?: boolean, hasLumbarSupport?: boolean } | undefined
    const chairSeatHeight = chairProperties?.seatHeight ?? (chairShape.height ? chairShape.height * 0.6 : 0.45)
    if (chairSeatHeight < 0.42 || chairSeatHeight > 0.53) { // General ergonomic chair height
      issues.push(`Chair seat height of ${chairSeatHeight.toFixed(2)}m is outside typical ergonomic range (0.42-0.53m).`)
    }
    if (!chairProperties?.isAdjustable) {
      recommendations.push('Consider an adjustable height chair for optimal ergonomics.')
    }
    if (!chairProperties?.hasLumbarSupport) {
      recommendations.push('Ensure the chair provides good lumbar support.')
    }

    const chairBackSpaceResult = this.checkBackSpace(chairElement, elements, 0.8) // Min 0.8m for chair movement
    if (!chairBackSpaceResult.isValid) {
      issues.push(`Insufficient space behind chair: ${chairBackSpaceResult.actualSpace.toFixed(2)}m (min 0.8m needed).`)
    }

    recommendations.push('Ensure monitor is at eye level or slightly below.')
    recommendations.push('Provide adequate task lighting to avoid eye strain.')

    return { isValid: issues.length === 0, issues, recommendations }
  }

  public calculateBookshelfCapacity(elements: Element[], bookshelfElements: Element[]): { totalShelfLength: number, estimatedBookCapacity: number, recommendations: string[] } {
    if (!Array.isArray(bookshelfElements) || bookshelfElements.length === 0) {
      throw new CoreError(ErrorType.InvalidParameter, 'Must provide at least one bookshelf element.')
    }

    let totalShelfLength = 0
    let totalShelfFootprintArea = 0

    for (const bookshelf of bookshelfElements) {
      const bs = bookshelf as unknown as Shape.Rectangle // Assuming bookshelves are rectangular
      const bsShapeElement = bookshelf as unknown as ShapeElement
      const bbox = getElementBoundingBoxClass(bsShapeElement) // Use helper to get BBox for footprint
      if (!bbox)
        continue

      const bookshelfProperties = bsShapeElement.properties as { levels?: number } | undefined
      const levelsValue = bookshelfProperties?.levels
      const shelfLevels = (typeof levelsValue === 'number' && levelsValue > 0) ? levelsValue : 5
      const shelfLengthPerLevel = bs.width ?? 0
      totalShelfLength += shelfLengthPerLevel * shelfLevels
      totalShelfFootprintArea += bbox.width * bbox.height // Footprint area
    }

    const estimatedBookCapacity = Math.floor(totalShelfLength / 0.3 * 20) // Approx 20 books per 30cm
    const recommendations: string[] = []

    if (totalShelfLength < 3)
      recommendations.push(`Total shelf length ${totalShelfLength.toFixed(2)}m is small; consider more shelving.`)
    recommendations.push(`Estimated capacity: ${estimatedBookCapacity} books.`)

    const roomArea = this.calculateRoomArea(elements)
    if (roomArea > 0) {
      const shelfAreaRatio = totalShelfFootprintArea / roomArea
      if (shelfAreaRatio > 0.25)
        recommendations.push(`Bookshelves occupy ${(shelfAreaRatio * 100).toFixed(0)}% of room area; ensure sufficient circulation space.`)
    }
    return { totalShelfLength, estimatedBookCapacity, recommendations }
  }

  public evaluateLighting(elements: Element[]): { isValid: boolean, issues: string[], recommendations: string[] } {
    const issues: string[] = []
    const recommendations: string[] = []
    let hasOverhead = false
    let hasTask = false
    let hasNatural = false

    for (const el of elements) {
      const lightType = (el as ShapeElement).properties?.lightType as string || el.type.toString() // Use customType or type
      if (lightType.includes('ceiling') || lightType.includes('pendant') || lightType.includes('recessed'))
        hasOverhead = true
      if (lightType.includes('desk') || lightType.includes('task') || lightType.includes('floor_lamp_task'))
        hasTask = true
      if (el.type === CoreElementType.WINDOW)
        hasNatural = true
    }

    if (!hasOverhead)
      issues.push('Lack of general overhead lighting.')
    if (!hasTask)
      issues.push('Lack of dedicated task lighting for study desk.')
    if (!hasNatural)
      recommendations.push('Consider maximizing natural light if possible.')

    recommendations.push('Aim for 300-500 lux for general study area, 500+ lux for task areas.')
    recommendations.push('Use lighting with color temperature of 4000K-5000K for focus.')
    return { isValid: issues.length === 0, issues, recommendations }
  }

  private checkBackSpace(element: Element, allElements: Element[], minSpace: number): { isValid: boolean, actualSpace: number } {
    const elBBox = getElementBoundingBoxClass(element)
    if (!elBBox)
      return { isValid: false, actualSpace: 0 }

    const backY = elBBox.position.y // Assuming back is -Y direction relative to element's orientation 0
    let minDistanceToObstacle = Infinity

    for (const other of allElements) {
      if (other.id === element.id)
        continue
      const otherBBox = getElementBoundingBoxClass(other)
      if (!otherBBox)
        continue

      // Check for obstacles directly "behind" (in -Y direction of element's local space, simplified here)
      // This simplified check assumes axis-aligned elements and "behind" means smaller Y.
      // A full check would consider element rotation.
      if (otherBBox.position.y + otherBBox.height <= backY) { // Obstacle is "above" or "behind" in screen coords
        // Check for horizontal overlap
        const xOverlap = Math.max(0, Math.min(elBBox.position.x + elBBox.width, otherBBox.position.x + otherBBox.width) - Math.max(elBBox.position.x, otherBBox.position.x))
        if (xOverlap > 0) {
          minDistanceToObstacle = Math.min(minDistanceToObstacle, backY - (otherBBox.position.y + otherBBox.height))
        }
      }
    }
    const actualSpace = (minDistanceToObstacle === Infinity) ? minSpace * 2 : minDistanceToObstacle // Assume ample if no obstacle
    return { isValid: actualSpace >= minSpace, actualSpace }
  }

  private calculateRoomArea(elements: Element[]): number {
    const roomElement = elements.find(el => (el as ShapeElement).properties?.isRoomBoundary === true || el.type === ('room_boundary' as CoreElementType))
    if (roomElement) {
      const bbox = getElementBoundingBoxClass(roomElement)
      return bbox ? bbox.width * bbox.height : 0
    }
    const overallBBox = this.calculateOverallBoundingBox(elements)
    return overallBBox ? overallBBox.width * overallBBox.height : 0
  }

  private calculateOverallBoundingBox(elements: Element[]): BoundingBoxClass | null {
    if (elements === null || elements === undefined || elements.length === 0)
      return null
    let minX = Infinity
    let minY = Infinity
    let maxX = -Infinity
    let maxY = -Infinity
    let hasBBox = false
    for (const element of elements) {
      const bbox = getElementBoundingBoxClass(element)
      if (bbox) {
        hasBBox = true
        minX = Math.min(minX, bbox.position.x)
        minY = Math.min(minY, bbox.position.y)
        maxX = Math.max(maxX, bbox.position.x + bbox.width)
        maxY = Math.max(maxY, bbox.position.y + bbox.height)
      }
    }
    return hasBBox ? new BoundingBoxClass(minX, minY, maxX - minX, maxY - minY) : null
  }

  public getSpaceType(): SpaceType {
    return 'study'
  }
}
