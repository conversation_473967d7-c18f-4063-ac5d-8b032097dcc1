/**
 * Test Setup File
 *
 * Provides global test environment configuration, including DOM element mocks and global mock functions
 */

import { vi } from 'vitest'
import '@testing-library/jest-dom/vitest'

// 确保全局SVG元素在测试环境中可用
if (typeof global.SVGElement === 'undefined') {
  global.SVGElement = window.SVGElement
}

if (typeof global.SVGSVGElement === 'undefined') {
  global.SVGSVGElement = window.SVGSVGElement
}

// 模拟SVG功能
if (typeof window.SVGSVGElement.prototype.createSVGPoint !== 'function') {
  window.SVGSVGElement.prototype.createSVGPoint = vi.fn(() => ({
    x: 0,
    y: 0,
    matrixTransform: vi.fn(matrix => ({ x: 0, y: 0 })),
  }))
}

// 模拟SVG矩阵功能
if (typeof window.SVGSVGElement.prototype.createSVGMatrix !== 'function') {
  window.SVGSVGElement.prototype.createSVGMatrix = vi.fn(() => ({
    a: 1,
    b: 0,
    c: 0,
    d: 1,
    e: 0,
    f: 0,
    multiply: vi.fn(() => window.SVGSVGElement.prototype.createSVGMatrix()),
    inverse: vi.fn(() => window.SVGSVGElement.prototype.createSVGMatrix()),
    translate: vi.fn(() => window.SVGSVGElement.prototype.createSVGMatrix()),
    scale: vi.fn(() => window.SVGSVGElement.prototype.createSVGMatrix()),
    rotate: vi.fn(() => window.SVGSVGElement.prototype.createSVGMatrix()),
  }))
}

// 模拟全局工具函数
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// 模拟全局定时器函数
global.requestAnimationFrame = vi.fn((callback) => {
  return setTimeout(() => callback(Date.now()), 0)
})

global.cancelAnimationFrame = vi.fn((id) => {
  clearTimeout(id)
})

// 模拟全局事件总线实例
vi.mock('@/services/event-bus', () => {
  const mockEventBus = {
    publish: vi.fn(),
    subscribe: vi.fn(),
    unsubscribe: vi.fn(),
    clear: vi.fn(),
    reset: vi.fn(),
    getSubscriptions: vi.fn(() => []),
  }

  return {
    getEventBus: vi.fn(() => mockEventBus),
    createEventBus: vi.fn(() => mockEventBus),
  }
})

// 模拟公共钩子
vi.mock('@/hooks/useEventBus', () => ({
  useEventBus: vi.fn(() => ({
    publish: vi.fn(),
    subscribe: vi.fn(),
    unsubscribe: vi.fn(),
    clear: vi.fn(),
    reset: vi.fn(),
    getSubscriptions: vi.fn(() => []),
  })),
}))

// 清理每次测试后的模拟状态
afterEach(() => {
  vi.clearAllMocks()
})

// 辅助测试工具
global.testUtils = {
  createMockMouseEvent: (x = 0, y = 0, button = 0) => ({
    clientX: x,
    clientY: y,
    button,
    stopPropagation: vi.fn(),
    preventDefault: vi.fn(),
  }),
  createSvgRef: () => ({
    current: document.createElementNS('http://www.w3.org/2000/svg', 'svg'),
  }),
}
