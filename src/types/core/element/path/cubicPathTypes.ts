/**
 * Defines the type for a cubic Bézier curve path element.
 *
 * @remarks
 * This module provides the `Cubic` interface, which extends {@link ShapeElement}
 * to include properties specific to defining a cubic Bézier curve, namely its
 * start point, end point, and two control points.
 *
 * @module types/core/element/path/cubicPathTypes
 */
import type { ShapeElement } from '../../elementDefinitions' // Corrected import path
import type Point from '@/types/core/element/geometry/point'

/**
 * Represents a cubic Bézier curve path, defined by a start point, an end point, and two control points.
 *
 * @remarks
 * A cubic Bézier curve is a smooth curve that passes through its start and end points,
 * and whose shape is influenced by two control points.
 * - The curve begins at `start` and ends at `end`.
 * - `control1` influences the curve's departure from `start`.
 * - `control2` influences the curve's approach to `end`.
 * The curve does not typically pass through the control points themselves.
 *
 * @example
 * ```typescript
 * const myCubicCurve: Cubic = {
 *   // Properties from ShapeElement (id, type, visible, etc.)
 *   id: 'cubic-001',
 *   type: 'cubic', // Or a more specific ElementType if defined
 *   visible: true,
 *   // ... other ShapeElement properties
 *   // Cubic-specific properties
 *   start: { x: 10, y: 10 },
 *   control1: { x: 50, y: 100 },
 *   control2: { x: 150, y: 100 },
 *   end: { x: 190, y: 10 }
 * };
 * ```
 *
 * @see {@link ShapeElement} for base shape properties.
 * @see {@link Point} for the structure of coordinate points.
 * @see {@link ../../../lib/utils/element/path/cubicImplementation.ts} for potential implementation details.
 */
export interface Cubic extends ShapeElement {
  /**
   * Properties specific to a Cubic Bezier curve element.
   */
  properties: ShapeElement['properties'] & {
    start: Point
    control1: Point
    control2: Point
    end: Point
    closed?: boolean
  }
}
