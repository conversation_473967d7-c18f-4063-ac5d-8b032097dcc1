# Custom React Hooks

Custom React Hooks in `src/hooks/` encapsulate reusable logic, often related to state management, side effects, or complex interactions. They help keep components clean and promote code reuse across the application.

## Key Custom Hooks and Their Responsibilities

### 1. `useEventBus.ts` (located in `src/hooks/core/`)

*   **Responsibility:** Provides a convenient way for React components to subscribe to and unsubscribe from events on the global `appEventBus`.
*   **Key Functionality:**
    *   Takes an event type (e.g., `AppEvent.ELEMENT_SELECTED`) and a handler function as arguments.
    *   Uses `useEffect` to subscribe the handler to the specified event when the component mounts or when the event type/handler changes.
    *   Ensures the handler is unsubscribed when the component unmounts to prevent memory leaks.
*   **Usage Example:**
    ```typescript
    // MyComponent.tsx
    import { useEventBus, AppEvent } from '@/hooks/core/useEventBus';

    const MyComponent = () => {
      useEventBus(AppEvent.SOME_EVENT, (payload) => {
        console.log('SOME_EVENT occurred:', payload);
      });
      // ...
    }
    ```
*   **Interactions:**
    *   `appEventBus`: The global event bus instance.

### 2. `useElementActions.ts`

*   **Responsibility:** Provides convenient access to the element action services (`ElementCreationService`, `ElementEditService`, `ElementDeleteService`, `ElementSelectionService`) from the `CoreCoordinator`.
*   **Key Functionality:**
    *   Typically retrieves the `CoreCoordinator` instance (e.g., from a React Context or a global singleton).
    *   Exposes the element action services directly or wraps their methods in memoized functions.
    *   This hook centralizes access to these core services, making it easier for components to interact with elements without directly depending on the `CoreCoordinator`'s full structure.
*   **Usage Example:**
    ```typescript
    // ShapeToolbar.tsx
    import useElementActions from '@/hooks/useElementActions';

    const ShapeToolbar = () => {
      const { createElement, deleteElement } = useElementActions();

      const handleAddRectangle = () => {
        createElement('rectangle', { x: 10, y: 10, width: 100, height: 50 });
      };
      // ...
    }
    ```
*   **Interactions:**
    *   `CoreCoordinator`: To access its element action service instances.

### 3. `useUndoRedo.ts`

*   **Responsibility:** Integrates with an undo/redo service (e.g., `HistoryService`) to provide components with functions to trigger undo and redo operations, and to get the current state of the undo/redo stacks (e.g., `canUndo`, `canRedo`).
*   **Key Functionality:**
    *   Retrieves the `HistoryService` instance.
    *   Exposes functions like `undo()`, `redo()`.
    *   Provides reactive state variables like `canUndo: boolean` and `canRedo: boolean` by subscribing to events from the `HistoryService` or by deriving them from the service's state.
*   **Usage Example:**
    ```typescript
    // EditMenu.tsx
    import useUndoRedo from '@/hooks/useUndoRedo';

    const EditMenu = () => {
      const { undo, redo, canUndo, canRedo } = useUndoRedo();

      return (
        <div>
          <button onClick={undo} disabled={!canUndo}>Undo</button>
          <button onClick={redo} disabled={!canRedo}>Redo</button>
        </div>
      );
    }
    ```
*   **Interactions:**
    *   `HistoryService` (or equivalent undo/redo manager).
    *   `EventBus` (potentially, to listen for history changes if the service emits them).

### 4. `useTemplate.ts` (or `useTemplateIntegration.ts`)

*   **Responsibility:** Manages the logic related to loading, applying, and interacting with templates. This could involve fetching template data, applying template shapes to the canvas, and handling template selection UI.
*   **Key Functionality:**
    *   Interacts with `TemplateService` and `TemplateIntegrationService`.
    *   Provides functions to: 
        *   `loadTemplatesList()`
        *   `applyTemplate(templateId: string)`
        *   `getCurrentTemplate()`
    *   Manages state related to template loading status, available templates, and the currently active template.
*   **Usage Example:**
    ```typescript
    // TemplateSidebar.tsx
    import useTemplate from '@/hooks/useTemplate';

    const TemplateSidebar = () => {
      const { templates, applyTemplate, isLoading } = useTemplate();
      // ... render list of templates and apply button ...
    }
    ```
*   **Interactions:**
    *   `TemplateService`: For fetching template data.
    *   `TemplateIntegrationService`: For applying templates to the canvas (which involves `shapesStore` and potentially `ElementCreationService`).
    *   `shapesStore`: To clear existing shapes and add template shapes.

### 5. `useTutorial.ts`

*   **Responsibility:** Manages the state and logic for the interactive tutorial system.
*   **Key Functionality:**
    *   Interacts with `TutorialService`.
    *   Provides functions to:
        *   `startTutorial(tutorialId: string)`
        *   `nextStep()`
        *   `previousStep()`
        *   `exitTutorial()`
    *   Exposes the current tutorial state: `isActive`, `currentStepData`, `isCompleted`.
    *   Often used in conjunction with a `TutorialProvider` context to make tutorial state and actions available throughout the component tree.
*   **Usage Example:**
    ```typescript
    // TutorialGuideUI.tsx
    import useTutorial from '@/hooks/useTutorial';

    const TutorialGuideUI = () => {
      const { currentStepData, nextStep, isActive } = useTutorial();
      if (!isActive || !currentStepData) return null;
      // ... render tutorial step instructions and next button ...
    }
    ```
*   **Interactions:**
    *   `TutorialService`: To manage tutorial progression and content.
    *   React Context (`TutorialContext`): To provide tutorial state and controls to nested components.

## Other Notable Hooks

*   **`useCanvasPanZoom.ts`:** Manages panning and zooming logic for the canvas, updating transformation matrices.
*   **`useCanvasSetup.ts`:** Handles initial setup of the canvas, including context creation and event listener attachment.
*   **`useCoordinateSystem.ts`:** Provides utilities for converting coordinates between different systems (e.g., screen to canvas, world to view).
*   **`useDragAndDrop.ts`:** Generic hook for implementing drag-and-drop interactions.
*   **`useKeyboardShortcuts.ts`:** Manages registration and handling of global keyboard shortcuts.
*   **`usePathDrawHandler.ts`:** Specific logic for drawing complex paths (e.g., polygons, freehand lines) on the canvas, managing intermediate points and preview.
*   **`usePersistentSettings.ts`:** A hook to easily persist and retrieve user settings or UI state to/from `localStorage`.

## Significance

Custom hooks are a powerful pattern in React for:

*   **Logic Reusability:** Extracting component logic into reusable functions.
*   **Separation of Concerns:** Keeping UI components focused on rendering by moving complex logic into hooks.
*   **Readability and Maintainability:** Making components easier to understand and maintain.
*   **Testability:** Hooks can often be tested in isolation more easily than complex components.

By leveraging these custom hooks, the RenoPilot.JS.Shapes2 application achieves a more modular, maintainable, and testable codebase.