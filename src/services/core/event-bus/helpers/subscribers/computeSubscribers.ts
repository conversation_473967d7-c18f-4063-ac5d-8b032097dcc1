/**
 * Compute Event Subscribers Module
 *
 * This module provides type-safe helper functions for subscribing to compute-related events
 * on the application's event bus. It covers geometric calculations, transformations,
 * and other computational operations.
 *
 * All functions follow dependency injection principles by accepting an EventBus
 * instance as their first parameter.
 *
 * @module event-bus/helpers/subscribers/compute-subscribers
 */

import type { EventBus } from '@/types/services/events'
import type { ComputeEvent, TransformEvent, ValidationEvent } from '@/types/services/events/computeEvents'
import type { EventSubscriptionOptions } from '@/types/services/events/eventCore'
import { AppEventType } from '@/types/services/events'
import { typedSubscribe } from './utils'

/**
 * Subscribe to compute request events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToComputeRequestEvents(
  eventBus: EventBus,
  handler: (event: ComputeEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<ComputeEvent>(
    eventBus,
    AppEventType.ComputeRequest,
    handler,
    options,
  )
}

/**
 * Subscribe to compute progress events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToComputeProgressEvents(
  eventBus: EventBus,
  handler: (event: ComputeEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<ComputeEvent>(
    eventBus,
    AppEventType.ComputeProgress,
    handler,
    options,
  )
}

/**
 * Subscribe to compute complete events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToComputeCompleteEvents(
  eventBus: EventBus,
  handler: (event: ComputeEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<ComputeEvent>(
    eventBus,
    AppEventType.ComputeComplete,
    handler,
    options,
  )
}

/**
 * Subscribe to compute error events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToComputeErrorEvents(
  eventBus: EventBus,
  handler: (event: ComputeEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<ComputeEvent>(
    eventBus,
    AppEventType.ComputeError,
    handler,
    options,
  )
}

/**
 * Subscribe to transform events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToTransformEvents(
  eventBus: EventBus,
  handler: (event: TransformEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<TransformEvent>(
    eventBus,
    AppEventType.ComputeTransform,
    handler,
    options,
  )
}

/**
 * Subscribe to validation events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param phase - The phase of the process to listen for
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToValidationEvents(
  eventBus: EventBus,
  handler: (event: ValidationEvent) => void,
  phase: 'shape' | 'template' | 'operation' | 'constraint',
  options?: EventSubscriptionOptions,
): () => void {
  const eventType = getValidationEventType(phase)
  return typedSubscribe<ValidationEvent>(
    eventBus,
    eventType,
    handler,
    options,
  )
}

/**
 * Get the appropriate event type for validation events
 * @param phase - The validation phase or type
 * @returns The corresponding AppEventType
 */
function getValidationEventType(phase: string): AppEventType {
  switch (phase) {
    case 'shape': return AppEventType.ValidateShape
    case 'template': return AppEventType.ValidateTemplate
    case 'operation': return AppEventType.ValidateOperation
    case 'constraint': return AppEventType.ValidateConstraint
    default: throw new Error(`Invalid validation phase: ${phase}`)
  }
}
