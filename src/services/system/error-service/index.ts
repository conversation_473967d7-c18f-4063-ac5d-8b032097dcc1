/**
 * Error Service
 *
 * Provides centralized error handling, logging, and reporting capabilities.
 *
 * @module services/system/error-service
 */

// 导出特殊错误类
export * from './classes'

// 导出核心错误类和错误工厂函数
export { CoreError, createError } from './coreError'
// 导出错误服务类和实现
export * from './errorService'

// Logger functionality moved to main logging system
// 导出服务注册函数
export * from './registry'

export * from './servicesErrorService'

// 导出实用工具函数
export * from './utils'

// 导出验证错误处理工具
export {
  // handleValidationError, // Not defined in validationUtils.ts
  safeValidate,
  // convertToCoreError, // Not defined in validationUtils.ts
  // convertValidationResultToCoreError, // Not defined in validationUtils.ts
  throwIfInvalid,
} from '@/lib/utils/validationUtils' // Corrected path, removed non-existent exports

// 导出类型从types模块（方便使用）
export {
  type ErrorContext,
  ErrorSeverity,
  ErrorType,
} from '@/types/services/errors'
