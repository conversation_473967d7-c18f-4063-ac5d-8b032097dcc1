/**
 * Circle Geometry Component
 *
 * Implements the standardized geometry layout for Circle elements
 * according to the design document specifications.
 *
 * Layout:
 * - Position (2 columns): Position X, Position Y
 * - Scale (1 column): Radius
 * - Calculation & Cost (unified layout)
 *
 * Note: Circle does not display Transform controls as rotation is meaningless for circles.
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'

import { ElementType } from '@/types/core/elementDefinitions'
import { GeometrySection, PropertyField } from '../shared'

interface CircleGeometryProps {
  /** The circle element being edited */
  element: ShapeElement
  /** Function to get input values with proper formatting */
  getValueForInput: (path: string) => string
  /** Function to get placeholder values */
  getPlaceholderForInput: (path: string) => string
  /** Function to handle input changes */
  handleInputChange: (path: string, value: string, isNumeric?: boolean, convertToPixels?: boolean) => void
  /** Unit name for display (e.g., "mm", "cm") */
  unitName: string
}

/**
 * CircleGeometry component for editing circle properties.
 *
 * Provides a standardized layout for circle geometry editing
 * with proper grouping and responsive design. Circles do not
 * support rotation as it is meaningless for circular shapes.
 */
export function CircleGeometry({
  element,
  getValueForInput: _getValueForInput,
  getPlaceholderForInput: _getPlaceholderForInput,
  handleInputChange,
  unitName,
}: CircleGeometryProps) {
  // Verify this is a circle element
  if (element.type !== ElementType.CIRCLE) {
    return null
  }

  return (
    <>
      {/* Position (2 columns) */}
      <GeometrySection type="position" columns={2}>
        <PropertyField
          label={`Position X (${unitName})`}
          type="number"
          value={_getValueForInput('position.x')}
          onChange={value => handleInputChange('position.x', value)}
          placeholder={_getPlaceholderForInput('position.x')}
        />
        <PropertyField
          label={`Position Y (${unitName})`}
          type="number"
          value={_getValueForInput('position.y')}
          onChange={value => handleInputChange('position.y', value)}
          placeholder={_getPlaceholderForInput('position.y')}
        />
      </GeometrySection>

      {/* Scale (1 column) */}
      <GeometrySection type="dimensions" columns={1}>
        <PropertyField
          label={`Radius (${unitName})`}
          type="number"
          value={_getValueForInput('radius')}
          onChange={value => handleInputChange('radius', value)}
          placeholder="0"
        />
      </GeometrySection>

      {/* Note: No Transform section for circles as rotation is meaningless */}
    </>
  )
}
