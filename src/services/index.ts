/**
 * Services Index
 *
 * Central export point for all services in the application.
 * This file re-exports all services from their respective modules.
 *
 * @module services
 */

// Export core services
// Export ErrorService namespace to avoid name conflicts
import * as ErrorService from './system/error-service'

export * from './core'

// Export service initialization, cleanup, and accessor functions
export {
  cleanupServices,
  getService,
  initializeServices,
} from './core/registry'

// Export input services
export * from './input'

// Export shape services
// export * from './shapes'
export { ErrorService }

// Export system services
export * from './system'
