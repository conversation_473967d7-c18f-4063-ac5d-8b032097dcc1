import { describe, it, expect, vi, beforeEach } from 'vitest';
import { toSvgBlob } from '@/data/export/blob';
import type { ShapeElement } from '@/types/core';

// Canvas size defined in blob.ts
const WIDTH: number = 842;
const HEIGHT: number = 595;

vi.mock('@/data/export/render', () => ({
  svgRenderersMap: {
    RECTANGLE: vi.fn(() => '<rect id="mock" />'),
    CIRCLE: vi.fn(() => '<circle id="mock" />'),
  }
}));

describe('toSvgBlob', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should generate correct SVG Blob for known elements', async () => {
    const elements: ShapeElement[] = [
      { type: 'RECTANGLE' } as any,
      { type: 'CIRCLE' } as any,
    ];

    const blob = toSvgBlob(elements);

    expect(blob).toBeInstanceOf(Blob);
    expect(blob.type).toBe('image/svg+xml');

    // 兼容 Node 环境下的 Blob 读取
    const reader = new FileReader();
    const textPromise = new Promise<string>((resolve, reject) => {
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
    });
    reader.readAsText(blob);
    const text = await textPromise;

    expect(text).toContain('<svg');
    expect(text).toContain(`width="${WIDTH}"`);
    expect(text).toContain(`height="${HEIGHT}"`);
    expect(text).toContain('<rect id="mock" />');
    expect(text).toContain('<circle id="mock" />');
    expect(text).toContain('</svg>');
  });

  it('should skip unknown element types', async () => {
    const elements: ShapeElement[] = [
      { type: 'RECTANGLE' } as any,
      { type: 'UNKNOWN_TYPE' } as any,
    ];
    const blob = toSvgBlob(elements);
    
    // 使用 FileReader 读取 Blob 内容
    const reader = new FileReader();
    const textPromise = new Promise<string>((resolve, reject) => {
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
    });
    reader.readAsText(blob);
    const text = await textPromise;

    expect(text).toContain('<rect id="mock" />');
    // 不应包含任何 unknown type 渲染内容
    expect(text).not.toContain('UNKNOWN_TYPE');
  });
});