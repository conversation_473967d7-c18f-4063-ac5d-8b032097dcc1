/**
 * Geometric Intersection Utilities
 *
 * @remarks
 * This module provides a collection of utility functions for detecting and calculating
 * intersections between various geometric entities, primarily focusing on:
 * - Line segment to line segment intersection checks and point calculation.
 * - Line segment to rectangle intersection checks.
 * - Point in rectangle checks.
 *
 * It utilizes {@link IPoint} (aliased from `PointData`) for point representations,
 * {@link PointClass} for internal point instantiation, and {@link IRectangle}
 * (imported from core types) for rectangle definitions.
 *
 * The functions are exported directly and also grouped under the {@link IntersectionUtils}
 * namespace object for convenient access.
 *
 * @module lib/utils/geometry/intersection
 */

import type { PointData as IPoint } from '../../../types/core/element/geometry/point'
import type { Rectangle as IRectangle } from '../../../types/core/element/shape/rectangleShapeTypes'
import type { ShapeElement } from '../../../types/core/elementDefinitions' // Import ShapeElement
import { PointClass as Point } from './PointClass'
// import { calculatePointToCircleDistance } from './pointUtils'; // commonUtils is deprecated

// import { calculatePointToLineDistance } from './coordinates'; // Unused
// import { isPointInside as isPointInsidePolygon } from './polygonUtils'; // Unused

/**
 * Checks if two line segments intersect.
 *
 * @remarks
 * This function uses a common line segment intersection algorithm based on determinants
 * to check if the segments cross each other. It handles parallel and collinear cases
 * by returning `false` (a more robust implementation might check for overlap in collinear cases).
 *
 * @param line1Start - The start {@link IPoint} of the first line segment.
 * @param line1End - The end {@link IPoint} of the first line segment.
 * @param line2Start - The start {@link IPoint} of the second line segment.
 * @param line2End - The end {@link IPoint} of the second line segment.
 * @returns `true` if the line segments intersect, `false` otherwise.
 * @throws {@link Error} if any of the input points are null or undefined.
 */
export function checkLinesIntersect(
  line1Start: IPoint,
  line1End: IPoint,
  line2Start: IPoint,
  line2End: IPoint,
): boolean {
  if (!line1Start || !line1End || !line2Start || !line2End) {
    throw new Error('All line points must be provided')
  }

  const dx1 = line1End.x - line1Start.x
  const dy1 = line1End.y - line1Start.y
  const dx2 = line2End.x - line2Start.x
  const dy2 = line2End.y - line2Start.y

  const determinant = dx1 * dy2 - dy1 * dx2

  if (determinant === 0) { // Lines are parallel or collinear
    // Check for collinearity and overlap if needed, for now, consider parallel non-intersecting
    return false
  }

  const dx3 = line2Start.x - line1Start.x
  const dy3 = line2Start.y - line1Start.y

  const t = (dx3 * dy2 - dy3 * dx2) / determinant
  const u = (dx3 * dy1 - dy3 * dx1) / determinant

  return t >= 0 && t <= 1 && u >= 0 && u <= 1
}

/**
 * Calculates the intersection point of two line segments, if it exists.
 *
 * @remarks
 * This function uses a common line segment intersection algorithm. If the segments
 * are parallel, collinear, or do not intersect within their bounds, it returns `null`.
 *
 * @param line1Start - The start {@link IPoint} of the first line segment.
 * @param line1End - The end {@link IPoint} of the first line segment.
 * @param line2Start - The start {@link IPoint} of the second line segment.
 * @param line2End - The end {@link IPoint} of the second line segment.
 * @returns A {@link Point} instance representing the intersection point, or `null` if they do not intersect.
 * @throws {@link Error} if any of the input points are null or undefined.
 */
export function calculateLinesIntersection(
  line1Start: IPoint,
  line1End: IPoint,
  line2Start: IPoint,
  line2End: IPoint,
): Point | null {
  if (!line1Start || !line1End || !line2Start || !line2End) {
    throw new Error('All line points must be provided')
  }

  // Calculate direction vectors
  const dx1 = line1End.x - line1Start.x
  const dy1 = line1End.y - line1Start.y
  const dx2 = line2End.x - line2Start.x
  const dy2 = line2End.y - line2Start.y

  // Calculate determinant
  const determinant = dx1 * dy2 - dy1 * dx2

  // If determinant is zero, lines are parallel or collinear
  if (determinant === 0) {
    return null
  }

  // Calculate parameters for the intersection point
  const dx3 = line2Start.x - line1Start.x
  const dy3 = line2Start.y - line1Start.y

  const t = (dx3 * dy2 - dy3 * dx2) / determinant
  const u = (dx3 * dy1 - dy3 * dx1) / determinant

  // Check if intersection point is within both line segments
  if (t >= 0 && t <= 1 && u >= 0 && u <= 1) {
    // Calculate intersection point
    return new Point(
      line1Start.x + t * dx1,
      line1Start.y + t * dy1,
    )
  }

  return null
}

/**
 * Checks if a line segment intersects with an axis-aligned rectangle.
 *
 * @remarks
 * This function first checks if either endpoint of the line segment is inside the rectangle
 * using {@link isPointInRectangle}. If not, it then checks for intersection between
 * the line segment and each of the four edges of the rectangle using {@link checkLinesIntersect}.
 *
 * @param lineStart - The start {@link IPoint} of the line segment.
 * @param lineEnd - The end {@link IPoint} of the line segment.
 * @param rect - The {@link IRectangle} to check for intersection. Its `position` is the top-left corner.
 * @returns `true` if the line segment intersects the rectangle, `false` otherwise.
 * @throws {@link Error} if line points or `rect` are null or undefined.
 */
export function checkLineRectangleIntersect(
  lineStart: IPoint,
  lineEnd: IPoint,
  rect: IRectangle,
): boolean {
  if (!lineStart || !lineEnd || !rect) {
    throw new Error('Line points and rectangle must be provided')
  }

  // Check if either endpoint is inside the rectangle
  if (isPointInRectangle(lineStart, rect) || isPointInRectangle(lineEnd, rect)) {
    return true
  }

  // Check if the line intersects with any of the rectangle's edges
  const rectShapeElement = rect as unknown as ShapeElement // Use double assertion
  const rectTopLeft = { x: rectShapeElement.position.x, y: rectShapeElement.position.y }
  const rectTopRight = { x: rectShapeElement.position.x + rect.width, y: rectShapeElement.position.y }
  const rectBottomLeft = { x: rectShapeElement.position.x, y: rectShapeElement.position.y + rect.height }
  const rectBottomRight = { x: rectShapeElement.position.x + rect.width, y: rectShapeElement.position.y + rect.height }

  // Check intersection with each edge of the rectangle
  return (
    checkLinesIntersect(lineStart, lineEnd, rectTopLeft, rectTopRight)
    || checkLinesIntersect(lineStart, lineEnd, rectTopRight, rectBottomRight)
    || checkLinesIntersect(lineStart, lineEnd, rectBottomRight, rectBottomLeft)
    || checkLinesIntersect(lineStart, lineEnd, rectBottomLeft, rectTopLeft)
  )
}

/**
 * Checks if a point is inside an axis-aligned rectangle (inclusive of boundaries).
 *
 * @param point - The {@link IPoint} to check.
 * @param rect - The {@link IRectangle} to check against. Its `position` is the top-left corner.
 * @returns `true` if the point is inside or on the boundary of the rectangle, `false` otherwise.
 * @throws {@link Error} if `point` or `rect` are null or undefined.
 */
export function isPointInRectangle(point: IPoint, rect: IRectangle): boolean {
  if (!point || !rect) {
    throw new Error('Point and rectangle must be provided')
  }
  const rectShapeElement = rect as unknown as ShapeElement // Use double assertion
  return (
    point.x >= rectShapeElement.position.x
    && point.x <= rectShapeElement.position.x + rect.width
    && point.y >= rectShapeElement.position.y
    && point.y <= rectShapeElement.position.y + rect.height
  )
}

/**
 * Namespace object for convenient access to intersection utility functions.
 *
 * @remarks
 * This object groups the functions from this module, allowing for usage like
 * `IntersectionUtils.checkLinesIntersect(...)`.
 */
export const IntersectionUtils = {
  // Line intersections
  checkLinesIntersect,
  calculateLinesIntersection,

  // Shape intersections
  checkLineRectangleIntersect,
  isPointInRectangle,
}
