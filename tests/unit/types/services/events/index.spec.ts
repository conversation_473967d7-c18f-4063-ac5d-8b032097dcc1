// Import a selection of types/interfaces re-exported by the events index file
import type {
  // From ./eventRegistry
  AppEvent,
  AppEventMap, // From ./base
  BaseEvent,
  // KeyDownEvent, // Removed - Not found/exported
  // RenderRequestEvent, // Removed - Not found/exported
  DataLoadEvent,
  EventBus,
  // From specific event files
  ShapeSelectedEvent,
  TypedEvent,
} from '@/types/services/events/index'

import { describe, expect, it } from 'vitest'
import { // From ./eventTypes
  AppEventType,
  // SystemInitializedEvent, // Removed - Not found/exported
  // ButtonClickedEvent // Removed - Not found/exported
} from '@/types/services/events/index'

describe('service Events Index File', () => {
  it('should re-export AppEventType enum', () => {
    expect(AppEventType).toBeDefined()
    // Changed to an existing member
    expect(AppEventType.SHAPE_SELECTED).toBe('shape.selected')
    expect(AppEventType.COMPUTE_REQUEST).toBe('compute.request')
  })

  it('should re-export base event types', () => {
    const base: BaseEvent | undefined = undefined
    expect(base).toBeUndefined()
    // Correctly provide payload shape to TypedEvent
    let typed: TypedEvent<{ data: number }> | undefined
    expect(typed).toBeUndefined()
    let bus: EventBus | undefined
    expect(bus).toBeUndefined()
  })

  it('should re-export event registry types', () => {
    const appEvent: AppEvent | undefined = undefined
    expect(appEvent).toBeUndefined()
    let map: AppEventMap | undefined
    expect(map).toBeUndefined()
  })

  it('should re-export specific event interfaces', () => {
    const shapeSelected: ShapeSelectedEvent | undefined = undefined
    expect(shapeSelected).toBeUndefined()
    // let keyDown: KeyDownEvent | undefined = undefined;
    // expect(keyDown).toBeUndefined();
    // let renderReq: RenderRequestEvent | undefined = undefined;
    // expect(renderReq).toBeUndefined();
    let dataLoad: DataLoadEvent | undefined
    expect(dataLoad).toBeUndefined()
    // let sysInit: SystemInitializedEvent | undefined = undefined;
    // expect(sysInit).toBeUndefined();
    // let btnClick: ButtonClickedEvent | undefined = undefined;
    // expect(btnClick).toBeUndefined();
  })
})
