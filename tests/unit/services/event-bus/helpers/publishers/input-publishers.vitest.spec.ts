import { beforeEach, describe, expect, it, vi } from 'vitest'
import * as inputPublishers from '@/services/event-bus/helpers/publishers/input-publishers'
import { AppEventType } from '@/types/services/events/eventTypes'

describe('input Publishers', () => {
  let mockEventBus: any

  beforeEach(() => {
    // Create a mock event bus
    mockEventBus = {
      publish: vi.fn(),
    }

    // Clear all mocks before each test
    vi.clearAllMocks()
  })

  describe('mouse Events', () => {
    it('should publish canvas mouse move event correctly', () => {
      const x = 100
      const y = 200

      inputPublishers.publishCanvasMouseMove(mockEventBus, x, y)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.CANVAS_MOUSE_MOVE,
        payload: {
          x,
          y,
        },
      })
    })

    it('should publish canvas mouse down event correctly', () => {
      const options = {
        x: 100,
        y: 200,
        button: 0,
        clientX: 300,
        clientY: 400,
      }

      inputPublishers.publishCanvasMouseDown(mockEventBus, options)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.CANVAS_MOUSE_DOWN,
        payload: options,
      })
    })

    it('should publish canvas mouse up event correctly', () => {
      const options = {
        x: 100,
        y: 200,
        button: 0,
        clientX: 300,
        clientY: 400,
      }

      inputPublishers.publishCanvasMouseUp(mockEventBus, options)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.CANVAS_MOUSE_UP,
        payload: options,
      })
    })

    it('should publish canvas clicked event correctly', () => {
      const options = {
        x: 100,
        y: 200,
        button: 0,
        clientX: 300,
        clientY: 400,
      }

      inputPublishers.publishCanvasClicked(mockEventBus, options)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.CANVAS_CLICKED,
        payload: options,
      })
    })
  })

  describe('keyboard Events', () => {
    it('should publish key pressed event correctly', () => {
      const key = 'Enter'
      const code = 'Enter'
      const modifiers = { ctrlKey: true, shiftKey: false, altKey: false, metaKey: false }

      inputPublishers.publishKeyPressed(mockEventBus, key, code, modifiers)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.KEY_PRESSED,
        payload: {
          key,
          code,
          modifiers,
          originalEvent: undefined,
        },
      })
    })

    it('should publish key released event correctly', () => {
      const key = 'Enter'
      const code = 'Enter'
      const modifiers = { ctrlKey: true, shiftKey: false, altKey: false, metaKey: false }

      inputPublishers.publishKeyReleased(mockEventBus, key, code, modifiers)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.KEY_RELEASED,
        payload: {
          key,
          code,
          modifiers,
          originalEvent: undefined,
        },
      })
    })
  })
})
