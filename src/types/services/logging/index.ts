/**
 * Logger Service Types
 *
 * This file defines types related to the logging service functionality.
 *
 * @module types/services/logging
 */

/**
 * Log level enumeration
 */
export enum LogLevel {
  /** Debug information */
  Debug = 'debug',
  /** General information */
  Info = 'info',
  /** Warning messages */
  Warn = 'warn',
  /** Error messages */
  Error = 'error',
}

/**
 * Logger configuration interface
 */
export interface LoggerConfig {
  /** Minimum log level to record */
  minLevel: LogLevel
  /** Whether console output is enabled */
  enableConsole: boolean
  /** Whether remote logging is enabled */
  enableRemoteLogging?: boolean
  /** Remote logging service URL */
  remoteLoggingUrl?: string
  /** Whether to include timestamps in each log entry */
  includeTimestamp: boolean
  /** Whether to include context information */
  includeContext: boolean
}

/**
 * Log entry structure
 */
export interface LogEntry {
  /** Log level */
  level: LogLevel
  /** Log message */
  message: string
  /** Optional additional details */
  details?: unknown
  /** Timestamp */
  timestamp: number
  /** Context information */
  context?: Record<string, unknown>
}

/**
 * Logger service interface
 */
export interface LoggerService {
  /** Log debug level message */
  debug: (message: string, ...args: unknown[]) => void
  /** Log info level message */
  info: (message: string, ...args: unknown[]) => void
  /** Log warning level message */
  warn: (message: string, ...args: unknown[]) => void
  /** Log error level message */
  error: (message: string, ...args: unknown[]) => void
  /** Set context information */
  setContext: (context: Record<string, unknown>) => void
  /** Get logger configuration */
  getConfig: () => LoggerConfig
  /** Set logger configuration */
  setConfig: (config: Partial<LoggerConfig>) => void
}
