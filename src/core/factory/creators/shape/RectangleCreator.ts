// import { MajorCategory, MinorCategory } from '@/types/core/majorMinorTypes'; // MajorCategory was unused, MinorCategory might be needed if commonProps doesn't provide it fully typed.
// CommonShapeElementProps already types minorCategory.
import type { CreateRectangleParams } from '@/core/factory/ElementFactory' // Changed from ShapeCreationParamsUnion
import type { PointData } from '@/types/core/element/geometry/point' // Import PointData
import type {
  ShapeElement,
} from '@/types/core/elementDefinitions'
import { ensureCompleteMetadata } from '@/lib/utils/element/elementUtils'
/**
 * Creator for Rectangle and Square Shape Elements
 *
 * @remarks
 * This class implements the {@link ShapeCreator} interface to specialize in creating
 * Rectangle ({@link CoreElementType.RECTANGLE}) and Square ({@link CoreElementType.SQUARE})
 * shape elements. Squares are handled as a special case of rectangles where `width`
 * and `height` are equal.
 *
 * The `create` method takes {@link CreateRectangleParams} (which accommodates both
 * rectangles and squares) and constructs a {@link ShapeElement} that conforms to the
 * {@link Shape.Rectangle} interface. It ensures all necessary properties, including
 * those inherited from `ShapeElement` and `BaseStyleProperties`, are correctly
 * initialized. The `position` of the rectangle/square is typically its center, and
 * corner points are calculated based on this center, width, and height.
 *
 * The `createDefault` method provides a simple way to create a default rectangle.
 *
 * @module core/factory/creators/shape/RectangleCreator
 * @see {@link ShapeCreator}
 * @see {@link CoreElementType.RECTANGLE}
 * @see {@link CoreElementType.SQUARE}
 * @see {@link Shape.Rectangle}
 * @see {@link CreateRectangleParams}
 */
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  // ShapeElement as ShapeModel, // Removed unused alias
  ElementType as CoreElementType,
  // MetadataProperties // Unused
} from '@/types/core/elementDefinitions'
import { ShapeCreator } from './ShapeCreator' // Import CommonShapeElementProps
// import { MajorCategory } from '@/types/core/majorMinorTypes'; // Removed unused import
// ensurePointInstance is not strictly needed if position is already PointData from Create...Params

/**
 * Creator class for instantiating Rectangle and Square shape elements.
 * It implements the {@link ShapeCreator} interface.
 *
 * @implements {ShapeCreator}
 */
export class RectangleCreator extends ShapeCreator<ShapeElement, CreateRectangleParams> { // Return Shape.Rectangle
  constructor() { // Added constructor
    super(CoreElementType.RECTANGLE)
  }

  /**
   * Creates a Rectangle or Square {@link ShapeElement} based on the provided parameters.
   * Squares are treated as a special case of Rectangles.
   *
   * @param params - A {@link CreateRectangleParams} object
   *                 containing all necessary information for creation.
   * @returns A Promise resolving to the created {@link Shape.Rectangle}.
   * @throws {@link CoreError} if `params.type` is not 'rectangle' or 'square', or if essential parameters
   *         (id, position, width, height) are missing or invalid.
   */
  public async create(params: CreateRectangleParams): Promise<ShapeElement> { // Return Shape.Rectangle
    console.warn('[RectangleCreator create] Received params:', JSON.stringify(params, null, 2))
    if (params.type !== CoreElementType.RECTANGLE && params.type !== CoreElementType.SQUARE
      && params.type !== 'rectangle' && params.type !== 'square') {
      console.error(`[RectangleCreator create] Invalid type: ${String(params.type)}`)
      throw new CoreError(ErrorType.InvalidPayload, `RectangleCreator cannot create type: ${String(params.type)}`)
    }

    // const rectParams = params as unknown as CreateRectangleParams; // No longer needed if params is CreateRectangleParams
    const rectParams = params

    const {
      id,
      // type: elementType, // Removed unused variable
      position: inputPosition, // This is IPoint
      width,
      height,
      // cornerRadius, // Removed from destructuring as params.cornerRadius is used directly
      // metadata, // Unused
      // visible, // Unused
      // locked, // Unused
      // rotation, // Unused
      // selectable, // Unused
      // draggable, // Unused
      // showHandles, // Unused
      // layer, // Unused
      // zIndex, // Unused
      properties: customProperties, // Keep this one
      fill: rectParamsFill,
      stroke: rectParamsStroke,
      strokeWidth: rectParamsStrokeWidth,
      opacity: rectParamsOpacity,
      // strokeDasharray // Unused
    } = rectParams

    if (!id)
      throw new CoreError(ErrorType.InvalidPayload, 'Rectangle/Square requires an ID.')
    if (inputPosition == null || typeof inputPosition.x !== 'number' || typeof inputPosition.y !== 'number') {
      throw new CoreError(ErrorType.InvalidPayload, 'Rectangle/Square requires a valid position.')
    }

    // 优先从 properties 取 width/height
    const widthFromProps = customProperties && typeof customProperties.width === 'number' ? customProperties.width : undefined
    const heightFromProps = customProperties && typeof customProperties.height === 'number' ? customProperties.height : undefined
    const finalWidth = typeof widthFromProps === 'number' ? widthFromProps : width
    const finalHeight = typeof heightFromProps === 'number' ? heightFromProps : height

    if (typeof finalWidth !== 'number' || finalWidth <= 0 || !Number.isFinite(finalWidth)
      || typeof finalHeight !== 'number' || finalHeight <= 0 || !Number.isFinite(finalHeight)) {
      console.error('[RectangleCreator create] Invalid width or height.', { id: String(id), type: String(params.type), width: String(finalWidth), height: String(finalHeight), inputPosition })
      throw new CoreError(ErrorType.InvalidPayload, `Rectangle/Square requires positive finite width and height. Received: w=${String(finalWidth)}, h=${String(finalHeight)}`)
    }

    const position: PointData = inputPosition // Changed to PointData
    // const finalCornerRadius = (typeof cornerRadius === 'number' && cornerRadius >= 0) ? cornerRadius : 0; // Unused

    // For Shape.Rectangle, points are usually not stored directly but calculated if needed.
    // The position is typically the center for rectangles in many systems.
    // If position is top-left, adjustments would be needed for width/height.
    // Assuming 'position' in CreateRectangleParams refers to the center.

    // Step 1: Create the base ShapeElement part
    // The commonProps will now include majorCategory, minorCategory, zLevelId, zIndex, isFixedCategory from params if provided.
    const commonProps = this.createCommonProperties(id, rectParams)

    const fill = rectParamsFill ?? '#CCCCCC'
    const stroke = rectParamsStroke ?? '#333333'
    const strokeWidth = rectParamsStrokeWidth ?? 1
    const opacity = rectParamsOpacity ?? 1

    const rect: ShapeElement = {
      ...commonProps,
      fill,
      stroke,
      strokeWidth,
      opacity,
      type: rectParams.type === CoreElementType.SQUARE || rectParams.type === 'square' ? CoreElementType.SQUARE : CoreElementType.RECTANGLE,
      cornerRadius: params.cornerRadius ?? 0, // Default cornerRadius to 0 if not provided
      properties: {
        // 设置默认的成本相关属性 - 但默认不启用成本计算
        costUnitPrice: 1,
        costMultiplierOrCount: 1,
        costBasis: 'unit', // 默认使用单位计算，用户需要手动选择面积或周长
        // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
        computedAreaStatus: 'none',
        computedPerimeterStatus: 'none',
        computedLengthStatus: 'none',
        // 然后合并其他属性
        ...(customProperties || {}),
        width: finalWidth, // 优先用 properties 里的 width
        height: finalHeight, // 优先用 properties 里的 height
        fill,
        stroke,
        strokeWidth,
        opacity,
        // Points are calculated and added below
      },
      locked: params.locked ?? false, // Corrected: use commonProps.locked or params.locked
      // isFixedCategory is part of commonProps, so it's already spread.
      // If it needs to be overridable specifically for rectangles from params, ensure CreateRectangleParams includes it.
      // For now, relying on commonProps for isFixedCategory
    }

    // Ensure points are added to the properties
    if (rect.properties) {
      rect.properties.points = [
        { x: position.x - finalWidth / 2, y: position.y - finalHeight / 2, z: position.z },
        { x: position.x + finalWidth / 2, y: position.y - finalHeight / 2, z: position.z },
        { x: position.x + finalWidth / 2, y: position.y + finalHeight / 2, z: position.z },
        { x: position.x - finalWidth / 2, y: position.y + finalHeight / 2, z: position.z },
      ]
    }

    // PRE-RETURN CHECK log
    const preReturnCheck = {
      id: rect.id,
      type: rect.type,
      majorCategory: rect.majorCategory,
      minorCategory: rect.minorCategory,
      zLevelId: rect.zLevelId,
      isFixedCategory: rect.isFixedCategory,
      zIndex: rect.zIndex,
      position: rect.position,
      properties: rect.properties,
    }
    console.warn('[RectangleCreator create] Constructed ShapeElement (Rectangle/Square) PRE-RETURN CHECK:', JSON.stringify(preReturnCheck, null, 2))

    console.warn('[RectangleCreator create] Constructed ShapeElement (Rectangle/Square):', JSON.stringify(rect, null, 2))
    return rect // Return as Shape.Rectangle
  }

  /**
   * Creates a default Rectangle element.
   *
   * @param id - The unique identifier for the default rectangle.
   * @param position - The center {@link PointData} for the default rectangle.
   * @returns A Promise resolving to the created default {@link Shape.Rectangle}.
   */
  public async createDefault(id: string, position: PointData): Promise<ShapeElement> { // Changed return type and PointData
    const defaultMetadata = ensureCompleteMetadata({ name: `Default Rectangle ${id}` })
    const params: CreateRectangleParams = {
      id,
      type: CoreElementType.RECTANGLE,
      position,
      width: 240, // 🔧 修复：使用与defaultElementSettings一致的默认值
      height: 160, // 🔧 修复：使用与defaultElementSettings一致的默认值
      cornerRadius: 0,
      metadata: defaultMetadata,
      fill: '#F8D7DA', // 🔧 修复：使用与defaultElementSettings一致的颜色
      stroke: '#E1A3A8', // 🔧 修复：使用与defaultElementSettings一致的颜色
      strokeWidth: 1,
      visible: true,
      locked: false,
      rotation: 0,
      selectable: true,
      draggable: true,
      properties: {
        // 设置默认的成本相关属性 - 默认不启用成本计算
        costUnitPrice: 1,
        costMultiplierOrCount: 1,
        costBasis: 'unit', // 默认使用单位计算，用户需要手动选择面积或周长
        // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
        computedAreaStatus: 'none',
        computedPerimeterStatus: 'none',
        computedLengthStatus: 'none',
      },
      showHandles: true,
    }
    return this.create(params)
  }
}
