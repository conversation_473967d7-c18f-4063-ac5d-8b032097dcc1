import type { ShapeElement } from '@/types/core'
import type { AppEventMap, EventBus } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { createPropertyPatch, ElementEditServiceImpl, NUMERIC_PROPERTIES, STYLE_PROPERTIES } from '@/services/elements/element-actions/elementEditService'

// Mock dependencies
const mockEventBus = {
  publish: vi.fn(),
  subscribe: vi.fn(),
  unsubscribe: vi.fn(),
  clear: vi.fn(),
} as unknown as EventBus<AppEventMap>

const mockLogger = {
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  debug: vi.fn(),
  setContext: vi.fn(),
  getConfig: vi.fn(),
  setConfig: vi.fn(),
} as unknown as LoggerService

// Mock getLoggerService
vi.mock('@/services/system/logging', () => ({
  getLoggerService: () => mockLogger,
}))

describe('elementEditServiceImpl', () => {
  let editService: ElementEditServiceImpl

  beforeEach(() => {
    vi.clearAllMocks()
    editService = new ElementEditServiceImpl(mockEventBus, mockLogger)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('constructor', () => {
    it('should initialize with dependencies', () => {
      expect(editService).toBeDefined()
      expect(editService.serviceId).toBeDefined()
    })
  })

  describe('createPropertyPatch', () => {
    const mockElement: ShapeElement = {
      id: 'test-element',
      type: 'RECTANGLE',
      position: { x: 100, y: 100, z: 0 },
      properties: {
        width: 200,
        height: 150,
        fill: '#ff0000',
      },
    }

    describe('basic property updates', () => {
      it('should create patch for numeric properties', () => {
        const patch = createPropertyPatch(mockElement, 'width', 300)

        expect(patch).toBeDefined()
        expect(patch?.id).toBe('test-element')
        expect(patch?.properties.width).toBe(300)
        // Note: width property may not be set at top level depending on implementation
      })

      it('should create patch for style properties', () => {
        const patch = createPropertyPatch(mockElement, 'fill', '#00ff00')

        expect(patch).toBeDefined()
        expect(patch?.id).toBe('test-element')
        expect(patch?.properties.fill).toBe('#00ff00')
        // Note: fill property may not be set at top level depending on implementation
      })

      it('should handle string numeric values', () => {
        const patch = createPropertyPatch(mockElement, 'width', '250')

        expect(patch).toBeDefined()
        expect(patch?.properties.width).toBe(250)
        // Note: width property may not be set at top level depending on implementation
      })

      it('should return null for invalid numeric values', () => {
        const patch = createPropertyPatch(mockElement, 'width', 'invalid')

        expect(patch).toBeNull()
      })
    })

    describe('position property updates', () => {
      it('should update position.x', () => {
        const patch = createPropertyPatch(mockElement, 'position.x', 150)

        expect(patch?.position).toEqual({ x: 150, y: 100, z: 0 })
        expect(patch?.properties.position).toEqual({ x: 150, y: 100, z: 0 })
      })

      it('should update position.y', () => {
        const patch = createPropertyPatch(mockElement, 'position.y', 200)

        expect(patch?.position).toEqual({ x: 100, y: 200, z: 0 })
        expect(patch?.properties.position).toEqual({ x: 100, y: 200, z: 0 })
      })

      it('should handle invalid position values', () => {
        const patch = createPropertyPatch(mockElement, 'position.x', 'invalid')

        expect(patch).toBeNull()
      })
    })

    describe('nested property updates', () => {
      it('should update properties.* paths', () => {
        const patch = createPropertyPatch(mockElement, 'properties.stroke', '#000000')

        expect(patch?.properties.stroke).toBe('#000000')
      })

      it('should handle deeply nested properties', () => {
        const elementWithNested: ShapeElement = {
          ...mockElement,
          properties: {
            ...mockElement.properties,
            controlPoint: { x: 50, y: 50 },
          },
        }

        const patch = createPropertyPatch(elementWithNested, 'properties.controlPoint.x', 75)

        expect(patch?.properties.controlPoint).toEqual({ x: 75, y: 50 })
      })

      it('should create nested objects when they don\'t exist', () => {
        const patch = createPropertyPatch(mockElement, 'properties.newObject.value', 42)

        expect(patch?.properties.newObject).toEqual({ value: 42 })
      })
    })

    describe('layer property updates', () => {
      it('should update majorCategory at both levels', () => {
        const patch = createPropertyPatch(mockElement, 'majorCategory', 'floor')

        expect(patch?.majorCategory).toBe('floor')
        expect(patch?.properties.majorCategory).toBe('floor')
      })

      it('should update minorCategory at both levels', () => {
        const patch = createPropertyPatch(mockElement, 'minorCategory', 'coverings')

        expect(patch?.minorCategory).toBe('coverings')
        expect(patch?.properties.minorCategory).toBe('coverings')
      })

      it('should update zLevelId at both levels', () => {
        const patch = createPropertyPatch(mockElement, 'zLevelId', 'level-1')

        expect(patch?.zLevelId).toBe('level-1')
        expect(patch?.properties.zLevelId).toBe('level-1')
      })
    })

    describe('special numeric properties', () => {
      it('should handle strokeWidth as numeric', () => {
        const patch = createPropertyPatch(mockElement, 'strokeWidth', 3)

        expect(patch).toBeDefined()
        expect(patch?.properties.strokeWidth).toBe(3)
        // Top-level strokeWidth may or may not be set depending on element structure
      })

      it('should handle opacity as numeric', () => {
        const patch = createPropertyPatch(mockElement, 'opacity', 0.5)

        expect(patch).toBeDefined()
        expect(patch?.properties.opacity).toBe(0.5)
        // Top-level opacity may or may not be set depending on element structure
      })

      it('should parse string strokeWidth', () => {
        const patch = createPropertyPatch(mockElement, 'strokeWidth', '2.5')

        expect(patch).toBeDefined()
        expect(patch?.properties.strokeWidth).toBe(2.5)
        // Top-level strokeWidth may or may not be set depending on element structure
      })

      it('should return null for invalid strokeWidth', () => {
        const patch = createPropertyPatch(mockElement, 'strokeWidth', 'invalid')

        expect(patch).toBeNull()
      })
    })

    describe('polygon radius updates', () => {
      const polygonElement: ShapeElement = {
        id: 'polygon-1',
        type: 'POLYGON',
        position: { x: 100, y: 100, z: 0 },
        properties: {
          sides: 6,
          radius: 50,
          isRegular: true,
          points: [],
        },
      }

      it('should regenerate points when radius is updated', () => {
        const patch = createPropertyPatch(polygonElement, 'properties.radius', 75)

        expect(patch?.properties.radius).toBe(75)
        expect(patch?.properties.creationRadius).toBe(75)
        expect(patch?.properties.points).toBeDefined()
        expect(Array.isArray(patch?.properties.points)).toBe(true)
      })

      it('should regenerate points when creationRadius is updated', () => {
        const patch = createPropertyPatch(polygonElement, 'properties.creationRadius', 60)

        expect(patch?.properties.creationRadius).toBe(60)
        expect(patch?.properties.radius).toBe(60)
        expect(patch?.properties.points).toBeDefined()
      })

      it('should not regenerate points for non-polygon elements', () => {
        const patch = createPropertyPatch(mockElement, 'properties.radius', 50)

        expect(patch?.properties.radius).toBe(50)
        expect(patch?.properties.points).toBeUndefined()
      })
    })

    describe('pattern property updates', () => {
      it('should handle valid pattern objects', () => {
        const pattern = { id: 'pattern-1', type: 'stripe' }
        const patch = createPropertyPatch(mockElement, 'pattern', pattern)

        expect(patch?.pattern).toEqual(pattern)
      })

      it('should handle undefined pattern', () => {
        const patch = createPropertyPatch(mockElement, 'pattern', undefined)

        expect(patch?.pattern).toBeUndefined()
      })

      it('should return null for invalid pattern', () => {
        const patch = createPropertyPatch(mockElement, 'pattern', 'invalid')

        expect(patch).toBeNull()
      })
    })

    describe('cost property updates', () => {
      it('should handle costUnitPrice', () => {
        const patch = createPropertyPatch(mockElement, 'properties.costUnitPrice', 25.50)

        expect(patch?.properties.costUnitPrice).toBe(25.50)
      })

      it('should handle costMultiplierOrCount', () => {
        const patch = createPropertyPatch(mockElement, 'properties.costMultiplierOrCount', 3)

        expect(patch?.properties.costMultiplierOrCount).toBe(3)
      })

      it('should handle costBasis as string', () => {
        const patch = createPropertyPatch(mockElement, 'properties.costBasis', 'per-unit')

        expect(patch?.properties.costBasis).toBe('per-unit')
      })
    })

    describe('edge cases', () => {
      it('should handle empty string values for numeric properties', () => {
        const patch = createPropertyPatch(mockElement, 'width', '')

        expect(patch?.properties.width).toBeDefined() // Should get default value
      })

      it('should handle null values', () => {
        const patch = createPropertyPatch(mockElement, 'width', null)

        expect(patch?.properties.width).toBeDefined() // Should get default value
      })

      it('should handle undefined values', () => {
        const patch = createPropertyPatch(mockElement, 'width', undefined)

        expect(patch?.properties.width).toBeDefined() // Should get default value
      })

      it('should handle properties that don\'t exist on element', () => {
        const patch = createPropertyPatch(mockElement, 'newProperty', 'newValue')

        expect(patch?.properties.newProperty).toBe('newValue')
      })
    })
  })

  describe('constants', () => {
    it('should export STYLE_PROPERTIES', () => {
      expect(STYLE_PROPERTIES).toContain('fill')
      expect(STYLE_PROPERTIES).toContain('stroke')
      expect(STYLE_PROPERTIES).toContain('strokeWidth')
      expect(STYLE_PROPERTIES).toContain('opacity')
    })

    it('should export NUMERIC_PROPERTIES', () => {
      expect(NUMERIC_PROPERTIES).toContain('width')
      expect(NUMERIC_PROPERTIES).toContain('height')
      expect(NUMERIC_PROPERTIES).toContain('rotation')
      expect(NUMERIC_PROPERTIES).toContain('strokeWidth')
      expect(NUMERIC_PROPERTIES).toContain('opacity')
    })
  })
})
