
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/core/factory/creators/shape/PolygonCreator.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> / <a href="index.html">src/core/factory/creators/shape</a> PolygonCreator.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/278</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/278</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import type {
  CreatePolygonParams,
} from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point'
import type {
  Shape,
} from '@/types/core/elementDefinitions'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
<span class="cstat-no" title="statement not covered" >import { ensureCompleteMetadata } from '@/lib/utils/element/elementUtils'</span>
/**
 * Creator for Polygon Shape Elements
 *
 * @remarks
 * This class implements the {@link ShapeCreator} interface to specialize in creating
 * Polygon shape elements ({@link CoreElementType.POLYGON}, {@link CoreElementType.TRIANGLE},
 * {@link CoreElementType.HEXAGON}, etc.). Polygons can be defined either by an explicit
 * array of vertex points or as regular polygons specified by a center, radius, and number of sides.
 *
 * The `create` method takes {@link CreatePolygonParams} and constructs a {@link ShapeElement}
 * that conforms to the {@link Shape.Polygon} interface. It handles both custom and regular
 * polygon creation, normalizes point data, calculates the centroid for positioning,
 * and ensures all necessary properties are correctly initialized.
 *
 * The `createDefault` method provides a simple way to create a default hexagon.
 * Local helper functions `normalizeToPointData` and `createRegularPolygonPointsInternal`
 * assist in point normalization and regular polygon vertex generation.
 *
 * @module core/factory/creators/shape/PolygonCreator
 * @see {@link ShapeCreator}
 * @see {@link CoreElementType.POLYGON}
 * @see {@link Shape.Polygon}
 * @see {@link CreatePolygonParams}
 */
<span class="cstat-no" title="statement not covered" >import { CoreError, ErrorType } from '@/services/system/error-service'</span>
<span class="cstat-no" title="statement not covered" >import {</span>
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'
<span class="cstat-no" title="statement not covered" >import { MajorCategory } from '@/types/core/majorMinorTypes'</span>
<span class="cstat-no" title="statement not covered" >import { ShapeCreator } from './ShapeCreator'</span>
&nbsp;
/**
 * Helper function to normalize a point input to {@link PointData}.
 *
 * @param pointInput - The point input, can be a PointData-like object or an array of numbers.
 * @param paramName - The name of the parameter for error reporting purposes.
 * @returns A normalized {@link PointData} object.
 * @throws {@link CoreError} If the point input is invalid or undefined.
 * @private
 */
<span class="cstat-no" title="statement not covered" >function normalizeToPointData(pointInput: PointData | [number, number, number?] | [number, number] | undefined, paramName: string): PointData {</span>
<span class="cstat-no" title="statement not covered" >  if (!pointInput) {</span>
<span class="cstat-no" title="statement not covered" >    throw new CoreError(ErrorType.InvalidParameter, `${paramName} point input is undefined.`)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  if (Array.isArray(pointInput)) {</span>
<span class="cstat-no" title="statement not covered" >    if (pointInput.length &gt;= 2 &amp;&amp; typeof pointInput[0] === 'number' &amp;&amp; typeof pointInput[1] === 'number') {</span>
<span class="cstat-no" title="statement not covered" >      return { x: pointInput[0], y: pointInput[1], z: pointInput.length &gt; 2 &amp;&amp; typeof pointInput[2] === 'number' ? pointInput[2] : undefined }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    throw new CoreError(ErrorType.InvalidParameter, `Invalid array for ${paramName} point input.`)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else if (typeof pointInput.x === 'number' &amp;&amp; typeof pointInput.y === 'number') {</span>
<span class="cstat-no" title="statement not covered" >    return { x: pointInput.x, y: pointInput.y, z: pointInput.z } // z can be undefined</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  throw new CoreError(ErrorType.InvalidParameter, `Invalid object for ${paramName} point input.`)</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Creates an array of {@link PointData} objects representing the vertices of a regular polygon.
 *
 * @param center - The center {@link PointData} of the polygon.
 * @param radius - The radius of the circumcircle of the polygon.
 * @param sides - The number of sides (must be &gt;= 3).
 * @param startAngleRad - Optional starting angle in radians for the first vertex (default: 0, along the positive x-axis).
 * @returns An array of {@link PointData} objects forming the regular polygon.
 * @throws {@link CoreError} If `sides` is less than 3 or `radius` is not positive and finite.
 * @private
 */
/**
 * Creates an array of points for a regular polygon.
 *
 * @remarks
 * This function generates points for a regular polygon with the specified number of sides,
 * centered at the given center point and with the given radius. The points are generated
 * in counter-clockwise order starting from the angle specified by startAngleRad.
 *
 * For different polygon types, we use different starting angles to ensure the polygon
 * is oriented correctly:
 * - Triangle (3 sides): Start with a point at the top (startAngleRad = -Math.PI/2)
 * - Square/Rectangle (4 sides): Start with a point at the top-right (startAngleRad = -Math.PI/4)
 * - Pentagon (5 sides): Start with a point at the top (startAngleRad = -Math.PI/2)
 * - Hexagon (6 sides): Start with a point at the right (startAngleRad = 0)
 *
 * @param center - The center point of the polygon
 * @param radius - The radius of the polygon (distance from center to vertices)
 * @param sides - The number of sides of the polygon
 * @param startAngleRad - The starting angle in radians (default: 0)
 * @returns An array of points representing the vertices of the polygon
 */
<span class="cstat-no" title="statement not covered" >function createRegularPolygonPointsInternal(center: PointData, radius: number, sides: number, startAngleRad: number = 0): PointData[] {</span>
<span class="cstat-no" title="statement not covered" >  if (sides &lt; 3) {</span>
<span class="cstat-no" title="statement not covered" >    throw new CoreError(ErrorType.InvalidPayload, 'Regular polygon must have at least 3 sides.', undefined, { metadata: { sides } })</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  if (radius &lt;= 0 || !Number.isFinite(radius)) {</span>
<span class="cstat-no" title="statement not covered" >    throw new CoreError(ErrorType.InvalidPayload, 'Regular polygon radius must be positive and finite.', undefined, { metadata: { radius } })</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // 根据多边形类型调整起始角度，使多边形朝向更自然
<span class="cstat-no" title="statement not covered" >  let adjustedStartAngle = startAngleRad</span>
<span class="cstat-no" title="statement not covered" >  if (startAngleRad === 0 || startAngleRad === undefined) {</span>
    // 如果没有指定起始角度，根据边数自动调整
<span class="cstat-no" title="statement not covered" >    if (sides === 3) {</span>
      // 三角形：顶点朝上
<span class="cstat-no" title="statement not covered" >      adjustedStartAngle = -Math.PI / 2</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else if (sides === 4) {</span>
      // 四边形：一个角在右上方
<span class="cstat-no" title="statement not covered" >      adjustedStartAngle = -Math.PI / 4</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else if (sides === 5) {</span>
      // 五边形：一个顶点朝上
<span class="cstat-no" title="statement not covered" >      adjustedStartAngle = -Math.PI / 2</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    // 六边形和其他多边形使用默认的0度（右侧开始）
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  console.warn(`[createRegularPolygonPointsInternal] Creating ${sides}-sided polygon with radius ${radius} and adjusted start angle ${adjustedStartAngle}`)</span>
&nbsp;
  // 生成相对于原点(0,0)的顶点坐标
<span class="cstat-no" title="statement not covered" >  const points: PointData[] = []</span>
<span class="cstat-no" title="statement not covered" >  const angleStep = (2 * Math.PI) / sides</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  for (let i = 0; i &lt; sides; i++) {</span>
<span class="cstat-no" title="statement not covered" >    const angle = adjustedStartAngle + i * angleStep</span>
<span class="cstat-no" title="statement not covered" >    const x = radius * Math.cos(angle)</span>
<span class="cstat-no" title="statement not covered" >    const y = radius * Math.sin(angle)</span>
<span class="cstat-no" title="statement not covered" >    points.push({ x, y, z: center.z })</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // 计算几何中心
<span class="cstat-no" title="statement not covered" >  let sumX = 0</span>
<span class="cstat-no" title="statement not covered" >  let sumY = 0</span>
<span class="cstat-no" title="statement not covered" >  for (const point of points) {</span>
<span class="cstat-no" title="statement not covered" >    sumX += point.x</span>
<span class="cstat-no" title="statement not covered" >    sumY += point.y</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  const centroidX = sumX / sides</span>
<span class="cstat-no" title="statement not covered" >  const centroidY = sumY / sides</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  console.warn(`[createRegularPolygonPointsInternal] Original centroid: (${centroidX}, ${centroidY})`)</span>
&nbsp;
  // 调整顶点坐标，使几何中心与原点(0,0)重合
<span class="cstat-no" title="statement not covered" >  const adjustedPoints: PointData[] = []</span>
<span class="cstat-no" title="statement not covered" >  for (const point of points) {</span>
<span class="cstat-no" title="statement not covered" >    adjustedPoints.push({</span>
<span class="cstat-no" title="statement not covered" >      x: point.x - centroidX,</span>
<span class="cstat-no" title="statement not covered" >      y: point.y - centroidY,</span>
<span class="cstat-no" title="statement not covered" >      z: point.z,</span>
<span class="cstat-no" title="statement not covered" >    })</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  console.warn(`[createRegularPolygonPointsInternal] Adjusted points to ensure centroid is at (0,0)`)</span>
<span class="cstat-no" title="statement not covered" >  console.warn(`[createRegularPolygonPointsInternal] Generated ${adjustedPoints.length} points:`, adjustedPoints)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return adjustedPoints</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Creator class for instantiating Polygon shape elements, including regular polygons
 * like triangles and hexagons, as well as custom polygons defined by a list of vertices.
 * It implements the {@link ShapeCreator} interface.
 *
 * @implements {ShapeCreator}
 */
<span class="cstat-no" title="statement not covered" >export class PolygonCreator extends ShapeCreator&lt;Shape.Polygon, CreatePolygonParams&gt; {</span>
  /**
   * Creates a Polygon {@link ShapeElement} based on the provided parameters.
   *
   * @param params - A {@link CreatePolygonParams} object
   *                 containing information for polygon creation.
   * @returns A Promise resolving to the created {@link Shape.Polygon}.
   */
<span class="cstat-no" title="statement not covered" >  public async create(params: CreatePolygonParams): Promise&lt;Shape.Polygon&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const allowedTypes: string[] = [</span>
<span class="cstat-no" title="statement not covered" >      CoreElementType.POLYGON,</span>
<span class="cstat-no" title="statement not covered" >      CoreElementType.TRIANGLE,</span>
<span class="cstat-no" title="statement not covered" >      CoreElementType.QUADRILATERAL,</span>
<span class="cstat-no" title="statement not covered" >      CoreElementType.PENTAGON,</span>
<span class="cstat-no" title="statement not covered" >      CoreElementType.HEXAGON,</span>
<span class="cstat-no" title="statement not covered" >      'polygon',</span>
<span class="cstat-no" title="statement not covered" >      'triangle',</span>
<span class="cstat-no" title="statement not covered" >      'quadrilateral',</span>
<span class="cstat-no" title="statement not covered" >      'pentagon',</span>
<span class="cstat-no" title="statement not covered" >      'hexagon',</span>
<span class="cstat-no" title="statement not covered" >    ]</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!allowedTypes.includes(params.type as string)) {</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(ErrorType.InvalidPayload, `PolygonCreator cannot create type: ${params.type}`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const {</span>
<span class="cstat-no" title="statement not covered" >      id,</span>
<span class="cstat-no" title="statement not covered" >      type: elementType,</span>
<span class="cstat-no" title="statement not covered" >      points: inputPoints,</span>
<span class="cstat-no" title="statement not covered" >      center: inputCenter,</span>
<span class="cstat-no" title="statement not covered" >      sides: paramSides,</span>
<span class="cstat-no" title="statement not covered" >      radius: paramRadius,</span>
<span class="cstat-no" title="statement not covered" >      isRegular: directIsRegular,</span>
<span class="cstat-no" title="statement not covered" >      properties: customProperties,</span>
<span class="cstat-no" title="statement not covered" >      startAngleRad,</span>
<span class="cstat-no" title="statement not covered" >    } = params</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const startAngleForCreation = startAngleRad</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!id)</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(ErrorType.InvalidPayload, 'Polygon requires an ID.')</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    let finalPoints: PointData[]</span>
<span class="cstat-no" title="statement not covered" >    let calculatedCenter: PointData</span>
<span class="cstat-no" title="statement not covered" >    let finalIsRegular: boolean</span>
<span class="cstat-no" title="statement not covered" >    let finalSides: number</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const isIntendedRegular = typeof paramSides === 'number' &amp;&amp; paramSides &gt;= 3</span>
<span class="cstat-no" title="statement not covered" >      &amp;&amp; typeof paramRadius === 'number' &amp;&amp; paramRadius &gt; 0</span>
<span class="cstat-no" title="statement not covered" >      &amp;&amp; inputCenter &amp;&amp; directIsRegular === true</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (isIntendedRegular) {</span>
      // 对于正多边形，我们使用传入的position作为多边形的位置
      // 这确保了多边形的中心点就是鼠标落点位置
<span class="cstat-no" title="statement not covered" >      if (params.position) {</span>
        // 如果有传入position（从Canvas.tsx的handleSvgDrop方法传入），优先使用它
<span class="cstat-no" title="statement not covered" >        calculatedCenter = params.position</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[PolygonCreator][create] Using provided position as center:', calculatedCenter)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
        // 否则使用传入的center
<span class="cstat-no" title="statement not covered" >        calculatedCenter = normalizeToPointData(inputCenter as PointData | [number, number, number?], 'center (for regular polygon)')</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[PolygonCreator][create] Using provided center:', calculatedCenter)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // 生成相对于原点(0,0)的顶点
      // 这样当ShapeRenderer将整个多边形组平移到position位置时，多边形的中心点就是position
<span class="cstat-no" title="statement not covered" >      finalPoints = createRegularPolygonPointsInternal({ x: 0, y: 0, z: calculatedCenter.z }, paramRadius, paramSides, startAngleForCreation)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      finalIsRegular = true</span>
<span class="cstat-no" title="statement not covered" >      finalSides = paramSides</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      console.warn('[PolygonCreator][create] Created regular polygon with center:', calculatedCenter, 'and points:', finalPoints)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else if (inputPoints != null &amp;&amp; Array.isArray(inputPoints) &amp;&amp; inputPoints.length &gt;= 3) {</span>
<span class="cstat-no" title="statement not covered" >      const processedPoints = inputPoints.map((p, index) =&gt; normalizeToPointData(p as PointData | [number, number, number?], `points[${index}]`))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (processedPoints.length &gt; 1</span>
<span class="cstat-no" title="statement not covered" >        &amp;&amp; processedPoints[0].x === processedPoints[processedPoints.length - 1].x</span>
<span class="cstat-no" title="statement not covered" >        &amp;&amp; processedPoints[0].y === processedPoints[processedPoints.length - 1].y</span>
<span class="cstat-no" title="statement not covered" >        &amp;&amp; (processedPoints[0].z === processedPoints[processedPoints.length - 1].z || (processedPoints[0].z === undefined &amp;&amp; processedPoints[processedPoints.length - 1].z === undefined))) {</span>
<span class="cstat-no" title="statement not covered" >        finalPoints = processedPoints.slice(0, -1)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
<span class="cstat-no" title="statement not covered" >        finalPoints = processedPoints</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      finalSides = finalPoints.length</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (inputCenter) {</span>
<span class="cstat-no" title="statement not covered" >        calculatedCenter = normalizeToPointData(inputCenter as PointData | [number, number, number?], 'center (from input with points)')</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
<span class="cstat-no" title="statement not covered" >        let sumX = 0</span>
<span class="cstat-no" title="statement not covered" >        let sumY = 0</span>
<span class="cstat-no" title="statement not covered" >        let sumZ = 0</span>
<span class="cstat-no" title="statement not covered" >        let countZ = 0</span>
<span class="cstat-no" title="statement not covered" >        finalPoints.forEach((p) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >          sumX += p.x</span>
<span class="cstat-no" title="statement not covered" >          sumY += p.y</span>
<span class="cstat-no" title="statement not covered" >          if (p.z !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >            sumZ += p.z</span>
<span class="cstat-no" title="statement not covered" >            countZ++</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        })</span>
<span class="cstat-no" title="statement not covered" >        const avgZ = countZ &gt; 0 ? sumZ / countZ : undefined</span>
<span class="cstat-no" title="statement not covered" >        calculatedCenter = { x: sumX / finalPoints.length, y: sumY / finalPoints.length, z: avgZ }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (directIsRegular === false) {</span>
<span class="cstat-no" title="statement not covered" >        finalIsRegular = false</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
<span class="cstat-no" title="statement not covered" >        finalIsRegular = (customProperties as unknown as { isRegular?: boolean })?.isRegular ?? false</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(ErrorType.InvalidPayload, 'Polygon creation requires either valid regular polygon params (sides, radius, center, isRegular=true) OR a valid points array (&gt;=3).')</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // 确保position是中心点，这对于正确渲染和边界框计算非常重要
    // 对于从抽屉拖拽到画布上的多边形，position就是鼠标落点位置
<span class="cstat-no" title="statement not covered" >    const creationParamsForCommonProps = {</span>
<span class="cstat-no" title="statement not covered" >      ...params,</span>
      // 如果params.position存在（从ShapeCreationService传入），优先使用它
      // 否则使用calculatedCenter
<span class="cstat-no" title="statement not covered" >      position: params.position || calculatedCenter,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // 记录位置信息，便于调试
<span class="cstat-no" title="statement not covered" >    console.warn('[PolygonCreator][create] Setting polygon position to:', creationParamsForCommonProps.position)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const commonProps = this.createCommonProperties(id, creationParamsForCommonProps)</span>
&nbsp;
    // 记录创建多边形的关键信息
<span class="cstat-no" title="statement not covered" >    console.warn('[PolygonCreator][create] Creating polygon with:', {</span>
<span class="cstat-no" title="statement not covered" >      id,</span>
<span class="cstat-no" title="statement not covered" >      type: elementType,</span>
<span class="cstat-no" title="statement not covered" >      sides: finalSides,</span>
<span class="cstat-no" title="statement not covered" >      isRegular: finalIsRegular,</span>
<span class="cstat-no" title="statement not covered" >      pointsCount: finalPoints.length,</span>
<span class="cstat-no" title="statement not covered" >    })</span>
&nbsp;
    // 计算多边形的边界框以获取width和height
<span class="cstat-no" title="statement not covered" >    const boundingBox = this.calculatePolygonBoundingBox(finalPoints)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const polygonElement: Shape.Polygon = {</span>
<span class="cstat-no" title="statement not covered" >      ...commonProps,</span>
<span class="cstat-no" title="statement not covered" >      type: elementType as CoreElementType.POLYGON | CoreElementType.TRIANGLE | CoreElementType.QUADRILATERAL | CoreElementType.PENTAGON | CoreElementType.HEXAGON,</span>
<span class="cstat-no" title="statement not covered" >      metadata: ensureCompleteMetadata({</span>
<span class="cstat-no" title="statement not covered" >        ...(commonProps.metadata ?? {}),</span>
<span class="cstat-no" title="statement not covered" >        name: commonProps.metadata?.name ?? `${elementType} ${id.substring(0, 8)}`,</span>
<span class="cstat-no" title="statement not covered" >      }),</span>
<span class="cstat-no" title="statement not covered" >      points: finalPoints,</span>
<span class="cstat-no" title="statement not covered" >      sides: finalSides,</span>
<span class="cstat-no" title="statement not covered" >      isRegular: finalIsRegular,</span>
      // 添加计算出的width和height属性，用于属性栏显示
<span class="cstat-no" title="statement not covered" >      width: boundingBox.width,</span>
<span class="cstat-no" title="statement not covered" >      height: boundingBox.height,</span>
<span class="cstat-no" title="statement not covered" >      properties: {</span>
        // 设置默认的成本相关属性
<span class="cstat-no" title="statement not covered" >        costUnitPrice: 1,</span>
<span class="cstat-no" title="statement not covered" >        costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1</span>
<span class="cstat-no" title="statement not covered" >        costBasis: 'unit', // 🔧 修复：默认使用单位计算，而不是面积</span>
        // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
<span class="cstat-no" title="statement not covered" >        computedAreaStatus: 'none',</span>
<span class="cstat-no" title="statement not covered" >        computedPerimeterStatus: 'none',</span>
<span class="cstat-no" title="statement not covered" >        computedLengthStatus: 'none',</span>
        // 然后合并其他属性
<span class="cstat-no" title="statement not covered" >        ...(customProperties || {}),</span>
        // 确保points属性在properties中，因为渲染器和验证器期望在properties中找到points
<span class="cstat-no" title="statement not covered" >        points: finalPoints,</span>
        // 确保sides和isRegular属性在properties中，因为PropertyUpdateService需要这些属性
<span class="cstat-no" title="statement not covered" >        sides: finalSides,</span>
<span class="cstat-no" title="statement not covered" >        isRegular: finalIsRegular,</span>
        // 添加计算出的width和height到properties中，确保属性栏能够访问
<span class="cstat-no" title="statement not covered" >        width: boundingBox.width,</span>
<span class="cstat-no" title="statement not covered" >        height: boundingBox.height,</span>
<span class="cstat-no" title="statement not covered" >        ...(isIntendedRegular &amp;&amp; paramRadius != null &amp;&amp; inputCenter != null</span>
<span class="cstat-no" title="statement not covered" >          ? {</span>
<span class="cstat-no" title="statement not covered" >              creationRadius: paramRadius,</span>
<span class="cstat-no" title="statement not covered" >              creationCenter: inputCenter,</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          : {}),</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // 记录创建的多边形元素
<span class="cstat-no" title="statement not covered" >    console.warn('[PolygonCreator][create] Created polygon element:', {</span>
<span class="cstat-no" title="statement not covered" >      id: polygonElement.id,</span>
<span class="cstat-no" title="statement not covered" >      type: polygonElement.type,</span>
<span class="cstat-no" title="statement not covered" >      pointsCount: polygonElement.points.length,</span>
<span class="cstat-no" title="statement not covered" >      propertiesPointsCount: Array.isArray(polygonElement.properties?.points) ? polygonElement.properties?.points.length : 0,</span>
<span class="cstat-no" title="statement not covered" >    })</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return polygonElement</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a default Polygon element (specifically, a Hexagon).
   *
   * @param id - The unique identifier for the default polygon.
   * @param position - The center {@link PointData} for the default polygon.
   * @param majorCategoryOverride - Optional override for majorCategory.
   * @param minorCategoryOverride - Optional override for minorCategory.
   * @param zLevelIdOverride - Optional override for zLevelId.
   * @param isFixedCategoryOverride - Optional override for isFixedCategory.
   * @returns A Promise resolving to the created default {@link Shape.Polygon}.
   */
<span class="cstat-no" title="statement not covered" >  public async createDefault(</span>
<span class="cstat-no" title="statement not covered" >    id: string,</span>
<span class="cstat-no" title="statement not covered" >    position: PointData,</span>
<span class="cstat-no" title="statement not covered" >    majorCategoryOverride?: MajorCategory,</span>
<span class="cstat-no" title="statement not covered" >    minorCategoryOverride?: MinorCategory,</span>
<span class="cstat-no" title="statement not covered" >    zLevelIdOverride?: string,</span>
<span class="cstat-no" title="statement not covered" >    isFixedCategoryOverride?: boolean,</span>
<span class="cstat-no" title="statement not covered" >  ): Promise&lt;Shape.Polygon&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const sides = 6</span>
<span class="cstat-no" title="statement not covered" >    const radius = 50</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const params: CreatePolygonParams = {</span>
<span class="cstat-no" title="statement not covered" >      id,</span>
<span class="cstat-no" title="statement not covered" >      type: CoreElementType.HEXAGON,</span>
<span class="cstat-no" title="statement not covered" >      majorCategory: majorCategoryOverride ?? MajorCategory.FURNITURE,</span>
<span class="cstat-no" title="statement not covered" >      minorCategory: minorCategoryOverride,</span>
<span class="cstat-no" title="statement not covered" >      zLevelId: zLevelIdOverride,</span>
<span class="cstat-no" title="statement not covered" >      isFixedCategory: isFixedCategoryOverride,</span>
<span class="cstat-no" title="statement not covered" >      center: position,</span>
<span class="cstat-no" title="statement not covered" >      sides,</span>
<span class="cstat-no" title="statement not covered" >      radius,</span>
<span class="cstat-no" title="statement not covered" >      isRegular: true,</span>
<span class="cstat-no" title="statement not covered" >      metadata: { name: `Default Hexagon ${id}` },</span>
<span class="cstat-no" title="statement not covered" >      fill: '#E0E0E0',</span>
<span class="cstat-no" title="statement not covered" >      stroke: '#555555',</span>
<span class="cstat-no" title="statement not covered" >      strokeWidth: 2,</span>
<span class="cstat-no" title="statement not covered" >      visible: true,</span>
<span class="cstat-no" title="statement not covered" >      locked: false,</span>
<span class="cstat-no" title="statement not covered" >      rotation: 0,</span>
<span class="cstat-no" title="statement not covered" >      selectable: true,</span>
<span class="cstat-no" title="statement not covered" >      draggable: true,</span>
<span class="cstat-no" title="statement not covered" >      showHandles: true,</span>
<span class="cstat-no" title="statement not covered" >      points: [],</span>
<span class="cstat-no" title="statement not covered" >      properties: {</span>
        // 设置默认的成本相关属性
<span class="cstat-no" title="statement not covered" >        costUnitPrice: 1,</span>
<span class="cstat-no" title="statement not covered" >        costMultiplierOrCount: 0,</span>
<span class="cstat-no" title="statement not covered" >        costBasis: 'unit',</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return this.create(params)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * 计算多边形的边界框
   * @param points - 多边形的顶点数组
   * @returns 边界框对象，包含width和height
   */
<span class="cstat-no" title="statement not covered" >  private calculatePolygonBoundingBox(points: PointData[]): { width: number, height: number } {</span>
<span class="cstat-no" title="statement not covered" >    if (!points || points.length === 0) {</span>
<span class="cstat-no" title="statement not covered" >      return { width: 0, height: 0 }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    let minX = points[0].x</span>
<span class="cstat-no" title="statement not covered" >    let minY = points[0].y</span>
<span class="cstat-no" title="statement not covered" >    let maxX = points[0].x</span>
<span class="cstat-no" title="statement not covered" >    let maxY = points[0].y</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    for (let i = 1; i &lt; points.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >      const p = points[i]</span>
<span class="cstat-no" title="statement not covered" >      minX = Math.min(minX, p.x)</span>
<span class="cstat-no" title="statement not covered" >      minY = Math.min(minY, p.y)</span>
<span class="cstat-no" title="statement not covered" >      maxX = Math.max(maxX, p.x)</span>
<span class="cstat-no" title="statement not covered" >      maxY = Math.max(maxY, p.y)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return {</span>
<span class="cstat-no" title="statement not covered" >      width: maxX - minX,</span>
<span class="cstat-no" title="statement not covered" >      height: maxY - minY,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:58:01.486Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    