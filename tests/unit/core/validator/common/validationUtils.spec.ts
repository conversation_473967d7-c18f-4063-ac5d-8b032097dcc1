/**
 * Unit tests for validation utilities
 *
 * Tests the utility functions used by validators
 */

import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  createValidationResult,
  isPolygonClosed,
  isValidColor,
  isValidPoint,
  isValueInRange,
} from '@/core/validator/common/validationUtils'
import { Point } from '@/types/core/element/geometry/point'

describe('validation Utilities', () => {
  let consoleSpy: any

  beforeEach(() => {
    consoleSpy = {
      warn: vi.spyOn(console, 'warn').mockImplementation(() => {}),
      error: vi.spyOn(console, 'error').mockImplementation(() => {}),
    }
    vi.clearAllMocks()
  })

  describe('isValidPoint', () => {
    it('returns true for valid points', () => {
      expect(isValidPoint({ x: 0, y: 0 })).toBe(true)
      expect(isValidPoint({ x: 100, y: 200 })).toBe(true)
      expect(isValidPoint({ x: -50, y: 75 })).toBe(true)
      expect(isValidPoint(new Point(10, 20))).toBe(true)
    })

    it('returns false for invalid points', () => {
      expect(isValidPoint({ x: Number.NaN, y: 0 })).toBe(false)
      expect(isValidPoint({ x: 0, y: Number.NaN })).toBe(false)
      expect(isValidPoint({ x: Infinity, y: 0 })).toBe(false)
      expect(isValidPoint({ x: 0, y: Infinity })).toBe(false)
      expect(isValidPoint(null as any)).toBe(false)
      expect(isValidPoint(undefined as any)).toBe(false)
      expect(isValidPoint({} as any)).toBe(false)
    })
  })

  describe('isValidColor', () => {
    it('returns true for valid hex colors', () => {
      expect(isValidColor('#000')).toBe(true)
      expect(isValidColor('#FFF')).toBe(true)
      expect(isValidColor('#000000')).toBe(true)
      expect(isValidColor('#FFFFFF')).toBe(true)
      expect(isValidColor('#3b82f6')).toBe(true)
    })

    it('returns true for valid rgb colors', () => {
      expect(isValidColor('rgb(0, 0, 0)')).toBe(true)
      expect(isValidColor('rgb(255, 255, 255)')).toBe(true)
      expect(isValidColor('rgb(100, 150, 200)')).toBe(true)
    })

    it('returns true for valid rgba colors', () => {
      expect(isValidColor('rgba(0, 0, 0, 0)')).toBe(true)
      expect(isValidColor('rgba(255, 255, 255, 1)')).toBe(true)
      expect(isValidColor('rgba(100, 150, 200, 0.5)')).toBe(true)
    })

    it('returns true for valid named colors', () => {
      expect(isValidColor('red')).toBe(true)
      expect(isValidColor('blue')).toBe(true)
      expect(isValidColor('green')).toBe(true)
      expect(isValidColor('transparent')).toBe(true)
    })

    it('returns false for invalid colors', () => {
      expect(isValidColor('')).toBe(false)
      expect(isValidColor(null as any)).toBe(false)
      expect(isValidColor(undefined as any)).toBe(false)
      expect(isValidColor('#XYZ')).toBe(false)
      expect(isValidColor('rgb(300, 0, 0)')).toBe(false)
      expect(isValidColor('rgba(0, 0, 0, 2)')).toBe(false)
      expect(isValidColor('not-a-color')).toBe(false)
    })
  })

  describe('isPolygonClosed', () => {
    it('returns true for closed polygons', () => {
      const closedTriangle = [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 50, y: 100 },
        { x: 0, y: 0 }, // Same as first point
      ]
      expect(isPolygonClosed(closedTriangle)).toBe(true)

      // Points with very small difference (within error margin)
      const almostClosedTriangle = [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 50, y: 100 },
        { x: 0.0001, y: 0.0001 }, // Very close to first point
      ]
      expect(isPolygonClosed(almostClosedTriangle)).toBe(true)
    })

    it('returns false for open polygons', () => {
      const openTriangle = [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 50, y: 100 },
        // Missing the closing point
      ]
      expect(isPolygonClosed(openTriangle)).toBe(false)

      const notClosedTriangle = [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 50, y: 100 },
        { x: 10, y: 10 }, // Different from first point
      ]
      expect(isPolygonClosed(notClosedTriangle)).toBe(false)
    })

    it('returns false for invalid polygons', () => {
      expect(isPolygonClosed([])).toBe(false)
      expect(isPolygonClosed([{ x: 0, y: 0 }])).toBe(false)
      expect(isPolygonClosed([{ x: 0, y: 0 }, { x: 100, y: 0 }])).toBe(false)
      expect(isPolygonClosed(null as any)).toBe(false)
      expect(isPolygonClosed(undefined as any)).toBe(false)
    })
  })

  describe('isValueInRange', () => {
    it('returns true for values within range', () => {
      expect(isValueInRange(5, 0, 10)).toBe(true)
      expect(isValueInRange(0, 0, 10)).toBe(true) // Min boundary
      expect(isValueInRange(10, 0, 10)).toBe(true) // Max boundary
    })

    it('returns false for values outside range', () => {
      expect(isValueInRange(-1, 0, 10)).toBe(false)
      expect(isValueInRange(11, 0, 10)).toBe(false)
      expect(isValueInRange(Number.NaN, 0, 10)).toBe(false)
      expect(isValueInRange(Infinity, 0, 10)).toBe(false)
    })
  })

  describe('createValidationResult', () => {
    it('should create valid result with no errors', () => {
      const result = createValidationResult(true)
      expect(result.valid).toBe(true)
      expect(result.errors).toBeUndefined()
      expect(result.message).toBeUndefined()
    })

    it('should create invalid result with string errors', () => {
      const result = createValidationResult(false, ['Error 1', 'Error 2'])
      expect(result.valid).toBe(false)
      expect(result.errors).toHaveLength(2)
      expect(result.errors?.[0].code).toBe('GENERIC_VALIDATION')
      expect(result.errors?.[0].message).toBe('Error 1')
      expect(result.errors?.[1].message).toBe('Error 2')
      expect(result.message).toBe('Error 1') // First error becomes message
    })

    it('should create invalid result with ValidationError objects', () => {
      const errors = [
        { code: 'ERROR_1', message: 'First error', path: 'prop1' },
        { code: 'ERROR_2', message: 'Second error', path: 'prop2' },
      ]
      const result = createValidationResult(false, errors)
      expect(result.valid).toBe(false)
      expect(result.errors).toHaveLength(2)
      expect(result.errors?.[0].code).toBe('ERROR_1')
      expect(result.errors?.[0].path).toBe('prop1')
      expect(result.errors?.[1].code).toBe('ERROR_2')
      expect(result.message).toBe('First error') // First error becomes message
    })

    it('should use provided message instead of error message', () => {
      const errors = [
        { code: 'ERROR_1', message: 'First error' },
        { code: 'ERROR_2', message: 'Second error' },
      ]
      const result = createValidationResult(false, errors, 'Custom message')
      expect(result.valid).toBe(false)
      expect(result.message).toBe('Custom message')
    })

    it('should handle mixed string and object errors', () => {
      const errors = [
        'String error',
        { code: 'ERROR_1', message: 'Object error' },
      ]
      const result = createValidationResult(false, errors as any)
      expect(result.valid).toBe(false)
      expect(result.errors).toHaveLength(2)
      expect(result.errors?.[0].code).toBe('GENERIC_VALIDATION')
      expect(result.errors?.[0].message).toBe('String error')
      expect(result.errors?.[1].code).toBe('ERROR_1')
    })

    it('should skip invalid error items', () => {
      const errors = [
        'Valid string',
        null,
        { notAValidError: true },
        { code: 'VALID_ERROR', message: 'Valid error' },
      ]
      const result = createValidationResult(false, errors as any)
      expect(result.valid).toBe(false)
      expect(result.errors).toHaveLength(2)
      expect(result.errors?.[0].message).toBe('Valid string')
      expect(result.errors?.[1].code).toBe('VALID_ERROR')
      expect(consoleSpy.warn).toHaveBeenCalledTimes(2) // Two invalid items
    })

    it('should skip empty string errors', () => {
      const result = createValidationResult(false, ['', '  '])
      expect(result.valid).toBe(false)
      expect(result.errors).toBeUndefined()
    })

    it('should ensure valid is false if errors exist', () => {
      // Even if isValid param is true, result should be false if errors exist
      const result = createValidationResult(true, ['Error exists'])
      expect(result.valid).toBe(false)
    })
  })
})
