import { beforeEach, describe, expect, it, vi } from 'vitest'
import { AppEventBusImpl } from '@/services/event-bus/AppEventBus'
import { AppEventType } from '@/types/services/events'

describe('appEventBus', () => {
  let eventBus: AppEventBusImpl
  let mockLogger: any

  beforeEach(() => {
    mockLogger = {
      info: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    }
    eventBus = new AppEventBusImpl()
  })

  describe('basic functionality', () => {
    it('should initialize with empty handlers', () => {
      expect(eventBus.handlers.size).toBe(0)
    })

    it('should publish events and notify subscribers', () => {
      const mockCallback = vi.fn()
      const eventType = AppEventType.SHAPE_CREATE_REQUEST
      const payload = { ElementType: 'rectangle' }

      eventBus.subscribe(eventType, mockCallback)
      eventBus.publish({ type: eventType, payload })

      expect(mockCallback).toHaveBeenCalledWith(expect.objectContaining({
        type: eventType,
        payload,
      }))
    })

    it('should allow subscribing to multiple events', () => {
      const mockCallback = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, mockCallback)
      eventBus.subscribe(AppEventType.SHAPE_EDIT_REQUEST, mockCallback)
      eventBus.publish({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: { ElementType: 'rectangle' } })
      eventBus.publish({ type: AppEventType.SHAPE_EDIT_REQUEST, payload: { shapeId: '123' } })

      expect(mockCallback).toHaveBeenCalledTimes(2)
    })

    it('should allow unsubscribing from specific event types', () => {
      const mockCallback = vi.fn()
      const eventType = AppEventType.SHAPE_CREATE_REQUEST

      const unsubscribe = eventBus.subscribe(eventType, mockCallback)
      eventBus.publish({ type: eventType, payload: {} })
      expect(mockCallback).toHaveBeenCalledTimes(1)

      unsubscribe()
      eventBus.publish({ type: eventType, payload: {} })
      expect(mockCallback).toHaveBeenCalledTimes(1) // Still 1, not called again
    })

    it('should allow unsubscribing from all events', () => {
      const mockCallback = vi.fn()
      const eventType = AppEventType.SHAPE_CREATE_REQUEST

      eventBus.subscribe(eventType, mockCallback)
      eventBus.publish({ type: eventType, payload: {} })
      expect(mockCallback).toHaveBeenCalledTimes(1)

      eventBus.unsubscribeAll(eventType)
      eventBus.publish({ type: eventType, payload: {} })
      expect(mockCallback).toHaveBeenCalledTimes(1) // Still 1, not called again
    })
  })

  describe('error handling', () => {
    it('should catch and log errors in subscribers', () => {
      const errorCallback = vi.fn().mockImplementation(() => {
        throw new Error('Subscriber error')
      })

      console.error = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, errorCallback)
      eventBus.publish({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: {} })

      expect(errorCallback).toHaveBeenCalled()
      expect(console.error).toHaveBeenCalled()
    })

    it('should continue notifying other subscribers after an error', () => {
      const errorCallback = vi.fn().mockImplementation(() => {
        throw new Error('Subscriber error')
      })
      const validCallback = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, errorCallback)
      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, validCallback)

      eventBus.publish({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: {} })

      expect(errorCallback).toHaveBeenCalled()
      expect(validCallback).toHaveBeenCalled()
    })
  })

  describe('configuration and reset', () => {
    it('should configure the event bus', () => {
      eventBus.configure({ enableLogging: true })
      expect(eventBus.config.enableLogging).toBe(true)
    })

    it('should reset the event bus', () => {
      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, () => {})
      eventBus.reset()
      expect(eventBus.getSubscriptions().size).toBe(0)
    })

    it('should clear all subscriptions', () => {
      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, () => {})
      eventBus.clear()
      expect(eventBus.getSubscriptions().size).toBe(0)
    })
  })

  describe('advanced functionality', () => {
    it('should support async handlers', async () => {
      const asyncHandler = vi.fn().mockResolvedValue('done')

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, asyncHandler, { async: true })
      await eventBus.publishAsync({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: {} })

      expect(asyncHandler).toHaveBeenCalled()
    })

    it('should handle errors in handlers', () => {
      const errorHandler = vi.fn().mockImplementation(() => {
        throw new Error('Test error')
      })

      console.error = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, errorHandler)
      eventBus.publish({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: {} })

      expect(errorHandler).toHaveBeenCalled()
      expect(console.error).toHaveBeenCalled()
    })

    it('should support debounced events', () => {
      vi.useFakeTimers()

      const mockCallback = vi.fn()
      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, mockCallback, { debounce: 100 })

      eventBus.publish({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: {} })
      eventBus.publish({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: {} })

      expect(mockCallback).not.toHaveBeenCalled()

      vi.advanceTimersByTime(150)

      expect(mockCallback).toHaveBeenCalledTimes(1)

      vi.useRealTimers()
    })

    it('should support throttled events', () => {
      vi.useFakeTimers()

      const mockCallback = vi.fn()
      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, mockCallback, { throttle: 100 })

      eventBus.publish({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: {} })
      expect(mockCallback).toHaveBeenCalledTimes(1)

      vi.advanceTimersByTime(50)
      eventBus.publish({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: {} })
      expect(mockCallback).toHaveBeenCalledTimes(1)

      vi.advanceTimersByTime(100)
      eventBus.publish({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: {} })
      expect(mockCallback).toHaveBeenCalledTimes(2)

      vi.useRealTimers()
    })
  })

  describe('subscription options', () => {
    it('should handle once option correctly', () => {
      const mockCallback = vi.fn()

      eventBus.once(AppEventType.SHAPE_CREATE_REQUEST, mockCallback)

      eventBus.publish({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: {} })
      eventBus.publish({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: {} })

      expect(mockCallback).toHaveBeenCalledTimes(1)
    })

    it('should support filter option', () => {
      const mockCallback = vi.fn()
      const filter = (event: any) => event.payload.value > 10

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, mockCallback, { filter })

      eventBus.publish({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: { value: 5 } })
      eventBus.publish({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: { value: 15 } })

      expect(mockCallback).toHaveBeenCalledTimes(1)
      expect(mockCallback).toHaveBeenCalledWith(expect.objectContaining({
        payload: { value: 15 },
      }))
    })

    it('should support context option', () => {
      const context = { value: 'test' }
      const contextHandler = vi.fn(function (this: any) {
        return this.value
      })

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, contextHandler, { context })
      eventBus.publish({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: {} })

      expect(contextHandler).toHaveBeenCalled()
      expect(contextHandler.mock.results[0].value).toBe('test')
    })
  })

  describe('aliases', () => {
    it('should support on as alias for subscribe', () => {
      const mockCallback = vi.fn()

      const unsubscribe = eventBus.on(AppEventType.SHAPE_CREATE_REQUEST, mockCallback)

      expect(typeof unsubscribe).toBe('function')
      expect(eventBus.getSubscriptions().has(AppEventType.SHAPE_CREATE_REQUEST)).toBe(true)
    })

    it('should support off as alias for unsubscribe', () => {
      const mockCallback = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, mockCallback)
      const result = eventBus.off(AppEventType.SHAPE_CREATE_REQUEST, mockCallback)

      expect(result).toBe(true)
      expect(eventBus.getSubscriptions().has(AppEventType.SHAPE_CREATE_REQUEST)).toBe(false)
    })

    it('should support emit as alias for publish', () => {
      const mockCallback = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, mockCallback)
      const result = eventBus.emit({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: {} })

      expect(result).toBe(true)
      expect(mockCallback).toHaveBeenCalled()
    })

    it('should support emitAsync as alias for publishAsync', async () => {
      const asyncHandler = vi.fn().mockResolvedValue('done')

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, asyncHandler, { async: true })
      const result = await eventBus.emitAsync({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: {} })

      expect(asyncHandler).toHaveBeenCalled()
      expect(result.length).toBeGreaterThan(0)
    })
  })

  describe('edge cases', () => {
    it('should handle invalid events', () => {
      console.error = vi.fn()

      const result = eventBus.publish(null as any)

      expect(result).toBe(false)
      expect(console.error).toHaveBeenCalled()
    })

    it('should handle events with no subscribers', () => {
      const result = eventBus.publish({ type: AppEventType.SHAPE_CREATE_REQUEST, payload: {} })

      expect(result).toBe(false)
    })

    it('should handle unsubscribing from non-existent event type', () => {
      const result = eventBus.unsubscribe(AppEventType.SHAPE_CREATE_REQUEST, () => {})

      expect(result).toBe(false)
    })

    it('should handle unsubscribing from non-existent handler', () => {
      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, () => {})

      const result = eventBus.unsubscribe(AppEventType.SHAPE_CREATE_REQUEST, () => {})

      expect(result).toBe(false)
    })
  })
})
