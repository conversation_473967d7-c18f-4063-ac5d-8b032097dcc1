/**
 * Provides a comprehensive keyboard event handling system for the application.
 *
 * @module services/input/keyboard-service
 */

import type { AppEventMap, AppEventType, BaseEvent, EventBus } from '@/types/services/events'
import type {
  KeyboardEventContext,
  KeyboardService,
  KeyboardServiceConfig, // Added
  KeyboardShortcut,
  KeyCombination, // Added KeyCombination
  KeyEventData,
  KeyModifiers,
} from '@/types/services/keyboard'
import type { LoggerService } from '@/types/services/logging'
import { handleError, safeExecute } from '@/lib/utils/errorUtils'
import { publishKeyPressed, publishKeyReleased } from '@/services/core/event-bus/helpers/publishers/inputPublishers'
import { getService, ServiceId } from '@/services/core/registry'

/**
 * Concrete implementation of the KeyboardService interface that manages
 * keyboard events and key bindings for the application.
 */
export class KeyboardServiceImpl implements KeyboardService {
  readonly serviceId: string = ServiceId.KeyboardService

  private keyBindings: Map<string, {
    callback: (event: KeyEventData) => void // Changed from inline object to KeyEventData
    options: {
      preventDefault: boolean
      stopPropagation: boolean
      onKeyUp: boolean
      description?: string
    }
  }> = new Map()

  private shortcuts: Map<string, KeyboardShortcut> = new Map() // Added for shortcut management
  private currentContext: KeyboardEventContext = { source: 'global' } // Added for context management
  private config: KeyboardServiceConfig = { // Added for config management
    enabled: true,
    preventDefaultForRegistered: true,
    stopPropagationForRegistered: false,
  }

  private eventBus: EventBus<AppEventMap>
  public logger: LoggerService

  constructor(
    eventBus: EventBus<AppEventMap>,
    logger: LoggerService,
  ) {
    this.eventBus = eventBus
    this.logger = logger
    this.logger.info('[KeyboardServiceImpl] Initialized.')
  }

  public initialize(): void {
    safeExecute(() => {
      document.addEventListener('keydown', this.handleKeyDown)
      document.addEventListener('keyup', this.handleKeyUp)
      this.logger.info('Keyboard service event listeners initialized.')
    }, { component: 'KeyboardServiceImpl', operation: 'initialize' })
  }

  public cleanup(): void {
    safeExecute(() => {
      document.removeEventListener('keydown', this.handleKeyDown)
      document.removeEventListener('keyup', this.handleKeyUp)
      this.keyBindings.clear()
      this.shortcuts.clear()
      this.logger.info('Keyboard service cleaned up.')
    }, { component: 'KeyboardServiceImpl', operation: 'cleanup' })
  }

  public static create(logger?: LoggerService): KeyboardService {
    try {
      const eventBus = getService<EventBus<AppEventMap>>(ServiceId.EventBus)
      const loggerService = logger || getService<LoggerService>(ServiceId.Logger)
      const service = new KeyboardServiceImpl(eventBus, loggerService)
      service.initialize() // Initialize after creation
      return service
    }
    catch (error) {
      const err = error instanceof Error ? error.message : String(error);
      // Fallback logger if service logger isn't available yet
      (logger || console).error(`Failed to create KeyboardService: ${err}`)
      throw new Error(`Failed to create KeyboardService: ${err}`)
    }
  }

  private handleKeyDown = (e: globalThis.KeyboardEvent): void => {
    const keyEvent: KeyEventData = {
      key: e.key,
      code: e.code,
      modifiers: { altKey: e.altKey, ctrlKey: e.ctrlKey, shiftKey: e.shiftKey, metaKey: e.metaKey },
      originalEvent: e,
    }
    publishKeyPressed(this.eventBus as unknown as EventBus, e.key, e.code, keyEvent.modifiers, e)

    if (!this.config.enabled)
      return
    this.processKeyBindings(keyEvent, false)
    this.processShortcuts(keyEvent, e) // Pass original event for preventDefault/stopPropagation
  }

  private handleKeyUp = (e: globalThis.KeyboardEvent): void => {
    const keyEvent: KeyEventData = {
      key: e.key,
      code: e.code,
      modifiers: { altKey: e.altKey, ctrlKey: e.ctrlKey, shiftKey: e.shiftKey, metaKey: e.metaKey },
      originalEvent: e,
    }
    publishKeyReleased(this.eventBus as unknown as EventBus, e.key, e.code, keyEvent.modifiers, e)

    if (!this.config.enabled)
      return
    this.processKeyBindings(keyEvent, true)
  }

  private processKeyBindings(keyEvent: KeyEventData, isKeyUp: boolean): void {
    const keyString = this.createKeyStringFromKeyEvent(keyEvent)
    const binding = this.keyBindings.get(keyString)
    if (binding && binding.options.onKeyUp === isKeyUp) {
      if (binding.options.preventDefault && keyEvent.originalEvent)
        keyEvent.originalEvent.preventDefault()
      if (binding.options.stopPropagation && keyEvent.originalEvent)
        keyEvent.originalEvent.stopPropagation()
      binding.callback(keyEvent)
    }
  }

  private processShortcuts(keyEvent: KeyEventData, originalDomEvent: globalThis.KeyboardEvent): void {
    for (const shortcut of this.shortcuts.values()) {
      if (shortcut.enabled && this.isShortcutMatch(shortcut.keyCombination, keyEvent)) {
        if (this.config.preventDefaultForRegistered)
          originalDomEvent.preventDefault()
        if (this.config.stopPropagationForRegistered)
          originalDomEvent.stopPropagation()

        if (typeof shortcut.action === 'function') {
          shortcut.action(originalDomEvent)
        }
        else {
          // Handle string action identifier (e.g., publish an event)
          const eventPayload: BaseEvent = {
            type: shortcut.action as AppEventType,
            payload: keyEvent,
            timestamp: Date.now(),
          }
          this.eventBus.publish(eventPayload)
          this.logger.info(`Shortcut action triggered: ${shortcut.action}`)
        }
        return // Assuming one shortcut match is enough
      }
    }
  }

  private isShortcutMatch(combo: KeyCombination, event: KeyEventData): boolean {
    return event.key.toLowerCase() === combo.key.toLowerCase()
      && (combo.alt === undefined || combo.alt === event.modifiers.altKey)
      && (combo.ctrl === undefined || combo.ctrl === event.modifiers.ctrlKey)
      && (combo.shift === undefined || combo.shift === event.modifiers.shiftKey)
      && (combo.meta === undefined || combo.meta === event.modifiers.metaKey)
  }

  private createKeyString(key: string, modifiers: KeyModifiers): string { // Modifiers are expected to be complete
    let keyString = ''
    if (modifiers.ctrlKey)
      keyString += 'ctrl+'
    if (modifiers.altKey)
      keyString += 'alt+'
    if (modifiers.shiftKey)
      keyString += 'shift+'
    if (modifiers.metaKey)
      keyString += 'meta+'
    keyString += key.toLowerCase()
    return keyString
  }

  private createKeyStringFromKeyEvent(e: KeyEventData): string {
    return this.createKeyString(e.key, e.modifiers)
  }

  // --- Methods from KeyboardService interface ---

  public registerShortcut(shortcut: KeyboardShortcut): string {
    this.logger.info(`Registering shortcut: ${shortcut.id}`, shortcut)
    if (this.shortcuts.has(shortcut.id)) {
      this.logger.warn(`Shortcut with ID ${shortcut.id} already exists. Overwriting.`)
    }
    this.shortcuts.set(shortcut.id, { ...shortcut, enabled: shortcut.enabled !== false })
    return shortcut.id
  }

  public unregisterShortcut(id: string): boolean {
    this.logger.info(`Unregistering shortcut: ${id}`)
    return this.shortcuts.delete(id)
  }

  public getShortcut(id: string): KeyboardShortcut | undefined {
    return this.shortcuts.get(id)
  }

  public getAllShortcuts(): KeyboardShortcut[] {
    return Array.from(this.shortcuts.values())
  }

  public setContext(context: KeyboardEventContext): void {
    this.logger.info('Setting keyboard context:', context)
    this.currentContext = { ...this.currentContext, ...context }
  }

  public getContext(): KeyboardEventContext {
    return { ...this.currentContext }
  }

  public handleKeyEvent(event: globalThis.KeyboardEvent): boolean {
    this.logger.debug('Manually handling KeyEvent:', event.type, event.key)
    if (event.type === 'keydown') {
      this.handleKeyDown(event)
      return true // Indicate event was potentially handled
    }
    else if (event.type === 'keyup') {
      this.handleKeyUp(event)
      return true // Indicate event was potentially handled
    }
    return false // Event type not handled by this method
  }

  public enableShortcut(id: string): boolean {
    const shortcut = this.shortcuts.get(id)
    if (shortcut) {
      shortcut.enabled = true
      this.logger.info(`Shortcut enabled: ${id}`)
      return true
    }
    this.logger.warn(`Shortcut not found for enabling: ${id}`)
    return false
  }

  public disableShortcut(id: string): boolean {
    const shortcut = this.shortcuts.get(id)
    if (shortcut) {
      shortcut.enabled = false
      this.logger.info(`Shortcut disabled: ${id}`)
      return true
    }
    this.logger.warn(`Shortcut not found for disabling: ${id}`)
    return false
  }

  public getConfig(): KeyboardServiceConfig {
    return { ...this.config }
  }

  public setConfig(config: Partial<KeyboardServiceConfig>): void {
    this.config = { ...this.config, ...config }
    this.logger.info('KeyboardService config updated:', this.config)
  }

  public registerKeyBinding(
    key: string,
    callback: (event: KeyEventData) => void,
    options?: {
      preventDefault?: boolean
      stopPropagation?: boolean
      onKeyUp?: boolean
      description?: string
      // If modifiers are to be passed via options, they should be defined here
      // e.g., ctrl?: boolean, alt?: boolean, etc.
      // For now, assuming 'key' parameter is a pre-normalized string like "ctrl+a" or "b"
    },
  ): () => void {
    return safeExecute(() => {
      // Assuming 'key' is already a normalized string representing the combination
      // e.g., "ctrl+s", "a", "shift+alt+k"
      // The createKeyString and createKeyStringFromKeyEvent methods are used for
      // generating such strings from actual keyboard events.
      const normalizedKey = key.toLowerCase()

      this.keyBindings.set(normalizedKey, {
        callback,
        options: {
          preventDefault: options?.preventDefault ?? true,
          stopPropagation: options?.stopPropagation ?? false,
          onKeyUp: options?.onKeyUp ?? false,
          description: options?.description,
        },
      })
      this.logger.debug?.(`Registered key binding for '${normalizedKey}'`)
      return () => this.unregisterKeyBinding(normalizedKey) // Pass the same normalized key
    }, { component: 'KeyboardServiceImpl', operation: 'registerKeyBinding', metadata: { key, options } }) || (() => {})
  }

  public unregisterKeyBinding(key: string): void {
    safeExecute(() => {
      const normalizedKey = key.toLowerCase() // Ensure consistency with registration
      const removed = this.keyBindings.delete(normalizedKey)
      if (removed)
        this.logger.debug?.(`Unregistered key binding for '${normalizedKey}'`)
    }, { component: 'KeyboardServiceImpl', operation: 'unregisterKeyBinding', metadata: { key } })
  }

  public getKeyBindings(): Map<string, {
    callback: (event: KeyEventData) => void
    options: {
      preventDefault: boolean
      stopPropagation: boolean
      onKeyUp: boolean
      description?: string
    }
  }> {
    return new Map(this.keyBindings)
  }

  public addKeyDownListener(listener: (event: globalThis.KeyboardEvent) => void): () => void {
    return safeExecute(() => {
      const handler = (e: globalThis.KeyboardEvent) => {
        try {
          listener(e)
        }
        catch (error) {
          handleError(error, { component: 'KeyboardServiceImpl', operation: 'keyDownListener', metadata: { key: e.key } })
        }
      }
      document.addEventListener('keydown', handler)
      return () => document.removeEventListener('keydown', handler)
    }, { component: 'KeyboardServiceImpl', operation: 'addKeyDownListener' }) || (() => {})
  }

  public addKeyUpListener(listener: (event: globalThis.KeyboardEvent) => void): () => void {
    return safeExecute(() => {
      const handler = (e: globalThis.KeyboardEvent) => {
        try {
          listener(e)
        }
        catch (error) {
          handleError(error, { component: 'KeyboardServiceImpl', operation: 'keyUpListener', metadata: { key: e.key } })
        }
      }
      document.addEventListener('keyup', handler)
      return () => document.removeEventListener('keyup', handler)
    }, { component: 'KeyboardServiceImpl', operation: 'addKeyUpListener' }) || (() => {})
  }
}

// Legacy singleton functions removed - use service registry instead
// Use getService(ServiceId.KeyboardService) to access the keyboard service
