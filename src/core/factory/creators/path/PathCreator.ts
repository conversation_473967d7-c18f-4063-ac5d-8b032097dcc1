/**
 * Abstract Base Class for Path Element Creator Implementations
 *
 * @remarks
 * This abstract class defines the common structure and helper methods for all concrete path element creators
 * (e.g., `LineCreator`, `ArcCreator`, `CubicCreator`).
 * It provides a `createCommonProperties` method to ensure consistency in how base
 * {@link ShapeElement} properties (ID, type, styling, layer info, metadata, etc.) are handled.
 *
 * Concrete path creators must extend this class and implement the `create` and `createDefault` methods.
 *
 * @module core/factory/creators/path/PathCreator
 * @see {@link ElementFactory} - Uses these creators.
 * @see {@link PathCreationOptionsUnion} - Union of all possible path creation parameters.
 * @see {@link ShapeElement} - The base type for created path elements.
 * @see {@link CommonShapeElementProps} - For the structure of common properties.
 */

import type { CommonShapeElementProps } from '../shape/ShapeCreator' // Reuse from ShapeCreator
import type { BaseElementCreationParams } from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point'
import type {
  ElementType as CoreElementType,
  ShapeElement,
  // MetadataProperties, // Removed
  // BaseStyleProperties, // Removed
  // PathElement, // Removed as it's not exported
} from '@/types/core/elementDefinitions'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
import { ensureCompleteMetadata } from '@/lib/utils/element/elementUtils'
import { MajorCategory } from '@/types/core/majorMinorTypes'

/**
 * Defines the contract for classes that create path-based elements.
 */
export abstract class PathCreator<
  T extends ShapeElement,
  P extends BaseElementCreationParams, // PathCreationOptionsUnion is a subset of BaseElementCreationParams based types
> {
  protected elementType: CoreElementType

  constructor(type: CoreElementType) {
    this.elementType = type
  }

  // Reusing CommonShapeElementProps type from ShapeCreator as path elements are also ShapeElements
  protected createCommonProperties(
    id: string,
    params: P,
  ): CommonShapeElementProps {
    const paramSpecificPosition = (params as unknown as { position?: PointData }).position
    const paramSpecificStart = (params as unknown as { start?: PointData }).start

    // For paths, 'position' might be the start point or a nominal center.
    // Default to (0,0) if no specific position or start point is easily determined from params here.
    // Concrete creators should override position if a more meaningful one (like centroid) is calculated.
    let position: PointData = { x: 0, y: 0, z: 0 }
    if (paramSpecificPosition) {
      position = { x: paramSpecificPosition.x, y: paramSpecificPosition.y, z: paramSpecificPosition.z ?? 0 }
    }
    else if (paramSpecificStart) {
      position = { x: paramSpecificStart.x, y: paramSpecificStart.y, z: paramSpecificStart.z ?? 0 }
    }

    // Helper function to get descriptive names for path elements
    const getPathElementName = (elementType: string): string => {
      switch (elementType.toUpperCase()) {
        case 'LINE':
          return 'Line'
        case 'POLYLINE':
          return 'Polyline'
        case 'ARC':
          return 'Arc'
        case 'QUADRATIC':
          return 'Quadratic Curve'
        case 'CUBIC':
          return 'Cubic Curve'
        default:
          return `${elementType}`
      }
    }

    const metadata = ensureCompleteMetadata({
      ...(params.metadata || {}),
      name: params.metadata?.name ?? getPathElementName(params.type),
      createdAt: params.metadata?.createdAt ?? Date.now(),
      updatedAt: params.metadata?.updatedAt ?? Date.now(),
    })

    const majorCategory = params.majorCategory ?? MajorCategory.FURNITURE // Defaulting, expect override or from params
    const minorCategory = params.minorCategory
    const zLevelId = params.zLevelId
    const isFixedCategory = params.isFixedCategory

    return {
      id,
      type: params.type as CoreElementType,
      majorCategory,
      minorCategory,
      zLevelId,
      isFixedCategory,
      position,
      rotation: params.rotation ?? 0,
      visible: params.visible ?? true,
      locked: params.locked ?? false,
      selectable: params.selectable ?? true,
      draggable: params.draggable ?? true,
      showHandles: params.showHandles ?? true,
      metadata,
      layer: params.layer,
      zIndex: params.zIndex,
      fill: params.fill, // Paths might not always have fill
      stroke: params.stroke,
      strokeWidth: params.strokeWidth,
      opacity: params.opacity,
      strokeDasharray: params.strokeDasharray,
    }
  }

  /**
   * Creates a {@link ShapeElement} representing a specific path type.
   *
   * @param params - A {@link PathCreationOptionsUnion} object containing parameters
   *                 specific to the type of path being created.
   * @returns A Promise that resolves to the created {@link ShapeElement}.
   */
  abstract create(params: P): Promise<T>

  /**
   * Creates a default instance of the path element type.
   *
   * @param id - A unique identifier for the new default element.
   * @param position - The nominal {@link PointData} for positioning the default path element.
   * @param majorCategoryOverride - Optional override for majorCategory.
   * @param minorCategoryOverride - Optional override for minorCategory.
   * @param zLevelIdOverride - Optional override for zLevelId.
   * @param isFixedCategoryOverride - Optional override for isFixedCategory.
   * @returns A Promise that resolves to the created default {@link ShapeElement}.
   */
  abstract createDefault(
    id: string,
    position: PointData,
    majorCategoryOverride?: MajorCategory,
    minorCategoryOverride?: MinorCategory,
    zLevelIdOverride?: string,
    isFixedCategoryOverride?: boolean
  ): Promise<T>
}
