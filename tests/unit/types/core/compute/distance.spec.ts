import type { DistanceCalculatorStrategy } from '@/types/core/compute/distanceComputeTypes'

import type { Element } from '@/types/core/element/element'
import { describe, expect, it } from 'vitest'

describe('distance Calculator Strategy Interface', () => {
  it('should define the required methods for distance calculation strategies', () => {
    // Create a mock implementation of DistanceCalculatorStrategy
    class MockDistanceCalculator implements DistanceCalculatorStrategy {
      calculateDistance(elementA: Element, elementB: Element): number {
        // Simple mock implementation
        return 100
      }

      getElementType(): string {
        return 'mock-element'
      }
    }

    const calculator = new MockDistanceCalculator()

    // Test the interface implementation
    expect(typeof calculator.calculateDistance).toBe('function')
    expect(typeof calculator.getElementType).toBe('function')

    // Test the mock implementation
    const mockElementA = { id: 'test-element-a' } as Element
    const mockElementB = { id: 'test-element-b' } as Element
    expect(calculator.calculateDistance(mockElementA, mockElementB)).toBe(100)
    expect(calculator.getElementType()).toBe('mock-element')
  })

  it('should allow implementation for different shape types', () => {
    // Create a mock rectangle distance calculator
    class MockRectangleDistanceCalculator implements DistanceCalculatorStrategy {
      calculateDistance(elementA: Element, elementB: Element): number {
        // Mock implementation for rectangle
        return 200
      }

      getElementType(): string {
        return 'rectangle'
      }
    }

    // Create a mock circle distance calculator
    class MockCircleDistanceCalculator implements DistanceCalculatorStrategy {
      calculateDistance(elementA: Element, elementB: Element): number {
        // Mock implementation for circle
        return 150
      }

      getElementType(): string {
        return 'circle'
      }
    }

    const rectangleCalculator = new MockRectangleDistanceCalculator()
    const circleCalculator = new MockCircleDistanceCalculator()

    // Test the implementations
    const mockElementA = { id: 'test-element-a' } as Element
    const mockElementB = { id: 'test-element-b' } as Element

    expect(rectangleCalculator.calculateDistance(mockElementA, mockElementB)).toBe(200)
    expect(rectangleCalculator.getElementType()).toBe('rectangle')

    expect(circleCalculator.calculateDistance(mockElementA, mockElementB)).toBe(150)
    expect(circleCalculator.getElementType()).toBe('circle')
  })

  it('should allow for complex distance calculation logic', () => {
    // Create a mock distance calculator with more complex logic
    class MockComplexDistanceCalculator implements DistanceCalculatorStrategy {
      calculateDistance(elementA: Element, elementB: Element): number {
        // Mock implementation that simulates getting positions from elements
        const positionA = { x: 0, y: 0 }
        const positionB = { x: 30, y: 40 }

        // Calculate Euclidean distance
        const dx = positionB.x - positionA.x
        const dy = positionB.y - positionA.y
        return Math.sqrt(dx * dx + dy * dy)
      }

      getElementType(): string {
        return 'complex-element'
      }
    }

    const complexCalculator = new MockComplexDistanceCalculator()

    // Test the implementation
    const mockElementA = { id: 'test-element-a' } as Element
    const mockElementB = { id: 'test-element-b' } as Element

    expect(complexCalculator.calculateDistance(mockElementA, mockElementB)).toBe(50) // sqrt(30^2 + 40^2) = 50
    expect(complexCalculator.getElementType()).toBe('complex-element')
  })

  it('should allow for error handling in implementations', () => {
    // Create a mock distance calculator with error handling
    class MockErrorHandlingCalculator implements DistanceCalculatorStrategy {
      calculateDistance(elementA: Element, elementB: Element): number {
        // Check if elements have required properties
        if (!elementA?.id) {
          throw new Error('Invalid first element')
        }

        if (!elementB?.id) {
          throw new Error('Invalid second element')
        }

        // Mock implementation
        return 300
      }

      getElementType(): string {
        return 'error-handling-element'
      }
    }

    const errorHandlingCalculator = new MockErrorHandlingCalculator()

    // Test valid case
    const validElementA = { id: 'test-element-a' } as Element
    const validElementB = { id: 'test-element-b' } as Element
    expect(errorHandlingCalculator.calculateDistance(validElementA, validElementB)).toBe(300)

    // Test error cases
    const invalidElementA = {} as Element
    const invalidElementB = {} as Element

    expect(() => errorHandlingCalculator.calculateDistance(invalidElementA, validElementB)).toThrow('Invalid first element')
    expect(() => errorHandlingCalculator.calculateDistance(validElementA, invalidElementB)).toThrow('Invalid second element')
  })

  it('should support different distance calculation algorithms', () => {
    // Create a mock distance calculator with multiple algorithms
    class MockMultiAlgorithmCalculator implements DistanceCalculatorStrategy {
      calculateDistance(elementA: Element, elementB: Element): number {
        // Get element types to determine which algorithm to use
        const typeA = (elementA as any).type || 'unknown'
        const typeB = (elementB as any).type || 'unknown'

        // Use different algorithms based on element types
        if (typeA === 'point' && typeB === 'point') {
          // Point-to-point distance
          return this.calculatePointToPointDistance(elementA, elementB)
        }
        else if (typeA === 'rectangle' && typeB === 'rectangle') {
          // Rectangle-to-rectangle distance
          return this.calculateRectangleToRectangleDistance(elementA, elementB)
        }
        else {
          // Default to bounding box distance
          return this.calculateBoundingBoxDistance(elementA, elementB)
        }
      }

      private calculatePointToPointDistance(pointA: Element, pointB: Element): number {
        // Mock point-to-point distance calculation
        return 10
      }

      private calculateRectangleToRectangleDistance(rectA: Element, rectB: Element): number {
        // Mock rectangle-to-rectangle distance calculation
        return 20
      }

      private calculateBoundingBoxDistance(elementA: Element, elementB: Element): number {
        // Mock bounding box distance calculation
        return 30
      }

      getElementType(): string {
        return 'multi-algorithm-element'
      }
    }

    const multiAlgorithmCalculator = new MockMultiAlgorithmCalculator()

    // Test with different element types
    const pointA = { id: 'point-a', type: 'point' } as Element
    const pointB = { id: 'point-b', type: 'point' } as Element
    const rectA = { id: 'rect-a', type: 'rectangle' } as Element
    const rectB = { id: 'rect-b', type: 'rectangle' } as Element
    const otherElement = { id: 'other', type: 'other' } as Element

    expect(multiAlgorithmCalculator.calculateDistance(pointA, pointB)).toBe(10)
    expect(multiAlgorithmCalculator.calculateDistance(rectA, rectB)).toBe(20)
    expect(multiAlgorithmCalculator.calculateDistance(pointA, otherElement)).toBe(30)
    expect(multiAlgorithmCalculator.getElementType()).toBe('multi-algorithm-element')
  })
})
