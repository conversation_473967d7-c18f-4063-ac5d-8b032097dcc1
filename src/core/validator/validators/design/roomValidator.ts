/**
 * Validator for room elements.
 */

import type { RoomProperties } from '@/types/core/element/design/roomDesignTypes'
import type {
  ShapeElement as ShapeModel,
} from '@/types/core/elementDefinitions'
import type { ValidatableShape, ValidationError } from '@/types/core/validator/validator-interface'
import { RoomType } from '@/types/core/element/design/roomDesignTypes'
import {
  ElementType, // For type alias
} from '@/types/core/elementDefinitions'
import { ValidationErrorCode } from '@/types/core/validator/error-codes'
import { BaseDesignValidator } from './designValidatorBase'
// import { isValidPoint } from '../common/validationUtils'; // Not used in this file

/**
 * Represents the expected structure of a room element for validation purposes.
 * This type combines the base {@link ShapeModel} (aliased from ShapeElement)
 * with room-specific {@link RoomProperties}.
 * @private
 * @internalremarks Combines generic shape data with specific room attributes for validation.
 */
type ValidatableRoom = ShapeModel & RoomProperties

/**
 * Validator for room elements.
 *
 * @extends BaseDesignValidator
 */
export class RoomValidator extends BaseDesignValidator {
  /**
   * The design element category this validator is for.
   * @protected
   * @readonly
   */
  protected readonly expectedElementType = ElementType.ROOM

  /**
   * Validate specific room properties.
   *
   * @param shape - The shape to validate.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected validateSpecific(shape: ValidatableShape): ValidationError[] {
    const errors: ValidationError[] = []

    const typeValidationErrors = this.validateBaseType(shape)
    if (typeValidationErrors.length > 0) {
      return typeValidationErrors
    }

    const room = shape as ValidatableRoom

    // Validate roomType
    if (room.roomType == null || !Object.values(RoomType).includes(room.roomType)) {
      errors.push({
        code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
        message: `Room requires a valid 'roomType'. Received: ${room.roomType}`,
        path: 'roomType',
        value: room.roomType,
      })
    }

    // Validate name (RoomProperties has 'name: string;')
    if (typeof room.name !== 'string' || room.name.trim() === '') {
      errors.push({
        code: ValidationErrorCode.INVALID_PROPERTY_VALUE, // Corrected Error Code
        message: `Room requires a non-empty 'name'. Received: ${room.name}`,
        path: 'name',
        value: room.name,
      })
    }

    // Validate floorLevel
    if (typeof room.floorLevel !== 'number' || !Number.isFinite(room.floorLevel)) {
      errors.push({
        code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
        message: `Room 'floorLevel' must be a finite number. Received: ${room.floorLevel}`,
        path: 'floorLevel',
        value: room.floorLevel,
      })
    }

    // Validate ceilingHeight
    if (typeof room.ceilingHeight !== 'number' || room.ceilingHeight <= 0 || !Number.isFinite(room.ceilingHeight)) {
      errors.push({
        code: ValidationErrorCode.INVALID_DIMENSION,
        message: `Room 'ceilingHeight' must be a positive finite number. Received: ${room.ceilingHeight}`,
        path: 'ceilingHeight',
        value: room.ceilingHeight,
      })
    }

    // Validate materials (basic check for string type)
    if (typeof room.floorMaterial !== 'string' || room.floorMaterial.trim() === '') {
      errors.push({ code: ValidationErrorCode.MISSING_PROPERTY, message: 'floorMaterial is required.', path: 'floorMaterial' })
    }
    if (typeof room.wallMaterial !== 'string' || room.wallMaterial.trim() === '') {
      errors.push({ code: ValidationErrorCode.MISSING_PROPERTY, message: 'wallMaterial is required.', path: 'wallMaterial' })
    }
    if (typeof room.ceilingMaterial !== 'string' || room.ceilingMaterial.trim() === '') {
      errors.push({ code: ValidationErrorCode.MISSING_PROPERTY, message: 'ceilingMaterial is required.', path: 'ceilingMaterial' })
    }

    // Validate ID arrays
    const idArrays: { prop: keyof RoomProperties, name: string }[] = [
      { prop: 'wallIds', name: 'wallIds' },
      { prop: 'openingIds', name: 'openingIds' },
      { prop: 'furnitureIds', name: 'furnitureIds' },
      { prop: 'fixtureIds', name: 'fixtureIds' },
    ]

    idArrays.forEach((item) => {
      const ids = room[item.prop] as string[] | undefined
      if (!Array.isArray(ids)) {
        errors.push({ code: ValidationErrorCode.INVALID_PROPERTY_VALUE, message: `${item.name} must be an array.`, path: item.name })
      }
      else {
        ids.forEach((id, index) => {
          if (typeof id !== 'string' || id.trim() === '') {
            errors.push({ code: ValidationErrorCode.INVALID_PROPERTY_VALUE, message: `Invalid ID in ${item.name} at index ${index}: must be a non-empty string.`, path: `${item.name}[${index}]`, value: id }) // Corrected Error Code
          }
        })
      }
    })

    // Validate shape property (Rectangle, Ellipse, or Polygon)
    const roomShape = room.shape as unknown as ShapeModel // Use ShapeModel alias
    if (roomShape == null || typeof roomShape !== 'object' || roomShape.type == null) {
      errors.push({ code: ValidationErrorCode.MISSING_PROPERTY, message: 'Room "shape" property is missing or invalid.', path: 'shape' })
    }
    else {
      // Further validation of the room.shape object could be done here,
      // potentially by dispatching to other validators (RectangleValidator, etc.)
      // For now, just checking its existence and basic structure.
      if (![ElementType.RECTANGLE, ElementType.SQUARE, ElementType.ELLIPSE, ElementType.CIRCLE, ElementType.POLYGON].includes(roomShape.type as ElementType)) {
        errors.push({ code: ValidationErrorCode.INVALID_SHAPE_TYPE, message: `Room 'shape.type' is invalid. Received: ${roomShape.type}`, path: 'shape.type' })
      }
    }

    // Add results from validateRoomSize and validateRoomEnclosure (now part of applySpecificRules or called from here)
    errors.push(...this.validateRoomSizeRules(room))
    // errors.push(...this.validateRoomEnclosureRules(room, room.wallIds || [])); // wallIds might be undefined if previous check failed

    return errors
  }

  /**
   * Applies room-specific business rules.
   *
   * @param shape - The shape to validate against business rules.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected applySpecificRules(shape: ValidatableShape): ValidationError[] {
    const errors: ValidationError[] = []
    const room = shape as ValidatableRoom

    // Example: Minimum area requirements (moved from old validateRoomSize)
    // const roomType = room.roomType; // Uncomment when needed
    // Calculate area from room.shape (Rectangle, Polygon, Ellipse)
    // This is complex and would require area calculation strategies.
    // For simplicity, let's assume 'area' is a direct or calculated property for validation if available.
    // If 'area' is not directly on RoomProperties, this rule needs to be re-thought or area calculated here.
    // const area = (room as any).area || 0; // Placeholder if area was a direct prop

    // const minAreaByType: Record<string, number> = { /* ... as before ... */ };
    // const minArea = minAreaByType[roomType as string] || 1;
    // if (area < minArea) { /* ... error ... */ }

    // Example: Enclosure (moved from old validateRoomEnclosure)
    if (room.isOutdoor !== true) { // Assuming isOutdoor is a property of RoomProperties or ValidatableRoom
      if (room.wallIds == null || room.wallIds.length < 3) {
        errors.push({
          code: ValidationErrorCode.VALIDATION_RULE_ERROR, // Corrected Error Code
          message: 'Indoor room must have at least 3 walls defining its boundary.',
          path: 'wallIds',
        })
      }
      // More complex enclosure logic (e.g., checking if walls form a closed loop) would go here.
    }
    return errors
  }

  /**
   * Helper method for validating room size rules.
   *
   * @param room - The room to validate.
   * @returns An array of validation errors.
   * @private
   */
  private validateRoomSizeRules(room: ValidatableRoom): ValidationError[] {
    const errors: ValidationError[] = []
    // This is a placeholder. Actual area calculation would be needed.
    // For now, we can only validate ceilingHeight if it's a direct property.
    if (typeof room.ceilingHeight === 'number' && !room.isOutdoor && room.ceilingHeight < 2400) { // Assuming 2400mm min
      errors.push({
        code: ValidationErrorCode.INVALID_DIMENSION,
        message: `Ceiling height (${room.ceilingHeight}mm) is less than minimum required (2400mm)`,
        path: 'ceilingHeight',
      })
    }
    return errors
  }
}
