import type { ShapeElement as ShapeModel } from '@/types/core/elementDefinitions'
import { ElementType as CoreElementType } from '@/types/core/elementDefinitions'

// --- START New Special Element Logic ---
export interface SpecialElementDetails {
  isSpecial: boolean
  attemptedIconPath: string | null
  displayName: string // For placeholder text or if icon fails
  // We might add more fields later, like a specific icon filename if derived separately
}

// This function will be expanded in Phase 2 based on BottomAssetDrawer.tsx analysis
export function determineSpecialElementInfo(shapeData: ShapeModel): SpecialElementDetails {
  // --- START Enhanced Logging ---
  // console.log(`[determineSpecialElementInfo] Processing Shape ID: ${shapeData.id}, Type: ${shapeData.type}`);
  // --- Check for predefined imagePath first ---
  if (shapeData.properties && typeof shapeData.properties.imagePath === 'string' && (shapeData.properties.imagePath.startsWith('/icon/') || shapeData.properties.imagePath.startsWith('assets/images/'))) {
    // console.log(`[determineSpecialElementInfo] Using predefined imagePath: ${shapeData.properties.imagePath} for ${shapeData.id}`);
    return {
      isSpecial: true,
      attemptedIconPath: shapeData.properties.imagePath,
      displayName: shapeData.metadata?.name || (shapeData.properties.name as string) || shapeData.type,
    }
  }

  // console.log(`[determineSpecialElementInfo] shapeData.type === CoreElementType.FURNITURE: ${shapeData.type === CoreElementType.FURNITURE} (CoreElementType.FURNITURE is ${CoreElementType.FURNITURE})`);
  const major = shapeData.majorCategory?.toLowerCase()
  const minor = shapeData.minorCategory?.toLowerCase()
  const nameFromProps = (shapeData.properties && typeof shapeData.properties.name === 'string') ? shapeData.properties.name : null
  const categoryFromProps = (shapeData.properties && typeof shapeData.properties.category === 'string') ? shapeData.properties.category.toLowerCase() : null
  const fixtureCategoryFromProps = (shapeData.properties && typeof shapeData.properties.fixtureCategory === 'string') ? shapeData.properties.fixtureCategory.toLowerCase() : null

  // console.log(`[determineSpecialElementInfo] For ${shapeData.id}: major='${major}', minor='${minor}', nameFromProps='${nameFromProps}', categoryFromProps='${categoryFromProps}', fixtureCategoryFromProps='${fixtureCategoryFromProps}'`);
  // console.log(`[determineSpecialElementInfo] shapeData.properties:`, JSON.stringify(shapeData.properties));
  // console.log(`[determineSpecialElementInfo] shapeData.metadata:`, JSON.stringify(shapeData.metadata));
  // --- END Enhanced Logging ---

  let displayName = nameFromProps || shapeData.metadata?.name || shapeData.type
  let iconFilename = ''
  let iconSubfolder = minor
  let isSpecial = false
  let attemptedIconPath: string | null = null

  if (major && minor) {
    const normalizedName = nameFromProps?.toLowerCase().replace(/\s+/g, '_')

    switch (major) {
      case 'floor':
        iconSubfolder = 'floor-coverings'
        switch (minor) {
          case 'coverings':
            if (shapeData.type === CoreElementType.FURNITURE && categoryFromProps === 'rug') {
              displayName = 'Area Rug'
              iconFilename = 'carpet.svg'
            }
            else if (nameFromProps && normalizedName === 'tile_pattern' && shapeData.type === CoreElementType.FLOOR_AREA) {
              displayName = nameFromProps
              iconFilename = 'tile_pattern.svg'
            }
            break
        }
        break
      case 'ceiling':
        iconSubfolder = minor
        if (minor === 'utilities') {
          if (shapeData.type === CoreElementType.FIXTURE && fixtureCategoryFromProps === 'vent') {
            displayName = 'Vent'
            iconFilename = 'vent.svg'
          }
          else if (shapeData.type === CoreElementType.FIXTURE && fixtureCategoryFromProps === 'switch' && nameFromProps === 'Smoke Detector') {
            displayName = 'Smoke Detector'
            iconFilename = ''
            isSpecial = true
          }
        }
        else if (minor === 'lighting') {
          if (shapeData.type === CoreElementType.LIGHT && fixtureCategoryFromProps === 'light') {
            if (nameFromProps === 'Ceiling Light') {
              displayName = 'Ceiling Light'
              iconFilename = ''
              isSpecial = true
            }
            else if (nameFromProps === 'Recessed Light') {
              displayName = 'Recessed Light'
              iconFilename = ''
              isSpecial = true
            }
          }
        }
        else if (minor === 'fans') {
          if (shapeData.type === CoreElementType.FIXTURE && fixtureCategoryFromProps === 'fan' && nameFromProps === 'Ceiling Fan') {
            displayName = 'Ceiling Fan'
            iconFilename = ''
            isSpecial = true
          }
        }
        break
      case 'furniture':
        iconSubfolder = minor
        if (shapeData.type === CoreElementType.FURNITURE) {
          switch (minor) {
            case 'storage':
              if (categoryFromProps === 'bookcase') { displayName = 'Bookcase'; iconFilename = 'bookshelf.svg' }
              else if (categoryFromProps === 'cabinet') { displayName = 'Cabinet'; iconFilename = 'cabinet.svg' }
              else if (categoryFromProps === 'dresser') { displayName = 'Dresser'; iconFilename = 'dresser.svg' }
              else if (categoryFromProps === 'shelf') { displayName = 'Shelf'; iconFilename = 'shelf.svg' }
              else if (categoryFromProps === 'wardrobe') { displayName = 'Wardrobe'; iconFilename = 'wardrobe.svg' }
              break
            case 'beds':
              if (categoryFromProps === 'bed') { displayName = 'Bed'; iconFilename = 'single-bed.svg' }
              else if (nameFromProps === 'Bunk Bed' || categoryFromProps === 'bunk-bed') {
                displayName = 'Bunk Bed'
                iconFilename = ''
                isSpecial = true
              }
              else if (categoryFromProps === 'crib') { displayName = 'Crib'; iconFilename = 'crib.svg' }
              break
            case 'tables':
              if (nameFromProps === 'Dining Table' || categoryFromProps === 'dining-table') {
                displayName = 'Dining Table'
                iconFilename = ''
                isSpecial = true
              }
              else if (nameFromProps === 'Coffee Table' || categoryFromProps === 'coffee-table') {
                displayName = 'Coffee Table'
                iconFilename = ''
                isSpecial = true
              }
              else if (nameFromProps === 'Side Table' || categoryFromProps === 'side-table') {
                displayName = 'Side Table'
                iconFilename = ''
                isSpecial = true
              }
              else if (categoryFromProps === 'desk') { displayName = 'Desk'; iconFilename = 'desk.svg' }
              break
            case 'seating':
              if (categoryFromProps === 'sofa') { displayName = 'Sofa'; iconFilename = 'sofa.svg' }
              else if (categoryFromProps === 'chair') { displayName = 'Armchair'; iconFilename = 'chair.svg' }
              else if (categoryFromProps === 'stool') { displayName = 'Stool'; iconFilename = 'stool.svg' }
              else if (categoryFromProps === 'bench') { displayName = 'Bench'; iconFilename = 'bench.svg' }
              break
            case 'decor':
              if (categoryFromProps === 'lamp') { displayName = 'Lamp'; iconFilename = 'lamp.svg' }
              else if (categoryFromProps === 'mirror') { displayName = 'Mirror'; iconFilename = 'mirror.svg' }
              else if (categoryFromProps === 'artwork') { displayName = 'Artwork'; iconFilename = 'artwork.svg' }
              else if (categoryFromProps === 'plant') { displayName = 'Plant'; iconFilename = 'plant.svg' }
              break
          }
        }
        else if (shapeData.type === CoreElementType.FIXTURE && minor === 'appliances') {
          if (fixtureCategoryFromProps === 'refrigerator') { displayName = 'Refrigerator'; iconFilename = 'refrigerator.svg' }
          else if (fixtureCategoryFromProps === 'oven') { displayName = 'Oven'; iconFilename = 'oven.svg' }
          else if (fixtureCategoryFromProps === 'dishwasher') { displayName = 'Dishwasher'; iconFilename = 'dishwasher.svg' }
        }
        break
      case 'wall':
        iconSubfolder = minor
        switch (minor) {
          case 'base': case 'wall-base':
            if (shapeData.type === CoreElementType.FLOOR_AREA && nameFromProps === 'Room Definition') { displayName = 'Room Definition'; iconFilename = 'room_definition.svg' }
            else if (shapeData.type === CoreElementType.FLOOR_AREA && nameFromProps === 'Wall Structure') { displayName = 'Wall Structure'; iconFilename = 'wall_structure.svg' }
            else if (shapeData.type === CoreElementType.OPENING && nameFromProps === 'Opening (Door/Window)') { displayName = 'Opening'; iconFilename = 'opening.svg' }
            break
          case 'paint': case 'wall-paint':
            if (shapeData.type === CoreElementType.WALL_PAINT && nameFromProps === 'Paint Can') { displayName = 'Paint Can'; iconFilename = 'paint_can.svg' }
            break
          case 'wallpaper': case 'wall-wallpaper':
            if (shapeData.type === CoreElementType.WALL_PAPER && nameFromProps === 'Wallpaper Roll') { displayName = 'Wallpaper Roll'; iconFilename = 'wallpaper_roll.svg' }
            break
        }
        break
    }

    if (iconFilename && iconSubfolder && major) {
      // If an icon filename was determined, it's definitely special.
      isSpecial = true
      attemptedIconPath = `/icon/${major}/${iconSubfolder}/${iconFilename}`.toLowerCase()
      // console.log(`[determineSpecialElementInfo] For ${shapeData.id}: MATCH! Path: ${attemptedIconPath}, Display: ${displayName}`);
    }
    else {
      // No iconFilename was set.
      // isSpecial might be true if a rule (like Bunk Bed) set it and set iconFilename to ''.
      // Or, isSpecial might still be false if no rule matched.
      if (isSpecial) {
        // This means a rule explicitly set iconFilename = '' and isSpecial = true.
        // This is our desired path for Bunk Bed, etc.
        // attemptedIconPath remains null.
        // console.log(`[determineSpecialElementInfo] For ${shapeData.id}: SPECIAL (no icon file), Display: ${displayName}`);
      }
      else {
        // console.log(`[determineSpecialElementInfo] For ${shapeData.id}: NO MATCH, Display: ${displayName}`);
      }
    }
  }
  else {
    // console.log(`[determineSpecialElementInfo] For ${shapeData.id}: Major or Minor category missing. Not processed for special icon path.`);
  }

  return {
    isSpecial,
    attemptedIconPath,
    displayName,
  }
}
// --- END New Special Element Logic ---
