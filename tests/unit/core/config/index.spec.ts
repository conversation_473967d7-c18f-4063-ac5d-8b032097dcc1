import { describe, expect, it } from 'vitest'
import * as coreConfigIndex from '@/core/config'
import { createConfig, defaultCoreConfig } from '@/core/config/core'

describe('core Configuration Index', () => {
  it('should re-export defaultCoreConfig from core.ts', () => {
    expect(coreConfigIndex.defaultCoreConfig).toBe(defaultCoreConfig)
  })

  it('should re-export createConfig function from core.ts', () => {
    expect(coreConfigIndex.createConfig).toBe(createConfig)
  })

  it('should have the CoreConfig type available for export', () => {
    // This is a type-level test, so we can't directly test it at runtime
    // But we can verify that the module structure is as expected
    expect(typeof coreConfigIndex).toBe('object')
    expect(Object.keys(coreConfigIndex).length).toBeGreaterThanOrEqual(2)
    expect(Object.keys(coreConfigIndex)).toContain('defaultCoreConfig')
    expect(Object.keys(coreConfigIndex)).toContain('createConfig')
  })
})
