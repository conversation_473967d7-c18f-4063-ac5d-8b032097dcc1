/**
 * Alert Component
 *
 * A flexible alert component for displaying important messages, notifications,
 * and status updates. Built with class-variance-authority for consistent styling
 * and accessibility features.
 *
 * Features:
 * - Multiple visual variants (default, destructive)
 * - Icon support with proper positioning
 * - Accessible role="alert" for screen readers
 * - Composable structure with title and description
 * - Consistent spacing and typography
 * - Responsive design
 *
 * Components:
 * - Alert: Main container with role="alert"
 * - AlertTitle: Heading for the alert message
 * - AlertDescription: Body content for detailed information
 *
 * @example
 * ```tsx
 * <Alert variant="destructive">
 *   <AlertCircle className="h-4 w-4" />
 *   <AlertTitle>Error</AlertTitle>
 *   <AlertDescription>
 *     Your session has expired. Please log in again.
 *   </AlertDescription>
 * </Alert>
 * ```
 */

import type { VariantProps } from 'class-variance-authority'
import { cva } from 'class-variance-authority'
import * as React from 'react'

import { cn } from '@/lib/utils/index'

/**
 * Alert style variants using class-variance-authority.
 * Defines different visual styles for alert messages.
 */
const alertVariants = cva(
  'relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7',
  {
    variants: {
      variant: {
        default: 'bg-background text-foreground',
        destructive:
          'border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
)

/**
 * Alert component that displays important messages with proper accessibility.
 *
 * This is the main container component that provides the alert structure
 * and styling. It automatically includes role="alert" for screen readers.
 *
 * @param props - The component props
 * @param props.className - Additional CSS classes to apply
 * @param props.variant - Visual style variant (default, destructive)
 * @param props.ref - React ref for the div element
 * @returns The rendered alert container
 */
function Alert({ ref, className, variant, ...props }: React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants> & { ref?: React.RefObject<HTMLDivElement | null> }) {
  return (
    <div
      ref={ref}
      role="alert"
      className={cn(alertVariants({ variant }), className)}
      {...props}
    />
  )
}
Alert.displayName = 'Alert'

/**
 * AlertTitle component for displaying the alert heading.
 *
 * This component renders a styled heading (h5) for the alert message.
 * It provides consistent typography and spacing for alert titles.
 *
 * @param props - The component props
 * @param props.className - Additional CSS classes to apply
 * @param props.ref - React ref for the heading element
 * @returns The rendered alert title
 */
function AlertTitle({ ref, className, ...props }: React.HTMLAttributes<HTMLHeadingElement> & { ref?: React.RefObject<HTMLParagraphElement | null> }) {
  return (
    <h5
      ref={ref}
      className={cn('mb-1 font-medium leading-none tracking-tight', className)}
      {...props}
    />
  )
}
AlertTitle.displayName = 'AlertTitle'

/**
 * AlertDescription component for displaying the alert body content.
 *
 * This component renders the detailed message content of the alert.
 * It supports rich content including paragraphs and other elements.
 *
 * @param props - The component props
 * @param props.className - Additional CSS classes to apply
 * @param props.ref - React ref for the div element
 * @returns The rendered alert description
 */
function AlertDescription({ ref, className, ...props }: React.HTMLAttributes<HTMLParagraphElement> & { ref?: React.RefObject<HTMLParagraphElement | null> }) {
  return (
    <div
      ref={ref}
      className={cn('text-sm [&_p]:leading-relaxed', className)}
      {...props}
    />
  )
}
AlertDescription.displayName = 'AlertDescription'

export { Alert, AlertDescription, AlertTitle }
