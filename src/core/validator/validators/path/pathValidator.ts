/**
 * Contains validation logic specific to Path shapes (point-based paths).
 */

import type { Path } from '@/types/core/elementDefinitions'
import type { ValidatableShape, ValidationError } from '@/types/core/validator/validator-interface'
import { ElementType, isPathType } from '@/types/core/elementDefinitions' // Import Path namespace
import { ValidationErrorCode } from '@/types/core/validator/error-codes'
import { isValidPoint } from '../common/validationUtils'
import { BaseShapeValidator } from '../common/validatorBase'

/**
 * Concrete validator implementation for generic Path shapes defined by points.
 *
 * @remarks
 * This validator assumes a path structure similar to a Polyline for ElementType.PATH.
 *
 * @extends BaseShapeValidator
 */
export class PathValidator extends BaseShapeValidator {
  /**
   * Validates the specific properties of a Path shape.
   *
   * @remarks
   * Checks for a valid points array.
   *
   * @param shape - The shape to validate.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected validateSpecific(shape: ValidatableShape): ValidationError[] {
    const errors: ValidationError[] = []

    if (!isPathType(shape.type as ElementType)) {
      errors.push({
        code: ValidationErrorCode.INVALID_SHAPE_TYPE,
        message: `Internal Error: PathValidator received shape type ${shape.type}`,
        path: 'type',
        value: shape.type,
      })
      return errors
    }

    // For ElementType.PATH, we assume it should have a 'points' array like Polyline.
    // If ElementType.PATH is meant for SVG 'd' attribute strings, this validator needs a different logic.
    const pathElement = shape as unknown as Path.Polyline // Use Path.Polyline as it has 'points' with double assertion

    // @ts-expect-error - Accessing points property on path element
    const points = pathElement.points as Point[] | undefined
    if (!Array.isArray(points)) {
      errors.push({
        code: ValidationErrorCode.MISSING_PROPERTY,
        message: 'Path must have a points array.',
        path: 'points',
        value: points,
      })
      return errors
    }

    if (points.length < 2) {
      errors.push({
        code: ValidationErrorCode.INSUFFICIENT_POINTS,
        message: `Path must have at least 2 points. Found: ${points.length}`,
        path: 'points.length',
        value: points.length,
      })
      return errors
    }

    for (let i = 0; i < points.length; i++) {
      const point = points[i] as { x: number, y: number }
      if (!isValidPoint(point)) {
        errors.push({
          code: ValidationErrorCode.INVALID_POINT,
          message: `Invalid point at index ${i}: ${JSON.stringify(point)}`,
          path: `points[${i}]`,
          value: point,
        })
      }
    }

    return errors
  }

  /**
   * Applies specific business rules for paths.
   *
   * @param shape - The shape to validate against business rules.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected applySpecificRules(shape: ValidatableShape): ValidationError[] {
    const errors: ValidationError[] = []
    const pathElement = shape as unknown as Path.Polyline // Use Path.Polyline with double assertion

    // @ts-expect-error - Accessing points property on path element
    const points = pathElement.points as Point[] | undefined
    if (!Array.isArray(points) || points.length < 2) {
      return errors
    }

    for (let i = 1; i < points.length; i++) {
      const current = points[i] as { x: number, y: number }
      const previous = points[i - 1] as { x: number, y: number }

      if (isValidPoint(current) && isValidPoint(previous)
        && current.x === previous.x && current.y === previous.y) {
        errors.push({
          code: ValidationErrorCode.DUPLICATE_POINTS,
          message: `Path contains consecutive duplicate points at indices ${i - 1} and ${i}.`,
          path: `points[${i}]`,
          value: current,
        })
      }
    }

    return errors
  }
}
