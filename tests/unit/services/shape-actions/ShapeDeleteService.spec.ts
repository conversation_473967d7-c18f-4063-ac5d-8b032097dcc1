import type { ShapeRepository } from '@/core/state/ShapeRepository'
import type { ShapeModel } from '@/types/core/models'
import type { EventBus, ShapeDeleteEvent } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ErrorType } from '@/core/errors/CoreError'
import * as errorHelpers from '@/services/event-bus/helpers/error-helpers'
import { ShapeDeleteService } from '@/services/shape-actions/ShapeDeleteService'
import { ElementType } from '@/types/core/shape-type'
import { AppEventType } from '@/types/services/events'

describe('shapeDeleteService', () => {
  // Test dependencies
  let repository: ShapeRepository
  let eventBus: EventBus
  let logger: LoggerService
  let service: ShapeDeleteService

  // Mock shape models
  const mockShapes: Record<string, ShapeModel> = {
    shape1: {
      id: 'shape1',
      type: ElementType.RECTANGLE,
      position: { x: 100, y: 100 },
      properties: { width: 200, height: 100 },
    },
    shape2: {
      id: 'shape2',
      type: ElementType.CIRCLE,
      position: { x: 300, y: 300 },
      properties: { radius: 50 },
    },
  }

  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks()

    // Mock publishError function
    vi.spyOn(errorHelpers, 'publishError').mockImplementation((eventBus, errorType, message, details) => {
      eventBus.publish({
        type: AppEventType.ERROR_OCCURRED,
        payload: {
          code: errorType,
          message,
          details,
          errorType, // For backward compatibility with tests
        },
      })
    })

    // Create mock dependencies
    repository = {
      getById: vi.fn((id: string) => mockShapes[id]),
      remove: vi.fn((id: string) => true),
    } as unknown as ShapeRepository

    eventBus = {
      publish: vi.fn(),
    } as unknown as EventBus

    logger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    } as unknown as LoggerService

    // Create service instance
    service = new ShapeDeleteService(repository, eventBus, logger)
  })

  describe('handleRequest', () => {
    it('should delete shapes and publish success event when valid payload is provided', async () => {
      // Arrange
      const deleteEvent: ShapeDeleteEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: {
          shapeIds: ['shape1', 'shape2'],
        },
      }

      // Act
      await service.handleRequest(deleteEvent)

      // Assert
      expect(repository.getById).toHaveBeenCalledWith('shape1')
      expect(repository.getById).toHaveBeenCalledWith('shape2')
      expect(repository.remove).toHaveBeenCalledWith('shape1')
      expect(repository.remove).toHaveBeenCalledWith('shape2')
      expect(eventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        type: AppEventType.SHAPE_DELETE_COMPLETE,
        payload: expect.objectContaining({
          shapeIds: ['shape1', 'shape2'],
        }),
      }))
    })

    it('should publish error event when payload is missing', async () => {
      // Arrange
      const invalidEvent: ShapeDeleteEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: undefined,
      }

      // Act
      await service.handleRequest(invalidEvent)

      // Assert
      expect(eventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          errorType: ErrorType.INVALID_PAYLOAD,
        }),
      }))
      expect(repository.remove).not.toHaveBeenCalled()
    })

    it('should publish error event when shapeIds array is empty', async () => {
      // Arrange
      const emptyEvent: ShapeDeleteEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: {
          shapeIds: [],
        },
      }

      // Act
      await service.handleRequest(emptyEvent)

      // Assert
      expect(eventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          errorType: ErrorType.INVALID_PAYLOAD,
        }),
      }))
      expect(repository.remove).not.toHaveBeenCalled()
    })

    it('should handle shapes that do not exist', async () => {
      // Arrange
      const deleteEvent: ShapeDeleteEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: {
          shapeIds: ['shape1', 'nonexistent'],
        },
      }

      // Mock repository.getById to return undefined for 'nonexistent'
      vi.mocked(repository.getById).mockImplementation((id: string) =>
        id === 'nonexistent' ? undefined : mockShapes[id],
      )

      // Act
      await service.handleRequest(deleteEvent)

      // Assert
      expect(repository.getById).toHaveBeenCalledWith('shape1')
      expect(repository.getById).toHaveBeenCalledWith('nonexistent')
      expect(repository.remove).toHaveBeenCalledWith('shape1')
      expect(repository.remove).not.toHaveBeenCalledWith('nonexistent')
      expect(eventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          errorType: ErrorType.COORDINATOR_OPERATION_FAILED,
        }),
      }))
      expect(eventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        type: AppEventType.SHAPE_DELETE_COMPLETE,
        payload: expect.objectContaining({
          shapeIds: ['shape1'],
        }),
      }))
    })

    it('should handle repository removal failures', async () => {
      // Arrange
      const deleteEvent: ShapeDeleteEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: {
          shapeIds: ['shape1', 'shape2'],
        },
      }

      // Mock repository.remove to fail for 'shape2'
      vi.mocked(repository.remove).mockImplementation((id: string) =>
        id !== 'shape2',
      )

      // Act
      await service.handleRequest(deleteEvent)

      // Assert
      expect(repository.remove).toHaveBeenCalledWith('shape1')
      expect(repository.remove).toHaveBeenCalledWith('shape2')
      expect(eventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          errorType: ErrorType.COORDINATOR_OPERATION_FAILED,
        }),
      }))
      expect(eventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        type: AppEventType.SHAPE_DELETE_COMPLETE,
        payload: expect.objectContaining({
          shapeIds: ['shape1'],
        }),
      }))
    })

    it('should handle unexpected errors during processing', async () => {
      // Arrange
      const deleteEvent: ShapeDeleteEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: {
          shapeIds: ['shape1', 'shape2'],
        },
      }

      // Mock repository.getById to throw an error
      const unexpectedError = new Error('Unexpected error')
      vi.mocked(repository.getById).mockImplementation(() => {
        throw unexpectedError
      })

      // Act
      await service.handleRequest(deleteEvent)

      // Assert
      expect(eventBus.publish).toHaveBeenCalledWith(expect.objectContaining({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          errorType: ErrorType.UNKNOWN,
          message: 'Unexpected error',
        }),
      }))
    })
  })
})
