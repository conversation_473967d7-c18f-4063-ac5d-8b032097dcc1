/**
 * Perimeter/Length Calculation Strategy for Polyline Elements
 *
 * @remarks
 * This strategy implements the {@link PerimeterCalculatorStrategy} for calculating
 * the total length (perimeter) of Polyline elements ({@link CoreElementType.POLYLINE}).
 *
 * It handles both open and closed polylines:
 * - For open polylines, it sums the lengths of all segments.
 * - For closed polylines, it sums the lengths of all segments, including the segment
 *   connecting the last point back to the first point.
 *
 * The calculations are delegated to utility functions `calculateOpenPolylineLength`
 * and `calculateClosedPolylinePerimeter` (aliased from `calculatePerimeter` in
 * `../../../../lib/utils/geometry/polygonUtils`).
 *
 * The strategy validates that the element is of type `POLYLINE` and that its
 * `points` array is valid and contains valid point data. It also checks for
 * a minimum number of points based on whether the polyline is open or closed.
 * The points are assumed to be relative to the polyline's `position` if it's a
 * `ShapeElement`, and the utility functions are expected to handle these local coordinates.
 *
 * @module core/compute/strategies/perimeter/PolylinePerimeterStrategy
 * @see {@link Path.Polyline} for the polyline element type definition.
 * @see {@link PerimeterCalculatorStrategy}
 * @see {@link calculateOpenPolylineLength}
 * @see {@link calculateClosedPolylinePerimeter}
 */
import type { PerimeterCalculatorStrategy } from '../../../../types/core/compute'
import type {
  Element,
  Path,
  ShapeElement,
} from '../../../../types/core/elementDefinitions'
import {
  calculatePerimeter as calculateClosedPolylinePerimeter,
  calculateOpenPolylineLength,
} from '../../../../lib/utils/geometry/polygonUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

/**
 * Implements the {@link PerimeterCalculatorStrategy} for {@link CoreElementType.POLYLINE} elements.
 * It sums the lengths of the segments forming the polyline, handling both open and closed polylines appropriately.
 */
export class PolylinePerimeterStrategy implements PerimeterCalculatorStrategy {
  /**
   * Calculates the perimeter (total length) of a Polyline element.
   *
   * @param element - The polyline element, expected to be of type {@link ShapeElement}
   *                  and specifically {@link CoreElementType.POLYLINE}, conforming to {@link Path.Polyline}.
   * @returns The calculated perimeter.
   * @throws {@link CoreError} if the element type is not `POLYLINE`.
   * @throws {@link CoreError} if the `points` array is invalid, or contains invalid point data,
   *         or if there are insufficient points for a closed polyline.
   */
  public calculatePerimeter(element: Element): number {
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement.type !== CoreElementType.POLYLINE) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `PolylinePerimeterStrategy can only calculate perimeter for POLYLINE elements, got ${shapeElement.type} (ID: ${shapeElement.id}).`,
        undefined,
        { component: 'PolylinePerimeterStrategy', operation: 'calculatePerimeter', target: shapeElement.id },
      )
    }

    const polylineElement = shapeElement as unknown as Path.Polyline // Added unknown for type safety
    const points = polylineElement.properties?.points // Points are in properties
    const closed = polylineElement.properties?.closed === true

    // Validate points based on whether the polyline is considered closed or open
    // The Path.Polyline interface defines points as Point[], not Point[] | undefined.
    // So, !points check might be redundant if type contract is strictly followed.
    // However, Array.isArray is a good runtime check.
    if (!Array.isArray(points)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Polyline (ID: ${element.id}) has invalid points property (not an array).`,
        undefined,
        { component: 'PolylinePerimeterStrategy', operation: 'calculatePerimeter', target: element.id },
      )
    }

    if (closed && points.length < 3) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Closed Polyline (ID: ${element.id}) must have at least 3 points. Found: ${points.length}`,
        undefined,
        { component: 'PolylinePerimeterStrategy', operation: 'calculatePerimeter', target: element.id, metadata: { pointsLength: points.length, closed } },
      )
    }
    if (!closed && points.length < 2) {
      // calculateOpenPolylineLength from polygonUtils returns 0 for < 2 points, so this is acceptable.
      // No error needed here if that's the desired behavior for 0 or 1 point open polylines.
      if (points.length === 0)
        return 0 // Explicitly handle 0 points
    }

    // Validate all points before calculation
    for (let i = 0; i < points.length; i++) {
      const point = points[i]
      if (point === null || point === undefined || typeof point.x !== 'number' || typeof point.y !== 'number'
        || !Number.isFinite(point.x) || !Number.isFinite(point.y)) {
        throw new CoreError(
          ErrorType.InvalidParameter,
          `Polyline (ID: ${element.id}) contains invalid point data at index ${i}.`,
          undefined,
          { component: 'PolylinePerimeterStrategy', operation: 'calculatePerimeter', target: element.id, metadata: { pointIndex: i, pointData: point } },
        )
      }
    }

    // Note: The points are relative to the polylineElement.position.
    // The imported calculateClosedPolylinePerimeter (aliased calculatePerimeter from polygonUtils)
    // and calculateOpenPolylineLength expect points relative to their own local origin (0,0).
    // This is consistent if Path.Polyline.points are defined relative to Path.Polyline.position.
    if (closed) {
      return calculateClosedPolylinePerimeter(points)
    }
    else {
      return calculateOpenPolylineLength(points)
    }

    // The logic for closed/open is already handled by the first if/else block (lines 91-95)
  }

  /**
   * Returns the element type that this strategy is responsible for.
   * @returns {@link CoreElementType.POLYLINE} - The polyline element type that this strategy handles
   */
  public getElementType(): CoreElementType {
    return CoreElementType.POLYLINE
  }
}
