/**
 * Defines the Strategy Interface for Area Calculation
 *
 * @remarks
 * This module introduces the {@link AreaCalculatorStrategy} interface, a core component
 * of the strategy design pattern applied to geometric computations. This pattern enables
 * the encapsulation of different area calculation algorithms, each tailored to specific
 * types of {@link Element | elements} (e.g., rectangles, circles, polygons).
 *
 * Concrete implementations of this interface will provide the specific logic for
 * computing the area of one or more element types, promoting flexibility and
 * extensibility in how area calculations are performed.
 *
 * @module types/core/compute/areaComputeTypes
 * @see {@link Element} from '@/types/core/elementDefinitions'
 * @see {@link ../computeInterfaces} for other computation strategy interfaces.
 */

import type { Element } from '@/types/core/elementDefinitions'

/**
 * Defines the contract for an area calculation strategy.
 *
 * @remarks
 * Any class that implements this interface is responsible for providing a specific
 * algorithm to calculate the area of an {@link Element}. Concrete strategies
 * will typically be specialized for one or more {@link ElementType | element types}.
 *
 * @see {@link Element} from '@/types/core/elementDefinitions'
 * @see {@link ElementType} from '@/types/core/elementDefinitions'
 */
export interface AreaCalculatorStrategy {
  /**
   * Calculates the area of the provided element.
   *
   * @param element - The {@link Element} for which to calculate the area.
   *                  The element is expected to have properties (e.g., width, height, radius, points)
   *                  necessary for the specific strategy's calculation.
   * @returns The calculated area as a `number`. The units of the area (e.g., square meters, square pixels)
   *          are implicitly defined by the units used in the element's dimensions.
   */
  calculateArea: (element: Element) => number

  /**
   * Gets the specific element type (or types) that this strategy is designed to handle.
   *
   * @returns A string identifier (or an array of string identifiers) corresponding to
   *          one or more {@link ElementType | ElementTypes} that this strategy supports.
   * @remarks Consider returning `ElementType` or `ElementType[]` for enhanced type safety in future revisions.
   */
  getElementType: () => string | string[] // Allow for multiple types or a single type
}
