import type { AsyncServiceResult, ServiceResult } from '@/types/services/core/serviceResult'
import { describe, expect, it } from 'vitest'

describe('service Result Types', () => {
  describe('serviceResult', () => {
    it('should allow creating a successful result', () => {
      const result: ServiceResult<string> = {
        success: true,
        data: 'Test data',
        timestamp: Date.now(),
      }

      expect(result.success).toBe(true)
      expect(result.data).toBe('Test data')
      expect(result.timestamp).toBeGreaterThan(0)
      expect(result.error).toBeUndefined()
    })

    it('should allow creating an error result', () => {
      const result: ServiceResult<string> = {
        success: false,
        error: 'Test error',
        timestamp: Date.now(),
      }

      expect(result.success).toBe(false)
      expect(result.error).toBe('Test error')
      expect(result.timestamp).toBeGreaterThan(0)
      expect(result.data).toBeUndefined()
    })

    it('should work with different data types', () => {
      // Number type
      const numberResult: ServiceResult<number> = {
        success: true,
        data: 42,
        timestamp: Date.now(),
      }
      expect(numberResult.data).toBe(42)

      // Object type
      const objectResult: ServiceResult<{ id: string, value: number }> = {
        success: true,
        data: { id: 'test', value: 123 },
        timestamp: Date.now(),
      }
      expect(objectResult.data?.id).toBe('test')
      expect(objectResult.data?.value).toBe(123)

      // Array type
      const arrayResult: ServiceResult<string[]> = {
        success: true,
        data: ['a', 'b', 'c'],
        timestamp: Date.now(),
      }
      expect(arrayResult.data).toHaveLength(3)
      expect(arrayResult.data?.[0]).toBe('a')
    })
  })

  describe('asyncServiceResult', () => {
    it('should allow creating a Promise of ServiceResult', async () => {
      const asyncResult: AsyncServiceResult<string> = Promise.resolve({
        success: true,
        data: 'Async test data',
        timestamp: Date.now(),
      })

      const result = await asyncResult
      expect(result.success).toBe(true)
      expect(result.data).toBe('Async test data')
      expect(result.timestamp).toBeGreaterThan(0)
    })

    it('should handle rejected promises', async () => {
      const asyncResult: AsyncServiceResult<string> = Promise.reject(new Error('Async error'))

      await expect(asyncResult).rejects.toThrow('Async error')
    })

    it('should work with different data types', async () => {
      // Number type
      const numberResult: AsyncServiceResult<number> = Promise.resolve({
        success: true,
        data: 42,
        timestamp: Date.now(),
      })
      const resolvedNumberResult = await numberResult
      expect(resolvedNumberResult.data).toBe(42)

      // Object type
      const objectResult: AsyncServiceResult<{ id: string, value: number }> = Promise.resolve({
        success: true,
        data: { id: 'test', value: 123 },
        timestamp: Date.now(),
      })
      const resolvedObjectResult = await objectResult
      expect(resolvedObjectResult.data?.id).toBe('test')
      expect(resolvedObjectResult.data?.value).toBe(123)
    })
  })
})
