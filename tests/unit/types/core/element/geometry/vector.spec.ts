import type Point from '@/types/core/element/geometry/point'
import type { Vector } from '@/types/core/element/geometry/vector'
import { describe, expect, it } from 'vitest'

describe('vector Interface', () => {
  it('should define a vector with x and y components', () => {
    // Create a valid Vector object
    const vector: Vector = {
      x: 10,
      y: 20,
    }

    // Verify the object is valid
    expect(vector.x).toBe(10)
    expect(vector.y).toBe(20)
  })

  it('should allow creating vectors with different component values', () => {
    // Create vectors with different values
    const zeroVector: Vector = { x: 0, y: 0 }
    const positiveVector: Vector = { x: 100, y: 200 }
    const negativeVector: Vector = { x: -50, y: -75 }
    const mixedVector: Vector = { x: 25, y: -30 }

    // Verify all vectors are valid
    expect(zeroVector.x).toBe(0)
    expect(zeroVector.y).toBe(0)

    expect(positiveVector.x).toBe(100)
    expect(positiveVector.y).toBe(200)

    expect(negativeVector.x).toBe(-50)
    expect(negativeVector.y).toBe(-75)

    expect(mixedVector.x).toBe(25)
    expect(mixedVector.y).toBe(-30)
  })

  it('should allow creating vectors with decimal components', () => {
    // Create vectors with decimal values
    const decimalVector: Vector = { x: 10.5, y: 20.75 }
    const smallDecimalVector: Vector = { x: 0.01, y: 0.001 }

    // Verify the vectors are valid
    expect(decimalVector.x).toBe(10.5)
    expect(decimalVector.y).toBe(20.75)

    expect(smallDecimalVector.x).toBe(0.01)
    expect(smallDecimalVector.y).toBe(0.001)
  })

  it('should be usable in vector operations', () => {
    // Define vector operation functions
    function addVectors(v1: Vector, v2: Vector): Vector {
      return {
        x: v1.x + v2.x,
        y: v1.y + v2.y,
      }
    }

    function subtractVectors(v1: Vector, v2: Vector): Vector {
      return {
        x: v1.x - v2.x,
        y: v1.y - v2.y,
      }
    }

    function scaleVector(v: Vector, scalar: number): Vector {
      return {
        x: v.x * scalar,
        y: v.y * scalar,
      }
    }

    function dotProduct(v1: Vector, v2: Vector): number {
      return v1.x * v2.x + v1.y * v2.y
    }

    function magnitude(v: Vector): number {
      return Math.sqrt(v.x * v.x + v.y * v.y)
    }

    // Test the functions
    const v1: Vector = { x: 3, y: 4 }
    const v2: Vector = { x: 1, y: 2 }

    const sum = addVectors(v1, v2)
    expect(sum.x).toBe(4)
    expect(sum.y).toBe(6)

    const difference = subtractVectors(v1, v2)
    expect(difference.x).toBe(2)
    expect(difference.y).toBe(2)

    const scaled = scaleVector(v1, 2)
    expect(scaled.x).toBe(6)
    expect(scaled.y).toBe(8)

    const dot = dotProduct(v1, v2)
    expect(dot).toBe(11) // 3*1 + 4*2 = 3 + 8 = 11

    const mag = magnitude(v1)
    expect(mag).toBe(5) // sqrt(3^2 + 4^2) = sqrt(9 + 16) = sqrt(25) = 5
  })

  it('should be convertible to/from Point', () => {
    // Convert Point to Vector
    const point: Point = { x: 5, y: 10 }
    const vectorFromPoint: Vector = { x: point.x, y: point.y }

    expect(vectorFromPoint.x).toBe(5)
    expect(vectorFromPoint.y).toBe(10)

    // Convert Vector to Point
    const vector: Vector = { x: 15, y: 25 }
    const pointFromVector: Point = { x: vector.x, y: vector.y }

    expect(pointFromVector.x).toBe(15)
    expect(pointFromVector.y).toBe(25)
  })

  it('should be usable in arrays and collections', () => {
    // Create an array of vectors
    const vectors: Vector[] = [
      { x: 1, y: 0 },
      { x: 0, y: 1 },
      { x: -1, y: 0 },
      { x: 0, y: -1 },
    ]

    // Verify the array is valid
    expect(vectors.length).toBe(4)
    expect(vectors[0].x).toBe(1)
    expect(vectors[0].y).toBe(0)
    expect(vectors[1].x).toBe(0)
    expect(vectors[1].y).toBe(1)
    expect(vectors[2].x).toBe(-1)
    expect(vectors[2].y).toBe(0)
    expect(vectors[3].x).toBe(0)
    expect(vectors[3].y).toBe(-1)
  })

  it('should be compatible with JSON serialization/deserialization', () => {
    // Create a vector
    const vector: Vector = { x: 15, y: 25 }

    // Serialize to JSON and deserialize
    const serialized = JSON.stringify(vector)
    const deserialized = JSON.parse(serialized) as Vector

    // Verify the deserialized object is valid
    expect(deserialized.x).toBe(15)
    expect(deserialized.y).toBe(25)
  })
})
