/**
 * Compute status utility constants and types
 */

import { ComputeStatus } from '@/types/core/computeStatus'

/**
 * Utility function to determine compute status from element properties
 */
export function getComputeStatus(
  computedValue: number | undefined,
  computedStatus: ComputeStatus | undefined,
  computedError: string | undefined,
): ComputeStatus {
  // If explicit status is provided, use it
  if (computedStatus !== undefined && computedStatus !== null && computedStatus !== ComputeStatus.NONE) {
    return computedStatus
  }

  // If there's an error, return ERROR status
  if (computedError !== undefined && computedError !== null && computedError !== '') {
    return ComputeStatus.ERROR
  }

  // If there's a computed value, return SUCCESS
  if (computedValue !== undefined && Number.isFinite(computedValue)) {
    return ComputeStatus.SUCCESS
  }

  // Otherwise, no computation available
  return ComputeStatus.NONE
}
