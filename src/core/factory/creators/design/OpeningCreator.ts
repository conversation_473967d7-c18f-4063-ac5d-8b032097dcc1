/**
 * Creator Classes for Opening Design Elements (Doors and Windows)
 *
 * @remarks
 * This module provides an abstract base class {@link OpeningCreator} and concrete
 * subclasses {@link DoorCreator} and {@link WindowCreator} for instantiating
 * door and window elements within the design.
 *
 * These creators extend {@link DesignCreator} and handle the specific properties
 * and default values associated with openings, such as `openingType`,
 * `swingDirection` (for doors), `operationType` (for windows), dimensions,
 * and materials.
 *
 * They also include convenience methods for creating specific subtypes of doors
 * (e.g., exterior, sliding) and windows (e.g., fixed, sliding).
 *
 * Note: Placeholders for `DesignMaterial` and `DesignElementCategory` are used locally.
 *
 * @module core/factory/creators/design/OpeningCreator
 * @see {@link DesignCreator}
 * @see {@link DoorCreator}
 * @see {@link WindowCreator}
 * @see {@link OpeningType}
 * @see {@link DoorSwingDirection}
 * @see {@link WindowOperationType}
 */

import type { ShapeCreationParamsUnion } from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point' // Changed to PointData
import type {
  ShapeElement,
} from '@/types/core/elementDefinitions'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
import { ensureCompleteMetadata } from '@/lib/utils/element/elementUtils'
import {
  DoorSwingDirection,
  OpeningType,
  WindowOperationType,
} from '@/types/core/element/design/openingDesignTypes'
import {
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'
import { DesignCreator } from './DesignCreator'

/**
 * Placeholder type for design material.
 * @private
 * @remarks TODO: Define or import a proper DesignMaterial type/enum.
 */
type DesignMaterial = string

/**
 * Placeholder for DesignElementCategory. The base DesignCreator expects this.
 * For OpeningCreators, the category is 'OPENING'.
 * @private
 * @remarks TODO: Ideally, this would be an enum shared across design creators.
 */
const DesignElementCategory = {
  OPENING: 'OPENING',
  // Other categories like WALL, FURNITURE, FIXTURE, ROOM would be part of this enum.
} as const

/**
 * Abstract base class for creating opening elements such as doors and windows.
 * It extends {@link DesignCreator} and provides common functionality for openings.
 *
 * @abstract
 * @extends DesignCreator
 */
abstract class OpeningCreator extends DesignCreator {
  /**
   * The category of design element this creator produces.
   * @protected
   * @readonly
   */
  protected readonly category = DesignElementCategory.OPENING
  // The specific `elementType` (DOOR or WINDOW) will be defined in concrete subclasses.

  /**
   * Creates a default opening element with common properties shared by doors and windows.
   *
   * @param id - The unique identifier for the new opening element.
   * @param position - The {@link PointData} representing the opening's position.
   * @param wallId - The ID of the wall this opening is associated with.
   * @param params - Optional {@link ShapeCreationParamsUnion} providing additional creation parameters.
   * @returns A Promise resolving to the created {@link ShapeElement} representing the base opening.
   * @protected
   */
  protected async createDefaultOpening(
    id: string,
    position: PointData, // Changed to PointData
    wallId: string,
    params?: ShapeCreationParamsUnion,
  ): Promise<ShapeElement> {
    // Convert ShapeCreationParamsUnion to MajorCategory for super.createDefault
    const majorCategoryOverride = MajorCategory.FURNITURE // Use FURNITURE as fallback since OPENING doesn't exist
    const minorCategoryOverride = 'door' as MinorCategory // Default to 'door', can be overridden

    const baseElement = await super.createDefault(id, position, majorCategoryOverride, minorCategoryOverride)
    const p = params as {
      material?: DesignMaterial
      heightFromFloor?: number
      totalHeight?: number
      thickness?: number
    }

    // Opening-specific properties go into the 'properties' object
    baseElement.properties = {
      ...baseElement.properties,
      wallId,
      openingType: OpeningType.GENERIC_OPENING, // Default, will be overridden
      material: p?.material ?? 'wood',
      heightFromFloor: p?.heightFromFloor ?? 0,
      // totalHeight and thickness are usually part of the main dimensions,
      // or specific properties for openings.
      // Let's assume they are part of the 'properties' if not covered by base ShapeElement dimensions.
      totalHeight: p?.totalHeight ?? 2000, // Example default
      thickness: p?.thickness ?? 40, // Example default
    }

    // Metadata should contain general info, not dimensional/functional properties
    // unless specifically designed for metadata (like tags, descriptions).
    baseElement.metadata = ensureCompleteMetadata({
      ...baseElement.metadata,
      // category: DesignElementCategory.OPENING, // Already set by DesignCreator
      // wallId can also be in metadata if it's for search/filtering rather than direct rendering property
    })

    return baseElement
  }
}

/**
 * Creator class for instantiating Door elements.
 * It extends {@link OpeningCreator} and specializes in door-specific properties.
 *
 * @extends OpeningCreator
 */
export class DoorCreator extends OpeningCreator {
  /**
   * The specific element type this creator produces.
   * @protected
   * @readonly
   */
  protected readonly elementType = CoreElementType.DOOR

  /**
   * Creates a Door element with default properties.
   * This method overrides the base `createDefault` from {@link DesignCreator} (via {@link OpeningCreator}).
   *
   * @param id - The unique identifier for the new door element.
   * @param position - The {@link PointData} representing the door's position.
   * @param majorCategoryOverride - Optional major category override.
   * @param _minorCategoryOverride - Optional minor category override (unused).
   * @returns A Promise resolving to the created {@link ShapeElement} representing the door.
   * @override
   */
  async createDefault(
    id: string,
    position: PointData,
    majorCategoryOverride?: MajorCategory,
    _minorCategoryOverride?: MinorCategory,
  ): Promise<ShapeElement> { // Changed to match DesignCreator signature
    // For backward compatibility, handle old-style params if passed as majorCategoryOverride
    const params = majorCategoryOverride as unknown as {
      width?: number
      height?: number
      fill?: string
      stroke?: string
      strokeWidth?: number
      wallId?: string
      properties?: {
        wallId?: string
        width?: number
        height?: number
        heightFromFloor?: number
        thickness?: number
        openingType?: OpeningType
        swingDirection?: DoorSwingDirection
        subCategory?: string
        wallPosition?: number
        frameType?: string
        hardwareType?: string
        isExterior?: boolean
        isAutomatic?: boolean
      }
    }
    const p = params
    const wallId = p?.properties?.wallId ?? p?.wallId ?? ''

    const baseElement = await this.createDefaultOpening(id, position, wallId, params as ShapeCreationParamsUnion)

    const doorWidth = p?.width ?? p?.properties?.width ?? 900
    const doorHeight = p?.height ?? p?.properties?.height ?? 2100
    const heightFromFloor = p?.properties?.heightFromFloor ?? 0
    const thickness = p?.properties?.thickness ?? 40

    baseElement.properties = {
      ...baseElement.properties,
      type: 'rectangle', // Visual representation for a simple door
      width: doorWidth,
      height: doorHeight,
      fill: p?.fill ?? '#FFFFFF',
      stroke: p?.stroke ?? '#000000',
      strokeWidth: p?.strokeWidth ?? 2,
      openingType: p?.properties?.openingType ?? OpeningType.HINGED_DOOR,
      swingDirection: p?.properties?.swingDirection ?? DoorSwingDirection.LEFT_INSWING,
      subCategory: p?.properties?.subCategory ?? 'hinged',
      wallPosition: p?.properties?.wallPosition ?? 0.5,
      frameType: p?.properties?.frameType ?? 'standard',
      hardwareType: p?.properties?.hardwareType ?? 'lever',
      isExterior: p?.properties?.isExterior ?? false,
      isAutomatic: p?.properties?.isAutomatic ?? false,
      heightFromFloor, // Store here
      thickness, // Store here
      // totalHeight is effectively doorHeight for a simple door model
    }
    baseElement.type = CoreElementType.DOOR
    // Visual dimensions (width, height) are now part of baseElement.properties
    // No need to set them directly on ShapeElement if it doesn't have these top-level fields.

    return baseElement
  }

  /**
   * Convenience method to create an exterior door element.
   *
   * @param id - The unique identifier for the exterior door.
   * @param position - The {@link PointData} for the door's position.
   * @param wallId - The ID of the wall this door is associated with.
   * @param params - Optional {@link ShapeCreationParamsUnion} for additional properties.
   * @returns A Promise resolving to the created {@link ShapeElement}.
   */
  async createExteriorDoor(id: string, position: PointData, wallId: string, params?: ShapeCreationParamsUnion): Promise<ShapeElement> { // Changed to PointData
    const p = params
    return this.createDefault(id, position, {
      ...p,
      properties: { ...p?.properties, wallId, isExterior: true, width: 1000, thickness: 50, material: 'wood_exterior' },
    } as unknown as MajorCategory)
  }

  /**
   * Convenience method to create a sliding door element.
   *
   * @param id - The unique identifier for the sliding door.
   * @param position - The {@link PointData} for the door's position.
   * @param wallId - The ID of the wall this door is associated with.
   * @param params - Optional {@link ShapeCreationParamsUnion} for additional properties.
   * @returns A Promise resolving to the created {@link ShapeElement}.
   */
  async createSlidingDoor(id: string, position: PointData, wallId: string, params?: ShapeCreationParamsUnion): Promise<ShapeElement> { // Changed to PointData
    const p = params
    return this.createDefault(id, position, {
      ...p,
      properties: { ...p?.properties, wallId, openingType: OpeningType.SLIDING_DOOR, width: 1200, subCategory: 'sliding' },
    } as unknown as MajorCategory)
  }
}

/**
 * Creator class for instantiating Window elements.
 * It extends {@link OpeningCreator} and specializes in window-specific properties.
 *
 * @extends OpeningCreator
 */
export class WindowCreator extends OpeningCreator {
  /**
   * The specific element type this creator produces.
   * @protected
   * @readonly
   */
  protected readonly elementType = CoreElementType.WINDOW

  /**
   * Creates a Window element with default properties.
   * This method overrides the base `createDefault` from {@link DesignCreator} (via {@link OpeningCreator}).
   *
   * @param id - The unique identifier for the new window element.
   * @param position - The {@link PointData} representing the window's position.
   * @param majorCategoryOverride - Optional major category override.
   * @param _minorCategoryOverride - Optional minor category override (unused).
   * @returns A Promise resolving to the created {@link ShapeElement} representing the window.
   * @override
   */
  async createDefault(
    id: string,
    position: PointData,
    majorCategoryOverride?: MajorCategory,
    _minorCategoryOverride?: MinorCategory,
  ): Promise<ShapeElement> { // Changed to match DesignCreator signature
    // For backward compatibility, handle old-style params if passed as majorCategoryOverride
    const params = majorCategoryOverride as unknown as {
      width?: number
      height?: number
      fill?: string
      stroke?: string
      strokeWidth?: number
      wallId?: string
      properties?: {
        wallId?: string
        width?: number
        height?: number
        sillHeight?: number
        heightFromFloor?: number
        thickness?: number
        openingType?: OpeningType
        operationType?: WindowOperationType
        subCategory?: string
        wallPosition?: number
        glazingType?: string
        paneCount?: number
        hasScreen?: boolean
        hasBlinds?: boolean
      }
    }
    const p = params
    const wallId = p?.properties?.wallId ?? p?.wallId ?? ''
    const baseElement = await this.createDefaultOpening(id, position, wallId, params as ShapeCreationParamsUnion)

    const windowWidth = p?.width ?? p?.properties?.width ?? 1200
    const windowHeight = p?.height ?? p?.properties?.height ?? 1200
    const sillHeight = p?.properties?.sillHeight ?? p?.properties?.heightFromFloor ?? 900
    const thickness = p?.properties?.thickness ?? 30

    baseElement.properties = {
      ...baseElement.properties,
      type: 'rectangle',
      width: windowWidth,
      height: windowHeight,
      fill: p?.fill ?? '#E6F7FF',
      stroke: p?.stroke ?? '#000000',
      strokeWidth: p?.strokeWidth ?? 2,
      openingType: p?.properties?.openingType ?? OpeningType.CASEMENT_WINDOW,
      operationType: p?.properties?.operationType ?? WindowOperationType.CASEMENT_LEFT,
      subCategory: p?.properties?.subCategory ?? 'casement',
      wallPosition: p?.properties?.wallPosition ?? 0.5,
      sillHeight,
      headHeight: sillHeight + windowHeight,
      glazingType: p?.properties?.glazingType ?? 'double',
      paneCount: p?.properties?.paneCount ?? 2,
      hasScreen: p?.properties?.hasScreen ?? true,
      hasBlinds: p?.properties?.hasBlinds ?? false,
      heightFromFloor: sillHeight, // Store here
      thickness,
    }
    baseElement.type = CoreElementType.WINDOW
    // Visual dimensions (width, height) are now part of baseElement.properties
    // position might need adjustment based on sillHeight if position is bottom-left
    // Assuming position is top-left or center for now as per DesignCreator.

    return baseElement
  }

  /**
   * Convenience method to create a fixed window element.
   *
   * @param id - The unique identifier for the fixed window.
   * @param position - The {@link PointData} for the window's position.
   * @param wallId - The ID of the wall this window is associated with.
   * @param params - Optional {@link ShapeCreationParamsUnion} for additional properties.
   * @returns A Promise resolving to the created {@link ShapeElement}.
   */
  async createFixedWindow(id: string, position: PointData, wallId: string, params?: ShapeCreationParamsUnion): Promise<ShapeElement> { // Changed to PointData
    const p = params
    return this.createDefault(id, position, {
      ...p,
      properties: { ...p?.properties, wallId, openingType: OpeningType.FIXED_WINDOW, operationType: WindowOperationType.FIXED, subCategory: 'fixed' },
    } as unknown as MajorCategory)
  }

  /**
   * Convenience method to create a sliding window element.
   *
   * @param id - The unique identifier for the sliding window.
   * @param position - The {@link PointData} for the window's position.
   * @param wallId - The ID of the wall this window is associated with.
   * @param params - Optional {@link ShapeCreationParamsUnion} for additional properties.
   * @returns A Promise resolving to the created {@link ShapeElement}.
   */
  async createSlidingWindow(id: string, position: PointData, wallId: string, params?: ShapeCreationParamsUnion): Promise<ShapeElement> { // Changed to PointData
    const p = params
    return this.createDefault(id, position, {
      ...p,
      properties: { ...p?.properties, wallId, openingType: OpeningType.SLIDING_WINDOW, operationType: WindowOperationType.SLIDING_HORIZONTAL, subCategory: 'sliding' },
    } as unknown as MajorCategory)
  }
}
