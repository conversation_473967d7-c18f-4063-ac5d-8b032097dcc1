/**
 * Settings Dialog Component
 *
 * A comprehensive settings management dialog that allows users to configure
 * default properties for all element types in the application. This component
 * provides a two-panel interface for browsing element types and editing their
 * default properties with proper unit conversion and validation.
 *
 * Features:
 * - Two-panel layout: element type list and property editor
 * - Automatic unit conversion for dimensional properties (pixels ↔ mm)
 * - Type-specific property editors (text, number, boolean, select)
 * - Real-time validation and error handling
 * - Bulk reset functionality for individual types or all settings
 * - Responsive design with scrollable content areas
 * - Integration with persistent settings system
 *
 * Property Types Supported:
 * - Dimensional properties (width, height, radius, etc.) with unit conversion
 * - Non-dimensional numeric properties (opacity, rotation, etc.)
 * - Boolean properties with checkbox controls
 * - String properties with text inputs or select dropdowns
 * - Special handling for complex types (font weight, etc.)
 *
 * @example
 * ```tsx
 * <SettingsPanel
 *   isOpen={true}
 *   onClose={() => setIsOpen(false)}
 *   effectiveSettings={settings}
 *   saveSettings={handleSave}
 *   getSettingsForType={getDefaults}
 *   resetAllSettings={handleReset}
 *   pixelsPerMM={0.08}
 * />
 * ```
 */

import type React from 'react'
import type {
  // defaultElementSettings, // No longer directly used for initial state here
  InitialElementProperties,
  // getDefaultSettingsForElementType // Will use hook's getter
} from '@/config/defaultElementSettings'
import type Point from '@/types/core/element/geometry/point' // Corrected: Point is likely a default export
import { useEffect, useMemo, useState } from 'react'
// Importing UI components from the provided 'ui' directory
import { Button } from '@/components/ui/button' // Adjusted import path
// import { usePersistentSettings } from '@/hooks/usePersistentSettings'; // Remove local import

import { Checkbox } from '@/components/ui/checkbox' // Adjusted import path
import { Dialog, DialogClose, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog' // Adjusted import path
import { Input } from '@/components/ui/input' // Adjusted import path
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'; // Unused import
import { Label } from '@/components/ui/label' // Adjusted import path
import { ScrollArea } from '@/components/ui/scroll-area' // Import ScrollArea
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select' // Adjusted import path
import { ElementType } from '@/types/core/elementDefinitions'

/**
 * Type definition for the settings store structure
 */
type SettingsStore = Partial<Record<ElementType, InitialElementProperties>>

/**
 * Type definition for property values with better type safety
 */
type PropertyValue = string | number | boolean | Point[] | null | undefined

/**
 * Type definition for property metadata
 */
interface PropertyMetadata {
  type: 'number' | 'boolean' | 'string' | 'select' | 'color' | 'points'
  isDimensional?: boolean
  min?: number
  max?: number
  step?: number
  options?: string[]
  unit?: string
  validation?: (value: PropertyValue) => boolean
  description?: string
}

/**
 * Props for the SettingsPanel component
 */
interface SettingsPanelProps {
  /** Whether the settings dialog is open */
  isOpen: boolean
  /** Callback to close the dialog */
  onClose: () => void
  /** Current effective settings from the persistent settings hook */
  effectiveSettings: SettingsStore
  /** Function to save settings changes */
  saveSettings: (settings: SettingsStore) => void
  /** Function to get default settings for a specific element type */
  getSettingsForType: (elementType: ElementType) => InitialElementProperties
  /** Function to reset all settings to defaults */
  resetAllSettings: () => void
  /** Conversion factor from pixels to millimeters */
  pixelsPerMM: number
}

// Unit name constant for future use
// const _UNIT_NAME = 'mm' // Consistent unit name
const PIXELS_PER_MM_FALLBACK_SETTINGS = 1.0 // Fallback, should be passed via props - matches CANVAS_PIXELS_PER_MM

/**
 * Property metadata configuration for better type handling and validation
 * Updated to match PropertySidebar actual implementation
 */
const PROPERTY_METADATA: Record<string, PropertyMetadata> = {
  // Dimensional properties (match PropertySidebar implementation)
  width: { type: 'number', isDimensional: true, min: 0, unit: 'mm', description: 'Size' }, // For square, this represents size
  height: { type: 'number', isDimensional: true, min: 0, unit: 'mm', description: 'Height' },
  radius: { type: 'number', isDimensional: true, min: 0, unit: 'mm', description: 'Size' }, // For circle and polygons
  radiusX: { type: 'number', isDimensional: true, min: 0, unit: 'mm', description: 'Radius X' }, // For ellipse
  radiusY: { type: 'number', isDimensional: true, min: 0, unit: 'mm', description: 'Radius Y' }, // For ellipse
  fontSize: { type: 'number', isDimensional: true, min: 1, unit: 'mm', description: 'Font Size' },
  cornerRadius: { type: 'number', isDimensional: true, min: 0, unit: 'mm', description: 'Corner Radius' },
  strokeWidth: { type: 'number', isDimensional: true, min: 0, step: 0.1, unit: 'mm', description: 'Stroke Width' },

  // Non-dimensional numeric properties
  opacity: { type: 'number', min: 0, max: 1, step: 0.01, description: 'Opacity' },
  rotation: { type: 'number', min: -360, max: 360, step: 1, unit: '°', description: 'Rotation' },
  startAngle: { type: 'number', min: 0, max: 360, step: 1, unit: '°', description: 'Start Angle' },
  endAngle: { type: 'number', min: 0, max: 360, step: 1, unit: '°', description: 'End Angle' },

  // Boolean properties
  visible: { type: 'boolean', description: 'Visible' },
  locked: { type: 'boolean', description: 'Locked' },

  // String properties with options (match PropertySidebar)
  fontFamily: {
    type: 'select',
    options: [
      'Inter, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      'Arial, sans-serif',
      'Helvetica, Arial, sans-serif',
      '\'Times New Roman\', Times, serif',
      'Georgia, "Times New Roman", serif',
      '\'Courier New\', Courier, monospace',
      'Verdana, Geneva, sans-serif',
      '\'Trebuchet MS\', Helvetica, sans-serif',
      'Impact, Charcoal, sans-serif',
      '\'Lucida Console\', Monaco, monospace',
      '\'Comic Sans MS\', cursive',
    ],
    description: 'Font Family',
  },
  textAlign: {
    type: 'select',
    options: ['left', 'center', 'right'],
    description: 'Text Alignment',
  },

  // Color properties
  fill: { type: 'color', description: 'Fill Color' },
  stroke: { type: 'color', description: 'Stroke Color' },

  // Text and media content
  text: { type: 'string', description: 'Text' },
  src: { type: 'string', description: 'Source' },
}

/**
 * Helper function to get a user-friendly display name for an element type.
 *
 * @param elementType - The element type enum value
 * @returns A formatted display name with proper capitalization
 */
function getElementDisplayName(elementType: ElementType): string {
  // Simple mapping, can be expanded
  return elementType.charAt(0).toUpperCase() + elementType.slice(1).toLowerCase()
}

/**
 * Get property metadata for a given property name
 * @param propertyName - The property name to get metadata for
 * @returns Property metadata or default metadata
 */
function getPropertyMetadata(propertyName: string): PropertyMetadata {
  return PROPERTY_METADATA[propertyName] ?? {
    type: 'string',
    description: `Property: ${propertyName}`,
  }
}

/**
 * Get supported properties for a specific element type
 * Based on actual PropertySidebar implementation analysis
 * @param elementType - The element type to get properties for
 * @returns Array of property names that should be shown for this element type
 */
function getSupportedProperties(elementType: ElementType): string[] {
  const baseProperties = ['fill', 'stroke', 'strokeWidth', 'opacity', 'visible', 'locked']

  switch (elementType) {
    case ElementType.RECTANGLE:
      return [...baseProperties, 'width', 'height', 'cornerRadius', 'rotation']

    case ElementType.SQUARE:
      // Square should only show size (using width as the size reference)
      return [...baseProperties, 'width', 'cornerRadius', 'rotation']

    case ElementType.CIRCLE:
      // Circle uses radius control
      return [...baseProperties, 'radius']

    case ElementType.ELLIPSE:
      // Ellipse uses radiusX and radiusY controls
      return [...baseProperties, 'radiusX', 'radiusY', 'rotation']

    case ElementType.POLYGON:
    case ElementType.TRIANGLE:
    case ElementType.QUADRILATERAL:
    case ElementType.PENTAGON:
    case ElementType.HEXAGON:
    case ElementType.HEPTAGON:
    case ElementType.OCTAGON:
    case ElementType.NONAGON:
    case ElementType.DECAGON:
      // Polygons use radius (size) and rotation controls
      return [...baseProperties, 'radius', 'rotation']

    case ElementType.LINE:
      // LINE uses start/end points, only stroke properties (no fill for paths)
      return ['stroke', 'strokeWidth', 'opacity', 'visible', 'locked']

    case ElementType.POLYLINE:
      // POLYLINE uses points array, only stroke properties (no fill for paths)
      return ['stroke', 'strokeWidth', 'opacity', 'visible', 'locked']

    case ElementType.ARC:
      // ARC only basic stroke properties (no fill for paths, no complex geometry)
      return ['stroke', 'strokeWidth', 'opacity', 'visible', 'locked']

    case ElementType.QUADRATIC:
      // QUADRATIC uses start/control/end points, only stroke properties (no fill for paths)
      return ['stroke', 'strokeWidth', 'opacity', 'visible', 'locked']

    case ElementType.CUBIC:
      // CUBIC uses start/control1/control2/end points, only stroke properties (no fill for paths)
      return ['stroke', 'strokeWidth', 'opacity', 'visible', 'locked']

    case ElementType.TEXT:
      // TEXT elements don't support stroke in PropertySidebar
      return ['text', 'fontSize', 'fontFamily', 'textAlign', 'width', 'height', 'fill', 'opacity', 'visible', 'locked', 'rotation']

    case ElementType.TEXT_LABEL:
      // TEXT_LABEL similar to TEXT
      return ['text', 'fontSize', 'fontFamily', 'textAlign', 'width', 'height', 'fill', 'opacity', 'visible', 'locked', 'rotation']

    case ElementType.IMAGE:
      // IMAGE elements only support opacity in PropertySidebar, src removed per user request
      return ['width', 'height', 'opacity', 'visible', 'locked', 'rotation']

    // Architectural elements
    case ElementType.WALL:
    case ElementType.DOOR:
    case ElementType.WINDOW:
    case ElementType.OPENING:
      return [...baseProperties, 'width', 'height', 'rotation']

    // Furniture and fixtures
    case ElementType.FURNITURE:
    case ElementType.FIXTURE:
    case ElementType.APPLIANCE:
      return [...baseProperties, 'width', 'height', 'rotation']

    // Room and area elements
    case ElementType.ROOM:
    case ElementType.FLOOR_AREA:
    case ElementType.ROOM_BOUNDARY:
      return [...baseProperties, 'width', 'height']

    // Utility elements
    case ElementType.LIGHT:
    case ElementType.ELECTRICAL_OUTLET:
      return [...baseProperties, 'radius']

    case ElementType.HANDRAIL:
      return ['stroke', 'strokeWidth', 'opacity', 'visible', 'locked']

    // Surface treatments
    case ElementType.WALL_PAINT:
    case ElementType.WALL_PAPER:
      return ['fill', 'opacity', 'visible', 'locked']

    // Group element
    case ElementType.GROUP:
      return ['opacity', 'visible', 'locked']

    default:
      return baseProperties
  }
}





/**
 * SettingsPanel component that provides a comprehensive interface for managing default element properties.
 *
 * This component renders a modal dialog with a two-panel layout: a list of element types
 * on the left and a property editor on the right. It handles unit conversion, validation,
 * and provides various reset options for managing default settings.
 *
 * The component integrates with the persistent settings system and provides real-time
 * feedback for property changes with proper type-specific editors.
 *
 * @param props - The component props
 * @param props.isOpen - Controls dialog visibility
 * @param props.onClose - Callback to close the dialog
 * @param props.effectiveSettings - Current settings from persistent store
 * @param props.saveSettings - Function to save settings changes
 * @param props.getSettingsForType - Function to get defaults for element types
 * @param props.resetAllSettings - Function to reset all settings to defaults
 * @param props.pixelsPerMM - Unit conversion factor
 * @returns The rendered settings panel dialog
 */
export const SettingsPanel: React.FC<SettingsPanelProps> = ({
  isOpen,
  onClose,
  effectiveSettings,
  saveSettings,
  getSettingsForType,
  resetAllSettings,
  pixelsPerMM,
}) => {
  // const { effectiveSettings, saveSettings, getSettingsForType, resetAllSettings } = usePersistentSettings(); // Remove local hook call

  // Local state for editing, initialized from persistent settings
  const [currentSettings, setCurrentSettings] = useState<SettingsStore>(() => JSON.parse(JSON.stringify(effectiveSettings)) as SettingsStore)

  // Use only ElementTypes that are actually provided in the UI (from assetConfig.tsx)
  const availableElementTypes: ElementType[] = [
    // Basic shapes
    ElementType.RECTANGLE,
    ElementType.SQUARE,
    ElementType.ELLIPSE,
    ElementType.CIRCLE,
    // Polygons (only those provided in UI)
    ElementType.TRIANGLE,
    ElementType.PENTAGON,
    ElementType.HEXAGON,
    // Paths
    ElementType.LINE,
    ElementType.POLYLINE,
    ElementType.ARC,
    ElementType.QUADRATIC,
    ElementType.CUBIC,
    // Content
    ElementType.TEXT,
    ElementType.IMAGE,
  ]

  // Add layout settings categories
  const settingsCategories = [
    { id: 'elements', name: 'Element Defaults', icon: '🎨' },
    { id: 'layout', name: 'Layout & Interface', icon: '📱' },
    { id: 'toolbar', name: 'Toolbar Visibility', icon: '🔧' },
  ]

  const [selectedCategory, setSelectedCategory] = useState<'elements' | 'layout' | 'toolbar'>('elements')

  // Layout settings state
  interface LayoutSettings {
    autoCollapseSidebars: boolean
    sidebarMutualExclusion: boolean
    responsiveToolbar: boolean
    smallScreenBreakpoint: number
  }

  const [layoutSettings, setLayoutSettings] = useState<LayoutSettings>(() => {
    const saved = localStorage.getItem('RenoPilot.layoutSettings')
    if (saved !== null && saved !== undefined && saved !== '') {
      try {
        const parsed = JSON.parse(saved) as LayoutSettings
        return parsed
      }
      catch {
        // Fall back to defaults if parsing fails
      }
    }
    return {
      autoCollapseSidebars: true,
      sidebarMutualExclusion: true,
      responsiveToolbar: true,
      smallScreenBreakpoint: 768,
    }
  })

  // Toolbar visibility settings state
  interface ToolbarSettings {
    showFileOperations: boolean
    showUndoRedo: boolean
    showInteractionModes: boolean
    showZoomControls: boolean
    showCanvasInfo: boolean
    showGridToggle: boolean
    showKeyboardShortcuts: boolean
    showTutorial: boolean
    showSettings: boolean
  }

  const [toolbarSettings, setToolbarSettings] = useState<ToolbarSettings>(() => {
    const saved = localStorage.getItem('RenoPilot.toolbarSettings')
    if (saved !== null && saved !== undefined && saved !== '') {
      try {
        const parsed = JSON.parse(saved) as ToolbarSettings
        return parsed
      }
      catch {
        // Fall back to defaults if parsing fails
      }
    }
    return {
      showFileOperations: true,
      showUndoRedo: true,
      showInteractionModes: true,
      showZoomControls: true,
      showCanvasInfo: true,
      showGridToggle: true,
      showKeyboardShortcuts: true,
      showTutorial: true,
      showSettings: true,
    }
  })

  // Save layout settings to localStorage
  useEffect(() => {
    localStorage.setItem('RenoPilot.layoutSettings', JSON.stringify(layoutSettings))
  }, [layoutSettings])

  // Save toolbar settings to localStorage and notify other components
  useEffect(() => {
    localStorage.setItem('RenoPilot.toolbarSettings', JSON.stringify(toolbarSettings))
    // Dispatch custom event to notify Toolbar component of changes
    window.dispatchEvent(new CustomEvent('toolbarSettingsChanged'))
  }, [toolbarSettings])

  const [selectedElementTypeForEditing, setSelectedElementTypeForEditing] = useState<ElementType>(() => {
    const firstKeyFromEffective = Object.keys(effectiveSettings)[0] as ElementType | undefined
    // Ensure firstKeyFromEffective is a valid ElementType string and is a known ElementType before using includes
    if (
      firstKeyFromEffective !== undefined
      && firstKeyFromEffective !== null
      && typeof firstKeyFromEffective === 'string'
      && (Object.values(ElementType) as string[]).includes(firstKeyFromEffective)
    ) {
      return firstKeyFromEffective
    }
    return availableElementTypes.length > 0 ? availableElementTypes[0] : ElementType.RECTANGLE // Default fallback
  })

  useEffect(() => {
    if (isOpen) {
      // When dialog opens, reset local editing state to the latest from persistent store
      // Deep copy to avoid direct mutation of prop
      const newEditingDefaults = JSON.parse(JSON.stringify(effectiveSettings)) as SettingsStore
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setCurrentSettings(newEditingDefaults)

      // Validate current selection, default if necessary
      if (!availableElementTypes.includes(selectedElementTypeForEditing) && availableElementTypes.length > 0) {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setSelectedElementTypeForEditing(availableElementTypes[0])
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, effectiveSettings, selectedElementTypeForEditing]) // availableElementTypes is stable

  // Properties to display for the selected element type
  const propertiesForSelectedType = useMemo<InitialElementProperties | undefined>(() => { // Can be undefined if no settings
    const defaults = getSettingsForType(selectedElementTypeForEditing)
    const current = currentSettings[selectedElementTypeForEditing]
    return { ...defaults, ...current } as InitialElementProperties // Assume current settings merge to full properties
  }, [currentSettings, selectedElementTypeForEditing, getSettingsForType])

  const toDisplayUnit = (internalValue: unknown): number | string => {
    if (typeof internalValue === 'number') {
      return Number.parseFloat((internalValue / (pixelsPerMM || PIXELS_PER_MM_FALLBACK_SETTINGS)).toFixed(2))
    }
    return String(internalValue) // Fallback to string
  }

  const toInternalUnit = (displayValue: unknown): number | undefined => {
    const numericValue = typeof displayValue === 'string' ? Number.parseFloat(displayValue) : displayValue
    if (typeof numericValue === 'number' && !Number.isNaN(numericValue)) { // Use Number.isNaN
      return numericValue * (pixelsPerMM || PIXELS_PER_MM_FALLBACK_SETTINGS)
    }
    return undefined
  }

  const handleInputChange = (
    elementType: ElementType,
    property: keyof InitialElementProperties,
    value: unknown, // This is the display value from the input
  ) => {
    setCurrentSettings((prevSettings) => {
      const elementSpecificSettings = prevSettings[elementType] ?? {}
      const metadata = getPropertyMetadata(String(property))
      let internalValue: string | number | boolean | undefined | Point[] | null = null

      if (value === '') { // Allow clearing the field, sets to undefined
        internalValue = undefined
      }
      // Convert to internal unit if it's a dimensional property based on metadata
      else if (typeof value === 'number' && metadata.isDimensional) {
        internalValue = toInternalUnit(value)
      }
      else if (typeof value === 'string' && metadata.isDimensional) {
        const numValue = Number.parseFloat(value)
        if (!Number.isNaN(numValue)) { // Use Number.isNaN
          internalValue = toInternalUnit(numValue)
        }
        // If not parseable to number but was string, retain as string (or let it be undefined if it was cleared)
        else {
          internalValue = value
        }
      }
      else if (typeof value === 'boolean') {
        internalValue = value
      }
      // Add other specific type handling if necessary, e.g. for Point[]
      // For now, if not handled above and not empty string, it might remain as original value or be issues
      else {
        // Fallback for other types or if it wasn't cleared
        // This might need more specific handling depending on what 'value: unknown' can be
        internalValue = value as string | number | boolean | undefined | Point[] | null
      }

      // Special handling for square: when width changes, also update height to maintain square ratio
      const updatedSettings = {
        ...elementSpecificSettings,
        [property]: internalValue,
      }

      if (elementType === ElementType.SQUARE && property === 'width' && typeof internalValue === 'number') {
        updatedSettings.height = internalValue
      }

      return {
        ...prevSettings,
        [elementType]: updatedSettings,
      }
    })
  }

  const handleCheckboxChange = (
    elementType: ElementType,
    property: keyof InitialElementProperties,
    checked: boolean,
  ) => {
    handleInputChange(elementType, property, checked)
  }

  const handleSelectChange = (
    elementType: ElementType,
    property: keyof InitialElementProperties,
    selectedValue: string,
  ) => {
    handleInputChange(elementType, property, selectedValue)
  }

  const renderPropertyField = (elementType: ElementType, property: keyof InitialElementProperties, value: unknown) => {
    const propertyKey = `${elementType}-${String(property)}`
    const metadata = getPropertyMetadata(String(property))

    // Determine displayValue based on type and potential conversion
    let displayValue: string | number | boolean | readonly Point[] = ''
    if (metadata.isDimensional && typeof value === 'number') {
      displayValue = toDisplayUnit(value)
    }
    else if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
      displayValue = value
    }
    else if (Array.isArray(value) && value.every((p: unknown) => typeof p === 'object' && p !== null && 'x' in p && 'y' in p)) {
      displayValue = value as readonly Point[]
    }
    else if (value === null || value === undefined) {
      displayValue = ''
    }

    // Special handling for property descriptions based on element type
    let propertyDescription = metadata.description ?? String(property)
    if (property === 'width') {
      if (elementType === ElementType.SQUARE) {
        propertyDescription = 'Size'
      }
      else {
        propertyDescription = 'Width'
      }
    }

    const labelSuffix = (metadata.unit !== undefined && metadata.unit !== null && metadata.unit !== '') ? ` (${metadata.unit})` : ''
    const labelText = `${propertyDescription}${labelSuffix}`

    // Render based on metadata type
    if (metadata.type === 'number' || typeof value === 'number') {
      return (
        <div key={propertyKey} className="grid grid-cols-2 items-center gap-4 mb-2">
          <Label htmlFor={propertyKey} className="text-right" title={metadata.description}>
            {labelText}
          </Label>
          <Input
            id={propertyKey}
            type="number"
            min={metadata.min}
            max={metadata.max}
            step={metadata.step ?? 0.1}
            value={displayValue === null || displayValue === undefined ? '' : String(displayValue)}
            onChange={(e) => {
              const numValue = e.target.value === '' ? '' : Number.parseFloat(e.target.value) || 0
              // Apply validation if provided
              if (metadata.validation && typeof numValue === 'number' && !metadata.validation(numValue)) {
                return // Don't update if validation fails
              }
              handleInputChange(elementType, property, numValue)
            }}
            className="col-span-1"
            placeholder={metadata.description}
          />
        </div>
      )
    }
    else if (metadata.type === 'color') {
      return (
        <div key={propertyKey} className="grid grid-cols-2 items-center gap-4 mb-2">
          <Label htmlFor={propertyKey} className="text-right" title={metadata.description}>
            {labelText}
          </Label>
          <Input
            id={propertyKey}
            type="color"
            value={typeof value === 'string' ? value : '#000000'}
            onChange={e => handleInputChange(elementType, property, e.target.value)}
            className="col-span-1 h-10 w-full p-1 border rounded cursor-pointer"
            title={`Select ${metadata.description}`}
          />
        </div>
      )
    }
    else if (typeof value === 'boolean') {
      return (
        <div key={propertyKey} className="flex items-center space-x-2 mb-2">
          <Checkbox
            id={propertyKey}
            checked={Boolean(value)}
            onCheckedChange={checked => handleCheckboxChange(elementType, property, Boolean(checked))}
          />
          <Label htmlFor={propertyKey} className="cursor-pointer" title={metadata.description}>
            {metadata.description ?? String(property)}
          </Label>
        </div>
      )
    }
    else if (metadata.type === 'select' || (typeof value === 'string' && metadata.options)) {
      let selectValue = typeof value === 'string' ? value : ''

      // For fontFamily, try to match the full font family string
      if (property === 'fontFamily' && selectValue !== '' && selectValue !== null && selectValue !== undefined && metadata.options !== undefined) {
        // Try to find exact match first
        const exactMatch = metadata.options.find(option => option === selectValue)
        if (exactMatch !== undefined) {
          selectValue = exactMatch
        }
        else {
          // If no exact match, try to find by first font name
          const firstFontName = selectValue.split(',')[0].trim().replace(/['"]/g, '')
          const partialMatch = metadata.options.find((option) => {
            const optionFirstFont = option.split(',')[0].trim().replace(/['"]/g, '')
            return optionFirstFont.toLowerCase() === firstFontName.toLowerCase()
          })
          selectValue = partialMatch ?? selectValue
        }
      }

      return (
        <div key={propertyKey} className="grid grid-cols-2 items-center gap-4 mb-2">
          <Label htmlFor={propertyKey} className="text-right" title={metadata.description}>
            {labelText}
          </Label>
          <Select
            value={selectValue}
            onValueChange={newValue => handleSelectChange(elementType, property, newValue)}
          >
            <SelectTrigger id={propertyKey} className="col-span-1">
              <SelectValue placeholder={`Select ${property}`} />
            </SelectTrigger>
            <SelectContent>
              {metadata.options?.map(option => (
                <SelectItem key={option} value={option}>
                  {property === 'fontFamily'
                    ? option.split(',')[0].trim().replace(/['"]/g, '') // Show just the font name for display
                    : option.charAt(0).toUpperCase() + option.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )
    }
    else if (typeof value === 'string') {
      return (
        <div key={propertyKey} className="grid grid-cols-2 items-center gap-4 mb-2">
          <Label htmlFor={propertyKey} className="text-right" title={metadata.description}>
            {labelText}
          </Label>
          <Input
            id={propertyKey}
            type="text"
            value={value === null || value === undefined ? '' : value}
            onChange={e => handleInputChange(elementType, property, e.target.value)}
            className="col-span-1"
            placeholder={metadata.description}
          />
        </div>
      )
    }
    // Add more type handlers (e.g., for select dropdowns based on property name)
    // For complex types like 'points', a more specialized editor or just string display might be needed.
    return (
      <div key={propertyKey} className="grid grid-cols-2 items-center gap-4 mb-2">
        <Label htmlFor={propertyKey} className="text-right" title={metadata.description}>
          {metadata.description ?? String(property)}
        </Label>
        <span className="col-span-1 text-sm text-gray-500">Unsupported type or complex value</span>
      </div>
    )
  }

  // Render toolbar visibility settings
  const renderToolbarSettings = () => {
    const toolbarElements = [
      { key: 'showFileOperations', label: 'File Operations (New, Export)', description: 'Show file operation buttons in the toolbar' },
      { key: 'showUndoRedo', label: 'Undo/Redo', description: 'Show undo and redo buttons' },
      { key: 'showInteractionModes', label: 'Interaction Modes (Select/Pan)', description: 'Show interaction mode toggle buttons' },
      { key: 'showZoomControls', label: 'Zoom Controls', description: 'Show zoom in, zoom out, and reset zoom buttons' },
      { key: 'showCanvasInfo', label: 'Canvas Information', description: 'Show canvas dimensions and scale information' },
      { key: 'showGridToggle', label: 'Grid Toggle', description: 'Show grid visibility toggle button' },
      { key: 'showKeyboardShortcuts', label: 'Keyboard Shortcuts Guide', description: 'Show keyboard shortcuts help button' },
      { key: 'showTutorial', label: 'Tutorial Menu', description: 'Show tutorial access button' },
      { key: 'showSettings', label: 'Settings Button', description: 'Show settings dialog access button' },
    ]

    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-xl font-semibold mb-4">Toolbar Element Visibility</h3>
          <p className="text-sm text-muted-foreground mb-6">
            Control which elements are displayed in the toolbar. Note: Responsive design may still hide elements on smaller screens.
          </p>
        </div>

        <div className="space-y-4">
          {toolbarElements.map(element => (
            <div key={element.key} className="space-y-2">
              <div className="flex items-center space-x-3">
                <Checkbox
                  id={element.key}
                  checked={toolbarSettings[element.key as keyof ToolbarSettings]}
                  onCheckedChange={checked =>
                    setToolbarSettings((prev: ToolbarSettings) => ({ ...prev, [element.key]: Boolean(checked) }))}
                />
                <Label htmlFor={element.key} className="cursor-pointer font-medium">
                  {element.label}
                </Label>
              </div>
              <p className="text-sm text-muted-foreground ml-6">
                {element.description}
              </p>
            </div>
          ))}
        </div>

        <div className="bg-amber-50 dark:bg-amber-950/20 p-4 rounded-lg border border-amber-200 dark:border-amber-800">
          <h4 className="font-medium text-amber-800 dark:text-amber-200 mb-2">Important Notes</h4>
          <ul className="text-sm text-amber-700 dark:text-amber-300 space-y-1">
            <li>• Responsive design will still hide elements on smaller screens regardless of these settings</li>
            <li>• Some elements like sidebar toggles are always visible for functionality</li>
            <li>• Changes take effect immediately and are saved automatically</li>
            <li>• Hiding the Settings button will make this dialog inaccessible until reset</li>
          </ul>
        </div>
      </div>
    )
  }

  // Render layout settings
  const renderLayoutSettings = () => {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-xl font-semibold mb-4">Responsive Layout Settings</h3>
          <p className="text-sm text-muted-foreground mb-6">
            Configure how the interface adapts to different screen sizes and user interactions.
          </p>
        </div>

        {/* Auto-collapse sidebars */}
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <Checkbox
              id="autoCollapseSidebars"
              checked={layoutSettings.autoCollapseSidebars}
              onCheckedChange={checked =>
                setLayoutSettings((prev: LayoutSettings) => ({ ...prev, autoCollapseSidebars: Boolean(checked) }))}
            />
            <Label htmlFor="autoCollapseSidebars" className="cursor-pointer font-medium">
              Auto-collapse sidebars on small screens
            </Label>
          </div>
          <p className="text-sm text-muted-foreground ml-6">
            Automatically hide left and right sidebars when screen width is below the breakpoint to maximize canvas space.
          </p>
        </div>

        {/* Sidebar mutual exclusion */}
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <Checkbox
              id="sidebarMutualExclusion"
              checked={layoutSettings.sidebarMutualExclusion}
              onCheckedChange={checked =>
                setLayoutSettings((prev: LayoutSettings) => ({ ...prev, sidebarMutualExclusion: Boolean(checked) }))}
            />
            <Label htmlFor="sidebarMutualExclusion" className="cursor-pointer font-medium">
              Enable sidebar mutual exclusion on very small screens
            </Label>
          </div>
          <p className="text-sm text-muted-foreground ml-6">
            On very small screens (below 768px), only allow one sidebar to be open at a time. Opening one will automatically close the other.
          </p>
        </div>

        {/* Responsive toolbar */}
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <Checkbox
              id="responsiveToolbar"
              checked={layoutSettings.responsiveToolbar}
              onCheckedChange={checked =>
                setLayoutSettings((prev: LayoutSettings) => ({ ...prev, responsiveToolbar: Boolean(checked) }))}
            />
            <Label htmlFor="responsiveToolbar" className="cursor-pointer font-medium">
              Enable responsive toolbar
            </Label>
          </div>
          <p className="text-sm text-muted-foreground ml-6">
            Progressively hide toolbar elements on smaller screens to prevent content wrapping. Essential controls always remain visible.
          </p>
        </div>

        {/* Small screen breakpoint */}
        <div className="space-y-3">
          <Label htmlFor="smallScreenBreakpoint" className="font-medium">
            Small screen breakpoint (px)
          </Label>
          <div className="flex items-center space-x-3">
            <Input
              id="smallScreenBreakpoint"
              type="number"
              min={320}
              max={1200}
              step={1}
              value={layoutSettings.smallScreenBreakpoint}
              onChange={(e) => {
                const value = Number.parseInt(e.target.value) || 768
                setLayoutSettings((prev: LayoutSettings) => ({ ...prev, smallScreenBreakpoint: value }))
              }}
              className="w-24"
            />
            <span className="text-sm text-muted-foreground">
              Screen width below this value triggers small screen optimizations
            </span>
          </div>
          <p className="text-sm text-muted-foreground">
            Default: 768px (tablet breakpoint). Recommended range: 320px - 1200px.
          </p>
        </div>

        {/* Current screen info */}
        <div className="bg-muted/30 p-4 rounded-lg">
          <h4 className="font-medium mb-2">Current Screen Information</h4>
          <div className="text-sm text-muted-foreground space-y-1">
            <p>
              Screen width:
              {typeof window !== 'undefined' ? window.innerWidth : 'Unknown'}
              px
            </p>
            <p>
              Screen height:
              {typeof window !== 'undefined' ? window.innerHeight : 'Unknown'}
              px
            </p>
            <p>
              Current mode:
              {' '}
              {typeof window !== 'undefined' && window.innerWidth < layoutSettings.smallScreenBreakpoint
                ? 'Small screen mode'
                : 'Large screen mode'}
            </p>
          </div>
        </div>
      </div>
    )
  }

  const handleSaveChanges = () => {
    saveSettings(currentSettings)
    onClose()
  }

  const _handleSaveChanges = handleSaveChanges

  const handleResetAllTypesToDefaults = () => {
    resetAllSettings() // This updates effectiveSettings via the hook
    // The useEffect for isOpen will then update currentSettings
    // For immediate UI update of current form, also explicitly reset current settings
    const allGlobalDefaults: SettingsStore = {}
    availableElementTypes.forEach((type) => { // Add parentheses for arrow function arg
      allGlobalDefaults[type] = getSettingsForType(type)
    })
    setCurrentSettings(allGlobalDefaults)
    if (availableElementTypes.length > 0) {
      setSelectedElementTypeForEditing(availableElementTypes[0]) // Corrected indentation
    }
  }

  if (!isOpen) {
    return null
  }

  // Ensure propertiesForSelectedType is not undefined/null before trying to get its keys
  // const currentTypeProperties = propertiesForSelectedType || {} // Line 329: Removed unused variable

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="max-w-[90vw] md:max-w-[70vw] lg:max-w-[60vw] xl:max-w-[900px] h-[80vh] flex flex-col p-0 z-50">
        <DialogHeader className="p-6 border-b">
          <DialogTitle>
            {selectedCategory === 'elements' && 'Default Element Settings'}
            {selectedCategory === 'layout' && 'Layout & Interface Settings'}
            {selectedCategory === 'toolbar' && 'Toolbar Visibility Settings'}
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-1 overflow-hidden">
          {' '}
          {/* Main two-column container */}
          {/* Left Column: Categories and Element Type List */}
          <div className="w-1/3 md:w-1/4 border-r">
            <ScrollArea className="h-full p-3">
              {/* Category Selection */}
              <div className="mb-4">
                <nav className="flex flex-col gap-1">
                  {settingsCategories.map(category => (
                    <Button
                      key={category.id}
                      variant={selectedCategory === category.id ? 'secondary' : 'ghost'}
                      className="w-full justify-start text-left h-auto py-2 px-3"
                      onClick={() => setSelectedCategory(category.id as 'elements' | 'layout' | 'toolbar')}
                    >
                      <span className="mr-2">{category.icon}</span>
                      {category.name}
                    </Button>
                  ))}
                </nav>
              </div>

              {/* Element Type List (only show when elements category is selected) */}
              {selectedCategory === 'elements' && (
                <>
                  <div className="border-t pt-3 mt-3">
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">Element Types</h4>
                    <nav className="flex flex-col gap-1">
                      {availableElementTypes.map(elType => (
                        <Button
                          key={elType}
                          variant={selectedElementTypeForEditing === elType ? 'secondary' : 'ghost'}
                          className="w-full justify-start text-left h-auto py-2 px-3"
                          onClick={() => setSelectedElementTypeForEditing(elType)}
                        >
                          {getElementDisplayName(elType)}
                        </Button>
                      ))}
                    </nav>
                  </div>
                </>
              )}
            </ScrollArea>
          </div>

          {/* Right Column: Settings Content */}
          <div className="w-2/3 md:w-3/4 overflow-y-auto">
            <ScrollArea className="h-full p-6">
              {selectedCategory === 'elements' && selectedElementTypeForEditing != null && propertiesForSelectedType != null && (
                <div>
                  <h3 className="text-xl font-semibold mb-6">
                    {getElementDisplayName(selectedElementTypeForEditing)}
                  </h3>
                  <div className="space-y-3">
                    {Boolean(selectedElementTypeForEditing != null && propertiesForSelectedType != null) && (() => {
                      // Get supported properties for this element type
                      const supportedProps = getSupportedProperties(selectedElementTypeForEditing)

                      // Filter properties to only show supported ones
                      const filteredEntries = Object.entries(propertiesForSelectedType).filter(([prop]) =>
                        supportedProps.includes(prop),
                      )

                      if (filteredEntries.length === 0) {
                        return (
                          <p className="text-xs text-muted-foreground">
                            No configurable properties for this element type.
                          </p>
                        )
                      }

                      return filteredEntries.map(([prop, value]) => {
                        // Skip 'points' for direct editing if it's an array of objects (complex)
                        if (prop === 'points' && Array.isArray(value) && value.length > 0 && typeof value[0] === 'object') {
                          return null
                        }
                        return renderPropertyField(selectedElementTypeForEditing, prop as keyof InitialElementProperties, value)
                      })
                    })()}
                  </div>
                </div>
              )}

              {selectedCategory === 'layout' && renderLayoutSettings()}
              {selectedCategory === 'toolbar' && renderToolbarSettings()}
            </ScrollArea>
          </div>
        </div>

        <DialogFooter className="p-6 border-t flex justify-end">
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={onClose} className="mr-2">
              Cancel
            </Button>
          </DialogClose>
          <Button type="button" onClick={handleResetAllTypesToDefaults} className="mr-2">
            Reset All to Defaults
          </Button>
          <Button type="button" onClick={_handleSaveChanges}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default SettingsPanel
