/**
 * Validation Service Module
 *
 * This module exports the ValidationService and related utility functions.
 * It also provides a convenience function `getValidationService` to retrieve
 * an instance of the ValidationService.
 *
 * @module services/core/validation
 */

// Validation utility functions are available from @/lib/utils/validationUtils

// 导出验证服务注册函数
// 导入必要的类型和服务
import { ElementValidator, validator as elementValidatorSingleton } from '@/core/validator'
import { getService, ServiceId } from '@/services/core/registry'

export * from './registry'

/**
 * 获取验证服务实例
 *
 * 此函数首先尝试从服务注册表获取验证服务，
 * 如果未注册则返回直接从@/core/validator导出的单例。
 *
 * @returns 验证服务实例
 */
export function getValidationService(): ElementValidator {
  try {
    // 尝试从注册表获取服务
    return getService<ElementValidator>(ServiceId.ValidationService)
  }
  catch (e) {
    console.warn('验证服务未在注册表中找到，返回直接的单例实例。', e)
    // Initialize validator modules before returning the singleton instance
    ElementValidator.initializeValidatorModules()
    // 回退到直接导出的单例
    return elementValidatorSingleton
  }
}
