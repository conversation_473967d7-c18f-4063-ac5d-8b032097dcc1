/**
 * Tutorial Service
 *
 * Service for managing tutorial functionality using driver.js.
 * Handles tutorial state, progression, and completion tracking.
 */

import type { Driver } from 'driver.js'
import type { TutorialEvent, TutorialService, TutorialState } from '@/types/tutorial'
import { driver } from 'driver.js'
import { tutorialConfig } from '@/config/tutorialConfig'
import { appEventBus } from '@/services/core/event-bus'

/**
 * Tutorial service implementation
 */
class TutorialServiceImpl implements TutorialService {
  private driver: Driver | null = null
  private state: TutorialState = {
    activeTutorial: null,
    currentStep: 0,
    isRunning: false,
    completedTutorials: [],
    showHints: true,
  }

  constructor() {
    this.loadCompletedTutorials()
    this.initializeDriver()
  }

  /**
   * Initialize the driver.js instance with default configuration
   */
  private initializeDriver(): void {
    this.driver = driver({
      showProgress: tutorialConfig.defaults.showProgress,
      allowClose: tutorialConfig.defaults.allowClose,
      overlayOpacity: tutorialConfig.defaults.overlayOpacity,
      animate: tutorialConfig.defaults.animationDuration ? tutorialConfig.defaults.animationDuration > 0 : true,
      // Global event handlers
      onDestroyed: () => {
        this.handleTutorialStopped()
      },
      onDeselected: (_element, _step, _options) => {
        this.state.currentStep = _options.state.activeIndex || 0
        this.emitEvent({
          type: 'tutorial:step:changed',
          payload: {
            moduleId: this.state.activeTutorial || '',
            stepIndex: this.state.currentStep,
          },
        })
      },
    })
  }

  /**
   * Start a specific tutorial module
   */
  startTutorial = (moduleId: string): void => {
    const module = tutorialConfig.modules[moduleId]
    if (!module) {
      console.error(`Tutorial module '${moduleId}' not found`)
      return
    }

    // Check prerequisites
    if (module.prerequisites) {
      const unmetPrerequisites = module.prerequisites.filter(
        prereq => !this.isCompleted(prereq),
      )
      if (unmetPrerequisites.length > 0) {
        console.warn(`Prerequisites not met for tutorial '${moduleId}': ${unmetPrerequisites.join(', ')}`)
        // Could show a dialog here asking if user wants to start prerequisites first
      }
    }

    // Stop any running tutorial
    if (this.state.isRunning) {
      this.stopTutorial()
    }

    // Update state
    this.state.activeTutorial = moduleId
    this.state.currentStep = 0
    this.state.isRunning = true

    // Configure driver with module steps
    if (this.driver) {
      // Prepare steps with proper driver.js configuration format
      const steps = module.steps.map((step, index) => {
        const isLastStep = index === module.steps.length - 1
        return {
          element: step.element,
          popover: {
            title: step.title,
            description: step.description,
            side: (step.position === 'auto' ? 'bottom' : step.position) || 'bottom',
            showProgress: step.showProgress !== false,
            nextBtnText: isLastStep ? 'Done!' : 'Next →',
            prevBtnText: '← Previous',
            doneBtnText: 'Done!',
            progressText: `Step ${index + 1} of ${module.steps.length}`,
          },
          onHighlighted: () => {
            this.state.currentStep = index
          },
        }
      })

      // Ensure panels are open for app-overview tutorial
      if (moduleId === 'app-overview') {
        // Emit events to ensure panels are open for the tutorial
        appEventBus.emit({
          type: 'tutorial:ensure-panels-open',
          payload: {},
          timestamp: Date.now()
        })
      }

      // Start the tutorial with steps
      try {
        this.driver.setConfig({
          steps,
          onDestroyed: () => {
            this.handleTutorialCompleted(moduleId)
          },
        })

        this.driver.drive()
        this.emitEvent({
          type: 'tutorial:started',
          payload: { moduleId },
        })
      }
      catch (error) {
        console.error('Error starting tutorial:', error)
      }
    }
  }

  /**
   * Stop the current tutorial
   */
  stopTutorial = (): void => {
    if (this.driver && this.state.isRunning) {
      this.driver.destroy()
    }
    this.handleTutorialStopped()
  }

  /**
   * Go to the next step
   */
  nextStep = (): void => {
    if (this.driver && this.state.isRunning) {
      this.driver.moveNext()
    }
  }

  /**
   * Go to the previous step
   */
  previousStep = (): void => {
    if (this.driver && this.state.isRunning) {
      this.driver.movePrevious()
    }
  }

  /**
   * Skip the current tutorial
   */
  skipTutorial = (): void => {
    if (this.state.activeTutorial) {
      this.emitEvent({
        type: 'tutorial:skipped',
        payload: { moduleId: this.state.activeTutorial },
      })
    }
    this.stopTutorial()
  }

  /**
   * Mark a tutorial as completed
   */
  markCompleted = (moduleId: string): void => {
    if (!this.state.completedTutorials.includes(moduleId)) {
      this.state.completedTutorials.push(moduleId)
      this.saveCompletedTutorials()
    }
  }

  /**
   * Check if a tutorial is completed
   */
  isCompleted = (moduleId: string): boolean => {
    return this.state.completedTutorials.includes(moduleId)
  }

  /**
   * Get current tutorial state
   */
  getState = (): TutorialState => {
    return { ...this.state }
  }

  /**
   * Reset all tutorial progress
   */
  resetAll = (): void => {
    this.state.completedTutorials = []
    this.saveCompletedTutorials()
    this.stopTutorial()
  }

  /**
   * Handle tutorial completion
   */
  private handleTutorialCompleted = (moduleId: string): void => {
    this.markCompleted(moduleId)
    this.handleTutorialStopped()
    this.emitEvent({
      type: 'tutorial:completed',
      payload: { moduleId },
    })
  }

  /**
   * Handle tutorial stopped
   */
  private handleTutorialStopped = (): void => {
    const moduleId = this.state.activeTutorial
    this.state.activeTutorial = null
    this.state.currentStep = 0
    this.state.isRunning = false
    if (moduleId) {
      this.emitEvent({
        type: 'tutorial:stopped',
        payload: { moduleId },
      })
    }
  }

  /**
   * Load completed tutorials from storage
   */
  private loadCompletedTutorials(): void {
    if (!tutorialConfig.completion.enabled)
      return

    try {
      const stored = localStorage.getItem(tutorialConfig.completion.storageKey)
      if (stored) {
        this.state.completedTutorials = JSON.parse(stored)
      }
    }
    catch (error) {
      console.error('Failed to load completed tutorials:', error)
    }
  }

  /**
   * Save completed tutorials to storage
   */
  private saveCompletedTutorials(): void {
    if (!tutorialConfig.completion.enabled)
      return

    try {
      localStorage.setItem(
        tutorialConfig.completion.storageKey,
        JSON.stringify(this.state.completedTutorials),
      )
    }
    catch (error) {
      console.error('Failed to save completed tutorials:', error)
    }
  }

  /**
   * Emit tutorial event
   */
  private emitEvent(event: TutorialEvent): void {
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('tutorial:event', {
        detail: event,
      }))
    }
    appEventBus.emit({
      type: 'tutorial:event',
      payload: event,
      timestamp: Date.now()
    })
  }
}

// Export singleton instance
export const tutorialService = new TutorialServiceImpl()
