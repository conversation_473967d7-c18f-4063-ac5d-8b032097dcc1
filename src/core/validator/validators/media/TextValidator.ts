import type { IBaseShapeValidator } from '../common/validatorBase'
import type { ValidatableShape, ValidationError } from '@/types/core/validator/validator-interface'
import { ElementType } from '@/types/core/elementDefinitions'
import { BaseShapeValidator } from '../common/validatorBase'
// import { ValidationErrorCode } from '@/types/core/validator/error-codes'; // For specific error codes if needed

export default class TextValidator extends BaseShapeValidator implements IBaseShapeValidator {
  protected validateSpecific(shape: ValidatableShape): ValidationError[] {
    if (shape.type !== ElementType.TEXT) {
      console.warn(`[TextValidator] validateSpecific called for non-TEXT type: ${shape.type}`)
      return []
    }
    const errors: ValidationError[] = []

    // Placeholder for actual validation logic
    // Example: Validate that 'text' property is a string
    // const textValue = shape.properties?.text;
    // if (typeof textValue !== 'string') {
    //     errors.push({
    //         code: ValidationErrorCode.INVALID_PROPERTY_TYPE, // Example error code
    //         message: `Text property must be a string, received ${typeof textValue}`,
    //         path: 'properties.text',
    //         value: textValue
    //     });
    // }

    // Example: Validate that 'fontSize' property is a positive number
    // const fontSizeValue = shape.properties?.fontSize;
    // if (typeof fontSizeValue !== 'number' || fontSizeValue <= 0) {
    //     errors.push({
    //         code: ValidationErrorCode.INVALID_PROPERTY_VALUE, // Example error code
    //         message: `FontSize property must be a positive number, received ${fontSizeValue}`,
    //         path: 'properties.fontSize',
    //         value: fontSizeValue
    //     });
    // }

    return errors
  }

  // Optionally override applySpecificRules if specific business logic for Text is needed
  // protected applySpecificRules(shape: ValidatableShape): ValidationError[] {
  //     const errors: ValidationError[] = super.applySpecificRules(shape); // Call base if it has shared rules
  //     // Add text-specific business rules, e.g., max length, forbidden characters
  //     return errors;
  // }
}
