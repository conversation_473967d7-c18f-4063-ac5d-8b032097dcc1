/**
 * Contains validation logic specific to Quadratic Bezier curves.
 */

import type { ValidatableShape, ValidationError } from '@/types/core/validator/validator-interface' // Corrected
import { ElementType } from '@/types/core/elementDefinitions' // Import ElementType only
import { ValidationErrorCode } from '@/types/core/validator/error-codes' // Corrected
import { isValidPoint } from '../common/validationUtils'
import { BaseShapeValidator } from '../common/validatorBase'

/**
 * Concrete validator implementation for Quadratic Bezier curves.
 *
 * @extends BaseShapeValidator
 */
export class QuadraticValidator extends BaseShapeValidator {
  /**
   * Validates properties specific to a Quadratic Bezier curve element data structure.
   *
   * @param shape - The shape to validate.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected validateSpecific(shape: ValidatableShape): ValidationError[] {
    const errors: ValidationError[] = []

    if (shape.type !== ElementType.QUADRATIC) {
      errors.push({
        code: ValidationErrorCode.INVALID_SHAPE_TYPE,
        message: `Internal Error: QuadraticValidator received shape type ${shape.type}`,
        path: 'type',
      })
      return errors
    }

    const props = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined || {}
    const startPoint = props.start as { x: number, y: number } | undefined
    const controlPoint = props.control as { x: number, y: number } | undefined
    const endPoint = props.end as { x: number, y: number } | undefined

    // Validate start point
    if (startPoint == null || !isValidPoint(startPoint)) {
      errors.push({
        code: ValidationErrorCode.INVALID_POINT,
        message: `Quadratic curve requires a valid 'start' point in properties. Received: ${JSON.stringify(startPoint)}`,
        path: 'properties.start',
        value: startPoint,
      })
    }

    // Validate control point
    if (controlPoint == null || !isValidPoint(controlPoint)) {
      errors.push({
        code: ValidationErrorCode.INVALID_POINT,
        message: `Quadratic curve requires a valid 'control' point in properties. Received: ${JSON.stringify(controlPoint)}`,
        path: 'properties.control',
        value: controlPoint,
      })
    }

    // Validate end point
    if (endPoint == null || !isValidPoint(endPoint)) {
      errors.push({
        code: ValidationErrorCode.INVALID_POINT,
        message: `Quadratic curve requires a valid 'end' point in properties. Received: ${JSON.stringify(endPoint)}`,
        path: 'properties.end',
        value: endPoint,
      })
    }

    return errors
  }

  /**
   * Applies quadratic-specific business rules.
   *
   * @param _shape - The shape to validate against business rules.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected applySpecificRules(_shape: ValidatableShape): ValidationError[] {
    // No quadratic-specific business rules defined currently beyond common ones.
    return []
  }
}
