// Import types needed for generic parameters or properties
import type { ComputeResult } from '@/types/core/element/compute'

import type { Element } from '@/types/core/element/element'

import type { ValidationResult as CoreValidationResult } from '@/types/core/element/validator'
// Import the specific service result interfaces and aliases
import type {
  ComputeServiceResult,
  FactoryResult, // Alias
  FactoryServiceResult,
  ValidatorResult, // Alias
  ValidatorServiceResult,
} from '@/types/services/result-types'
import { describe, expect, it } from 'vitest'

describe('service Result Types', () => {
  it('should allow declaration using ComputeServiceResult interface', () => {
    const computeResult: ComputeServiceResult<number> | undefined = undefined
    expect(computeResult).toBeUndefined()

    // Example usage
    const exampleResult: ComputeServiceResult<string> = {
      success: true,
      data: {
        operation: 'AREA',
        result: '100 sq units',
        metadata: { executionTime: 5 },
      } as ComputeResult<string>,
      elementId: 'elem-1',
      operation: 'AREA',
      timestamp: Date.now(),
    }
    expect(exampleResult.success).toBe(true)
    expect(exampleResult.data?.result).toBe('100 sq units')
    expect(exampleResult.operation).toBe('AREA')
  })

  it('should allow declaration using FactoryServiceResult interface and alias', () => {
    const factoryResult: FactoryServiceResult | undefined = undefined
    expect(factoryResult).toBeUndefined()
    let factoryAlias: FactoryResult | undefined
    expect(factoryAlias).toBeUndefined()

    // Example usage
    const exampleResult: FactoryServiceResult = {
      success: true,
      data: { id: 'new-rect' } as Element, // Mock Element
      ElementType: 'rectangle',
      elementId: 'new-rect',
      timestamp: Date.now(),
    }
    expect(exampleResult.success).toBe(true)
    expect(exampleResult.data?.id).toBe('new-rect')
    expect(exampleResult.ElementType).toBe('rectangle')

    factoryAlias = exampleResult // Check alias compatibility
    expect(factoryAlias.elementId).toBe('new-rect')
  })

  it('should allow declaration using ValidatorServiceResult interface and alias', () => {
    const validatorResult: ValidatorServiceResult | undefined = undefined
    expect(validatorResult).toBeUndefined()
    let validatorAlias: ValidatorResult | undefined
    expect(validatorAlias).toBeUndefined()

    // Example usage
    const exampleResult: ValidatorServiceResult = {
      success: false,
      error: 'Validation failed',
      data: {
        valid: false,
        errors: [{ code: 'SIZE_INVALID', message: 'Too small' }],
      } as CoreValidationResult,
      elementId: 'elem-2',
      timestamp: Date.now(),
    }
    expect(exampleResult.success).toBe(false)
    expect(exampleResult.data?.valid).toBe(false)
    expect(exampleResult.data?.errors?.[0].code).toBe('SIZE_INVALID')
    expect(exampleResult.elementId).toBe('elem-2')

    validatorAlias = exampleResult // Check alias compatibility
    expect(validatorAlias.error).toBe('Validation failed')
  })
})
