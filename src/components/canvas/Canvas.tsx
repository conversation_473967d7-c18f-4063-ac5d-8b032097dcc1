/**
 * Canvas Component
 *
 * The primary drawing surface and interaction area for the design application.
 * This component serves as the central workspace where users can view, create,
 * and manipulate design elements with comprehensive interaction capabilities.
 *
 * Features:
 * - Element rendering with layer-based z-ordering
 * - Interactive zoom and pan controls
 * - Grid display with responsive scaling
 * - Multi-selection with marquee selection
 * - Drag-and-drop asset placement
 * - Path drawing mode support
 * - Real-time coordinate transformation
 * - Responsive canvas sizing
 * - Scale bar with unit conversion
 * - Context-aware interaction modes
 *
 * Interaction Modes:
 * - Pan Mode: Navigate the canvas by dragging
 * - Draw Mode: Create paths and shapes interactively
 * - Select Mode: Select and manipulate existing elements
 *
 * Coordinate Systems:
 * - Screen coordinates: Browser viewport pixels
 * - SVG coordinates: SVG element coordinate space
 * - World coordinates: Design space with physical units
 *
 * @example
 * ```tsx
 * <Canvas
 *   elements={designElements}
 *   selectedElementIds={selectedIds}
 *   showGrid={true}
 *   zoom={1.0}
 *   pan={{ x: 0, y: 0 }}
 *   onElementSelect={handleSelection}
 *   onElementAdd={handleAddElement}
 *   onMouseDown={handleMouseDown}
 * />
 * ```
 */

// Internal Type Imports
import type {
  CanvasDimensionChangeCallback,
  CanvasMouseEvent,
  PointData as Point,
} from '@/types'
import type { ElementType, ShapeElement } from '@/types/core/elementDefinitions'
// import type { ZLevel } from '@/types/core/layerPanelTypes' // Not directly used after changes

// External Libraries
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'

// Hooks & Utilities & Core
import { useCoordinateSystem } from '@/hooks/canvas/useCoordinateSystem'
import { calculateScaleBarInfo } from '@/lib/utils/canvas/scaleBarUtils'
import {
  extractKeyboardModifiers,
  handleMarqueeSelection,
  shouldClearSelectionOnBackgroundClick,
} from '@/lib/utils/canvas/selectionUtils'
import { ShapeElementUtils } from '@/lib/utils/element/shapeElementUtils'
import { BoundingBoxClass } from '@/lib/utils/geometry/BoundingBoxClass'

// Store Imports
import { useLayerStore } from '@/store/layerStore'

// Internal Components
import CanvasScaleBar from './CanvasScaleBar'
import PathPreview from './PathPreview'
import { ShapeRenderer } from './ShapeRenderer'

/** Default pan position for canvas initialization */
const INITIAL_PAN: Point = { x: 0, y: 0 }
/** Default zoom level for canvas initialization */
const INITIAL_ZOOM = 1.0

/**
 * Calculates the bounding box for marquee selection.
 *
 * This function computes the correct rectangle coordinates for a selection box
 * based on the drag start point and current mouse position, ensuring the box
 * is always drawn with positive width and height regardless of drag direction.
 *
 * @param dragStartPoint - The point where the drag operation started
 * @param currentMousePoint - The current mouse position
 * @returns Rectangle coordinates with x, y, width, and height
 */
function calculateMarqueeBox(
  dragStartPoint: Point,
  currentMousePoint: Point,
): { x: number, y: number, width: number, height: number } {
  const x = Math.min(dragStartPoint.x, currentMousePoint.x)
  const y = Math.min(dragStartPoint.y, currentMousePoint.y)
  const width = Math.abs(dragStartPoint.x - currentMousePoint.x)
  const height = Math.abs(dragStartPoint.y - currentMousePoint.y)
  return { x, y, width, height }
}

/**
 * Props interface for the Canvas component
 */
export interface CanvasProps {
  /** Array of design elements to render on the canvas */
  elements: ShapeElement[]
  /** IDs of currently selected elements */
  selectedElementIds: string[]
  /** Whether the canvas is in pan mode for navigation */
  isPanMode?: boolean
  /** Whether the canvas is in drawing mode for creating paths */
  isDrawMode?: boolean
  /** Whether to display the grid overlay */
  showGrid?: boolean
  /** Size of grid cells in world units */
  gridSize?: number
  /** Current zoom level (1.0 = 100%) */
  zoom?: number
  /** Current pan offset in screen coordinates */
  pan?: Point
  /** Asset type primed for single-click placement */
  primedAssetTypeForCanvasClick?: ElementType | null
  /** Current state of path drawing operation */
  pathDrawState?: {
    /** Whether a path is currently being drawn */
    isDrawing: boolean
    /** Type of path being drawn */
    pathType: ElementType | null
    /** Starting point of the path */
    startPoint: Point | null
    /** Current cursor position */
    currentPoint: Point | null
    /** All points for multi-point paths */
    allPoints?: Point[]
    /** Number of clicks completed */
    clickCount?: number
  }
  /** Callback for mouse down events */
  onMouseDown?: (event: CanvasMouseEvent) => void
  /** Callback for mouse move events */
  onMouseMove?: (event: CanvasMouseEvent) => void
  /** Callback for mouse up events */
  onMouseUp?: (event: CanvasMouseEvent) => void
  /** Callback for drag over events */
  onDragOver?: (event: React.DragEvent<Element>) => void
  /** Callback for drop events */
  onDrop?: (event: CanvasMouseEvent) => void
  /** Callback for adding new elements */
  onElementAdd?: (
    asset: { elementType: string, properties?: Record<string, unknown>, id?: string },
    position: Point
  ) => void
  /** Callback for element selection changes */
  onElementSelect?: (
    elementIdOrIds: string | string[] | null,
    isMultiSelect?: boolean,
    selectionMode?: 'replace' | 'toggle' | 'clear' | 'add'
  ) => void
  /** Callback for world coordinate mouse movement */
  onWorldMouseMove?: (worldPoint: Point) => void
  /** Callback for canvas dimension changes */
  onDimensionsChange?: CanvasDimensionChangeCallback
  /** Whether the property sidebar is open */
  isPropertySidebarOpen?: boolean
  /** Current selection box coordinates */
  selectionBox?: { x: number, y: number, width: number, height: number } | null
  /** Callback for zoom level changes */
  onZoomChange?: (zoom: number) => void
  /** Callback for pan offset changes */
  onPanChange?: (pan: Point) => void
  /** Callback for wheel events (zoom/scroll) */
  onWheel?: (event: React.WheelEvent<SVGSVGElement>) => void
  /** Callback to start pan operation */
  doStartPan?: (event: React.MouseEvent<SVGElement>) => void
  /** Callback to continue pan operation */
  doContinuePan?: (event: React.MouseEvent<SVGElement>) => void
  /** Callback to end pan operation */
  doEndPan?: () => void
  /** Callback for shape drop events */
  onShapeDrop?: (
    event: React.DragEvent<SVGSVGElement>,
    droppedAssetData: { elementType: string, properties?: Record<string, unknown>, name?: string, id?: string } | null
  ) => void
  /** Whether the bottom asset drawer sheet is open */
  sheetOpen?: boolean
}

/** Empty function used as default callback to avoid null checks */
function EMPTY_FUNCTION() {}

// Type definitions for environment checking
interface ImportMetaWithEnv {
  env?: {
    DEV?: boolean
    NODE_ENV?: string
  }
}

interface WindowWithDev {
  __DEV__?: boolean
}

// Debug utility function to replace console.log in production
function debug(message: string, ...args: unknown[]) {
  // Use environment variable check without require
  try {
    // Check if we're in development mode using various methods
    // Use import.meta.env for Vite environments, fallback to NODE_ENV check
    const hasImportMeta = typeof import.meta !== 'undefined'
    const importMetaWithEnv = hasImportMeta ? (import.meta as ImportMetaWithEnv) : null
    const hasImportMetaEnv = importMetaWithEnv?.env !== undefined
    const isViteDev = hasImportMetaEnv && importMetaWithEnv?.env?.DEV === true

    // Skip process-based environment detection to avoid ESLint node/prefer-global/process rule
    // In browser environments, we rely on import.meta.env and window.__DEV__ instead
    const isNodeDev = false

    const hasWindow = typeof window !== 'undefined'
    const windowWithDev = hasWindow ? (window as WindowWithDev) : null
    const hasWindowDev = windowWithDev?.__DEV__ !== undefined && typeof windowWithDev.__DEV__ === 'boolean'
    const isWindowDev = hasWindowDev && windowWithDev?.__DEV__ === true

    const isDevelopment = isViteDev || isNodeDev || isWindowDev

    if (isDevelopment) {
      console.warn(`[Canvas Debug] ${message}`, ...args)
    }
  }
  catch {
    // Fallback for environments where process is not available
    // Only log in development mode
    const hasWindow = typeof window !== 'undefined'
    const windowWithDev = hasWindow ? (window as WindowWithDev) : null
    const hasWindowDev = windowWithDev?.__DEV__ !== undefined && typeof windowWithDev.__DEV__ === 'boolean'
    const isWindowDev = hasWindowDev && windowWithDev?.__DEV__ === true

    if (isWindowDev) {
      console.warn(`[Canvas Debug] ${message}`, ...args)
    }
  }
}

/**
 * Formats a dimension value in millimeters to a human-readable string with appropriate units.
 *
 * This function automatically selects the most appropriate unit (mm, cm, or m) based on
 * the magnitude of the value to provide the most readable representation.
 *
 * @param valueInMM - The dimension value in millimeters
 * @returns Formatted string with value and unit (e.g., "150 mm", "1.5 m")
 */
function formatDimensionForDisplay(valueInMM: number): string {
  const CM_IN_MM = 10
  const M_IN_MM = 1000

  if (valueInMM >= M_IN_MM) {
    return `${(valueInMM / M_IN_MM).toFixed(2)} m`
  }
  if (valueInMM >= CM_IN_MM) {
    return `${(valueInMM / CM_IN_MM).toFixed(1)} cm`
  }
  return `${valueInMM.toFixed(0)} mm`
}

/**
 * Conversion factor from pixels to millimeters for the canvas coordinate system.
 *
 * This value determines the scale relationship between screen pixels and real-world
 * measurements. A value of 1.0 means 1 pixel = 1 millimeter, which provides a
 * reasonable scale for interior design applications where elements need to be
 * clearly visible and manipulable.
 *
 * Previous value of 0.08 caused display issues where a 200px square showed as 2500mm.
 * With 1.0, a 200px square will display as 200mm, which is more reasonable.
 */
export const CANVAS_PIXELS_PER_MM = 1.0

const Canvas: React.FC<CanvasProps> = React.memo(
  ({
    elements,
    selectedElementIds,
    isPanMode = false,
    isDrawMode = false,
    showGrid = true,
    gridSize = 50,
    zoom = INITIAL_ZOOM,
    pan = INITIAL_PAN,
    primedAssetTypeForCanvasClick: _primedAssetTypeForCanvasClick = null,
    pathDrawState: pathDrawStateProp,
    onMouseDown: onMouseDownProp = EMPTY_FUNCTION,
    onMouseMove: onMouseMoveProp = EMPTY_FUNCTION,
    onMouseUp: onMouseUpProp = EMPTY_FUNCTION,
    onDragOver = EMPTY_FUNCTION,
    onDrop: onDropProp = EMPTY_FUNCTION,
    onElementAdd = EMPTY_FUNCTION,
    onElementSelect = EMPTY_FUNCTION,
    onWorldMouseMove: _onWorldMouseMove,
    onDimensionsChange,
    isPropertySidebarOpen,
    onZoomChange: _onZoomChange,
    onPanChange: _onPanChange,
    onWheel,
    doStartPan,
    doContinuePan,
    doEndPan,
    onShapeDrop,
    sheetOpen,
  }: CanvasProps) => {
    const svgRef = useRef<SVGSVGElement | null>(null)
    const canvasWrapperRef = useRef<HTMLDivElement | null>(null)
    const mainGroupRef = useRef<SVGGElement | null>(null)
    const shapesLayerRef = useRef<SVGGElement | null>(null)
    const interactionLayerRef = useRef<SVGGElement | null>(null)
    const handlesLayerRef = useRef<SVGGElement | null>(null)

    const [currentSelectionBox, setCurrentSelectionBox] = useState<{ x: number, y: number, width: number, height: number } | null>(null)
    const [dragStartWorld, setDragStartWorld] = useState<Point | null>(null)

    // 路径绘制状态现在从props传入

    // --- NEW: Responsive canvas size ---
    const [canvasWidth, setCanvasWidth] = useState<number>(window.innerWidth)
    const [canvasHeight, setCanvasHeight] = useState<number>(window.innerHeight)

    const updateSize = useCallback(() => {
      if (canvasWrapperRef.current) {
        const rect = canvasWrapperRef.current.getBoundingClientRect()
        return { width: rect.width, height: rect.height }
      }
      else {
        return { width: window.innerWidth, height: window.innerHeight }
      }
    }, [])

    useEffect(() => {
      const initialSize = updateSize()

      // Use a flag to track if component is still mounted
      let isMounted = true

      // Schedule initial state updates using requestAnimationFrame to avoid direct setState in useEffect
      requestAnimationFrame(() => {
        if (isMounted) {
          setCanvasWidth(prevWidth => initialSize.width !== prevWidth ? initialSize.width : prevWidth)
          setCanvasHeight(prevHeight => initialSize.height !== prevHeight ? initialSize.height : prevHeight)
        }
      })

      const timeoutId = setTimeout(() => {
        if (isMounted) {
          const delayedSize = updateSize()
          requestAnimationFrame(() => {
            if (isMounted) {
              setCanvasWidth(prevWidth => delayedSize.width !== prevWidth ? delayedSize.width : prevWidth)
              setCanvasHeight(prevHeight => delayedSize.height !== prevHeight ? delayedSize.height : prevHeight)
            }
          })
        }
      }, 0) // 关键：初始渲染后再测量一次，确保布局完成

      // Use ResizeObserver for container
      let observer: ResizeObserver | null = null
      const currentElement = canvasWrapperRef.current
      if (currentElement && typeof ResizeObserver !== 'undefined') {
        observer = new ResizeObserver(() => {
          const newSize = updateSize()
          // Use requestAnimationFrame to defer state updates
          requestAnimationFrame(() => {
            setCanvasWidth(prevWidth => newSize.width !== prevWidth ? newSize.width : prevWidth)
            setCanvasHeight(prevHeight => newSize.height !== prevHeight ? newSize.height : prevHeight)
          })
        })
        observer.observe(currentElement)
      }
      else {
        const handleResize = () => {
          const newSize = updateSize()
          // Use requestAnimationFrame to defer state updates
          requestAnimationFrame(() => {
            setCanvasWidth(prevWidth => newSize.width !== prevWidth ? newSize.width : prevWidth)
            setCanvasHeight(prevHeight => newSize.height !== prevHeight ? newSize.height : prevHeight)
          })
        }
        window.addEventListener('resize', handleResize)
        return () => {
          clearTimeout(timeoutId)
          window.removeEventListener('resize', handleResize)
        }
      }
      return () => {
        isMounted = false
        clearTimeout(timeoutId)
        // Use the captured element reference for cleanup
        if (observer !== null && currentElement !== null) {
          observer.unobserve(currentElement)
        }
      }
    }, [updateSize])
    // --- END Responsive canvas size ---

    // Get Z-Level data from layerStore
    const layerModulesFromStore = useLayerStore(state => state.modules) // Get all modules

    const { activeZLevelIds, zLevelZIndexMap } = useMemo(() => {
      const activeIds = new Set<string>()
      const zIndexMap = new Map<string, number>()
      layerModulesFromStore.forEach((module) => {
        module.steps.forEach((step) => {
          step.zLevels.forEach((zLevel) => {
            zIndexMap.set(zLevel.id, zLevel.zIndex) // Populate zIndexMap for all layers
            if (zLevel.active === true) {
              activeIds.add(zLevel.id) // Add to active set if zLevel.active is true
            }
          })
        })
      })
      // Debug: Derived activeZLevelIds
      return { activeZLevelIds: activeIds, zLevelZIndexMap: zIndexMap }
    }, [layerModulesFromStore])

    const visibleAndSortedElements = useMemo(() => {
      const visibleElements = elements.filter((element: ShapeElement) => {
        if (element.zLevelId === null || element.zLevelId === undefined || element.zLevelId === '') {
          // console.log('[Canvas Filter] Element lacks zLevelId:', element.id);
          return false
        }
        const isActive = activeZLevelIds.has(element.zLevelId)
        // console.log(`[Canvas Filter] Element ID: ${element.id}, zLevelId: ${element.zLevelId}, Is In Active Set: ${isActive}`);

        // TEMPORARY FIX: Allow pattern test elements to bypass layer filtering
        if (element.id !== undefined && element.id.includes('test-square') && element.pattern !== undefined) {
          // Debug: Allowing pattern test element to bypass layer filtering
          return true
        }

        // TEMPORARY FIX: Allow TEXT elements to bypass layer filtering for testing
        if (element.type === 'TEXT') {
          // Debug: Allowing TEXT element to bypass layer filtering
          return true
        }

        return isActive
      })

      const sorted = visibleElements.sort((a: ShapeElement, b: ShapeElement) => {
        // 首先按zLevelId的zIndex比较
        const zIndexA = (a.zLevelId !== null && a.zLevelId !== undefined && a.zLevelId !== '') ? zLevelZIndexMap.get(a.zLevelId) ?? Infinity : Infinity
        const zIndexB = (b.zLevelId !== null && b.zLevelId !== undefined && b.zLevelId !== '') ? zLevelZIndexMap.get(b.zLevelId) ?? Infinity : Infinity

        if (zIndexA !== zIndexB) {
          return zIndexA - zIndexB
        }

        // 如果zLevelId的zIndex相同，则按intraLayerZIndex比较
        // 确保intraLayerZIndex为数字，默认值为0
        const intraLayerZIndexA = typeof a.intraLayerZIndex === 'number' ? a.intraLayerZIndex : 0
        const intraLayerZIndexB = typeof b.intraLayerZIndex === 'number' ? b.intraLayerZIndex : 0

        // Debug: Compare intraLayerZIndex for different elements

        return intraLayerZIndexA - intraLayerZIndexB
      })

      // Debug: Log rendering order and count
      return sorted
    }, [elements, activeZLevelIds, zLevelZIndexMap])

    const { createMouseEventDetails } = useCoordinateSystem({
      svgRef,
      pan,
      zoom,
    })

    const isPanningRef = useRef<boolean>(false)

    // 用于跟踪document级别的事件监听器
    const documentEventListenersRef = useRef<{
      mousemove?: (e: MouseEvent) => void
      mouseup?: (e: MouseEvent) => void
    }>({})

    // 坐标转换函数：将document事件转换为SVG坐标
    const convertDocumentEventToSvgCoordinates = useCallback((event: MouseEvent): CanvasMouseEvent | null => {
      if (svgRef.current === null) {
        debug('convertDocumentEventToSvgCoordinates: svgRef.current is null')
        return null
      }

      try {
        const svgRect = svgRef.current.getBoundingClientRect()
        const svgX = event.clientX - svgRect.left
        const svgY = event.clientY - svgRect.top

        // 检查鼠标是否在Canvas区域内
        const isInsideCanvas = svgX >= 0 && svgX <= svgRect.width && svgY >= 0 && svgY <= svgRect.height

        // 对于框选，我们需要允许鼠标移动到Canvas外部，但仍然计算有效的坐标
        // 将超出边界的坐标限制在Canvas边界内，这样框选框会正确显示
        const clampedSvgX = Math.max(0, Math.min(svgRect.width, svgX))
        const clampedSvgY = Math.max(0, Math.min(svgRect.height, svgY))

        const worldX = (clampedSvgX - pan.x) / zoom
        const worldY = (clampedSvgY - pan.y) / zoom

        return {
          svgPosition: { x: clampedSvgX, y: clampedSvgY },
          worldPosition: { x: worldX, y: worldY },
          originalEvent: event,
          isInsideCanvas, // 添加标记，指示鼠标是否在Canvas内
        }
      }
      catch (error) {
        debug('[Canvas] convertDocumentEventToSvgCoordinates: Error converting coordinates', error)
        return null
      }
    }, [pan, zoom])

    // 清理document事件监听器的函数
    const cleanupDocumentEventListeners = useCallback(() => {
      if (documentEventListenersRef.current.mousemove) {
        document.removeEventListener('mousemove', documentEventListenersRef.current.mousemove)
        documentEventListenersRef.current.mousemove = undefined
      }
      if (documentEventListenersRef.current.mouseup) {
        document.removeEventListener('mouseup', documentEventListenersRef.current.mouseup)
        documentEventListenersRef.current.mouseup = undefined
      }
      // Debug: Document event listeners cleaned up
    }, [])

    // 组件卸载时清理事件监听器
    useEffect(() => {
      return () => {
        cleanupDocumentEventListeners()
      }
    }, [cleanupDocumentEventListeners])

    const canvasSize = useMemo(() => {
      const w = canvasWidth || 0
      const h = canvasHeight || 0
      return { width: w, height: h, viewBoxX: -w / 2, viewBoxY: -h / 2 }
    }, [canvasWidth, canvasHeight])

    const debouncedOnDimensionsChange = onDimensionsChange || EMPTY_FUNCTION

    useEffect(() => {
      if (canvasSize.width !== undefined && canvasSize.height !== undefined && canvasSize.width > 0 && canvasSize.height > 0 && typeof zoom === 'number' && zoom > 0) {
        // 使用CANVAS_PIXELS_PER_MM来正确计算物理尺寸
        const displayWidth = formatDimensionForDisplay(canvasSize.width / (zoom * CANVAS_PIXELS_PER_MM))
        const displayHeight = formatDimensionForDisplay(canvasSize.height / (zoom * CANVAS_PIXELS_PER_MM))
        debouncedOnDimensionsChange(displayWidth, displayHeight)
      }
    }, [canvasSize, zoom, debouncedOnDimensionsChange])

    useEffect(() => {
      if (
        mainGroupRef.current !== undefined && mainGroupRef.current !== null && pan !== undefined && pan !== null && typeof pan.x === 'number' && typeof pan.y === 'number' && typeof zoom === 'number'
      ) {
        mainGroupRef.current.setAttribute(
          'transform',
          `translate(${pan.x}, ${pan.y}) scale(${zoom})`,
        )
      }
    }, [pan, zoom])

    const handleSvgMouseDown = useCallback((event: React.MouseEvent<SVGElement>) => {
      // Debug: handleSvgMouseDown

      // 如果处于平移模式，启动平移
      if (isPanMode && doStartPan && event.button === 0) {
        // Debug: Calling doStartPan
        doStartPan(event)
        isPanningRef.current = true
        // Debug: isPanningRef.current set to true
        event.preventDefault()
        return
      }

      try {
        // Debug: Event target
        const mouseDetails: CanvasMouseEvent = createMouseEventDetails(event, svgRef.current)

        if (svgRef.current === undefined || svgRef.current === null) {
          return
        }

        // 绘制模式处理已移至EditorLayout，这里不再处理
        // 避免重复的绘制处理逻辑

        const targetIsSvgBackground
          = event.target === svgRef.current
            || (event.target instanceof Element
              && event.target.classList.contains('canvas-grid-rect'))

        // 如果点击了背景，且不在绘制模式下，处理选择逻辑
        if (targetIsSvgBackground && !isDrawMode) {
          debug('handleSvgMouseDown: Clicked on SVG background or grid.')

          // 提取键盘修饰键
          const modifiers = extractKeyboardModifiers(event)

          // 检查是否应该清空选择
          if (shouldClearSelectionOnBackgroundClick(modifiers) && selectedElementIds.length > 0) {
            debug('handleSvgMouseDown: Clearing selection (background click without modifier)')
            if (typeof onElementSelect === 'function') {
              onElementSelect(null, false, 'clear')
            }
          }

          // 开始框选操作
          if (mouseDetails.svgPosition != null && pan != null && typeof pan.x === 'number' && typeof pan.y === 'number' && typeof zoom === 'number' && zoom !== 0) {
            const trueWorldDragStart = {
              x: (mouseDetails.svgPosition.x - pan.x) / zoom,
              y: (mouseDetails.svgPosition.y - pan.y) / zoom,
            }
            setDragStartWorld(trueWorldDragStart)
            debug('handleSvgMouseDown: Set dragStartWorld (true world):', trueWorldDragStart)

            // 添加document级别的事件监听，确保即使鼠标离开SVG也能继续框选
            const handleDocumentMouseMove = (e: MouseEvent) => {
              const details = convertDocumentEventToSvgCoordinates(e)
              if (!details?.svgPosition) {
                return
              }

              const currentTrueWorldMousePosition = {
                x: (details.svgPosition.x - pan.x) / zoom,
                y: (details.svgPosition.y - pan.y) / zoom,
              }
              const newSelectionBox = calculateMarqueeBox(trueWorldDragStart, currentTrueWorldMousePosition)
              setCurrentSelectionBox(newSelectionBox)
              debug('Document mousemove: Updated selection box', newSelectionBox)
            }

            const handleDocumentMouseUp = (e: MouseEvent) => {
              debug('Document mouseup: Completing marquee selection')

              // 清理document事件监听器
              cleanupDocumentEventListeners()

              // 完成框选逻辑
              if (currentSelectionBox && (currentSelectionBox.width > 0 || currentSelectionBox.height > 0)) {
                const selBox = new BoundingBoxClass(
                  currentSelectionBox.x,
                  currentSelectionBox.y,
                  currentSelectionBox.width,
                  currentSelectionBox.height,
                )

                const idsToSelect = elements
                  .filter((el: ShapeElement) => {
                    const bbox = ShapeElementUtils.calculateShapeBoundingBox(el)
                    if (!bbox) {
                      return false
                    }
                    return bbox.intersects(selBox)
                  })
                  .map((el: ShapeElement) => el.id)

                debug('Document mouseup: IDs to select from marquee:', idsToSelect)

                if (typeof onElementSelect === 'function') {
                  const modifiers = extractKeyboardModifiers(e)
                  const newSelection = handleMarqueeSelection(idsToSelect, selectedElementIds, modifiers)
                  const isMultiSelect = modifiers.shiftKey || modifiers.ctrlKey || modifiers.metaKey
                  const selectionMode = isMultiSelect ? 'add' : 'replace'

                  onElementSelect(newSelection.length > 0 ? newSelection : null, isMultiSelect, selectionMode)
                }
              }

              // 重置状态
              setDragStartWorld(null)
              setCurrentSelectionBox(null)
            }

            // 先清理之前的监听器，然后添加新的
            cleanupDocumentEventListeners()
            documentEventListenersRef.current.mousemove = handleDocumentMouseMove
            documentEventListenersRef.current.mouseup = handleDocumentMouseUp
            document.addEventListener('mousemove', handleDocumentMouseMove)
            document.addEventListener('mouseup', handleDocumentMouseUp)

            debug('Document event listeners added for marquee selection')
          }
          else {
            debug('[Canvas] handleSvgMouseDown: Could not calculate trueWorldDragStart due to missing data. Using raw worldPosition as fallback.', { svgPos: mouseDetails.svgPosition, pan, zoom })
            setDragStartWorld(mouseDetails.worldPosition)
          }
          setCurrentSelectionBox(null)
        }

        // 总是调用传入的鼠标按下处理函数
        if (typeof onMouseDownProp === 'function') {
          onMouseDownProp(mouseDetails)
        }
      }
      catch (error: unknown) {
        if (error instanceof Error) {
          debug('Error in handleSvgMouseDown creating event details:', error.message)
        }
        else {
          debug('Unknown error in handleSvgMouseDown:', error)
        }
      }
    }, [isPanMode, isDrawMode, svgRef, pan, zoom, selectedElementIds, onElementSelect, onMouseDownProp, doStartPan, createMouseEventDetails, convertDocumentEventToSvgCoordinates, cleanupDocumentEventListeners, currentSelectionBox, elements, setDragStartWorld, setCurrentSelectionBox])

    const handleSvgMouseMove = useCallback((event: React.MouseEvent<SVGElement>) => {
      try {
        // 检查事件是否有效
        if (event == null || svgRef.current == null) {
          debug('[Canvas] handleSvgMouseMove: Invalid event or svgRef')
          return
        }

        // 安全地创建鼠标事件详情
        let details: CanvasMouseEvent
        try {
          details = createMouseEventDetails(event, svgRef.current)
        }
        catch (error) {
          debug('[Canvas] handleSvgMouseMove: Error creating mouse event details', error)
          return
        }

        // 如果处于平移模式且正在平移，继续平移
        if (isPanMode && isPanningRef.current && doContinuePan) {
          debug('handleSvgMouseMove: Calling doContinuePan.')
          doContinuePan(event)
          event.preventDefault()
          return
        }

        // 绘制模式的鼠标移动处理已移至EditorLayout，这里不再处理

        // 如果有拖动起点且不在绘制模式下，更新选择框
        if (dragStartWorld !== undefined && dragStartWorld !== null
          && details.worldPosition !== undefined && details.worldPosition !== null
          && !isDrawMode) {
          if (details.svgPosition != null && pan != null && typeof pan.x === 'number' && typeof pan.y === 'number' && typeof zoom === 'number' && zoom !== 0) {
            const currentTrueWorldMousePosition = {
              x: (details.svgPosition.x - pan.x) / zoom,
              y: (details.svgPosition.y - pan.y) / zoom,
            }
            const newSelectionBox = calculateMarqueeBox(dragStartWorld, currentTrueWorldMousePosition)
            setCurrentSelectionBox(newSelectionBox)
          }
          else {
            debug('[Canvas] handleSvgMouseMove: Could not calculate currentTrueWorldMousePosition. Marquee might be incorrect.', { svgPos: details.svgPosition, pan, zoom })
            const fallbackWorldPos = (details.worldPosition !== undefined && details.worldPosition !== null) ? details.worldPosition : { x: 0, y: 0 }
            const newSelectionBox = calculateMarqueeBox(dragStartWorld, fallbackWorldPos)
            setCurrentSelectionBox(newSelectionBox)
          }
        }

        // 总是调用传入的鼠标移动处理函数
        if (typeof onMouseMoveProp === 'function') {
          onMouseMoveProp(details)
        }
      }
      catch (error: unknown) {
        if (error instanceof Error) {
          debug('Error in handleSvgMouseMove creating/using event details:', error.message)
        }
        else {
          debug('Unknown error in handleSvgMouseMove:', error)
        }
      }
    }, [createMouseEventDetails, dragStartWorld, pan, zoom, onMouseMoveProp, isPanMode, isDrawMode, doContinuePan, setCurrentSelectionBox, svgRef])

    const handleSvgMouseUp = useCallback((event: React.MouseEvent<SVGElement>) => {
      debug('handleSvgMouseUp. isPanMode:', isPanMode, 'isDrawMode:', isDrawMode, 'isPanningRef.current:', isPanningRef.current)

      // 如果处于平移模式且正在平移，结束平移
      if (isPanMode && isPanningRef.current && doEndPan) {
        debug('handleSvgMouseUp: Calling doEndPan.')
        doEndPan()
        isPanningRef.current = false
        debug('handleSvgMouseUp: isPanningRef.current set to false.')
        event.preventDefault()
        setDragStartWorld(null)
        setCurrentSelectionBox(null)
        return
      }

      try {
        const details: CanvasMouseEvent = createMouseEventDetails(event, svgRef.current)

        // 绘制模式的mouseup处理已移至EditorLayout，这里不再处理

        // 如果不在绘制模式下，处理选择框
        if (!isDrawMode && dragStartWorld !== undefined && dragStartWorld !== null
          && currentSelectionBox !== undefined && currentSelectionBox !== null
          && details.worldPosition !== undefined && details.worldPosition !== null) {
          debug('handleSvgMouseUp: Marquee selection ending.', { dragStartWorld, currentSelectionBox })

          const selBox = new BoundingBoxClass(
            currentSelectionBox.x,
            currentSelectionBox.y,
            currentSelectionBox.width,
            currentSelectionBox.height,
          )

          if (selBox.width > 0 || selBox.height > 0) {
            const idsToSelect = elements
              .filter((el: ShapeElement) => {
                const bbox = ShapeElementUtils.calculateShapeBoundingBox(el)
                if (!bbox) {
                  return false
                }
                const intersects = bbox.intersects(selBox)
                return intersects
              })
              .map((el: ShapeElement) => el.id)

            debug('handleSvgMouseUp: IDs to select from marquee:', idsToSelect)
            if (typeof onElementSelect === 'function') {
              // 提取键盘修饰键
              const modifiers = extractKeyboardModifiers(details.originalEvent ?? {})

              // 使用工具函数处理框选逻辑
              const newSelection = handleMarqueeSelection(
                idsToSelect,
                selectedElementIds,
                modifiers,
              )

              // 确定选择模式
              const isMultiSelect = modifiers.shiftKey || modifiers.ctrlKey || modifiers.metaKey
              const selectionMode = isMultiSelect ? 'add' : 'replace'

              debug('handleSvgMouseUp: Marquee selection result:', {
                idsToSelect,
                currentSelection: selectedElementIds,
                newSelection,
                selectionMode,
              })

              onElementSelect(newSelection.length > 0 ? newSelection : null, isMultiSelect, selectionMode)
            }
          }
          else if (event.target === svgRef.current || (event.target instanceof Element && event.target.classList.contains('canvas-grid-rect'))) {
            // 提取键盘修饰键
            const modifiers = extractKeyboardModifiers(details.originalEvent ?? {})

            // 检查是否应该清空选择
            if (shouldClearSelectionOnBackgroundClick(modifiers)) {
              debug('handleSvgMouseUp: Click on background (not marquee drag), clearing selection.')
              if (typeof onElementSelect === 'function') {
                onElementSelect(null, false, 'clear')
              }
            }
            else {
              debug('handleSvgMouseUp: Click on background with multi-select key, keeping selection.')
            }
          }
        }
        // 如果不在绘制模式下，且点击了背景，处理选择清除
        else if (!isDrawMode
          && (event.target === svgRef.current
            || (event.target instanceof Element && event.target.classList.contains('canvas-grid-rect')))) {
          // 提取键盘修饰键
          const modifiers = extractKeyboardModifiers(details.originalEvent ?? {})

          // 检查是否应该清空选择
          if (shouldClearSelectionOnBackgroundClick(modifiers)) {
            debug('handleSvgMouseUp: Simple click on background, clearing selection.')
            if (typeof onElementSelect === 'function') {
              onElementSelect(null, false, 'clear')
            }
          }
          else {
            debug('handleSvgMouseUp: Simple click on background with multi-select key, keeping selection.')
          }
        }

        // 总是重置拖动状态和清理document事件监听器
        cleanupDocumentEventListeners()
        setDragStartWorld(null)
        setCurrentSelectionBox(null)

        // 总是调用传入的鼠标抬起处理函数
        if (typeof onMouseUpProp === 'function') {
          onMouseUpProp(details)
        }
      }
      catch (error: unknown) {
        if (error instanceof Error) {
          debug('Error in handleSvgMouseUp creating/using event details:', error.message)
        }
        else {
          debug('Unknown error in handleSvgMouseUp:', error)
        }
      }
    }, [isPanMode, isDrawMode, doEndPan, createMouseEventDetails, dragStartWorld, currentSelectionBox, elements, onElementSelect, onMouseUpProp, cleanupDocumentEventListeners, selectedElementIds])

    const handleSvgMouseLeave = useCallback((event: React.MouseEvent<SVGElement>) => {
      debug('handleSvgMouseLeave. isPanMode:', isPanMode, 'isDrawMode:', isDrawMode, 'isPanningRef.current:', isPanningRef.current)

      // 如果处于平移模式且正在平移，结束平移
      if (isPanMode && isPanningRef.current && doEndPan) {
        debug('handleSvgMouseLeave: Calling doEndPan.')
        doEndPan()
        isPanningRef.current = false
        debug('handleSvgMouseLeave: isPanningRef.current set to false.')
        event.preventDefault()
      }

      // 不再清理框选状态，因为现在使用document事件监听器来处理框选
      // 框选会在document mouseup时完成，而不是在鼠标离开SVG时
      debug('handleSvgMouseLeave: Mouse left SVG, but marquee selection continues via document listeners.')
    }, [isPanMode, isDrawMode, doEndPan])

    const handleSvgDragOver = (event: React.DragEvent<SVGSVGElement>) => {
      event.preventDefault()
      if (typeof onDragOver === 'function') {
        onDragOver(event)
      }
    }

    const handleSvgDrop = useCallback(
      (event: React.DragEvent<SVGSVGElement>) => {
        event.preventDefault()
        event.stopPropagation()
        debug('handleSvgDrop: Entered function.')

        const mouseDetails = createMouseEventDetails(event as React.MouseEvent<SVGSVGElement>, svgRef.current)
        debug(
          'handleSvgDrop: Raw mouse event details created. svgPosition:',
          mouseDetails.svgPosition,
          'worldPosition (from createMouseEventDetails):',
          mouseDetails.worldPosition,
        )

        if (!svgRef.current) {
          debug('[Canvas] handleSvgDrop: svgRef.current is null, cannot proceed.')
          return
        }

        const rawData = event.dataTransfer.getData('application/json')
        debug('handleSvgDrop: Raw data from dataTransfer:', rawData)

        if (!rawData) {
          debug('[Canvas] handleSvgDrop: No data transferred.')
          return
        }

        try {
          const parsedAssetData = JSON.parse(rawData) as {
            elementType: string
            properties?: Record<string, unknown>
            id?: string
            name?: string
            elementSpecificData?: Record<string, unknown>
          }
          debug('handleSvgDrop: Parsed droppedAssetData:', parsedAssetData)

          let trueWorldPosition: Point
          if (
            mouseDetails.svgPosition != null
            && pan != null
            && typeof mouseDetails.svgPosition.x === 'number'
            && typeof mouseDetails.svgPosition.y === 'number'
            && typeof pan.x === 'number'
            && typeof pan.y === 'number'
            && typeof zoom === 'number'
            && zoom !== 0
          ) {
            // 计算真实世界坐标 - 这是鼠标落点的精确位置
            trueWorldPosition = {
              x: (mouseDetails.svgPosition.x - pan.x) / zoom,
              y: (mouseDetails.svgPosition.y - pan.y) / zoom,
            }

            // 确保多边形的中心点就是鼠标落点位置
            // 多边形的points是相对于原点(0,0)的，position是中心点

            debug(
              'handleSvgDrop: Recalculated trueWorldPosition:',
              trueWorldPosition,
              'from svgPosition:',
              mouseDetails.svgPosition,
              'pan:',
              pan,
              'zoom:',
              zoom,
            )
          }
          else {
            debug(
              '[Canvas] handleSvgDrop: Using potentially incorrect mouseEventDetails.worldPosition due to missing svgPosition, pan, or zoom.',
              { svgPos: mouseDetails.svgPosition, panVal: pan, zoomVal: zoom },
            )
            trueWorldPosition = mouseDetails.worldPosition ?? { x: 0, y: 0 }
          }

          debug('handleSvgDrop: typeof onElementAdd:', typeof onElementAdd)
          if (typeof onElementAdd === 'function') {
            debug('handleSvgDrop: Calling onElementAdd with asset and trueWorldPosition.')
            onElementAdd(parsedAssetData, trueWorldPosition)
          }

          if (typeof onDropProp === 'function') {
            debug('handleSvgDrop: Calling legacy onDropProp.')
            onDropProp(mouseDetails)
          }

          if (typeof onShapeDrop === 'function') {
            debug('handleSvgDrop: Calling onShapeDrop.')
            onShapeDrop(event, parsedAssetData)
          }
        }
        catch (error) {
          debug('[Canvas] handleSvgDrop: Error parsing data or calling handlers:', error)
        }
      },
      [createMouseEventDetails, onElementAdd, onDropProp, onShapeDrop, pan, zoom, svgRef],
    )

    // 监听 showGrid 变化，强制重新渲染
    useEffect(() => {
      // 当 showGrid 变化时，强制重新渲染
      if (mainGroupRef.current) {
        // 触发一个微小的变化，强制 SVG 重新渲染
        const currentTransform = mainGroupRef.current.getAttribute('transform')
        mainGroupRef.current.setAttribute('transform', currentTransform ?? '')

        // 如果网格应该显示，确保它被正确渲染
        if (showGrid) {
          // 强制重新计算布局
          const timeoutId = setTimeout(() => {
            if (mainGroupRef.current) {
              // 再次触发变化，确保网格被渲染
              mainGroupRef.current.setAttribute('transform', currentTransform ?? '')
            }
          }, 50)

          return () => {
            clearTimeout(timeoutId)
          }
        }
      }
    }, [showGrid])

    useEffect(() => {
      // This effect is intentionally empty but needed for dependency tracking
      // It ensures the component re-renders when these dependencies change
    }, [visibleAndSortedElements, selectedElementIds, zoom])

    // 注意：网格的渲染逻辑已移至 CanvasGrid 组件内部

    return (
      <div className="relative w-full h-full overflow-hidden" ref={canvasWrapperRef}>
        <svg
          ref={svgRef}
          className="w-full h-full bg-gray-100 dark:bg-gray-800 cursor-default select-none"
          onMouseDown={handleSvgMouseDown}
          onMouseMove={handleSvgMouseMove}
          onMouseUp={handleSvgMouseUp}
          onMouseLeave={handleSvgMouseLeave}
          onWheel={onWheel as React.WheelEventHandler<SVGSVGElement>}
          onDragOver={handleSvgDragOver}
          onDrop={handleSvgDrop}
        >
          <g ref={mainGroupRef}>
            {/* 直接在Canvas组件中渲染网格 */}
            {showGrid && (
              <g className="canvas-grid-lines">
                {(() => {
                  // 只有在showGrid为true且所有必要参数有效时才渲染网格
                  if (!gridSize || !zoom || !canvasSize.width || !canvasSize.height) {
                    return null
                  }

                  // 确保pan对象有效
                  if (typeof pan.x !== 'number' || typeof pan.y !== 'number') {
                    return null
                  }

                  // 计算有效网格尺寸 - 改进低缩放级别的处理
                  let effectiveGridSize = gridSize
                  const multipliers = [1, 5, 10, 50, 100, 500] // 增加更大的乘数

                  for (const multiplier of multipliers) {
                    const potentialGridSize = gridSize * multiplier
                    const cellVisibleSizePx = potentialGridSize * zoom

                    // 在极低缩放级别(1%-10%)时，使用更大的最小像素要求
                    const minPixelSize = zoom < 0.1 ? 50 : 30

                    if (cellVisibleSizePx >= minPixelSize) {
                      effectiveGridSize = potentialGridSize
                      break
                    }
                  }

                  // 计算视口坐标
                  const viewBoxX = -pan.x / zoom
                  const viewBoxY = -pan.y / zoom
                  const viewBoxWidth = canvasSize.width / zoom
                  const viewBoxHeight = canvasSize.height / zoom

                  // 添加边距避免边缘问题
                  const padding = effectiveGridSize * 2
                  const rectX = viewBoxX - padding
                  const rectY = viewBoxY - padding
                  const rectWidth = viewBoxWidth + (padding * 2)
                  const rectHeight = viewBoxHeight + (padding * 2)

                  // 计算网格线数量
                  const horizontalLines = Math.ceil(rectHeight / effectiveGridSize) + 1
                  const verticalLines = Math.ceil(rectWidth / effectiveGridSize) + 1

                  // 计算起始位置（对齐到网格）
                  const startX = Math.floor(rectX / effectiveGridSize) * effectiveGridSize
                  const startY = Math.floor(rectY / effectiveGridSize) * effectiveGridSize

                  // 创建网格线
                  const gridLines = []

                  // 水平线
                  for (let i = 0; i < horizontalLines; i++) {
                    const y = startY + i * effectiveGridSize
                    gridLines.push(
                      <line
                        key={`h-${i}`}
                        x1={rectX}
                        y1={y}
                        x2={rectX + rectWidth}
                        y2={y}
                        stroke="rgba(128,128,128,0.3)"
                        strokeWidth={0.5 / zoom}
                      />,
                    )
                  }

                  // 垂直线
                  for (let i = 0; i < verticalLines; i++) {
                    const x = startX + i * effectiveGridSize
                    gridLines.push(
                      <line
                        key={`v-${i}`}
                        x1={x}
                        y1={rectY}
                        x2={x}
                        y2={rectY + rectHeight}
                        stroke="rgba(128,128,128,0.3)"
                        strokeWidth={0.5 / zoom}
                      />,
                    )
                  }

                  return gridLines
                })()}
              </g>
            )}
            <g ref={shapesLayerRef} className="shapes-layer">
              <ShapeRenderer
                shapesLayerRef={shapesLayerRef}
                shapes={visibleAndSortedElements}
                selectedIds={selectedElementIds}
                currentZoom={zoom}
                onShapeSelected={(elementId: string | string[] | null, isMultiSelect?: boolean) => {
                  debug('ShapeRenderer onShapeSelected called:', { elementId, isMultiSelect })
                  if (typeof onElementSelect === 'function') {
                    onElementSelect(elementId, isMultiSelect, isMultiSelect ? 'toggle' : 'replace')
                  }
                }}
                isPanMode={isPanMode}
              />
            </g>
            <g ref={interactionLayerRef} className="interaction-layer">
              {/* 选择框 */}
              {currentSelectionBox && (
                <rect
                  x={currentSelectionBox.x}
                  y={currentSelectionBox.y}
                  width={currentSelectionBox.width}
                  height={currentSelectionBox.height}
                  fill="rgba(0, 120, 255, 0.2)"
                  stroke="rgba(0, 120, 255, 0.8)"
                  strokeWidth={1 / (zoom !== 0 ? zoom : 1)}
                  pointerEvents="none"
                />
              )}

              {/* 路径预览 */}
              <PathPreview
                isDrawing={pathDrawStateProp?.isDrawing ?? false}
                pathType={pathDrawStateProp?.pathType ?? null}
                startPoint={pathDrawStateProp?.startPoint ?? null}
                currentPoint={pathDrawStateProp?.currentPoint ?? null}
                allPoints={pathDrawStateProp?.allPoints ?? []}
                clickCount={pathDrawStateProp?.clickCount ?? 0}
              />

              {/* 路径绘制处理已移至EditorLayout，这里不再需要PathDrawHandler */}
            </g>

            {/* 边界框和控制手柄层 */}
            <g ref={handlesLayerRef} className="handles-layer">
              {/* HandlesRenderer will be rendered via useEffect */}
            </g>
          </g>
        </svg>
        {/* Scale bar absolute positioning and animation */}
        <CanvasScaleBar
          zoom={zoom}
          pixelsPerMM={CANVAS_PIXELS_PER_MM}
          canvasWidth={canvasSize.width}
          position={isPropertySidebarOpen === true ? 'sidebar' : (sheetOpen ? 'left' : 'right')}
          offset={(() => {
            // 计算比例尺的实际宽度
            const { pixelLength } = calculateScaleBarInfo(zoom, CANVAS_PIXELS_PER_MM, canvasSize.width)
            const actualBarWidth = pixelLength + 4 // 加上左右两侧的TICK_WIDTH (2px each)

            // 设置安全边距
            const barHeight = 60
            const sidebarWidth = 440 // 属性栏宽度
            const sidebarRightMargin = 8 // 属性栏右侧边距（从className='right-2'推断）
            const margin = 16 // 减小边距，给比例尺更多空间
            const drawerHeight = 56 // 底部抽屉收起时的高度（h-14 = 56px）
            const safetyGap = 20 // 比例尺和属性栏之间的安全间距

            // 计算属性栏左边缘的位置
            const sidebarLeftEdge = isPropertySidebarOpen
              ? canvasSize.width - sidebarWidth - sidebarRightMargin
              : canvasSize.width

            // 默认位置：右下角
            let x = canvasSize.width - margin - actualBarWidth
            const y = canvasSize.height - drawerHeight - barHeight

            if (isPropertySidebarOpen === true) {
              // 当属性栏打开时，将比例尺放在属性栏左侧，保持安全间距
              x = sidebarLeftEdge - safetyGap - actualBarWidth
            }
            else if (sheetOpen) {
              // 当sheet打开时，将比例尺放在左侧
              x = margin
            }

            // 确保比例尺不会超出画布左边界
            x = Math.max(margin, x)

            return { x, y }
          })()}
        />
        <div className="absolute bottom-2 left-2 p-1 bg-gray-700 bg-opacity-50 text-white text-xs rounded">
          {`Zoom: ${(zoom * 100).toFixed(0)}% | Pan: (${pan.x.toFixed(0)}, ${pan.y.toFixed(0)})`}
        </div>
      </div>
    )
  },
)

Canvas.displayName = 'Canvas'

export default Canvas
