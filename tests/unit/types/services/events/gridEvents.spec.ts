import type {
  GridColorChangeEvent,
  GridEnableEvent,
  GridSizeChangeEvent,
  GridSnapChangeEvent,
} from '@/types/services/events/gridEvents'
import { describe, expect, it } from 'vitest'
import { AppEventType } from '@/types/services/events/eventTypes'

describe('grid Events', () => {
  describe('grid Event Types', () => {
    it('should define all expected event types in AppEventType', () => {
      expect(AppEventType.GRID_ENABLED).toBeDefined()
      expect(AppEventType.GRID_SIZE_CHANGED).toBeDefined()
      expect(AppEventType.GRID_COLOR_CHANGED).toBeDefined()
      expect(AppEventType.GRID_SNAP_CHANGED).toBeDefined()
    })
  })

  describe('gridEnableEvent', () => {
    it('should allow creating a grid enable event', () => {
      const event: GridEnableEvent = {
        type: AppEventType.GRID_ENABLED,
        payload: {
          enabled: true,
        },
      }

      expect(event.type).toBe(AppEventType.GRID_ENABLED)
      expect(event.payload.enabled).toBe(true)
    })
  })

  describe('gridSizeChangeEvent', () => {
    it('should allow creating a grid size change event', () => {
      const event: GridSizeChangeEvent = {
        type: AppEventType.GRID_SIZE_CHANGED,
        payload: {
          size: 20,
        },
      }

      expect(event.type).toBe(AppEventType.GRID_SIZE_CHANGED)
      expect(event.payload.size).toBe(20)
    })
  })

  describe('gridColorChangeEvent', () => {
    it('should allow creating a grid color change event', () => {
      const event: GridColorChangeEvent = {
        type: AppEventType.GRID_COLOR_CHANGED,
        payload: {
          color: '#cccccc',
        },
      }

      expect(event.type).toBe(AppEventType.GRID_COLOR_CHANGED)
      expect(event.payload.color).toBe('#cccccc')
    })
  })

  describe('gridSnapChangeEvent', () => {
    it('should allow creating a grid snap change event', () => {
      const event: GridSnapChangeEvent = {
        type: AppEventType.GRID_SNAP_CHANGED,
        payload: {
          enabled: true,
        },
      }

      expect(event.type).toBe(AppEventType.GRID_SNAP_CHANGED)
      expect(event.payload.enabled).toBe(true)
    })
  })
})
