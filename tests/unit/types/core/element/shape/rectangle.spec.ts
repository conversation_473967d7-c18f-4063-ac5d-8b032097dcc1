import type { ShapeElement } from '@/types/core/element/element'
import type { Rectangle } from '@/types/core/element/shape/rectangleShapeTypes'
import { describe, expect, it } from 'vitest'

describe('rectangle Interface', () => {
  it('should define a rectangle with required properties', () => {
    // Create a valid Rectangle object
    const rectangle: Rectangle = {
      id: 'rectangle-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 25 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 0, y: 0 }, // top-left
        { x: 100, y: 0 }, // top-right
        { x: 100, y: 50 }, // bottom-right
        { x: 0, y: 50 }, // bottom-left
      ],
      width: 100,
      height: 50,
    }

    // Verify the object is valid
    expect(rectangle.id).toBe('rectangle-1')
    expect(rectangle.type).toBe('shape')
    expect(rectangle.points.length).toBe(4)
    expect(rectangle.width).toBe(100)
    expect(rectangle.height).toBe(50)
  })

  it('should extend ShapeElement interface', () => {
    // Create a rectangle
    const rectangle: Rectangle = {
      id: 'rectangle-2',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 50 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 0, y: 0 },
        { x: 200, y: 0 },
        { x: 200, y: 100 },
        { x: 0, y: 100 },
      ],
      width: 200,
      height: 100,
    }

    // Verify the rectangle extends ShapeElement
    const shapeElement: ShapeElement = rectangle
    expect(shapeElement.id).toBe('rectangle-2')
    expect(shapeElement.type).toBe('shape')
    expect(shapeElement.visible).toBe(true)
    expect(shapeElement.locked).toBe(false)
    expect(shapeElement.position.x).toBe(100)
    expect(shapeElement.position.y).toBe(50)
  })

  it('should allow creating rectangles with different dimensions', () => {
    // Create rectangles with different dimensions
    const smallRectangle: Rectangle = {
      id: 'small',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 25, y: 12.5 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 0, y: 0 },
        { x: 50, y: 0 },
        { x: 50, y: 25 },
        { x: 0, y: 25 },
      ],
      width: 50,
      height: 25,
    }

    const largeRectangle: Rectangle = {
      id: 'large',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 250, y: 150 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 0, y: 0 },
        { x: 500, y: 0 },
        { x: 500, y: 300 },
        { x: 0, y: 300 },
      ],
      width: 500,
      height: 300,
    }

    const squareRectangle: Rectangle = {
      id: 'square',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 100, y: 100 },
        { x: 0, y: 100 },
      ],
      width: 100,
      height: 100,
    }

    // Verify all rectangles are valid
    expect(smallRectangle.width).toBe(50)
    expect(smallRectangle.height).toBe(25)

    expect(largeRectangle.width).toBe(500)
    expect(largeRectangle.height).toBe(300)

    expect(squareRectangle.width).toBe(100)
    expect(squareRectangle.height).toBe(100)
  })

  it('should be usable in arrays and collections', () => {
    // Create an array of rectangles
    const rectangles: Rectangle[] = [
      {
        id: 'rectangle-3',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 50, y: 25 },
        rotation: 0,
        scale: { x: 1, y: 1 },
        selectable: true,
        draggable: true,
        showHandles: true,
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 100, y: 50 },
          { x: 0, y: 50 },
        ],
        width: 100,
        height: 50,
      },
      {
        id: 'rectangle-4',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 100, y: 50 },
        rotation: 0,
        scale: { x: 1, y: 1 },
        selectable: true,
        draggable: true,
        showHandles: true,
        points: [
          { x: 0, y: 0 },
          { x: 200, y: 0 },
          { x: 200, y: 100 },
          { x: 0, y: 100 },
        ],
        width: 200,
        height: 100,
      },
    ]

    // Verify the array is valid
    expect(rectangles.length).toBe(2)
    expect(rectangles[0].id).toBe('rectangle-3')
    expect(rectangles[0].width).toBe(100)
    expect(rectangles[0].height).toBe(50)

    expect(rectangles[1].id).toBe('rectangle-4')
    expect(rectangles[1].width).toBe(200)
    expect(rectangles[1].height).toBe(100)
  })

  it('should be usable in functions that require Rectangle', () => {
    // Define a function that uses Rectangle
    function calculateRectangleArea(rectangle: Rectangle): number {
      return rectangle.width * rectangle.height
    }

    // Create a rectangle
    const rectangle: Rectangle = {
      id: 'rectangle-5',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 5, y: 10 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 0, y: 0 },
        { x: 10, y: 0 },
        { x: 10, y: 20 },
        { x: 0, y: 20 },
      ],
      width: 10,
      height: 20,
    }

    // Test the function
    const area = calculateRectangleArea(rectangle)
    expect(area).toBe(200) // 10 * 20 = 200
  })

  it('should be compatible with JSON serialization/deserialization', () => {
    // Create a rectangle
    const rectangle: Rectangle = {
      id: 'rectangle-6',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 60, y: 45 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 10, y: 20 },
        { x: 110, y: 20 },
        { x: 110, y: 70 },
        { x: 10, y: 70 },
      ],
      width: 100,
      height: 50,
    }

    // Serialize to JSON and deserialize
    const serialized = JSON.stringify(rectangle)
    const deserialized = JSON.parse(serialized) as Rectangle

    // Verify the deserialized object is valid
    expect(deserialized.id).toBe('rectangle-6')
    expect(deserialized.type).toBe('shape')
    expect(deserialized.points.length).toBe(4)
    expect(deserialized.points[0].x).toBe(10)
    expect(deserialized.points[0].y).toBe(20)
    expect(deserialized.points[1].x).toBe(110)
    expect(deserialized.points[1].y).toBe(20)
    expect(deserialized.points[2].x).toBe(110)
    expect(deserialized.points[2].y).toBe(70)
    expect(deserialized.points[3].x).toBe(10)
    expect(deserialized.points[3].y).toBe(70)
    expect(deserialized.width).toBe(100)
    expect(deserialized.height).toBe(50)
  })
})
