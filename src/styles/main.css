@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 0 0% 98%;
  }
}

@layer base {
  * {
    /* @apply border-border; */ /* This line was causing the error */
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  /* color: #333; */ /* Replaced by Tailwind text-foreground */
  /* background-color: #f5f5f5; */ /* Replaced by Tailwind bg-background */
}

.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

/* Layout styles */
.editor-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.editor-content {
  display: flex;
  flex: 1;
  width: 100%;
  overflow: hidden;
}

/* Toolbar styles */
.toolbar {
  display: flex;
  align-items: center;
  height: 50px; /* Consider using Tailwind spacing, e.g., h-12 */
  padding: 0 10px; /* Consider using Tailwind spacing, e.g., px-2.5 */
  /* background-color: #333; */ /* Will be handled by bg-background or specific component styles */
  /* color: white; */ /* Will be handled by text-foreground or specific component styles */
  /* border-bottom: 1px solid #444; */ /* Will be handled by border-b and border-border */
}

.toolbar-section {
  display: flex;
  align-items: center;
  margin-right: 20px; /* Consider using Tailwind spacing, e.g., mr-5 */
}

.toolbar-button {
  /* background-color: transparent; */
  /* border: none; */
  /* color: white; */
  padding: 5px 10px; /* Consider using Tailwind spacing, e.g., p-1 px-2.5 */
  cursor: pointer;
  border-radius: 3px; /* Consider using Tailwind rounded-sm or rounded-md */
  /* transition: background-color 0.2s; */ /* Tailwind transitions can be used */
}

.toolbar-button:hover {
  /* background-color: rgba(255, 255, 255, 0.1); */ /* Tailwind hover:bg-... */
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-button.active {
  /* background-color: rgba(255, 255, 255, 0.2); */ /* Tailwind active:bg-... or variant based on state */
}

.zoom-level {
  margin-left: 5px; /* Consider using Tailwind spacing, e.g., ml-1 */
  font-size: 12px; /* Consider using Tailwind text-xs */
}

/* Sidebar styles */
/*
.asset-sidebar,
.property-sidebar {
  width: 250px;
  height: 100%;
  background-color: #f8f8f8;
  border-right: 1px solid #ddd;
  border-left: 1px solid #ddd;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}
*/

.sidebar-header {
  padding: 10px; /* Consider using Tailwind p-2.5 */
  border-bottom: 1px solid #ddd; /* Will be handled by border-b and border-border */
  background-color: #eee; /* Consider using Tailwind bg-muted or similar */
}

.sidebar-header h2 {
  font-size: 16px; /* Consider using Tailwind text-base or text-lg */
  font-weight: 600; /* Consider using Tailwind font-semibold */
}

/* Asset sidebar specific styles */
.category-tabs {
  display: flex;
  flex-wrap: wrap;
  background-color: #f0f0f0; /* Consider using Tailwind bg-muted */
  border-bottom: 1px solid #ddd; /* Will be handled by border-b and border-border */
}

.category-tab {
  padding: 8px 12px; /* Consider using Tailwind p-2 px-3 */
  /* background-color: transparent; */
  /* border: none; */
  cursor: pointer;
  font-size: 12px; /* Consider using Tailwind text-xs */
}

.category-tab.active {
  /* background-color: #fff; */ /* Will be handled by bg-background or specific component state */
  border-bottom: 2px solid #333; /* Consider using border-primary or similar */
}

.assets-container {
  padding: 10px; /* Consider using Tailwind p-2.5 */
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* Consider using Tailwind gap-2.5 */
}

.asset-item {
  width: calc(50% - 5px); /* Consider using Tailwind grid for layout */
  background-color: white; /* Will be handled by bg-card or bg-background */
  border: 1px solid #ddd; /* Will be handled by border and border-border */
  border-radius: 4px; /* Consider using Tailwind rounded-sm or rounded-md */
  padding: 5px; /* Consider using Tailwind p-1 */
  cursor: grab;
  /* transition: transform 0.2s, box-shadow 0.2s; */ /* Tailwind transitions can be used */
}

.asset-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Consider using Tailwind shadow-md */
}

.asset-preview {
  height: 60px; /* Consider using Tailwind h-15 or h-16 */
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9; /* Consider using Tailwind bg-muted */
  margin-bottom: 5px; /* Consider using Tailwind mb-1 */
}

.asset-name {
  font-size: 12px; /* Consider using Tailwind text-xs */
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Property sidebar specific styles */
.properties-panel {
  padding: 10px; /* Consider using Tailwind p-2.5 */
}

.property-section {
  margin-bottom: 15px; /* Consider using Tailwind mb-4 */
  padding-bottom: 15px; /* Consider using Tailwind pb-4 */
  border-bottom: 1px solid #eee; /* Will be handled by border-b and border-border */
}

.property-section h3 {
  font-size: 14px; /* Consider using Tailwind text-sm */
  margin-bottom: 10px; /* Consider using Tailwind mb-2.5 */
}

.property-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px; /* Consider using Tailwind mb-2 */
}

.property-row label {
  width: 100px; /* Consider using Tailwind w-24 or w-28 or grid layout */
  font-size: 12px; /* Consider using Tailwind text-xs */
}

.property-row input {
  flex: 1;
  padding: 5px; /* Consider using Tailwind p-1 */
  border: 1px solid #ddd; /* Will be handled by border and border-input */
  border-radius: 3px; /* Consider using Tailwind rounded-sm */
}

.delete-button {
  background-color: #f44336; /* Consider using Tailwind bg-destructive */
  color: white; /* Consider using Tailwind text-destructive-foreground */
  border: none;
  padding: 5px 10px; /* Consider using Tailwind p-1 px-2.5 */
  border-radius: 3px; /* Consider using Tailwind rounded-sm */
  cursor: pointer;
  margin-top: 10px; /* Consider using Tailwind mt-2.5 */
}

.empty-selection-message {
  padding: 20px; /* Consider using Tailwind p-5 */
  text-align: center;
  color: #777; /* Consider using Tailwind text-muted-foreground */
}

/* Canvas styles */
.canvas-container {
  flex: 1;
  position: relative;
  /* background-color: white; */ /* Will be handled by bg-muted or bg-background */
  overflow: hidden;
}

.canvas {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.canvas.pan-mode {
  cursor: grab;
}

.canvas.pan-mode:active {
  cursor: grabbing;
}

.canvas-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform-origin: 0 0;
}

.canvas-element {
  position: absolute;
  /* Additional styles for specific elements can go here */
}

/* Selection box style */
.selection-box {
  position: absolute;
  border: 1px dashed #007bff;
  background-color: rgba(0, 123, 255, 0.1);
  z-index: 1000; /* Ensure it's above other elements */
}

/* Grid styles */
/* Grid lines are drawn directly on canvas, so no CSS here */

/* Zoom and pan indicator styles */
.zoom-indicator,
.scale-indicator {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 3px;
  font-size: 12px;
}

/* Layer display styles */
.layer-display {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 3px;
  font-size: 12px;
}

/* Asset preview styles in sidebar */
/* Basic shapes */
.preview-rectangle, .preview-square {
  width: 40px;
  height: 30px;
  background-color: #ccc;
}
.preview-square { height: 40px; }

.preview-ellipse, .preview-circle {
  width: 40px;
  height: 30px;
  background-color: #ccc;
  border-radius: 50%;
}
.preview-circle { height: 40px; }

.preview-triangle {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 35px solid #ccc;
}

.preview-polygon {
  /* Using clip-path for a simple hexagon, more complex polygons might need SVG */
  width: 40px;
  height: 35px;
  background-color: #ccc;
  clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
}

/* Paths */
.preview-line {
  width: 40px;
  height: 2px;
  background-color: #333;
}

.preview-path {
  width: 40px;
  height: 20px;
  position: relative;
  border-top: 2px solid #333;
  border-bottom: 2px solid #333;
}
.preview-path:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #333;
  transform: translateY(-50%) rotate(-20deg) scaleX(0.7);
  transform-origin: left;
}

.preview-polyline {
  width: 40px;
  height: 20px;
  position: relative;
  border-top: 2px solid #333;
}
.preview-polyline:after {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 2px;
  height: 100%;
  background-color: #333;
  transform: translateX(-50%);
}

/* Text */
.preview-text {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

/* Architectural */
.preview-wall {
  width: 40px;
  height: 5px;
  background-color: #555;
}
.preview-door {
  width: 20px;
  height: 30px;
  border: 2px solid #777;
  position: relative;
}
.preview-door:after {
  content: '';
  position: absolute;
  top: 50%;
  right: 2px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #777;
  transform: translateY(-50%);
}

.preview-window {
  width: 30px;
  height: 20px;
  border: 2px solid #777;
  position: relative;
}
.preview-window:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #777;
  transform: translateY(-50%);
}

/* Furniture and fixtures */
.preview-furniture {
  width: 30px;
  height: 30px;
  background-color: #aaa;
  border-radius: 4px;
}
.preview-furniture:after {
  content: 'F';
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: white;
  font-weight: bold;
}

.preview-fixture {
  width: 20px;
  height: 20px;
  background-color: #bbb;
  border-radius: 50%;
}
.preview-fixture:after {
  content: 'L';
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: white;
  font-weight: bold;
}

/* Room (as a placeholder) */
.preview-room {
  width: 40px;
  height: 40px;
  border: 2px dashed #aaa;
}

/* Keyboard shortcuts tooltip */
.keyboard-shortcuts {
  /* Styles for the button that triggers the tooltip */
}
.keyboard-help {
  /* If you have a specific button for help */
}
.shortcut-tooltip {
  visibility: hidden;
  width: 220px;
  background-color: #333;
  color: #fff;
  text-align: left;
  border-radius: 6px;
  padding: 8px;
  position: absolute;
  z-index: 101;
  bottom: 125%; /* Position above the button */
  right: 0;
  opacity: 0;
  transition: opacity 0.3s;
}

.keyboard-help:hover .shortcut-tooltip {
  visibility: visible;
  opacity: 1;
}

.shortcut-tooltip table {
  width: 100%;
  border-collapse: collapse;
}

.shortcut-tooltip th,
.shortcut-tooltip td {
  padding: 4px;
  border-bottom: 1px solid #555;
}

.shortcut-tooltip th {
  text-align: left;
  font-weight: bold;
}

.shortcut-tooltip tr:last-child td {
  border-bottom: none;
}

/* Fullscreen styles */
.editor-layout.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999;
  background-color: var(--background); /* Ensure background matches */
}

.editor-layout.fullscreen .toolbar {
  border-radius: 0;
}

.editor-layout.fullscreen .editor-content {
  height: calc(100vh - 50px); /* Adjust based on toolbar height */
} 