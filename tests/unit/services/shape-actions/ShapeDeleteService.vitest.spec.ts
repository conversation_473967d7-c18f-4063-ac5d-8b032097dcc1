import type { ShapeRepository } from '@/core/state/ShapeRepository'
import type { ShapeDeleteEvent } from '@/types/services/events'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ErrorType } from '@/core/errors/CoreError'
import * as errorHelpers from '@/services/event-bus/helpers/error-helpers'
import { ShapeDeleteService } from '@/services/shape-actions/ShapeDeleteService'
import { AppEventType } from '@/types/services/events'

// Mock dependencies
vi.mock('@/core/state/ShapeRepository')
vi.mock('@/services/event-bus/helpers/error-helpers', () => ({
  publishError: vi.fn(),
}))

describe('shapeDeleteService', () => {
  // Mock dependencies
  let mockRepository: any
  let mockEventBus: any
  let mockLogger: any
  let service: ShapeDeleteService

  beforeEach(() => {
    // Create mock implementations
    mockRepository = {
      getById: vi.fn(),
      remove: vi.fn(),
    }

    mockEventBus = {
      publish: vi.fn(),
    }

    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    }

    // Create service instance with mocks
    service = new ShapeDeleteService(
      mockRepository as unknown as ShapeRepository,
      mockEventBus,
      mockLogger,
    )
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('handleRequest', () => {
    it('should handle invalid payload', async () => {
      // Create invalid event (missing shapeIds)
      const event: ShapeDeleteEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: {
          shapeIds: null as any,
        },
      }

      // Call the method
      await service.handleRequest(event)

      // Verify error was published
      expect(errorHelpers.publishError).toHaveBeenCalledWith(
        mockEventBus,
        ErrorType.INVALID_PAYLOAD,
        expect.stringContaining('Invalid payload for shape delete'),
        expect.anything(),
      )

      // Verify repository methods were not called
      expect(mockRepository.getById).not.toHaveBeenCalled()
      expect(mockRepository.remove).not.toHaveBeenCalled()
    })

    it('should handle empty shapeIds array', async () => {
      // Create event with empty shapeIds array
      const event: ShapeDeleteEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: {
          shapeIds: [],
        },
      }

      // Call the method
      await service.handleRequest(event)

      // Verify error was published
      expect(errorHelpers.publishError).toHaveBeenCalledWith(
        mockEventBus,
        ErrorType.INVALID_PAYLOAD,
        expect.stringContaining('Invalid payload for shape delete'),
        expect.anything(),
      )

      // Verify repository methods were not called
      expect(mockRepository.getById).not.toHaveBeenCalled()
      expect(mockRepository.remove).not.toHaveBeenCalled()
    })

    it('should successfully delete shapes', async () => {
      // Setup repository mocks
      mockRepository.getById.mockImplementation((id: string) => ({ id })) // Return mock shape
      mockRepository.remove.mockReturnValue(true) // Successful removal

      // Create event with valid shapeIds
      const event: ShapeDeleteEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: {
          shapeIds: ['shape1', 'shape2', 'shape3'],
        },
      }

      // Call the method
      await service.handleRequest(event)

      // Verify repository methods were called for each shape
      expect(mockRepository.getById).toHaveBeenCalledTimes(3)
      expect(mockRepository.remove).toHaveBeenCalledTimes(3)

      // Verify success event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_DELETE_COMPLETE,
        payload: {
          shapeIds: ['shape1', 'shape2', 'shape3'],
        },
      })

      // Verify no error was published
      expect(errorHelpers.publishError).not.toHaveBeenCalled()
    })

    it('should handle shapes not found in repository', async () => {
      // Setup repository mocks
      mockRepository.getById.mockImplementation((id: string) => {
        // Only return shape for shape1, others not found
        return id === 'shape1' ? { id } : undefined
      })
      mockRepository.remove.mockReturnValue(true) // Successful removal

      // Create event with valid shapeIds
      const event: ShapeDeleteEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: {
          shapeIds: ['shape1', 'shape2', 'shape3'],
        },
      }

      // Call the method
      await service.handleRequest(event)

      // Verify repository methods were called correctly
      expect(mockRepository.getById).toHaveBeenCalledTimes(3)
      expect(mockRepository.remove).toHaveBeenCalledTimes(1) // Only for shape1

      // Verify both success and error events were published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_DELETE_COMPLETE,
        payload: {
          shapeIds: ['shape1'],
        },
      })

      expect(errorHelpers.publishError).toHaveBeenCalledWith(
        mockEventBus,
        ErrorType.COORDINATOR_OPERATION_FAILED,
        expect.stringContaining('Failed to delete some shapes'),
        expect.objectContaining({
          failedIds: ['shape2', 'shape3'],
          successfullyDeletedIds: ['shape1'],
        }),
      )
    })

    it('should handle repository removal failures', async () => {
      // Setup repository mocks
      mockRepository.getById.mockImplementation((id: string) => ({ id })) // All shapes exist
      mockRepository.remove.mockImplementation((id: string) => {
        // Only successfully remove shape1
        return id === 'shape1'
      })

      // Create event with valid shapeIds
      const event: ShapeDeleteEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: {
          shapeIds: ['shape1', 'shape2', 'shape3'],
        },
      }

      // Call the method
      await service.handleRequest(event)

      // Verify repository methods were called for each shape
      expect(mockRepository.getById).toHaveBeenCalledTimes(3)
      expect(mockRepository.remove).toHaveBeenCalledTimes(3)

      // Verify both success and error events were published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_DELETE_COMPLETE,
        payload: {
          shapeIds: ['shape1'],
        },
      })

      expect(errorHelpers.publishError).toHaveBeenCalledWith(
        mockEventBus,
        ErrorType.COORDINATOR_OPERATION_FAILED,
        expect.stringContaining('Failed to delete some shapes'),
        expect.objectContaining({
          failedIds: ['shape2', 'shape3'],
          successfullyDeletedIds: ['shape1'],
        }),
      )
    })

    it('should handle unexpected errors', async () => {
      // Setup repository to throw error
      mockRepository.getById.mockImplementation(() => {
        throw new Error('Unexpected repository error')
      })

      // Create event with valid shapeIds
      const event: ShapeDeleteEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: {
          shapeIds: ['shape1', 'shape2'],
        },
      }

      // Call the method
      await service.handleRequest(event)

      // Verify error was published
      expect(errorHelpers.publishError).toHaveBeenCalledWith(
        mockEventBus,
        ErrorType.UNKNOWN,
        'Unexpected repository error',
        expect.objectContaining({
          originalError: expect.any(Error),
        }),
      )

      // Verify no success event was published
      expect(mockEventBus.publish).not.toHaveBeenCalled()
    })
  })

  describe('handleRequest with different scenarios', () => {
    it('should handle multiple shapes successfully', async () => {
      // Setup repository mocks
      mockRepository.getById.mockImplementation((id: string) => ({ id }))
      mockRepository.remove.mockReturnValue(true)

      // Call the method with multiple shape IDs
      await service.handleRequest({
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: { shapeIds: ['shape1', 'shape2'] },
      })

      // Verify repository methods were called
      expect(mockRepository.getById).toHaveBeenCalledTimes(2)
      expect(mockRepository.remove).toHaveBeenCalledTimes(2)

      // Verify success event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_DELETE_COMPLETE,
        payload: { shapeIds: ['shape1', 'shape2'] },
      })
    })

    it('should handle partial success when deleting multiple shapes', async () => {
      // Setup repository mocks
      mockRepository.getById.mockImplementation((id: string) => {
        // Only return shape for shape1, shape3
        return (id === 'shape1' || id === 'shape3') ? { id } : undefined
      })
      mockRepository.remove.mockImplementation((id: string) => {
        // Only successfully remove shape1
        return id === 'shape1'
      })

      // Call the method with multiple shape IDs
      await service.handleRequest({
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: { shapeIds: ['shape1', 'shape2', 'shape3'] },
      })

      // Verify repository methods were called
      expect(mockRepository.getById).toHaveBeenCalledTimes(3)
      expect(mockRepository.remove).toHaveBeenCalledTimes(2) // Only for shape1 and shape3

      // Verify both success and error events were published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_DELETE_COMPLETE,
        payload: { shapeIds: ['shape1'] },
      })

      expect(errorHelpers.publishError).toHaveBeenCalledWith(
        mockEventBus,
        ErrorType.COORDINATOR_OPERATION_FAILED,
        expect.stringContaining('Failed to delete some shapes'),
        expect.objectContaining({
          failedIds: expect.arrayContaining(['shape2', 'shape3']),
          successfullyDeletedIds: ['shape1'],
        }),
      )
    })

    it('should handle complete failure when deleting shapes', async () => {
      // Setup repository mocks
      mockRepository.getById.mockReturnValue(undefined) // No shapes found

      // Call the method with multiple shape IDs
      await service.handleRequest({
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: { shapeIds: ['shape1', 'shape2'] },
      })

      // Verify repository methods were called
      expect(mockRepository.getById).toHaveBeenCalledTimes(2)
      expect(mockRepository.remove).not.toHaveBeenCalled()

      // Verify error event was published
      expect(errorHelpers.publishError).toHaveBeenCalledWith(
        mockEventBus,
        ErrorType.COORDINATOR_OPERATION_FAILED,
        expect.stringContaining('Failed to delete some shapes'),
        expect.objectContaining({
          failedIds: expect.arrayContaining(['shape1', 'shape2']),
          successfullyDeletedIds: [],
        }),
      )

      // Verify no success event was published
      expect(mockEventBus.publish).not.toHaveBeenCalled()
    })

    it('should handle empty array of shape IDs', async () => {
      // Call the method with empty array
      await service.handleRequest({
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: { shapeIds: [] },
      })

      // Verify error was published
      expect(errorHelpers.publishError).toHaveBeenCalledWith(
        mockEventBus,
        ErrorType.INVALID_PAYLOAD,
        expect.stringContaining('Invalid payload for shape delete'),
        expect.anything(),
      )
    })

    it('should handle unexpected errors during deletion', async () => {
      // Setup repository to throw error
      mockRepository.getById.mockImplementation(() => {
        throw new Error('Unexpected error')
      })

      // Call the method
      await service.handleRequest({
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: { shapeIds: ['shape1'] },
      })

      // Verify error was published
      expect(errorHelpers.publishError).toHaveBeenCalledWith(
        mockEventBus,
        ErrorType.UNKNOWN,
        'Unexpected error',
        expect.objectContaining({
          originalError: expect.any(Error),
        }),
      )

      // Verify error was logged
      expect(mockLogger.error).toHaveBeenCalled()
    })
  })

  describe('edge cases', () => {
    it('should handle non-array shapeIds in payload', async () => {
      // Create event with non-array shapeIds
      const event: ShapeDeleteEvent = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: {
          shapeIds: 'shape1' as any,
        },
      }

      // Call the method
      await service.handleRequest(event)

      // Verify error was published
      expect(errorHelpers.publishError).toHaveBeenCalledWith(
        mockEventBus,
        ErrorType.INVALID_PAYLOAD,
        expect.stringContaining('Invalid payload for shape delete'),
        expect.anything(),
      )
    })

    it('should handle missing payload', async () => {
      // Create event with missing payload
      const event = {
        type: AppEventType.SHAPE_DELETE_REQUEST,
      } as ShapeDeleteEvent

      // Call the method
      await service.handleRequest(event)

      // Verify error was published
      expect(errorHelpers.publishError).toHaveBeenCalledWith(
        mockEventBus,
        ErrorType.INVALID_PAYLOAD,
        expect.stringContaining('Invalid payload for shape delete'),
        expect.anything(),
      )
    })

    it('should handle invalid event type', async () => {
      // Create event with invalid type
      const event = {
        type: 'INVALID_EVENT_TYPE' as any,
        payload: {
          shapeIds: ['shape1'],
        },
      }

      // Call the method
      await service.handleRequest(event)

      // The service doesn't specifically check for invalid event types,
      // but it will try to process the request and may fail when accessing
      // repository methods or when shapes are not found
      expect(errorHelpers.publishError).toHaveBeenCalled()
    })
  })
})
