/**
 * @file ElementFactory.additional.spec.ts
 * @description Additional unit tests for ElementFactory class to improve coverage
 */

import type { ShapeCreator } from '@/core/factory/creators/ShapeCreator.interface'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { CoreError } from '@/core/errors'
import { ElementFactory } from '@/core/factory/ElementFactory'
import { Point } from '@/types/core/element/geometry/point'
import { ElementType } from '@/types/core/shape-type'

describe('elementFactory - Additional Tests', () => {
  let factory: ElementFactory

  beforeEach(() => {
    factory = new ElementFactory()
  })

  describe('registerCreator', () => {
    it('should register a creator for a shape type', () => {
      // Arrange
      const mockCreator: ShapeCreator = {
        create: vi.fn(),
        createDefault: vi.fn(),
      }

      // Act
      factory.registerCreator(ElementType.RECTANGLE, mockCreator)

      // Assert - We can't directly test the private creators map, so we'll test indirectly
      // by creating a shape and seeing if our mock is called
      const createSpy = vi.spyOn(mockCreator, 'create').mockResolvedValue({} as any)

      factory.createShape(ElementType.RECTANGLE, {
        id: 'test-rect',
        type: ElementType.RECTANGLE,
        position: new Point(0, 0),
        width: 100,
        height: 50,
      })

      expect(createSpy).toHaveBeenCalled()
    })

    it('should override existing creator for the same shape type', () => {
      // Arrange
      const firstCreator: ShapeCreator = {
        create: vi.fn(),
        createDefault: vi.fn(),
      }

      const secondCreator: ShapeCreator = {
        create: vi.fn(),
        createDefault: vi.fn(),
      }

      // Act
      factory.registerCreator(ElementType.RECTANGLE, firstCreator)
      factory.registerCreator(ElementType.RECTANGLE, secondCreator)

      // Assert - Test indirectly
      const createSpy = vi.spyOn(secondCreator, 'create').mockResolvedValue({} as any)

      factory.createShape(ElementType.RECTANGLE, {
        id: 'test-rect',
        type: ElementType.RECTANGLE,
        position: new Point(0, 0),
        width: 100,
        height: 50,
      })

      expect(createSpy).toHaveBeenCalled()
      expect(firstCreator.create).not.toHaveBeenCalled()
    })
  })

  describe('registerPathFactory', () => {
    it('should register a path factory function', async () => {
      // Arrange
      const mockPathFactory = vi.fn().mockResolvedValue({ id: 'test-path', type: 'custom-path' })

      // Act
      factory.registerPathFactory('custom-path', mockPathFactory)

      // Assert
      const result = await factory.createPath('custom-path', {
        id: 'test-path',
        position: new Point(0, 0),
      })

      expect(mockPathFactory).toHaveBeenCalled()
      expect(result).toEqual({ id: 'test-path', type: 'custom-path' })
    })

    it('should override existing path factory for the same path type', async () => {
      // Arrange
      const firstPathFactory = vi.fn().mockResolvedValue({ id: 'test-path', type: 'custom-path', version: 1 })
      const secondPathFactory = vi.fn().mockResolvedValue({ id: 'test-path', type: 'custom-path', version: 2 })

      // Act
      factory.registerPathFactory('custom-path', firstPathFactory)
      factory.registerPathFactory('custom-path', secondPathFactory)

      // Assert
      const result = await factory.createPath('custom-path', {
        id: 'test-path',
        position: new Point(0, 0),
      })

      expect(firstPathFactory).not.toHaveBeenCalled()
      expect(secondPathFactory).toHaveBeenCalled()
      expect(result).toEqual({ id: 'test-path', type: 'custom-path', version: 2 })
    })
  })

  describe('createDefaultShape', () => {
    it('should call the createDefault method on the correct creator', async () => {
      // Arrange
      const mockCreator: ShapeCreator = {
        create: vi.fn(),
        createDefault: vi.fn().mockResolvedValue({
          id: 'default-rect',
          type: ElementType.RECTANGLE,
          position: new Point(100, 100),
          width: 100,
          height: 50,
        }),
      }

      factory.registerCreator(ElementType.RECTANGLE, mockCreator)

      // Act
      const result = await factory.createDefaultShape(ElementType.RECTANGLE, 'default-rect', new Point(100, 100))

      // Assert
      expect(mockCreator.createDefault).toHaveBeenCalledWith('default-rect', expect.any(Point))
      expect(result).toEqual({
        id: 'default-rect',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 100,
        height: 50,
      })
    })

    it('should handle different position input formats', async () => {
      // Arrange
      const mockCreator: ShapeCreator = {
        create: vi.fn(),
        createDefault: vi.fn().mockResolvedValue({
          id: 'default-rect',
          type: ElementType.RECTANGLE,
        }),
      }

      factory.registerCreator(ElementType.RECTANGLE, mockCreator)

      // Act & Assert - Point object
      await factory.createDefaultShape(ElementType.RECTANGLE, 'rect1', new Point(100, 100))
      expect(mockCreator.createDefault).toHaveBeenLastCalledWith('rect1', expect.objectContaining({ x: 100, y: 100 }))

      // Act & Assert - Plain object
      await factory.createDefaultShape(ElementType.RECTANGLE, 'rect2', { x: 200, y: 200 })
      expect(mockCreator.createDefault).toHaveBeenLastCalledWith('rect2', expect.objectContaining({ x: 200, y: 200 }))

      // Act & Assert - Array
      await factory.createDefaultShape(ElementType.RECTANGLE, 'rect3', [300, 300])
      expect(mockCreator.createDefault).toHaveBeenLastCalledWith('rect3', expect.objectContaining({ x: 300, y: 300 }))
    })

    it('should throw an error if no creator is found for the shape type', async () => {
      // Act & Assert
      await expect(factory.createDefaultShape(ElementType.RECTANGLE, 'rect1', new Point(100, 100)))
        .rejects
        .toThrow(CoreError)
      await expect(factory.createDefaultShape(ElementType.RECTANGLE, 'rect1', new Point(100, 100)))
        .rejects
        .toThrow('No creator registered for shape type: rectangle')
    })
  })

  describe('convenience Methods', () => {
    it('should call createShape with correct parameters for createRectangle', async () => {
      // Arrange
      const createShapeSpy = vi.spyOn(factory, 'createShape').mockResolvedValue({} as any)

      // Act
      await factory.createRectangle('rect1', new Point(100, 100), 200, 100)

      // Assert
      expect(createShapeSpy).toHaveBeenCalledWith(ElementType.RECTANGLE, {
        id: 'rect1',
        type: ElementType.RECTANGLE,
        position: expect.objectContaining({ x: 100, y: 100 }),
        width: 200,
        height: 100,
      })
    })

    it('should call createShape with correct parameters for createSquare', async () => {
      // Arrange
      const createShapeSpy = vi.spyOn(factory, 'createShape').mockResolvedValue({} as any)

      // Act
      await factory.createSquare('square1', new Point(100, 100), 200)

      // Assert
      expect(createShapeSpy).toHaveBeenCalledWith(ElementType.SQUARE, {
        id: 'square1',
        type: ElementType.SQUARE,
        position: expect.objectContaining({ x: 100, y: 100 }),
        width: 200,
        height: 200,
      })
    })

    it('should call createShape with correct parameters for createEllipse', async () => {
      // Arrange
      const createShapeSpy = vi.spyOn(factory, 'createShape').mockResolvedValue({} as any)

      // Act
      await factory.createEllipse('ellipse1', new Point(100, 100), 200, 100)

      // Assert
      expect(createShapeSpy).toHaveBeenCalledWith(ElementType.ELLIPSE, {
        id: 'ellipse1',
        type: ElementType.ELLIPSE,
        position: expect.objectContaining({ x: 100, y: 100 }),
        radiusX: 200,
        radiusY: 100,
      })
    })

    it('should call createShape with correct parameters for createCircle', async () => {
      // Arrange
      const createShapeSpy = vi.spyOn(factory, 'createShape').mockResolvedValue({} as any)

      // Act
      await factory.createCircle('circle1', new Point(100, 100), 200)

      // Assert
      expect(createShapeSpy).toHaveBeenCalledWith(ElementType.CIRCLE, {
        id: 'circle1',
        type: ElementType.CIRCLE,
        position: expect.objectContaining({ x: 100, y: 100 }),
        radius: 200,
      })
    })

    it('should call createShape with correct parameters for createLine', async () => {
      // Arrange
      const createShapeSpy = vi.spyOn(factory, 'createShape').mockResolvedValue({} as any)
      const start = new Point(0, 0)
      const end = new Point(100, 100)

      // Act
      await factory.createLine('line1', start, end)

      // Assert
      expect(createShapeSpy).toHaveBeenCalledWith(ElementType.LINE, {
        id: 'line1',
        type: ElementType.LINE,
        start: expect.objectContaining({ x: 0, y: 0 }),
        end: expect.objectContaining({ x: 100, y: 100 }),
      })
    })

    it('should call createShape with correct parameters for createRegularPolygon', async () => {
      // Arrange
      const createShapeSpy = vi.spyOn(factory, 'createShape').mockResolvedValue({} as any)

      // Act
      await factory.createRegularPolygon('polygon1', new Point(100, 100), 6, 50)

      // Assert
      expect(createShapeSpy).toHaveBeenCalledWith(ElementType.POLYGON, {
        id: 'polygon1',
        type: ElementType.POLYGON,
        center: expect.objectContaining({ x: 100, y: 100 }),
        sides: 6,
        radius: 50,
        points: [],
      })
    })

    it('should call createShape with correct parameters for createCustomPolygon', async () => {
      // Arrange
      const createShapeSpy = vi.spyOn(factory, 'createShape').mockResolvedValue({} as any)
      const points = [
        new Point(0, 0),
        new Point(100, 0),
        new Point(100, 100),
        new Point(0, 100),
      ]

      // Act
      await factory.createCustomPolygon('polygon1', points)

      // Assert
      expect(createShapeSpy).toHaveBeenCalledWith(ElementType.POLYGON, {
        id: 'polygon1',
        type: ElementType.POLYGON,
        points,
      })
    })
  })

  describe('iD Generation', () => {
    it('should generate an ID if none is provided for createShape', async () => {
      // Arrange
      const mockCreator: ShapeCreator = {
        create: vi.fn().mockImplementation(async params => Promise.resolve(params)),
        createDefault: vi.fn(),
      }

      factory.registerCreator(ElementType.RECTANGLE, mockCreator)

      // Act
      const result = await factory.createShape(ElementType.RECTANGLE, {
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
      })

      // Assert
      expect(result.id).toBeDefined()
      expect(result.id).toMatch(/^rectangle-[a-z0-9]+$/)
    })

    it('should generate an ID if none is provided for createPath', async () => {
      // Arrange
      const mockPathFactory = vi.fn().mockImplementation(async params => Promise.resolve(params))
      factory.registerPathFactory('custom-path', mockPathFactory)

      // Act
      const result = await factory.createPath('custom-path', {
        position: new Point(100, 100),
      })

      // Assert
      expect(result.id).toBeDefined()
      expect(result.id).toMatch(/^custom-path-[a-z0-9]+$/)
    })
  })
})
