// Import DesignData for payload checks
import type { DesignData } from '@/types/services/data-types'

// Import the event interfaces
import type {
  DataLoadEvent,
  DataStoreEvent,
  ExportEvent,
  HistoryEvent,
  StorageEvent,
  TemplateEvent,
} from '@/types/services/events/dataEvents'

import { describe, expect, it } from 'vitest'
// Import AppEventType for type checking
import { AppEventType } from '@/types/services/events/eventTypes'

describe('data Operation Event Types', () => {
  it('should allow declaration using DataLoadEvent interface', () => {
    const event: DataLoadEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: DataLoadEvent = {
      type: AppEventType.DATA_LOAD_COMPLETE,
      payload: { designId: 'd1', data: {} as DesignData }, // Mock DesignData
    }
    expect(example.type).toBe(AppEventType.DATA_LOAD_COMPLETE)
    expect(example.payload.designId).toBe('d1')
  })

  it('should allow declaration using HistoryEvent interface', () => {
    const event: HistoryEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: HistoryEvent = {
      type: AppEventType.HISTORY_UNDO,
      payload: { timestamp: Date.now() },
    }
    expect(example.type).toBe(AppEventType.HISTORY_UNDO)
    expect(example.payload.timestamp).toBeDefined()
  })

  it('should allow declaration using TemplateEvent interface', () => {
    const event: TemplateEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: TemplateEvent = {
      type: AppEventType.TEMPLATE_APPLY_REQUEST,
      payload: { templateId: 't-abc' },
    }
    expect(example.type).toBe(AppEventType.TEMPLATE_APPLY_REQUEST)
    expect(example.payload.templateId).toBe('t-abc')
  })

  it('should allow declaration using ExportEvent interface', () => {
    const event: ExportEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: ExportEvent = {
      type: AppEventType.EXPORT_COMPLETE,
      payload: { format: 'png', data: new Blob(['test']) },
    }
    expect(example.type).toBe(AppEventType.EXPORT_COMPLETE)
    expect(example.payload.format).toBe('png')
    expect(example.payload.data).toBeInstanceOf(Blob)
  })

  it('should allow declaration using StorageEvent interface', () => {
    const event: StorageEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: StorageEvent = {
      type: AppEventType.STORAGE_SAVE_COMPLETE,
      payload: { operation: 'save' },
    }
    expect(example.type).toBe(AppEventType.STORAGE_SAVE_COMPLETE)
    expect(example.payload.operation).toBe('save')
  })

  it('should allow declaration using DataStoreEvent interface', () => {
    const event: DataStoreEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: DataStoreEvent = {
      type: AppEventType.STORE_STATE_CHANGE,
      payload: { stateType: 'shapes', data: { shapes: [] } },
    }
    expect(example.type).toBe(AppEventType.STORE_STATE_CHANGE)
    expect(example.payload.stateType).toBe('shapes')
  })
})
