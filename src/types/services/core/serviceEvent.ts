/**
 * Service Event Types
 *
 * This file defines the event interfaces for services.
 *
 * @module types/services/core
 */

/**
 * Base event interface for all service events.
 */
export interface ServiceEvent {
  /** Type of event */
  type: string
  /** Source service identifier */
  source: string
  /** Timestamp when the event occurred */
  timestamp: number
  /** Optional event data */
  data?: unknown
}

/**
 * Event handler type for service events.
 */
export type ServiceEventHandler = (event: ServiceEvent) => void | Promise<void>
