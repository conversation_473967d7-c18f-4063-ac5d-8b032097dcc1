/**
 * Canvas Utilities Index
 *
 * @remarks
 * This module serves as the central entry point for all canvas-related utility functions.
 * It primarily re-exports utilities from `./layerUtils.ts` and potentially other
 * canvas-specific utility files within this directory.
 *
 * If `layerUtils.ts` is the only utility file in this canvas directory, consider
 * whether this index file is necessary or if `layerUtils.ts` could be renamed
 * to `index.ts` or its contents moved here directly.
 *
 * @module lib/utils/canvas/index
 */

export * from './layerUtils' // Assuming layerUtils.ts contains relevant exports
// Add other exports from this directory if needed.
// If layerUtils is the only file, this could just be its content or layerUtils could be renamed to index.ts.
