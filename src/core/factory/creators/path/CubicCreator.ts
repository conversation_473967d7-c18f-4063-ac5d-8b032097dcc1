import type { CreateCubicParams } from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point'
import type {
  ShapeElement,
} from '@/types/core/elementDefinitions'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
/**
 * Creator for Cubic Bezier Curve Path Elements
 *
 * @remarks
 * This class extends {@link PathCreator} to specialize in creating
 * Cubic Bezier curve path elements ({@link CoreElementType.CUBIC}). Cubic Bezier curves
 * are defined by a start point, an end point, and two control points (`control1`, `control2`).
 *
 * The `createSpecificProperties` method constructs the path-specific properties.
 * The `create` method combines common properties from `PathCreator` with specific properties.
 * The `createDefault` method provides a simple way to create a default S-shaped cubic Bezier curve.
 *
 * @module core/factory/creators/path/CubicCreator
 * @see {@link PathCreator}
 * @see {@link CoreElementType.CUBIC}
 * @see {@link CreateCubicParams}
 */
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'
import { PathCreator } from './PathCreator'

/**
 * Creator class for instantiating Cubic Bezier curve path elements.
 * Extends {@link PathCreator}.
 */
export class CubicCreator extends PathCreator<ShapeElement, CreateCubicParams> {
  constructor() {
    super(CoreElementType.CUBIC)
  }

  /**
   * Creates the path-specific properties for a Cubic Bezier curve.
   *
   * @param params - A {@link CreateCubicParams} object.
   * @returns An object containing the path-specific properties like start, control1, control2, end.
   * @throws {@link CoreError} if essential parameters are missing.
   */
  protected createSpecificProperties(params: CreateCubicParams): { start: PointData, control1: PointData, control2: PointData, end: PointData } {
    if (params.start == null || params.control1 == null || params.control2 == null || params.end == null) {
      throw new CoreError(ErrorType.InvalidPayload, 'Cubic curve requires start, control1, control2, and end points.')
    }
    return {
      start: params.start,
      control1: params.control1,
      control2: params.control2,
      end: params.end,
    }
  }

  /**
   * Creates a Cubic Bezier curve {@link ShapeElement} based on the provided parameters.
   *
   * @param params - A {@link CreateCubicParams} object containing all necessary information for creation.
   * @returns A Promise resolving to the created {@link ShapeElement} for the cubic Bezier curve.
   * @throws {@link CoreError} if `params.type` is not 'cubic'.
   */
  public async create(params: CreateCubicParams): Promise<ShapeElement> {
    if (params.type !== CoreElementType.CUBIC && params.type !== 'cubic') {
      throw new CoreError(ErrorType.InvalidPayload, `CubicCreator cannot create type: ${String(params.type)}`)
    }
    if (!params.id) {
      throw new CoreError(ErrorType.InvalidPayload, 'Cubic curve requires an ID.')
    }

    const commonProps = this.createCommonProperties(params.id, params)
    const specificPathPoints = this.createSpecificProperties(params)

    return {
      ...commonProps, // Spreads properties from CommonShapeElementProps
      type: CoreElementType.CUBIC, // Ensure type is correctly CUBIC
      properties: {
        // 设置默认的成本相关属性
        costUnitPrice: 1,
        costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1
        costBasis: 'unit',
        // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
        computedAreaStatus: 'none',
        computedPerimeterStatus: 'none',
        computedLengthStatus: 'none',
        // 然后合并其他属性
        ...(params.properties || {}), // Start with any generic properties from input params
        ...specificPathPoints, // Merge in the path-specific points (start, control1, etc.)
      },
      // Paths don't have fill, only stroke properties
    }
  }

  /**
   * Creates a default Cubic Bezier curve element with predefined geometry.
   *
   * @param id - The unique identifier for the default cubic curve.
   * @param dropPosition - The nominal {@link PointData} for positioning the default curve (center of the S-shape).
   * @param majorCategoryOverride - Optional override for majorCategory.
   * @param minorCategoryOverride - Optional override for minorCategory.
   * @param zLevelIdOverride - Optional override for zLevelId.
   * @param isFixedCategoryOverride - Optional override for isFixedCategory.
   * @returns A Promise resolving to the created default {@link ShapeElement}.
   */
  public async createDefault(
    id: string,
    dropPosition: PointData, // Renamed for clarity, this is the reference/drop position
    majorCategoryOverride?: MajorCategory,
    minorCategoryOverride?: MinorCategory,
    zLevelIdOverride?: string,
    isFixedCategoryOverride?: boolean,
  ): Promise<ShapeElement> {
    // Define points relative to the dropPosition, which will become the center of the S-curve
    const startPt: PointData = { x: dropPosition.x - 50, y: dropPosition.y, z: dropPosition.z }
    const control1Pt: PointData = { x: dropPosition.x - 25, y: dropPosition.y - 50, z: dropPosition.z }
    const control2Pt: PointData = { x: dropPosition.x + 25, y: dropPosition.y + 50, z: dropPosition.z }
    const endPt: PointData = { x: dropPosition.x + 50, y: dropPosition.y, z: dropPosition.z }

    // Get default settings for CUBIC elements
    const { getDefaultSettingsForElementType } = await import('@/config/defaultElementSettings')
    const defaults = getDefaultSettingsForElementType('CUBIC' as any)

    const paramsForCreate: CreateCubicParams = {
      id,
      type: CoreElementType.CUBIC,
      // No 'position' field here. PathCreator.createCommonProperties will use 'startPt' as the ShapeElement.position.
      start: startPt,
      control1: control1Pt,
      control2: control2Pt,
      end: endPt,
      metadata: { name: 'Cubic Curve' },
      majorCategory: majorCategoryOverride ?? MajorCategory.FURNITURE, // Default if no override
      minorCategory: minorCategoryOverride,
      zLevelId: zLevelIdOverride,
      isFixedCategory: isFixedCategoryOverride,
      stroke: defaults.stroke ?? '#000000',
      strokeWidth: defaults.strokeWidth ?? 2,
      opacity: defaults.opacity ?? 1,
      visible: true,
      locked: false,
      rotation: 0,
      selectable: true,
      draggable: true,
      showHandles: true,
      properties: {
        // 设置默认的成本相关属性
        costUnitPrice: 1,
        costMultiplierOrCount: 0,
        costBasis: 'unit', // 默认使用单位计算
      },
    }
    return this.create(paramsForCreate)
  }
}
