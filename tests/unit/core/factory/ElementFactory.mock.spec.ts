/**
 * @file ElementFactory.mock.spec.ts
 * @description Unit tests for the ElementFactory class with mocked creators
 */

import type { PathCreator } from '@/core/factory/creators/PathCreator.interface'
import type { ShapeCreator } from '@/core/factory/creators/ShapeCreator.interface'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ElementFactory } from '@/core/factory/ElementFactory'
import { Point } from '@/types/core/element/geometry/point'
import { ElementType } from '@/types/core/shape-type'

// Mock console methods
const mockConsoleWarn = vi.spyOn(console, 'warn').mockImplementation(() => {})
const mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => {})

describe('elementFactory with Mocked Creators', () => {
  let factory: ElementFactory
  let mockRectangleCreator: ShapeCreator
  let mockEllipseCreator: ShapeCreator
  let mockPolygonCreator: ShapeCreator
  let mockLineCreator: ShapeCreator
  let mockPolylineCreator: ShapeCreator
  let mockArcCreator: PathCreator

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()

    // Create mock creators
    mockRectangleCreator = {
      create: vi.fn().mockImplementation(async params => Promise.resolve({
        id: params.id,
        type: params.type,
        position: params.position,
        properties: {
          width: params.width,
          height: params.height,
        },
      })),
      createDefault: vi.fn().mockImplementation(async (id, position) => Promise.resolve({
        id,
        type: ElementType.RECTANGLE,
        position,
        properties: {
          width: 100,
          height: 50,
        },
      })),
    }

    mockEllipseCreator = {
      create: vi.fn().mockImplementation(async params => Promise.resolve({
        id: params.id,
        type: params.type,
        position: params.position,
        properties: {
          radiusX: params.radiusX || params.radius,
          radiusY: params.radiusY || params.radius,
        },
      })),
      createDefault: vi.fn().mockImplementation(async (id, position) => Promise.resolve({
        id,
        type: ElementType.ELLIPSE,
        position,
        properties: {
          radiusX: 50,
          radiusY: 30,
        },
      })),
    }

    mockPolygonCreator = {
      create: vi.fn().mockImplementation(async params => Promise.resolve({
        id: params.id,
        type: params.type,
        position: params.position,
        properties: {
          points: params.points || [],
          sides: params.sides,
          radius: params.radius,
        },
      })),
      createDefault: vi.fn().mockImplementation(async (id, position) => Promise.resolve({
        id,
        type: ElementType.POLYGON,
        position,
        properties: {
          sides: 6,
          radius: 50,
        },
      })),
    }

    mockLineCreator = {
      create: vi.fn().mockImplementation(async params => Promise.resolve({
        id: params.id,
        type: params.type,
        position: params.position,
        properties: {
          start: params.start,
          end: params.end,
        },
      })),
      createDefault: vi.fn().mockImplementation(async (id, position) => Promise.resolve({
        id,
        type: ElementType.LINE,
        position,
        properties: {
          start: { x: 0, y: 0 },
          end: { x: 100, y: 100 },
        },
      })),
    }

    mockPolylineCreator = {
      create: vi.fn().mockImplementation(async params => Promise.resolve({
        id: params.id,
        type: params.type,
        position: params.position,
        properties: {
          points: params.points || [],
        },
      })),
      createDefault: vi.fn().mockImplementation(async (id, position) => Promise.resolve({
        id,
        type: ElementType.POLYLINE,
        position,
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 50, y: 50 },
            { x: 100, y: 0 },
          ],
        },
      })),
    }

    mockArcCreator = {
      create: vi.fn().mockImplementation(async params => Promise.resolve({
        id: params.id,
        type: 'arc',
        position: params.position,
        properties: {
          radius: params.radius,
          startAngle: params.startAngle,
          endAngle: params.endAngle,
        },
      })),
    }

    // Create factory with mock creators
    factory = new ElementFactory()

    // Register mock creators using the public API
    factory.registerCreator(ElementType.RECTANGLE, mockRectangleCreator)
    factory.registerCreator(ElementType.SQUARE, mockRectangleCreator)
    factory.registerCreator(ElementType.ELLIPSE, mockEllipseCreator)
    factory.registerCreator(ElementType.CIRCLE, mockEllipseCreator)
    factory.registerCreator(ElementType.POLYGON, mockPolygonCreator)
    factory.registerCreator(ElementType.TRIANGLE, mockPolygonCreator)
    factory.registerCreator(ElementType.HEXAGON, mockPolygonCreator)
    factory.registerCreator(ElementType.LINE, mockLineCreator)
    factory.registerCreator(ElementType.POLYLINE, mockPolylineCreator)

    // Register mock path factory
    factory.registerPathFactory('arc', params => mockArcCreator.create(params))
  })

  describe('createShape', () => {
    it('should call the correct creator for Rectangle', async () => {
      // Arrange
      const params = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
        width: 200,
        height: 100,
      }

      // Act
      const result = await factory.createShape(params.type, params)

      // Assert
      expect(mockRectangleCreator.create).toHaveBeenCalledWith(params)
      expect(result).toEqual({
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
        properties: {
          width: 200,
          height: 100,
        },
      })
    })

    it('should call the correct creator for Ellipse', async () => {
      // Arrange
      const params = {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 100, y: 100 },
        radiusX: 100,
        radiusY: 50,
      }

      // Act
      const result = await factory.createShape(params.type, params)

      // Assert
      expect(mockEllipseCreator.create).toHaveBeenCalledWith(params)
      expect(result).toEqual({
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 100, y: 100 },
        properties: {
          radiusX: 100,
          radiusY: 50,
        },
      })
    })

    it('should call the correct creator for Circle (using EllipseCreator)', async () => {
      // Arrange
      const params = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: { x: 100, y: 100 },
        radius: 50,
      }

      // Act
      const result = await factory.createShape(params.type, params)

      // Assert
      expect(mockEllipseCreator.create).toHaveBeenCalledWith(params)
      expect(result).toEqual({
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: { x: 100, y: 100 },
        properties: {
          radiusX: 50,
          radiusY: 50,
        },
      })
    })

    it('should call the correct creator for Polygon', async () => {
      // Arrange
      const params = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 100, y: 100 },
        sides: 6,
        radius: 50,
      }

      // Act
      const result = await factory.createShape(params.type, params)

      // Assert
      expect(mockPolygonCreator.create).toHaveBeenCalledWith(params)
      expect(result).toEqual({
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 100, y: 100 },
        properties: {
          points: [],
          sides: 6,
          radius: 50,
        },
      })
    })

    it('should call the correct creator for Triangle (using PolygonCreator)', async () => {
      // Arrange
      const params = {
        id: 'triangle-1',
        type: ElementType.TRIANGLE,
        position: { x: 100, y: 100 },
        radius: 50,
      }

      // Act
      const result = await factory.createShape(params.type, params)

      // Assert
      expect(mockPolygonCreator.create).toHaveBeenCalledWith(params)
      expect(result).toEqual({
        id: 'triangle-1',
        type: ElementType.TRIANGLE,
        position: { x: 100, y: 100 },
        properties: {
          points: [],
          sides: undefined,
          radius: 50,
        },
      })
    })

    it('should call the correct creator for Line', async () => {
      // Arrange
      const params = {
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 100, y: 100 },
        start: { x: 0, y: 0 },
        end: { x: 200, y: 200 },
      }

      // Act
      const result = await factory.createShape(params.type, params)

      // Assert
      expect(mockLineCreator.create).toHaveBeenCalledWith(params)
      expect(result).toEqual({
        id: 'line-1',
        type: ElementType.LINE,
        position: { x: 100, y: 100 },
        properties: {
          start: { x: 0, y: 0 },
          end: { x: 200, y: 200 },
        },
      })
    })

    it('should call the correct creator for Polyline', async () => {
      // Arrange
      const params = {
        id: 'polyline-1',
        type: ElementType.POLYLINE,
        position: { x: 100, y: 100 },
        points: [
          { x: 0, y: 0 },
          { x: 50, y: 50 },
          { x: 100, y: 0 },
        ],
      }

      // Act
      const result = await factory.createShape(params.type, params)

      // Assert
      expect(mockPolylineCreator.create).toHaveBeenCalledWith(params)
      expect(result).toEqual({
        id: 'polyline-1',
        type: ElementType.POLYLINE,
        position: { x: 100, y: 100 },
        properties: {
          points: [
            { x: 0, y: 0 },
            { x: 50, y: 50 },
            { x: 100, y: 0 },
          ],
        },
      })
    })

    it.skip('should generate an ID if none is provided', async () => {
      // Arrange
      const params = {
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
        width: 200,
        height: 100,
      }

      // Mock the create method to capture the ID generation
      mockRectangleCreator.create.mockImplementation(async (params) => {
        // Verify ID was generated
        expect(params.id).toBeDefined()
        expect(params.id).toMatch(/^rectangle-[a-z0-9]+$/)
        return Promise.resolve({
          id: params.id,
          type: params.type,
          position: params.position,
          properties: {
            width: params.width,
            height: params.height,
          },
        })
      })

      // Act
      const result = await factory.createShape(params.type, params)

      // Assert
      expect(mockRectangleCreator.create).toHaveBeenCalled()
    })

    it('should throw an error if no creator is found for the shape type', async () => {
      // Arrange
      const params = {
        id: 'unknown-1',
        type: 'unknown-shape' as ElementType,
        position: { x: 100, y: 100 },
      }

      // Act & Assert
      await expect(factory.createShape(params.type, params)).rejects.toThrow('No creator registered for shape type: unknown-shape')
    })
  })

  describe('createPath', () => {
    it('should call the correct creator for path type', async () => {
      // Arrange
      const params = {
        id: 'arc-1',
        position: { x: 100, y: 100 },
        radius: 50,
        startAngle: 0,
        endAngle: 180,
      }

      // Act
      const result = await factory.createPath('arc', params)

      // Assert
      expect(mockArcCreator.create).toHaveBeenCalledWith(params)
      expect(result).toEqual({
        id: 'arc-1',
        type: 'arc',
        position: { x: 100, y: 100 },
        properties: {
          radius: 50,
          startAngle: 0,
          endAngle: 180,
        },
      })
    })

    it.skip('should generate an ID if none is provided for path', async () => {
      // Arrange
      const params = {
        position: { x: 100, y: 100 },
        radius: 50,
        startAngle: 0,
        endAngle: 180,
      }

      // Mock the create method to capture the ID generation
      mockArcCreator.create.mockImplementation(async (params) => {
        // Verify ID was generated
        expect(params.id).toBeDefined()
        expect(params.id).toMatch(/^arc-[a-z0-9]+$/)
        return Promise.resolve({
          id: params.id,
          type: 'arc',
          position: params.position,
          properties: {
            radius: params.radius,
            startAngle: params.startAngle,
            endAngle: params.endAngle,
          },
        })
      })

      // Act
      const result = await factory.createPath('arc', params)

      // Assert
      expect(mockArcCreator.create).toHaveBeenCalled()
    })

    it('should throw an error if no creator is found for the path type', async () => {
      // Arrange
      const params = {
        id: 'unknown-1',
        position: { x: 100, y: 100 },
      }

      // Act & Assert
      await expect(factory.createPath('unknown-path', params)).rejects.toThrow('No factory registered for path type: unknown-path')
    })
  })

  describe('convenience Methods', () => {
    it('createRectangle should call createShape with correct params', async () => {
      // Arrange
      const createShapeSpy = vi.spyOn(factory, 'createShape')

      // Act
      await factory.createRectangle('rect-1', { x: 100, y: 100 }, 200, 100)

      // Assert
      expect(createShapeSpy).toHaveBeenCalledWith(ElementType.RECTANGLE, {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
        width: 200,
        height: 100,
      })
    })

    it('createSquare should call createShape with correct params', async () => {
      // Arrange
      const createShapeSpy = vi.spyOn(factory, 'createShape')

      // Act
      await factory.createSquare('square-1', { x: 100, y: 100 }, 100)

      // Assert
      expect(createShapeSpy).toHaveBeenCalledWith(ElementType.SQUARE, {
        id: 'square-1',
        type: ElementType.SQUARE,
        position: { x: 100, y: 100 },
        width: 100,
        height: 100,
      })
    })

    it('createEllipse should call createShape with correct params', async () => {
      // Arrange
      const createShapeSpy = vi.spyOn(factory, 'createShape')

      // Act
      await factory.createEllipse('ellipse-1', { x: 100, y: 100 }, 100, 50)

      // Assert
      expect(createShapeSpy).toHaveBeenCalledWith(ElementType.ELLIPSE, {
        id: 'ellipse-1',
        type: ElementType.ELLIPSE,
        position: { x: 100, y: 100 },
        radiusX: 100,
        radiusY: 50,
      })
    })

    it('createCircle should call createShape with correct params', async () => {
      // Arrange
      const createShapeSpy = vi.spyOn(factory, 'createShape')

      // Act
      await factory.createCircle('circle-1', { x: 100, y: 100 }, 50)

      // Assert
      expect(createShapeSpy).toHaveBeenCalledWith(ElementType.CIRCLE, {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: { x: 100, y: 100 },
        radius: 50,
      })
    })

    it('createLine should call createShape with correct params', async () => {
      // Arrange
      const createShapeSpy = vi.spyOn(factory, 'createShape')

      // Act
      await factory.createLine('line-1', { x: 0, y: 0 }, { x: 200, y: 200 })

      // Assert
      expect(createShapeSpy).toHaveBeenCalledWith(ElementType.LINE, {
        id: 'line-1',
        type: ElementType.LINE,
        start: { x: 0, y: 0 },
        end: { x: 200, y: 200 },
      })
    })

    it('createRegularPolygon should call createShape with correct params', async () => {
      // Arrange
      const createShapeSpy = vi.spyOn(factory, 'createShape')

      // Act
      await factory.createRegularPolygon('polygon-1', { x: 100, y: 100 }, 6, 50)

      // Assert
      expect(createShapeSpy).toHaveBeenCalledWith(ElementType.POLYGON, {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        points: [],
        sides: 6,
        radius: 50,
        center: { x: 100, y: 100 },
      })
    })

    it('createCustomPolygon should call createShape with correct params', async () => {
      // Arrange
      const createShapeSpy = vi.spyOn(factory, 'createShape')
      const points = [
        { x: 0, y: 0 },
        { x: 50, y: 50 },
        { x: 100, y: 0 },
      ]

      // Act
      await factory.createCustomPolygon('polygon-1', points)

      // Assert
      expect(createShapeSpy).toHaveBeenCalledWith(ElementType.POLYGON, {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        points,
      })
    })
  })

  describe('createDefaultShape', () => {
    it('should call the createDefault method on the correct creator', async () => {
      // Arrange
      const id = 'default-rect'
      const position = new Point(100, 100)

      // Act
      const result = await factory.createDefaultShape(ElementType.RECTANGLE, id, position)

      // Assert
      expect(mockRectangleCreator.createDefault).toHaveBeenCalledWith(id, position)
      expect(result).toEqual({
        id,
        type: ElementType.RECTANGLE,
        position,
        properties: {
          width: 100,
          height: 50,
        },
      })
    })

    it('should throw an error if no creator is found for the shape type', async () => {
      // Arrange
      const id = 'unknown-1'
      const position = new Point(100, 100)

      // Act & Assert
      await expect(factory.createDefaultShape('unknown-shape' as ElementType, id, position))
        .rejects
        .toThrow('No creator registered for shape type: unknown-shape')
    })

    it('should handle Point instance as position', async () => {
      // Arrange
      const id = 'default-rect'
      const position = new Point(100, 100)

      // Act
      await factory.createDefaultShape(ElementType.RECTANGLE, id, position)

      // Assert
      expect(mockRectangleCreator.createDefault).toHaveBeenCalledWith(id, position)
    })

    it('should handle object as position', async () => {
      // Arrange
      const id = 'default-rect'
      const position = { x: 100, y: 100 }

      // Act
      await factory.createDefaultShape(ElementType.RECTANGLE, id, position)

      // Assert
      expect(mockRectangleCreator.createDefault).toHaveBeenCalledWith(
        id,
        expect.objectContaining({ x: 100, y: 100 }),
      )
    })

    it('should handle array as position', async () => {
      // Arrange
      const id = 'default-rect'
      const position = [100, 100]

      // Act
      await factory.createDefaultShape(ElementType.RECTANGLE, id, position)

      // Assert
      expect(mockRectangleCreator.createDefault).toHaveBeenCalledWith(
        id,
        expect.objectContaining({ x: 100, y: 100 }),
      )
    })
  })
})
