/**
 * Compute Properties Types
 *
 * Defines interfaces for computation-related properties that can be stored
 * in element properties. These include calculation results, status information,
 * and error details for geometry and cost calculations.
 *
 * @module types/core/computeProperties
 */

import type { ComputeStatus } from './computeStatus'

/**
 * Interface for geometry calculation properties stored in element.properties.
 *
 * @remarks
 * These properties store the results of geometry calculations along with
 * their status and any error information. They are persisted with the element
 * and used to provide visual feedback in the UI.
 */
export interface GeometryComputeProperties {
  // Area calculation
  /** Computed area value in square pixels */
  computedArea?: number
  /** Status of the area calculation */
  computedAreaStatus?: ComputeStatus
  /** Error message if area calculation failed */
  computedAreaError?: string
  /** Unit for the area calculation (default: 'mm²') */
  computedAreaUnit?: string

  // Perimeter calculation
  /** Computed perimeter value in pixels */
  computedPerimeter?: number
  /** Status of the perimeter calculation */
  computedPerimeterStatus?: ComputeStatus
  /** Error message if perimeter calculation failed */
  computedPerimeterError?: string
  /** Unit for the perimeter calculation (default: 'mm') */
  computedPerimeterUnit?: string

  // Length calculation (for path elements)
  /** Computed length value in pixels */
  computedLength?: number
  /** Status of the length calculation */
  computedLengthStatus?: ComputeStatus
  /** Error message if length calculation failed */
  computedLengthError?: string
  /** Unit for the length calculation (default: 'mm') */
  computedLengthUnit?: string
}

/**
 * Interface for cost calculation properties stored in element.properties.
 */
export interface CostComputeProperties {
  /** Total calculated cost */
  costTotal?: number
  /** Unit price for cost calculation */
  costUnitPrice?: number
  /** Basis for cost calculation ('area' | 'perimeter' | 'length' | 'unit') */
  costBasis?: string
  /** Multiplier or count for cost calculation */
  costMultiplierOrCount?: number
  /** Status of the cost calculation */
  costStatus?: ComputeStatus
  /** Error message if cost calculation failed */
  costError?: string
  /** Whether cost calculation is enabled for this element */
  costEnabled?: boolean
}

/**
 * Combined interface for all computation-related properties.
 *
 * @remarks
 * This interface combines geometry and cost computation properties
 * and can be used to type the properties field of elements that
 * support computation features.
 */
export interface ComputeProperties extends GeometryComputeProperties, CostComputeProperties {
  // Additional computation-related properties can be added here

  /** Timestamp when calculations were last performed */
  lastComputeTimestamp?: number
  /** Version of the computation engine used */
  computeEngineVersion?: string
}

/**
 * Type guard to check if an object has geometry compute properties.
 */
export function hasGeometryComputeProperties(obj: unknown): obj is GeometryComputeProperties {
  if (!obj || typeof obj !== 'object')
    return false

  const props = obj as Record<string, unknown>
  return (
    'computedArea' in props
    || 'computedPerimeter' in props
    || 'computedLength' in props
    || 'computedAreaStatus' in props
    || 'computedPerimeterStatus' in props
    || 'computedLengthStatus' in props
  )
}

/**
 * Type guard to check if an object has cost compute properties.
 */
export function hasCostComputeProperties(obj: unknown): obj is CostComputeProperties {
  if (!obj || typeof obj !== 'object')
    return false

  const props = obj as Record<string, unknown>
  return (
    'costTotal' in props
    || 'costUnitPrice' in props
    || 'costBasis' in props
    || 'costStatus' in props
    || 'costEnabled' in props
  )
}

/**
 * Helper function to extract geometry compute properties from element properties.
 */
export function extractGeometryComputeProperties(properties: Record<string, unknown> | undefined): GeometryComputeProperties {
  if (!properties)
    return {}

  return {
    computedArea: properties.computedArea as number | undefined,
    computedAreaStatus: properties.computedAreaStatus as ComputeStatus | undefined,
    computedAreaError: properties.computedAreaError as string | undefined,
    computedAreaUnit: properties.computedAreaUnit as string | undefined,

    computedPerimeter: properties.computedPerimeter as number | undefined,
    computedPerimeterStatus: properties.computedPerimeterStatus as ComputeStatus | undefined,
    computedPerimeterError: properties.computedPerimeterError as string | undefined,
    computedPerimeterUnit: properties.computedPerimeterUnit as string | undefined,

    computedLength: properties.computedLength as number | undefined,
    computedLengthStatus: properties.computedLengthStatus as ComputeStatus | undefined,
    computedLengthError: properties.computedLengthError as string | undefined,
    computedLengthUnit: properties.computedLengthUnit as string | undefined,
  }
}

/**
 * Helper function to extract cost compute properties from element properties.
 */
export function extractCostComputeProperties(properties: Record<string, unknown> | undefined): CostComputeProperties {
  if (!properties)
    return {}

  return {
    costTotal: properties.costTotal as number | undefined,
    costUnitPrice: properties.costUnitPrice as number | undefined,
    costBasis: properties.costBasis as string | undefined,
    costMultiplierOrCount: properties.costMultiplierOrCount as number | undefined,
    costStatus: properties.costStatus as ComputeStatus | undefined,
    costError: properties.costError as string | undefined,
    costEnabled: properties.costEnabled as boolean | undefined,
  }
}
