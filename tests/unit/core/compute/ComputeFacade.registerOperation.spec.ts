import type { ComputeOptions } from '../../../../src/types/core/element/compute'
/**
 * Unit tests for ComputeFacade registerOperation functionality
 */
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ComputeFacade } from '../../../../src/core/compute/ComputeFacade'
import { ComputeOperation } from '../../../../src/types/core/element/compute'

// Mock dependencies
const mockStrategyRegistry = {
  getAreaStrategy: vi.fn(),
  getPerimeterStrategy: vi.fn(),
  getBoundingBoxStrategy: vi.fn(),
  getIsPointInsideStrategy: vi.fn(),
  getTransformStrategy: vi.fn(),
  hasStrategy: vi.fn().mockReturnValue(true),
}

const mockShapeRepository = {
  getById: vi.fn(),
  update: vi.fn(),
}

const mockElementFactory = {
  createShape: vi.fn(),
}

// Mock console methods
const mockConsoleLog = vi.fn()
const mockConsoleWarn = vi.fn()
const mockConsoleError = vi.fn()
const mockConsoleDebug = vi.fn()

describe('computeFacade - registerOperation', () => {
  let computeFacade: ComputeFacade

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()

    // Mock console methods
    Object.defineProperty(console, 'log', { value: mockConsoleLog })
    Object.defineProperty(console, 'warn', { value: mockConsoleWarn })
    Object.defineProperty(console, 'error', { value: mockConsoleError })
    Object.defineProperty(console, 'debug', { value: mockConsoleDebug })

    // Mock performance.now
    vi.spyOn(performance, 'now').mockReturnValue(100)

    // Create facade
    computeFacade = new ComputeFacade(
      mockStrategyRegistry as any,
      mockShapeRepository as any,
      mockElementFactory as any,
    )
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('registerOperation', () => {
    it('should register a custom operation', () => {
      // Arrange
      const customOperation = async (_shapeIds: string[], _options?: ComputeOptions) => 42

      // Act
      computeFacade.registerOperation('customOp', customOperation)

      // Assert
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Registered custom operation'))
    })

    it('should warn when overwriting an existing custom operation', () => {
      // Arrange
      const customOperation1 = async (_shapeIds: string[], _options?: ComputeOptions) => 42
      const customOperation2 = async (_shapeIds: string[], _options?: ComputeOptions) => 84

      // Act
      computeFacade.registerOperation('customOp', customOperation1)
      computeFacade.registerOperation('customOp', customOperation2)

      // Assert
      expect(mockConsoleWarn).toHaveBeenCalledWith(expect.stringContaining('Overwriting existing'))
    })

    it('should warn when overwriting a standard operation', () => {
      // Arrange
      const customOperation = async (_shapeIds: string[], _options?: ComputeOptions) => 42

      // Act
      computeFacade.registerOperation(ComputeOperation.AREA, customOperation)

      // Assert
      expect(mockConsoleWarn).toHaveBeenCalledWith(expect.stringContaining('Overwriting existing'))
    })

    it('should execute a registered custom operation', async () => {
      // Arrange
      const customOperation = vi.fn().mockResolvedValue(42)
      computeFacade.registerOperation('customOp', customOperation)

      // Act
      const result = await computeFacade.compute('customOp', ['rect-1'], { precision: 2 })

      // Assert
      expect(customOperation).toHaveBeenCalledWith(['rect-1'], { precision: 2 })
      expect(result).toMatchObject({
        operation: 'customOp',
        result: 42,
        metadata: {
          precision: 2,
          shapeIds: ['rect-1'],
          options: { precision: 2 },
        },
      })
      expect(result.metadata).toHaveProperty('executionTime')
    })

    it('should handle custom operation that matches standard operation name', async () => {
      // Arrange
      const customOperation = vi.fn().mockResolvedValue(42)
      computeFacade.registerOperation(ComputeOperation.AREA, customOperation)

      // Act
      const result = await computeFacade.compute(ComputeOperation.AREA, ['rect-1'])

      // Assert
      expect(customOperation).toHaveBeenCalled()
      expect(result.operation).toBe(ComputeOperation.AREA) // Should keep standard operation type
    })

    it('should handle missing custom operation function', async () => {
      // Arrange
      // Mock Map.prototype methods to simulate inconsistent state
      const originalHas = Map.prototype.has
      const originalGet = Map.prototype.get

      Map.prototype.has = vi.fn().mockReturnValue(true)
      Map.prototype.get = vi.fn().mockReturnValue(undefined)

      try {
        // Act & Assert
        await expect(computeFacade.compute('missingOp', ['rect-1'])).rejects.toThrow()
      }
      finally {
        // Restore original methods
        Map.prototype.has = originalHas
        Map.prototype.get = originalGet
      }
    })
  })

  describe('isComputationSupported', () => {
    it('should return true for registered custom operations', () => {
      // Arrange
      const customOperation = async (_shapeIds: string[], _options?: ComputeOptions) => 42
      computeFacade.registerOperation('customOp', customOperation)

      // Act
      const result = computeFacade.isComputationSupported('customOp', 'rectangle')

      // Assert
      expect(result).toBe(true)
    })

    it('should check strategy registry for standard operations', () => {
      // Arrange
      mockStrategyRegistry.hasStrategy.mockReturnValue(true)

      // Act
      const result = computeFacade.isComputationSupported(ComputeOperation.AREA, 'rectangle')

      // Assert
      expect(mockStrategyRegistry.hasStrategy).toHaveBeenCalledWith('rectangle', 'area')
      expect(result).toBe(true)
    })

    it('should return false for unrecognized operations', () => {
      // Act
      const result = computeFacade.isComputationSupported('unknownOp', 'rectangle')

      // Assert
      expect(result).toBe(false)
      expect(mockConsoleWarn).toHaveBeenCalled()
    })
  })
})
