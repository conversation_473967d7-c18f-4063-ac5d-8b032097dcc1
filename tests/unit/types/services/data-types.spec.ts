// Import ShapeModel for DesignData check
import type { ShapeModel } from '@/types/core/models'

// Import the interfaces
import type {
  DesignData,
  LayerData,
} from '@/types/services/data-types'

import { describe, expect, it } from 'vitest'

describe('service Data Types', () => {
  it('should allow declaration using LayerData interface', () => {
    const layer: LayerData | undefined = undefined
    expect(layer).toBeUndefined()

    // Example usage
    const floorLayer: LayerData = {
      id: 'floor-1',
      name: 'Ground Floor',
      visible: true,
    }
    expect(floorLayer.id).toBe('floor-1')
  })

  it('should allow declaration using DesignData interface', () => {
    const design: DesignData | undefined = undefined
    expect(design).toBeUndefined()

    // Example usage with placeholder shapes/layers
    const myDesign: DesignData = {
      id: 'design-abc',
      name: 'My First Design',
      width: 800,
      height: 600,
      shapes: [] as ShapeModel[], // Needs ShapeModel import
      layers: [{ id: 'l1', name: 'Layer 1', visible: true }],
      metadata: { version: '1.0' },
    }
    expect(myDesign.name).toBe('My First Design')
    expect(myDesign.shapes).toBeInstanceOf(Array)
    expect(myDesign.layers).toHaveLength(1)
  })
})
