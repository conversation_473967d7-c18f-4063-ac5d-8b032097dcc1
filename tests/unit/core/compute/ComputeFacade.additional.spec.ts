import type { ComputeOperationType, TransformOptions } from '../../../../src/types/core/element/compute'
/**
 * Additional unit tests for ComputeFacade
 */
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ComputeFacade } from '../../../../src/core/compute/ComputeFacade'
import { CoreError } from '../../../../src/core/errors'
import { ComputeOperation } from '../../../../src/types/core/element/compute'

// Mock dependencies
const mockGetAreaStrategy = vi.fn()
const mockGetPerimeterStrategy = vi.fn()
const mockGetBoundingBoxStrategy = vi.fn()
const mockGetIsPointInsideStrategy = vi.fn()
const mockGetTransformStrategy = vi.fn()
const mockHasStrategy = vi.fn()

const mockShapeRepository = {
  getById: vi.fn(),
  update: vi.fn(),
}

const mockElementFactory = {
  createShape: vi.fn(),
}

// Mock console methods
const mockConsoleLog = vi.fn()
const mockConsoleWarn = vi.fn()
const mockConsoleError = vi.fn()
const mockConsoleDebug = vi.fn()

describe('computeFacade - Additional Tests', () => {
  let computeFacade: ComputeFacade
  let mockStrategyRegistry: any
  let mockAreaStrategy: any
  let mockPerimeterStrategy: any
  let mockBoundingBoxStrategy: any
  let mockIsPointInsideStrategy: any
  let mockTransformStrategy: any
  let mockElement: any

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()

    // Mock console methods
    Object.defineProperty(console, 'log', { value: mockConsoleLog })
    Object.defineProperty(console, 'warn', { value: mockConsoleWarn })
    Object.defineProperty(console, 'error', { value: mockConsoleError })
    Object.defineProperty(console, 'debug', { value: mockConsoleDebug })

    // Mock performance.now
    vi.spyOn(performance, 'now').mockReturnValue(100)

    // Create mock strategies
    mockAreaStrategy = {
      calculateArea: vi.fn().mockReturnValue(100),
    }

    mockPerimeterStrategy = {
      calculatePerimeter: vi.fn().mockReturnValue(40),
    }

    mockBoundingBoxStrategy = {
      calculateBoundingBox: vi.fn().mockReturnValue({
        x: 0,
        y: 0,
        width: 100,
        height: 100,
      }),
    }

    mockIsPointInsideStrategy = {
      isPointInside: vi.fn().mockReturnValue(true),
    }

    mockTransformStrategy = {
      execute: vi.fn().mockResolvedValue({
        toJson: vi.fn().mockReturnValue({ id: 'rect-1', type: 'rectangle' }),
      }),
    }

    // Setup mock returns
    mockGetAreaStrategy.mockReturnValue(mockAreaStrategy)
    mockGetPerimeterStrategy.mockReturnValue(mockPerimeterStrategy)
    mockGetBoundingBoxStrategy.mockReturnValue(mockBoundingBoxStrategy)
    mockGetIsPointInsideStrategy.mockReturnValue(mockIsPointInsideStrategy)
    mockGetTransformStrategy.mockReturnValue(mockTransformStrategy)
    mockHasStrategy.mockReturnValue(true)

    // Create mock element
    mockElement = {
      getSubType: vi.fn().mockReturnValue('rectangle'),
      getId: vi.fn().mockReturnValue('rect-1'),
    }

    // Setup mock repository
    mockShapeRepository.getById.mockReturnValue({ id: 'rect-1', type: 'rectangle' })

    // Setup mock factory
    mockElementFactory.createShape.mockResolvedValue(mockElement)

    // Create mock strategy registry
    mockStrategyRegistry = {
      getAreaStrategy: mockGetAreaStrategy,
      getPerimeterStrategy: mockGetPerimeterStrategy,
      getBoundingBoxStrategy: mockGetBoundingBoxStrategy,
      getIsPointInsideStrategy: mockGetIsPointInsideStrategy,
      getTransformStrategy: mockGetTransformStrategy,
      hasStrategy: mockHasStrategy,
    }

    // Create facade
    computeFacade = new ComputeFacade(
      mockStrategyRegistry,
      mockShapeRepository as any,
      mockElementFactory as any,
    )
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('compute method edge cases', () => {
    it('should throw error for empty shapeIds array', async () => {
      await expect(computeFacade.compute(ComputeOperation.AREA, [])).rejects.toThrow(CoreError)
      expect(mockConsoleError).not.toHaveBeenCalled() // This is a validation error, not logged
    })

    it('should throw error for unregistered operations', async () => {
      await expect(computeFacade.compute('unknownOp', ['rect-1'])).rejects.toThrow(CoreError)
      // Note: In the actual implementation, console.error might not be called for validation errors
      // so we're not asserting that here
    })

    it('should throw error for is-point-inside without coordinates', async () => {
      await expect(computeFacade.compute(
        ComputeOperation.IS_POINT_INSIDE,
        ['rect-1'],
      )).rejects.toThrow(CoreError)
    })

    it('should throw error for transform without options', async () => {
      await expect(computeFacade.compute(
        ComputeOperation.TRANSFORM,
        ['rect-1'],
      )).rejects.toThrow(CoreError)
    })

    it('should handle unrecognized standard operation', async () => {
      // Create an unhandled operation type
      const unhandledOp = 'UNHANDLED_OP' as ComputeOperationType

      // Modify Object.values to include our custom operation
      const originalValues = Object.values
      Object.values = vi.fn().mockReturnValue([...Object.values(ComputeOperation), unhandledOp])

      try {
        await expect(computeFacade.compute(unhandledOp, ['rect-1'])).rejects.toThrow(CoreError)
      }
      finally {
        // Restore original Object.values
        Object.values = originalValues
      }
    })

    it('should wrap non-CoreError exceptions', async () => {
      mockAreaStrategy.calculateArea.mockImplementation(() => {
        throw new Error('Strategy error')
      })

      await expect(computeFacade.compute(ComputeOperation.AREA, ['rect-1'])).rejects.toThrow(CoreError)
      expect(mockConsoleError).toHaveBeenCalled()
    })
  })

  describe('transformElement', () => {
    it('should transform elements correctly', async () => {
      const options: TransformOptions = { type: 'translate', x: 10, y: 20 }

      await computeFacade.transformElement(['rect-1', 'rect-2'], options)

      expect(mockShapeRepository.getById).toHaveBeenCalledTimes(2)
      expect(mockElementFactory.createShape).toHaveBeenCalledTimes(2)
      expect(mockGetTransformStrategy).toHaveBeenCalledTimes(2)
      expect(mockTransformStrategy.execute).toHaveBeenCalledTimes(2)
      expect(mockShapeRepository.update).toHaveBeenCalledTimes(2)
    })

    it('should throw CoreError when shape not found', async () => {
      mockShapeRepository.getById.mockReturnValueOnce(null)

      const options: TransformOptions = { type: 'translate', x: 10, y: 20 }

      await expect(computeFacade.transformElement(['unknown-id'], options)).rejects.toThrow(CoreError)
    })

    it('should wrap non-CoreError exceptions', async () => {
      mockElementFactory.createShape.mockImplementation(() => {
        throw new Error('Factory error')
      })

      const options: TransformOptions = { type: 'translate', x: 10, y: 20 }

      await expect(computeFacade.transformElement(['rect-1'], options)).rejects.toThrow(CoreError)
      expect(mockConsoleError).toHaveBeenCalled()
    })
  })
})
