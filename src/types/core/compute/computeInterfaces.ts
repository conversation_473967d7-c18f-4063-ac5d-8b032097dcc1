/**
 * Core Interfaces for Computation Operations
 *
 * @remarks
 * This module defines fundamental interfaces used for various computation operations
 * performed on geometric elements within the application. These interfaces
 * standardize how computation options are passed and how results are structured.
 *
 * Key interfaces include:
 * - {@link TransformOptions}: Defines parameters for geometric transformations like translate, rotate, and scale.
 * - {@link ComputeOptions}: A flexible interface for passing parameters to diverse computation tasks
 *   (e.g., geometric calculations, cost estimations).
 * - {@link ComputeResult}: A generic structure for returning the outcome of any computation,
 *   including the result itself and relevant metadata.
 * - {@link ComputeShape}: A minimal interface that shapes must implement to be usable in
 *   standardized computation operations, ensuring access to basic properties like type and position.
 *
 * @module types/core/compute/computeInterfaces
 * @see {@link Point} from '@/types/core/element/geometry/point' (aliased as PointData)
 * @see {@link BoundingBox} from '@/types/core/element/geometry/bounding-box'
 */

import type { BoundingBox } from '../element/geometry/bounding-box'
import type Point from '../element/geometry/point'

/** Re-exports the {@link BoundingBox} type from its geometry definition file. */
export type { BoundingBox }

/**
 * Defines options for various geometric transformation operations that can be applied to elements.
 *
 * @remarks
 * This interface supports common transformations:
 * - `translate`: Moves an element by specified x and y offsets.
 * - `rotate`: Rotates an element around a specified origin by a given angle.
 * - `scale`: Resizes an element by specified x and y scale factors, relative to an origin.
 * - `matrix`: Applies a custom 2D transformation matrix (e.g., for affine transformations).
 *
 * The `originX` and `originY` properties are used for `rotate` and `scale` operations.
 * If not provided, the element's own center or a default origin might be used by the
 * transformation logic.
 *
 * @example
 * ```typescript
 * const rotateOptions: TransformOptions = {
 *   type: 'rotate',
 *   angle: 45, // degrees
 *   originX: 100,
 *   originY: 100
 * };
 * const translateOptions: TransformOptions = {
 *   type: 'translate',
 *   x: 50,
 *   y: -20
 * };
 * ```
 */
export interface TransformOptions {
  /** Specifies the type of transformation to perform. */
  type: 'translate' | 'rotate' | 'scale' | 'matrix'
  /** The horizontal offset for translation. Required if `type` is 'translate'. */
  x?: number
  /** The vertical offset for translation. Required if `type` is 'translate'. */
  y?: number
  /** The rotation angle in degrees. Required if `type` is 'rotate'. */
  angle?: number
  /** The scale factor along the x-axis. Required if `type` is 'scale'. */
  scaleX?: number
  /** The scale factor along the y-axis. Required if `type` is 'scale'. */
  scaleY?: number
  /**
   * A 2D transformation matrix (e.g., a 6-element array `[a, b, c, d, e, f]` for an affine transformation).
   * Required if `type` is 'matrix'.
   */
  matrix?: number[]
  /** The x-coordinate of the origin point for rotation or scaling. Defaults to element's center if not provided. */
  originX?: number
  /** The y-coordinate of the origin point for rotation or scaling. Defaults to element's center if not provided. */
  originY?: number
}

/**
 * Defines a flexible set of options that can be passed to various computation operations.
 *
 * @remarks
 * This interface serves as a general-purpose container for parameters that might be
 * relevant to different types of computations, such as geometric calculations (e.g., distance),
 * cost estimations, material analyses, or space planning evaluations.
 *
 * Not all properties will be relevant for every computation. Implementations of
 * computation strategies should pick the properties they require.
 *
 * @example
 * ```typescript
 * const distanceOptions: ComputeOptions = {
 *   precision: 2, // Number of decimal places for the result
 *   targetShapeId: 'shape-002' // For distance between two shapes
 * };
 * const costOptions: ComputeOptions = {
 *   unitCost: 10.50,
 *   costOptions: { currency: 'USD', taxRate: 0.1 }
 * };
 * ```
 */
export interface ComputeOptions {
  /** Desired precision for numeric results (e.g., number of decimal places). */
  precision?: number
  /** Identifier for a specific algorithm or method to be used for the computation, if multiple are available. */
  algorithm?: string
  /** The x-coordinate, relevant for point-based operations (e.g., distance to a point). */
  x?: number
  /** The y-coordinate, relevant for point-based operations. */
  y?: number
  /** The ID of a target shape, used in operations like distance calculation between two shapes. */
  targetShapeId?: string
  /** The cost per unit (e.g., per square meter, per item) for cost calculation operations. */
  unitCost?: number
  /**
   * Additional options specific to cost calculation.
   * @remarks Consider defining a more specific type (e.g., `CostCalculationDetails`)
   *          instead of `unknown`. Using `unknown` is preferred over `any` for type safety.
   */
  costOptions?: Record<string, unknown> // TODO: Define a specific type e.g., CostCalculationDetails
  /** The type of material to consider for material-related calculations (e.g., 'wood', 'steel'). */
  materialType?: string
  /**
   * Additional options specific to material calculation.
   * @remarks Consider defining a more specific type (e.g., `MaterialCalculationDetails`)
   *          instead of `unknown`. Using `unknown` is preferred over `any` for type safety.
   */
  materialOptions?: Record<string, unknown> // TODO: Define a specific type e.g., MaterialCalculationDetails
  /** The type of space being considered for space planning operations (e.g., 'office', 'residential'). */
  spaceType?: string
  /**
   * Additional options specific to space planning operations.
   * @remarks Consider defining a more specific type (e.g., `SpacePlanningDetails`)
   *          instead of `unknown`. Using `unknown` is preferred over `any` for type safety.
   */
  spacePlanningOptions?: Record<string, unknown> // TODO: Define a specific type e.g., SpacePlanningDetails
}

/**
 * Represents the structured result of a computation operation.
 *
 * @typeParam T - The data type of the primary computation `result` (e.g., `number` for area, `Point` for a centroid).
 *
 * @remarks
 * This generic interface provides a standardized format for returning the outcome
 * of various computations. It includes:
 * - `operation`: A string identifying the type of computation performed.
 * - `result`: The main data payload of the computation.
 * - `metadata`: An optional object containing additional information about the
 *   computation, such as execution time, precision, or involved elements.
 *
 * @example
 * ```typescript
 * const areaResult: ComputeResult<number> = {
 *   operation: 'calculateArea',
 *   result: 150.75,
 *   metadata: {
 *     executionTime: 5, // ms
 *     shapeId: 'rect-001',
 *     precision: 2
 *   }
 * };
 * ```
 */
export interface ComputeResult<T> {
  /** A string identifying the computation operation that was performed (e.g., 'calculateArea', 'getDistance'). */
  operation: string
  /** The primary result of the computation, with its type defined by the generic parameter `T`. */
  result: T
  /** Optional metadata providing additional information about the computation. */
  metadata?: {
    /** The time taken to perform the computation, in milliseconds. */
    executionTime: number
    /** The level of precision used for numeric results, if applicable. */
    precision?: number
    /** The specific point coordinates used in the operation, if relevant (e.g., for a 'distanceToPoint' operation). */
    point?: { x: number, y: number }
    /** The type of transformation performed, if the operation involved a transformation. */
    transformType?: 'translate' | 'rotate' | 'scale' | 'matrix'
    /** The specific algorithm or method used for the computation, if alternatives exist. */
    algorithmUsed?: string
    /** The ID of the primary shape involved in the computation, if applicable. */
    shapeId?: string
    /** An array of IDs of all shapes involved in the computation, if the operation involved multiple shapes. */
    shapeIds?: string[]
  }
}

/**
 * Defines a minimal contract for shapes that can be used in computation operations.
 *
 * @remarks
 * This interface ensures that any shape intended for use with computation strategies
 * (e.g., for area, perimeter calculations) provides a way to identify its specific
 * type/sub-type and its primary position. This allows strategies to adapt their
 * logic or retrieve necessary geometric data.
 *
 * @example
 * ```typescript
 * // Assuming PointData is the actual type for Point
 * import { PointData } from '../element/geometry/point';
 *
 * class CircleShape implements ComputeShape {
 *   // ... other properties ...
 *   constructor(public id: string, private radius: number, private center: PointData) {}
 *
 *   getSubType(): string { // Corresponds to an ElementType string value
 *     return 'CIRCLE';
 *   }
 *
 *   getPosition(): PointData {
 *     return this.center;
 *   }
 *
 *   // ... other methods ...
 * }
 * ```
 * @see {@link Point} (which is an alias for `PointData` from `../element/geometry/point`)
 * @see {@link ElementType} from '@/types/core/elementDefinitions'
 */
export interface ComputeShape {
  /**
   * Returns a string identifier for the specific type or sub-type of the shape.
   * This typically corresponds to a value from the {@link ElementType} enumeration
   * or a more granular classification if needed.
   * @returns The subtype identifier string.
   */
  getSubType: () => string
  /**
   * Returns the primary position (e.g., center, top-left corner, start point) of the shape.
   * @returns A {@link Point} object (which is an alias for `PointData`) representing the shape's position.
   */
  getPosition: () => Point
}
