/**
 * Shape Event Subscribers Module
 *
 * This module provides type-safe helper functions for subscribing to shape-related events
 * on the application's event bus. It covers shape creation, editing, deletion, and selection.
 *
 * All functions follow dependency injection principles by accepting an EventBus
 * instance as their first parameter.
 *
 * @module event-bus/helpers/subscribers/shape-subscribers
 */

import type { EventBus } from '@/types/services/events'
import type { EventSubscriptionOptions } from '@/types/services/events/eventCore'
import type { SelectionChangedEvent, ShapeCreateEvent, ShapeDeleteEvent, ShapeDuplicateEvent, ShapeEditEvent } from '@/types/services/events/shapeEvents'
import { AppEventType } from '@/types/services/events'
import { getShapeCreateEventType, getShapeDeleteEventType, getShapeEditEventType, typedSubscribe } from './utils'

/**
 * Subscribe to shape creation events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param phase - The phase of the process to listen for (request, complete, etc.)
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToShapeCreateEvents(
  eventBus: EventBus,
  handler: (event: ShapeCreateEvent) => void,
  phase: 'request' | 'validate' | 'complete' | 'error',
  options?: EventSubscriptionOptions,
): () => void {
  const eventType = getShapeCreateEventType(phase)
  return typedSubscribe<ShapeCreateEvent>(eventBus, eventType, handler, options)
}

/**
 * Subscribe to shape edit events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param phase - The phase of the process to listen for (request, complete, etc.)
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToShapeEditEvents(
  eventBus: EventBus,
  handler: (event: ShapeEditEvent) => void,
  phase: 'request' | 'compute' | 'complete' | 'error',
  options?: EventSubscriptionOptions,
): () => void {
  const eventType = getShapeEditEventType(phase)
  return typedSubscribe<ShapeEditEvent>(eventBus, eventType, handler, options)
}

/**
 * Subscribe to shape delete events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param phase - The phase of the process to listen for (request, complete, etc.)
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToShapeDeleteEvents(
  eventBus: EventBus,
  handler: (event: ShapeDeleteEvent) => void,
  phase: 'request' | 'complete' | 'error',
  options?: EventSubscriptionOptions,
): () => void {
  const eventType = getShapeDeleteEventType(phase)
  return typedSubscribe<ShapeDeleteEvent>(eventBus, eventType, handler, options)
}

/**
 * Subscribe to selection changed events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToSelectionChangedEvents(
  eventBus: EventBus,
  handler: (event: SelectionChangedEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<SelectionChangedEvent>(
    eventBus,
    AppEventType.SelectionChanged,
    handler,
    options,
  )
}

/**
 * Subscribe to shape duplication events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param phase - The phase of the process to listen for (request or complete)
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToShapeDuplicationEvents(
  eventBus: EventBus,
  handler: (event: ShapeDuplicateEvent) => void,
  phase: 'request' | 'complete',
  options?: EventSubscriptionOptions,
): () => void {
  const eventType = phase === 'request'
    ? AppEventType.ShapeDuplicateRequest
    : AppEventType.ShapeDuplicateComplete
  return typedSubscribe<ShapeDuplicateEvent>(eventBus, eventType, handler, options)
}
