/**
 * Service Configuration Types
 *
 * This file defines the configuration interfaces for services.
 *
 * @module types/services/core
 */

/**
 * Configuration options for services.
 */
export interface ServiceConfiguration {
  /** Whether the service is enabled */
  enabled: boolean
  /** Whether the service should start automatically */
  autoStart: boolean
  /** Logging level for the service */
  logLevel: 'debug' | 'info' | 'warn' | 'error'
  /** Additional service-specific configuration options */
  [key: string]: unknown
}

/**
 * Type alias for ServiceConfiguration for consistency
 */
export type ServiceConfig = ServiceConfiguration
