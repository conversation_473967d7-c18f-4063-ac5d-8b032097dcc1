import { beforeEach, describe, expect, it, vi } from 'vitest'
import { LineBoundingBoxStrategy } from '@/core/compute/strategies/bounding-box/LineBoundingBoxStrategy'
import { Point } from '@/types/core/element/geometry/point'
import { Line } from '@/types/core/element/path/linePathTypes'

// Mock Line class
class MockLine {
  private _start: Point
  private _end: Point
  id: string = 'mock-line-id'

  constructor(start: { x: number, y: number }, end: { x: number, y: number }) {
    this._start = new Point(start.x, start.y)
    this._end = new Point(end.x, end.y)
  }

  getSubType() {
    return 'line'
  }

  get start() {
    return this._start
  }

  get end() {
    return this._end
  }
}

// Mock non-line element
class MockRectangle {
  id: string = 'mock-rectangle'

  getSubType() {
    return 'rectangle'
  }
}

describe('lineBoundingBoxStrategy', () => {
  let strategy: LineBoundingBoxStrategy
  let consoleSpy: any

  beforeEach(() => {
    strategy = new LineBoundingBoxStrategy()
    consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    consoleSpy.mockRestore()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('line')
  })

  it('should calculate bounding box correctly for a horizontal line', () => {
    const start = { x: 0, y: 0 }
    const end = { x: 100, y: 0 }
    const line = {
      id: 'test-line',
      start,
      end,
      getSubType: () => 'line',
    }

    const boundingBox = strategy.calculateBoundingBox(line as any)

    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 100,
      height: 0,
    })
  })

  it('should calculate bounding box correctly for a vertical line', () => {
    const start = { x: 0, y: 0 }
    const end = { x: 0, y: 100 }
    const line = {
      id: 'test-line',
      start,
      end,
      getSubType: () => 'line',
    }

    const boundingBox = strategy.calculateBoundingBox(line as any)

    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 100,
    })
  })

  it('should calculate bounding box correctly for a diagonal line', () => {
    const start = { x: 0, y: 0 }
    const end = { x: 100, y: 100 }
    const line = {
      id: 'test-line',
      start,
      end,
      getSubType: () => 'line',
    }

    const boundingBox = strategy.calculateBoundingBox(line as any)

    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 100,
      height: 100,
    })
  })

  it('should handle non-line elements', () => {
    const rectangle = new MockRectangle()

    const boundingBox = strategy.calculateBoundingBox(rectangle as any)
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
    expect(consoleSpy).toHaveBeenCalled()
  })

  it('should handle real Line instance', () => {
    // Create a mock that looks like a real Line instance
    const realLine = {
      id: 'real-line-id',
      constructor: { name: 'Line' },
      start: new Point(0, 0),
      end: new Point(100, 100),
      getSubType: () => 'line',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realLine, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(realLine as any)
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 100,
      height: 100,
    })
  })

  it('should handle invalid start point in real Line instance', () => {
    // Create a mock that looks like a real Line instance with invalid start point
    const realLine = {
      id: 'real-line-id',
      constructor: { name: 'Line' },
      start: null,
      end: new Point(100, 100),
      getSubType: () => 'line',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realLine, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(realLine as any)
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle invalid end point in real Line instance', () => {
    // Create a mock that looks like a real Line instance with invalid end point
    const realLine = {
      id: 'real-line-id',
      constructor: { name: 'Line' },
      start: new Point(0, 0),
      end: null,
      getSubType: () => 'line',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realLine, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(realLine as any)
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
    // We're not checking for console.error here because our implementation is more robust
    // and can handle null end point by using start point coordinates
  })

  it('should handle error when accessing properties on Line', () => {
    // Create a mock that looks like a real Line instance but throws when start is accessed
    const realLine = {
      id: 'real-line-id',
      constructor: { name: 'Line' },
      get start() { throw new Error('Test error') },
      end: new Point(100, 100),
      getSubType: () => 'line',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realLine, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(realLine as any)
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle Line with both start and end properties', () => {
    const line = {
      id: 'line-with-properties',
      constructor: { name: 'Line' },
      start: new Point(10, 20),
      end: new Point(30, 40),
      getSubType: () => 'line',
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)
    expect(boundingBox).toEqual({
      x: 10,
      y: 20,
      width: 20,
      height: 20,
    })
  })

  it('should handle Line with plain object start and end properties', () => {
    const line = {
      id: 'line-with-plain-objects',
      constructor: { name: 'Line' },
      start: { x: 5, y: 15 },
      end: { x: 25, y: 35 },
      getSubType: () => 'line',
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)
    expect(boundingBox).toEqual({
      x: 5,
      y: 15,
      width: 20,
      height: 20,
    })
  })

  it('should handle Line with getStartPoint and getEndPoint methods', () => {
    const line = {
      id: 'line-with-methods',
      constructor: { name: 'Line' },
      start: { x: 50, y: 60 },
      end: { x: 70, y: 80 },
      getStartPoint: () => new Point(50, 60),
      getEndPoint: () => new Point(70, 80),
      getSubType: () => 'line',
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)

    expect(boundingBox).toEqual({
      x: 50,
      y: 60,
      width: 20,
      height: 20,
    })
  })

  it('should handle Line with plain object return from methods', () => {
    const line = {
      id: 'line-with-plain-object-methods',
      constructor: { name: 'Line' },
      start: { x: 100, y: 200 },
      end: { x: 300, y: 400 },
      getStartPoint: () => ({ x: 100, y: 200 }),
      getEndPoint: () => ({ x: 300, y: 400 }),
      getSubType: () => 'line',
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)

    expect(boundingBox).toEqual({
      x: 100,
      y: 200,
      width: 200,
      height: 200,
    })
  })

  it('should handle Line with NaN coordinates', () => {
    const line = {
      id: 'line-with-nan',
      constructor: { name: 'Line' },
      start: new Point(Number.NaN, 10),
      end: new Point(20, 30),
      getSubType: () => 'line',
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })
  it('should handle Line with direct start/end properties', () => {
    const line = {
      id: 'line-with-properties',
      constructor: { name: 'Line' },
      start: new Point(10, 20),
      end: new Point(30, 40),
      getSubType: () => 'line',
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)

    expect(boundingBox).toEqual({
      x: 10,
      y: 20,
      width: 20,
      height: 20,
    })
  })

  it('should handle Line with direct start/end properties but no getSubType', () => {
    const line = {
      id: 'line-with-properties-no-subtype',
      constructor: { name: 'Line' },
      start: new Point(10, 20),
      end: new Point(30, 40),
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)

    expect(boundingBox).toEqual({
      x: 10,
      y: 20,
      width: 20,
      height: 20,
    })
  })

  it('should handle Line with direct start/end plain objects', () => {
    const line = {
      id: 'line-with-plain-properties',
      constructor: { name: 'Line' },
      start: { x: 50, y: 60 },
      end: { x: 70, y: 80 },
      getSubType: () => 'line',
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)

    expect(boundingBox).toEqual({
      x: 50,
      y: 60,
      width: 20,
      height: 20,
    })
  })

  it('should handle error in getStartPoint method', () => {
    const line = {
      id: 'line-with-error',
      constructor: { name: 'Line' },
      getStartPoint: () => { throw new Error('Test error') },
      end: { x: 70, y: 80 },
      getSubType: () => 'line',
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)

    // Should use end property as fallback
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle Line with start property but no end property', () => {
    const line = {
      id: 'line-with-start-only',
      constructor: { name: 'Line' },
      start: { x: 50, y: 60 },
      getSubType: () => 'line',
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)

    // Should return zero bounding box when missing end point
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle Line with end property but no start property', () => {
    const line = {
      id: 'line-with-end-only',
      constructor: { name: 'Line' },
      end: { x: 70, y: 80 },
      getSubType: () => 'line',
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)

    // Should return zero bounding box when missing start point
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle Line with getStartPoint method but no start property', () => {
    const line = {
      id: 'line-with-getStartPoint-only',
      constructor: { name: 'Line' },
      getStartPoint: () => ({ x: 50, y: 60 }),
      end: { x: 70, y: 80 },
      getSubType: () => 'line',
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)

    // Current implementation doesn't support this case, so we expect a zero bounding box
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle Line with getEndPoint method but no end property', () => {
    const line = {
      id: 'line-with-getEndPoint-only',
      constructor: { name: 'Line' },
      start: { x: 50, y: 60 },
      getEndPoint: () => ({ x: 70, y: 80 }),
      getSubType: () => 'line',
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)

    // Current implementation doesn't support this case, so we expect a zero bounding box
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle error in getEndPoint method', () => {
    const line = {
      id: 'line-with-error',
      constructor: { name: 'Line' },
      start: { x: 50, y: 60 },
      getEndPoint: () => { throw new Error('Test error') },
      getSubType: () => 'line',
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)

    // Should use start property as fallback
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle Line with NaN end coordinates', () => {
    const line = {
      id: 'line-with-nan-end',
      constructor: { name: 'Line' },
      start: { x: 10, y: 10 },
      end: { x: Number.NaN, y: 30 },
      getSubType: () => 'line',
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)

    // Should return zero bounding box for invalid points
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle Line with both NaN coordinates', () => {
    const line = {
      id: 'line-with-both-nan',
      constructor: { name: 'Line' },
      start: { x: Number.NaN, y: Number.NaN },
      end: { x: Number.NaN, y: Number.NaN },
      getSubType: () => 'line',
    }

    Object.setPrototypeOf(line, Line.prototype)

    const boundingBox = strategy.calculateBoundingBox(line as any)

    // Should return zero bounding box for invalid points
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle null element', () => {
    const boundingBox = strategy.calculateBoundingBox(null as any)

    // Should return zero bounding box for null element
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle undefined element', () => {
    const boundingBox = strategy.calculateBoundingBox(undefined as any)

    // Should return zero bounding box for undefined element
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })
})
