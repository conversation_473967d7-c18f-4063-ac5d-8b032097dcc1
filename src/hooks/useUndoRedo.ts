/**
 * useUndoRedo.ts - Hook for managing undo/redo operations in the application.
 *
 * This hook provides a convenient way to use the zundo-enabled shapes store
 * for undo/redo operations. It exposes undo, redo functions and related state.
 */

/**
 * Undo/Redo Hook
 *
 * This module provides a React hook for managing undo and redo operations.
 * It serves as a simplified interface to the element actions system,
 * specifically for history management functionality.
 *
 * @module hooks/useUndoRedo
 */

import { useElementActions } from './useElementActions'

/**
 * Hook to access undo/redo functionality from canonical element actions
 * @returns Object with undo, redo functions and canUndo/canRedo state
 */
function useUndoRedo() {
  const { undo, redo } = useElementActions()
  // TODO: Implement canUndo/canRedo state if needed, for now always true
  return {
    undo,
    redo,
    canUndo: true,
    canRedo: true,
  }
}

export default useUndoRedo
