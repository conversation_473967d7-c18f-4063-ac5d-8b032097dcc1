/**
 * Material Calculation Strategy for Wood (e.g., Flooring, Panels)
 *
 * @remarks
 * This strategy implements the {@link IMaterialCalculatorStrategy} to calculate
 * the required amount of wood material for applications like flooring, wall paneling,
 * or custom furniture based on area.
 *
 * It first determines the area of the input `element` (currently assuming the element
 * has a `getArea()` or `compute.area()` method - marked as TODO for refactoring).
 *
 * Key inputs from {@link MaterialCalculationOptions} include:
 * - `boardWidth` or `unitSize.width`: Width of individual wood planks/boards.
 * - `boardLength` or `unitSize.height`: Length of individual wood planks/boards.
 * - `thickness`: For volume calculation.
 * - `installationPattern`: Affects default wastage if `wastageRate` is not provided.
 * - `expansion`: Allowance for wood expansion (as a decimal).
 * - `wastageRate`: Overrides default wastage factors.
 * - `adhesivePerSqm`: For calculating adhesive quantity.
 * - `finishPerSqm`: For calculating finishing material quantity.
 * - `unitsPerBox`: For calculating number of boxes.
 *
 * The strategy calculates the number of boards needed, total wood area (including
 * expansion and wastage), volume, and quantities of adhesive and finish.
 *
 * The `calculateMaterialAmount` method returns a summarized {@link MaterialCalculationResult},
 * where `amount` represents the raw area before expansion/wastage, and `unitCount` is
 * the number of boards. A private `calculateDetailedMaterial` method provides a more
 * granular breakdown.
 *
 * @module core/compute/strategies/material/WoodMaterialStrategy
 * @see {@link IMaterialCalculatorStrategy}
 * @see {@link MaterialCalculationOptions}
 * @see {@link MaterialCalculationResult}
 */
import type {
  MaterialCalculatorStrategy as IMaterialCalculatorStrategy,
  MaterialCalculationOptions,
  MaterialCalculationResult,
} from '@/types/core/compute' // Corrected import path
import type { Element } from '@/types/core/elementDefinitions'
import { CoreError, ErrorType } from '@/services/system/error-service'
import { ElementType as CoreElementType } from '@/types/core/elementDefinitions'

export class WoodMaterialStrategy implements IMaterialCalculatorStrategy { // Renamed IMaterialCalculatorStrategy for consistency
  /**
   * Calculates the amount of wood material required.
   *
   * @param element - The element (e.g., floor area, wall surface) for which wood material is being calculated.
   *                  Expected to have a method to compute its area.
   * @param materialType - The type of material, expected to be 'wood'.
   * @param options - Optional {@link MaterialCalculationOptions} to customize the calculation.
   * @returns A {@link MaterialCalculationResult} summarizing the wood material quantity.
   * @throws {@link CoreError} if `materialType` is not 'wood', or if area calculation fails or is invalid,
   *         or if essential options like board dimensions are missing or invalid.
   */
  public calculateMaterialAmount(element: Element, materialType: string, options?: MaterialCalculationOptions): MaterialCalculationResult {
    const detailedResult = this.calculateDetailedMaterial(element, materialType, options)

    const rawArea = detailedResult.rawArea || 0

    return {
      amount: rawArea,
      unit: 'm²', // Standard symbol for square meters
      unitType: 'plank', // Or could be 'board'
      unitCount: detailedResult.boards,
      amountWithWastage: detailedResult.totalArea, // This is totalWoodAreaWithWastage from detailed calc
      boxes: Math.ceil(detailedResult.boards / ((options?.unitsPerBox !== null && options?.unitsPerBox !== undefined && options?.unitsPerBox !== 0) ? options.unitsPerBox : 1)), // Default to 1 if unitsPerBox is 0 or undefined
    }
  }

  /**
   * Calculates a detailed breakdown of wood and related installation materials.
   * @private
   * @param element - The element to be covered with wood.
   * @param materialType - Expected to be 'wood'.
   * @param options - Optional {@link MaterialCalculationOptions}.
   * @returns A record containing quantities for boards, total area, volume, adhesive, finish, etc.
   * @throws {@link CoreError} if `materialType` is not 'wood', or if area calculation fails or is invalid,
   *         or if essential options like board dimensions are missing or invalid.
   */
  private calculateDetailedMaterial(element: Element, materialType: string, options?: MaterialCalculationOptions): Record<string, number> {
    if (materialType.toLowerCase() !== 'wood') {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `WoodMaterialStrategy can only calculate for 'wood' material type, got ${materialType}`,
      )
    }

    const computableElement = element as unknown as { getArea?: () => number, compute?: { area?: () => number } } // TODO: Refactor for type safety
    let area: number
    if (computableElement.getArea !== null && computableElement.getArea !== undefined && typeof computableElement.getArea === 'function') {
      area = computableElement.getArea()
    }
    else if (computableElement.compute?.area && typeof computableElement.compute.area === 'function') {
      area = computableElement.compute.area()
    }
    else {
      throw new CoreError(ErrorType.ComputationError, `Element ${element.id} does not have a method to calculate area.`)
    }

    if (typeof area !== 'number' || area < 0 || !Number.isFinite(area)) {
      throw new CoreError(ErrorType.ComputationError, `Invalid area for element ${element.id}: ${area}`)
    }
    if (area === 0) {
      console.warn(`[WoodMaterialStrategy] Area for element ${element.id} is 0. No wood needed.`)
      return { boards: 0, totalArea: 0, volume: 0, adhesive: 0, finish: 0, wasteFactor: 0, rawArea: 0 }
    }

    const materialOptions = options ?? {}
    const boardWidth = materialOptions.boardWidth ?? materialOptions.unitSize?.width ?? 0.1
    const boardLength = materialOptions.boardLength ?? materialOptions.unitSize?.height ?? 1.0
    const thickness = materialOptions.thickness ?? 0.015
    const installationPattern = materialOptions.installationPattern ?? 'straight'
    const expansion = materialOptions.expansion ?? 0.02 // Expansion as decimal

    let wasteFactorDecimal = (materialOptions.wastageRate ?? 10) / 100 // Use options.wastageRate if provided (as percentage)

    if (materialOptions.wastageRate === null || materialOptions.wastageRate === undefined) { // Only apply default logic if wastageRate is not in options
      switch (installationPattern) {
        case 'straight':
          wasteFactorDecimal = 0.10
          break
        case 'herringbone':
          wasteFactorDecimal = 0.15
          break
        case 'diagonal':
          wasteFactorDecimal = 0.18
          break
        case 'parquet':
          wasteFactorDecimal = 0.12
          break
        default:
          wasteFactorDecimal = 0.10
      }
    }

    const areaWithExpansion = area * (1 + expansion)
    const totalWoodAreaWithWastage = areaWithExpansion * (1 + wasteFactorDecimal)

    if (boardWidth <= 0 || boardLength <= 0) {
      throw new CoreError(ErrorType.InvalidParameter, 'Board dimensions (width and length) must be positive.')
    }
    const boardArea = boardWidth * boardLength
    const boardsNeeded = Math.ceil(totalWoodAreaWithWastage / boardArea)
    const totalVolume = totalWoodAreaWithWastage * thickness
    const adhesivePerSqm = materialOptions.adhesivePerSqm ?? 0.8
    const adhesiveNeeded = areaWithExpansion * adhesivePerSqm // Adhesive based on area before wastage
    const finishPerSqm = materialOptions.finishPerSqm ?? 0.1
    const finishNeeded = areaWithExpansion * finishPerSqm // Finish based on area before wastage

    return {
      boards: boardsNeeded,
      totalArea: totalWoodAreaWithWastage, // This is area including expansion and wastage
      rawArea: area, // Store raw area for the main result
      volume: totalVolume,
      adhesive: adhesiveNeeded,
      finish: finishNeeded,
      wasteFactor: wasteFactorDecimal * 100, // Return as percentage
    }
  }

  /**
   * Returns the primary element type this strategy is typically associated with.
   * @remarks Wood materials are often applied to rectangular areas like floors or walls.
   * @returns {@link CoreElementType.RECTANGLE} - The rectangle element type typically used for wood flooring and wall applications
   */
  public getElementType(): string {
    return CoreElementType.RECTANGLE
  }

  /**
   * Returns the specific material type this strategy calculates.
   * @returns The string 'wood'.
   */
  public getMaterialType(): string {
    return 'wood'
  }
}
