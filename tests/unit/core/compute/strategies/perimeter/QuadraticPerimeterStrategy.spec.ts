import { beforeEach, describe, expect, it, vi } from 'vitest'
import { QuadraticPerimeterStrategy } from '@/core/compute/strategies/perimeter/QuadraticPerimeterStrategy'
import { CoreError } from '@/services/system/error-service'
import { ElementType } from '@/types/core/elementDefinitions'

// Mock the utility functions
vi.mock('@/lib/utils/geometry/bezierUtils', () => ({
  calculateQuadraticBezierLengthGauss: vi.fn((p0, p1, p2) => {
    // Simple mock implementation: approximate length based on control points
    const dx = p2.x - p0.x
    const dy = p2.y - p0.y
    return Math.sqrt(dx * dx + dy * dy) * 1.3 // Rough approximation
  }),
}))

vi.mock('@/lib/utils/geometry/pointUtils', () => ({
  calculateDistance: vi.fn((p1, p2) => {
    const dx = p2.x - p1.x
    const dy = p2.y - p1.y
    return Math.sqrt(dx * dx + dy * dy)
  }),
}))

describe('quadraticPerimeterStrategy', () => {
  let strategy: QuadraticPerimeterStrategy

  beforeEach(() => {
    strategy = new QuadraticPerimeterStrategy()
    vi.clearAllMocks()
  })

  describe('constructor and Basic Properties', () => {
    it('should be defined and instantiated', () => {
      expect(strategy).toBeDefined()
      expect(strategy).toBeInstanceOf(QuadraticPerimeterStrategy)
    })

    it('should return correct element type', () => {
      expect(strategy.getElementType()).toBe(ElementType.QUADRATIC)
    })

    it('should have required methods', () => {
      expect(typeof strategy.calculatePerimeter).toBe('function')
      expect(typeof strategy.getElementType).toBe('function')
    })
  })

  describe('perimeter Calculation', () => {
    it('should calculate perimeter for open quadratic curve correctly', () => {
      const quadraticElement = {
        id: 'quadratic-1',
        type: ElementType.QUADRATIC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: -10 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(quadraticElement as any)

      // Expected: curve length only = sqrt(20^2 + 0^2) * 1.3 = 26
      expect(perimeter).toBe(26)
    })

    it('should calculate perimeter for closed quadratic curve correctly', () => {
      const quadraticElement = {
        id: 'quadratic-2',
        type: ElementType.QUADRATIC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: -10 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      const perimeter = strategy.calculatePerimeter(quadraticElement as any)

      // Expected: curve length + closing distance = 26 + 20 = 46
      expect(perimeter).toBe(46)
    })

    it('should handle missing closed property (defaults to false)', () => {
      const quadraticElement = {
        id: 'quadratic-no-closed',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: -10 },
          end: { x: 20, y: 0 },
          // closed property missing
        },
      }

      const perimeter = strategy.calculatePerimeter(quadraticElement as any)

      // Should be treated as open curve
      expect(perimeter).toBe(26)
    })

    it('should call bezier length calculation with correct parameters', () => {
      const { calculateQuadraticBezierLengthGauss } = require('@/lib/utils/geometry/bezierUtils')

      const quadraticElement = {
        id: 'quadratic-test',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: -10 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      strategy.calculatePerimeter(quadraticElement as any)

      expect(calculateQuadraticBezierLengthGauss).toHaveBeenCalledWith(
        { x: 0, y: 0 },
        { x: 10, y: -10 },
        { x: 20, y: 0 },
      )
    })

    it('should call distance calculation for closed curve', () => {
      const { calculateDistance } = require('@/lib/utils/geometry/pointUtils')

      const quadraticElement = {
        id: 'quadratic-closed-test',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: -10 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      strategy.calculatePerimeter(quadraticElement as any)

      expect(calculateDistance).toHaveBeenCalledWith(
        { x: 0, y: 0 },
        { x: 20, y: 0 },
      )
    })
  })

  describe('error Handling', () => {
    it('should throw error for non-quadratic element', () => {
      const rectangleElement = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        properties: {},
      }

      expect(() => strategy.calculatePerimeter(rectangleElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculatePerimeter(rectangleElement as any))
        .toThrow('QuadraticPerimeterStrategy can only calculate perimeter for QUADRATIC elements')
    })

    it('should throw error for missing start point', () => {
      const quadraticElement = {
        id: 'quadratic-no-start',
        type: ElementType.QUADRATIC,
        properties: {
          start: null,
          control: { x: 10, y: -10 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(quadraticElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculatePerimeter(quadraticElement as any))
        .toThrow('Invalid quadratic curve points')
    })

    it('should throw error for invalid start point coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-invalid-start',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 'invalid', y: 0 },
          control: { x: 10, y: -10 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(quadraticElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for missing control point', () => {
      const quadraticElement = {
        id: 'quadratic-no-control',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: undefined,
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(quadraticElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for invalid control point coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-invalid-control',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: Number.NaN },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(quadraticElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for missing end point', () => {
      const quadraticElement = {
        id: 'quadratic-no-end',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: -10 },
          end: null,
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(quadraticElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for invalid end point coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-invalid-end',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: -10 },
          end: { x: Infinity, y: 0 },
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(quadraticElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for points with missing coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-missing-coords',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0 }, // missing y
          control: { x: 10, y: -10 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(quadraticElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for infinite coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-infinite-coords',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: Infinity, y: -10 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(quadraticElement as any))
        .toThrow(CoreError)
    })
  })

  describe('edge Cases', () => {
    it('should handle points with zero coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-zero-coords',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 0, y: 0 },
          end: { x: 0, y: 0 },
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(quadraticElement as any)
      expect(perimeter).toBe(0) // All points are the same
    })

    it('should handle negative coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-negative-coords',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: -10, y: -10 },
          control: { x: 0, y: -20 },
          end: { x: 10, y: -10 },
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(quadraticElement as any)
      expect(perimeter).toBeGreaterThan(0)
    })

    it('should handle very large coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-large-coords',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 1000000, y: 1000000 },
          control: { x: 1000010, y: 999990 },
          end: { x: 1000020, y: 1000000 },
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(quadraticElement as any)
      expect(perimeter).toBeGreaterThan(0)
      expect(Number.isFinite(perimeter)).toBe(true)
    })

    it('should handle very small coordinates', () => {
      const quadraticElement = {
        id: 'quadratic-small-coords',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0.001, y: 0.001 },
          control: { x: 0.010, y: -0.009 },
          end: { x: 0.020, y: 0.001 },
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(quadraticElement as any)
      expect(perimeter).toBeGreaterThan(0)
      expect(perimeter).toBeLessThan(1)
    })

    it('should handle collinear points', () => {
      const quadraticElement = {
        id: 'quadratic-collinear',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: 0 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(quadraticElement as any)
      expect(perimeter).toBe(26) // Should approximate to straight line
    })

    it('should handle closed curve with same start and end points', () => {
      const quadraticElement = {
        id: 'quadratic-same-endpoints',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: 0, y: 0 },
          control: { x: 10, y: 10 },
          end: { x: 0, y: 0 },
          closed: true,
        },
      }

      const perimeter = strategy.calculatePerimeter(quadraticElement as any)
      expect(perimeter).toBe(0) // Curve length + 0 closing distance
    })

    it('should handle symmetric curve', () => {
      const quadraticElement = {
        id: 'quadratic-symmetric',
        type: ElementType.QUADRATIC,
        properties: {
          start: { x: -10, y: 0 },
          control: { x: 0, y: 10 },
          end: { x: 10, y: 0 },
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(quadraticElement as any)
      expect(perimeter).toBe(26) // sqrt(20^2 + 0^2) * 1.3
    })
  })
})
