/**
 * Template Events
 *
 * This file defines event types related to export templates, including
 * template creation, update, deletion, and error handling.
 *
 * @module types/services/events/templateEvents
 */

import type { AppEventType } from './eventTypes'
import type { ExportFormat } from './exportEvents'

/**
 * Template Error Event
 */
export interface TemplateErrorEvent {
  type: AppEventType.TemplateError
  payload: {
    operation: string
    error: string
  }
}

/**
 * Template Created Event
 */
export interface TemplateCreatedEvent {
  type: AppEventType.TemplateCreated
  payload: {
    templateId: string
    format: ExportFormat
  }
}

/**
 * Template Updated Event
 */
export interface TemplateUpdatedEvent {
  type: AppEventType.TemplateUpdated
  payload: {
    templateId: string
    format: ExportFormat
  }
}

/**
 * Template Deleted Event
 */
export interface TemplateDeletedEvent {
  type: AppEventType.TemplateDeleted
  payload: {
    templateId: string
  }
}

/**
 * Template Default Changed Event
 */
export interface TemplateDefaultChangedEvent {
  type: AppEventType.TemplateDefaultChanged
  payload: {
    templateId: string
  }
}

/**
 * Templates Imported Event
 */
export interface TemplatesImportedEvent {
  type: AppEventType.TemplatesImported
  payload: {
    count: number
  }
}

/**
 * Template Applied Event
 */
export interface TemplateAppliedEvent {
  type: AppEventType.TemplateApplied
  payload: {
    templateId: string
    format: ExportFormat
  }
}
