/**
 * Tutorial Hook
 *
 * React hook for managing tutorial functionality and state.
 * Provides easy access to tutorial controls and state management.
 */

import type { TutorialState, UseTutorialReturn } from '@/types/tutorial'
import type { BaseEvent } from '@/types/services/events'
import { useCallback, useEffect, useState } from 'react'
import { tutorialConfig } from '@/config/tutorialConfig'
import { appEventBus } from '@/services/core/event-bus'
import { tutorialService } from '@/services/tutorial/tutorialService'

/**
 * Custom hook for tutorial functionality
 */
export function useTutorial(): UseTutorialReturn {
  const [state, setState] = useState<TutorialState>(tutorialService.getState())

  /**
   * Update state when tutorial events occur
   */
  const handleTutorialEvent = useCallback((_event: BaseEvent) => {
    // The event payload should contain tutorial event data
    // Note: event parameter is available but not currently used
    setState(tutorialService.getState())
  }, [])

  /**
   * Start a tutorial module
   */
  const startTutorial = useCallback((moduleId: string) => {
    tutorialService.startTutorial(moduleId)
  }, [])

  /**
   * Stop the current tutorial
   */
  const stopTutorial = useCallback(() => {
    tutorialService.stopTutorial()
  }, [])

  /**
   * Skip the current tutorial
   */
  const skipTutorial = useCallback(() => {
    tutorialService.skipTutorial()
  }, [])

  /**
   * Check if a tutorial is available
   */
  const isTutorialAvailable = useCallback((moduleId: string): boolean => {
    return moduleId in tutorialConfig.modules
  }, [])

  /**
   * Check if a tutorial is completed
   */
  const isTutorialCompleted = useCallback((moduleId: string): boolean => {
    return tutorialService.isCompleted(moduleId)
  }, [])

  /**
   * Set up event listeners
   */
  useEffect(() => {
    const unsubscribe = appEventBus.on('tutorial:event', handleTutorialEvent)
    return unsubscribe
  }, [handleTutorialEvent])

  /**
   * Auto-start tutorial on first visit
   * Note: Auto-start is now handled by App.tsx after template selection
   * This effect is kept for potential future use but disabled
   */
  useEffect(() => {
    // Check if this is the first time the user is visiting
    const hasSeenTutorial = localStorage.getItem('renopilot-has-seen-tutorial')

    if (!hasSeenTutorial && !state.isRunning) {
      // Find the first auto-start tutorial
      const autoStartTutorial = Object.values(tutorialConfig.modules).find(
        module => module.autoStart && !tutorialService.isCompleted(module.id),
      )

      if (autoStartTutorial) {
        // Delay to ensure DOM is ready
        setTimeout(() => {
          startTutorial(autoStartTutorial.id)
          localStorage.setItem('renopilot-has-seen-tutorial', 'true')
        }, 1000)
      }
    }
  }, [startTutorial, state.isRunning])

  return {
    state,
    startTutorial,
    stopTutorial,
    skipTutorial,
    isTutorialAvailable,
    isTutorialCompleted,
  }
}

/**
 * Hook for tutorial trigger button
 */
export function useTutorialTrigger() {
  const { startTutorial, isTutorialAvailable, isTutorialCompleted } = useTutorial()

  /**
   * Get available tutorials for a menu
   */
  const getAvailableTutorials = useCallback(() => {
    return Object.values(tutorialConfig.modules).map(module => ({
      id: module.id,
      name: module.name,
      description: module.description,
      category: module.category,
      completed: isTutorialCompleted(module.id),
      available: isTutorialAvailable(module.id),
    }))
  }, [isTutorialAvailable, isTutorialCompleted])

  /**
   * Start tutorial by category
   */
  const startTutorialByCategory = useCallback((category: 'onboarding' | 'feature' | 'advanced') => {
    const tutorial = Object.values(tutorialConfig.modules).find(
      module => module.category === category && !isTutorialCompleted(module.id),
    )
    if (tutorial) {
      startTutorial(tutorial.id)
    }
  }, [startTutorial, isTutorialCompleted])

  return {
    startTutorial,
    getAvailableTutorials,
    startTutorialByCategory,
  }
}
