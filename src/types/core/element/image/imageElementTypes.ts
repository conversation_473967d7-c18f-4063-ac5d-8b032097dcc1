/**
 * Defines the type for image elements within the application.
 *
 * @remarks
 * This module provides the `Image` interface, which extends {@link ShapeElement}
 * to include properties specific to displaying images, such as the source URL,
 * position, and dimensions.
 *
 * @module types/core/element/image/imageElementTypes
 */
import type { ShapeElement } from '@/types/core/elementDefinitions' // Changed from '@core/elementDefinitions'

/**
 * Represents an image element with a source URL and display properties.
 *
 * @remarks
 * This interface extends the base {@link ShapeElement} to add image-specific attributes, including:
 * - Source URL or path (`src`).
 * - Display dimensions (`width`, `height`).
 * - Position coordinates (`position`).
 * - Geometry properties for cost calculations (`perimeter`, `area`).
 *
 * @example
 * ```typescript
 * const myImage: Image = {
 *   // Properties from ShapeElement (id, type, visible, etc.)
 *   id: 'img-001',
 *   type: 'image', // Or a more specific ElementType if defined
 *   visible: true,
 *   locked: false,
 *   position: { x: 100, y: 100 },
 *   rotation: 0,
 *   scale: { x: 1, y: 1 },
 *   selectable: true,
 *   draggable: true,
 *   showHandles: true,
 *   // Image-specific properties
 *   src: 'path/to/my_image.png',
 *   width: 200,
 *   height: 150,
 *   // Geometry properties for cost calculations
 *   properties: {
 *     perimeter: 700, // 2 * (width + height)
 *     area: 30000, // width * height
 *     costUnitPrice: 10,
 *     costMultiplier: 1,
 *     costBasis: 'area'
 *   }
 * };
 * ```
 *
 * @see {@link ShapeElement} for base shape properties.
 * @see {@link Point} for the structure of position coordinates.
 */
export interface Image extends ShapeElement {
  /**
   * The source of the image.
   * If sourceType is 'url', this is a URL or local path to the image resource.
   * If sourceType is 'svg_inline_data', this is the actual SVG string.
   *
   * @remarks
   * When sourceType is 'url', this string should point to a valid image file
   * (e.g., PNG, JPG, SVG) that can be loaded and displayed.
   * It supports both relative/absolute local paths and remote URLs.
   */
  // src: string;

  /**
   * Specifies the nature of the 'src' property.
   * 'url': src is a path/URL to an image file.
   * 'svg_inline_data': src is an inline SVG string.
   */
  // sourceType: 'url' | 'svg_inline_data';

  /**
   * Alternative text for the image, for accessibility.
   */
  // alt?: string;

  /**
   * The x and y coordinates where the top-left corner of the image should be rendered
   * on the canvas or parent container.
   *
   * @remarks
   * Uses a {@link Point} object. The coordinate system (e.g., relative to canvas,
   * parent group) depends on the rendering context.
   */
  // position: Point;

  /**
   * The display width of the image in pixels.
   * @remarks The value must be a positive number.
   * @throws {Error} Implementations might throw an error if a non-positive value is set.
   */
  // width: number;

  /**
   * The display height of the image in pixels.
   * @remarks The value must be a positive number.
   * @throws {Error} Implementations might throw an error if a non-positive value is set.
   */
  // height: number;

  /**
   * Properties specific to the image element.
   * @remarks
   * This object contains additional properties that are specific to image elements,
   * including geometry properties for cost calculations.
   */
  properties?: {
    /**
     * The source URL or path of the image.
     */
    src?: string

    /**
     * The width of the image in pixels.
     */
    width?: number

    /**
     * The height of the image in pixels.
     */
    height?: number

    /**
     * Alternative text for the image.
     */
    alt?: string

    /**
     * The type of source (URL or inline SVG data).
     */
    sourceType?: 'url' | 'svg_inline_data'

    /**
     * The position X coordinate of the image.
     */
    x?: number

    /**
     * The position Y coordinate of the image.
     */
    y?: number

    /**
     * The rotation angle of the image in degrees.
     */
    rotation?: number

    /**
     * The opacity of the image (0-1).
     */
    opacity?: number

    /**
     * The perimeter of the image in pixels.
     * @remarks
     * For an image, this is calculated as 2 * (width + height).
     */
    perimeter?: number

    /**
     * The area of the image in square pixels.
     * @remarks
     * For an image, this is calculated as width * height.
     */
    area?: number

    /**
     * The unit cost for cost calculations.
     * @default 0
     */
    costUnitPrice?: number

    /**
     * The multiplier for cost calculations.
     * @default 1
     */
    costMultiplier?: number

    /**
     * The basis for cost calculations ('area', 'perimeter', or 'unit').
     * @default 'area'
     */
    costBasis?: 'area' | 'perimeter' | 'unit'

    /**
     * The total cost calculated based on the unit cost, basis, and multiplier.
     */
    costTotal?: number
  }
}
