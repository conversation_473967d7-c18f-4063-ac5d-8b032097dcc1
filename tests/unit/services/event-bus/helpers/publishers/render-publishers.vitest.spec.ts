import type { ShapeModel } from '@/types/core/models'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  publishDataUpdated,
  publishRenderTrigger,
} from '@/services/event-bus/helpers/publishers/render-publishers'
import { AppEventType } from '@/types/services/events'
// Create a simple Point class for testing
class Point {
  constructor(public x: number, public y: number) {}
}

describe('render Publishers', () => {
  let mockEventBus: any
  let mockShapes: ShapeModel[]

  beforeEach(() => {
    mockEventBus = {
      publish: vi.fn(),
    }

    mockShapes = [
      { id: 'shape-1', type: 'rectangle', properties: { width: 100, height: 50 } },
      { id: 'shape-2', type: 'circle', properties: { radius: 30 } },
    ] as ShapeModel[]
  })

  describe('render Trigger', () => {
    it('should publish render trigger event correctly', () => {
      const reason = 'test_trigger'

      publishRenderTrigger(mockEventBus, mockShapes, reason)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.RENDER_TRIGGER,
        payload: { shapes: mockShapes, reason },
      })
    })

    it('should use default reason if not provided', () => {
      publishRenderTrigger(mockEventBus, mockShapes)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.RENDER_TRIGGER,
        payload: { shapes: mockShapes, reason: 'manual_trigger' },
      })
    })

    it('should handle empty shapes array', () => {
      publishRenderTrigger(mockEventBus, [])

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.RENDER_TRIGGER,
        payload: { shapes: [], reason: 'manual_trigger' },
      })
    })
  })

  describe('data Updated', () => {
    it('should publish data updated event correctly', () => {
      publishDataUpdated(mockEventBus, mockShapes)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.DATA_UPDATED,
        payload: { type: 'shapes', data: mockShapes },
      })
    })

    it('should handle empty shapes array', () => {
      publishDataUpdated(mockEventBus, [])

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.DATA_UPDATED,
        payload: { type: 'shapes', data: [] },
      })
    })
  })
})
