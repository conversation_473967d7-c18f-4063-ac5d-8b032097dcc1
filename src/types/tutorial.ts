/**
 * Tutorial System Types
 *
 * Type definitions for the driver.js tutorial system that provides
 * guided tours and onboarding experiences for different application modules.
 */

import type { DriveStep } from 'driver.js'

/**
 * Tutorial step configuration extending driver.js DriveStep
 */
export interface TutorialStep extends Omit<DriveStep, 'element'> {
  /** CSS selector or element reference for the step */
  element: string
  /** Step title */
  title: string
  /** Step description */
  description: string
  /** Optional step position */
  position?: 'top' | 'bottom' | 'left' | 'right' | 'auto'
  /** Whether to show progress indicator */
  showProgress?: boolean
  /** Custom CSS classes for the step */
  className?: string
  /** Whether this step can be skipped */
  allowClose?: boolean
  /** Custom buttons for this step */
  showButtons?: ('next' | 'previous' | 'close')[]
}

/**
 * Tutorial module configuration
 */
export interface TutorialModule {
  /** Unique identifier for the tutorial module */
  id: string
  /** Display name for the tutorial */
  name: string
  /** Brief description of what this tutorial covers */
  description: string
  /** Array of tutorial steps */
  steps: TutorialStep[]
  /** Whether this tutorial should auto-start */
  autoStart?: boolean
  /** Prerequisites for this tutorial */
  prerequisites?: string[]
  /** Tutorial category */
  category: 'onboarding' | 'feature' | 'advanced'
}

/**
 * Tutorial configuration for the entire application
 */
export interface TutorialConfig {
  /** Available tutorial modules */
  modules: Record<string, TutorialModule>
  /** Default tutorial settings */
  defaults: {
    /** Default animation duration */
    animationDuration?: number
    /** Whether to show dots indicator */
    showProgress?: boolean
    /** Whether to allow closing tutorial */
    allowClose?: boolean
    /** Default overlay opacity */
    overlayOpacity?: number
  }
  /** Tutorial completion tracking */
  completion: {
    /** Storage key for tracking completed tutorials */
    storageKey: string
    /** Whether to track completion */
    enabled: boolean
  }
}

/**
 * Tutorial state management
 */
export interface TutorialState {
  /** Currently active tutorial */
  activeTutorial: string | null
  /** Current step index */
  currentStep: number
  /** Whether tutorial is running */
  isRunning: boolean
  /** Completed tutorials */
  completedTutorials: string[]
  /** Whether to show tutorial hints */
  showHints: boolean
}

/**
 * Tutorial service interface
 */
export interface TutorialService {
  /** Start a specific tutorial */
  startTutorial: (moduleId: string) => void
  /** Stop current tutorial */
  stopTutorial: () => void
  /** Go to next step */
  nextStep: () => void
  /** Go to previous step */
  previousStep: () => void
  /** Skip current tutorial */
  skipTutorial: () => void
  /** Mark tutorial as completed */
  markCompleted: (moduleId: string) => void
  /** Check if tutorial is completed */
  isCompleted: (moduleId: string) => boolean
  /** Get tutorial state */
  getState: () => TutorialState
  /** Reset all tutorials */
  resetAll: () => void
}

/**
 * Tutorial event types
 */
export type TutorialEvent =
  | { type: 'tutorial:started', payload: { moduleId: string } }
  | { type: 'tutorial:completed', payload: { moduleId: string } }
  | { type: 'tutorial:skipped', payload: { moduleId: string } }
  | { type: 'tutorial:step:changed', payload: { moduleId: string, stepIndex: number } }
  | { type: 'tutorial:stopped', payload: { moduleId: string } }

/**
 * Tutorial hook return type
 */
export interface UseTutorialReturn {
  /** Current tutorial state */
  state: TutorialState
  /** Start tutorial function */
  startTutorial: (moduleId: string) => void
  /** Stop tutorial function */
  stopTutorial: () => void
  /** Skip tutorial function */
  skipTutorial: () => void
  /** Check if tutorial is available */
  isTutorialAvailable: (moduleId: string) => boolean
  /** Check if tutorial is completed */
  isTutorialCompleted: (moduleId: string) => boolean
}
