import type { ErrorPayload } from '@/services/event-bus/helpers/error-helpers'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { CoreError, ErrorType } from '@/core/errors/CoreError'
import { publishCoreError, publishError, publishValidationError } from '@/services/event-bus/helpers/error-helpers'
import { AppEventType } from '@/types/services/events'

describe('error Helpers', () => {
  // Mock EventBus
  const mockEventBus = {
    publish: vi.fn(),
    subscribe: vi.fn(),
    unsubscribe: vi.fn(),
    reset: vi.fn(),
    getSubscriptionCount: vi.fn(),
    setConfig: vi.fn(),
    getConfig: vi.fn(),
  }

  // Spy on console methods
  beforeEach(() => {
    vi.spyOn(console, 'warn').mockImplementation(() => {})
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
    mockEventBus.publish.mockClear()
  })

  describe('publishError', () => {
    it('should publish an error event with the correct payload', () => {
      const code = ErrorType.VALIDATION_FAILED
      const message = 'Test error message'
      const details = { test: 'details' }

      publishError(mockEventBus, code, message, details)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: {
          message,
          code,
          details,
          critical: false,
          stack: undefined,
        },
      })
    })

    it('should log to console by default', () => {
      publishError(mockEventBus, ErrorType.VALIDATION_FAILED, 'Test message')

      expect(console.warn).toHaveBeenCalled()
      expect(console.error).not.toHaveBeenCalled()
    })

    it('should use console.error for critical errors', () => {
      publishError(
        mockEventBus,
        ErrorType.INTERNAL_ERROR,
        'Critical error',
        {},
        { critical: true },
      )

      expect(console.error).toHaveBeenCalled()
      expect(console.warn).not.toHaveBeenCalled()
    })

    it('should not log to console when logToConsole is false', () => {
      publishError(
        mockEventBus,
        ErrorType.VALIDATION_FAILED,
        'Silent error',
        {},
        { logToConsole: false },
      )

      expect(console.warn).not.toHaveBeenCalled()
      expect(console.error).not.toHaveBeenCalled()
    })

    it('should include stack trace when details is an Error', () => {
      const error = new Error('Test error')

      publishError(mockEventBus, ErrorType.INTERNAL_ERROR, 'Error with stack', error)

      const publishCall = mockEventBus.publish.mock.calls[0][0]
      expect(publishCall.payload.stack).toBe(error.stack)
    })
  })

  describe('publishCoreError', () => {
    it('should call publishError with CoreError properties', () => {
      const error = new CoreError(
        ErrorType.VALIDATION_FAILED,
        'Validation error',
        { field: 'name' },
      )

      publishCoreError(mockEventBus, error)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: {
          message: error.message,
          code: error.type,
          details: error.metadata,
          critical: false,
          stack: undefined,
        },
      })
    })

    it('should merge additional context with error metadata', () => {
      const error = new CoreError(
        ErrorType.VALIDATION_FAILED,
        'Validation error',
        { field: 'name' },
      )

      const additionalContext = { userId: '123' }

      publishCoreError(mockEventBus, error, additionalContext)

      const publishCall = mockEventBus.publish.mock.calls[0][0]
      expect(publishCall.payload.details).toEqual({
        field: 'name',
        userId: '123',
      })
    })

    it('should pass options to publishError', () => {
      const error = new CoreError(ErrorType.INTERNAL_ERROR, 'Critical error')

      publishCoreError(
        mockEventBus,
        error,
        {},
        { critical: true, logToConsole: false },
      )

      // Should not log to console
      expect(console.warn).not.toHaveBeenCalled()
      expect(console.error).not.toHaveBeenCalled()

      // Should mark as critical in payload
      const publishCall = mockEventBus.publish.mock.calls[0][0]
      expect(publishCall.payload.critical).toBe(true)
    })
  })

  describe('publishValidationError', () => {
    it('should call publishError with validation error details', () => {
      const message = 'Validation failed'
      const validationErrors = ['Field is required', 'Value is invalid']
      const context = { formData: { name: '' } }

      publishValidationError(mockEventBus, message, validationErrors, context)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: {
          message,
          code: ErrorType.VALIDATION_FAILED,
          details: {
            validationErrors,
            context,
          },
          critical: false,
          stack: undefined,
        },
      })
    })

    it('should handle empty validation errors array', () => {
      const message = 'Validation failed'
      const validationErrors: string[] = []

      publishValidationError(mockEventBus, message, validationErrors)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: {
          message,
          code: ErrorType.VALIDATION_FAILED,
          details: {
            validationErrors,
            context: undefined,
          },
          critical: false,
          stack: undefined,
        },
      })
    })

    it('should handle null validation errors', () => {
      const message = 'Validation failed'

      publishValidationError(mockEventBus, message, null as any)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: {
          message,
          code: ErrorType.VALIDATION_FAILED,
          details: {
            validationErrors: null,
            context: undefined,
          },
          critical: false,
          stack: undefined,
        },
      })
    })

    // Note: publishValidationError doesn't accept options parameter in the implementation
    it('should use default options when publishing validation errors', () => {
      const message = 'Validation error'
      const validationErrors = ['Field is required']

      // Use a spy on the event bus instead
      publishValidationError(
        mockEventBus,
        message,
        validationErrors,
        {},
      )

      // Verify the event bus publish was called with the right parameters
      expect(mockEventBus.publish).toHaveBeenCalledWith(
        expect.objectContaining({
          type: AppEventType.ERROR_OCCURRED,
          payload: expect.objectContaining({
            message,
            code: ErrorType.VALIDATION_FAILED,
            details: expect.objectContaining({
              validationErrors,
              context: {},
            }),
          }),
        }),
      )
    })
  })

  describe('error Payload', () => {
    it('should create a valid error payload', () => {
      const payload: ErrorPayload = {
        message: 'Test error',
        code: ErrorType.VALIDATION_FAILED,
        details: { test: 'value' },
        critical: true,
        stack: 'test stack trace',
      }

      expect(payload.message).toBe('Test error')
      expect(payload.code).toBe(ErrorType.VALIDATION_FAILED)
      expect(payload.details).toEqual({ test: 'value' })
      expect(payload.critical).toBe(true)
      expect(payload.stack).toBe('test stack trace')
    })

    it('should allow minimal error payload', () => {
      const payload: ErrorPayload = {
        message: 'Test error',
        code: ErrorType.UNKNOWN,
      }

      expect(payload.message).toBe('Test error')
      expect(payload.code).toBe(ErrorType.UNKNOWN)
      expect(payload.details).toBeUndefined()
      expect(payload.critical).toBeUndefined()
      expect(payload.stack).toBeUndefined()
    })
  })

  describe('error handling with null/undefined values', () => {
    it('should handle error objects in details parameter', () => {
      const errorObj = new Error('Original error')

      publishError(
        mockEventBus,
        ErrorType.UNKNOWN,
        'Test error',
        errorObj,
      )

      const publishCall = mockEventBus.publish.mock.calls[0][0]
      expect(publishCall.payload.stack).toBe(errorObj.stack)
    })

    it('should handle non-error objects in details parameter', () => {
      const details = { userId: '123', action: 'login' }

      publishError(
        mockEventBus,
        ErrorType.UNKNOWN,
        'Test error',
        details,
      )

      const publishCall = mockEventBus.publish.mock.calls[0][0]
      expect(publishCall.payload.details).toEqual(details)
      expect(publishCall.payload.stack).toBeUndefined()
    })

    it('should use default values for optional parameters', () => {
      publishError(
        mockEventBus,
        ErrorType.UNKNOWN,
        'Test error',
      )

      const publishCall = mockEventBus.publish.mock.calls[0][0]
      expect(publishCall.payload.details).toBeUndefined()
      expect(publishCall.payload.critical).toBe(false)
    })
  })
})
