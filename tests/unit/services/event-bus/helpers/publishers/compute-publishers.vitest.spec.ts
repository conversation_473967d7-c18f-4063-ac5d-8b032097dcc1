import type { TransformOptions } from '@/types/core/element/compute'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  publishComputeRequest,
  publishComputeTransform,
} from '@/services/event-bus/helpers/publishers/compute-publishers'
import { ComputeOperation } from '@/types/core/element/compute'
import { AppEventType } from '@/types/services/events'

describe('compute Publishers', () => {
  let mockEventBus: any

  beforeEach(() => {
    mockEventBus = {
      publish: vi.fn(),
    }
  })

  describe('compute Request', () => {
    it('should publish compute request event correctly', () => {
      const operation = ComputeOperation.AREA
      const shapeIds = ['shape-1', 'shape-2']
      const computeConfig = { precision: 2 }

      publishComputeRequest(mockEventBus, operation, shapeIds, computeConfig)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.COMPUTE_REQUEST,
        payload: {
          operation,
          shapeIds,
          computeConfig,
        },
      })
    })

    it('should handle compute request without config', () => {
      const operation = ComputeOperation.PERIMETER
      const shapeIds = ['shape-1']

      publishComputeRequest(mockEventBus, operation, shapeIds)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.COMPUTE_REQUEST,
        payload: {
          operation,
          shapeIds,
          computeConfig: undefined,
        },
      })
    })

    it('should handle empty shape IDs array', () => {
      const operation = ComputeOperation.BOUNDING_BOX
      const shapeIds: string[] = []

      publishComputeRequest(mockEventBus, operation, shapeIds)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.COMPUTE_REQUEST,
        payload: {
          operation,
          shapeIds: [],
          computeConfig: undefined,
        },
      })
    })
  })

  describe('compute Transform', () => {
    it('should publish compute transform event correctly', () => {
      const shapeIds = ['shape-1', 'shape-2']
      const transformOptions: TransformOptions = {
        rotation: 45,
        scale: { x: 1.5, y: 1.5 },
        translation: { x: 10, y: 20 },
      }

      publishComputeTransform(mockEventBus, shapeIds, transformOptions)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.COMPUTE_TRANSFORM,
        payload: {
          shapeIds,
          transformOptions,
        },
      })
    })

    it('should handle partial transform options', () => {
      const shapeIds = ['shape-1']
      const transformOptions: TransformOptions = {
        rotation: 90,
      }

      publishComputeTransform(mockEventBus, shapeIds, transformOptions)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.COMPUTE_TRANSFORM,
        payload: {
          shapeIds,
          transformOptions,
        },
      })
    })

    it('should handle empty shape IDs array', () => {
      const shapeIds: string[] = []
      const transformOptions: TransformOptions = {
        scale: { x: 2, y: 2 },
      }

      publishComputeTransform(mockEventBus, shapeIds, transformOptions)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.COMPUTE_TRANSFORM,
        payload: {
          shapeIds: [],
          transformOptions,
        },
      })
    })
  })
})
