/**
 * Interface for shape element creator implementations.
 *
 * @remarks
 * This interface serves as the foundation for all shape creator implementations.
 * It defines the standard contract that all concrete shape creators must follow,
 * ensuring consistent behavior across different shape types.
 */

import type { BaseElementCreationParams } from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point'
import type {
  BaseStyleProperties,
  ElementType,
  MetadataProperties,
  ShapeElement,
} from '@/types/core/elementDefinitions'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
import { ensureCompleteMetadata } from '@/lib/utils/element/elementUtils'
import { MajorCategory } from '@/types/core/majorMinorTypes'

// Helper function to format the element type into a more readable name
function formatElementTypeName(elementType: ElementType): string {
  if (elementType == null)
    return 'Unnamed Element'
  const typeStr = elementType.toString()
  return typeStr.charAt(0).toUpperCase() + typeStr.slice(1).toLowerCase()
}

// Define a type for the common properties returned by createCommonProperties
// This should align with the properties all ShapeElement implementors will have at their base.
export type CommonShapeElementProps = {
  id: string
  type: ElementType
  majorCategory: MajorCategory
  minorCategory?: MinorCategory
  zLevelId?: string
  isFixedCategory?: boolean
  position: PointData
  rotation: number
  visible: boolean
  locked: boolean
  selectable: boolean
  draggable: boolean
  showHandles: boolean
  metadata: MetadataProperties
  layer?: string
  zIndex?: number
  cornerRadius?: number // Add cornerRadius property
} & BaseStyleProperties

/**
 * Interface that shape element creators must implement.
 */
export abstract class ShapeCreator<
  T extends ShapeElement,
  P extends BaseElementCreationParams,
> {
  protected elementType: ElementType

  constructor(type: ElementType) {
    this.elementType = type
  }

  protected createCommonProperties(
    id: string,
    params: P,
  ): CommonShapeElementProps {
    // Ensure position is handled correctly. If P guarantees position, direct access is fine.
    // If P is a union, some members might not have `position` directly.
    // For now, we assume `params.position` will be available if relevant for the specific P type.
    const paramSpecificPosition = (params as unknown as { position?: PointData }).position

    const position: PointData = {
      x: paramSpecificPosition?.x ?? 0,
      y: paramSpecificPosition?.y ?? 0,
    }
    if (paramSpecificPosition && typeof paramSpecificPosition.z === 'number') {
      position.z = paramSpecificPosition.z
    }

    const metadata = ensureCompleteMetadata({
      ...(params.metadata ?? {}),
      name: params.metadata?.name ?? formatElementTypeName(params.type as ElementType),
      createdAt: params.metadata?.createdAt ?? Date.now(),
      updatedAt: params.metadata?.updatedAt ?? Date.now(),
    })

    // Layer properties should now be directly available on params from ShapeCreationService
    const paramsWithLayer = params as unknown as {
      majorCategory?: MajorCategory
      minorCategory?: MinorCategory
      zLevelId?: string
      isFixedCategory?: boolean
      zIndex?: number
    }
    const majorCategory = paramsWithLayer.majorCategory
    const minorCategory = paramsWithLayer.minorCategory
    const zLevelId = paramsWithLayer.zLevelId
    const isFixedCategory = paramsWithLayer.isFixedCategory
    const zIndex = paramsWithLayer.zIndex // zIndex is also passed through

    console.warn('[ShapeCreator createCommonProperties] PARAMS RECEIVED:', {
      majorCategory,
      minorCategory,
      zLevelId,
      isFixedCategory,
      zIndex,
      paramType: params.type,
    })

    const common: CommonShapeElementProps = {
      id,
      type: params.type as ElementType,
      majorCategory: majorCategory ?? MajorCategory.FURNITURE, // Default if not provided
      minorCategory,
      zLevelId,
      isFixedCategory,
      position,
      rotation: params.rotation ?? 0,
      visible: params.visible ?? true,
      locked: params.locked ?? false,
      selectable: params.selectable ?? true,
      draggable: params.draggable ?? true,
      showHandles: params.showHandles ?? true,
      metadata,
      layer: params.layer, // This is the old layer property, keep for now if used elsewhere
      zIndex, // Use the zIndex passed from ShapeCreationService
      fill: params.fill,
      stroke: params.stroke,
      strokeWidth: params.strokeWidth,
      opacity: params.opacity,
      strokeDasharray: params.strokeDasharray,
      cornerRadius: (params as unknown as { cornerRadius?: number }).cornerRadius ?? 0, // Use type assertion for cornerRadius
    }

    console.warn('[ShapeCreator createCommonProperties] COMMON OBJECT CREATED:', {
      id: common.id,
      type: common.type,
      majorCategory: common.majorCategory,
      minorCategory: common.minorCategory,
      zLevelId: common.zLevelId,
      isFixedCategory: common.isFixedCategory,
    })

    return common
  }

  abstract create(params: P): Promise<T>
  abstract createDefault(id: string, position: PointData): Promise<T>
}
