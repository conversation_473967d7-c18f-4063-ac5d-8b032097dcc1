import type { CreatePolygonParams } from '@/core/factory/ElementFactory'
// Import specific property types for assertions
import type { PolygonProperties } from '@/types/core/models'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { PolygonCreator } from '@/core/factory/creators/PolygonCreator'
import { Point, PointUtils } from '@/types/core/element/geometry/point'
import { Polygon } from '@/types/core/element/shape/shape'
import { ElementType } from '@/types/core/shape-type'

// Mock the Polygon class constructor
vi.mock('@/types/core/element/shape/polygon', () => {
  return {
    Polygon: vi.fn().mockImplementation((id, props, opts, pos) => ({
      id,
      properties: props,
      options: opts,
      position: pos ?? PointUtils.center(props.points.map((p: { x: number, y: number }) => new Point(p.x, p.y))), // Mock position calculation if needed
      // type: props.type, // Remove direct type assignment
      // points: props.points, // Remove direct points assignment
      getType: () => props.type,
      getPoints: () => props.points.map((p: { x: number, y: number }) => new Point(p.x, p.y)),
      // Add toJson method to fix the test
      toJson: () => ({
        id,
        type: props.type,
        properties: props,
        position: pos ?? PointUtils.center(props.points.map((p: { x: number, y: number }) => new Point(p.x, p.y))),
        options: opts,
      }),
    })),
  }
})

// Mock PointUtils.createRegularPolygon
vi.mock('@/types/core/element/geometry/point', async (importOriginal) => {
  const actual = await importOriginal<typeof import('@/types/core/element/geometry/point')>()
  return {
    ...actual, // Keep other PointUtils methods and Point class
    PointUtils: {
      ...actual.PointUtils, // Keep other methods
      createRegularPolygon: vi.fn((center, radius, sides) => {
        // Return simple mock points for testing creator logic
        const mockPoints = []
        for (let i = 0; i < sides; i++) {
          mockPoints.push(new Point(center.x + radius * Math.cos(i * 2 * Math.PI / sides), center.y + radius * Math.sin(i * 2 * Math.PI / sides)))
        }
        return mockPoints
      }),
      center: actual.PointUtils.center, // Ensure center calculation is real
    },
  }
})

describe('polygonCreator', () => {
  let creator: PolygonCreator

  beforeEach(() => {
    creator = new PolygonCreator()
    vi.clearAllMocks()
  })

  it('should create a custom Polygon from points', async () => {
    const inputPoints = [{ x: 0, y: 0 }, { x: 100, y: 0 }, { x: 50, y: 50 }]
    const params: CreatePolygonParams = {
      id: 'poly-custom',
      type: ElementType.POLYGON,
      points: inputPoints,
      stroke: '#ff00ff',
    }

    const polygon = await creator.create(params)

    expect(Polygon).toHaveBeenCalledTimes(1)
    expect(PointUtils.createRegularPolygon).not.toHaveBeenCalled()
    expect(Polygon).toHaveBeenCalledWith(
      'poly-custom',
      expect.objectContaining({
        type: ElementType.POLYGON,
        points: inputPoints,
        stroke: '#ff00ff',
      }),
      expect.objectContaining({}),
      undefined,
    )

    expect(polygon).toBeDefined()
    expect(polygon.id).toBe('poly-custom')
    // Assert on properties object
    expect(polygon.properties.type).toBe(ElementType.POLYGON)
    expect((polygon.properties as PolygonProperties).points).toEqual(inputPoints)
  })

  it('should create a regular Polygon from sides, radius, and center', async () => {
    const centerPos = { x: 200, y: 200 }
    const sides = 6
    const radius = 80
    const params: CreatePolygonParams = {
      id: 'poly-regular',
      type: ElementType.POLYGON,
      points: [],
      sides,
      radius,
      center: centerPos,
      fill: '#00ffff',
    }
    const mockGeneratedPoints = [
      new Point(280, 200),
      new Point(240, 269.28),
      new Point(160, 269.28),
      new Point(120, 200),
      new Point(160, 130.72),
      new Point(240, 130.72),
    ]
    const mockGeneratedPointsJson = mockGeneratedPoints.map(p => p.toJson());
    (PointUtils.createRegularPolygon as any).mockReturnValue(mockGeneratedPoints)

    const polygon = await creator.create(params)

    expect(PointUtils.createRegularPolygon).toHaveBeenCalledTimes(1)
    expect(PointUtils.createRegularPolygon).toHaveBeenCalledWith(centerPos, radius, sides)
    expect(Polygon).toHaveBeenCalledTimes(1)
    expect(Polygon).toHaveBeenCalledWith(
      'poly-regular',
      expect.objectContaining({
        type: ElementType.POLYGON,
        points: mockGeneratedPointsJson,
        fill: '#00ffff',
      }),
      expect.objectContaining({}),
      centerPos,
    )

    expect(polygon).toBeDefined()
    expect(polygon.id).toBe('poly-regular')
    // Assert on properties object
    expect(polygon.properties.type).toBe(ElementType.POLYGON)
    expect((polygon.properties as PolygonProperties).points).toEqual(mockGeneratedPointsJson)
  })

  it('should create a Triangle when type is TRIANGLE (using points)', async () => {
    const inputPoints = [{ x: 10, y: 10 }, { x: 110, y: 10 }, { x: 60, y: 110 }]
    const params: CreatePolygonParams = {
      id: 'tri-points',
      type: ElementType.TRIANGLE as any, // Cast needed
      points: inputPoints,
    }

    const triangle = await creator.create(params)

    expect(Polygon).toHaveBeenCalledTimes(1)
    expect(Polygon).toHaveBeenCalledWith(
      'tri-points',
      expect.objectContaining({
        type: ElementType.TRIANGLE,
        points: inputPoints,
      }),
      expect.objectContaining({}),
      undefined,
    )
    // Assert on properties object
    expect(triangle.properties.type).toBe(ElementType.TRIANGLE)
  })

  it('should create a Hexagon when type is HEXAGON (using points)', async () => {
    const inputPoints = [{ x: 1, y: 1 }, { x: 2, y: 1 }, { x: 3, y: 2 }, { x: 2, y: 3 }, { x: 1, y: 3 }, { x: 0, y: 2 }]
    const params: CreatePolygonParams = {
      id: 'hex-points',
      type: ElementType.HEXAGON as any, // Cast needed
      points: inputPoints,
    }

    const hexagon = await creator.create(params)

    expect(Polygon).toHaveBeenCalledTimes(1)
    expect(Polygon).toHaveBeenCalledWith(
      'hex-points',
      expect.objectContaining({
        type: ElementType.HEXAGON,
        points: inputPoints,
      }),
      expect.objectContaining({}),
      undefined,
    )
    // Assert on properties object
    expect(hexagon.properties.type).toBe(ElementType.HEXAGON)
  })

  it('should throw error for incorrect shape type', async () => {
    const params = {
      id: 'wrong-type-poly',
      type: ElementType.LINE,
      points: [{ x: 0, y: 0 }, { x: 10, y: 10 }],
    } as any

    await expect(creator.create(params)).rejects.toThrow(
      'PolygonCreator received incorrect shape type: line',
    )
    expect(Polygon).not.toHaveBeenCalled()
    expect(PointUtils.createRegularPolygon).not.toHaveBeenCalled()
  })

  it('should throw error if points array has less than 3 points', async () => {
    const params: CreatePolygonParams = {
      id: 'too-few-points',
      type: ElementType.POLYGON,
      points: [{ x: 0, y: 0 }, { x: 100, y: 0 }],
    }
    await expect(creator.create(params)).rejects.toThrow(
      'Polygon creation requires either a valid points array (>= 3 points) or valid sides (>= 3), radius (> 0), and position.',
    )
    expect(Polygon).not.toHaveBeenCalled()
  })

  it('should throw error if creating regular polygon without enough info (missing radius)', async () => {
    const params: CreatePolygonParams = {
      id: 'missing-radius',
      type: ElementType.POLYGON,
      points: [],
      sides: 5,
      // radius missing
      center: { x: 1, y: 1 },
    }
    await expect(creator.create(params)).rejects.toThrow(
      'Polygon creation requires either a valid points array (>= 3 points) or valid sides (>= 3), radius (> 0), and position.',
    )
    expect(PointUtils.createRegularPolygon).not.toHaveBeenCalled()
    expect(Polygon).not.toHaveBeenCalled()
  })

  it('should throw error if creating regular polygon without enough info (missing center)', async () => {
    const params: CreatePolygonParams = {
      id: 'missing-center',
      type: ElementType.POLYGON,
      points: [],
      sides: 4,
      radius: 10,
      // center missing
    }
    await expect(creator.create(params)).rejects.toThrow(
      'Polygon creation requires either a valid points array (>= 3 points) or valid sides (>= 3), radius (> 0), and position.',
    )
    expect(PointUtils.createRegularPolygon).not.toHaveBeenCalled()
    expect(Polygon).not.toHaveBeenCalled()
  })

  it('should create a default Polygon (Triangle)', async () => {
    const id = 'default-poly'
    const position = new Point(50, 50)
    const defaultSides = 3
    const defaultRadius = 50
    const mockDefaultPoints = [new Point(100, 50), new Point(25, 93.3), new Point(25, 6.7)]
    const mockDefaultPointsJson = mockDefaultPoints.map(p => p.toJson());
    (PointUtils.createRegularPolygon as any).mockReturnValue(mockDefaultPoints)

    const polygon = await creator.createDefault(id, position)

    expect(PointUtils.createRegularPolygon).toHaveBeenCalledTimes(1)
    expect(PointUtils.createRegularPolygon).toHaveBeenCalledWith(position, defaultRadius, defaultSides)
    expect(Polygon).toHaveBeenCalledTimes(1)
    expect(Polygon).toHaveBeenCalledWith(
      id,
      expect.objectContaining({ // Properties + Styles
        type: 'triangle', // Default creates a triangle
        points: mockDefaultPointsJson,
        stroke: '#000000',
        strokeWidth: 2,
        fill: '#FFFFFF',
      }),
      expect.objectContaining({}), // Options
      position, // Explicit position passed
    )

    expect(polygon).toBeDefined()
    expect(polygon.id).toBe(id)
    // Assert on properties object
    expect(polygon.properties.type).toBe('triangle')
    expect((polygon.properties as PolygonProperties).points).toEqual(mockDefaultPointsJson)
  })
})
