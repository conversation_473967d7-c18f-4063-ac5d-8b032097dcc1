/**
 * Unified Geometry Layout Component
 *
 * A standardized layout system for organizing geometry properties across all element types.
 * Provides consistent grid layouts and responsive design for property panels.
 *
 * Features:
 * - Unified grid system for all element types
 * - Responsive layout with consistent spacing
 * - Standardized property grouping
 * - Element-specific property visibility
 * - Consistent styling and typography
 *
 * Layout Structure:
 * - Position Controls: X, Y coordinates (2-column grid)
 * - Dimension Controls: Width, Height, Radius, etc. (adaptive grid)
 * - Transform Controls: Rotation, Scale (adaptive grid)
 * - Special Controls: Element-specific properties (adaptive grid)
 * - Calculation Section: Area, Perimeter, Cost (unified layout)
 *
 * @example
 * ```tsx
 * <UnifiedGeometryLayout elementType={ElementType.RECTANGLE}>
 *   <GeometrySection type="position" columns={2}>
 *     <PropertyField label="X" value={x} onChange={setX} />
 *     <PropertyField label="Y" value={y} onChange={setY} />
 *   </GeometrySection>
 *   <GeometrySection type="dimensions" columns={2}>
 *     <PropertyField label="Width" value={width} onChange={setWidth} />
 *     <PropertyField label="Height" value={height} onChange={setHeight} />
 *   </GeometrySection>
 * </UnifiedGeometryLayout>
 * ```
 */

import React from 'react'
import { cn } from '@/lib/utils'
import { ElementType } from '@/types/core/elementDefinitions'

/**
 * Props for the UnifiedGeometryLayout component
 */
interface UnifiedGeometryLayoutProps {
  /** The type of element being edited */
  elementType?: ElementType
  /** Child components to render within the layout */
  children: React.ReactNode
  /** Additional CSS classes */
  className?: string
}

/**
 * Props for the GeometrySection component
 */
interface GeometrySectionProps {
  /** The type of geometry section */
  type: 'position' | 'dimensions' | 'transform' | 'special' | 'calculation' | 'typography' | 'style' | 'content' | 'appearance' | 'colors' | 'patterns'
  /** Section title (optional, will be auto-generated if not provided) */
  title?: string
  /** Child components to render within the section */
  children: React.ReactNode
  /** Number of columns for the grid layout */
  columns?: 1 | 2 | 3
  /** Additional CSS classes */
  className?: string
  /** Whether to show the section title */
  showTitle?: boolean
}

/**
 * Default titles for geometry sections
 */
const SECTION_TITLES = {
  position: 'Position',
  dimensions: 'Scale',
  transform: 'Transform',
  special: 'Element Properties',
  calculation: 'Calculation & Cost',
  typography: 'Typography',
  style: 'Style',
  content: 'Content',
  appearance: 'Appearance',
  colors: 'Colors',
  patterns: 'Patterns',
}

/**
 * GeometrySection component for organizing related geometry properties.
 *
 * This component provides a standardized way to group related geometry controls
 * with consistent spacing, grid layout, and typography.
 *
 * @param props - The component props
 * @returns The rendered geometry section
 */
export function GeometrySection({
  type,
  title,
  children,
  columns = 2,
  className,
  showTitle = true,
}: GeometrySectionProps) {
  const sectionTitle = title ?? SECTION_TITLES[type]

  // Enhanced grid classes with responsive design
  const gridClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3',
  }

  // Section-specific styling for visual hierarchy
  const sectionStyles = {
    position: 'border-l-2 border-blue-200 pl-3',
    dimensions: 'border-l-2 border-green-200 pl-3',
    transform: 'border-l-2 border-purple-200 pl-3',
    special: 'border-l-2 border-orange-200 pl-3',
    calculation: 'border-l-2 border-gray-200 pl-3 bg-gray-50/50 p-3 rounded-md',
    typography: 'border-l-2 border-indigo-200 pl-3',
    style: 'border-l-2 border-pink-200 pl-3',
    content: 'border-l-2 border-teal-200 pl-3',
    appearance: 'border-l-2 border-rose-200 pl-3',
    colors: 'border-l-2 border-amber-200 pl-3',
    patterns: 'border-l-2 border-violet-200 pl-3',
  }

  return (
    <div className={cn(
      'space-y-3',
      sectionStyles[type],
      className,
    )}
    >
      {showTitle && (
        <h4 className="text-sm font-semibold text-foreground mb-2">
          {sectionTitle}
        </h4>
      )}
      <div className={cn(
        'grid gap-x-3 gap-y-2',
        gridClasses[columns],
      )}
      >
        {children}
      </div>
    </div>
  )
}

/**
 * UnifiedGeometryLayout component for standardized geometry property organization.
 *
 * This component provides a consistent layout system for all element types,
 * ensuring uniform spacing, grid alignment, and visual hierarchy across
 * different property panels.
 *
 * @param props - The component props
 * @returns The rendered unified geometry layout
 */
export function UnifiedGeometryLayout({
  elementType: _elementType,
  children,
  className,
}: UnifiedGeometryLayoutProps) {
  return (
    <div className={cn(
      'space-y-4 p-1',
      'max-h-[calc(100vh-200px)] overflow-y-auto', // Responsive height with scroll
      className,
    )}
    >
      {children}
    </div>
  )
}




