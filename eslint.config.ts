import { dirname } from 'node:path'
import { fileURLToPath } from 'node:url'
import antfu from '@antfu/eslint-config'
import globals from 'globals'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

export default antfu(
  {
    // General project settings
    ignores: ['dist', 'node_modules', 'docs', 'tests/**/*.md'],
    javascript: {
      // Ensure consistent JS/JSX parsing and environment
      overrides: {
        'no-console': ['warn', { allow: ['warn', 'error'] }],
        'prefer-const': 'error',
        'no-var': 'error',
      },
    },
    typescript: {
      // Global TypeScript settings
      tsconfigPath: './tsconfig.json',
      parserOptions: {
        tsconfigRootDir: __dirname,
        EXPERIMENTAL_useProjectService: true,
      },
      overrides: {
        '@typescript-eslint/no-explicit-any': 'warn',
        '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],
        '@typescript-eslint/no-non-null-assertion': 'warn',
        '@typescript-eslint/consistent-type-imports': 'error',
        '@typescript-eslint/prefer-optional-chain': 'error',
        '@typescript-eslint/no-unnecessary-type-assertion': 'warn',
        // Enable TypeScript unsafe rules that were missing
        '@typescript-eslint/no-unsafe-member-access': 'warn',
        '@typescript-eslint/no-unsafe-assignment': 'warn',
        '@typescript-eslint/no-unsafe-call': 'warn',
        // Configure strict boolean expressions with more permissive settings
        '@typescript-eslint/strict-boolean-expressions': [
          'warn',
          {
            allowString: true,
            allowNumber: true,
            allowNullableObject: true,
            allowNullableBoolean: true,
            allowNullableString: true,
            allowNullableNumber: true,
            allowAny: true,
          },
        ],
      },
    },
    react: {
      // Enables React specific linting rules.
      // antfu should handle reactPlugin, reactHooksPlugin, reactRefreshPlugin.
      // version: 'detect', // antfu usually autodetects this. If issues arise, this can be set here.
      overrides: {
        'react/prop-types': 'off',
        'react-refresh/only-export-components': [
          'warn',
          { allowConstantExport: true },
        ],
      },
    },
    stylistic: {
      indent: 2,
      quotes: 'single',
      semi: false,
    },
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
      },
    },
  },
  {
    files: ['vitest.config.ts'],
    languageOptions: {
      parserOptions: {
        project: './tsconfig.vitest.json',
        tsconfigRootDir: __dirname,
      },
    },
  },
  {
    files: ['playwright.config.ts'],
    languageOptions: {
      parserOptions: {
        project: './tsconfig.json',
        tsconfigRootDir: __dirname,
        EXPERIMENTAL_useProjectService: true,
      },
    },
  },
)
