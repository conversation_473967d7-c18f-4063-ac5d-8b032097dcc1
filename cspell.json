{"version": "0.2", "language": "en", "words": ["agentdeskai", "<PERSON><PERSON><PERSON>", "bbox", "<PERSON><PERSON><PERSON>", "centerlines", "circumcircle", "circumradius", "collinearity", "conv", "countertop", "Countertop", "custpoly", "dblclicked", "<PERSON><PERSON><PERSON>", "draggables", "executeautomation", "<PERSON><PERSON>", "Flexbox", "filechooser", "firecrawl", "FIRECRAWL", "glassmorphism", "<PERSON>", "Grasscloth", "grasscloth", "HVAC", "INSWING", "inswing", "internalremarks", "KBSC", "<PERSON>by", "lerp", "loggable", "lucide", "modelcontextprotocol", "mozfullscreenchange", "mouseup", "MVVM", "nesw", "NESW", "Nums", "nowrap", "OUTSWING", "outswing", "parseable", "partialize", "partialized", "Partialized", "pinnable", "Pinnable", "pline", "regpoly", "renopilot", "renderable", "reparent", "rgba", "RRGGBB", "sandboxed", "shadcn", "shapesapp", "semibold", "Spacebar", "tailwindcss", "Toggleable", "tooltips", "Ungroup", "ungroup", "ungrouping", "Unuse", "unregistration", "Uppercased", "uuidv", "validatable", "Validatable", "wicg", "zindex", "ZINDEX", "zundo", "const<PERSON><PERSON>", "constwoodfinal", "contenteditable", "keyup", "isNaN", "nullish", "eslint", "tsdoc", "jsdoc", "bbox", "util", "utils", "bezier", "quadratic", "cubic", "perimeter", "bounding", "ergonomics", "wastage", "polyline", "quadratic", "ellipse", "polygon", "rect", "bbox", "coord", "coords", "metadata", "params", "calc", "impl", "impl", "utils", "util", "facade", "strategy", "strategies", "validator", "validators", "creator", "creators", "factory", "factories", "registry", "registries", "compute", "computation", "computations", "area", "distance", "material", "materials", "cost", "costs", "space", "spaces", "planning", "layout", "layouts", "pathway", "pathways", "utilization", "score", "scores", "element", "elements", "shape", "shapes", "path", "paths", "line", "lines", "curve", "curves", "bezier", "cubic", "arc", "arcs", "circle", "circles", "rectangle", "rectangles", "square", "squares", "triangle", "triangles", "bezier", "quadratic", "cubic", "polyline", "ellipse", "polygon", "rect", "coord", "coords", "radiusx", "radiusy", "linestart", "lineend", "curtain", "flooring", "wastage", "tiebacks", "hemtop", "<PERSON><PERSON><PERSON>", "fullness", "hooksspacing", "tilecount", "tileable", "painttype", "surfacetype", "coverageperliter", "wastagerate", "thinnerperliter", "cansize", "stonetype", "tilewidth", "tilelength", "patternmatching", "jointwidth", "includejoints", "adhesivepersqm", "groutpersqm", "sealantpersqm", "travertine", "quartz", "granite", "marble", "bbox", "wallpaint", "wallpaper", "woodmaterial", "boardwidth", "boardlength", "installationpattern", "herringbone", "parquet", "adhesivepersqm", "finishpersqm", "rollwidth", "rolllength", "patternrepeat", "adhesivepersqmwallpaper", "roomheight", "stripscount", "wallsurfacearea", "stripsperroll", "rollsneeded", "countertop", "perimeter", "polyline", "quadratic", "bezier", "absolutepoints", "positionx", "positiony", "accessible", "accessibility", "wheelchair", "handrail", "handrails", "bedroom", "bedrooms", "children", "childage", "elderly", "kitchen", "kitchens", "multifunction", "multifunctional", "workspace", "workspaces", "seating", "furniture", "armrests", "adjustable", "ergonomic", "ergonomics", "deskheight", "chairheight", "seatheight", "clearance", "clearances", "corridor", "corridors", "pathway", "pathways", "turning", "diameter", "doorwidth", "opening", "openings", "lighting", "tasklight", "dimmable", "contrast", "glare", "nonslip", "threshold", "thresholds", "safetyglass", "safetylock", "roundedcorners", "isSecured", "hasSafetyLock", "sillHeight", "<PERSON><PERSON><PERSON><PERSON>", "hasContrastMarking", "isDimmable", "hasArmrests", "isStable", "isTaskLight", "customType", "furnitureType", "isLivingArea", "isRoomBoundary", "roomType", "childAge", "openingWidth", "<PERSON><PERSON><PERSON><PERSON>", "gaj<PERSON>", "<PERSON><PERSON><PERSON>", "gantt", "bookshelf", "bookshelves", "wardrobe", "wardrobes", "closet", "closets", "drawer", "drawers", "cabinet", "cabinets", "storage", "storages", "shelf", "shelves", "levels", "footprint", "footprints", "lightType", "ceiling", "pendant", "recessed", "overhead", "tasklight", "natural", "outdoor", "balcony", "bathroom", "weatherprotection", "protection", "protections", "molding", "moldings", "baseboard", "baseboards", "wallpaper", "wallpapers", "flooring", "floorings", "hardwood", "laminate", "vinyl", "carpet", "carpets", "concrete", "concretes", "slab", "slabs", "perimeter", "perimeters", "vertices", "vertex", "polygon", "polygons", "wastage", "wastages", "coverage", "coverages", "coats", "coat", "liters", "liter", "pieces", "piece", "rolls", "roll", "meters", "meter", "cubic", "square", "joints", "joint", "jointWidth", "unitsPerBox", "unitLength", "unitCount", "unitType", "amountWithWastage", "boxes", "box", "patternRepeat", "stripsPerRoll", "stripsNeeded", "rollsNeeded", "effectiveWidth", "effectiveHeight", "effectiveUnitArea", "tileArea", "tileCount", "tileWidth", "tileHeight", "coveragePerLiter", "requiredLiters", "litersWithWastage", "roundedLiters", "unitArea", "unitSize", "includeJoints", "repeatsPerWall", "adjustedWallHeight", "volumeWithWastage", "areaWithWastage", "perimeterWithWastage", "balcony", "balconies", "bathroom", "bathrooms", "pathway", "pathways", "ergonomics", "ergonomic", "workspace", "workspaces", "weatherresistant", "weatherprotection", "fixtures", "fixture", "clearance", "clearances", "nonslip", "grab", "bars", "accessibility", "accessible", "fileoverview", "overridable", "instantiation", "subclasses", "draggable", "selectable", "show<PERSON><PERSON>les", "strokeWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeLinecap", "strokeLinejoin", "creationRadius", "zIndex", "zLevelId", "isFixedCategory", "majorCategory", "minorCategory", "designType", "designCategory", "metadataBase", "elementName", "specificDesignMetadata", "majorCategoryOverride", "minorCategoryOverride", "zLevelIdOverride", "isFixedCategoryOverride", "openings", "swingDirection", "operationType", "wallId", "heightFromFloor", "totalHeight", "thickness", "doorWidth", "doorHeight", "windowWidth", "windowHeight", "sillHeight", "headHeight", "glazingType", "paneCount", "hasScreen", "<PERSON><PERSON><PERSON><PERSON>", "frameType", "hardwareType", "isExterior", "isAutomatic", "subCategory", "wallPosition", "roomType", "floorLevel", "ceilingHeight", "floorMaterial", "wallMaterial", "ceilingMaterial", "wallIds", "openingIds", "furnitureIds", "fixtureIds", "isOutdoor", "centroid", "perimeter", "drywall", "hardwood", "shoelace", "wallType", "construction", "totalHeight", "isLoadBearing", "baseElevation", "openingIds", "centerline", "midpoint", "startPoint", "endPoint", "loadbearing", "masonry", "framed", "concrete", "brick", "fireRating", "thermalRating", "sourceType", "svg", "inline", "fontFamily", "fontWeight", "fontStyle", "textAlign", "textBaseline", "lineHeight", "multiline", "ternary", "consequent", "nullish", "startAngle", "endAngle", "counterClockwise", "pathData", "generateArcPathData", "arcUtils", "bezier", "cubic", "quadratic", "control1", "control2", "costUnitPrice", "costMultiplierOrCount", "costBasis", "arrowStart", "arrowEnd", "polyline", "quadratic", "radiusX", "radiusY", "circumcircle", "startAngleRad", "centroid", "quadrilateral", "pentagon", "hexagon", "isRegular", "creationRadius", "creationCenter", "normalizeToPointData", "createRegularPolygonPointsInternal", "adjustedStartAngle", "angleStep", "adjustedPoints", "isIntendedRegular", "finalPoints", "calculatedCenter", "finalIsRegular", "finalSides", "processedPoints", "sumX", "sumY", "sumZ", "countZ", "creationParamsForCommonProps", "polygonElement", "pointsCount", "propertiesPointsCount", "startAngleForCreation", "cornerRadius", "finalCornerRadius", "createCommonProperties", "createSpecificProperties", "createDefault", "createRectangle", "createSquare", "createRoundedRectangle", "createRoundedSquare", "createEllipse", "createCircle", "createPolygon", "createTriangle", "createHexagon", "createLine", "createArc", "createPolyline", "createQuadratic", "createCubic", "createImage", "createText", "createWall", "createLoadBearingWall", "createExteriorWall", "normalizePosition", "ensureCompleteMetadata", "registerCreator", "getCreator", "hasCreator", "unregisterCreator", "listCreators", "createShape", "createPath", "createMedia", "createDesign", "UserRectangleInputParams", "UserEllipseInputParams", "UserPolygonInputParams", "UserLineInputParams", "UserArcInputParams", "UserPolylineInputParams", "UserQuadraticInputParams", "UserCubicInputParams", "UserImageInputParams", "UserTextInputParams", "UserWallInputParams", "ShapeCreationParamsUnion", "PathCreationOptionsUnion", "MediaCreationParamsUnion", "DesignCreationParamsUnion", "ElementCreationParamsUnion", "IElementFactory", "ElementFactory", "ShapeRepository", "getById", "getAll", "update", "remove", "setSelectedIds", "addToSelection", "removeFromSelection", "clearSelection", "getSelectedIds", "isSelected", "clearAll", "getByType", "getByProperty", "getByMetadata", "filter", "find", "exists", "count", "bringToFrontInLayer", "sendToBackInLayer", "setShapesFromExternal", "publishShapesUpdate", "deepMerge", "mergedMetadata", "topLevelChanges", "propertyChanges", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "layerChanged", "nextIntraLayerZIndex", "shapesInSameLayer", "maxZ", "mergedProperties", "updatedShape", "selectedShapeIds", "shapesPayload", "selectedIdsPayload", "syncL<PERSON><PERSON><PERSON><PERSON><PERSON>", "syncedShapes", "newShape", "styleKeys", "layerKeys", "targetValue", "sourceValue", "finalValue", "CoreCoordinator", "isInternalUpdate", "computeFacade", "shapeCreationService", "shapeEditService", "shapeDeleteService", "shapeSelectionService", "registerEventHandlers", "handleError", "updateConfig", "publishShapesUpdate", "getConfig", "ShapeCreateRequest", "ShapeEditRequest", "ShapeDeleteRequest", "ShapeSelectRequest", "ElementType", "specificEvent", "servicePayload", "serviceEventForCreationService", "currentSelectedIds", "shapeId", "changes", "updates", "changesProps", "<PERSON><PERSON><PERSON>", "costUnitPrice", "costBasis", "costMultiplierOrCount", "majorCategory", "minorCategory", "zLevelId", "cleanPattern", "PatternDefinition", "doesChangeRequireComputation", "ShapeCreationService", "ShapeEditServiceImpl", "ShapeDeleteService", "ShapeSelectionService", "IShapeEditService", "ComputeFacade", "ElementValidator", "ValidatorShape", "BaseEvent", "ShapeCreateEvent", "ShapeDeleteEvent", "ShapeEditEvent", "ShapeSelectRequestEvent", "AppEventMap", "ErrorService", "CoreConfig", "defaultCoreConfig", "createConfig", "safeValidate", "getService", "getServiceFactory", "ServiceId", "CoreError", "ErrorType", "AppEventType", "useShapesStore", "ElementValidator", "IElementValidator", "IBaseShapeValidator", "ValidatorConstructor", "initializeValidatorModules", "registerValidator", "hasValidator", "getValidator", "formatErrorMessage", "validateElement", "validateElements", "areAllElementsValid", "validatorModules", "validatorPaths", "typeEnumKey", "firstExport", "ValidatorClass", "validationUtils", "isValidPoint", "isValidColor", "isPolygonClosed", "isValueInRange", "createValidationResult", "trimmedColor", "rgbRegex", "rgbaRegex", "basicColors", "firstPoint", "lastPoint", "validatorBase", "BaseShapeValidator", "validateSpecific", "applyAllRules", "applySpecificRules", "specificErrors", "ruleErrors", "designValidatorBase", "BaseDesignValidator", "expectedElementType", "validateBaseType", "validateRequiredProperties", "checkLocation", "objectToCheck", "basePath", "shapeWithProperties", "elementWithMetadata", "openingValidator", "OpeningValidator", "DoorValidator", "WindowValidator", "ValidatableOpening", "ValidatableDoor", "ValidatableWindow", "validateSpecificOpeningProperties", "validateOpeningPlacementRule", "openingType", "heightFromFloor", "wallId", "wallPosition", "referencePoint", "swingDirection", "isExterior", "isAutomatic", "operationType", "sillHeight", "headHeight", "roomValidator", "RoomValidator", "ValidatableRoom", "validateRoomSizeRules", "roomType", "floorLevel", "ceilingHeight", "floorMaterial", "wallMaterial", "ceilingMaterial", "wallIds", "openingIds", "furnitureIds", "fixtureIds", "idArrays", "roomShape", "isOutdoor", "wallValidator", "WallValidator", "ConcreteWallElement", "wallType", "thickness", "pathObject", "linePath", "polylinePath", "arcValidator", "ArcValidator", "startAngle", "endAngle", "cubicValidator", "CubicValidator", "startPoint", "control1Point", "control2Point", "endPoint", "applyBusinessRules", "ValidationErrorCode", "MISSING_OR_INVALID_ID", "INVALID_STROKE_COLOR", "INVALID_FILL_COLOR", "VALIDATION_SPECIFIC_ERROR", "VALIDATION_RULE_ERROR", "INVALID_SHAPE_TYPE", "MISSING_PROPERTY", "INVALID_PROPERTY_VALUE", "INVALID_DIMENSION", "INVALID_POSITION", "INVALID_POINT", "INVALID_RADIUS", "INSUFFICIENT_POINTS", "GENERIC_VALIDATION", "LineValidator", "PathValidator", "PolylineValidator", "QuadraticValidator", "businessRules", "sizeRule", "pointCountRule", "polygonClosureRule", "idRule", "minSizeRule", "allRules", "applyBusinessRules", "isSamePoint", "validateSpecific", "applySpecificRules", "MIN_SIZE", "MAX_SIZE", "MIN_POINTS", "MAX_POINTS", "MIN_DIMENSION", "circleProps", "circleRadius", "rectProps", "rectWidth", "rectHeight", "ellipseProps", "ellipseRadiusX", "ellipseRadiusY", "lineProps", "startPt", "endPt", "pointCount", "minPointsForType", "ruleErrors", "POLYGON_NOT_CLOSED", "DUPLICATE_POINTS", "createValidationRule", "IValidationRule", "ValidatorShape", "PointData", "isPolygonClosed", "isValueInRange", "radiusX", "radiusY", "curved", "tension", "controlPoint", "pathElement", "startValid", "endValid", "Validatable", "ValidatableShape", "ValidatorShape", "INSWING", "<PERSON><PERSON><PERSON>", "gaj<PERSON>", "circumcircle", "RRGGBB", "internalremarks", "EllipseValidator", "PolygonValidator", "RectangleValidator", "validate<PERSON><PERSON><PERSON>", "validateCircle", "validatePolygon", "validateTriangle", "validateHexagon", "validateQuadrilateral", "validatePentagon", "validateRectangle", "validateSquare", "radiusX", "radiusY", "cornerRadius", "isRegular", "sidesFromProps", "effectivePointsLength", "isPossiblyClosedViaPoints", "pointStrings", "<PERSON><PERSON><PERSON>", "INVALID_SIDES", "POLYGON_NOT_CLOSED", "DUPLICATE_POINTS", "INVALID_DIMENSION", "INVALID_PROPERTY_VALUE", "INVALID_RADIUS", "INVALID_POSITION", "INSUFFICIENT_POINTS", "INVALID_POINT", "MISSING_PROPERTY", "INVALID_SHAPE_TYPE", "QUADRILATERAL", "PENTAGON", "HEXAGON", "TRIANGLE", "POLYGON", "RECTANGLE", "SQUARE", "ELLIPSE", "CIRCLE", "ImageValidator", "TextValidator", "validateSpecific", "IBaseShapeValidator", "ValidatableShape", "ValidationError", "srcValue", "widthValue", "heightValue", "textValue", "fontSizeValue", "applySpecificRules", "IMAGE", "TEXT", "shapesStore", "ShapesStoreState", "EditorMode", "createActualShapesStore", "shapesStoreSingleton", "useShapesStore", "useShapesStoreEventSync", "useSyncShapesStoreToRepository", "setShapesFromExternal", "addShape", "selectShape", "selectShapes", "deselectShape", "clearSelection", "bringToFrontInLayer", "sendToBackInLayer", "selectedShapeIds", "multiSelect", "clearExisting", "elementId", "shapeId", "onDataUpdated", "onSelectionChanged", "onZOrderChange", "validSelectedIds", "updatedShape", "updatedShapes", "selectedIds", "intraLayerZIndex", "ShapeBringToFrontRequest", "ShapeSendToBackRequest", "ShapeBringToFrontComplete", "ShapeSendToBackComplete", "DataUpdated", "SelectionChanged", "ZUSTAND", "devtools", "subscribeWithSelector", "partialize", "layerStore", "LayerState", "LayerActions", "LayerStoreType", "TaskModule", "ZLevel", "MinorCategory", "MajorCategory", "TaskStatus", "useLayerStore", "layerStateCreator", "initialModules", "initialState", "setModules", "selectLayerIdentifiers", "setCurrentZLevelFocus", "updateStepStatus", "toggleZLevelActive", "deleteZLevel", "addZLevel", "renameZLevel", "resetLayers", "currentModuleId", "currentStepId", "currentZLevelId", "moduleIdString", "stepIdString", "zLevelId", "validatedModuleId", "validatedStepId", "validatedZLevelId", "moduleId", "stepId", "zLevelIdToDelete", "newZLevelId", "newZLevelName", "newZIndex", "newActiveZLevelInStep", "remainingZLevels", "updatedZLevels", "newModules", "newCurrentZLevelId", "next<PERSON><PERSON>er<PERSON><PERSON>", "newLayerNamePattern", "existingNewLayerNums", "getActiveZLevel", "getActiveZLevelZIndex", "activeZLevel", "zLevels", "<PERSON><PERSON><PERSON><PERSON>", "targetMajorCategory", "targetMinorCategory", "PENDING", "BASE", "CEILING", "FURNITURE", "architecture", "coverings", "utilities", "lighting", "storage", "appliances", "beds", "tables", "seating", "decor", "nocheck", "tsnocheck", "unreachable", "useCoordinateSystem", "useCanvasPanZoom", "useCanvasSetup", "useDragAndDrop", "useElementActions", "useKeyboardShortcuts", "useLayerChangeEffect", "usePathDrawHandler", "usePersistentSettings", "useSpecialDrawerAssets", "useUndoRedo", "useUnitConversion", "DraggedElementInfo", "ParsedDragData", "DroppedElementData", "PathDrawState", "PathDrawHandlerConfig", "PathDrawHandler", "ShortcutConfig", "KeyboardShortcutOptions", "SettingsStore", "DesignElement", "FixtureMountingType", "PredefinedElement", "MinorCategory", "MajorCategory", "CoreElementType", "InitialElementProperties", "CanvasMouseEvent", "PointData", "createMouseEventDetails", "handleWheel", "startPan", "continuePan", "endPan", "setManualPan", "setManualZoom", "onDragStart", "onDragOver", "onDragLeave", "onDropCanvas", "endDrag", "createElement", "editElement", "deleteElement", "selectElement", "clearSelection", "moveShape", "updateElements", "startDrawing", "updateDrawing", "completeDrawing", "finishPolyline", "cancelDrawing", "getDrawState", "saveSettings", "getSettingsForType", "resetAllSettings", "getSpecialElements", "specialElements", "isDrawing", "pathType", "currentPoint", "clickCount", "startPointRef", "pointsRef", "lastClickTimeRef", "clickThresholdRef", "draggedElement", "isDraggingOverCanvas", "effectiveSettings", "compileTimeDefaults", "storedSettingsJson", "storedSettings", "mergedSettings", "comprehensiveNewSettings", "newDefaults", "currentModuleId", "currentStepId", "currentMajorCat", "currentMinorCat", "predefinedElements", "specialArchitecturalAndFinishTools", "getIconForPredefinedElement", "predefinedId", "imagePath", "mountingType", "defaultWidth", "defaultDepth", "defaultHeight", "openingType", "doorType", "windowType", "subtypes", "stepId", "onUndo", "onRedo", "onDelete", "onTogglePanMode", "onZoomIn", "onZoomOut", "onResetZoom", "onSelectAll", "onToggleFullscreen", "isConfigArray", "configsOrOptions", "handleKeyDown", "isCtrl", "shift<PERSON>ey", "isInputFocused", "PropertyUpdateService", "ConsoleLoggerService", "ServicesErrorService", "parseNumericValue", "getDefaultValueForNumericProperty", "getDefaultValueForCostProperty", "updateNestedProperty", "updatePositionProperty", "updateNumericProperty", "updateCostProperty", "updateStyleProperty", "createPropertyPatch", "centroidX", "centroidY", "angleStep", "newPoints", "adjustedPoints", "numValue", "final<PERSON>ey", "propertyKey", "propertyPath", "costTotal", "shapeCreationService", "ShapeCreationService", "ShapeCreateEventPayload", "ShapeCreateEvent", "ShapeCreationErrorType", "createShapeInternal", "handleRequest", "positionInstance", "baseParams", "propertiesForValidation", "validationSides", "tempPoints", "tempCenter", "centroidX", "centroidY", "adjustedPoints", "effectiveStartAngle", "angleStep", "inferredSides", "shapeDeleteService", "shapeSelectionService", "shapeEditService", "servicesErrorService", "propertyUpdateService", "ShapeDeleteService", "ShapeSelectionService", "ShapeEditService", "ServicesErrorService", "PropertyUpdateService", "handleRequest", "deleteShape", "deleteShapes", "selectShape", "selectShapes", "clearSelection", "editShape", "handleServiceError", "logServiceWarning", "updateProperty", "updateNestedProperty", "extractChanges", "emitError", "publishError", "startAngleRad", "effectiveStartAngle", "numSides", "angleStep", "tempCenter", "centroidX", "centroidY", "adjustedPoints", "rP", "cD", "radiusX", "radiusY", "isRegular", "startAngle", "counterClockwise", "control1", "control2", "fontFamily", "textAlign", "textBaseline", "fontWeight", "fontStyle", "lineHeight", "cornerRadius", "arrowStart", "arrowEnd", "curved", "tension", "validationSides", "inferredSides", "propertiesForValidation", "validatableData", "specificParams", "createdElementModel", "operationName", "baseContext", "safeExecuteErrorContext", "final<PERSON>ey", "propertyKey", "<PERSON><PERSON><PERSON>", "patchValue", "<PERSON><PERSON><PERSON>", "propValue", "editedShapeData", "updatedShape", "selectedIds", "errorId", "fullMessage", "transformations", "currentShape", "changes", "properties", "mergedProperties", "paramsForFactory", "pathTypes", "validationResult", "tempCenter", "tempPoints", "adjustedPoints", "centroidX", "centroidY", "effectiveStartAngle", "angleStep", "startAngleRad", "mcreference", "servicesErrorService", "consoleLoggerService", "propertyUpdateService", "shapeCreationService", "shapeEditService", "shapeDeleteService", "shapeSelectionService", "storageService", "errorService", "coreError", "servicesErrorService", "errorUtils", "eventHandlers", "appEventBus", "keyboardService", "validationService", "storageService", "loggingService", "shapeActions", "eventCore", "eventRegistry", "eventTypes", "propertyUpdateService", "Segoe", "Neue", "checkmark", "AABB", "AABBs", "prereq"], "ignorePaths": ["node_modules/**", "dist/**", "build/**", "coverage/**", "playwright-report/**", "test-results/**", "*.lock", "*.svg", "package-lock.json", "tsconfig.tsbuildinfo", "**/node_modules/**"], "flagWords": []}