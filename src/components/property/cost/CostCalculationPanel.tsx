/**
 * Cost Calculation Panel Component
 *
 * Provides optional cost calculation functionality for elements:
 * - Enable/disable cost calculation
 * - Configure unit price, quantity factor, and cost basis
 * - Real-time cost calculation and display
 * - Persistent storage of all settings
 *
 * Features:
 * - Element-specific default cost basis
 * - Input validation (positive numbers only)
 * - Consistent UI with GeometrySection layout
 * - Supports all 14 basic element types
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { ElementType } from '@/types/core/elementDefinitions'
import { GeometrySection } from '../shared'

interface CostCalculationPanelProps {
  /** The element to calculate costs for */
  element: ShapeElement
  /** Function to get common property values */
  getCommonValue: (path: string) => unknown
  /** Function to update element properties */
  updateProperty: (path: string, value: unknown) => void
}

// 获取元素类型可用的成本基准选项
function getAvailableCostBasisOptions(elementType: ElementType): Array<{ value: string, label: string }> {
  const closedShapeTypes = [
    ElementType.RECTANGLE,
    ElementType.SQUARE,
    ElementType.CIRCLE,
    ElementType.ELLIPSE,
    ElementType.TRIANGLE,
    ElementType.PENTAGON,
    ElementType.HEXAGON,
  ]

  const pathTypes = [ElementType.LINE, ElementType.POLYLINE, ElementType.ARC, ElementType.QUADRATIC, ElementType.CUBIC]

  if (closedShapeTypes.includes(elementType)) {
    return [
      { value: 'unit', label: 'Per Unit' },
      { value: 'area', label: 'Per Area' },
      { value: 'perimeter', label: 'Per Perimeter' },
    ]
  }
  else if (pathTypes.includes(elementType)) {
    return [
      { value: 'unit', label: 'Per Unit' },
      { value: 'length', label: 'Per Length' },
    ]
  }
  else {
    // 文本和图片只支持按单位计算
    return [
      { value: 'unit', label: 'Per Unit' },
    ]
  }
}

/**
 * CostCalculationPanel component for configuring and displaying cost calculations.
 */
export function CostCalculationPanel({
  element,
  getCommonValue: _getCommonValue,
  updateProperty,
}: CostCalculationPanelProps) {
  // 获取当前设置 - 检查多个可能的位置，默认使用 "unit" 作为 Cost Basis
  const costEnabled = (element.properties?.costEnabled as boolean) ?? (element.costEnabled as boolean) ?? false
  const costUnitPrice = (element.properties?.costUnitPrice as number) ?? (element.costUnitPrice as number) ?? 1.0
  const costQuantityFactor = (element.properties?.costQuantityFactor as number) ?? (element.costQuantityFactor as number) ?? 1
  const costBasis = (element.properties?.costBasis as string) ?? (element.costBasis as string) ?? 'unit'

  // 记录当前元素ID，用于检测元素切换
  const currentElementIdRef = useRef(element.id)

  // 本地显示状态 - 用于立即响应用户输入
  const [displayState, setDisplayState] = useState({
    unitPrice: costUnitPrice,
    quantityFactor: costQuantityFactor,
    costBasis,
  })

  // 只在切换到不同元素时重新初始化显示状态
  useEffect(() => {
    if (currentElementIdRef.current !== element.id) {
      // 元素切换了，重新初始化显示状态
      const timeoutId = setTimeout(() => {
        setDisplayState({
          unitPrice: costUnitPrice,
          quantityFactor: costQuantityFactor,
          costBasis,
        })
        currentElementIdRef.current = element.id
      }, 0)

      return () => clearTimeout(timeoutId)
    }
  }, [element.id, costUnitPrice, costQuantityFactor, costBasis])

  // 获取几何数据用于成本计算
  const geometryData = useMemo(() => {
    const area = (element.properties?.computedArea as number) ?? 0
    const perimeter = (element.properties?.computedPerimeter as number) ?? 0
    const length = (element.properties?.computedLength as number) ?? 0

    return { area, perimeter, length }
  }, [element.properties])

  // 基于显示状态实时计算总成本
  const calculatedTotal = useMemo(() => {
    if (!costEnabled)
      return 0

    let baseValue = 1 // 默认按单位计算

    switch (displayState.costBasis) {
      case 'area':
        baseValue = geometryData.area || 1
        break
      case 'perimeter':
        baseValue = geometryData.perimeter || 1
        break
      case 'length':
        baseValue = geometryData.length || 1
        break
      case 'unit':
      default:
        baseValue = 1
        break
    }

    return displayState.unitPrice * displayState.quantityFactor * baseValue
  }, [costEnabled, displayState, geometryData])

  // 处理启用/禁用切换
  const handleEnabledChange = useCallback((enabled: boolean) => {
    updateProperty('properties.costEnabled', enabled)

    // 如果启用，确保有默认值
    if (enabled) {
      if (element.properties?.costBasis === undefined || element.properties?.costBasis === null) {
        updateProperty('properties.costBasis', 'unit')
      }
      if (element.properties?.costUnitPrice === undefined || element.properties?.costUnitPrice === null) {
        updateProperty('properties.costUnitPrice', 1.0)
      }
      if (element.properties?.costQuantityFactor === undefined || element.properties?.costQuantityFactor === null) {
        updateProperty('properties.costQuantityFactor', 1)
      }
    }
  }, [updateProperty, element.properties])

  // 处理单价变更 - 立即更新显示状态和属性
  const handleUnitPriceChange = useCallback((value: string) => {
    const numValue = value === '' ? 0 : (Number.parseFloat(value) || 0)

    // 立即更新显示状态
    setDisplayState(prev => ({ ...prev, unitPrice: numValue }))

    // 立即更新属性
    updateProperty('properties.costUnitPrice', numValue)
  }, [updateProperty])

  // 处理数量因子变更 - 立即更新显示状态和属性
  const handleQuantityFactorChange = useCallback((value: string) => {
    const numValue = value === '' ? 1 : (Number.parseFloat(value) || 1)

    // 立即更新显示状态
    setDisplayState(prev => ({ ...prev, quantityFactor: numValue }))

    // 立即更新属性
    updateProperty('properties.costQuantityFactor', numValue)
  }, [updateProperty])

  // 处理成本基础变更 - 立即更新显示状态和属性
  const handleCostBasisChange = useCallback((value: string) => {
    // 立即更新显示状态
    setDisplayState(prev => ({ ...prev, costBasis: value }))

    // 立即更新属性
    updateProperty('properties.costBasis', value)
  }, [updateProperty])

  // 移除自动更新总成本到属性，避免循环更新
  // 总成本只用于显示，不存储到属性中

  const availableOptions = getAvailableCostBasisOptions(element.type as ElementType)

  return (
    <GeometrySection type="calculation" title="Material Cost Calculation" columns={1}>

      {/* 启用/禁用开关 */}
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">Enable Cost Calculation</Label>
        <Switch
          checked={costEnabled}
          onCheckedChange={handleEnabledChange}
        />
      </div>

      {/* Cost Calculation Configuration */}
      {costEnabled
        ? (
            <div className="space-y-3 mt-3">
              <div className="text-xs text-muted-foreground">
                Cost calculation is enabled for this element
              </div>

              {/* Configuration Controls */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-3 gap-y-2">
                <div className="space-y-1">
                  <Label className="text-xs font-medium text-muted-foreground">Unit Price ($)</Label>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={displayState.unitPrice.toString()}
                    onChange={e => handleUnitPriceChange(e.target.value)}
                    placeholder="1.00"
                    className="h-8 text-sm"
                  />
                </div>

                <div className="space-y-1">
                  <Label className="text-xs font-medium text-muted-foreground">Quantity Factor</Label>
                  <Input
                    type="number"
                    min="0"
                    step="0.1"
                    value={displayState.quantityFactor.toString()}
                    onChange={e => handleQuantityFactorChange(e.target.value)}
                    placeholder="1.0"
                    className="h-8 text-sm"
                  />
                </div>

                <div className="space-y-1 sm:col-span-2">
                  <Label className="text-xs font-medium text-muted-foreground">Cost Basis</Label>
                  <Select
                    value={displayState.costBasis}
                    onValueChange={handleCostBasisChange}
                  >
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue placeholder="Select cost basis" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Total Cost Display */}
              <div className="p-3 bg-muted/30 border rounded-md">
                <div className="flex items-center justify-between mb-2">
                  <Label className="text-xs font-medium text-muted-foreground">Total Cost</Label>
                  <div className="text-lg font-semibold text-foreground">
                    $
                    {calculatedTotal.toFixed(2)}
                  </div>
                </div>
                <div className="text-xs text-muted-foreground">
                  {displayState.costBasis === 'unit'
                    ? 'Per Unit'
                    : displayState.costBasis === 'area'
                      ? 'Per Area'
                      : displayState.costBasis === 'perimeter'
                        ? 'Per Perimeter'
                        : displayState.costBasis === 'length' ? 'Per Length' : displayState.costBasis}
                  {' '}
                  calculation
                </div>
                <div className="text-xs text-muted-foreground mt-1 font-mono bg-background/50 px-2 py-1 rounded">
                  {displayState.unitPrice}
                  {' '}
                  ×
                  {displayState.quantityFactor}
                  {' '}
                  ×
                  {
                    displayState.costBasis === 'area'
                      ? geometryData.area.toFixed(1)
                      : displayState.costBasis === 'perimeter'
                        ? geometryData.perimeter.toFixed(1)
                        : displayState.costBasis === 'length' ? geometryData.length.toFixed(1) : '1'
                  }
                  {' '}
                  =
                  {calculatedTotal.toFixed(2)}
                </div>
              </div>
            </div>
          )
        : (
            <div className="mt-3 text-xs text-muted-foreground">
              Cost calculation is disabled. Enable the switch above to configure cost settings.
            </div>
          )}
    </GeometrySection>
  )
}
