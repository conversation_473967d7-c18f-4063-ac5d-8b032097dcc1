import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  subscribeToSelectionChangedEvents,
  subscribeToShapeCreateEvents,
  subscribeToShapeDeleteEvents,
  subscribeToShapeDuplicationEvents,
  subscribeToShapeEditEvents,
} from '@/services/event-bus/helpers/subscribers/shape-subscribers'
import * as utils from '@/services/event-bus/helpers/subscribers/utils'
import { AppEventType } from '@/types/services/events'

describe('shape Subscribers', () => {
  let mockEventBus: any
  let mockHandler: any
  let mockOptions: any
  let typedSubscribeSpy: any

  beforeEach(() => {
    mockEventBus = {
      subscribe: vi.fn().mockReturnValue(() => {}),
    }
    mockHandler = vi.fn()
    mockOptions = { once: true }

    // Spy on the typedSubscribe function
    typedSubscribeSpy = vi.spyOn(utils, 'typedSubscribe').mockReturnValue(() => {})

    // Spy on the utility functions
    vi.spyOn(utils, 'getShapeCreateEventType').mockImplementation((phase) => {
      switch (phase) {
        case 'request': return AppEventType.SHAPE_CREATE_REQUEST
        case 'validate': return AppEventType.SHAPE_CREATE_VALIDATE
        case 'complete': return AppEventType.SHAPE_CREATE_COMPLETE
        case 'error': return AppEventType.SHAPE_CREATE_ERROR
        default: throw new Error(`Invalid phase: ${phase}`)
      }
    })

    vi.spyOn(utils, 'getShapeEditEventType').mockImplementation((phase) => {
      switch (phase) {
        case 'request': return AppEventType.SHAPE_EDIT_REQUEST
        case 'compute': return AppEventType.SHAPE_EDIT_COMPUTE
        case 'complete': return AppEventType.SHAPE_EDIT_COMPLETE
        case 'error': return AppEventType.SHAPE_EDIT_ERROR
        default: throw new Error(`Invalid phase: ${phase}`)
      }
    })

    vi.spyOn(utils, 'getShapeDeleteEventType').mockImplementation((phase) => {
      switch (phase) {
        case 'request': return AppEventType.SHAPE_DELETE_REQUEST
        case 'complete': return AppEventType.SHAPE_DELETE_COMPLETE
        case 'error': return AppEventType.SHAPE_DELETE_ERROR
        default: throw new Error(`Invalid phase: ${phase}`)
      }
    })
  })

  describe('subscribeToShapeCreateEvents', () => {
    it('should call getShapeCreateEventType and typedSubscribe with correct parameters', () => {
      const phase = 'request'
      const unsubscribe = subscribeToShapeCreateEvents(
        mockEventBus,
        mockHandler,
        phase,
        mockOptions,
      )

      expect(utils.getShapeCreateEventType).toHaveBeenCalledWith(phase)
      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.SHAPE_CREATE_REQUEST,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should work with different phases', () => {
      const phases: ('request' | 'validate' | 'complete' | 'error')[] = ['validate', 'complete', 'error']

      phases.forEach((phase) => {
        subscribeToShapeCreateEvents(mockEventBus, mockHandler, phase, mockOptions)
        expect(utils.getShapeCreateEventType).toHaveBeenCalledWith(phase)
      })
    })
  })

  describe('subscribeToShapeEditEvents', () => {
    it('should call getShapeEditEventType and typedSubscribe with correct parameters', () => {
      const phase = 'request'
      const unsubscribe = subscribeToShapeEditEvents(
        mockEventBus,
        mockHandler,
        phase,
        mockOptions,
      )

      expect(utils.getShapeEditEventType).toHaveBeenCalledWith(phase)
      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.SHAPE_EDIT_REQUEST,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should work with different phases', () => {
      const phases: ('request' | 'compute' | 'complete' | 'error')[] = ['compute', 'complete', 'error']

      phases.forEach((phase) => {
        subscribeToShapeEditEvents(mockEventBus, mockHandler, phase, mockOptions)
        expect(utils.getShapeEditEventType).toHaveBeenCalledWith(phase)
      })
    })
  })

  describe('subscribeToShapeDeleteEvents', () => {
    it('should call getShapeDeleteEventType and typedSubscribe with correct parameters', () => {
      const phase = 'request'
      const unsubscribe = subscribeToShapeDeleteEvents(
        mockEventBus,
        mockHandler,
        phase,
        mockOptions,
      )

      expect(utils.getShapeDeleteEventType).toHaveBeenCalledWith(phase)
      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.SHAPE_DELETE_REQUEST,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should work with different phases', () => {
      const phases: ('request' | 'complete' | 'error')[] = ['complete', 'error']

      phases.forEach((phase) => {
        subscribeToShapeDeleteEvents(mockEventBus, mockHandler, phase, mockOptions)
        expect(utils.getShapeDeleteEventType).toHaveBeenCalledWith(phase)
      })
    })
  })

  describe('subscribeToSelectionChangedEvents', () => {
    it('should call typedSubscribe with correct parameters', () => {
      const unsubscribe = subscribeToSelectionChangedEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.SELECTION_CHANGED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('subscribeToShapeDuplicationEvents', () => {
    it('should call typedSubscribe with correct parameters for request phase', () => {
      const unsubscribe = subscribeToShapeDuplicationEvents(
        mockEventBus,
        mockHandler,
        'request',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.SHAPE_DUPLICATE_REQUEST,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for complete phase', () => {
      const unsubscribe = subscribeToShapeDuplicationEvents(
        mockEventBus,
        mockHandler,
        'complete',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.SHAPE_DUPLICATE_COMPLETE,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })
})
