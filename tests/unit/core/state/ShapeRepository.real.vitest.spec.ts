import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import type { Mocked } from 'vitest'
import { ShapeRepository } from '@/core/state/ShapeRepository'
import type { LoggerService } from '@/types/services/logging'
import type { EventBus } from '@/types/services/events'
import type { AppEventMap } from '@/types/services/events/eventRegistry'
import type { ShapeElement } from '@/types/core/elementDefinitions'
import { ElementType } from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'

describe('ShapeRepository (Real Implementation)', () => {
  let repository: ShapeRepository
  let mockLogger: Mocked<LoggerService>
  let mockEventBus: Mocked<EventBus<AppEventMap>>
  let testShape1: ShapeElement
  let testShape2: ShapeElement
  let testShape3: ShapeElement

  beforeEach(() => {
    // Mock Logger
    mockLogger = {
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      setContext: vi.fn(),
      getConfig: vi.fn(),
      setConfig: vi.fn(),
    } as unknown as Mocked<LoggerService>

    // Mock EventBus
    mockEventBus = {
      publish: vi.fn(),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
      on: vi.fn(),
      off: vi.fn(),
      configure: vi.fn(),
      getSubscriptions: vi.fn().mockReturnValue(new Map()),
      reset: vi.fn(),
      createEvent: vi.fn(),
    } as unknown as Mocked<EventBus<AppEventMap>>

    // Test shapes
    testShape1 = {
      id: 'shape-1',
      type: ElementType.RECTANGLE,
      position: { x: 0, y: 0 },
      properties: { width: 100, height: 50 },
      majorCategory: MajorCategory.BASE,
      minorCategory: 'architecture',
    }

    testShape2 = {
      id: 'shape-2',
      type: ElementType.ELLIPSE,
      position: { x: 50, y: 50 },
      properties: { radiusX: 30, radiusY: 20 },
      majorCategory: MajorCategory.FURNITURE,
      minorCategory: 'seating',
    }

    testShape3 = {
      id: 'shape-3',
      type: ElementType.LINE,
      position: { x: 0, y: 0 },
      properties: { endX: 100, endY: 100 },
      majorCategory: MajorCategory.BASE,
      minorCategory: 'architecture',
    }

    repository = new ShapeRepository(mockLogger, mockEventBus)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Constructor', () => {
    it('should create ShapeRepository instance', () => {
      expect(repository).toBeDefined()
      expect(repository).toBeInstanceOf(ShapeRepository)
    })

    it('should initialize with empty collections', () => {
      expect(repository.getAll()).toEqual([])
      expect(repository.getSelectedIds()).toEqual([])
    })

    it('should initialize with provided dependencies', () => {
      expect(repository.logger).toBe(mockLogger)
      expect(repository.eventBus).toBe(mockEventBus)
    })
  })

  describe('Shape Addition', () => {
    it('should add a shape successfully', () => {
      repository.add(testShape1)

      expect(repository.getById(testShape1.id)).toBe(testShape1)
      expect(repository.getAll()).toContain(testShape1)
    })

    it('should publish update event when shape is added', () => {
      repository.add(testShape1)

      expect(mockEventBus.publish).toHaveBeenCalled()
    })

    it('should add multiple shapes', () => {
      repository.add(testShape1)
      repository.add(testShape2)
      repository.add(testShape3)

      expect(repository.getAll()).toHaveLength(3)
      expect(repository.getAll()).toContain(testShape1)
      expect(repository.getAll()).toContain(testShape2)
      expect(repository.getAll()).toContain(testShape3)
    })

    it('should overwrite existing shape with same ID', () => {
      const updatedShape = {
        ...testShape1,
        properties: { width: 200, height: 100 },
      }

      repository.add(testShape1)
      repository.add(updatedShape)

      expect(repository.getAll()).toHaveLength(1)
      expect(repository.getById(testShape1.id)).toBe(updatedShape)
    })

    it('should handle invalid shape gracefully', () => {
      const invalidShape = null as any
      repository.add(invalidShape)

      expect(repository.getAll()).toHaveLength(0)
      expect(mockLogger.error).toHaveBeenCalledWith(
        '[ShapeRepository] Attempted to add invalid shape:',
        invalidShape
      )
    })

    it('should handle shape without ID', () => {
      const shapeWithoutId = {
        ...testShape1,
        id: '',
      }

      repository.add(shapeWithoutId)

      expect(repository.getAll()).toHaveLength(0)
      expect(mockLogger.error).toHaveBeenCalled()
    })
  })

  describe('Shape Retrieval', () => {
    beforeEach(() => {
      repository.add(testShape1)
      repository.add(testShape2)
      repository.add(testShape3)
    })

    it('should get shape by ID', () => {
      const shape = repository.getById(testShape1.id)

      expect(shape).toBe(testShape1)
    })

    it('should return undefined for non-existent ID', () => {
      const shape = repository.getById('non-existent')

      expect(shape).toBeUndefined()
    })

    it('should get all shapes', () => {
      const allShapes = repository.getAll()

      expect(allShapes).toHaveLength(3)
      expect(allShapes).toContain(testShape1)
      expect(allShapes).toContain(testShape2)
      expect(allShapes).toContain(testShape3)
    })

    it('should return empty array when no shapes exist', () => {
      const emptyRepo = new ShapeRepository(mockLogger, mockEventBus)
      expect(emptyRepo.getAll()).toEqual([])
    })
  })

  describe('Shape Updates', () => {
    beforeEach(() => {
      repository.add(testShape1)
      repository.add(testShape2)
    })

    it('should update shape successfully', () => {
      const updates = {
        properties: { width: 200, height: 100 },
        position: { x: 10, y: 20 },
      }

      const result = repository.update(testShape1.id, updates)

      expect(result).toBe(true)
      const updatedShape = repository.getById(testShape1.id)
      expect(updatedShape?.properties).toEqual(updates.properties)
      expect(updatedShape?.position).toEqual(updates.position)
    })

    it('should publish update event when shape is updated', () => {
      const updates = { properties: { width: 200 } }

      repository.update(testShape1.id, updates)

      expect(mockEventBus.publish).toHaveBeenCalled()
    })

    it('should handle partial updates', () => {
      const updates = {
        properties: { width: 150 },
      }

      repository.update(testShape1.id, updates)

      const updatedShape = repository.getById(testShape1.id)
      expect(updatedShape?.properties.width).toBe(150)
      expect(updatedShape?.properties.height).toBe(50) // Should remain unchanged
    })

    it('should return false for non-existent shape', () => {
      const result = repository.update('non-existent', { properties: {} })

      expect(result).toBe(false)
    })

    it('should handle empty updates', () => {
      const result = repository.update(testShape1.id, {})

      expect(result).toBe(true)
      expect(mockEventBus.publish).toHaveBeenCalled()
    })
  })

  describe('Shape Removal', () => {
    beforeEach(() => {
      repository.add(testShape1)
      repository.add(testShape2)
      repository.add(testShape3)
    })

    it('should remove shape successfully', () => {
      const result = repository.remove(testShape1.id)

      expect(result).toBe(true)
      expect(repository.getById(testShape1.id)).toBeUndefined()
      expect(repository.getAll()).toHaveLength(2)
    })

    it('should publish update event when shape is removed', () => {
      repository.remove(testShape1.id)

      expect(mockEventBus.publish).toHaveBeenCalled()
    })

    it('should remove shape from selection when deleted', () => {
      repository.addToSelection(testShape1.id)
      expect(repository.getSelectedIds()).toContain(testShape1.id)

      repository.remove(testShape1.id)

      expect(repository.getSelectedIds()).not.toContain(testShape1.id)
    })

    it('should return false for non-existent shape', () => {
      const result = repository.remove('non-existent')

      expect(result).toBe(false)
      expect(repository.getAll()).toHaveLength(3)
    })

    it('should clear all shapes', () => {
      repository.clear()

      expect(repository.getAll()).toHaveLength(0)
      expect(repository.getSelectedIds()).toHaveLength(0)
      expect(mockEventBus.publish).toHaveBeenCalled()
    })
  })

  describe('Selection Management', () => {
    beforeEach(() => {
      repository.add(testShape1)
      repository.add(testShape2)
      repository.add(testShape3)
    })

    it('should add shape to selection', () => {
      repository.addToSelection(testShape1.id)

      expect(repository.getSelectedIds()).toContain(testShape1.id)
    })

    it('should remove shape from selection', () => {
      repository.addToSelection(testShape1.id)
      repository.addToSelection(testShape2.id)

      repository.removeFromSelection(testShape1.id)

      expect(repository.getSelectedIds()).toHaveLength(1)
      expect(repository.getSelectedIds()).toContain(testShape2.id)
      expect(repository.getSelectedIds()).not.toContain(testShape1.id)
    })

    it('should clear all selections', () => {
      repository.addToSelection(testShape1.id)
      repository.addToSelection(testShape2.id)

      repository.clearSelection()

      expect(repository.getSelectedIds()).toHaveLength(0)
    })

    it('should set selected IDs', () => {
      const selectedIds = [testShape1.id, testShape2.id]
      repository.setSelectedIds(selectedIds)

      expect(repository.getSelectedIds()).toEqual(selectedIds)
    })

    it('should handle selection of non-existent shape', () => {
      repository.addToSelection('non-existent')

      expect(repository.getSelectedIds()).toContain('non-existent')
    })

    it('should not duplicate selections', () => {
      repository.addToSelection(testShape1.id)
      repository.addToSelection(testShape1.id) // Add again

      expect(repository.getSelectedIds()).toHaveLength(1)
      expect(repository.getSelectedIds()).toContain(testShape1.id)
    })
  })

  describe('External Data Synchronization', () => {
    it('should set shapes from external source', () => {
      const externalShapes = [testShape1, testShape2]
      const selectedIds = [testShape1.id]

      repository.setShapesFromExternal(externalShapes, selectedIds)

      expect(repository.getAll()).toEqual(externalShapes)
      expect(repository.getSelectedIds()).toEqual(selectedIds)
    })

    it('should handle empty external data', () => {
      repository.add(testShape1) // Add some data first

      repository.setShapesFromExternal([], [])

      expect(repository.getAll()).toEqual([])
      expect(repository.getSelectedIds()).toEqual([])
    })
  })

  describe('Event Publishing', () => {
    it('should publish events on shape addition', () => {
      repository.add(testShape1)

      expect(mockEventBus.publish).toHaveBeenCalled()
    })

    it('should publish events on shape update', () => {
      repository.add(testShape1)
      vi.clearAllMocks()

      repository.update(testShape1.id, { properties: { width: 200 } })

      expect(mockEventBus.publish).toHaveBeenCalled()
    })

    it('should publish events on shape removal', () => {
      repository.add(testShape1)
      vi.clearAllMocks()

      repository.remove(testShape1.id)

      expect(mockEventBus.publish).toHaveBeenCalled()
    })

    it('should publish events on clear', () => {
      repository.add(testShape1)
      repository.add(testShape2)
      vi.clearAllMocks()

      repository.clear()

      expect(mockEventBus.publish).toHaveBeenCalled()
    })
  })
})
