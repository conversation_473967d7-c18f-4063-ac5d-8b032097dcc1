import { beforeEach, describe, expect, it } from 'vitest'
import { EllipseBoundingBoxStrategy } from '@/core/compute/strategies/bounding-box/EllipseBoundingBoxStrategy'
import { Point } from '@/types/core/element/geometry/point'
import { Ellipse } from '@/types/core/element/shape/ellipseShapeTypes'

// Mock Ellipse class
class MockEllipse {
  private radiusX: number
  private radiusY: number
  private position: { x: number, y: number }
  id: string = 'mock-ellipse-id'

  constructor(radiusX: number, radiusY: number, position: { x: number, y: number } = { x: 100, y: 100 }) {
    this.radiusX = radiusX
    this.radiusY = radiusY
    this.position = position
  }

  getSubType() {
    return 'ellipse'
  }

  getRadiusX() {
    return this.radiusX
  }

  getRadiusY() {
    return this.radiusY
  }

  getPosition() {
    return new Point(this.position.x, this.position.y)
  }
}

// Make MockEllipse instances pass the instanceof Ellipse check
Object.setPrototypeOf(MockEllipse.prototype, Ellipse.prototype)

// Mock non-ellipse element
class MockRectangle {
  id: string = 'mock-rectangle'

  getSubType() {
    return 'rectangle'
  }
}

describe('ellipseBoundingBoxStrategy', () => {
  let strategy: EllipseBoundingBoxStrategy

  beforeEach(() => {
    strategy = new EllipseBoundingBoxStrategy()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('ellipse')
  })

  it('should calculate bounding box correctly', () => {
    const radiusX = 50
    const radiusY = 30
    const position = { x: 100, y: 100 }
    const ellipse = new MockEllipse(radiusX, radiusY, position)

    const boundingBox = strategy.calculateBoundingBox(ellipse as any)

    // BBox should be (x - rx, y - ry, 2rx, 2ry)
    expect(boundingBox).toEqual({
      x: 50,
      y: 70,
      width: 100,
      height: 60,
    })
  })

  it('should handle zero radii', () => {
    const ellipse = new MockEllipse(0, 0, { x: 100, y: 100 })
    const boundingBox = strategy.calculateBoundingBox(ellipse as any)

    expect(boundingBox).toEqual({
      x: 100,
      y: 100,
      width: 0,
      height: 0,
    })
  })

  it('should handle negative radii', () => {
    const ellipse = new MockEllipse(-50, -30, { x: 100, y: 100 })
    const boundingBox = strategy.calculateBoundingBox(ellipse as any)

    // Should use absolute value of radii
    expect(boundingBox).toEqual({
      x: 50,
      y: 70,
      width: 100,
      height: 60,
    })
  })

  it('should handle non-ellipse elements', () => {
    const rectangle = new MockRectangle()

    const boundingBox = strategy.calculateBoundingBox(rectangle as any)
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle real Ellipse instance', () => {
    // Create a mock that looks like a real Ellipse instance
    const realEllipse = {
      id: 'real-ellipse-id',
      constructor: { name: 'Ellipse' },
      getPosition: () => new Point(100, 100),
      getRadiusX: () => 50,
      getRadiusY: () => 30,
      getSubType: () => 'ellipse',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realEllipse, Ellipse.prototype)

    const boundingBox = strategy.calculateBoundingBox(realEllipse as any)
    expect(boundingBox).toEqual({
      x: 50,
      y: 70,
      width: 100,
      height: 60,
    })
  })

  it('should handle invalid position in real Ellipse instance', () => {
    // Create a mock that looks like a real Ellipse instance with invalid position
    const realEllipse = {
      id: 'real-ellipse-id',
      constructor: { name: 'Ellipse' },
      getPosition: () => null as any,
      getRadiusX: () => 50,
      getRadiusY: () => 30,
      getSubType: () => 'ellipse',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realEllipse, Ellipse.prototype)

    const boundingBox = strategy.calculateBoundingBox(realEllipse as any)
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle invalid radiusX in real Ellipse instance', () => {
    // Create a mock that looks like a real Ellipse instance with invalid radiusX
    const realEllipse = {
      id: 'real-ellipse-id',
      constructor: { name: 'Ellipse' },
      getPosition: () => new Point(100, 100),
      getRadiusX: () => Number.NaN,
      getRadiusY: () => 30,
      getSubType: () => 'ellipse',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realEllipse, Ellipse.prototype)

    const boundingBox = strategy.calculateBoundingBox(realEllipse as any)
    expect(boundingBox).toEqual({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    })
  })

  it('should handle error when accessing methods on Ellipse', () => {
    // Create a mock that looks like a real Ellipse instance but throws when getRadiusX is called
    const realEllipse = {
      id: 'real-ellipse-id',
      constructor: { name: 'Ellipse' },
      getPosition: () => new Point(100, 100),
      getRadiusX: () => { throw new Error('Test error') },
      getRadiusY: () => 30,
      getSubType: () => 'ellipse',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realEllipse, Ellipse.prototype)

    // This should not throw but return a default bounding box
    expect(() => {
      const boundingBox = strategy.calculateBoundingBox(realEllipse as any)
      expect(boundingBox).toEqual({
        x: 0,
        y: 0,
        width: 0,
        height: 0,
      })
    }).not.toThrow()
  })
})
