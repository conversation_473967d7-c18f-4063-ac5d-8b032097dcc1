import { beforeEach, describe, expect, it } from 'vitest'
import { PolygonPerimeterStrategy } from '@/core/compute/strategies/perimeter/PolygonPerimeterStrategy'
import type { PointData } from '@/types/core/element/geometry/point'
import { Polygon } from '@/types/core/element/shape/shape'
import { ElementType } from '@/types/core/elementDefinitions'

// Mock Polygon class
class MockPolygon {
  id = 'mock-polygon'
  type = ElementType.POLYGON
  position = { x: 0, y: 0 }
  properties = { points: [] as PointData[] }
  points: Array<{ x: number, y: number }>
  private vertices: Array<{ x: number, y: number }>

  constructor(vertices: Array<{ x: number, y: number }>) {
    this.vertices = vertices
    this.points = vertices
    this.properties.points = vertices
  }

  getSubType() {
    return 'polygon'
  }

  getPoints() {
    return this.vertices
  }
}

// Mock non-polygon element
class MockCircle {
  id = 'mock-circle'
  type = 'CIRCLE'

  getSubType() {
    return 'circle'
  }
}

describe('polygonPerimeterStrategy', () => {
  let strategy: PolygonPerimeterStrategy

  beforeEach(() => {
    strategy = new PolygonPerimeterStrategy()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('POLYGON')
  })

  it('should calculate perimeter of a square correctly', () => {
    // Create a square with vertices at (0,0), (0,5), (5,5), and (5,0)
    // Perimeter should be 4 * 5 = 20
    const vertices = [
      { x: 0, y: 0 },
      { x: 0, y: 5 },
      { x: 5, y: 5 },
      { x: 5, y: 0 },
    ]

    const polygon = new MockPolygon(vertices)
    const perimeter = strategy.calculatePerimeter(polygon as any)

    expect(perimeter).toBe(20)
  })

  it('should calculate perimeter of a triangle correctly', () => {
    // Create a 3-4-5 right triangle
    const vertices = [
      { x: 0, y: 0 },
      { x: 3, y: 0 },
      { x: 0, y: 4 },
    ]

    const polygon = new MockPolygon(vertices)
    const perimeter = strategy.calculatePerimeter(polygon as any)

    // Perimeter should be 3 + 4 + 5 = 12
    expect(perimeter).toBe(12)
  })

  it('should return 0 for polygon with fewer than 2 vertices', () => {
    const vertices = [{ x: 0, y: 0 }]

    const polygon = new MockPolygon(vertices)
    const perimeter = strategy.calculatePerimeter(polygon as any)

    expect(perimeter).toBe(0)
  })

  it('should throw error if element is not a polygon', () => {
    const circle = new MockCircle()

    expect(() => strategy.calculatePerimeter(circle as any)).toThrow('PolygonPerimeterStrategy')
  })

  it('should handle invalid vertices', () => {
    const vertices = [
      { x: 0, y: 0 },
      { x: 0, y: 5 },
      null,
      { x: 5, y: 0 },
    ]

    const polygon = new MockPolygon(vertices as any)

    expect(() => strategy.calculatePerimeter(polygon as any)).toThrow('Invalid point data in polygon')
  })

  it('should handle real Polygon instance', () => {
    // Create a mock that looks like a real Polygon instance
    const points = [
      { x: 0, y: 0 } as PointData,
      { x: 0, y: 5 } as PointData,
      { x: 5, y: 5 } as PointData,
      { x: 5, y: 0 } as PointData,
    ]
    const realPolygon = {
      id: 'real-polygon-id',
      type: ElementType.POLYGON,
      position: { x: 0, y: 0 },
      points: points,
      properties: {
        points: points
      },
      constructor: { name: 'Polygon' },
      getPoints: () => points,
      getSubType: () => 'polygon',
    }

    // Make instanceof check pass
    if (Polygon?.prototype) {
      Object.setPrototypeOf(realPolygon, Polygon.prototype)
    }

    const perimeter = strategy.calculatePerimeter(realPolygon as any)
    expect(perimeter).toBe(20)
  })

  it('should handle null points array in real Polygon instance', () => {
    // Create a mock that looks like a real Polygon instance with null points array
    const realPolygon = {
      id: 'real-polygon-id',
      type: ElementType.POLYGON,
      position: { x: 0, y: 0 },
      points: null,
      properties: { points: null },
      constructor: { name: 'Polygon' },
      getPoints: () => null,
      getSubType: () => 'polygon',
    }

    // Make instanceof check pass
    if (Polygon?.prototype) {
      Object.setPrototypeOf(realPolygon, Polygon.prototype)
    }

    expect(() => strategy.calculatePerimeter(realPolygon as any)).toThrow('has invalid points property')
  })

  it('should handle error when accessing methods on Polygon', () => {
    // Create a mock that looks like a real Polygon instance but throws when getPoints is called
    const realPolygon = {
      id: 'real-polygon-id',
      type: ElementType.POLYGON,
      position: { x: 0, y: 0 },
      get points() { throw new Error('Test error') },
      properties: { points: [] },
      constructor: { name: 'Polygon' },
      getPoints: () => { throw new Error('Test error') },
      getSubType: () => 'polygon',
    }

    // Make instanceof check pass
    if (Polygon?.prototype) {
      Object.setPrototypeOf(realPolygon, Polygon.prototype)
    }

    // Wrap in try-catch to handle the error
    try {
      const perimeter = strategy.calculatePerimeter(realPolygon as any)
      expect(isNaN(perimeter)).toBe(true)
    }
    catch (error) {
      // If it throws, that's also acceptable
      expect(error).toBeDefined()
    }
  })
})
