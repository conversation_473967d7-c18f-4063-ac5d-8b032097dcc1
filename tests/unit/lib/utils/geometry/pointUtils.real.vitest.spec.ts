import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  calculateAngle,
  arePointsEqual,
  calculateDistance,
  calculateMidpoint,
  calculatePointToPointDistance,
  calculatePointToCircleDistance,
  calculatePointToEllipseDistance,
  rotatePoint,
  scalePoint,
  ensurePointInstance,
} from '@/lib/utils/geometry/pointUtils'
import { PointClass } from '@/lib/utils/geometry/PointClass'
import type { IPoint } from '@/types/core/element/geometry/point'

describe('Point Utils (Real Implementation)', () => {
  let testPoint1: IPoint
  let testPoint2: IPoint
  let testPoint3: IPoint

  beforeEach(() => {
    testPoint1 = { x: 0, y: 0 }
    testPoint2 = { x: 3, y: 4 }
    testPoint3 = { x: -2, y: 5, z: 1 }
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Angle Calculations', () => {
    it('should calculate angle between two points', () => {
      const angle = calculateAngle(testPoint1, testPoint2)
      expect(angle).toBeCloseTo(Math.atan2(4, 3), 5)
    })

    it('should handle same points', () => {
      const angle = calculateAngle(testPoint1, testPoint1)
      expect(angle).toBe(0)
    })

    it('should handle negative coordinates', () => {
      const angle = calculateAngle(testPoint1, { x: -3, y: -4 })
      expect(angle).toBeCloseTo(Math.atan2(-4, -3), 5)
    })

    it('should handle invalid points', () => {
      const angle = calculateAngle(null as any, testPoint2)
      expect(angle).toBeNaN()
    })
  })

  describe('Point Equality', () => {
    it('should detect equal points with default tolerance', () => {
      const point1 = { x: 1.0, y: 2.0 }
      const point2 = { x: 1.0001, y: 2.0001 }
      expect(arePointsEqual(point1, point2)).toBe(true)
    })

    it('should detect unequal points', () => {
      const point1 = { x: 1.0, y: 2.0 }
      const point2 = { x: 1.1, y: 2.1 }
      expect(arePointsEqual(point1, point2)).toBe(false)
    })

    it('should use custom tolerance', () => {
      const point1 = { x: 1.0, y: 2.0 }
      const point2 = { x: 1.1, y: 2.1 }
      expect(arePointsEqual(point1, point2, 0.2)).toBe(true)
    })

    it('should handle 3D points', () => {
      const point1 = { x: 1.0, y: 2.0, z: 3.0 }
      const point2 = { x: 1.0001, y: 2.0001, z: 3.0001 }
      expect(arePointsEqual(point1, point2)).toBe(true)
    })

    it('should handle invalid points', () => {
      expect(arePointsEqual(null as any, testPoint1)).toBe(false)
      expect(arePointsEqual(testPoint1, null as any)).toBe(false)
    })
  })

  describe('Distance Calculations', () => {
    it('should calculate 2D distance between points', () => {
      const distance = calculateDistance(testPoint1, testPoint2)
      expect(distance).toBe(5) // 3-4-5 triangle
    })

    it('should calculate 3D distance between points', () => {
      const point1 = { x: 0, y: 0, z: 0 }
      const point2 = { x: 1, y: 1, z: 1 }
      const distance = calculateDistance(point1, point2)
      expect(distance).toBeCloseTo(Math.sqrt(3), 5)
    })

    it('should handle same points', () => {
      const distance = calculateDistance(testPoint1, testPoint1)
      expect(distance).toBe(0)
    })

    it('should handle invalid points', () => {
      const distance = calculateDistance(null as any, testPoint2)
      expect(distance).toBeNaN()
    })

    it('should be symmetric', () => {
      const d1 = calculateDistance(testPoint1, testPoint2)
      const d2 = calculateDistance(testPoint2, testPoint1)
      expect(d1).toBe(d2)
    })
  })

  describe('Point to Point Distance', () => {
    it('should calculate distance between points', () => {
      const distance = calculatePointToPointDistance(testPoint1, testPoint2)
      expect(distance).toBe(5)
    })

    it('should handle negative coordinates', () => {
      const distance = calculatePointToPointDistance(testPoint1, testPoint3)
      expect(distance).toBeCloseTo(Math.sqrt(29), 5) // sqrt(4 + 25)
    })
  })

  describe('Midpoint Calculations', () => {
    it('should calculate midpoint between two points', () => {
      const midpoint = calculateMidpoint(testPoint1, testPoint2)
      expect(midpoint.x).toBe(1.5)
      expect(midpoint.y).toBe(2)
    })

    it('should handle 3D points', () => {
      const point1 = { x: 0, y: 0, z: 0 }
      const point2 = { x: 2, y: 4, z: 6 }
      const midpoint = calculateMidpoint(point1, point2)
      expect(midpoint.x).toBe(1)
      expect(midpoint.y).toBe(2)
      expect(midpoint.z).toBe(3)
    })

    it('should handle negative coordinates', () => {
      const midpoint = calculateMidpoint(testPoint1, testPoint3)
      expect(midpoint.x).toBe(-1)
      expect(midpoint.y).toBe(2.5)
    })

    it('should handle invalid points', () => {
      const midpoint = calculateMidpoint(null as any, testPoint2)
      expect(midpoint.x).toBeNaN()
      expect(midpoint.y).toBeNaN()
    })
  })

  describe('Point to Circle Distance', () => {
    it('should calculate distance from point to circle', () => {
      const center = { x: 0, y: 0 }
      const radius = 3
      const point = { x: 5, y: 0 }
      const distance = calculatePointToCircleDistance(point, center, radius)
      expect(distance).toBe(2) // 5 - 3
    })

    it('should handle point inside circle', () => {
      const center = { x: 0, y: 0 }
      const radius = 5
      const point = { x: 3, y: 0 }
      const distance = calculatePointToCircleDistance(point, center, radius)
      expect(distance).toBe(-2) // 3 - 5
    })

    it('should handle point on circle', () => {
      const center = { x: 0, y: 0 }
      const radius = 5
      const point = { x: 5, y: 0 }
      const distance = calculatePointToCircleDistance(point, center, radius)
      expect(distance).toBeCloseTo(0, 5)
    })

    it('should throw error for invalid inputs', () => {
      expect(() => calculatePointToCircleDistance(null as any, testPoint1, 5)).toThrow()
      expect(() => calculatePointToCircleDistance(testPoint1, null as any, 5)).toThrow()
      expect(() => calculatePointToCircleDistance(testPoint1, testPoint2, -1)).toThrow()
    })
  })

  describe('Point to Ellipse Distance', () => {
    it('should calculate distance from point to ellipse', () => {
      const center = { x: 0, y: 0 }
      const radiusX = 3
      const radiusY = 2
      const point = { x: 3, y: 0 }
      const distance = calculatePointToEllipseDistance(point, center, radiusX, radiusY)
      expect(distance).toBeCloseTo(0, 5) // Point on ellipse
    })

    it('should handle point inside ellipse', () => {
      const center = { x: 0, y: 0 }
      const radiusX = 3
      const radiusY = 2
      const point = { x: 1, y: 1 }
      const distance = calculatePointToEllipseDistance(point, center, radiusX, radiusY)
      expect(distance).toBeLessThan(0) // Inside ellipse
    })

    it('should handle point outside ellipse', () => {
      const center = { x: 0, y: 0 }
      const radiusX = 3
      const radiusY = 2
      const point = { x: 5, y: 3 }
      const distance = calculatePointToEllipseDistance(point, center, radiusX, radiusY)
      expect(distance).toBeGreaterThan(0) // Outside ellipse
    })

    it('should throw error for invalid inputs', () => {
      expect(() => calculatePointToEllipseDistance(null as any, testPoint1, 3, 2)).toThrow()
      expect(() => calculatePointToEllipseDistance(testPoint1, null as any, 3, 2)).toThrow()
      expect(() => calculatePointToEllipseDistance(testPoint1, testPoint2, 0, 2)).toThrow()
      expect(() => calculatePointToEllipseDistance(testPoint1, testPoint2, 3, 0)).toThrow()
    })
  })

  describe('Point Transformations', () => {
    it('should rotate point around center', () => {
      const point = { x: 1, y: 0 }
      const center = { x: 0, y: 0 }
      const angle = Math.PI / 2 // 90 degrees
      const rotated = rotatePoint(point, center, angle)
      expect(rotated.x).toBeCloseTo(0, 5)
      expect(rotated.y).toBeCloseTo(1, 5)
    })

    it('should rotate point around custom center', () => {
      const point = { x: 2, y: 1 }
      const center = { x: 1, y: 1 }
      const angle = Math.PI / 2 // 90 degrees
      const rotated = rotatePoint(point, center, angle)
      expect(rotated.x).toBeCloseTo(1, 5)
      expect(rotated.y).toBeCloseTo(2, 5)
    })

    it('should scale point from center', () => {
      const point = { x: 2, y: 3 }
      const center = { x: 0, y: 0 }
      const scale = 2
      const scaled = scalePoint(point, center, scale)
      expect(scaled.x).toBe(4)
      expect(scaled.y).toBe(6)
    })

    it('should scale point from custom center', () => {
      const point = { x: 3, y: 4 }
      const center = { x: 1, y: 1 }
      const scale = 2
      const scaled = scalePoint(point, center, scale)
      expect(scaled.x).toBe(5) // 1 + (3-1)*2
      expect(scaled.y).toBe(7) // 1 + (4-1)*2
    })

    it('should handle invalid transformations', () => {
      const rotated = rotatePoint(null as any, testPoint1, Math.PI)
      expect(rotated.x).toBeNaN()
      expect(rotated.y).toBeNaN()

      const scaled = scalePoint(null as any, testPoint1, 2)
      expect(scaled.x).toBeNaN()
      expect(scaled.y).toBeNaN()
    })
  })

  describe('Point Instance Utilities', () => {
    it('should ensure point instance from plain object', () => {
      const plainPoint = { x: 1, y: 2 }
      const pointInstance = ensurePointInstance(plainPoint)
      expect(pointInstance).toBeInstanceOf(PointClass)
      expect(pointInstance.x).toBe(1)
      expect(pointInstance.y).toBe(2)
    })

    it('should return existing PointClass instance', () => {
      const pointInstance = new PointClass(1, 2)
      const result = ensurePointInstance(pointInstance)
      expect(result).toBe(pointInstance)
    })

    it('should handle 3D points', () => {
      const plainPoint = { x: 1, y: 2, z: 3 }
      const pointInstance = ensurePointInstance(plainPoint)
      expect(pointInstance).toBeInstanceOf(PointClass)
      expect(pointInstance.x).toBe(1)
      expect(pointInstance.y).toBe(2)
      expect(pointInstance.z).toBe(3)
    })

    it('should handle invalid points', () => {
      const result = ensurePointInstance(null as any)
      expect(result).toBeInstanceOf(PointClass)
      expect(result.x).toBeNaN()
      expect(result.y).toBeNaN()
    })
  })

  describe('Edge Cases and Error Handling', () => {
    it('should handle NaN coordinates', () => {
      const nanPoint = { x: NaN, y: NaN }
      const distance = calculateDistance(nanPoint, testPoint1)
      expect(distance).toBeNaN()

      const midpoint = calculateMidpoint(nanPoint, testPoint1)
      expect(midpoint.x).toBeNaN()
      expect(midpoint.y).toBeNaN()
    })

    it('should handle Infinity coordinates', () => {
      const infPoint = { x: Infinity, y: Infinity }
      const distance = calculateDistance(infPoint, testPoint1)
      expect(distance).toBe(Infinity)
    })

    it('should handle very large coordinates', () => {
      const largePoint = { x: Number.MAX_SAFE_INTEGER, y: Number.MAX_SAFE_INTEGER }
      const distance = calculateDistance(testPoint1, largePoint)
      expect(distance).toBeGreaterThan(0)
      expect(Number.isFinite(distance)).toBe(true)
    })

    it('should handle very small coordinates', () => {
      const smallPoint = { x: Number.MIN_VALUE, y: Number.MIN_VALUE }
      const distance = calculateDistance(testPoint1, smallPoint)
      expect(distance).toBeGreaterThanOrEqual(0)
    })
  })

  describe('Performance', () => {
    it('should handle many calculations efficiently', () => {
      const startTime = Date.now()
      
      for (let i = 0; i < 10000; i++) {
        const p1 = { x: i, y: i }
        const p2 = { x: i + 1, y: i + 1 }
        calculateDistance(p1, p2)
        calculateMidpoint(p1, p2)
        rotatePoint(p1, testPoint1, i / 1000)
        scalePoint(p1, testPoint1, 1.1)
      }
      
      const endTime = Date.now()
      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly
    })
  })
})
