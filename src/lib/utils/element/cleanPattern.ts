// cleanPattern.ts
// Utility to recursively remove nested 'pattern' fields from an object (except the top-level)

/**
 * Recursively removes any nested 'pattern' fields from an object (except the top-level).
 * Use this to sanitize pattern objects before saving or rendering.
 *
 * @param obj - The object to clean
 * @returns A deep clone of the object with all nested 'pattern' fields removed
 */
export function cleanPattern<T = any>(obj: T): T {
  if (typeof obj !== 'object' || obj === null)
    return obj
  const newObj: any = Array.isArray(obj) ? [] : {}
  for (const key in obj as Record<string, unknown>) {
    if (key === 'pattern')
      continue // Remove all nested 'pattern' fields
    newObj[key] = cleanPattern((obj as Record<string, unknown>)[key])
  }
  return newObj as T
}
