/**
 * Utility functions for validation.
 */

import type { ErrorService } from '@/services/system/error-service' // May cause issues if ErrorService not fully initialized
import type { ErrorDetails as ErrorDetailsType } from '@/types/services/core/errorService' // Added import for ErrorDetailsType
import type { ErrorContext } from '@/types/services/errors'
import { ErrorSeverity, ErrorType } from '@/types/services/errors' // Corrected CoreErrorContext to ErrorContext

// Basic interface for what a validation result might look like, based on docs
export interface ValidationIssue {
  message: string
  code?: string
  path?: string
  [key: string]: any
}

export interface ValidationResult {
  valid: boolean
  errors: ValidationIssue[]
  data?: any
}

/**
 * Creates a standardized validation result object.
 * @param valid - Whether validation passed.
 * @param errors - Array of validation issues.
 * @param data - Original data that was validated (optional).
 * @returns A ValidationResult object.
 */
export function createValidationResult(
  valid: boolean,
  errors: ValidationIssue[] = [],
  data?: any,
): ValidationResult {
  return { valid, errors, data }
}

/**
 * Formats validation errors into human-readable messages.
 * Placeholder implementation.
 * @param errors - Array of validation issues.
 * @returns A string with formatted errors.
 */
export function formatValidationErrors(errors: ValidationIssue[]): string {
  if (!errors || errors.length === 0) {
    return 'No validation errors.'
  }
  return errors.map(err => `${err.path ? `${err.path}: ` : ''}${err.message} (${err.code || 'N/A'})`).join('\\n')
}

/**
 * Implements fail-fast validation. Throws an error if validation is not successful.
 * Placeholder implementation.
 * @param validationResult - The result of a validation.
 * @param targetId - Optional identifier for what was validated.
 */
export function throwIfInvalid(validationResult: ValidationResult, targetId?: string): void {
  if (!validationResult.valid) {
    const message = `Validation failed${targetId ? ` for ${targetId}` : ''}: ${formatValidationErrors(validationResult.errors)}`
    // In a real scenario, this might throw a specific CoreError
    throw new Error(message)
  }
}

/**
 * Provides robust validation with proper error handling.
 * Placeholder implementation.
 * @param data - Data to validate.
 * @param validatorFn - The function to perform validation.
 * @param errorService - Instance of ErrorService to handle errors.
 * @param component - Component name for error context.
 * @param operation - Operation name for error context.
 * @param targetId - Optional target ID for error context.
 * @returns A promise that resolves to true if validation is successful, false otherwise.
 */
export async function safeValidate<T>(
  data: T,
  validatorFn: (data: T) => ValidationResult | Promise<ValidationResult>,
  errorService: ErrorService,
  component: string,
  operation: string,
  targetId?: string,
): Promise<boolean> {
  try {
    const result = await validatorFn(data)
    if (!result.valid) {
      const errorContextForDetails: ErrorContext = { // Use the correctly imported ErrorContext
        component,
        operation,
        target: targetId,
        metadata: { validationErrors: result.errors, validatedData: data },
      }
      const errorDetails: ErrorDetailsType = { // Use the correctly imported ErrorDetailsType
        message: `Validation failed for ${operation}: ${formatValidationErrors(result.errors)}`,
        code: ErrorType.Validation.toString(),
        severity: ErrorSeverity.Medium,
        context: errorContextForDetails,
      }
      errorService.handleError(errorDetails)
      return false
    }
    return true
  }
  catch (error) {
    const caughtError = error as Error
    const errorContextForDetails: ErrorContext = { // Use the correctly imported ErrorContext
      component,
      operation,
      target: targetId,
      metadata: { originalErrorMessage: caughtError.message, validatedData: data },
      stack: caughtError.stack,
    }
    const errorDetails: ErrorDetailsType = { // Use the correctly imported ErrorDetailsType
      message: caughtError.message || `An unexpected error occurred during ${operation}`,
      code: (caughtError as any).code || ErrorType.Runtime.toString(),
      severity: ErrorSeverity.High, // Default for unexpected errors
      context: errorContextForDetails,
      originalError: caughtError,
    }
    errorService.handleError(errorDetails)
    return false
  }
}

// Placeholder for other validation utils mentioned in docs if needed:
// checkValidationResult, convertToCoreError, handleValidationError

// Also re-exporting from errorUtils for convenience if validation errors need to be converted
// export { convertToCoreError, convertValidationResultToCoreError, handleValidationError } from '@/services/system/error-service'; // Removed re-export to break cycle
