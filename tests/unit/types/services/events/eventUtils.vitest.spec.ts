import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { AppEventType } from '@/types/services/events/eventTypes'
import { createEvent } from '@/types/services/events/eventUtils'

describe('event Utilities', () => {
  // Mock Date.now() to return a consistent value
  const mockTimestamp = 1620000000000

  beforeEach(() => {
    vi.spyOn(Date, 'now').mockReturnValue(mockTimestamp)
    vi.spyOn(Math, 'random').mockReturnValue(0.5)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('createEvent', () => {
    it('should create an event with the specified type and payload', () => {
      const type = AppEventType.SHAPE_CREATE_REQUEST
      const payload = { ElementType: 'rectangle', position: { x: 100, y: 100 } }

      const event = createEvent(type, payload)

      expect(event).toEqual({
        id: expect.stringMatching(/^event-\d+-[a-z0-9]+$/),
        type,
        timestamp: mockTimestamp,
        payload,
      })
    })

    it('should use the provided timestamp if specified', () => {
      const type = AppEventType.SHAPE_CREATE_REQUEST
      const payload = { ElementType: 'rectangle', position: { x: 100, y: 100 } }
      const customTimestamp = 1610000000000

      const event = createEvent(type, payload, customTimestamp)

      expect(event).toEqual({
        id: expect.stringMatching(/^event-\d+-[a-z0-9]+$/),
        type,
        timestamp: customTimestamp,
        payload,
      })
    })

    it('should generate a unique ID for each event', () => {
      const type = AppEventType.SHAPE_CREATE_REQUEST
      const payload = { ElementType: 'rectangle', position: { x: 100, y: 100 } }

      // Reset the Math.random mock to return different values for each call
      vi.spyOn(Math, 'random')
        .mockReturnValueOnce(0.1)
        .mockReturnValueOnce(0.2)

      const event1 = createEvent(type, payload)
      const event2 = createEvent(type, payload)

      expect(event1.id).not.toEqual(event2.id)
    })

    it('should work with different event types and payloads', () => {
      // Test with a different event type
      const deleteEvent = createEvent(
        AppEventType.SHAPE_DELETE_REQUEST,
        { shapeIds: ['shape1', 'shape2'] },
      )

      expect(deleteEvent).toEqual({
        id: expect.stringMatching(/^event-\d+-[a-z0-9]+$/),
        type: AppEventType.SHAPE_DELETE_REQUEST,
        timestamp: mockTimestamp,
        payload: { shapeIds: ['shape1', 'shape2'] },
      })

      // Test with a different payload type
      const errorEvent = createEvent(
        AppEventType.ERROR_OCCURRED,
        { message: 'An error occurred', code: 'ERR_001' },
      )

      expect(errorEvent).toEqual({
        id: expect.stringMatching(/^event-\d+-[a-z0-9]+$/),
        type: AppEventType.ERROR_OCCURRED,
        timestamp: mockTimestamp,
        payload: { message: 'An error occurred', code: 'ERR_001' },
      })
    })
  })
})
