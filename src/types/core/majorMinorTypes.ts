/**
 * Defines Major and Minor categories for element classification.
 */

export enum MajorCategory {
  BASE = 'BASE', // Renamed from FLOOR
  CEILING = 'CEILING',
  FURNITURE = 'FURNITURE',
}

// 10 Minor Categories for organizing predefined assets and tools
export type MinorCategory =
  // BASE Major Category uses (3):
  | 'coverings' // e.g., Tiles, Wood, Carpet
  | 'architecture' // NEW: Room/Wall/Opening tools, Paint, Wallpaper
  | 'decor' // e.g., Rugs (for BASE), Lamps, Plants (for FURNITURE)
  // CEILING Major Category uses (2):
  | 'utilities' // e.g., Vents, Detectors
  | 'lighting' // e.g., Recessed, Pendants, Ceiling Fans (fans merged here)
  // FURNITURE Major Category uses (5 of its own + decor & utilities shared):
  | 'storage' // e.g., Bookshelves, Cabinets
  | 'beds' // e.g., Single, Queen
  | 'tables' // e.g., Dining, Coffee, Desk
  | 'seating' // e.g., Sofas, Chairs
  | 'appliances' // e.g., Refrigerator, Oven, Sinks, Toilets (also includes furniture utilities like stairs)
  // | 'fans'          // Removed, merged into lighting

export const MajorCategoryDisplayNames: Record<MajorCategory, string> = {
  [MajorCategory.BASE]: 'Base & Structure',
  [MajorCategory.CEILING]: 'Ceiling',
  [MajorCategory.FURNITURE]: 'Furniture & Fixtures',
}

// Display names for the 10 Minor Categories
export const MinorCategoryDisplayNames: Record<MinorCategory, string> = {
  // BASE
  coverings: 'Floor Coverings',
  architecture: 'Layout & Arch. Tools',
  decor: 'Decor & Accents',
  // CEILING
  utilities: 'Ceiling Utilities',
  lighting: 'Lighting & Fans', // Fans merged
  // FURNITURE
  storage: 'Storage Furniture',
  beds: 'Beds & Headboards',
  tables: 'Tables & Desks',
  seating: 'Seating Furniture',
  appliances: 'Appliances & Utilities', // Includes furniture utilities like stairs
}
