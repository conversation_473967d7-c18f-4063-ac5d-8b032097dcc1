/**
 * Cost Calculation Strategy for Curve Elements (Arc, Quadratic, Cubic)
 *
 * @remarks
 * This strategy implements the {@link CostCalculatorStrategy} to calculate costs
 * for various curve elements: {@link CoreElementType.ARC},
 * {@link CoreElementType.QUADRATIC}, and {@link CoreElementType.CUBIC}.
 *
 * It determines the cost based on a specified `costType` in the
 * {@link CostCalculationOptions}. Supported cost types are:
 * - `'perimeter'`: Cost is based on the curve's length (perimeter).
 * - `'area'`: Cost is based on the area enclosed by the curve (applicable if the curve is closed).
 * - `'fixed'`: Cost is based on a `baseCost` defined in options, potentially adjusted by type-specific factors.
 *
 * This strategy internally instantiates and uses specific area and perimeter calculation
 * strategies (e.g., {@link ArcPerimeterStrategy}, {@link CubicAreaStrategy}) to obtain the
 * necessary geometric measure (length or area) before applying the `unitCost` and other
 * cost adjustment factors from {@link CostCalculationOptions} (like `complexityFactor`,
 * `materialFactor`, `additionalCost`, `discountRate`, `taxRate`).
 *
 * The `getElementType()` method returns a generic string 'curve_composite', indicating
 * its applicability to multiple curve types. The {@link StrategyRegistry} would need
 * to map specific curve element types to this strategy.
 *
 * @module core/compute/strategies/cost/CurveCostStrategy
 * @see {@link CostCalculatorStrategy}
 * @see {@link CostCalculationOptions}
 * @see {@link ArcPerimeterStrategy}
 * @see {@link ArcAreaStrategy}
 */

import type { CostCalculationOptions, CostCalculatorStrategy } from '../../../../types/core/compute'
import type {
  Element,
  ShapeElement,
} from '../../../../types/core/elementDefinitions'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
  // Path // Not directly used here, specific curve types are handled by sub-strategies
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

// Import area strategies
import {
  ArcAreaStrategy,
  CubicAreaStrategy,
  QuadraticAreaStrategy,
} from '../area' // Assumes index.ts in ../area exports these

// Import perimeter calculation strategies
import {
  ArcPerimeterStrategy,
  CubicPerimeterStrategy,
  QuadraticPerimeterStrategy,
} from '../perimeter' // Assumes index.ts in ../perimeter exports these

export class CurveCostStrategy implements CostCalculatorStrategy {
  private arcPerimeterStrategy: ArcPerimeterStrategy
  private quadraticPerimeterStrategy: QuadraticPerimeterStrategy
  private cubicPerimeterStrategy: CubicPerimeterStrategy
  private arcAreaStrategy: ArcAreaStrategy
  private quadraticAreaStrategy: QuadraticAreaStrategy
  private cubicAreaStrategy: CubicAreaStrategy

  constructor() {
    this.arcPerimeterStrategy = new ArcPerimeterStrategy()
    this.quadraticPerimeterStrategy = new QuadraticPerimeterStrategy()
    this.cubicPerimeterStrategy = new CubicPerimeterStrategy()
    this.arcAreaStrategy = new ArcAreaStrategy()
    this.quadraticAreaStrategy = new QuadraticAreaStrategy()
    this.cubicAreaStrategy = new CubicAreaStrategy()
  }

  /**
   * Calculates the cost for a given curve element (Arc, Quadratic Bezier, or Cubic Bezier).
   *
   * The calculation method depends on the `costType` specified in the `options`:
   * - 'perimeter': `measure * unitCost * complexityFactor * materialFactor` + adjustments.
   * - 'area': `measure * unitCost * complexityFactor * materialFactor` + adjustments (measure is 0 if not closed).
   * - 'fixed': `baseCost * typeFactor * complexityFactor * materialFactor` + adjustments.
   *
   * @param element - The curve element ({@link CoreElementType.ARC}, {@link CoreElementType.QUADRATIC}, or {@link CoreElementType.CUBIC}).
   * @param unitCost - The base cost per unit of measure (length or area, depending on `costType`). Not used if `costType` is 'fixed'.
   * @param options - Optional {@link CostCalculationOptions}. Key properties include:
   *                  `costType` (default: 'perimeter'), `complexityFactor`, `materialFactor`,
   *                  `baseCost` (for 'fixed' type), `arcFactor`, `quadraticFactor`, `cubicFactor` (for 'fixed' type),
   *                  `additionalCost`, `discountRate`, `taxRate`.
   * @returns The total calculated cost for the curve element.
   * @throws {@link CoreError} if the element type is not one of the supported curve types.
   * @throws {@link CoreError} if `unitCost` is invalid (e.g., negative) when `costType` is not 'fixed'.
   * @throws {@link CoreError} if `costType` is invalid.
   * @throws {@link CoreError} if an underlying area or perimeter calculation fails.
   */
  public calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
    const validCurveTypes = [CoreElementType.ARC, CoreElementType.QUADRATIC, CoreElementType.CUBIC]
    if (!validCurveTypes.includes(element.type as CoreElementType)) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `CurveCostStrategy can only calculate cost for curve elements (ARC, QUADRATIC, CUBIC), got ${element.type}`,
        undefined,
        { component: 'CurveCostStrategy', operation: 'calculateCost', target: element.id },
      )
    }

    if (typeof unitCost !== 'number' || unitCost < 0) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Unit cost must be a non-negative number, got ${unitCost}`,
        undefined,
        { component: 'CurveCostStrategy', operation: 'calculateCost', target: element.id, metadata: { unitCost } },
      )
    }

    const costType = options?.costType ?? 'perimeter' // Default to perimeter-based cost
    const complexityFactor = options?.complexityFactor ?? 1.0
    const materialFactor = options?.materialFactor ?? 1.0
    let measure = 0

    try {
      const shapeElem = element as ShapeElement // Sub-strategies expect ShapeElement
      if (costType === 'perimeter') {
        switch (element.type as CoreElementType) {
          case CoreElementType.ARC:
            measure = this.arcPerimeterStrategy.calculatePerimeter(shapeElem)
            break
          case CoreElementType.QUADRATIC:
            measure = this.quadraticPerimeterStrategy.calculatePerimeter(shapeElem)
            break
          case CoreElementType.CUBIC:
            measure = this.cubicPerimeterStrategy.calculatePerimeter(shapeElem)
            break
          case CoreElementType.RECTANGLE:
          case CoreElementType.SQUARE:
          case CoreElementType.ELLIPSE:
          case CoreElementType.CIRCLE:
          case CoreElementType.POLYGON:
          case CoreElementType.TRIANGLE:
          case CoreElementType.QUADRILATERAL:
          case CoreElementType.PENTAGON:
          case CoreElementType.HEXAGON:
          case CoreElementType.HEPTAGON:
          case CoreElementType.OCTAGON:
          case CoreElementType.NONAGON:
          case CoreElementType.DECAGON:
          case CoreElementType.LINE:
          case CoreElementType.LINE:
          case CoreElementType.POLYLINE:
          case CoreElementType.ARC:
          case CoreElementType.QUADRATIC:
          case CoreElementType.CUBIC:
          case CoreElementType.POLYLINE:
          case CoreElementType.TEXT_LABEL:
          case CoreElementType.WALL:
          case CoreElementType.DOOR:
          case CoreElementType.WINDOW:
          case CoreElementType.FURNITURE:
          case CoreElementType.FIXTURE:
          case CoreElementType.ROOM:
          case CoreElementType.LIGHT:
          case CoreElementType.FLOOR_AREA:
          case CoreElementType.HANDRAIL:
          case CoreElementType.ELECTRICAL_OUTLET:
          case CoreElementType.ROOM_BOUNDARY:
          case CoreElementType.APPLIANCE:
          case CoreElementType.TEXT:
          case CoreElementType.IMAGE:
          case CoreElementType.GROUP:
          case CoreElementType.OPENING:
          case CoreElementType.WALL_PAINT:
          case CoreElementType.WALL_PAPER:
          default:
            // This should never happen as type is validated above
            throw new CoreError(
              ErrorType.InvalidElementType,
              `Unexpected element type in perimeter calculation: ${element.type}`,
              undefined,
              { component: 'CurveCostStrategy', operation: 'calculateCost', target: element.id },
            )
        }
      }
      else if (costType === 'area') {
        // Area calculation is typically for closed shapes.
        // Ensure the specific curve element type (Path.Arc, Path.Quadratic, Path.Cubic) has a 'closed' property.
        // The area strategies themselves handle the 'closed' check.
        switch (element.type as CoreElementType) {
          case CoreElementType.ARC:
            measure = this.arcAreaStrategy.calculateArea(shapeElem)
            break
          case CoreElementType.QUADRATIC:
            measure = this.quadraticAreaStrategy.calculateArea(shapeElem)
            break
          case CoreElementType.CUBIC:
            measure = this.cubicAreaStrategy.calculateArea(shapeElem)
            break
          case CoreElementType.RECTANGLE:
          case CoreElementType.SQUARE:
          case CoreElementType.ELLIPSE:
          case CoreElementType.CIRCLE:
          case CoreElementType.POLYGON:
          case CoreElementType.TRIANGLE:
          case CoreElementType.QUADRILATERAL:
          case CoreElementType.PENTAGON:
          case CoreElementType.HEXAGON:
          case CoreElementType.HEPTAGON:
          case CoreElementType.OCTAGON:
          case CoreElementType.NONAGON:
          case CoreElementType.DECAGON:
          case CoreElementType.LINE:
          case CoreElementType.ARC:
          case CoreElementType.QUADRATIC:
          case CoreElementType.CUBIC:
          case CoreElementType.POLYLINE:
          case CoreElementType.TEXT_LABEL:
          case CoreElementType.WALL:
          case CoreElementType.DOOR:
          case CoreElementType.WINDOW:
          case CoreElementType.FURNITURE:
          case CoreElementType.FIXTURE:
          case CoreElementType.ROOM:
          case CoreElementType.LIGHT:
          case CoreElementType.FLOOR_AREA:
          case CoreElementType.HANDRAIL:
          case CoreElementType.ELECTRICAL_OUTLET:
          case CoreElementType.ROOM_BOUNDARY:
          case CoreElementType.APPLIANCE:
          case CoreElementType.TEXT:
          case CoreElementType.IMAGE:
          case CoreElementType.GROUP:
          case CoreElementType.OPENING:
          case CoreElementType.WALL_PAINT:
          case CoreElementType.WALL_PAPER:
          default:
            // This should never happen as type is validated above
            throw new CoreError(
              ErrorType.InvalidElementType,
              `Unexpected element type in area calculation: ${element.type}`,
              undefined,
              { component: 'CurveCostStrategy', operation: 'calculateCost', target: element.id },
            )
        }
      }
      else if (costType === 'fixed') {
        const baseCost = options?.baseCost ?? 100
        let typeFactor = 1.0
        switch (element.type as CoreElementType) {
          case CoreElementType.ARC:
            typeFactor = options?.arcFactor ?? 1.0
            break
          case CoreElementType.QUADRATIC:
            typeFactor = options?.quadraticFactor ?? 1.2
            break
          case CoreElementType.CUBIC:
            typeFactor = options?.cubicFactor ?? 1.5
            break
          case CoreElementType.RECTANGLE:
          case CoreElementType.SQUARE:
          case CoreElementType.ELLIPSE:
          case CoreElementType.CIRCLE:
          case CoreElementType.POLYGON:
          case CoreElementType.TRIANGLE:
          case CoreElementType.QUADRILATERAL:
          case CoreElementType.PENTAGON:
          case CoreElementType.HEXAGON:
          case CoreElementType.HEPTAGON:
          case CoreElementType.OCTAGON:
          case CoreElementType.NONAGON:
          case CoreElementType.DECAGON:
          case CoreElementType.LINE:
          case CoreElementType.ARC:
          case CoreElementType.QUADRATIC:
          case CoreElementType.CUBIC:
          case CoreElementType.POLYLINE:
          case CoreElementType.TEXT_LABEL:
          case CoreElementType.WALL:
          case CoreElementType.DOOR:
          case CoreElementType.WINDOW:
          case CoreElementType.FURNITURE:
          case CoreElementType.FIXTURE:
          case CoreElementType.ROOM:
          case CoreElementType.LIGHT:
          case CoreElementType.FLOOR_AREA:
          case CoreElementType.HANDRAIL:
          case CoreElementType.ELECTRICAL_OUTLET:
          case CoreElementType.ROOM_BOUNDARY:
          case CoreElementType.APPLIANCE:
          case CoreElementType.TEXT:
          case CoreElementType.IMAGE:
          case CoreElementType.GROUP:
          case CoreElementType.OPENING:
          case CoreElementType.WALL_PAINT:
          case CoreElementType.WALL_PAPER:
          default:
            // This should never happen as type is validated above
            throw new CoreError(
              ErrorType.InvalidElementType,
              `Unexpected element type in fixed cost calculation: ${element.type}`,
              undefined,
              { component: 'CurveCostStrategy', operation: 'calculateCost', target: element.id },
            )
        }
        // For fixed cost, adjustments like additionalCost, discount, tax are applied below if present
        measure = baseCost * typeFactor // Use measure to hold this intermediate value before other factors
        // The main unitCost is not used for 'fixed' type, complexity and material factors are applied
      }
      else {
        throw new CoreError(
          ErrorType.InvalidParameter,
          `Invalid cost type: ${costType}`,
          undefined,
          { component: 'CurveCostStrategy', operation: 'calculateCost', target: element.id, metadata: { costType } },
        )
      }
    }
    catch (error) {
      throw new CoreError(
        ErrorType.ComputationError,
        `Failed to get ${costType} for element (ID: ${element.id}): ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        { component: 'CurveCostStrategy', operation: 'calculateCost', target: element.id, metadata: { originalError: error, costType } },
      )
    }

    if (typeof measure !== 'number' || Number.isNaN(measure) || measure < 0) {
      // TODO: Consider if this should be an error or if 0 is acceptable.
      // If an upstream strategy (area/perimeter) returned NaN, it should have thrown.
      // This might catch cases where 'fixed' cost calculation results in NaN.
      console.warn(`[CurveCostStrategy] Invalid measure (${costType}: ${measure}) for element ${element.id}. Defaulting to 0 for cost calculation.`)
      measure = 0
    }

    let calculatedCost = (costType === 'fixed') ? measure : (measure * unitCost) // For fixed, measure is already baseCost*typeFactor
    calculatedCost *= complexityFactor * materialFactor

    if (options?.additionalCost != null && typeof options.additionalCost === 'number' && options.additionalCost > 0) {
      calculatedCost += options.additionalCost
    }
    if (options?.discountRate != null && typeof options.discountRate === 'number' && options.discountRate >= 0 && options.discountRate <= 100) {
      calculatedCost *= (1 - (options.discountRate / 100))
    }
    if (options?.taxRate != null && typeof options.taxRate === 'number' && options.taxRate >= 0) {
      calculatedCost *= (1 + (options.taxRate / 100))
    }

    return calculatedCost
  }

  /**
   * Returns a string identifier indicating that this strategy handles multiple curve types.
   *
   * @remarks
   * This strategy is designed for various curve elements (ARC, QUADRATIC, CUBIC).
   * It returns a generic identifier 'curve_composite'. The {@link StrategyRegistry}
   * would be responsible for mapping specific curve {@link ElementType}s to this
   * composite strategy if desired, or this strategy could be invoked directly
   * when the element is known to be a curve.
   *
   * @returns The string 'curve_composite'.
   */
  public getElementType(): string {
    return 'curve_composite' // Special identifier for this composite curve cost strategy
  }
}
