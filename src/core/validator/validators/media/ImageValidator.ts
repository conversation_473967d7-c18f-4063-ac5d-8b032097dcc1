import type { IBaseShapeValidator } from '../common/validatorBase'
import type { ValidatableShape, ValidationError } from '@/types/core/validator/validator-interface'
import { ElementType } from '@/types/core/elementDefinitions'
import { BaseShapeValidator } from '../common/validatorBase'
// import { ValidationErrorCode } from '@/types/core/validator/error-codes'; // For specific error codes if needed

export default class ImageValidator extends BaseShapeValidator implements IBaseShapeValidator {
  protected validateSpecific(shape: ValidatableShape): ValidationError[] {
    if (shape.type !== ElementType.IMAGE) {
      console.warn(`[ImageValidator] validateSpecific called for non-IMAGE type: ${shape.type}`)
      return []
    }
    const errors: ValidationError[] = []

    // Placeholder for actual validation logic
    // Example: Validate that 'src' property is a non-empty string
    // const srcValue = shape.properties?.src;
    // if (!srcValue || typeof srcValue !== 'string' || srcValue.trim() === '') {
    //     errors.push({
    //         code: ValidationErrorCode.MISSING_OR_INVALID_PROPERTY, // Example error code
    //         message: `Image 'src' property must be a non-empty string, received: ${srcValue}`,
    //         path: 'properties.src',
    //         value: srcValue
    //     });
    // }

    // Example: Validate that 'width' and 'height' are positive numbers
    // const widthValue = shape.properties?.width;
    // if (typeof widthValue !== 'number' || widthValue <= 0) {
    //     errors.push({
    //         code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
    //         message: `Image 'width' must be a positive number, received: ${widthValue}`,
    //         path: 'properties.width',
    //         value: widthValue
    //     });
    // }
    // const heightValue = shape.properties?.height;
    // if (typeof heightValue !== 'number' || heightValue <= 0) {
    //     errors.push({
    //         code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
    //         message: `Image 'height' must be a positive number, received: ${heightValue}`,
    //         path: 'properties.height',
    //         value: heightValue
    //     });
    // }

    return errors
  }

  // Optionally override applySpecificRules if specific business logic for Image is needed
  // protected applySpecificRules(shape: ValidatableShape): ValidationError[] {
  //     const errors: ValidationError[] = super.applySpecificRules(shape);
  //     // Add image-specific business rules, e.g., aspect ratio constraints, max dimensions
  //     return errors;
  // }
}
