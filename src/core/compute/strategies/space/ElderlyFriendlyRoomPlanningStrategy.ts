// import Point from '@/types/core/element/geometry/point'; // Correct import for Point interface // Unused
import type {
  ErgonomicsEvaluationResult,
  PathwayCheckResult,
  SpacePlanningStrategy,
  SpaceType,
} from '@/types/core/compute'
/**
 * Elderly-Friendly Space Planning Strategy
 * Provides planning functionality for elderly-friendly spaces, including safety and accessibility, lighting and contrast, assistive facilities, etc.
 */
import type {
  Element,
  Shape,
  // Path, // Unused
  // MetadataProperties, // Unused
  ShapeElement,
} from '@/types/core/elementDefinitions'
// import {
//     calculateSpaceUtilization,
//     checkPathwaysWidth
// } from '@/lib/utils/space/spacePlanning'; // Module not found
// import { BoundingBoxClass } from '@/lib/utils/geometry'; // Unused
import { calculatePointToPointDistance } from '@/lib/utils/geometry' // Migrated from commonUtils
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'

// Helper to get BoundingBoxClass from an Element (Unused in this file)
// function getElementBoundingBoxClass(element: Element): BoundingBoxClass | null {
//     if (!element) return null;
//     if (element.type === CoreElementType.RECTANGLE || element.type === CoreElementType.SQUARE) {
//         const shape = element as Shape.Rectangle;
//         if (shape.position && typeof shape.width === 'number' && typeof shape.height === 'number') {
//             return new BoundingBoxClass(shape.position.x - shape.width / 2, shape.position.y - shape.height / 2, shape.width, shape.height);
//         }
//     }
//      if (element.type === CoreElementType.CIRCLE) {
//         const shape = element as Shape.Circle;
//         if (shape.position && typeof shape.radius === 'number') {
//             return new BoundingBoxClass(shape.position.x - shape.radius, shape.position.y - shape.radius, shape.radius * 2, shape.radius * 2);
//         }
//     }
//     // Add more cases for other element types if needed
//     console.warn(`[ElderlyFriendlyRoomPlanningStrategy] getElementBoundingBoxClass: Bounding box for type ${element.type} not fully implemented here.`);
//     return null;
// }

/**
 * Elderly-Friendly Space Planning Strategy
 */
export class ElderlyFriendlyRoomPlanningStrategy implements SpacePlanningStrategy {
  public calculateSpaceUtilization(/* elements: Element[], roomBoundaryElement: Element */): number { // Params unused due to commented out body
    // const roomBBoxClass = getElementBoundingBoxClass(roomBoundaryElement);
    // if (!roomBBoxClass) {
    //   throw new CoreError( ErrorType.INVALID_PARAMETER, 'Room boundary must be an element from which a bounding box can be derived.');
    // }
    // return calculateSpaceUtilization(elements, roomBBoxClass);
    console.warn('[ElderlyFriendlyRoomPlanningStrategy] calculateSpaceUtilization is temporarily disabled due to missing dependencies.')
    return 0
  }

  public checkPathwayWidth(/* elements: Element[], pathways: Path.Line[], minWidth: number */): PathwayCheckResult[] { // Params unused due to commented out body
    // if (!Array.isArray(elements)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Elements must be an array');
    // if (!Array.isArray(pathways)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Pathways must be an array');

    // const elderlyMinWidth = Math.max(minWidth, 0.9); // Specific for elderly

    // const formattedPathways = pathways.map((path, index) => {
    //     const lineForUtil = {
    //         id: path.id ?? `pathway-${index}`,
    //         start: path.start as Point,
    //         end: path.end as Point,
    //         type: CoreElementType.LINE,
    //         getSubType: () => CoreElementType.LINE,
    //     } as any;
    //     return { id: path.id ?? `pathway-${index + 1}`, line: lineForUtil as import('@/lib/utils/element/path/lineImplementation').Line };
    // });

    // return checkPathwaysWidth(elements, formattedPathways, elderlyMinWidth);
    console.warn('[ElderlyFriendlyRoomPlanningStrategy] checkPathwayWidth is temporarily disabled due to missing dependencies.')
    return []
  }

  public evaluateErgonomics(
    elements: Element[],
    deskElement: Element,
    chairElement: Element,
  ): ErgonomicsEvaluationResult {
    const issues: string[] = []
    const recommendations: string[] = []

    if (deskElement?.type === null || deskElement?.type === undefined || chairElement?.type === null || chairElement?.type === undefined) {
      throw new CoreError(ErrorType.InvalidParameter, 'Desk and chair elements must be provided and have a type.')
    }

    const deskShape = deskElement as unknown as Shape.Rectangle
    const chairShape = chairElement as unknown as Shape.Rectangle

    const deskHeight = deskShape.height ?? 0
    if (deskHeight < 0.7 || deskHeight > 0.76) {
      issues.push(`Desk height of ${deskHeight.toFixed(2)}m is outside the recommended range for elderly users (0.7-0.76m).`)
    }
    else {
      recommendations.push(`Desk height of ${deskHeight.toFixed(2)}m is within recommended range.`)
    }

    const chairShapeAsShapeElement = chairShape as unknown as ShapeElement
    const chairProperties = chairShapeAsShapeElement.properties as { seatHeight?: number, hasArmrests?: boolean, isStable?: boolean } | undefined

    const chairSeatHeight = chairProperties?.seatHeight ?? (chairShape.height ? chairShape.height * 0.6 : 0.45)
    if (chairSeatHeight < 0.45 || chairSeatHeight > 0.5) {
      issues.push(`Chair seat height of ${chairSeatHeight.toFixed(2)}m is outside the recommended range for elderly users (0.45-0.5m).`)
    }
    else {
      recommendations.push(`Chair seat height of ${chairSeatHeight.toFixed(2)}m is within recommended range.`)
    }

    if (!chairProperties?.hasArmrests) {
      issues.push('Chair lacks armrests, recommended for elderly users.')
      recommendations.push('Provide a chair with sturdy armrests.')
    }
    if (!chairProperties?.isStable) {
      issues.push('Chair may not provide adequate stability.')
      recommendations.push('Ensure chair has a stable base.')
    }

    if (!this.checkForTaskLighting(elements, deskElement)) {
      issues.push('Workspace lacks dedicated task lighting.')
      recommendations.push('Add adjustable task lighting to the workspace.')
    }

    recommendations.push('Provide adequate knee clearance under the desk.')
    recommendations.push('Ensure frequently used items are within easy reach.')

    return { isValid: issues.length === 0, issues, recommendations }
  }

  private checkForTaskLighting(elements: Element[], deskElement: Element): boolean {
    const lightingElements = elements.filter(el => el.type === CoreElementType.LIGHT || (el as ShapeElement).properties?.category === 'lighting')
    const deskPos = (deskElement as ShapeElement).position
    if (deskPos === null || deskPos === undefined)
      return false

    for (const light of lightingElements) {
      const lightPos = (light as ShapeElement).position
      if (lightPos === null || lightPos === undefined)
        continue
      const distance = calculatePointToPointDistance(lightPos, deskPos)
      if (distance < 1.5 && ((light as ShapeElement).properties?.isTaskLight === true || (light as ShapeElement).properties?.customType === 'desk-lamp')) {
        return true
      }
    }
    return false
  }

  public evaluateSafetyAndAccessibility(elements: Element[]): { isValid: boolean, issues: string[], recommendations: string[] } {
    const issues: string[] = []
    const recommendations: string[] = []

    const floorElements = elements.filter(el => el.type === CoreElementType.FLOOR_AREA || (el as ShapeElement).properties?.category === 'floor')
    if (floorElements.length > 0) {
      if (!floorElements.some(f => ((f as ShapeElement).properties?.material as string)?.toLowerCase().includes('non-slip'))) {
        issues.push('Floor material lacks non-slip properties.')
        recommendations.push('Use non-slip flooring materials.')
      }
    }

    elements.forEach((el) => {
      const elWithProps = el as ShapeElement
      if (elWithProps.type === ('step' as CoreElementType) || elWithProps.type === ('threshold' as CoreElementType) || elWithProps.properties?.category === 'step' || elWithProps.properties?.category === 'threshold') {
        if (elWithProps.properties?.hasHandrail !== true)
          issues.push(`Step/threshold ${el.id} lacks handrail.`)
        if (elWithProps.properties?.hasContrastMarking !== true)
          issues.push(`Step/threshold ${el.id} lacks contrast marking.`)
      }
    })

    if (!elements.some(el => el.type === CoreElementType.HANDRAIL || (el as ShapeElement).properties?.category === 'handrail')) {
      issues.push('No handrail elements detected.')
      recommendations.push('Install handrails in key areas like bathrooms and corridors.')
    }

    const furnitureIssues = this.checkFurnitureAccessibility(elements)
    issues.push(...furnitureIssues.issues)
    recommendations.push(...furnitureIssues.recommendations)

    // Simplified corridor and turning space checks
    recommendations.push('Ensure pathways are at least 0.9m wide and turning spaces are 1.5m x 1.5m.')

    return { isValid: issues.length === 0, issues, recommendations }
  }

  public evaluateLightingAndContrast(elements: Element[]): { isValid: boolean, issues: string[], recommendations: string[] } {
    const issues: string[] = []
    const recommendations: string[] = []
    const lightingElements = elements.filter(el => el.type === CoreElementType.LIGHT || (el as ShapeElement).properties?.category === 'lighting')

    if (lightingElements.length === 0) {
      issues.push('No lighting elements detected; elderly need stronger lighting.')
      recommendations.push('Increase overall lighting brightness.')
    }
    else {
      if (lightingElements.some(l => (l as ShapeElement).properties?.isDimmable === true) === false) {
        recommendations.push('Consider dimmable lighting.')
      }
    }
    // Simplified contrast and glare checks
    recommendations.push('Use high contrast colors for important features.')
    recommendations.push('Install window treatments to control glare.')
    return { isValid: issues.length === 0, issues, recommendations }
  }

  private checkFurnitureAccessibility(elements: Element[]): { issues: string[], recommendations: string[] } {
    const issues: string[] = []
    const recommendations: string[] = []
    const furnitureElements = elements.filter(el => el.type === CoreElementType.FURNITURE || (el as ShapeElement).properties?.category === 'furniture')

    furnitureElements.forEach((f) => {
      const furn = f as unknown as Shape.Rectangle // Assuming furniture can be approximated or is Rectangle
      const furnShapeElement = furn as unknown as ShapeElement
      const furnProps = furnShapeElement.properties as { furnitureType?: string, hasArmrests?: boolean, isStable?: boolean, seatHeight?: number } | undefined
      const height = furn.height ?? 0 // Using shape height as a fallback

      if (furnProps?.furnitureType === 'chair' || furnProps?.furnitureType === 'sofa') {
        const seatH = furnProps?.seatHeight ?? height // Prefer specific seatHeight
        if (seatH < 0.45 || seatH > 0.5)
          issues.push(`Seating ${f.id} height (${seatH.toFixed(2)}m) not ideal (0.45-0.5m).`)
        if (furnProps?.hasArmrests !== true)
          issues.push(`Seating ${f.id} lacks armrests.`)
      }
      if (furnProps?.furnitureType === 'bed' && (height < 0.45 || height > 0.5)) {
        issues.push(`Bed ${f.id} height (${height.toFixed(2)}m) not ideal (0.45-0.5m).`)
      }
    })
    if (furnitureElements.length > 0)
      recommendations.push('Choose furniture with rounded corners.')
    return { issues, recommendations }
  }

  // Removed identifyCorridors, identifyTurningSpaces, calculateBoundingBox, doBoxesOverlap, isSimilarColor, findNearestElement
  // as their logic is either too complex for this pass or should be part of more generic utils or ComputeFacade.

  public getSpaceType(): SpaceType {
    return 'elderly'
  }
}
