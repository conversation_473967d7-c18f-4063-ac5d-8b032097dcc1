import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import {
  ComputeError,
  DefaultErrorLogger,
  ElementNotFoundError,
  errorLogger,
  ErrorType,
  FactoryError,
  UnsupportedElementTypeError,
  UnsupportedOperationError,
  ValidationResultError,
  ValidatorError,
} from '@/core/errors'

describe('error Classes', () => {
  describe('factoryError', () => {
    it('should create a FactoryError with the specified message', () => {
      const error = new FactoryError('Failed to create shape')

      expect(error.name).toBe('FactoryError')
      expect(error.message).toBe('Failed to create shape')
      expect(error.type).toBe(ErrorType.FACTORY_CREATION_FAILED)
    })

    it('should include metadata when provided', () => {
      const metadata = { ElementType: 'circle', properties: { radius: 10 } }
      const error = new FactoryError('Failed to create shape', metadata)

      expect(error.metadata).toEqual(metadata)
    })
  })

  describe('validatorError', () => {
    it('should create a ValidatorError with the specified message', () => {
      const error = new ValidatorError('Validation failed')

      expect(error.name).toBe('ValidatorError')
      expect(error.message).toBe('Validation failed')
      expect(error.type).toBe(ErrorType.VALIDATION_FAILED)
    })

    it('should include metadata when provided', () => {
      const metadata = { shapeId: 'shape-1', property: 'radius' }
      const error = new ValidatorError('Validation failed', metadata)

      expect(error.metadata).toEqual(metadata)
    })
  })

  describe('computeError', () => {
    it('should create a ComputeError with the specified message', () => {
      const error = new ComputeError('Failed to compute bounds')

      expect(error.name).toBe('ComputeError')
      expect(error.message).toBe('Failed to compute bounds')
      expect(error.type).toBe(ErrorType.COMPUTE_BOUNDS_ERROR)
    })

    it('should include metadata when provided', () => {
      const metadata = { shapeId: 'shape-1', operation: 'getBounds' }
      const error = new ComputeError('Failed to compute bounds', metadata)

      expect(error.metadata).toEqual(metadata)
    })
  })

  describe('validationResultError', () => {
    it('should create a ValidationResultError with the specified errors and shape type', () => {
      const errors = ['Invalid radius', 'Missing position']
      const ElementType = 'circle'

      const error = new ValidationResultError(errors, ElementType)

      expect(error.name).toBe('ValidationResultError')
      expect(error.message).toBe('Validation failed for circle: Invalid radius, Missing position')
      expect(error.errors).toEqual(errors)
      expect(error.ElementType).toBe(ElementType)
      expect(error.shapeId).toBeUndefined()
    })

    it('should include shapeId when provided', () => {
      const errors = ['Invalid radius', 'Missing position']
      const ElementType = 'circle'
      const shapeId = 'circle-1'

      const error = new ValidationResultError(errors, ElementType, shapeId)

      expect(error.message).toBe('Validation failed for circle (ID: circle-1): Invalid radius, Missing position')
      expect(error.shapeId).toBe(shapeId)
    })
  })

  describe('unsupportedElementTypeError', () => {
    it('should create an UnsupportedElementTypeError with the specified shape type', () => {
      const ElementType = 'unknown'

      const error = new UnsupportedElementTypeError(ElementType)

      expect(error.name).toBe('UnsupportedElementTypeError')
      expect(error.message).toBe('Unsupported shape type: unknown')
      expect(error.ElementType).toBe(ElementType)
      expect(error.type).toBe(ErrorType.FACTORY_INVALID_TYPE)
    })
  })

  describe('unsupportedOperationError', () => {
    it('should create an UnsupportedOperationError with the specified operation', () => {
      const operation = 'getArea'

      const error = new UnsupportedOperationError(operation)

      expect(error.name).toBe('UnsupportedOperationError')
      expect(error.message).toBe('Unsupported operation: getArea')
      expect(error.operation).toBe(operation)
      expect(error.ElementType).toBeUndefined()
      expect(error.type).toBe(ErrorType.STRATEGY_NOT_FOUND)
    })

    it('should include ElementType when provided', () => {
      const operation = 'getArea'
      const ElementType = 'polygon'

      const error = new UnsupportedOperationError(operation, ElementType)

      expect(error.message).toBe('Unsupported operation: getArea for shape type: polygon')
      expect(error.ElementType).toBe(ElementType)
    })
  })

  describe('elementNotFoundError', () => {
    it('should create an ElementNotFoundError with the specified element ID', () => {
      const elementId = 'shape-1'

      const error = new ElementNotFoundError(elementId)

      expect(error.name).toBe('ElementNotFoundError')
      expect(error.message).toBe('Element not found: shape-1')
      expect(error.elementId).toBe(elementId)
      expect(error.type).toBe(ErrorType.COORDINATOR_SHAPE_NOT_FOUND)
    })
  })

  describe('defaultErrorLogger', () => {
    let consoleSpy: any

    beforeEach(() => {
      consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    })

    afterEach(() => {
      consoleSpy.mockRestore()
    })

    it('should be the implementation used by errorLogger', () => {
      expect(errorLogger).toBeInstanceOf(DefaultErrorLogger)
    })
  })
})
