import type { PointData as IPoint } from '../../../types/core/element/geometry/point' // Use IPoint for type hinting
/**
 * Coordinate System Conversion and Point Operation Utilities
 *
 * @remarks
 * This module provides a collection of utility functions for:
 * - Converting coordinates between different systems (e.g., client to SVG, SVG to client).
 * - Performing basic geometric operations on points, such as rotation and translation.
 * - Calculating distances (e.g., point to line segment).
 *
 * It utilizes the {@link PointClass} for returning new point instances and expects
 * input points to conform to the {@link IPoint} (aliased from `PointData`) interface.
 *
 * @module lib/utils/geometry/coordinates
 */
// Removed Chinese performance optimization comments from the top of the file.
import { PointClass } from './PointClass' // Use PointClass for instantiation
// Import necessary functions from pointUtils (previously from commonUtils)
import { calculateDistance as pointUtilCalculateDistance, rotatePoint as pointUtilRotatePoint } from './pointUtils'

/**
 * Interface for an object containing client (mouse event) coordinates.
 * @private
 * @remarks This is a local helper type for functions like `mouseEventToSvgPoint`.
 *          Consider moving to a shared types file if used more broadly.
 */
interface ClientCoordinates {
  clientX: number
  clientY: number
}

// Note: Other common geometry functions (calculateMidpoint, calculateDistance, etc.)
// are typically found in specific utility files like pointUtils.ts.

// -----------------------------------------------------------------------------
// Coordinate Conversion Functions
// -----------------------------------------------------------------------------

/**
 * Converts client (browser window) coordinates to SVG coordinates.
 *
 * @param clientX - The x-coordinate relative to the browser window.
 * @param clientY - The y-coordinate relative to the browser window.
 * @param element - The target SVG element to which the coordinates should be mapped.
 * @returns A {@link PointClass} instance representing the coordinates in the SVG's coordinate system,
 *          or `null` if the element is null or the transformation fails.
 *
 * @example
 * ```typescript
 * const svg = document.getElementById('mySvg') as SVGSVGElement | null;
 * const svgCoords = clientToSvgPoint(event.clientX, event.clientY, svg);
 * if (svgCoords) {
 *   // Use svgCoords.x and svgCoords.y
 * }
 * ```
 */
export function clientToSvgPoint(
  clientX: number,
  clientY: number,
  element: SVGElement | null,
): PointClass | null { // Return PointClass instance
  if (!element)
    return null

  try {
    // 查找SVG根元素
    let svgRoot: SVGSVGElement | null = null
    let currentElement: Element | null = element

    // 向上遍历DOM树，查找SVG根元素
    while (currentElement) {
      if (currentElement instanceof SVGSVGElement) {
        svgRoot = currentElement
        break
      }
      currentElement = currentElement.parentElement
    }

    // 如果找不到SVG根元素，尝试从文档中查找
    if (!svgRoot) {
      svgRoot = document.querySelector('svg')
      if (!svgRoot) {
        console.error('[clientToSvgPoint] Cannot find SVG root element')
        return null
      }
    }

    // 获取SVG元素的边界矩形
    const svgRect = svgRoot.getBoundingClientRect()

    // 计算相对于SVG元素的坐标
    const svgX = clientX - svgRect.left
    const svgY = clientY - svgRect.top

    // 获取当前的变换矩阵
    const transform = svgRoot.getAttribute('transform')
    const pan = { x: 0, y: 0 }
    let zoom = 1

    // 如果有变换矩阵，解析它
    if (transform) {
      const translateMatch = transform.match(/translate\(([^,]+),\s*([^)]+)\)/)
      const scaleMatch = transform.match(/scale\(([^)]+)\)/)

      if (translateMatch && translateMatch.length >= 3) {
        pan.x = Number.parseFloat(translateMatch[1])
        pan.y = Number.parseFloat(translateMatch[2])
      }

      if (scaleMatch && scaleMatch.length >= 2) {
        const scaleValue = Number.parseFloat(scaleMatch[1])
        if (!isNaN(scaleValue) && scaleValue !== 0) {
          zoom = scaleValue
        }
      }
    }

    // 计算世界坐标
    const worldX = (svgX - pan.x) / zoom
    const worldY = (svgY - pan.y) / zoom

    // 添加调试信息
    console.warn('[clientToSvgPoint]', {
      clientX,
      clientY,
      svgRect,
      svgX,
      svgY,
      pan,
      zoom,
      worldX,
      worldY,
      element: element.tagName,
      svgRoot: svgRoot.tagName,
    })

    // 返回变换后的点
    return new PointClass(worldX, worldY)
  }
  catch (error) {
    console.error('Coordinate conversion failed:', error)
    return null
  }
}

/**
 * Converts mouse event coordinates (from an object implementing {@link ClientCoordinates}) to SVG coordinates.
 *
 * @param event - An object containing `clientX` and `clientY` properties (e.g., a MouseEvent).
 * @param element - The target SVG element.
 * @returns A {@link PointClass} instance representing the coordinates in the SVG's system,
 *          or `null` if conversion fails.
 *
 * @example
 * ```typescript
 * svgElement.addEventListener('click', (e) => {
 *   const svgP = mouseEventToSvgPoint(e, svgElement);
 *   // ...
 * });
 * ```
 */
export function mouseEventToSvgPoint(
  event: ClientCoordinates, // Changed from React.MouseEvent
  element: SVGElement | null,
): PointClass | null { // Return PointClass instance
  if (!element)
    return null

  return clientToSvgPoint(event.clientX, event.clientY, element)
}

/**
 * Converts SVG coordinates to client (browser window) coordinates.
 *
 * @param svgX - The x-coordinate within the SVG's coordinate system.
 * @param svgY - The y-coordinate within the SVG's coordinate system.
 * @param element - The SVG element from which the coordinates originate.
 * @returns A {@link PointClass} instance representing the client coordinates,
 *          or `null` if the element is null or the transformation fails.
 */
export function svgToClientPoint(
  svgX: number,
  svgY: number,
  element: SVGElement | null,
): PointClass | null { // Return PointClass instance
  if (!element)
    return null

  try {
    // 查找SVG根元素
    let svgRoot: SVGSVGElement | null = null
    let currentElement: Element | null = element

    // 向上遍历DOM树，查找SVG根元素
    while (currentElement) {
      if (currentElement instanceof SVGSVGElement) {
        svgRoot = currentElement
        break
      }
      currentElement = currentElement.parentElement
    }

    // 如果找不到SVG根元素，尝试从文档中查找
    if (!svgRoot) {
      svgRoot = document.querySelector('svg')
      if (!svgRoot) {
        console.error('[svgToClientPoint] Cannot find SVG root element')
        return null
      }
    }

    // 获取SVG元素的边界矩形
    const svgRect = svgRoot.getBoundingClientRect()

    // 获取当前的变换矩阵
    const transform = svgRoot.getAttribute('transform')
    const pan = { x: 0, y: 0 }
    let zoom = 1

    // 如果有变换矩阵，解析它
    if (transform) {
      const translateMatch = transform.match(/translate\(([^,]+),\s*([^)]+)\)/)
      const scaleMatch = transform.match(/scale\(([^)]+)\)/)

      if (translateMatch && translateMatch.length >= 3) {
        pan.x = Number.parseFloat(translateMatch[1])
        pan.y = Number.parseFloat(translateMatch[2])
      }

      if (scaleMatch && scaleMatch.length >= 2) {
        const scaleValue = Number.parseFloat(scaleMatch[1])
        if (!isNaN(scaleValue) && scaleValue !== 0) {
          zoom = scaleValue
        }
      }
    }

    // 计算客户端坐标
    const clientX = svgX * zoom + pan.x + svgRect.left
    const clientY = svgY * zoom + pan.y + svgRect.top

    // 添加调试信息
    console.warn('[svgToClientPoint]', {
      svgX,
      svgY,
      svgRect,
      pan,
      zoom,
      clientX,
      clientY,
      element: element.tagName,
      svgRoot: svgRoot.tagName,
    })

    // 返回变换后的点
    return new PointClass(clientX, clientY)
  }
  catch (error) {
    console.error('Coordinate conversion failed:', error)
    return null
  }
}

// -----------------------------------------------------------------------------
// Point Operation Functions
// -----------------------------------------------------------------------------

/**
 * Rotates a given point around a center point by a specified angle in degrees.
 *
 * @remarks
 * This function converts the angle to radians and then delegates to `pointUtilRotatePoint`
 * (from `./pointUtils.ts`).
 *
 * @param point - The {@link IPoint} to rotate.
 * @param center - The {@link IPoint} representing the center of rotation.
 * @param angleDegrees - The angle of rotation in degrees.
 * @returns A new {@link PointClass} instance representing the rotated point.
 */
export function rotatePointDegrees(
  point: IPoint, // Parameter type to IPoint
  center: IPoint, // Parameter type to IPoint
  angleDegrees: number,
): PointClass { // Return PointClass instance
  // Convert degrees to radians
  const angleRadians = (angleDegrees * Math.PI) / 180
  // commonRotatePoint from pointUtils now expects IPoint and returns PointClass
  return pointUtilRotatePoint(point, center, angleRadians)
}

/**
 * Translates (moves) a given point by specified x and y offsets.
 *
 * @param point - The {@link IPoint} to translate.
 * @param dx - The amount to translate along the x-axis.
 * @param dy - The amount to translate along the y-axis.
 * @returns A new {@link PointClass} instance representing the translated point.
 *          The z-coordinate, if present, is preserved.
 */
export function translatePoint(
  point: IPoint, // Parameter type to IPoint
  dx: number,
  dy: number,
): PointClass { // Return PointClass instance
  // Calculate translated position
  return new PointClass(
    point.x + dx,
    point.y + dy,
    point.z, // Preserve z-coordinate if present
  )
}

/**
 * Calculates the shortest distance from a point to a line segment.
 *
 * @remarks
 * The calculation involves projecting the point onto the line containing the segment.
 * If the projection falls within the segment, the distance is to this projection.
 * Otherwise, the distance is to the closest endpoint of the segment.
 * It uses `pointUtilCalculateDistance` (from `./pointUtils.ts`) for distance calculations.
 *
 * @param point - The {@link IPoint} from which to calculate the distance.
 * @param lineStart - The start {@link IPoint} of the line segment.
 * @param lineEnd - The end {@link IPoint} of the line segment.
 * @returns The shortest distance from the point to the line segment.
 */
export function calculatePointToLineDistance(
  point: IPoint, // Parameter type to IPoint
  lineStart: IPoint, // Parameter type to IPoint
  lineEnd: IPoint, // Parameter type to IPoint
): number {
  // Calculate squared length of the line segment
  const lengthSquared
    = (lineEnd.x - lineStart.x) * (lineEnd.x - lineStart.x)
      + (lineEnd.y - lineStart.y) * (lineEnd.y - lineStart.y)

  // If the line segment is actually a point, return the distance to that point
  if (lengthSquared === 0) {
    return pointUtilCalculateDistance(point, lineStart) // Use pointUtilCalculateDistance
  }

  // Calculate projection ratio t
  let t
    = ((point.x - lineStart.x) * (lineEnd.x - lineStart.x)
      + (point.y - lineStart.y) * (lineEnd.y - lineStart.y))
    / lengthSquared

  // Clamp t to [0, 1] to ensure we get a point on the line segment
  t = Math.max(0, Math.min(1, t))

  // Calculate projection point
  const projectionX = lineStart.x + t * (lineEnd.x - lineStart.x)
  const projectionY = lineStart.y + t * (lineEnd.y - lineStart.y)

  // Return distance from point to projection point
  return pointUtilCalculateDistance(point, new PointClass(projectionX, projectionY)) // Use pointUtilCalculateDistance and PointClass
}

// Export backward compatible function names
export { calculatePointToLineDistance as pointToLineDistance }
