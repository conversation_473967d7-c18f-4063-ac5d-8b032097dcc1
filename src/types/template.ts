/**
 * Template System Types
 *
 * Type definitions for the template system that allows users to start
 * with pre-configured designs or blank canvases.
 */

import type { Element } from './core/elementDefinitions'

/**
 * Template metadata information
 */
export interface TemplateMetadata {
  /** Unique template identifier */
  id: string
  /** Display name for the template */
  name: string
  /** Template description */
  description: string
  /** Template category */
  category: TemplateCategory
  /** Template tags for filtering */
  tags: string[]
  /** Preview image URL */
  previewImage: string
  /** Template author */
  author?: string
  /** Creation date */
  createdAt: Date
  /** Last modified date */
  updatedAt: Date
  /** Template version */
  version: string
  /** Whether template is featured */
  featured?: boolean
  /** Difficulty level */
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  /** Estimated completion time in minutes */
  estimatedTime?: number
}

/**
 * Template categories
 */
export type TemplateCategory =
  | 'residential'
  | 'commercial'
  | 'office'
  | 'retail'
  | 'hospitality'
  | 'healthcare'
  | 'education'
  | 'industrial'
  | 'outdoor'
  | 'custom'

/**
 * Template state structure
 */
export interface TemplateState {
  /** Array of elements in the template */
  shapes: Element[]
  /** Selected element IDs */
  selectedShapeIds: string[]
  /** Canvas settings */
  canvas?: {
    /** Canvas dimensions */
    width?: number
    height?: number
    /** Background color */
    backgroundColor?: string
    /** Grid settings */
    grid?: {
      enabled: boolean
      size: number
      color: string
    }
  }
  /** Layer configuration */
  layers?: {
    /** Current active layer */
    activeLayer?: string
    /** Layer visibility settings */
    visibility?: Record<string, boolean>
  }
}

/**
 * Complete template definition
 */
export interface Template {
  /** Template metadata */
  metadata: TemplateMetadata
  /** Template state/content */
  state: TemplateState
  /** Template version for compatibility */
  version: number
}

/**
 * Template filter options
 */
export interface TemplateFilter {
  /** Filter by category */
  category?: TemplateCategory
  /** Filter by tags */
  tags?: string[]
  /** Filter by difficulty */
  difficulty?: ('beginner' | 'intermediate' | 'advanced')[]
  /** Filter by featured status */
  featured?: boolean
  /** Search query */
  search?: string
}

/**
 * Template sort options
 */
export type TemplateSortBy =
  | 'name'
  | 'createdAt'
  | 'updatedAt'
  | 'difficulty'
  | 'estimatedTime'
  | 'featured'

export type TemplateSortOrder = 'asc' | 'desc'

/**
 * Template service interface
 */
export interface TemplateService {
  /** Get all available templates */
  getTemplates: () => Promise<Template[]>
  /** Get template by ID */
  getTemplate: (id: string) => Promise<Template | null>
  /** Get filtered templates */
  getFilteredTemplates: (filter: TemplateFilter) => Promise<Template[]>
  /** Load template into application */
  loadTemplate: (template: Template) => Promise<void>
  /** Create new template from current state */
  saveAsTemplate: (metadata: Omit<TemplateMetadata, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Template>
  /** Delete template */
  deleteTemplate: (id: string) => Promise<void>
  /** Get template categories */
  getCategories: () => TemplateCategory[]
}

/**
 * Template selection state
 */
export interface TemplateSelectionState {
  /** Available templates */
  templates: Template[]
  /** Currently selected template */
  selectedTemplate: Template | null
  /** Loading state */
  isLoading: boolean
  /** Error state */
  error: string | null
  /** Current filter */
  filter: TemplateFilter
  /** Current sort settings */
  sort: {
    by: TemplateSortBy
    order: TemplateSortOrder
  }
  /** Whether to show blank canvas option */
  showBlankOption: boolean
}

/**
 * Template hook return type
 */
export interface UseTemplateReturn {
  /** Template selection state */
  state: TemplateSelectionState
  /** Load templates */
  loadTemplates: () => Promise<void>
  /** Select template */
  selectTemplate: (template: Template | null) => void
  /** Apply filter */
  applyFilter: (filter: TemplateFilter) => void
  /** Change sort */
  changeSort: (by: TemplateSortBy, order: TemplateSortOrder) => void
  /** Start with selected template */
  startWithTemplate: () => Promise<void>
  /** Start with blank canvas */
  startBlank: () => Promise<void>
}

/**
 * Template events
 */
export type TemplateEvent =
  | { type: 'template:loaded', payload: { template: Template } }
  | { type: 'template:selected', payload: { template: Template } }
  | { type: 'template:applied', payload: { template: Template } }
  | { type: 'template:created', payload: { template: Template } }
  | { type: 'template:deleted', payload: { templateId: string } }
