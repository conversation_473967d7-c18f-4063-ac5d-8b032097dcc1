/**
 * Persistent Settings Hook
 *
 * This module provides a React hook for managing persistent application settings.
 * It handles loading, saving, and synchronizing settings with local storage,
 * and provides methods for managing element-specific configuration.
 *
 * @module hooks/usePersistentSettings
 */

import type { InitialElementProperties } from '@/config/defaultElementSettings'
import type { ElementType } from '@/types/core/elementDefinitions'
import { useCallback, useState } from 'react'
import { defaultElementSettings as compileTimeDefaults } from '@/config/defaultElementSettings'

const LOCAL_STORAGE_KEY = 'userElementSettings'

type SettingsStore = Partial<Record<ElementType, InitialElementProperties>>

export function usePersistentSettings() {
  const [effectiveSettings, setEffectiveSettings] = useState<SettingsStore>(() => {
    const storedSettingsJson = localStorage.getItem(LOCAL_STORAGE_KEY)
    let storedSettings: SettingsStore = {}
    if (storedSettingsJson !== null && storedSettingsJson !== '') {
      try {
        storedSettings = JSON.parse(storedSettingsJson) as SettingsStore
      }
      catch (e) {
        console.error('Failed to parse user settings from localStorage', e)
      }
    }
    // Merge compile-time defaults with stored settings
    // For each element type, merge its properties
    const mergedSettings: SettingsStore = {};
    (Object.keys(compileTimeDefaults) as ElementType[]).forEach((type) => {
      mergedSettings[type] = {
        ...(compileTimeDefaults[type] || {}), // Ensure compileTimeDefaults[type] is an object
        ...(storedSettings[type] || {}), // Ensure storedSettings[type] is an object
      }
    });
    // Also include any types user might have stored that are not in compileTimeDefaults
    (Object.keys(storedSettings) as ElementType[]).forEach((type) => {
      if (!mergedSettings[type]) {
        mergedSettings[type] = storedSettings[type]
      }
    })
    return mergedSettings
  })

  const saveSettings = useCallback((newSettings: SettingsStore) => {
    try {
      // Create a full settings object to save, preserving existing customizations
      const comprehensiveNewSettings: SettingsStore = {};
      (Object.keys(compileTimeDefaults) as ElementType[]).forEach((type) => {
        comprehensiveNewSettings[type] = {
          ...(compileTimeDefaults[type] || {}),
          ...(effectiveSettings[type] || {}), // Preserve existing user customizations
          ...(newSettings[type] || {}), // Apply new changes
        }
      });
      // Also include any types in newSettings not in compileTimeDefaults (e.g. if user added one, though UI doesn't support this)
      (Object.keys(newSettings) as ElementType[]).forEach((type) => {
        if (!comprehensiveNewSettings[type]) {
          comprehensiveNewSettings[type] = {
            ...(effectiveSettings[type] || {}), // Preserve existing customizations
            ...newSettings[type], // Apply new changes
          }
        }
      })

      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(comprehensiveNewSettings))
      setEffectiveSettings(comprehensiveNewSettings) // Update runtime state to the fully merged new settings
    }
    catch (e) {
      console.error('Failed to save user settings to localStorage', e)
    }
  }, [effectiveSettings])

  const getSettingsForType = useCallback((elementType: ElementType): InitialElementProperties => {
    // Return a merged view: component's current state for this type, over compile-time defaults
    return {
      ...(compileTimeDefaults[elementType] || {}),
      ...(effectiveSettings[elementType] || {}),
    }
  }, [effectiveSettings])

  const resetAllSettings = useCallback(() => {
    localStorage.removeItem(LOCAL_STORAGE_KEY)
    const newDefaults = JSON.parse(JSON.stringify(compileTimeDefaults)) as SettingsStore // Deep copy
    setEffectiveSettings(newDefaults)
  }, [])

  // The initial state is already properly loaded in useState initializer above
  // No need for additional useEffect since localStorage is synchronous

  return { effectiveSettings, saveSettings, getSettingsForType, resetAllSettings }
}
