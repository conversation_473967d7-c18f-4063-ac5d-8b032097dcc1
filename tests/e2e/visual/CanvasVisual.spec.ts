import { test, expect } from '@playwright/test';

/**
 * Visual regression test suite for the Canvas Application.
 * These tests verify the visual appearance of the application.
 */
test.describe('Canvas Visual Tests', () => {
  /**
   * Setup for each test.
   * Navigates to the application before each test.
   */
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the canvas to be loaded
    await page.waitForSelector('#canvas-container');
  });

  /**
   * Tests the initial appearance of the canvas.
   * Verifies that the canvas is rendered correctly.
   */
  test('Initial canvas appearance', async ({ page }) => {
    // Take a screenshot of the canvas
    await expect(page.locator('#canvas-container')).toHaveScreenshot('initial-canvas.png');
  });

  /**
   * Tests the appearance of the toolbar.
   * Verifies that the toolbar is rendered correctly.
   */
  test('Toolbar appearance', async ({ page }) => {
    // Take a screenshot of the toolbar
    await expect(page.locator('.toolbar')).toHaveScreenshot('toolbar.png');
  });

  /**
   * Tests the appearance of the properties panel.
   * Verifies that the properties panel is rendered correctly.
   */
  test('Properties panel appearance', async ({ page }) => {
    // Take a screenshot of the properties panel
    await expect(page.locator('.properties-panel')).toHaveScreenshot('properties-panel.png');
  });

  /**
   * Tests the appearance of a rectangle on the canvas.
   * Verifies that a rectangle is rendered correctly.
   */
  test('Rectangle appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Take a screenshot of the canvas with the rectangle
    await expect(page.locator('#canvas-container')).toHaveScreenshot('rectangle.png');
  });

  /**
   * Tests the appearance of an ellipse on the canvas.
   * Verifies that an ellipse is rendered correctly.
   */
  test('Ellipse appearance', async ({ page }) => {
    // Create an ellipse
    await page.click('.tool-button[data-tool="ellipse"]');
    await page.mouse.move(200, 200);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Take a screenshot of the canvas with the ellipse
    await expect(page.locator('#canvas-container')).toHaveScreenshot('ellipse.png');
  });

  /**
   * Tests the appearance of a polygon on the canvas.
   * Verifies that a polygon is rendered correctly.
   */
  test('Polygon appearance', async ({ page }) => {
    // Create a polygon
    await page.click('.tool-button[data-tool="polygon"]');
    
    await page.mouse.move(100, 100);
    await page.mouse.click();
    
    await page.mouse.move(200, 100);
    await page.mouse.click();
    
    await page.mouse.move(150, 200);
    await page.mouse.click();
    
    // Double-click to finish the polygon
    await page.mouse.move(100, 100);
    await page.mouse.dblclick();
    
    // Take a screenshot of the canvas with the polygon
    await expect(page.locator('#canvas-container')).toHaveScreenshot('polygon.png');
  });

  /**
   * Tests the appearance of a line on the canvas.
   * Verifies that a line is rendered correctly.
   */
  test('Line appearance', async ({ page }) => {
    // Create a line
    await page.click('.tool-button[data-tool="line"]');
    
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 300);
    await page.mouse.up();
    
    // Take a screenshot of the canvas with the line
    await expect(page.locator('#canvas-container')).toHaveScreenshot('line.png');
  });

  /**
   * Tests the appearance of text on the canvas.
   * Verifies that text is rendered correctly.
   */
  test('Text appearance', async ({ page }) => {
    // Create text
    await page.click('.tool-button[data-tool="text"]');
    
    await page.mouse.move(200, 200);
    await page.mouse.click();
    
    // Type some text
    await page.keyboard.type('Hello, World!');
    
    // Press Enter to finish editing
    await page.keyboard.press('Enter');
    
    // Take a screenshot of the canvas with the text
    await expect(page.locator('#canvas-container')).toHaveScreenshot('text.png');
  });

  /**
   * Tests the appearance of a selected shape.
   * Verifies that a selected shape is rendered correctly.
   */
  test('Selected shape appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Select the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Click on the rectangle to select it
    await page.mouse.move(200, 175);
    await page.mouse.click();
    
    // Take a screenshot of the canvas with the selected rectangle
    await expect(page.locator('#canvas-container')).toHaveScreenshot('selected-shape.png');
  });

  /**
   * Tests the appearance of a group.
   * Verifies that a group is rendered correctly.
   */
  test('Group appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(200, 200);
    await page.mouse.up();
    
    // Create an ellipse
    await page.click('.tool-button[data-tool="ellipse"]');
    await page.mouse.move(300, 300);
    await page.mouse.down();
    await page.mouse.move(350, 350);
    await page.mouse.up();
    
    // Select the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Select both shapes (using shift key for multi-select)
    await page.mouse.move(150, 150);
    await page.mouse.click();
    
    await page.keyboard.down('Shift');
    await page.mouse.move(325, 325);
    await page.mouse.click();
    await page.keyboard.up('Shift');
    
    // Group the shapes
    await page.keyboard.press('Control+G');
    
    // Take a screenshot of the canvas with the group
    await expect(page.locator('#canvas-container')).toHaveScreenshot('group.png');
  });

  /**
   * Tests the appearance of a rotated shape.
   * Verifies that a rotated shape is rendered correctly.
   */
  test('Rotated shape appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Select the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Click on the rectangle to select it
    await page.mouse.move(200, 175);
    await page.mouse.click();
    
    // Wait for the rotation handle to appear
    await page.waitForSelector('.rotation-handle');
    
    // Rotate the rectangle using the rotation handle
    const rotationHandle = await page.locator('.rotation-handle');
    const handleBox = await rotationHandle.boundingBox();
    
    await page.mouse.move(handleBox.x + handleBox.width / 2, handleBox.y + handleBox.height / 2);
    await page.mouse.down();
    await page.mouse.move(handleBox.x + 50, handleBox.y - 50);
    await page.mouse.up();
    
    // Take a screenshot of the canvas with the rotated rectangle
    await expect(page.locator('#canvas-container')).toHaveScreenshot('rotated-shape.png');
  });

  /**
   * Tests the appearance of a scaled shape.
   * Verifies that a scaled shape is rendered correctly.
   */
  test('Scaled shape appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Select the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Click on the rectangle to select it
    await page.mouse.move(200, 175);
    await page.mouse.click();
    
    // Wait for the resize handles to appear
    await page.waitForSelector('.resize-handle');
    
    // Resize the rectangle using the bottom-right handle
    const bottomRightHandle = await page.locator('.resize-handle.bottom-right');
    const handleBox = await bottomRightHandle.boundingBox();
    
    await page.mouse.move(handleBox.x + handleBox.width / 2, handleBox.y + handleBox.height / 2);
    await page.mouse.down();
    await page.mouse.move(handleBox.x + 100, handleBox.y + 50);
    await page.mouse.up();
    
    // Take a screenshot of the canvas with the scaled rectangle
    await expect(page.locator('#canvas-container')).toHaveScreenshot('scaled-shape.png');
  });

  /**
   * Tests the appearance of the color picker.
   * Verifies that the color picker is rendered correctly.
   */
  test('Color picker appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Select the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Click on the rectangle to select it
    await page.mouse.move(200, 175);
    await page.mouse.click();
    
    // Click on the fill color input to open the color picker
    await page.click('.properties-panel input[name="fill"]');
    
    // Wait for the color picker to appear
    await page.waitForSelector('.color-picker');
    
    // Take a screenshot of the color picker
    await expect(page.locator('.color-picker')).toHaveScreenshot('color-picker.png');
  });

  /**
   * Tests the appearance of the zoomed canvas.
   * Verifies that the zoomed canvas is rendered correctly.
   */
  test('Zoomed canvas appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Zoom in
    await page.click('.zoom-in-button');
    
    // Take a screenshot of the zoomed canvas
    await expect(page.locator('#canvas-container')).toHaveScreenshot('zoomed-canvas.png');
  });

  /**
   * Tests the appearance of the panned canvas.
   * Verifies that the panned canvas is rendered correctly.
   */
  test('Panned canvas appearance', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Select the pan tool
    await page.click('.tool-button[data-tool="pan"]');
    
    // Pan the canvas
    await page.mouse.move(200, 200);
    await page.mouse.down();
    await page.mouse.move(300, 300);
    await page.mouse.up();
    
    // Take a screenshot of the panned canvas
    await expect(page.locator('#canvas-container')).toHaveScreenshot('panned-canvas.png');
  });

  /**
   * Tests the appearance of the grid.
   * Verifies that the grid is rendered correctly.
   */
  test('Grid appearance', async ({ page }) => {
    // Toggle the grid
    await page.click('.toggle-grid-button');
    
    // Take a screenshot of the canvas with the grid
    await expect(page.locator('#canvas-container')).toHaveScreenshot('grid.png');
  });

  /**
   * Tests the appearance of the rulers.
   * Verifies that the rulers are rendered correctly.
   */
  test('Rulers appearance', async ({ page }) => {
    // Toggle the rulers
    await page.click('.toggle-rulers-button');
    
    // Take a screenshot of the canvas with the rulers
    await expect(page.locator('#canvas-container')).toHaveScreenshot('rulers.png');
  });

  /**
   * Tests the appearance of the snap to grid feature.
   * Verifies that shapes snap to the grid correctly.
   */
  test('Snap to grid appearance', async ({ page }) => {
    // Toggle the grid
    await page.click('.toggle-grid-button');
    
    // Toggle snap to grid
    await page.click('.toggle-snap-button');
    
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(105, 105);
    await page.mouse.down();
    await page.mouse.move(295, 245);
    await page.mouse.up();
    
    // Take a screenshot of the canvas with the snapped rectangle
    await expect(page.locator('#canvas-container')).toHaveScreenshot('snap-to-grid.png');
  });

  /**
   * Tests the appearance of the dark theme.
   * Verifies that the dark theme is applied correctly.
   */
  test('Dark theme appearance', async ({ page }) => {
    // Toggle the theme
    await page.click('.toggle-theme-button');
    
    // Take a screenshot of the application with the dark theme
    await expect(page).toHaveScreenshot('dark-theme.png');
  });

  /**
   * Tests the appearance of the application on mobile.
   * Verifies that the application is responsive.
   */
  test('Mobile appearance', async ({ page }) => {
    // Set the viewport to mobile size
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Take a screenshot of the application on mobile
    await expect(page).toHaveScreenshot('mobile.png');
  });

  /**
   * Tests the appearance of the application on tablet.
   * Verifies that the application is responsive.
   */
  test('Tablet appearance', async ({ page }) => {
    // Set the viewport to tablet size
    await page.setViewportSize({ width: 768, height: 1024 });
    
    // Take a screenshot of the application on tablet
    await expect(page).toHaveScreenshot('tablet.png');
  });
});
