/**
 * Compute Status Types
 *
 * Defines the possible states of geometry calculations for elements.
 * Used to provide visual feedback about calculation success, errors, and warnings.
 *
 * @module types/core/computeStatus
 */

/**
 * Enumeration of possible computation states.
 *
 * @remarks
 * These states are used to provide visual feedback in the UI:
 * - SUCCESS: Green dot - calculation completed successfully
 * - ERROR: Red dot - calculation failed due to an error
 * - WARNING: Yellow dot - calculation completed with warnings
 * - PENDING: Gray dot - calculation is in progress
 * - NONE: No dot - no calculation performed or available
 */
export enum ComputeStatus {
  /** Calculation completed successfully */
  SUCCESS = 'success',
  /** Calculation failed due to an error */
  ERROR = 'error',
  /** Calculation completed with warnings */
  WARNING = 'warning',
  /** Calculation is in progress */
  PENDING = 'pending',
  /** No calculation performed or available */
  NONE = 'none',
}

/**
 * Interface for compute status information including optional error details.
 */
export interface ComputeStatusInfo {
  /** The current status of the computation */
  status: ComputeStatus
  /** Optional error message if status is ERROR */
  error?: string
  /** Optional warning message if status is WARNING */
  warning?: string
  /** Optional timestamp when the status was last updated */
  timestamp?: number
}

/**
 * Type guard to check if a value is a valid ComputeStatus.
 */
export function isComputeStatus(value: unknown): value is ComputeStatus {
  return typeof value === 'string' && Object.values(ComputeStatus).includes(value as ComputeStatus)
}

/**
 * Get the CSS color class for a compute status.
 */
export function getStatusColor(status: ComputeStatus): string {
  switch (status) {
    case ComputeStatus.SUCCESS:
      return 'text-green-600'
    case ComputeStatus.ERROR:
      return 'text-red-600'
    case ComputeStatus.WARNING:
      return 'text-yellow-600'
    case ComputeStatus.PENDING:
      return 'text-gray-400'
    case ComputeStatus.NONE:
    default:
      return 'text-gray-300'
  }
}

/**
 * Get the background color class for a compute status indicator.
 */
export function getStatusBgColor(status: ComputeStatus): string {
  switch (status) {
    case ComputeStatus.SUCCESS:
      return 'bg-green-600'
    case ComputeStatus.ERROR:
      return 'bg-red-600'
    case ComputeStatus.WARNING:
      return 'bg-yellow-600'
    case ComputeStatus.PENDING:
      return 'bg-gray-400'
    case ComputeStatus.NONE:
    default:
      return 'bg-gray-300'
  }
}

/**
 * Get a human-readable description for a compute status.
 */
export function getStatusDescription(status: ComputeStatus): string {
  switch (status) {
    case ComputeStatus.SUCCESS:
      return 'Calculation completed successfully'
    case ComputeStatus.ERROR:
      return 'Calculation failed'
    case ComputeStatus.WARNING:
      return 'Calculation completed with warnings'
    case ComputeStatus.PENDING:
      return 'Calculation in progress'
    case ComputeStatus.NONE:
    default:
      return 'No calculation available'
  }
}
