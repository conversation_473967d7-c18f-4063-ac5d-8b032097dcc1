# Editor Layout Component (`EditorLayout.tsx`)

`EditorLayout.tsx` is the main component responsible for rendering the entire editor interface of the RenoPilot.JS.Shapes2 application. It orchestrates various sub-components like the toolbar, canvas, properties panel, layer panel, and handles much of the editor's interactive logic and state management integration.

## Key Responsibilities

1.  **Overall UI Structure:**
    *   Defines the visual layout of the editor, arranging components such as:
        *   `Header`: For application title, global actions (e.g., save, load, export, settings).
        *   `Toolbar`: For selecting drawing tools, colors, stroke properties, etc.
        *   `CanvasRegion`: The main area where users draw and interact with shapes.
        *   `PropertiesPanel`: Displays and allows editing of properties for selected elements.
        *   `LayerPanel`: Manages layers and the Z-ordering of shapes.
        *   `StatusBar`/`Footer`: May display contextual information, zoom levels, or quick help.
        *   `Dialogs`: Manages various modal dialogs (e.g., settings, export options, confirmation dialogs).

2.  **Integration of Core Editor Components:**
    *   Renders and passes necessary props to specialized editor components like `Canvas`, `Toolbar`, `PropertiesPanel`, `LayerManager`, etc.

3.  **State Management Integration:**
    *   Heavily interacts with Zustand stores (`shapesStore`, `uiStore`, `canvasStore`, `layerStore`) to:
        *   Read application state (e.g., selected tool, selected shapes, zoom level, active layer).
        *   Dispatch actions to update state based on user interactions within the layout or its child components.
    *   Uses custom hooks that often encapsulate store interactions.

4.  **Event Handling and Coordination:**
    *   Manages global editor events, such as keyboard shortcuts (`useKeyboardShortcuts`).
    *   Coordinates interactions between different parts of the editor (e.g., selecting a tool in the `Toolbar` should change the interaction mode on the `Canvas`).

5.  **Feature Integration:**
    *   Integrates various editor functionalities through custom hooks and services:
        *   **Pan and Zoom:** (`usePanAndZoom` for canvas navigation).
        *   **Drag and Drop:** (`useDragAndDrop` for adding assets or elements).
        *   **Element Manipulation:** Logic for creating, selecting, moving, resizing, rotating elements (often delegated to `Canvas` and related services/hooks like `useElementActions`).
        *   **Undo/Redo:** (`useUndoRedo` for managing action history).
        *   **Settings Management:** (`usePersistentSettings` for loading/saving user preferences).
        *   **Export Functionality:** (`useExport` for exporting the canvas content).
        *   **Unit Conversion:** (`useUnitConversion` if the editor supports different measurement units).

6.  **Context Provision (Potentially):**
    *   Might provide React contexts specific to the editor environment if certain states or functions need to be accessible deeply within the editor component tree without excessive prop drilling.

## Core Hooks and Components Used (Illustrative)

*   **Layout Components (Shadcn/ui or custom):** `ResizablePanelGroup`, `ResizablePanel`, `ResizableHandle` for creating a flexible and responsive layout.
*   **Editor Specific Components:**
    *   `Header`: Custom component for the top bar.
    *   `Toolbar`: `src/components/toolbar/Toolbar.tsx`
    *   `CanvasRegion`: Wrapper for the main `Canvas` component.
    *   `Canvas`: `src/components/canvas/Canvas.tsx`
    *   `PropertiesPanel`: `src/components/property/PropertiesPanel.tsx`
    *   `LayerManager`: `src/components/layer/LayerManager.tsx`
    *   `Dialogs`: Various dialog components from `src/components/dialogs/` (e.g., `SettingsDialog`, `ExportDialog`).
    *   `AssetDrawer`: `src/components/drawer/AssetDrawer.tsx`
*   **Custom Hooks:**
    *   `useKeyboardShortcuts`: For handling global keyboard commands.
    *   `useUndoRedo`: For history management.
    *   `useElementActions`: For common actions on elements.
    *   `usePanAndZoom`: For canvas navigation.
    *   `useDragAndDrop`: For asset management.
    *   `usePersistentSettings`: For user preferences.
    *   `useExport`: For exporting functionality.
    *   `useUnitConversion`: For handling different units of measurement.
    *   Hooks interacting with Zustand stores (e.g., `useUiStore`, `useShapesStore`).
*   **Zustand Stores:** `shapesStore`, `uiStore`, `canvasStore`, `layerStore`.
*   **Event Bus (`appEventBus`):** For inter-component communication and decoupling.

## Conceptual Structure

```tsx
// src/components/layout/EditorLayout.tsx
import React from 'react';

// Layout & UI Primitives (e.g., from shadcn/ui)
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';

// Core Editor Components
import Header from './Header'; // Assuming a Header component
import Toolbar from '../toolbar/Toolbar';
import CanvasRegion from '../canvas/CanvasRegion'; // Wrapper for Canvas
import PropertiesPanel from '../property/PropertiesPanel';
import LayerManager from '../layer/LayerManager';
import AssetDrawer from '../drawer/AssetDrawer';
import SettingsDialog from '../dialogs/SettingsDialog';
import ExportDialog from '../dialogs/ExportDialog';
// ... other dialogs and components

// Hooks
import { useKeyboardShortcuts } from '@/hooks/useKeyboardShortcuts';
import { useUndoRedo } from '@/hooks/useUndoRedo';
import { usePersistentSettings } from '@/hooks/usePersistentSettings';
import { useUiStore, useShapesStore, useCanvasStore, useLayerStore } from '@/store';
// ... other relevant hooks

interface EditorLayoutProps {
  initialShapes?: Shape[]; // For loading templates or existing projects
}

const EditorLayout: React.FC<EditorLayoutProps> = ({ initialShapes }) => {
  // Initialize hooks
  useKeyboardShortcuts();
  const { undo, redo, canUndo, canRedo } = useUndoRedo();
  usePersistentSettings(); // Load settings

  // Access store states and actions
  const { isPropertiesPanelOpen, isLayerPanelOpen, isAssetDrawerOpen } = useUiStore(
    (state) => ({ 
      isPropertiesPanelOpen: state.isPropertiesPanelOpen, 
      isLayerPanelOpen: state.isLayerPanelOpen,
      isAssetDrawerOpen: state.isAssetDrawerOpen
      // ... other UI states
    })
  );

  // Effect to load initial shapes if provided (e.g., from a template)
  useEffect(() => {
    if (initialShapes) {
      // Logic to load these shapes into the shapesStore or ShapeRepository
      // This might be handled by a dedicated hook or service already
    }
  }, [initialShapes]);

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden bg-background text-foreground">
      <Header />
      <div className="flex flex-1 overflow-hidden">
        <Toolbar />
        <ResizablePanelGroup direction="horizontal" className="flex-1">
          {isAssetDrawerOpen && (
            <>
              <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
                <AssetDrawer />
              </ResizablePanel>
              <ResizableHandle withHandle />
            </>
          )}
          <ResizablePanel defaultSize={isAssetDrawerOpen ? (isPropertiesPanelOpen || isLayerPanelOpen ? 40 : 60) : (isPropertiesPanelOpen || isLayerPanelOpen ? 60 : 80)} minSize={30}>
            <CanvasRegion />
          </ResizablePanel>
          {(isPropertiesPanelOpen || isLayerPanelOpen) && <ResizableHandle withHandle />}
          {(isPropertiesPanelOpen || isLayerPanelOpen) && (
            <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
              {isPropertiesPanelOpen && <PropertiesPanel />}
              {isLayerPanelOpen && !isPropertiesPanelOpen && <LayerManager />}
              {isLayerPanelOpen && isPropertiesPanelOpen && (
                <ResizablePanelGroup direction="vertical">
                  <ResizablePanel><PropertiesPanel /></ResizablePanel>
                  <ResizableHandle withHandle />
                  <ResizablePanel><LayerManager /></ResizablePanel>
                </ResizablePanelGroup>
              )}
            </ResizablePanel>
          )}
        </ResizablePanelGroup>
      </div>
      {/* Global Dialogs */}
      <SettingsDialog />
      <ExportDialog />
      {/* ... other global UI elements like notifications or a status bar */}
    </div>
  );
};

export default EditorLayout;

```

## Significance

`EditorLayout.tsx` is the heart of the user-facing editor. It brings together all the individual pieces of the editing experience into a cohesive whole. Its proper functioning and organization are critical for:

*   **User Experience:** Providing an intuitive and efficient workspace.
*   **Maintainability:** A well-structured layout component makes it easier to manage and update different parts of the editor.
*   **Scalability:** Allows for the addition of new features and panels within a defined structure.

Understanding this component is essential for anyone looking to modify the editor's UI, add new tools or panels, or trace the flow of user interactions within the main editing environment.