/**
 * Appearance Selector Component
 *
 * Implements the standardized appearance layout for visual properties
 * according to the design document specifications.
 *
 * Layout:
 * - Colors: Fill and stroke color controls (left side)
 * - Properties: Stroke width, opacity controls (grid layout)
 * - Patterns: Pattern type selection and options (grid layout)
 *
 * Features:
 * - Color controls with visual color pickers
 * - Stroke properties with unit conversion
 * - Pattern/texture system with type-specific options
 * - Consistent styling with geometry components
 */

import type { PatternDefinition } from '@/types/core/element/elementPatternTypes'
import type { ShapeElement as ShapeModel } from '@/types/core/elementDefinitions'
import { useCallback } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useUnitConversion } from '@/hooks/useUnitConversion'
import { GeometrySection, PropertyField } from '../shared'

type ResolvedPatternType = 'lines' | 'circles' | 'paths' | 'none' | 'mixed-placeholder'

interface AppearanceSelectorProps {
  /** Array of currently selected elements */
  selectedElements: ShapeModel[]
  /** Function to get common property values across selected elements */
  getCommonValue: (propertyPath: string) => unknown
  /** Function to update property values for selected elements */
  updateProperty: (propertyPath: string, value: unknown) => void
  /** Common pattern definition across selected elements, or 'mixed' if different patterns */
  commonPattern: PatternDefinition | 'mixed' | undefined
  /** Whether any pattern is currently active on selected elements */
  isPatternActive: boolean
  /** Current pattern type selection value for the dropdown */
  currentPatternSelectValue: ResolvedPatternType
  /** Callback to handle pattern type changes */
  handlePatternChange: (newPattern: { textureType?: string }, fillColorLocal?: string) => void
  /** Callback to handle changes to specific pattern options */
  handlePatternOptionChange: (optionPath: string, value: unknown) => void
  /** Conversion factor from pixels to millimeters for unit conversion */
  pixelsPerMM: number
}

export function AppearanceSelector({
  selectedElements,
  getCommonValue,
  updateProperty,
  commonPattern,
  isPatternActive,
  currentPatternSelectValue,
  handlePatternChange,
  handlePatternOptionChange,
  pixelsPerMM,
}: AppearanceSelectorProps) {
  const { toDisplayUnit, toInternalUnit } = useUnitConversion(pixelsPerMM)

  const getValueForInput = useCallback((path: string): string => {
    try {
      const val = getCommonValue(path)
      if (path.endsWith('background') && (val === 'mixed' || val === null || val === undefined || val === '')) {
        return '#ffffff'
      }
      if (val === 'mixed' || val === null || val === undefined) {
        return ''
      }
      return String(val)
    }
    catch (error) {
      console.warn(`[AppearanceSelector] Error getting value for path "${path}":`, error)
      return ''
    }
  }, [getCommonValue])

  const handleDimensionChange = useCallback((
    propertyPath: string,
    value: string | number,
  ) => {
    const numericValue = typeof value === 'string' ? Number(value) : value
    if (!Number.isNaN(numericValue)) {
      const internalValue = toInternalUnit(numericValue)
      if (internalValue !== undefined) {
        updateProperty(propertyPath, internalValue)
      }
    }
  }, [updateProperty, toInternalUnit])

  const getDisplayValue = useCallback((path: string): string => {
    const val = getCommonValue(path)
    if (val === 'mixed' || val === null || val === undefined) {
      return ''
    }
    if (typeof val === 'number') {
      return toDisplayUnit(val)
    }
    return String(val)
  }, [getCommonValue, toDisplayUnit])

  if (selectedElements.length === 0) {
    return (
      <GeometrySection type="style" title="Colors" columns={1}>
        <p className="text-xs text-muted-foreground p-2">
          Select elements to edit appearance properties.
        </p>
      </GeometrySection>
    )
  }

  return (
    <>
      {/* Colors Section */}
      <GeometrySection type="style" title="Colors" columns={2}>
        <div className="space-y-1">
          <Label htmlFor="fillColor" className="text-sm font-medium">Fill Color</Label>
          <Input
            id="fillColor"
            type="color"
            value={getValueForInput('fill') || '#ffffff'}
            onChange={e => updateProperty('fill', e.target.value)}
            className="h-10 w-full"
          />
        </div>
        <div className="space-y-1">
          <Label htmlFor="strokeColor" className="text-sm font-medium">Stroke Color</Label>
          <Input
            id="strokeColor"
            type="color"
            value={getValueForInput('stroke') || '#000000'}
            onChange={e => updateProperty('stroke', e.target.value)}
            className="h-10 w-full"
          />
        </div>
      </GeometrySection>

      {/* Properties Section */}
      <GeometrySection type="dimensions" title="Properties" columns={2}>
        <PropertyField
          label="Stroke Width (mm)"
          type="number"
          value={getDisplayValue('strokeWidth')}
          onChange={value => handleDimensionChange('strokeWidth', value)}
          placeholder="1"
        />
        <PropertyField
          label="Opacity (%)"
          type="number"
          value={(() => {
            const opacity = getCommonValue('opacity')
            if (opacity === 'mixed' || opacity === null || opacity === undefined) {
              return ''
            }
            return String(Math.round((Number(opacity) || 1) * 100))
          })()}
          onChange={(value) => {
            const numericValue = Number(value)
            if (!Number.isNaN(numericValue)) {
              updateProperty('opacity', Math.max(0, Math.min(100, numericValue)) / 100)
            }
          }}
          placeholder="100"
        />
      </GeometrySection>

      {/* Patterns Section */}
      <GeometrySection type="special" title="Patterns" columns={1}>
        <div className="space-y-3">
          {/* Pattern Type Selection */}
          <div className="space-y-1">
            <Label htmlFor="patternType" className="text-sm font-medium">Pattern Type</Label>
            <Select
              value={currentPatternSelectValue}
              onValueChange={(value) => {
                if (value === 'none') {
                  handlePatternChange({ textureType: undefined })
                }
                else {
                  handlePatternChange({ textureType: value })
                }
              }}
            >
              <SelectTrigger id="patternType" className="h-10">
                <SelectValue placeholder="Select pattern type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                <SelectItem value="lines">Lines</SelectItem>
                <SelectItem value="circles">Circles</SelectItem>
                <SelectItem value="paths">Paths</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Pattern Options */}
          {isPatternActive && commonPattern !== 'mixed' && commonPattern?.textureType && (
            <div className="space-y-3 p-3 bg-muted/30 rounded-md">
              <h5 className="text-sm font-medium text-foreground">
                {commonPattern.textureType.charAt(0).toUpperCase() + commonPattern.textureType.slice(1)}
                {' '}
                Options
              </h5>

              {/* Pattern Color */}
              <div className="space-y-1">
                <Label className="text-sm font-medium">Pattern Color</Label>
                <Input
                  type="color"
                  value={(() => {
                    if (commonPattern.textureType === 'lines') {
                      return (commonPattern.linesOptions?.color !== undefined && commonPattern.linesOptions?.color !== null && commonPattern.linesOptions?.color !== '') ? commonPattern.linesOptions.color : '#333333'
                    }
                    else if (commonPattern.textureType === 'circles') {
                      return (commonPattern.circlesOptions?.fill !== undefined && commonPattern.circlesOptions?.fill !== null && commonPattern.circlesOptions?.fill !== '') ? commonPattern.circlesOptions.fill : '#333333'
                    }
                    else if (commonPattern.textureType === 'paths') {
                      return (commonPattern.pathsOptions?.fill !== undefined && commonPattern.pathsOptions?.fill !== null && commonPattern.pathsOptions?.fill !== '') ? commonPattern.pathsOptions.fill : '#333333'
                    }
                    return '#333333'
                  })()}
                  onChange={(e) => {
                    const newColor = e.target.value
                    if (commonPattern.textureType === 'lines') {
                      handlePatternOptionChange('linesOptions.color', newColor)
                      handlePatternOptionChange('linesOptions.stroke', newColor)
                    }
                    else if (commonPattern.textureType === 'circles') {
                      handlePatternOptionChange('circlesOptions.fill', newColor)
                    }
                    else if (commonPattern.textureType === 'paths') {
                      handlePatternOptionChange('pathsOptions.fill', newColor)
                    }
                  }}
                  className="h-10 w-full"
                />
              </div>

              {/* Pattern Size/Spacing */}
              <div className="grid grid-cols-2 gap-x-3">
                {commonPattern.textureType === 'lines' && (
                  <>
                    <PropertyField
                      label="Stroke Width (mm)"
                      type="number"
                      value={String(commonPattern.linesOptions?.strokeWidth ?? '')}
                      onChange={value => handlePatternOptionChange('linesOptions.strokeWidth', Math.max(0.1, Number(value)))}
                      placeholder="1"
                    />
                    <PropertyField
                      label="Size (mm)"
                      type="number"
                      value={String(commonPattern.linesOptions?.size ?? '')}
                      onChange={value => handlePatternOptionChange('linesOptions.size', Math.max(1, Math.round(Number(value))))}
                      placeholder="4"
                    />
                  </>
                )}

                {commonPattern.textureType === 'circles' && (
                  <>
                    <PropertyField
                      label="Radius (mm)"
                      type="number"
                      value={String((commonPattern.circlesOptions && typeof commonPattern.circlesOptions === 'object' && 'radius' in commonPattern.circlesOptions) ? (commonPattern.circlesOptions as { radius?: number }).radius ?? '' : '')}
                      onChange={value => handlePatternOptionChange('circlesOptions.radius', Math.max(1, Math.round(Number(value))))}
                      placeholder="2"
                    />
                    <PropertyField
                      label="Spacing (mm)"
                      type="number"
                      value={String((commonPattern.circlesOptions && typeof commonPattern.circlesOptions === 'object' && 'size' in commonPattern.circlesOptions) ? (commonPattern.circlesOptions as { size?: number }).size ?? '' : '')}
                      onChange={value => handlePatternOptionChange('circlesOptions.size', Math.max(1, Math.round(Number(value))))}
                      placeholder="5"
                    />
                  </>
                )}

                {commonPattern.textureType === 'paths' && (
                  <div className="col-span-2">
                    <PropertyField
                      label="Spacing (mm)"
                      type="number"
                      value={String((commonPattern.pathsOptions && typeof commonPattern.pathsOptions === 'object' && 'size' in commonPattern.pathsOptions) ? (commonPattern.pathsOptions as { size?: number }).size ?? '' : '')}
                      onChange={value => handlePatternOptionChange('pathsOptions.size', Math.max(1, Math.round(Number(value))))}
                      placeholder="5"
                    />
                  </div>
                )}
              </div>

              {/* Lines Orientation */}
              {commonPattern.textureType === 'lines' && (
                <div className="space-y-1">
                  <Label className="text-sm font-medium">Orientation</Label>
                  <Select
                    value={String(commonPattern.linesOptions?.orientation || 'horizontal')}
                    onValueChange={value => handlePatternOptionChange('linesOptions.orientation', value)}
                  >
                    <SelectTrigger className="h-10">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="horizontal">Horizontal</SelectItem>
                      <SelectItem value="vertical">Vertical</SelectItem>
                      <SelectItem value="diagonal">Diagonal (↘)</SelectItem>
                      <SelectItem value="diagonal-sw-ne">Diagonal (↗)</SelectItem>
                      <SelectItem value="radial">Radial</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Paths Type */}
              {commonPattern.textureType === 'paths' && (
                <div className="space-y-1">
                  <Label className="text-sm font-medium">Path Type</Label>
                  <Select
                    value={(commonPattern.pathsOptions && typeof commonPattern.pathsOptions === 'object' && 'd' in commonPattern.pathsOptions) ? String((commonPattern.pathsOptions as { d?: string }).d ?? 'squares') : 'squares'}
                    onValueChange={value => handlePatternOptionChange('pathsOptions.d', value)}
                  >
                    <SelectTrigger className="h-10">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="squares">Squares</SelectItem>
                      <SelectItem value="waves">Waves</SelectItem>
                      <SelectItem value="woven">Woven</SelectItem>
                      <SelectItem value="crosses">Crosses</SelectItem>
                      <SelectItem value="caps">Caps</SelectItem>
                      <SelectItem value="hexes">Hexes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          )}
        </div>
      </GeometrySection>
    </>
  )
}
