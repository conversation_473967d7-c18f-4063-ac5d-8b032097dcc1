/**
 * Defines types for polygon shape elements, including a base polygon and specific N-sided polygons.
 *
 * @remarks
 * This module provides the `Polygon` base interface and specialized interfaces for common polygons
 * like `Triangle`, `Quadrilateral`, etc., up to `Decagon`. These interfaces extend
 * {@link ShapeElement} to include properties specific to polygonal geometry.
 *
 * @module types/core/element/shape/polygonShapeTypes
 */
import type { ShapeElement } from '../../elementDefinitions' // Corrected import path
import type Point from '@/types/core/element/geometry/point'

/**
 * Represents a generic polygon shape, defined by a sequence of vertices and a number of sides.
 * A polygon is a closed shape with straight sides.
 *
 * @remarks
 * This interface defines the common properties for all polygons, typically supporting
 * from 3 (triangle) up to 10 (decagon) sides in this context. For specific polygon types
 * with a fixed number of sides, use their dedicated interfaces (e.g., {@link Triangle}, {@link Quadrilateral}).
 *
 * @see {@link Triangle} for a 3-sided polygon.
 * @see {@link Quadrilateral} for a 4-sided polygon.
 * @see {@link Pentagon} for a 5-sided polygon.
 * @see {@link Hexagon} for a 6-sided polygon.
 * @see {@link Heptagon} for a 7-sided polygon.
 * @see {@link Octagon} for an 8-sided polygon.
 * @see {@link Nonagon} for a 9-sided polygon.
 * @see {@link Decagon} for a 10-sided polygon.
 * @see {@link ShapeElement} for base shape properties.
 * @see {@link Point} for the structure of vertex coordinates.
 */
export interface Polygon extends ShapeElement {
  /**
   * An array of {@link Point} objects representing the vertices of the polygon,
   * typically in clockwise or counter-clockwise order.
   * @remarks
   * The number of points in this array must match the `sides` property.
   * The points must form a valid, non-self-intersecting polygon.
   * @throws {Error} Implementations might throw an error if the points count doesn't match `sides` or if the points form an invalid polygon.
   */
  points: Point[]

  /**
   * The number of sides (and vertices) of the polygon.
   * @remarks
   * This implementation context typically supports polygons with 3 to 10 sides.
   * For specific N-sided polygons, consider using the more specialized interfaces like {@link Triangle}, etc.
   * @throws {RangeError} Implementations might throw an error if the value is outside the supported range (e.g., 3-10).
   */
  sides: number

  /**
   * Indicates whether the polygon is regular, meaning all its sides are of equal length
   * and all its internal angles are equal.
   * @remarks
   * If `true`, the polygon is regular. If `false` or undefined, it is considered irregular.
   * @defaultValue false
   */
  isRegular: boolean
}

/**
 * Represents a triangle, a polygon with exactly three vertices.
 * @remarks This interface specializes the {@link Polygon} interface for 3-sided shapes.
 * The `points` property is typed as a tuple of three {@link Point}s.
 * @see {@link Polygon} for the base polygon interface.
 */
export interface Triangle extends Polygon {
  /** The three vertices of the triangle. */
  points: [Point, Point, Point]
  // sides property would be implicitly 3
}

/**
 * Represents a quadrilateral, a polygon with exactly four vertices.
 * @remarks This interface specializes the {@link Polygon} interface for 4-sided shapes.
 * The `points` property is typed as a tuple of four {@link Point}s.
 * @see {@link Polygon} for the base polygon interface.
 */
export interface Quadrilateral extends Polygon {
  /** The four vertices of the quadrilateral. */
  points: [Point, Point, Point, Point]
  // sides property would be implicitly 4
}

/**
 * Represents a pentagon, a polygon with exactly five vertices.
 * @remarks This interface specializes the {@link Polygon} interface for 5-sided shapes.
 * The `points` property is typed as a tuple of five {@link Point}s.
 * @see {@link Polygon} for the base polygon interface.
 */
export interface Pentagon extends Polygon {
  /** The five vertices of the pentagon. */
  points: [Point, Point, Point, Point, Point]
  // sides property would be implicitly 5
}

/**
 * Represents a hexagon, a polygon with exactly six vertices.
 * @remarks This interface specializes the {@link Polygon} interface for 6-sided shapes.
 * The `points` property is typed as a tuple of six {@link Point}s.
 * @see {@link Polygon} for the base polygon interface.
 */
export interface Hexagon extends Polygon {
  /** The six vertices of the hexagon. */
  points: [Point, Point, Point, Point, Point, Point]
  // sides property would be implicitly 6
}

/**
 * Represents a heptagon, a polygon with exactly seven vertices.
 * @remarks This interface specializes the {@link Polygon} interface for 7-sided shapes.
 * The `points` property is typed as a tuple of seven {@link Point}s.
 * @see {@link Polygon} for the base polygon interface.
 */
export interface Heptagon extends Polygon {
  /** The seven vertices of the heptagon. */
  points: [Point, Point, Point, Point, Point, Point, Point]
  // sides property would be implicitly 7
}

/**
 * Represents an octagon, a polygon with exactly eight vertices.
 * @remarks This interface specializes the {@link Polygon} interface for 8-sided shapes.
 * The `points` property is typed as a tuple of eight {@link Point}s.
 * @see {@link Polygon} for the base polygon interface.
 */
export interface Octagon extends Polygon {
  /** The eight vertices of the octagon. */
  points: [Point, Point, Point, Point, Point, Point, Point, Point]
  // sides property would be implicitly 8
}

/**
 * Represents a nonagon, a polygon with exactly nine vertices.
 * @remarks This interface specializes the {@link Polygon} interface for 9-sided shapes.
 * The `points` property is typed as a tuple of nine {@link Point}s.
 * @see {@link Polygon} for the base polygon interface.
 */
export interface Nonagon extends Polygon {
  /** The nine vertices of the nonagon. */
  points: [Point, Point, Point, Point, Point, Point, Point, Point, Point]
  // sides property would be implicitly 9
}

/**
 * Represents a decagon, a polygon with exactly ten vertices.
 * @remarks This interface specializes the {@link Polygon} interface for 10-sided shapes.
 * The `points` property is typed as a tuple of ten {@link Point}s.
 * @see {@link Polygon} for the base polygon interface.
 */
export interface Decagon extends Polygon {
  /** The ten vertices of the decagon. */
  points: [Point, Point, Point, Point, Point, Point, Point, Point, Point, Point]
  // sides property would be implicitly 10
}
