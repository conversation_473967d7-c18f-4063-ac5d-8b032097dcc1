import type { PerimeterCalculatorStrategy } from '@/types/core/compute/perimeterComputeTypes'
import type { Element } from '@/types/core/elementDefinitions'
import { describe, expect, it } from 'vitest'

describe('perimeter Calculator Strategy Interface', () => {
  it('should define the required methods for perimeter calculation strategies', () => {
    // Create a mock implementation of PerimeterCalculatorStrategy
    class MockPerimeterCalculator implements PerimeterCalculatorStrategy {
      calculatePerimeter(element: Element): number {
        // Simple mock implementation
        return 100
      }

      getElementType(): string {
        return 'mock-element'
      }
    }

    const calculator = new MockPerimeterCalculator()

    // Test the interface implementation
    expect(typeof calculator.calculatePerimeter).toBe('function')
    expect(typeof calculator.getElementType).toBe('function')

    // Test the mock implementation
    const mockElement = { id: 'test-element' } as Element
    expect(calculator.calculatePerimeter(mockElement)).toBe(100)
    expect(calculator.getElementType()).toBe('mock-element')
  })

  it('should allow implementation for different shape types', () => {
    // Create a mock rectangle perimeter calculator
    class MockRectanglePerimeterCalculator implements PerimeterCalculatorStrategy {
      calculatePerimeter(element: Element): number {
        // Mock implementation for rectangles
        return 40 // 2 * (width + height) = 2 * (10 + 10) = 40
      }

      getElementType(): string {
        return 'rectangle'
      }
    }

    // Create a mock circle perimeter calculator
    class MockCirclePerimeterCalculator implements PerimeterCalculatorStrategy {
      calculatePerimeter(element: Element): number {
        // Mock implementation for circles
        return 31.4 // 2 * π * r = 2 * π * 5 ≈ 31.4
      }

      getElementType(): string {
        return 'circle'
      }
    }

    const rectangleCalculator = new MockRectanglePerimeterCalculator()
    const circleCalculator = new MockCirclePerimeterCalculator()

    // Test the implementations
    const mockElement = { id: 'test-element' } as Element

    expect(rectangleCalculator.calculatePerimeter(mockElement)).toBe(40)
    expect(rectangleCalculator.getElementType()).toBe('rectangle')

    expect(circleCalculator.calculatePerimeter(mockElement)).toBe(31.4)
    expect(circleCalculator.getElementType()).toBe('circle')
  })

  it('should allow for complex perimeter calculation implementations', () => {
    // Create a mock implementation with more complex logic
    class MockComplexPerimeterCalculator implements PerimeterCalculatorStrategy {
      calculatePerimeter(element: Element): number {
        // Mock implementation that simulates calculating perimeter for a polygon
        const mockPoints = [
          { x: 0, y: 0 },
          { x: 10, y: 0 },
          { x: 10, y: 10 },
          { x: 0, y: 10 },
        ]

        // Calculate perimeter by summing the distances between consecutive points
        let perimeter = 0
        for (let i = 0; i < mockPoints.length; i++) {
          const p1 = mockPoints[i]
          const p2 = mockPoints[(i + 1) % mockPoints.length] // Wrap around to the first point

          // Calculate distance between points
          const dx = p2.x - p1.x
          const dy = p2.y - p1.y
          perimeter += Math.sqrt(dx * dx + dy * dy)
        }

        return perimeter
      }

      getElementType(): string {
        return 'polygon'
      }
    }

    const complexCalculator = new MockComplexPerimeterCalculator()

    // Test the implementation
    const mockElement = { id: 'test-element' } as Element

    // For a 10x10 square, perimeter should be 40
    expect(complexCalculator.calculatePerimeter(mockElement)).toBe(40)
    expect(complexCalculator.getElementType()).toBe('polygon')
  })

  it('should allow for error handling in implementations', () => {
    // Create a mock perimeter calculator with error handling
    class MockErrorHandlingCalculator implements PerimeterCalculatorStrategy {
      calculatePerimeter(element: Element): number {
        // Check if element has required properties
        if (!element?.id) {
          throw new Error('Invalid element')
        }

        // Mock implementation
        return 50
      }

      getElementType(): string {
        return 'error-handling-element'
      }
    }

    const errorHandlingCalculator = new MockErrorHandlingCalculator()

    // Test valid case
    const validElement = { id: 'test-element' } as Element
    expect(errorHandlingCalculator.calculatePerimeter(validElement)).toBe(50)

    // Test error case
    const invalidElement = {} as Element
    expect(() => errorHandlingCalculator.calculatePerimeter(invalidElement)).toThrow('Invalid element')
  })
})
