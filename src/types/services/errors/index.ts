/**
 * Error Types
 *
 * This file defines the core error types used in the error service.
 */

// Core error enum for error categorization
export enum ErrorType {
  Validation = 'VALIDATION',
  Runtime = 'RUNTIME',
  Network = 'NETWORK',
  Fatal = 'FATAL',
  Warning = 'WARNING',
  Info = 'INFO',
  // Added missing types based on usage in strategies
  InvalidParameter = 'INVALID_PARAMETER',
  InvalidElementType = 'INVALID_ELEMENT_TYPE',
  ComputationError = 'COMPUTATION_ERROR', // From LineBoundingBoxStrategy (original)
  Configuration = 'CONFIGURATION', // From ComputeFacade (original)
  CoordinatorShapeNotFound = 'COORDINATOR_SHAPE_NOT_FOUND', // From ComputeFacade
  CoordinatorOperationFailed = 'COORDINATOR_OPERATION_FAILED', // From ComputeFacade
  FactoryFailed = 'FACTORY_FAILED', // From ComputeFacade
  InvalidPayload = 'INVALID_PAYLOAD', // From ComputeFacade
  ComputeBoundsError = 'COMPUTE_BOUNDS_ERROR', // From ComputeFacade
  NotFound = 'NOT_FOUND',
  UnknownError = 'UNKNOWN_ERROR',
}

// Error severity levels
export enum ErrorSeverity {
  Low = 'LOW',
  Medium = 'MEDIUM',
  High = 'HIGH',
  Critical = 'CRITICAL',
}

// Error context information
export interface ErrorContext {
  component?: string
  operation?: string
  target?: string
  metadata?: Record<string, unknown>
  recoverable?: boolean
  timestamp?: number
  stack?: string // Added stack trace
}

// Core error class interface
export interface ICoreError extends Error {
  type: ErrorType
  severity: ErrorSeverity
  context?: ErrorContext
}

// Error factory function type
export type ErrorFactory = (
  type: ErrorType,
  message: string,
  severity?: ErrorSeverity,
  context?: ErrorContext
) => ICoreError

// Specialized error interfaces
export interface IFactoryError extends ICoreError {}
export interface IValidatorError extends ICoreError {}
export interface IComputeError extends ICoreError {}
export interface IValidationResultError extends IValidatorError {
  validationErrors: unknown[]
}
export interface IUnsupportedElementTypeError extends ICoreError {}
export interface IUnsupportedOperationError extends ICoreError {}
export interface IElementNotFoundError extends ICoreError {}
