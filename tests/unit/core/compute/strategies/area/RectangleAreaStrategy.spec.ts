import type { Element } from '@/types/core/elementDefinitions'
import { describe, expect, it } from 'vitest'
import { RectangleAreaStrategy } from '@/core/compute/strategies/area/RectangleAreaStrategy'
import { CoreError } from '@/services/system/error-service/coreError'
import { ElementType } from '@/types/core/elementDefinitions'

// Mock non-rectangle element
class MockCircle {
  id = 'mock-circle'
  type = 'CIRCLE'
  visible = true
  locked = false
  metadata = {}
}

/**
 * Unit tests for the RectangleAreaStrategy class
 */
describe('rectangleAreaStrategy', () => {
  let strategy: RectangleAreaStrategy

  beforeEach(() => {
    strategy = new RectangleAreaStrategy()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe(ElementType.RECTANGLE)
  })

  it('should calculate area correctly', () => {
    const width = 10
    const height = 5
    const rectangle: Element = {
      id: 'rect-test',
      type: ElementType.RECTANGLE,
      visible: true,
      locked: false,
      metadata: {},
      properties: {
        width,
        height,
      },
    }

    const area = strategy.calculateArea(rectangle)

    expect(area).toBe(width * height)
  })

  it('should handle small values', () => {
    const rectangle: Element = {
      id: 'rect-small',
      type: ElementType.RECTANGLE,
      visible: true,
      locked: false,
      metadata: {},
      properties: {
        width: 0.001,
        height: 0.001,
      },
    }
    const area = strategy.calculateArea(rectangle)

    // The area should be 0.001 * 0.001 = 0.000001
    expect(area).toBeCloseTo(0.000001, 9) // Close to 0.000001 with 9 decimal precision
  })

  it('should handle absolute values', () => {
    const width = 5
    const height = 10
    const rectangle: Element = {
      id: 'rect-abs',
      type: ElementType.RECTANGLE,
      visible: true,
      locked: false,
      metadata: {},
      properties: {
        width,
        height,
      },
    }
    const area = strategy.calculateArea(rectangle)

    // Area should be width * height
    expect(area).toBe(50)
  })

  it('should handle NaN dimensions', () => {
    const rectangle: Element = {
      id: 'invalid-rect',
      type: ElementType.RECTANGLE,
      visible: true,
      locked: false,
      metadata: {},
      properties: {
        width: Number.NaN,
        height: 10,
      },
    }

    expect(() => strategy.calculateArea(rectangle)).toThrow(CoreError)
    expect(() => strategy.calculateArea(rectangle)).toThrow('Rectangle element (ID: invalid-rect) must have finite, non-negative width and height')
  })

  it('should handle non-number dimensions', () => {
    const rectangle: Element = {
      id: 'string-rect',
      type: ElementType.RECTANGLE,
      visible: true,
      locked: false,
      metadata: {},
      properties: {
        width: '5' as any, // String instead of number
        height: 10,
      },
    }

    expect(() => strategy.calculateArea(rectangle)).toThrow(CoreError)
    expect(() => strategy.calculateArea(rectangle)).toThrow('Rectangle element (ID: string-rect) must have finite, non-negative width and height')
  })

  it('should handle negative dimensions', () => {
    const rectangle: Element = {
      id: 'negative-rect',
      type: ElementType.RECTANGLE,
      visible: true,
      locked: false,
      metadata: {},
      properties: {
        width: -5,
        height: 10,
      },
    }

    expect(() => strategy.calculateArea(rectangle)).toThrow(CoreError)
    expect(() => strategy.calculateArea(rectangle)).toThrow('Rectangle element (ID: negative-rect) must have finite, non-negative width and height')
  })

  it('should throw error if element is not a rectangle', () => {
    const circle = new MockCircle()

    expect(() => strategy.calculateArea(circle as any)).toThrow(CoreError)
    expect(() => strategy.calculateArea(circle as any)).toThrow('Expected element type RECTANGLE or SQUARE, got \'CIRCLE\' for ID mock-circle')
  })

  it('should handle null element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    expect(() => strategy.calculateArea(null)).toThrow(CoreError)
    expect(() => strategy.calculateArea(null)).toThrow('Invalid element: null or undefined')
  })

  it('should handle undefined element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    expect(() => strategy.calculateArea(undefined)).toThrow(CoreError)
    expect(() => strategy.calculateArea(undefined)).toThrow('Invalid element: null or undefined')
  })
})
