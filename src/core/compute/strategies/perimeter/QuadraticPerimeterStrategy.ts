/**
 * Perimeter/Length Calculation Strategy for Quadratic Bezier Curve Elements
 *
 * @remarks
 * This strategy implements the {@link PerimeterCalculatorStrategy} for calculating
 * the perimeter (length) of Quadratic Bezier curve elements ({@link CoreElementType.QUADRATIC}).
 *
 * The length of the curve itself is approximated using Gaussian quadrature via the
 * `calculateQuadraticBezierLengthGauss` utility function from
 * `../../../../lib/utils/geometry/bezierUtils`.
 *
 * If the `closed` property of the quadratic Bezier curve element is `true`, this strategy
 * also calculates the straight-line distance between the curve's start and end points
 * (using `calculateDistance` from `../../../../lib/utils/geometry/pointUtils`)
 * and adds this to the curve length to form the total perimeter of the closed shape.
 *
 * The strategy validates that the element is of type `QUADRATIC` and that its
 * defining points (start, control, end) are valid.
 *
 * @module core/compute/strategies/perimeter/QuadraticPerimeterStrategy
 * @see {@link Path.Quadratic} for the quadratic Bezier curve element type definition.
 * @see {@link PerimeterCalculatorStrategy}
 * @see {@link calculateQuadraticBezierLengthGauss}
 * @see {@link calculateDistance}
 */
import type { PerimeterCalculatorStrategy } from '../../../../types/core/compute'
import type { PointData as IPoint } from '../../../../types/core/element/geometry/point' // Use PointData, alias as IPoint
import type {
  Element,
  Path,
  ShapeElement,
} from '../../../../types/core/elementDefinitions'
import { calculateQuadraticBezierLengthGauss } from '../../../../lib/utils/geometry/bezierUtils'
import { calculateDistance } from '../../../../lib/utils/geometry/pointUtils' // For closing segment
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

/**
 * Implements the {@link PerimeterCalculatorStrategy} for {@link CoreElementType.QUADRATIC} elements.
 * It uses Gaussian quadrature for accurate curve length approximation and adds the
 * closing segment length if the curve is marked as `closed`.
 */
export class QuadraticPerimeterStrategy implements PerimeterCalculatorStrategy {
  /**
   * Calculates the perimeter of a Quadratic Bezier curve element.
   *
   * @remarks
   * The curve length is calculated using Gaussian quadrature. If the curve is `closed`,
   * the straight-line distance between its start and end points is added to the curve length.
   *
   * @param element - The quadratic Bezier curve element, expected to be of type {@link ShapeElement}
   *                  and specifically {@link CoreElementType.QUADRATIC}, conforming to {@link Path.Quadratic}.
   * @returns The calculated perimeter.
   * @throws {@link CoreError} if the provided element is not of type `QUADRATIC`.
   * @throws {@link CoreError} if the curve's control points (`start`, `control`, `end`) are invalid.
   */
  public calculatePerimeter(element: Element): number {
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement.type !== CoreElementType.QUADRATIC) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `QuadraticPerimeterStrategy can only calculate perimeter for QUADRATIC elements, got ${shapeElement.type}`,
        undefined,
        { component: 'QuadraticPerimeterStrategy', operation: 'calculatePerimeter', target: shapeElement.id },
      )
    }

    const quadElement = shapeElement as unknown as Path.Quadratic // Added unknown for type safety

    const p0 = quadElement.properties?.start as IPoint | undefined
    const p1 = quadElement.properties?.control as IPoint | undefined
    const p2 = quadElement.properties?.end as IPoint | undefined
    const closed = quadElement.properties?.closed === true

    if (p0 === null || p0 === undefined || p1 === null || p1 === undefined || p2 === null || p2 === undefined
      || typeof p0.x !== 'number' || typeof p0.y !== 'number' || !Number.isFinite(p0.x) || !Number.isFinite(p0.y)
      || typeof p1.x !== 'number' || typeof p1.y !== 'number' || !Number.isFinite(p1.x) || !Number.isFinite(p1.y)
      || typeof p2.x !== 'number' || typeof p2.y !== 'number' || !Number.isFinite(p2.x) || !Number.isFinite(p2.y)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid quadratic curve points for element ID ${shapeElement.id}`,
        undefined,
        { component: 'QuadraticPerimeterStrategy', operation: 'calculatePerimeter', target: shapeElement.id, metadata: { p0, p1, p2 } },
      )
    }

    // Delegate to the utility function for curve length
    const length = calculateQuadraticBezierLengthGauss(p0, p1, p2)

    if (closed) {
      // Calculate distance between relative start and end points for closing segment
      const closingLength = calculateDistance(p0, p2) // For quadratic, it closes from p2 to p0
      return length + closingLength
    }

    return length
  }

  // private calculateQuadraticCurveLengthInternal method removed

  /**
   * Returns the element type that this strategy is responsible for.
   * @returns {@link CoreElementType.QUADRATIC} - The quadratic Bezier curve element type that this strategy handles
   */
  public getElementType(): CoreElementType {
    return CoreElementType.QUADRATIC
  }
}
