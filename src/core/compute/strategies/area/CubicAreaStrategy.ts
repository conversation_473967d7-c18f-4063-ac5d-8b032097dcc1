/**
 * Area Calculation Strategy for Cubic Bezier Curve Elements
 *
 * @remarks
 * This strategy implements the {@link AreaCalculatorStrategy} for calculating the
 * area of cubic Bezier curve elements ({@link CoreElementType.CUBIC}).
 *
 * For 'closed' cubic Bezier curves, the area is approximated by:
 * 1. Sampling a number of points along the curve using `sampleCubicBezier` utility.
 * 2. Treating these sampled points as vertices of a polygon.
 * 3. Calculating the area of this polygon using `calculatePolygonAreaUtil` from
 *    `../../../../lib/utils/geometry/polygonUtils`.
 *
 * If the cubic Bezier curve is not 'closed', it is considered to have no
 * enclosed area, and this strategy will return `0`.
 *
 * @module core/compute/strategies/area/CubicAreaStrategy
 * @see {@link Path.Cubic} for the cubic Bezier curve element type definition.
 */
import type { AreaCalculatorStrategy } from '../../../../types/core/compute'
import type { PointData as IPoint } from '../../../../types/core/element/geometry/point' // Use PointData, alias as IPoint
import type { Element, Path } from '../../../../types/core/elementDefinitions'
import { sampleCubicBezier } from '../../../../lib/utils/geometry/bezierUtils'
import { calculateArea as calculatePolygonAreaUtil } from '../../../../lib/utils/geometry/polygonUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import { ElementType as CoreElementType } from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

/**
 * Implements the {@link AreaCalculatorStrategy} for {@link CoreElementType.CUBIC} elements.
 * @remarks
 * This strategy approximates the area of a closed cubic Bezier curve by sampling points
 * along the curve and then calculating the area of the polygon formed by these points.
 * For open cubic Bezier curves, it returns 0, as they typically do not enclose an area.
 */
export class CubicAreaStrategy implements AreaCalculatorStrategy {
  /**
   * Calculates the area of a Cubic Bezier curve element.
   *
   * @param element - The cubic Bezier curve element, expected to conform to {@link Path.Cubic} and have type {@link CoreElementType.CUBIC}.
   * @returns The calculated (approximated) area if the curve is closed, or `0` if it's open.
   * @throws {@link CoreError} if the provided element is not of type `CUBIC`.
   * @throws {@link CoreError} if the curve's control points (`start`, `control1`, `control2`, `end`) are invalid or not numbers.
   */
  public calculateArea(element: Element): number {
    if (element.type !== CoreElementType.CUBIC) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `CubicAreaStrategy can only calculate area for CUBIC elements, got ${element.type}`,
        undefined, // severity
        { component: 'CubicAreaStrategy', operation: 'calculateArea', target: element.id }, // context
      )
    }

    const cubicElement = element as unknown as Path.Cubic

    // Access properties from the properties object
    const p0 = cubicElement.properties.start
    const p1 = cubicElement.properties.control1
    const p2 = cubicElement.properties.control2
    const p3 = cubicElement.properties.end
    const closed = cubicElement.properties.closed === true

    if (p0 == null || p1 == null || p2 == null || p3 == null
      || typeof p0.x !== 'number' || typeof p0.y !== 'number'
      || typeof p1.x !== 'number' || typeof p1.y !== 'number'
      || typeof p2.x !== 'number' || typeof p2.y !== 'number'
      || typeof p3.x !== 'number' || typeof p3.y !== 'number') {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid cubic curve points for element ID ${element.id}`,
        undefined,
        { component: 'CubicAreaStrategy', operation: 'calculateArea', target: element.id, metadata: { p0, p1, p2, p3 } },
      )
    }

    if (closed) {
      const numSegments = 30 // Number of segments to approximate the curve
      const sampledPoints: IPoint[] = sampleCubicBezier(p0, p1, p2, p3, numSegments)

      // Use the utility function for polygon area calculation
      return calculatePolygonAreaUtil(sampledPoints)
    }

    return 0 // Open cubic Bezier curves typically have no enclosed area.
  }

  // private calculatePolygonArea method removed, using polygonUtils.calculateArea instead.

  public getElementType(): CoreElementType {
    return CoreElementType.CUBIC
  }
}
