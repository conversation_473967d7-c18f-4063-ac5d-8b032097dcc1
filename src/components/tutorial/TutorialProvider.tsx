/**
 * Tutorial Provider Component
 *
 * A provider component that initializes and manages the tutorial system.
 * This component should wrap the main application to provide tutorial functionality.
 */

import React, { useEffect } from 'react'
import { useTutorial } from '@/hooks/useTutorial'

interface TutorialProviderProps {
  children: React.ReactNode
}

/**
 * Tutorial provider component that initializes the tutorial system
 */
export const TutorialProvider: React.FC<TutorialProviderProps> = ({ children }) => {
  const { state } = useTutorial()

  /**
   * Initialize tutorial system
   */
  useEffect(() => {
    // Tutorial system is automatically initialized through the useTutorial hook
    // This effect can be used for any additional setup if needed
    // Tutorial system initialized (removed console.log for production)
  }, [])

  /**
   * Log tutorial state changes for debugging
   */
  useEffect(() => {
    if (state.isRunning) {
      // Tutorial started (removed console.log for production)
    }
  }, [state.isRunning, state.activeTutorial])

  return <>{children}</>
}
