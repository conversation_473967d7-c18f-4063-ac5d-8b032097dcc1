/**
 * Area Calculation Strategy for Line Elements
 *
 * @remarks
 * This strategy implements the {@link AreaCalculatorStrategy} for Line elements
 * ({@link CoreElementType.LINE}).
 *
 * A geometric line, by definition, has no area. Therefore, this strategy
 * consistently returns `0`. It primarily serves to provide a concrete strategy
 * for line elements within the computation framework, ensuring that an area
 * calculation request for a line element is handled gracefully.
 *
 * The actual "calculation" (which is just returning 0) is delegated to the
 * `calculateLineAreaUtil` function from `../../../../lib/utils/geometry/areaUtils`.
 *
 * @module core/compute/strategies/area/LineAreaStrategy
 * @see {@link Path.Line} for the line element type definition.
 */
import type { AreaCalculatorStrategy } from '../../../../types/core/compute'
import type { Element } from '../../../../types/core/elementDefinitions'
import { calculateLineArea as calculateLineAreaUtil } from '../../../../lib/utils/geometry/areaUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import { ElementType as CoreElementType } from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

export class LineAreaStrategy implements AreaCalculatorStrategy {
  /**
   * Calculates the area of a Line element.
   *
   * @remarks
   * Since a line has no area, this method always returns `0`.
   *
   * @param element - The line element, expected to be of type {@link CoreElementType.LINE}.
   * @returns Always returns `0`.
   * @throws {@link CoreError} if the provided element is `null` or `undefined`.
   * @throws {@link CoreError} if the provided element is not of type `LINE`.
   */
  public calculateArea(element: Element): number {
    if (element == null) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid element provided to LineAreaStrategy (null or undefined).`,
        undefined,
        { component: 'LineAreaStrategy', operation: 'calculateArea' },
      )
    }

    if (element.type !== CoreElementType.LINE) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `LineAreaStrategy can only calculate area for LINE elements, got ${element.type}`,
        undefined,
        { component: 'LineAreaStrategy', operation: 'calculateArea', target: element.id },
      )
    }

    // Delegate to the utility function which should return 0
    return calculateLineAreaUtil()
  }

  /**
   * Returns the element type that this strategy is responsible for.
   * @returns The element type this strategy handles
   */
  public getElementType(): CoreElementType {
    return CoreElementType.LINE
  }
}
