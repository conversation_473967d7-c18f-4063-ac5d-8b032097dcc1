# Testing Guide

This document outlines the testing strategy, types of tests, tools used, and guidelines for writing tests in the RenoPilot.JS.Shapes2 project.

## Testing Philosophy

We aim for a comprehensive testing approach to ensure application reliability, prevent regressions, and facilitate confident refactoring. Our strategy includes a mix of unit, integration, and end-to-end (E2E) tests.

## Testing Tools

*   **[Vitest](https://vitest.dev/):** A blazing fast unit test framework powered by Vite. Used for unit testing core logic, utility functions, and potentially individual React components (though Playwright Component Tests can also be used).
*   **[Playwright](https://playwright.dev/):** A framework for Web Testing and Automation. Used for:
    *   **End-to-End (E2E) Tests:** Simulating full user scenarios across the application.
    *   **Integration Tests:** Testing interactions between different parts of the application (e.g., UI components and services, or multiple UI components).
    *   **Component Tests (Optional but Recommended):** <PERSON>wright also supports testing React components in isolation, mounting them in a real browser environment.

## Types of Tests and Scope

### 1. Unit Tests (`tests/unit` or co-located with source files)

*   **Tool:** Vitest
*   **Scope:** Test individual functions, modules, or classes in isolation. Focus on the smallest testable parts of the application, particularly core logic (`src/core`), utility functions (`src/lib`), and potentially individual Zustand store actions/reducers if complex.
*   **Goals:**
    *   Verify correctness of algorithms and business logic.
    *   Ensure functions produce expected outputs for given inputs.
    *   Test edge cases and error handling.
*   **Location:**
    *   Typically in `tests/unit/` mirroring the `src/` directory structure (e.g., `tests/unit/core/compute/ComputeFacade.spec.ts`).
    *   Alternatively, co-located with the source files (e.g., `src/core/compute/ComputeFacade.spec.ts`).
*   **Example (`*.spec.ts` or `*.test.ts`):
    ```typescript
    // src/core/utils/math.spec.ts
    import { describe, it, expect } from 'vitest';
    import { add } from './math'; // Assuming math.ts exports add

    describe('Math utils', () => {
      describe('add function', () => {
        it('should return the sum of two positive numbers', () => {
          expect(add(2, 3)).toBe(5);
        });

        it('should return the sum of a positive and a negative number', () => {
          expect(add(5, -2)).toBe(3);
        });
      });
    });
    ```

### 2. Integration Tests (`tests/integration`)

*   **Tool:** Playwright
*   **Scope:** Test the interaction between multiple components, services, or modules. For example, testing if a UI action correctly updates a Zustand store and if a service correctly processes data from a component.
*   **Goals:**
    *   Verify that different parts of the application work together as expected.
    *   Catch issues at the interfaces between modules.
*   **Location:** `tests/integration/`
*   **Example (Conceptual - Playwright syntax would be used):
    *   Test that selecting a tool in the `Toolbar` updates the `uiStore` and that the `Canvas` interaction mode changes accordingly.
    *   Test that creating a shape via UI dispatches correct actions to `shapesStore` and calls the appropriate services.

### 3. End-to-End (E2E) Tests (`tests/e2e`)

*   **Tool:** Playwright
*   **Scope:** Test complete user workflows from the user's perspective, interacting with the application as a whole in a real browser environment.
*   **Goals:**
    *   Ensure key user journeys are functioning correctly.
    *   Validate the integration of all application layers.
    *   Catch regressions that might be missed by unit or integration tests.
*   **Location:** `tests/e2e/`
*   **Example (`*.spec.ts`):
    ```typescript
    // tests/e2e/shapeCreation.spec.ts
    import { test, expect } from '@playwright/test';

    test.beforeEach(async ({ page }) => {
      await page.goto('/'); // Assuming the app is served at the root
    });

    test('should allow creating a rectangle shape', async ({ page }) => {
      // 1. Select the rectangle tool
      await page.getByTestId('toolbar-rectangle-button').click();

      // 2. Draw on the canvas
      const canvas = page.getByTestId('canvas');
      await canvas.dragTo(canvas, {
        sourcePosition: { x: 100, y: 100 },
        targetPosition: { x: 200, y: 150 },
      });

      // 3. Verify the shape is created and visible
      const createdRectangle = page.locator('svg g[data-shape-type="rectangle"]');
      await expect(createdRectangle).toBeVisible();
      await expect(createdRectangle).toHaveCount(1);
      // Further assertions on properties if needed
    });
    ```

### 4. Component Tests (Playwright - Optional)

*   **Tool:** Playwright
*   **Scope:** Test individual React components in isolation, mounted within a browser environment. This allows testing component rendering, interaction, and behavior with more realism than JSDOM-based unit tests for components.
*   **Goals:**
    *   Verify component rendering based on props.
    *   Test component event handling and state changes.
*   **Location:** Typically co-located with components (e.g., `src/components/toolbar/Toolbar.ct.spec.ts`).

## Running Tests

Refer to the `package.json` scripts:

*   **Run all Playwright tests (E2E, Integration):**
    ```bash
    npm test
    # or
    yarn test
    ```
    Playwright tests will typically run in headless mode by default. You can configure this in `playwright.config.ts` or via CLI options.

*   **Run Vitest unit tests:**
    ```bash
    npm run test:vitest
    # or
    yarn test:vitest
    ```

*   **Run Vitest in watch mode:**
    ```bash
    npm run test:vitest -- --watch
    # or
    yarn test:vitest --watch
    ```

*   **Run Playwright tests in UI mode (for debugging):**
    ```bash
    npx playwright test --ui
    ```

*   **Run Playwright tests headed:**
    ```bash
    npx playwright test --headed
    ```

## Writing Good Tests

*   **Descriptive Names:** Test descriptions (`describe`, `it`, `test`) should clearly state what is being tested and the expected outcome.
*   **AAA Pattern (Arrange, Act, Assert):**
    *   **Arrange:** Set up the necessary preconditions and inputs.
    *   **Act:** Execute the code or perform the action being tested.
    *   **Assert:** Verify that the outcome is as expected.
*   **Independent Tests:** Tests should be independent of each other. The outcome of one test should not affect another.
*   **Test One Thing:** Each test case should ideally focus on a single piece of functionality or behavior.
*   **Use Test IDs:** For E2E and component tests, prefer using `data-testid` attributes or other stable selectors for locating elements, rather than relying on CSS classes or text content that might change frequently.
    ```html
    <button data-testid="submit-button">Submit</button>
    ```
    ```typescript
    await page.getByTestId('submit-button').click();
    ```
*   **Avoid `sleep` or fixed delays:** Use Playwright's auto-waiting capabilities and web-first assertions. If you need to wait for a specific condition, use `waitForFunction`, `waitForSelector`, or other explicit waiting mechanisms.
*   **Mock Dependencies (Unit Tests):** For unit tests, mock external dependencies (modules, services, API calls) to isolate the unit under test. Vitest provides mocking utilities (`vi.mock`, `vi.fn`).
*   **Clean Up:** Ensure tests clean up any state they create, if necessary (e.g., resetting mocks, clearing stores if tests are not fully isolated by test runners).
*   **Focus on Behavior, Not Implementation:** Tests should verify the observable behavior of a component or module, rather than its internal implementation details. This makes tests more resilient to refactoring.

## Test Coverage

While aiming for high test coverage is good, focus on testing critical paths, complex logic, and areas prone to regressions. Use coverage reports (e.g., from Vitest with `c8` or `istanbul`) as a guide, not an absolute target.

To generate coverage reports with Vitest:
```bash
npm run test:vitest -- --coverage
# or
_yarn test:vitest --coverage
```

## Continuous Integration (CI)

All tests (unit, integration, E2E) should be run as part of the CI/CD pipeline (e.g., using GitHub Actions defined in `.github/workflows/`) on every push and pull request to ensure that no regressions are introduced.

By following these guidelines, we can build a robust test suite that supports the development and maintenance of RenoPilot.JS.Shapes2.