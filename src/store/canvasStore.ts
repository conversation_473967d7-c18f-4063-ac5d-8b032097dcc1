/**
 * Canvas Store
 *
 * This module defines the global state for canvas configuration and view settings.
 * It manages zoom/pan transforms and provides actions to manipulate the canvas view state.
 *
 * The store uses Zustand with devtools and subscribe-with-selector middlewares for
 * improved development experience and fine-grained subscription control.
 *
 * @module store/canvasStore
 */

// import { LAYER_FLOOR, LAYER_FURNITURE, LAYER_CEILING } from '@/types/constants'; // Removed
import type { ZoomTransform } from 'd3-zoom'
import * as d3 from 'd3'
import { create } from 'zustand'
import { devtools, subscribeWithSelector } from 'zustand/middleware'
// import { CanvasLayer } from '@/types/core/canvasTypes' // More direct import

/**
 * Canvas view mode types
 * Defines how the canvas view behaves (select or pan mode)
 */
export type CanvasViewMode = 'select' | 'pan'

/**
 * Canvas state interface
 * Core state properties for the canvas
 */
export interface CanvasState {
  /** Current zoom transform */
  transform: ZoomTransform

  /** Current zoom level for easier access */
  currentZoom: number

  /** View mode (select or pan) */
  viewMode: CanvasViewMode
}

/**
 * Canvas actions interface
 * Actions available to manipulate the canvas state
 */
interface CanvasActions {
  /**
   * Update the zoom transform
   * @param transform - The new D3 zoom transform
   */
  setTransform: (transform: ZoomTransform) => void

  /**
   * Set the canvas view mode
   * @param mode - The view mode to set
   */
  setViewMode: (mode: CanvasViewMode) => void

  /**
   * Reset the canvas view to default settings
   */
  resetCanvasView: () => void
}

// Initial state values
// const initialLayerVisibility = [CanvasLayer.FLOOR, CanvasLayer.FURNITURE, CanvasLayer.CEILING].reduce((acc, layer) => {
//   acc[layer] = true
//   return acc
// }, {} as Record<CanvasLayer, boolean>)

/**
 * Initial canvas state
 */
const initialState: CanvasState = {
  // activeLayer: CanvasLayer.FURNITURE,
  // layerVisibility: initialLayerVisibility,
  transform: d3.zoomIdentity,
  currentZoom: 1,
  viewMode: 'select',
}

/**
 * Canvas store hook for accessing and manipulating canvas state
 */
export const useCanvasStore = create<CanvasState & CanvasActions>()(
  devtools(
    subscribeWithSelector(set => ({
      ...initialState,
      // setActiveLayer: (layer) => {
      //   console.log(`[canvasStore] Setting active layer to: ${layer}`)
      //   set({ activeLayer: layer }, false, 'setActiveLayer')
      // },
      // toggleLayerVisibility: layer => set(state => ({
      //   layerVisibility: {
      //     ...state.layerVisibility,
      //     [layer]: !state.layerVisibility[layer],
      //   },
      // }), false, 'toggleLayerVisibility'),
      setTransform: transform => set({
        transform,
        currentZoom: transform.k,
      }, false, 'setTransform'),
      setViewMode: viewMode => set({ viewMode }, false, 'setViewMode'),
      resetCanvasView: () => set({
        // activeLayer: initialState.activeLayer,
        // layerVisibility: { ...initialState.layerVisibility },
        transform: d3.zoomIdentity,
        currentZoom: 1,
        viewMode: 'select',
      }, false, 'resetCanvasView'),
    })),
    { name: 'Canvas Store' },
  ),
)
