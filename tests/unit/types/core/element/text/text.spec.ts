import type { ShapeElement } from '@/types/core/element/element'
import type { Text } from '@/types/core/element/text/textElementTypes'
import { describe, expect, it } from 'vitest'

describe('text Interface', () => {
  it('should define a text element with required properties', () => {
    // Create a valid Text object
    const text: Text = {
      id: 'text-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      content: 'Hello, World!',
      fontSize: 24,
      fontFamily: 'Arial, sans-serif',
    }

    // Verify the object is valid
    expect(text.id).toBe('text-1')
    expect(text.type).toBe('shape')
    expect(text.content).toBe('Hello, World!')
    expect(text.position.x).toBe(100)
    expect(text.position.y).toBe(100)
    expect(text.fontSize).toBe(24)
    expect(text.fontFamily).toBe('Arial, sans-serif')
  })

  it('should extend ShapeElement interface', () => {
    // Create a text element
    const text: Text = {
      id: 'text-2',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 150, y: 150 },
      rotation: 45,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      content: 'Testing Text',
      fontSize: 16,
      fontFamily: 'Helvetica, Arial, sans-serif',
    }

    // Verify the text extends ShapeElement
    const shapeElement: ShapeElement = text
    expect(shapeElement.id).toBe('text-2')
    expect(shapeElement.type).toBe('shape')
    expect(shapeElement.visible).toBe(true)
    expect(shapeElement.locked).toBe(false)
    expect(shapeElement.position.x).toBe(150)
    expect(shapeElement.position.y).toBe(150)
    expect(shapeElement.rotation).toBe(45)
  })

  it('should allow creating text elements with different properties', () => {
    // Create text elements with different properties
    const smallText: Text = {
      id: 'small-text',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      content: 'Small Text',
      fontSize: 10,
      fontFamily: 'Times New Roman, serif',
    }

    const largeText: Text = {
      id: 'large-text',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 200, y: 200 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      content: 'Large Text',
      fontSize: 36,
      fontFamily: 'Impact, sans-serif',
    }

    const rotatedText: Text = {
      id: 'rotated-text',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 150, y: 150 },
      rotation: 90,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      content: 'Rotated Text',
      fontSize: 18,
      fontFamily: 'Courier New, monospace',
    }

    // Verify all text elements are valid
    expect(smallText.content).toBe('Small Text')
    expect(smallText.fontSize).toBe(10)
    expect(smallText.fontFamily).toBe('Times New Roman, serif')

    expect(largeText.content).toBe('Large Text')
    expect(largeText.fontSize).toBe(36)
    expect(largeText.fontFamily).toBe('Impact, sans-serif')

    expect(rotatedText.content).toBe('Rotated Text')
    expect(rotatedText.fontSize).toBe(18)
    expect(rotatedText.fontFamily).toBe('Courier New, monospace')
    expect(rotatedText.rotation).toBe(90)
  })

  it('should be usable in arrays and collections', () => {
    // Create an array of text elements
    const texts: Text[] = [
      {
        id: 'text-3',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 100, y: 100 },
        rotation: 0,
        scale: { x: 1, y: 1 },
        selectable: true,
        draggable: true,
        showHandles: true,
        content: 'First Text',
        fontSize: 14,
        fontFamily: 'Arial, sans-serif',
      },
      {
        id: 'text-4',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 200, y: 200 },
        rotation: 0,
        scale: { x: 1, y: 1 },
        selectable: true,
        draggable: true,
        showHandles: true,
        content: 'Second Text',
        fontSize: 18,
        fontFamily: 'Helvetica, sans-serif',
      },
    ]

    // Verify the array is valid
    expect(texts.length).toBe(2)
    expect(texts[0].id).toBe('text-3')
    expect(texts[0].content).toBe('First Text')
    expect(texts[0].fontSize).toBe(14)

    expect(texts[1].id).toBe('text-4')
    expect(texts[1].content).toBe('Second Text')
    expect(texts[1].fontSize).toBe(18)
  })

  it('should be usable in functions that require Text', () => {
    // Define a function that uses Text
    function calculateTextWidth(text: Text): number {
      // Simple approximation: each character is roughly 0.6 times the font size
      return text.content.length * 0.6 * text.fontSize
    }

    // Create a text element
    const text: Text = {
      id: 'text-5',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      content: 'Hello',
      fontSize: 20,
      fontFamily: 'Arial, sans-serif',
    }

    // Test the function
    const width = calculateTextWidth(text)
    expect(width).toBe(5 * 0.6 * 20) // 5 characters * 0.6 * 20px font size
  })

  it('should be compatible with JSON serialization/deserialization', () => {
    // Create a text element
    const text: Text = {
      id: 'text-6',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      content: 'Serializable Text',
      fontSize: 16,
      fontFamily: 'Arial, sans-serif',
    }

    // Serialize to JSON and deserialize
    const serialized = JSON.stringify(text)
    const deserialized = JSON.parse(serialized) as Text

    // Verify the deserialized object is valid
    expect(deserialized.id).toBe('text-6')
    expect(deserialized.type).toBe('shape')
    expect(deserialized.content).toBe('Serializable Text')
    expect(deserialized.fontSize).toBe(16)
    expect(deserialized.fontFamily).toBe('Arial, sans-serif')
    expect(deserialized.position.x).toBe(100)
    expect(deserialized.position.y).toBe(100)
  })

  it('should support multiline text content', () => {
    // Create a text element with multiline content
    const text: Text = {
      id: 'multiline-text',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      content: 'First line\nSecond line\nThird line',
      fontSize: 14,
      fontFamily: 'Arial, sans-serif',
    }

    // Verify the multiline content
    expect(text.content).toBe('First line\nSecond line\nThird line')
    expect(text.content.split('\n').length).toBe(3)
  })
})
