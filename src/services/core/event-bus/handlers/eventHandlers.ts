/**
 * Defines the core event handlers for the application's event system.
 *
 * @remarks
 * These handlers respond to various events triggered throughout the application
 * and are organized by functional domain. Currently, most handlers perform simple
 * logging for monitoring and debugging, but they can be extended to implement more
 * complex behavior as needed.
 *
 * The handlers are organized into the following categories:
 * - Canvas handlers: Handle canvas-related events (clearing, resizing, mouse movement)
 * - Tool handlers: Handle tool selection and changes
 * - History handlers: Handle undo/redo operations
 * - Layer handlers: Handle layer visibility, locking, and ordering
 * - File handlers: Handle import/export operations
 * - View handlers: Handle zoom and pan operations
 * - Template handlers: Handle template application
 * - Notification handlers: Handle system notifications and toasts
 * - Sidebar handlers: Handle sidebar visibility toggles
 *
 * @example
 * ```typescript
 * import { setupEventHandlers } from '@/services/core/event-bus/handlers/eventHandlers';
 *
 * // Initialize all event handlers
 * setupEventHandlers();
 * ```
 *
 * @module services/core/event-bus/handlers/eventHandlers
 */

import { handleExportProgress, handleExportRequest } from '@/data/export/handlers'
import { AppEventBusImpl, DataSubscribers } from '@/services/'
import { handleExportPrepare } from '@/store/handlers'
import { AppEventType } from '@/types/services/events'

// Singleton appEventBus instance
const appEventBus = AppEventBusImpl.getInstance()

/**
 * Sets up all event handlers.
 *
 * @remarks
 * This function registers all the application's core event handlers
 * by calling domain-specific setup functions. These handlers provide
 * baseline functionality like logging and monitoring.
 *
 * Note: Component-specific handlers should be registered separately
 * by their respective components, not in this centralized setup.
 */
export function setupEventHandlers(): void {
  // Set up canvas event handlers
  setupCanvasHandlers()

  // Set up tool event handlers
  setupToolHandlers()

  // Set up history event handlers
  setupHistoryHandlers()

  // Set up layer event handlers
  setupLayerHandlers()

  // Set up file event handlers
  setupFileHandlers()

  // Set up view event handlers
  setupViewHandlers()

  // Set up template event handlers
  setupTemplateHandlers()

  // Set up notification event handlers
  setupNotificationHandlers()

  // Set up sidebar event handlers
  setupSidebarHandlers()
}

/**
 * Sets up canvas event handlers.
 *
 * @remarks
 * Registers event handlers for canvas-related events such as canvas clearing,
 * resizing, and mouse movement. These handlers currently log events for
 * debugging, but could be extended to implement more complex behaviors.
 *
 * @private
 */
function setupCanvasHandlers(): void {
  // Canvas cleared handler
  appEventBus.subscribe(AppEventType.CanvasCleared, (event) => {
    console.warn('Canvas cleared:', event.payload.canvasId)
  })

  // Canvas resized handler
  appEventBus.subscribe(AppEventType.CanvasResized, (event) => {
    console.warn('Canvas resized:', event.payload.width, event.payload.height)
  })

  // Canvas mouse move handler
  appEventBus.subscribe(AppEventType.CanvasMouseMove, (event) => {
    // Only log occasionally to avoid flooding the console
    if (Math.random() < 0.01) {
      console.warn('Canvas mouse move:', event.payload.x, event.payload.y)
    }
  })
}

/**
 * Sets up tool event handlers.
 *
 * @remarks
 * Registers event handlers for tool-related events such as tool selection changes.
 * These handlers provide monitoring capabilities during development.
 *
 * @private
 */
function setupToolHandlers(): void {
  // Tool changed handler
  appEventBus.subscribe(AppEventType.ToolChanged, (event) => {
    console.warn('Tool changed:', event.payload.tool)
  })
}

/**
 * Sets up history event handlers.
 *
 * @remarks
 * Registers event handlers for undo/redo history events. These handlers monitor
 * operations on the application's history stack for debugging purposes.
 *
 * @private
 */
function setupHistoryHandlers(): void {
  // History checkpoint handler
  appEventBus.subscribe(AppEventType.HistoryCheckpoint, (event) => {
    console.warn('History checkpoint:', event.payload)
  })

  // History undo handler
  appEventBus.subscribe(AppEventType.HistoryUndo, (event) => {
    console.warn('History undo:', event.payload)
  })

  // History redo handler
  appEventBus.subscribe(AppEventType.HistoryRedo, (event) => {
    console.warn('History redo:', event.payload)
  })
}

/**
 * Sets up layer event handlers.
 *
 * @remarks
 * Registers event handlers for layer-related events such as visibility changes,
 * lock status changes, and layer reordering.
 *
 * @private
 */
function setupLayerHandlers(): void {
  // Layer visibility change handler
  appEventBus.subscribe(AppEventType.LayerVisibilityChange, (event) => {
    console.warn('Layer visibility change:', event.payload.layerId, event.payload.visible)
  })

  // Layer lock change handler
  appEventBus.subscribe(AppEventType.LayerLockChange, (event) => {
    console.warn('Layer lock change:', event.payload.layerId, event.payload.locked)
  })

  // Layer order change handler
  appEventBus.subscribe(AppEventType.LayerOrderChange, (event) => {
    console.warn('Layer order change:', event.payload.layerId, event.payload.newOrder)
  })
}

/**
 * Sets up file event handlers.
 *
 * @remarks
 * Registers event handlers for file operations such as importing and exporting.
 * These handlers can be extended to implement additional behaviors like
 * success notifications or error handling.
 *
 * @private
 */
function setupFileHandlers(): void {
  // File imported handler
  appEventBus.subscribe(AppEventType.FileImported, (event) => {
    console.warn('File imported:', event)
  })

  // File exported handler
  appEventBus.subscribe(AppEventType.FileExported, (event) => {
    console.log('File exported:', event)
  })

  // Listen to ExportRequest and forward it as ExportPrepare.
  DataSubscribers.subscribeToExportEvents(
    appEventBus,
    handleExportRequest,
    'request',
  )

  // Listen to ExportPrepare, deliver shapes to data module.
  DataSubscribers.subscribeToExportEvents(
    appEventBus,
    handleExportPrepare,
    'prepare',
  )

  // Listen to ExportProgress, generate blob and publish ExportComplete.
  DataSubscribers.subscribeToExportEvents(
    appEventBus,
    handleExportProgress,
    'progress',
    { async: true },
  )
}

/**
 * Sets up view event handlers.
 *
 * @remarks
 * Registers event handlers for view manipulation events such as zooming and panning.
 * These handlers currently provide monitoring but could be extended to implement
 * additional behaviors like auto-centering or zoom limits.
 *
 * @private
 */
function setupViewHandlers(): void {
  // View zoomed handler
  appEventBus.subscribe(AppEventType.ViewZoomed, (event) => {
    console.warn('View zoomed:', event.payload.scale)
  })

  // View panned handler
  appEventBus.subscribe(AppEventType.ViewPanned, (event) => {
    console.warn('View panned:', event.payload.x, event.payload.y)
  })
}

/**
 * Sets up template event handlers.
 *
 * @remarks
 * Registers event handlers for template-related events such as template application.
 *
 * @private
 */
function setupTemplateHandlers(): void {
  // Template apply handler
  appEventBus.subscribe(AppEventType.TemplateApply, (event) => {
    console.warn('Template apply:', event.payload.templateId)
  })
}

/**
 * Sets up notification event handlers.
 *
 * @remarks
 * Registers event handlers for system notifications. These handlers can be extended
 * to implement additional behaviors like logging or UI feedback.
 *
 * @private
 */
function setupNotificationHandlers(): void {
  // Notification add handler
  appEventBus.subscribe(AppEventType.NotificationAdd, (event) => {
    console.warn('Notification add:', event.payload.message, event.payload.type)
  })

  // Toast show handler
  appEventBus.subscribe(AppEventType.ToastShow, (event) => {
    console.warn('Toast show:', event.payload.message, event.payload.type, event.payload.duration)
  })
}

/**
 * Sets up sidebar event handlers.
 *
 * @remarks
 * Registers event handlers for sidebar interactions. These handlers currently
 * provide monitoring but could be extended to implement additional behaviors.
 *
 * @private
 */
function setupSidebarHandlers(): void {
  // Sidebar left toggle handler
  appEventBus.subscribe(AppEventType.SidebarLeftToggle, (event) => {
    console.warn('Sidebar left toggle:', event.payload.visible)
  })

  // Sidebar right toggle handler
  appEventBus.subscribe(AppEventType.SidebarRightToggle, (event) => {
    console.warn('Sidebar right toggle:', event.payload.visible)
  })
}
