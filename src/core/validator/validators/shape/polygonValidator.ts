/**
 * Contains validation logic specific to Polygon shapes (including Triangle and Hexagon).
 */

import type { PointData } from '@/types/core/element/geometry/point' // Import PointData
import type { Shape } from '@/types/core/elementDefinitions'
import type { ValidationError, ValidatableShape as ValidatorShape } from '@/types/core/validator/validator-interface'
import { ElementType } from '@/types/core/elementDefinitions' // Import Shape namespace
import { ValidationErrorCode } from '@/types/core/validator/error-codes'
import { isPolygonClosed, isValidPoint } from '../common/validationUtils'
import { BaseShapeValidator } from '../common/validatorBase'

/**
 * Concrete validator implementation for Polygon, Triangle, and Hexagon shapes.
 *
 * @extends BaseShapeValidator
 */
export class PolygonValidator extends BaseShapeValidator {
  /**
   * Validates the specific properties of a Polygon shape.
   *
   * @param shape - The shape to validate.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected validateSpecific(shape: ValidatorShape): ValidationError[] {
    const errors: ValidationError[] = []

    if (shape.type === ElementType.POLYGON) {
      return this.validatePolygon(shape as unknown as Shape.Polygon)
    }
    else if (shape.type === ElementType.TRIANGLE) {
      // Shape.Triangle extends Shape.Polygon, so casting to Shape.Polygon first for common checks is fine
      return this.validateTriangle(shape as unknown as Shape.Triangle)
    }
    else if (shape.type === ElementType.QUADRILATERAL) {
      return this.validateQuadrilateral(shape as unknown as Shape.Polygon)
    }
    else if (shape.type === ElementType.PENTAGON) {
      return this.validatePentagon(shape as unknown as Shape.Polygon)
    }
    else if (shape.type === ElementType.HEXAGON) {
      // Shape.Hexagon extends Shape.Polygon
      return this.validateHexagon(shape as unknown as Shape.Hexagon)
    }
    else {
      errors.push({
        code: ValidationErrorCode.INVALID_SHAPE_TYPE,
        message: `Internal Error: PolygonValidator received shape type ${shape.type}`,
        path: 'type',
        value: shape.type,
      })
      return errors
    }
  }

  /**
   * Validates a general polygon shape.
   *
   * @param shape - The polygon shape to validate.
   * @returns An array of validation errors.
   * @private
   */
  private validatePolygon(shape: Shape.Polygon): ValidationError[] {
    const errors: ValidationError[] = []
    const props = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined || {}
    const points = props.points as PointData[] | undefined
    const sides = props.sides as number | undefined
    const isRegular = props.isRegular as boolean | undefined

    // Basic checks (points array, length, valid points)
    if (!Array.isArray(points)) {
      errors.push({
        code: ValidationErrorCode.MISSING_PROPERTY,
        message: 'Polygon must have a points array in its properties.',
        path: 'properties.points',
        value: points,
      })
      return errors // Early return if points array is fundamentally missing
    }
    if (points.length < 3) {
      errors.push({
        code: ValidationErrorCode.INSUFFICIENT_POINTS,
        message: `Polygon must have at least 3 points. Found: ${points.length}`,
        path: 'properties.points.length',
        value: points.length,
      })
      // Continue validating other properties
    }
    for (let i = 0; i < points.length; i++) {
      const point = points[i]
      if (!isValidPoint(point)) {
        errors.push({
          code: ValidationErrorCode.INVALID_POINT,
          message: `Invalid point at index ${i} in properties.points: ${JSON.stringify(point)}`,
          path: `properties.points[${i}]`,
          value: point,
        })
      }
    }

    // Check closure (only for non-regular)
    const isPossiblyClosedViaPoints = points.length >= 4 && points[0].x === points[points.length - 1].x && points[0].y === points[points.length - 1].y
    if (points.length >= 3 && !isRegular && !isPossiblyClosedViaPoints
      && !isPolygonClosed(points.map(p => ({ x: p.x, y: p.y })), 0.1)) {
      errors.push({
        code: ValidationErrorCode.POLYGON_NOT_CLOSED,
        message: 'Non-regular polygon must be closed (first and last points must match).',
        path: 'properties.points',
        value: points,
      })
    }

    // Validate 'sides'
    if (typeof sides !== 'number' || sides < 3 || !Number.isInteger(sides)) {
      errors.push({
        code: ValidationErrorCode.INVALID_SIDES,
        message: `Polygon sides must be an integer >= 3. Received: ${sides}`,
        path: 'properties.sides',
        value: sides,
      })
    }
    else if (isRegular) {
      const expectedLength = isPossiblyClosedViaPoints ? sides + 1 : sides
      if (points.length !== expectedLength) {
        // This is now only a warning, not an error
        console.warn(`[validatePolygon WARN] Regular polygon points.length (${points.length}) does not match expected (${expectedLength}), but continuing.`)
      }
    }
    else if (points.length >= 3) { // Non-regular sides check
      const effectivePointsLength = isPossiblyClosedViaPoints ? points.length - 1 : points.length
      if (sides !== effectivePointsLength) {
        // This is now only a warning, not an error
        console.warn(`[validatePolygon WARN] Non-regular polygon sides (${sides}) does not match effective points length (${effectivePointsLength}), but continuing.`)
      }
    }

    // Validate isRegular type
    if (typeof isRegular !== 'boolean') {
      console.warn(`[validatePolygon WARN] isRegular is not a boolean (${typeof isRegular}), defaulting to false assumption.`)
    }

    return errors
  }

  /**
   * Validates a triangle shape.
   *
   * @param triangle - The triangle shape to validate.
   * @returns An array of validation errors.
   * @private
   */
  private validateTriangle(triangle: Shape.Triangle): ValidationError[] {
    // First validate as a generic polygon
    const errors: ValidationError[] = this.validatePolygon(triangle) // Triangle extends Polygon

    // Access sides from the 'properties' bag of the ValidatableShape
    // The 'triangle' parameter here is the ValidatableShape object passed to the validator
    const props = (triangle as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined || {}
    const sidesFromProps = props.sides as number | undefined

    if (sidesFromProps !== 3) {
      errors.push({
        code: ValidationErrorCode.INVALID_SIDES,
        message: `Triangle 'sides' property (from properties) must be 3. Found: ${sidesFromProps}`,
        path: 'properties.sides', // Corrected path
        value: sidesFromProps,
      })
    }

    return errors
  }

  /**
   * Validates a hexagon shape.
   *
   * @param hexagon - The hexagon shape to validate.
   * @returns An array of validation errors.
   * @private
   */
  private validateHexagon(hexagon: Shape.Hexagon): ValidationError[] {
    // First validate as a generic polygon
    const errors: ValidationError[] = this.validatePolygon(hexagon) // Hexagon extends Polygon

    // Access sides from the 'properties' bag of the ValidatableShape
    const props = (hexagon as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined || {}
    const sidesFromProps = props.sides as number | undefined

    if (sidesFromProps !== 6) {
      errors.push({
        code: ValidationErrorCode.INVALID_SIDES,
        message: `Hexagon 'sides' property (from properties) must be 6. Found: ${sidesFromProps}`,
        path: 'properties.sides', // Corrected path
        value: sidesFromProps,
      })
    }
    return errors
  }

  /**
   * Validates a quadrilateral shape.
   *
   * @param quadrilateral - The quadrilateral shape to validate.
   * @returns An array of validation errors.
   * @private
   */
  private validateQuadrilateral(quadrilateral: Shape.Polygon): ValidationError[] {
    const errors: ValidationError[] = this.validatePolygon(quadrilateral)
    const props = (quadrilateral as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined || {}
    const sidesFromProps = props.sides as number | undefined

    if (sidesFromProps !== 4) {
      errors.push({
        code: ValidationErrorCode.INVALID_SIDES,
        message: `Quadrilateral 'sides' property (from properties) must be 4. Found: ${sidesFromProps}`,
        path: 'properties.sides',
        value: sidesFromProps,
      })
    }
    return errors
  }

  /**
   * Validates a pentagon shape.
   *
   * @param pentagon - The pentagon shape to validate.
   * @returns An array of validation errors.
   * @private
   */
  private validatePentagon(pentagon: Shape.Polygon): ValidationError[] {
    const errors: ValidationError[] = this.validatePolygon(pentagon)
    const props = (pentagon as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined || {}
    const sidesFromProps = props.sides as number | undefined

    if (sidesFromProps !== 5) {
      errors.push({
        code: ValidationErrorCode.INVALID_SIDES,
        message: `Pentagon 'sides' property (from properties) must be 5. Found: ${sidesFromProps}`,
        path: 'properties.sides',
        value: sidesFromProps,
      })
    }
    return errors
  }

  /**
   * Applies specific business rules for polygons.
   *
   * @param shape - The shape to validate against business rules.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected applySpecificRules(shape: ValidatorShape): ValidationError[] {
    const errors: ValidationError[] = []
    const props = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined || {} // Get the properties object
    const points = props.points as PointData[] | undefined // Access points from properties
    const isRegular = props.isRegular as boolean | undefined // Check if it's a regular polygon

    // Skip duplicate point check for regular polygons generated from sides/radius
    if (isRegular) {
      return errors // Assume generated points for regular polygons are valid
    }

    // Ensure points exist and is an array before checking for duplicates
    if (!Array.isArray(points) || points.length < 3) {
      console.warn('[applySpecificRules] Points array not found or invalid in properties.')
      return errors // Cannot check duplicates without valid points array
    }

    const pointStrings = new Set<string>()
    for (let i = 0; i < points.length; i++) {
      const point = points[i]
      if (!isValidPoint(point))
        continue

      // Use a fixed precision string key to handle minor floating point differences
      // const pointKey = `${point.x.toFixed(6)},${point.y.toFixed(6)}`;
      // --- OR --- Use exact string comparison (current approach, more likely to find true duplicates)
      const pointKey = `${point.x},${point.y}`

      if (pointStrings.has(pointKey)) {
        // Allow duplicate only if it's the last point matching the first point (closure)
        if (i === points.length - 1 && points.length > 0 && pointKey === `${points[0].x},${points[0].y}`) {
          continue
        }

        errors.push({
          code: ValidationErrorCode.DUPLICATE_POINTS,
          message: `多边形包含重复点：第${i}个点与之前的点重复。`, // Polygon contains duplicate points
          path: 'properties.points', // Path should refer to properties
          value: point,
        })
        // Consider breaking after first duplicate found?
        // break;
      }

      pointStrings.add(pointKey)
    }

    return errors
  }
}
