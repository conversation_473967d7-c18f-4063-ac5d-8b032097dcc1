/**
 * Core Configuration Module
 *
 * @remarks
 * This module provides configuration types and utilities for the core application.
 * It exports the CoreConfig interface, default configuration values, and helper
 * functions for creating and manipulating configuration objects.
 */

/**
 * Core configuration interface defining all configurable aspects of the core module.
 */
export interface CoreConfig {
  /**
   * Whether validation is enabled for shape operations.
   */
  validationEnabled: boolean

  /**
   * Whether to log debug information.
   */
  debugLoggingEnabled: boolean

  /**
   * Maximum number of shapes allowed in the repository.
   */
  maxShapesLimit: number

  /**
   * Default z-index for newly created shapes.
   */
  defaultZIndex: number

  /**
   * Whether to automatically select shapes after creation.
   */
  autoSelectAfterCreate: boolean
}

/**
 * Default core configuration values.
 */
export const defaultCoreConfig: CoreConfig = {
  validationEnabled: true,
  debugLoggingEnabled: false,
  maxShapesLimit: 1000,
  defaultZIndex: 1,
  autoSelectAfterCreate: true,
}

/**
 * Creates a new configuration object by merging the provided partial config
 * with the default configuration.
 *
 * @param partialConfig - Partial configuration to merge with defaults.
 * @returns A complete configuration object.
 */
export function createConfig(partialConfig: Partial<CoreConfig> = {}): CoreConfig {
  return {
    ...defaultCoreConfig,
    ...partialConfig,
  }
}
