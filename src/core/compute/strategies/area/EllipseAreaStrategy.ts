/**
 * Area Calculation Strategy for Ellipse and Circle Elements
 *
 * @remarks
 * This strategy implements the {@link AreaCalculatorStrategy} for calculating the
 * area of Ellipse ({@link CoreElementType.ELLIPSE}) and Circle
 * ({@link CoreElementType.CIRCLE}) elements.
 *
 * For Ellipse elements, it uses the `radiusX` and `radiusY` properties.
 * For Circle elements, it uses the `radius` property.
 * The actual area calculation is delegated to utility functions `calculateEllipseArea`
 * and `calculateCircleArea` from `../../../../lib/utils/geometry/ellipseUtils`.
 *
 * This strategy is registered for the `ELLIPSE` type but internally handles `CIRCLE`
 * as a special case of an ellipse.
 *
 * @module core/compute/strategies/area/EllipseAreaStrategy
 * @see {@link Shape.Ellipse}
 * @see {@link Shape.Circle}
 */
import type { AreaCalculatorStrategy } from '../../../../types/core/compute'
import type { Element } from '../../../../types/core/elementDefinitions'
import { calculateCircleArea, calculateEllipseArea } from '../../../../lib/utils/geometry/ellipseUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import { ElementType as CoreElementType } from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

export class EllipseAreaStrategy implements AreaCalculatorStrategy {
  /**
   * Returns all element types this strategy supports.
   * @remarks This strategy handles both `ELLIPSE` and `CIRCLE` types.
   * @returns An array of element types this strategy handles
   */
  public getElementType(): CoreElementType[] {
    return [CoreElementType.ELLIPSE, CoreElementType.CIRCLE]
  }

  /**
   * Calculates the area of an Ellipse or Circle element.
   *
   * @param element - The element for which to calculate the area. Expected to be
   *                  of type {@link CoreElementType.ELLIPSE} or {@link CoreElementType.CIRCLE}.
   * @returns The calculated area of the ellipse or circle.
   * @throws {@link CoreError} if the element is null/undefined.
   * @throws {@link CoreError} if the element type is not `ELLIPSE` or `CIRCLE`.
   * @throws {@link CoreError} if the radii (`radiusX`, `radiusY` for ellipse, or `radius` for circle)
   *         are invalid (e.g., non-numeric, zero, or negative).
   */
  public calculateArea(element: Element): number {
    if (element == null) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `[EllipseAreaStrategy] Error: Invalid element provided (null or undefined).`,
        undefined, // severity
        { component: 'EllipseAreaStrategy', operation: 'calculateArea' }, // context
      )
    }

    // 优先从 properties 取值，兼容顶层
    const el = element as { properties?: Record<string, unknown>, radius?: number, radiusX?: number, radiusY?: number }
    const props = el.properties ?? {} as Record<string, unknown>

    if (element.type === CoreElementType.CIRCLE) {
      // 对于圆形，优先从 properties.radius 获取，然后是顶层 radius
      const radius = typeof props.radius === 'number' ? props.radius : (typeof el.radius === 'number' ? el.radius : undefined)

      if (typeof radius !== 'number' || Number.isNaN(radius) || radius <= 0) {
        throw new CoreError(
          ErrorType.InvalidParameter,
          `[EllipseAreaStrategy] Invalid radius (${radius}) for Circle ID: ${element.id}.`,
          undefined,
          { component: 'EllipseAreaStrategy', operation: 'calculateArea', target: element.id, metadata: { radius } },
        )
      }
      return calculateCircleArea(radius)
    }
    else if (element.type === CoreElementType.ELLIPSE) {
      // 对于椭圆，优先从 properties 获取 radiusX 和 radiusY
      const rx = typeof props.radiusX === 'number' ? props.radiusX : (typeof el.radiusX === 'number' ? el.radiusX : undefined)
      const ry = typeof props.radiusY === 'number' ? props.radiusY : (typeof el.radiusY === 'number' ? el.radiusY : undefined)

      if (typeof rx !== 'number' || typeof ry !== 'number' || Number.isNaN(rx) || Number.isNaN(ry) || rx <= 0 || ry <= 0) {
        throw new CoreError(
          ErrorType.InvalidParameter,
          `[EllipseAreaStrategy] Invalid radii (rx: ${rx}, ry: ${ry}) for Ellipse ID: ${element.id}.`,
          undefined,
          { component: 'EllipseAreaStrategy', operation: 'calculateArea', target: element.id, metadata: { rx, ry } },
        )
      }
      return calculateEllipseArea(rx, ry)
    }
    else {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `EllipseAreaStrategy can only calculate area for Ellipse or Circle elements, got ${element.type}`,
        undefined,
        { component: 'EllipseAreaStrategy', operation: 'calculateArea', target: element.id },
      )
    }
  }
}
