import { describe, expect, it } from 'vitest'

// Test basic object utility functions
describe('object Utils - Basic Tests', () => {
  it('should handle basic object operations', () => {
    const obj = { a: 1, b: 2, c: 3 }

    expect(obj.a).toBe(1)
    expect(obj.b).toBe(2)
    expect(Object.keys(obj)).toEqual(['a', 'b', 'c'])
    expect(Object.values(obj)).toEqual([1, 2, 3])
    expect(Object.entries(obj)).toEqual([['a', 1], ['b', 2], ['c', 3]])
  })

  it('should handle object property checking', () => {
    const obj = { a: 1, b: 2, c: 3 }

    expect('a' in obj).toBe(true)
    expect('d' in obj).toBe(false)
    expect(obj.hasOwnProperty('b')).toBe(true)
    expect(obj.hasOwnProperty('d')).toBe(false)
  })

  it('should handle object copying', () => {
    const obj = { a: 1, b: 2, c: 3 }

    const shallowCopy = { ...obj }
    expect(shallowCopy).toEqual(obj)
    expect(shallowCopy).not.toBe(obj)

    const assignCopy = Object.assign({}, obj)
    expect(assignCopy).toEqual(obj)
    expect(assignCopy).not.toBe(obj)
  })

  it('should handle object merging', () => {
    const obj1 = { a: 1, b: 2 }
    const obj2 = { c: 3, d: 4 }

    const merged = { ...obj1, ...obj2 }
    expect(merged).toEqual({ a: 1, b: 2, c: 3, d: 4 })

    const assigned = Object.assign({}, obj1, obj2)
    expect(assigned).toEqual({ a: 1, b: 2, c: 3, d: 4 })
  })

  it('should handle object property deletion', () => {
    const obj = { a: 1, b: 2, c: 3 }

    delete obj.b
    expect(obj).toEqual({ a: 1, c: 3 })
    expect('b' in obj).toBe(false)
  })

  it('should handle nested objects', () => {
    const obj = {
      user: {
        name: 'John',
        age: 30,
        address: {
          city: 'New York',
          zip: '10001',
        },
      },
    }

    expect(obj.user.name).toBe('John')
    expect(obj.user.address.city).toBe('New York')
  })

  it('should handle object iteration', () => {
    const obj = { a: 1, b: 2, c: 3 }

    const keys = []
    const values = []

    for (const key in obj) {
      keys.push(key)
      values.push(obj[key])
    }

    expect(keys).toEqual(['a', 'b', 'c'])
    expect(values).toEqual([1, 2, 3])
  })

  it('should handle object freezing and sealing', () => {
    const obj = { a: 1, b: 2 }

    const frozen = Object.freeze({ ...obj })
    expect(Object.isFrozen(frozen)).toBe(true)

    const sealed = Object.seal({ ...obj })
    expect(Object.isSealed(sealed)).toBe(true)
  })
})
