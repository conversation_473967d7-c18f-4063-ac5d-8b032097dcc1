/**
 * Defines types for ellipse and circle shape elements.
 *
 * @remarks
 * This module provides the `Ellipse` interface and the `Circle` interface,
 * where `Circle` is a specialized form of `Ellipse`. These interfaces extend
 * {@link ShapeElement} to include properties specific to these geometric shapes.
 *
 * @module types/core/element/shape/ellipseShapeTypes
 */
import type { ShapeElement } from '../../elementDefinitions' // Corrected import path
import type Point from '@/types/core/element/geometry/point'

/**
 * Represents an ellipse shape, defined by its center and two radii (or points defining them).
 *
 * @remarks
 * An ellipse is typically defined by three control points:
 * - The center point, which defines its position.
 * - A point on its x-radius (major or semi-major axis).
 * - A point on its y-radius (minor or semi-minor axis).
 *
 * An ellipse becomes a circle when its x-radius and y-radius are equal.
 * For circle-specific operations or a more constrained type, see the {@link Circle} interface.
 *
 * @example
 * ```typescript
 * const myEllipse: Ellipse = {
 *   // Properties from ShapeElement (id, type, visible, etc.)
 *   id: 'ellipse-001',
 *   type: 'ellipse', // Or a more specific ElementType if defined
 *   visible: true,
 *   // ... other ShapeElement properties
 *   // Ellipse-specific properties
 *   points: [
 *     { x: 100, y: 100 }, // Center point
 *     { x: 150, y: 100 }, // Point defining x-radius (length 50)
 *     { x: 100, y: 130 }  // Point defining y-radius (length 30)
 *   ],
 *   // Methods are part of the implementation, not typically on the plain data object
 * };
 * ```
 *
 * @see {@link ShapeElement} for base shape properties.
 * @see {@link Point} for the structure of coordinate points.
 * @see {@link Circle} for the specialized case where radii are equal.
 */
export interface Ellipse extends ShapeElement {
  /**
   * An array of three {@link Point} objects defining the ellipse's geometry.
   *
   * @remarks
   * The points are typically interpreted in the following order:
   * 1. The center point of the ellipse.
   * 2. A point on the ellipse's x-radius (along its major or semi-major axis relative to the center).
   * 3. A point on the ellipse's y-radius (along its minor or semi-minor axis relative to the center).
   */
  points: [Point, Point, Point] // Center, X-radius point, Y-radius point

  /**
   * Properties specific to an Ellipse element.
   */
  properties: ShapeElement['properties'] & {
    width: number
    height: number
  }
}

/**
 * Represents a circle shape, which is a special case of an {@link Ellipse} where both radii are equal.
 *
 * @remarks
 * This interface extends `Ellipse` and adds a convenience property `radius`
 * for circles, as their x-radius and y-radius are identical.
 * For a circle, width and height in properties will be equal (2 * radius).
 *
 * @see {@link Ellipse} which this interface extends.
 */
export interface Circle extends Ellipse {
  /**
   * Properties specific to a Circle element, extending Ellipse properties.
   */
  properties: Ellipse['properties'] & { // Inherits width, height from Ellipse['properties']
    radius: number
  }
}
