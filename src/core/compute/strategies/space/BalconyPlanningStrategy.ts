// import Point from '@/types/core/element/geometry/point'; // Unused
import type {
  ErgonomicsEvaluationResult,
  PathwayCheckResult,
  SpacePlanningStrategy,
  SpaceType,
} from '@/types/core/compute'
/**
 * Balcony planning strategy implementation.
 */
import type {
  Element,
  ShapeElement, // Import ShapeElement
  // Path // Unused
} from '@/types/core/elementDefinitions'
// import { CoreError, ErrorType } from '@/services/system/error-service'; // Unused
// import {
//     calculateSpaceUtilization,
//     checkPathwaysWidth
// } from '@/lib/utils/space/spacePlanning'; // Module not found
import { BoundingBoxClass } from '@/lib/utils/geometry' // BoundingBoxClass is exported from here
import {
  ElementType as CoreElementType, // Import ShapeElement
  // Path // Unused
} from '@/types/core/elementDefinitions'

/**
 * Helper to get BoundingBoxClass from an Element.
 *
 * @param element - The element to get the bounding box for.
 * @returns A BoundingBoxClass instance or null if the element type is not supported.
 * @private
 */
function getElementBoundingBoxClass(element: Element): BoundingBoxClass | null {
  if (element.type === CoreElementType.RECTANGLE || element.type === CoreElementType.SQUARE) {
    // Assert element to ShapeElement first to access common properties like position
    // Then, if properties exist, treat them as potentially having width/height
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement.position != null && shapeElement.properties != null
      && typeof shapeElement.properties.width === 'number'
      && typeof shapeElement.properties.height === 'number') {
      // Assuming position is center for rectangle/square as per previous creator logic
      return new BoundingBoxClass(
        shapeElement.position.x - shapeElement.properties.width / 2,
        shapeElement.position.y - shapeElement.properties.height / 2,
        shapeElement.properties.width,
        shapeElement.properties.height,
      )
    }
  }
  console.warn(`[BalconyPlanningStrategy] getElementBoundingBoxClass: Bounding box calculation for type ${element.type} not fully implemented or properties missing.`)
  return null
}

/**
 * Strategy for planning balcony spaces with appropriate furniture and clearances.
 *
 * @implements SpacePlanningStrategy
 */
export class BalconyPlanningStrategy implements SpacePlanningStrategy {
  /**
   * Calculates how efficiently the balcony space is being utilized.
   *
   * @returns A utilization percentage between 0 and 100.
   * @throws {CoreError} If the room boundary element is invalid.
   */
  public calculateSpaceUtilization(/* elements: Element[], roomBoundaryElement: Element */): number { // Params unused due to commented out body
    // const roomBBoxClass = getElementBoundingBoxClass(roomBoundaryElement);
    // if (!roomBBoxClass) {
    //   throw new CoreError( ErrorType.INVALID_PARAMETER, 'Room boundary must be an element from which a bounding box can be derived.');
    // }
    // return calculateSpaceUtilization(elements, roomBBoxClass);
    console.warn('[BalconyPlanningStrategy] calculateSpaceUtilization is temporarily disabled due to missing dependencies.')
    return 0
  }

  /**
   * Checks if pathways on the balcony have sufficient width for comfortable movement.
   *
   * @returns An array of pathway check results.
   * @throws {CoreError} If any parameters are invalid.
   */
  public checkPathwayWidth(/* elements: Element[], pathways: Path.Line[], minWidth: number */): PathwayCheckResult[] { // Params unused due to commented out body
    // if (!Array.isArray(elements)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Elements must be an array');
    // if (!Array.isArray(pathways)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Pathways must be an array');

    // const accessibleMinWidth = Math.max(minWidth, 0.9);

    // const formattedPathways = pathways.map((path, index) => {
    //     const lineForUtil = {
    //         id: path.id ?? `pathway-${index}`,
    //         start: path.start as Point,
    //         end: path.end as Point,
    //         type: CoreElementType.LINE,
    //         getSubType: () => CoreElementType.LINE,
    //     } as any; // Changed to 'as any' to bypass strict type checking for now.
    //               // This assumes 'checkPathwaysWidth' primarily uses .start and .end.
    //               // TODO: Refactor checkPathwaysWidth to accept Path.Line[] or IPoint[] directly.
    //     return { id: path.id ?? `pathway-${index + 1}`, line: lineForUtil as import('@/lib/utils/element/path/lineImplementation').Line };
    // });

    // return checkPathwaysWidth(elements, formattedPathways, accessibleMinWidth);
    console.warn('[BalconyPlanningStrategy] checkPathwayWidth is temporarily disabled due to missing dependencies.')
    return []
  }

  /**
   * Evaluates the ergonomics of the balcony layout, particularly for outdoor workspace setups.
   *
   * @param _elements - The elements within the balcony space.
   * @param deskElement - Optional desk element for outdoor workspace evaluation.
   * @param chairElement - Optional chair element for outdoor workspace evaluation.
   * @returns An ergonomics evaluation result with issues and recommendations.
   */
  public evaluateErgonomics(
    _elements: Element[],
    deskElement: Element,
    chairElement: Element,
  ): ErgonomicsEvaluationResult {
    const issues: string[] = []
    const recommendations: string[] = []

    if (deskElement == null || chairElement == null) {
      recommendations.push('Desk and chair elements are required for ergonomics evaluation.')
    }

    const deskBBox = getElementBoundingBoxClass(deskElement)
    if (deskBBox && (deskBBox.width < 0.8 || deskBBox.height < 0.6) && deskElement.type === CoreElementType.FURNITURE) {
      issues.push('Desk dimensions might be too small for comfortable outdoor workspace if used as such.')
    }

    recommendations.push('For outdoor workspaces, ensure desk and chair are weather-resistant.')
    recommendations.push('Position seating to maximize views and protect from elements if possible.')

    return {
      isValid: issues.length === 0,
      issues,
      recommendations,
    }
  }

  /**
   * Gets the type of space this strategy is designed for.
   *
   * @returns The space type identifier ('balcony').
   */
  public getSpaceType(): SpaceType {
    return 'balcony'
  }
}
