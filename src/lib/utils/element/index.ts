/**
 * Element Utilities Index
 *
 * @remarks
 * This module serves as the central entry point for all element-related utility functions
 * and namespaces within the `lib/utils/element` directory. It re-exports functionalities
 * from various submodules, such as:
 * - `elementUtils`: Common element operations.
 * - `shapeElementUtils`: Utilities specific to shape elements.
 * - `pathElementUtils`: Utilities specific to path elements.
 * - `designElementUtils`: Utilities specific to design elements.
 * - `transformUtils`: Functions for element transformations (rotate, scale, move).
 * - `elementTypeUtils`: Helpers for checking and categorizing element types.
 * - `elementModelUtils`: Utilities for accessing and validating element model properties.
 *
 * Additionally, it exports a consolidated namespace `AllElementUtilities` which groups
 * these utilities for easier access.
 *
 * @module lib/utils/element/index
 */

import { DesignUtils } from './designElementUtils'
import * as ElementModelUtilsModule from './elementModelUtils'
import * as ElementTypeUtilsModule from './elementTypeUtils'
// Export all element utilities
// Export element utilities namespaces
import * as ElementUtilsModule from './elementUtils'
import { PathUtils } from './pathElementUtils'
import { ShapeUtils } from './shapeElementUtils'
import { TransformationUtils } from './transformUtils'

export * from './cleanPattern'
export * from './designElementUtils'
// export * from './path'; // Removed as src/lib/utils/element/path/ was deleted
// export * from './shape'; // Removed as src/lib/utils/element/shape/ was deleted
export * from './elementModelUtils'
export * from './elementTypeUtils'
export * from './elementUtils'
export * from './pathElementUtils'
export * from './shapeElementUtils'
export * from './transformUtils'

/**
 * Element utilities namespace
 *
 * This namespace provides organized access to all element utility functions,
 * grouped by their domain and purpose. It serves as a central access point
 * for all element-related operations in the application.
 */
export const AllElementUtilities = { // Renamed to avoid conflict with ElementUtils class
  // Common element operations (type checking, ID generation, etc.)
  Common: ElementUtilsModule,

  // Shape-specific operations (area, perimeter, etc.)
  Shape: ShapeUtils,

  // Path-specific operations (length, bounding box, etc.)
  Path: PathUtils,

  // Design-specific operations (room, wall calculations, etc.)
  Design: DesignUtils,

  // Transformation operations (rotate, scale, move, etc.)
  Transform: TransformationUtils,

  // Element type utilities (type checking, categorization, etc.)
  Type: ElementTypeUtilsModule,

  // Element model utilities (property extraction, type checking, etc.)
  Model: ElementModelUtilsModule,
}
