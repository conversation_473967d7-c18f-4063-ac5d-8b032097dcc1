import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'

// Mock event bus implementation
class MockEventBus {
  private listeners: Map<string, Array<(payload: any) => void>> = new Map()

  subscribe(eventType: string, handler: (payload: any) => void): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, [])
    }

    this.listeners.get(eventType)!.push(handler)

    // Return unsubscribe function
    return () => {
      const handlers = this.listeners.get(eventType)
      if (handlers) {
        const index = handlers.indexOf(handler)
        if (index > -1) {
          handlers.splice(index, 1)
        }
      }
    }
  }

  publish(event: { type: string; payload?: any }): void {
    const handlers = this.listeners.get(event.type)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(event.payload)
        } catch (error) {
          console.error(`Error in event handler for ${event.type}:`, error)
        }
      })
    }
  }

  on(eventType: string, handler: (payload: any) => void): void {
    this.subscribe(eventType, handler)
  }

  off(eventType: string, handler: (payload: any) => void): void {
    const handlers = this.listeners.get(eventType)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  clear(): void {
    this.listeners.clear()
  }

  getListenerCount(eventType: string): number {
    return this.listeners.get(eventType)?.length || 0
  }
}

describe('Event Bus', () => {
  let eventBus: MockEventBus

  beforeEach(() => {
    eventBus = new MockEventBus()
  })

  afterEach(() => {
    eventBus.clear()
  })

  describe('Event Subscription', () => {
    it('should subscribe to events', () => {
      const handler = vi.fn()

      eventBus.subscribe('test-event', handler)

      expect(eventBus.getListenerCount('test-event')).toBe(1)
    })

    it('should allow multiple subscriptions to same event', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()

      eventBus.subscribe('test-event', handler1)
      eventBus.subscribe('test-event', handler2)

      expect(eventBus.getListenerCount('test-event')).toBe(2)
    })

    it('should return unsubscribe function', () => {
      const handler = vi.fn()

      const unsubscribe = eventBus.subscribe('test-event', handler)
      expect(eventBus.getListenerCount('test-event')).toBe(1)

      unsubscribe()
      expect(eventBus.getListenerCount('test-event')).toBe(0)
    })

    it('should handle subscription to different events', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()

      eventBus.subscribe('event-1', handler1)
      eventBus.subscribe('event-2', handler2)

      expect(eventBus.getListenerCount('event-1')).toBe(1)
      expect(eventBus.getListenerCount('event-2')).toBe(1)
    })
  })

  describe('Event Publishing', () => {
    it('should publish events to subscribers', () => {
      const handler = vi.fn()
      const payload = { data: 'test' }

      eventBus.subscribe('test-event', handler)
      eventBus.publish({ type: 'test-event', payload })

      expect(handler).toHaveBeenCalledWith(payload)
      expect(handler).toHaveBeenCalledTimes(1)
    })

    it('should publish to multiple subscribers', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      const payload = { data: 'test' }

      eventBus.subscribe('test-event', handler1)
      eventBus.subscribe('test-event', handler2)
      eventBus.publish({ type: 'test-event', payload })

      expect(handler1).toHaveBeenCalledWith(payload)
      expect(handler2).toHaveBeenCalledWith(payload)
    })

    it('should not affect other event types', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()

      eventBus.subscribe('event-1', handler1)
      eventBus.subscribe('event-2', handler2)
      eventBus.publish({ type: 'event-1', payload: 'test' })

      expect(handler1).toHaveBeenCalledWith('test')
      expect(handler2).not.toHaveBeenCalled()
    })

    it('should handle events with no payload', () => {
      const handler = vi.fn()

      eventBus.subscribe('test-event', handler)
      eventBus.publish({ type: 'test-event' })

      expect(handler).toHaveBeenCalledWith(undefined)
    })

    it('should handle events with null payload', () => {
      const handler = vi.fn()

      eventBus.subscribe('test-event', handler)
      eventBus.publish({ type: 'test-event', payload: null })

      expect(handler).toHaveBeenCalledWith(null)
    })
  })

  describe('Event Unsubscription', () => {
    it('should unsubscribe using returned function', () => {
      const handler = vi.fn()

      const unsubscribe = eventBus.subscribe('test-event', handler)
      eventBus.publish({ type: 'test-event', payload: 'before' })

      unsubscribe()
      eventBus.publish({ type: 'test-event', payload: 'after' })

      expect(handler).toHaveBeenCalledTimes(1)
      expect(handler).toHaveBeenCalledWith('before')
    })

    it('should unsubscribe using off method', () => {
      const handler = vi.fn()

      eventBus.on('test-event', handler)
      eventBus.publish({ type: 'test-event', payload: 'before' })

      eventBus.off('test-event', handler)
      eventBus.publish({ type: 'test-event', payload: 'after' })

      expect(handler).toHaveBeenCalledTimes(1)
      expect(handler).toHaveBeenCalledWith('before')
    })

    it('should only unsubscribe specific handler', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()

      const unsubscribe1 = eventBus.subscribe('test-event', handler1)
      eventBus.subscribe('test-event', handler2)

      unsubscribe1()
      eventBus.publish({ type: 'test-event', payload: 'test' })

      expect(handler1).not.toHaveBeenCalled()
      expect(handler2).toHaveBeenCalledWith('test')
    })
  })

  describe('Error Handling', () => {
    it('should handle errors in event handlers', () => {
      const errorHandler = vi.fn(() => {
        throw new Error('Handler error')
      })
      const normalHandler = vi.fn()
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      eventBus.subscribe('test-event', errorHandler)
      eventBus.subscribe('test-event', normalHandler)

      eventBus.publish({ type: 'test-event', payload: 'test' })

      expect(errorHandler).toHaveBeenCalled()
      expect(normalHandler).toHaveBeenCalled()
      expect(consoleSpy).toHaveBeenCalled()

      consoleSpy.mockRestore()
    })

    it('should handle publishing to non-existent event type', () => {
      expect(() => {
        eventBus.publish({ type: 'non-existent', payload: 'test' })
      }).not.toThrow()
    })

    it('should handle unsubscribing non-existent handler', () => {
      const handler = vi.fn()

      expect(() => {
        eventBus.off('non-existent', handler)
      }).not.toThrow()
    })

    it('should handle multiple unsubscriptions', () => {
      const handler = vi.fn()

      const unsubscribe = eventBus.subscribe('test-event', handler)
      unsubscribe()

      expect(() => {
        unsubscribe() // Second call should not throw
      }).not.toThrow()
    })
  })

  describe('Complex Event Scenarios', () => {
    it('should handle rapid subscribe/unsubscribe cycles', () => {
      const handler = vi.fn()

      for (let i = 0; i < 100; i++) {
        const unsubscribe = eventBus.subscribe('test-event', handler)
        unsubscribe()
      }

      eventBus.publish({ type: 'test-event', payload: 'test' })
      expect(handler).not.toHaveBeenCalled()
    })

    it('should handle subscription during event handling', () => {
      const handler1 = vi.fn(() => {
        eventBus.subscribe('test-event', handler2)
      })
      const handler2 = vi.fn()

      eventBus.subscribe('test-event', handler1)
      eventBus.publish({ type: 'test-event', payload: 'test' })

      expect(handler1).toHaveBeenCalled()
      expect(eventBus.getListenerCount('test-event')).toBe(2)
    })

    it('should handle unsubscription during event handling', () => {
      let unsubscribe2: (() => void) | undefined

      const handler1 = vi.fn(() => {
        if (unsubscribe2) unsubscribe2()
      })
      const handler2 = vi.fn()

      eventBus.subscribe('test-event', handler1)
      unsubscribe2 = eventBus.subscribe('test-event', handler2)

      eventBus.publish({ type: 'test-event', payload: 'test' })

      expect(handler1).toHaveBeenCalled()
      // Note: handler2 might not be called if unsubscribed during iteration
      // This depends on the implementation details
      expect(eventBus.getListenerCount('test-event')).toBe(1)
    })
  })

  describe('Performance', () => {
    it('should handle many subscribers efficiently', () => {
      const handlers = Array.from({ length: 1000 }, () => vi.fn())

      const startTime = Date.now()

      handlers.forEach(handler => {
        eventBus.subscribe('test-event', handler)
      })

      eventBus.publish({ type: 'test-event', payload: 'test' })

      const endTime = Date.now()

      handlers.forEach(handler => {
        expect(handler).toHaveBeenCalledWith('test')
      })

      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly
    })

    it('should handle many events efficiently', () => {
      const handler = vi.fn()
      eventBus.subscribe('test-event', handler)

      const startTime = Date.now()

      for (let i = 0; i < 1000; i++) {
        eventBus.publish({ type: 'test-event', payload: i })
      }

      const endTime = Date.now()

      expect(handler).toHaveBeenCalledTimes(1000)
      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly
    })
  })
})
