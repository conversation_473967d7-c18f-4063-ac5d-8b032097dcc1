import type {
  CostCalculationOptions,
  CostCalculatorStrategy,
} from '@/types/core/compute/costComputeTypes'

import type { Element } from '@/types/core/element/element'
import { describe, expect, it } from 'vitest'

describe('cost Calculator Strategy Interface', () => {
  it('should define the required methods for cost calculation strategies', () => {
    // Create a mock implementation of CostCalculatorStrategy
    class MockCostCalculator implements CostCalculatorStrategy {
      calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
        // Simple mock implementation
        return unitCost * 100
      }

      getElementType(): string {
        return 'mock-element'
      }
    }

    const calculator = new MockCostCalculator()

    // Test the interface implementation
    expect(typeof calculator.calculateCost).toBe('function')
    expect(typeof calculator.getElementType).toBe('function')

    // Test the mock implementation
    const mockElement = { id: 'test-element' } as Element
    expect(calculator.calculateCost(mockElement, 10)).toBe(1000)
    expect(calculator.getElementType()).toBe('mock-element')
  })

  it('should allow implementation for different cost calculation types', () => {
    // Create a mock area-based cost calculator
    class MockAreaCostCalculator implements CostCalculatorStrategy {
      calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
        // Mock implementation for area-based calculation
        const mockArea = 50 // Simulated area
        return mockArea * unitCost
      }

      getElementType(): string {
        return 'rectangle'
      }
    }

    // Create a mock perimeter-based cost calculator
    class MockPerimeterCostCalculator implements CostCalculatorStrategy {
      calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
        // Mock implementation for perimeter-based calculation
        const mockPerimeter = 30 // Simulated perimeter
        return mockPerimeter * unitCost
      }

      getElementType(): string {
        return 'line'
      }
    }

    // Create a mock unit-based cost calculator
    class MockUnitCostCalculator implements CostCalculatorStrategy {
      calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
        // Mock implementation for unit-based calculation
        const quantity = options?.quantity || 1
        return quantity * unitCost
      }

      getElementType(): string {
        return 'fixture'
      }
    }

    const areaCostCalculator = new MockAreaCostCalculator()
    const perimeterCostCalculator = new MockPerimeterCostCalculator()
    const unitCostCalculator = new MockUnitCostCalculator()

    // Test the implementations
    const mockElement = { id: 'test-element' } as Element

    expect(areaCostCalculator.calculateCost(mockElement, 20)).toBe(1000) // 50 * 20
    expect(areaCostCalculator.getElementType()).toBe('rectangle')

    expect(perimeterCostCalculator.calculateCost(mockElement, 15)).toBe(450) // 30 * 15
    expect(perimeterCostCalculator.getElementType()).toBe('line')

    expect(unitCostCalculator.calculateCost(mockElement, 100)).toBe(100) // 1 * 100
    expect(unitCostCalculator.calculateCost(mockElement, 100, { quantity: 5 })).toBe(500) // 5 * 100
    expect(unitCostCalculator.getElementType()).toBe('fixture')
  })

  it('should handle cost calculation options correctly', () => {
    // Create a mock cost calculator that uses all options
    class MockComprehensiveCostCalculator implements CostCalculatorStrategy {
      calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
        // Base cost calculation
        let baseCost = 0

        // Use the appropriate calculation method based on costType
        if (options?.costType === 'area') {
          const mockArea = 50
          baseCost = mockArea * unitCost
        }
        else if (options?.costType === 'perimeter') {
          const mockPerimeter = 30
          baseCost = mockPerimeter * unitCost
        }
        else if (options?.costType === 'unit') {
          const quantity = options?.quantity || 1
          baseCost = quantity * unitCost
        }
        else {
          // Default to unit-based
          baseCost = unitCost
        }

        // Apply material, labor, design, and overhead costs if provided
        if (options?.materialCostPerUnit) {
          baseCost += options.materialCostPerUnit
        }

        if (options?.laborCostPerUnit) {
          baseCost += options.laborCostPerUnit
        }

        if (options?.designCostPerUnit) {
          baseCost += options.designCostPerUnit
        }

        if (options?.overheadCostPerUnit) {
          baseCost += options.overheadCostPerUnit
        }

        // Add installation cost if provided
        if (options?.installationCost) {
          baseCost += options.installationCost
        }

        // Add additional cost if provided
        if (options?.additionalCost) {
          baseCost += options.additionalCost
        }

        // Apply discount if provided
        if (options?.discountRate) {
          baseCost *= (1 - options.discountRate)
        }

        // Apply tax if provided
        if (options?.taxRate) {
          baseCost *= (1 + options.taxRate)
        }

        return baseCost
      }

      getElementType(): string {
        return 'comprehensive'
      }
    }

    const comprehensiveCostCalculator = new MockComprehensiveCostCalculator()
    const mockElement = { id: 'test-element' } as Element

    // Test with area-based calculation
    expect(comprehensiveCostCalculator.calculateCost(mockElement, 20, {
      costType: 'area',
    })).toBe(1000) // 50 * 20

    // Test with perimeter-based calculation
    expect(comprehensiveCostCalculator.calculateCost(mockElement, 15, {
      costType: 'perimeter',
    })).toBe(450) // 30 * 15

    // Test with unit-based calculation and quantity
    expect(comprehensiveCostCalculator.calculateCost(mockElement, 100, {
      costType: 'unit',
      quantity: 5,
    })).toBe(500) // 5 * 100

    // Test with additional costs
    expect(comprehensiveCostCalculator.calculateCost(mockElement, 100, {
      costType: 'unit',
      quantity: 1,
      materialCostPerUnit: 20,
      laborCostPerUnit: 30,
      designCostPerUnit: 10,
      overheadCostPerUnit: 15,
      installationCost: 25,
      additionalCost: 50,
    })).toBe(250) // 100 + 20 + 30 + 10 + 15 + 25 + 50

    // Test with discount and tax
    expect(comprehensiveCostCalculator.calculateCost(mockElement, 100, {
      costType: 'unit',
      quantity: 1,
      discountRate: 0.1, // 10% discount
      taxRate: 0.2, // 20% tax
    })).toBe(108) // 100 * 0.9 * 1.2 = 108
  })

  it('should allow for error handling in implementations', () => {
    // Create a mock cost calculator with error handling
    class MockErrorHandlingCostCalculator implements CostCalculatorStrategy {
      calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
        // Check if element has required properties
        if (!element?.id) {
          throw new Error('Invalid element')
        }

        // Check if unit cost is valid
        if (unitCost < 0) {
          throw new Error('Unit cost must be non-negative')
        }

        // Check if options are valid
        if (options?.discountRate && (options.discountRate < 0 || options.discountRate > 1)) {
          throw new Error('Discount rate must be between 0 and 1')
        }

        if (options?.taxRate && (options.taxRate < 0 || options.taxRate > 1)) {
          throw new Error('Tax rate must be between 0 and 1')
        }

        // Mock implementation
        return unitCost * 100
      }

      getElementType(): string {
        return 'error-handling-element'
      }
    }

    const errorHandlingCalculator = new MockErrorHandlingCostCalculator()

    // Test valid case
    const validElement = { id: 'test-element' } as Element
    expect(errorHandlingCalculator.calculateCost(validElement, 10)).toBe(1000)

    // Test error cases
    const invalidElement = {} as Element
    expect(() => errorHandlingCalculator.calculateCost(invalidElement, 10)).toThrow('Invalid element')
    expect(() => errorHandlingCalculator.calculateCost(validElement, -10)).toThrow('Unit cost must be non-negative')
    expect(() => errorHandlingCalculator.calculateCost(validElement, 10, { discountRate: 1.5 })).toThrow('Discount rate must be between 0 and 1')
    expect(() => errorHandlingCalculator.calculateCost(validElement, 10, { taxRate: -0.2 })).toThrow('Tax rate must be between 0 and 1')
  })

  it('should validate CostCalculationOptions interface', () => {
    // Create valid options objects of different types
    const areaBasedOptions: CostCalculationOptions = {
      costType: 'area',
      materialCostPerUnit: 20,
      laborCostPerUnit: 30,
      designCostPerUnit: 10,
      overheadCostPerUnit: 15,
      additionalCost: 50,
      discountRate: 0.1,
      taxRate: 0.2,
    }

    const perimeterBasedOptions: CostCalculationOptions = {
      costType: 'perimeter',
      materialCostPerUnit: 15,
      laborCostPerUnit: 25,
      installationCost: 100,
      additionalCost: 30,
      discountRate: 0.05,
      taxRate: 0.15,
    }

    const unitBasedOptions: CostCalculationOptions = {
      costType: 'unit',
      quantity: 5,
      installationCost: 50,
      additionalCost: 20,
      discountRate: 0.2,
      taxRate: 0.1,
    }

    // Verify the options objects are valid
    expect(areaBasedOptions.costType).toBe('area')
    expect(areaBasedOptions.materialCostPerUnit).toBe(20)
    expect(areaBasedOptions.laborCostPerUnit).toBe(30)

    expect(perimeterBasedOptions.costType).toBe('perimeter')
    expect(perimeterBasedOptions.materialCostPerUnit).toBe(15)
    expect(perimeterBasedOptions.installationCost).toBe(100)

    expect(unitBasedOptions.costType).toBe('unit')
    expect(unitBasedOptions.quantity).toBe(5)
    expect(unitBasedOptions.installationCost).toBe(50)
  })
})
