import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { PolygonAreaStrategy } from '@/core/compute/strategies/area/PolygonAreaStrategy'
import { Point } from '@/types/core/element/geometry/point'
import { Polygon } from '@/types/core/element/shape/shape'

// Mock non-polygon element
class MockRectangle {
  id = 'mock-rectangle'
  getSubType() {
    return 'rectangle'
  }
}

/**
 * Unit tests for the PolygonAreaStrategy class
 */
describe('polygonAreaStrategy', () => {
  let strategy: PolygonAreaStrategy

  beforeEach(() => {
    strategy = new PolygonAreaStrategy()
    // Spy on console methods
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(console, 'debug').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('polygon')
  })

  it('should calculate area of a square correctly', () => {
    // Create a square with vertices at (0,0), (0,5), (5,5), and (5,0)
    // Area should be 5 * 5 = 25
    const points = [
      new Point(0, 0),
      new Point(0, 5),
      new Point(5, 5),
      new Point(5, 0),
    ]

    const polygon = new Polygon('square-test', {
      type: 'polygon',
      points: points.map(p => ({ x: p.x, y: p.y })),
    })
    const area = strategy.calculateArea(polygon)

    expect(area).toBe(25)
  })

  it('should calculate area of a triangle correctly', () => {
    // Create a triangle with vertices at (0,0), (0,5), and (5,0)
    // Area should be (5 * 5) / 2 = 12.5
    const points = [
      new Point(0, 0),
      new Point(0, 5),
      new Point(5, 0),
    ]

    const polygon = new Polygon('triangle-test', {
      type: 'triangle',
      points: points.map(p => ({ x: p.x, y: p.y })),
    })
    const area = strategy.calculateArea(polygon)

    expect(area).toBe(12.5)
  })

  it('should return 0 for polygon with fewer than 3 vertices', () => {
    // We can't directly create a Polygon with fewer than 3 points
    // as the constructor would throw an error, so we'll modify a valid polygon
    const points = [
      new Point(0, 0),
      new Point(5, 5),
      new Point(10, 0),
    ]

    const polygon = new Polygon('few-points-test', {
      type: 'polygon',
      points: points.map(p => ({ x: p.x, y: p.y })),
    });

    // Manually override the internal points array to simulate fewer than 3 points
    (polygon as any)._points = [new Point(0, 0), new Point(5, 5)]

    const area = strategy.calculateArea(polygon)

    expect(area).toBe(0)
  })

  it('should calculate area of a complex polygon correctly', () => {
    // Create a complex polygon (concave)
    const points = [
      new Point(0, 0),
      new Point(0, 5),
      new Point(3, 3),
      new Point(5, 5),
      new Point(5, 0),
    ]

    const polygon = new Polygon('complex-test', {
      type: 'polygon',
      points: points.map(p => ({ x: p.x, y: p.y })),
    })
    const area = strategy.calculateArea(polygon)

    // Expected area calculated using shoelace formula
    expect(area).toBe(20)
  })

  it('should throw error if element is not a polygon', () => {
    const rectangle = new MockRectangle()

    expect(() => strategy.calculateArea(rectangle as any)).toThrow('Element received is not a Polygon instance')
  })

  it('should handle real Polygon instance', () => {
    // Create a mock that looks like a real Polygon instance
    const realPolygon = {
      id: 'real-polygon-id',
      constructor: { name: 'Polygon' },
      getPoints: () => [
        new Point(0, 0),
        new Point(0, 5),
        new Point(5, 5),
        new Point(5, 0),
      ],
      getSubType: () => 'polygon',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolygon, Polygon.prototype)

    const area = strategy.calculateArea(realPolygon as any)
    expect(area).toBe(25)
  })

  it('should handle invalid points in real Polygon instance', () => {
    // Create a mock that looks like a real Polygon instance with invalid points
    const realPolygon = {
      id: 'real-polygon-id',
      constructor: { name: 'Polygon' },
      getPoints: () => [
        new Point(0, 0),
        new Point(0, 5),
        null as any,
        new Point(5, 0),
      ],
      getSubType: () => 'polygon',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolygon, Polygon.prototype)

    const area = strategy.calculateArea(realPolygon as any)
    expect(isNaN(area)).toBe(true)
  })

  it('should handle null points array in real Polygon instance', () => {
    // Create a mock that looks like a real Polygon instance with null points array
    const realPolygon = {
      id: 'real-polygon-id',
      constructor: { name: 'Polygon' },
      getPoints: () => null,
      getSubType: () => 'polygon',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolygon, Polygon.prototype)

    const area = strategy.calculateArea(realPolygon as any)
    expect(area).toBe(0)
  })
  it('should handle null element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    const result = strategy.calculateArea(null)

    expect(isNaN(result)).toBe(true)
    expect(console.error).toHaveBeenCalledWith(
      expect.stringContaining('Invalid element provided (null)'),
    )
  })

  it('should handle undefined element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    const result = strategy.calculateArea(undefined)

    expect(isNaN(result)).toBe(true)
    expect(console.error).toHaveBeenCalledWith(
      expect.stringContaining('Invalid element provided (undefined)'),
    )
  })

  it('should handle error when getPoints throws an exception', () => {
    // Create a mock Polygon that throws an error when getPoints is called
    const mockPolygon = {
      id: 'error-polygon',
      constructor: { name: 'Polygon' },
      getPoints: () => { throw new Error('Test error in getPoints') },
    }

    // Make instanceof check pass
    Object.setPrototypeOf(mockPolygon, Polygon.prototype)

    const result = strategy.calculateArea(mockPolygon as any)

    expect(isNaN(result)).toBe(true)
    expect(console.error).toHaveBeenCalledWith(
      expect.stringContaining('Error retrieving points for polygon error-polygon'),
      expect.any(Error),
    )
  })
})
