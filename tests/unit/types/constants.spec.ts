import { describe, expect, it } from 'vitest'
import {
  CANVAS_LAYERS,
  DEFAULT_CORNER_RADIUS,
  DEFAULT_FILL_COLOR,
  DEFAULT_FONT_FAMILY,
  DEFAULT_FONT_SIZE,
  DEFAULT_GRID_COLOR,
  DEFAULT_GRID_OPACITY,
  DEFAULT_GRID_SIZE,
  DEFAULT_LINE_STYLE,
  DEFAULT_STROKE_COLOR,
  DEFAULT_STROKE_WIDTH,
  DEFAULT_ZOOM_LEVEL,
  DEFAULT_ZOOM_MAX,
  DEFAULT_ZOOM_MIN,
  DEFAULT_ZOOM_STEP,
  LAYER_CEILING,
  LAYER_FLOOR,
  LAYER_FURNITURE,
  UNIT,
} from '@/types/constants'

describe('application Constants', () => {
  it('should export correct constant values', () => {
    expect(UNIT).toBe(100)
    expect(DEFAULT_STROKE_WIDTH).toBe(2)
    expect(DEFAULT_STROKE_COLOR).toBe('#000000')
    expect(DEFAULT_FILL_COLOR).toBe('#FFFFFF')
    expect(DEFAULT_FONT_FAMILY).toBe('Arial, sans-serif')
    expect(DEFAULT_FONT_SIZE).toBe(16)
    expect(DEFAULT_LINE_STYLE).toBe('solid')
    expect(DEFAULT_CORNER_RADIUS).toBe(0)
    expect(DEFAULT_GRID_SIZE).toBe(20)
    expect(DEFAULT_GRID_COLOR).toBe('#CCCCCC')
    expect(DEFAULT_GRID_OPACITY).toBe(0.5)
    expect(DEFAULT_ZOOM_LEVEL).toBe(1)
    expect(DEFAULT_ZOOM_STEP).toBe(0.1)
    expect(DEFAULT_ZOOM_MIN).toBe(0.1)
    expect(DEFAULT_ZOOM_MAX).toBe(10)
    expect(LAYER_FLOOR).toBe('FLOOR')
    expect(LAYER_FURNITURE).toBe('FURNITURE')
    expect(LAYER_CEILING).toBe('CEILING')
    // Use toStrictEqual for array comparison
    expect(CANVAS_LAYERS).toStrictEqual(['FLOOR', 'FURNITURE', 'CEILING'])
  })

  // Optional: Add tests to ensure types are correct, though TS handles this mostly
  it('should have correct types (basic check)', () => {
    expect(typeof UNIT).toBe('number')
    expect(typeof DEFAULT_STROKE_COLOR).toBe('string')
    // Check array type (loosely)
    expect(Array.isArray(CANVAS_LAYERS)).toBe(true)
    // Check if it's a readonly array (`as const`)
    // This is a runtime check, TS ensures readonly at compile time
    // expect(Object.isFrozen(CANVAS_LAYERS)).toBe(true); // NOTE: This might fail depending on JS engine/test env
    expect(true).toBe(true) // Placeholder, relying on TS compilation for immutability
  })
})
