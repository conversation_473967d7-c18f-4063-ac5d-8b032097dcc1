import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ErrorType } from '@/core/errors/CoreError'
import { ElementFactory } from '@/core/factory'
import { ShapeRepository } from '@/core/state/ShapeRepository'

import * as validationUtils from '@/core/utils/validationUtils'
import { ElementValidator } from '@/core/validator'
// Import after mocking
import { ShapeCreationService } from '@/services/shape-actions/ShapeCreationService'
import { ElementType } from '@/types/core/shape-type'
import { AppEventType } from '@/types/services/events'

// Mock dependencies
vi.mock('@/core/factory', () => ({
  ElementFactory: vi.fn().mockImplementation(() => ({
    createShape: vi.fn().mockImplementation((type, data) => {
      if (type === ElementType.RECTANGLE) {
        return {
          id: 'mock-rectangle-id',
          type: ElementType.RECTANGLE,
          position: data.position,
          properties: data.properties,
        }
      }
      return null
    }),
  })),
}))

vi.mock('@/core/state/ShapeRepository', () => ({
  ShapeRepository: vi.fn().mockImplementation(() => ({
    add: vi.fn().mockImplementation((shape) => {
      if (shape.id === 'error-shape') {
        throw new Error('Repository error')
      }
      return true
    }),
  })),
}))

vi.mock('@/core/validator', () => ({
  ElementValidator: {
    validateElement: vi.fn().mockResolvedValue({
      isValid: true,
      errors: [],
    }),
  },
}))

vi.mock('@/core/utils/geometryUtils', () => ({
  ensurePointInstance: vi.fn().mockImplementation((point) => {
    if (!point || typeof point.x !== 'number' || typeof point.y !== 'number') {
      throw new Error('Invalid point')
    }
    return {
      x: point.x,
      y: point.y,
      toJson: () => ({ x: point.x, y: point.y }),
    }
  }),
}))

vi.mock('@/core/utils/validationUtils', () => ({
  handleValidationResultAndPublishError: vi.fn().mockReturnValue(true),
}))

describe('shapeCreationService', () => {
  let service: ShapeCreationService
  let mockFactory: any
  let mockRepository: any
  let mockEventBus: any
  let mockLogger: any

  beforeEach(() => {
    // Create mock implementations
    mockFactory = new ElementFactory()
    mockRepository = new ShapeRepository()

    mockEventBus = {
      publish: vi.fn(),
    }

    mockLogger = {
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    }

    // Create service instance with mocks
    service = new ShapeCreationService(
      mockFactory,
      mockRepository,
      mockEventBus,
      mockLogger,
    )
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('handleRequest', () => {
    it('should handle invalid payload', async () => {
      // Call the method with invalid payload
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: null as any,
          position: { x: 100, y: 100 },
        },
      })

      // Verify error event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          code: ErrorType.INVALID_PAYLOAD,
          message: expect.stringContaining('Invalid payload for shape creation'),
        }),
      })

      // Verify factory and repository methods were not called
      expect(mockFactory.createShape).not.toHaveBeenCalled()
      expect(mockRepository.add).not.toHaveBeenCalled()
    })

    it('should handle invalid position', async () => {
      // Call the method with invalid position
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: null as any,
        },
      })

      // Verify error event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          code: ErrorType.VALIDATION_FAILED,
          message: expect.stringContaining('Invalid position data'),
        }),
      })

      // Verify factory and repository methods were not called
      expect(mockFactory.createShape).not.toHaveBeenCalled()
      expect(mockRepository.add).not.toHaveBeenCalled()
    })

    it('should handle validation failure', async () => {
      // Mock validation failure
      vi.mocked(validationUtils.handleValidationResultAndPublishError).mockReturnValueOnce(false)

      // Call the method
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: -10, // Invalid width
            height: 50,
          },
        },
      })

      // Verify validation was called
      expect(ElementValidator.validateElement).toHaveBeenCalled()

      // Verify factory and repository methods were not called
      expect(mockFactory.createShape).not.toHaveBeenCalled()
      expect(mockRepository.add).not.toHaveBeenCalled()
    })

    it('should successfully create a rectangle shape', async () => {
      // Call the method with valid data
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 100,
            height: 50,
          },
        },
      })

      // Verify factory was called with correct parameters
      expect(mockFactory.createShape).toHaveBeenCalledWith(
        ElementType.RECTANGLE,
        expect.objectContaining({
          type: ElementType.RECTANGLE,
          position: expect.objectContaining({ x: 100, y: 100 }),
          properties: expect.objectContaining({
            width: 100,
            height: 50,
          }),
        }),
      )

      // Verify repository was called with the created shape
      expect(mockRepository.add).toHaveBeenCalled()

      // Verify success event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_CREATE_COMPLETE,
        payload: expect.objectContaining({
          shapeId: 'mock-rectangle-id',
          ElementType: ElementType.RECTANGLE,
          position: expect.objectContaining({ x: 100, y: 100 }),
          properties: expect.objectContaining({
            width: 100,
            height: 50,
          }),
        }),
      })
    })

    it('should handle factory creation failure', async () => {
      // Call the method with a shape type that will cause factory to return null
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.CIRCLE, // Not implemented in our mock
          position: { x: 100, y: 100 },
          properties: {
            radius: 50,
          },
        },
      })

      // Verify error event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          code: ErrorType.FACTORY_CREATION_FAILED,
        }),
      })

      // Verify repository method was not called
      expect(mockRepository.add).not.toHaveBeenCalled()
    })

    it('should handle repository addition failure', async () => {
      // Mock factory to return a shape that will cause repository to throw
      mockFactory.createShape.mockReturnValueOnce({
        id: 'error-shape',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
        properties: {
          width: 100,
          height: 50,
        },
      })

      // Call the method
      await service.handleRequest({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType: ElementType.RECTANGLE,
          position: { x: 100, y: 100 },
          properties: {
            width: 100,
            height: 50,
          },
        },
      })

      // Verify error event was published
      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ERROR_OCCURRED,
        payload: expect.objectContaining({
          code: ErrorType.COORDINATOR_OPERATION_FAILED,
        }),
      })
    })
  })
})
