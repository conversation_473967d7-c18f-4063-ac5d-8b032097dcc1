import type { EventBusConfig, EventSubscriptionOptions } from '@/types/services/events/eventCore'
import type { AppEventMap } from '@/types/services/events/eventRegistry'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { AppEventBusImpl } from '@/services/core/event-bus/appEventBus'
import { AppEventType } from '@/types/services/events/eventTypes'

describe('appEventBusImpl', () => {
  let eventBus: AppEventBusImpl
  let consoleSpy: ReturnType<typeof vi.spyOn>

  beforeEach(() => {
    consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
    eventBus = new AppEventBusImpl()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('constructor', () => {
    it('should initialize with default configuration', () => {
      expect(eventBus).toBeDefined()
      expect(eventBus.isConfigured()).toBe(false)
    })
  })

  describe('configure', () => {
    it('should configure the event bus', () => {
      const config: EventBusConfig = {
        enableLogging: true,
        defaultPriority: 5,
        maxAsyncHandlers: 50,
      }

      eventBus.configure(config)
      expect(eventBus.isConfigured()).toBe(true)
    })

    it('should merge with default configuration', () => {
      eventBus.configure({ enableLogging: true })
      expect(eventBus.isConfigured()).toBe(true)
    })
  })

  describe('subscribe', () => {
    it('should subscribe to events', () => {
      const handler = vi.fn()
      const unsubscribe = eventBus.subscribe(AppEventType.ShapeCreated, handler)

      expect(typeof unsubscribe).toBe('function')
    })

    it('should subscribe with options', () => {
      const handler = vi.fn()
      const options: EventSubscriptionOptions = {
        priority: 10,
        once: true,
      }

      const unsubscribe = eventBus.subscribe(AppEventType.ShapeCreated, handler, options)
      expect(typeof unsubscribe).toBe('function')
    })

    it('should allow multiple handlers for same event', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()

      eventBus.subscribe(AppEventType.ShapeCreated, handler1)
      eventBus.subscribe(AppEventType.ShapeCreated, handler2)

      const event: AppEventMap[AppEventType.ShapeCreated] = {
        type: AppEventType.ShapeCreated,
        payload: { shapeId: 'test-shape' },
        timestamp: Date.now(),
      }

      eventBus.publish(event)

      expect(handler1).toHaveBeenCalledWith(event)
      expect(handler2).toHaveBeenCalledWith(event)
    })

    it('should handle subscription errors gracefully', () => {
      expect(() => {
        eventBus.subscribe(null as any, vi.fn())
      }).not.toThrow()
    })
  })

  describe('unsubscribe', () => {
    it('should unsubscribe handlers', () => {
      const handler = vi.fn()
      const unsubscribe = eventBus.subscribe(AppEventType.ShapeCreated, handler)

      unsubscribe()

      const event: AppEventMap[AppEventType.ShapeCreated] = {
        type: AppEventType.ShapeCreated,
        payload: { shapeId: 'test-shape' },
        timestamp: Date.now(),
      }

      eventBus.publish(event)
      expect(handler).not.toHaveBeenCalled()
    })

    it('should handle multiple unsubscribe calls gracefully', () => {
      const handler = vi.fn()
      const unsubscribe = eventBus.subscribe(AppEventType.ShapeCreated, handler)

      expect(() => {
        unsubscribe()
        unsubscribe()
      }).not.toThrow()
    })
  })

  describe('publish', () => {
    it('should publish events to subscribers', () => {
      const handler = vi.fn()
      eventBus.subscribe(AppEventType.ShapeCreated, handler)

      const event: AppEventMap[AppEventType.ShapeCreated] = {
        type: AppEventType.ShapeCreated,
        payload: { shapeId: 'test-shape' },
        timestamp: Date.now(),
      }

      const result = eventBus.publish(event)

      expect(result).toBe(true)
      expect(handler).toHaveBeenCalledWith(event)
    })

    it('should return false when no handlers exist', () => {
      eventBus.configure({ enableLogging: true })

      const event: AppEventMap[AppEventType.ShapeCreated] = {
        type: AppEventType.ShapeCreated,
        payload: { shapeId: 'test-shape' },
        timestamp: Date.now(),
      }

      const result = eventBus.publish(event)

      expect(result).toBe(false)
    })

    it('should validate event format', () => {
      const invalidEvent = {
        // Missing required properties
        payload: { shapeId: 'test' },
      } as any

      const result = eventBus.publish(invalidEvent)
      expect(result).toBe(false)
    })

    it('should handle handler errors gracefully', () => {
      const errorHandler = vi.fn().mockImplementation(() => {
        throw new Error('Handler error')
      })
      const normalHandler = vi.fn()

      eventBus.subscribe(AppEventType.ShapeCreated, errorHandler)
      eventBus.subscribe(AppEventType.ShapeCreated, normalHandler)

      const event: AppEventMap[AppEventType.ShapeCreated] = {
        type: AppEventType.ShapeCreated,
        payload: { shapeId: 'test-shape' },
        timestamp: Date.now(),
      }

      expect(() => eventBus.publish(event)).not.toThrow()
      expect(normalHandler).toHaveBeenCalled()
    })
  })

  describe('once subscription', () => {
    it('should unsubscribe after first event when once option is true', () => {
      const handler = vi.fn()
      eventBus.subscribe(AppEventType.ShapeCreated, handler, { once: true })

      const event: AppEventMap[AppEventType.ShapeCreated] = {
        type: AppEventType.ShapeCreated,
        payload: { shapeId: 'test-shape' },
        timestamp: Date.now(),
      }

      eventBus.publish(event)
      eventBus.publish(event)

      expect(handler).toHaveBeenCalledTimes(1)
    })
  })

  describe('priority handling', () => {
    it('should call handlers in priority order', () => {
      const callOrder: number[] = []

      const handler1 = vi.fn(() => callOrder.push(1))
      const handler2 = vi.fn(() => callOrder.push(2))
      const handler3 = vi.fn(() => callOrder.push(3))

      // Subscribe with different priorities
      eventBus.subscribe(AppEventType.ShapeCreated, handler2, { priority: 5 })
      eventBus.subscribe(AppEventType.ShapeCreated, handler1, { priority: 10 })
      eventBus.subscribe(AppEventType.ShapeCreated, handler3, { priority: 1 })

      const event: AppEventMap[AppEventType.ShapeCreated] = {
        type: AppEventType.ShapeCreated,
        payload: { shapeId: 'test-shape' },
        timestamp: Date.now(),
      }

      eventBus.publish(event)

      // Should be called in priority order (highest first)
      expect(callOrder).toEqual([1, 2, 3])
    })
  })

  describe('async handling', () => {
    it('should handle async handlers', async () => {
      const asyncHandler = vi.fn().mockResolvedValue(undefined)
      eventBus.subscribe(AppEventType.ShapeCreated, asyncHandler)

      const event: AppEventMap[AppEventType.ShapeCreated] = {
        type: AppEventType.ShapeCreated,
        payload: { shapeId: 'test-shape' },
        timestamp: Date.now(),
      }

      eventBus.publish(event)

      // Give async handlers time to execute
      await new Promise(resolve => setTimeout(resolve, 0))

      expect(asyncHandler).toHaveBeenCalledWith(event)
    })
  })

  describe('filtering', () => {
    it('should support event filtering', () => {
      const handler = vi.fn()
      const filter = (event: any) => event.payload.shapeId === 'allowed'

      eventBus.subscribe(AppEventType.ShapeCreated, handler, { filter })

      const allowedEvent: AppEventMap[AppEventType.ShapeCreated] = {
        type: AppEventType.ShapeCreated,
        payload: { shapeId: 'allowed' },
        timestamp: Date.now(),
      }

      const blockedEvent: AppEventMap[AppEventType.ShapeCreated] = {
        type: AppEventType.ShapeCreated,
        payload: { shapeId: 'blocked' },
        timestamp: Date.now(),
      }

      eventBus.publish(allowedEvent)
      eventBus.publish(blockedEvent)

      expect(handler).toHaveBeenCalledTimes(1)
      expect(handler).toHaveBeenCalledWith(allowedEvent)
    })
  })

  describe('clear', () => {
    it('should remove all handlers', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()

      eventBus.subscribe(AppEventType.ShapeCreated, handler1)
      eventBus.subscribe(AppEventType.ShapeDeleted, handler2)

      eventBus.clear()

      const event1: AppEventMap[AppEventType.ShapeCreated] = {
        type: AppEventType.ShapeCreated,
        payload: { shapeId: 'test' },
        timestamp: Date.now(),
      }

      const event2: AppEventMap[AppEventType.ShapeDeleted] = {
        type: AppEventType.ShapeDeleted,
        payload: { shapeId: 'test' },
        timestamp: Date.now(),
      }

      eventBus.publish(event1)
      eventBus.publish(event2)

      expect(handler1).not.toHaveBeenCalled()
      expect(handler2).not.toHaveBeenCalled()
    })
  })

  describe('getHandlerCount', () => {
    it('should return correct handler count', () => {
      expect(eventBus.getHandlerCount(AppEventType.ShapeCreated)).toBe(0)

      eventBus.subscribe(AppEventType.ShapeCreated, vi.fn())
      expect(eventBus.getHandlerCount(AppEventType.ShapeCreated)).toBe(1)

      eventBus.subscribe(AppEventType.ShapeCreated, vi.fn())
      expect(eventBus.getHandlerCount(AppEventType.ShapeCreated)).toBe(2)
    })
  })
})
