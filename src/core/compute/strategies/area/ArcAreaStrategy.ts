/**
 * Area Calculation Strategy for Arc Elements
 *
 * @remarks
 * This strategy implements the {@link AreaCalculatorStrategy} for calculating the
 * area of arc elements ({@link CoreElementType.ARC}).
 *
 * If the arc is defined as 'closed' (forming a circular sector or "pie slice"),
 * this strategy computes the area of that sector. The calculation uses the arc's
 * radius and the angle between its start and end points.
 *
 * If the arc is not 'closed' (i.e., it represents just a curved line segment),
 * it is considered to have no area, and this strategy will return `0`.
 *
 * The strategy relies on `toRadians` for angle conversion and `calculateArcAreaUtil`
 * from `../../../../lib/utils/geometry/areaUtils` for the core sector area calculation.
 *
 * @module core/compute/strategies/area/ArcAreaStrategy
 * @see {@link Path.Arc} for the arc element type definition.
 */
import type { AreaCalculatorStrategy } from '../../../../types/core/compute'
import type { Element, Path } from '../../../../types/core/elementDefinitions'
import { calculateArcArea as calculateArcAreaUtil } from '../../../../lib/utils/geometry/areaUtils' // Import from areaUtils
import { toRadians } from '../../../../lib/utils/math' // Import toRadians from mathUtils
import { CoreError } from '../../../../services/system/error-service/coreError'
import { ElementType as CoreElementType } from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

// Local degreesToRadians helper is removed

/**
 * Implements the {@link AreaCalculatorStrategy} for {@link CoreElementType.ARC} elements.
 */
export class ArcAreaStrategy implements AreaCalculatorStrategy {
  /**
   * Calculates the area of an Arc element.
   *
   * @param element - The arc element, expected to conform to {@link Path.Arc} and have type {@link CoreElementType.ARC}.
   * @returns The calculated area of the arc. If the arc is not `closed`, returns `0`.
   * @throws {@link CoreError} if the provided element is not of type `ARC`.
   * @throws {@link CoreError} if the arc's `radius`, `startAngle`, or `endAngle` properties are invalid (e.g., non-numeric, radius <= 0).
   * @throws {@link CoreError} if the utility function `calculateArcAreaUtil` returns `NaN`.
   */
  public calculateArea(element: Element): number {
    if (element.type !== CoreElementType.ARC) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `ArcAreaStrategy can only calculate area for ARC elements, got ${element.type}`,
        undefined, // severity
        { component: 'ArcAreaStrategy', operation: 'calculateArea', target: element.id }, // context
      )
    }

    const arcElement = element as unknown as Path.Arc

    // Access properties from the properties object
    const radius = arcElement.properties.radius
    const startAngleDegrees = arcElement.properties.startAngle
    const endAngleDegrees = arcElement.properties.endAngle
    const closed = arcElement.properties.closed === true

    if (typeof radius !== 'number' || radius <= 0 || !Number.isFinite(radius)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid radius: ${radius}`,
        undefined,
        { component: 'ArcAreaStrategy', operation: 'calculateArea', target: element.id, metadata: { radius } },
      )
    }
    if (typeof startAngleDegrees !== 'number' || typeof endAngleDegrees !== 'number' || !Number.isFinite(startAngleDegrees) || !Number.isFinite(endAngleDegrees)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid angles: startAngle=${startAngleDegrees}, endAngle=${endAngleDegrees}`,
        undefined,
        { component: 'ArcAreaStrategy', operation: 'calculateArea', target: element.id, metadata: { startAngleDegrees, endAngleDegrees } },
      )
    }

    const startAngleRad = toRadians(startAngleDegrees) // Use imported toRadians
    const endAngleRad = toRadians(endAngleDegrees) // Use imported toRadians

    let angleDiffRad = endAngleRad - startAngleRad
    // Normalize angle difference to be between 0 and 2*PI
    angleDiffRad = angleDiffRad % (2 * Math.PI)
    if (angleDiffRad < 0) {
      angleDiffRad += 2 * Math.PI
    }

    // If not closed, an arc line itself has no area.
    if (!closed) {
      return 0
    }

    // For a closed arc (pie slice), calculate sector area using the utility function
    const sectorArea = calculateArcAreaUtil(radius, angleDiffRad)
    if (Number.isNaN(sectorArea)) {
      throw new CoreError(
        ErrorType.ComputationError, // More specific error type
        `Arc element (ID: ${element.id}) resulted in invalid area calculation with radius: ${radius}, angleDiffRad: ${angleDiffRad}`,
        undefined,
        { component: 'ArcAreaStrategy', operation: 'calculateArea', target: element.id, metadata: { radius, angleDiffRad } },
      )
    }
    return sectorArea
  }

  public getElementType(): CoreElementType {
    return CoreElementType.ARC
  }
}
