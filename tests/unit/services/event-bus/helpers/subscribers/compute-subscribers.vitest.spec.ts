import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  subscribeToComputeCompleteEvents,
  subscribeToComputeErrorEvents,
  subscribeToComputeProgressEvents,
  subscribeToComputeRequestEvents,
  subscribeToTransformEvents,
  subscribeToValidationEvents,
} from '@/services/event-bus/helpers/subscribers/compute-subscribers'
import * as utils from '@/services/event-bus/helpers/subscribers/utils'
import { AppEventType } from '@/types/services/events'

describe('compute Subscribers', () => {
  let mockEventBus: any
  let mockHandler: any
  let mockOptions: any
  let typedSubscribeSpy: any

  beforeEach(() => {
    mockEventBus = {
      subscribe: vi.fn().mockReturnValue(() => {}),
    }
    mockHandler = vi.fn()
    mockOptions = { once: true }

    // Spy on the typedSubscribe function
    typedSubscribeSpy = vi.spyOn(utils, 'typedSubscribe').mockReturnValue(() => {})
  })

  describe('subscribeToComputeRequestEvents', () => {
    it('should call typedSubscribe with correct parameters', () => {
      const unsubscribe = subscribeToComputeRequestEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.COMPUTE_REQUEST,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('subscribeToComputeProgressEvents', () => {
    it('should call typedSubscribe with correct parameters', () => {
      const unsubscribe = subscribeToComputeProgressEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.COMPUTE_PROGRESS,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('subscribeToComputeCompleteEvents', () => {
    it('should call typedSubscribe with correct parameters', () => {
      const unsubscribe = subscribeToComputeCompleteEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.COMPUTE_COMPLETE,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('subscribeToComputeErrorEvents', () => {
    it('should call typedSubscribe with correct parameters', () => {
      const unsubscribe = subscribeToComputeErrorEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.COMPUTE_ERROR,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('subscribeToTransformEvents', () => {
    it('should call typedSubscribe with correct parameters', () => {
      const unsubscribe = subscribeToTransformEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.COMPUTE_TRANSFORM,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('subscribeToValidationEvents', () => {
    it('should call typedSubscribe with correct parameters for shape validation', () => {
      const unsubscribe = subscribeToValidationEvents(
        mockEventBus,
        mockHandler,
        'shape',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.VALIDATE_SHAPE,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for template validation', () => {
      const unsubscribe = subscribeToValidationEvents(
        mockEventBus,
        mockHandler,
        'template',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.VALIDATE_TEMPLATE,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for operation validation', () => {
      const unsubscribe = subscribeToValidationEvents(
        mockEventBus,
        mockHandler,
        'operation',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.VALIDATE_OPERATION,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for constraint validation', () => {
      const unsubscribe = subscribeToValidationEvents(
        mockEventBus,
        mockHandler,
        'constraint',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.VALIDATE_CONSTRAINT,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should throw error for invalid validation phase', () => {
      expect(() => subscribeToValidationEvents(
        mockEventBus,
        mockHandler,
        'invalid' as any,
        mockOptions,
      )).toThrow('Invalid validation phase: invalid')
    })
  })
})
