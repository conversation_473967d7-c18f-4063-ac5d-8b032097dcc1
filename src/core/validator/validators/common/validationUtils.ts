/**
 * @file validationUtils.ts
 * @description Provides common utility functions used across different validators within the core module.
 * These functions help check the validity of common data types and structures such as points, colors, and numeric ranges.
 * They are essential for ensuring data integrity before processing or storing element information.
 *
 * @module core/validator/validators/common/validationUtils
 */

import type Point from '@/types/core/element/geometry/point'
import type { ValidationError, ValidationResult } from '@/types/core/validator/validator-interface'
import { ValidationErrorCode } from '@/types/core/validator/error-codes'

/**
 * Color type alias for string to improve readability in function signatures.
 * @private
 */
type Color = string

/**
 * Checks if a given object represents a valid point with finite number coordinates.
 *
 * @remarks
 * Accepts either a Point instance or a plain object with `x` and `y` number properties.
 *
 * @param point - The point object or Point instance to check.
 * @returns `true` if the input is a valid point object or Point instance with finite `x` and `y` coordinates, `false` otherwise.
 */
export function isValidPoint(point: { x: number, y: number } | Point | null | undefined): point is { x: number, y: number } | Point {
  // Explicitly check for null or undefined first
  if (point === null || point === undefined) {
    return false
  }

  // Check for finite number properties x and y
  return (
    typeof point.x === 'number'
    && typeof point.y === 'number'
    && Number.isFinite(point.x) // Use Number.isFinite to exclude NaN and Infinity
    && Number.isFinite(point.y)
  )
}

/**
 * Checks if a given string represents a valid CSS color value.
 *
 * @remarks
 * Supports common formats:
 * - Hexadecimal: `#RGB`, `#RRGGBB` (Red, Green, Blue color values in hexadecimal)
 * - RGB: `rgb(r, g, b)` (values 0-255)
 * - RGBA: `rgba(r, g, b, a)` (rgb 0-255, alpha 0.0-1.0)
 * - Common CSS color names (lowercase check).
 *
 * @param color - The color string to validate.
 * @returns `true` if the string is a valid color representation in one of the supported formats, `false` otherwise.
 */
export function isValidColor(color: Color | null | undefined): color is Color {
  // Check if color exists and is a non-empty string
  if (color == null || typeof color !== 'string' || color.trim() === '') {
    return false
  }

  const trimmedColor = color.trim()

  // Support multiple color formats
  // #RGB or #RRGGBB hexadecimal format (cspell:disable-line)
  if (/^#(?:[0-9A-F]{3}){1,2}$/i.test(trimmedColor)) { // Added 'i' flag for case-insensitivity
    return true
  }

  // rgb(r,g,b) or rgba(r,g,b,a) format
  // More robust regex for rgb/rgba, allowing spaces and validating values
  // cspell:disable-next-line
  const rgbRegex = /^rgb\(\s*0*(?:\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\s*,\s*0*(?:\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\s*,\s*0*(?:\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\s*\)$/i
  // cspell:disable-next-line
  const rgbaRegex = /^rgba\(\s*0*(?:\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\s*,\s*0*(?:\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\s*,\s*0*(?:\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\s*,\s*(?:1|0\.\d+|1\.0*)\s*\)$/i

  if (rgbRegex.test(trimmedColor)) {
    return true // Regex now ensures values are in range
  }

  if (rgbaRegex.test(trimmedColor)) {
    return true // Regex now ensures values are in range
  }

  // Common CSS color names (lowercase check)
  // Based on CSS Color Module Level 3/4 standard names
  const basicColors = new Set([
    'black',
    'silver',
    'gray',
    'white',
    'maroon',
    'red',
    'purple',
    'fuchsia',
    'green',
    'lime',
    'olive',
    'yellow',
    'navy',
    'blue',
    'teal',
    'aqua',
    'orange', // Added orange as common
    'transparent', // Include transparent
    // Add more comprehensive list if needed, but this covers common cases
  ])

  if (basicColors.has(trimmedColor.toLowerCase())) {
    return true
  }

  // Could add hsl/hsla checks if needed

  return false
}

/**
 * Checks if a polygon defined by an array of points is considered closed.
 *
 * @remarks
 * A polygon is closed if its first and last points are sufficiently close (coincident).
 * Uses Euclidean distance and a small tolerance (`epsilon`) for comparison.
 * It also checks if the input is a valid array with at least 3 valid points.
 *
 * @param points - An array of point objects or Point instances representing the polygon vertices.
 * @param epsilon - Optional tolerance for considering points coincident. Defaults to 0.001.
 * @returns `true` if the array has at least 3 valid points and the distance between the first and last point is less than or equal to `epsilon`, `false` otherwise.
 */
export function isPolygonClosed(points: ({ x: number, y: number } | Point)[], epsilon: number = 0.001): boolean {
  if (points == null || !Array.isArray(points) || points.length < 3) {
    return false // Need at least 3 points for a polygon
  }

  // Check validity of first and last points specifically
  const firstPoint = points[0]
  const lastPoint = points[points.length - 1]

  if (!isValidPoint(firstPoint) || !isValidPoint(lastPoint)) {
    console.warn('[isPolygonClosed] First or last point is invalid.')
    return false // Cannot check closure if endpoints are invalid
  }

  // Calculate distance using Math.hypot for robustness (avoids intermediate squaring/sqrt issues)
  const distance = Math.hypot(lastPoint.x - firstPoint.x, lastPoint.y - firstPoint.y)
  return distance <= epsilon
}

/**
 * Checks if a given numerical value falls within a specified range (inclusive).
 *
 * @param value - The number to check.
 * @param min - The minimum allowed value (inclusive).
 * @param max - The maximum allowed value (inclusive).
 * @returns `true` if `value` is a finite number such that `min <= value <= max`, `false` otherwise.
 */
export function isValueInRange(value: number | null | undefined, min: number, max: number): value is number {
  return (
    typeof value === 'number'
    && Number.isFinite(value) // Ensure it's not NaN or Infinity
    && value >= min
    && value <= max
  )
}

// --- Moved from CoreError.ts --- //

/**
 * Creates a standard ValidationResult object.
 *
 * @remarks
 * Simplifies the process of constructing validation results, especially when dealing
 * with a mix of error messages (strings) and structured ValidationError objects.
 *
 * If error messages (strings) are provided in the `errors` array, they are automatically
 * converted into ValidationError objects with a default code (`GENERIC_VALIDATION`).
 * The top-level `message` for the result is determined automatically from the first error
 * if not explicitly provided.
 *
 * @param isValid - Boolean flag indicating if the overall validation passed.
 * @param errors - Optional array containing error details.
 *        Can be a mix of string messages and ValidationError objects.
 * @param message - Optional explicit top-level summary message for the ValidationResult.
 *        If omitted and errors exist, the message from the first error in the errors array will be used.
 * @returns A populated ValidationResult object.
 */
export function createValidationResult(
  isValid: boolean,
  errors?: (ValidationError | string)[],
  message?: string,
): ValidationResult {
  const validationErrors: ValidationError[] = []

  // Process the input errors array
  if (errors && Array.isArray(errors)) {
    errors.forEach((err) => {
      if (typeof err === 'string' && err.trim() !== '') {
        // Convert non-empty strings to ValidationError objects
        validationErrors.push({ code: ValidationErrorCode.VALIDATION_SPECIFIC_ERROR, message: err })
      }
      else if (typeof err === 'object' && err?.code != null && err?.message != null) {
        // Add valid ValidationError objects directly
        validationErrors.push(err) // Cast is safe due to checks
      }
      else {
        console.warn('[createValidationResult] Skipping invalid item in errors array:', err)
      }
    })
  }

  // Determine the top-level message: use provided message, or first error message, or undefined
  const topLevelMessage = message ?? (validationErrors.length > 0 ? validationErrors[0].message : undefined)

  // Construct the final ValidationResult object
  return {
    valid: isValid && validationErrors.length === 0, // Ensure isValid reflects error presence
    message: topLevelMessage,
    // Include errors array only if it contains errors
    errors: validationErrors.length > 0 ? validationErrors : undefined,
    // `warnings` property is not handled by this helper, defaults to undefined
  }
}
// --- Moved from CoreError.ts --- //
