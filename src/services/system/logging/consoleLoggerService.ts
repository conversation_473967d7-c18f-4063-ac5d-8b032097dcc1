import type { LoggerConfig, LoggerService } from '@/types/services/logging'
// import { getService, ServiceId } from '../../core/registry'; // Unused import
import { safeExecute } from '@/lib/utils/errorUtils'
import { LogLevel } from '@/types/services/logging'

/**
 * A simple implementation of the LoggerService interface that logs messages
 * to the browser console with appropriate prefix tags and styling.
 *
 * @module services/system/logging/ConsoleLoggerService
 */
export class ConsoleLoggerService implements LoggerService {
  private config: LoggerConfig = {
    minLevel: LogLevel.Debug,
    includeTimestamp: true,
    enableConsole: true,
    includeContext: true,
  }

  private currentLoggingContext: Record<string, unknown> = {} // Added to store context

  constructor() {
    this.info('ConsoleLoggerService initialized.')
  }

  private logInternal(level: LogLevel, message: string, ...optionalParams: unknown[]): void {
    if (this.shouldLog(level)) {
      safeExecute(() => {
        const logMessage = this.formatMessage(level, message)
        const contextArgs = (this.config.includeContext && Object.keys(this.currentLoggingContext).length > 0)
          ? [this.currentLoggingContext]
          : []

        const allParams = [...optionalParams, ...contextArgs]

        switch (level) {
          case LogLevel.Debug:
            // console.debug(logMessage, ...allParams)
            break
          case LogLevel.Info:
            // eslint-disable-next-line no-console
            console.info(logMessage, ...allParams)
            break
          case LogLevel.Warn:
            console.warn(logMessage, ...allParams)
            break
          case LogLevel.Error:
            console.error(logMessage, ...allParams)
            break
        }
      }, this.createErrorContext(level, { message, optionalParams }))
    }
  }

  info(message: string, ...optionalParams: unknown[]): void {
    this.logInternal(LogLevel.Info, message, ...optionalParams)
  }

  warn(message: string, ...optionalParams: unknown[]): void {
    this.logInternal(LogLevel.Warn, message, ...optionalParams)
  }

  error(message: string, ...optionalParams: unknown[]): void {
    this.logInternal(LogLevel.Error, message, ...optionalParams)
  }

  debug(message: string, ...optionalParams: unknown[]): void {
    this.logInternal(LogLevel.Debug, message, ...optionalParams)
  }

  setContext(context: Record<string, unknown>): void {
    safeExecute(() => {
      this.currentLoggingContext = { ...this.currentLoggingContext, ...context }
    }, this.createErrorContext('setContext', { context }))
  }

  getConfig(): LoggerConfig {
    return safeExecute(() => {
      // Return a copy to prevent direct modification of internal config
      return { ...this.config }
    }, this.createErrorContext('getConfig')) || { ...this.config } // Fallback to a copy
  }

  setConfig(config: Partial<LoggerConfig>): void {
    safeExecute(() => {
      this.config = { ...this.config, ...config }
    }, this.createErrorContext('setConfig', { config }))
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      [LogLevel.Debug]: 0,
      [LogLevel.Info]: 1,
      [LogLevel.Warn]: 2,
      [LogLevel.Error]: 3,
    }
    // Ensure minLevel is valid, default to DEBUG if not
    const currentMinLevel = Object.values(LogLevel).includes(this.config.minLevel)
      ? this.config.minLevel
      : LogLevel.Debug
    return levels[level] >= levels[currentMinLevel]
  }

  private formatMessage(level: LogLevel, message: string): string {
    let prefix = `[${level.toUpperCase()}]`
    if (this.config.includeTimestamp) {
      prefix = `[${new Date().toISOString()}] ${prefix}`
    }
    return `${prefix} ${message}`
  }

  private createErrorContext(operation: string, metadata?: Record<string, unknown>) {
    return {
      component: 'ConsoleLoggerService',
      operation,
      metadata,
    }
  }

  public static create(): LoggerService {
    return new ConsoleLoggerService()
  }
}
