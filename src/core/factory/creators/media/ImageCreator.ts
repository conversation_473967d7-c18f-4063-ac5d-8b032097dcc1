// Params types from ElementFactory
import type { CreateImageParams } from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point'
// Corrected import: Image is the main interface for image elements
import type { Image } from '@/types/core/element/image/imageElementTypes'
import { CoreError, ErrorType } from '@/services/system/error-service'

import { ElementType as CoreElementType } from '@/types/core/elementDefinitions'

/**
 * Creator for Image Media Elements
 *
 * @remarks
 * This class implements the {@link ShapeCreator} interface to specialize in creating
 * Image ({@link CoreElementType.IMAGE}) media elements.
 *
 * @module core/factory/creators/media/ImageCreator
 * @see {@link ShapeCreator}
 * @see {@link CoreElementType.IMAGE}
 * @see {@link CreateImageParams}
 */
import { ShapeCreator } from '../shape/ShapeCreator'

// Helper type for the return of getProperties, matching direct props of Image interface
// Ensure this type is defined before its first use.
interface ImageSpecificProps {
  src: string
  width: number
  height: number
  alt?: string
  sourceType: 'url' | 'svg_inline_data'
  // Allow other properties to be merged in
  [key: string]: unknown
}

export default class ImageCreator extends ShapeCreator<Image, CreateImageParams> {
  constructor() {
    super(CoreElementType.IMAGE)
  }

  // This method now prepares the complete 'properties' object for the ShapeModel
  protected getProperties(params: CreateImageParams, generatedId: string): ImageSpecificProps {
    // console.warn(`[ImageCreator getProperties START] for ID: ${generatedId}. params.properties:`, JSON.stringify(params.properties))
    const customPropsFromParams = params.properties || {}
    // console.warn(`[ImageCreator getProperties] customPropsFromParams (after params.properties || {}):`, JSON.stringify(customPropsFromParams))

    // Default to 'url' if sourceType is not provided or invalid
    const sourceType: 'url' | 'svg_inline_data'
            = customPropsFromParams.sourceType === 'svg_inline_data'
              ? 'svg_inline_data' // Check customProps first
              : params.sourceType === 'svg_inline_data'
                ? 'svg_inline_data'
                : 'url' // Then params

    const defaultImageSrc = '/public/icon/image.svg'
    const inputSrc = (customPropsFromParams.src as string) ?? (params.src as string) // Prioritize from customProps
    let finalSrc = defaultImageSrc

    if (inputSrc != null && typeof inputSrc === 'string' && inputSrc.trim() !== '') {
      finalSrc = inputSrc.trim()
    }
    else {
      // const idForLog = params.id || generatedId; // Unused variable
      // console.warn is kept for debugging, can be removed if too noisy
      // console.warn(`[ImageCreator getProperties] 'src' for ID '${idForLog}' is empty or invalid. Using default: "${finalSrc}". Input was:`, inputSrc);
    }

    const widthInput = (customPropsFromParams.width as number) ?? (params.width as number) // Prioritize
    let finalWidth = 100 // Default
    if (typeof widthInput === 'number' && widthInput > 0) {
      finalWidth = widthInput
    }
    else if (widthInput !== undefined) {
      // const idForLog = params.id || generatedId; // Unused variable
      // console.warn(`[ImageCreator getProperties] Invalid 'width' for ID '${idForLog}': ${widthInput}. Using default ${finalWidth}.`);
    }

    const heightInput = (customPropsFromParams.height as number) ?? (params.height as number) // Prioritize
    let finalHeight = 100 // Default
    if (typeof heightInput === 'number' && heightInput > 0) {
      finalHeight = heightInput
    }
    else if (heightInput !== undefined) {
      // const idForLog = params.id || generatedId; // Unused variable
      // console.warn(`[ImageCreator getProperties] Invalid 'height' for ID '${idForLog}': ${heightInput}. Using default ${finalHeight}.`);
    }

    const altText = (customPropsFromParams.alt as string) ?? (params.alt as string) ?? `Image ${(params.id ?? generatedId).substring(0, 5)}`

    // Merge all properties: customPropsFromParams takes precedence for overlaps,
    // then specific image props, then remaining params.properties (though most are covered)
    const returnedProps = {
      // 设置默认的成本相关属性
      costUnitPrice: 1,
      costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1
      costBasis: 'unit',
      // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
      computedAreaStatus: 'none',
      computedPerimeterStatus: 'none',
      computedLengthStatus: 'none',
      // 然后合并其他属性
      ...customPropsFromParams, // Start with all properties passed in params.properties
      src: finalSrc,
      sourceType,
      width: finalWidth,
      height: finalHeight,
      alt: altText,
    }
    // console.warn(`[ImageCreator getProperties END] Props to be returned:`, JSON.stringify(returnedProps))
    return returnedProps
  }

  public async create(params: CreateImageParams): Promise<Image> {
    if (!params.id) {
      throw new CoreError(ErrorType.InvalidPayload, 'ImageCreator requires an ID in params.')
    }
    const id = params.id

    const commonProps = this.createCommonProperties(id, params) // type, majorCat, minorCat, zLevelId etc. are here

    // All shape-specific properties, including src, width, height, and custom ones like category,
    // are now prepared by getProperties and will be assigned to the 'properties' field.
    const allShapeProperties = this.getProperties(params, id)

    const imageElement = {
      ...commonProps, // Common props (id, type, position, rotation, categories, etc.)
      properties: allShapeProperties, // All other props (src, width, height, category, fill, stroke, etc.)
    } as Image // Cast to Image, acknowledging the structural difference to be resolved in Image type def

    // Adjust metadata name if it's the generic default one
    // This check needs to access commonProps.type and id correctly.
    const defaultNamePattern = `${commonProps.type ?? CoreElementType.IMAGE}-${id.substring(0, 8)}`
    if (imageElement.metadata && imageElement.metadata.name === defaultNamePattern) {
      // Use name from params.metadata if available, otherwise from params.properties.name, or a more specific default.
      imageElement.metadata.name = params.metadata?.name ?? (allShapeProperties.name as string) ?? `${commonProps.type ?? 'Element'} ${id.substring(0, 8)}`
    }
    else if (imageElement.metadata && params.metadata?.name != null && params.metadata.name !== '') {
      imageElement.metadata.name = params.metadata.name // Ensure explicit metadata name is preferred
    }
    else if (imageElement.metadata && (imageElement.metadata.name == null || imageElement.metadata.name === '') && allShapeProperties.name != null && allShapeProperties.name !== '') {
      imageElement.metadata.name = allShapeProperties.name as string // Fallback to name from properties if metadata.name is initially empty
    }

    // console.warn(`[ImageCreator create] Successfully created imageElement (ID: ${id}):`, JSON.stringify(imageElement, null, 2))
    return imageElement
  }

  public async createDefault(id: string, position: PointData): Promise<Image> {
    // console.warn(`[ImageCreator createDefault] Creating default image ID: ${id} at position:`, position)

    const now = Date.now()
    // For createDefault, the properties are explicitly defined here.
    const defaultProperties = {
      src: '/public/icon/image.svg', // 设置默认图片为image.svg
      width: 150,
      height: 150,
      alt: 'Placeholder Image',
      sourceType: 'url' as 'url' | 'svg_inline_data', // Ensure type correctness
      // 设置默认的成本相关属性
      costUnitPrice: 1,
      costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1
      costBasis: 'unit',
      // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
      computedAreaStatus: 'none',
      computedPerimeterStatus: 'none',
      computedLengthStatus: 'none',
      // Add any other core properties expected for a default image, like fill/stroke if applicable.
      // For an IMAGE type, fill/stroke might not be standard, but if this creator is used for
      // other types that look like images but need a background, they could be added here.
      // e.g. fill: 'transparent', stroke: 'none', strokeWidth: 0
    }

    const params: CreateImageParams = {
      id,
      type: CoreElementType.IMAGE, // This is for a default *IMAGE* element
      position,
      properties: defaultProperties, // Pass the defined default properties
      metadata: {
        name: `Default Image ${id.substring(0, 8)}`,
        createdAt: now,
        updatedAt: now,
      },
      // Common style and behavior props are typically part of BaseElement/CommonElementParams,
      // so they are set via createCommonProperties or directly in the final assignment.
      // However, if some visual defaults (like opacity) are desired *within* properties for some reason,
      // they could be added to defaultProperties. Otherwise, they are handled by commonProps.
      visible: true,
      locked: false,
      rotation: 0,
      selectable: true,
      draggable: true,
      showHandles: true,
      opacity: 1,
      // Note: fill, stroke, strokeWidth if they are common and NOT part of specific properties
      // should be passed at the root of params to be picked up by createCommonProperties if it expects them.
      // If they are truly part of the *shape's visual characteristics* (like a text's color),
      // they belong in the properties object. For a generic image, they might be less relevant
      // unless it's an SVG or has a background.
    }

    // Create an object that conforms to the structure ShapeModel expects.
    // Create an object that conforms to the structure ShapeModel expects.
    const common = this.createCommonProperties(id, params)
    const finalShape = {
      ...common,
      properties: defaultProperties as ImageSpecificProps, // Cast since defaultProperties is simpler than full ImageSpecificProps
    } as Image // Cast to Image

    // Ensure metadata name is consistent
    if (finalShape.metadata) {
      finalShape.metadata.name = params.metadata?.name ?? `Default Image ${id.substring(0, 8)}`
    }

    // console.warn(`[ImageCreator createDefault] Default imageElement (ID: ${id}):`, JSON.stringify(finalShape, null, 2))
    return finalShape
  }
}
