// Import ShapeModel for payload check
import type { ShapeModel } from '@/types/core/models'

// Import the interfaces
import type {
  LayerEvent,
  LayerLockChangeEvent,
  LayerOrderChangeEvent,
  LayerVisibilityChangeEvent,
  RenderEvent,
} from '@/types/services/events/renderEvents'

import type { ViewTransform } from '@/types/services/events/viewEvents'

import { describe, expect, it } from 'vitest'
// Import AppEventType for type checking
import { AppEventType } from '@/types/services/events/eventTypes'

describe('render and Layer Event Types', () => {
  it('should allow declaration using RenderEvent interface', () => {
    const event: RenderEvent | undefined = undefined
    expect(event).toBeUndefined()

    const example: RenderEvent = {
      type: AppEventType.RENDER_COMPLETE,
      payload: { shapes: [] as ShapeModel[] },
    }
    expect(example.type).toBe(AppEventType.RENDER_COMPLETE)
    expect(example.payload.shapes).toBeInstanceOf(Array)
  })

  it('should allow declaration using LayerEvent interface', () => {
    const event: LayerEvent | undefined = undefined
    expect(event).toBeUndefined()

    const example: LayerEvent = {
      type: AppEventType.LAYER_CREATE,
      payload: { layerId: 'layer-floor', config: { zIndex: 0, visible: true, opacity: 1 } },
    }
    expect(example.type).toBe(AppEventType.LAYER_CREATE)
    expect(example.payload.layerId).toBe('layer-floor')
    expect(example.payload.config?.zIndex).toBe(0)
  })

  it('should allow declaration using LayerVisibilityChangeEvent interface', () => {
    const event: LayerVisibilityChangeEvent | undefined = undefined
    expect(event).toBeUndefined()

    const example: LayerVisibilityChangeEvent = {
      type: AppEventType.LAYER_VISIBILITY_CHANGE,
      payload: { layerId: 'layer-furniture', visible: false },
    }
    expect(example.type).toBe(AppEventType.LAYER_VISIBILITY_CHANGE)
    expect(example.payload.visible).toBe(false)
  })

  it('should allow declaration using LayerLockChangeEvent interface', () => {
    const event: LayerLockChangeEvent | undefined = undefined
    expect(event).toBeUndefined()

    const example: LayerLockChangeEvent = {
      type: AppEventType.LAYER_LOCK_CHANGE,
      payload: { layerId: 'layer-base', locked: true },
    }
    expect(example.type).toBe(AppEventType.LAYER_LOCK_CHANGE)
    expect(example.payload.locked).toBe(true)
  })

  it('should allow declaration using LayerOrderChangeEvent interface', () => {
    const event: LayerOrderChangeEvent | undefined = undefined
    expect(event).toBeUndefined()

    const example: LayerOrderChangeEvent = {
      type: AppEventType.LAYER_ORDER_CHANGE,
      payload: { layerId: 'layer-top', newOrder: 5 },
    }
    expect(example.type).toBe(AppEventType.LAYER_ORDER_CHANGE)
    expect(example.payload.newOrder).toBe(5)
  })

  it('should allow declaration using ViewTransform interface', () => {
    const transform: ViewTransform | undefined = undefined
    expect(transform).toBeUndefined()

    const example: ViewTransform = { x: 100, y: -50, k: 2.5 }
    expect(example.x).toBe(100)
    expect(example.k).toBe(2.5)
  })
})
