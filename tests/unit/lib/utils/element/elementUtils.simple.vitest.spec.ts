import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  points,
  calculateElementsBoundingBox,
  getCenter,
  isVisible,
  distanceBetween,
  doElementsOverlap,
  sortByZIndex,
  groupByLayer,
  filterByType,
  filterByCategory,
} from '@/lib/utils/element/elementUtils'
import {
  getElementCategory,
  isElementType,
  isPathType,
  isInteriorDesignType,
} from '@/lib/utils/element/elementTypeUtils'
import {
  getRectangleProps,
  getEllipseProps,
  getLineProps,
  getPolygonProps,
  getTextProps,
  isRectangle,
  isEllipse,
  isLine,
  isPolygon,
  isText,
  isImage,
} from '@/lib/utils/element/elementModelUtils'
import type { Element, ShapeElement } from '@/types/core/elementDefinitions'
import { ElementType } from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'

describe('Element Utils (Simple Implementation)', () => {
  let testRectangle: ShapeElement
  let testEllipse: ShapeElement
  let testLine: ShapeElement
  let testPolygon: ShapeElement
  let testElements: Element[]

  beforeEach(() => {
    testRectangle = {
      id: 'rect-1',
      type: ElementType.RECTANGLE,
      position: { x: 0, y: 0 },
      properties: { width: 100, height: 50 },
      majorCategory: MajorCategory.BASE,
      minorCategory: 'architecture',
      intraLayerZIndex: 1,
    }

    testEllipse = {
      id: 'ellipse-1',
      type: ElementType.ELLIPSE,
      position: { x: 50, y: 50 },
      properties: { radiusX: 30, radiusY: 20 },
      majorCategory: MajorCategory.FURNITURE,
      minorCategory: 'seating',
      intraLayerZIndex: 2,
    }

    testLine = {
      id: 'line-1',
      type: ElementType.LINE,
      position: { x: 0, y: 0 },
      properties: { endX: 100, endY: 100 },
      majorCategory: MajorCategory.BASE,
      minorCategory: 'architecture',
      intraLayerZIndex: 0,
    }

    testPolygon = {
      id: 'polygon-1',
      type: ElementType.POLYGON,
      position: { x: 0, y: 0 },
      properties: {
        points: [
          { x: 0, y: 0 },
          { x: 50, y: 0 },
          { x: 25, y: 50 },
        ],
      },
      majorCategory: MajorCategory.BASE,
      minorCategory: 'architecture',
      intraLayerZIndex: 3,
    }

    testElements = [testRectangle, testEllipse, testLine, testPolygon]
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Element Type Detection', () => {
    it('should detect element categories', () => {
      expect(getElementCategory(testRectangle.type)).toBe('BASIC_SHAPES')
      expect(getElementCategory(testEllipse.type)).toBe('BASIC_SHAPES')
    })

    it('should detect element types', () => {
      expect(isElementType(testRectangle.type)).toBe(true)
      expect(isElementType(testEllipse.type)).toBe(true)
      expect(isElementType('INVALID_TYPE' as any)).toBe(false)
    })

    it('should detect shape elements', () => {
      expect(isRectangle(testRectangle)).toBe(true)
      expect(isEllipse(testEllipse)).toBe(true)
      expect(isLine(testLine)).toBe(true)
      expect(isPolygon(testPolygon)).toBe(true)
    })

    it('should detect text elements', () => {
      const textElement = {
        ...testRectangle,
        type: ElementType.TEXT,
      }
      expect(isText(textElement)).toBe(true)
      expect(isText(testRectangle)).toBe(false)
    })

    it('should detect image elements', () => {
      const imageElement = {
        ...testRectangle,
        type: ElementType.IMAGE,
      }
      expect(isImage(imageElement)).toBe(true)
      expect(isImage(testRectangle)).toBe(false)
    })
  })

  describe('Property Getters', () => {
    it('should get rectangle properties', () => {
      const props = getRectangleProps(testRectangle)
      expect(props?.width).toBe(100)
      expect(props?.height).toBe(50)
    })

    it('should get ellipse properties', () => {
      const props = getEllipseProps(testEllipse)
      expect(props?.radiusX).toBe(30)
      expect(props?.radiusY).toBe(20)
    })

    it('should get polygon properties', () => {
      const props = getPolygonProps(testPolygon)
      expect(props?.points).toHaveLength(3)
      expect(props?.points?.[0]).toEqual({ x: 0, y: 0 })
    })

    it('should handle missing properties gracefully', () => {
      const elementWithoutProps = {
        ...testRectangle,
        properties: undefined,
      }
      const props = getRectangleProps(elementWithoutProps as any)
      expect(props).toBeUndefined()
    })
  })

  describe('Points Extraction', () => {
    it('should extract points from polygon', () => {
      const extractedPoints = points(testPolygon)
      expect(extractedPoints).toHaveLength(3)
      expect(extractedPoints[0]).toEqual({ x: 0, y: 0, z: undefined })
    })

    it('should return empty array for elements without points', () => {
      const extractedPoints = points(testRectangle)
      expect(extractedPoints).toEqual([])
    })

    it('should handle invalid elements', () => {
      const extractedPoints = points(null)
      expect(extractedPoints).toEqual([])
    })
  })

  describe('Element Utilities', () => {
    it('should calculate distance between elements', () => {
      const distance = distanceBetween(testRectangle, testEllipse)
      expect(typeof distance).toBe('number')
      expect(distance).toBeGreaterThanOrEqual(0)
    })

    it('should check element overlap', () => {
      const overlap = doElementsOverlap(testRectangle, testEllipse)
      expect(typeof overlap).toBe('boolean')
    })

    it('should sort elements by z-index', () => {
      const sorted = sortByZIndex(testElements)
      expect(sorted).toHaveLength(testElements.length)

      // Just check that sorting doesn't break the array
      expect(Array.isArray(sorted)).toBe(true)
      expect(sorted.length).toBe(testElements.length)
    })

    it('should group elements by layer', () => {
      const grouped = groupByLayer(testElements)
      expect(typeof grouped).toBe('object')
    })

    it('should filter elements by type', () => {
      const rectangles = filterByType(testElements, ElementType.RECTANGLE)
      expect(rectangles).toHaveLength(1)
      expect(rectangles[0].type).toBe(ElementType.RECTANGLE)
    })

    it('should filter elements by category', () => {
      const baseElements = filterByCategory(testElements, MajorCategory.BASE)
      expect(Array.isArray(baseElements)).toBe(true)
      // Just check that filtering doesn't break
      expect(baseElements.length).toBeGreaterThanOrEqual(0)
    })
  })

  describe('Error Handling', () => {
    it('should handle null elements gracefully', () => {
      // These functions may throw for null inputs, which is acceptable behavior
      try {
        isRectangle(null as any)
      } catch (error) {
        expect(error).toBeInstanceOf(TypeError)
      }

      try {
        isEllipse(null as any)
      } catch (error) {
        expect(error).toBeInstanceOf(TypeError)
      }
    })

    it('should handle undefined properties', () => {
      const elementWithoutProps = {
        id: 'test',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        majorCategory: MajorCategory.BASE,
        minorCategory: 'test',
      } as any

      expect(() => getRectangleProps(elementWithoutProps)).not.toThrow()
    })

    it('should handle invalid element arrays', () => {
      expect(() => sortByZIndex([])).not.toThrow()
      expect(() => filterByType([], ElementType.RECTANGLE)).not.toThrow()
    })

    it('should handle malformed elements', () => {
      const malformedElement = {
        id: 'malformed',
        // Missing required properties
      } as any

      expect(() => isRectangle(malformedElement)).not.toThrow()
    })
  })

  describe('Performance', () => {
    it('should handle large element arrays efficiently', () => {
      const largeElementArray = Array.from({ length: 1000 }, (_, i) => ({
        ...testRectangle,
        id: `element-${i}`,
        position: { x: i, y: i },
      }))

      const startTime = Date.now()

      sortByZIndex(largeElementArray)
      filterByType(largeElementArray, ElementType.RECTANGLE)
      filterByCategory(largeElementArray, MajorCategory.BASE)

      const endTime = Date.now()
      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly
    })

    it('should handle many property extractions efficiently', () => {
      const startTime = Date.now()

      for (let i = 0; i < 10000; i++) {
        getRectangleProps(testRectangle)
        getEllipseProps(testEllipse)
        getPolygonProps(testPolygon)
        isRectangle(testRectangle)
        isEllipse(testEllipse)
      }

      const endTime = Date.now()
      expect(endTime - startTime).toBeLessThan(100) // Should complete quickly
    })
  })

  describe('Edge Cases', () => {
    it('should handle elements with zero dimensions', () => {
      const zeroElement = {
        ...testRectangle,
        properties: { width: 0, height: 0 },
      }

      expect(() => getRectangleProps(zeroElement)).not.toThrow()
    })

    it('should handle elements with negative dimensions', () => {
      const negativeElement = {
        ...testRectangle,
        properties: { width: -100, height: -50 },
      }

      expect(() => getRectangleProps(negativeElement)).not.toThrow()
    })

    it('should handle circular references in element arrays', () => {
      const circularArray = [testRectangle, testEllipse]
      // Add circular reference (if the implementation supports it)
      ;(circularArray as any).circular = circularArray

      expect(() => filterByType(circularArray, ElementType.RECTANGLE)).not.toThrow()
      expect(() => sortByZIndex(circularArray)).not.toThrow()
    })
  })
})
