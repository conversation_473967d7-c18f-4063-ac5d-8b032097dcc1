import type { PredefinedElement } from './element/definitions/predefinedElements'
import type { ElementType } from './elementDefinitions'
import type { ElementType as CoreElementType, MajorCategory, MinorCategory } from '@/types/core/elementDefinitions'

// Define SidebarAsset for use in sidebars and drag-and-drop operations
export interface SidebarAsset {
  elementType: ElementType | string // ElementType for core shapes, string for others like 'IMAGE'
  name: string
  assetId: string // A unique ID for the asset, often derived from name or type
  properties?: Record<string, unknown> // Default properties for the element
  // Optional fields for more complex assets
  category?: string
  fixtureCategory?: string
  mountingType?: string
  subtypes?: unknown[]
  icon?: React.ComponentType<{ className?: string }> // Optional: For UI representation
  description?: string // Optional: Brief description of the asset
}

/**
 * Represents an asset that can be pinned to a quick access bar or similar UI.
 */
export interface PinnedAsset {
  type: CoreElementType
  isSpecific: boolean
  moduleId?: MajorCategory
  stepId?: MinorCategory
  assetName?: string
  predefinedElementData?: PredefinedElement
}
