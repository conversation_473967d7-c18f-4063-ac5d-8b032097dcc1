/**
 * Core Type Definitions for Space Planning Strategies and Evaluations
 *
 * @remarks
 * This module defines interfaces and types essential for implementing space planning
 * logic within the application. This includes strategies for evaluating various aspects
 * of a space design, such as space utilization, pathway accessibility, and ergonomics.
 *
 * Key definitions include:
 * - {@link PathwayCheckResult}: Structures the outcome of pathway width validations.
 * - {@link SpaceType}: A string literal union defining various types of spaces (e.g., 'kitchen', 'bedroom').
 * - {@link SpacePlanningStandard}: Defines standard configuration values for space planning, like minimum pathway widths.
 * - {@link ErgonomicsEvaluationResult}: Structures the findings of an ergonomic assessment.
 * - {@link SpacePlanningStrategy}: The core strategy interface that concrete space planning
 *   evaluation classes must implement.
 *
 * @module types/core/compute/spaceComputeTypes
 * @see {@link Element} from '@/types/core/elementDefinitions'
 * @see {@link Path.Line} from '@/types/core/elementDefinitions'
 * @see {@link ../computeInterfaces} for other computation strategy interfaces.
 */

import type { Element, Path } from '@/types/core/elementDefinitions'

/**
 * Represents the detailed result of a pathway width validation check.
 *
 * @remarks
 * This interface structures the output from a pathway validation, indicating whether
 * a specific pathway meets the required minimum width and, if not, which elements
 * might be causing an obstruction.
 */
export interface PathwayCheckResult {
  /** A unique identifier for the pathway that was checked. */
  pathwayId: string

  /** A boolean indicating if the pathway meets the minimum width requirements (`true`) or not (`false`). */
  isValid: boolean

  /** The actual minimum width measured for the pathway, in the project's default units (e.g., meters). */
  actualWidth: number

  /** An optional array of string identifiers for {@link Element | elements} that are obstructing or narrowing the pathway. */
  blockingElements?: string[]
}

/**
 * Defines a set of standard space types, each potentially having specific planning requirements or characteristics.
 *
 * @remarks
 * This string literal union type is used to categorize different kinds of spaces
 * (e.g., 'kitchen', 'bedroom', 'office'). This categorization allows for the application
 * of type-specific planning standards, ergonomic rules, or functional requirements
 * during space design and evaluation.
 */
export type SpaceType =
  | 'accessible' // Spaces designed for accessibility needs.
  | 'children' // Spaces designed for children (e.g., playrooms, nurseries).
  | 'multifunction' // Spaces designed to serve multiple purposes.
  | 'study' // Home offices or study areas.
  | 'bedroom' // Bedrooms.
  | 'living' // Living rooms or general family areas.
  | 'kitchen' // Kitchens.
  | 'bathroom' // Bathrooms.
  | 'storage' // Storage areas (e.g., closets, utility rooms).
  | 'outdoor' // General outdoor spaces.
  | 'balcony' // Balconies or terraces.
  | 'elderly' // Spaces designed with consideration for elderly occupants.

/**
 * Defines standard configuration values for space planning calculations and evaluations.
 *
 * @remarks
 * These values, typically expressed in meters for physical dimensions, establish baseline
 * requirements or targets for various aspects of space design, such as minimum pathway
 * widths, clearances around furniture, and desired space utilization efficiency.
 */
export interface SpacePlanningStandard {
  /** The minimum required width for pathways, typically in meters. */
  minPathwayWidth: number

  /** The minimum required clearance space around furniture and other objects, typically in meters. */
  minClearance: number

  /** The recommended or target space utilization ratio (e.g., 0.6 for 60%). A value between 0 and 1. */
  recommendedUtilization: number
}

/**
 * Represents the detailed result of an ergonomics evaluation for a workspace or specific area.
 *
 * @remarks
 * This interface structures the output of an ergonomic assessment. It indicates overall
 * validity against ergonomic standards and provides lists of identified issues and
 * actionable recommendations for improvement. Optional fields for severity and
 * compliance scores allow for more nuanced reporting.
 */
export interface ErgonomicsEvaluationResult {
  /** A boolean indicating if the evaluated space meets ergonomic standards (`true`) or not (`false`). */
  isValid: boolean

  /** An array of strings, where each string describes a specific ergonomic issue identified. */
  issues: string[]

  /** An array of strings, where each string offers a suggestion or recommendation to address identified issues. */
  recommendations: string[]

  /** An optional numeric rating (e.g., 1-5, where 5 is most severe) for the most critical issue, or an overall severity. */
  severity?: number

  /** An optional numeric score (e.g., 0-100) indicating the overall ergonomic quality or compliance level. */
  complianceScore?: number
}

/**
 * Defines the contract for a space planning strategy.
 *
 * @remarks
 * Any class implementing this interface is responsible for providing specific logic
 * for various types of space analysis and evaluation. This can include calculating
 * space utilization, checking pathway clearances, assessing workspace ergonomics,
 * or other planning functions relevant to a particular {@link SpaceType}.
 *
 * @see {@link Element} from '@/types/core/elementDefinitions'
 * @see {@link Path.Line} from '@/types/core/elementDefinitions'
 * @see {@link SpaceType}
 * @see {@link PathwayCheckResult}
 * @see {@link ErgonomicsEvaluationResult}
 */
export interface SpacePlanningStrategy {
  /**
   * Calculates the space utilization ratio for a given room or defined area.
   *
   * @param elements - An array of all {@link Element | elements} present within the space.
   * @param roomBoundary - The {@link Element} (e.g., a Room element or a boundary shape)
   *                       that defines the perimeter of the space being analyzed.
   * @returns The space utilization ratio, typically as a decimal between 0 (no utilization)
   *          and 1 (100% utilization of usable area by functional elements).
   */
  calculateSpaceUtilization: (elements: Element[], roomBoundary: Element) => number

  /**
   * Checks if defined pathways within a space meet specified minimum width standards.
   *
   * @param elements - An array of all {@link Element | elements} within the space that might obstruct pathways.
   * @param pathways - An array of {@link Path.Line | Line paths} representing the centerlines of the pathways to be checked.
   * @param minWidth - The minimum required width for the pathways, in the project's default units.
   * @returns An array of {@link PathwayCheckResult} objects, one for each pathway, detailing its validity and actual width.
   */
  checkPathwayWidth: (elements: Element[], pathways: Path.Line[], minWidth: number) => PathwayCheckResult[]

  /**
   * Evaluates the ergonomics of a workspace, typically involving a desk and chair,
   * but potentially adaptable to other functional areas.
   *
   * @param elements - An array of all {@link Element | elements} in the relevant space,
   *                   which might influence or be part of the ergonomic setup.
   * @param deskElement - The {@link Element} representing the primary work surface (e.g., desk, table).
   * @param chairElement - The {@link Element} representing the primary seating (e.g., office chair, stool).
   * @returns An {@link ErgonomicsEvaluationResult} object detailing the findings, issues, and recommendations.
   */
  evaluateErgonomics: (
    elements: Element[],
    deskElement: Element,
    chairElement: Element
  ) => ErgonomicsEvaluationResult

  /**
   * Gets the specific {@link SpaceType} that this strategy is designed to handle or for which it provides standards.
   * This allows the {@link ComputeFacade} or {@link StrategyRegistry} to select the appropriate strategy.
   * @returns The {@link SpaceType} this strategy applies to.
   */
  getSpaceType: () => SpaceType
}
