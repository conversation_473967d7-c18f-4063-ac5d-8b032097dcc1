import { describe, expect, it } from 'vitest'

// Test basic string utility functions
describe('string Utils - Basic Tests', () => {
  it('should handle basic string operations', () => {
    const str = 'Hello World'

    expect(str.length).toBe(11)
    expect(str.toLowerCase()).toBe('hello world')
    expect(str.toUpperCase()).toBe('HELLO WORLD')
    expect(str.charAt(0)).toBe('H')
    expect(str.charAt(6)).toBe('W')
  })

  it('should handle string trimming', () => {
    const str = '  Hello World  '

    expect(str.trim()).toBe('Hello World')
    expect(str.trimStart()).toBe('Hello World  ')
    expect(str.trimEnd()).toBe('  Hello World')
  })

  it('should handle string splitting and joining', () => {
    const str = 'apple,banana,cherry'
    const parts = str.split(',')

    expect(parts).toEqual(['apple', 'banana', 'cherry'])
    expect(parts.length).toBe(3)
    expect(parts.join(' | ')).toBe('apple | banana | cherry')
  })

  it('should handle string searching', () => {
    const str = 'Hello World'

    expect(str.indexOf('World')).toBe(6)
    expect(str.indexOf('xyz')).toBe(-1)
    expect(str.includes('Hello')).toBe(true)
    expect(str.includes('xyz')).toBe(false)
    expect(str.startsWith('Hello')).toBe(true)
    expect(str.endsWith('World')).toBe(true)
  })

  it('should handle string replacement', () => {
    const str = 'Hello World'

    expect(str.replace('World', 'Universe')).toBe('Hello Universe')
    expect(str.replace(/l/g, 'L')).toBe('HeLLo WorLd')
  })

  it('should handle string slicing and substring', () => {
    const str = 'Hello World'

    expect(str.slice(0, 5)).toBe('Hello')
    expect(str.slice(6)).toBe('World')
    expect(str.substring(0, 5)).toBe('Hello')
    expect(str.substr(6, 5)).toBe('World')
  })

  it('should handle string padding', () => {
    const str = '42'

    expect(str.padStart(5, '0')).toBe('00042')
    expect(str.padEnd(5, '0')).toBe('42000')
  })

  it('should handle string repetition', () => {
    const str = 'abc'

    expect(str.repeat(3)).toBe('abcabcabc')
    expect(str.repeat(0)).toBe('')
  })
})
