/**
 * Bounding Box Calculation Strategy for Arc Elements
 *
 * @remarks
 * This strategy implements the {@link BoundingBoxCalculatorStrategy} for calculating
 * the axis-aligned bounding box of Arc elements ({@link CoreElementType.ARC}).
 *
 * The calculation considers the arc's center, radius, start angle, end angle,
 * and whether it's a closed sector. It delegates the core geometric calculation
 * to the `calculateArcBoundingBoxUtil` function from
 * `../../../../lib/utils/geometry/arcUtils`.
 *
 * The strategy validates that the element is of type `ARC` and that its
 * defining properties (center, radius, angles) are valid.
 *
 * @module core/compute/strategies/bounding-box/ArcBoundingBoxStrategy
 * @see {@link Path.Arc} for the arc element type definition.
 * @see {@link IBoundingBoxType} for the bounding box structure.
 */
import type { BoundingBoxCalculatorStrategy } from '../../../../types/core/compute'
import type { BoundingBox as IBoundingBoxType } from '../../../../types/core/element/geometry/bounding-box'
import type { Element, Path, ShapeElement } from '../../../../types/core/elementDefinitions'
import { calculateArcBoundingBox as calculateArcBoundingBoxUtil } from '../../../../lib/utils/geometry/arcUtils'
// BoundingBoxClass is not directly used here, but calculateArcBoundingBoxUtil returns it.
import { CoreError } from '../../../../services/system/error-service/coreError'
import { ElementType as CoreElementType } from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

// Local degreesToRadians helper removed

/**
 * Implements the {@link BoundingBoxCalculatorStrategy} for {@link CoreElementType.ARC} elements.
 */
export class ArcBoundingBoxStrategy implements BoundingBoxCalculatorStrategy {
  /**
   * Calculates the axis-aligned bounding box of an Arc element.
   *
   * @param element - The arc element, expected to conform to {@link Path.Arc} and have type {@link CoreElementType.ARC}.
   * @returns An {@link IBoundingBoxType} object representing the bounding box.
   * @throws {@link CoreError} if the provided element is not of type `ARC`.
   * @throws {@link CoreError} if the arc's properties (center, radius, angles) are invalid.
   */
  public calculateBoundingBox(element: Element): IBoundingBoxType {
    if (element.type !== CoreElementType.ARC) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `ArcBoundingBoxStrategy can only calculate bounding box for ARC elements, got ${element.type}`,
        undefined,
        { component: 'ArcBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    const baseShapeElement = element as ShapeElement
    const properties = baseShapeElement.properties || {}

    // Get arc properties from the properties object
    const center = baseShapeElement.position
    const radius = (properties.radius as number) || (properties.rx as number) || 0
    const startAngleDegrees = properties.startAngle as number
    const endAngleDegrees = properties.endAngle as number
    // const closed = arcElement.closed === true; // This variable is unused, arcElement.closed is used directly later

    if (center == null || typeof center.x !== 'number' || typeof center.y !== 'number') {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid center point for Arc ID: ${element.id}`,
        undefined,
        { component: 'ArcBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id, metadata: { center } },
      )
    }
    if (typeof radius !== 'number' || radius < 0 || !Number.isFinite(radius)) { // Allow radius === 0 for a point
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid radius: ${radius} for Arc ID: ${element.id}`,
        undefined,
        { component: 'ArcBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id, metadata: { radius } },
      )
    }
    if (typeof startAngleDegrees !== 'number' || typeof endAngleDegrees !== 'number' || !Number.isFinite(startAngleDegrees) || !Number.isFinite(endAngleDegrees)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid angles for Arc ID: ${element.id}`,
        undefined,
        { component: 'ArcBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id, metadata: { startAngleDegrees, endAngleDegrees } },
      )
    }

    // const startAngleRad = toRadians(startAngleDegrees); // Conversion is done inside calculateArcBoundingBoxUtil
    // const endAngleRad = toRadians(endAngleDegrees);

    // Create an ArcProperties compatible object to pass to the utility function
    // Construct an object that satisfies the Path.Arc interface,
    // ensuring all inherited and specific properties are present.
    const arcPropertiesForUtil = {
      // Properties from Element (via ShapeElement)
      id: baseShapeElement.id,
      type: CoreElementType.ARC, // arcElement.type should also be ARC
      visible: baseShapeElement.visible,
      locked: baseShapeElement.locked,
      metadata: baseShapeElement.metadata,

      // Properties from ShapeElement
      position: baseShapeElement.position,
      rotation: baseShapeElement.rotation,
      selectable: baseShapeElement.selectable,
      draggable: baseShapeElement.draggable,
      showHandles: baseShapeElement.showHandles,
      fill: baseShapeElement.fill,
      stroke: baseShapeElement.stroke,
      strokeWidth: baseShapeElement.strokeWidth,
      opacity: baseShapeElement.opacity,
      strokeDasharray: baseShapeElement.strokeDasharray,
      layer: baseShapeElement.layer,
      properties: baseShapeElement.properties,

      // Arc specific properties from properties
      radius,
      startAngle: startAngleDegrees,
      endAngle: endAngleDegrees,
      closed: Boolean(properties.closed),
    } as unknown as Path.Arc // Assert the constructed object to Path.Arc

    // Delegate to the utility function which returns a BoundingBoxClass instance
    // BoundingBoxClass implements IBoundingBoxType
    return calculateArcBoundingBoxUtil(arcPropertiesForUtil)
  }

  public getElementType(): CoreElementType {
    return CoreElementType.ARC
  }
}
