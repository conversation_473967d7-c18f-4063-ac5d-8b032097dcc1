/**
 * Event System Types
 *
 * This file re-exports all types related to the event system.
 *
 * @module types/services/events
 */

// Core event types - Using named imports to avoid naming conflicts
// Explicitly import and re-export from computeEvents to avoid naming conflicts
import type * as ComputeEvents from './computeEvents'
// ComputeEvents.TransformOptions is not re-exported to avoid conflicts

// Explicitly import and re-export from shapeEvents to avoid naming conflicts
import type * as ShapeEvents from './shapeEvents'

export type {
  BatchEvent,
  ComputeEvent,
  TransformEvent,
  ValidationEvent,
} from './computeEvents'
// Event domain-specific types - explicitly export types to avoid ambiguity
export * from './dataEvents'

export type {
  BaseEvent,
  EventBus,
  EventBusConfig, // Added
  EventHandler,
  EventSubscriptionOptions, // Added
  ExtendedEventBus,
  TypedEvent,
} from './eventCore'

export type {
  AppEventMap,
} from './eventRegistry' // Corrected export source for AppEventMap

export type {
  AppEvent,
  AppEventRegistry,
  ErrorDetails,
  FileData,
  StateValue,
} from './eventRegistry'
export { AppEventType } from './eventTypes'
// Utility functions
export * from './eventUtils'
export * from './exportEvents'
export * from './gridEvents'
export * from './keyboardEvents'
export * from './renderEvents'
export type {
  SelectionChangedEvent,
  Shape,
  ShapeAddEvent,
  ShapeCreateEvent,
  ShapeDeleteEvent,
  ShapeDuplicateEvent,
  ShapeEditEvent,
  ShapeSelectedEvent,
  ShapeSelectionEvent,
  ShapeSelectRequestEvent,
  ShapeTransformEvent,
  ShapeUpdateRequestEvent,
  ShapeUpdateSuccessEvent,
  // TransformOptions not re-exported to avoid conflicts
} from './shapeEvents'
export * from './systemEvents'

export * from './templateEvents'
export * from './uiEvents'

export * from './viewEvents'

// Re-export TransformOptions with type aliases to resolve ambiguity
/**
 * Shape-specific transformation options.
 * Used to disambiguate from other TransformOptions types.
 */
export type ShapeTransformOptions = ShapeEvents.TransformOptions

/**
 * Compute-specific transformation options.
 * Used to disambiguate from other TransformOptions types.
 */
export type ComputeTransformOptions = ComputeEvents.TransformOptions
