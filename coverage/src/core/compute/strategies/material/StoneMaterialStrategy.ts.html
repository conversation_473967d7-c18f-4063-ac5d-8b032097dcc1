
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/core/compute/strategies/material/StoneMaterialStrategy.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> / <a href="index.html">src/core/compute/strategies/material</a> StoneMaterialStrategy.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/120</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/120</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * Material Calculation Strategy for Stone (e.g., Tiles, Slabs)
 *
 * @remarks
 * This strategy implements the {@link IMaterialCalculatorStrategy} to calculate
 * the required amount of stone material (like marble, granite, tiles) for a given area.
 *
 * It first determines the area of the input `element` (currently assuming the element
 * has a `getArea()` or `compute.area()` method - marked as TODO for refactoring).
 *
 * Key inputs from {@link MaterialCalculationOptions} include:
 * - `stoneType`: Affects default wastage factors if `wastageRate` is not provided.
 * - `unitSize` (or `tileWidth`, `tileLength` as fallback): Dimensions of individual stone pieces.
 * - `thickness`: For volume and weight calculations.
 * - `patternMatching`: Influences default wastage.
 * - `jointWidth`: If `includeJoints` is true.
 * - `wastageRate`: Overrides default wastage factors.
 * - `density`: For weight calculation.
 * - `adhesivePerSqm`, `groutPerSqm`, `sealantPerSqm`: For calculating auxiliary materials.
 *
 * The strategy calculates the number of tiles needed, total stone area (including wastage),
 * volume, weight, and quantities of adhesive, grout, and sealant.
 *
 * The `calculateMaterialAmount` method returns a summarized {@link MaterialCalculationResult},
 * while a private `calculateDetailedMaterial` method provides a more granular breakdown.
 *
 * @module core/compute/strategies/material/StoneMaterialStrategy
 * @see {@link IMaterialCalculatorStrategy}
 * @see {@link MaterialCalculationOptions}
 * @see {@link MaterialCalculationResult}
 */
import type {
  MaterialCalculatorStrategy as IMaterialCalculatorStrategy, // Renamed for consistency
  MaterialCalculationOptions,
  MaterialCalculationResult,
} from '@/types/core/compute' // Corrected import path
import type { Element } from '@/types/core/elementDefinitions'
<span class="cstat-no" title="statement not covered" >import { CoreError, ErrorType } from '@/services/system/error-service'</span>
<span class="cstat-no" title="statement not covered" >import { ElementType as CoreElementType } from '@/types/core/elementDefinitions'</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export class StoneMaterialStrategy implements IMaterialCalculatorStrategy { // Renamed IMaterialCalculatorStrategy for consistency</span>
  /**
   * Calculates the amount of stone material required.
   *
   * @param element - The element (e.g., floor, countertop) for which stone material is being calculated.
   *                  Expected to have a method to compute its area.
   * @param materialType - The type of material, expected to be 'stone'.
   * @param options - Optional {@link MaterialCalculationOptions} to customize the calculation.
   * @returns A {@link MaterialCalculationResult} summarizing the stone material quantity.
   * @throws {@link CoreError} if `materialType` is not 'stone', or if area calculation fails or is invalid,
   *         or if essential options like tile dimensions (if applicable) are missing or invalid.
   */
<span class="cstat-no" title="statement not covered" >  public calculateMaterialAmount(element: Element, materialType: string, options?: MaterialCalculationOptions): MaterialCalculationResult {</span>
<span class="cstat-no" title="statement not covered" >    const detailedResult = this.calculateDetailedMaterial(element, materialType, options)</span>
    // Ensure the returned object matches MaterialCalculationResult structure
<span class="cstat-no" title="statement not covered" >    return {</span>
<span class="cstat-no" title="statement not covered" >      amount: detailedResult.totalArea, // This is totalStoneAreaWithWastage</span>
<span class="cstat-no" title="statement not covered" >      unit: 'm²', // Standard symbol for Square meters</span>
<span class="cstat-no" title="statement not covered" >      unitCount: detailedResult.tiles,</span>
<span class="cstat-no" title="statement not covered" >      unitType: 'tile',</span>
<span class="cstat-no" title="statement not covered" >      amountWithWastage: detailedResult.totalArea, // totalArea from detailedResult already includes wastage</span>
      // Optionally, other details like volume or weight could be added if MaterialCalculationResult is extended
      // For now, they are part of the internal detailedResult but not the primary MaterialCalculationResult.
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Calculates a detailed breakdown of stone and related installation materials.
   * @private
   * @param element - The element to be covered with stone.
   * @param materialType - Expected to be 'stone'.
   * @param options - Optional {@link MaterialCalculationOptions}.
   * @returns A record containing quantities for tiles, total area, volume, weight, adhesive, etc.
   * @throws {@link CoreError} if `materialType` is not 'stone', or if area calculation fails or is invalid,
   *         or if essential options like tile dimensions are missing or invalid.
   */
<span class="cstat-no" title="statement not covered" >  private calculateDetailedMaterial(element: Element, materialType: string, options?: MaterialCalculationOptions): Record&lt;string, number&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (materialType.toLowerCase() !== 'stone') {</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(</span>
<span class="cstat-no" title="statement not covered" >        ErrorType.InvalidParameter,</span>
<span class="cstat-no" title="statement not covered" >        `StoneMaterialStrategy can only calculate for 'stone' material type, got ${materialType}`,</span>
<span class="cstat-no" title="statement not covered" >      )</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // TODO: Refactor to avoid direct access to a potential 'getArea' or 'compute' property.
<span class="cstat-no" title="statement not covered" >    const computableElement = element as unknown as { getArea?: () =&gt; number, compute?: { area?: () =&gt; number } }</span>
<span class="cstat-no" title="statement not covered" >    let area: number</span>
<span class="cstat-no" title="statement not covered" >    if (computableElement.getArea !== null &amp;&amp; computableElement.getArea !== undefined &amp;&amp; typeof computableElement.getArea === 'function') {</span>
<span class="cstat-no" title="statement not covered" >      area = computableElement.getArea()</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else if (computableElement.compute?.area &amp;&amp; typeof computableElement.compute.area === 'function') {</span>
<span class="cstat-no" title="statement not covered" >      area = computableElement.compute.area()</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(ErrorType.ComputationError, `Element ${element.id} does not have a method to calculate area.`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (typeof area !== 'number' || area &lt; 0 || !Number.isFinite(area)) { // Area can be 0</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(ErrorType.ComputationError, `Invalid area for element ${element.id}: ${area}`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    if (area === 0) {</span>
<span class="cstat-no" title="statement not covered" >      console.warn(`[StoneMaterialStrategy] Area for element ${element.id} is 0. No stone needed.`)</span>
<span class="cstat-no" title="statement not covered" >      return { tiles: 0, totalArea: 0, volume: 0, weight: 0, adhesive: 0, grout: 0, sealant: 0, wasteFactor: 0 }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const materialOptions = options ?? {}</span>
<span class="cstat-no" title="statement not covered" >    const stoneType = (materialOptions.stoneType !== null &amp;&amp; materialOptions.stoneType !== undefined &amp;&amp; materialOptions.stoneType !== '') ? materialOptions.stoneType : 'marble'</span>
    // Use unitSize from options if available, otherwise default
<span class="cstat-no" title="statement not covered" >    const tileWidth = materialOptions.unitSize?.width ?? materialOptions.tileWidth ?? 0.6</span>
<span class="cstat-no" title="statement not covered" >    const tileLength = materialOptions.unitSize?.height ?? materialOptions.tileLength ?? 0.6</span>
<span class="cstat-no" title="statement not covered" >    const thickness = (materialOptions.thickness !== null &amp;&amp; materialOptions.thickness !== undefined &amp;&amp; materialOptions.thickness !== 0) ? materialOptions.thickness : 0.02</span>
<span class="cstat-no" title="statement not covered" >    const patternMatching = materialOptions.patternMatching ?? false</span>
<span class="cstat-no" title="statement not covered" >    const jointWidth = (materialOptions.jointWidth !== null &amp;&amp; materialOptions.jointWidth !== undefined &amp;&amp; materialOptions.jointWidth !== 0) ? materialOptions.jointWidth : 0.003</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    let wasteFactor = materialOptions.wastageRate !== undefined ? materialOptions.wastageRate / 100 : 0.1 // Use wastageRate from options if present (as percentage)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (materialOptions.wastageRate === null || materialOptions.wastageRate === undefined) { // Only apply default logic if wastageRate is not in options</span>
<span class="cstat-no" title="statement not covered" >      if (patternMatching) {</span>
<span class="cstat-no" title="statement not covered" >        switch (stoneType) {</span>
<span class="cstat-no" title="statement not covered" >          case 'marble':</span>
<span class="cstat-no" title="statement not covered" >            wasteFactor = 0.20</span>
<span class="cstat-no" title="statement not covered" >            break</span>
<span class="cstat-no" title="statement not covered" >          case 'granite':</span>
<span class="cstat-no" title="statement not covered" >            wasteFactor = 0.15</span>
<span class="cstat-no" title="statement not covered" >            break</span>
<span class="cstat-no" title="statement not covered" >          case 'travertine':</span>
<span class="cstat-no" title="statement not covered" >            wasteFactor = 0.18</span>
<span class="cstat-no" title="statement not covered" >            break</span>
<span class="cstat-no" title="statement not covered" >          case 'quartz':</span>
<span class="cstat-no" title="statement not covered" >            wasteFactor = 0.12</span>
<span class="cstat-no" title="statement not covered" >            break</span>
<span class="cstat-no" title="statement not covered" >          default:</span>
<span class="cstat-no" title="statement not covered" >            wasteFactor = 0.15</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
<span class="cstat-no" title="statement not covered" >        switch (stoneType) {</span>
<span class="cstat-no" title="statement not covered" >          case 'marble':</span>
<span class="cstat-no" title="statement not covered" >            wasteFactor = 0.12</span>
<span class="cstat-no" title="statement not covered" >            break</span>
<span class="cstat-no" title="statement not covered" >          case 'granite':</span>
<span class="cstat-no" title="statement not covered" >            wasteFactor = 0.10</span>
<span class="cstat-no" title="statement not covered" >            break</span>
<span class="cstat-no" title="statement not covered" >          case 'travertine':</span>
<span class="cstat-no" title="statement not covered" >            wasteFactor = 0.12</span>
<span class="cstat-no" title="statement not covered" >            break</span>
<span class="cstat-no" title="statement not covered" >          case 'quartz':</span>
<span class="cstat-no" title="statement not covered" >            wasteFactor = 0.08</span>
<span class="cstat-no" title="statement not covered" >            break</span>
<span class="cstat-no" title="statement not covered" >          default:</span>
<span class="cstat-no" title="statement not covered" >            wasteFactor = 0.10</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const effectiveTileWidth = tileWidth - (materialOptions.includeJoints ? jointWidth : 0)</span>
<span class="cstat-no" title="statement not covered" >    const effectiveTileLength = tileLength - (materialOptions.includeJoints ? jointWidth : 0)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (effectiveTileWidth &lt;= 0 || effectiveTileLength &lt;= 0) {</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(ErrorType.InvalidParameter, 'Effective tile dimensions must be positive after considering joint width.')</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    const effectiveTileArea = effectiveTileWidth * effectiveTileLength</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const totalStoneAreaRaw = area // Raw area before wastage</span>
<span class="cstat-no" title="statement not covered" >    const totalStoneAreaWithWastage = totalStoneAreaRaw * (1 + wasteFactor)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const tilesNeeded = Math.ceil(totalStoneAreaWithWastage / effectiveTileArea)</span>
<span class="cstat-no" title="statement not covered" >    const totalVolume = totalStoneAreaWithWastage * thickness</span>
<span class="cstat-no" title="statement not covered" >    const density = (materialOptions.density !== null &amp;&amp; materialOptions.density !== undefined &amp;&amp; materialOptions.density !== 0) ? materialOptions.density : 2700</span>
<span class="cstat-no" title="statement not covered" >    const totalWeight = totalVolume * density</span>
<span class="cstat-no" title="statement not covered" >    const adhesivePerSqm = (materialOptions.adhesivePerSqm !== null &amp;&amp; materialOptions.adhesivePerSqm !== undefined &amp;&amp; materialOptions.adhesivePerSqm !== 0) ? materialOptions.adhesivePerSqm : 5</span>
<span class="cstat-no" title="statement not covered" >    const adhesiveNeeded = totalStoneAreaRaw * adhesivePerSqm // Base adhesive on raw area</span>
<span class="cstat-no" title="statement not covered" >    const groutPerSqm = (materialOptions.groutPerSqm !== null &amp;&amp; materialOptions.groutPerSqm !== undefined &amp;&amp; materialOptions.groutPerSqm !== 0) ? materialOptions.groutPerSqm : 0.5</span>
<span class="cstat-no" title="statement not covered" >    const groutNeeded = totalStoneAreaRaw * groutPerSqm // Base grout on raw area</span>
<span class="cstat-no" title="statement not covered" >    const sealantPerSqm = (materialOptions.sealantPerSqm !== null &amp;&amp; materialOptions.sealantPerSqm !== undefined &amp;&amp; materialOptions.sealantPerSqm !== 0) ? materialOptions.sealantPerSqm : 0.1</span>
<span class="cstat-no" title="statement not covered" >    const sealantNeeded = totalStoneAreaRaw * sealantPerSqm // Base sealant on raw area</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return {</span>
<span class="cstat-no" title="statement not covered" >      tiles: tilesNeeded,</span>
<span class="cstat-no" title="statement not covered" >      totalArea: totalStoneAreaWithWastage, // This is the area including wastage</span>
<span class="cstat-no" title="statement not covered" >      volume: totalVolume,</span>
<span class="cstat-no" title="statement not covered" >      weight: totalWeight,</span>
<span class="cstat-no" title="statement not covered" >      adhesive: adhesiveNeeded,</span>
<span class="cstat-no" title="statement not covered" >      grout: groutNeeded,</span>
<span class="cstat-no" title="statement not covered" >      sealant: sealantNeeded,</span>
<span class="cstat-no" title="statement not covered" >      wasteFactor: wasteFactor * 100, // Return as percentage</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Returns the primary element type this strategy is typically associated with.
   * @remarks Stone materials are often applied to rectangular or polygonal areas like floors or counter tops.
   * @returns {@link CoreElementType.RECTANGLE} (as a common example).
   */
<span class="cstat-no" title="statement not covered" >  public getElementType(): string {</span>
<span class="cstat-no" title="statement not covered" >    return CoreElementType.RECTANGLE // Could also be POLYGON or a generic 'surface' type</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Returns the specific material type this strategy calculates.
   * @returns The string 'stone'.
   */
<span class="cstat-no" title="statement not covered" >  public getMaterialType(): string {</span>
<span class="cstat-no" title="statement not covered" >    return 'stone'</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:58:01.486Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    