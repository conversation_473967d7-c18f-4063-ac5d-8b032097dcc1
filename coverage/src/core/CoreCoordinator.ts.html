
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/core/CoreCoordinator.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">src/core</a> CoreCoordinator.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/1267</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/1267</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a>
<a name='L618'></a><a href='#L618'>618</a>
<a name='L619'></a><a href='#L619'>619</a>
<a name='L620'></a><a href='#L620'>620</a>
<a name='L621'></a><a href='#L621'>621</a>
<a name='L622'></a><a href='#L622'>622</a>
<a name='L623'></a><a href='#L623'>623</a>
<a name='L624'></a><a href='#L624'>624</a>
<a name='L625'></a><a href='#L625'>625</a>
<a name='L626'></a><a href='#L626'>626</a>
<a name='L627'></a><a href='#L627'>627</a>
<a name='L628'></a><a href='#L628'>628</a>
<a name='L629'></a><a href='#L629'>629</a>
<a name='L630'></a><a href='#L630'>630</a>
<a name='L631'></a><a href='#L631'>631</a>
<a name='L632'></a><a href='#L632'>632</a>
<a name='L633'></a><a href='#L633'>633</a>
<a name='L634'></a><a href='#L634'>634</a>
<a name='L635'></a><a href='#L635'>635</a>
<a name='L636'></a><a href='#L636'>636</a>
<a name='L637'></a><a href='#L637'>637</a>
<a name='L638'></a><a href='#L638'>638</a>
<a name='L639'></a><a href='#L639'>639</a>
<a name='L640'></a><a href='#L640'>640</a>
<a name='L641'></a><a href='#L641'>641</a>
<a name='L642'></a><a href='#L642'>642</a>
<a name='L643'></a><a href='#L643'>643</a>
<a name='L644'></a><a href='#L644'>644</a>
<a name='L645'></a><a href='#L645'>645</a>
<a name='L646'></a><a href='#L646'>646</a>
<a name='L647'></a><a href='#L647'>647</a>
<a name='L648'></a><a href='#L648'>648</a>
<a name='L649'></a><a href='#L649'>649</a>
<a name='L650'></a><a href='#L650'>650</a>
<a name='L651'></a><a href='#L651'>651</a>
<a name='L652'></a><a href='#L652'>652</a>
<a name='L653'></a><a href='#L653'>653</a>
<a name='L654'></a><a href='#L654'>654</a>
<a name='L655'></a><a href='#L655'>655</a>
<a name='L656'></a><a href='#L656'>656</a>
<a name='L657'></a><a href='#L657'>657</a>
<a name='L658'></a><a href='#L658'>658</a>
<a name='L659'></a><a href='#L659'>659</a>
<a name='L660'></a><a href='#L660'>660</a>
<a name='L661'></a><a href='#L661'>661</a>
<a name='L662'></a><a href='#L662'>662</a>
<a name='L663'></a><a href='#L663'>663</a>
<a name='L664'></a><a href='#L664'>664</a>
<a name='L665'></a><a href='#L665'>665</a>
<a name='L666'></a><a href='#L666'>666</a>
<a name='L667'></a><a href='#L667'>667</a>
<a name='L668'></a><a href='#L668'>668</a>
<a name='L669'></a><a href='#L669'>669</a>
<a name='L670'></a><a href='#L670'>670</a>
<a name='L671'></a><a href='#L671'>671</a>
<a name='L672'></a><a href='#L672'>672</a>
<a name='L673'></a><a href='#L673'>673</a>
<a name='L674'></a><a href='#L674'>674</a>
<a name='L675'></a><a href='#L675'>675</a>
<a name='L676'></a><a href='#L676'>676</a>
<a name='L677'></a><a href='#L677'>677</a>
<a name='L678'></a><a href='#L678'>678</a>
<a name='L679'></a><a href='#L679'>679</a>
<a name='L680'></a><a href='#L680'>680</a>
<a name='L681'></a><a href='#L681'>681</a>
<a name='L682'></a><a href='#L682'>682</a>
<a name='L683'></a><a href='#L683'>683</a>
<a name='L684'></a><a href='#L684'>684</a>
<a name='L685'></a><a href='#L685'>685</a>
<a name='L686'></a><a href='#L686'>686</a>
<a name='L687'></a><a href='#L687'>687</a>
<a name='L688'></a><a href='#L688'>688</a>
<a name='L689'></a><a href='#L689'>689</a>
<a name='L690'></a><a href='#L690'>690</a>
<a name='L691'></a><a href='#L691'>691</a>
<a name='L692'></a><a href='#L692'>692</a>
<a name='L693'></a><a href='#L693'>693</a>
<a name='L694'></a><a href='#L694'>694</a>
<a name='L695'></a><a href='#L695'>695</a>
<a name='L696'></a><a href='#L696'>696</a>
<a name='L697'></a><a href='#L697'>697</a>
<a name='L698'></a><a href='#L698'>698</a>
<a name='L699'></a><a href='#L699'>699</a>
<a name='L700'></a><a href='#L700'>700</a>
<a name='L701'></a><a href='#L701'>701</a>
<a name='L702'></a><a href='#L702'>702</a>
<a name='L703'></a><a href='#L703'>703</a>
<a name='L704'></a><a href='#L704'>704</a>
<a name='L705'></a><a href='#L705'>705</a>
<a name='L706'></a><a href='#L706'>706</a>
<a name='L707'></a><a href='#L707'>707</a>
<a name='L708'></a><a href='#L708'>708</a>
<a name='L709'></a><a href='#L709'>709</a>
<a name='L710'></a><a href='#L710'>710</a>
<a name='L711'></a><a href='#L711'>711</a>
<a name='L712'></a><a href='#L712'>712</a>
<a name='L713'></a><a href='#L713'>713</a>
<a name='L714'></a><a href='#L714'>714</a>
<a name='L715'></a><a href='#L715'>715</a>
<a name='L716'></a><a href='#L716'>716</a>
<a name='L717'></a><a href='#L717'>717</a>
<a name='L718'></a><a href='#L718'>718</a>
<a name='L719'></a><a href='#L719'>719</a>
<a name='L720'></a><a href='#L720'>720</a>
<a name='L721'></a><a href='#L721'>721</a>
<a name='L722'></a><a href='#L722'>722</a>
<a name='L723'></a><a href='#L723'>723</a>
<a name='L724'></a><a href='#L724'>724</a>
<a name='L725'></a><a href='#L725'>725</a>
<a name='L726'></a><a href='#L726'>726</a>
<a name='L727'></a><a href='#L727'>727</a>
<a name='L728'></a><a href='#L728'>728</a>
<a name='L729'></a><a href='#L729'>729</a>
<a name='L730'></a><a href='#L730'>730</a>
<a name='L731'></a><a href='#L731'>731</a>
<a name='L732'></a><a href='#L732'>732</a>
<a name='L733'></a><a href='#L733'>733</a>
<a name='L734'></a><a href='#L734'>734</a>
<a name='L735'></a><a href='#L735'>735</a>
<a name='L736'></a><a href='#L736'>736</a>
<a name='L737'></a><a href='#L737'>737</a>
<a name='L738'></a><a href='#L738'>738</a>
<a name='L739'></a><a href='#L739'>739</a>
<a name='L740'></a><a href='#L740'>740</a>
<a name='L741'></a><a href='#L741'>741</a>
<a name='L742'></a><a href='#L742'>742</a>
<a name='L743'></a><a href='#L743'>743</a>
<a name='L744'></a><a href='#L744'>744</a>
<a name='L745'></a><a href='#L745'>745</a>
<a name='L746'></a><a href='#L746'>746</a>
<a name='L747'></a><a href='#L747'>747</a>
<a name='L748'></a><a href='#L748'>748</a>
<a name='L749'></a><a href='#L749'>749</a>
<a name='L750'></a><a href='#L750'>750</a>
<a name='L751'></a><a href='#L751'>751</a>
<a name='L752'></a><a href='#L752'>752</a>
<a name='L753'></a><a href='#L753'>753</a>
<a name='L754'></a><a href='#L754'>754</a>
<a name='L755'></a><a href='#L755'>755</a>
<a name='L756'></a><a href='#L756'>756</a>
<a name='L757'></a><a href='#L757'>757</a>
<a name='L758'></a><a href='#L758'>758</a>
<a name='L759'></a><a href='#L759'>759</a>
<a name='L760'></a><a href='#L760'>760</a>
<a name='L761'></a><a href='#L761'>761</a>
<a name='L762'></a><a href='#L762'>762</a>
<a name='L763'></a><a href='#L763'>763</a>
<a name='L764'></a><a href='#L764'>764</a>
<a name='L765'></a><a href='#L765'>765</a>
<a name='L766'></a><a href='#L766'>766</a>
<a name='L767'></a><a href='#L767'>767</a>
<a name='L768'></a><a href='#L768'>768</a>
<a name='L769'></a><a href='#L769'>769</a>
<a name='L770'></a><a href='#L770'>770</a>
<a name='L771'></a><a href='#L771'>771</a>
<a name='L772'></a><a href='#L772'>772</a>
<a name='L773'></a><a href='#L773'>773</a>
<a name='L774'></a><a href='#L774'>774</a>
<a name='L775'></a><a href='#L775'>775</a>
<a name='L776'></a><a href='#L776'>776</a>
<a name='L777'></a><a href='#L777'>777</a>
<a name='L778'></a><a href='#L778'>778</a>
<a name='L779'></a><a href='#L779'>779</a>
<a name='L780'></a><a href='#L780'>780</a>
<a name='L781'></a><a href='#L781'>781</a>
<a name='L782'></a><a href='#L782'>782</a>
<a name='L783'></a><a href='#L783'>783</a>
<a name='L784'></a><a href='#L784'>784</a>
<a name='L785'></a><a href='#L785'>785</a>
<a name='L786'></a><a href='#L786'>786</a>
<a name='L787'></a><a href='#L787'>787</a>
<a name='L788'></a><a href='#L788'>788</a>
<a name='L789'></a><a href='#L789'>789</a>
<a name='L790'></a><a href='#L790'>790</a>
<a name='L791'></a><a href='#L791'>791</a>
<a name='L792'></a><a href='#L792'>792</a>
<a name='L793'></a><a href='#L793'>793</a>
<a name='L794'></a><a href='#L794'>794</a>
<a name='L795'></a><a href='#L795'>795</a>
<a name='L796'></a><a href='#L796'>796</a>
<a name='L797'></a><a href='#L797'>797</a>
<a name='L798'></a><a href='#L798'>798</a>
<a name='L799'></a><a href='#L799'>799</a>
<a name='L800'></a><a href='#L800'>800</a>
<a name='L801'></a><a href='#L801'>801</a>
<a name='L802'></a><a href='#L802'>802</a>
<a name='L803'></a><a href='#L803'>803</a>
<a name='L804'></a><a href='#L804'>804</a>
<a name='L805'></a><a href='#L805'>805</a>
<a name='L806'></a><a href='#L806'>806</a>
<a name='L807'></a><a href='#L807'>807</a>
<a name='L808'></a><a href='#L808'>808</a>
<a name='L809'></a><a href='#L809'>809</a>
<a name='L810'></a><a href='#L810'>810</a>
<a name='L811'></a><a href='#L811'>811</a>
<a name='L812'></a><a href='#L812'>812</a>
<a name='L813'></a><a href='#L813'>813</a>
<a name='L814'></a><a href='#L814'>814</a>
<a name='L815'></a><a href='#L815'>815</a>
<a name='L816'></a><a href='#L816'>816</a>
<a name='L817'></a><a href='#L817'>817</a>
<a name='L818'></a><a href='#L818'>818</a>
<a name='L819'></a><a href='#L819'>819</a>
<a name='L820'></a><a href='#L820'>820</a>
<a name='L821'></a><a href='#L821'>821</a>
<a name='L822'></a><a href='#L822'>822</a>
<a name='L823'></a><a href='#L823'>823</a>
<a name='L824'></a><a href='#L824'>824</a>
<a name='L825'></a><a href='#L825'>825</a>
<a name='L826'></a><a href='#L826'>826</a>
<a name='L827'></a><a href='#L827'>827</a>
<a name='L828'></a><a href='#L828'>828</a>
<a name='L829'></a><a href='#L829'>829</a>
<a name='L830'></a><a href='#L830'>830</a>
<a name='L831'></a><a href='#L831'>831</a>
<a name='L832'></a><a href='#L832'>832</a>
<a name='L833'></a><a href='#L833'>833</a>
<a name='L834'></a><a href='#L834'>834</a>
<a name='L835'></a><a href='#L835'>835</a>
<a name='L836'></a><a href='#L836'>836</a>
<a name='L837'></a><a href='#L837'>837</a>
<a name='L838'></a><a href='#L838'>838</a>
<a name='L839'></a><a href='#L839'>839</a>
<a name='L840'></a><a href='#L840'>840</a>
<a name='L841'></a><a href='#L841'>841</a>
<a name='L842'></a><a href='#L842'>842</a>
<a name='L843'></a><a href='#L843'>843</a>
<a name='L844'></a><a href='#L844'>844</a>
<a name='L845'></a><a href='#L845'>845</a>
<a name='L846'></a><a href='#L846'>846</a>
<a name='L847'></a><a href='#L847'>847</a>
<a name='L848'></a><a href='#L848'>848</a>
<a name='L849'></a><a href='#L849'>849</a>
<a name='L850'></a><a href='#L850'>850</a>
<a name='L851'></a><a href='#L851'>851</a>
<a name='L852'></a><a href='#L852'>852</a>
<a name='L853'></a><a href='#L853'>853</a>
<a name='L854'></a><a href='#L854'>854</a>
<a name='L855'></a><a href='#L855'>855</a>
<a name='L856'></a><a href='#L856'>856</a>
<a name='L857'></a><a href='#L857'>857</a>
<a name='L858'></a><a href='#L858'>858</a>
<a name='L859'></a><a href='#L859'>859</a>
<a name='L860'></a><a href='#L860'>860</a>
<a name='L861'></a><a href='#L861'>861</a>
<a name='L862'></a><a href='#L862'>862</a>
<a name='L863'></a><a href='#L863'>863</a>
<a name='L864'></a><a href='#L864'>864</a>
<a name='L865'></a><a href='#L865'>865</a>
<a name='L866'></a><a href='#L866'>866</a>
<a name='L867'></a><a href='#L867'>867</a>
<a name='L868'></a><a href='#L868'>868</a>
<a name='L869'></a><a href='#L869'>869</a>
<a name='L870'></a><a href='#L870'>870</a>
<a name='L871'></a><a href='#L871'>871</a>
<a name='L872'></a><a href='#L872'>872</a>
<a name='L873'></a><a href='#L873'>873</a>
<a name='L874'></a><a href='#L874'>874</a>
<a name='L875'></a><a href='#L875'>875</a>
<a name='L876'></a><a href='#L876'>876</a>
<a name='L877'></a><a href='#L877'>877</a>
<a name='L878'></a><a href='#L878'>878</a>
<a name='L879'></a><a href='#L879'>879</a>
<a name='L880'></a><a href='#L880'>880</a>
<a name='L881'></a><a href='#L881'>881</a>
<a name='L882'></a><a href='#L882'>882</a>
<a name='L883'></a><a href='#L883'>883</a>
<a name='L884'></a><a href='#L884'>884</a>
<a name='L885'></a><a href='#L885'>885</a>
<a name='L886'></a><a href='#L886'>886</a>
<a name='L887'></a><a href='#L887'>887</a>
<a name='L888'></a><a href='#L888'>888</a>
<a name='L889'></a><a href='#L889'>889</a>
<a name='L890'></a><a href='#L890'>890</a>
<a name='L891'></a><a href='#L891'>891</a>
<a name='L892'></a><a href='#L892'>892</a>
<a name='L893'></a><a href='#L893'>893</a>
<a name='L894'></a><a href='#L894'>894</a>
<a name='L895'></a><a href='#L895'>895</a>
<a name='L896'></a><a href='#L896'>896</a>
<a name='L897'></a><a href='#L897'>897</a>
<a name='L898'></a><a href='#L898'>898</a>
<a name='L899'></a><a href='#L899'>899</a>
<a name='L900'></a><a href='#L900'>900</a>
<a name='L901'></a><a href='#L901'>901</a>
<a name='L902'></a><a href='#L902'>902</a>
<a name='L903'></a><a href='#L903'>903</a>
<a name='L904'></a><a href='#L904'>904</a>
<a name='L905'></a><a href='#L905'>905</a>
<a name='L906'></a><a href='#L906'>906</a>
<a name='L907'></a><a href='#L907'>907</a>
<a name='L908'></a><a href='#L908'>908</a>
<a name='L909'></a><a href='#L909'>909</a>
<a name='L910'></a><a href='#L910'>910</a>
<a name='L911'></a><a href='#L911'>911</a>
<a name='L912'></a><a href='#L912'>912</a>
<a name='L913'></a><a href='#L913'>913</a>
<a name='L914'></a><a href='#L914'>914</a>
<a name='L915'></a><a href='#L915'>915</a>
<a name='L916'></a><a href='#L916'>916</a>
<a name='L917'></a><a href='#L917'>917</a>
<a name='L918'></a><a href='#L918'>918</a>
<a name='L919'></a><a href='#L919'>919</a>
<a name='L920'></a><a href='#L920'>920</a>
<a name='L921'></a><a href='#L921'>921</a>
<a name='L922'></a><a href='#L922'>922</a>
<a name='L923'></a><a href='#L923'>923</a>
<a name='L924'></a><a href='#L924'>924</a>
<a name='L925'></a><a href='#L925'>925</a>
<a name='L926'></a><a href='#L926'>926</a>
<a name='L927'></a><a href='#L927'>927</a>
<a name='L928'></a><a href='#L928'>928</a>
<a name='L929'></a><a href='#L929'>929</a>
<a name='L930'></a><a href='#L930'>930</a>
<a name='L931'></a><a href='#L931'>931</a>
<a name='L932'></a><a href='#L932'>932</a>
<a name='L933'></a><a href='#L933'>933</a>
<a name='L934'></a><a href='#L934'>934</a>
<a name='L935'></a><a href='#L935'>935</a>
<a name='L936'></a><a href='#L936'>936</a>
<a name='L937'></a><a href='#L937'>937</a>
<a name='L938'></a><a href='#L938'>938</a>
<a name='L939'></a><a href='#L939'>939</a>
<a name='L940'></a><a href='#L940'>940</a>
<a name='L941'></a><a href='#L941'>941</a>
<a name='L942'></a><a href='#L942'>942</a>
<a name='L943'></a><a href='#L943'>943</a>
<a name='L944'></a><a href='#L944'>944</a>
<a name='L945'></a><a href='#L945'>945</a>
<a name='L946'></a><a href='#L946'>946</a>
<a name='L947'></a><a href='#L947'>947</a>
<a name='L948'></a><a href='#L948'>948</a>
<a name='L949'></a><a href='#L949'>949</a>
<a name='L950'></a><a href='#L950'>950</a>
<a name='L951'></a><a href='#L951'>951</a>
<a name='L952'></a><a href='#L952'>952</a>
<a name='L953'></a><a href='#L953'>953</a>
<a name='L954'></a><a href='#L954'>954</a>
<a name='L955'></a><a href='#L955'>955</a>
<a name='L956'></a><a href='#L956'>956</a>
<a name='L957'></a><a href='#L957'>957</a>
<a name='L958'></a><a href='#L958'>958</a>
<a name='L959'></a><a href='#L959'>959</a>
<a name='L960'></a><a href='#L960'>960</a>
<a name='L961'></a><a href='#L961'>961</a>
<a name='L962'></a><a href='#L962'>962</a>
<a name='L963'></a><a href='#L963'>963</a>
<a name='L964'></a><a href='#L964'>964</a>
<a name='L965'></a><a href='#L965'>965</a>
<a name='L966'></a><a href='#L966'>966</a>
<a name='L967'></a><a href='#L967'>967</a>
<a name='L968'></a><a href='#L968'>968</a>
<a name='L969'></a><a href='#L969'>969</a>
<a name='L970'></a><a href='#L970'>970</a>
<a name='L971'></a><a href='#L971'>971</a>
<a name='L972'></a><a href='#L972'>972</a>
<a name='L973'></a><a href='#L973'>973</a>
<a name='L974'></a><a href='#L974'>974</a>
<a name='L975'></a><a href='#L975'>975</a>
<a name='L976'></a><a href='#L976'>976</a>
<a name='L977'></a><a href='#L977'>977</a>
<a name='L978'></a><a href='#L978'>978</a>
<a name='L979'></a><a href='#L979'>979</a>
<a name='L980'></a><a href='#L980'>980</a>
<a name='L981'></a><a href='#L981'>981</a>
<a name='L982'></a><a href='#L982'>982</a>
<a name='L983'></a><a href='#L983'>983</a>
<a name='L984'></a><a href='#L984'>984</a>
<a name='L985'></a><a href='#L985'>985</a>
<a name='L986'></a><a href='#L986'>986</a>
<a name='L987'></a><a href='#L987'>987</a>
<a name='L988'></a><a href='#L988'>988</a>
<a name='L989'></a><a href='#L989'>989</a>
<a name='L990'></a><a href='#L990'>990</a>
<a name='L991'></a><a href='#L991'>991</a>
<a name='L992'></a><a href='#L992'>992</a>
<a name='L993'></a><a href='#L993'>993</a>
<a name='L994'></a><a href='#L994'>994</a>
<a name='L995'></a><a href='#L995'>995</a>
<a name='L996'></a><a href='#L996'>996</a>
<a name='L997'></a><a href='#L997'>997</a>
<a name='L998'></a><a href='#L998'>998</a>
<a name='L999'></a><a href='#L999'>999</a>
<a name='L1000'></a><a href='#L1000'>1000</a>
<a name='L1001'></a><a href='#L1001'>1001</a>
<a name='L1002'></a><a href='#L1002'>1002</a>
<a name='L1003'></a><a href='#L1003'>1003</a>
<a name='L1004'></a><a href='#L1004'>1004</a>
<a name='L1005'></a><a href='#L1005'>1005</a>
<a name='L1006'></a><a href='#L1006'>1006</a>
<a name='L1007'></a><a href='#L1007'>1007</a>
<a name='L1008'></a><a href='#L1008'>1008</a>
<a name='L1009'></a><a href='#L1009'>1009</a>
<a name='L1010'></a><a href='#L1010'>1010</a>
<a name='L1011'></a><a href='#L1011'>1011</a>
<a name='L1012'></a><a href='#L1012'>1012</a>
<a name='L1013'></a><a href='#L1013'>1013</a>
<a name='L1014'></a><a href='#L1014'>1014</a>
<a name='L1015'></a><a href='#L1015'>1015</a>
<a name='L1016'></a><a href='#L1016'>1016</a>
<a name='L1017'></a><a href='#L1017'>1017</a>
<a name='L1018'></a><a href='#L1018'>1018</a>
<a name='L1019'></a><a href='#L1019'>1019</a>
<a name='L1020'></a><a href='#L1020'>1020</a>
<a name='L1021'></a><a href='#L1021'>1021</a>
<a name='L1022'></a><a href='#L1022'>1022</a>
<a name='L1023'></a><a href='#L1023'>1023</a>
<a name='L1024'></a><a href='#L1024'>1024</a>
<a name='L1025'></a><a href='#L1025'>1025</a>
<a name='L1026'></a><a href='#L1026'>1026</a>
<a name='L1027'></a><a href='#L1027'>1027</a>
<a name='L1028'></a><a href='#L1028'>1028</a>
<a name='L1029'></a><a href='#L1029'>1029</a>
<a name='L1030'></a><a href='#L1030'>1030</a>
<a name='L1031'></a><a href='#L1031'>1031</a>
<a name='L1032'></a><a href='#L1032'>1032</a>
<a name='L1033'></a><a href='#L1033'>1033</a>
<a name='L1034'></a><a href='#L1034'>1034</a>
<a name='L1035'></a><a href='#L1035'>1035</a>
<a name='L1036'></a><a href='#L1036'>1036</a>
<a name='L1037'></a><a href='#L1037'>1037</a>
<a name='L1038'></a><a href='#L1038'>1038</a>
<a name='L1039'></a><a href='#L1039'>1039</a>
<a name='L1040'></a><a href='#L1040'>1040</a>
<a name='L1041'></a><a href='#L1041'>1041</a>
<a name='L1042'></a><a href='#L1042'>1042</a>
<a name='L1043'></a><a href='#L1043'>1043</a>
<a name='L1044'></a><a href='#L1044'>1044</a>
<a name='L1045'></a><a href='#L1045'>1045</a>
<a name='L1046'></a><a href='#L1046'>1046</a>
<a name='L1047'></a><a href='#L1047'>1047</a>
<a name='L1048'></a><a href='#L1048'>1048</a>
<a name='L1049'></a><a href='#L1049'>1049</a>
<a name='L1050'></a><a href='#L1050'>1050</a>
<a name='L1051'></a><a href='#L1051'>1051</a>
<a name='L1052'></a><a href='#L1052'>1052</a>
<a name='L1053'></a><a href='#L1053'>1053</a>
<a name='L1054'></a><a href='#L1054'>1054</a>
<a name='L1055'></a><a href='#L1055'>1055</a>
<a name='L1056'></a><a href='#L1056'>1056</a>
<a name='L1057'></a><a href='#L1057'>1057</a>
<a name='L1058'></a><a href='#L1058'>1058</a>
<a name='L1059'></a><a href='#L1059'>1059</a>
<a name='L1060'></a><a href='#L1060'>1060</a>
<a name='L1061'></a><a href='#L1061'>1061</a>
<a name='L1062'></a><a href='#L1062'>1062</a>
<a name='L1063'></a><a href='#L1063'>1063</a>
<a name='L1064'></a><a href='#L1064'>1064</a>
<a name='L1065'></a><a href='#L1065'>1065</a>
<a name='L1066'></a><a href='#L1066'>1066</a>
<a name='L1067'></a><a href='#L1067'>1067</a>
<a name='L1068'></a><a href='#L1068'>1068</a>
<a name='L1069'></a><a href='#L1069'>1069</a>
<a name='L1070'></a><a href='#L1070'>1070</a>
<a name='L1071'></a><a href='#L1071'>1071</a>
<a name='L1072'></a><a href='#L1072'>1072</a>
<a name='L1073'></a><a href='#L1073'>1073</a>
<a name='L1074'></a><a href='#L1074'>1074</a>
<a name='L1075'></a><a href='#L1075'>1075</a>
<a name='L1076'></a><a href='#L1076'>1076</a>
<a name='L1077'></a><a href='#L1077'>1077</a>
<a name='L1078'></a><a href='#L1078'>1078</a>
<a name='L1079'></a><a href='#L1079'>1079</a>
<a name='L1080'></a><a href='#L1080'>1080</a>
<a name='L1081'></a><a href='#L1081'>1081</a>
<a name='L1082'></a><a href='#L1082'>1082</a>
<a name='L1083'></a><a href='#L1083'>1083</a>
<a name='L1084'></a><a href='#L1084'>1084</a>
<a name='L1085'></a><a href='#L1085'>1085</a>
<a name='L1086'></a><a href='#L1086'>1086</a>
<a name='L1087'></a><a href='#L1087'>1087</a>
<a name='L1088'></a><a href='#L1088'>1088</a>
<a name='L1089'></a><a href='#L1089'>1089</a>
<a name='L1090'></a><a href='#L1090'>1090</a>
<a name='L1091'></a><a href='#L1091'>1091</a>
<a name='L1092'></a><a href='#L1092'>1092</a>
<a name='L1093'></a><a href='#L1093'>1093</a>
<a name='L1094'></a><a href='#L1094'>1094</a>
<a name='L1095'></a><a href='#L1095'>1095</a>
<a name='L1096'></a><a href='#L1096'>1096</a>
<a name='L1097'></a><a href='#L1097'>1097</a>
<a name='L1098'></a><a href='#L1098'>1098</a>
<a name='L1099'></a><a href='#L1099'>1099</a>
<a name='L1100'></a><a href='#L1100'>1100</a>
<a name='L1101'></a><a href='#L1101'>1101</a>
<a name='L1102'></a><a href='#L1102'>1102</a>
<a name='L1103'></a><a href='#L1103'>1103</a>
<a name='L1104'></a><a href='#L1104'>1104</a>
<a name='L1105'></a><a href='#L1105'>1105</a>
<a name='L1106'></a><a href='#L1106'>1106</a>
<a name='L1107'></a><a href='#L1107'>1107</a>
<a name='L1108'></a><a href='#L1108'>1108</a>
<a name='L1109'></a><a href='#L1109'>1109</a>
<a name='L1110'></a><a href='#L1110'>1110</a>
<a name='L1111'></a><a href='#L1111'>1111</a>
<a name='L1112'></a><a href='#L1112'>1112</a>
<a name='L1113'></a><a href='#L1113'>1113</a>
<a name='L1114'></a><a href='#L1114'>1114</a>
<a name='L1115'></a><a href='#L1115'>1115</a>
<a name='L1116'></a><a href='#L1116'>1116</a>
<a name='L1117'></a><a href='#L1117'>1117</a>
<a name='L1118'></a><a href='#L1118'>1118</a>
<a name='L1119'></a><a href='#L1119'>1119</a>
<a name='L1120'></a><a href='#L1120'>1120</a>
<a name='L1121'></a><a href='#L1121'>1121</a>
<a name='L1122'></a><a href='#L1122'>1122</a>
<a name='L1123'></a><a href='#L1123'>1123</a>
<a name='L1124'></a><a href='#L1124'>1124</a>
<a name='L1125'></a><a href='#L1125'>1125</a>
<a name='L1126'></a><a href='#L1126'>1126</a>
<a name='L1127'></a><a href='#L1127'>1127</a>
<a name='L1128'></a><a href='#L1128'>1128</a>
<a name='L1129'></a><a href='#L1129'>1129</a>
<a name='L1130'></a><a href='#L1130'>1130</a>
<a name='L1131'></a><a href='#L1131'>1131</a>
<a name='L1132'></a><a href='#L1132'>1132</a>
<a name='L1133'></a><a href='#L1133'>1133</a>
<a name='L1134'></a><a href='#L1134'>1134</a>
<a name='L1135'></a><a href='#L1135'>1135</a>
<a name='L1136'></a><a href='#L1136'>1136</a>
<a name='L1137'></a><a href='#L1137'>1137</a>
<a name='L1138'></a><a href='#L1138'>1138</a>
<a name='L1139'></a><a href='#L1139'>1139</a>
<a name='L1140'></a><a href='#L1140'>1140</a>
<a name='L1141'></a><a href='#L1141'>1141</a>
<a name='L1142'></a><a href='#L1142'>1142</a>
<a name='L1143'></a><a href='#L1143'>1143</a>
<a name='L1144'></a><a href='#L1144'>1144</a>
<a name='L1145'></a><a href='#L1145'>1145</a>
<a name='L1146'></a><a href='#L1146'>1146</a>
<a name='L1147'></a><a href='#L1147'>1147</a>
<a name='L1148'></a><a href='#L1148'>1148</a>
<a name='L1149'></a><a href='#L1149'>1149</a>
<a name='L1150'></a><a href='#L1150'>1150</a>
<a name='L1151'></a><a href='#L1151'>1151</a>
<a name='L1152'></a><a href='#L1152'>1152</a>
<a name='L1153'></a><a href='#L1153'>1153</a>
<a name='L1154'></a><a href='#L1154'>1154</a>
<a name='L1155'></a><a href='#L1155'>1155</a>
<a name='L1156'></a><a href='#L1156'>1156</a>
<a name='L1157'></a><a href='#L1157'>1157</a>
<a name='L1158'></a><a href='#L1158'>1158</a>
<a name='L1159'></a><a href='#L1159'>1159</a>
<a name='L1160'></a><a href='#L1160'>1160</a>
<a name='L1161'></a><a href='#L1161'>1161</a>
<a name='L1162'></a><a href='#L1162'>1162</a>
<a name='L1163'></a><a href='#L1163'>1163</a>
<a name='L1164'></a><a href='#L1164'>1164</a>
<a name='L1165'></a><a href='#L1165'>1165</a>
<a name='L1166'></a><a href='#L1166'>1166</a>
<a name='L1167'></a><a href='#L1167'>1167</a>
<a name='L1168'></a><a href='#L1168'>1168</a>
<a name='L1169'></a><a href='#L1169'>1169</a>
<a name='L1170'></a><a href='#L1170'>1170</a>
<a name='L1171'></a><a href='#L1171'>1171</a>
<a name='L1172'></a><a href='#L1172'>1172</a>
<a name='L1173'></a><a href='#L1173'>1173</a>
<a name='L1174'></a><a href='#L1174'>1174</a>
<a name='L1175'></a><a href='#L1175'>1175</a>
<a name='L1176'></a><a href='#L1176'>1176</a>
<a name='L1177'></a><a href='#L1177'>1177</a>
<a name='L1178'></a><a href='#L1178'>1178</a>
<a name='L1179'></a><a href='#L1179'>1179</a>
<a name='L1180'></a><a href='#L1180'>1180</a>
<a name='L1181'></a><a href='#L1181'>1181</a>
<a name='L1182'></a><a href='#L1182'>1182</a>
<a name='L1183'></a><a href='#L1183'>1183</a>
<a name='L1184'></a><a href='#L1184'>1184</a>
<a name='L1185'></a><a href='#L1185'>1185</a>
<a name='L1186'></a><a href='#L1186'>1186</a>
<a name='L1187'></a><a href='#L1187'>1187</a>
<a name='L1188'></a><a href='#L1188'>1188</a>
<a name='L1189'></a><a href='#L1189'>1189</a>
<a name='L1190'></a><a href='#L1190'>1190</a>
<a name='L1191'></a><a href='#L1191'>1191</a>
<a name='L1192'></a><a href='#L1192'>1192</a>
<a name='L1193'></a><a href='#L1193'>1193</a>
<a name='L1194'></a><a href='#L1194'>1194</a>
<a name='L1195'></a><a href='#L1195'>1195</a>
<a name='L1196'></a><a href='#L1196'>1196</a>
<a name='L1197'></a><a href='#L1197'>1197</a>
<a name='L1198'></a><a href='#L1198'>1198</a>
<a name='L1199'></a><a href='#L1199'>1199</a>
<a name='L1200'></a><a href='#L1200'>1200</a>
<a name='L1201'></a><a href='#L1201'>1201</a>
<a name='L1202'></a><a href='#L1202'>1202</a>
<a name='L1203'></a><a href='#L1203'>1203</a>
<a name='L1204'></a><a href='#L1204'>1204</a>
<a name='L1205'></a><a href='#L1205'>1205</a>
<a name='L1206'></a><a href='#L1206'>1206</a>
<a name='L1207'></a><a href='#L1207'>1207</a>
<a name='L1208'></a><a href='#L1208'>1208</a>
<a name='L1209'></a><a href='#L1209'>1209</a>
<a name='L1210'></a><a href='#L1210'>1210</a>
<a name='L1211'></a><a href='#L1211'>1211</a>
<a name='L1212'></a><a href='#L1212'>1212</a>
<a name='L1213'></a><a href='#L1213'>1213</a>
<a name='L1214'></a><a href='#L1214'>1214</a>
<a name='L1215'></a><a href='#L1215'>1215</a>
<a name='L1216'></a><a href='#L1216'>1216</a>
<a name='L1217'></a><a href='#L1217'>1217</a>
<a name='L1218'></a><a href='#L1218'>1218</a>
<a name='L1219'></a><a href='#L1219'>1219</a>
<a name='L1220'></a><a href='#L1220'>1220</a>
<a name='L1221'></a><a href='#L1221'>1221</a>
<a name='L1222'></a><a href='#L1222'>1222</a>
<a name='L1223'></a><a href='#L1223'>1223</a>
<a name='L1224'></a><a href='#L1224'>1224</a>
<a name='L1225'></a><a href='#L1225'>1225</a>
<a name='L1226'></a><a href='#L1226'>1226</a>
<a name='L1227'></a><a href='#L1227'>1227</a>
<a name='L1228'></a><a href='#L1228'>1228</a>
<a name='L1229'></a><a href='#L1229'>1229</a>
<a name='L1230'></a><a href='#L1230'>1230</a>
<a name='L1231'></a><a href='#L1231'>1231</a>
<a name='L1232'></a><a href='#L1232'>1232</a>
<a name='L1233'></a><a href='#L1233'>1233</a>
<a name='L1234'></a><a href='#L1234'>1234</a>
<a name='L1235'></a><a href='#L1235'>1235</a>
<a name='L1236'></a><a href='#L1236'>1236</a>
<a name='L1237'></a><a href='#L1237'>1237</a>
<a name='L1238'></a><a href='#L1238'>1238</a>
<a name='L1239'></a><a href='#L1239'>1239</a>
<a name='L1240'></a><a href='#L1240'>1240</a>
<a name='L1241'></a><a href='#L1241'>1241</a>
<a name='L1242'></a><a href='#L1242'>1242</a>
<a name='L1243'></a><a href='#L1243'>1243</a>
<a name='L1244'></a><a href='#L1244'>1244</a>
<a name='L1245'></a><a href='#L1245'>1245</a>
<a name='L1246'></a><a href='#L1246'>1246</a>
<a name='L1247'></a><a href='#L1247'>1247</a>
<a name='L1248'></a><a href='#L1248'>1248</a>
<a name='L1249'></a><a href='#L1249'>1249</a>
<a name='L1250'></a><a href='#L1250'>1250</a>
<a name='L1251'></a><a href='#L1251'>1251</a>
<a name='L1252'></a><a href='#L1252'>1252</a>
<a name='L1253'></a><a href='#L1253'>1253</a>
<a name='L1254'></a><a href='#L1254'>1254</a>
<a name='L1255'></a><a href='#L1255'>1255</a>
<a name='L1256'></a><a href='#L1256'>1256</a>
<a name='L1257'></a><a href='#L1257'>1257</a>
<a name='L1258'></a><a href='#L1258'>1258</a>
<a name='L1259'></a><a href='#L1259'>1259</a>
<a name='L1260'></a><a href='#L1260'>1260</a>
<a name='L1261'></a><a href='#L1261'>1261</a>
<a name='L1262'></a><a href='#L1262'>1262</a>
<a name='L1263'></a><a href='#L1263'>1263</a>
<a name='L1264'></a><a href='#L1264'>1264</a>
<a name='L1265'></a><a href='#L1265'>1265</a>
<a name='L1266'></a><a href='#L1266'>1266</a>
<a name='L1267'></a><a href='#L1267'>1267</a>
<a name='L1268'></a><a href='#L1268'>1268</a>
<a name='L1269'></a><a href='#L1269'>1269</a>
<a name='L1270'></a><a href='#L1270'>1270</a>
<a name='L1271'></a><a href='#L1271'>1271</a>
<a name='L1272'></a><a href='#L1272'>1272</a>
<a name='L1273'></a><a href='#L1273'>1273</a>
<a name='L1274'></a><a href='#L1274'>1274</a>
<a name='L1275'></a><a href='#L1275'>1275</a>
<a name='L1276'></a><a href='#L1276'>1276</a>
<a name='L1277'></a><a href='#L1277'>1277</a>
<a name='L1278'></a><a href='#L1278'>1278</a>
<a name='L1279'></a><a href='#L1279'>1279</a>
<a name='L1280'></a><a href='#L1280'>1280</a>
<a name='L1281'></a><a href='#L1281'>1281</a>
<a name='L1282'></a><a href='#L1282'>1282</a>
<a name='L1283'></a><a href='#L1283'>1283</a>
<a name='L1284'></a><a href='#L1284'>1284</a>
<a name='L1285'></a><a href='#L1285'>1285</a>
<a name='L1286'></a><a href='#L1286'>1286</a>
<a name='L1287'></a><a href='#L1287'>1287</a>
<a name='L1288'></a><a href='#L1288'>1288</a>
<a name='L1289'></a><a href='#L1289'>1289</a>
<a name='L1290'></a><a href='#L1290'>1290</a>
<a name='L1291'></a><a href='#L1291'>1291</a>
<a name='L1292'></a><a href='#L1292'>1292</a>
<a name='L1293'></a><a href='#L1293'>1293</a>
<a name='L1294'></a><a href='#L1294'>1294</a>
<a name='L1295'></a><a href='#L1295'>1295</a>
<a name='L1296'></a><a href='#L1296'>1296</a>
<a name='L1297'></a><a href='#L1297'>1297</a>
<a name='L1298'></a><a href='#L1298'>1298</a>
<a name='L1299'></a><a href='#L1299'>1299</a>
<a name='L1300'></a><a href='#L1300'>1300</a>
<a name='L1301'></a><a href='#L1301'>1301</a>
<a name='L1302'></a><a href='#L1302'>1302</a>
<a name='L1303'></a><a href='#L1303'>1303</a>
<a name='L1304'></a><a href='#L1304'>1304</a>
<a name='L1305'></a><a href='#L1305'>1305</a>
<a name='L1306'></a><a href='#L1306'>1306</a>
<a name='L1307'></a><a href='#L1307'>1307</a>
<a name='L1308'></a><a href='#L1308'>1308</a>
<a name='L1309'></a><a href='#L1309'>1309</a>
<a name='L1310'></a><a href='#L1310'>1310</a>
<a name='L1311'></a><a href='#L1311'>1311</a>
<a name='L1312'></a><a href='#L1312'>1312</a>
<a name='L1313'></a><a href='#L1313'>1313</a>
<a name='L1314'></a><a href='#L1314'>1314</a>
<a name='L1315'></a><a href='#L1315'>1315</a>
<a name='L1316'></a><a href='#L1316'>1316</a>
<a name='L1317'></a><a href='#L1317'>1317</a>
<a name='L1318'></a><a href='#L1318'>1318</a>
<a name='L1319'></a><a href='#L1319'>1319</a>
<a name='L1320'></a><a href='#L1320'>1320</a>
<a name='L1321'></a><a href='#L1321'>1321</a>
<a name='L1322'></a><a href='#L1322'>1322</a>
<a name='L1323'></a><a href='#L1323'>1323</a>
<a name='L1324'></a><a href='#L1324'>1324</a>
<a name='L1325'></a><a href='#L1325'>1325</a>
<a name='L1326'></a><a href='#L1326'>1326</a>
<a name='L1327'></a><a href='#L1327'>1327</a>
<a name='L1328'></a><a href='#L1328'>1328</a>
<a name='L1329'></a><a href='#L1329'>1329</a>
<a name='L1330'></a><a href='#L1330'>1330</a>
<a name='L1331'></a><a href='#L1331'>1331</a>
<a name='L1332'></a><a href='#L1332'>1332</a>
<a name='L1333'></a><a href='#L1333'>1333</a>
<a name='L1334'></a><a href='#L1334'>1334</a>
<a name='L1335'></a><a href='#L1335'>1335</a>
<a name='L1336'></a><a href='#L1336'>1336</a>
<a name='L1337'></a><a href='#L1337'>1337</a>
<a name='L1338'></a><a href='#L1338'>1338</a>
<a name='L1339'></a><a href='#L1339'>1339</a>
<a name='L1340'></a><a href='#L1340'>1340</a>
<a name='L1341'></a><a href='#L1341'>1341</a>
<a name='L1342'></a><a href='#L1342'>1342</a>
<a name='L1343'></a><a href='#L1343'>1343</a>
<a name='L1344'></a><a href='#L1344'>1344</a>
<a name='L1345'></a><a href='#L1345'>1345</a>
<a name='L1346'></a><a href='#L1346'>1346</a>
<a name='L1347'></a><a href='#L1347'>1347</a>
<a name='L1348'></a><a href='#L1348'>1348</a>
<a name='L1349'></a><a href='#L1349'>1349</a>
<a name='L1350'></a><a href='#L1350'>1350</a>
<a name='L1351'></a><a href='#L1351'>1351</a>
<a name='L1352'></a><a href='#L1352'>1352</a>
<a name='L1353'></a><a href='#L1353'>1353</a>
<a name='L1354'></a><a href='#L1354'>1354</a>
<a name='L1355'></a><a href='#L1355'>1355</a>
<a name='L1356'></a><a href='#L1356'>1356</a>
<a name='L1357'></a><a href='#L1357'>1357</a>
<a name='L1358'></a><a href='#L1358'>1358</a>
<a name='L1359'></a><a href='#L1359'>1359</a>
<a name='L1360'></a><a href='#L1360'>1360</a>
<a name='L1361'></a><a href='#L1361'>1361</a>
<a name='L1362'></a><a href='#L1362'>1362</a>
<a name='L1363'></a><a href='#L1363'>1363</a>
<a name='L1364'></a><a href='#L1364'>1364</a>
<a name='L1365'></a><a href='#L1365'>1365</a>
<a name='L1366'></a><a href='#L1366'>1366</a>
<a name='L1367'></a><a href='#L1367'>1367</a>
<a name='L1368'></a><a href='#L1368'>1368</a>
<a name='L1369'></a><a href='#L1369'>1369</a>
<a name='L1370'></a><a href='#L1370'>1370</a>
<a name='L1371'></a><a href='#L1371'>1371</a>
<a name='L1372'></a><a href='#L1372'>1372</a>
<a name='L1373'></a><a href='#L1373'>1373</a>
<a name='L1374'></a><a href='#L1374'>1374</a>
<a name='L1375'></a><a href='#L1375'>1375</a>
<a name='L1376'></a><a href='#L1376'>1376</a>
<a name='L1377'></a><a href='#L1377'>1377</a>
<a name='L1378'></a><a href='#L1378'>1378</a>
<a name='L1379'></a><a href='#L1379'>1379</a>
<a name='L1380'></a><a href='#L1380'>1380</a>
<a name='L1381'></a><a href='#L1381'>1381</a>
<a name='L1382'></a><a href='#L1382'>1382</a>
<a name='L1383'></a><a href='#L1383'>1383</a>
<a name='L1384'></a><a href='#L1384'>1384</a>
<a name='L1385'></a><a href='#L1385'>1385</a>
<a name='L1386'></a><a href='#L1386'>1386</a>
<a name='L1387'></a><a href='#L1387'>1387</a>
<a name='L1388'></a><a href='#L1388'>1388</a>
<a name='L1389'></a><a href='#L1389'>1389</a>
<a name='L1390'></a><a href='#L1390'>1390</a>
<a name='L1391'></a><a href='#L1391'>1391</a>
<a name='L1392'></a><a href='#L1392'>1392</a>
<a name='L1393'></a><a href='#L1393'>1393</a>
<a name='L1394'></a><a href='#L1394'>1394</a>
<a name='L1395'></a><a href='#L1395'>1395</a>
<a name='L1396'></a><a href='#L1396'>1396</a>
<a name='L1397'></a><a href='#L1397'>1397</a>
<a name='L1398'></a><a href='#L1398'>1398</a>
<a name='L1399'></a><a href='#L1399'>1399</a>
<a name='L1400'></a><a href='#L1400'>1400</a>
<a name='L1401'></a><a href='#L1401'>1401</a>
<a name='L1402'></a><a href='#L1402'>1402</a>
<a name='L1403'></a><a href='#L1403'>1403</a>
<a name='L1404'></a><a href='#L1404'>1404</a>
<a name='L1405'></a><a href='#L1405'>1405</a>
<a name='L1406'></a><a href='#L1406'>1406</a>
<a name='L1407'></a><a href='#L1407'>1407</a>
<a name='L1408'></a><a href='#L1408'>1408</a>
<a name='L1409'></a><a href='#L1409'>1409</a>
<a name='L1410'></a><a href='#L1410'>1410</a>
<a name='L1411'></a><a href='#L1411'>1411</a>
<a name='L1412'></a><a href='#L1412'>1412</a>
<a name='L1413'></a><a href='#L1413'>1413</a>
<a name='L1414'></a><a href='#L1414'>1414</a>
<a name='L1415'></a><a href='#L1415'>1415</a>
<a name='L1416'></a><a href='#L1416'>1416</a>
<a name='L1417'></a><a href='#L1417'>1417</a>
<a name='L1418'></a><a href='#L1418'>1418</a>
<a name='L1419'></a><a href='#L1419'>1419</a>
<a name='L1420'></a><a href='#L1420'>1420</a>
<a name='L1421'></a><a href='#L1421'>1421</a>
<a name='L1422'></a><a href='#L1422'>1422</a>
<a name='L1423'></a><a href='#L1423'>1423</a>
<a name='L1424'></a><a href='#L1424'>1424</a>
<a name='L1425'></a><a href='#L1425'>1425</a>
<a name='L1426'></a><a href='#L1426'>1426</a>
<a name='L1427'></a><a href='#L1427'>1427</a>
<a name='L1428'></a><a href='#L1428'>1428</a>
<a name='L1429'></a><a href='#L1429'>1429</a>
<a name='L1430'></a><a href='#L1430'>1430</a>
<a name='L1431'></a><a href='#L1431'>1431</a>
<a name='L1432'></a><a href='#L1432'>1432</a>
<a name='L1433'></a><a href='#L1433'>1433</a>
<a name='L1434'></a><a href='#L1434'>1434</a>
<a name='L1435'></a><a href='#L1435'>1435</a>
<a name='L1436'></a><a href='#L1436'>1436</a>
<a name='L1437'></a><a href='#L1437'>1437</a>
<a name='L1438'></a><a href='#L1438'>1438</a>
<a name='L1439'></a><a href='#L1439'>1439</a>
<a name='L1440'></a><a href='#L1440'>1440</a>
<a name='L1441'></a><a href='#L1441'>1441</a>
<a name='L1442'></a><a href='#L1442'>1442</a>
<a name='L1443'></a><a href='#L1443'>1443</a>
<a name='L1444'></a><a href='#L1444'>1444</a>
<a name='L1445'></a><a href='#L1445'>1445</a>
<a name='L1446'></a><a href='#L1446'>1446</a>
<a name='L1447'></a><a href='#L1447'>1447</a>
<a name='L1448'></a><a href='#L1448'>1448</a>
<a name='L1449'></a><a href='#L1449'>1449</a>
<a name='L1450'></a><a href='#L1450'>1450</a>
<a name='L1451'></a><a href='#L1451'>1451</a>
<a name='L1452'></a><a href='#L1452'>1452</a>
<a name='L1453'></a><a href='#L1453'>1453</a>
<a name='L1454'></a><a href='#L1454'>1454</a>
<a name='L1455'></a><a href='#L1455'>1455</a>
<a name='L1456'></a><a href='#L1456'>1456</a>
<a name='L1457'></a><a href='#L1457'>1457</a>
<a name='L1458'></a><a href='#L1458'>1458</a>
<a name='L1459'></a><a href='#L1459'>1459</a>
<a name='L1460'></a><a href='#L1460'>1460</a>
<a name='L1461'></a><a href='#L1461'>1461</a>
<a name='L1462'></a><a href='#L1462'>1462</a>
<a name='L1463'></a><a href='#L1463'>1463</a>
<a name='L1464'></a><a href='#L1464'>1464</a>
<a name='L1465'></a><a href='#L1465'>1465</a>
<a name='L1466'></a><a href='#L1466'>1466</a>
<a name='L1467'></a><a href='#L1467'>1467</a>
<a name='L1468'></a><a href='#L1468'>1468</a>
<a name='L1469'></a><a href='#L1469'>1469</a>
<a name='L1470'></a><a href='#L1470'>1470</a>
<a name='L1471'></a><a href='#L1471'>1471</a>
<a name='L1472'></a><a href='#L1472'>1472</a>
<a name='L1473'></a><a href='#L1473'>1473</a>
<a name='L1474'></a><a href='#L1474'>1474</a>
<a name='L1475'></a><a href='#L1475'>1475</a>
<a name='L1476'></a><a href='#L1476'>1476</a>
<a name='L1477'></a><a href='#L1477'>1477</a>
<a name='L1478'></a><a href='#L1478'>1478</a>
<a name='L1479'></a><a href='#L1479'>1479</a>
<a name='L1480'></a><a href='#L1480'>1480</a>
<a name='L1481'></a><a href='#L1481'>1481</a>
<a name='L1482'></a><a href='#L1482'>1482</a>
<a name='L1483'></a><a href='#L1483'>1483</a>
<a name='L1484'></a><a href='#L1484'>1484</a>
<a name='L1485'></a><a href='#L1485'>1485</a>
<a name='L1486'></a><a href='#L1486'>1486</a>
<a name='L1487'></a><a href='#L1487'>1487</a>
<a name='L1488'></a><a href='#L1488'>1488</a>
<a name='L1489'></a><a href='#L1489'>1489</a>
<a name='L1490'></a><a href='#L1490'>1490</a>
<a name='L1491'></a><a href='#L1491'>1491</a>
<a name='L1492'></a><a href='#L1492'>1492</a>
<a name='L1493'></a><a href='#L1493'>1493</a>
<a name='L1494'></a><a href='#L1494'>1494</a>
<a name='L1495'></a><a href='#L1495'>1495</a>
<a name='L1496'></a><a href='#L1496'>1496</a>
<a name='L1497'></a><a href='#L1497'>1497</a>
<a name='L1498'></a><a href='#L1498'>1498</a>
<a name='L1499'></a><a href='#L1499'>1499</a>
<a name='L1500'></a><a href='#L1500'>1500</a>
<a name='L1501'></a><a href='#L1501'>1501</a>
<a name='L1502'></a><a href='#L1502'>1502</a>
<a name='L1503'></a><a href='#L1503'>1503</a>
<a name='L1504'></a><a href='#L1504'>1504</a>
<a name='L1505'></a><a href='#L1505'>1505</a>
<a name='L1506'></a><a href='#L1506'>1506</a>
<a name='L1507'></a><a href='#L1507'>1507</a>
<a name='L1508'></a><a href='#L1508'>1508</a>
<a name='L1509'></a><a href='#L1509'>1509</a>
<a name='L1510'></a><a href='#L1510'>1510</a>
<a name='L1511'></a><a href='#L1511'>1511</a>
<a name='L1512'></a><a href='#L1512'>1512</a>
<a name='L1513'></a><a href='#L1513'>1513</a>
<a name='L1514'></a><a href='#L1514'>1514</a>
<a name='L1515'></a><a href='#L1515'>1515</a>
<a name='L1516'></a><a href='#L1516'>1516</a>
<a name='L1517'></a><a href='#L1517'>1517</a>
<a name='L1518'></a><a href='#L1518'>1518</a>
<a name='L1519'></a><a href='#L1519'>1519</a>
<a name='L1520'></a><a href='#L1520'>1520</a>
<a name='L1521'></a><a href='#L1521'>1521</a>
<a name='L1522'></a><a href='#L1522'>1522</a>
<a name='L1523'></a><a href='#L1523'>1523</a>
<a name='L1524'></a><a href='#L1524'>1524</a>
<a name='L1525'></a><a href='#L1525'>1525</a>
<a name='L1526'></a><a href='#L1526'>1526</a>
<a name='L1527'></a><a href='#L1527'>1527</a>
<a name='L1528'></a><a href='#L1528'>1528</a>
<a name='L1529'></a><a href='#L1529'>1529</a>
<a name='L1530'></a><a href='#L1530'>1530</a>
<a name='L1531'></a><a href='#L1531'>1531</a>
<a name='L1532'></a><a href='#L1532'>1532</a>
<a name='L1533'></a><a href='#L1533'>1533</a>
<a name='L1534'></a><a href='#L1534'>1534</a>
<a name='L1535'></a><a href='#L1535'>1535</a>
<a name='L1536'></a><a href='#L1536'>1536</a>
<a name='L1537'></a><a href='#L1537'>1537</a>
<a name='L1538'></a><a href='#L1538'>1538</a>
<a name='L1539'></a><a href='#L1539'>1539</a>
<a name='L1540'></a><a href='#L1540'>1540</a>
<a name='L1541'></a><a href='#L1541'>1541</a>
<a name='L1542'></a><a href='#L1542'>1542</a>
<a name='L1543'></a><a href='#L1543'>1543</a>
<a name='L1544'></a><a href='#L1544'>1544</a>
<a name='L1545'></a><a href='#L1545'>1545</a>
<a name='L1546'></a><a href='#L1546'>1546</a>
<a name='L1547'></a><a href='#L1547'>1547</a>
<a name='L1548'></a><a href='#L1548'>1548</a>
<a name='L1549'></a><a href='#L1549'>1549</a>
<a name='L1550'></a><a href='#L1550'>1550</a>
<a name='L1551'></a><a href='#L1551'>1551</a>
<a name='L1552'></a><a href='#L1552'>1552</a>
<a name='L1553'></a><a href='#L1553'>1553</a>
<a name='L1554'></a><a href='#L1554'>1554</a>
<a name='L1555'></a><a href='#L1555'>1555</a>
<a name='L1556'></a><a href='#L1556'>1556</a>
<a name='L1557'></a><a href='#L1557'>1557</a>
<a name='L1558'></a><a href='#L1558'>1558</a>
<a name='L1559'></a><a href='#L1559'>1559</a>
<a name='L1560'></a><a href='#L1560'>1560</a>
<a name='L1561'></a><a href='#L1561'>1561</a>
<a name='L1562'></a><a href='#L1562'>1562</a>
<a name='L1563'></a><a href='#L1563'>1563</a>
<a name='L1564'></a><a href='#L1564'>1564</a>
<a name='L1565'></a><a href='#L1565'>1565</a>
<a name='L1566'></a><a href='#L1566'>1566</a>
<a name='L1567'></a><a href='#L1567'>1567</a>
<a name='L1568'></a><a href='#L1568'>1568</a>
<a name='L1569'></a><a href='#L1569'>1569</a>
<a name='L1570'></a><a href='#L1570'>1570</a>
<a name='L1571'></a><a href='#L1571'>1571</a>
<a name='L1572'></a><a href='#L1572'>1572</a>
<a name='L1573'></a><a href='#L1573'>1573</a>
<a name='L1574'></a><a href='#L1574'>1574</a>
<a name='L1575'></a><a href='#L1575'>1575</a>
<a name='L1576'></a><a href='#L1576'>1576</a>
<a name='L1577'></a><a href='#L1577'>1577</a>
<a name='L1578'></a><a href='#L1578'>1578</a>
<a name='L1579'></a><a href='#L1579'>1579</a>
<a name='L1580'></a><a href='#L1580'>1580</a>
<a name='L1581'></a><a href='#L1581'>1581</a>
<a name='L1582'></a><a href='#L1582'>1582</a>
<a name='L1583'></a><a href='#L1583'>1583</a>
<a name='L1584'></a><a href='#L1584'>1584</a>
<a name='L1585'></a><a href='#L1585'>1585</a>
<a name='L1586'></a><a href='#L1586'>1586</a>
<a name='L1587'></a><a href='#L1587'>1587</a>
<a name='L1588'></a><a href='#L1588'>1588</a>
<a name='L1589'></a><a href='#L1589'>1589</a>
<a name='L1590'></a><a href='#L1590'>1590</a>
<a name='L1591'></a><a href='#L1591'>1591</a>
<a name='L1592'></a><a href='#L1592'>1592</a>
<a name='L1593'></a><a href='#L1593'>1593</a>
<a name='L1594'></a><a href='#L1594'>1594</a>
<a name='L1595'></a><a href='#L1595'>1595</a>
<a name='L1596'></a><a href='#L1596'>1596</a>
<a name='L1597'></a><a href='#L1597'>1597</a>
<a name='L1598'></a><a href='#L1598'>1598</a>
<a name='L1599'></a><a href='#L1599'>1599</a>
<a name='L1600'></a><a href='#L1600'>1600</a>
<a name='L1601'></a><a href='#L1601'>1601</a>
<a name='L1602'></a><a href='#L1602'>1602</a>
<a name='L1603'></a><a href='#L1603'>1603</a>
<a name='L1604'></a><a href='#L1604'>1604</a>
<a name='L1605'></a><a href='#L1605'>1605</a>
<a name='L1606'></a><a href='#L1606'>1606</a>
<a name='L1607'></a><a href='#L1607'>1607</a>
<a name='L1608'></a><a href='#L1608'>1608</a>
<a name='L1609'></a><a href='#L1609'>1609</a>
<a name='L1610'></a><a href='#L1610'>1610</a>
<a name='L1611'></a><a href='#L1611'>1611</a>
<a name='L1612'></a><a href='#L1612'>1612</a>
<a name='L1613'></a><a href='#L1613'>1613</a>
<a name='L1614'></a><a href='#L1614'>1614</a>
<a name='L1615'></a><a href='#L1615'>1615</a>
<a name='L1616'></a><a href='#L1616'>1616</a>
<a name='L1617'></a><a href='#L1617'>1617</a>
<a name='L1618'></a><a href='#L1618'>1618</a>
<a name='L1619'></a><a href='#L1619'>1619</a>
<a name='L1620'></a><a href='#L1620'>1620</a>
<a name='L1621'></a><a href='#L1621'>1621</a>
<a name='L1622'></a><a href='#L1622'>1622</a>
<a name='L1623'></a><a href='#L1623'>1623</a>
<a name='L1624'></a><a href='#L1624'>1624</a>
<a name='L1625'></a><a href='#L1625'>1625</a>
<a name='L1626'></a><a href='#L1626'>1626</a>
<a name='L1627'></a><a href='#L1627'>1627</a>
<a name='L1628'></a><a href='#L1628'>1628</a>
<a name='L1629'></a><a href='#L1629'>1629</a>
<a name='L1630'></a><a href='#L1630'>1630</a>
<a name='L1631'></a><a href='#L1631'>1631</a>
<a name='L1632'></a><a href='#L1632'>1632</a>
<a name='L1633'></a><a href='#L1633'>1633</a>
<a name='L1634'></a><a href='#L1634'>1634</a>
<a name='L1635'></a><a href='#L1635'>1635</a>
<a name='L1636'></a><a href='#L1636'>1636</a>
<a name='L1637'></a><a href='#L1637'>1637</a>
<a name='L1638'></a><a href='#L1638'>1638</a>
<a name='L1639'></a><a href='#L1639'>1639</a>
<a name='L1640'></a><a href='#L1640'>1640</a>
<a name='L1641'></a><a href='#L1641'>1641</a>
<a name='L1642'></a><a href='#L1642'>1642</a>
<a name='L1643'></a><a href='#L1643'>1643</a>
<a name='L1644'></a><a href='#L1644'>1644</a>
<a name='L1645'></a><a href='#L1645'>1645</a>
<a name='L1646'></a><a href='#L1646'>1646</a>
<a name='L1647'></a><a href='#L1647'>1647</a>
<a name='L1648'></a><a href='#L1648'>1648</a>
<a name='L1649'></a><a href='#L1649'>1649</a>
<a name='L1650'></a><a href='#L1650'>1650</a>
<a name='L1651'></a><a href='#L1651'>1651</a>
<a name='L1652'></a><a href='#L1652'>1652</a>
<a name='L1653'></a><a href='#L1653'>1653</a>
<a name='L1654'></a><a href='#L1654'>1654</a>
<a name='L1655'></a><a href='#L1655'>1655</a>
<a name='L1656'></a><a href='#L1656'>1656</a>
<a name='L1657'></a><a href='#L1657'>1657</a>
<a name='L1658'></a><a href='#L1658'>1658</a>
<a name='L1659'></a><a href='#L1659'>1659</a>
<a name='L1660'></a><a href='#L1660'>1660</a>
<a name='L1661'></a><a href='#L1661'>1661</a>
<a name='L1662'></a><a href='#L1662'>1662</a>
<a name='L1663'></a><a href='#L1663'>1663</a>
<a name='L1664'></a><a href='#L1664'>1664</a>
<a name='L1665'></a><a href='#L1665'>1665</a>
<a name='L1666'></a><a href='#L1666'>1666</a>
<a name='L1667'></a><a href='#L1667'>1667</a>
<a name='L1668'></a><a href='#L1668'>1668</a>
<a name='L1669'></a><a href='#L1669'>1669</a>
<a name='L1670'></a><a href='#L1670'>1670</a>
<a name='L1671'></a><a href='#L1671'>1671</a>
<a name='L1672'></a><a href='#L1672'>1672</a>
<a name='L1673'></a><a href='#L1673'>1673</a>
<a name='L1674'></a><a href='#L1674'>1674</a>
<a name='L1675'></a><a href='#L1675'>1675</a>
<a name='L1676'></a><a href='#L1676'>1676</a>
<a name='L1677'></a><a href='#L1677'>1677</a>
<a name='L1678'></a><a href='#L1678'>1678</a>
<a name='L1679'></a><a href='#L1679'>1679</a>
<a name='L1680'></a><a href='#L1680'>1680</a>
<a name='L1681'></a><a href='#L1681'>1681</a>
<a name='L1682'></a><a href='#L1682'>1682</a>
<a name='L1683'></a><a href='#L1683'>1683</a>
<a name='L1684'></a><a href='#L1684'>1684</a>
<a name='L1685'></a><a href='#L1685'>1685</a>
<a name='L1686'></a><a href='#L1686'>1686</a>
<a name='L1687'></a><a href='#L1687'>1687</a>
<a name='L1688'></a><a href='#L1688'>1688</a>
<a name='L1689'></a><a href='#L1689'>1689</a>
<a name='L1690'></a><a href='#L1690'>1690</a>
<a name='L1691'></a><a href='#L1691'>1691</a>
<a name='L1692'></a><a href='#L1692'>1692</a>
<a name='L1693'></a><a href='#L1693'>1693</a>
<a name='L1694'></a><a href='#L1694'>1694</a>
<a name='L1695'></a><a href='#L1695'>1695</a>
<a name='L1696'></a><a href='#L1696'>1696</a>
<a name='L1697'></a><a href='#L1697'>1697</a>
<a name='L1698'></a><a href='#L1698'>1698</a>
<a name='L1699'></a><a href='#L1699'>1699</a>
<a name='L1700'></a><a href='#L1700'>1700</a>
<a name='L1701'></a><a href='#L1701'>1701</a>
<a name='L1702'></a><a href='#L1702'>1702</a>
<a name='L1703'></a><a href='#L1703'>1703</a>
<a name='L1704'></a><a href='#L1704'>1704</a>
<a name='L1705'></a><a href='#L1705'>1705</a>
<a name='L1706'></a><a href='#L1706'>1706</a>
<a name='L1707'></a><a href='#L1707'>1707</a>
<a name='L1708'></a><a href='#L1708'>1708</a>
<a name='L1709'></a><a href='#L1709'>1709</a>
<a name='L1710'></a><a href='#L1710'>1710</a>
<a name='L1711'></a><a href='#L1711'>1711</a>
<a name='L1712'></a><a href='#L1712'>1712</a>
<a name='L1713'></a><a href='#L1713'>1713</a>
<a name='L1714'></a><a href='#L1714'>1714</a>
<a name='L1715'></a><a href='#L1715'>1715</a>
<a name='L1716'></a><a href='#L1716'>1716</a>
<a name='L1717'></a><a href='#L1717'>1717</a>
<a name='L1718'></a><a href='#L1718'>1718</a>
<a name='L1719'></a><a href='#L1719'>1719</a>
<a name='L1720'></a><a href='#L1720'>1720</a>
<a name='L1721'></a><a href='#L1721'>1721</a>
<a name='L1722'></a><a href='#L1722'>1722</a>
<a name='L1723'></a><a href='#L1723'>1723</a>
<a name='L1724'></a><a href='#L1724'>1724</a>
<a name='L1725'></a><a href='#L1725'>1725</a>
<a name='L1726'></a><a href='#L1726'>1726</a>
<a name='L1727'></a><a href='#L1727'>1727</a>
<a name='L1728'></a><a href='#L1728'>1728</a>
<a name='L1729'></a><a href='#L1729'>1729</a>
<a name='L1730'></a><a href='#L1730'>1730</a>
<a name='L1731'></a><a href='#L1731'>1731</a>
<a name='L1732'></a><a href='#L1732'>1732</a>
<a name='L1733'></a><a href='#L1733'>1733</a>
<a name='L1734'></a><a href='#L1734'>1734</a>
<a name='L1735'></a><a href='#L1735'>1735</a>
<a name='L1736'></a><a href='#L1736'>1736</a>
<a name='L1737'></a><a href='#L1737'>1737</a>
<a name='L1738'></a><a href='#L1738'>1738</a>
<a name='L1739'></a><a href='#L1739'>1739</a>
<a name='L1740'></a><a href='#L1740'>1740</a>
<a name='L1741'></a><a href='#L1741'>1741</a>
<a name='L1742'></a><a href='#L1742'>1742</a>
<a name='L1743'></a><a href='#L1743'>1743</a>
<a name='L1744'></a><a href='#L1744'>1744</a>
<a name='L1745'></a><a href='#L1745'>1745</a>
<a name='L1746'></a><a href='#L1746'>1746</a>
<a name='L1747'></a><a href='#L1747'>1747</a>
<a name='L1748'></a><a href='#L1748'>1748</a>
<a name='L1749'></a><a href='#L1749'>1749</a>
<a name='L1750'></a><a href='#L1750'>1750</a>
<a name='L1751'></a><a href='#L1751'>1751</a>
<a name='L1752'></a><a href='#L1752'>1752</a>
<a name='L1753'></a><a href='#L1753'>1753</a>
<a name='L1754'></a><a href='#L1754'>1754</a>
<a name='L1755'></a><a href='#L1755'>1755</a>
<a name='L1756'></a><a href='#L1756'>1756</a>
<a name='L1757'></a><a href='#L1757'>1757</a>
<a name='L1758'></a><a href='#L1758'>1758</a>
<a name='L1759'></a><a href='#L1759'>1759</a>
<a name='L1760'></a><a href='#L1760'>1760</a>
<a name='L1761'></a><a href='#L1761'>1761</a>
<a name='L1762'></a><a href='#L1762'>1762</a>
<a name='L1763'></a><a href='#L1763'>1763</a>
<a name='L1764'></a><a href='#L1764'>1764</a>
<a name='L1765'></a><a href='#L1765'>1765</a>
<a name='L1766'></a><a href='#L1766'>1766</a>
<a name='L1767'></a><a href='#L1767'>1767</a>
<a name='L1768'></a><a href='#L1768'>1768</a>
<a name='L1769'></a><a href='#L1769'>1769</a>
<a name='L1770'></a><a href='#L1770'>1770</a>
<a name='L1771'></a><a href='#L1771'>1771</a>
<a name='L1772'></a><a href='#L1772'>1772</a>
<a name='L1773'></a><a href='#L1773'>1773</a>
<a name='L1774'></a><a href='#L1774'>1774</a>
<a name='L1775'></a><a href='#L1775'>1775</a>
<a name='L1776'></a><a href='#L1776'>1776</a>
<a name='L1777'></a><a href='#L1777'>1777</a>
<a name='L1778'></a><a href='#L1778'>1778</a>
<a name='L1779'></a><a href='#L1779'>1779</a>
<a name='L1780'></a><a href='#L1780'>1780</a>
<a name='L1781'></a><a href='#L1781'>1781</a>
<a name='L1782'></a><a href='#L1782'>1782</a>
<a name='L1783'></a><a href='#L1783'>1783</a>
<a name='L1784'></a><a href='#L1784'>1784</a>
<a name='L1785'></a><a href='#L1785'>1785</a>
<a name='L1786'></a><a href='#L1786'>1786</a>
<a name='L1787'></a><a href='#L1787'>1787</a>
<a name='L1788'></a><a href='#L1788'>1788</a>
<a name='L1789'></a><a href='#L1789'>1789</a>
<a name='L1790'></a><a href='#L1790'>1790</a>
<a name='L1791'></a><a href='#L1791'>1791</a>
<a name='L1792'></a><a href='#L1792'>1792</a>
<a name='L1793'></a><a href='#L1793'>1793</a>
<a name='L1794'></a><a href='#L1794'>1794</a>
<a name='L1795'></a><a href='#L1795'>1795</a>
<a name='L1796'></a><a href='#L1796'>1796</a>
<a name='L1797'></a><a href='#L1797'>1797</a>
<a name='L1798'></a><a href='#L1798'>1798</a>
<a name='L1799'></a><a href='#L1799'>1799</a>
<a name='L1800'></a><a href='#L1800'>1800</a>
<a name='L1801'></a><a href='#L1801'>1801</a>
<a name='L1802'></a><a href='#L1802'>1802</a>
<a name='L1803'></a><a href='#L1803'>1803</a>
<a name='L1804'></a><a href='#L1804'>1804</a>
<a name='L1805'></a><a href='#L1805'>1805</a>
<a name='L1806'></a><a href='#L1806'>1806</a>
<a name='L1807'></a><a href='#L1807'>1807</a>
<a name='L1808'></a><a href='#L1808'>1808</a>
<a name='L1809'></a><a href='#L1809'>1809</a>
<a name='L1810'></a><a href='#L1810'>1810</a>
<a name='L1811'></a><a href='#L1811'>1811</a>
<a name='L1812'></a><a href='#L1812'>1812</a>
<a name='L1813'></a><a href='#L1813'>1813</a>
<a name='L1814'></a><a href='#L1814'>1814</a>
<a name='L1815'></a><a href='#L1815'>1815</a>
<a name='L1816'></a><a href='#L1816'>1816</a>
<a name='L1817'></a><a href='#L1817'>1817</a>
<a name='L1818'></a><a href='#L1818'>1818</a>
<a name='L1819'></a><a href='#L1819'>1819</a>
<a name='L1820'></a><a href='#L1820'>1820</a>
<a name='L1821'></a><a href='#L1821'>1821</a>
<a name='L1822'></a><a href='#L1822'>1822</a>
<a name='L1823'></a><a href='#L1823'>1823</a>
<a name='L1824'></a><a href='#L1824'>1824</a>
<a name='L1825'></a><a href='#L1825'>1825</a>
<a name='L1826'></a><a href='#L1826'>1826</a>
<a name='L1827'></a><a href='#L1827'>1827</a>
<a name='L1828'></a><a href='#L1828'>1828</a>
<a name='L1829'></a><a href='#L1829'>1829</a>
<a name='L1830'></a><a href='#L1830'>1830</a>
<a name='L1831'></a><a href='#L1831'>1831</a>
<a name='L1832'></a><a href='#L1832'>1832</a>
<a name='L1833'></a><a href='#L1833'>1833</a>
<a name='L1834'></a><a href='#L1834'>1834</a>
<a name='L1835'></a><a href='#L1835'>1835</a>
<a name='L1836'></a><a href='#L1836'>1836</a>
<a name='L1837'></a><a href='#L1837'>1837</a>
<a name='L1838'></a><a href='#L1838'>1838</a>
<a name='L1839'></a><a href='#L1839'>1839</a>
<a name='L1840'></a><a href='#L1840'>1840</a>
<a name='L1841'></a><a href='#L1841'>1841</a>
<a name='L1842'></a><a href='#L1842'>1842</a>
<a name='L1843'></a><a href='#L1843'>1843</a>
<a name='L1844'></a><a href='#L1844'>1844</a>
<a name='L1845'></a><a href='#L1845'>1845</a>
<a name='L1846'></a><a href='#L1846'>1846</a>
<a name='L1847'></a><a href='#L1847'>1847</a>
<a name='L1848'></a><a href='#L1848'>1848</a>
<a name='L1849'></a><a href='#L1849'>1849</a>
<a name='L1850'></a><a href='#L1850'>1850</a>
<a name='L1851'></a><a href='#L1851'>1851</a>
<a name='L1852'></a><a href='#L1852'>1852</a>
<a name='L1853'></a><a href='#L1853'>1853</a>
<a name='L1854'></a><a href='#L1854'>1854</a>
<a name='L1855'></a><a href='#L1855'>1855</a>
<a name='L1856'></a><a href='#L1856'>1856</a>
<a name='L1857'></a><a href='#L1857'>1857</a>
<a name='L1858'></a><a href='#L1858'>1858</a>
<a name='L1859'></a><a href='#L1859'>1859</a>
<a name='L1860'></a><a href='#L1860'>1860</a>
<a name='L1861'></a><a href='#L1861'>1861</a>
<a name='L1862'></a><a href='#L1862'>1862</a>
<a name='L1863'></a><a href='#L1863'>1863</a>
<a name='L1864'></a><a href='#L1864'>1864</a>
<a name='L1865'></a><a href='#L1865'>1865</a>
<a name='L1866'></a><a href='#L1866'>1866</a>
<a name='L1867'></a><a href='#L1867'>1867</a>
<a name='L1868'></a><a href='#L1868'>1868</a>
<a name='L1869'></a><a href='#L1869'>1869</a>
<a name='L1870'></a><a href='#L1870'>1870</a>
<a name='L1871'></a><a href='#L1871'>1871</a>
<a name='L1872'></a><a href='#L1872'>1872</a>
<a name='L1873'></a><a href='#L1873'>1873</a>
<a name='L1874'></a><a href='#L1874'>1874</a>
<a name='L1875'></a><a href='#L1875'>1875</a>
<a name='L1876'></a><a href='#L1876'>1876</a>
<a name='L1877'></a><a href='#L1877'>1877</a>
<a name='L1878'></a><a href='#L1878'>1878</a>
<a name='L1879'></a><a href='#L1879'>1879</a>
<a name='L1880'></a><a href='#L1880'>1880</a>
<a name='L1881'></a><a href='#L1881'>1881</a>
<a name='L1882'></a><a href='#L1882'>1882</a>
<a name='L1883'></a><a href='#L1883'>1883</a>
<a name='L1884'></a><a href='#L1884'>1884</a>
<a name='L1885'></a><a href='#L1885'>1885</a>
<a name='L1886'></a><a href='#L1886'>1886</a>
<a name='L1887'></a><a href='#L1887'>1887</a>
<a name='L1888'></a><a href='#L1888'>1888</a>
<a name='L1889'></a><a href='#L1889'>1889</a>
<a name='L1890'></a><a href='#L1890'>1890</a>
<a name='L1891'></a><a href='#L1891'>1891</a>
<a name='L1892'></a><a href='#L1892'>1892</a>
<a name='L1893'></a><a href='#L1893'>1893</a>
<a name='L1894'></a><a href='#L1894'>1894</a>
<a name='L1895'></a><a href='#L1895'>1895</a>
<a name='L1896'></a><a href='#L1896'>1896</a>
<a name='L1897'></a><a href='#L1897'>1897</a>
<a name='L1898'></a><a href='#L1898'>1898</a>
<a name='L1899'></a><a href='#L1899'>1899</a>
<a name='L1900'></a><a href='#L1900'>1900</a>
<a name='L1901'></a><a href='#L1901'>1901</a>
<a name='L1902'></a><a href='#L1902'>1902</a>
<a name='L1903'></a><a href='#L1903'>1903</a>
<a name='L1904'></a><a href='#L1904'>1904</a>
<a name='L1905'></a><a href='#L1905'>1905</a>
<a name='L1906'></a><a href='#L1906'>1906</a>
<a name='L1907'></a><a href='#L1907'>1907</a>
<a name='L1908'></a><a href='#L1908'>1908</a>
<a name='L1909'></a><a href='#L1909'>1909</a>
<a name='L1910'></a><a href='#L1910'>1910</a>
<a name='L1911'></a><a href='#L1911'>1911</a>
<a name='L1912'></a><a href='#L1912'>1912</a>
<a name='L1913'></a><a href='#L1913'>1913</a>
<a name='L1914'></a><a href='#L1914'>1914</a>
<a name='L1915'></a><a href='#L1915'>1915</a>
<a name='L1916'></a><a href='#L1916'>1916</a>
<a name='L1917'></a><a href='#L1917'>1917</a>
<a name='L1918'></a><a href='#L1918'>1918</a>
<a name='L1919'></a><a href='#L1919'>1919</a>
<a name='L1920'></a><a href='#L1920'>1920</a>
<a name='L1921'></a><a href='#L1921'>1921</a>
<a name='L1922'></a><a href='#L1922'>1922</a>
<a name='L1923'></a><a href='#L1923'>1923</a>
<a name='L1924'></a><a href='#L1924'>1924</a>
<a name='L1925'></a><a href='#L1925'>1925</a>
<a name='L1926'></a><a href='#L1926'>1926</a>
<a name='L1927'></a><a href='#L1927'>1927</a>
<a name='L1928'></a><a href='#L1928'>1928</a>
<a name='L1929'></a><a href='#L1929'>1929</a>
<a name='L1930'></a><a href='#L1930'>1930</a>
<a name='L1931'></a><a href='#L1931'>1931</a>
<a name='L1932'></a><a href='#L1932'>1932</a>
<a name='L1933'></a><a href='#L1933'>1933</a>
<a name='L1934'></a><a href='#L1934'>1934</a>
<a name='L1935'></a><a href='#L1935'>1935</a>
<a name='L1936'></a><a href='#L1936'>1936</a>
<a name='L1937'></a><a href='#L1937'>1937</a>
<a name='L1938'></a><a href='#L1938'>1938</a>
<a name='L1939'></a><a href='#L1939'>1939</a>
<a name='L1940'></a><a href='#L1940'>1940</a>
<a name='L1941'></a><a href='#L1941'>1941</a>
<a name='L1942'></a><a href='#L1942'>1942</a>
<a name='L1943'></a><a href='#L1943'>1943</a>
<a name='L1944'></a><a href='#L1944'>1944</a>
<a name='L1945'></a><a href='#L1945'>1945</a>
<a name='L1946'></a><a href='#L1946'>1946</a>
<a name='L1947'></a><a href='#L1947'>1947</a>
<a name='L1948'></a><a href='#L1948'>1948</a>
<a name='L1949'></a><a href='#L1949'>1949</a>
<a name='L1950'></a><a href='#L1950'>1950</a>
<a name='L1951'></a><a href='#L1951'>1951</a>
<a name='L1952'></a><a href='#L1952'>1952</a>
<a name='L1953'></a><a href='#L1953'>1953</a>
<a name='L1954'></a><a href='#L1954'>1954</a>
<a name='L1955'></a><a href='#L1955'>1955</a>
<a name='L1956'></a><a href='#L1956'>1956</a>
<a name='L1957'></a><a href='#L1957'>1957</a>
<a name='L1958'></a><a href='#L1958'>1958</a>
<a name='L1959'></a><a href='#L1959'>1959</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import type { CoreConfig } from '../config/index'
import type { ValidationIssue } from '../lib/utils/validationUtils'
import type { ElementCreateEvent, ElementCreateEventPayload } from '../services/elements/element-actions/elementCreationService'
import type { ErrorService } from '../services/system/error-service/errorService'
import type { ElementType, ShapeElement as ShapeModel } from '../types/core/elementDefinitions'
import type { ValidatableShape as ValidatorShape } from '../types/core/validator/validator-interface'
import type {
  BaseEvent,
  EventBus,
  ShapeCreateEvent,
  ShapeDeleteEvent,
  ShapeEditEvent,
} from '../types/services/events'
import type { AppEventMap } from '../types/services/events/eventRegistry'
import type { LoggerService } from '../types/services/logging'
import type {
  ElementEditService as IShapeEditService,
} from '../types/services/shapes/shapeService'
import type { ComputeFacade } from './compute/ComputeFacade'
import type { ElementFactory } from './factory/ElementFactory'
import type { ShapeRepository } from './state/ShapeRepository'
import type { ElementValidator } from './validator/ElementValidator'
import type { PatternDefinition } from '@/types/core/element/elementPatternTypes'
&nbsp;
<span class="cstat-no" title="statement not covered" >import { cleanPattern } from '@/lib/utils/element/cleanPattern'</span>
<span class="cstat-no" title="statement not covered" >import { useShapesStore } from '@/store/shapesStore'</span>
<span class="cstat-no" title="statement not covered" >import { createConfig, defaultCoreConfig } from '../config/index'</span>
<span class="cstat-no" title="statement not covered" >import { safeValidate } from '../lib/utils/validationUtils'</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >import { ElementCreationService } from '../services/elements/element-actions/elementCreationService'</span>
<span class="cstat-no" title="statement not covered" >import { ElementDeleteService } from '../services/elements/element-actions/elementDeleteService'</span>
<span class="cstat-no" title="statement not covered" >import { ElementEditServiceImpl } from '../services/elements/element-actions/elementEditService'</span>
<span class="cstat-no" title="statement not covered" >import { ElementSelectionService } from '../services/elements/element-actions/elementSelectionService'</span>
<span class="cstat-no" title="statement not covered" >import { CoreError } from '../services/system/error-service/coreError'</span>
<span class="cstat-no" title="statement not covered" >import { ErrorType } from '../types/services/errors'</span>
<span class="cstat-no" title="statement not covered" >import { AppEventType } from '../types/services/events'</span>
&nbsp;
/**
 * Core Coordinator for Application Logic
 *
 * Acts as the central hub for orchestrating core application logic.
 *
 * @remarks
 * The `CoreCoordinator`'s primary responsibilities include:
 * - Initializing and managing the core application configuration.
 * - Instantiating and providing access to core services such as the `ShapeRepository`,
 *   `ElementFactory`, and various shape action services (creation, editing, deletion, selection).
 * - Registering event handlers on the application's `EventBus` to delegate shape manipulation
 *   requests (e.g., `SHAPE_CREATE_REQUEST`, `SHAPE_EDIT_REQUEST`) to the appropriate action services.
 * - Coordinating data synchronization between the internal `ShapeRepository` and any external
 *   state management solutions or data stores.
 * - Providing centralized error handling mechanisms for core operations and publishing
 *   standardized error events.
 *
 * It typically relies on dependency injection for essential components like the
 * `ElementFactory` and `ShapeRepository`, while action services might be instantiated
 * internally or also injected, depending on the application's architecture.
 *
 * @module core/CoreCoordinator
 */
<span class="cstat-no" title="statement not covered" >export class CoreCoordinator {</span>
  /**
   * Flag to prevent feedback loop during store synchronization.
   *
   * @remarks
   * Set to true before publishing internal updates that will trigger store changes,
   * checked within store update handlers to ignore self-induced updates.
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private isInternalUpdate: boolean = false</span>
&nbsp;
  /**
   * Core configuration settings.
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private config: CoreConfig</span>
&nbsp;
  /**
   * Reference to the injected ShapeRepository instance.
   * @private
   * @readonly
   */
<span class="cstat-no" title="statement not covered" >  private readonly repository: ShapeRepository</span>
&nbsp;
  /**
   * Reference to the injected ElementFactory instance.
   * @private
   * @readonly
   */
<span class="cstat-no" title="statement not covered" >  private readonly factory: ElementFactory</span>
&nbsp;
  /**
   * Central application event bus.
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private eventBus: EventBus&lt;AppEventMap&gt;</span>
&nbsp;
  /**
   * Validator for element data.
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private validator: ElementValidator</span>
&nbsp;
  /**
   * Logger instance.
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private logger: LoggerService</span>
&nbsp;
  /**
   * Error service instance.
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private errorService: ErrorService</span>
&nbsp;
  /**
   * Service for handling element creation operations.
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private elementCreationService: ElementCreationService</span>
&nbsp;
  /**
   * Service for handling element editing operations.
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private elementEditService: IShapeEditService</span>
&nbsp;
  /**
   * Service for handling element deletion operations.
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private elementDeleteService: ElementDeleteService</span>
&nbsp;
  /**
   * Service for handling element selection operations.
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private elementSelectionService: ElementSelectionService</span>
&nbsp;
  /**
   * Undo/Redo history stacks
   */
<span class="cstat-no" title="statement not covered" >  private past: ShapeModel[][] = [[]]</span>
<span class="cstat-no" title="statement not covered" >  private future: ShapeModel[][] = []</span>
&nbsp;
  /**
   * Optional compute facade dependency
   */
<span class="cstat-no" title="statement not covered" >  private computeFacade?: ComputeFacade</span>
&nbsp;
  /**
   * Creates an instance of CoreCoordinator.
   *
   * @param eventBus - The central application event bus instance, conforming to the {@link EventBus} interface.
   * @param repository - An instance of {@link ShapeRepository} for managing the state of core shape elements.
   * @param validator - An instance of {@link ElementValidator} for validating element data.
   * @param factory - An instance of {@link ElementFactory} used for creating new shape elements.
   * @param logger - A logger service instance, conforming to the {@link LoggerService} interface.
   * @param errorService - An instance of {@link ErrorService} for centralized error management.
   * @param initialConfig - Optional partial configuration ({@link CoreConfig}) to override default settings.
   * @param computeFacade - Optional compute facade dependency
   */
<span class="cstat-no" title="statement not covered" >  constructor(</span>
    // Required dependencies first
<span class="cstat-no" title="statement not covered" >    eventBus: EventBus&lt;AppEventMap&gt;,</span>
<span class="cstat-no" title="statement not covered" >    repository: ShapeRepository,</span>
<span class="cstat-no" title="statement not covered" >    validator: ElementValidator,</span>
<span class="cstat-no" title="statement not covered" >    factory: ElementFactory,</span>
<span class="cstat-no" title="statement not covered" >    logger: LoggerService, // Add logger parameter</span>
<span class="cstat-no" title="statement not covered" >    errorService: ErrorService,</span>
    // Optional config last
<span class="cstat-no" title="statement not covered" >    initialConfig?: Partial&lt;CoreConfig&gt;,</span>
<span class="cstat-no" title="statement not covered" >    computeFacade?: ComputeFacade, // 新增参数</span>
<span class="cstat-no" title="statement not covered" >  ) {</span>
<span class="cstat-no" title="statement not covered" >    console.warn('[CoreCoordinator] constructor called')</span>
    // Assign dependencies
<span class="cstat-no" title="statement not covered" >    this.eventBus = eventBus</span>
    // Note: The 'repository' parameter is already an instance of ShapeRepository.
    // If it was being created here, we'd pass eventBus.
    // However, ShapeRepository is passed *into* CoreCoordinator's constructor.
    // The place where CoreCoordinator is instantiated (likely App.tsx) is where
    // ShapeRepository needs to be created with the eventBus.
    // We've already handled that in App.tsx.
    // So, no change needed for 'this.repository = repository;' line itself.
    // The key is that the 'repository' instance passed to CoreCoordinator
    // must have been created with the eventBus.
<span class="cstat-no" title="statement not covered" >    this.repository = repository</span>
<span class="cstat-no" title="statement not covered" >    this.validator = validator</span>
<span class="cstat-no" title="statement not covered" >    this.factory = factory</span>
<span class="cstat-no" title="statement not covered" >    this.logger = logger // Assign logger</span>
<span class="cstat-no" title="statement not covered" >    this.errorService = errorService</span>
<span class="cstat-no" title="statement not covered" >    this.computeFacade = computeFacade</span>
&nbsp;
    // Initialize config
<span class="cstat-no" title="statement not covered" >    this.config = createConfig(initialConfig || defaultCoreConfig)</span>
&nbsp;
    // Initialize all services with direct instantiation for better type safety
<span class="cstat-no" title="statement not covered" >    this.elementCreationService = ElementCreationService.create(this.factory, this.logger)</span>
<span class="cstat-no" title="statement not covered" >    this.elementEditService = new ElementEditServiceImpl(this.eventBus, this.logger)</span>
<span class="cstat-no" title="statement not covered" >    this.elementDeleteService = new ElementDeleteService(this.eventBus, this.logger)</span>
<span class="cstat-no" title="statement not covered" >    this.elementSelectionService = new ElementSelectionService(this.eventBus, this.logger)</span>
&nbsp;
    // Register event handlers to delegate requests to services
<span class="cstat-no" title="statement not covered" >    this.registerEventHandlers()</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.logger.info('CoreCoordinator initialized and event handlers registered.')</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * 判断是否应该触发几何计算
   * 只有在几何相关属性变更时才触发，避免成本属性变更时的不必要计算
   */
<span class="cstat-no" title="statement not covered" >  private shouldTriggerGeometryCalculation(changes: Record&lt;string, unknown&gt;): boolean {</span>
    // 几何相关的属性列表
<span class="cstat-no" title="statement not covered" >    const geometryProperties = [</span>
<span class="cstat-no" title="statement not covered" >      'width',</span>
<span class="cstat-no" title="statement not covered" >      'height',</span>
<span class="cstat-no" title="statement not covered" >      'radius',</span>
<span class="cstat-no" title="statement not covered" >      'radiusX',</span>
<span class="cstat-no" title="statement not covered" >      'radiusY',</span>
<span class="cstat-no" title="statement not covered" >      'x',</span>
<span class="cstat-no" title="statement not covered" >      'y',</span>
<span class="cstat-no" title="statement not covered" >      'position',</span>
<span class="cstat-no" title="statement not covered" >      'start',</span>
<span class="cstat-no" title="statement not covered" >      'end',</span>
<span class="cstat-no" title="statement not covered" >      'control',</span>
<span class="cstat-no" title="statement not covered" >      'control1',</span>
<span class="cstat-no" title="statement not covered" >      'control2',</span>
<span class="cstat-no" title="statement not covered" >      'points',</span>
<span class="cstat-no" title="statement not covered" >      'startAngle',</span>
<span class="cstat-no" title="statement not covered" >      'endAngle',</span>
<span class="cstat-no" title="statement not covered" >      'rotation',</span>
<span class="cstat-no" title="statement not covered" >      'scaleX',</span>
<span class="cstat-no" title="statement not covered" >      'scaleY',</span>
<span class="cstat-no" title="statement not covered" >    ]</span>
&nbsp;
    // 成本相关的属性列表（这些属性变更不应该触发几何计算）
<span class="cstat-no" title="statement not covered" >    const costProperties = [</span>
<span class="cstat-no" title="statement not covered" >      'costEnabled',</span>
<span class="cstat-no" title="statement not covered" >      'costUnitPrice',</span>
<span class="cstat-no" title="statement not covered" >      'costBasis',</span>
<span class="cstat-no" title="statement not covered" >      'costMultiplierOrCount',</span>
<span class="cstat-no" title="statement not covered" >      'costQuantityFactor',</span>
<span class="cstat-no" title="statement not covered" >      'costTotal',</span>
<span class="cstat-no" title="statement not covered" >      'costStatus',</span>
<span class="cstat-no" title="statement not covered" >      'costError',</span>
<span class="cstat-no" title="statement not covered" >      'computeCostEnabled', // 添加这个属性</span>
<span class="cstat-no" title="statement not covered" >    ]</span>
&nbsp;
    // 检查顶层属性
<span class="cstat-no" title="statement not covered" >    for (const key of Object.keys(changes)) {</span>
<span class="cstat-no" title="statement not covered" >      if (costProperties.includes(key)) {</span>
<span class="cstat-no" title="statement not covered" >        console.warn(`[CoreCoordinator] 检测到成本属性变更: ${key}，跳过几何计算`)</span>
<span class="cstat-no" title="statement not covered" >        continue</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      if (geometryProperties.includes(key)) {</span>
<span class="cstat-no" title="statement not covered" >        console.warn(`[CoreCoordinator] 检测到几何属性变更: ${key}`)</span>
<span class="cstat-no" title="statement not covered" >        return true</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // 检查 properties 中的属性
<span class="cstat-no" title="statement not covered" >    if (changes.properties &amp;&amp; typeof changes.properties === 'object') {</span>
<span class="cstat-no" title="statement not covered" >      const props = changes.properties as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >      for (const key of Object.keys(props)) {</span>
<span class="cstat-no" title="statement not covered" >        if (costProperties.includes(key)) {</span>
<span class="cstat-no" title="statement not covered" >          console.warn(`[CoreCoordinator] 检测到 properties 中的成本属性变更: ${key}，跳过几何计算`)</span>
<span class="cstat-no" title="statement not covered" >          continue</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        if (geometryProperties.includes(key)) {</span>
<span class="cstat-no" title="statement not covered" >          console.warn(`[CoreCoordinator] 检测到 properties 中的几何属性变更: ${key}`)</span>
<span class="cstat-no" title="statement not covered" >          return true</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    console.warn(`[CoreCoordinator] 未检测到几何属性变更，跳过几何计算`)</span>
<span class="cstat-no" title="statement not covered" >    return false</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Returns a deep copy of the current core configuration.
   * @returns The current configuration.
   */
<span class="cstat-no" title="statement not covered" >  public getConfig(): CoreConfig {</span>
<span class="cstat-no" title="statement not covered" >    return createConfig(this.config) // Return a deep copy</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Updates the core configuration with partial settings.
   *
   * @param partialConfig - An object containing configuration properties to update.
   */
<span class="cstat-no" title="statement not covered" >  public updateConfig(partialConfig: Partial&lt;CoreConfig&gt;): void {</span>
<span class="cstat-no" title="statement not covered" >    this.config = createConfig({</span>
<span class="cstat-no" title="statement not covered" >      ...this.config,</span>
<span class="cstat-no" title="statement not covered" >      ...partialConfig,</span>
<span class="cstat-no" title="statement not covered" >    })</span>
&nbsp;
    // Check if this is an internal update to avoid circular updates
<span class="cstat-no" title="statement not covered" >    if (!this.isInternalUpdate) {</span>
<span class="cstat-no" title="statement not covered" >      this.publishShapesUpdate() // Reference publishShapesUpdate to ensure it's used</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >      type: AppEventType.ConfigUpdated,</span>
<span class="cstat-no" title="statement not covered" >      payload: { config: this.getConfig() },</span>
<span class="cstat-no" title="statement not covered" >    })</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Centralized error handler for the core module.
   *
   * @remarks
   * Logs the error and publishes an `ERROR_OCCURRED` event.
   *
   * @param error - The error object (can be `Error` or `CoreError`).
   * @param context - Optional additional context information about the error.
   */
<span class="cstat-no" title="statement not covered" >  public handleError(error: Error | CoreError, context?: unknown): void {</span>
    // handleError in ErrorService expects one argument.
    // If 'error' is already a CoreError with context, it will be used.
    // If 'error' is a standard Error, ErrorService will wrap it.
    // The 'context' object here was previously passed as a second argument.
    // We might need to enrich the 'error' object itself if this context is vital
    // or adjust ErrorService.handleError if it's meant to take a context separately.
    // For now, assuming ErrorService's single argument design is intended.
<span class="cstat-no" title="statement not covered" >    if (error instanceof Error &amp;&amp; !(error instanceof CoreError) &amp;&amp; context != null) {</span>
      // Attach context to a standard error if it's not a CoreError
<span class="cstat-no" title="statement not covered" >      (error as Error &amp; { _additionalContext?: unknown })._additionalContext = context</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    this.errorService.handleError(error)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Registers event handlers to delegate actions to the appropriate services.
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private registerEventHandlers(): void {</span>
<span class="cstat-no" title="statement not covered" >    console.warn('[CoreCoordinator] registerEventHandlers called')</span>
<span class="cstat-no" title="statement not covered" >    this.logger.info('Registering CoreCoordinator event handlers...')</span>
&nbsp;
    // Shape Creation
<span class="cstat-no" title="statement not covered" >    this.eventBus.subscribe(</span>
<span class="cstat-no" title="statement not covered" >      AppEventType.ShapeCreateRequest,</span>
<span class="cstat-no" title="statement not covered" >      (event: BaseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (event.type === AppEventType.ShapeCreateRequest) {</span>
<span class="cstat-no" title="statement not covered" >          const specificEvent = event as ShapeCreateEvent // This is ShapeCreateEvent from @/types/services/events</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug?.('CoreCoordinator received SHAPE_CREATE_REQUEST', specificEvent.payload)</span>
&nbsp;
          // Directly use the payload from the event published by useShapeManipulation
          // The payload from useShapeManipulation has: ElementType (string), position, properties
<span class="cstat-no" title="statement not covered" >          if (specificEvent.payload?.ElementType != null &amp;&amp; specificEvent.payload.position != null) {</span>
            // Construct the payload that ShapeCreationService.handleRequest expects
            // ShapeCreationService's ShapeCreateEventPayload is { elementType: ElementType (enum), position, properties }
<span class="cstat-no" title="statement not covered" >            const servicePayload: ElementCreateEventPayload = {</span>
<span class="cstat-no" title="statement not covered" >              elementType: specificEvent.payload.ElementType as ElementType, // Cast string to ElementType enum</span>
<span class="cstat-no" title="statement not covered" >              position: specificEvent.payload.position as { x: number, y: number }, // Explicit type assertion</span>
<span class="cstat-no" title="statement not covered" >              properties: specificEvent.payload.properties || {},</span>
<span class="cstat-no" title="statement not covered" >            }</span>
&nbsp;
            // Construct the event for ShapeCreationService.handleRequest
<span class="cstat-no" title="statement not covered" >            const serviceEventForCreationService: ElementCreateEvent = {</span>
<span class="cstat-no" title="statement not covered" >              type: specificEvent.type, // Use the original event type string</span>
<span class="cstat-no" title="statement not covered" >              payload: servicePayload,</span>
<span class="cstat-no" title="statement not covered" >              timestamp: specificEvent.timestamp ?? Date.now(),</span>
<span class="cstat-no" title="statement not covered" >            }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            this.elementCreationService.handleRequest(serviceEventForCreationService)</span>
<span class="cstat-no" title="statement not covered" >              .catch((err: unknown) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                const errorToHandle = err instanceof Error ? err : new Error(String(err))</span>
<span class="cstat-no" title="statement not covered" >                this.handleError(errorToHandle)</span>
<span class="cstat-no" title="statement not covered" >              })</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          else {</span>
<span class="cstat-no" title="statement not covered" >            this.logger.warn('CoreCoordinator: Invalid or unexpected payload for SHAPE_CREATE_REQUEST', specificEvent.payload)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    )</span>
&nbsp;
    // Shape Editing
<span class="cstat-no" title="statement not covered" >    this.eventBus.subscribe(</span>
<span class="cstat-no" title="statement not covered" >      AppEventType.ShapeEditRequest,</span>
<span class="cstat-no" title="statement not covered" >      (event: BaseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        void (async () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >          if (event.type === AppEventType.ShapeEditRequest) {</span>
<span class="cstat-no" title="statement not covered" >            const specificEvent = event as ShapeEditEvent</span>
<span class="cstat-no" title="statement not covered" >            this.logger.debug?.('CoreCoordinator received SHAPE_EDIT_REQUEST', specificEvent.payload)</span>
<span class="cstat-no" title="statement not covered" >            console.warn('[CoreCoordinator] ShapeEditRequest payload:', specificEvent.payload)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            console.warn('[CoreCoordinator] SHAPE_EDIT_REQUEST received with changes:', JSON.stringify(specificEvent.payload.changes, null, 2)) // Added logging for changes</span>
&nbsp;
            // 保存当前选择状态
<span class="cstat-no" title="statement not covered" >            const currentSelectedIds = Array.from(this.repository.getSelectedIds())</span>
<span class="cstat-no" title="statement not covered" >            console.warn('[CoreCoordinator] Current selection before edit:', currentSelectedIds)</span>
&nbsp;
            // 获取形状ID和变更内容
<span class="cstat-no" title="statement not covered" >            const shapeId = specificEvent.payload.shapeId</span>
<span class="cstat-no" title="statement not covered" >            const changes = specificEvent.payload.changes || {}</span>
&nbsp;
            // 获取要编辑的形状
<span class="cstat-no" title="statement not covered" >            if (!shapeId || shapeId === '') {</span>
<span class="cstat-no" title="statement not covered" >              console.error(`[CoreCoordinator] ELEMENT_EDIT_REQUEST missing shapeId`)</span>
<span class="cstat-no" title="statement not covered" >              return</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            const shape = this.repository.getById(shapeId)</span>
<span class="cstat-no" title="statement not covered" >            if (!shape) {</span>
<span class="cstat-no" title="statement not covered" >              console.error(`[CoreCoordinator] Shape ${shapeId} not found`)</span>
<span class="cstat-no" title="statement not covered" >              return</span>
<span class="cstat-no" title="statement not covered" >            }</span>
&nbsp;
            // 创建更新对象
<span class="cstat-no" title="statement not covered" >            const updates: Partial&lt;ShapeModel&gt; = {}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            console.warn(`[CoreCoordinator] Processing changes:`, JSON.stringify(changes, null, 2))</span>
&nbsp;
            // 处理所有属性
<span class="cstat-no" title="statement not covered" >            for (const key in changes) {</span>
<span class="cstat-no" title="statement not covered" >              if (Object.prototype.hasOwnProperty.call(changes, key)) {</span>
                // 特殊处理pattern字段
<span class="cstat-no" title="statement not covered" >                if (key === 'pattern') {</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator] Processing pattern field for shape ${shapeId}`)</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator] Original pattern value:`, JSON.stringify(changes.pattern, null, 2))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                  if (changes.pattern === undefined || changes.pattern === null) {</span>
<span class="cstat-no" title="statement not covered" >                    console.warn(`[CoreCoordinator] Setting pattern to undefined/null for deletion`);</span>
                    // 🔧 修复：正确处理pattern为undefined的情况
<span class="cstat-no" title="statement not covered" >                    (updates as any).pattern = changes.pattern // 使用any类型避免类型检查问题</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                  else {</span>
<span class="cstat-no" title="statement not covered" >                    const cleanedPattern = cleanPattern(changes.pattern) as PatternDefinition</span>
<span class="cstat-no" title="statement not covered" >                    console.warn(`[CoreCoordinator] Cleaned pattern:`, JSON.stringify(cleanedPattern, null, 2))</span>
<span class="cstat-no" title="statement not covered" >                    updates.pattern = cleanedPattern</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                }</span>
                // 特殊处理position字段
<span class="cstat-no" title="statement not covered" >                else if (key === 'position' &amp;&amp; typeof changes.position === 'object') {</span>
<span class="cstat-no" title="statement not covered" >                  updates.position = { ...shape.position, ...changes.position as { x: number, y: number } }</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator] Merged position:`, updates.position)</span>
<span class="cstat-no" title="statement not covered" >                }</span>
                // 处理properties字段
<span class="cstat-no" title="statement not covered" >                else if (key === 'properties' &amp;&amp; typeof changes.properties === 'object') {</span>
                  // 确保properties存在
<span class="cstat-no" title="statement not covered" >                  if (!updates.properties) {</span>
<span class="cstat-no" title="statement not covered" >                    updates.properties = { ...shape.properties }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
&nbsp;
                  // 合并properties
<span class="cstat-no" title="statement not covered" >                  const changesProps = changes.properties as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >                  for (const propKey in changesProps) {</span>
<span class="cstat-no" title="statement not covered" >                    if (Object.prototype.hasOwnProperty.call(changesProps, propKey)) {</span>
<span class="cstat-no" title="statement not covered" >                      const propertiesObj = updates.properties</span>
<span class="cstat-no" title="statement not covered" >                      propertiesObj[propKey] = changesProps[propKey]</span>
<span class="cstat-no" title="statement not covered" >                      console.warn(`[CoreCoordinator] Setting property ${propKey} to`, changesProps[propKey])</span>
&nbsp;
                      // 特殊处理图层属性，确保同时更新到顶层
<span class="cstat-no" title="statement not covered" >                      if (propKey === 'majorCategory' || propKey === 'minorCategory' || propKey === 'zLevelId') {</span>
<span class="cstat-no" title="statement not covered" >                        const updatesObj = updates as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >                        updatesObj[propKey] = changesProps[propKey]</span>
<span class="cstat-no" title="statement not covered" >                        console.warn(`[CoreCoordinator] Also setting top-level ${propKey} to`, changesProps[propKey])</span>
&nbsp;
                        // 添加额外的日志，确认图层属性已正确设置
<span class="cstat-no" title="statement not covered" >                        setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                          const updatedShape = this.repository.getById(shapeId)</span>
<span class="cstat-no" title="statement not covered" >                          if (updatedShape) {</span>
<span class="cstat-no" title="statement not covered" >                            console.warn(`[CoreCoordinator] DELAYED CHECK: Shape ${shapeId} ${propKey} after update:`, updatedShape[propKey as keyof ShapeModel])</span>
<span class="cstat-no" title="statement not covered" >                          }</span>
<span class="cstat-no" title="statement not covered" >                        }, 500)</span>
<span class="cstat-no" title="statement not covered" >                      }</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                }</span>
                // 特殊处理图层属性
<span class="cstat-no" title="statement not covered" >                else if (key === 'majorCategory' || key === 'minorCategory' || key === 'zLevelId') {</span>
<span class="cstat-no" title="statement not covered" >                  const updatesObj = updates as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >                  updatesObj[key] = changes[key]</span>
                  // 确保properties存在
<span class="cstat-no" title="statement not covered" >                  if (!updates.properties) {</span>
<span class="cstat-no" title="statement not covered" >                    updates.properties = { ...shape.properties }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
                  // 同时更新properties中的图层属性
<span class="cstat-no" title="statement not covered" >                  const propertiesObj = updates.properties</span>
<span class="cstat-no" title="statement not covered" >                  propertiesObj[key] = changes[key]</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator] Setting layer property ${key} to both top-level and properties:`, changes[key])</span>
<span class="cstat-no" title="statement not covered" >                }</span>
                // 🔧 特殊处理LINE元素的start和end属性
<span class="cstat-no" title="statement not covered" >                else if ((key === 'start' || key === 'end') &amp;&amp; shape.type === 'LINE') {</span>
                  // 确保properties存在
<span class="cstat-no" title="statement not covered" >                  if (!updates.properties) {</span>
<span class="cstat-no" title="statement not covered" >                    updates.properties = { ...shape.properties }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
&nbsp;
                  // properties中存储相对坐标
<span class="cstat-no" title="statement not covered" >                  const propertiesObj = updates.properties</span>
<span class="cstat-no" title="statement not covered" >                  propertiesObj[key] = changes[key]</span>
&nbsp;
                  // 顶层存储绝对坐标：position + 相对坐标
<span class="cstat-no" title="statement not covered" >                  const position = shape.position || { x: 0, y: 0, z: 0 }</span>
<span class="cstat-no" title="statement not covered" >                  const relativePoint = changes[key] as { x: number, y: number, z?: number }</span>
<span class="cstat-no" title="statement not covered" >                  const absolutePoint = {</span>
<span class="cstat-no" title="statement not covered" >                    x: position.x + relativePoint.x,</span>
<span class="cstat-no" title="statement not covered" >                    y: position.y + relativePoint.y,</span>
<span class="cstat-no" title="statement not covered" >                    z: (position.z || 0) + (relativePoint.z || 0),</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                  const updatesObj = updates as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >                  updatesObj[key] = absolutePoint</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator] Setting LINE ${key} property:`, {</span>
<span class="cstat-no" title="statement not covered" >                    relative: changes[key],</span>
<span class="cstat-no" title="statement not covered" >                    position,</span>
<span class="cstat-no" title="statement not covered" >                    absolute: absolutePoint,</span>
<span class="cstat-no" title="statement not covered" >                  })</span>
<span class="cstat-no" title="statement not covered" >                }</span>
                // 🔧 特殊处理QUADRATIC元素的控制点属性
<span class="cstat-no" title="statement not covered" >                else if ((key === 'start' || key === 'control' || key === 'end') &amp;&amp; shape.type === 'QUADRATIC') {</span>
                  // 确保properties存在
<span class="cstat-no" title="statement not covered" >                  if (!updates.properties) {</span>
<span class="cstat-no" title="statement not covered" >                    updates.properties = { ...shape.properties }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
&nbsp;
                  // properties中存储相对坐标
<span class="cstat-no" title="statement not covered" >                  const propertiesObj = updates.properties</span>
<span class="cstat-no" title="statement not covered" >                  propertiesObj[key] = changes[key]</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator] Setting QUADRATIC ${key} property (relative):`, changes[key])</span>
<span class="cstat-no" title="statement not covered" >                }</span>
                // 🔧 特殊处理CUBIC元素的控制点属性
<span class="cstat-no" title="statement not covered" >                else if ((key === 'start' || key === 'control1' || key === 'control2' || key === 'end') &amp;&amp; shape.type === 'CUBIC') {</span>
                  // 确保properties存在
<span class="cstat-no" title="statement not covered" >                  if (!updates.properties) {</span>
<span class="cstat-no" title="statement not covered" >                    updates.properties = { ...shape.properties }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
&nbsp;
                  // properties中存储相对坐标
<span class="cstat-no" title="statement not covered" >                  const propertiesObj = updates.properties</span>
<span class="cstat-no" title="statement not covered" >                  propertiesObj[key] = changes[key]</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator] Setting CUBIC ${key} property (relative):`, changes[key])</span>
<span class="cstat-no" title="statement not covered" >                }</span>
                // 处理几何属性（需要同时设置到顶层和properties中）
<span class="cstat-no" title="statement not covered" >                else if (['radius', 'points', 'width', 'height', 'sides', 'isRegular'].includes(key)) {</span>
                  // 设置到顶层
<span class="cstat-no" title="statement not covered" >                  const updatesObj = updates as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >                  updatesObj[key] = changes[key]</span>
                  // 确保properties存在
<span class="cstat-no" title="statement not covered" >                  if (!updates.properties) {</span>
<span class="cstat-no" title="statement not covered" >                    updates.properties = { ...shape.properties }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
                  // 同时更新properties中的几何属性
<span class="cstat-no" title="statement not covered" >                  const propertiesObj = updates.properties</span>
<span class="cstat-no" title="statement not covered" >                  propertiesObj[key] = changes[key]</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator] Setting geometry property ${key} to both top-level and properties:`, changes[key])</span>
<span class="cstat-no" title="statement not covered" >                }</span>
                // 处理样式属性（需要同时设置到顶层和properties中）
<span class="cstat-no" title="statement not covered" >                else if (['fill', 'stroke', 'strokeWidth', 'opacity', 'strokeDasharray', 'strokeLinecap', 'strokeLinejoin', 'cornerRadius', 'rotation'].includes(key)) {</span>
                  // 设置到顶层
<span class="cstat-no" title="statement not covered" >                  const updatesObj = updates as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >                  updatesObj[key] = changes[key]</span>
                  // 确保properties存在
<span class="cstat-no" title="statement not covered" >                  if (!updates.properties) {</span>
<span class="cstat-no" title="statement not covered" >                    updates.properties = { ...shape.properties }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
                  // 同时更新properties中的样式属性
<span class="cstat-no" title="statement not covered" >                  const propertiesObj = updates.properties</span>
<span class="cstat-no" title="statement not covered" >                  propertiesObj[key] = changes[key]</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator] Setting style property ${key} to both top-level and properties:`, changes[key])</span>
<span class="cstat-no" title="statement not covered" >                }</span>
                // 处理其他属性
<span class="cstat-no" title="statement not covered" >                else {</span>
<span class="cstat-no" title="statement not covered" >                  const updatesObj = updates as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >                  updatesObj[key] = changes[key]</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator] Setting ${key} to`, changes[key])</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >            }</span>
&nbsp;
            // 确保关键属性被正确处理
<span class="cstat-no" title="statement not covered" >            console.warn(`[CoreCoordinator] Final updates object:`, JSON.stringify(updates, null, 2))</span>
&nbsp;
            // --- Handle nested properties (including geometry and cost) ---
            // Ensure existing properties are spread first, then changes.properties are merged
            // IMPORTANT: Preserve any properties already set in updates.properties from style/geometry processing above
<span class="cstat-no" title="statement not covered" >            if ('properties' in changes &amp;&amp; typeof changes.properties === 'object' &amp;&amp; changes.properties !== null) {</span>
              // Merge in this order: shape.properties -&gt; updates.properties (from style/geometry processing) -&gt; changes.properties
<span class="cstat-no" title="statement not covered" >              updates.properties = {</span>
<span class="cstat-no" title="statement not covered" >                ...shape.properties,</span>
<span class="cstat-no" title="statement not covered" >                ...(updates.properties || {}), // Preserve style/geometry properties set above</span>
<span class="cstat-no" title="statement not covered" >                ...changes.properties as Record&lt;string, unknown&gt;,</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              console.warn(`[CoreCoordinator] Merged nested properties for shape ${shapeId}:`, JSON.stringify(updates.properties, null, 2))</span>
<span class="cstat-no" title="statement not covered" >              console.warn(`[CoreCoordinator] Original shape.properties:`, JSON.stringify(shape.properties, null, 2))</span>
<span class="cstat-no" title="statement not covered" >              console.warn(`[CoreCoordinator] Changes.properties:`, JSON.stringify(changes.properties, null, 2))</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            else if (shape.properties) {</span>
              // If no changes.properties but shape has properties, merge with existing updates.properties
<span class="cstat-no" title="statement not covered" >              updates.properties = { ...shape.properties, ...(updates.properties || {}) }</span>
<span class="cstat-no" title="statement not covered" >              console.warn(`[CoreCoordinator] Merged existing shape.properties with updates.properties:`, JSON.stringify(updates.properties, null, 2))</span>
<span class="cstat-no" title="statement not covered" >            }</span>
&nbsp;
            // 确保成本相关属性正确地应用到元素上
            // 如果changes中包含成本相关属性，则使用changes中的值
            // 否则，使用shape中的现有值
<span class="cstat-no" title="statement not covered" >            console.warn(`[CoreCoordinator] Starting cost properties processing for shape ${shapeId}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (shape.properties) {</span>
              // 创建或获取现有的properties对象
<span class="cstat-no" title="statement not covered" >              if (!updates.properties) {</span>
<span class="cstat-no" title="statement not covered" >                updates.properties = {}</span>
<span class="cstat-no" title="statement not covered" >              }</span>
&nbsp;
              // 安全地处理 changes.properties - 修复原始问题
<span class="cstat-no" title="statement not covered" >              const changesProps = (changes &amp;&amp; typeof changes === 'object' &amp;&amp; 'properties' in changes &amp;&amp; changes.properties != null &amp;&amp; typeof changes.properties === 'object')</span>
<span class="cstat-no" title="statement not covered" >                ? (changes.properties as Record&lt;string, unknown&gt;)</span>
<span class="cstat-no" title="statement not covered" >                : {}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              console.warn(`[CoreCoordinator] changesProps extracted:`, JSON.stringify(changesProps, null, 2))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              const propertiesObj = updates.properties</span>
&nbsp;
              // 处理costUnitPrice - 明确检查是否在changes.properties中存在
<span class="cstat-no" title="statement not covered" >              if ('costUnitPrice' in changesProps) {</span>
                // 如果明确设置了值（即使是undefined），使用该值
<span class="cstat-no" title="statement not covered" >                propertiesObj.costUnitPrice = changesProps.costUnitPrice</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              else if (shape.properties.costUnitPrice !== undefined) {</span>
                // 如果changes.properties中没有明确设置，但shape中有值，则保留原值
<span class="cstat-no" title="statement not covered" >                propertiesObj.costUnitPrice = shape.properties.costUnitPrice</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              else {</span>
                // 如果都没有，设置默认值为1
<span class="cstat-no" title="statement not covered" >                propertiesObj.costUnitPrice = 1</span>
<span class="cstat-no" title="statement not covered" >              }</span>
&nbsp;
              // 处理costBasis
<span class="cstat-no" title="statement not covered" >              if ('costBasis' in changesProps) {</span>
                // 如果明确设置了值（即使是undefined），使用该值
<span class="cstat-no" title="statement not covered" >                propertiesObj.costBasis = changesProps.costBasis</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              else if (shape.properties.costBasis !== undefined) {</span>
                // 如果changes.properties中没有明确设置，但shape中有值，则保留原值
<span class="cstat-no" title="statement not covered" >                propertiesObj.costBasis = shape.properties.costBasis</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              else {</span>
                // 如果都没有，设置默认值为'unit'
<span class="cstat-no" title="statement not covered" >                propertiesObj.costBasis = 'unit'</span>
<span class="cstat-no" title="statement not covered" >              }</span>
&nbsp;
              // 处理costMultiplierOrCount
<span class="cstat-no" title="statement not covered" >              if ('costMultiplierOrCount' in changesProps) {</span>
                // 如果明确设置了值（即使是undefined），使用该值
<span class="cstat-no" title="statement not covered" >                propertiesObj.costMultiplierOrCount = changesProps.costMultiplierOrCount</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              else if (shape.properties.costMultiplierOrCount !== undefined) {</span>
                // 如果changes.properties中没有明确设置，但shape中有值，则保留原值
<span class="cstat-no" title="statement not covered" >                propertiesObj.costMultiplierOrCount = shape.properties.costMultiplierOrCount</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              else {</span>
                // 如果都没有，设置默认值为0，确保初始成本为0
<span class="cstat-no" title="statement not covered" >                propertiesObj.costMultiplierOrCount = 0</span>
<span class="cstat-no" title="statement not covered" >              }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              console.warn(`[CoreCoordinator] Final cost properties for shape ${shapeId}:`, JSON.stringify({</span>
<span class="cstat-no" title="statement not covered" >                costUnitPrice: propertiesObj.costUnitPrice,</span>
<span class="cstat-no" title="statement not covered" >                costBasis: propertiesObj.costBasis,</span>
<span class="cstat-no" title="statement not covered" >                costMultiplierOrCount: propertiesObj.costMultiplierOrCount,</span>
<span class="cstat-no" title="statement not covered" >              }, null, 2))</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            console.warn(`[CoreCoordinator] Completed cost properties processing for shape ${shapeId}`)</span>
&nbsp;
            // 更新形状
<span class="cstat-no" title="statement not covered" >            console.warn(`[CoreCoordinator] Updating shape ${shapeId} with:`, JSON.stringify(updates, null, 2))</span>
<span class="cstat-no" title="statement not covered" >            this.repository.update(shapeId, updates)</span>
<span class="cstat-no" title="statement not covered" >            await new Promise(resolve =&gt; setTimeout(resolve, 50)) // Add a small delay to allow repository update to propagate</span>
<span class="cstat-no" title="statement not covered" >            console.warn(`[CoreCoordinator] 已更新形状 ${shapeId} 的属性`)</span>
&nbsp;
            // 确保选择状态保持不变
<span class="cstat-no" title="statement not covered" >            this.repository.setSelectedIds(currentSelectedIds)</span>
&nbsp;
            // Synchronize state from repository to Zustand store via event
<span class="cstat-no" title="statement not covered" >            this.publishShapesUpdate()</span>
&nbsp;
            // 发布ShapeEditComplete事件
<span class="cstat-no" title="statement not covered" >            this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >              type: AppEventType.ShapeEditComplete,</span>
<span class="cstat-no" title="statement not covered" >              timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >              payload: {</span>
<span class="cstat-no" title="statement not covered" >                shape: this.repository.getById(shapeId), // 传递最新 shape 对象</span>
<span class="cstat-no" title="statement not covered" >                selectedIds: currentSelectedIds,</span>
<span class="cstat-no" title="statement not covered" >              },</span>
<span class="cstat-no" title="statement not covered" >            })</span>
&nbsp;
            // 确保UI更新显示正确的选择状态
<span class="cstat-no" title="statement not covered" >            setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >              this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >                type: AppEventType.SelectionChanged,</span>
<span class="cstat-no" title="statement not covered" >                timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >                payload: { selectedIds: currentSelectedIds },</span>
<span class="cstat-no" title="statement not covered" >              })</span>
<span class="cstat-no" title="statement not covered" >            }, 50) // 增加延迟以确保选择状态正确应用</span>
&nbsp;
            // === 新增：属性变更后，调用 core 计算 area/perimeter/cost 并加日志 ===
            // 只有在几何相关属性变更时才触发几何计算，避免成本属性变更时的不必要计算
<span class="cstat-no" title="statement not covered" >            const shouldTriggerGeometryCalculation = this.shouldTriggerGeometryCalculation(changes)</span>
<span class="cstat-no" title="statement not covered" >            console.warn(`[CoreCoordinator][compute] 是否需要触发几何计算: ${shouldTriggerGeometryCalculation}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (this.computeFacade &amp;&amp; shouldTriggerGeometryCalculation) {</span>
<span class="cstat-no" title="statement not covered" >              try {</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] 开始计算 shapeId=${shapeId} 的面积、周长和成本...`)</span>
&nbsp;
                // 获取最新的shape对象，确保使用最新的属性值
<span class="cstat-no" title="statement not covered" >                const shapeToCompute = this.repository.getById(shapeId)</span>
<span class="cstat-no" title="statement not covered" >                if (!shapeToCompute) {</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator][compute] 无法找到形状 ${shapeId}`)</span>
<span class="cstat-no" title="statement not covered" >                  return</span>
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] 形状类型: ${shapeToCompute.type}`)</span>
&nbsp;
                // 计算面积
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] 计算面积...`)</span>
<span class="cstat-no" title="statement not covered" >                let area = 0</span>
<span class="cstat-no" title="statement not covered" >                let areaStatus = 'none' // ComputeStatus.NONE</span>
<span class="cstat-no" title="statement not covered" >                let areaError: string | undefined</span>
<span class="cstat-no" title="statement not covered" >                try {</span>
<span class="cstat-no" title="statement not covered" >                  area = await this.computeFacade.computeArea(shapeId)</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator][compute] 面积计算成功: ${area}`)</span>
<span class="cstat-no" title="statement not covered" >                  areaStatus = 'success' // ComputeStatus.SUCCESS</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >                catch (areaErr) {</span>
<span class="cstat-no" title="statement not covered" >                  console.error(`[CoreCoordinator][compute] 面积计算失败:`, areaErr)</span>
<span class="cstat-no" title="statement not covered" >                  areaStatus = 'error' // ComputeStatus.ERROR</span>
<span class="cstat-no" title="statement not covered" >                  areaError = areaErr instanceof Error ? areaErr.message : String(areaErr)</span>
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
                // 计算周长
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] 计算周长...`)</span>
<span class="cstat-no" title="statement not covered" >                let perimeter = 0</span>
<span class="cstat-no" title="statement not covered" >                let perimeterStatus = 'none' // ComputeStatus.NONE</span>
<span class="cstat-no" title="statement not covered" >                let perimeterError: string | undefined</span>
<span class="cstat-no" title="statement not covered" >                try {</span>
<span class="cstat-no" title="statement not covered" >                  perimeter = await this.computeFacade.computePerimeter(shapeId)</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator][compute] 周长计算成功: ${perimeter}`)</span>
<span class="cstat-no" title="statement not covered" >                  perimeterStatus = 'success' // ComputeStatus.SUCCESS</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >                catch (perimeterErr) {</span>
<span class="cstat-no" title="statement not covered" >                  console.error(`[CoreCoordinator][compute] 周长计算失败:`, perimeterErr)</span>
<span class="cstat-no" title="statement not covered" >                  perimeterStatus = 'error' // ComputeStatus.ERROR</span>
<span class="cstat-no" title="statement not covered" >                  perimeterError = perimeterErr instanceof Error ? perimeterErr.message : String(perimeterErr)</span>
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
                // 获取最新的shape对象的properties - 使用更新后的properties
<span class="cstat-no" title="statement not covered" >                const shapeProps = updates.properties || shapeToCompute.properties || {}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] USING properties from shape for cost computation:`, JSON.stringify(shapeProps, null, 2))</span>
&nbsp;
                // 从shape对象中提取成本计算参数
                // 默认单位成本为1而不是0
<span class="cstat-no" title="statement not covered" >                const unitCost = typeof shapeProps.costUnitPrice === 'number' &amp;&amp; !Number.isNaN(shapeProps.costUnitPrice) &amp;&amp; shapeProps.costUnitPrice &gt; 0</span>
<span class="cstat-no" title="statement not covered" >                  ? shapeProps.costUnitPrice</span>
<span class="cstat-no" title="statement not covered" >                  : 1</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] 单位成本 (costUnitPrice): ${unitCost}, 原始值: ${String(shapeProps.costUnitPrice)}, 类型: ${typeof shapeProps.costUnitPrice})`)</span>
&nbsp;
                // 获取乘数
<span class="cstat-no" title="statement not covered" >                const multiplier = typeof shapeProps.costMultiplierOrCount === 'number' &amp;&amp; !Number.isNaN(shapeProps.costMultiplierOrCount)</span>
<span class="cstat-no" title="statement not covered" >                  ? shapeProps.costMultiplierOrCount</span>
<span class="cstat-no" title="statement not covered" >                  : 1 // 🔧 修复：默认为1而不是0，避免总成本为0</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] 乘数 (costMultiplierOrCount): ${multiplier}, 原始值: ${String(shapeProps.costMultiplierOrCount)}, 类型: ${typeof shapeProps.costMultiplierOrCount})`)</span>
&nbsp;
                // 将 costBasis 映射到 costType
<span class="cstat-no" title="statement not covered" >                let costType: 'area' | 'perimeter' | 'unit' | 'segment' | 'fixed' = 'unit'</span>
<span class="cstat-no" title="statement not covered" >                const costBasisToUse = shapeProps.costBasis as string | undefined</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                if (typeof costBasisToUse === 'string' &amp;&amp; ['area', 'perimeter', 'unit', 'segment', 'fixed'].includes(costBasisToUse)) {</span>
<span class="cstat-no" title="statement not covered" >                  costType = costBasisToUse as 'area' | 'perimeter' | 'unit' | 'segment' | 'fixed'</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] 成本基准 (costType): ${costType}, 原始值: ${String(shapeProps.costBasis)}, 类型: ${typeof costBasisToUse})`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                const quantity = typeof shapeProps.costMultiplierOrCount === 'number' ? shapeProps.costMultiplierOrCount : 0</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] 数量 (quantity): ${quantity}, 原始值: ${String(shapeProps.costMultiplierOrCount)}, 类型: ${typeof shapeProps.costMultiplierOrCount})`)</span>
&nbsp;
                // 组装计算选项
<span class="cstat-no" title="statement not covered" >                const options = {</span>
<span class="cstat-no" title="statement not covered" >                  costType,</span>
<span class="cstat-no" title="statement not covered" >                  quantity,</span>
<span class="cstat-no" title="statement not covered" >                  multiplier, // 添加乘数到计算选项</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] 计算选项: unitCost=${unitCost}, costType=${costType}, quantity=${quantity}, multiplier=${multiplier}`)</span>
&nbsp;
                // 计算成本
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] 计算成本...`)</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] Passing unitCost to computeFacade.computeCost: ${unitCost}, Type: ${typeof unitCost}`)</span>
<span class="cstat-no" title="statement not covered" >                let cost = 0</span>
<span class="cstat-no" title="statement not covered" >                try {</span>
<span class="cstat-no" title="statement not covered" >                  cost = await this.computeFacade.computeCost(shapeId, unitCost, options)</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator][compute] 成本计算成功: ${cost}`)</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >                catch (costErr) {</span>
<span class="cstat-no" title="statement not covered" >                  console.error(`[CoreCoordinator][compute] 成本计算失败:`, costErr)</span>
&nbsp;
                  // 手动计算成本
                  // 基于costType计算基础值
<span class="cstat-no" title="statement not covered" >                  let baseValue = 1 // 默认为1单位</span>
<span class="cstat-no" title="statement not covered" >                  if (costType === 'area') {</span>
<span class="cstat-no" title="statement not covered" >                    try {</span>
<span class="cstat-no" title="statement not covered" >                      baseValue = await this.computeFacade.computeArea(shapeId)</span>
<span class="cstat-no" title="statement not covered" >                      console.warn(`[CoreCoordinator][compute] 面积计算成功: ${baseValue}`)</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                    catch (areaErr) {</span>
<span class="cstat-no" title="statement not covered" >                      console.error(`[CoreCoordinator][compute] 面积计算失败:`, areaErr)</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                  else if (costType === 'perimeter') {</span>
<span class="cstat-no" title="statement not covered" >                    try {</span>
<span class="cstat-no" title="statement not covered" >                      baseValue = await this.computeFacade.computePerimeter(shapeId)</span>
<span class="cstat-no" title="statement not covered" >                      console.warn(`[CoreCoordinator][compute] 周长计算成功: ${baseValue}`)</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                    catch (perimeterErr) {</span>
<span class="cstat-no" title="statement not covered" >                      console.error(`[CoreCoordinator][compute] 周长计算失败:`, perimeterErr)</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
&nbsp;
                  // 计算总成本
<span class="cstat-no" title="statement not covered" >                  if (costType === 'unit' || costType === 'segment') {</span>
                    // 对于按单位或线段计算的元素，使用quantity
<span class="cstat-no" title="statement not covered" >                    cost = unitCost * baseValue * quantity</span>
<span class="cstat-no" title="statement not covered" >                    console.warn(`[CoreCoordinator][compute] 手动计算成本(按单位): ${unitCost} * ${baseValue} * ${quantity} = ${cost}`)</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                  else {</span>
                    // 对于按面积或周长计算的元素，使用multiplier
<span class="cstat-no" title="statement not covered" >                    cost = unitCost * baseValue * multiplier</span>
<span class="cstat-no" title="statement not covered" >                    console.warn(`[CoreCoordinator][compute] 手动计算成本(按面积/周长): ${unitCost} * ${baseValue} * ${multiplier} = ${cost}`)</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
                // 成本可以为0（当乘数为0时）
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] 最终计算成本: ${cost}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] 计算结果: shapeId=${shapeId}, area=${area}, perimeter=${perimeter}, cost=${cost}`)</span>
&nbsp;
                // Update the shape's costTotal property and ensure other cost-related properties are included
&nbsp;
                // 保存关键几何属性，确保不会被覆盖
<span class="cstat-no" title="statement not covered" >                const { radius, width, height } = shapeProps</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                const updatedProps = {</span>
<span class="cstat-no" title="statement not covered" >                  ...shapeProps, // Start with the properties used for computation</span>
                  // 几何计算结果 - 持久化存储（支持14个基本元素）
<span class="cstat-no" title="statement not covered" >                  computedArea: area,</span>
<span class="cstat-no" title="statement not covered" >                  computedPerimeter: perimeter,</span>
<span class="cstat-no" title="statement not covered" >                  computedLength: perimeter, // 对于路径类型，周长就是长度</span>
<span class="cstat-no" title="statement not covered" >                  computedAreaUnit: 'mm²', // 基础单位：平方毫米</span>
<span class="cstat-no" title="statement not covered" >                  computedPerimeterUnit: 'mm', // 基础单位：毫米</span>
<span class="cstat-no" title="statement not covered" >                  computedLengthUnit: 'mm', // 基础单位：毫米</span>
                  // 计算状态信息
<span class="cstat-no" title="statement not covered" >                  computedAreaStatus: areaStatus,</span>
<span class="cstat-no" title="statement not covered" >                  computedPerimeterStatus: perimeterStatus,</span>
<span class="cstat-no" title="statement not covered" >                  computedLengthStatus: perimeterStatus, // 对于路径类型，长度状态与周长状态相同</span>
<span class="cstat-no" title="statement not covered" >                  computedAreaError: areaError,</span>
<span class="cstat-no" title="statement not covered" >                  computedPerimeterError: perimeterError,</span>
<span class="cstat-no" title="statement not covered" >                  computedLengthError: perimeterError, // 对于路径类型，长度错误与周长错误相同</span>
                  // 成本计算结果
<span class="cstat-no" title="statement not covered" >                  costTotal: cost,</span>
                  // 确保所有成本相关属性都被包含
<span class="cstat-no" title="statement not covered" >                  costUnitPrice: unitCost,</span>
                  // 🔧 修复：不要覆盖用户设置的costBasis！保持原有的值
                  // costBasis: costType, // 这行是错误的！会覆盖用户设置
                  // 不要覆盖用户设置的乘数值！保持原有的 costMultiplierOrCount
                  // costMultiplierOrCount: quantity, // 这行是错误的！
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
                // 如果是圆形元素，确保保留原始的半径和直径
<span class="cstat-no" title="statement not covered" >                if (shapeToCompute.type === 'circle' &amp;&amp; radius !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator][compute] 保留圆形元素的原始半径: ${String(radius)}和直径: ${String(width)}x${String(height)}`)</span>
                  // 使用明确的类型定义来避免TypeScript错误
<span class="cstat-no" title="statement not covered" >                  const propsObj = updatedProps as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >                  propsObj.radius = radius</span>
<span class="cstat-no" title="statement not covered" >                  if (width !== undefined)</span>
<span class="cstat-no" title="statement not covered" >                    propsObj.width = width</span>
<span class="cstat-no" title="statement not covered" >                  if (height !== undefined)</span>
<span class="cstat-no" title="statement not covered" >                    propsObj.height = height</span>
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] 更新前的属性:`, JSON.stringify(shapeProps, null, 2))</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] 更新后的属性:`, JSON.stringify(updatedProps, null, 2))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                this.repository.update(shapeId, { properties: updatedProps })</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][compute] 已更新形状 ${shapeId} 的属性`)</span>
&nbsp;
                // 触发 UI 层数据刷新
<span class="cstat-no" title="statement not covered" >                const storeShapes = useShapesStore.getState().shapes</span>
<span class="cstat-no" title="statement not covered" >                const updatedShapes = storeShapes.map(s =&gt;</span>
<span class="cstat-no" title="statement not covered" >                  s.id === shapeId</span>
<span class="cstat-no" title="statement not covered" >                    ? {</span>
<span class="cstat-no" title="statement not covered" >                        ...s,</span>
<span class="cstat-no" title="statement not covered" >                        properties: {</span>
<span class="cstat-no" title="statement not covered" >                          ...s.properties,</span>
                          // 几何计算结果 - 持久化存储（支持14个基本元素）
<span class="cstat-no" title="statement not covered" >                          computedArea: area,</span>
<span class="cstat-no" title="statement not covered" >                          computedPerimeter: perimeter,</span>
<span class="cstat-no" title="statement not covered" >                          computedLength: perimeter, // 对于路径类型，周长就是长度</span>
<span class="cstat-no" title="statement not covered" >                          computedAreaUnit: 'mm²',</span>
<span class="cstat-no" title="statement not covered" >                          computedPerimeterUnit: 'mm',</span>
<span class="cstat-no" title="statement not covered" >                          computedLengthUnit: 'mm',</span>
                          // 计算状态信息
<span class="cstat-no" title="statement not covered" >                          computedAreaStatus: areaStatus,</span>
<span class="cstat-no" title="statement not covered" >                          computedPerimeterStatus: perimeterStatus,</span>
<span class="cstat-no" title="statement not covered" >                          computedLengthStatus: perimeterStatus, // 对于路径类型，长度状态与周长状态相同</span>
<span class="cstat-no" title="statement not covered" >                          computedAreaError: areaError,</span>
<span class="cstat-no" title="statement not covered" >                          computedPerimeterError: perimeterError,</span>
<span class="cstat-no" title="statement not covered" >                          computedLengthError: perimeterError, // 对于路径类型，长度错误与周长错误相同</span>
                          // 成本计算结果
<span class="cstat-no" title="statement not covered" >                          costTotal: cost,</span>
                          // Ensure UI update uses the correct cost properties
<span class="cstat-no" title="statement not covered" >                          costUnitPrice: unitCost,</span>
                          // 🔧 修复：不要覆盖用户设置的costBasis！保持原有的值
                          // costBasis: costType, // 这行是错误的！会覆盖用户设置
                          // 不要覆盖用户设置的乘数值！保持原有的 costMultiplierOrCount
                          // costMultiplierOrCount: quantity, // 这行是错误的！
<span class="cstat-no" title="statement not covered" >                        },</span>
<span class="cstat-no" title="statement not covered" >                      }</span>
<span class="cstat-no" title="statement not covered" >                    : s,</span>
<span class="cstat-no" title="statement not covered" >                )</span>
<span class="cstat-no" title="statement not covered" >                useShapesStore.getState().setShapesFromExternal(updatedShapes, currentSelectedIds)</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              catch (err) {</span>
<span class="cstat-no" title="statement not covered" >                console.warn('[CoreCoordinator][compute] 计算 area/perimeter/cost 失败', err)</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        })()</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    )</span>
&nbsp;
    // Shape Deletion
<span class="cstat-no" title="statement not covered" >    this.eventBus.subscribe(</span>
<span class="cstat-no" title="statement not covered" >      AppEventType.ShapeDeleteRequest,</span>
<span class="cstat-no" title="statement not covered" >      (event: BaseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (event.type === AppEventType.ShapeDeleteRequest) {</span>
<span class="cstat-no" title="statement not covered" >          const specificEvent = event as ShapeDeleteEvent</span>
<span class="cstat-no" title="statement not covered" >          this.logger.debug?.('CoreCoordinator received SHAPE_DELETE_REQUEST', specificEvent.payload)</span>
          // ShapeDeleteService has handleRequest(event: ShapeDeleteEvent)
<span class="cstat-no" title="statement not covered" >          if (specificEvent.payload !== null &amp;&amp; specificEvent.payload !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >            this.elementDeleteService.handleRequest(specificEvent)</span>
<span class="cstat-no" title="statement not covered" >              .catch((err: unknown) =&gt; this.handleError(err as Error))</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          else {</span>
<span class="cstat-no" title="statement not covered" >            this.logger.warn?.('SHAPE_DELETE_REQUEST received with invalid payload', specificEvent)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    )</span>
&nbsp;
    // Shape Selection
<span class="cstat-no" title="statement not covered" >    this.eventBus.subscribe(</span>
<span class="cstat-no" title="statement not covered" >      AppEventType.ShapeSelectRequest,</span>
<span class="cstat-no" title="statement not covered" >      (event: BaseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (event.type === AppEventType.ShapeSelectRequest) {</span>
          // ...原有ShapeSelectRequest处理逻辑...
<span class="cstat-no" title="statement not covered" >          this._handleShapeSelectRequest(event)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    )</span>
    // 新增：监听ElementSelectRequest，复用同一处理逻辑
<span class="cstat-no" title="statement not covered" >    this.eventBus.subscribe(</span>
<span class="cstat-no" title="statement not covered" >      AppEventType.ElementSelectRequest,</span>
<span class="cstat-no" title="statement not covered" >      (event: BaseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (event.type === AppEventType.ElementSelectRequest) {</span>
<span class="cstat-no" title="statement not covered" >          this._handleShapeSelectRequest(event)</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    )</span>
&nbsp;
    // Shape Creation Complete (add new shape to store)
<span class="cstat-no" title="statement not covered" >    this.eventBus.subscribe(</span>
<span class="cstat-no" title="statement not covered" >      AppEventType.ShapeCreateComplete,</span>
<span class="cstat-no" title="statement not covered" >      (event: BaseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[CoreCoordinator] ShapeCreateComplete event received:', event)</span>
<span class="cstat-no" title="statement not covered" >        if (event.type === AppEventType.ShapeCreateComplete) {</span>
          // Strict payload validation
<span class="cstat-no" title="statement not covered" >          if (event.payload !== null &amp;&amp; event.payload !== undefined &amp;&amp; typeof event.payload === 'object' &amp;&amp; 'shape' in event.payload) {</span>
<span class="cstat-no" title="statement not covered" >            const shape = (event.payload as { shape?: Record&lt;string, unknown&gt; }).shape</span>
<span class="cstat-no" title="statement not covered" >            if (shape !== null &amp;&amp; shape !== undefined &amp;&amp; typeof shape === 'object' &amp;&amp; typeof (shape as { id?: unknown }).id === 'string') {</span>
              // --- PATCH: push current shapes to past, clear future ---
<span class="cstat-no" title="statement not covered" >              const currentShapes = useShapesStore.getState().shapes</span>
<span class="cstat-no" title="statement not covered" >              this.past.push([...currentShapes])</span>
<span class="cstat-no" title="statement not covered" >              this.future = []</span>
              // Debug: log zustand singleton debug id
<span class="cstat-no" title="statement not covered" >              console.warn('[CoreCoordinator] useShapesStore singleton debug id:', (window as { __ZUSTAND_SHAPES_STORE_DEBUG_ID__?: string }).__ZUSTAND_SHAPES_STORE_DEBUG_ID__)</span>
&nbsp;
              // 获取当前选中的元素ID或使用事件中传递的选中IDs
<span class="cstat-no" title="statement not covered" >              let currentSelectedIds: string[] = []</span>
&nbsp;
              // 从事件payload中获取选中IDs（如果有）
<span class="cstat-no" title="statement not covered" >              if ('selectedIds' in event.payload &amp;&amp; Array.isArray(event.payload.selectedIds)) {</span>
<span class="cstat-no" title="statement not covered" >                currentSelectedIds = event.payload.selectedIds as string[]</span>
<span class="cstat-no" title="statement not covered" >                console.warn('[CoreCoordinator] Using selectedIds from event payload:', currentSelectedIds)</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              else {</span>
                // 否则从Zustand获取当前选中IDs
<span class="cstat-no" title="statement not covered" >                currentSelectedIds = useShapesStore.getState().selectedShapeIds</span>
<span class="cstat-no" title="statement not covered" >                console.warn('[CoreCoordinator] Using selectedIds from Zustand store:', currentSelectedIds)</span>
<span class="cstat-no" title="statement not covered" >              }</span>
&nbsp;
              // 新功能：确保新形状在同层中显示在最顶层
<span class="cstat-no" title="statement not covered" >              console.warn('[CoreCoordinator] Setting new shape to be on top of its layer')</span>
&nbsp;
              // 只在有图层信息时处理
<span class="cstat-no" title="statement not covered" >              const shapeWithTypes = shape as Record&lt;string, unknown&gt; &amp; {</span>
                majorCategory?: string
                zLevelId?: string
                minorCategory?: string
                id?: string
                intraLayerZIndex?: number
              }
<span class="cstat-no" title="statement not covered" >              if (shapeWithTypes.majorCategory !== null &amp;&amp; shapeWithTypes.majorCategory !== undefined</span>
<span class="cstat-no" title="statement not covered" >                &amp;&amp; shapeWithTypes.zLevelId !== null &amp;&amp; shapeWithTypes.zLevelId !== undefined) {</span>
                // 找到同一图层中的所有形状
<span class="cstat-no" title="statement not covered" >                const shapesInSameLayer = currentShapes.filter((s: ShapeModel) =&gt;</span>
<span class="cstat-no" title="statement not covered" >                  s.majorCategory === shapeWithTypes.majorCategory</span>
<span class="cstat-no" title="statement not covered" >                  &amp;&amp; s.minorCategory === shapeWithTypes.minorCategory</span>
<span class="cstat-no" title="statement not covered" >                  &amp;&amp; s.zLevelId === shapeWithTypes.zLevelId,</span>
<span class="cstat-no" title="statement not covered" >                )</span>
&nbsp;
                // 计算同层中的最大Z索引
<span class="cstat-no" title="statement not covered" >                let maxZIndex = 0</span>
<span class="cstat-no" title="statement not covered" >                if (shapesInSameLayer.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >                  maxZIndex = shapesInSameLayer.reduce(</span>
<span class="cstat-no" title="statement not covered" >                    (max: number, s: ShapeModel) =&gt; Math.max(max, s.intraLayerZIndex ?? 0),</span>
<span class="cstat-no" title="statement not covered" >                    0,</span>
<span class="cstat-no" title="statement not covered" >                  )</span>
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
                // 设置新形状的intraLayerZIndex为最大值+1
<span class="cstat-no" title="statement not covered" >                shapeWithTypes.intraLayerZIndex = maxZIndex + 1</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator] Set new shape ${String(shapeWithTypes.id)} intraLayerZIndex to ${maxZIndex + 1}`)</span>
<span class="cstat-no" title="statement not covered" >              }</span>
&nbsp;
              // 关键修复：将新形状添加到ShapeRepository中
<span class="cstat-no" title="statement not covered" >              console.warn('[CoreCoordinator] Adding shape to repository:', shape)</span>
<span class="cstat-no" title="statement not covered" >              this.repository.add(shape as unknown as ShapeModel)</span>
&nbsp;
              // 确保在repository中保持选中状态
<span class="cstat-no" title="statement not covered" >              this.repository.setSelectedIds(currentSelectedIds)</span>
&nbsp;
              // Merge new shape into store - 使用深拷贝避免引用问题
<span class="cstat-no" title="statement not covered" >              const newShapes = [...currentShapes.filter((s: ShapeModel) =&gt; s.id !== (shape as { id?: string }).id), { ...shape } as unknown as ShapeModel]</span>
&nbsp;
              // 使用现有的选中状态更新store
<span class="cstat-no" title="statement not covered" >              useShapesStore.getState().setShapesFromExternal(newShapes, currentSelectedIds)</span>
<span class="cstat-no" title="statement not covered" >              this.logger.info?.('[CoreCoordinator] ShapeCreateComplete: Added new shape to store.', shape)</span>
&nbsp;
              // 延迟重新发布SelectionChanged事件，确保UI更新选中状态
<span class="cstat-no" title="statement not covered" >              setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                console.warn('[CoreCoordinator] Re-publishing SelectionChanged after create to maintain selection:', currentSelectedIds)</span>
<span class="cstat-no" title="statement not covered" >                this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >                  type: AppEventType.SelectionChanged,</span>
<span class="cstat-no" title="statement not covered" >                  timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >                  payload: { selectedIds: currentSelectedIds },</span>
<span class="cstat-no" title="statement not covered" >                })</span>
<span class="cstat-no" title="statement not covered" >              }, 0)</span>
&nbsp;
              // 自动触发几何计算，确保新创建的元素立即显示计算结果
<span class="cstat-no" title="statement not covered" >              setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                const shapeId = (shape as { id: string }).id</span>
<span class="cstat-no" title="statement not covered" >                console.warn('[CoreCoordinator] Auto-triggering geometry calculation for newly created shape:', shapeId)</span>
<span class="cstat-no" title="statement not covered" >                this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >                  type: AppEventType.ComputeRequest,</span>
<span class="cstat-no" title="statement not covered" >                  timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >                  payload: {</span>
<span class="cstat-no" title="statement not covered" >                    shapeId,</span>
<span class="cstat-no" title="statement not covered" >                    operation: 'all',</span>
<span class="cstat-no" title="statement not covered" >                    source: 'CoreCoordinator-AutoCompute',</span>
<span class="cstat-no" title="statement not covered" >                  },</span>
<span class="cstat-no" title="statement not covered" >                })</span>
<span class="cstat-no" title="statement not covered" >              }, 50) // 🔧 修复：减少延迟，确保计算更快完成</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            else {</span>
<span class="cstat-no" title="statement not covered" >              this.logger.warn?.('[CoreCoordinator] ShapeCreateComplete event payload.shape is missing or invalid.', event.payload)</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          else {</span>
<span class="cstat-no" title="statement not covered" >            this.logger.error?.('[CoreCoordinator] ShapeCreateComplete event payload structure invalid. Expected { shape }, got:', event.payload)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    )</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.eventBus.subscribe(</span>
<span class="cstat-no" title="statement not covered" >      AppEventType.HistoryUndo,</span>
<span class="cstat-no" title="statement not covered" >      (event: BaseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[CoreCoordinator] HistoryUndo event received:', event)</span>
<span class="cstat-no" title="statement not covered" >        if (this.past.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >          const currentShapes = useShapesStore.getState().shapes</span>
<span class="cstat-no" title="statement not covered" >          this.future.push([...currentShapes])</span>
<span class="cstat-no" title="statement not covered" >          const prev = this.past.pop()</span>
<span class="cstat-no" title="statement not covered" >          if (prev) {</span>
<span class="cstat-no" title="statement not covered" >            console.warn(`[CoreCoordinator] Undo: restoring ${prev.length} shapes from previous state. Current shapes: ${currentShapes.length}`)</span>
<span class="cstat-no" title="statement not covered" >            useShapesStore.getState().setShapesFromExternal([...prev])</span>
<span class="cstat-no" title="statement not covered" >            this.logger.info?.('[CoreCoordinator] Undo: restored previous shapes snapshot.')</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        else {</span>
<span class="cstat-no" title="statement not covered" >          this.logger.info?.('[CoreCoordinator] Undo: no more history.')</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    )</span>
<span class="cstat-no" title="statement not covered" >    this.eventBus.subscribe(</span>
<span class="cstat-no" title="statement not covered" >      AppEventType.HistoryRedo,</span>
<span class="cstat-no" title="statement not covered" >      (event: BaseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[CoreCoordinator] HistoryRedo event received:', event)</span>
<span class="cstat-no" title="statement not covered" >        if (this.future.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >          const currentShapes = useShapesStore.getState().shapes</span>
<span class="cstat-no" title="statement not covered" >          this.past.push([...currentShapes])</span>
<span class="cstat-no" title="statement not covered" >          const next = this.future.pop()</span>
<span class="cstat-no" title="statement not covered" >          if (next) {</span>
<span class="cstat-no" title="statement not covered" >            useShapesStore.getState().setShapesFromExternal([...next])</span>
<span class="cstat-no" title="statement not covered" >            this.logger.info?.('[CoreCoordinator] Redo: restored next shapes snapshot.')</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        else {</span>
<span class="cstat-no" title="statement not covered" >          this.logger.info?.('[CoreCoordinator] Redo: no more future.')</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    )</span>
&nbsp;
    // Shape Editing Complete (add edit snapshot to history)
<span class="cstat-no" title="statement not covered" >    this.eventBus.subscribe(</span>
<span class="cstat-no" title="statement not covered" >      AppEventType.ShapeEditComplete,</span>
<span class="cstat-no" title="statement not covered" >      (event: BaseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[CoreCoordinator] ShapeEditComplete event received:', event)</span>
<span class="cstat-no" title="statement not covered" >        if (event.type === AppEventType.ShapeEditComplete) {</span>
<span class="cstat-no" title="statement not covered" >          if (event.payload !== null &amp;&amp; event.payload !== undefined &amp;&amp; typeof event.payload === 'object' &amp;&amp; 'shape' in event.payload) {</span>
<span class="cstat-no" title="statement not covered" >            const shape = (event.payload as { shape?: unknown }).shape</span>
<span class="cstat-no" title="statement not covered" >            if (shape !== null &amp;&amp; shape !== undefined &amp;&amp; typeof shape === 'object' &amp;&amp; 'id' in shape &amp;&amp; typeof (shape as { id: unknown }).id === 'string') {</span>
              // 不再保存状态到past数组，因为已在preEdit中保存
&nbsp;
              // 获取当前选中的元素ID或使用事件中传递的选中IDs
<span class="cstat-no" title="statement not covered" >              let currentSelectedIds: string[] = []</span>
&nbsp;
              // 从事件payload中获取选中IDs（如果有）
<span class="cstat-no" title="statement not covered" >              if ('selectedIds' in event.payload &amp;&amp; Array.isArray(event.payload.selectedIds)) {</span>
<span class="cstat-no" title="statement not covered" >                currentSelectedIds = event.payload.selectedIds as string[]</span>
<span class="cstat-no" title="statement not covered" >                console.warn('[CoreCoordinator] Using selectedIds from event payload:', currentSelectedIds)</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              else {</span>
                // 否则从Zustand获取当前选中IDs
<span class="cstat-no" title="statement not covered" >                currentSelectedIds = useShapesStore.getState().selectedShapeIds</span>
<span class="cstat-no" title="statement not covered" >                console.warn('[CoreCoordinator] Using selectedIds from Zustand store:', currentSelectedIds)</span>
<span class="cstat-no" title="statement not covered" >              }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              const currentShapes = useShapesStore.getState().shapes</span>
<span class="cstat-no" title="statement not covered" >              console.warn('[CoreCoordinator] Current selection before update:', currentSelectedIds)</span>
&nbsp;
              // 使用深拷贝更新形状，避免引用问题
<span class="cstat-no" title="statement not covered" >              const newShapes = currentShapes.map((s: ShapeModel) =&gt;</span>
<span class="cstat-no" title="statement not covered" >                s.id === (shape as { id: string }).id ? JSON.parse(JSON.stringify(shape)) as ShapeModel : s,</span>
<span class="cstat-no" title="statement not covered" >              )</span>
&nbsp;
              // 更新Zustand store，同时保留选中状态
<span class="cstat-no" title="statement not covered" >              useShapesStore.getState().setShapesFromExternal(newShapes, currentSelectedIds)</span>
&nbsp;
              // 确保repository中也更新形状
<span class="cstat-no" title="statement not covered" >              this.repository.update((shape as { id: string }).id, JSON.parse(JSON.stringify(shape)) as Partial&lt;Omit&lt;ShapeModel, 'id'&gt;&gt;)</span>
&nbsp;
              // 确保repository中的选中状态也一致 - 关键修复：在更新后重新设置选中状态
<span class="cstat-no" title="statement not covered" >              this.repository.setSelectedIds(currentSelectedIds)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              this.logger.info?.('[CoreCoordinator] ShapeEditComplete: Edited shape in store.', shape)</span>
&nbsp;
              // 延迟重新发布SelectionChanged事件，确保UI更新选中状态
<span class="cstat-no" title="statement not covered" >              setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                console.warn('[CoreCoordinator] Re-publishing SelectionChanged to maintain selection:', currentSelectedIds)</span>
<span class="cstat-no" title="statement not covered" >                this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >                  type: AppEventType.SelectionChanged,</span>
<span class="cstat-no" title="statement not covered" >                  timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >                  payload: { selectedIds: currentSelectedIds },</span>
<span class="cstat-no" title="statement not covered" >                })</span>
<span class="cstat-no" title="statement not covered" >              }, 0)</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            else {</span>
<span class="cstat-no" title="statement not covered" >              this.logger.warn?.('[CoreCoordinator] ShapeEditComplete event payload.shape is missing or invalid.', event.payload)</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          else {</span>
<span class="cstat-no" title="statement not covered" >            this.logger.error?.('[CoreCoordinator] ShapeEditComplete event payload structure invalid. Expected { shape }, got:', event.payload)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    )</span>
&nbsp;
    // Shape Deletion Complete (add delete snapshot to history)
<span class="cstat-no" title="statement not covered" >    this.eventBus.subscribe(</span>
<span class="cstat-no" title="statement not covered" >      AppEventType.ShapeDeleteComplete,</span>
<span class="cstat-no" title="statement not covered" >      (event: BaseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[CoreCoordinator] ShapeDeleteComplete event received:', event)</span>
<span class="cstat-no" title="statement not covered" >        if (event.type === AppEventType.ShapeDeleteComplete) {</span>
<span class="cstat-no" title="statement not covered" >          if (event.payload !== null &amp;&amp; event.payload !== undefined &amp;&amp; typeof event.payload === 'object' &amp;&amp; 'shapeId' in event.payload) {</span>
<span class="cstat-no" title="statement not covered" >            const shapeIdOrIds = (event.payload as { shapeId?: string | string[] }).shapeId</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (shapeIdOrIds === undefined || shapeIdOrIds === null) {</span>
<span class="cstat-no" title="statement not covered" >              this.logger.warn?.('[CoreCoordinator] ShapeDeleteComplete event payload.shapeId is undefined or null.', event.payload)</span>
<span class="cstat-no" title="statement not covered" >              return</span>
<span class="cstat-no" title="statement not covered" >            }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            const currentShapes = useShapesStore.getState().shapes</span>
&nbsp;
            // 不再保存状态到past数组，因为已在preDelete中保存
            // 现在执行实际的删除操作
<span class="cstat-no" title="statement not covered" >            if (Array.isArray(shapeIdOrIds)) {</span>
<span class="cstat-no" title="statement not covered" >              console.warn(`[CoreCoordinator] Deleting ${shapeIdOrIds.length} shapes from repository`)</span>
              // 从repository中删除这些形状
<span class="cstat-no" title="statement not covered" >              shapeIdOrIds.forEach((id) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                this.repository.remove(id)</span>
<span class="cstat-no" title="statement not covered" >              })</span>
              // 更新store中的形状列表
<span class="cstat-no" title="statement not covered" >              const newShapes = currentShapes.filter((s: ShapeModel) =&gt; !shapeIdOrIds.includes(s.id))</span>
<span class="cstat-no" title="statement not covered" >              useShapesStore.getState().setShapesFromExternal(newShapes)</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            else if (typeof shapeIdOrIds === 'string') {</span>
<span class="cstat-no" title="statement not covered" >              console.warn(`[CoreCoordinator] Deleting single shape ${shapeIdOrIds} from repository`)</span>
              // 从repository删除单个形状
<span class="cstat-no" title="statement not covered" >              this.repository.remove(shapeIdOrIds)</span>
              // 更新store中的形状列表
<span class="cstat-no" title="statement not covered" >              const newShapes = currentShapes.filter((s: ShapeModel) =&gt; s.id !== shapeIdOrIds)</span>
<span class="cstat-no" title="statement not covered" >              useShapesStore.getState().setShapesFromExternal(newShapes)</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            else {</span>
<span class="cstat-no" title="statement not covered" >              this.logger.warn?.('[CoreCoordinator] ShapeDeleteComplete event payload.shapeId has invalid type.', event.payload)</span>
<span class="cstat-no" title="statement not covered" >              return</span>
<span class="cstat-no" title="statement not covered" >            }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            this.logger.info?.(</span>
<span class="cstat-no" title="statement not covered" >              '[CoreCoordinator] Completed deletion of shapes from repository.',</span>
<span class="cstat-no" title="statement not covered" >              { shapeIdOrIds },</span>
<span class="cstat-no" title="statement not covered" >            )</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          else {</span>
<span class="cstat-no" title="statement not covered" >            this.logger.error?.('[CoreCoordinator] ShapeDeleteComplete event payload structure invalid. Expected { shapeId }, got:', event.payload)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    )</span>
&nbsp;
    // Selection Changed (update selectedShapeIds in store)
<span class="cstat-no" title="statement not covered" >    this.eventBus.subscribe(</span>
<span class="cstat-no" title="statement not covered" >      AppEventType.SelectionChanged,</span>
<span class="cstat-no" title="statement not covered" >      (event: BaseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[CoreCoordinator] SelectionChanged event received:', event)</span>
<span class="cstat-no" title="statement not covered" >        if (event.type === AppEventType.SelectionChanged) {</span>
<span class="cstat-no" title="statement not covered" >          if (event.payload !== null &amp;&amp; event.payload !== undefined &amp;&amp; typeof event.payload === 'object' &amp;&amp; 'selectedIds' in event.payload) {</span>
<span class="cstat-no" title="statement not covered" >            const selectedShapeIds = (event.payload as { selectedIds?: string[] }).selectedIds</span>
<span class="cstat-no" title="statement not covered" >            if (Array.isArray(selectedShapeIds)) {</span>
              // Only update selectedShapeIds, keep shapes unchanged
<span class="cstat-no" title="statement not covered" >              const currentShapes = useShapesStore.getState().shapes</span>
<span class="cstat-no" title="statement not covered" >              useShapesStore.getState().setShapesFromExternal(currentShapes, selectedShapeIds)</span>
<span class="cstat-no" title="statement not covered" >              this.logger.info?.('[CoreCoordinator] SelectionChanged: Updated selectedShapeIds in store.', selectedShapeIds)</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            else {</span>
<span class="cstat-no" title="statement not covered" >              this.logger.warn?.('[CoreCoordinator] SelectionChanged event payload.selectedIds is missing or invalid.', event.payload)</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          else {</span>
<span class="cstat-no" title="statement not covered" >            this.logger.error?.('[CoreCoordinator] SelectionChanged event payload structure invalid. Expected { selectedIds }, got:', event.payload)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    )</span>
&nbsp;
    // 添加对StateUpdated事件的处理，特别是处理删除操作前的状态保存
<span class="cstat-no" title="statement not covered" >    this.eventBus.subscribe(</span>
<span class="cstat-no" title="statement not covered" >      AppEventType.StateUpdated,</span>
<span class="cstat-no" title="statement not covered" >      (event: BaseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[CoreCoordinator] StateUpdated event received:', event)</span>
<span class="cstat-no" title="statement not covered" >        if (event.type === AppEventType.StateUpdated) {</span>
<span class="cstat-no" title="statement not covered" >          if (event.payload !== null &amp;&amp; event.payload !== undefined &amp;&amp; typeof event.payload === 'object') {</span>
            // 如果是删除前的状态更新，保存当前状态到历史记录
<span class="cstat-no" title="statement not covered" >            if ('action' in event.payload) {</span>
<span class="cstat-no" title="statement not covered" >              if (event.payload.action === 'preDelete') {</span>
<span class="cstat-no" title="statement not covered" >                console.warn('[CoreCoordinator] Saving state before delete operation')</span>
<span class="cstat-no" title="statement not covered" >                const currentShapes = useShapesStore.getState().shapes</span>
                // 保存当前状态到past数组，清空future数组
<span class="cstat-no" title="statement not covered" >                this.past.push([...currentShapes])</span>
<span class="cstat-no" title="statement not covered" >                this.future = []</span>
<span class="cstat-no" title="statement not covered" >                this.logger.info?.('[CoreCoordinator] Pre-delete state saved to history.', { elementCount: currentShapes.length })</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              else if (event.payload.action === 'preEdit') {</span>
<span class="cstat-no" title="statement not covered" >                console.warn('[CoreCoordinator] Saving state before edit operation')</span>
<span class="cstat-no" title="statement not covered" >                const currentShapes = useShapesStore.getState().shapes</span>
                // 保存当前状态到past数组，清空future数组
<span class="cstat-no" title="statement not covered" >                this.past.push([...currentShapes])</span>
<span class="cstat-no" title="statement not covered" >                this.future = []</span>
<span class="cstat-no" title="statement not covered" >                this.logger.info?.('[CoreCoordinator] Pre-edit state saved to history.', { elementCount: currentShapes.length, elementIds: 'elementIds' in event.payload ? event.payload.elementIds : 'unknown' })</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    )</span>
&nbsp;
    // 处理形状置顶请求
<span class="cstat-no" title="statement not covered" >    this.eventBus.subscribe(</span>
<span class="cstat-no" title="statement not covered" >      AppEventType.ShapeBringToFrontRequest,</span>
<span class="cstat-no" title="statement not covered" >      (event: BaseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (event.type === AppEventType.ShapeBringToFrontRequest) {</span>
<span class="cstat-no" title="statement not covered" >          console.warn('[CoreCoordinator] ShapeBringToFrontRequest event received:', event)</span>
&nbsp;
          // 提取形状ID
<span class="cstat-no" title="statement not covered" >          const shapeId = event.payload !== null &amp;&amp; event.payload !== undefined &amp;&amp; typeof event.payload === 'object' &amp;&amp; 'shapeId' in event.payload</span>
<span class="cstat-no" title="statement not covered" >            ? (event.payload as { shapeId: string }).shapeId</span>
<span class="cstat-no" title="statement not covered" >            : undefined</span>
<span class="cstat-no" title="statement not covered" >          if (shapeId === null || shapeId === undefined || shapeId === '') {</span>
<span class="cstat-no" title="statement not covered" >            console.error('[CoreCoordinator] ShapeBringToFrontRequest: Missing shapeId in payload')</span>
<span class="cstat-no" title="statement not covered" >            return</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
          // 保存当前选择状态
<span class="cstat-no" title="statement not covered" >          const currentSelectedIds = Array.from(this.repository.getSelectedIds())</span>
<span class="cstat-no" title="statement not covered" >          console.warn('[CoreCoordinator] Current selection before bringToFront:', currentSelectedIds)</span>
&nbsp;
          // 执行置顶操作
<span class="cstat-no" title="statement not covered" >          try {</span>
            // 直接调用repository的方法，不再使用类型断言
<span class="cstat-no" title="statement not covered" >            const updatedShape = this.repository.bringToFrontInLayer(shapeId)</span>
<span class="cstat-no" title="statement not covered" >            if (updatedShape) {</span>
<span class="cstat-no" title="statement not covered" >              console.warn(`[CoreCoordinator] Successfully brought shape ${shapeId} to front in layer, new intraLayerZIndex: ${updatedShape.intraLayerZIndex}`)</span>
&nbsp;
              // 确保选择状态保持不变
<span class="cstat-no" title="statement not covered" >              this.repository.setSelectedIds(currentSelectedIds)</span>
&nbsp;
              // 发布完成事件
<span class="cstat-no" title="statement not covered" >              this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >                type: AppEventType.ShapeBringToFrontComplete,</span>
<span class="cstat-no" title="statement not covered" >                timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >                payload: {</span>
<span class="cstat-no" title="statement not covered" >                  shape: updatedShape,</span>
<span class="cstat-no" title="statement not covered" >                  selectedIds: currentSelectedIds,</span>
<span class="cstat-no" title="statement not covered" >                  source: 'CoreCoordinator',</span>
<span class="cstat-no" title="statement not covered" >                },</span>
<span class="cstat-no" title="statement not covered" >              })</span>
&nbsp;
              // 更新Zustand store
<span class="cstat-no" title="statement not covered" >              this.publishShapesUpdate()</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            else {</span>
<span class="cstat-no" title="statement not covered" >              console.error(`[CoreCoordinator] Failed to bring shape ${shapeId} to front in layer`)</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          catch (error) {</span>
<span class="cstat-no" title="statement not covered" >            console.error(`[CoreCoordinator] Error bringing shape ${shapeId} to front:`, error)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    )</span>
&nbsp;
    // 处理计算请求
<span class="cstat-no" title="statement not covered" >    this.eventBus.subscribe(</span>
<span class="cstat-no" title="statement not covered" >      AppEventType.ComputeRequest,</span>
<span class="cstat-no" title="statement not covered" >      (event: BaseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        void (async () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >          if (event.type === AppEventType.ComputeRequest) {</span>
<span class="cstat-no" title="statement not covered" >            console.warn('[CoreCoordinator] ComputeRequest event received:', event)</span>
&nbsp;
            // 检查payload是否有效
<span class="cstat-no" title="statement not covered" >            if (event.payload === null || event.payload === undefined || typeof event.payload !== 'object') {</span>
<span class="cstat-no" title="statement not covered" >              console.error('[CoreCoordinator] ComputeRequest: Invalid payload', event.payload)</span>
<span class="cstat-no" title="statement not covered" >              return</span>
<span class="cstat-no" title="statement not covered" >            }</span>
&nbsp;
            // 提取形状ID
<span class="cstat-no" title="statement not covered" >            const payloadWithShapeId = event.payload as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >            const shapeId = 'shapeId' in payloadWithShapeId ? payloadWithShapeId.shapeId : undefined</span>
<span class="cstat-no" title="statement not covered" >            if (shapeId === null || shapeId === undefined || typeof shapeId !== 'string') {</span>
<span class="cstat-no" title="statement not covered" >              console.error('[CoreCoordinator] ComputeRequest: Missing shapeId in payload')</span>
<span class="cstat-no" title="statement not covered" >              return</span>
<span class="cstat-no" title="statement not covered" >            }</span>
&nbsp;
            // 提取操作类型
<span class="cstat-no" title="statement not covered" >            const operation = 'operation' in payloadWithShapeId ? payloadWithShapeId.operation : undefined</span>
<span class="cstat-no" title="statement not covered" >            if (operation === null || operation === undefined || typeof operation !== 'string') {</span>
<span class="cstat-no" title="statement not covered" >              console.error('[CoreCoordinator] ComputeRequest: Missing operation in payload')</span>
<span class="cstat-no" title="statement not covered" >              return</span>
<span class="cstat-no" title="statement not covered" >            }</span>
&nbsp;
            // 保存当前选择状态
<span class="cstat-no" title="statement not covered" >            const currentSelectedIds = Array.from(this.repository.getSelectedIds())</span>
&nbsp;
            // 处理单独的面积计算请求
<span class="cstat-no" title="statement not covered" >            if (operation === 'area' &amp;&amp; this.computeFacade) {</span>
<span class="cstat-no" title="statement not covered" >              try {</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][computeRequest] 开始计算形状 ${shapeId} 的面积...`)</span>
&nbsp;
                // 获取最新的shape对象
<span class="cstat-no" title="statement not covered" >                const shapeToCompute = this.repository.getById(shapeId)</span>
<span class="cstat-no" title="statement not covered" >                if (!shapeToCompute) {</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator][computeRequest] 无法找到形状 ${shapeId}`)</span>
<span class="cstat-no" title="statement not covered" >                  return</span>
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
                // 计算面积
<span class="cstat-no" title="statement not covered" >                const area = await this.computeFacade.computeArea(shapeId)</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][computeRequest] 面积计算成功: ${area}`)</span>
&nbsp;
                // 更新形状的面积属性
<span class="cstat-no" title="statement not covered" >                const shapeProps = shapeToCompute.properties || {}</span>
<span class="cstat-no" title="statement not covered" >                const updatedProps = {</span>
<span class="cstat-no" title="statement not covered" >                  ...shapeProps,</span>
<span class="cstat-no" title="statement not covered" >                  computedArea: area,</span>
<span class="cstat-no" title="statement not covered" >                  computedAreaUnit: 'mm²',</span>
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
                // 更新形状
<span class="cstat-no" title="statement not covered" >                this.repository.update(shapeId, { properties: updatedProps })</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][computeRequest] 已更新形状 ${shapeId} 的面积属性`)</span>
&nbsp;
                // 触发UI层数据刷新
<span class="cstat-no" title="statement not covered" >                const storeShapes = useShapesStore.getState().shapes</span>
<span class="cstat-no" title="statement not covered" >                const updatedShapes = storeShapes.map((s) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                  if (s.id === shapeId) {</span>
<span class="cstat-no" title="statement not covered" >                    return {</span>
<span class="cstat-no" title="statement not covered" >                      ...s,</span>
<span class="cstat-no" title="statement not covered" >                      properties: {</span>
<span class="cstat-no" title="statement not covered" >                        ...s.properties,</span>
<span class="cstat-no" title="statement not covered" >                        computedArea: area,</span>
<span class="cstat-no" title="statement not covered" >                        computedAreaUnit: 'mm²',</span>
<span class="cstat-no" title="statement not covered" >                      },</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                  return s</span>
<span class="cstat-no" title="statement not covered" >                })</span>
<span class="cstat-no" title="statement not covered" >                useShapesStore.getState().setShapesFromExternal(updatedShapes, currentSelectedIds)</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              catch (err) {</span>
<span class="cstat-no" title="statement not covered" >                console.warn('[CoreCoordinator][computeRequest] 计算面积失败', err)</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >            }</span>
            // 处理单独的周长计算请求
<span class="cstat-no" title="statement not covered" >            else if (operation === 'perimeter' &amp;&amp; this.computeFacade) {</span>
<span class="cstat-no" title="statement not covered" >              try {</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][computeRequest] 开始计算形状 ${shapeId} 的周长...`)</span>
&nbsp;
                // 获取最新的shape对象
<span class="cstat-no" title="statement not covered" >                const shapeToCompute = this.repository.getById(shapeId)</span>
<span class="cstat-no" title="statement not covered" >                if (!shapeToCompute) {</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator][computeRequest] 无法找到形状 ${shapeId}`)</span>
<span class="cstat-no" title="statement not covered" >                  return</span>
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
                // 计算周长
<span class="cstat-no" title="statement not covered" >                const perimeter = await this.computeFacade.computePerimeter(shapeId)</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][computeRequest] 周长计算成功: ${perimeter}`)</span>
&nbsp;
                // 更新形状的周长属性
<span class="cstat-no" title="statement not covered" >                const shapeProps = shapeToCompute.properties || {}</span>
<span class="cstat-no" title="statement not covered" >                const updatedProps = {</span>
<span class="cstat-no" title="statement not covered" >                  ...shapeProps,</span>
<span class="cstat-no" title="statement not covered" >                  computedPerimeter: perimeter,</span>
<span class="cstat-no" title="statement not covered" >                  computedPerimeterUnit: 'mm',</span>
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
                // 更新形状
<span class="cstat-no" title="statement not covered" >                this.repository.update(shapeId, { properties: updatedProps })</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][computeRequest] 已更新形状 ${shapeId} 的周长属性`)</span>
&nbsp;
                // 触发UI层数据刷新
<span class="cstat-no" title="statement not covered" >                const storeShapes = useShapesStore.getState().shapes</span>
<span class="cstat-no" title="statement not covered" >                const updatedShapes = storeShapes.map((s) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                  if (s.id === shapeId) {</span>
<span class="cstat-no" title="statement not covered" >                    return {</span>
<span class="cstat-no" title="statement not covered" >                      ...s,</span>
<span class="cstat-no" title="statement not covered" >                      properties: {</span>
<span class="cstat-no" title="statement not covered" >                        ...s.properties,</span>
<span class="cstat-no" title="statement not covered" >                        computedPerimeter: perimeter,</span>
<span class="cstat-no" title="statement not covered" >                        computedPerimeterUnit: 'mm',</span>
<span class="cstat-no" title="statement not covered" >                      },</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                  return s</span>
<span class="cstat-no" title="statement not covered" >                })</span>
<span class="cstat-no" title="statement not covered" >                useShapesStore.getState().setShapesFromExternal(updatedShapes, currentSelectedIds)</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              catch (err) {</span>
<span class="cstat-no" title="statement not covered" >                console.warn('[CoreCoordinator][computeRequest] 计算周长失败', err)</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >            }</span>
            // 处理成本计算请求
<span class="cstat-no" title="statement not covered" >            else if (operation === 'cost' &amp;&amp; this.computeFacade) {</span>
<span class="cstat-no" title="statement not covered" >              try {</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][computeRequest] 开始计算形状 ${shapeId} 的成本...`)</span>
&nbsp;
                // 获取最新的shape对象
<span class="cstat-no" title="statement not covered" >                const shapeToCompute = this.repository.getById(shapeId)</span>
<span class="cstat-no" title="statement not covered" >                if (!shapeToCompute) {</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator][computeRequest] 无法找到形状 ${shapeId}`)</span>
<span class="cstat-no" title="statement not covered" >                  return</span>
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
                // 检查是否有costParams参数
<span class="cstat-no" title="statement not covered" >                const costParams = 'costParams' in payloadWithShapeId ? payloadWithShapeId.costParams : undefined</span>
&nbsp;
                // 获取成本计算参数
<span class="cstat-no" title="statement not covered" >                let unitCost = 1</span>
<span class="cstat-no" title="statement not covered" >                let costType: 'area' | 'perimeter' | 'unit' | 'segment' | 'fixed' = 'unit'</span>
<span class="cstat-no" title="statement not covered" >                let quantity = 0</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                if (costParams !== null &amp;&amp; costParams !== undefined &amp;&amp; typeof costParams === 'object') {</span>
<span class="cstat-no" title="statement not covered" >                  const costParamsObj = costParams as Record&lt;string, unknown&gt;</span>
                  // 使用传入的参数
<span class="cstat-no" title="statement not covered" >                  unitCost = typeof costParamsObj.costUnitPrice === 'number' ? costParamsObj.costUnitPrice : 1</span>
&nbsp;
                  // 将costBasis映射到costType
<span class="cstat-no" title="statement not covered" >                  if (typeof costParamsObj.costBasis === 'string') {</span>
<span class="cstat-no" title="statement not covered" >                    switch (costParamsObj.costBasis) {</span>
<span class="cstat-no" title="statement not covered" >                      case 'area':</span>
<span class="cstat-no" title="statement not covered" >                      case 'perimeter':</span>
<span class="cstat-no" title="statement not covered" >                      case 'unit':</span>
<span class="cstat-no" title="statement not covered" >                        costType = costParamsObj.costBasis</span>
<span class="cstat-no" title="statement not covered" >                        break</span>
<span class="cstat-no" title="statement not covered" >                      default:</span>
<span class="cstat-no" title="statement not covered" >                        costType = 'unit'</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                  quantity = typeof costParamsObj.costMultiplierOrCount === 'number' ? costParamsObj.costMultiplierOrCount : 0</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >                else {</span>
                  // 使用shape的属性
<span class="cstat-no" title="statement not covered" >                  const shapeProps = shapeToCompute.properties || {}</span>
<span class="cstat-no" title="statement not covered" >                  unitCost = typeof shapeProps.costUnitPrice === 'number' ? shapeProps.costUnitPrice : 1</span>
&nbsp;
                  // 将costBasis映射到costType
<span class="cstat-no" title="statement not covered" >                  if (typeof shapeProps.costBasis === 'string') {</span>
<span class="cstat-no" title="statement not covered" >                    switch (shapeProps.costBasis) {</span>
<span class="cstat-no" title="statement not covered" >                      case 'area':</span>
<span class="cstat-no" title="statement not covered" >                      case 'perimeter':</span>
<span class="cstat-no" title="statement not covered" >                      case 'unit':</span>
<span class="cstat-no" title="statement not covered" >                        costType = shapeProps.costBasis</span>
<span class="cstat-no" title="statement not covered" >                        break</span>
<span class="cstat-no" title="statement not covered" >                      default:</span>
<span class="cstat-no" title="statement not covered" >                        costType = 'unit'</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                  quantity = typeof shapeProps.costMultiplierOrCount === 'number' ? shapeProps.costMultiplierOrCount : 0</span>
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
                // 组装计算选项
<span class="cstat-no" title="statement not covered" >                const options = { costType, quantity }</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][computeRequest] 计算选项: unitCost=${unitCost}, costType=${costType}, quantity=${quantity}`)</span>
&nbsp;
                // 计算成本
<span class="cstat-no" title="statement not covered" >                let cost = 0</span>
<span class="cstat-no" title="statement not covered" >                try {</span>
<span class="cstat-no" title="statement not covered" >                  cost = await this.computeFacade.computeCost(shapeId, unitCost, options)</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator][computeRequest] 成本计算成功: ${cost}`)</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >                catch (costErr) {</span>
<span class="cstat-no" title="statement not covered" >                  console.error(`[CoreCoordinator][computeRequest] 成本计算失败:`, costErr)</span>
                  // 不要直接返回，而是使用基本计算
                  // 基于costType计算基础值
<span class="cstat-no" title="statement not covered" >                  let baseValue = 0 // 默认为0，避免意外的成本计算</span>
<span class="cstat-no" title="statement not covered" >                  if (costType === 'area') {</span>
<span class="cstat-no" title="statement not covered" >                    try {</span>
<span class="cstat-no" title="statement not covered" >                      baseValue = await this.computeFacade.computeArea(shapeId)</span>
<span class="cstat-no" title="statement not covered" >                      console.warn(`[CoreCoordinator][computeRequest] 面积计算成功: ${baseValue}`)</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                    catch (areaErr) {</span>
<span class="cstat-no" title="statement not covered" >                      console.error(`[CoreCoordinator][computeRequest] 面积计算失败:`, areaErr)</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                  else if (costType === 'perimeter') {</span>
<span class="cstat-no" title="statement not covered" >                    try {</span>
<span class="cstat-no" title="statement not covered" >                      baseValue = await this.computeFacade.computePerimeter(shapeId)</span>
<span class="cstat-no" title="statement not covered" >                      console.warn(`[CoreCoordinator][computeRequest] 周长计算成功: ${baseValue}`)</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                    catch (perimeterErr) {</span>
<span class="cstat-no" title="statement not covered" >                      console.error(`[CoreCoordinator][computeRequest] 周长计算失败:`, perimeterErr)</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
                  // 计算总成本
<span class="cstat-no" title="statement not covered" >                  cost = unitCost * baseValue * quantity</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator][computeRequest] 手动计算成本: ${unitCost} * ${baseValue} * ${quantity} = ${cost}`)</span>
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
                // 成本可以为0（当乘数为0时）
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][computeRequest] 最终计算成本: ${cost}`)</span>
&nbsp;
                // 更新形状的costTotal属性
<span class="cstat-no" title="statement not covered" >                const shapeProps = shapeToCompute.properties || {}</span>
&nbsp;
                // 保存关键几何属性，确保不会被覆盖
<span class="cstat-no" title="statement not covered" >                const { radius, width, height } = shapeProps</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                const updatedProps = {</span>
<span class="cstat-no" title="statement not covered" >                  ...shapeProps,</span>
<span class="cstat-no" title="statement not covered" >                  costTotal: cost,</span>
<span class="cstat-no" title="statement not covered" >                  costUnitPrice: unitCost,</span>
                  // 🔧 修复：不要覆盖用户设置的costBasis！保持原有的值
                  // costBasis: costType, // 这行是错误的！会覆盖用户设置
                  // 不要覆盖用户设置的乘数值！保持原有的 costMultiplierOrCount
                  // costMultiplierOrCount: quantity, // 这行是错误的！
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
                // 如果是圆形元素，确保保留原始的半径和直径
<span class="cstat-no" title="statement not covered" >                if (shapeToCompute.type === 'circle' &amp;&amp; radius !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >                  console.warn(`[CoreCoordinator][computeRequest] 保留圆形元素的原始半径: ${String(radius)}和直径: ${String(width)}x${String(height)}`)</span>
&nbsp;
                  // 使用类型断言来避免TypeScript错误
                  // 确保半径和直径值保持一致关系：直径 = 2 * 半径
<span class="cstat-no" title="statement not covered" >                  const propsWithGeometry = updatedProps as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >                  propsWithGeometry.radius = radius</span>
<span class="cstat-no" title="statement not covered" >                  const diameter = (typeof radius === 'number' ? radius : 0) * 2</span>
<span class="cstat-no" title="statement not covered" >                  if (width !== undefined)</span>
<span class="cstat-no" title="statement not covered" >                    propsWithGeometry.width = diameter</span>
<span class="cstat-no" title="statement not covered" >                  if (height !== undefined)</span>
<span class="cstat-no" title="statement not covered" >                    propsWithGeometry.height = diameter</span>
&nbsp;
                  // 确保properties存在
<span class="cstat-no" title="statement not covered" >                  if (propsWithGeometry.properties === null || propsWithGeometry.properties === undefined || typeof propsWithGeometry.properties !== 'object')</span>
<span class="cstat-no" title="statement not covered" >                    propsWithGeometry.properties = {}</span>
&nbsp;
                  // 更新properties中的几何属性
<span class="cstat-no" title="statement not covered" >                  const propertiesObj = propsWithGeometry.properties as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >                  propertiesObj.radius = radius</span>
<span class="cstat-no" title="statement not covered" >                  propertiesObj.width = diameter</span>
<span class="cstat-no" title="statement not covered" >                  propertiesObj.height = diameter</span>
<span class="cstat-no" title="statement not covered" >                }</span>
&nbsp;
                // 更新形状
<span class="cstat-no" title="statement not covered" >                this.repository.update(shapeId, { properties: updatedProps })</span>
<span class="cstat-no" title="statement not covered" >                console.warn(`[CoreCoordinator][computeRequest] 已更新形状 ${shapeId} 的属性`)</span>
&nbsp;
                // 触发UI层数据刷新
<span class="cstat-no" title="statement not covered" >                const storeShapes = useShapesStore.getState().shapes</span>
<span class="cstat-no" title="statement not covered" >                const updatedShapes = storeShapes.map((s) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                  if (s.id === shapeId) {</span>
                    // 创建更新后的属性对象
<span class="cstat-no" title="statement not covered" >                    const updatedProperties = {</span>
<span class="cstat-no" title="statement not covered" >                      ...s.properties,</span>
<span class="cstat-no" title="statement not covered" >                      costTotal: cost,</span>
<span class="cstat-no" title="statement not covered" >                      costUnitPrice: unitCost,</span>
                      // 🔧 修复：不要覆盖用户设置的costBasis！保持原有的值
                      // costBasis: costType, // 这行是错误的！会覆盖用户设置
                      // 不要覆盖用户设置的乘数值！保持原有的 costMultiplierOrCount
                      // costMultiplierOrCount: quantity, // 这行是错误的！
<span class="cstat-no" title="statement not covered" >                    }</span>
&nbsp;
                    // 如果是圆形元素，确保保留原始的半径和直径
<span class="cstat-no" title="statement not covered" >                    if (s.type === 'circle' &amp;&amp; radius !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >                      console.warn(`[CoreCoordinator][UI更新] 保留圆形元素的原始半径: ${String(radius)}和直径: ${String(width)}x${String(height)}`)</span>
<span class="cstat-no" title="statement not covered" >                      const propertiesWithGeometry = updatedProperties as Record&lt;string, unknown&gt;</span>
<span class="cstat-no" title="statement not covered" >                      propertiesWithGeometry.radius = radius</span>
<span class="cstat-no" title="statement not covered" >                      if (width !== undefined)</span>
<span class="cstat-no" title="statement not covered" >                        propertiesWithGeometry.width = width</span>
<span class="cstat-no" title="statement not covered" >                      if (height !== undefined)</span>
<span class="cstat-no" title="statement not covered" >                        propertiesWithGeometry.height = height</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                    return {</span>
<span class="cstat-no" title="statement not covered" >                      ...s,</span>
<span class="cstat-no" title="statement not covered" >                      properties: updatedProperties,</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                  return s</span>
<span class="cstat-no" title="statement not covered" >                })</span>
<span class="cstat-no" title="statement not covered" >                useShapesStore.getState().setShapesFromExternal(updatedShapes, currentSelectedIds)</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >              catch (err) {</span>
<span class="cstat-no" title="statement not covered" >                console.warn('[CoreCoordinator][computeRequest] 计算成本失败', err)</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        })()</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    )</span>
&nbsp;
    // 处理形状置底请求
<span class="cstat-no" title="statement not covered" >    this.eventBus.subscribe(</span>
<span class="cstat-no" title="statement not covered" >      AppEventType.ShapeSendToBackRequest,</span>
<span class="cstat-no" title="statement not covered" >      (event: BaseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (event.type === AppEventType.ShapeSendToBackRequest) {</span>
<span class="cstat-no" title="statement not covered" >          console.warn('[CoreCoordinator] ShapeSendToBackRequest event received:', event)</span>
&nbsp;
          // 提取形状ID
<span class="cstat-no" title="statement not covered" >          const shapeId = event.payload !== null &amp;&amp; event.payload !== undefined &amp;&amp; typeof event.payload === 'object' &amp;&amp; 'shapeId' in event.payload</span>
<span class="cstat-no" title="statement not covered" >            ? (event.payload as { shapeId: string }).shapeId</span>
<span class="cstat-no" title="statement not covered" >            : undefined</span>
<span class="cstat-no" title="statement not covered" >          if (shapeId === null || shapeId === undefined || shapeId === '') {</span>
<span class="cstat-no" title="statement not covered" >            console.error('[CoreCoordinator] ShapeSendToBackRequest: Missing shapeId in payload')</span>
<span class="cstat-no" title="statement not covered" >            return</span>
<span class="cstat-no" title="statement not covered" >          }</span>
&nbsp;
          // 保存当前选择状态
<span class="cstat-no" title="statement not covered" >          const currentSelectedIds = Array.from(this.repository.getSelectedIds())</span>
<span class="cstat-no" title="statement not covered" >          console.warn('[CoreCoordinator] Current selection before sendToBack:', currentSelectedIds)</span>
&nbsp;
          // 执行置底操作
<span class="cstat-no" title="statement not covered" >          try {</span>
            // 直接调用repository的方法，不再使用类型断言
<span class="cstat-no" title="statement not covered" >            const updatedShape = this.repository.sendToBackInLayer(shapeId)</span>
<span class="cstat-no" title="statement not covered" >            if (updatedShape) {</span>
<span class="cstat-no" title="statement not covered" >              console.warn(`[CoreCoordinator] Successfully sent shape ${shapeId} to back in layer, new intraLayerZIndex: ${updatedShape.intraLayerZIndex}`)</span>
&nbsp;
              // 确保选择状态保持不变
<span class="cstat-no" title="statement not covered" >              this.repository.setSelectedIds(currentSelectedIds)</span>
&nbsp;
              // 发布完成事件
<span class="cstat-no" title="statement not covered" >              this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >                type: AppEventType.ShapeSendToBackComplete,</span>
<span class="cstat-no" title="statement not covered" >                timestamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >                payload: {</span>
<span class="cstat-no" title="statement not covered" >                  shape: updatedShape,</span>
<span class="cstat-no" title="statement not covered" >                  selectedIds: currentSelectedIds,</span>
<span class="cstat-no" title="statement not covered" >                  source: 'CoreCoordinator',</span>
<span class="cstat-no" title="statement not covered" >                },</span>
<span class="cstat-no" title="statement not covered" >              })</span>
&nbsp;
              // 更新Zustand store
<span class="cstat-no" title="statement not covered" >              this.publishShapesUpdate()</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            else {</span>
<span class="cstat-no" title="statement not covered" >              console.error(`[CoreCoordinator] Failed to send shape ${shapeId} to back in layer`)</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          catch (error) {</span>
<span class="cstat-no" title="statement not covered" >            console.error(`[CoreCoordinator] Error sending shape ${shapeId} to back:`, error)</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >    )</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.logger.info('CoreCoordinator event handlers registered.')</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // --- Repository Accessor Methods (Provide controlled access to repository state) ---
&nbsp;
  /**
   * Retrieves a single shape model by its ID from the repository.
   *
   * @param id - The ID of the shape to retrieve.
   * @returns The shape model if found, otherwise `undefined`.
   */
<span class="cstat-no" title="statement not covered" >  public getShapeById(id: string): ShapeModel | undefined {</span>
<span class="cstat-no" title="statement not covered" >    return this.repository.getById(id)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Retrieves all shape models currently stored in the repository.
   *
   * @returns An array containing all shape models.
   */
<span class="cstat-no" title="statement not covered" >  public getAllShapes(): ShapeModel[] {</span>
<span class="cstat-no" title="statement not covered" >    return this.repository.getAll()</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Retrieves the IDs of all currently selected shapes from the repository.
   *
   * @returns A `Set` containing the IDs of the selected shapes.
   */
<span class="cstat-no" title="statement not covered" >  public getSelectedShapeIds(): Set&lt;string&gt; {</span>
<span class="cstat-no" title="statement not covered" >    return this.repository.getSelectedIds()</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Uses the validator and core resources to check shape model validity.
   *
   * @remarks
   * Throws an appropriate CoreError if validation fails.
   *
   * @param model - The shape model to validate.
   * @returns Promise that resolves to true if the model is valid.
   * @throws CoreError if validation fails or an unexpected error occurs.
   */
<span class="cstat-no" title="statement not covered" >  public async validateShape(model: ShapeModel): Promise&lt;boolean&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (model?.id === null || model?.id === undefined || model?.id === '') {</span>
<span class="cstat-no" title="statement not covered" >      this.logger.warn('Invalid shape model provided for validation')</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(</span>
<span class="cstat-no" title="statement not covered" >        ErrorType.InvalidParameter,</span>
<span class="cstat-no" title="statement not covered" >        'Invalid shape model: missing ID',</span>
<span class="cstat-no" title="statement not covered" >        undefined, // severity, defaults in CoreError constructor</span>
<span class="cstat-no" title="statement not covered" >        { metadata: { model } }, // context</span>
<span class="cstat-no" title="statement not covered" >      )</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // Use the safe validation helper that handles all error cases
<span class="cstat-no" title="statement not covered" >    return safeValidate(</span>
<span class="cstat-no" title="statement not covered" >      model as unknown as ValidatorShape,</span>
<span class="cstat-no" title="statement not covered" >      async (shape) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const interfaceResult = await this.validator.validateElement(shape)</span>
        // Convert ValidationResult from validator-interface.ts to ValidationResult from validationUtils.ts
<span class="cstat-no" title="statement not covered" >        const utilErrors: ValidationIssue[] = (interfaceResult.errors || []).map(err =&gt; ({</span>
<span class="cstat-no" title="statement not covered" >          message: err.message,</span>
<span class="cstat-no" title="statement not covered" >          code: err.code.toString(), // Convert ValidationErrorCode enum to string</span>
<span class="cstat-no" title="statement not covered" >          path: err.path,</span>
<span class="cstat-no" title="statement not covered" >          value: err.value, // Map 'value' to the index signature if needed, or handle appropriately</span>
<span class="cstat-no" title="statement not covered" >        }))</span>
<span class="cstat-no" title="statement not covered" >        return {</span>
<span class="cstat-no" title="statement not covered" >          valid: interfaceResult.valid,</span>
<span class="cstat-no" title="statement not covered" >          errors: utilErrors,</span>
          // data property is optional in validationUtils.ValidationResult, can be omitted or set if available
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      },</span>
<span class="cstat-no" title="statement not covered" >      this.errorService,</span>
<span class="cstat-no" title="statement not covered" >      'CoreCoordinator',</span>
<span class="cstat-no" title="statement not covered" >      'validateShape',</span>
<span class="cstat-no" title="statement not covered" >      model.id,</span>
<span class="cstat-no" title="statement not covered" >    )</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * 检查变更是否需要触发计算
   * @param _changes 变更对象
   * @returns 是否需要触发计算
   */
<span class="cstat-no" title="statement not covered" >  private _doesChangeRequireComputation(_changes: Record&lt;string, unknown&gt;): boolean {</span>
    // 始终返回true，确保任何属性变更都触发计算
<span class="cstat-no" title="statement not covered" >    return true</span>
&nbsp;
    /* 原始实现，现在不再使用
    // 检查顶层属性
    const computationTriggeringProperties = ['width', 'height', 'cornerRadius', 'points', 'controlPoints'];
    for (const prop of computationTriggeringProperties) {
      if (prop in changes) {
        return true;
      }
    }
&nbsp;
    // 检查properties对象中的属性
    if ('properties' in changes &amp;&amp; typeof changes.properties === 'object' &amp;&amp; changes.properties !== null) {
      const props = changes.properties as Record&lt;string, unknown&gt;;
      for (const prop of computationTriggeringProperties) {
        if (prop in props) {
          return true;
        }
      }
&nbsp;
      // 检查成本相关属性
      const costProperties = ['costUnitPrice', 'costMultiplierOrCount', 'costBasis'];
      for (const prop of costProperties) {
        if (prop in props) {
          return true;
        }
      }
    }
&nbsp;
    return false;
    */
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * 获取服务状态信息，用于调试和监控
   * @returns 服务状态信息
   */
<span class="cstat-no" title="statement not covered" >  public getServiceStatus(): {</span>
    editService: boolean
    selectionService: boolean
    computationEnabled: boolean
<span class="cstat-no" title="statement not covered" >  } {</span>
<span class="cstat-no" title="statement not covered" >    return {</span>
<span class="cstat-no" title="statement not covered" >      editService: this.elementEditService !== null &amp;&amp; this.elementEditService !== undefined,</span>
<span class="cstat-no" title="statement not covered" >      selectionService: this.elementSelectionService !== null &amp;&amp; this.elementSelectionService !== undefined,</span>
<span class="cstat-no" title="statement not covered" >      computationEnabled: this._doesChangeRequireComputation({}),</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Publishes shapes update event with current repository data.
   *
   * @remarks
   * Sets isInternalUpdate flag to prevent feedback loops when store subscribers react to the event.
   *
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private publishShapesUpdate(): void {</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      // Set flag to prevent feedback loops
<span class="cstat-no" title="statement not covered" >      this.isInternalUpdate = true</span>
&nbsp;
      // Get all shapes from repository
<span class="cstat-no" title="statement not covered" >      const shapes = this.repository.getAll()</span>
<span class="cstat-no" title="statement not covered" >      const selectedIds = Array.from(this.repository.getSelectedIds())</span>
&nbsp;
      // Publish update event with current shape data
<span class="cstat-no" title="statement not covered" >      this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >        type: AppEventType.DataUpdated,</span>
<span class="cstat-no" title="statement not covered" >        payload: { type: 'shapes', data: shapes },</span>
<span class="cstat-no" title="statement not covered" >      } as { type: AppEventType, payload: { type: string, data: ShapeModel[] } })</span>
&nbsp;
      // === 新增：强制刷新 UI store ===
<span class="cstat-no" title="statement not covered" >      useShapesStore.getState().setShapesFromExternal(shapes, selectedIds)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      this.logger.debug?.('Published shapes update event')</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    catch (error) {</span>
<span class="cstat-no" title="statement not covered" >      this.handleError(error as Error, { context: 'publishShapesUpdate' })</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    finally {</span>
      // Always reset flag to allow future external updates
<span class="cstat-no" title="statement not covered" >      this.isInternalUpdate = false</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * 统一处理ShapeSelectRequest和ElementSelectRequest
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private _handleShapeSelectRequest(event: BaseEvent): void {</span>
<span class="cstat-no" title="statement not covered" >    console.warn('[CoreCoordinator] received SHAPE/ELEMENT_SELECT_REQUEST', event)</span>
<span class="cstat-no" title="statement not covered" >    const specificEvent = event as { payload?: unknown } // 兼容两种payload</span>
<span class="cstat-no" title="statement not covered" >    const selectPayload = specificEvent.payload</span>
<span class="cstat-no" title="statement not covered" >    this.logger.debug?.('CoreCoordinator received SHAPE/ELEMENT_SELECT_REQUEST', selectPayload)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (selectPayload !== null &amp;&amp; selectPayload !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >      try {</span>
<span class="cstat-no" title="statement not covered" >        let newSelectedIds: string[] = []</span>
<span class="cstat-no" title="statement not covered" >        const selectionMode = (selectPayload as { selectionMode?: string }).selectionMode</span>
<span class="cstat-no" title="statement not covered" >        const mode = (selectionMode !== null &amp;&amp; selectionMode !== undefined ? selectionMode : '').toLowerCase()</span>
        // 兼容 elementIds/shapeIds 字段
<span class="cstat-no" title="statement not covered" >        const ids = (selectPayload as { elementIds?: string[] }).elementIds</span>
<span class="cstat-no" title="statement not covered" >          || (selectPayload as { shapeIds?: string[] }).shapeIds</span>
<span class="cstat-no" title="statement not covered" >          || []</span>
<span class="cstat-no" title="statement not covered" >        if (mode === 'replace') {</span>
<span class="cstat-no" title="statement not covered" >          this.repository.setSelectedIds(ids)</span>
<span class="cstat-no" title="statement not covered" >          newSelectedIds = Array.from(this.repository.getSelectedIds())</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        else if (mode === 'add') {</span>
<span class="cstat-no" title="statement not covered" >          this.repository.addToSelection(ids)</span>
<span class="cstat-no" title="statement not covered" >          newSelectedIds = Array.from(this.repository.getSelectedIds())</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        else if (mode === 'clear') {</span>
<span class="cstat-no" title="statement not covered" >          this.repository.clearSelection()</span>
<span class="cstat-no" title="statement not covered" >          newSelectedIds = []</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        else {</span>
          // fallback: if ids present, replace selection
<span class="cstat-no" title="statement not covered" >          this.repository.setSelectedIds(ids)</span>
<span class="cstat-no" title="statement not covered" >          newSelectedIds = Array.from(this.repository.getSelectedIds())</span>
<span class="cstat-no" title="statement not covered" >        }</span>
        // Publish SelectionChanged event
<span class="cstat-no" title="statement not covered" >        console.warn('[CoreCoordinator] publishing SelectionChanged', newSelectedIds)</span>
<span class="cstat-no" title="statement not covered" >        this.eventBus.publish({</span>
<span class="cstat-no" title="statement not covered" >          type: AppEventType.SelectionChanged,</span>
<span class="cstat-no" title="statement not covered" >          payload: { selectedIds: newSelectedIds },</span>
<span class="cstat-no" title="statement not covered" >        })</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[CoreCoordinator] published SelectionChanged', newSelectedIds)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      catch (err: unknown) {</span>
<span class="cstat-no" title="statement not covered" >        const errorToHandle = err instanceof Error ? err : new Error(String(err))</span>
<span class="cstat-no" title="statement not covered" >        this.handleError(errorToHandle)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
<span class="cstat-no" title="statement not covered" >      this.logger.warn?.('SHAPE/ELEMENT_SELECT_REQUEST received with invalid payload', event)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:28:25.559Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    