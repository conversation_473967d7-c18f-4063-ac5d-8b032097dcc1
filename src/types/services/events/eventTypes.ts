/**
 * Event Types
 *
 * This file defines all event type constants used in the application.
 * It's separated from other event files to avoid circular dependencies.
 */

/**
 * Application Event Type Enum
 *
 * This enum contains all event types used in the application.
 * It's used with the event system (appEventBus).
 *
 * Note: This enum provides a centralized definition of all event types.
 * The individual *EventTypes constant objects in each event file provide
 * more granular access to event types for specific domains.
 */
export enum AppEventType {
  // System events
  EventRegister = 'event.register',
  EventUnregister = 'event.unregister',
  EventError = 'event.error',

  // Factory events
  FactoryRegister = 'factory.register',
  FactoryUnregister = 'factory.unregister',
  FactoryError = 'factory.error',

  // Shape events
  ShapeCreateRequest = 'shape.create.request',
  ShapeCreateValidate = 'shape.create.validate',
  ShapeCreateComplete = 'shape.create.complete',
  ShapeCreateError = 'shape.create.error',
  ShapeEditRequest = 'shape.edit.request',
  ShapeEditCompute = 'shape.edit.compute',
  ShapeEditComplete = 'shape.edit.complete',
  ShapeEditError = 'shape.edit.error',
  ShapeDeleteRequest = 'shape.delete.request',
  ShapeDeleteComplete = 'shape.delete.complete',
  ShapeDeleteError = 'shape.delete.error',
  ShapeSelected = 'shape.selected',
  ShapeAdd = 'shape.add',
  ShapeDuplicateRequest = 'shape.duplicate.request',
  ShapeDuplicateComplete = 'shape.duplicate.complete',
  ShapeUpdateRequest = 'shape.update.request',
  ShapeUpdateSuccess = 'shape.update.success',
  ShapeSelectRequest = 'shape.select.request',
  ShapeSelectionSuccess = 'shape.selection.success',
  ElementSelectRequest = 'element.select.request',
  ShapeBringToFrontRequest = 'shape.bring.to.front.request',
  ShapeBringToFrontComplete = 'shape.bring.to.front.complete',
  ShapeSendToBackRequest = 'shape.send.to.back.request',
  ShapeSendToBackComplete = 'shape.send.to.back.complete',

  // Shape transformation events (for handle operations)
  ShapeTransformRequest = 'shape.transform.request',
  ShapeTransformUpdate = 'shape.transform.update',
  ShapeTransformComplete = 'shape.transform.complete',

  // UI events
  ViewPanToggle = 'view.pan.toggle',
  ViewZoomIn = 'view.zoom.in',
  ViewZoomOut = 'view.zoom.out',
  ViewZoomReset = 'view.zoom.reset',
  SelectionModeChange = 'selection.mode.change',
  CanvasClicked = 'canvas.clicked',
  CanvasDblClicked = 'canvas.dblclicked',
  CanvasMouseDown = 'canvas.mousedown',
  CanvasMouseUp = 'canvas.mouseup',
  CanvasMouseMove = 'canvas.mousemove',
  CanvasContextMenu = 'canvas.contextmenu',
  ToastShow = 'toast.show',
  NotificationAdd = 'notification.add',
  ModalExportOpen = 'modal.export.open',
  ModalTemplateOpen = 'modal.template.open',

  // Canvas events
  CanvasCleared = 'canvas.cleared',
  CanvasResized = 'canvas.resized',

  // Tool events
  ToolChanged = 'tool.changed',

  // View events
  ViewZoomed = 'view.zoomed',
  ViewPanned = 'view.panned',

  // History events
  HistoryCheckpoint = 'history.checkpoint',
  HistoryUndo = 'history.undo',
  HistoryRedo = 'history.redo',
  HistoryError = 'history.error',
  HistoryUndoPerformed = 'history.undo.performed',
  HistoryRedoPerformed = 'history.redo.performed',
  HistoryBatchOperationPerformed = 'history.batch.operation.performed',

  // Render events
  RenderTrigger = 'render.trigger',
  RenderStart = 'render.start',
  RenderComplete = 'render.complete',
  RenderError = 'render.error',

  // Layer events
  LayerVisibilityChange = 'layer.visibility.change',
  LayerLockChange = 'layer.lock.change',
  LayerOrderChange = 'layer.order.change',
  LayerCreate = 'layer.create',
  LayerUpdate = 'layer.update',
  LayerDelete = 'layer.delete',
  LayerOrder = 'layer.order',

  // File events
  FileImported = 'file.imported',
  FileExported = 'file.exported',
  ExportRequest = 'export.request',
  ExportPrepare = 'export.prepare',
  ExportProgress = 'export.progress',
  ExportComplete = 'export.complete',
  ExportError = 'export.error',

  // Template events
  TemplateApply = 'template.apply',
  TemplateApplyRequest = 'template.apply.request',
  TemplateApplyValidate = 'template.apply.validate',
  TemplateApplyComplete = 'template.apply.complete',
  TemplateApplyError = 'template.apply.error',
  TemplateError = 'template.error',
  TemplateCreated = 'template.created',
  TemplateUpdated = 'template.updated',
  TemplateDeleted = 'template.deleted',
  TemplateDefaultChanged = 'template.default.changed',
  TemplatesImported = 'templates.imported',
  TemplateApplied = 'template.applied',

  // Compute events
  ComputeRequest = 'compute.request',
  ComputeProgress = 'compute.progress',
  ComputeComplete = 'compute.complete',
  ComputeError = 'compute.error',
  ComputeTransform = 'compute.transform',
  ValidateShape = 'validate.shape',
  ValidateTemplate = 'validate.template',
  ValidateOperation = 'validate.operation',
  ValidateConstraint = 'validate.constraint',
  BatchStart = 'batch.start',
  BatchProgress = 'batch.progress',
  BatchComplete = 'batch.complete',
  BatchError = 'batch.error',

  // State events
  StateUpdated = 'state.updated',
  DataUpdated = 'data.updated',
  SelectionChanged = 'selection.changed',
  ConfigUpdated = 'config.updated',
  ErrorOccurred = 'error.occurred',
  DataLoadRequest = 'data.load.request',
  DataLoadProgress = 'data.load.progress',
  DataLoadComplete = 'data.load.complete',
  DataLoadError = 'data.load.error',
  StorageSaveRequest = 'storage.save.request',
  StorageSaveComplete = 'storage.save.complete',
  StorageLoadRequest = 'storage.load.request',
  StorageLoadComplete = 'storage.load.complete',
  StorageBatch = 'storage.batch',
  StorageError = 'storage.error',
  StoreStateChange = 'store.state.change',
  StoreStateSync = 'store.state.sync',
  StoreStateError = 'store.state.error',

  // Grid events
  GridEnabled = 'grid.enabled',
  GridSizeChanged = 'grid.size.changed',
  GridColorChanged = 'grid.color.changed',
  GridSnapChanged = 'grid.snap.changed',

  // Sidebar events
  SidebarLeftToggle = 'sidebar.left.toggle',
  SidebarRightToggle = 'sidebar.right.toggle',

  // Keyboard events
  KeyPressed = 'keyboard.key.pressed',
  KeyReleased = 'keyboard.key.released',
}
