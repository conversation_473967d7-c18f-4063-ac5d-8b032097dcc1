import type { Ellipse } from '../../../../../src/types/core/element/shape/ellipseShapeTypes'
import type { Rectangle } from '../../../../../src/types/core/element/shape/rectangleShapeTypes'
import type { Polygon } from '../../../../../src/types/core/element/shape/shape'
import { describe, expect, it } from 'vitest'
import { calculateArea, calculateEllipseArea, calculatePolygonArea, calculateRectangleArea } from '../../../../../src/core/compute/properties/area'
import { Point } from '../../../../../src/types/core/element/geometry/point'

describe('area Calculation Module', () => {
  describe('calculateArea', () => {
    it('should return 0 for null or undefined element', () => {
      expect(calculateArea(null as any)).toBe(0)
      expect(calculateArea(undefined as any)).toBe(0)
    })

    it('should return 0 for element with no getSubType method', () => {
      const element = { id: 'test-element' } as any
      expect(calculateArea(element)).toBe(0)
    })

    it('should calculate rectangle area', () => {
      const rectangle = {
        getSubType: () => 'Rectangle',
        width: 10,
        height: 5,
      } as Rectangle

      expect(calculateArea(rectangle)).toBe(50)
    })

    it('should calculate ellipse area', () => {
      const ellipse = {
        getSubType: () => 'Ellipse',
        getRadiusX: () => 5,
        getRadiusY: () => 3,
      } as Ellipse

      expect(calculateArea(ellipse)).toBeCloseTo(Math.PI * 5 * 3)
    })

    it('should calculate polygon area', () => {
      const polygon = {
        getSubType: () => 'Polygon',
        getPoints: () => [
          new Point(0, 0),
          new Point(10, 0),
          new Point(10, 10),
          new Point(0, 10),
        ],
      } as Polygon

      expect(calculateArea(polygon)).toBe(100)
    })

    it('should return 0 for line elements', () => {
      const line = {
        getSubType: () => 'Line',
      } as any

      expect(calculateArea(line)).toBe(0)
    })

    it('should return 0 for unknown element types', () => {
      const unknown = {
        getSubType: () => 'Unknown',
      } as any

      expect(calculateArea(unknown)).toBe(0)
    })
  })

  describe('calculateRectangleArea', () => {
    it('should calculate rectangle area correctly', () => {
      const rectangle = {
        width: 10,
        height: 5,
      } as Rectangle

      expect(calculateRectangleArea(rectangle)).toBe(50)
    })

    it('should handle zero dimensions', () => {
      const rectangle = {
        width: 0,
        height: 5,
      } as Rectangle

      expect(calculateRectangleArea(rectangle)).toBe(0)
    })

    it('should handle negative dimensions', () => {
      const rectangle = {
        width: -10,
        height: 5,
      } as Rectangle

      // Area is calculated as absolute value of width * height
      expect(calculateRectangleArea(rectangle)).toBe(-50)
    })
  })

  describe('calculateEllipseArea', () => {
    it('should calculate ellipse area correctly', () => {
      const ellipse = {
        getRadiusX: () => 5,
        getRadiusY: () => 3,
      } as Ellipse

      expect(calculateEllipseArea(ellipse)).toBeCloseTo(Math.PI * 5 * 3)
    })

    it('should handle zero radius', () => {
      const ellipse = {
        getRadiusX: () => 0,
        getRadiusY: () => 3,
      } as Ellipse

      expect(calculateEllipseArea(ellipse)).toBe(0)
    })

    it('should handle negative radius', () => {
      const ellipse = {
        getRadiusX: () => -5,
        getRadiusY: () => 3,
      } as Ellipse

      // Area is calculated as absolute value of PI * rx * ry
      expect(calculateEllipseArea(ellipse)).toBeCloseTo(Math.PI * -5 * 3)
    })
  })

  describe('calculatePolygonArea', () => {
    it('should calculate square area correctly', () => {
      const polygon = {
        getPoints: () => [
          new Point(0, 0),
          new Point(10, 0),
          new Point(10, 10),
          new Point(0, 10),
        ],
      } as Polygon

      expect(calculatePolygonArea(polygon)).toBe(100)
    })

    it('should calculate triangle area correctly', () => {
      const polygon = {
        getPoints: () => [
          new Point(0, 0),
          new Point(10, 0),
          new Point(5, 10),
        ],
      } as Polygon

      expect(calculatePolygonArea(polygon)).toBe(50)
    })

    it('should return 0 for polygon with fewer than 3 points', () => {
      const polygon = {
        getPoints: () => [
          new Point(0, 0),
          new Point(10, 0),
        ],
      } as Polygon

      expect(calculatePolygonArea(polygon)).toBe(0)
    })

    it('should return 0 for empty polygon', () => {
      const polygon = {
        getPoints: () => [],
      } as Polygon

      expect(calculatePolygonArea(polygon)).toBe(0)
    })

    it('should handle complex polygon shapes', () => {
      const polygon = {
        getPoints: () => [
          new Point(0, 0),
          new Point(10, 0),
          new Point(10, 5),
          new Point(5, 5),
          new Point(5, 10),
          new Point(0, 10),
        ],
      } as Polygon

      // L-shaped polygon with area 75
      expect(calculatePolygonArea(polygon)).toBe(75)
    })
  })
})
