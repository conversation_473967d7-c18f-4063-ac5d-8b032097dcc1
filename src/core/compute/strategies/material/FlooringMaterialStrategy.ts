/**
 * Material Calculation Strategy for Flooring
 *
 * @remarks
 * This strategy implements the {@link IMaterialCalculatorStrategy} to calculate
 * the required amount of flooring material (e.g., hardwood, laminate, vinyl, carpet, tile).
 *
 * It relies on the area of the element (typically a room or floor space) and uses
 * the `calculateFlooringAmount` utility function from `./materialUtils.ts` to perform
 * the core calculation. This utility considers factors like material type, unit size
 * (for tiles/planks), wastage rate, and how materials are packaged (units per box).
 *
 * The strategy validates that the `materialType` is one of the supported flooring types
 * and that necessary options (like `unitSize` for certain materials) are provided.
 * It also depends on the input `element` having a way to compute its area (either via
 * a `getArea()` method or a `compute.area()` method), which is marked as a TODO for
 * potential refactoring to improve type safety and separation of concerns.
 *
 * The `getElementType()` method suggests this strategy is primarily for `RECTANGLE`
 * elements, though it could apply to any shape for which an area can be determined.
 *
 * @module core/compute/strategies/material/FlooringMaterialStrategy
 * @see {@link IMaterialCalculatorStrategy}
 * @see {@link MaterialCalculationOptions}
 * @see {@link MaterialCalculationResult}
 * @see {@link calculateFlooringAmount}
 */
import type {
  MaterialCalculatorStrategy as IMaterialCalculatorStrategy,
  MaterialCalculationOptions,
  MaterialCalculationResult,
} from '@/types/core/compute'
import type { Element } from '@/types/core/elementDefinitions'
import { calculateFlooringAmount } from '@/core/compute/strategies/material/materialUtils' // Import the specific function
import { CoreError, ErrorType } from '@/services/system/error-service'
import { ElementType as CoreElementType } from '@/types/core/elementDefinitions'

export class FlooringMaterialStrategy implements IMaterialCalculatorStrategy {
  /**
   * Calculates the amount of flooring material required for a given element.
   *
   * @param element - The element (e.g., room, floor area) for which to calculate flooring material.
   *                  Expected to have a method to compute its area.
   * @param materialType - The type of flooring material (e.g., 'hardwood', 'tile', 'carpet').
   * @param options - Optional {@link MaterialCalculationOptions} to customize the calculation,
   *                  such as `unitSize` (for planks/tiles) and `wastageRate`.
   * @returns A {@link MaterialCalculationResult} detailing the required material amount.
   * @throws {@link CoreError} if the `materialType` is unsupported, if required `options` like
   *         `unitSize` are missing for certain material types, or if the element's area
   *         cannot be determined or is invalid.
   */
  public calculateMaterialAmount(
    element: Element,
    materialType: string,
    options?: MaterialCalculationOptions,
  ): MaterialCalculationResult {
    // Element type validation can be more specific if this strategy only applies to certain shapes.
    // For now, relying on the presence of an area calculation method.
    // if (element.type !== CoreElementType.RECTANGLE && element.type !== CoreElementType.POLYGON etc.) {
    //   throw new CoreError(
    //     ErrorType.INVALID_ELEMENT_TYPE,
    //     `FlooringMaterialStrategy primarily applies to area-based shapes, got ${element.type}`
    //   );
    // }

    const validMaterialTypes = ['hardwood', 'laminate', 'vinyl', 'carpet', 'tile']
    if (!validMaterialTypes.includes(materialType)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `FlooringMaterialStrategy supports ${validMaterialTypes.join(', ')} material types, got '${materialType}'`,
      )
    }

    if ((materialType === 'hardwood' || materialType === 'laminate' || materialType === 'vinyl' || materialType === 'tile')
      && (!options?.unitSize || options.unitSize.width <= 0 || options.unitSize.height <= 0)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `${materialType} flooring requires unitSize (width and height) to be positive in options.`,
      )
    }

    let area: number
    const computableElement = element as unknown as { getArea?: () => number, compute?: { area?: () => number } } // TODO: Refactor for type safety

    try {
      if (computableElement.getArea !== null && computableElement.getArea !== undefined && typeof computableElement.getArea === 'function') {
        area = computableElement.getArea()
      }
      else if (computableElement.compute?.area && typeof computableElement.compute.area === 'function') {
        area = computableElement.compute.area()
      }
      else {
        throw new Error('Element does not have a getArea or compute.area method')
      }
    }
    catch (error) {
      throw new CoreError(
        ErrorType.ComputationError,
        `Failed to calculate area for element (ID: ${element.id}): ${error instanceof Error ? error.message : String(error)}`,
      )
    }

    if (typeof area !== 'number' || Number.isNaN(area) || area < 0) {
      throw new CoreError(ErrorType.ComputationError, `Invalid area value: ${area} for element ID: ${element.id}`)
    }
    if (area === 0 && materialType !== 'carpet') { // Carpet might be bought for a 0 area room if it's a remnant etc.
      console.warn(`[FlooringMaterialStrategy] Area for element ${element.id} is 0. Resulting material amount will be 0.`)
    }

    // Use the imported calculateFlooringAmount function directly
    const result = calculateFlooringAmount(area, materialType, options)

    // Construct the MaterialCalculationResult based on what calculateFlooringAmount returns
    // The calculateFlooringAmount function already returns a MaterialCalculationResult.
    // We just need to ensure the fields are correctly mapped or directly returned.
    // calculateFlooringAmount returns: amount (which is areaWithWastage), unit, unitCount, unitType, boxes.
    // We need to align this with what FlooringMaterialStrategy should return.
    // The strategy's `amount` should be the raw area, and `amountWithWastage` should be the adjusted one.

    return {
      amount: area, // Raw area before wastage
      unit: result.unit, // Should be 'square meters' or similar from calculateFlooringAmount
      unitCount: result.unitCount,
      unitType: result.unitType,
      amountWithWastage: result.amountWithWastage, // This is areaWithWastage from calculateFlooringAmount
      boxes: result.boxes,
    }
  }

  /**
   * Returns the primary element type this strategy is typically associated with.
   * @remarks Flooring is often applied to rectangular or polygonal room/floor areas.
   *          This strategy is registered for `RECTANGLE` but can be used for other
   *          shapes if their area can be computed.
   * @returns {@link CoreElementType.RECTANGLE} - The rectangle element type typically used for floor areas
   */
  public getElementType(): string {
    // This strategy is generally for shapes that define a floor area.
    // 'rectangle' is a common case, but it could apply to 'polygon' as well.
    return CoreElementType.RECTANGLE
  }

  // getMaterialType is not part of IMaterialCalculatorStrategy interface.
  // The material type is passed as an argument to calculateMaterialAmount.
}
