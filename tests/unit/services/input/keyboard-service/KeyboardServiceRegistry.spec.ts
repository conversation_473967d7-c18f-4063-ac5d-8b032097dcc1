import type { ServiceRegistry } from '@/services/core/registry'
import type { KeyboardService } from '@/types/services/keyboard'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { getServiceFactory } from '@/services/core/registry'
import { registerKeyboardService } from '@/services/input/keyboard-service/registry'
import { ServiceId } from '@/types/services/core/serviceIdentifier'

// Mock dependencies
vi.mock('@/services/core/registry', () => ({
  getServiceFactory: vi.fn(),
}))

describe('keyboard Service Registry', () => {
  let mockRegistry: ServiceRegistry
  let mockLogger: LoggerService
  let mockServiceFactory: any
  let mockKeyboardService: KeyboardService

  beforeEach(() => {
    mockKeyboardService = {
      initialize: vi.fn(),
      cleanup: vi.fn(),
      isKeyPressed: vi.fn(),
      getModifierKeys: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    } as unknown as KeyboardService

    mockServiceFactory = {
      createKeyboardService: vi.fn().mockReturnValue(mockKeyboardService),
    }

    mockRegistry = {
      register: vi.fn(),
      get: vi.fn(),
      has: vi.fn(),
      unregister: vi.fn(),
      clear: vi.fn(),
      getAll: vi.fn(),
    }

    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    }

    vi.mocked(getServiceFactory).mockReturnValue(mockServiceFactory)
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('registerKeyboardService', () => {
    it('should register keyboard service successfully', () => {
      const result = registerKeyboardService(mockRegistry, mockLogger)

      expect(getServiceFactory).toHaveBeenCalled()
      expect(mockServiceFactory.createKeyboardService).toHaveBeenCalledWith(mockLogger)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.KeyboardService,
        mockKeyboardService,
      )
      expect(mockLogger.info).toHaveBeenCalledWith('键盘服务注册成功')
      expect(result).toBe(mockKeyboardService)
    })

    it('should create keyboard service with provided logger', () => {
      const customLogger = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
      }

      const result = registerKeyboardService(mockRegistry, customLogger)

      expect(mockServiceFactory.createKeyboardService).toHaveBeenCalledWith(customLogger)
      expect(customLogger.info).toHaveBeenCalledWith('键盘服务注册成功')
      expect(result).toBe(mockKeyboardService)
    })

    it('should return the created keyboard service instance', () => {
      const result = registerKeyboardService(mockRegistry, mockLogger)

      expect(result).toBe(mockKeyboardService)
      expect(result).toBeDefined()
    })

    it('should handle service factory errors gracefully', () => {
      const error = new Error('Factory creation failed')
      mockServiceFactory.createKeyboardService.mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerKeyboardService(mockRegistry, mockLogger)
      }).toThrow(error)

      expect(getServiceFactory).toHaveBeenCalled()
      expect(mockServiceFactory.createKeyboardService).toHaveBeenCalledWith(mockLogger)
      expect(mockRegistry.register).not.toHaveBeenCalled()
      expect(mockLogger.info).not.toHaveBeenCalled()
    })

    it('should handle registry registration errors gracefully', () => {
      const error = new Error('Registration failed')
      mockRegistry.register.mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerKeyboardService(mockRegistry, mockLogger)
      }).toThrow(error)

      expect(mockServiceFactory.createKeyboardService).toHaveBeenCalledWith(mockLogger)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.KeyboardService,
        mockKeyboardService,
      )
      expect(mockLogger.info).not.toHaveBeenCalled()
    })

    it('should handle getServiceFactory errors gracefully', () => {
      const error = new Error('Service factory not available')
      vi.mocked(getServiceFactory).mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerKeyboardService(mockRegistry, mockLogger)
      }).toThrow(error)

      expect(getServiceFactory).toHaveBeenCalled()
      expect(mockRegistry.register).not.toHaveBeenCalled()
      expect(mockLogger.info).not.toHaveBeenCalled()
    })

    it('should work with different logger implementations', () => {
      const loggers = [
        {
          info: vi.fn(),
          warn: vi.fn(),
          error: vi.fn(),
          debug: vi.fn(),
        },
        {
          info: vi.fn(),
          warn: vi.fn(),
          error: vi.fn(),
          debug: vi.fn(),
          trace: vi.fn(), // Additional method
        },
      ]

      loggers.forEach((logger, index) => {
        vi.clearAllMocks()

        const result = registerKeyboardService(mockRegistry, logger)

        expect(mockServiceFactory.createKeyboardService).toHaveBeenCalledWith(logger)
        expect(logger.info).toHaveBeenCalledWith('键盘服务注册成功')
        expect(result).toBe(mockKeyboardService)
      })
    })

    it('should work with different registry implementations', () => {
      const registries = [
        {
          register: vi.fn(),
          get: vi.fn(),
          has: vi.fn(),
          unregister: vi.fn(),
          clear: vi.fn(),
          getAll: vi.fn(),
        },
        {
          register: vi.fn(),
          get: vi.fn(),
          has: vi.fn(),
          unregister: vi.fn(),
          clear: vi.fn(),
          getAll: vi.fn(),
          size: vi.fn(), // Additional method
        },
      ]

      registries.forEach((registry, index) => {
        vi.clearAllMocks()

        const result = registerKeyboardService(registry, mockLogger)

        expect(registry.register).toHaveBeenCalledWith(
          ServiceId.KeyboardService,
          mockKeyboardService,
        )
        expect(mockLogger.info).toHaveBeenCalledWith('键盘服务注册成功')
        expect(result).toBe(mockKeyboardService)
      })
    })

    it('should handle multiple registration calls', () => {
      const result1 = registerKeyboardService(mockRegistry, mockLogger)
      const result2 = registerKeyboardService(mockRegistry, mockLogger)

      expect(mockServiceFactory.createKeyboardService).toHaveBeenCalledTimes(2)
      expect(mockRegistry.register).toHaveBeenCalledTimes(2)
      expect(mockLogger.info).toHaveBeenCalledTimes(2)
      expect(result1).toBe(mockKeyboardService)
      expect(result2).toBe(mockKeyboardService)
    })

    it('should create new keyboard service instance for each registration', () => {
      const service1 = { initialize: vi.fn(), id: 'service1' } as unknown as KeyboardService
      const service2 = { initialize: vi.fn(), id: 'service2' } as unknown as KeyboardService

      mockServiceFactory.createKeyboardService
        .mockReturnValueOnce(service1)
        .mockReturnValueOnce(service2)

      const result1 = registerKeyboardService(mockRegistry, mockLogger)
      const result2 = registerKeyboardService(mockRegistry, mockLogger)

      expect(result1).toBe(service1)
      expect(result2).toBe(service2)
      expect(mockRegistry.register).toHaveBeenNthCalledWith(1, ServiceId.KeyboardService, service1)
      expect(mockRegistry.register).toHaveBeenNthCalledWith(2, ServiceId.KeyboardService, service2)
    })

    it('should preserve correct execution order', () => {
      const callOrder: string[] = []

      vi.mocked(getServiceFactory).mockImplementation(() => {
        callOrder.push('getServiceFactory')
        return mockServiceFactory
      })

      mockServiceFactory.createKeyboardService.mockImplementation((logger: LoggerService) => {
        callOrder.push('createKeyboardService')
        return mockKeyboardService
      })

      mockRegistry.register.mockImplementation(() => {
        callOrder.push('register')
      })

      mockLogger.info.mockImplementation(() => {
        callOrder.push('info')
      })

      registerKeyboardService(mockRegistry, mockLogger)

      expect(callOrder).toEqual([
        'getServiceFactory',
        'createKeyboardService',
        'register',
        'info',
      ])
    })

    it('should use correct service identifier', () => {
      registerKeyboardService(mockRegistry, mockLogger)

      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.KeyboardService,
        mockKeyboardService,
      )

      const registrationCall = mockRegistry.register.mock.calls[0]
      expect(registrationCall[0]).toBe(ServiceId.KeyboardService)
      expect(typeof registrationCall[0]).toBe('string')
    })

    it('should handle logger method failures gracefully', () => {
      const faultyLogger = {
        info: vi.fn().mockImplementation(() => {
          throw new Error('Logging failed')
        }),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
      }

      expect(() => {
        registerKeyboardService(mockRegistry, faultyLogger)
      }).toThrow('Logging failed')

      // Service should still be created and registered even if logging fails
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.KeyboardService,
        mockKeyboardService,
      )
    })

    it('should work with minimal logger interface', () => {
      const minimalLogger = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
      }

      const result = registerKeyboardService(mockRegistry, minimalLogger)

      expect(mockServiceFactory.createKeyboardService).toHaveBeenCalledWith(minimalLogger)
      expect(minimalLogger.info).toHaveBeenCalledWith('键盘服务注册成功')
      expect(result).toBe(mockKeyboardService)
    })

    it('should handle service factory returning null or undefined', () => {
      mockServiceFactory.createKeyboardService.mockReturnValue(null)

      const result = registerKeyboardService(mockRegistry, mockLogger)

      expect(mockRegistry.register).toHaveBeenCalledWith(ServiceId.KeyboardService, null)
      expect(mockLogger.info).toHaveBeenCalledWith('键盘服务注册成功')
      expect(result).toBeNull()

      // Test with undefined
      vi.clearAllMocks()
      mockServiceFactory.createKeyboardService.mockReturnValue(undefined)

      const result2 = registerKeyboardService(mockRegistry, mockLogger)

      expect(mockRegistry.register).toHaveBeenCalledWith(ServiceId.KeyboardService, undefined)
      expect(mockLogger.info).toHaveBeenCalledWith('键盘服务注册成功')
      expect(result2).toBeUndefined()
    })

    it('should handle complex keyboard service objects', () => {
      const complexKeyboardService = {
        initialize: vi.fn(),
        cleanup: vi.fn(),
        isKeyPressed: vi.fn(),
        getModifierKeys: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        getKeyState: vi.fn(),
        setKeyBindings: vi.fn(),
        metadata: {
          version: '1.0.0',
          capabilities: ['shortcuts', 'modifiers'],
        },
      } as unknown as KeyboardService

      mockServiceFactory.createKeyboardService.mockReturnValue(complexKeyboardService)

      const result = registerKeyboardService(mockRegistry, mockLogger)

      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.KeyboardService,
        complexKeyboardService,
      )
      expect(mockLogger.info).toHaveBeenCalledWith('键盘服务注册成功')
      expect(result).toBe(complexKeyboardService)
    })
  })

  describe('integration scenarios', () => {
    it('should work in a typical application initialization flow', () => {
      const result = registerKeyboardService(mockRegistry, mockLogger)

      // Verify the complete flow
      expect(getServiceFactory).toHaveBeenCalled()
      expect(mockServiceFactory.createKeyboardService).toHaveBeenCalledWith(mockLogger)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.KeyboardService,
        mockKeyboardService,
      )
      expect(mockLogger.info).toHaveBeenCalledWith('键盘服务注册成功')
      expect(result).toBe(mockKeyboardService)
    })

    it('should handle service replacement scenarios', () => {
      // Initial registration
      const result1 = registerKeyboardService(mockRegistry, mockLogger)

      const initialService = mockServiceFactory.createKeyboardService.mock.results[0].value

      // Service replacement
      const newService = { initialize: vi.fn(), id: 'new-service' } as unknown as KeyboardService
      mockServiceFactory.createKeyboardService.mockReturnValue(newService)

      const result2 = registerKeyboardService(mockRegistry, mockLogger)

      expect(mockRegistry.register).toHaveBeenCalledTimes(2)
      expect(mockRegistry.register).toHaveBeenNthCalledWith(1, ServiceId.KeyboardService, initialService)
      expect(mockRegistry.register).toHaveBeenNthCalledWith(2, ServiceId.KeyboardService, newService)
      expect(result1).toBe(initialService)
      expect(result2).toBe(newService)
    })

    it('should work with real-world service dependencies', () => {
      // Simulate more realistic scenario
      const result = registerKeyboardService(mockRegistry, mockLogger)

      // Verify that the service is properly configured
      expect(result).toBeDefined()
      expect(mockServiceFactory.createKeyboardService).toHaveBeenCalledWith(mockLogger)

      // Verify that the service can be retrieved from registry
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.KeyboardService,
        result,
      )
    })
  })
})
