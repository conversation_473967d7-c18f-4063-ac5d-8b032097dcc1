/**
 * Keyboard Service Types
 *
 * This file defines types related to the keyboard service functionality.
 *
 * @module types/services/keyboard
 */

/**
 * Key modifiers interface
 */
export interface KeyModifiers {
  /** Whether the Alt/Option key is pressed */
  altKey: boolean
  /** Whether the Control key is pressed */
  ctrlKey: boolean
  /** Whether the Shift key is pressed */
  shiftKey: boolean
  /** Whether the Meta/Command key is pressed */
  metaKey: boolean
}

/**
 * Key event data interface
 */
export interface KeyEventData {
  /** Key value */
  key: string
  /** Key code */
  code: string
  /** Modifier keys state */
  modifiers: KeyModifiers
  /** Original keyboard event */
  originalEvent?: KeyboardEvent
}

/**
 * Key combination interface
 */
export interface KeyCombination {
  /** Primary key code */
  key: string
  /** Whether the Alt/Option key is required */
  alt?: boolean
  /** Whether the Control key is required */
  ctrl?: boolean
  /** Whether the Shift key is required */
  shift?: boolean
  /** Whether the Meta/Command key is required */
  meta?: boolean
}

/**
 * Shortcut action type - can be a string identifier or a function
 */
export type ShortcutAction = string | ((event: KeyboardEvent) => void)

/**
 * Keyboard shortcut interface
 */
export interface KeyboardShortcut {
  /** Unique identifier for the shortcut */
  id: string
  /** Description of what the shortcut does */
  description: string
  /** Key combination that triggers the shortcut */
  keyCombination: KeyCombination
  /** Action to perform when the shortcut is triggered */
  action: ShortcutAction
  /** Whether the shortcut is enabled */
  enabled: boolean
  /** Context in which the shortcut is active */
  context?: string[]
  /** Priority for handling (higher values get precedence) */
  priority?: number
}

/**
 * Event context interface (provides information about where a keyboard event occurred)
 */
export interface KeyboardEventContext {
  /** Component/element where the event occurred */
  source: string
  /** Current application mode */
  mode?: string
  /** Current tool */
  tool?: string
  /** Active element or selection */
  activeElement?: string
  /** Other contextual information */
  [key: string]: unknown
}

/**
 * Extended keyboard event interface
 */
export interface KeyboardEventExt extends KeyboardEvent {
  /** Additional application-specific context */
  appContext?: KeyboardEventContext
}

/**
 * Keyboard service configuration
 */
export interface KeyboardServiceConfig {
  /** Whether the service is enabled */
  enabled: boolean
  /** Whether to prevent default browser actions for registered shortcuts */
  preventDefaultForRegistered: boolean
  /** Whether to stop propagation for registered shortcuts */
  stopPropagationForRegistered: boolean
  /** Global context filters (restricts shortcut activation) */
  globalContextFilters?: string[]
}

/**
 * Keyboard service interface
 */
export interface KeyboardService {
  /** Register a keyboard shortcut */
  registerShortcut: (shortcut: KeyboardShortcut) => string
  /** Unregister a keyboard shortcut */
  unregisterShortcut: (id: string) => boolean
  /** Get a registered shortcut by ID */
  getShortcut: (id: string) => KeyboardShortcut | undefined
  /** Get all registered shortcuts */
  getAllShortcuts: () => KeyboardShortcut[]
  /** Set the current context */
  setContext: (context: KeyboardEventContext) => void
  /** Get the current context */
  getContext: () => KeyboardEventContext
  /** Handle a keyboard event */
  handleKeyEvent: (event: KeyboardEvent) => boolean
  /** Enable a specific shortcut */
  enableShortcut: (id: string) => boolean
  /** Disable a specific shortcut */
  disableShortcut: (id: string) => boolean
  /** Get the service configuration */
  getConfig: () => KeyboardServiceConfig
  /** Set the service configuration */
  setConfig: (config: Partial<KeyboardServiceConfig>) => void

  /** Register a key binding */
  registerKeyBinding: (
    key: string,
    callback: (event: KeyEventData) => void,
    options?: {
      preventDefault?: boolean
      stopPropagation?: boolean
      onKeyUp?: boolean
      description?: string
    }
  ) => () => void

  /** Unregister a key binding */
  unregisterKeyBinding: (key: string) => void

  /** Get all registered key bindings */
  getKeyBindings: () => Map<string, unknown>

  /** Add a key down listener */
  addKeyDownListener: (listener: (event: KeyboardEvent) => void) => () => void

  /** Add a key up listener */
  addKeyUpListener: (listener: (event: KeyboardEvent) => void) => () => void

  /** Initialize the service */
  initialize: () => void

  /** Clean up the service */
  cleanup: () => void
}
