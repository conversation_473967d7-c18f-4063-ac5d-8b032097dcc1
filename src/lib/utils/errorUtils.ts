/**
 * Utility functions for error handling.
 */

/**
 * Type-safe check to determine if a value is an Error instance.
 * @param value - The value to check.
 * @returns True if the value is an Error, false otherwise.
 */
export function isError(value: unknown): value is Error {
  return value instanceof Error
}

/**
 * Merges multiple metadata objects.
 * Later sources can overwrite keys from earlier sources.
 * Returns undefined if no metadata sources have any own properties.
 * @param metadataSources - An array of metadata objects (or undefined).
 * @returns A merged metadata object, or undefined if all sources are empty or undefined.
 */
export function mergeErrorMetadata(
  ...metadataSources: (Record<string, any> | undefined)[]
): Record<string, any> | undefined {
  const merged: Record<string, any> = {}
  let hasMetadata = false
  for (const source of metadataSources) {
    if (source) {
      for (const key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          merged[key] = source[key]
          hasMetadata = true
        }
      }
    }
  }
  return hasMetadata ? merged : undefined
}

/**
 * Ensures a value is an Error object. If it's not, it creates a new Error.
 * @param value - The value to ensure is an Error.
 * @param defaultMessage - The default message to use if creating a new Error from a non-Error value.
 * @returns An Error object.
 */
export function ensureError(value: unknown, defaultMessage = 'An unknown error occurred'): Error {
  if (value instanceof Error) {
    return value
  }
  if (typeof value === 'string') {
    return new Error(value)
  }
  return new Error(defaultMessage)
}

// Add other error utility functions mentioned in the document if needed,
// e.g., createErrorContext, formatErrorMessage, extractStackTrace, toError, createErrorPayload.
// For now, only implementing what's directly causing the import error.
// Placeholder for ErrorContext, ideally import from '@/types/services/errors'
interface PlaceholderErrorContext {
  component?: string
  operation?: string
  metadata?: Record<string, any>
  [key: string]: any // Allow other properties for flexibility during refactor
}

// Placeholder for ServiceError, ideally import from '@/types/services/core/serviceResult'
interface PlaceholderServiceError {
  code: string
  message: string
  details?: unknown
}

/**
 * Placeholder handleError function.
 * In a real scenario, this would likely call an ErrorService.
 * @param error - The error to handle.
 * @param context - Contextual information about the error.
 */
export function handleError(error: unknown, context?: PlaceholderErrorContext): void {
  const err = ensureError(error) // Use existing ensureError
  console.error(
    `Error handled in component: ${context?.component || 'UnknownComponent'}, operation: ${context?.operation || 'UnknownOperation'}. Message: ${err.message}`,
    { error: err, context },
  )
}

/**
 * Executes a function synchronously and catches any errors, handling them with handleError.
 * @param fn - The function to execute.
 * @param errorContext - Context for error handling if an exception occurs.
 * @returns The result of the function, or undefined if an error occurs.
 */
export function safeExecute<T>(
  fn: () => T,
  errorContext?: PlaceholderErrorContext,
): T | undefined {
  try {
    return fn()
  }
  catch (error) {
    handleError(error, errorContext)
    return undefined
  }
}

/**
 * Executes an async function and catches any errors, handling them with handleError.
 * @param fn - The async function to execute.
 * @param errorContext - Context for error handling if an exception occurs.
 * @returns A promise that resolves with the result of the function, or undefined if an error occurs.
 */
export async function safeExecuteAsync<T>(
  fn: () => Promise<T>,
  errorContext?: PlaceholderErrorContext,
): Promise<T | undefined> {
  try {
    return await fn()
  }
  catch (error) {
    handleError(error, errorContext)
    return undefined
  }
}

/**
 * Creates a standardized service error object.
 * @param message - The error message.
 * @param code - The error code.
 * @param details - Optional additional details.
 * @returns A ServiceError object.
 */
export function createServiceError(
  message: string,
  code: string,
  details?: unknown,
): PlaceholderServiceError {
  return {
    code,
    message,
    details,
  }
}
