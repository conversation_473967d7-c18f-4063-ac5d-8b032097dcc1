import { beforeEach, describe, expect, it } from 'vitest'
import { PolygonBoundingBoxStrategy } from '../../../../../../src/core/compute/strategies/bounding-box/PolygonBoundingBoxStrategy'
import { Point } from '../../../../../../src/types/core/element/geometry/point'

// Import Rectangle class
import { Rectangle } from '../../../../../../src/types/core/element/shape/rectangleShapeTypes'

// Import Polygon class
import { Polygon } from '../../../../../../src/types/core/element/shape/shape'

// Mock Polygon class
class MockPolygon extends Polygon {
  constructor(points: Point[]) {
    if (points.length < 3) {
      // For testing empty polygon case
      super('mock-polygon-empty', { type: 'polygon', points: [new Point(0, 0), new Point(1, 0), new Point(0, 1)].map(p => p.to<PERSON><PERSON>()) })
      // Clear the points after construction
      this._points = []
    }
    else {
      super('mock-polygon', { type: 'polygon', points: points.map(p => p.toJson()) })
    }
  }

  // For testing error cases
  throwOnGetPoints: boolean = false
  throwOnGetPosition: boolean = false
  invalidVertices: boolean = false

  override getPoints(): Point[] {
    if (this.throwOnGetPoints) {
      throw new Error('Error getting points')
    }

    if (this.invalidVertices) {
      return [
        new Point(0, 0),
        new Point(0, 5),
        null as any,
        { x: Number.NaN, y: Number.NaN } as any,
        { x: 'invalid', y: 'invalid' } as any,
        new Point(5, 0),
      ]
    }

    return super.getPoints()
  }

  override getPosition(): Point {
    if (this.throwOnGetPosition) {
      throw new Error('Error getting position')
    }
    return super.getPosition()
  }
}

// Mock non-polygon element
class MockRectangle extends Rectangle {
  constructor() {
    super('mock-rectangle', new Point(0, 0), { type: 'rectangle', width: 10, height: 10 })
  }
}

/**
 * Unit tests for the PolygonBoundingBoxStrategy class
 */
describe('polygonBoundingBoxStrategy', () => {
  let strategy: PolygonBoundingBoxStrategy

  beforeEach(() => {
    strategy = new PolygonBoundingBoxStrategy()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('polygon')
  })

  it('should calculate bounding box of a square correctly', () => {
    // Create a square with vertices at (0,0), (0,5), (5,5), and (5,0)
    const points = [
      new Point(0, 0),
      new Point(0, 5),
      new Point(5, 5),
      new Point(5, 0),
    ]

    const polygon = new MockPolygon(points)
    const boundingBox = strategy.calculateBoundingBox(polygon as any)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBe(0)
    expect(boundingBox.y).toBe(0)
    expect(boundingBox.width).toBe(5)
    expect(boundingBox.height).toBe(5)
  })

  it('should calculate bounding box of a triangle correctly', () => {
    // Create a triangle with vertices at (0,0), (5,10), and (10,0)
    const points = [
      new Point(0, 0),
      new Point(5, 10),
      new Point(10, 0),
    ]

    const polygon = new MockPolygon(points)
    const boundingBox = strategy.calculateBoundingBox(polygon as any)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBe(0)
    expect(boundingBox.y).toBe(0)
    expect(boundingBox.width).toBe(10)
    expect(boundingBox.height).toBe(10)
  })

  it('should calculate bounding box of a complex polygon correctly', () => {
    // Create a complex polygon
    const points = [
      new Point(0, 0),
      new Point(5, 10),
      new Point(10, 5),
      new Point(15, 15),
      new Point(20, 0),
      new Point(10, -5),
    ]

    const polygon = new MockPolygon(points)
    const boundingBox = strategy.calculateBoundingBox(polygon as any)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBe(0)
    expect(boundingBox.y).toBe(-5)
    expect(boundingBox.width).toBe(20)
    expect(boundingBox.height).toBe(20)
  })

  it('should handle polygon with no vertices', () => {
    const polygon = new MockPolygon([])
    const boundingBox = strategy.calculateBoundingBox(polygon)

    expect(boundingBox).toBeDefined()
    // Since we're using a mock polygon with default points, we don't expect exact zeros
    expect(boundingBox.x).toBeGreaterThanOrEqual(0)
    expect(boundingBox.y).toBeGreaterThanOrEqual(0)
    expect(boundingBox.width).toBeGreaterThanOrEqual(0)
    expect(boundingBox.height).toBeGreaterThanOrEqual(0)
  })

  it('should handle non-polygon elements', () => {
    const rectangle = new MockRectangle()
    const boundingBox = strategy.calculateBoundingBox(rectangle as any)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBe(0)
    expect(boundingBox.y).toBe(0)
    expect(boundingBox.width).toBe(0)
    expect(boundingBox.height).toBe(0)
  })

  it('should handle error when getting points', () => {
    const polygon = new MockPolygon([new Point(0, 0), new Point(5, 5), new Point(10, 0)])
    polygon.throwOnGetPoints = true

    const boundingBox = strategy.calculateBoundingBox(polygon)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBe(polygon.position.x)
    expect(boundingBox.y).toBe(polygon.position.y)
    expect(boundingBox.width).toBe(0)
    expect(boundingBox.height).toBe(0)
  })

  it('should handle error when getting points and position', () => {
    const polygon = new MockPolygon([new Point(0, 0), new Point(5, 5), new Point(10, 0)])
    polygon.throwOnGetPoints = true
    polygon.throwOnGetPosition = true

    const boundingBox = strategy.calculateBoundingBox(polygon)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBe(0)
    expect(boundingBox.y).toBe(0)
    expect(boundingBox.width).toBe(0)
    expect(boundingBox.height).toBe(0)
  })

  it('should handle invalid vertices', () => {
    const polygon = new MockPolygon([new Point(0, 0), new Point(5, 5), new Point(10, 0)])
    polygon.invalidVertices = true

    const boundingBox = strategy.calculateBoundingBox(polygon)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBe(0)
    expect(boundingBox.y).toBe(0)
    expect(boundingBox.width).toBe(5)
    expect(boundingBox.height).toBe(5)
  })

  it('should handle all invalid vertices', () => {
    const polygon = new MockPolygon([new Point(0, 0), new Point(5, 5), new Point(10, 0)])
    // Override getPoints to return all invalid vertices
    polygon.getPoints = () => [
      null as any,
      { x: Number.NaN, y: Number.NaN } as any,
      { x: 'invalid', y: 'invalid' } as any,
    ]

    const boundingBox = strategy.calculateBoundingBox(polygon)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBe(polygon.position.x)
    expect(boundingBox.y).toBe(polygon.position.y)
    expect(boundingBox.width).toBe(0)
    expect(boundingBox.height).toBe(0)
  })

  it('should handle unexpected errors during calculation', () => {
    const polygon = new MockPolygon([new Point(0, 0), new Point(5, 5), new Point(10, 0)])
    // Create a situation that will cause an error during calculation
    polygon.getPoints = () => {
      const points = [new Point(0, 0), new Point(5, 5), new Point(10, 0)]
      // Make the array non-iterable to cause an error
      Object.defineProperty(points, 'forEach', {
        value: () => { throw new Error('Test error') },
      })
      return points
    }

    const boundingBox = strategy.calculateBoundingBox(polygon)

    expect(boundingBox).toBeDefined()
    // Since we're mocking the error, we can't predict the exact values
    // Just check that we get a valid bounding box with some values
    expect(boundingBox.x).toBeGreaterThanOrEqual(0)
    expect(boundingBox.y).toBeGreaterThanOrEqual(0)
    expect(boundingBox.width).toBeGreaterThanOrEqual(0)
    expect(boundingBox.height).toBeGreaterThanOrEqual(0)
  })

  it('should handle catastrophic errors', () => {
    const polygon = new MockPolygon([new Point(0, 0), new Point(5, 5), new Point(10, 0)])
    // Create a situation that will cause an error in the outer try-catch block
    // We'll use a mock that throws an error when getPoints is called
    // and also throws when getPosition is called
    polygon.throwOnGetPoints = true
    polygon.throwOnGetPosition = true

    // Also override the element's id to cause an error when logging
    Object.defineProperty(polygon, 'id', {
      get() { throw new Error('Error getting ID') },
    })

    const boundingBox = strategy.calculateBoundingBox(polygon)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBe(0)
    expect(boundingBox.y).toBe(0)
    expect(boundingBox.width).toBe(0)
    expect(boundingBox.height).toBe(0)
  })
})
