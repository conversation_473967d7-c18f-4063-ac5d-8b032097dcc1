/**
 * Bounding Box Calculation Strategy for Quadratic Bezier Curve Elements
 *
 * @remarks
 * This strategy implements the {@link BoundingBoxCalculatorStrategy} for calculating
 * the axis-aligned bounding box of Quadratic Bezier curve elements
 * ({@link CoreElementType.QUADRATIC}).
 *
 * The calculation involves:
 * 1. Retrieving the start point, end point, and control point of the quadratic Bezier
 *    curve from the element's properties.
 * 2. Converting these points (which might be relative to the element's position) to
 *    absolute coordinates.
 * 3. Delegating the core geometric calculation to the
 *    `calculateQuadraticBezierBoundingBoxAccurate` function from
 *    `../../../../lib/utils/geometry/bezierUtils`. This utility function
 *    typically finds the extrema of the curve.
 *
 * The strategy validates that the element is of type `QUADRATIC` and that its
 * defining points are valid.
 *
 * @module core/compute/strategies/bounding-box/QuadraticBoundingBoxStrategy
 * @see {@link Path.Quadratic} for the quadratic Bezier curve element type definition.
 * @see {@link BoundingBoxInterface} for the bounding box structure.
 */
import type { BoundingBoxCalculatorStrategy } from '../../../../types/core/compute'
import type { BoundingBox as BoundingBoxInterface } from '../../../../types/core/element/geometry/bounding-box'
import type { PointData as IPoint } from '../../../../types/core/element/geometry/point' // Use PointData, alias as IPoint
import type {
  Element,
  ShapeElement,
  // MetadataProperties // Not used
} from '../../../../types/core/elementDefinitions'
import { calculateQuadraticBezierBoundingBoxAccurate } from '../../../../lib/utils/geometry/bezierUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
  // MetadataProperties // Not used
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

// createBBoxShapeElement helper function is removed.
// Private helper methods for extrema calculation are moved to bezierUtils.

export class QuadraticBoundingBoxStrategy implements BoundingBoxCalculatorStrategy {
  /**
   * Calculates the axis-aligned bounding box for a quadratic Bezier curve element.
   *
   * @param element - The quadratic Bezier curve element, expected to be of type {@link ShapeElement}
   *                  and specifically {@link CoreElementType.QUADRATIC}, conforming to {@link Path.Quadratic}.
   * @returns A {@link BoundingBoxInterface} object representing the bounding box.
   * @throws {@link CoreError} if the provided element is not of type `QUADRATIC`.
   * @throws {@link CoreError} if the curve's control points (`start`, `control`, `end`)
   *         are invalid or not numbers.
   */
  public calculateBoundingBox(element: Element): BoundingBoxInterface {
    if (element.type !== CoreElementType.QUADRATIC) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `QuadraticBoundingBoxStrategy can only calculate bounding box for QUADRATIC elements, got ${element.type}`,
        undefined,
        { component: 'QuadraticBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    // Access quadratic element properties from properties object
    const shapeElement = element as ShapeElement
    const properties = shapeElement.properties || {}

    // Type guard function to validate point structure
    function isValidPoint(point: unknown): point is IPoint {
      if (point == null || typeof point !== 'object') {
        return false
      }
      const pointObj = point as Record<string, unknown>
      return 'x' in pointObj
        && 'y' in pointObj
        && typeof pointObj.x === 'number'
        && typeof pointObj.y === 'number'
        && Number.isFinite(pointObj.x)
        && Number.isFinite(pointObj.y)
    }

    // Get points from properties
    const startPoint = (properties.start) ?? (properties.startPoint)
    const controlPoint = (properties.control) ?? (properties.controlPoint1)
    const endPoint = (properties.end) ?? (properties.endPoint)

    // Validate that the required properties exist and are valid points
    if (startPoint == null || controlPoint == null || endPoint == null) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Quadratic curve element (ID: ${element.id}) is missing required points (start, control, end)`,
        undefined,
        { component: 'QuadraticBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    // Validate and extract points
    if (!isValidPoint(startPoint) || !isValidPoint(controlPoint) || !isValidPoint(endPoint)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid quadratic curve points for element ID: ${element.id}`,
        undefined,
        { component: 'QuadraticBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    // Now TypeScript knows these are valid points
    const relP0 = startPoint
    const relP1 = controlPoint
    const relP2 = endPoint

    // Convert relative points to absolute for BBox calculation
    const elPosX = shapeElement.position?.x ?? 0
    const elPosY = shapeElement.position?.y ?? 0

    const p0_abs: IPoint = {
      x: relP0.x + elPosX,
      y: relP0.y + elPosY,
      z: relP0.z,
    }
    const p1_abs: IPoint = {
      x: relP1.x + elPosX,
      y: relP1.y + elPosY,
      z: relP1.z,
    }
    const p2_abs: IPoint = {
      x: relP2.x + elPosX,
      y: relP2.y + elPosY,
      z: relP2.z,
    }

    // Delegate to the utility function which returns a BoundingBoxClass instance
    return calculateQuadraticBezierBoundingBoxAccurate(p0_abs, p1_abs, p2_abs)
  }

  // Private helper methods (solveQuadraticExtremum, evaluateQuadraticBezier) are removed

  /**
   * Returns the element type that this strategy is responsible for.
   * @returns The element type {@link CoreElementType.QUADRATIC}
   */
  public getElementType(): CoreElementType {
    return CoreElementType.QUADRATIC
  }
}
