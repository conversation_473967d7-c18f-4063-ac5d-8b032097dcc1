/**
 * Checkbox Component
 *
 * A styled checkbox component built on top of Radix UI's Checkbox primitive.
 * Provides consistent styling, accessibility features, and proper state management.
 *
 * Features:
 * - Built on Radix UI for accessibility and keyboard navigation
 * - Consistent styling with design system
 * - Support for checked, unchecked, and indeterminate states
 * - Focus and hover states
 * - Disabled state styling
 * - Dark mode support
 * - Custom check icon
 *
 * @example
 * ```tsx
 * <Checkbox
 *   checked={isChecked}
 *   onCheckedChange={setIsChecked}
 *   id="terms"
 * />
 * <label htmlFor="terms">Accept terms and conditions</label>
 * ```
 */

import * as CheckboxPrimitive from '@radix-ui/react-checkbox'
import { CheckIcon } from 'lucide-react'
import * as React from 'react'

import { cn } from '@/lib/utils'

/**
 * Checkbox component with consistent styling and accessibility features.
 *
 * This component wraps Radix UI's Checkbox primitive with custom styling
 * and includes a check icon indicator. It supports all standard checkbox
 * functionality including controlled and uncontrolled usage.
 *
 * @param props - The component props
 * @param props.className - Additional CSS classes to apply
 * @param props.ref - React ref for the checkbox element
 * @param props.checked - Controlled checked state
 * @param props.onCheckedChange - Callback fired when checked state changes
 * @returns The rendered checkbox component
 */
function Checkbox({ ref, className, ...props }: React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> & { ref?: React.RefObject<React.ComponentRef<typeof CheckboxPrimitive.Root> | null> }) {
  return (
    <CheckboxPrimitive.Root
      ref={ref}
      className={cn(
        'peer h-4 w-4 shrink-0 rounded-sm border border-gray-300 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-gray-400 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:focus-visible:ring-gray-800 data-[state=checked]:bg-blue-500 data-[state=checked]:text-white',
        className,
      )}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        className={cn('flex items-center justify-center text-current')}
      >
        <CheckIcon className="h-3.5 w-3.5" />
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  )
}
Checkbox.displayName = CheckboxPrimitive.Root.displayName

export { Checkbox }
