// Import the interfaces
import type {
  KeyModifiers,
  KeyPressedEvent,
  KeyReleasedEvent,
} from '@/types/services/events/keyboardEvents'

import { describe, expect, it } from 'vitest'

// Import AppEventType for type checking
import { AppEventType } from '@/types/services/events/eventTypes'

describe('keyboard Event Types', () => {
  it('should allow declaration using KeyModifiers interface', () => {
    const mods: KeyModifiers | undefined = undefined
    expect(mods).toBeUndefined()

    const example: KeyModifiers = {
      altKey: false,
      ctrlKey: true,
      shiftKey: true,
      metaKey: false,
    }
    expect(example.ctrlKey).toBe(true)
    expect(example.shiftKey).toBe(true)
  })

  it('should allow declaration using KeyPressedEvent interface', () => {
    const event: KeyPressedEvent | undefined = undefined
    expect(event).toBeUndefined()

    const example: KeyPressedEvent = {
      type: AppEventType.KEY_PRESSED,
      payload: {
        key: 'Shift',
        code: 'ShiftLeft',
        modifiers: { altKey: false, ctrlKey: false, shiftKey: true, metaKey: false },
        // originalEvent can be omitted
      },
    }
    expect(event).toBeUndefined() // Corrected: expect(event) instead of expect(example)
    expect(example.type).toBe(AppEventType.KEY_PRESSED)
    expect(example.payload.key).toBe('Shift')
    expect(example.payload.modifiers.shiftKey).toBe(true)
  })

  it('should allow declaration using KeyReleasedEvent interface', () => {
    const event: KeyReleasedEvent | undefined = undefined
    expect(event).toBeUndefined()

    const example: KeyReleasedEvent = {
      type: AppEventType.KEY_RELEASED,
      payload: {
        key: 'a',
        code: 'KeyA',
        modifiers: { altKey: false, ctrlKey: false, shiftKey: false, metaKey: false },
      },
    }
    expect(example.type).toBe(AppEventType.KEY_RELEASED)
    expect(example.payload.code).toBe('KeyA')
    expect(example.payload.modifiers.ctrlKey).toBe(false)
  })
})
