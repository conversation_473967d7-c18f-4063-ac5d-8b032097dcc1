/**
 * Element Services
 *
 * This module provides element manipulation services for the application.
 *
 * @module services/elements/element-actions
 */

// Import service implementations for type assertions
import type { ElementCreationService } from './elementCreationService'
import type { ElementDeleteService } from './elementDeleteService'
import type { ElementEditServiceImpl } from './elementEditService'
import type { ElementSelectionService } from './elementSelectionService'

import type { ElementFactory } from '@/core/factory'
import type { ShapeRepository } from '@/core/state/ShapeRepository'
import type { ServiceRegistry } from '@/services/core/registry'
import type { LoggerService } from '@/types/services/logging'

import { getServiceFactory } from '@/services/core/registry' // Added getServiceFactory
import { ServiceId } from '@/types/services/core/serviceIdentifier'
// import { getEventBus } from '@/services/core/event-bus'; // No longer needed directly
// import type { EventBus, AppEventMap } from '@/types/services/events'; // Unuse import type

// Re-export service classes
export { ElementCreationService } from './elementCreationService'
export { ElementDeleteService } from './elementDeleteService'
export { ElementEditServiceImpl as ElementEditService } from './elementEditService'
export { ElementSelectionService } from './elementSelectionService'

/**
 * Register all element services to the service registry
 *
 * @param registry Service registry
 * @param elementFactory Element factory
 * @param _shapeRepository Shape repository (unused)
 * @param logger Logger service
 * @returns Registered element service objects
 */
export function registerElementServices(
  registry: ServiceRegistry,
  elementFactory: ElementFactory,
  _shapeRepository: ShapeRepository,
  logger: LoggerService,
): Record<string, unknown> {
  const factory = getServiceFactory()

  // Create event bus instance via factory to ensure correct type (EventBus<AppEventMap>)
  const eventBus = factory.createEventBus()

  // Create services using the factory
  const creationService = (factory.createElementCreationService as unknown as (
    elementFactory: ElementFactory,
    logger: LoggerService
  ) => ElementCreationService)(elementFactory, logger)

  const editService = (factory.createElementEditService as unknown as (
    eventBus: unknown,
    logger: LoggerService
  ) => ElementEditServiceImpl)(eventBus, logger)

  // Pass the eventBus obtained from the factory
  const deleteService = (factory.createElementDeleteService as unknown as (
    eventBus: unknown,
    logger: LoggerService
  ) => ElementDeleteService)(eventBus, logger)

  const selectionService = (factory.createElementSelectionService as unknown as (
    eventBus: unknown,
    logger: LoggerService
  ) => ElementSelectionService)(eventBus, logger)

  // Register services
  registry.register(ServiceId.ElementCreationService as string, creationService)
  registry.register(ServiceId.ElementEditService as string, editService)
  registry.register(ServiceId.ElementDeleteService as string, deleteService)
  registry.register(ServiceId.ElementSelectionService as string, selectionService)

  logger.info('Element services registered successfully')

  return {
    creationService,
    editService,
    deleteService,
    selectionService,
  }
}
