/**
 * Central export module for all element-related types.
 *
 * @remarks
 * This module serves as the barrel export for all core element types including:
 * - Geometry primitives (points, bounding boxes).
 * - Path elements.
 * - Shape definitions.
 * - Design components.
 * - Pattern types.
 *
 * @module types/core/element/index
 * @see {@link BoundingBox} - Core geometry type.
 * @see {@link Point} - Coordinate point structure.
 * @see {@link ShapeElement} - Base shape interface.
 * @see {@link Text} - Text element type.
 * @see {@link Image} - Image element type.
 */

// Design elements
/** Re-exports all design element types from './design'. */
export * from './design/design'
// Pattern types
/** Re-exports all pattern types from './pattern'. */
export * from './elementPatternTypes'

// Core geometry types
/** Re-exports {@link BoundingBox} type from geometry. */
export type { BoundingBox } from './geometry/bounding-box'

/** Re-exports all from './geometry/point'. */
export * from './geometry/point'

// Path elements
/** Re-exports all path element types from './path'. */
export * from './path/path'

// Shape elements
/** Re-exports all shape element types from './shape'. */
export * from './shape/shape'
