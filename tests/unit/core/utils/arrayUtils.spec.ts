/**
 * @file arrayUtils.spec.ts
 * @description Unit tests for array utility functions
 */

import { describe, expect, it } from 'vitest'
import {
  chunk,
  difference,
  flatten,
  groupBy,
  intersection,
  range,
  shuffle,
  sortBy,
  union,
  unique,
} from '@/core/utils/arrayUtils'

describe('arrayUtils', () => {
  describe('chunk', () => {
    it('should split an array into chunks of the specified size', () => {
      // Arrange
      const array = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

      // Act
      const result = chunk(array, 3)

      // Assert
      expect(result).toEqual([[1, 2, 3], [4, 5, 6], [7, 8, 9], [10]])
    })

    it('should handle empty arrays', () => {
      expect(chunk([], 3)).toEqual([])
    })

    it('should handle chunk size larger than array length', () => {
      expect(chunk([1, 2, 3], 5)).toEqual([[1, 2, 3]])
    })

    it('should handle chunk size of 1', () => {
      expect(chunk([1, 2, 3], 1)).toEqual([[1], [2], [3]])
    })

    it('should handle invalid chunk sizes', () => {
      expect(chunk([1, 2, 3], 0)).toEqual([])
      expect(chunk([1, 2, 3], -1)).toEqual([])
    })
  })

  describe('unique', () => {
    it('should remove duplicate values from an array', () => {
      // Arrange
      const array = [1, 2, 2, 3, 4, 4, 5]

      // Act
      const result = unique(array)

      // Assert
      expect(result).toEqual([1, 2, 3, 4, 5])
    })

    it('should handle empty arrays', () => {
      expect(unique([])).toEqual([])
    })

    it('should handle arrays with no duplicates', () => {
      expect(unique([1, 2, 3])).toEqual([1, 2, 3])
    })

    it('should handle arrays with objects', () => {
      const obj1 = { id: 1 }
      const obj2 = { id: 2 }
      const array = [obj1, obj2, obj1]

      expect(unique(array)).toEqual([obj1, obj2])
    })

    it('should use the provided key function for objects', () => {
      const array = [
        { id: 1, name: 'Alice' },
        { id: 2, name: 'Bob' },
        { id: 1, name: 'Charlie' },
      ]

      const result = unique(array, item => item.id)

      // Should keep the first occurrence of each id
      expect(result).toEqual([
        { id: 1, name: 'Alice' },
        { id: 2, name: 'Bob' },
      ])
    })
  })

  describe('flatten', () => {
    it('should flatten a nested array', () => {
      // Arrange
      const array = [1, [2, 3], [4, [5, 6]]]

      // Act
      const result = flatten(array)

      // Assert
      expect(result).toEqual([1, 2, 3, 4, 5, 6])
    })

    it('should handle empty arrays', () => {
      expect(flatten([])).toEqual([])
    })

    it('should handle arrays with no nesting', () => {
      expect(flatten([1, 2, 3])).toEqual([1, 2, 3])
    })

    it('should handle arrays with empty nested arrays', () => {
      expect(flatten([1, [], 2, []])).toEqual([1, 2])
    })

    it('should handle deeply nested arrays', () => {
      expect(flatten([1, [2, [3, [4, [5]]]]])).toEqual([1, 2, 3, 4, 5])
    })
  })

  describe('groupBy', () => {
    it('should group array elements by the specified key', () => {
      // Arrange
      const array = [
        { id: 1, category: 'A' },
        { id: 2, category: 'B' },
        { id: 3, category: 'A' },
        { id: 4, category: 'C' },
        { id: 5, category: 'B' },
      ]

      // Act
      const result = groupBy(array, item => item.category)

      // Assert
      expect(result).toEqual({
        A: [
          { id: 1, category: 'A' },
          { id: 3, category: 'A' },
        ],
        B: [
          { id: 2, category: 'B' },
          { id: 5, category: 'B' },
        ],
        C: [
          { id: 4, category: 'C' },
        ],
      })
    })

    it('should handle empty arrays', () => {
      expect(groupBy([], item => item)).toEqual({})
    })

    it('should handle arrays with primitive values', () => {
      const array = [1, 2, 1, 3, 2]
      const result = groupBy(array, item => item)

      expect(result).toEqual({
        1: [1, 1],
        2: [2, 2],
        3: [3],
      })
    })

    it('should handle custom key functions', () => {
      const array = ['apple', 'banana', 'cherry', 'date', 'elderberry']
      const result = groupBy(array, item => item.length)

      expect(result).toEqual({
        5: ['apple'],
        6: ['banana', 'cherry'],
        4: ['date'],
        10: ['elderberry'],
      })
    })
  })

  describe('sortBy', () => {
    it('should sort array elements by the specified key', () => {
      // Arrange
      const array = [
        { id: 3, name: 'Charlie' },
        { id: 1, name: 'Alice' },
        { id: 2, name: 'Bob' },
      ]

      // Act
      const result = sortBy(array, item => item.id)

      // Assert
      expect(result).toEqual([
        { id: 1, name: 'Alice' },
        { id: 2, name: 'Bob' },
        { id: 3, name: 'Charlie' },
      ])
    })

    it('should handle empty arrays', () => {
      expect(sortBy([], item => item)).toEqual([])
    })

    it('should handle arrays with primitive values', () => {
      const array = [3, 1, 4, 1, 5, 9, 2]
      const result = sortBy(array, item => item)

      expect(result).toEqual([1, 1, 2, 3, 4, 5, 9])
    })

    it('should handle custom key functions', () => {
      const array = ['apple', 'banana', 'cherry', 'date', 'elderberry']
      const result = sortBy(array, item => item.length)

      expect(result).toEqual(['date', 'apple', 'banana', 'cherry', 'elderberry'])
    })

    it('should handle descending order', () => {
      const array = [3, 1, 4, 1, 5, 9, 2]
      const result = sortBy(array, item => item, 'desc')

      expect(result).toEqual([9, 5, 4, 3, 2, 1, 1])
    })
  })

  describe('shuffle', () => {
    it('should shuffle an array', () => {
      // Arrange
      const array = [1, 2, 3, 4, 5]

      // Act
      const result = shuffle([...array]) // Create a copy to avoid modifying the original

      // Assert
      expect(result).toHaveLength(array.length)
      expect(result).toEqual(expect.arrayContaining(array))

      // This test could occasionally fail due to the random nature of shuffling
      // but it's very unlikely that a shuffle would return the exact same order
      const isSameOrder = result.every((item, index) => item === array[index])
      expect(isSameOrder).toBe(false)
    })

    it('should handle empty arrays', () => {
      expect(shuffle([])).toEqual([])
    })

    it('should handle arrays with a single element', () => {
      expect(shuffle([1])).toEqual([1])
    })
  })

  describe('intersection', () => {
    it('should return the intersection of two arrays', () => {
      // Arrange
      const array1 = [1, 2, 3, 4]
      const array2 = [3, 4, 5, 6]

      // Act
      const result = intersection(array1, array2)

      // Assert
      expect(result).toEqual([3, 4])
    })

    it('should handle empty arrays', () => {
      expect(intersection([], [1, 2, 3])).toEqual([])
      expect(intersection([1, 2, 3], [])).toEqual([])
      expect(intersection([], [])).toEqual([])
    })

    it('should handle arrays with no common elements', () => {
      expect(intersection([1, 2, 3], [4, 5, 6])).toEqual([])
    })

    it('should handle arrays with duplicate elements', () => {
      expect(intersection([1, 2, 2, 3], [2, 2, 3, 4])).toEqual([2, 3])
    })

    it('should use the provided key function for objects', () => {
      const array1 = [
        { id: 1, name: 'Alice' },
        { id: 2, name: 'Bob' },
        { id: 3, name: 'Charlie' },
      ]

      const array2 = [
        { id: 2, name: 'Robert' },
        { id: 3, name: 'Charles' },
        { id: 4, name: 'David' },
      ]

      const result = intersection(array1, array2, item => item.id)

      // Should return objects from the first array
      expect(result).toEqual([
        { id: 2, name: 'Bob' },
        { id: 3, name: 'Charlie' },
      ])
    })
  })

  describe('difference', () => {
    it('should return the difference between two arrays', () => {
      // Arrange
      const array1 = [1, 2, 3, 4]
      const array2 = [3, 4, 5, 6]

      // Act
      const result = difference(array1, array2)

      // Assert
      expect(result).toEqual([1, 2])
    })

    it('should handle empty arrays', () => {
      expect(difference([], [1, 2, 3])).toEqual([])
      expect(difference([1, 2, 3], [])).toEqual([1, 2, 3])
      expect(difference([], [])).toEqual([])
    })

    it('should handle arrays with no common elements', () => {
      expect(difference([1, 2, 3], [4, 5, 6])).toEqual([1, 2, 3])
    })

    it('should handle arrays with duplicate elements', () => {
      expect(difference([1, 2, 2, 3], [2, 3, 4])).toEqual([1])
    })

    it('should use the provided key function for objects', () => {
      const array1 = [
        { id: 1, name: 'Alice' },
        { id: 2, name: 'Bob' },
        { id: 3, name: 'Charlie' },
      ]

      const array2 = [
        { id: 2, name: 'Robert' },
        { id: 3, name: 'Charles' },
        { id: 4, name: 'David' },
      ]

      const result = difference(array1, array2, item => item.id)

      expect(result).toEqual([
        { id: 1, name: 'Alice' },
      ])
    })
  })

  describe('union', () => {
    it('should return the union of two arrays', () => {
      // Arrange
      const array1 = [1, 2, 3]
      const array2 = [3, 4, 5]

      // Act
      const result = union(array1, array2)

      // Assert
      expect(result).toEqual([1, 2, 3, 4, 5])
    })

    it('should handle empty arrays', () => {
      expect(union([], [1, 2, 3])).toEqual([1, 2, 3])
      expect(union([1, 2, 3], [])).toEqual([1, 2, 3])
      expect(union([], [])).toEqual([])
    })

    it('should handle arrays with duplicate elements', () => {
      expect(union([1, 2, 2, 3], [2, 3, 4])).toEqual([1, 2, 3, 4])
    })

    it('should use the provided key function for objects', () => {
      const array1 = [
        { id: 1, name: 'Alice' },
        { id: 2, name: 'Bob' },
      ]

      const array2 = [
        { id: 2, name: 'Robert' },
        { id: 3, name: 'Charlie' },
      ]

      const result = union(array1, array2, item => item.id)

      // Should keep the first occurrence of each id
      expect(result).toEqual([
        { id: 1, name: 'Alice' },
        { id: 2, name: 'Bob' },
        { id: 3, name: 'Charlie' },
      ])
    })
  })

  describe('range', () => {
    it('should generate a range of numbers', () => {
      expect(range(5)).toEqual([0, 1, 2, 3, 4])
      expect(range(1, 6)).toEqual([1, 2, 3, 4, 5])
      expect(range(1, 10, 2)).toEqual([1, 3, 5, 7, 9])
    })

    it('should handle negative ranges', () => {
      expect(range(0, -5, -1)).toEqual([0, -1, -2, -3, -4])
    })

    it('should handle invalid ranges', () => {
      expect(range(0)).toEqual([])
      expect(range(5, 1)).toEqual([])
      expect(range(1, 10, -1)).toEqual([])
    })

    it('should handle step of 0', () => {
      expect(range(1, 5, 0)).toEqual([])
    })
  })
})
