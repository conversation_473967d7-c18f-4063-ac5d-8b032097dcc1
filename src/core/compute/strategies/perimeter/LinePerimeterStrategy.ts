/**
 * Perimeter/Length Calculation Strategy for Line Elements
 *
 * @remarks
 * This strategy implements the {@link PerimeterCalculatorStrategy} for calculating
 * the length (considered as perimeter for a 1D element) of Line elements
 * ({@link CoreElementType.LINE}).
 *
 * It retrieves the `start` and `end` points of the line element and delegates
 * the distance calculation between these two points to the `calculateLineLengthUtil`
 * function from `../../../../lib/utils/geometry/lineUtils`.
 *
 * The strategy validates that the element is of type `LINE` and that its
 * start and end points are valid. Note that `Path.Line` stores these points
 * directly, and they are typically relative to the element's `position` if it's
 * a `ShapeElement`. However, `calculateLineLengthUtil` usually expects absolute
 * coordinates or calculates based on the direct difference, assuming the points
 * are in the same coordinate system. If line points are relative, they should be
 * made absolute before calling the utility, or the utility must handle relative points.
 * Currently, this strategy passes the points as-is from `Path.Line`.
 *
 * @module core/compute/strategies/perimeter/LinePerimeterStrategy
 * @see {@link Path.Line} for the line element type definition.
 * @see {@link PerimeterCalculatorStrategy}
 * @see {@link calculateLineLengthUtil}
 */
import type { PerimeterCalculatorStrategy } from '../../../../types/core/compute'
import type { PointData as IPoint } from '../../../../types/core/element/geometry/point' // Use PointData, alias as IPoint
import type {
  Element,
  Path,
} from '../../../../types/core/elementDefinitions'
import { calculateLineLength as calculateLineLengthUtil } from '../../../../lib/utils/geometry/lineUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

/**
 * Implements the {@link PerimeterCalculatorStrategy} for {@link CoreElementType.LINE} elements.
 */
export class LinePerimeterStrategy implements PerimeterCalculatorStrategy {
  /**
   * Calculates the length (perimeter) of a Line element.
   *
   * @param element - The line element, expected to be of type {@link ShapeElement}
   *                  and specifically {@link CoreElementType.LINE}, conforming to {@link Path.Line}.
   * @returns The calculated length of the line.
   * @throws {@link CoreError} if the provided element is `null`.
   * @throws {@link CoreError} if the element type is not `LINE`.
   * @throws {@link CoreError} if the line's `start` or `end` points are invalid or missing.
   */
  public calculatePerimeter(element: Element): number {
    console.warn(`[LinePerimeterStrategy] 🚀 calculatePerimeter 开始执行`, {
      elementId: element?.id,
      elementType: element?.type,
    })

    if (element == null) { // Should not happen if called from ComputeFacade which checks element
      console.error(`[LinePerimeterStrategy] ❌ Element cannot be null`)
      throw new CoreError(
        ErrorType.InvalidParameter,
        'Invalid element: null or undefined',
        undefined,
        { component: 'LinePerimeterStrategy', operation: 'calculatePerimeter' },
      )
    }

    if (element.type !== CoreElementType.LINE) {
      console.error(`[LinePerimeterStrategy] ❌ 元素类型不匹配: 期望 LINE, 实际 ${element.type}`)
      throw new CoreError(
        ErrorType.InvalidElementType,
        `Expected element type LINE, got '${element.type}' for ID ${element.id}`,
        undefined,
        { component: 'LinePerimeterStrategy', operation: 'calculatePerimeter', target: element.id },
      )
    }

    const lineElement = element as unknown as Path.Line // Added unknown for type safety

    // 线段元素的坐标处理：
    // 1. 优先使用顶层的绝对坐标 (element.start, element.end)
    // 2. 如果没有，则使用 properties 中的坐标（可能是绝对坐标或相对坐标）
    let startP: IPoint | undefined = lineElement.start as IPoint | undefined
    let endP: IPoint | undefined = lineElement.end as IPoint | undefined

    console.warn(`[LinePerimeterStrategy] 📍 坐标检查 - 顶层坐标:`, {
      'lineElement.start': lineElement.start,
      'lineElement.end': lineElement.end,
      startP,
      endP,
    })

    // 如果顶层没有坐标，尝试从 properties 获取
    if (!startP || !endP) {
      startP = lineElement.properties?.start as IPoint | undefined
      endP = lineElement.properties?.end as IPoint | undefined
      console.warn(`[LinePerimeterStrategy] 📍 坐标检查 - properties坐标:`, {
        'lineElement.properties?.start': lineElement.properties?.start,
        'lineElement.properties?.end': lineElement.properties?.end,
        startP,
        endP,
      })
    }

    if (startP == null || endP == null
      || typeof startP.x !== 'number' || typeof startP.y !== 'number'
      || typeof endP.x !== 'number' || typeof endP.y !== 'number'
      || !Number.isFinite(startP.x) || !Number.isFinite(startP.y)
      || !Number.isFinite(endP.x) || !Number.isFinite(endP.y)
    ) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Line element (ID: ${element.id}) is missing valid start or end points`,
        undefined,
        { component: 'LinePerimeterStrategy', operation: 'calculatePerimeter', target: element.id, metadata: { startP, endP } },
      )
    }

    // Delegate to the utility function
    const result = calculateLineLengthUtil(startP, endP)
    console.warn(`[LinePerimeterStrategy] ✅ 计算完成`, {
      elementId: element.id,
      startPoint: startP,
      endPoint: endP,
      calculatedLength: result,
    })
    return result
  }

  /**
   * Returns the element type that this strategy is responsible for.
   * @returns {@link CoreElementType.LINE} - The line element type that this strategy handles
   */
  public getElementType(): CoreElementType {
    return CoreElementType.LINE
  }
}
