// import Point from '@/types/core/element/geometry/point'; // Point interface (IPoint) // Unused
import type {
  ErgonomicsEvaluationResult,
  PathwayCheckResult,
  SpacePlanningStrategy,
  SpaceType,
} from '@/types/core/compute'
/**
 * Storage Room/Closet Planning Strategy
 * Provides planning functionality for storage rooms and closets, including storage capacity, space utilization, ventilation requirements, etc.
 */
import type {
  Element,
  ShapeElement, // For properties access
} from '@/types/core/elementDefinitions'
// import { CoreError, ErrorType } from '@/services/system/error-service'; // Commented out as unused for now
// import {
//     calculateSpaceUtilization,
//     checkPathwaysWidth
// } from '@/lib/utils/space/spacePlanning'; // Module not found
import { BoundingBoxClass } from '@/lib/utils/geometry' // BoundingBoxClass is exported from here
import {
  ElementType as CoreElementType, // For properties access
} from '@/types/core/elementDefinitions'
// calculatePointToPointDistance might be needed for some internal helpers if they were kept

// Helper to get BoundingBoxClass from an Element
function getElementBoundingBoxClass(element: Element): BoundingBoxClass | null {
  if (element == null)
    return null
  if (element.type === CoreElementType.RECTANGLE || element.type === CoreElementType.SQUARE) {
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement.position != null && shapeElement.properties != null
      && typeof shapeElement.properties.width === 'number'
      && typeof shapeElement.properties.height === 'number') {
      return new BoundingBoxClass(
        shapeElement.position.x - shapeElement.properties.width / 2,
        shapeElement.position.y - shapeElement.properties.height / 2,
        shapeElement.properties.width,
        shapeElement.properties.height,
      )
    }
  }
  if (element.type === CoreElementType.CIRCLE) {
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement.position != null && shapeElement.properties != null
      && typeof shapeElement.properties.radius === 'number') {
      return new BoundingBoxClass(
        shapeElement.position.x - shapeElement.properties.radius,
        shapeElement.position.y - shapeElement.properties.radius,
        shapeElement.properties.radius * 2,
        shapeElement.properties.radius * 2,
      )
    }
  }
  // This helper might need to be expanded or use ComputeFacade for complex shapes
  console.warn(`[StorageRoomPlanningStrategy] getElementBoundingBoxClass: Bounding box for type ${element.type} not fully implemented or properties missing.`)
  return null
}

/**
 * Storage Room/Closet Planning Strategy
 */
export class StorageRoomPlanningStrategy implements SpacePlanningStrategy {
  public calculateSpaceUtilization(/* elements: Element[], roomBoundaryElement: Element */): number { // Params unused due to commented out body
    // const roomBBoxClass = getElementBoundingBoxClass(roomBoundaryElement);
    // if (!roomBBoxClass) {
    //   throw new CoreError( ErrorType.INVALID_PARAMETER, 'Room boundary must be an element from which a bounding box can be derived.');
    // }
    // return calculateSpaceUtilization(elements, roomBBoxClass);
    console.warn('[StorageRoomPlanningStrategy] calculateSpaceUtilization is temporarily disabled due to missing dependencies.')
    return 0
  }

  public checkPathwayWidth(/* elements: Element[], pathways: Path.Line[], minWidth: number */): PathwayCheckResult[] { // Params unused due to commented out body
    // if (!Array.isArray(elements)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Elements must be an array');
    // if (!Array.isArray(pathways)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Pathways must be an array');
    // if (typeof minWidth !== 'number' || minWidth <= 0) {
    //   throw new CoreError(ErrorType.INVALID_PARAMETER, 'Minimum width must be a positive number');
    // }

    // const formattedPathways = pathways.map((path, index) => {
    //     const lineForUtil = {
    //         id: path.id ?? `pathway-${index}`,
    //         start: path.start as Point,
    //         end: path.end as Point,
    //         type: CoreElementType.LINE,
    //         getSubType: () => CoreElementType.LINE,
    //     } as any;
    //     return { id: path.id ?? `pathway-${index + 1}`, line: lineForUtil as import('@/lib/utils/element/path/lineImplementation').Line };
    // });

    // return checkPathwaysWidth(elements, formattedPathways, minWidth);
    console.warn('[StorageRoomPlanningStrategy] checkPathwayWidth is temporarily disabled due to missing dependencies.')
    return []
  }

  public calculateStorageCapacity(elements: Element[]): { totalVolume: number, shelfLength: number, hangingLength: number, drawerVolume: number, recommendations: string[] } {
    let totalVolume = 0
    let shelfLength = 0
    let hangingLength = 0
    let drawerVolume = 0
    const recommendations: string[] = []

    for (const element of elements) {
      const el = element as ShapeElement // Assume storage elements are ShapeElements
      const bbox = getElementBoundingBoxClass(el)
      if (!bbox)
        continue

      const volume = bbox.width * bbox.height * (el.properties?.depth as number || 0.3) // Assume default depth if not specified
      const categoryValue = el.properties?.category
      const category = (typeof categoryValue === 'string' ? categoryValue.toLowerCase() : '') || ''
      const customTypeValue = el.properties?.customType
      const customType = (typeof customTypeValue === 'string' ? customTypeValue.toLowerCase() : '') || ''

      if (category === 'shelf' || customType === 'shelf') {
        const levels = (el.properties?.levels as number) || 1
        shelfLength += bbox.width * levels
        totalVolume += volume * levels // This volume is more like footprint * depth * levels
      }
      else if (category === 'wardrobe' || customType === 'wardrobe' || category === 'closet' || customType === 'closet') {
        if (el.properties?.hasHangingRod !== false)
          hangingLength += bbox.width
        totalVolume += volume
      }
      else if (category === 'drawer_unit' || customType === 'drawer') {
        drawerVolume += volume // This is volume of the unit, not sum of drawer internal volumes
        totalVolume += volume
      }
      else if (category === 'cabinet' || customType === 'cabinet' || category === 'storage_unit' || customType === 'storage') {
        totalVolume += volume
      }
    }

    const roomArea = this.calculateRoomArea(elements)
    if (roomArea > 0) {
      const volumeToAreaRatio = totalVolume / roomArea
      if (volumeToAreaRatio < 0.5)
        recommendations.push(`Low storage capacity (ratio: ${volumeToAreaRatio.toFixed(2)}), consider adding more or taller units.`)
    }
    if (hangingLength < 1.5)
      recommendations.push(`Insufficient hanging space (${hangingLength.toFixed(2)}m), aim for at least 1.5m.`)
    if (shelfLength < 3)
      recommendations.push(`Insufficient shelf length (${shelfLength.toFixed(2)}m), aim for at least 3m.`)
    if (drawerVolume < 0.3)
      recommendations.push(`Insufficient drawer volume (${drawerVolume.toFixed(2)}m³), aim for at least 0.3m³.`)

    return { totalVolume, shelfLength, hangingLength, drawerVolume, recommendations }
  }

  public evaluateSpaceEfficiency(elements: Element[]): { efficiency: number, recommendations: string[] } {
    const roomArea = this.calculateRoomArea(elements)
    if (roomArea <= 0)
      return { efficiency: 0, recommendations: ['Room area is zero or invalid.'] }

    let storageFootprint = 0
    for (const element of elements) {
      const el = element as ShapeElement
      const categoryValue = el.properties?.category
      const category = (typeof categoryValue === 'string' ? categoryValue.toLowerCase() : '') || ''
      const customTypeValue = el.properties?.customType
      const customType = (typeof customTypeValue === 'string' ? customTypeValue.toLowerCase() : '') || ''
      if (['shelf', 'wardrobe', 'closet', 'drawer_unit', 'cabinet', 'storage_unit', 'storage'].includes(category)
        || ['shelf', 'wardrobe', 'closet', 'drawer', 'cabinet', 'storage'].includes(customType)
      ) {
        const bbox = getElementBoundingBoxClass(el)
        if (bbox)
          storageFootprint += bbox.width * bbox.height
      }
    }
    const efficiency = roomArea > 0 ? storageFootprint / roomArea : 0
    const recommendations: string[] = [`Storage footprint efficiency: ${(efficiency * 100).toFixed(0)}%.`]
    if (efficiency < 0.4)
      recommendations.push('Low efficiency, consider more vertical storage or optimizing layout.')

    return { efficiency, recommendations }
  }

  public evaluateErgonomics(/* elements: Element[], deskElement?: Element, chairElement?: Element */): ErgonomicsEvaluationResult { // Parameters elements, deskElement, chairElement are declared but their value is never read.
    const issues: string[] = []
    const recommendations: string[] = []
    // Ergonomics in a storage room might relate to accessibility of items
    recommendations.push('Ensure frequently used items are stored between waist and shoulder height.')
    recommendations.push('Use clear labeling for storage containers.')
    // if (deskElement) { // If there's a small work/sorting surface
    //     const deskShape = deskElement as Shape.Rectangle;
    //     const deskHeight = deskShape.height ?? 0.8;
    //     if (deskHeight < 0.75 || deskHeight > 0.9) issues.push(`Work surface height ${deskHeight.toFixed(2)}m is not ideal.`);
    // }
    return { isValid: issues.length === 0, issues, recommendations }
  }

  public evaluateVentilation(elements: Element[]): { isValid: boolean, issues: string[], recommendations: string[] } {
    const issues: string[] = []
    const recommendations: string[] = []
    let hasWindow = false
    let hasVent = false
    for (const element of elements) {
      if (element.type === CoreElementType.WINDOW)
        hasWindow = true
      if ((element as ShapeElement).properties?.customType === 'vent' || element.type === ('ventilation_unit' as CoreElementType))
        hasVent = true
    }
    if (!hasWindow && !hasVent) {
      issues.push('Storage room lacks adequate ventilation. This can lead to stuffiness or moisture buildup.')
      recommendations.push('Consider adding a ventilation fan or ensuring door allows for air circulation.')
    }
    recommendations.push('Ensure storage units allow for some air flow around items.')
    return { isValid: issues.length === 0, issues, recommendations }
  }

  private calculateRoomArea(elements: Element[]): number {
    const roomElement = elements.find(el => (el as ShapeElement).properties?.isRoomBoundary === true || el.type === ('room_boundary' as CoreElementType))
    if (roomElement) {
      const bbox = getElementBoundingBoxClass(roomElement)
      return bbox ? bbox.width * bbox.height : 0
    }
    const overallBBox = this.calculateOverallBoundingBox(elements)
    return overallBBox ? overallBBox.width * overallBBox.height : 0
  }

  private calculateOverallBoundingBox(elements: Element[]): BoundingBoxClass | null {
    if (elements == null || elements.length === 0)
      return null
    let minX = Infinity
    let minY = Infinity
    let maxX = -Infinity
    let maxY = -Infinity
    let hasBBox = false
    for (const element of elements) {
      const bbox = getElementBoundingBoxClass(element)
      if (bbox) {
        hasBBox = true
        minX = Math.min(minX, bbox.position.x)
        minY = Math.min(minY, bbox.position.y)
        maxX = Math.max(maxX, bbox.position.x + bbox.width)
        maxY = Math.max(maxY, bbox.position.y + bbox.height)
      }
    }
    return hasBBox ? new BoundingBoxClass(minX, minY, maxX - minX, maxY - minY) : null
  }

  public getSpaceType(): SpaceType {
    return 'storage'
  }
}
