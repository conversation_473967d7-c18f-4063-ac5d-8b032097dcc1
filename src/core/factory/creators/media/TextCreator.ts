import type {
  CreateTextParams,
} from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point'
import type {
  Text, // Import Text namespace
} from '@/types/core/elementDefinitions'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
/**
 * Creator for Text Media Elements
 *
 * @remarks
 * This class implements the {@link ShapeCreator} interface to specialize in creating
 * Text ({@link CoreElementType.TEXT}) media elements. It utilizes `createCommonProperties`
 * for base attributes and then adds text-specific properties.
 *
 * @module core/factory/creators/media/TextCreator
 * @see {@link ShapeCreator}
 * @see {@link CoreElementType.TEXT}
 * @see {@link Text.Text}
 * @see {@link CreateTextParams}
 */
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  ElementType as CoreElementType, // Import Text namespace
} from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes' // Added MinorCategory
import { ShapeCreator } from '../shape/ShapeCreator'
// ensureCompleteMetadata is handled by createCommonProperties

export class TextCreator extends ShapeCreator<Text.Text, CreateTextParams> {
  constructor() {
    super(CoreElementType.TEXT)
  }

  /**
   * Creates a Text {@link ShapeElement} based on the provided parameters.
   *
   * @param params - A {@link CreateTextParams} object
   *                 containing all necessary information for text element creation.
   * @returns A Promise resolving to the created {@link Text.Text} element.
   */
  public async create(params: CreateTextParams): Promise<Text.Text> {
    if (params.type !== CoreElementType.TEXT && params.type !== 'text') {
      throw new CoreError(ErrorType.InvalidPayload, `TextCreator cannot create type: ${String(params.type)}`)
    }

    const { id } = params
    if (!id)
      throw new CoreError(ErrorType.InvalidPayload, 'Text element requires an ID.')
    if (params.position == null)
      throw new CoreError(ErrorType.InvalidPayload, 'Text element requires a position.')

    const commonProps = this.createCommonProperties(id, params)

    // Text specific properties from CreateTextParams, with fallbacks
    const {
      text,
      fontSize,
      fontFamily,
      fontWeight,
      fontStyle,
      textAlign,
      textBaseline,
      lineHeight,
    } = params

    const defaultTextAlign: CanvasTextAlign = 'left'
    const defaultTextBaseline: CanvasTextBaseline = 'top'

    const textElement: Text.Text = {
      ...commonProps,
      // type: CoreElementType.TEXT, // Already set by commonProps from params.type
      // position: commonProps.position, // Already in commonProps
      // rotation: commonProps.rotation, // Already in commonProps
      // visible: commonProps.visible, // Already in commonProps
      // locked: commonProps.locked, // Already in commonProps
      // selectable: commonProps.selectable, // Already in commonProps
      // draggable: commonProps.draggable, // Already in commonProps
      // showHandles: commonProps.showHandles, // Already in commonProps
      // metadata: commonProps.metadata, // Already in commonProps
      // layer: commonProps.layer, // Already in commonProps
      // zIndex: commonProps.zIndex, // Already in commonProps
      // fill: commonProps.fill, // Already in commonProps (can be text-specific)
      // stroke: commonProps.stroke, // Already in commonProps
      // strokeWidth: commonProps.strokeWidth, // Already in commonProps
      // opacity: commonProps.opacity, // Already in commonProps
      // majorCategory: commonProps.majorCategory, // Already in commonProps
      // minorCategory: commonProps.minorCategory, // Already in commonProps
      // zLevelId: commonProps.zLevelId, // Already in commonProps
      // isFixedCategory: commonProps.isFixedCategory, // Already in commonProps

      // Text specific properties
      content: text ?? 'Text',
      text: text ?? 'Text', // Required by Text interface
      fontSize: fontSize ?? 16,
      fontFamily: fontFamily ?? 'Arial',
      fontWeight: fontWeight ?? 'normal',
      fontStyle: fontStyle ?? 'normal',
      textAlign: textAlign ?? defaultTextAlign,
      textBaseline: textBaseline ?? defaultTextBaseline,
      lineHeight, // Optional, no default here if not provided

      // Custom properties from params, potentially merged with any defaults if necessary
      properties: {
        // 设置默认的成本相关属性
        costUnitPrice: 1,
        costMultiplierOrCount: 0,
        costBasis: 'unit',
        // 然后合并其他属性
        ...(params.properties || {}),
      },
    }

    return textElement
  }

  /**
   * Creates a default Text element.
   *
   * @param id - The unique identifier for the default text element.
   * @param position - The {@link PointData} for the default text's position.
   * @param majorCategoryOverride - Optional override for majorCategory.
   * @param minorCategoryOverride - Optional override for minorCategory.
   * @param zLevelIdOverride - Optional override for zLevelId.
   * @param isFixedCategoryOverride - Optional override for isFixedCategory.
   * @returns A Promise resolving to the created default {@link Text.Text}.
   */
  public async createDefault(
    id: string,
    position: PointData,
    majorCategoryOverride?: MajorCategory,
    minorCategoryOverride?: MinorCategory,
    zLevelIdOverride?: string,
    isFixedCategoryOverride?: boolean,
  ): Promise<Text.Text> {
    const params: CreateTextParams = {
      id,
      type: CoreElementType.TEXT,
      majorCategory: majorCategoryOverride ?? MajorCategory.FURNITURE, // Changed to FURNITURE
      minorCategory: minorCategoryOverride || 'text' as MinorCategory, // Default minorCategory for text
      zLevelId: zLevelIdOverride,
      isFixedCategory: isFixedCategoryOverride,
      position,
      text: 'Hello Text',
      fontSize: 24,
      fontFamily: 'Arial',
      fill: '#333333',
      metadata: { name: `Default Text ${id.substring(0, 8)}` }, // Partial metadata
      visible: true,
      locked: false,
      rotation: 0,
      selectable: true,
      draggable: true,
      showHandles: true,
      properties: {
        initialContent: 'Placeholder',
        // 设置默认的成本相关属性
        costUnitPrice: 1,
        costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1
        costBasis: 'unit',
        // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
        computedAreaStatus: 'none',
        computedPerimeterStatus: 'none',
        computedLengthStatus: 'none',
      }, // Example of default custom properties
    }
    return this.create(params)
  }
}
