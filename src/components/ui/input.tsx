/**
 * Input Component
 *
 * A styled input component that provides consistent styling and behavior
 * across the application. Built on top of the native input element with
 * enhanced styling and accessibility features.
 *
 * Features:
 * - Consistent styling with other form elements
 * - Support for all native input types
 * - File input styling
 * - Focus and hover states
 * - Disabled state styling
 * - Responsive text size
 */

import * as React from 'react'

import { cn } from '@/lib/utils'

/**
 * Input component with consistent styling
 *
 * @param props - The component props
 * @param props.className - Additional CSS classes to apply
 * @param props.type - The type of input (text, number, file, etc.)
 *
 * @example
 * ```tsx
 * <Input type="text" placeholder="Enter your name" />
 * <Input type="file" accept="image/*" />
 * <Input type="number" min={0} max={100} />
 * ```
 */
function Input({ ref, className, type, ...props }: React.ComponentProps<'input'> & { ref?: React.RefObject<HTMLInputElement | null> }) {
  return (
    <input
      type={type}
      className={cn(
        'flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
        className,
      )}
      ref={ref}
      {...props}
    />
  )
}
Input.displayName = 'Input'

export { Input }
