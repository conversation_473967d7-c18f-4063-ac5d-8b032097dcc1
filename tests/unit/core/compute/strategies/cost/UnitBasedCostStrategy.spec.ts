import { describe, expect, it, vi } from 'vitest'
import { UnitBasedCostStrategy } from '@/core/compute/strategies/cost/UnitBasedCostStrategy'
import { CoreError } from '@/services/errors'

describe('unitBasedCostStrategy', () => {
  const strategy = new UnitBasedCostStrategy()

  it('should return the correct shape type', () => {
    expect(strategy.getElementType()).toBe('all')
  })

  it('should calculate cost based on unit count', () => {
    const mockElement = {
      id: 'element-1',
      type: 'shape',
      getSubType: () => 'rectangle',
      getUnitCount: vi.fn().mockReturnValue(10), // 10 units
    }

    const unitCost = 50 // 50 per unit
    const result = strategy.calculateCost(mockElement, unitCost, {
      additionalCost: 100,
      discountRate: 0.1, // 折扣率应该是小数，不是百分比
      taxRate: 0.05, // 税率应该是小数，不是百分比
    })

    // 基本成本：10 * 50 = 500
    // 额外成本：100
    // 折扣：(500 + 100) * 0.1 = 60
    // 税前：(500 + 100) - 60 = 540
    // 税后：540 * 1.05 = 567
    // 由于元素使用了 quantity 而不是 getUnitCount，所以实际计算是：10 * 50 = 500
    // 额外成本：100
    // 折扣：(500 + 100) * 0.1 = 60
    // 税前：(500 + 100) - 60 = 540
    // 税后：540 * 1.05 = 567
    expect(result).toBeCloseTo(567)
  })

  it('should calculate cost with default options', () => {
    const mockElement = {
      id: 'element-1',
      type: 'shape',
      getSubType: () => 'rectangle',
      getUnitCount: vi.fn().mockReturnValue(10), // 10 units
      quantity: 10, // 添加 quantity 属性
    }

    const unitCost = 50 // 50 per unit
    const result = strategy.calculateCost(mockElement, unitCost)

    // 基本成本：10 * 50 = 500
    // 无额外成本、折扣或税率
    expect(result).toBe(50 * 10) // unitCost * quantity = 50 * 10 = 500
  })

  it('should throw error for invalid element type', () => {
    const mockElement = {
      id: 'invalid-1',
      type: 'invalid',
      quantity: 10, // 添加 quantity 属性
    }

    expect(() => strategy.calculateCost(mockElement, -50)).toThrow(CoreError)
    expect(() => strategy.calculateCost(mockElement, -50)).toThrow('Unit cost must be a non-negative number')
  })

  it('should throw error if element has no getUnitCount method', () => {
    const mockElement = {
      id: 'element-1',
      type: 'shape',
      getSubType: () => 'rectangle',
      // 故意不添加 quantity 属性和 getUnitCount 方法
    }

    expect(() => strategy.calculateCost(mockElement, 50)).toThrow(CoreError)
    expect(() => strategy.calculateCost(mockElement, 50)).toThrow('Element does not have a getUnitCount method')
  })

  it('should throw error if getUnitCount returns invalid value', () => {
    const mockElement = {
      id: 'element-1',
      type: 'shape',
      getSubType: () => 'rectangle',
      getUnitCount: vi.fn().mockReturnValue(-10), // Negative unit count
      quantity: -10, // 添加负数 quantity 属性
    }

    expect(() => strategy.calculateCost(mockElement, 50)).toThrow(CoreError)
    expect(() => strategy.calculateCost(mockElement, 50)).toThrow('Invalid unit count: -10')
  })

  it('should throw error if unit cost is invalid', () => {
    const mockElement = {
      id: 'element-1',
      type: 'shape',
      getSubType: () => 'rectangle',
      getUnitCount: vi.fn().mockReturnValue(10),
      quantity: 10, // 添加 quantity 属性
    }

    expect(() => strategy.calculateCost(mockElement, -50)).toThrow(CoreError)
    expect(() => strategy.calculateCost(mockElement, -50)).toThrow('Unit cost must be a non-negative number')
  })
})
