/**
 * Property Field Component
 *
 * A standardized field component for property inputs with consistent styling and layout.
 * Supports various input types and provides unified spacing and typography.
 *
 * Features:
 * - Consistent label and input styling
 * - Support for different input types (text, number, color, etc.)
 * - Optional unit display
 * - Error state handling
 * - Responsive layout options
 * - Placeholder and help text support
 *
 * @example
 * ```tsx
 * <PropertyField
 *   label="Width"
 *   type="number"
 *   value={width}
 *   onChange={setWidth}
 *   unit="mm"
 *   min={0}
 * />
 *
 * <PropertyField
 *   label="Fill Color"
 *   type="color"
 *   value={color}
 *   onChange={setColor}
 * />
 * ```
 */

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'

/**
 * Props for the PropertyField component
 */
interface PropertyFieldProps {
  /** Field label */
  label: string
  /** Input type */
  type?: 'text' | 'number' | 'color' | 'email' | 'password' | 'url'
  /** Current value */
  value: string | number
  /** Change handler */
  onChange: (value: string) => void
  /** Optional unit to display after the input */
  unit?: string
  /** Placeholder text */
  placeholder?: string
  /** Minimum value for number inputs */
  min?: number
  /** Maximum value for number inputs */
  max?: number
  /** Step value for number inputs */
  step?: number
  /** Whether the field is disabled */
  disabled?: boolean
  /** Whether the field is read-only */
  readOnly?: boolean
  /** Whether the field is required */
  required?: boolean
  /** Error message to display */
  error?: string
  /** Help text to display below the input */
  helpText?: string
  /** Additional CSS classes for the container */
  className?: string
  /** Additional CSS classes for the input */
  inputClassName?: string
  /** Custom input ID */
  id?: string
}

/**
 * PropertyField component for standardized property inputs.
 *
 * This component provides a consistent way to render labeled inputs with
 * proper spacing, typography, and optional features like units and error states.
 *
 * @param props - The component props
 * @param props.label - The field label text
 * @param props.type - The input type (text, number, color, etc.)
 * @param props.value - The current field value
 * @param props.onChange - Callback function when value changes
 * @param props.unit - Optional unit suffix to display
 * @param props.placeholder - Optional placeholder text
 * @param props.min - Minimum value for numeric inputs
 * @param props.max - Maximum value for numeric inputs
 * @param props.step - Step value for numeric inputs
 * @param props.disabled - Whether the field is disabled
 * @param props.readOnly - Whether the field is read-only
 * @param props.required - Whether the field is required
 * @param props.error - Error message to display
 * @param props.helpText - Help text to display below the field
 * @param props.className - Additional CSS classes
 * @param props.inputClassName - Additional CSS classes for the input element
 * @param props.id - Optional field ID
 * @returns The rendered property field
 */
export function PropertyField({
  label,
  type = 'text',
  value,
  onChange,
  unit,
  placeholder,
  min,
  max,
  step,
  disabled = false,
  readOnly = false,
  required = false,
  error,
  helpText,
  className,
  inputClassName,
  id,
}: PropertyFieldProps) {
  const fieldId = id ?? `field-${label.toLowerCase().replace(/\s+/g, '-')}`

  return (
    <div className={cn('space-y-1', className)}>
      <Label
        htmlFor={fieldId}
        className={cn(
          'text-sm font-medium',
          disabled && 'text-muted-foreground',
          error !== undefined && error !== null && error.trim() !== '' && 'text-destructive',
        )}
      >
        {label}
        {required && <span className="text-destructive ml-1">*</span>}
        {unit !== undefined && unit !== null && unit.trim() !== '' && (
          <span className="text-muted-foreground ml-1">
            (
            {unit}
            )
          </span>
        )}
      </Label>

      <div className="relative">
        <Input
          id={fieldId}
          type={type}
          value={value}
          onChange={e => onChange(e.target.value)}
          placeholder={placeholder}
          min={min}
          max={max}
          step={step}
          disabled={disabled}
          readOnly={readOnly}
          required={required}
          className={cn(
            'h-9',
            error !== undefined && error !== null && error.trim() !== '' && 'border-destructive focus-visible:ring-destructive',
            readOnly && 'bg-muted cursor-default',
            inputClassName,
          )}
        />
        {unit !== undefined && unit !== null && unit.trim() !== '' && type !== 'color' && (
          <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground pointer-events-none">
            {unit}
          </span>
        )}
      </div>

      {error !== undefined && error !== null && error.trim() !== '' && (
        <p className="text-xs text-destructive">
          {error}
        </p>
      )}

      {helpText !== undefined && helpText !== null && helpText.trim() !== '' && !(error !== undefined && error !== null && error.trim() !== '') && (
        <p className="text-xs text-muted-foreground">
          {helpText}
        </p>
      )}
    </div>
  )
}
