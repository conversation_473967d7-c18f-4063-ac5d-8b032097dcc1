/**
 * Geometry Utilities Index
 *
 * @remarks
 * This module serves as the central entry point for all geometry-related utility functions
 * and helper classes within the `lib/utils/geometry` directory. It consolidates and
 * re-exports functionalities from various submodules, covering areas such as:
 * - Geometric primitive classes: {@link PointClass}, {@link BoundingBoxClass}.
 * - Point utilities: Distance, rotation, scaling, midpoint, angle calculations.
 * - Coordinate system transformations.
 * - Shape-specific calculations: Area, perimeter, bounding box for rectangles,
 *   circles, ellipses, polygons, lines, and arcs.
 * - General bounding box utilities.
 *
 * Additionally, it exports a consolidated namespace {@link GeometryUtils} which groups
 * these utilities and classes for easier access.
 *
 * Note: Core type definitions (like interfaces for PointData, BoundingBox) should ideally be
 * imported directly from ` '@/types/core/...'` by consuming modules, rather than being
 * re-exported here. This file aims to primarily export utility *functions* and *classes*
 * defined within `lib/utils/geometry`.
 *
 * @module lib/utils/geometry/index
 */

// -----------------------------------------------------------------------------
// Export Core Geometry Models & Classes
// -----------------------------------------------------------------------------
// export * from './core'; // Removed: ./core seems problematic or empty

// Namespace for core models (optional, but can be useful for clarity)
// import * as CoreModels from './core'; // Removed
// export const GeometryModels = CoreModels; // Removed

import * as ArcUtilsExport from './arcUtils'
// -----------------------------------------------------------------------------
// Export Consolidated Geometry Utilities Namespace
// -----------------------------------------------------------------------------
import * as AreaUtilsExport from './areaUtils'
// REMOVED: import type { BoundingBox as IBoundingBox } from '../../../types/core/element/geometry/bounding-box';
// Consumers should import IBoundingBox directly from its source in types.

// import type { PointData as IPoint } from '../../../types/core/element/geometry/point'; // IPoint is used by some exported functions if they were to be typed more strictly here.
import { BoundingBoxClass } from './BoundingBoxClass' // Import BoundingBoxClass

// -----------------------------------------------------------------------------
// Helper Functions (ensurePointInstance is now imported from pointUtils)
// -----------------------------------------------------------------------------

// ensurePointInstance is now imported from pointUtils and available via GeometryUtils.Common

import * as CoordinatesUtilsExport from './coordinates'
import * as DistanceUtilsExport from './distanceUtils' // Corrected import
import * as EllipseUtilsExport from './ellipseUtils'
import * as IntersectionUtilsExport from './intersection' // Corrected import
import * as LineUtilsExport from './lineUtils' // createPointInstance is unused
import { PointClass /* , createPoint as createPointInstance */ } from './PointClass' // Export PointClass
// Import point utilities to be used within GeometryUtils.Common and for direct export
import {
  calculatePointToCircleDistance, // Also from pointUtils
  calculatePointToEllipseDistance, // Also from pointUtils
  ensurePointInstance as ensurePointInstanceFromUtils, // Import and alias
  arePointsEqual as pointUtilArePointsEqual,
  calculateAngle as pointUtilCalculateAngle,
  calculateDistance as pointUtilCalculateDistance,
  calculateMidpoint as pointUtilCalculateMidpoint,
  calculatePointToPointDistance as pointUtilCalculatePointToPointDistance,
  rotatePoint as pointUtilRotatePoint,
  scalePoint as pointUtilScalePoint,
} from './pointUtils' // Alias for BBox from an array of points
// import * as ShapeUtilsExport from './shapeUtils'; // Removed, module does not exist
import * as PolygonUtilsExport from './polygonUtils'

export { PointClass }

// Arc specific
export {
  calculateArcArcDistance,
  calculateArcLineDistance,
  calculateArcPointDistance,
} from './arcUtils' // Corrected path to arcUtils

// -----------------------------------------------------------------------------
// Export Shape-Specific Calculation Utilities
// -----------------------------------------------------------------------------

// -----------------------------------------------------------------------------
// Export Coordinate System and Transformation Utilities
// -----------------------------------------------------------------------------
export {
  calculatePointToLineDistance,
  clientToSvgPoint,
  mouseEventToSvgPoint,
  pointToLineDistance,
  rotatePointDegrees,
  svgToClientPoint,
  translatePoint,
} from './coordinates'

// Circle and Ellipse specific
export {
  calculateCircleArea,
  calculateCircleBoundingBox,
  calculateCirclePerimeter,
  calculateEllipseArea,
  calculateEllipseBoundingBox,
  calculateEllipsePerimeter,
} from './ellipseUtils' // Corrected path to ellipseUtils

// Line specific
export {
  calculateLineBoundingBox,
  calculateLineLength,
} from './lineUtils' // Corrected path to lineUtils

// No need to re-import common utilities here, they are already imported above and scoped to the module.
// Re-export point utilities with their original names for direct import by other modules
export {
  arePointsEqual,
  calculateAngle,
  calculateDistance,
  calculateMidpoint,
  calculatePointToCircleDistance,
  calculatePointToEllipseDistance,
  calculatePointToPointDistance,
  rotatePoint,
  scalePoint,
} from './pointUtils'

// Polygon specific
export {
  calculateArea as calculatePolygonArea,
  calculateBoundingBox as calculatePolygonBoundingBoxFromPoints,
  calculateCenter as calculatePolygonCenter,
  calculatePerimeter as calculatePolygonPerimeter,
  createRegularPolygon,
  isPointInside as isPointInsidePolygon,
  rotatePolygon,
  scalePolygon,
} from './polygonUtils' // Corrected path to polygonUtils

// -----------------------------------------------------------------------------
// Export General Bounding Box Utilities from Core
// -----------------------------------------------------------------------------
// Export BoundingBoxClass and its static methods with aliases for convenience
export { BoundingBoxClass }
// REMOVED: export type { IBoundingBox }; // Consumers should import IBoundingBox directly.

export const boundingBoxFromObject = BoundingBoxClass.fromObject
export const calculateRectangleBoundingBox = BoundingBoxClass.fromTwoPoints // Alias for BBox from two points, often used for rectangles
export const boundingBoxFromCenter = BoundingBoxClass.fromCenter
export const calculatePointsBoundingBox = BoundingBoxClass.fromPointsArray
// No longer importing from a non-existent './boundingBox'
// BoundingBoxClass is imported from './BoundingBoxClass' and its static methods can be used directly.

/**
 * Geometry calculation utility functions namespace.
 * Contains advanced geometry calculation functionality.
 */
export const GeometryUtils = {
  Models: {
    Point: PointClass,
    BoundingBox: BoundingBoxClass,
    /* Other models like Vector, Rectangle to be added here */
  },
  Common: {
    calculateDistance: pointUtilCalculateDistance, // Using the alias from the import from ./pointUtils
    rotatePoint: pointUtilRotatePoint,
    scalePoint: pointUtilScalePoint,
    arePointsEqual: pointUtilArePointsEqual,
    calculateMidpoint: pointUtilCalculateMidpoint,
    calculateAngle: pointUtilCalculateAngle,
    calculatePointToPointDistance: pointUtilCalculatePointToPointDistance,
    calculatePointToCircleDistance, // Directly using the imported name from ./pointUtils
    calculatePointToEllipseDistance, // Directly using the imported name from ./pointUtils
    ensurePointInstance: ensurePointInstanceFromUtils, // Add to Common namespace
    // Rectangle functions are now directly exported from ./rectangleUtils and not part of this Common group from pointUtils
  },
  Coordinates: CoordinatesUtilsExport,
  Area: AreaUtilsExport,
  Distance: DistanceUtilsExport, // Corrected usage
  // Shape: ShapeUtilsExport, // Removed
  Polygon: PolygonUtilsExport,
  Arc: ArcUtilsExport,
  Ellipse: EllipseUtilsExport,
  Intersection: IntersectionUtilsExport,
  Line: LineUtilsExport,
  BoundingBoxUtils: { // Expose static BBox methods under a sub-namespace too
    fromObject: BoundingBoxClass.fromObject,
    fromPointsArray: BoundingBoxClass.fromPointsArray,
    fromTwoPoints: BoundingBoxClass.fromTwoPoints,
    fromCenter: BoundingBoxClass.fromCenter,
  },
}

// Rectangle specific
export {
  calculateRectangleArea,
  calculateRectanglePerimeter,
} from './rectangleUtils'
// calculateRectangleArea is now exported from './rectangleUtils' at the top of the geometry specific exports
// calculateRectanglePerimeter is now exported from './rectangleUtils' at the top of the geometry specific exports
