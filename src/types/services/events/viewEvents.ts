import type { BaseEvent } from './eventCore'
/**
 * View Events Type Definitions
 *
 * This file defines event types related to view manipulations,
 * such as panning and zooming the canvas.
 *
 * @module types/services/events
 */
import type { AppEventType } from './eventTypes'

/**
 * Event triggered when the canvas view is panned.
 */
export interface ViewPanEvent extends BaseEvent {
  /** The event type identifier. */
  type: AppEventType.ViewPanned
  /** Event payload containing the new center coordinates of the view. */
  payload: {
    /** The new x-coordinate of the view center. */
    x: number
    /** The new y-coordinate of the view center. */
    y: number
  }
}

/**
 * Event triggered when the canvas view is zoomed.
 */
export interface ViewZoomedEvent extends BaseEvent {
  /** The event type identifier. */
  type: AppEventType.ViewZoomed
  /** Event payload containing the new zoom scale. */
  payload: {
    /** The new zoom scale factor. */
    scale: number
  }
}

/**
 * View Transform Interface
 *
 * Represents a view transformation including position (x,y) and scale (k).
 * Used for tracking and applying view transformations.
 */
export interface ViewTransform {
  /** The x-coordinate of the view's translation. */
  x: number
  /** The y-coordinate of the view's translation. */
  y: number
  /** The scale factor of the view. */
  k: number
}

// Add other view-related events here if needed in the future
