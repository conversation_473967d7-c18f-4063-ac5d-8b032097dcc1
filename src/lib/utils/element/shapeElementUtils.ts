/**
 * Utility Functions for Shape Elements
 *
 * @remarks
 * This module provides a collection of utility functions specifically for working with
 * basic shape elements such as Rectangles, Circles, Ellipses, and Polygons
 * (as defined by {@link ShapeModel} and their respective {@link ElementType}s).
 * These utilities assist in calculations related to their geometric properties,
 * like area, perimeter, and bounding boxes.
 *
 * The functions are exposed as static methods of the {@link ShapeElementUtils} class
 * and are also re-exported directly and via the {@link ShapeUtils} namespace object
 * for convenient access.
 *
 * It assumes that shape-specific geometric data (e.g., width/height for rectangles,
 * radius for circles, points for polygons) are stored within the `properties` object
 * of the input {@link ShapeModel}.
 *
 * @module lib/utils/element/shapeElementUtils
 * @see {@link ShapeModel}
 * @see {@link ElementType}
 * @see {@link Point}
 * @see {@link BoundingBox}
 */

import type { PointData as IPoint } from '../../../types/core/element/geometry/point' // Use PointData and alias
import type { ShapeElement as ShapeModel } from '@/types/core/elementDefinitions' // Use type import
import { ElementType } from '@/types/core/elementDefinitions' // Import ElementType separately
import { BoundingBoxClass as BoundingBox } from '../geometry/BoundingBoxClass' // Import BoundingBoxClass and alias
import { PointClass as Point } from '../geometry/PointClass' // Import PointClass as Point
import {
  calculateArea as calculatePolygonAreaInternal,
  calculatePerimeter as calculatePolygonPerimeterInternal,
} from '../geometry/polygonUtils' // Corrected import path

export class ShapeElementUtils {
  static calculateRectangleArea(element: ShapeModel): number {
    if (element.properties && typeof element.properties.width === 'number' && typeof element.properties.height === 'number') {
      if (element.properties.width < 0 || element.properties.height < 0)
        return 0
      return element.properties.width * element.properties.height
    }
    return 0
  }

  /**
   * Calculates the perimeter of a rectangle or square element.
   * @param element - The shape element ({@link ShapeModel}), expected to be of type RECTANGLE or SQUARE.
   * @returns The perimeter of the rectangle. Returns 0 if properties are invalid or missing.
   */
  static calculateRectanglePerimeter(element: ShapeModel): number {
    if (element.properties && typeof element.properties.width === 'number' && typeof element.properties.height === 'number') {
      if (element.properties.width < 0 || element.properties.height < 0)
        return 0
      return 2 * (element.properties.width + element.properties.height)
    }
    return 0
  }

  /**
   * Calculates the area of a circle element.
   * @param element - The shape element ({@link ShapeModel}), expected to be of type CIRCLE.
   * @returns The area of the circle. Returns 0 if properties are invalid or missing.
   */
  static calculateCircleArea(element: ShapeModel): number {
    if (element.properties && typeof element.properties.radius === 'number' && element.properties.radius >= 0) {
      return Math.PI * element.properties.radius * element.properties.radius
    }
    return 0
  }

  /**
   * Calculates the perimeter (circumference) of a circle element.
   * @param element - The shape element ({@link ShapeModel}), expected to be of type CIRCLE.
   * @returns The perimeter of the circle. Returns 0 if properties are invalid or missing.
   */
  static calculateCirclePerimeter(element: ShapeModel): number {
    if (element.properties && typeof element.properties.radius === 'number' && element.properties.radius >= 0) {
      return 2 * Math.PI * element.properties.radius
    }
    return 0
  }

  /**
   * Calculates the area of an ellipse element.
   * @param element - The shape element ({@link ShapeModel}), expected to be of type ELLIPSE.
   * @returns The area of the ellipse. Returns 0 if properties are invalid or missing.
   */
  static calculateEllipseArea(element: ShapeModel): number {
    if (element.properties && typeof element.properties.radiusX === 'number' && typeof element.properties.radiusY === 'number' && element.properties.radiusX >= 0 && element.properties.radiusY >= 0) {
      return Math.PI * element.properties.radiusX * element.properties.radiusY
    }
    return 0
  }

  /**
   * Calculates the approximate perimeter of an ellipse element using Ramanujan's approximation.
   * @param element - The shape element ({@link ShapeModel}), expected to be of type ELLIPSE.
   * @returns The approximate perimeter of the ellipse. Returns 0 if properties are invalid or missing.
   */
  static calculateEllipsePerimeter(element: ShapeModel): number {
    if (element.properties && typeof element.properties.radiusX === 'number' && typeof element.properties.radiusY === 'number' && element.properties.radiusX >= 0 && element.properties.radiusY >= 0) {
      const a = element.properties.radiusX
      const b = element.properties.radiusY
      if (a === 0 && b === 0)
        return 0
      // Ramanujan's approximation
      const h = ((a - b) / (a + b)) ** 2
      return Math.PI * (a + b) * (1 + 3 * h / (10 + Math.sqrt(4 - 3 * h)))
    }
    return 0
  }

  /**
   * Calculates the area of a polygon element.
   * @param element - The shape element ({@link ShapeModel}), expected to be a polygon type with `properties.points`.
   * @returns The area of the polygon. Returns 0 if properties are invalid or points are insufficient.
   */
  static calculatePolygonArea(element: ShapeModel): number {
    if (element.properties && Array.isArray(element.properties.points)) {
      const polyPoints = element.properties.points as IPoint[]
      if (polyPoints.length < 3)
        return 0

      // 确保position是有效的
      if (typeof element.position?.x !== 'number'
        || typeof element.position?.y !== 'number'
        || Number.isNaN(element.position.x)
        || Number.isNaN(element.position.y)
        || !Number.isFinite(element.position.x)
        || !Number.isFinite(element.position.y)) {
        console.warn('Invalid position in calculatePolygonArea:', element.position)
        return 0
      }

      // 确保所有点都有有效的坐标
      for (const point of polyPoints) {
        if (typeof point.x !== 'number'
          || typeof point.y !== 'number'
          || Number.isNaN(point.x)
          || Number.isNaN(point.y)
          || !Number.isFinite(point.x)
          || !Number.isFinite(point.y)) {
          console.warn('Invalid point coordinates in calculatePolygonArea:', point)
          return 0
        }
      }

      // 多边形的points是相对于position的，所以我们需要转换为绝对坐标来计算面积
      // 创建一个新的点数组，每个点都加上element.position
      const absolutePoints = polyPoints.map(point => ({
        x: point.x + element.position.x,
        y: point.y + element.position.y,
        z: point.z,
      }))

      const area = calculatePolygonAreaInternal(absolutePoints)
      console.warn(`[shapeElementUtils] calculatePolygonArea: element.id=${element.id}, area=${area}`)
      return area
    }
    return 0
  }

  /**
   * Calculates the perimeter of a polygon element.
   * @param element - The shape element ({@link ShapeModel}), expected to be a polygon type with `properties.points`.
   * @returns The perimeter of the polygon. Returns 0 if properties are invalid or points are insufficient.
   */
  static calculatePolygonPerimeter(element: ShapeModel): number {
    if (element.properties && Array.isArray(element.properties.points)) {
      const polyPoints = element.properties.points as IPoint[]
      if (polyPoints.length < 2)
        return 0

      // 确保position是有效的
      if (typeof element.position?.x !== 'number'
        || typeof element.position?.y !== 'number'
        || Number.isNaN(element.position.x)
        || Number.isNaN(element.position.y)
        || !Number.isFinite(element.position.x)
        || !Number.isFinite(element.position.y)) {
        console.warn('Invalid position in calculatePolygonPerimeter:', element.position)
        return 0
      }

      // 确保所有点都有有效的坐标
      for (const point of polyPoints) {
        if (typeof point.x !== 'number'
          || typeof point.y !== 'number'
          || Number.isNaN(point.x)
          || Number.isNaN(point.y)
          || !Number.isFinite(point.x)
          || !Number.isFinite(point.y)) {
          console.warn('Invalid point coordinates in calculatePolygonPerimeter:', point)
          return 0
        }
      }

      // 多边形的points是相对于position的，所以我们需要转换为绝对坐标来计算周长
      // 创建一个新的点数组，每个点都加上element.position
      const absolutePoints = polyPoints.map(point => ({
        x: point.x + element.position.x,
        y: point.y + element.position.y,
        z: point.z,
      }))

      const perimeter = calculatePolygonPerimeterInternal(absolutePoints)
      console.warn(`[shapeElementUtils] calculatePolygonPerimeter: element.id=${element.id}, perimeter=${perimeter}`)
      return perimeter
    }
    return 0
  }

  /**
   * Calculates the area of a generic shape element by dispatching to type-specific area calculators.
   * @param element - The shape element ({@link ShapeModel}).
   * @returns The calculated area. Returns 0 for unsupported types or invalid elements.
   */
  static calculateShapeArea(element: ShapeModel): number {
    switch (element.type) {
      case ElementType.RECTANGLE: case ElementType.SQUARE:
        return ShapeElementUtils.calculateRectangleArea(element)
      case ElementType.CIRCLE:
        return ShapeElementUtils.calculateCircleArea(element)
      case ElementType.ELLIPSE:
        return ShapeElementUtils.calculateEllipseArea(element)
      case ElementType.POLYGON: case ElementType.TRIANGLE: case ElementType.HEXAGON: // Add other polygon types
        return ShapeElementUtils.calculatePolygonArea(element)
      default: return 0
    }
  }

  /**
   * Calculates the perimeter of a generic shape element by dispatching to type-specific perimeter calculators.
   * @param element - The shape element ({@link ShapeModel}).
   * @returns The calculated perimeter. Returns 0 for unsupported types or invalid elements.
   */
  static calculateShapePerimeter(element: ShapeModel): number {
    switch (element.type) {
      case ElementType.RECTANGLE: case ElementType.SQUARE:
        return ShapeElementUtils.calculateRectanglePerimeter(element)
      case ElementType.CIRCLE:
        return ShapeElementUtils.calculateCirclePerimeter(element)
      case ElementType.ELLIPSE:
        return ShapeElementUtils.calculateEllipsePerimeter(element)
      case ElementType.POLYGON: case ElementType.TRIANGLE: case ElementType.HEXAGON: // Add other polygon types
        return ShapeElementUtils.calculatePolygonPerimeter(element)
      default: return 0
    }
  }

  /**
   * Gets the number of sides for a polygon type.
   */
  static getPolygonSides(type: ElementType): number {
    switch (type) {
      case ElementType.TRIANGLE: return 3
      case ElementType.QUADRILATERAL: return 4
      case ElementType.PENTAGON: return 5
      case ElementType.HEXAGON: return 6
      case ElementType.HEPTAGON: return 7
      case ElementType.OCTAGON: return 8
      case ElementType.NONAGON: return 9
      case ElementType.DECAGON: return 10
      default: return 6 // Default to hexagon
    }
  }

  /**
   * Generates points for a regular polygon.
   */
  static generateRegularPolygonPoints(sides: number, radius: number): IPoint[] {
    const points: IPoint[] = []
    const angleStep = (2 * Math.PI) / sides

    for (let i = 0; i < sides; i++) {
      const angle = i * angleStep - Math.PI / 2 // Start from top
      const x = radius * Math.cos(angle)
      const y = radius * Math.sin(angle)
      points.push({ x, y, z: 0 })
    }

    return points
  }

  /**
   * Applies rotation to a bounding box around a center point.
   * @param bbox - The original bounding box.
   * @param rotation - The rotation angle in radians.
   * @param center - The center point of rotation.
   * @returns A new bounding box that encompasses the rotated shape.
   */
  static applyRotationToBoundingBox(bbox: BoundingBox, rotation: number, center: IPoint): BoundingBox {
    // Get the four corners of the original bounding box
    const corners = [
      { x: bbox.position.x, y: bbox.position.y },
      { x: bbox.position.x + bbox.width, y: bbox.position.y },
      { x: bbox.position.x, y: bbox.position.y + bbox.height },
      { x: bbox.position.x + bbox.width, y: bbox.position.y + bbox.height },
    ]

    // Rotate each corner around the center point
    const rotatedCorners = corners.map((corner) => {
      const dx = corner.x - center.x
      const dy = corner.y - center.y
      const cos = Math.cos(rotation)
      const sin = Math.sin(rotation)

      return {
        x: center.x + dx * cos - dy * sin,
        y: center.y + dx * sin + dy * cos,
      }
    })

    // Find the min/max coordinates of the rotated corners
    const minX = Math.min(...rotatedCorners.map(c => c.x))
    const minY = Math.min(...rotatedCorners.map(c => c.y))
    const maxX = Math.max(...rotatedCorners.map(c => c.x))
    const maxY = Math.max(...rotatedCorners.map(c => c.y))

    return new BoundingBox(minX, minY, maxX - minX, maxY - minY)
  }

  /**
   * Calculates the bounding box of a generic shape element.
   * @param element - The shape element ({@link ShapeModel}).
   * @param includeRotation - Whether to include rotation in the bounding box calculation.
   * @returns A {@link BoundingBox} instance or `null` if the bounding box cannot be determined.
   */
  static calculateShapeBoundingBox(element: ShapeModel, includeRotation: boolean = true): BoundingBox | null {
    if (!element.properties)
      return null
    try {
      // First calculate the base bounding box without rotation
      let baseBoundingBox: BoundingBox | null = null
      if (element.type === ElementType.RECTANGLE || element.type === ElementType.SQUARE) {
        if (typeof element.properties.width === 'number' && typeof element.properties.height === 'number') {
          // Rectangle position is typically the center, so calculate top-left corner
          const topLeftX = element.position.x - element.properties.width / 2
          const topLeftY = element.position.y - element.properties.height / 2
          baseBoundingBox = new BoundingBox(topLeftX, topLeftY, element.properties.width, element.properties.height)
        }
      }
      else if (element.type === ElementType.CIRCLE) {
        if (typeof element.properties.radius === 'number') {
          const r = element.properties.radius
          baseBoundingBox = new BoundingBox(element.position.x - r, element.position.y - r, r * 2, r * 2)
        }
      }
      else if (element.type === ElementType.ELLIPSE) {
        if (typeof element.properties.radiusX === 'number' && typeof element.properties.radiusY === 'number') {
          const rx = element.properties.radiusX
          const ry = element.properties.radiusY
          baseBoundingBox = new BoundingBox(element.position.x - rx, element.position.y - ry, rx * 2, ry * 2)
        }
      }
      else if (element.type === ElementType.TEXT) {
        // Fallback text bounding box calculation
        const textContent = (element.properties.text as string) || (element.properties.content as string) || 'Text'
        const fontSize = (element.properties.fontSize as number) || 16
        const fontFamily = (element.properties.fontFamily as string) || 'Arial'

        // Estimate text dimensions (similar to TextBoundingBoxStrategy)
        let charWidthRatio = 0.6
        const lowerFontFamily = fontFamily.toLowerCase()
        if (lowerFontFamily.includes('monospace') || lowerFontFamily.includes('courier')) {
          charWidthRatio = 0.6
        }
        else if (lowerFontFamily.includes('arial') || lowerFontFamily.includes('helvetica')) {
          charWidthRatio = 0.55
        }
        else if (lowerFontFamily.includes('times')) {
          charWidthRatio = 0.5
        }

        const estimatedWidth = Math.max(textContent.length * fontSize * charWidthRatio + fontSize * 0.2, fontSize)
        const estimatedHeight = fontSize * 1.3 + fontSize * 0.1

        // Text is centered at position
        baseBoundingBox = new BoundingBox(
          element.position.x - estimatedWidth / 2,
          element.position.y - estimatedHeight / 2,
          estimatedWidth,
          estimatedHeight,
        )
      }
      else if (element.type === ElementType.IMAGE) {
        // Fallback image bounding box calculation
        const width = (element.properties.width as number) || (element.width as number) || 200
        const height = (element.properties.height as number) || (element.height as number) || 200

        // Image is centered at position
        baseBoundingBox = new BoundingBox(
          element.position.x - width / 2,
          element.position.y - height / 2,
          width,
          height,
        )
      }
      else if (element.type === ElementType.LINE) {
        // Handle line elements
        const startPoint = element.properties.start as IPoint | undefined
        const endPoint = element.properties.end as IPoint | undefined

        if (startPoint && endPoint
          && typeof startPoint.x === 'number' && typeof startPoint.y === 'number'
          && typeof endPoint.x === 'number' && typeof endPoint.y === 'number') {
          const absStart = {
            x: startPoint.x + element.position.x,
            y: startPoint.y + element.position.y,
          }
          const absEnd = {
            x: endPoint.x + element.position.x,
            y: endPoint.y + element.position.y,
          }

          const minX = Math.min(absStart.x, absEnd.x)
          const minY = Math.min(absStart.y, absEnd.y)
          const maxX = Math.max(absStart.x, absEnd.x)
          const maxY = Math.max(absStart.y, absEnd.y)

          baseBoundingBox = new BoundingBox(minX, minY, maxX - minX, maxY - minY)
        }
      }
      else if (element.type === ElementType.ARC) {
        // Handle arc elements
        const radius = (element.properties.radius as number) || (element.properties.rx as number) || 0
        if (radius > 0) {
          // For simplicity, use the full circle bounding box
          // A more accurate implementation would calculate the actual arc bounds
          baseBoundingBox = new BoundingBox(
            element.position.x - radius,
            element.position.y - radius,
            radius * 2,
            radius * 2,
          )
        }
      }
      else if (element.type === ElementType.QUADRATIC) {
        // Handle quadratic bezier curves
        const startPoint = element.properties.start || element.properties.startPoint
        const controlPoint = element.properties.control || element.properties.controlPoint1
        const endPoint = element.properties.end || element.properties.endPoint

        if (startPoint && controlPoint && endPoint) {
          const points = [startPoint, controlPoint, endPoint] as IPoint[]
          const absPoints = points.map((p: IPoint) =>
            new Point(p.x + element.position.x, p.y + element.position.y, p.z),
          )
          baseBoundingBox = BoundingBox.fromPointsArray(absPoints)
        }
      }
      else if (element.type === ElementType.CUBIC) {
        // Handle cubic bezier curves
        const startPoint = element.properties.start
        const control1Point = element.properties.control1
        const control2Point = element.properties.control2
        const endPoint = element.properties.end

        if (startPoint && control1Point && control2Point && endPoint) {
          const points = [startPoint, control1Point, control2Point, endPoint] as IPoint[]
          const absPoints = points.map((p: IPoint) =>
            new Point(p.x + element.position.x, p.y + element.position.y, p.z),
          )
          baseBoundingBox = BoundingBox.fromPointsArray(absPoints)
        }
      }
      else if (element.type === ElementType.WALL) {
        // Handle wall elements - walls have a path property that defines their geometry
        const path = element.properties.path as any // Type assertion for path object
        if (path) {
          if (path.type === ElementType.LINE && path.start && path.end) {
            // Wall with line path
            const startPoint = path.start as { x: number, y: number }
            const endPoint = path.end as { x: number, y: number }

            const absStart = {
              x: startPoint.x + element.position.x,
              y: startPoint.y + element.position.y,
            }
            const absEnd = {
              x: endPoint.x + element.position.x,
              y: endPoint.y + element.position.y,
            }

            const thickness = (element.properties.thickness as number) || 100 // Default thickness
            const minX = Math.min(absStart.x, absEnd.x) - thickness / 2
            const minY = Math.min(absStart.y, absEnd.y) - thickness / 2
            const maxX = Math.max(absStart.x, absEnd.x) + thickness / 2
            const maxY = Math.max(absStart.y, absEnd.y) + thickness / 2

            baseBoundingBox = new BoundingBox(minX, minY, maxX - minX, maxY - minY)
          }
          // Handle other path types for walls (polyline, arc, etc.)
          else if (path.points && Array.isArray(path.points)) {
            const thickness = (element.properties.thickness as number) || 100
            const absPoints = path.points.map((p: IPoint) =>
              new Point(p.x + element.position.x, p.y + element.position.y, p.z),
            )
            const bbox = BoundingBox.fromPointsArray(absPoints)
            // Expand bbox by thickness
            baseBoundingBox = new BoundingBox(
              bbox.position.x - thickness / 2,
              bbox.position.y - thickness / 2,
              bbox.width + thickness,
              bbox.height + thickness,
            )
          }
        }
      }
      else if (element.type === ElementType.DOOR || element.type === ElementType.WINDOW || element.type === ElementType.OPENING) {
        // Handle door, window, and opening elements
        const width = (element.properties.width as number) || (element.width as number) || 800 // Default door/window width
        const height = (element.properties.height as number) || (element.height as number) || 2000 // Default door/window height

        // Doors and windows are typically positioned at their center
        baseBoundingBox = new BoundingBox(
          element.position.x - width / 2,
          element.position.y - height / 2,
          width,
          height,
        )
      }
      else if (element.type === ElementType.FURNITURE) {
        // Handle furniture elements
        const width = (element.properties.width as number) || (element.properties.defaultWidth as number) || (element.width as number) || 600
        // Note: height is available but not used in 2D representation
        const depth = (element.properties.depth as number) || (element.properties.defaultDepth as number) || 400

        // Use width and depth for 2D representation (depth becomes height in 2D view)
        baseBoundingBox = new BoundingBox(
          element.position.x - width / 2,
          element.position.y - depth / 2,
          width,
          depth,
        )
      }
      else if (element.type === ElementType.FIXTURE) {
        // Handle fixture elements (sinks, toilets, etc.)
        const width = (element.properties.width as number) || (element.properties.defaultWidth as number) || (element.width as number) || 400
        // Note: height is available but not used in 2D representation
        const depth = (element.properties.depth as number) || (element.properties.defaultDepth as number) || 300

        // Use width and depth for 2D representation
        baseBoundingBox = new BoundingBox(
          element.position.x - width / 2,
          element.position.y - depth / 2,
          width,
          depth,
        )
      }
      else if (element.type === ElementType.ROOM) {
        // Handle room elements - rooms have a shape property that defines their boundary
        const shape = element.properties.shape as any // Type assertion for shape object
        if (shape) {
          if (shape.type === ElementType.RECTANGLE && shape.width && shape.height) {
            baseBoundingBox = new BoundingBox(
              element.position.x - shape.width / 2,
              element.position.y - shape.height / 2,
              shape.width,
              shape.height,
            )
          }
          else if (shape.type === ElementType.ELLIPSE && shape.radiusX && shape.radiusY) {
            baseBoundingBox = new BoundingBox(
              element.position.x - shape.radiusX,
              element.position.y - shape.radiusY,
              shape.radiusX * 2,
              shape.radiusY * 2,
            )
          }
          else if (shape.points && Array.isArray(shape.points)) {
            const absPoints = shape.points.map((p: IPoint) =>
              new Point(p.x + element.position.x, p.y + element.position.y, p.z),
            )
            baseBoundingBox = BoundingBox.fromPointsArray(absPoints)
          }
        }

        // Fallback for room without shape
        if (!baseBoundingBox) {
          const defaultSize = 3000 // 3 meters default room size
          baseBoundingBox = new BoundingBox(
            element.position.x - defaultSize / 2,
            element.position.y - defaultSize / 2,
            defaultSize,
            defaultSize,
          )
        }
      }
      else if ([ElementType.LIGHT, ElementType.ELECTRICAL_OUTLET, ElementType.APPLIANCE].includes(element.type as ElementType)) {
        // Handle small fixtures and appliances
        const size = (element.properties.size as number) || 100 // Default size for small elements
        const width = (element.properties.width as number) || (element.width as number) || size
        const height = (element.properties.height as number) || (element.height as number) || size

        baseBoundingBox = new BoundingBox(
          element.position.x - width / 2,
          element.position.y - height / 2,
          width,
          height,
        )
      }
      else if ([ElementType.TEXT, ElementType.TEXT_LABEL].includes(element.type as ElementType)) {
        // Handle text elements
        const text = (element.properties.text as string) || 'Text'
        const fontSize = (element.properties.fontSize as number) || 16
        const fontFamily = (element.properties.fontFamily as string) || 'Arial'

        // Estimate text dimensions
        let charWidthRatio = 0.6 // Default character width ratio
        if (fontFamily.toLowerCase().includes('monospace')) {
          charWidthRatio = 0.6
        }
        else if (fontFamily.toLowerCase().includes('arial') || fontFamily.toLowerCase().includes('helvetica')) {
          charWidthRatio = 0.55
        }
        else if (fontFamily.toLowerCase().includes('times')) {
          charWidthRatio = 0.5
        }

        const estimatedWidth = text.length * fontSize * charWidthRatio
        const estimatedHeight = fontSize * 1.2 // Line height factor

        baseBoundingBox = new BoundingBox(
          element.position.x - estimatedWidth / 2,
          element.position.y - estimatedHeight / 2,
          estimatedWidth,
          estimatedHeight,
        )
      }
      else if ([ElementType.GROUP].includes(element.type as ElementType)) {
        // Handle group elements - use a default size or calculate from children
        const defaultSize = 200
        baseBoundingBox = new BoundingBox(
          element.position.x - defaultSize / 2,
          element.position.y - defaultSize / 2,
          defaultSize,
          defaultSize,
        )
      }
      else if ([ElementType.OPENING, ElementType.WALL_PAINT, ElementType.WALL_PAPER].includes(element.type as ElementType)) {
        // Handle wall-related elements
        const width = (element.properties.width as number) || (element.width as number) || 1000
        const height = (element.properties.height as number) || (element.height as number) || 200

        baseBoundingBox = new BoundingBox(
          element.position.x - width / 2,
          element.position.y - height / 2,
          width,
          height,
        )
      }
      else if ([ElementType.FLOOR_AREA, ElementType.ROOM_BOUNDARY].includes(element.type as ElementType)) {
        // Handle area elements - these are typically defined by points or shapes
        if (element.properties.points && Array.isArray(element.properties.points)) {
          const absPoints = element.properties.points.map((p: IPoint) =>
            new Point(p.x + element.position.x, p.y + element.position.y, p.z),
          )
          baseBoundingBox = BoundingBox.fromPointsArray(absPoints)
        }

        // Fallback for area elements
        if (!baseBoundingBox) {
          const defaultSize = 2000 // 2 meters default area size
          baseBoundingBox = new BoundingBox(
            element.position.x - defaultSize / 2,
            element.position.y - defaultSize / 2,
            defaultSize,
            defaultSize,
          )
        }
      }
      else if (element.type === ElementType.HANDRAIL) {
        // Handle handrail elements - similar to lines but with thickness
        const startPoint = element.properties.start as IPoint | undefined
        const endPoint = element.properties.end as IPoint | undefined

        if (startPoint && endPoint) {
          const absStart = {
            x: startPoint.x + element.position.x,
            y: startPoint.y + element.position.y,
          }
          const absEnd = {
            x: endPoint.x + element.position.x,
            y: endPoint.y + element.position.y,
          }

          const thickness = (element.properties.thickness as number) || 50 // Default handrail thickness
          const minX = Math.min(absStart.x, absEnd.x) - thickness / 2
          const minY = Math.min(absStart.y, absEnd.y) - thickness / 2
          const maxX = Math.max(absStart.x, absEnd.x) + thickness / 2
          const maxY = Math.max(absStart.y, absEnd.y) + thickness / 2

          baseBoundingBox = new BoundingBox(minX, minY, maxX - minX, maxY - minY)
        }

        // Fallback for handrail
        if (!baseBoundingBox) {
          const defaultLength = 1000
          baseBoundingBox = new BoundingBox(
            element.position.x - defaultLength / 2,
            element.position.y - 25,
            defaultLength,
            50,
          )
        }
      }
      else if (Array.isArray(element.properties.points) && element.properties.points.length > 0) {
        // Handle polylines and polygons
        const absPoints = element.properties.points.map((p: IPoint) =>
          new Point(p.x + element.position.x, p.y + element.position.y, p.z),
        )
        if (absPoints.length > 0)
          baseBoundingBox = BoundingBox.fromPointsArray(absPoints)
      }
      else if ([
        ElementType.TRIANGLE,
        ElementType.QUADRILATERAL,
        ElementType.PENTAGON,
        ElementType.HEXAGON,
        ElementType.HEPTAGON,
        ElementType.OCTAGON,
        ElementType.NONAGON,
        ElementType.DECAGON,
      ].includes(element.type as ElementType)) {
        // Handle specific polygon types - they should have points like POLYGON
        if (element.properties.points && Array.isArray(element.properties.points)) {
          const absPoints = element.properties.points.map((p: IPoint) =>
            new Point(p.x + element.position.x, p.y + element.position.y, p.z),
          )
          baseBoundingBox = BoundingBox.fromPointsArray(absPoints)
        }
        else {
          // Fallback: create a default polygon based on type
          const radius = (element.properties.radius as number) || (element.radius as number) || 100
          const sides = ShapeElementUtils.getPolygonSides(element.type as ElementType)
          const points = ShapeElementUtils.generateRegularPolygonPoints(sides, radius)
          const absPoints = points.map((p: IPoint) =>
            new Point(p.x + element.position.x, p.y + element.position.y, p.z || 0),
          )
          baseBoundingBox = BoundingBox.fromPointsArray(absPoints)
        }
      }
      // Apply rotation if the element has rotation and includeRotation is true
      if (baseBoundingBox && includeRotation && element.rotation && element.rotation !== 0) {
        baseBoundingBox = ShapeElementUtils.applyRotationToBoundingBox(baseBoundingBox, element.rotation, element.position)
      }

      return baseBoundingBox
    }
    catch (error) {
      console.warn(`[ShapeElementUtils.calculateShapeBoundingBox] Error: ${error instanceof Error ? error.message : String(error)}`)
    }
    return null
  }
}

export const {
  calculateRectangleArea,
  calculateRectanglePerimeter,
  calculateCircleArea,
  calculateCirclePerimeter,
  calculateEllipseArea,
  calculateEllipsePerimeter,
  calculatePolygonArea,
  calculatePolygonPerimeter,
  calculateShapeArea,
  calculateShapePerimeter,
  calculateShapeBoundingBox,
} = ShapeElementUtils

/**
 * Namespace object for convenient access to shape element utility functions.
 *
 * @remarks
 * This object groups the static methods from {@link ShapeElementUtils} under a
 * single export, allowing for usage like `ShapeUtils.calculateRectangleArea(...)`.
 */
export const ShapeUtils = {
  calculateRectangleArea,
  calculateCircleArea,
  calculateEllipseArea,
  calculatePolygonArea,
  calculateShapeArea,
  calculateRectanglePerimeter,
  calculateCirclePerimeter,
  calculateEllipsePerimeter,
  calculatePolygonPerimeter,
  calculateShapePerimeter,
  calculateShapeBoundingBox,
}
