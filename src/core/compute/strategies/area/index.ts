/**
 * Area Calculation Strategies Index
 *
 * @remarks
 * This module serves as a barrel export for all concrete strategy implementations
 * related to area calculation. It re-exports the `AreaCalculatorStrategy` interface
 * (and other relevant compute types) from `@/types/core/compute`, and then exports
 * all specific area calculation strategy classes defined within this directory
 * (e.g., {@link ArcAreaStrategy}, {@link RectangleAreaStrategy}).
 *
 * This allows for a centralized import point for accessing various area
 * calculation strategies.
 *
 * @module core/compute/strategies/area/index
 */

// Export concrete strategy implementations from the current directory
export * from './ArcAreaStrategy'

export * from './CubicAreaStrategy'
export * from './EllipseAreaStrategy'
export * from './LineAreaStrategy'
export * from './PolygonAreaStrategy'
export * from './PolylineAreaStrategy'
export * from './QuadraticAreaStrategy'
export * from './RectangleAreaStrategy'
// Export interface first for better code organization
export * from '@/types/core/compute' // This exports AreaCalculatorStrategy etc.
