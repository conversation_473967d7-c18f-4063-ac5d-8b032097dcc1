import type { ShapeElement } from '@/types/core/element/element'
import type { Image } from '@/types/core/element/image/imageElementTypes'
import { describe, expect, it } from 'vitest'

describe('image Interface', () => {
  it('should define an image element with required properties', () => {
    // Create a valid Image object
    const image: Image = {
      id: 'image-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      src: 'path/to/image.png',
      width: 200,
      height: 150,
    }

    // Verify the object is valid
    expect(image.id).toBe('image-1')
    expect(image.type).toBe('shape')
    expect(image.src).toBe('path/to/image.png')
    expect(image.position.x).toBe(100)
    expect(image.position.y).toBe(100)
    expect(image.width).toBe(200)
    expect(image.height).toBe(150)
  })

  it('should extend ShapeElement interface', () => {
    // Create an image element
    const image: Image = {
      id: 'image-2',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 150, y: 150 },
      rotation: 45,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      src: 'path/to/another-image.jpg',
      width: 300,
      height: 200,
    }

    // Verify the image extends ShapeElement
    const shapeElement: ShapeElement = image
    expect(shapeElement.id).toBe('image-2')
    expect(shapeElement.type).toBe('shape')
    expect(shapeElement.visible).toBe(true)
    expect(shapeElement.locked).toBe(false)
    expect(shapeElement.position.x).toBe(150)
    expect(shapeElement.position.y).toBe(150)
    expect(shapeElement.rotation).toBe(45)
  })

  it('should allow creating image elements with different properties', () => {
    // Create image elements with different properties
    const smallImage: Image = {
      id: 'small-image',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      src: 'path/to/small-image.png',
      width: 100,
      height: 75,
    }

    const largeImage: Image = {
      id: 'large-image',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 200, y: 200 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      src: 'path/to/large-image.png',
      width: 800,
      height: 600,
    }

    const rotatedImage: Image = {
      id: 'rotated-image',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 150, y: 150 },
      rotation: 90,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      src: 'path/to/rotated-image.png',
      width: 200,
      height: 200,
    }

    // Verify all image elements are valid
    expect(smallImage.src).toBe('path/to/small-image.png')
    expect(smallImage.width).toBe(100)
    expect(smallImage.height).toBe(75)

    expect(largeImage.src).toBe('path/to/large-image.png')
    expect(largeImage.width).toBe(800)
    expect(largeImage.height).toBe(600)

    expect(rotatedImage.src).toBe('path/to/rotated-image.png')
    expect(rotatedImage.width).toBe(200)
    expect(rotatedImage.height).toBe(200)
    expect(rotatedImage.rotation).toBe(90)
  })

  it('should be usable in arrays and collections', () => {
    // Create an array of image elements
    const images: Image[] = [
      {
        id: 'image-3',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 100, y: 100 },
        rotation: 0,
        scale: { x: 1, y: 1 },
        selectable: true,
        draggable: true,
        showHandles: true,
        src: 'path/to/first-image.png',
        width: 200,
        height: 150,
      },
      {
        id: 'image-4',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 300, y: 300 },
        rotation: 0,
        scale: { x: 1, y: 1 },
        selectable: true,
        draggable: true,
        showHandles: true,
        src: 'path/to/second-image.jpg',
        width: 400,
        height: 300,
      },
    ]

    // Verify the array is valid
    expect(images.length).toBe(2)
    expect(images[0].id).toBe('image-3')
    expect(images[0].src).toBe('path/to/first-image.png')
    expect(images[0].width).toBe(200)
    expect(images[0].height).toBe(150)

    expect(images[1].id).toBe('image-4')
    expect(images[1].src).toBe('path/to/second-image.jpg')
    expect(images[1].width).toBe(400)
    expect(images[1].height).toBe(300)
  })

  it('should be usable in functions that require Image', () => {
    // Define a function that uses Image
    function calculateAspectRatio(image: Image): number {
      return image.width / image.height
    }

    // Create an image element
    const image: Image = {
      id: 'image-5',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      src: 'path/to/aspect-ratio-test.png',
      width: 400,
      height: 300,
    }

    // Test the function
    const aspectRatio = calculateAspectRatio(image)
    expect(aspectRatio).toBe(4 / 3) // 400/300 = 4/3
  })

  it('should be compatible with JSON serialization/deserialization', () => {
    // Create an image element
    const image: Image = {
      id: 'image-6',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      src: 'path/to/serializable-image.png',
      width: 250,
      height: 200,
    }

    // Serialize to JSON and deserialize
    const serialized = JSON.stringify(image)
    const deserialized = JSON.parse(serialized) as Image

    // Verify the deserialized object is valid
    expect(deserialized.id).toBe('image-6')
    expect(deserialized.type).toBe('shape')
    expect(deserialized.src).toBe('path/to/serializable-image.png')
    expect(deserialized.width).toBe(250)
    expect(deserialized.height).toBe(200)
    expect(deserialized.position.x).toBe(100)
    expect(deserialized.position.y).toBe(100)
  })

  it('should support different image formats through src property', () => {
    // Create image elements with different source formats
    const pngImage: Image = {
      id: 'png-image',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      src: 'path/to/image.png',
      width: 200,
      height: 150,
    }

    const jpgImage: Image = {
      id: 'jpg-image',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      src: 'path/to/image.jpg',
      width: 200,
      height: 150,
    }

    const svgImage: Image = {
      id: 'svg-image',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      src: 'path/to/image.svg',
      width: 200,
      height: 150,
    }

    const remoteUrlImage: Image = {
      id: 'remote-image',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      src: 'https://example.com/image.png',
      width: 200,
      height: 150,
    }

    // Verify all image elements have valid src properties
    expect(pngImage.src).toBe('path/to/image.png')
    expect(jpgImage.src).toBe('path/to/image.jpg')
    expect(svgImage.src).toBe('path/to/image.svg')
    expect(remoteUrlImage.src).toBe('https://example.com/image.png')
  })
})
