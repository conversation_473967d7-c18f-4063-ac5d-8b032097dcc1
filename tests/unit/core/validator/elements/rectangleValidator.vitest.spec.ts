import type { ValidatorShape } from '@/types/core/element/validator'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { RectangleValidator } from '@/core/validator/elements/rectangleValidator'
import { ElementType } from '@/types/core/shape-type'

describe('rectangleValidator', () => {
  let validator: RectangleValidator

  beforeEach(() => {
    validator = new RectangleValidator()
  })

  describe('validateSpecific', () => {
    it('should return error for invalid shape type', () => {
      const invalidRectangle: ValidatorShape = {
        id: 'rect-1',
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
        radius: 50,
      }

      const errors = (validator as any).validateSpecific(invalidRectangle)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_SHAPE_TYPE')
    })
    it('should validate a valid rectangle', () => {
      const validRectangle: ValidatorShape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 50,
      }

      const errors = (validator as any).validateSpecific(validRectangle)
      expect(errors).toEqual([])
    })

    it('should return error for missing width', () => {
      const invalidRectangle: ValidatorShape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        height: 50,
      }

      const errors = (validator as any).validateSpecific(invalidRectangle)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_DIMENSION')
    })

    it('should return error for missing height', () => {
      const invalidRectangle: ValidatorShape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
      }

      const errors = (validator as any).validateSpecific(invalidRectangle)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_DIMENSION')
    })

    it('should return error for invalid width type', () => {
      const invalidRectangle: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 'hundred',
        height: 50,
      }

      const errors = (validator as any).validateSpecific(invalidRectangle)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_DIMENSION')
    })

    it('should return error for invalid height type', () => {
      const invalidRectangle: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 'fifty',
      }

      const errors = (validator as any).validateSpecific(invalidRectangle)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_DIMENSION')
    })

    it('should return error for negative width', () => {
      const invalidRectangle: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: -100,
        height: 50,
      }

      const errors = (validator as any).validateSpecific(invalidRectangle)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_DIMENSION')
    })

    it('should return error for negative height', () => {
      const invalidRectangle: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: -50,
      }

      const errors = (validator as any).validateSpecific(invalidRectangle)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_DIMENSION')
    })

    it('should return error for zero width', () => {
      const invalidRectangle: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 0,
        height: 50,
      }

      const errors = (validator as any).validateSpecific(invalidRectangle)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_DIMENSION')
    })

    it('should return error for zero height', () => {
      const invalidRectangle: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 0,
      }

      const errors = (validator as any).validateSpecific(invalidRectangle)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_DIMENSION')
    })

    it('should validate a rectangle with valid cornerRadius', () => {
      const validRectangle: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 50,
        cornerRadius: 10,
      }

      const errors = (validator as any).validateSpecific(validRectangle)
      expect(errors).toEqual([])
    })

    it('should return error for negative cornerRadius', () => {
      const invalidRectangle: any = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 50,
        cornerRadius: -10,
      }

      const errors = (validator as any).validateSpecific(invalidRectangle)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_PROPERTY_VALUE')
    })
  })

  describe('applySpecificRules', () => {
    it('should apply specific rules for rectangles', () => {
      // This is a protected method, so we need to cast to any
      const applySpecificRulesSpy = vi.spyOn(validator as any, 'applySpecificRules')

      const validRectangle: ValidatorShape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 50,
      }

      validator.validate(validRectangle)
      expect(applySpecificRulesSpy).toHaveBeenCalled()
    })
  })

  describe('validate', () => {
    it('should call validateSpecific', () => {
      const validRectangle: ValidatorShape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 50,
      }

      const validateSpecificSpy = vi.spyOn(validator as any, 'validateSpecific').mockReturnValue([])

      const result = validator.validate(validRectangle)

      expect(validateSpecificSpy).toHaveBeenCalledWith(validRectangle)
      expect(result.valid).toBe(true)
      expect(result.errors).toEqual([])

      validateSpecificSpy.mockRestore()
    })

    it('should return invalid result when errors are found', () => {
      const invalidRectangle: ValidatorShape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 0,
        height: 50,
      }

      const result = validator.validate(invalidRectangle)

      expect(result.valid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
      expect(result.errors.some(error => error.code === 'INVALID_DIMENSION')).toBe(true)
    })

    it('should detect multiple validation errors', () => {
      const invalidRectangle: ValidatorShape = {
        // Missing id
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 0,
        height: 0,
      }

      const result = validator.validate(invalidRectangle)

      expect(result.valid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(1)
      expect(result.errors.some(error => error.code === 'MISSING_OR_INVALID_ID')).toBe(true)
      expect(result.errors.some(error => error.code === 'INVALID_DIMENSION')).toBe(true)
    })

    it('should validate a square', () => {
      const validSquare: ValidatorShape = {
        id: 'square-1',
        type: ElementType.SQUARE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 100,
      }

      const result = validator.validate(validSquare)

      expect(result.valid).toBe(true)
      expect(result.errors).toEqual([])
    })
  })
})
