/**
 * Compute Status Indicator Component
 *
 * Displays a colored dot to indicate the status of geometry calculations.
 * Provides visual feedback for calculation success, errors, warnings, and pending states.
 *
 * @module components/property/geometry/ComputeStatusIndicator
 */

import { cn } from '@/lib/utils'
import { ComputeStatus, getStatusBgColor, getStatusDescription } from '@/types/core/computeStatus'

/**
 * Props for the ComputeStatusIndicator component
 */
interface ComputeStatusIndicatorProps {
  /** The compute status to display */
  status: ComputeStatus
  /** Optional additional CSS classes */
  className?: string
  /** Optional tooltip content - if not provided, uses default status description */
  tooltip?: string
  /** Size variant for the indicator */
  size?: 'sm' | 'md' | 'lg'
  /** Whether to show the indicator when status is NONE */
  showWhenNone?: boolean
}

/**
 * ComputeStatusIndicator component that displays a colored dot based on computation status.
 *
 * @param props - The component props
 * @param props.status - The computation status to display
 * @param props.className - Additional CSS classes to apply
 * @param props.tooltip - Custom tooltip text to display
 * @param props.size - Size of the indicator (sm, md, lg)
 * @param props.showWhenNone - Whether to show the indicator when status is NONE
 * @returns The rendered status indicator or null if status is NONE and showWhenNone is false
 */
export function ComputeStatusIndicator({
  status,
  className,
  tooltip,
  size = 'sm',
  showWhenNone = false,
}: ComputeStatusIndicatorProps) {
  // Don't render anything if status is NONE and showWhenNone is false
  if (status === ComputeStatus.NONE && !showWhenNone) {
    return null
  }

  // Get size classes
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4',
  }

  // Get the background color for the status
  const bgColor = getStatusBgColor(status)

  // Get the tooltip text
  const tooltipText = tooltip ?? getStatusDescription(status)

  return (
    <div
      className={cn(
        'inline-block rounded-full flex-shrink-0',
        sizeClasses[size],
        bgColor,
        className,
      )}
      title={tooltipText}
      role="status"
      aria-label={tooltipText}
    />
  )
}

/**
 * ComputeStatusIndicatorWithLabel component that displays the indicator with a label.
 */
interface ComputeStatusIndicatorWithLabelProps extends ComputeStatusIndicatorProps {
  /** The label text to display next to the indicator */
  label: string
  /** Whether to show the label before or after the indicator */
  labelPosition?: 'before' | 'after'
}

export function ComputeStatusIndicatorWithLabel({
  label,
  labelPosition = 'before',
  ...indicatorProps
}: ComputeStatusIndicatorWithLabelProps) {
  const indicator = <ComputeStatusIndicator {...indicatorProps} />

  if (labelPosition === 'before') {
    return (
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium">{label}</span>
        {indicator}
      </div>
    )
  }

  return (
    <div className="flex items-center gap-2">
      {indicator}
      <span className="text-sm font-medium">{label}</span>
    </div>
  )
}


