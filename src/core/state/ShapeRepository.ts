import type { ShapeElement as ShapeModel } from '@/types/core/elementDefinitions'
import type { AppEventMap, EventBus } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import { ensureCompleteMetadata } from '@/lib/utils/element/elementUtils' // Corrected import
import { AppEventType } from '@/types/services/events' // Added EventBus imports

/**
 * In-Memory Repository for Shape Data and Selection State
 *
 * @remarks
 * This class provides a centralized, in-memory store for managing {@link ShapeModel} (aliased from `ShapeElement`)
 * instances and their selection state within the application. It offers synchronous
 * CRUD (Create, Read, Update, Delete) operations for shapes, as well as methods
 * to manipulate the current set of selected shapes.
 *
 * The `ShapeRepository` acts as a single source of truth for the core shape data,
 * using a `Map` for efficient shape lookups by ID and a `Set` to manage selected shape IDs.
 * It also integrates a {@link LoggerService} for logging repository operations.
 *
 * Key functionalities include:
 * - Adding, retrieving, updating, and removing shapes.
 * - Managing selection: setting, adding to, removing from, and clearing selection.
 * - Querying shapes by type, property values, or metadata.
 * - Filtering shapes based on custom predicates.
 *
 * @module core/state/ShapeRepository
 * @see {@link ShapeModel}
 * @see {@link LoggerService}
 */
export class ShapeRepository {
  /**
   * Internal map storing ShapeModels keyed by their ID.
   * Provides fast lookups for individual shapes.
   * @private
   */
  private shapes: Map<string, ShapeModel> = new Map()
  /**
   * Internal set storing the IDs of currently selected shapes.
   * Allows for efficient checking of selection status and retrieval of selected IDs.
   * @private
   */
  private selectedShapeIds: Set<string> = new Set()

  /**
   * Logger instance.
   * @private
   */
  private logger: LoggerService
  private eventBus: EventBus<AppEventMap> // Added eventBus

  /**
   * Initializes a new ShapeRepository instance.
   *
   * @param logger - The LoggerService instance.
   * @param eventBus - The application's event bus.
   */
  constructor(logger: LoggerService, eventBus: EventBus<AppEventMap>) {
    this.logger = logger
    this.eventBus = eventBus // Assign eventBus
    this.logger.info('[ShapeRepository] Initialized.')
  }

  /**
   * Adds a new shape to the repository.
   *
   * @remarks
   * If a shape with the same ID already exists, it will be overwritten.
   * Logs an error and returns if the shape or its ID is invalid.
   *
   * @param shape - The shape object to add.
   */
  add(shape: ShapeModel): void {
    if (!shape?.id) {
      this.logger.error('[ShapeRepository] Attempted to add invalid shape:', shape)
      return
    }
    this.shapes.set(shape.id, shape)
    this._publishShapesUpdate()
  }

  /**
   * Retrieves a shape from the repository by its unique ID.
   *
   * @param id - The ID of the shape to retrieve.
   * @returns The shape if found, otherwise `undefined`.
   */
  getById(id: string): ShapeModel | undefined {
    return this.shapes.get(id)
  }

  /**
   * Retrieves all shapes currently stored in the repository.
   *
   * @returns An array containing all shape objects.
   *          The order is not guaranteed. The returned array is a copy; modifying it will not affect the repository's internal state.
   */
  getAll(): ShapeModel[] {
    return Array.from(this.shapes.values())
  }

  /**
   * Updates an existing shape in the repository with new property values.
   *
   * @param id - The ID of the shape to update.
   * @param changes - An object containing the properties to update.
   * @returns `true` if a shape with the given ID was found and updated, `false` otherwise.
   */
  update(id: string, changes: Partial<Omit<ShapeModel, 'id'>>): boolean {
    const existingShape = this.shapes.get(id)
    if (!existingShape) {
      return false
    }

    const mergedMetadata = ensureCompleteMetadata({
      ...(existingShape.metadata || { createdAt: Date.now() }),
      ...(changes.metadata || {}),
      updatedAt: Date.now(),
    })

    // 明确分离顶层 changes 和 properties changes
    const topLevelChanges: Partial<Omit<ShapeModel, 'id' | 'properties'>> = {}
    let propertyChanges: Partial<ShapeModel['properties']> | undefined

    // Check if there are layer-related changes (for potential future use)
    // const hasLayerChanges = 'majorCategory' in changes || 'minorCategory' in changes || 'zLevelId' in changes

    for (const key in changes) {
      if (Object.prototype.hasOwnProperty.call(changes, key)) {
        if (key === 'properties') {
          propertyChanges = changes[key] as Partial<ShapeModel['properties']>
        }
        else {
          (topLevelChanges as Record<string, unknown>)[key] = (changes as Record<string, unknown>)[key]
        }
      }
    }

    if (!propertyChanges) {
      propertyChanges = {}
    }

    // 检查是否有图层属性变更
    const layerChanged = (
      ('majorCategory' in changes && changes.majorCategory !== undefined && changes.majorCategory !== existingShape.majorCategory)
      || ('minorCategory' in changes && changes.minorCategory !== undefined && changes.minorCategory !== existingShape.minorCategory)
      || ('zLevelId' in changes && changes.zLevelId !== undefined && changes.zLevelId !== existingShape.zLevelId)
    )

    let nextIntraLayerZIndex: number | undefined
    if (layerChanged) {
      // 计算新层的最大 intraLayerZIndex
      const newMajor = (propertyChanges as Record<string, unknown>).majorCategory as string || (topLevelChanges as Record<string, unknown>).majorCategory as string
      const newMinor = (propertyChanges as Record<string, unknown>).minorCategory as string || (topLevelChanges as Record<string, unknown>).minorCategory as string
      const newZLevel = (propertyChanges as Record<string, unknown>).zLevelId as string || (topLevelChanges as Record<string, unknown>).zLevelId as string
      const shapesInSameLayer = this.getAll().filter(shape =>
        shape.id !== id
        && shape.majorCategory === newMajor
        && shape.minorCategory === newMinor
        && shape.zLevelId === newZLevel,
      )
      const maxZ = shapesInSameLayer.reduce((max, s) => Math.max(max, s.intraLayerZIndex ?? 0), 0)
      nextIntraLayerZIndex = maxZ + 1
      ;(topLevelChanges as Record<string, unknown>).intraLayerZIndex = nextIntraLayerZIndex
      ;(propertyChanges as Record<string, unknown>).intraLayerZIndex = nextIntraLayerZIndex
    }

    if ('majorCategory' in changes && changes.majorCategory !== undefined) {
      propertyChanges.majorCategory = changes.majorCategory
    }
    if ('minorCategory' in changes && changes.minorCategory !== undefined) {
      propertyChanges.minorCategory = changes.minorCategory
    }
    if ('zLevelId' in changes && changes.zLevelId !== undefined) {
      propertyChanges.zLevelId = changes.zLevelId
    }

    const mergedProperties = (propertyChanges != null && Object.keys(propertyChanges).length > 0)
      ? deepMerge(existingShape.properties || {}, propertyChanges)
      : existingShape.properties

    // 🔧 修复：特殊处理pattern属性，确保undefined时被正确删除
    const { pattern: _patternChange, ...otherTopLevelChanges } = topLevelChanges as { pattern?: unknown, [key: string]: unknown }

    const updatedShape: ShapeModel = {
      ...existingShape,
      ...otherTopLevelChanges,
      id: existingShape.id,
      metadata: mergedMetadata,
      properties: mergedProperties,
    }

    // 处理pattern属性和删除标记
    if ((changes as any).__deletePattern === true) {
      // 删除pattern属性
      delete updatedShape.pattern
      this.logger.debug('[ShapeRepository.update] Pattern deleted from shape:', updatedShape.id)
    }
    else if ('pattern' in changes) {
      // 设置新的pattern
      updatedShape.pattern = changes.pattern
      this.logger.debug('[ShapeRepository.update] Pattern set for shape:', updatedShape.id)
    }

    // 调试：打印最终同步结果
    this.logger.debug('[ShapeRepository.update] Shape updated:', {
      id: updatedShape.id,
      majorCategory: updatedShape.majorCategory,
      minorCategory: updatedShape.minorCategory,
      zLevelId: updatedShape.zLevelId,
      hasPattern: 'pattern' in updatedShape,
    })

    this.shapes.set(existingShape.id, updatedShape)
    this._publishShapesUpdate()
    return true
  }

  /**
   * Removes a shape from the repository using its ID.
   *
   * @remarks
   * If the removed shape was selected, it is also removed from the selection state.
   *
   * @param id - The ID of the shape to remove.
   * @returns `true` if a shape with the given ID was found and removed, `false` otherwise.
   */
  remove(id: string): boolean {
    this.selectedShapeIds.delete(id) // Ensure deselection
    const deleted = this.shapes.delete(id)
    // if (deleted) this.logger.debug(`[ShapeRepository] Removed shape: ${id}`);
    if (deleted) {
      this._publishShapesUpdate()
    }
    return deleted
  }

  /**
   * Sets the current selection, replacing any previously selected shapes.
   *
   * @param ids - An array or a Set containing the IDs of the shapes to be selected.
   */
  setSelectedIds(ids: string[] | Set<string>): void {
    this.selectedShapeIds = new Set(ids)
    // this.logger.debug(`[ShapeRepository] Selection set to:`, Array.from(this.selectedShapeIds));
  }

  /**
   * Adds one or more shapes to the current selection.
   *
   * @remarks
   * If a shape ID is already selected, it remains selected.
   *
   * @param ids - An array or a Set of shape IDs to add to the current selection.
   */
  addToSelection(ids: string[] | Set<string>): void {
    ids.forEach(id => this.selectedShapeIds.add(id))
    // if (this.selectedShapeIds.size > initialSize) this.logger.debug(`[ShapeRepository] Added to selection:`, ids);
  }

  /**
   * Removes one or more shapes from the current selection.
   *
   * @remarks
   * If a shape ID is not currently selected, attempting to remove it has no effect.
   *
   * @param ids - An array or a Set of shape IDs to remove from the current selection.
   */
  removeFromSelection(ids: string[] | Set<string>): void {
    ids.forEach(id => this.selectedShapeIds.delete(id))
    // if (this.selectedShapeIds.size < initialSize) this.logger.debug(`[ShapeRepository] Removed from selection:`, ids);
  }

  /**
   * Clears the current selection, leaving no shapes selected.
   */
  clearSelection(): void {
    this.selectedShapeIds.clear()
    // this.logger.debug(`[ShapeRepository] Selection cleared.`);
  }

  /**
   * Retrieves the set of currently selected shape IDs.
   *
   * @returns A **new** Set containing the IDs of the selected shapes.
   *          Modifying this returned Set will not affect the repository's internal state.
   */
  getSelectedIds(): Set<string> {
    return new Set(this.selectedShapeIds)
  }

  /**
   * Checks if a shape with the given ID is currently selected.
   *
   * @param id - The ID of the shape to check.
   * @returns `true` if the shape is selected, `false` otherwise.
   */
  isSelected(id: string): boolean {
    return this.selectedShapeIds.has(id)
  }

  /**
   * Removes all shapes from the repository and clears the selection state.
   *
   * @remarks
   * Resets the repository to its initial empty state.
   */
  clearAll(): void {
    this.shapes.clear()
    this.selectedShapeIds.clear()
    this._publishShapesUpdate()
  }

  /**
   * Returns all shapes of the specified type.
   *
   * @param type - The shape type to filter by.
   * @returns An array of shapes matching the specified type.
   */
  getByType(type: string): ShapeModel[] {
    const normalizedType = type.toLowerCase()
    return this.getAll().filter(shape =>
      shape.type.toLowerCase() === normalizedType,
    )
  }

  /**
   * Returns all shapes with the specified property value.
   *
   * @param key - The property key or path (e.g., 'color' or 'style.fill.color').
   * @param value - The property value to match.
   * @returns An array of shapes with the matching property value.
   */
  getByProperty(key: string, value: unknown): ShapeModel[] {
    return this.getAll().filter((shape): boolean => {
      if (!shape.properties) {
        return false
      }
      const keys = key.split('.')
      let current: unknown = shape.properties
      for (let i = 0; i < keys.length - 1; i++) {
        if (current != null && typeof current === 'object' && keys[i] in current) {
          current = (current as Record<string, unknown>)[keys[i]]
        }
        else {
          return false
        }
      }
      if (current != null && typeof current === 'object' && keys[keys.length - 1] in current) {
        const finalValue = (current as Record<string, unknown>)[keys[keys.length - 1]]
        return finalValue === value
      }
      return false
    })
  }

  /**
   * Returns all shapes with the specified metadata value.
   *
   * @param key - The metadata key or path (e.g., 'author' or 'history.created.by').
   * @param value - The metadata value to match.
   * @returns An array of shapes with the matching metadata value.
   */
  getByMetadata(key: string, value: unknown): ShapeModel[] {
    return this.getAll().filter((shape): boolean => {
      if (!shape.metadata) {
        return false
      }
      const keys = key.split('.')
      let current: unknown = shape.metadata
      for (let i = 0; i < keys.length - 1; i++) {
        if (current != null && typeof current === 'object' && keys[i] in current) {
          current = (current as Record<string, unknown>)[keys[i]]
        }
        else {
          return false
        }
      }
      if (current != null && typeof current === 'object' && keys[keys.length - 1] in current) {
        const finalValue = (current as Record<string, unknown>)[keys[keys.length - 1]]
        return finalValue === value
      }
      return false
    })
  }

  /**
   * Filters shapes based on a predicate function.
   *
   * @param predicate - A function that tests each shape.
   * @returns An array of shapes that satisfy the predicate.
   */
  filter(predicate: (shape: ShapeModel) => boolean): ShapeModel[] {
    return this.getAll().filter(predicate)
  }

  /**
   * Finds the first shape that satisfies the predicate.
   *
   * @param predicate - A function that tests each shape.
   * @returns The first shape that satisfies the predicate, or undefined if none is found.
   */
  find(predicate: (shape: ShapeModel) => boolean): ShapeModel | undefined {
    return this.getAll().find(predicate)
  }

  /**
   * Checks if a shape with the given ID exists in the repository.
   *
   * @param id - The ID to check.
   * @returns True if a shape with the given ID exists, false otherwise.
   */
  exists(id: string): boolean {
    return this.shapes.has(id)
  }

  /**
   * Returns the number of shapes in the repository.
   *
   * @returns The number of shapes.
   */
  count(): number {
    return this.shapes.size
  }

  /**
   * Publishes an event with the current state of all shapes.
   * @private
   */
  private _publishShapesUpdate(): void {
    const shapesPayload = this.getAll()
    const selectedIdsPayload = Array.from(this.selectedShapeIds)
    this.eventBus.publish({
      type: AppEventType.DataUpdated,
      timestamp: Date.now(),
      payload: {
        shapes: shapesPayload,
        selectedIds: selectedIdsPayload,
        source: 'ShapeRepository',
      },
    })
    this.logger.debug(`[ShapeRepository] Published DataUpdated event. Shapes count: ${shapesPayload.length}`)
  }

  /**
   * Brings a shape to the front within its specific layer.
   * This is achieved by setting its intraLayerZIndex to a value
   * higher than all other shapes in the same layer.
   *
   * @param id - The ID of the shape to bring to front.
   * @returns The updated shape if successful, undefined if not.
   */
  bringToFrontInLayer(id: string): ShapeModel | undefined {
    const target = this.getById(id)
    if (!target) {
      this.logger.error(`[ShapeRepository] bringToFrontInLayer: Shape with ID ${id} not found`)
      return undefined
    }

    // Get the target layer details
    const { majorCategory, minorCategory, zLevelId } = target
    if (majorCategory == null || zLevelId == null) {
      this.logger.error(`[ShapeRepository] bringToFrontInLayer: Shape ${id} is missing required layer information`)
      return undefined
    }

    // Find all shapes in the same layer
    const shapesInSameLayer = this.getAll().filter(shape =>
      shape.majorCategory === majorCategory
      && shape.minorCategory === minorCategory
      && shape.zLevelId === zLevelId
      && shape.id !== id, // 排除目标元素自身
    )

    // 计算当前层中的最大Z索引
    let maxZIndex = 0
    if (shapesInSameLayer.length > 0) {
      maxZIndex = shapesInSameLayer.reduce(
        (max, shape) => Math.max(max, shape.intraLayerZIndex ?? 0),
        0,
      )
    }

    // 始终设置为当前最大值 + 1，确保置顶
    const newZIndex = maxZIndex + 1

    // 获取当前的Z索引
    const currentZIndex = target.intraLayerZIndex ?? 0

    // 如果新值与旧值相同，则不需要更新
    if (newZIndex === currentZIndex) {
      this.logger.info(`[ShapeRepository] bringToFrontInLayer: Shape ${id} already at front with intraLayerZIndex ${currentZIndex}`)
      return target
    }

    // 更新Z索引并保存
    this.logger.info(`[ShapeRepository] bringToFrontInLayer: Setting shape ${id} intraLayerZIndex from ${currentZIndex} to ${newZIndex}`)

    const updated = this.update(id, { intraLayerZIndex: newZIndex })
    if (!updated) {
      this.logger.error(`[ShapeRepository] bringToFrontInLayer: Failed to update shape ${id}`)
      return undefined
    }

    // 返回更新后的元素
    return this.getById(id)
  }

  /**
   * Sends a shape to the back within its specific layer.
   * This is achieved by setting its intraLayerZIndex to a value
   * lower than all other shapes in the same layer.
   *
   * @param id - The ID of the shape to send to back.
   * @returns The updated shape if successful, undefined if not.
   */
  sendToBackInLayer(id: string): ShapeModel | undefined {
    const target = this.getById(id)
    if (!target) {
      this.logger.error(`[ShapeRepository] sendToBackInLayer: Shape with ID ${id} not found`)
      return undefined
    }

    // Get the target layer details
    const { majorCategory, minorCategory, zLevelId } = target
    if (majorCategory == null || zLevelId == null) {
      this.logger.error(`[ShapeRepository] sendToBackInLayer: Shape ${id} is missing required layer information`)
      return undefined
    }

    // Find all shapes in the same layer
    const shapesInSameLayer = this.getAll().filter(shape =>
      shape.majorCategory === majorCategory
      && shape.minorCategory === minorCategory
      && shape.zLevelId === zLevelId
      && shape.id !== id, // 排除目标元素自身
    )

    // 计算当前层中的最小Z索引
    let minZIndex = 0
    if (shapesInSameLayer.length > 0) {
      minZIndex = shapesInSameLayer.reduce(
        (min, shape) => Math.min(min, shape.intraLayerZIndex ?? 0),
        0,
      )
    }

    // 始终设置为当前最小值 - 1，确保置底
    const newZIndex = minZIndex - 1

    // 获取当前的Z索引
    const currentZIndex = target.intraLayerZIndex ?? 0

    // 如果新值与旧值相同，则不需要更新
    if (newZIndex === currentZIndex) {
      this.logger.info(`[ShapeRepository] sendToBackInLayer: Shape ${id} already at back with intraLayerZIndex ${currentZIndex}`)
      return target
    }

    // 更新Z索引并保存
    this.logger.info(`[ShapeRepository] sendToBackInLayer: Setting shape ${id} intraLayerZIndex from ${currentZIndex} to ${newZIndex}`)

    const updated = this.update(id, { intraLayerZIndex: newZIndex })
    if (!updated) {
      this.logger.error(`[ShapeRepository] sendToBackInLayer: Failed to update shape ${id}`)
      return undefined
    }

    // 返回更新后的元素
    return this.getById(id)
  }

  /**
   * 全量替换内部 shapes 和选中状态，并触发更新事件。
   * @param shapes - 新的 ShapeModel 数组
   * @param selectedIds - 选中的 shape id 数组（可选）
   */
  setShapesFromExternal(shapes: ShapeModel[], selectedIds?: string[]): void {
    // --- 强制同步顶层和 properties 的 majorCategory/minorCategory/zLevelId ---
    const syncLayerKeys = ['majorCategory', 'minorCategory', 'zLevelId'] as const
    const syncedShapes = shapes.map((shape) => {
      const newShape = { ...shape, properties: { ...(shape.properties || {}) } } as ShapeModel
      for (const key of syncLayerKeys) {
        if ((newShape.properties as unknown as { [key: string]: unknown })[key] !== undefined) {
          (newShape as unknown as { [key: string]: unknown })[key] = (newShape.properties as unknown as { [key: string]: unknown })[key]
        }
        else if ((newShape as unknown as { [key: string]: unknown })[key] !== undefined) {
          (newShape.properties as unknown as { [key: string]: unknown })[key] = (newShape as unknown as { [key: string]: unknown })[key]
        }
      }
      return newShape
    })
    this.shapes.clear()
    for (const shape of syncedShapes) {
      if (shape?.id) {
        this.shapes.set(shape.id, shape)
      }
    }
    if (selectedIds) {
      this.selectedShapeIds = new Set(selectedIds)
    }
    this._publishShapesUpdate()
  }
}

// deepMerge: 递归合并 plain object，仅合并对象属性
function deepMerge(target: Record<string, unknown>, source: Record<string, unknown>): Record<string, unknown> {
  const output = { ...target }

  // 特殊处理：如果source中有fill, stroke, strokeWidth, opacity等样式属性，直接应用
  const styleKeys = ['fill', 'stroke', 'strokeWidth', 'opacity', 'strokeDasharray', 'strokeLinecap', 'strokeLinejoin']
  for (const styleKey of styleKeys) {
    if (styleKey in source) {
      output[styleKey] = source[styleKey]
    }
  }

  // 特殊处理图层属性，确保它们能够正确传递
  const layerKeys = ['majorCategory', 'minorCategory', 'zLevelId']
  for (const layerKey of layerKeys) {
    if (layerKey in source && source[layerKey] !== undefined) {
      output[layerKey] = source[layerKey]
    }
  }

  for (const key in source) {
    if (Object.prototype.hasOwnProperty.call(source, key)) {
      const targetValue = output[key]
      const sourceValue = source[key]

      // 特殊处理成本相关属性，确保即使是undefined也能正确传递
      if (key === 'costUnitPrice' || key === 'costMultiplierOrCount' || key === 'costBasis' || key === 'costTotal') {
        // 如果源值是数字或字符串，直接赋值
        if (typeof sourceValue === 'number' || typeof sourceValue === 'string') {
          output[key] = sourceValue
        }
        // 如果源值是undefined或null，且目标是数字，则保留目标值
        else if ((sourceValue === undefined || sourceValue === null) && typeof targetValue === 'number') {
          // 保留目标值，不做任何操作
        }
        // 其他情况，如果源值是undefined，则明确设置为undefined
        else if (sourceValue === undefined) {
          output[key] = undefined
        }
        // 其他情况，使用源值
        else {
          output[key] = sourceValue
        }
      }
      // 特殊处理样式属性，确保它们能够正确传递
      else if (styleKeys.includes(key)) {
        // 已在上面处理过，这里跳过
        continue
      }
      // 特殊处理图层属性，确保它们能够正确传递
      else if (layerKeys.includes(key)) {
        // 已在上面处理过，这里跳过
        continue
      }
      // 对象递归合并
      else if (
        typeof sourceValue === 'object'
        && sourceValue !== null
        && !Array.isArray(sourceValue)
        && typeof targetValue === 'object'
        && targetValue !== null
        && !Array.isArray(targetValue)
      ) {
        output[key] = deepMerge(targetValue as Record<string, unknown>, sourceValue as Record<string, unknown>)
      }
      // 其他情况，直接使用源值
      else {
        output[key] = sourceValue
      }
    }
  }

  // 确保成本计算相关属性有默认值
  if ('costUnitPrice' in output && output.costUnitPrice === undefined) {
    output.costUnitPrice = 0
  }
  if ('costMultiplierOrCount' in output && output.costMultiplierOrCount === undefined) {
    output.costMultiplierOrCount = 1
  }
  if ('costBasis' in output && output.costBasis === undefined) {
    output.costBasis = 'area'
  }

  return output
}
