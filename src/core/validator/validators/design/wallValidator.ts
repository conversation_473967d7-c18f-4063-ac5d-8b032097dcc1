/**
 * Validator for wall elements.
 */

import type { WallProperties } from '@/types/core/element/design/wallDesignTypes'
import type Point from '@/types/core/element/geometry/point' // Default import
import type {
  Path, // To access specific path types like Path.Line, Path.Polyline
  <PERSON> as ShapeModel,
} from '@/types/core/elementDefinitions'
import type { ValidatableShape, ValidationError } from '@/types/core/validator/validator-interface'
import { WallType } from '@/types/core/element/design/wallDesignTypes'
import {
  ElementType, // Import ShapeElement as ShapeModel
} from '@/types/core/elementDefinitions'
import { ValidationErrorCode } from '@/types/core/validator/error-codes'
import { isValidPoint } from '../common/validationUtils'
import { BaseDesignValidator } from './designValidatorBase'

// ConcreteWallElement represents the expected structure of a wall element for validation.
// It combines ShapeModel (which is ShapeElement, providing id, type, metadata, position etc.)
// with WallProperties (providing wallType, thickness, height, path, openingIds).
/**
 * Represents the expected structure of a wall element for validation purposes.
 * This type combines the base {@link ShapeModel} (aliased from ShapeElement)
 * with wall-specific {@link WallProperties}.
 * @remarks Combines generic shape data with specific wall attributes.
 */
type ConcreteWallElement = ShapeModel & WallProperties

/**
 * Validator for wall elements.
 *
 * @extends BaseDesignValidator
 */
export class WallValidator extends BaseDesignValidator {
  /**
   * The design element category this validator is for.
   *
   * @readonly
   * @override
   */
  protected readonly expectedElementType = ElementType.WALL

  /**
   * Validate specific wall properties.
   *
   * @param shape - The shape to validate.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected validateSpecific(shape: ValidatableShape): ValidationError[] {
    const errors: ValidationError[] = []

    const typeValidationErrors = this.validateBaseType(shape)
    if (typeValidationErrors.length > 0) {
      return typeValidationErrors
    }

    const wall = shape as ConcreteWallElement

    // Validate wallType
    if (wall.wallType == null || !Object.values(WallType).includes(wall.wallType)) {
      errors.push({
        code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
        message: `Wall requires a valid 'wallType'. Received: ${wall.wallType}`,
        path: 'wallType',
        value: wall.wallType,
      })
    }

    // Validate thickness
    if (typeof wall.thickness !== 'number' || wall.thickness <= 0 || !Number.isFinite(wall.thickness)) {
      errors.push({
        code: ValidationErrorCode.INVALID_DIMENSION,
        message: `Wall 'thickness' must be a positive finite number. Received: ${wall.thickness}`,
        path: 'thickness',
        value: wall.thickness,
      })
    }

    // Validate height
    if (typeof wall.height !== 'number' || wall.height <= 0 || !Number.isFinite(wall.height)) {
      errors.push({
        code: ValidationErrorCode.INVALID_DIMENSION,
        message: `Wall 'height' must be a positive finite number. Received: ${wall.height}`,
        path: 'height',
        value: wall.height,
      })
    }

    // Validate path (centerline)
    if (wall.path == null) {
      errors.push({
        code: ValidationErrorCode.MISSING_PROPERTY,
        message: 'Wall requires a "path" (centerline) definition.',
        path: 'path',
      })
    }
    else {
      if (typeof wall.path !== 'object' || wall.path === null) {
        errors.push({
          code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
          message: 'Wall "path" must be a valid path object (Line, Polyline, etc.).',
          path: 'path',
          value: wall.path,
        })
      }
      else {
        // Check based on the type of the path
        const pathObject = wall.path as unknown as ShapeModel // Use double assertion
        if (pathObject.type === ElementType.LINE) {
          const linePath = wall.path as Path.Line // Now cast to specific path type
          if (!isValidPoint(linePath.start) || !isValidPoint(linePath.end)) {
            errors.push({ code: ValidationErrorCode.INVALID_POINT, message: 'Wall path (Line) has invalid start or end points.', path: 'path' })
          }
        }
        else if (pathObject.type === ElementType.POLYLINE) {
          const polylinePath = wall.path as Path.Polyline // Now cast to specific path type
          // @ts-expect-error - Accessing points property on polyline path
          const points = polylinePath.points as Point[] | undefined
          if (!Array.isArray(points) || points.length < 2) {
            errors.push({ code: ValidationErrorCode.INSUFFICIENT_POINTS, message: 'Wall path (Polyline) must have at least 2 points.', path: 'path.points' })
          }
          else {
            points.forEach((p: Point, i: number) => {
              if (!isValidPoint(p)) {
                errors.push({ code: ValidationErrorCode.INVALID_POINT, message: `Invalid point in wall path (Polyline) at index ${i}.`, path: `path.points[${i}]`, value: p })
              }
            })
          }
        }
        // Add checks for Arc, Quadratic, Cubic if necessary
      }
    }

    // Validate openingIds
    if (!Array.isArray(wall.openingIds)) {
      errors.push({
        code: ValidationErrorCode.INVALID_PROPERTY_VALUE, // Corrected error code
        message: 'Wall "openingIds" must be an array of strings.',
        path: 'openingIds',
        value: wall.openingIds,
      })
    }
    else {
      wall.openingIds.forEach((id: string, i: number) => { // Added types for id and i
        if (typeof id !== 'string' || id.trim() === '') {
          errors.push({
            code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
            message: `Invalid opening ID at index ${i}: must be a non-empty string.`,
            path: `openingIds[${i}]`,
            value: id,
          })
        }
      })
    }

    return errors
  }
}
