import type { ServiceResult } from '../../../types/services/core/serviceResult'
import type {
  ElementSelectRequest,
  ElementSelectResult,
  ElementSelectionService as IElementSelectionService,
} from '../../../types/services/shapes/shapeService'
/**
 * Shape Selection Service Implementation
 *
 * This service manages the selection state of shapes within the application.
 * It handles requests to select, deselect, and clear selections, and
 * publishes events when the selection changes.
 *
 * @module services/elements/element-actions/elementSelectionService
 */
import type { AppEventMap, EventBus } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import { v4 as uuidv4 } from 'uuid'

import { getService, ServiceId } from '@/services/core/registry'
import { AppEventType } from '@/types/services/events'
import {
  SelectionMode,
} from '../../../types/services/shapes'

/**
 * Implements the {@link IElementSelectionService} interface to manage shape selection state.
 * It interacts with the {@link ShapeRepository} to update selection and uses the
 * {@link EventBus} to notify other parts of the application about selection changes.
 */
export class ElementSelectionService implements IElementSelectionService {
  readonly serviceId: string = ServiceId.ElementSelectionService as string

  private eventBus: EventBus<AppEventMap>
  private logger: LoggerService

  constructor(eventBus: EventBus<AppEventMap>, logger: LoggerService) {
    this.eventBus = eventBus
    this.logger = logger
    this.logger.info('[ElementSelectionService] Initialized.')
  }

  /**
   * Factory method to create a {@link ElementSelectionService} instance.
   * Dependencies like EventBus and LoggerService are resolved from the service registry.
   * @param {LoggerService} [logger] - Optional logger service. If not provided, it's retrieved from the registry.
   * @returns {ElementSelectionService} A new instance of the ElementSelectionService.
   * @throws {Error} If dependencies cannot be resolved.
   */
  public static create(
    logger?: LoggerService,
  ): ElementSelectionService {
    try {
      const eventBus = getService<EventBus<AppEventMap>>(ServiceId.EventBus)
      const loggerService = logger || getService<LoggerService>(ServiceId.Logger)

      return new ElementSelectionService(eventBus, loggerService)
    }
    catch (error) {
      throw new Error(`Failed to create ElementSelectionService: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * Publishes a shape select request event for a single shape.
   * @param request Shape select parameters
   * @returns Promise that resolves when the event is published
   */
  async selectElement(request: ElementSelectRequest & { selectionMode?: string }): Promise<ElementSelectResult> {
    this.logger.debug('[ElementSelectionService] selectElement called', request)
    try {
      this.logger.debug('[ElementSelectionService] Publishing ElementSelectRequest', { request })
      this.logger.debug(`[ElementSelectionService] Publishing ElementSelectRequest for ID ${request.id}`, { request })
      this.eventBus.publish({
        type: AppEventType.ElementSelectRequest,
        timestamp: Date.now(),
        payload: {
          elementIds: [request.id],
          selectionMode: request.selectionMode ?? (request.multiSelect ? 'add' : 'replace'),
          source: ElementSelectionService.name,
        },
      })
      return {
        success: true,
        data: [],
        timestamp: Date.now(),
      }
    }
    catch (error) {
      this.emitError('ELEMENT_SELECT_ERROR', error instanceof Error ? error.message : 'Unknown error', { request })
      return {
        success: false,
        error: {
          code: 'ELEMENT_SELECT_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          details: error,
        },
        timestamp: Date.now(),
      }
    }
  }

  /**
   * Publishes a shape select request event for multiple shapes.
   * @param elementIds Array of shape IDs to select
   * @param clearExisting If true, replaces selection; otherwise, adds to selection
   * @param selectionMode Optional explicit selection mode
   * @returns Promise that resolves when the event is published
   */
  async selectElements(elementIds: string[], clearExisting: boolean = false, selectionMode?: string): Promise<ElementSelectResult> {
    this.logger.debug('[ElementSelectionService] selectElements called', { elementIds, clearExisting, selectionMode })
    try {
      this.logger.debug('[ElementSelectionService] Publishing ElementSelectRequest (batch)', { elementIds, clearExisting, selectionMode })
      this.logger.debug(`[ElementSelectionService] Publishing ElementSelectRequest for IDs`, { elementIds, clearExisting, selectionMode })
      this.eventBus.publish({
        type: AppEventType.ElementSelectRequest,
        timestamp: Date.now(),
        payload: {
          elementIds,
          selectionMode: selectionMode ?? (clearExisting ? 'replace' : 'add'),
          source: ElementSelectionService.name,
        },
      })
      return {
        success: true,
        data: [],
        timestamp: Date.now(),
      }
    }
    catch (error) {
      this.emitError('ELEMENT_SELECT_ERROR', error instanceof Error ? error.message : 'Unknown error', { elementIds, clearExisting, selectionMode })
      return {
        success: false,
        error: {
          code: 'ELEMENT_SELECT_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          details: error,
        },
        timestamp: Date.now(),
      }
    }
  }

  /**
   * Publishes a shape select request event to clear selection.
   * @returns Promise that resolves when the event is published
   */
  async clearElementSelection(): Promise<ServiceResult<void>> {
    try {
      this.logger.debug(`[ElementSelectionService] Publishing ElementSelectRequest to clear selection`)
      this.eventBus.publish({
        type: AppEventType.ElementSelectRequest,
        timestamp: Date.now(),
        payload: {
          elementIds: [],
          selectionMode: SelectionMode.Clear,
          source: ElementSelectionService.name,
        },
      })
      return {
        success: true,
        timestamp: Date.now(),
      }
    }
    catch (error) {
      this.emitError('ELEMENT_SELECT_ERROR', error instanceof Error ? error.message : 'Unknown error', {})
      return {
        success: false,
        error: {
          code: 'ELEMENT_SELECT_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          details: error,
        },
        timestamp: Date.now(),
      }
    }
  }

  /**
   * Emits an error event for shape selection failures.
   */
  private emitError(errorType: string, message: string, context?: Record<string, unknown>): void {
    const errorId = uuidv4()
    const fullMessage = `[${errorType}] ${message}`
    this.logger.error(fullMessage, { errorId, ...(context || {}) })
    this.eventBus.publish({
      type: AppEventType.EventError,
      timestamp: Date.now(),
      payload: {
        error: {
          code: errorType,
          message: fullMessage,
          details: context,
          errorId,
        },
        context,
      },
    })
  }
}
