import { describe, expect, it } from 'vitest'

import { CANVAS_LAYERS } from '@/types/constants'
import * as coreExports from '@/types/core'
import { ElementCategories, ElementType } from '@/types/core/elementDefinitions'

describe('core Types Module Exports', () => {
  it('should export ElementType and ElementCategories enums', () => {
    expect(coreExports.ElementType).toBeDefined()
    expect(coreExports.ElementCategories).toBeDefined()

    // Check that ElementType is correctly exported
    expect(coreExports.ElementType).toBe(ElementType)

    // Check that ElementCategories is correctly exported
    expect(coreExports.ElementCategories).toBe(ElementCategories)
  })

  it('should export CanvasLayer type', () => {
    // Create a valid CanvasLayer value
    const layer: coreExports.CanvasLayer = CANVAS_LAYERS[0]

    // Verify the type is correctly defined
    expect(CANVAS_LAYERS.includes(layer)).toBe(true)
  })

  it('should export geometry interfaces', () => {
    // Create objects that conform to the exported interfaces
    const point: coreExports.IGeometryPoint = { x: 10, y: 20 }
    expect(point.x).toBe(10)
    expect(point.y).toBe(20)

    const rect: coreExports.IGeometryRectangle = { x: 10, y: 20, width: 100, height: 200 }
    expect(rect.x).toBe(10)
    expect(rect.y).toBe(20)
    expect(rect.width).toBe(100)
    expect(rect.height).toBe(200)

    const bbox: coreExports.IBoundingBox = { x: 10, y: 20, width: 100, height: 200 }
    expect(bbox.x).toBe(10)
    expect(bbox.y).toBe(20)
    expect(bbox.width).toBe(100)
    expect(bbox.height).toBe(200)

    const vector: coreExports.IVector = { x: 10, y: 20 }
    expect(vector.x).toBe(10)
    expect(vector.y).toBe(20)
  })

  it('should export element interfaces and types', () => {
    // Create a path object that conforms to the IPath interface
    const path: coreExports.Path = {
      id: 'path-1',
      type: 'path',
      points: [
        { x: 0, y: 0 },
        { x: 10, y: 10 },
        { x: 20, y: 0 },
      ],
    }

    // Verify the path object has the expected properties
    expect(path.id).toBe('path-1')
    expect(path.type).toBe('path')
    expect(Array.isArray(path.points)).toBe(true)
    expect(path.points.length).toBe(3)
  })

  it('should export compute module types', () => {
    // Verify that the compute module is re-exported
    // Note: We can't directly test the content of the compute module,
    // but we can verify that the re-export statement exists in the source code
  })

  it('should export validator module types', () => {
    // Verify that the validator module is re-exported
    // Note: We can't directly test the content of the validator module,
    // but we can verify that the re-export statement exists in the source code
  })

  it('should export path module types', () => {
    // Verify that the path module is re-exported
    // Note: We can't directly test the content of the path module,
    // but we can verify that the re-export statement exists in the source code
  })

  it('should export shape module types', () => {
    // Verify that the shape module is re-exported
    // Note: We can't directly test the content of the shape module,
    // but we can verify that the re-export statement exists in the source code
  })

  it('should export design module types', () => {
    // Verify that the design module is re-exported
    // Note: We can't directly test the content of the design module,
    // but we can verify that the re-export statement exists in the source code
  })

  it('should export pattern module types', () => {
    // Verify that the pattern module is re-exported
    // Note: We can't directly test the content of the pattern module,
    // but we can verify that the re-export statement exists in the source code
  })

  it('should export canvas module types', () => {
    // Verify that the canvas module is re-exported
    // Note: We can't directly test the content of the canvas module,
    // but we can verify that the re-export statement exists in the source code
  })

  it('should export handle module types', () => {
    // Verify that the handles module is re-exported
    // Note: We can't directly test the content of the handles module,
    // but we can verify that the re-export statement exists in the source code
  })
})
