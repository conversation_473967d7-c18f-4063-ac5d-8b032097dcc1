/**
 * Centralized export for shape creator classes.
 *
 * @remarks
 * This index file serves as the public entry point for all shape creator implementations
 * within the `@/core/factory/creators/shape` directory. It re-exports the concrete
 * creator classes for various shape types, such as {@link RectangleCreator} (which also handles squares),
 * {@link EllipseCreator} (which also handles circles), and {@link PolygonCreator} (which handles
 * various n-sided polygons like triangles and hexagons).
 *
 * The {@link ShapeCreator} interface itself is typically exported from a higher-level index
 * or directly from its definition file if it's a shared base.
 * @module core/factory/creators/shape/index
 */

export { EllipseCreator } from './EllipseCreator' // Handles Ellipse and Circle
export { PolygonCreator } from './PolygonCreator' // Handles Polygon, Triangle, Hexagon
// Export concrete shape creator implementations
export { RectangleCreator } from './RectangleCreator' // Handles Rectangle and Square
