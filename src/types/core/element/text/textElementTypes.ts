import type Point from '@/types/core/element/geometry/point'
/**
 * Defines the type for text elements within the application.
 *
 * @remarks
 * This module provides the `Text` interface, which extends {@link ShapeElement}
 * to include properties specific to rendering and styling text, such as its content,
 * position, font size, and font family.
 *
 * @module types/core/element/text/textElementTypes
 */
import type { ShapeElement } from '@/types/core/elementDefinitions'

/**
 * Represents a text element with its content and styling properties.
 *
 * @remarks
 * This interface defines the structure for text elements, extending {@link ShapeElement}
 * with text-specific attributes. It forms the basis for core text rendering
 * capabilities within the system.
 *
 * @example
 * ```typescript
 * const myTextElement: Text = {
 *   // Properties from ShapeElement (id, type, visible, etc.)
 *   id: 'text-001',
 *   type: 'text', // Or a more specific ElementType if defined
 *   visible: true,
 *   // ... other ShapeElement properties
 *   // Text-specific properties
 *   content: 'Hello, World!',
 *   position: { x: 50, y: 150 },
 *   fontSize: 24,
 *   fontFamily: 'Helvetica, Arial, sans-serif'
 * };
 * ```
 *
 * @see {@link ShapeElement} for base shape properties.
 * @see {@link Point} for the structure of position coordinates.
 * @see {@link TextStyle} for more advanced text styling properties (if defined elsewhere).
 * @see {@link TextLayout} for text layout configuration options (if defined elsewhere).
 */
export interface Text extends ShapeElement {
  /**
   * The actual text content to be displayed.
   */
  content: string

  /**
   * The x and y coordinates where the text rendering should begin (e.g., top-left corner, baseline).
   * The exact interpretation might depend on text alignment and rendering settings.
   */
  position: Point // Note: ShapeElement also has a position. Clarify if this overrides or is an alias.

  /**
   * The size of the font in pixels.
   * @remarks This value must be a positive number.
   * @throws {Error} Implementations might throw an error if a non-positive value is set.
   */
  fontSize: number

  /**
   * A string specifying the font family (or a prioritized list of font families)
   * to be used for rendering the text (e.g., "Arial", "Times New Roman, serif").
   */
  fontFamily: string

  /** Optional: The weight (e.g., 'normal', 'bold') or numeric value (e.g., 400, 700) of the font. */
  fontWeight?: string | number

  /** Optional: The style of the font (e.g., 'normal', 'italic', 'oblique'). */
  fontStyle?: string

  /** Optional: Horizontal alignment of the text (e.g., 'left', 'right', 'center', 'start', 'end'). */
  textAlign?: CanvasTextAlign // Using CanvasTextAlign for standard values

  /** Optional: Vertical alignment of the text baseline (e.g., 'top', 'hanging', 'middle', 'alphabetic', 'ideographic', 'bottom'). */
  textBaseline?: CanvasTextBaseline // Using CanvasTextBaseline for standard values

  /** Optional: The height of a line of text. */
  lineHeight?: number

  // Due to errors in other code, the text field is added here for use by
  // the data module (TODO: this field should not exist, please check
  // which module incorrectly wrote this field)
  text: string
}
