/**
 * Element Transformation Utility Functions
 *
 * @remarks
 * This module provides a collection of utility functions for performing geometric
 * transformations on elements ({@link ShapeModel}) and points ({@link IPoint}).
 * These utilities include operations such as:
 * - Rotating and scaling individual points around a center.
 * - Rotating, scaling, moving (translating), and flipping entire elements.
 * - Aligning multiple elements to a grid.
 * - Distributing multiple elements horizontally or vertically.
 *
 * The functions are exposed as static methods of the {@link TransformUtils} class
 * and are also re-exported directly and via the {@link TransformationUtils} namespace object
 * for convenient access.
 *
 * These utilities are essential for user interactions like direct manipulation of
 * elements on a canvas, as well as for programmatic layout adjustments.
 *
 * @module lib/utils/element/transformUtils
 * @see {@link ShapeModel}
 * @see {@link IPoint}
 * @see {@link Point} (PointClass alias)
 */

import type { PointData as IPoint } from '../../../types/core/element/geometry/point' // Use PointData and alias
// Removed unused import for Vector
import type { ShapeElement as ShapeModel } from '@/types/core/elementDefinitions' // Use type import
import { PointClass as Point } from '../geometry/PointClass' // Import PointClass as Point

/**
 * Provides static utility methods for geometric transformations of elements and points.
 */
export class TransformUtils {
  /**
   * Rotates a given point around a specified center point by a given angle.
   *
   * @param point - The {@link IPoint} to rotate.
   * @param center - The {@link IPoint} representing the center of rotation.
   * @param angleRadians - The angle of rotation in radians.
   * @returns A new {@link Point} instance representing the rotated point.
   * @throws {@link Error} if point, center, or angle is invalid.
   */
  static rotatePoint(
    point: IPoint,
    center: IPoint,
    angleRadians: number,
  ): Point {
    if (!point || typeof point.x !== 'number' || typeof point.y !== 'number') {
      throw new Error('Valid point must be provided for rotation')
    }
    if (!center || typeof center.x !== 'number' || typeof center.y !== 'number') {
      throw new Error('Valid center point must be provided for rotation')
    }
    if (typeof angleRadians !== 'number') {
      throw new TypeError('Angle must be a number in radians')
    }

    const cos = Math.cos(angleRadians)
    const sin = Math.sin(angleRadians)
    const dx = point.x - center.x
    const dy = point.y - center.y

    return new Point(
      center.x + dx * cos - dy * sin,
      center.y + dx * sin + dy * cos,
      point.z, // Preserve z-coordinate if it exists
    )
  }

  /**
   * Scales a given point relative to a specified center point by given scale factors.
   *
   * @param point - The {@link IPoint} to scale.
   * @param center - The {@link IPoint} representing the center of scaling.
   * @param scaleX - The scaling factor along the x-axis.
   * @param scaleY - The scaling factor along the y-axis (defaults to `scaleX` if not provided).
   * @returns A new {@link Point} instance representing the scaled point.
   * @throws {@link Error} if point, center, or scale factors are invalid.
   */
  static scalePoint(
    point: IPoint,
    center: IPoint,
    scaleX: number,
    scaleY: number = scaleX,
  ): Point {
    if (!point || typeof point.x !== 'number' || typeof point.y !== 'number') {
      throw new Error('Valid point must be provided for scaling')
    }
    if (!center || typeof center.x !== 'number' || typeof center.y !== 'number') {
      throw new Error('Valid center point must be provided for scaling')
    }
    if (typeof scaleX !== 'number' || typeof scaleY !== 'number') {
      throw new TypeError('Scale factors must be numbers')
    }

    const dx = point.x - center.x
    const dy = point.y - center.y

    return new Point(
      center.x + dx * scaleX,
      center.y + dy * scaleY,
      point.z, // Preserve z-coordinate
    )
  }

  /**
   * Rotates a {@link ShapeModel} element around a specified center point by a given angle.
   *
   * @remarks
   * This function performs a deep copy of the element, then rotates its main `position`.
   * If the element has `properties.points` (e.g., Polygon, Polyline) or `properties.start`/`properties.end`
   * (e.g., Line), these points are also rotated relative to the new element position.
   * The element's `rotation` property is updated by converting the radian angle to degrees.
   *
   * @param element - The {@link ShapeModel} to rotate.
   * @param center - The {@link IPoint} representing the center of rotation.
   * @param angleRadians - The angle of rotation in radians.
   * @returns A new, rotated {@link ShapeModel} instance.
   * @throws {@link Error} if the element or center point is invalid.
   */
  static rotateElement(
    element: ShapeModel,
    center: IPoint,
    angleRadians: number,
  ): ShapeModel {
    if (!element) {
      throw new Error('Element must be provided for rotation')
    }

    const result: ShapeModel = JSON.parse(JSON.stringify(element)) // Deep copy

    const currentPosition = new Point(element.position.x, element.position.y, element.position.z)
    const rotatedPosition = TransformUtils.rotatePoint(currentPosition, center, angleRadians)
    result.position = { x: rotatedPosition.x, y: rotatedPosition.y, z: rotatedPosition.z }

    result.properties = result.properties || {}

    if (element.properties && Array.isArray(element.properties.points)) {
      result.properties.points = element.properties.points.map((p: IPoint) => {
        const absP = new Point(p.x + element.position.x, p.y + element.position.y, p.z)
        const rotatedAbsP = TransformUtils.rotatePoint(absP, center, angleRadians)
        return {
          x: rotatedAbsP.x - result.position.x,
          y: rotatedAbsP.y - result.position.y,
          z: rotatedAbsP.z,
        }
      })
    }

    if (element.properties?.start && element.properties.end) {
      const startPoint = element.properties.start as IPoint
      const endPoint = element.properties.end as IPoint
      const absStart = new Point(startPoint.x + element.position.x, startPoint.y + element.position.y, startPoint.z)
      const absEnd = new Point(endPoint.x + element.position.x, endPoint.y + element.position.y, endPoint.z)
      const rotatedAbsStart = TransformUtils.rotatePoint(absStart, center, angleRadians)
      const rotatedAbsEnd = TransformUtils.rotatePoint(absEnd, center, angleRadians)
      result.properties.start = { x: rotatedAbsStart.x - result.position.x, y: rotatedAbsStart.y - result.position.y, z: rotatedAbsStart.z }
      result.properties.end = { x: rotatedAbsEnd.x - result.position.x, y: rotatedAbsEnd.y - result.position.y, z: rotatedAbsEnd.z }
    }

    const currentRotation = typeof element.rotation === 'number' ? element.rotation : 0
    result.rotation = (currentRotation + (angleRadians * 180 / Math.PI))
    result.rotation %= 360

    return result
  }

  /**
   * Scales a {@link ShapeModel} element relative to a specified center point by given scale factors.
   *
   * @remarks
   * This function performs a deep copy of the element. It scales the element's main `position`.
   * It also attempts to scale common dimensional properties found in `element.properties`
   * (like `width`, `height`, `radius`, `radiusX`, `radiusY`) and the element's `strokeWidth`.
   * If the element has `properties.points` or `properties.start`/`properties.end`, these points
   * are also scaled relative to the new element position.
   *
   * @param element - The {@link ShapeModel} to scale.
   * @param center - The {@link IPoint} representing the center of scaling.
   * @param scaleX - The scaling factor along the x-axis.
   * @param scaleY - The scaling factor along the y-axis (defaults to `scaleX`).
   * @returns A new, scaled {@link ShapeModel} instance.
   * @throws {@link Error} if element, center, or scale factors are invalid. Warns if scaling by zero.
   */
  static scaleElement(
    element: ShapeModel,
    center: IPoint,
    scaleX: number,
    scaleY: number = scaleX,
  ): ShapeModel {
    if (!element) {
      throw new Error('Element must be provided for scaling')
    }
    if (!center || typeof center.x !== 'number' || typeof center.y !== 'number') {
      throw new Error('Valid center point must be provided for scaling')
    }
    if (typeof scaleX !== 'number' || typeof scaleY !== 'number') {
      throw new TypeError('Scale factors must be numbers')
    }
    if (scaleX === 0 || scaleY === 0) {
      console.warn('Scaling by zero can lead to degenerate elements.')
    }

    const result: ShapeModel = JSON.parse(JSON.stringify(element))

    const currentPosition = new Point(element.position.x, element.position.y, element.position.z)
    const scaledPosition = TransformUtils.scalePoint(currentPosition, center, scaleX, scaleY)
    result.position = { x: scaledPosition.x, y: scaledPosition.y, z: scaledPosition.z }

    result.properties = result.properties || {}

    if (element.properties) {
      if (typeof element.properties.width === 'number') {
        result.properties.width = element.properties.width * Math.abs(scaleX)
      }
      if (typeof element.properties.height === 'number') {
        result.properties.height = element.properties.height * Math.abs(scaleY)
      }
      if (typeof element.properties.radius === 'number') {
        result.properties.radius = element.properties.radius * Math.sqrt(Math.abs(scaleX) * Math.abs(scaleY))
      }
      if (typeof element.properties.radiusX === 'number') {
        result.properties.radiusX = element.properties.radiusX * Math.abs(scaleX)
      }
      if (typeof element.properties.radiusY === 'number') {
        result.properties.radiusY = element.properties.radiusY * Math.abs(scaleY)
      }
    }
    if (typeof element.strokeWidth === 'number') {
      const avgScale = (Math.abs(scaleX) + Math.abs(scaleY)) / 2
      result.strokeWidth = element.strokeWidth * avgScale
    }

    if (element.properties && Array.isArray(element.properties.points)) {
      result.properties.points = element.properties.points.map((p: IPoint) => {
        const absP = new Point(p.x + element.position.x, p.y + element.position.y, p.z)
        const scaledAbsP = TransformUtils.scalePoint(absP, center, scaleX, scaleY)
        return {
          x: scaledAbsP.x - result.position.x,
          y: scaledAbsP.y - result.position.y,
          z: scaledAbsP.z,
        }
      })
    }

    if (element.properties?.start && element.properties.end) {
      const startPoint = element.properties.start as IPoint
      const endPoint = element.properties.end as IPoint
      const absStart = new Point(startPoint.x + element.position.x, startPoint.y + element.position.y, startPoint.z)
      const absEnd = new Point(endPoint.x + element.position.x, endPoint.y + element.position.y, endPoint.z)
      const scaledAbsStart = TransformUtils.scalePoint(absStart, center, scaleX, scaleY)
      const scaledAbsEnd = TransformUtils.scalePoint(absEnd, center, scaleX, scaleY)
      result.properties.start = { x: scaledAbsStart.x - result.position.x, y: scaledAbsStart.y - result.position.y, z: scaledAbsStart.z }
      result.properties.end = { x: scaledAbsEnd.x - result.position.x, y: scaledAbsEnd.y - result.position.y, z: scaledAbsEnd.z }
    }

    return result
  }

  /**
   * Moves (translates) a {@link ShapeModel} element by a specified vector.
   *
   * @remarks
   * This function creates a shallow copy of the element and updates its `position`
   * by adding the vector's components. If element points are defined relative to its
   * position, they do not need to be explicitly updated here.
   *
   * @param element - The {@link ShapeModel} to move.
   * @param vector - An {@link IPoint} representing the translation vector (dx, dy, dz).
   * @returns A new {@link ShapeModel} instance at the translated position.
   * @throws {@link Error} if the element or vector is invalid.
   */
  static moveElement(
    element: ShapeModel,
    vector: IPoint,
  ): ShapeModel {
    if (!element) {
      throw new Error('Element must be provided for movement')
    }
    if (!vector || typeof vector.x !== 'number' || typeof vector.y !== 'number') {
      throw new Error('Valid vector must be provided for movement')
    }
    const result: ShapeModel = { ...element }
    result.position = {
      x: element.position.x + vector.x,
      y: element.position.y + vector.y,
      z: element.position.z, // Preserve z-coordinate
    }
    // If element points are relative to its position, they don't need to change during translation.
    return result
  }

  /**
   * Flips a {@link ShapeModel} element horizontally around a specified center point.
   *
   * @remarks
   * This is achieved by scaling the element with `scaleX = -1` and `scaleY = 1`
   * around the given center (or the element's current position if no center is provided).
   *
   * @param element - The {@link ShapeModel} to flip.
   * @param center - The {@link IPoint} representing the axis of horizontal flipping. Defaults to `element.position`.
   * @returns A new, horizontally flipped {@link ShapeModel} instance.
   * @throws {@link Error} if the element is invalid.
   */
  static flipElementHorizontally(
    element: ShapeModel,
    center: IPoint = element.position,
  ): ShapeModel {
    if (!element) {
      throw new Error('Element must be provided for horizontal flipping')
    }
    return TransformUtils.scaleElement(element, center, -1, 1)
  }

  /**
   * Flips a {@link ShapeModel} element vertically around a specified center point.
   *
   * @remarks
   * This is achieved by scaling the element with `scaleX = 1` and `scaleY = -1`
   * around the given center (or the element's current position if no center is provided).
   *
   * @param element - The {@link ShapeModel} to flip.
   * @param center - The {@link IPoint} representing the axis of vertical flipping. Defaults to `element.position`.
   * @returns A new, vertically flipped {@link ShapeModel} instance.
   * @throws {@link Error} if the element is invalid.
   */
  static flipElementVertically(
    element: ShapeModel,
    center: IPoint = element.position,
  ): ShapeModel {
    if (!element) {
      throw new Error('Element must be provided for vertical flipping')
    }
    return TransformUtils.scaleElement(element, center, 1, -1)
  }

  /**
   * Aligns the positions of multiple {@link ShapeModel} elements to a specified grid size.
   *
   * @remarks
   * For each element, its `position.x` and `position.y` are rounded to the nearest
   * multiple of `gridSize`. This returns a new array of elements with aligned positions.
   *
   * @param elements - An array of {@link ShapeModel} instances to align.
   * @param gridSize - The size of the grid cells to align to.
   * @returns A new array of {@link ShapeModel} instances with their positions aligned to the grid.
   */
  static alignElementsToGrid(
    elements: ShapeModel[],
    gridSize: number,
  ): ShapeModel[] {
    return elements.map((element) => {
      const alignedX = Math.round(element.position.x / gridSize) * gridSize
      const alignedY = Math.round(element.position.y / gridSize) * gridSize
      return {
        ...element,
        position: {
          ...element.position, // Preserve other potential position properties like z
          x: alignedX,
          y: alignedY,
        },
      }
    })
  }

  /**
   * Distributes an array of {@link ShapeModel} elements horizontally between a start and end x-coordinate.
   *
   * @remarks
   * Elements are first sorted by their current x-position. Then, they are evenly spaced
   * between `startX` and `endX`. This modifies the `position.x` of the elements and
   * returns a new array.
   *
   * @param elements - An array of {@link ShapeModel} instances to distribute.
   * @param startX - The starting x-coordinate for the distribution range.
   * @param endX - The ending x-coordinate for the distribution range.
   * @returns A new array of {@link ShapeModel} instances with horizontally distributed positions.
   *          Returns a copy of the original array if 0 or 1 elements are provided.
   */
  static distributeElementsHorizontally(
    elements: ShapeModel[],
    startX: number,
    endX: number,
  ): ShapeModel[] {
    if (elements.length <= 1) {
      return [...elements]
    }
    const sortedElements = [...elements].sort((a, b) => a.position.x - b.position.x)
    const step = (endX - startX) / (sortedElements.length - 1)
    return sortedElements.map((element, index) => {
      const newX = startX + index * step
      return {
        ...element,
        position: {
          ...element.position,
          x: newX,
        },
      }
    })
  }

  /**
   * Distributes an array of {@link ShapeModel} elements vertically between a start and end y-coordinate.
   *
   * @remarks
   * Elements are first sorted by their current y-position. Then, they are evenly spaced
   * between `startY` and `endY`. This modifies the `position.y` of the elements and
   * returns a new array.
   *
   * @param elements - An array of {@link ShapeModel} instances to distribute.
   * @param startY - The starting y-coordinate for the distribution range.
   * @param endY - The ending y-coordinate for the distribution range.
   * @returns A new array of {@link ShapeModel} instances with vertically distributed positions.
   *          Returns a copy of the original array if 0 or 1 elements are provided.
   */
  static distributeElementsVertically(
    elements: ShapeModel[],
    startY: number,
    endY: number,
  ): ShapeModel[] {
    if (elements.length <= 1) {
      return [...elements]
    }
    const sortedElements = [...elements].sort((a, b) => a.position.y - b.position.y)
    const step = (endY - startY) / (sortedElements.length - 1)
    return sortedElements.map((element, index) => {
      const newY = startY + index * step
      return {
        ...element,
        position: {
          ...element.position,
          y: newY,
        },
      }
    })
  }
}

// Export all static methods for direct use
export const {
  rotateElement,
  scaleElement,
  moveElement,
  rotatePoint,
  scalePoint,
  flipElementHorizontally,
  flipElementVertically,
  alignElementsToGrid,
  distributeElementsHorizontally,
  distributeElementsVertically,
} = TransformUtils

/**
 * Namespace object for convenient access to transformation utility functions.
 *
 * @remarks
 * This object groups the static methods from {@link TransformUtils} under a
 * single export, allowing for usage like `TransformationUtils.rotateElement(...)`.
 */
export const TransformationUtils = {
  rotateElement,
  scaleElement,
  moveElement,
  rotatePoint,
  scalePoint,
  flipElementHorizontally,
  flipElementVertically,
  alignElementsToGrid,
  distributeElementsHorizontally,
  distributeElementsVertically,
}
