/**
 * Area Calculation Strategy for Quadratic Bezier Curve Elements
 *
 * @remarks
 * This strategy implements the {@link AreaCalculatorStrategy} for calculating the
 * area of Quadratic Bezier curve elements ({@link CoreElementType.QUADRATIC}).
 *
 * For 'closed' quadratic Bezier curves, the area is approximated by:
 * 1. Sampling a number of points along the curve using the `sampleQuadraticBezier` utility.
 * 2. Treating these sampled points as vertices of a polygon.
 * 3. Calculating the area of this polygon using `calculatePolygonAreaUtil` from
 *    `../../../../lib/utils/geometry/polygonUtils`.
 *
 * If the quadratic Bezier curve is not 'closed', it is considered to have no
 * enclosed area, and this strategy will return `0`.
 *
 * @module core/compute/strategies/area/QuadraticAreaStrategy
 * @see {@link Path.Quadratic} for the quadratic Bezier curve element type definition.
 */
import type { AreaCalculatorStrategy } from '../../../../types/core/compute'
import type { PointData as IPoint } from '../../../../types/core/element/geometry/point' // Use PointData, alias as IPoint
import type { Element, Path } from '../../../../types/core/elementDefinitions'
import { sampleQuadraticBezier } from '../../../../lib/utils/geometry/bezierUtils'
import { calculateArea as calculatePolygonAreaUtil } from '../../../../lib/utils/geometry/polygonUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import { ElementType as CoreElementType } from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

/**
 * Implements the {@link AreaCalculatorStrategy} for {@link CoreElementType.QUADRATIC} elements.
 */
export class QuadraticAreaStrategy implements AreaCalculatorStrategy {
  /**
   * Calculates the area of a Quadratic Bezier curve element.
   *
   * @remarks
   * If the curve is `closed`, its area is approximated by sampling points along the curve
   * and then calculating the area of the polygon formed by these points.
   * If the curve is open, its area is considered `0`.
   *
   * @param element - The quadratic Bezier curve element, expected to conform to {@link Path.Quadratic} and have type {@link CoreElementType.QUADRATIC}.
   * @returns The calculated (approximated) area if the curve is closed, or `0` if it's open.
   * @throws {@link CoreError} if the provided element is not of type `QUADRATIC`.
   * @throws {@link CoreError} if the curve's control points (`start`, `control`, `end`) are invalid or not numbers.
   */
  public calculateArea(element: Element): number {
    if (element.type !== CoreElementType.QUADRATIC) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `QuadraticAreaStrategy can only calculate area for QUADRATIC elements, got ${element.type}`,
        undefined,
        { component: 'QuadraticAreaStrategy', operation: 'calculateArea', target: element.id },
      )
    }

    const quadElement = element as unknown as Path.Quadratic // Added unknown for type safety

    const p0 = quadElement.properties.start
    const p1 = quadElement.properties.control
    const p2 = quadElement.properties.end
    const closed = quadElement.properties.closed === true

    if (p0 == null || p1 == null || p2 == null
      || typeof p0.x !== 'number' || typeof p0.y !== 'number'
      || typeof p1.x !== 'number' || typeof p1.y !== 'number'
      || typeof p2.x !== 'number' || typeof p2.y !== 'number') {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid quadratic curve points for element ID ${element.id}`,
        undefined,
        { component: 'QuadraticAreaStrategy', operation: 'calculateArea', target: element.id, metadata: { p0, p1, p2 } },
      )
    }

    if (closed) {
      const numSegments = 20 // Number of segments to approximate the curve
      const sampledPoints: IPoint[] = sampleQuadraticBezier(p0, p1, p2, numSegments)

      // Use the utility function for polygon area calculation
      return calculatePolygonAreaUtil(sampledPoints)
    }

    return 0 // Open quadratic Bezier curves typically have no enclosed area.
  }

  /**
   * Returns the element type that this strategy is responsible for.
   * @returns The element type this strategy handles
   */
  public getElementType(): CoreElementType {
    return CoreElementType.QUADRATIC
  }
}
