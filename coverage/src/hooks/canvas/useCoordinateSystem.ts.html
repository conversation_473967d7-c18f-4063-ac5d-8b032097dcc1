
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/hooks/canvas/useCoordinateSystem.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">src/hooks/canvas</a> useCoordinateSystem.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/75</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/75</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * Canvas Coordinate System Hook
 *
 * This module provides a React hook for managing coordinate systems
 * on the canvas. It handles coordinate transformations between different
 * coordinate spaces (screen, SVG, world coordinates) and provides helper
 * functions for mouse event handling and shape positioning.
 *
 * @module hooks/canvas/useCoordinateSystem
 */
&nbsp;
import type React from 'react'
&nbsp;
import type { CanvasMouseEvent } from '@/types'
import type Point from '@/types/core/element/geometry/point'
import type { ShapeElement } from '@/types/core/elementDefinitions'
<span class="cstat-no" title="statement not covered" >import * as d3 from 'd3'</span>
<span class="cstat-no" title="statement not covered" >import { useCallback } from 'react'</span>
&nbsp;
interface UseCoordinateSystemProps {
  svgRef: React.RefObject&lt;SVGSVGElement | null&gt;
  // mainGroupRef: React.RefObject&lt;SVGGElement | null&gt;; // Not used in current simplified calculation
  pan: Point
  zoom: number
}
&nbsp;
<span class="cstat-no" title="statement not covered" >export function useCoordinateSystem({</span>
<span class="cstat-no" title="statement not covered" >  svgRef,</span>
<span class="cstat-no" title="statement not covered" >  pan,</span>
<span class="cstat-no" title="statement not covered" >  zoom,</span>
<span class="cstat-no" title="statement not covered" >}: UseCoordinateSystemProps) {</span>
<span class="cstat-no" title="statement not covered" >  const createMouseEventDetails = useCallback((</span>
<span class="cstat-no" title="statement not covered" >    originalEvent: React.MouseEvent&lt;SVGElement&gt; | React.DragEvent&lt;SVGElement&gt;,</span>
<span class="cstat-no" title="statement not covered" >    targetOverride?: EventTarget | null,</span>
<span class="cstat-no" title="statement not covered" >  ): CanvasMouseEvent =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const svgNode = svgRef.current</span>
<span class="cstat-no" title="statement not covered" >    let worldX = 0</span>
<span class="cstat-no" title="statement not covered" >    let worldY = 0</span>
<span class="cstat-no" title="statement not covered" >    let svgX = 0</span>
<span class="cstat-no" title="statement not covered" >    let svgY = 0</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const currentPan = pan</span>
<span class="cstat-no" title="statement not covered" >    const currentZoom = zoom</span>
<span class="cstat-no" title="statement not covered" >    const safeZoom = currentZoom !== 0 ? currentZoom : 1</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (svgNode) {</span>
<span class="cstat-no" title="statement not covered" >      const clientXImpl = (originalEvent as React.MouseEvent&lt;SVGElement&gt;).clientX</span>
<span class="cstat-no" title="statement not covered" >      const clientYImpl = (originalEvent as React.MouseEvent&lt;SVGElement&gt;).clientY</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const svgRect = svgNode.getBoundingClientRect()</span>
<span class="cstat-no" title="statement not covered" >      svgX = clientXImpl - (svgRect?.left || 0)</span>
<span class="cstat-no" title="statement not covered" >      svgY = clientYImpl - (svgRect?.top || 0)</span>
&nbsp;
      // 直接使用我们的坐标转换函数
<span class="cstat-no" title="statement not covered" >      worldX = (svgX - currentPan.x) / safeZoom</span>
<span class="cstat-no" title="statement not covered" >      worldY = (svgY - currentPan.y) / safeZoom</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      console.warn('[createMouseEventDetails] Direct calculation:', {</span>
<span class="cstat-no" title="statement not covered" >        clientX: clientXImpl,</span>
<span class="cstat-no" title="statement not covered" >        clientY: clientYImpl,</span>
<span class="cstat-no" title="statement not covered" >        svgRect,</span>
<span class="cstat-no" title="statement not covered" >        svgX,</span>
<span class="cstat-no" title="statement not covered" >        svgY,</span>
<span class="cstat-no" title="statement not covered" >        pan: currentPan,</span>
<span class="cstat-no" title="statement not covered" >        zoom: currentZoom,</span>
<span class="cstat-no" title="statement not covered" >        worldX,</span>
<span class="cstat-no" title="statement not covered" >        worldY,</span>
<span class="cstat-no" title="statement not covered" >      })</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      console.warn('[createMouseEventDetails] Input: clientX:', clientXImpl, 'clientY:', clientYImpl)</span>
<span class="cstat-no" title="statement not covered" >      console.warn('[createMouseEventDetails] SVG Rect:', svgRect)</span>
<span class="cstat-no" title="statement not covered" >      console.warn('[createMouseEventDetails] Calculated svgX:', svgX, 'svgY:', svgY)</span>
<span class="cstat-no" title="statement not covered" >      console.warn('[createMouseEventDetails] Current Pan:', currentPan, 'Current Zoom:', currentZoom)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (currentZoom === 0) {</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[createMouseEventDetails] Original calc warning: currentZoom was 0.')</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      if (Number.isNaN(worldX) || Number.isNaN(worldY)) {</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[createMouseEventDetails] NaN detected for world coordinates.')</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
<span class="cstat-no" title="statement not covered" >      console.warn('[createMouseEventDetails] svgNode is null. All coordinates will be (0,0).')</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    let d3Data: ShapeElement | null = null</span>
<span class="cstat-no" title="statement not covered" >    const targetElement = targetOverride || originalEvent.target</span>
<span class="cstat-no" title="statement not covered" >    if (targetElement instanceof SVGElement) {</span>
<span class="cstat-no" title="statement not covered" >      const rawD3Data = d3.select&lt;SVGElement, ShapeElement&gt;(targetElement).datum()</span>
<span class="cstat-no" title="statement not covered" >      if (rawD3Data != null &amp;&amp; typeof rawD3Data === 'object' &amp;&amp; 'id' in rawD3Data &amp;&amp; 'type' in rawD3Data) {</span>
<span class="cstat-no" title="statement not covered" >        d3Data = rawD3Data</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (targetElement.parentElement &amp;&amp; targetElement.parentElement instanceof SVGElement) {</span>
<span class="cstat-no" title="statement not covered" >        const parentD3Data = d3.select&lt;SVGElement, ShapeElement&gt;(targetElement.parentElement).datum()</span>
<span class="cstat-no" title="statement not covered" >        if (parentD3Data != null &amp;&amp; typeof parentD3Data === 'object' &amp;&amp; 'id' in parentD3Data &amp;&amp; 'type' in parentD3Data) {</span>
<span class="cstat-no" title="statement not covered" >          d3Data = parentD3Data</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return {</span>
<span class="cstat-no" title="statement not covered" >      originalEvent,</span>
<span class="cstat-no" title="statement not covered" >      worldPosition: { x: worldX, y: worldY },</span>
<span class="cstat-no" title="statement not covered" >      svgPosition: { x: svgX, y: svgY },</span>
<span class="cstat-no" title="statement not covered" >      d3Data,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }, [svgRef, pan, zoom])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return { createMouseEventDetails }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:58:01.486Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    