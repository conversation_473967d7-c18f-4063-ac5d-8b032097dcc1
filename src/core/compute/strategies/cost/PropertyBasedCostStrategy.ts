/**
 * Property-Based Cost Calculation Strategy
 *
 * 使用存储在element.properties中的计算结果来计算成本
 * 支持基于面积、周长/长度和单位的成本计算
 */

import type { CostCalculationOptions, CostCalculatorStrategy } from '../../../../types/core/compute'
import type { Element } from '../../../../types/core/elementDefinitions'

export class PropertyBasedCostStrategy implements CostCalculatorStrategy {
  /**
   * 基于元素属性计算成本
   *
   * @param element - 要计算成本的元素
   * @param unitCost - 单位成本
   * @param options - 成本计算选项，包含costBasis等参数
   * @returns 计算出的总成本
   */
  public calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
    console.warn(`[PropertyBasedCostStrategy] 开始计算成本`, {
      elementId: element.id,
      elementType: element.type,
      unitCost,
      options,
    })

    // 将 element 转换为具有 properties 的类型
    const elementWithProps = element as Element & {
      properties?: {
        computedArea?: number
        computedPerimeter?: number
        computedLength?: number
        costMultiplierOrCount?: number
        [key: string]: unknown
      }
    }

    // 验证unitCost
    if (typeof unitCost !== 'number' || Number.isNaN(unitCost) || unitCost < 0) {
      console.warn(`[PropertyBasedCostStrategy] 无效的单位成本: ${unitCost}，使用默认值1`)
      unitCost = 1
    }

    // 获取成本基准 - 使用 costType 而不是 costBasis
    const costBasis = options?.costType || 'unit'
    console.warn(`[PropertyBasedCostStrategy] 成本基准: ${costBasis}`)

    let baseValue = 1 // 默认基础值

    // 根据成本基准获取基础值
    switch (costBasis) {
      case 'area': {
        const computedArea = elementWithProps.properties?.computedArea
        if (typeof computedArea === 'number' && !Number.isNaN(computedArea) && computedArea > 0) {
          baseValue = computedArea
          console.warn(`[PropertyBasedCostStrategy] 使用面积作为基础值: ${baseValue} mm²`)
        }
        else {
          console.warn(`[PropertyBasedCostStrategy] 面积未计算或无效，使用默认值1`)
          baseValue = 1
        }
        break
      }
      case 'perimeter': {
        const computedPerimeter = elementWithProps.properties?.computedPerimeter
        const computedLength = elementWithProps.properties?.computedLength
        const perimeterValue = computedPerimeter || computedLength

        if (typeof perimeterValue === 'number' && !Number.isNaN(perimeterValue) && perimeterValue > 0) {
          baseValue = perimeterValue
          console.warn(`[PropertyBasedCostStrategy] 使用周长/长度作为基础值: ${baseValue} mm`)
        }
        else {
          console.warn(`[PropertyBasedCostStrategy] 周长/长度未计算或无效，使用默认值1`)
          baseValue = 1
        }
        break
      }
      case 'unit':
      default: {
        baseValue = 1
        console.warn(`[PropertyBasedCostStrategy] 使用单位作为基础值: ${baseValue}`)
        break
      }
    }

    // 计算基础成本
    let totalCost = baseValue * unitCost
    console.warn(`[PropertyBasedCostStrategy] 基础成本: ${baseValue} × ${unitCost} = ${totalCost}`)

    // 应用乘数/数量因子
    const multiplier = options?.multiplier
      || options?.quantity
      || elementWithProps.properties?.costMultiplierOrCount
      || 1

    if (typeof multiplier === 'number' && !Number.isNaN(multiplier) && multiplier > 0) {
      totalCost *= multiplier
      console.warn(`[PropertyBasedCostStrategy] 应用乘数: ${totalCost} × ${multiplier} = ${totalCost}`)
    }

    // 应用额外成本
    if (options?.additionalCost != null && typeof options.additionalCost === 'number' && !Number.isNaN(options.additionalCost)) {
      totalCost += options.additionalCost
      console.warn(`[PropertyBasedCostStrategy] 添加额外成本: ${totalCost} + ${options.additionalCost} = ${totalCost}`)
    }

    // 应用折扣
    if (options?.discountRate != null && typeof options.discountRate === 'number' && !Number.isNaN(options.discountRate) && options.discountRate >= 0 && options.discountRate <= 100) {
      const discountMultiplier = 1 - (options.discountRate / 100)
      totalCost *= discountMultiplier
      console.warn(`[PropertyBasedCostStrategy] 应用折扣: ${totalCost} × ${discountMultiplier} = ${totalCost}`)
    }

    // 应用税率
    if (options?.taxRate != null && typeof options.taxRate === 'number' && !Number.isNaN(options.taxRate) && options.taxRate >= 0) {
      const taxMultiplier = 1 + (options.taxRate / 100)
      totalCost *= taxMultiplier
      console.warn(`[PropertyBasedCostStrategy] 应用税率: ${totalCost} × ${taxMultiplier} = ${totalCost}`)
    }

    console.warn(`[PropertyBasedCostStrategy] 最终成本: ${totalCost}`)
    return totalCost
  }

  /**
   * 返回此策略适用的元素类型
   * 这个策略适用于所有元素类型
   */
  public getElementType(): string {
    return 'all_property_based'
  }
}
