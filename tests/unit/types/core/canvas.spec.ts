import type { LayerConfig, LayerVisibility } from '@/types/core/canvasTypes'

import { describe, expect, it } from 'vitest'
import {
  CanvasLayer,
  DefaultLayerVisibility,
  getLayerColor,
  getLayerDisplayName,
  getLayersInGroup,
  LayerColors,

  LayerDisplayNames,
  LayerGroup,
  LayerGroupMapping,
  LayerRenderOrder,

} from '@/types/core/canvasTypes'

describe('canvas Layer Types', () => {
  it('canvasLayer enum should contain correct values', () => {
    expect(CanvasLayer.FLOOR).toBe('FLOOR')
    expect(CanvasLayer.WALLS).toBe('WALLS')
    expect(CanvasLayer.CEILING).toBe('CEILING')
    expect(CanvasLayer.DOORS).toBe('DOORS')
    expect(CanvasLayer.WINDOWS).toBe('WINDOWS')
    expect(CanvasLayer.FURNITURE).toBe('FURNITURE')
    expect(CanvasLayer.FIXTURES).toBe('FIXTURES')
    expect(CanvasLayer.ELECTRICAL).toBe('ELECTRICAL')
    expect(CanvasLayer.PLUMBING).toBe('PLUMBING')
    expect(CanvasLayer.HVAC).toBe('HVAC')
    expect(CanvasLayer.DECOR).toBe('DECOR')
    expect(CanvasLayer.DIMENSIONS).toBe('DIMENSIONS')
    expect(CanvasLayer.TEXT).toBe('TEXT')
    expect(CanvasLayer.AREAS).toBe('AREAS')
    expect(CanvasLayer.LANDSCAPE).toBe('LANDSCAPE')
  })

  it('layerGroup enum should contain correct values', () => {
    expect(LayerGroup.STRUCTURE).toBe('STRUCTURE')
    expect(LayerGroup.OPENINGS).toBe('OPENINGS')
    expect(LayerGroup.FURNITURE_FIXTURES).toBe('FURNITURE_FIXTURES')
    expect(LayerGroup.MEP).toBe('MEP')
    expect(LayerGroup.ANNOTATIONS).toBe('ANNOTATIONS')
    expect(LayerGroup.SPECIAL).toBe('SPECIAL')
  })

  it('layerGroupMapping should map each layer to a group', () => {
    // Check structure group
    expect(LayerGroupMapping[CanvasLayer.FLOOR]).toBe(LayerGroup.STRUCTURE)
    expect(LayerGroupMapping[CanvasLayer.WALLS]).toBe(LayerGroup.STRUCTURE)
    expect(LayerGroupMapping[CanvasLayer.CEILING]).toBe(LayerGroup.STRUCTURE)

    // Check openings group
    expect(LayerGroupMapping[CanvasLayer.DOORS]).toBe(LayerGroup.OPENINGS)
    expect(LayerGroupMapping[CanvasLayer.WINDOWS]).toBe(LayerGroup.OPENINGS)

    // Check furniture and fixtures group
    expect(LayerGroupMapping[CanvasLayer.FURNITURE]).toBe(LayerGroup.FURNITURE_FIXTURES)
    expect(LayerGroupMapping[CanvasLayer.FIXTURES]).toBe(LayerGroup.FURNITURE_FIXTURES)

    // Check MEP group
    expect(LayerGroupMapping[CanvasLayer.ELECTRICAL]).toBe(LayerGroup.MEP)
    expect(LayerGroupMapping[CanvasLayer.PLUMBING]).toBe(LayerGroup.MEP)
    expect(LayerGroupMapping[CanvasLayer.HVAC]).toBe(LayerGroup.MEP)

    // Check annotations group
    expect(LayerGroupMapping[CanvasLayer.DECOR]).toBe(LayerGroup.ANNOTATIONS)
    expect(LayerGroupMapping[CanvasLayer.DIMENSIONS]).toBe(LayerGroup.ANNOTATIONS)
    expect(LayerGroupMapping[CanvasLayer.TEXT]).toBe(LayerGroup.ANNOTATIONS)

    // Check special group
    expect(LayerGroupMapping[CanvasLayer.AREAS]).toBe(LayerGroup.SPECIAL)
    expect(LayerGroupMapping[CanvasLayer.LANDSCAPE]).toBe(LayerGroup.SPECIAL)
  })

  it('layerDisplayNames should provide human-readable names for each layer', () => {
    expect(LayerDisplayNames[CanvasLayer.FLOOR]).toBe('Floor')
    expect(LayerDisplayNames[CanvasLayer.WALLS]).toBe('Walls')
    expect(LayerDisplayNames[CanvasLayer.FURNITURE]).toBe('Furniture')
    expect(LayerDisplayNames[CanvasLayer.TEXT]).toBe('Text Labels')
    // Check a few more to ensure consistency
    expect(LayerDisplayNames[CanvasLayer.ELECTRICAL]).toBe('Electrical')
    expect(LayerDisplayNames[CanvasLayer.LANDSCAPE]).toBe('Landscape')
  })

  it('layerColors should provide color codes for each layer', () => {
    expect(LayerColors[CanvasLayer.FLOOR]).toBe('#8B4513') // Brown
    expect(LayerColors[CanvasLayer.WALLS]).toBe('#808080') // Gray
    expect(LayerColors[CanvasLayer.WINDOWS]).toBe('#87CEEB') // Sky Blue
    expect(LayerColors[CanvasLayer.FURNITURE]).toBe('#4682B4') // Steel Blue
    // Check a few more to ensure consistency
    expect(LayerColors[CanvasLayer.DIMENSIONS]).toBe('#000000') // Black
    expect(LayerColors[CanvasLayer.LANDSCAPE]).toBe('#228B22') // Forest Green
  })

  it('defaultLayerVisibility should define initial visibility for each layer', () => {
    // Visible by default
    expect(DefaultLayerVisibility[CanvasLayer.FLOOR]).toBe(true)
    expect(DefaultLayerVisibility[CanvasLayer.WALLS]).toBe(true)
    expect(DefaultLayerVisibility[CanvasLayer.FURNITURE]).toBe(true)
    expect(DefaultLayerVisibility[CanvasLayer.TEXT]).toBe(true)

    // Hidden by default
    expect(DefaultLayerVisibility[CanvasLayer.ELECTRICAL]).toBe(false)
    expect(DefaultLayerVisibility[CanvasLayer.PLUMBING]).toBe(false)
    expect(DefaultLayerVisibility[CanvasLayer.HVAC]).toBe(false)
    expect(DefaultLayerVisibility[CanvasLayer.AREAS]).toBe(false)
    expect(DefaultLayerVisibility[CanvasLayer.LANDSCAPE]).toBe(false)
  })

  it('layerRenderOrder should define the rendering order from bottom to top', () => {
    // Check first and last layers
    expect(LayerRenderOrder[0]).toBe(CanvasLayer.FLOOR) // Bottom layer
    expect(LayerRenderOrder[LayerRenderOrder.length - 1]).toBe(CanvasLayer.TEXT) // Top layer

    // Check a few specific ordering relationships
    expect(LayerRenderOrder.indexOf(CanvasLayer.FLOOR)).toBeLessThan(
      LayerRenderOrder.indexOf(CanvasLayer.WALLS),
    )
    expect(LayerRenderOrder.indexOf(CanvasLayer.WALLS)).toBeLessThan(
      LayerRenderOrder.indexOf(CanvasLayer.FURNITURE),
    )
    expect(LayerRenderOrder.indexOf(CanvasLayer.FURNITURE)).toBeLessThan(
      LayerRenderOrder.indexOf(CanvasLayer.DIMENSIONS),
    )
  })

  it('getLayersInGroup should return all layers in a specific group', () => {
    const structureLayers = getLayersInGroup(LayerGroup.STRUCTURE)
    expect(structureLayers).toContain(CanvasLayer.FLOOR)
    expect(structureLayers).toContain(CanvasLayer.WALLS)
    expect(structureLayers).toContain(CanvasLayer.CEILING)
    expect(structureLayers.length).toBe(3)

    const mepLayers = getLayersInGroup(LayerGroup.MEP)
    expect(mepLayers).toContain(CanvasLayer.ELECTRICAL)
    expect(mepLayers).toContain(CanvasLayer.PLUMBING)
    expect(mepLayers).toContain(CanvasLayer.HVAC)
    expect(mepLayers.length).toBe(3)
  })

  it('getLayerDisplayName should return the display name for a layer', () => {
    expect(getLayerDisplayName(CanvasLayer.FLOOR)).toBe('Floor')
    expect(getLayerDisplayName(CanvasLayer.FURNITURE)).toBe('Furniture')
    expect(getLayerDisplayName(CanvasLayer.TEXT)).toBe('Text Labels')
  })

  it('getLayerColor should return the color code for a layer', () => {
    expect(getLayerColor(CanvasLayer.FLOOR)).toBe('#8B4513')
    expect(getLayerColor(CanvasLayer.WALLS)).toBe('#808080')
    expect(getLayerColor(CanvasLayer.FURNITURE)).toBe('#4682B4')
  })

  it('should allow declaration of LayerVisibility type', () => {
    const visibility: LayerVisibility = {
      [CanvasLayer.FLOOR]: true,
      [CanvasLayer.WALLS]: true,
      [CanvasLayer.CEILING]: false,
      [CanvasLayer.DOORS]: true,
      [CanvasLayer.WINDOWS]: true,
      [CanvasLayer.FURNITURE]: true,
      [CanvasLayer.FIXTURES]: false,
      [CanvasLayer.ELECTRICAL]: false,
      [CanvasLayer.PLUMBING]: false,
      [CanvasLayer.HVAC]: false,
      [CanvasLayer.DECOR]: true,
      [CanvasLayer.DIMENSIONS]: true,
      [CanvasLayer.TEXT]: true,
      [CanvasLayer.AREAS]: false,
      [CanvasLayer.LANDSCAPE]: false,
    }

    expect(visibility[CanvasLayer.FLOOR]).toBe(true)
    expect(visibility[CanvasLayer.CEILING]).toBe(false)
  })

  it('should allow declaration of LayerConfig type', () => {
    const config: LayerConfig = {
      id: CanvasLayer.FLOOR,
      name: 'Floor Layer',
      visible: true,
      color: '#8B4513',
      group: LayerGroup.STRUCTURE,
      renderOrder: 0,
    }

    expect(config.id).toBe(CanvasLayer.FLOOR)
    expect(config.name).toBe('Floor Layer')
    expect(config.visible).toBe(true)
    expect(config.color).toBe('#8B4513')
    expect(config.group).toBe(LayerGroup.STRUCTURE)
    expect(config.renderOrder).toBe(0)
  })
})
