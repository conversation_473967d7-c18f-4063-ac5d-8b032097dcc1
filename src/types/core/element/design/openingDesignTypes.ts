/**
 * Defines types for opening elements (doors, windows, etc.) in architectural design.
 *
 * @remarks
 * This module provides type definitions for standard opening types such as doors and windows,
 * which are typically embedded within walls.
 *
 * @module types/core/element/design/openingDesignTypes
 * @see {@link WallProperties} for properties of walls that might contain these openings.
 */

import type Point from '@/types/core/element/geometry/point' // Point might be needed if openings have their own absolute position
// Openings might extend or relate to ShapeElement

/**
 * Enumerates standard types of architectural openings.
 *
 * @remarks
 * This enumeration covers common types of doors, windows, and other architectural openings
 * found in building designs.
 *
 * @example
 * ```typescript
 * const myDoorType: OpeningType = OpeningType.HINGED_DOOR;
 * const myWindowType: OpeningType = OpeningType.CASEMENT_WINDOW;
 * ```
 */
export enum OpeningType {
  // Door types
  /** A door that swings on hinges, typically to one side. */
  HINGED_DOOR = 'hinged-door',
  /** A door that opens by sliding along a track. */
  SLIDING_DOOR = 'sliding-door',
  /** A door that slides into a compartment in the adjacent wall. */
  POCKET_DOOR = 'pocket-door',
  /** A door that folds in sections. */
  BIFOLD_DOOR = 'bifold-door',
  /** A door with glass panes throughout its length, typically in pairs. */
  FRENCH_DOOR = 'french-door',
  /** A door that slides along a track mounted above the opening, often with a rustic style. */
  BARN_DOOR = 'barn-door',
  /** A door assembly that rotates on a central axis. */
  REVOLVING_DOOR = 'revolving-door',

  // Window types
  /** A window that is attached to its frame by one or more hinges at the side. */
  CASEMENT_WINDOW = 'casement-window',
  /** A window that is hinged at the top and opens outward. */
  AWNING_WINDOW = 'awning-window',
  /** A window that is hinged at the bottom and opens inward or outward. */
  HOPPER_WINDOW = 'hopper-window',
  /** A window that opens by sliding horizontally. */
  SLIDING_WINDOW = 'sliding-window',
  /** A window with two sashes that slide vertically up and down in the frame. */
  DOUBLE_HUNG_WINDOW = 'double-hung-window',
  /** A window that does not open. */
  FIXED_WINDOW = 'fixed-window',
  /** A window space projecting outward from the main walls of a building and forming a bay in a room. */
  BAY_WINDOW = 'bay-window',
  /** A curved bay window. */
  BOW_WINDOW = 'bow-window',

  // Other openings
  /** An opening with a curved top; an arch. */
  ARCHWAY = 'archway',
  /** An opening in a wall between two rooms, typically a kitchen and dining area, for passing food or other items. */
  PASS_THROUGH = 'pass-through',
  /** A window set in a roof or ceiling. */
  SKYLIGHT = 'skylight',
  /** A generic or unspecified opening type. */
  GENERIC_OPENING = 'generic-opening', // This is the last member, no comma
}
/**
 * Defines the swing direction for hinged doors.
 */
export enum DoorSwingDirection {
  LEFT_INSWING = 'left-inswing',
  RIGHT_INSWING = 'right-inswing',
  LEFT_OUTSWING = 'left-outswing',
  RIGHT_OUTSWING = 'right-outswing',
  DOUBLE_INSWING = 'double-inswing',
  DOUBLE_OUTSWING = 'double-outswing',
  NONE = 'none', // For non-swinging doors like sliding
}

/**
 * Defines the operation type for windows.
 */
export enum WindowOperationType {
  FIXED = 'fixed',
  CASEMENT_LEFT = 'casement-left',
  CASEMENT_RIGHT = 'casement-right',
  AWNING = 'awning', // Hinged at top, opens outward
  HOPPER = 'hopper', // Hinged at bottom, opens inward
  SLIDING_HORIZONTAL = 'sliding-horizontal',
  SLIDING_VERTICAL_SINGLE_HUNG = 'sliding-vertical-single-hung',
  SLIDING_VERTICAL_DOUBLE_HUNG = 'sliding-vertical-double-hung',
  PIVOT = 'pivot',
  TILT_AND_TURN = 'tilt-and-turn',
}

/**
 * Defines the geometric and functional properties of an opening element within a wall.
 *
 * @remarks
 * This interface specifies the core properties needed to define, position, and size
 * architectural openings like doors and windows. It is assumed that all dimensional
 * measurements are in millimeters unless otherwise specified.
 *
 * @see {@link ShapeElement} for base element properties that an opening might extend or incorporate.
 * @see {@link WallProperties} for properties of the wall containing the opening.
 * @see {@link OpeningType} for the classification of the opening.
 */
export interface OpeningProperties {
  /**
   * The general type identifier for this element, e.g., 'opening', 'door', 'window'.
   * Required for compatibility with base {@link Element} or {@link ShapeElement} if extended.
   */
  type: string

  /** The specific classification of the opening, as defined by {@link OpeningType}. */
  openingType: OpeningType

  /** The width of the opening, typically measured in millimeters. */
  width: number

  /** The height of the opening, typically measured in millimeters. */
  height: number

  /**
   * The height of the bottom of the opening from the floor level,
   * typically measured in millimeters.
   */
  heightFromFloor: number

  /** The unique identifier of the wall element that contains this opening. */
  wallId: string

  /**
   * The position of the opening along the length of the wall.
   * This is typically represented as a normalized value between 0 and 1,
   * where 0 is one end of the wall and 1 is the other.
   */
  wallPosition: number

  /**
   * Optional absolute reference point for the opening in the design space.
   * This could be used for precise placement or if the opening is not strictly tied to a wall's relative coordinates.
   */
  referencePoint?: Point
}
