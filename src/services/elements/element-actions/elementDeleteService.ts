import type {
  ElementDeleteRequest,
  ElementDeleteResult,
  ElementDeleteService as IElementDeleteService,
} from '../../../types/services/shapes'
/**
 * Shape Delete Service Implementation
 *
 * This service is responsible for handling the deletion of shapes from the repository
 * and publishing relevant events to the application's event bus.
 *
 * @module services/elements/element-actions/elementDeleteService
 */
import type { ServiceResult } from '@/types/services/core/serviceResult'
import type { AppEventMap, EventBus, ShapeDeleteEvent } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import { v4 as uuidv4 } from 'uuid'
import { safeExecute, safeExecuteAsync } from '@/lib/utils/errorUtils'
import { getService, ServiceId } from '@/services/core/registry'
import { AppEventType } from '@/types/services/events'
import { ErrorType } from '../../../types/services/errors'

/**
 * Implements the {@link IElementDeleteService} interface to manage shape deletion operations.
 * It interacts with the {@link ShapeRepository} to remove shapes and uses the
 * {@link EventBus} to notify other parts of the application about deletion events.
 */
export class ElementDeleteService implements IElementDeleteService {
  readonly serviceId: string = ServiceId.ElementDeleteService as string

  /**
   * Creates an instance of ElementDeleteService.
   * @param {EventBus<AppEventMap>} eventBus - The application event bus.
   * @param {LoggerService} logger - The logger service.
   */
  constructor(
    private eventBus: EventBus<AppEventMap>,
    private logger: LoggerService,
  ) {
    this.logger.info('[ElementDeleteService] Initialized.')
  }

  /**
   * Factory method to create a {@link ElementDeleteService} instance.
   * Dependencies like EventBus and LoggerService are resolved from the service registry.
   * @param {LoggerService} [logger] - Optional logger service. If not provided, it's retrieved from the registry.
   * @returns {ElementDeleteService} A new instance of the ElementDeleteService.
   * @throws {Error} If dependencies cannot be resolved.
   */
  public static create(
    logger?: LoggerService,
  ): ElementDeleteService {
    try {
      const eventBus = getService<EventBus<AppEventMap>>(ServiceId.EventBus)
      const loggerService = logger || getService<LoggerService>(ServiceId.Logger)

      return new ElementDeleteService(eventBus, loggerService)
    }
    catch (error) {
      // Consider using a more specific error type or logging via a fallback logger if loggerService failed to init
      throw new Error(`Failed to create ElementDeleteService: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * Publishes a shape delete request event.
   * @param request Shape delete parameters
   * @returns Promise that resolves when the event is published
   */
  async deleteShape(request: ElementDeleteRequest): Promise<ElementDeleteResult> {
    try {
      this.logger.debug(`[ElementDeleteService] Publishing ShapeDeleteRequest for ID ${request.id}`, { request })
      this.eventBus.publish({
        type: AppEventType.ShapeDeleteRequest,
        timestamp: Date.now(),
        payload: {
          shapeId: request.id,
          source: ElementDeleteService.name,
        },
      })
      return {
        success: true,
        data: undefined,
        timestamp: Date.now(),
      }
    }
    catch (error) {
      this.emitError('SHAPE_DELETE_ERROR', error instanceof Error ? error.message : 'Unknown error', { request })
      return {
        success: false,
        error: {
          code: 'SHAPE_DELETE_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          details: error,
        },
        timestamp: Date.now(),
      }
    }
  }

  /**
   * Publishes a shape delete request event for multiple shapes.
   * @param shapeIds Array of shape IDs to delete
   * @returns Promise that resolves when the event is published
   */
  async deleteShapes(shapeIds: string[]): Promise<ServiceResult<string[]>> {
    try {
      this.logger.debug(`[ElementDeleteService] Publishing ShapeDeleteRequest for IDs`, { shapeIds })
      this.eventBus.publish({
        type: AppEventType.ShapeDeleteRequest,
        timestamp: Date.now(),
        payload: {
          shapeId: shapeIds,
          source: ElementDeleteService.name,
        },
      })
      return {
        success: true,
        data: [],
        timestamp: Date.now(),
      }
    }
    catch (error) {
      this.emitError('SHAPE_DELETE_ERROR', error instanceof Error ? error.message : 'Unknown error', { shapeIds })
      return {
        success: false,
        error: {
          code: 'SHAPE_DELETE_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          details: error,
        },
        timestamp: Date.now(),
      }
    }
  }

  /**
   * Emits an error event for shape delete failures.
   */
  private emitError(errorType: string, message: string, context?: Record<string, unknown>): void {
    const errorId = uuidv4()
    const fullMessage = `[${errorType}] ${message}`
    this.logger.error(fullMessage, { errorId, ...(context || {}) })
    this.eventBus.publish({
      type: AppEventType.EventError,
      timestamp: Date.now(),
      payload: {
        error: {
          code: errorType,
          message: fullMessage,
          details: context,
          errorId,
        },
        context,
      },
    })
  }

  /**
   * Handles an incoming {@link AppEventType.ShapeDeleteRequest} event to delete shapes.
   * @param {ShapeDeleteEvent} event - The shape delete event containing the IDs of shapes to delete.
   * @returns {Promise<void>} A promise that resolves when the deletion request has been processed.
   */
  public async handleRequest(event: ShapeDeleteEvent): Promise<void> {
    await safeExecuteAsync(async () => {
      this.logger.info('ElementDeleteService handling SHAPE_DELETE_REQUEST:', event?.payload)
      const errorContext = {
        component: 'ElementDeleteService',
        operation: 'handleRequest',
        metadata: {
          eventType: AppEventType.ShapeDeleteRequest, // Use PascalCase
          payload: event?.payload,
        },
      }

      if (event?.payload?.shapeId === undefined || event?.payload?.shapeId === null || event?.payload?.shapeId === '') {
        this.logger.error('Invalid or missing shapeId payload for SHAPE_DELETE_REQUEST', event?.payload)
        this.publishError(
          ErrorType.InvalidPayload, // Use PascalCase
          'Invalid payload for shape delete: shapeId (string or array) is required.',
          errorContext,
        )
        return
      }

      const shapeIdOrIds = event.payload.shapeId
      const idsToDelete: string[] = Array.isArray(shapeIdOrIds) ? shapeIdOrIds : [shapeIdOrIds]

      if (idsToDelete.length === 0) {
        this.logger.error('Empty idsToDelete array after processing shapeId payload', event?.payload)
        this.publishError(
          ErrorType.InvalidPayload, // Use PascalCase
          'Invalid payload for shape delete: resulting ID list is empty.',
          errorContext,
        )
        return
      }

      // 发布StateUpdated事件，确保当前状态被记录到历史中，以便后续可以撤销
      this.logger.debug('[ElementDeleteService] Publishing StateUpdated event with preDelete action')
      this.eventBus.publish({
        type: AppEventType.StateUpdated,
        timestamp: Date.now(),
        payload: {
          action: 'preDelete',
          elementIds: idsToDelete,
        },
      })

      // 我们不直接调用deleteShapes方法，也不尝试获取Repository（因为它没有通过ServiceId注册）
      // 而是发布一个完全不同的事件，这个事件已正确地被CoreCoordinator处理
      this.logger.debug(`[ElementDeleteService] Processing deletion of ${idsToDelete.length} shapes directly`)

      // 轻微等待，确保StateUpdated事件已被处理
      await new Promise(resolve => setTimeout(resolve, 50))

      // 直接发布完成事件，让CoreCoordinator处理后续逻辑
      this.logger.info('ElementDeleteService completed processing deletion request:', idsToDelete)
      this.logger.debug('[ElementDeleteService] Publishing ShapeDeleteComplete event')
      this.eventBus.publish({
        type: AppEventType.ShapeDeleteComplete,
        timestamp: Date.now(),
        payload: {
          shapeId: shapeIdOrIds,
          source: ElementDeleteService.name,
        },
      })
    }, {
      component: 'ElementDeleteService',
      operation: 'handleRequest',
      metadata: { event },
    })
  }

  /**
   * Publishes an error event to the event bus.
   * @private
   * @param {ErrorType} errorType - The type of error that occurred.
   * @param {string} message - A descriptive error message.
   * @param {any} context - Additional context about the error.
   */
  private publishError(errorType: ErrorType, message: string, context: unknown): void {
    safeExecute(() => {
      this.eventBus.publish({
        type: AppEventType.ErrorOccurred, // Use PascalCase
        payload: {
          code: errorType.toString(), // Ensure errorType is stringified if it's an enum
          message,
          details: context,
        },
      })
    }, {
      component: 'ElementDeleteService',
      operation: 'publishError',
      metadata: { errorType, message, context },
    })
  }
}
