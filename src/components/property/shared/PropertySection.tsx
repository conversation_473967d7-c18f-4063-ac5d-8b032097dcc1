/**
 * Property Section Component
 *
 * A reusable section component for organizing property controls with consistent styling.
 * Provides optional collapsible functionality and standardized spacing.
 *
 * Features:
 * - Consistent section styling and spacing
 * - Optional collapsible behavior with accordion
 * - Standardized title typography
 * - Flexible content layout
 * - Responsive design
 *
 * @example
 * ```tsx
 * <PropertySection title="Position & Transform">
 *   <PropertyField label="X Position" value={x} onChange={setX} />
 *   <PropertyField label="Y Position" value={y} onChange={setY} />
 * </PropertySection>
 *
 * <PropertySection title="Advanced Options" collapsible defaultOpen={false}>
 *   <PropertyField label="Corner Radius" value={radius} onChange={setRadius} />
 * </PropertySection>
 * ```
 */

import React from 'react'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { cn } from '@/lib/utils'

/**
 * Props for the PropertySection component
 */
interface PropertySectionProps {
  /** Section title displayed at the top */
  title: string
  /** Child components to render within the section */
  children: React.ReactNode
  /** Whether the section should be collapsible */
  collapsible?: boolean
  /** Default open state for collapsible sections */
  defaultOpen?: boolean
  /** Additional CSS classes */
  className?: string
  /** Optional section description */
  description?: string
}

/**
 * PropertySection component for organizing property controls with consistent styling.
 *
 * This component provides a standardized way to group related property controls
 * with consistent spacing, typography, and optional collapsible behavior.
 *
 * @param props - The component props
 * @param props.title - The section title
 * @param props.children - Child elements to render in the section
 * @param props.collapsible - Whether the section can be collapsed
 * @param props.defaultOpen - Whether the section is open by default
 * @param props.className - Additional CSS classes
 * @param props.description - Optional description text
 * @returns The rendered property section
 */
export function PropertySection({
  title,
  children,
  collapsible = false,
  defaultOpen = true,
  className,
  description,
}: PropertySectionProps) {
  if (collapsible) {
    return (
      <Accordion type="single" collapsible defaultValue={defaultOpen ? 'section' : undefined} className={cn('w-full', className)}>
        <AccordionItem value="section" className="border-none">
          <AccordionTrigger className="text-sm font-semibold text-foreground hover:no-underline py-2 px-0">
            <div className="flex flex-col items-start text-left">
              <span>{title}</span>
              {description !== undefined && description !== null && description.trim() !== '' && (
                <span className="text-xs font-normal text-muted-foreground mt-1">
                  {description}
                </span>
              )}
            </div>
          </AccordionTrigger>
          <AccordionContent className="pb-0 pt-2">
            <div className="space-y-3">
              {children}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    )
  }

  return (
    <div className={cn('space-y-3', className)}>
      <div className="space-y-1">
        <h3 className="text-sm font-semibold text-foreground">
          {title}
        </h3>
        {description !== undefined && description !== null && description.trim() !== '' && (
          <p className="text-xs text-muted-foreground">
            {description}
          </p>
        )}
      </div>
      <div className="space-y-3">
        {children}
      </div>
    </div>
  )
}
