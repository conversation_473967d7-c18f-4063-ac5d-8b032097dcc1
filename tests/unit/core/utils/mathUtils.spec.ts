/**
 * @file mathUtils.spec.ts
 * @description Unit tests for math utility functions
 */

import { describe, expect, it } from 'vitest'
import {
  angleBetweenPoints,
  clamp,
  degreesToRadians,
  distance,
  lerp,
  map,
  radiansToDegrees,
  randomFloat,
  randomInt,
  roundToDecimalPlaces,
} from '@/core/utils/mathUtils'

describe('mathUtils', () => {
  describe('clamp', () => {
    it('should clamp a value between min and max', () => {
      expect(clamp(5, 0, 10)).toBe(5)
      expect(clamp(-5, 0, 10)).toBe(0)
      expect(clamp(15, 0, 10)).toBe(10)
    })

    it('should handle equal min and max', () => {
      expect(clamp(5, 10, 10)).toBe(10)
    })

    it('should handle min greater than max', () => {
      expect(clamp(5, 10, 0)).toBe(5)
    })
  })

  describe('lerp', () => {
    it('should linearly interpolate between two values', () => {
      expect(lerp(0, 10, 0)).toBe(0)
      expect(lerp(0, 10, 0.5)).toBe(5)
      expect(lerp(0, 10, 1)).toBe(10)
    })

    it('should handle values outside the range [0, 1]', () => {
      expect(lerp(0, 10, -0.5)).toBe(-5)
      expect(lerp(0, 10, 1.5)).toBe(15)
    })

    it('should handle negative values', () => {
      expect(lerp(-10, 10, 0.5)).toBe(0)
    })
  })

  describe('map', () => {
    it('should map a value from one range to another', () => {
      expect(map(5, 0, 10, 0, 100)).toBe(50)
      expect(map(0, 0, 10, 0, 100)).toBe(0)
      expect(map(10, 0, 10, 0, 100)).toBe(100)
    })

    it('should handle values outside the input range', () => {
      expect(map(-5, 0, 10, 0, 100)).toBe(-50)
      expect(map(15, 0, 10, 0, 100)).toBe(150)
    })

    it('should handle negative ranges', () => {
      expect(map(0, -10, 10, -100, 100)).toBe(0)
      expect(map(-5, -10, 10, -100, 100)).toBe(-50)
    })

    it('should handle inverted output range', () => {
      expect(map(5, 0, 10, 100, 0)).toBe(50)
    })
  })

  describe('radiansToDegrees', () => {
    it('should convert radians to degrees', () => {
      expect(radiansToDegrees(0)).toBe(0)
      expect(radiansToDegrees(Math.PI)).toBe(180)
      expect(radiansToDegrees(Math.PI / 2)).toBe(90)
      expect(radiansToDegrees(Math.PI / 4)).toBe(45)
    })

    it('should handle negative values', () => {
      expect(radiansToDegrees(-Math.PI)).toBe(-180)
    })

    it('should handle values outside the range [-2π, 2π]', () => {
      expect(radiansToDegrees(3 * Math.PI)).toBe(540)
    })
  })

  describe('degreesToRadians', () => {
    it('should convert degrees to radians', () => {
      expect(degreesToRadians(0)).toBe(0)
      expect(degreesToRadians(180)).toBeCloseTo(Math.PI)
      expect(degreesToRadians(90)).toBeCloseTo(Math.PI / 2)
      expect(degreesToRadians(45)).toBeCloseTo(Math.PI / 4)
    })

    it('should handle negative values', () => {
      expect(degreesToRadians(-180)).toBeCloseTo(-Math.PI)
    })

    it('should handle values outside the range [-360, 360]', () => {
      expect(degreesToRadians(540)).toBeCloseTo(3 * Math.PI)
    })
  })

  describe('roundToDecimalPlaces', () => {
    it('should round a number to the specified number of decimal places', () => {
      expect(roundToDecimalPlaces(3.14159, 2)).toBe(3.14)
      expect(roundToDecimalPlaces(3.14159, 4)).toBe(3.1416)
      expect(roundToDecimalPlaces(3.14159, 0)).toBe(3)
    })

    it('should handle negative values', () => {
      expect(roundToDecimalPlaces(-3.14159, 2)).toBe(-3.14)
    })

    it('should handle negative decimal places', () => {
      expect(roundToDecimalPlaces(1234.5678, -2)).toBe(1200)
    })

    it('should handle rounding edge cases', () => {
      expect(roundToDecimalPlaces(0.5, 0)).toBe(1)
      expect(roundToDecimalPlaces(1.5, 0)).toBe(2)
      expect(roundToDecimalPlaces(2.5, 0)).toBe(3)
    })
  })

  describe('distance', () => {
    it('should calculate the distance between two points', () => {
      expect(distance(0, 0, 3, 4)).toBe(5)
      expect(distance(1, 1, 4, 5)).toBe(5)
    })

    it('should handle negative coordinates', () => {
      expect(distance(0, 0, -3, -4)).toBe(5)
    })

    it('should handle zero distance', () => {
      expect(distance(5, 5, 5, 5)).toBe(0)
    })
  })

  describe('angleBetweenPoints', () => {
    it('should calculate the angle between two points in radians', () => {
      expect(angleBetweenPoints(0, 0, 1, 0)).toBeCloseTo(0)
      expect(angleBetweenPoints(0, 0, 0, 1)).toBeCloseTo(Math.PI / 2)
      expect(angleBetweenPoints(0, 0, -1, 0)).toBeCloseTo(Math.PI)
      expect(angleBetweenPoints(0, 0, 0, -1)).toBeCloseTo(-Math.PI / 2)
    })

    it('should handle diagonal angles', () => {
      expect(angleBetweenPoints(0, 0, 1, 1)).toBeCloseTo(Math.PI / 4)
      expect(angleBetweenPoints(0, 0, -1, 1)).toBeCloseTo(3 * Math.PI / 4)
      expect(angleBetweenPoints(0, 0, -1, -1)).toBeCloseTo(-3 * Math.PI / 4)
      expect(angleBetweenPoints(0, 0, 1, -1)).toBeCloseTo(-Math.PI / 4)
    })

    it('should handle the same points', () => {
      expect(angleBetweenPoints(5, 5, 5, 5)).toBe(0)
    })
  })

  describe('randomInt', () => {
    it('should generate a random integer within the specified range', () => {
      // Test with a large number of iterations to ensure the range is respected
      for (let i = 0; i < 100; i++) {
        const result = randomInt(1, 10)
        expect(result).toBeGreaterThanOrEqual(1)
        expect(result).toBeLessThanOrEqual(10)
        expect(Number.isInteger(result)).toBe(true)
      }
    })

    it('should handle min equal to max', () => {
      expect(randomInt(5, 5)).toBe(5)
    })

    it('should handle min greater than max', () => {
      // Should swap the values
      const result = randomInt(10, 1)
      expect(result).toBeGreaterThanOrEqual(1)
      expect(result).toBeLessThanOrEqual(10)
    })
  })

  describe('randomFloat', () => {
    it('should generate a random float within the specified range', () => {
      // Test with a large number of iterations to ensure the range is respected
      for (let i = 0; i < 100; i++) {
        const result = randomFloat(1, 10)
        expect(result).toBeGreaterThanOrEqual(1)
        expect(result).toBeLessThanOrEqual(10)
      }
    })

    it('should handle min equal to max', () => {
      expect(randomFloat(5, 5)).toBe(5)
    })

    it('should handle min greater than max', () => {
      // Should swap the values
      const result = randomFloat(10, 1)
      expect(result).toBeGreaterThanOrEqual(1)
      expect(result).toBeLessThanOrEqual(10)
    })
  })
})
