import { test, expect } from '@playwright/test';

/**
 * End-to-end tests for shape workflows
 * 
 * These tests verify complete user workflows for creating, editing, and deleting shapes.
 */
test.describe('Shape Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the canvas to be ready
    await page.waitForSelector('#canvas-container svg', { state: 'visible' });
  });
  
  test('Create, edit, and delete rectangle workflow', async ({ page }) => {
    // Step 1: Create a rectangle
    await test.step('Create rectangle', async () => {
      // Click the rectangle tool
      await page.click('[data-testid="tool-rectangle"]');
      
      // Click on the canvas to create the rectangle
      await page.click('#canvas-container svg', { position: { x: 200, y: 200 } });
      
      // Verify the rectangle was created
      const rectangle = page.locator('#canvas-container svg rect').first();
      await expect(rectangle).toBeVisible();
    });
    
    // Step 2: Select and edit the rectangle
    await test.step('Edit rectangle', async () => {
      // Click the select tool
      await page.click('[data-testid="tool-select"]');
      
      // Select the rectangle
      await page.click('#canvas-container svg rect');
      
      // Verify the properties panel is visible
      const propertiesPanel = page.locator('[data-testid="properties-panel"]');
      await expect(propertiesPanel).toBeVisible();
      
      // Change the fill color
      await page.fill('[data-testid="fill-color-input"]', '#ff0000');
      await page.click('[data-testid="apply-changes"]');
      
      // Verify the rectangle's fill color has changed
      const rectangle = page.locator('#canvas-container svg rect').first();
      await expect(rectangle).toHaveAttribute('fill', '#ff0000');
    });
    
    // Step 3: Delete the rectangle
    await test.step('Delete rectangle', async () => {
      // Ensure the rectangle is selected
      await page.click('#canvas-container svg rect');
      
      // Click the delete button
      await page.click('[data-testid="delete-shape"]');
      
      // Verify the rectangle is no longer visible
      const rectangle = page.locator('#canvas-container svg rect');
      await expect(rectangle).not.toBeVisible();
    });
  });
  
  test('Selection and transformation workflow', async ({ page }) => {
    // Step 1: Create multiple shapes
    await test.step('Create shapes', async () => {
      // Create a rectangle
      await page.click('[data-testid="tool-rectangle"]');
      await page.click('#canvas-container svg', { position: { x: 150, y: 150 } });
      
      // Create an ellipse
      await page.click('[data-testid="tool-ellipse"]');
      await page.click('#canvas-container svg', { position: { x: 300, y: 150 } });
      
      // Verify both shapes were created
      const rectangle = page.locator('#canvas-container svg rect').first();
      const ellipse = page.locator('#canvas-container svg ellipse').first();
      await expect(rectangle).toBeVisible();
      await expect(ellipse).toBeVisible();
    });
    
    // Step 2: Select and move shapes
    await test.step('Select and move shapes', async () => {
      // Click the select tool
      await page.click('[data-testid="tool-select"]');
      
      // Select the rectangle
      await page.click('#canvas-container svg rect');
      
      // Get initial position
      const initialRect = await page.locator('#canvas-container svg rect').first().boundingBox();
      
      // Drag the rectangle
      await page.mouse.move(initialRect.x + initialRect.width / 2, initialRect.y + initialRect.height / 2);
      await page.mouse.down();
      await page.mouse.move(initialRect.x + 50, initialRect.y + 50);
      await page.mouse.up();
      
      // Verify the rectangle has moved
      const movedRect = await page.locator('#canvas-container svg rect').first().boundingBox();
      expect(movedRect.x).toBeGreaterThan(initialRect.x);
      expect(movedRect.y).toBeGreaterThan(initialRect.y);
    });
    
    // Step 3: Resize a shape
    await test.step('Resize shape', async () => {
      // Select the ellipse
      await page.click('#canvas-container svg ellipse');
      
      // Get initial size
      const initialEllipse = await page.locator('#canvas-container svg ellipse').first().boundingBox();
      
      // Resize the ellipse using the properties panel
      await page.fill('[data-testid="rx-input"]', '60');
      await page.fill('[data-testid="ry-input"]', '40');
      await page.click('[data-testid="apply-changes"]');
      
      // Verify the ellipse has been resized
      const resizedEllipse = await page.locator('#canvas-container svg ellipse').first();
      await expect(resizedEllipse).toHaveAttribute('rx', '60');
      await expect(resizedEllipse).toHaveAttribute('ry', '40');
    });
  });
  
  test('Grouping and ungrouping workflow', async ({ page }) => {
    // Step 1: Create multiple shapes
    await test.step('Create shapes', async () => {
      // Create a rectangle
      await page.click('[data-testid="tool-rectangle"]');
      await page.click('#canvas-container svg', { position: { x: 150, y: 150 } });
      
      // Create an ellipse
      await page.click('[data-testid="tool-ellipse"]');
      await page.click('#canvas-container svg', { position: { x: 250, y: 150 } });
      
      // Verify both shapes were created
      const rectangle = page.locator('#canvas-container svg rect').first();
      const ellipse = page.locator('#canvas-container svg ellipse').first();
      await expect(rectangle).toBeVisible();
      await expect(ellipse).toBeVisible();
    });
    
    // Step 2: Group shapes
    await test.step('Group shapes', async () => {
      // Click the select tool
      await page.click('[data-testid="tool-select"]');
      
      // Select both shapes (using shift key)
      await page.click('#canvas-container svg rect');
      await page.keyboard.down('Shift');
      await page.click('#canvas-container svg ellipse');
      await page.keyboard.up('Shift');
      
      // Click the group button
      await page.click('[data-testid="group-shapes"]');
      
      // Verify a group was created
      const group = page.locator('#canvas-container svg g');
      await expect(group).toBeVisible();
      
      // Verify the group contains both shapes
      const groupedRect = page.locator('#canvas-container svg g rect');
      const groupedEllipse = page.locator('#canvas-container svg g ellipse');
      await expect(groupedRect).toBeVisible();
      await expect(groupedEllipse).toBeVisible();
    });
    
    // Step 3: Move the group
    await test.step('Move group', async () => {
      // Get initial position
      const initialGroup = await page.locator('#canvas-container svg g').boundingBox();
      
      // Drag the group
      await page.mouse.move(initialGroup.x + initialGroup.width / 2, initialGroup.y + initialGroup.height / 2);
      await page.mouse.down();
      await page.mouse.move(initialGroup.x + 100, initialGroup.y + 50);
      await page.mouse.up();
      
      // Verify the group has moved
      const movedGroup = await page.locator('#canvas-container svg g').boundingBox();
      expect(movedGroup.x).toBeGreaterThan(initialGroup.x);
      expect(movedGroup.y).toBeGreaterThan(initialGroup.y);
    });
    
    // Step 4: Ungroup shapes
    await test.step('Ungroup shapes', async () => {
      // Select the group
      await page.click('#canvas-container svg g');
      
      // Click the ungroup button
      await page.click('[data-testid="ungroup-shapes"]');
      
      // Verify the group no longer exists
      const group = page.locator('#canvas-container svg g');
      await expect(group).not.toBeVisible();
      
      // Verify the individual shapes exist
      const rectangle = page.locator('#canvas-container svg rect').first();
      const ellipse = page.locator('#canvas-container svg ellipse').first();
      await expect(rectangle).toBeVisible();
      await expect(ellipse).toBeVisible();
    });
  });
  
  test('Undo and redo workflow', async ({ page }) => {
    // Step 1: Create a shape
    await test.step('Create shape', async () => {
      // Create a rectangle
      await page.click('[data-testid="tool-rectangle"]');
      await page.click('#canvas-container svg', { position: { x: 200, y: 200 } });
      
      // Verify the rectangle was created
      const rectangle = page.locator('#canvas-container svg rect').first();
      await expect(rectangle).toBeVisible();
    });
    
    // Step 2: Edit the shape
    await test.step('Edit shape', async () => {
      // Click the select tool
      await page.click('[data-testid="tool-select"]');
      
      // Select the rectangle
      await page.click('#canvas-container svg rect');
      
      // Change the fill color
      await page.fill('[data-testid="fill-color-input"]', '#00ff00');
      await page.click('[data-testid="apply-changes"]');
      
      // Verify the rectangle's fill color has changed
      const rectangle = page.locator('#canvas-container svg rect').first();
      await expect(rectangle).toHaveAttribute('fill', '#00ff00');
    });
    
    // Step 3: Undo the edit
    await test.step('Undo edit', async () => {
      // Click the undo button
      await page.click('[data-testid="undo-button"]');
      
      // Verify the rectangle's fill color has reverted
      const rectangle = page.locator('#canvas-container svg rect').first();
      await expect(rectangle).not.toHaveAttribute('fill', '#00ff00');
    });
    
    // Step 4: Redo the edit
    await test.step('Redo edit', async () => {
      // Click the redo button
      await page.click('[data-testid="redo-button"]');
      
      // Verify the rectangle's fill color has been reapplied
      const rectangle = page.locator('#canvas-container svg rect').first();
      await expect(rectangle).toHaveAttribute('fill', '#00ff00');
    });
    
    // Step 5: Delete the shape
    await test.step('Delete shape', async () => {
      // Select the rectangle
      await page.click('#canvas-container svg rect');
      
      // Click the delete button
      await page.click('[data-testid="delete-shape"]');
      
      // Verify the rectangle is no longer visible
      const rectangle = page.locator('#canvas-container svg rect');
      await expect(rectangle).not.toBeVisible();
    });
    
    // Step 6: Undo the deletion
    await test.step('Undo deletion', async () => {
      // Click the undo button
      await page.click('[data-testid="undo-button"]');
      
      // Verify the rectangle is visible again
      const rectangle = page.locator('#canvas-container svg rect');
      await expect(rectangle).toBeVisible();
    });
  });
});
