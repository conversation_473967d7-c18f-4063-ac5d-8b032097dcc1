import { beforeEach, describe, expect, it, vi } from 'vitest'
import { LivingRoomPlanningStrategy } from '@/core/compute/strategies/space/LivingRoomPlanningStrategy'
import { StudyRoomPlanningStrategy } from '@/core/compute/strategies/space/StudyRoomPlanningStrategy'
import { StrategyRegistry } from '@/core/registry/StrategyRegistry'
import { CoreError } from '@/services/errors'

describe('space Planning Integration', () => {
  let registry: StrategyRegistry
  let studyRoomStrategy: StudyRoomPlanningStrategy
  let livingRoomStrategy: LivingRoomPlanningStrategy
  let mockElements: any[]
  let mockRoomBoundary: any

  beforeEach(() => {
    // 创建策略注册表
    registry = new StrategyRegistry()

    // 创建策略实例
    studyRoomStrategy = new StudyRoomPlanningStrategy()
    livingRoomStrategy = new LivingRoomPlanningStrategy()

    // 注册策略
    registry.registerSpacePlanningStrategy(studyRoomStrategy)
    registry.registerSpacePlanningStrategy(livingRoomStrategy)

    // 创建模拟房间边界
    mockRoomBoundary = {
      id: 'room-boundary',
      type: 'room',
      getBoundingBox: vi.fn().mockReturnValue({
        x: 0,
        y: 0,
        width: 4,
        height: 5,
      }),
      getArea: vi.fn().mockReturnValue(20), // 20平方米
    }

    // 创建模拟元素数组
    mockElements = [
      mockRoomBoundary,
      {
        id: 'desk',
        type: 'desk',
        getBoundingBox: vi.fn().mockReturnValue({
          x: 1,
          y: 1,
          width: 1.5,
          height: 0.7,
        }),
      },
      {
        id: 'chair',
        type: 'chair',
        getBoundingBox: vi.fn().mockReturnValue({
          x: 1.2,
          y: 1.8,
          width: 0.5,
          height: 0.5,
        }),
      },
      {
        id: 'sofa',
        type: 'sofa',
        getBoundingBox: vi.fn().mockReturnValue({
          x: 0.5,
          y: 3.5,
          width: 2,
          height: 0.8,
        }),
      },
    ]
  })

  it('should register and retrieve space planning strategies correctly', () => {
    // 获取书房规划策略
    const retrievedStudyStrategy = registry.getSpacePlanningStrategy('study')
    expect(retrievedStudyStrategy).toBe(studyRoomStrategy)

    // 获取客厅规划策略
    const retrievedLivingStrategy = registry.getSpacePlanningStrategy('living')
    expect(retrievedLivingStrategy).toBe(livingRoomStrategy)

    // 尝试获取不存在的策略
    expect(() => {
      registry.getSpacePlanningStrategy('non-existent')
    }).toThrow(CoreError)
  })

  it('should calculate space utilization with different strategies', () => {
    // 使用书房规划策略计算空间利用率
    const studyUtilization = studyRoomStrategy.calculateSpaceUtilization(mockElements, mockRoomBoundary)
    expect(studyUtilization).toBeDefined()
    expect(typeof studyUtilization).toBe('number')

    // 使用客厅规划策略计算空间利用率
    const livingUtilization = livingRoomStrategy.calculateSpaceUtilization(mockElements, mockRoomBoundary)
    expect(livingUtilization).toBeDefined()
    expect(typeof livingUtilization).toBe('number')

    // 两种策略的计算结果应该相同（因为使用相同的元素和房间边界）
    expect(studyUtilization).toBeCloseTo(livingUtilization)
  })

  it('should provide different recommendations based on space type', () => {
    // 创建模拟通道
    const mockPathways = [
      {
        start: { x: 0, y: 2.5 },
        end: { x: 4, y: 2.5 },
      },
    ]

    // 使用书房规划策略检查通道宽度
    const studyPathwayResult = studyRoomStrategy.checkPathwayWidth(mockElements, mockPathways, 0.8)
    expect(studyPathwayResult).toBeDefined()
    expect(Array.isArray(studyPathwayResult)).toBe(true)

    // 使用客厅规划策略检查通道宽度
    const livingPathwayResult = livingRoomStrategy.checkPathwayWidth(mockElements, mockPathways, 0.8)
    expect(livingPathwayResult).toBeDefined()
    expect(Array.isArray(livingPathwayResult)).toBe(true)

    // 两种策略的通道宽度检查结果应该相同（因为使用相同的元素和通道）
    expect(studyPathwayResult.length).toBe(livingPathwayResult.length)
    expect(studyPathwayResult[0].isValid).toBe(livingPathwayResult[0].isValid)
  })

  it('should handle errors consistently across strategies', () => {
    const invalidRoomBoundary = { id: 'invalid' }

    // 使用书房规划策略，应该抛出错误
    expect(() => {
      studyRoomStrategy.calculateSpaceUtilization(mockElements, invalidRoomBoundary as any)
    }).toThrow(CoreError)

    // 使用客厅规划策略，也应该抛出错误
    expect(() => {
      livingRoomStrategy.calculateSpaceUtilization(mockElements, invalidRoomBoundary as any)
    }).toThrow(CoreError)
  })
})
