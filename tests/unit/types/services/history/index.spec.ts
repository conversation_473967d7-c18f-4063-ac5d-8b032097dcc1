import type {
  HistoryAction,
  HistoryEntry,
  HistoryService,
  HistoryServiceResult,
  HistoryState,
} from '@/types/services/history'
import { describe, expect, it, vi } from 'vitest'

describe('history Types', () => {
  describe('historyEntry', () => {
    it('should allow creating a history entry with all properties', () => {
      const entry: HistoryEntry = {
        id: 'entry-123',
        type: 'SHAPE_CREATED',
        timestamp: Date.now(),
        data: { shapeId: 'shape-123', type: 'rectangle' },
        description: 'Created rectangle shape',
      }

      expect(entry.id).toBe('entry-123')
      expect(entry.type).toBe('SHAPE_CREATED')
      expect(entry.timestamp).toBeGreaterThan(0)
      expect(entry.data).toBeDefined()
      expect(entry.data.shapeId).toBe('shape-123')
      expect(entry.description).toBe('Created rectangle shape')
    })

    it('should allow creating a history entry with minimal properties', () => {
      const entry: HistoryEntry = {
        id: 'entry-456',
        type: 'SHAPE_DELETED',
        timestamp: Date.now(),
        data: { shapeId: 'shape-456' },
      }

      expect(entry.id).toBe('entry-456')
      expect(entry.type).toBe('SHAPE_DELETED')
      expect(entry.timestamp).toBeGreaterThan(0)
      expect(entry.data).toBeDefined()
      expect(entry.data.shapeId).toBe('shape-456')
      expect(entry.description).toBeUndefined()
    })
  })

  describe('historyState', () => {
    it('should allow creating a history state with all properties', () => {
      const entries: HistoryEntry[] = [
        {
          id: 'entry-1',
          type: 'SHAPE_CREATED',
          timestamp: Date.now() - 1000,
          data: { shapeId: 'shape-1' },
        },
        {
          id: 'entry-2',
          type: 'SHAPE_EDITED',
          timestamp: Date.now(),
          data: { shapeId: 'shape-1', properties: { width: 100 } },
        },
      ]

      const state: HistoryState = {
        entries,
        currentIndex: 1,
        canUndo: true,
        canRedo: false,
        inBatch: false,
        batchId: null,
      }

      expect(state.entries).toHaveLength(2)
      expect(state.currentIndex).toBe(1)
      expect(state.canUndo).toBe(true)
      expect(state.canRedo).toBe(false)
      expect(state.inBatch).toBe(false)
      expect(state.batchId).toBeNull()
    })

    it('should allow creating a history state with batch operation', () => {
      const state: HistoryState = {
        entries: [],
        currentIndex: -1,
        canUndo: false,
        canRedo: false,
        inBatch: true,
        batchId: 'batch-123',
      }

      expect(state.entries).toHaveLength(0)
      expect(state.currentIndex).toBe(-1)
      expect(state.canUndo).toBe(false)
      expect(state.canRedo).toBe(false)
      expect(state.inBatch).toBe(true)
      expect(state.batchId).toBe('batch-123')
    })
  })

  describe('historyAction', () => {
    it('should allow creating a history action with undo and redo methods', () => {
      const undoFn = vi.fn()
      const redoFn = vi.fn()

      const action: HistoryAction = {
        type: 'SHAPE_MOVED',
        data: { shapeId: 'shape-123', from: { x: 0, y: 0 }, to: { x: 100, y: 100 } },
        description: 'Moved shape',
        undo: undoFn,
        redo: redoFn,
      }

      expect(action.type).toBe('SHAPE_MOVED')
      expect(action.data.shapeId).toBe('shape-123')
      expect(action.description).toBe('Moved shape')

      // Call the methods to verify they work
      action.undo()
      expect(undoFn).toHaveBeenCalledTimes(1)

      action.redo()
      expect(redoFn).toHaveBeenCalledTimes(1)
    })
  })

  describe('historyService', () => {
    it('should define all required methods', () => {
      // Create a mock implementation of HistoryService
      const mockHistoryService: HistoryService = {
        addEntry: vi.fn(),
        undo: vi.fn(),
        redo: vi.fn(),
        clear: vi.fn(),
        getState: vi.fn(),
        startBatch: vi.fn(),
        endBatch: vi.fn(),
        canUndo: vi.fn(),
        canRedo: vi.fn(),
      }

      expect(typeof mockHistoryService.addEntry).toBe('function')
      expect(typeof mockHistoryService.undo).toBe('function')
      expect(typeof mockHistoryService.redo).toBe('function')
      expect(typeof mockHistoryService.clear).toBe('function')
      expect(typeof mockHistoryService.getState).toBe('function')
      expect(typeof mockHistoryService.startBatch).toBe('function')
      expect(typeof mockHistoryService.endBatch).toBe('function')
      expect(typeof mockHistoryService.canUndo).toBe('function')
      expect(typeof mockHistoryService.canRedo).toBe('function')
    })
  })

  describe('historyServiceResult', () => {
    it('should allow creating successful result', () => {
      const entry: HistoryEntry = {
        id: 'entry-123',
        type: 'SHAPE_CREATED',
        timestamp: Date.now(),
        data: { shapeId: 'shape-123' },
      }

      const result: HistoryServiceResult<HistoryEntry> = {
        success: true,
        data: entry,
        timestamp: Date.now(),
      }

      expect(result.success).toBe(true)
      expect(result.data).toBeDefined()
      expect(result.data?.id).toBe('entry-123')
      expect(result.timestamp).toBeGreaterThan(0)
      expect(result.error).toBeUndefined()
    })

    it('should allow creating error result', () => {
      const result: HistoryServiceResult<HistoryEntry> = {
        success: false,
        error: 'History operation failed',
        timestamp: Date.now(),
      }

      expect(result.success).toBe(false)
      expect(result.error).toBe('History operation failed')
      expect(result.timestamp).toBeGreaterThan(0)
      expect(result.data).toBeUndefined()
    })
  })
})
