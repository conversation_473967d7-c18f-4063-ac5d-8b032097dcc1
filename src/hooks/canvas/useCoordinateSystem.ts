/**
 * Canvas Coordinate System Hook
 *
 * This module provides a React hook for managing coordinate systems
 * on the canvas. It handles coordinate transformations between different
 * coordinate spaces (screen, SVG, world coordinates) and provides helper
 * functions for mouse event handling and shape positioning.
 *
 * @module hooks/canvas/useCoordinateSystem
 */

import type React from 'react'

import type { CanvasMouseEvent } from '@/types'
import type Point from '@/types/core/element/geometry/point'
import type { ShapeElement } from '@/types/core/elementDefinitions'
import * as d3 from 'd3'
import { useCallback } from 'react'

interface UseCoordinateSystemProps {
  svgRef: React.RefObject<SVGSVGElement | null>
  // mainGroupRef: React.RefObject<SVGGElement | null>; // Not used in current simplified calculation
  pan: Point
  zoom: number
}

export function useCoordinateSystem({
  svgRef,
  pan,
  zoom,
}: UseCoordinateSystemProps) {
  const createMouseEventDetails = useCallback((
    originalEvent: React.MouseEvent<SVGElement> | React.DragEvent<SVGElement>,
    targetOverride?: EventTarget | null,
  ): CanvasMouseEvent => {
    const svgNode = svgRef.current
    let worldX = 0
    let worldY = 0
    let svgX = 0
    let svgY = 0

    const currentPan = pan
    const currentZoom = zoom
    const safeZoom = currentZoom !== 0 ? currentZoom : 1

    if (svgNode) {
      const clientXImpl = (originalEvent as React.MouseEvent<SVGElement>).clientX
      const clientYImpl = (originalEvent as React.MouseEvent<SVGElement>).clientY

      const svgRect = svgNode.getBoundingClientRect()
      svgX = clientXImpl - (svgRect?.left || 0)
      svgY = clientYImpl - (svgRect?.top || 0)

      // 直接使用我们的坐标转换函数
      worldX = (svgX - currentPan.x) / safeZoom
      worldY = (svgY - currentPan.y) / safeZoom

      console.warn('[createMouseEventDetails] Direct calculation:', {
        clientX: clientXImpl,
        clientY: clientYImpl,
        svgRect,
        svgX,
        svgY,
        pan: currentPan,
        zoom: currentZoom,
        worldX,
        worldY,
      })

      console.warn('[createMouseEventDetails] Input: clientX:', clientXImpl, 'clientY:', clientYImpl)
      console.warn('[createMouseEventDetails] SVG Rect:', svgRect)
      console.warn('[createMouseEventDetails] Calculated svgX:', svgX, 'svgY:', svgY)
      console.warn('[createMouseEventDetails] Current Pan:', currentPan, 'Current Zoom:', currentZoom)

      if (currentZoom === 0) {
        console.warn('[createMouseEventDetails] Original calc warning: currentZoom was 0.')
      }
      if (Number.isNaN(worldX) || Number.isNaN(worldY)) {
        console.warn('[createMouseEventDetails] NaN detected for world coordinates.')
      }
    }
    else {
      console.warn('[createMouseEventDetails] svgNode is null. All coordinates will be (0,0).')
    }

    let d3Data: ShapeElement | null = null
    const targetElement = targetOverride || originalEvent.target
    if (targetElement instanceof SVGElement) {
      const rawD3Data = d3.select<SVGElement, ShapeElement>(targetElement).datum()
      if (rawD3Data != null && typeof rawD3Data === 'object' && 'id' in rawD3Data && 'type' in rawD3Data) {
        d3Data = rawD3Data
      }
      else if (targetElement.parentElement && targetElement.parentElement instanceof SVGElement) {
        const parentD3Data = d3.select<SVGElement, ShapeElement>(targetElement.parentElement).datum()
        if (parentD3Data != null && typeof parentD3Data === 'object' && 'id' in parentD3Data && 'type' in parentD3Data) {
          d3Data = parentD3Data
        }
      }
    }

    return {
      originalEvent,
      worldPosition: { x: worldX, y: worldY },
      svgPosition: { x: svgX, y: svgY },
      d3Data,
    }
  }, [svgRef, pan, zoom])

  return { createMouseEventDetails }
}
