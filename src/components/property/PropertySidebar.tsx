/**
 * Property Sidebar Component
 *
 * A comprehensive property editing sidebar that provides tabbed interfaces for
 * managing all aspects of selected elements. This component adapts its display
 * and functionality based on the number and types of selected elements.
 *
 * Features:
 * - Multi-tab interface (Identity/Layer, Geometry/Transform, Appearance)
 * - Multi-element selection support with mixed value handling
 * - Real-time property updates with visual feedback
 * - Pattern and texture editing capabilities
 * - Layer assignment and z-ordering controls
 * - Element deletion functionality
 * - Responsive layout with fixed positioning
 * - Unit conversion for dimensional properties
 *
 * Tabs:
 * - Identity/Layer: Element metadata, layer assignment, z-ordering
 * - Geometry/Transform: Position, dimensions, rotation, calculations
 * - Appearance: Colors, patterns, textures, visual properties
 *
 * Multi-Selection Behavior:
 * - Shows common values when all selected elements share the same value
 * - Displays "Mixed" or empty fields when values differ
 * - Updates all selected elements simultaneously
 * - Provides appropriate controls based on element types
 *
 * @example
 * ```tsx
 * <PropertySidebar
 *   selectedElements={selectedShapes}
 *   onUpdateElements={handleUpdateElements}
 *   onDeleteElement={handleDeleteElements}
 *   layerPanelModules={layerModules}
 *   isPropertySidebarOpen={true}
 *   pixelsPerMM={0.08}
 * />
 * ```
 */

import type React from 'react'
import type {
  PatternDefinition,
} from '@/types/core/element/elementPatternTypes'
import type { ElementType, ShapeElement } from '@/types/core/elementDefinitions'
import type { TaskModule } from '@/types/core/layerPanelTypes'
import { Trash2 } from 'lucide-react'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import { createPropertyPatch } from '@/services/elements/element-actions/elementEditService'
import { useShapesStore } from '@/store/shapesStore'

import AppearanceTab from './AppearanceTab'
import { getDefaultPatternDefinition } from '@/utils/pattern'
import GeometryTransformTab from './GeometryTransformTab'
import IdentityLayerTab from './IdentityLayerTab'
import { ElementTypeBadge } from './shared'

/**
 * Type guard function to validate if an object is a valid PatternDefinition.
 *
 * This function performs runtime type checking to ensure objects conform to
 * the PatternDefinition interface before they are used in pattern operations.
 *
 * @param obj - The object to validate
 * @returns True if the object is a valid PatternDefinition, false otherwise
 */
function isPatternDefinition(obj: unknown): obj is PatternDefinition {
  if (typeof obj === 'object' && obj !== null) {
    const pattern = obj as PatternDefinition // Temporary cast for checks
    return typeof pattern.id === 'string'
      && typeof pattern.type === 'string' // Ideally check against PatternType enum values
      && (typeof pattern.textureType === 'string' || pattern.textureType === undefined) // textureType can be undefined initially
  }
  return false
}

/**
 * Props interface for the PropertySidebar component
 */
interface PropertySidebarProps {
  /** Array of currently selected elements to edit */
  selectedElements: ShapeElement[]
  /** Callback function to update element properties */
  onUpdateElements: (updates: Array<{ id: string, properties: Partial<ShapeElement>, majorCategory?: string, minorCategory?: string, zLevelId?: string, pattern?: PatternDefinition }>) => void
  /** Callback function to delete elements */
  onDeleteElement: (elementIds: string[]) => void
  /** Available layer panel modules for layer assignment */
  layerPanelModules: TaskModule[]
  /** Whether the property sidebar is open/visible */
  isPropertySidebarOpen?: boolean
  /** Conversion factor from pixels to millimeters */
  pixelsPerMM: number
  /** Controls whether the bottom drawer is open (affects panel height) */
  isBottomDrawerOpen?: boolean
}

/**
 * Retrieves a common property value from multiple selected elements.
 *
 * This function supports deep property path traversal using dot notation
 * (e.g., "properties.fill" or "metadata.name") and returns the value only
 * if all selected elements have the same value for that property.
 *
 * @param propertyPath - Dot-separated path to the property (e.g., "fill", "properties.width")
 * @param selectedElements - Array of elements to check for common values
 * @returns The common value if all elements share it, undefined otherwise
 */
function getCommonValue(propertyPath: string, selectedElements: ShapeElement[]): unknown {
  if (!propertyPath) {
    return undefined
  }
  const pathParts = propertyPath.split('.')

  /**
   * Recursively traverses an object using the provided path parts.
   *
   * @param obj - The object to traverse
   * @param parts - Array of property names forming the path
   * @returns The value at the end of the path, or undefined if not found
   */
  function getDeep(obj: unknown, parts: string[]): unknown {
    let cur: unknown = obj
    for (const part of parts) {
      if (cur === null || cur === undefined) {
        return undefined
      }
      if (typeof cur === 'object' && cur !== null) {
        cur = (cur as Record<string, unknown>)[part]
      }
      else {
        return undefined
      }
    }
    return cur
  }

  if (selectedElements.length === 0) {
    return undefined
  }

  // For single element, return the value directly
  if (selectedElements.length === 1) {
    return getDeep(selectedElements[0], pathParts)
  }

  // For multiple elements, check if all have the same value
  const firstValue = getDeep(selectedElements[0], pathParts)
  const allSame = selectedElements.every(el => getDeep(el, pathParts) === firstValue)
  return allSame ? firstValue : 'mixed'
}

/**
 * Right sidebar component for editing selected element properties
 */
const PropertySidebar: React.FC<PropertySidebarProps> = ({
  selectedElements: _propsSelectedElements,
  onUpdateElements,
  onDeleteElement,
  layerPanelModules,
  isPropertySidebarOpen = true,
  pixelsPerMM,
  isBottomDrawerOpen = false,
}) => {
  // Calculate dynamic height based on bottom drawer state
  // Use useEffect to get actual toolbar height instead of hardcoded value
  const [actualToolbarHeight, setActualToolbarHeight] = useState(56) // fallback to 56px
  const bottomDrawerHeight = isBottomDrawerOpen ? 384 : 56 // 384px (md:h-96) when expanded, 56px when collapsed
  const verticalGap = 8 // 8px gap on top and bottom (total 16px)
  const dynamicHeight = `calc(100vh - ${actualToolbarHeight}px - ${bottomDrawerHeight}px - ${verticalGap * 2}px)`

  // Get actual toolbar height on mount and window resize
  useEffect(() => {
    const updateToolbarHeight = () => {
      const toolbar = document.querySelector('.toolbar')
      if (toolbar) {
        const rect = toolbar.getBoundingClientRect()
        const newHeight = rect.height
        // Use setTimeout to avoid direct setState call in useEffect
        const timeoutId = setTimeout(() => {
          setActualToolbarHeight(prevHeight => prevHeight !== newHeight ? newHeight : prevHeight)
        }, 0)
        return () => clearTimeout(timeoutId)
      }
    }

    updateToolbarHeight()
    window.addEventListener('resize', updateToolbarHeight)
    return () => window.removeEventListener('resize', updateToolbarHeight)
  }, [])

  // Get fresh data directly from Zustand store to ensure we have the latest state
  const shapesFromStore = useShapesStore(state => state.shapes)
  const selectedShapeIds = useShapesStore(state => state.selectedShapeIds)

  // Compute selectedElements from fresh store data
  const selectedElements = shapesFromStore.filter(shape =>
    selectedShapeIds.includes(shape.id),
  )

  const [activeTab, setActiveTab] = useState('identity')
  const [, _setForceRefresh] = useState(0)

  // Handle tab switching
  const handleTabChange = (value: string) => {
    setActiveTab(value)
  }

  /**
   * Updates multiple properties for all selected elements in a single batch
   *
   * @param updates - Object containing property paths and their new values
   */
  const updateProperties = (updates: Record<string, unknown>): void => {
    // Ensure selectedElements is not empty before proceeding
    if (selectedElements.length === 0) {
      return
    }

    // Create patches for all selected elements
    const patches = selectedElements
      .map((element) => {
        // Start with a base patch
        const combinedPatch: { id: string, properties: Record<string, unknown>, [key: string]: unknown } = {
          id: element.id,
          properties: {},
        }

        // Apply each update to the patch
        for (const [propertyPath, value] of Object.entries(updates)) {
          const patch = createPropertyPatch(element, propertyPath, value)
          if (patch !== null) {
            // Merge the patch into the combined patch
            Object.entries(patch).forEach(([key, patchValue]) => {
              if (key === 'id')
                return // Skip ID
              if (key === 'properties') {
                // Merge properties
                combinedPatch.properties = { ...combinedPatch.properties, ...(patchValue as Record<string, unknown>) }
              }
              else {
                // Set top-level properties
                combinedPatch[key] = patchValue
              }
            })
          }
        }

        return combinedPatch
      })
      .filter((patch): patch is NonNullable<typeof patch> => patch !== null)

    if (patches.length === 0) {
      return
    }

    // Format patches for onUpdateElements
    const formattedPatches = patches.map((patch) => {
      const { id, ...rest } = patch

      // 🔧 特殊处理：确保顶层属性和 properties 中的属性保持一致
      const properties = { ...(rest.properties ?? {}) }

      // 对于其他顶层属性，添加到 properties 中，但要小心处理特殊属性
      Object.entries(rest).forEach(([key, value]) => {
        if (key !== 'properties' && key !== 'pattern' && key !== 'majorCategory' && key !== 'minorCategory' && key !== 'zLevelId') {
          properties[key] = value
        }
      })

      return {
        id,
        properties,
        ...(rest.pattern !== undefined && rest.pattern !== null && typeof rest.pattern === 'object' && { pattern: rest.pattern as PatternDefinition }),
        ...(rest.majorCategory !== undefined && rest.majorCategory !== null && typeof rest.majorCategory === 'string' && { majorCategory: rest.majorCategory }),
        ...(rest.minorCategory !== undefined && rest.minorCategory !== null && typeof rest.minorCategory === 'string' && { minorCategory: rest.minorCategory }),
        ...(rest.zLevelId !== undefined && rest.zLevelId !== null && typeof rest.zLevelId === 'string' && { zLevelId: rest.zLevelId }),
      }
    })

    // Use formatted patches to call onUpdateElements
    onUpdateElements(formattedPatches)

    // Force refresh selectedElements to ensure UI reflects latest properties
    if (typeof window !== 'undefined' && window.__ZUSTAND_SHAPES_STORE__) {
      const latestShapes = window.__ZUSTAND_SHAPES_STORE__.getState().shapes
      // Force refresh to ensure UI reflects latest properties
      selectedElements.map(el => latestShapes.find(s => s.id === el.id) || el)
      setTimeout(() => {
        setActiveTab(tab => tab)
      }, 150)
    }
  }

  /**
   * Updates properties for all selected elements
   *
   * @param propertyPath - The path to the property to update
   * @param value - The new value for the property
   */
  const updateProperty = (propertyPath: string, value: unknown): void => {
    // Ensure selectedElements is not empty before proceeding
    if (selectedElements === null || selectedElements === undefined || selectedElements.length === 0) {
      return
    }

    // Create patches for all selected elements
    const patches = selectedElements
      .map((element) => {
        const patch = createPropertyPatch(element, propertyPath, value)
        return patch
      })
      .filter((patch): patch is NonNullable<typeof patch> => patch !== null)

    if (patches.length === 0) {
      return
    }

    // 修改：将patches转换为EditorLayout.handleUpdateElements期望的格式
    // 但保留所有属性，不仅仅是properties
    const formattedPatches = patches.map((patch) => {
      // 提取id
      const { id, ...rest } = patch

      // 检查是否包含图层属性（用于调试）
      // const hasLayerProps = rest.majorCategory !== undefined
      //   || rest.minorCategory !== undefined
      //   || rest.zLevelId !== undefined

      // 返回正确的格式：{ id: string, properties: Partial<ShapeElement>, majorCategory?: string, minorCategory?: string, zLevelId?: string, pattern?: PatternDefinition }
      // 特殊处理pattern属性，保持为顶层属性
      const formattedPatch: {
        id: string
        properties: Record<string, unknown>
        majorCategory?: string
        minorCategory?: string
        zLevelId?: string
        pattern?: unknown
      } = {
        id,
        properties: {
          // 首先使用 rest.properties 中的属性（这些是 PropertyUpdateService 处理过的）
          ...(rest.properties ?? {}),
        },
      }

      // 🔧 特殊处理：对于其他顶层属性，添加到 properties 中，但要小心处理特殊属性
      Object.entries(rest).forEach(([key, value]) => {
        if (key !== 'properties' && key !== 'pattern' && key !== 'majorCategory' && key !== 'minorCategory' && key !== 'zLevelId') {
          formattedPatch.properties[key] = value
        }
      })

      // 特殊处理pattern属性，保持为顶层属性
      if ('pattern' in rest) {
        formattedPatch.pattern = rest.pattern
      }

      // 特殊处理图层属性，确保它们同时存在于顶层和properties中
      if (rest.majorCategory !== undefined) {
        formattedPatch.majorCategory = rest.majorCategory as string
        formattedPatch.properties.majorCategory = rest.majorCategory
      }
      if (rest.minorCategory !== undefined) {
        formattedPatch.minorCategory = rest.minorCategory as string
        formattedPatch.properties.minorCategory = rest.minorCategory
      }
      if (rest.zLevelId !== undefined) {
        formattedPatch.zLevelId = rest.zLevelId as string
        formattedPatch.properties.zLevelId = rest.zLevelId
      }

      return formattedPatch
    })

    // 使用格式化后的patches调用onUpdateElements
    onUpdateElements(formattedPatches as Array<{
      id: string
      properties: Partial<ShapeElement>
      majorCategory?: string
      minorCategory?: string
      zLevelId?: string
      pattern?: PatternDefinition
    }>)

    // 强制刷新 selectedElements，确保 UI 反映最新属性
    if (typeof window !== 'undefined' && window.__ZUSTAND_SHAPES_STORE__ !== undefined) {
      const latestShapes = window.__ZUSTAND_SHAPES_STORE__.getState().shapes
      // Force refresh to ensure UI reflects latest properties
      selectedElements.map(el => latestShapes.find(s => s.id === el.id) || el)
      setTimeout(() => {
        setActiveTab(tab => tab)

        // 特别处理pattern属性更新，强制重新计算currentPatternSelectValue
        if (propertyPath === 'pattern') {
          _setForceRefresh(prev => prev + 1)
        }
      }, 150)
    }
  }

  // 类型安全递归 patch pattern options 的工具函数
  function patchPatternOptionBackground(
    pattern: PatternDefinition,
    optionPath: string,
    value: unknown,
  ): PatternDefinition {
    const parts = optionPath.split('.')
    const newPattern = JSON.parse(JSON.stringify(pattern)) as PatternDefinition
    let target: Record<string, unknown> = newPattern as unknown as Record<string, unknown>
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i]
      if (typeof target[part] !== 'object' || target[part] === null || Array.isArray(target[part])) {
        target[part] = {}
      }
      target = target[part] as Record<string, unknown>
    }
    target[parts[parts.length - 1]] = value
    return newPattern
  }

  // Pattern Type 相关变量提前声明，供 handlePatternOptionChange 使用
  // Explicitly define the type for the Select component's value and pattern type logic
  type ResolvedPatternType = 'lines' | 'circles' | 'paths' | 'none'
  function normalizePatternType(type: string | undefined): ResolvedPatternType {
    if (type === undefined || type === null || type === '') {
      return 'none'
    }
    if (type === 'lines' || type === 'TEXTURE_LINES') {
      return 'lines'
    }
    if (type === 'circles' || type === 'TEXTURE_CIRCLES') {
      return 'circles'
    }
    if (type === 'paths' || type === 'TEXTURE_PATHS') {
      return 'paths'
    }
    return 'none'
  }
  // 🔧 修复：使用useMemo来确保currentPatternSelectValue能正确响应状态变化

  // 强制重新计算，依赖于forceRefresh状态
  const calculateCurrentPatternSelectValue = () => {
    if (selectedElements.length === 1 && typeof selectedElements[0] === 'object' && selectedElements[0] !== null) {
      const shape = selectedElements[0]
      // 🔧 修复：更严格的pattern检查，确保pattern不仅存在而且有有效值
      const hasValidPattern = shape.pattern
        && typeof shape.pattern === 'object'
        && shape.pattern !== null
        && typeof shape.pattern.textureType === 'string'
        && shape.pattern.textureType !== null
        && shape.pattern.textureType !== undefined

      const patternTextureType = hasValidPattern && shape.pattern ? shape.pattern.textureType : undefined
      const result = normalizePatternType(patternTextureType)
      return result
    }
    else {
      const rawCommonPattern = getCommonValue('pattern', selectedElements)
      if (
        rawCommonPattern !== null && typeof rawCommonPattern === 'object'
        && !Array.isArray(rawCommonPattern)
        && typeof (rawCommonPattern as { textureType?: unknown }).textureType === 'string'
        && (rawCommonPattern as { textureType?: unknown }).textureType !== ''
      ) {
        const textureType = (rawCommonPattern as { textureType?: unknown }).textureType
        const result = normalizePatternType(typeof textureType === 'string' ? textureType : undefined)
        return result
      }
      else {
        return 'none' as ResolvedPatternType
      }
    }
  }

  const currentPatternSelectValue = calculateCurrentPatternSelectValue()
  const isPatternActive = ['lines', 'circles', 'paths'].includes(currentPatternSelectValue)

  const metadataNameValue = getCommonValue('metadata.name', selectedElements)
  const inputNameValue = (typeof metadataNameValue === 'string' && metadataNameValue !== 'mixed' && metadataNameValue !== '')
    ? metadataNameValue
    : ''

  /**
   * 生成与元素颜色形成对比的 Pattern 颜色组合
   * @param elementFill 元素的填充颜色
   * @param elementStroke 元素的边框颜色
   * @returns Pattern 颜色组合 { background, stroke, fill }
   */
  const generatePatternColors = (elementFill?: string, elementStroke?: string): {
    background: string
    stroke: string
    fill: string
  } => {
    // 预定义的颜色方案
    const colorPalette = [
      '#000000', // 黑色
      '#333333', // 深灰色
      '#666666', // 中灰色
      '#999999', // 浅灰色
      '#cccccc', // 更浅灰色
      '#ffffff', // 白色
      '#2563eb', // 蓝色
      '#dc2626', // 红色
      '#16a34a', // 绿色
      '#ca8a04', // 黄色
      '#9333ea', // 紫色
      '#f97316', // 橙色
    ]

    // 获取元素的主要颜色
    const usedColors = [elementFill, elementStroke].filter(Boolean)

    // 简单的颜色亮度计算函数
    const getLuminance = (hex: string): number => {
      const color = hex.replace('#', '')
      const r = Number.parseInt(color.substring(0, 2), 16)
      const g = Number.parseInt(color.substring(2, 4), 16)
      const b = Number.parseInt(color.substring(4, 6), 16)
      // 使用相对亮度公式
      return (0.299 * r + 0.587 * g + 0.114 * b) / 255
    }

    // 检查颜色是否相似（亮度差异小于0.2认为相似）
    const isSimilarColor = (color1: string, color2: string): boolean => {
      const lum1 = getLuminance(color1)
      const lum2 = getLuminance(color2)
      return Math.abs(lum1 - lum2) < 0.2
    }

    // 找到三种不同的颜色：background, stroke, fill
    const availableColors = colorPalette.filter((color) => {
      // 排除与元素颜色相似的颜色
      return !usedColors.some(usedColor =>
        usedColor !== undefined && usedColor !== null && isSimilarColor(color, usedColor),
      )
    })

    // 如果可用颜色不足，使用默认组合
    if (availableColors.length < 3) {
      const validColors = usedColors.filter((color): color is string =>
        color !== undefined && color !== null,
      )
      const elementLuminance = validColors.length > 0
        ? validColors.reduce((sum, color) => sum + getLuminance(color), 0) / validColors.length
        : 0.5

      if (elementLuminance > 0.5) {
        // 元素颜色较亮，使用深色组合
        return {
          background: '#f0f0f0', // 浅灰背景
          stroke: '#333333', // 深灰边框
          fill: '#000000', // 黑色填充
        }
      }
      else {
        // 元素颜色较暗，使用亮色组合
        return {
          background: '#2a2a2a', // 深灰背景
          stroke: '#cccccc', // 浅灰边框
          fill: '#ffffff', // 白色填充
        }
      }
    }

    // 选择三种不同亮度的颜色
    const sortedColors = availableColors.sort((a, b) => getLuminance(a) - getLuminance(b))

    return {
      background: sortedColors[Math.floor(sortedColors.length * 0.3)], // 中等亮度作为背景
      stroke: sortedColors[Math.floor(sortedColors.length * 0.7)], // 较亮颜色作为边框
      fill: sortedColors[Math.floor(sortedColors.length * 0.1)], // 较暗颜色作为填充
    }
  }

  // Pattern Type 切换：整体替换 pattern 结构
  const handlePatternChange = (newPattern: { textureType?: string }, fillColorLocal?: string) => {
    const { textureType } = newPattern

    if (textureType === undefined || textureType === null || textureType === '' || String(textureType) === 'none') {
      updateProperty('pattern', undefined)

      // 🔧 修复：立即强制刷新以确保currentPatternSelectValue更新
      _setForceRefresh(prev => prev + 1)

      // 强制刷新以确保UI更新
      setTimeout(() => {
        // 再次强制刷新确保UI同步
        _setForceRefresh(prev => prev + 1)
      }, 50)

      return
    }
    const id = `${selectedElements[0]?.id || 'default'}-pattern`

    // 获取元素的填充色和边框色
    const elementFill = (typeof fillColorLocal === 'string' && fillColorLocal)
      ? fillColorLocal
      : (typeof selectedElements[0]?.properties?.fill === 'string' && selectedElements[0]?.properties?.fill
          ? selectedElements[0]?.properties?.fill
          : (typeof selectedElements[0]?.fill === 'string' && selectedElements[0]?.fill ? selectedElements[0]?.fill : '#ffffff'))

    const elementStroke = (typeof selectedElements[0]?.properties?.stroke === 'string' && selectedElements[0]?.properties?.stroke)
      ? selectedElements[0]?.properties?.stroke
      : (typeof selectedElements[0]?.stroke === 'string' && selectedElements[0]?.stroke ? selectedElements[0]?.stroke : undefined)

    // 生成 Pattern 颜色：背景色与元素填充色相同，图案颜色使用对比色
    const patternColors = generatePatternColors(elementFill, elementStroke)

    // 重新定义颜色逻辑：背景色 = 元素填充色，图案颜色 = 对比色
    const patternBackground = elementFill // Pattern 背景色与元素填充色相同
    const patternFillColor = patternColors.fill // Pattern 图案主色使用对比色

    let pattern: PatternDefinition
    if (textureType === 'lines') {
      pattern = {
        id,
        type: 'lines',
        textureType: 'lines',
        linesOptions: {
          size: 10,
          strokeWidth: 1,
          orientation: ['horizontal'],
          background: patternBackground, // Pattern 背景色与元素填充色相同
          stroke: patternFillColor, // Pattern 线条边框色
          color: patternFillColor, // Pattern 线条主色
          shapeRendering: 'auto',
          complement: false,
          weight: 'normal',
        },
        circlesOptions: undefined,
        pathsOptions: undefined,
      }
    }
    else if (textureType === 'circles') {
      pattern = {
        id,
        type: 'circles',
        textureType: 'circles',
        circlesOptions: {
          size: 10,
          radius: 5,
          background: patternBackground, // Pattern 背景色与元素填充色相同
          fill: patternFillColor, // Pattern 圆圈填充色
          shapeRendering: 'auto',
          complement: false,
        },
        linesOptions: undefined,
        pathsOptions: undefined,
      }
    }
    else if (textureType === 'paths') {
      pattern = {
        id,
        type: 'paths',
        textureType: 'paths',
        pathsOptions: {
          size: 10,
          d: 'squares',
          background: patternBackground, // Pattern 背景色与元素填充色相同
          fill: patternFillColor, // Pattern 路径填充色
          shapeRendering: 'auto',
          complement: false,
        },
        linesOptions: undefined,
        circlesOptions: undefined,
      }
    }
    else {
      // fallback
      pattern = {
        id,
        type: 'lines',
        textureType: 'lines',
        linesOptions: {
          size: 10,
          strokeWidth: 1,
          orientation: ['horizontal'],
          background: patternBackground, // Pattern 背景色与元素填充色相同
          stroke: patternFillColor, // Pattern 线条边框色
          color: patternFillColor, // Pattern 线条主色
          shapeRendering: 'auto',
          complement: false,
          weight: 'normal',
        },
        circlesOptions: undefined,
        pathsOptions: undefined,
      }
    }

    updateProperty('pattern', pattern)
  }

  const handlePatternOptionChange = (optionPath: string, value: unknown) => {
    // 🔧 修复：如果当前选择的是'none'，不要重新创建pattern
    if (currentPatternSelectValue === 'none') {
      return
    }

    const rawPattern = getCommonValue('pattern', selectedElements)
    let currentPattern: PatternDefinition | undefined
    if (isPatternDefinition(rawPattern)) {
      currentPattern = rawPattern
    }
    else {
      currentPattern = undefined
    }
    if (!currentPattern) {
      let patternType: 'lines' | 'circles' | 'paths' = 'lines'
      if (currentPatternSelectValue === 'lines' || currentPatternSelectValue === 'circles' || currentPatternSelectValue === 'paths') {
        patternType = currentPatternSelectValue
      }
      const newPattern = getDefaultPatternDefinition(patternType, selectedElements[0]?.id || 'default')
      const patched = patchPatternOptionBackground(newPattern, optionPath, value)
      updateProperty('pattern', patched)
      return
    }
    // patch 时 pattern.type/textureType 必须和 UI 当前选择一致
    currentPattern.type = currentPatternSelectValue
    currentPattern.textureType = currentPatternSelectValue
    const patched = patchPatternOptionBackground(currentPattern, optionPath, value)
    updateProperty('pattern', patched)
  }

  const handleDelete = () => {
    const idsToDelete = selectedElements.map(el => el.id)
    if (idsToDelete.length > 0) {
      onDeleteElement(idsToDelete)
    }
  }

  if (selectedElements.length === 0) {
    return (
      <div
        className={cn(
          'fixed right-2 z-60 w-[440px]',
          'transition-transform duration-300 ease-in-out',
          isPropertySidebarOpen ? 'translate-x-0' : 'translate-x-[calc(100%+0.5rem)]',
          'select-none',
        )}
        style={{
          top: `${actualToolbarHeight + verticalGap}px`,
          height: dynamicHeight,
        }}
        data-tutorial="property-sidebar-content"
      >
        <Card className={cn(
          'w-full h-full flex flex-col bg-background border rounded-lg',
        )}
        >
          <CardHeader className="p-3 border-b">
            <CardTitle className="text-muted-foreground text-sm">
              Select an element or elements to edit its or their properties
            </CardTitle>
          </CardHeader>
        </Card>
      </div>
    )
  }

  let commonPattern: PatternDefinition | 'mixed' | undefined
  if (selectedElements.length === 1) {
    commonPattern = selectedElements[0]?.pattern
  }
  else {
    const rawCommonPattern = getCommonValue('pattern', selectedElements)
    if (rawCommonPattern === 'mixed') {
      commonPattern = 'mixed'
    }
    else if (rawCommonPattern == null) {
      commonPattern = undefined
    }
    else if (isPatternDefinition(rawCommonPattern)) {
      commonPattern = rawCommonPattern
    }
    else {
      commonPattern = undefined
    }
  }

  // 在组件内部用 const getCommonValueLocal = (propertyPath) => getCommonValue(propertyPath, selectedElements)
  const getCommonValueLocal = (propertyPath: string) => getCommonValue(propertyPath, selectedElements)

  return (
    <div
      className={cn(
        'fixed right-2 z-60 w-[440px]',
        'transition-transform duration-300 ease-in-out',
        isPropertySidebarOpen ? 'translate-x-0' : 'translate-x-[calc(100%+0.5rem)]',
        'select-none',
      )}
      style={{
        top: `${actualToolbarHeight + verticalGap}px`,
        height: dynamicHeight,
      }}
    >
      <Card
        className={cn(
          'w-full h-full flex flex-col bg-background border rounded-lg',
        )}
        data-tutorial="property-sidebar-content"
      >
        <div className="p-3 border-b flex-shrink-0 flex flex-row items-center justify-between w-full gap-3" data-tutorial="property-sidebar-header">
          <div className="flex-grow min-w-0">
            {selectedElements.length === 1 && selectedElements[0] !== undefined
              ? (
                  <Input
                    type="text"
                    value={inputNameValue}
                    onChange={(e) => {
                      updateProperty('metadata.name', (e.target as HTMLInputElement).value)
                    }}
                    onInput={(e) => {
                      updateProperty('metadata.name', (e.target as HTMLInputElement).value)
                    }}
                    placeholder={metadataNameValue === 'mixed' ? 'Mixed Names' : `${selectedElements[0].type ?? 'Element'} Name`}
                    className="h-8 p-1 text-base font-semibold tracking-tight border-0 focus:ring-1 focus:ring-primary rounded-sm bg-transparent hover:bg-muted/50 focus:bg-muted/80"
                  />
                )
              : (
                  <div className="text-base font-semibold tracking-tight">
                    {selectedElements.length === 0 ? 'Properties Panel' : `${selectedElements.length} Elements Selected`}
                  </div>
                )}
          </div>
          {selectedElements.length > 0 && (
            <div className="flex-shrink-0 flex items-center gap-3">
              {selectedElements.length === 1 && (
                <ElementTypeBadge type={selectedElements[0].type as ElementType} />
              )}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" onClick={handleDelete} className="text-muted-foreground hover:text-destructive h-8 w-8 p-1.5">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Delete Selected</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )}
        </div>

        <Tabs value={activeTab} onValueChange={handleTabChange} className="flex flex-col flex-1 min-h-0">
          <TabsList className="grid w-full grid-cols-3 flex-shrink-0">
            <TabsTrigger value="identity" data-tutorial="identity-tab-trigger">Identity</TabsTrigger>
            <TabsTrigger value="appearance" data-tutorial="appearance-tab-trigger">Appearance</TabsTrigger>
            <TabsTrigger value="geometry" data-tutorial="geometry-tab-trigger">Geometry</TabsTrigger>
          </TabsList>

          <TabsContent value="identity" className="space-y-4 p-1 flex-1 overflow-y-auto" data-tutorial="identity-tab">

            {selectedElements.length > 0
              ? (
                  <IdentityLayerTab
                    selectedElements={selectedElements}
                    getCommonValue={getCommonValueLocal}
                    updateProperty={updateProperty}
                    layerPanelModules={layerPanelModules}
                  />
                )
              : (
                  <div className="flex flex-col items-center justify-center h-full text-center p-4">
                    <p className="text-muted-foreground text-sm">Select an element to edit its identity and layer properties.</p>
                  </div>
                )}
          </TabsContent>

          <TabsContent value="appearance" className="mt-0 p-1 flex-1 overflow-y-auto" data-tutorial="appearance-tab">
            {selectedElements.length > 0
              ? (
                  <AppearanceTab
                    selectedElements={selectedElements}
                    getCommonValue={getCommonValueLocal}
                    updateProperty={updateProperty}
                    updateProperties={updateProperties}
                    commonPattern={commonPattern}
                    isPatternActive={isPatternActive}
                    currentPatternSelectValue={currentPatternSelectValue}
                    handlePatternChange={handlePatternChange}
                    handlePatternOptionChange={handlePatternOptionChange}
                    pixelsPerMM={pixelsPerMM}
                  />
                )
              : (
                  <div className="flex flex-col items-center justify-center h-full text-center p-4">
                    <p className="text-muted-foreground text-sm">Select an element to edit its appearance properties.</p>
                  </div>
                )}
          </TabsContent>

          <TabsContent value="geometry" className="mt-0 p-1 flex-1 overflow-y-auto" data-tutorial="geometry-tab">
            {selectedElements.length > 0
              ? (
                  <GeometryTransformTab
                    key={`${selectedElements.map(el => el.id).join('-')}-geometry`}
                    selectedElements={selectedElements}
                    getCommonValue={getCommonValueLocal}
                    updateProperty={updateProperty}
                    updateProperties={updateProperties}
                    pixelsPerMM={pixelsPerMM}
                  />
                )
              : (
                  <div className="flex flex-col items-center justify-center h-full text-center p-4">
                    <p className="text-muted-foreground text-sm">Select an element to edit its geometry and transform properties.</p>
                  </div>
                )}
          </TabsContent>

        </Tabs>
      </Card>
    </div>
  )
}

export default PropertySidebar
