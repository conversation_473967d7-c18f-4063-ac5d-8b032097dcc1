import { describe, expect, it, vi } from 'vitest'
import { PerimeterBasedCostStrategy } from '@/core/compute/strategies/cost/PerimeterBasedCostStrategy'
import { CoreError } from '@/services/errors'

describe('perimeterBasedCostStrategy', () => {
  const strategy = new PerimeterBasedCostStrategy()

  it('should return the correct shape type', () => {
    expect(strategy.getElementType()).toBe('all')
  })

  it('should calculate cost based on perimeter', () => {
    const mockElement = {
      id: 'element-1',
      type: 'shape',
      compute: {
        perimeter: vi.fn().mockReturnValue(40), // 40 meters
      },
    }

    const unitCost = 30 // 30 per meter
    const result = strategy.calculateCost(mockElement, unitCost, {
      additionalCost: 100,
      discountRate: 0.1, // 折扣率应该是小数，不是百分比
      taxRate: 0.05, // 税率应该是小数，不是百分比
    })

    // 基本成本：40 * 30 = 1200
    // 额外成本：100
    // 折扣：(1200 + 100) * 0.1 = 130
    // 税前：(1200 + 100) - 130 = 1170
    // 税后：1170 * 1.05 = 1228.5
    expect(result).toBeCloseTo(1228.5)
  })

  it('should calculate cost with default options', () => {
    const mockElement = {
      id: 'element-1',
      type: 'shape',
      compute: {
        perimeter: vi.fn().mockReturnValue(40), // 40 meters
      },
    }

    const unitCost = 30 // 30 per meter
    const result = strategy.calculateCost(mockElement, unitCost)

    // 基本成本：40 * 30 = 1200
    // 无额外成本、折扣或税率
    expect(result).toBe(1200)
  })

  it('should throw error for invalid element type', () => {
    const mockElement = {
      id: 'invalid-1',
      type: 'invalid',
    }

    expect(() => strategy.calculateCost(mockElement, 30)).toThrow(CoreError)
    expect(() => strategy.calculateCost(mockElement, 30)).toThrow('PerimeterBasedCostStrategy can only calculate cost for shape or path elements')
  })

  it('should throw error if element has no perimeter calculation method', () => {
    const mockElement = {
      id: 'element-1',
      type: 'shape',
      compute: {},
    }

    expect(() => strategy.calculateCost(mockElement, 30)).toThrow(CoreError)
    expect(() => strategy.calculateCost(mockElement, 30)).toThrow('Failed to calculate perimeter for element')
  })

  it('should throw error if perimeter calculation returns invalid value', () => {
    const mockElement = {
      id: 'element-1',
      type: 'shape',
      compute: {
        perimeter: vi.fn().mockReturnValue(-10), // Negative perimeter
      },
    }

    expect(() => strategy.calculateCost(mockElement, 30)).toThrow(CoreError)
    expect(() => strategy.calculateCost(mockElement, 30)).toThrow('Invalid perimeter value')
  })

  it('should accept path elements', () => {
    const mockElement = {
      id: 'path-1',
      type: 'path',
      compute: {
        perimeter: vi.fn().mockReturnValue(40), // 40 meters
      },
    }

    const unitCost = 30 // 30 per meter
    const result = strategy.calculateCost(mockElement, unitCost)

    // 基本成本：40 * 30 = 1200
    expect(result).toBe(1200)
  })
})
