/**
 * Shape Service Interface
 *
 * Defines the interfaces for shape manipulation services.
 *
 * @module types/services/shapes
 */

import type { ServiceResult } from '../core/serviceResult'
import type {
  ElementEditRequest,
  ElementEditResult,
  ElementSelectRequest,
  ElementSelectResult,
  ShapeCreationRequest,
  ShapeCreationResult,
  ShapeDeleteRequest,
  ShapeDeleteResult,
  ShapeEditRequest,
  ShapeEditResult,
  ShapeSelectRequest,
  ShapeSelectResult,
} from './index'

import type { ShapeElement } from '@/types/core'

// Re-export request and result types so they can be imported from this module
export type {
  ElementEditRequest,
  ElementEditResult,
  ElementSelectRequest,
  ElementSelectResult,
  ShapeCreationRequest,
  ShapeCreationResult,
  ShapeDeleteRequest,
  ShapeDeleteResult,
  ShapeEditRequest,
  ShapeEditResult,
  ShapeSelectRequest,
  ShapeSelectResult,
}

/**
 * Base shape service interface
 * Common functionality for all shape services
 */
export interface ShapeService {
  /** Service identifier */
  readonly serviceId: string
}

/**
 * Shape creation service interface
 */
export interface ShapeCreationService extends ShapeService {
  /**
   * Creates a new shape
   * @param request Shape creation parameters
   * @returns Result containing the created shape or error
   */
  createShape: (request: ShapeCreationRequest) => Promise<ShapeCreationResult>

  /**
   * Duplicates an existing shape
   * @param shapeId ID of the shape to duplicate
   * @param offset Position offset for the duplicate
   * @returns Result containing the duplicated shape or error
   */
  duplicateShape: (shapeId: string, offset?: { x: number, y: number }) => Promise<ShapeCreationResult>
}

/**
 * Shape edit service interface
 */
export interface ShapeEditService extends ShapeService {
  /**
   * Edits an existing shape
   * @param request Shape edit parameters
   * @returns Result containing the edited shape or error
   */
  editShape: (request: ShapeEditRequest) => Promise<ShapeEditResult>

  /**
   * Transforms one or more shapes
   * @param shapeIds Array of shape IDs to transform
   * @param transformations Transform operations to apply
   * @returns Result containing transformed shapes or error
   */
  transformShapes: (
    shapeIds: string[],
    transformations: Record<string, unknown>
  ) => Promise<ServiceResult<ShapeElement[]>>
}

/**
 * Element edit service interface
 */
export interface ElementEditService extends ShapeService {
  /**
   * Edits an existing element
   * @param request Element edit parameters
   * @returns Result containing the edited element or error
   */
  editShape: (request: ElementEditRequest) => Promise<ElementEditResult>

  /**
   * Transforms one or more elements
   * @param shapeIds Array of element IDs to transform
   * @param transformations Transform operations to apply
   * @returns Result containing transformed elements or error
   */
  transformShapes: (
    shapeIds: string[],
    transformations: Record<string, unknown>
  ) => Promise<ServiceResult<ShapeElement[]>>
}

/**
 * Shape delete service interface
 */
export interface ShapeDeleteService extends ShapeService {
  /**
   * Deletes a shape
   * @param request Shape delete parameters
   * @returns Result indicating success or error
   */
  deleteShape: (request: ShapeDeleteRequest) => Promise<ShapeDeleteResult>

  /**
   * Deletes multiple shapes
   * @param shapeIds Array of shape IDs to delete
   * @returns Result indicating success or error
   */
  deleteShapes: (shapeIds: string[]) => Promise<ServiceResult<string[]>>
}

/**
 * Shape selection service interface
 */
export interface ShapeSelectionService extends ShapeService {
  /**
   * Selects a shape
   * @param request Shape selection parameters
   * @returns Result containing selected shape IDs or error
   */
  selectShape: (request: ShapeSelectRequest) => Promise<ShapeSelectResult>

  /**
   * Selects multiple shapes
   * @param shapeIds Array of shape IDs to select
   * @param clearExisting Whether to clear existing selection
   * @returns Result containing selected shape IDs or error
   */
  selectShapes: (shapeIds: string[], clearExisting?: boolean) => Promise<ShapeSelectResult>

  /**
   * Clears the current selection
   * @returns Result indicating success or error
   */
  clearSelection: () => Promise<ServiceResult<void>>
}

/**
 * Element selection service interface
 */
export interface ElementSelectionService extends ShapeService {
  /**
   * Selects an element
   * @param request Element selection parameters
   * @returns Result containing selected element IDs or error
   */
  selectElement: (request: ElementSelectRequest) => Promise<ElementSelectResult>

  /**
   * Selects multiple elements
   * @param elementIds Array of element IDs to select
   * @param clearExisting Whether to clear existing selection
   * @returns Result containing selected element IDs or error
   */
  selectElements: (elementIds: string[], clearExisting?: boolean) => Promise<ElementSelectResult>

  /**
   * Clears the current element selection
   * @returns Result indicating success or error
   */
  clearElementSelection: () => Promise<ServiceResult<void>>
}
