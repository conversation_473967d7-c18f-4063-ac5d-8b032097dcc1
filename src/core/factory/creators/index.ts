/**
 * Exports all shape, path, media, and design creators for use by the ElementFactory.
 *
 * @remarks
 * This index file serves as the public entry point for all creator implementations
 * within the `@/core/factory/creators` directory. It exports the abstract ShapeCreator
 * and PathCreator interfaces, which define the contracts for all creators, as well as
 * the concrete implementations for each specific shape, path, media, and design element type.
 *
 * The ElementFactory typically imports creators from this index file to register them
 * against their corresponding ElementType. This simplifies dependency management
 * and keeps the factory decoupled from the specific creator implementations.
 *
 * @module core/factory/creators/index
 */

// Export design creators
export { DesignCreator } from './design/DesignCreator'
export { DoorCreator, WindowCreator } from './design/OpeningCreator'

export { RoomCreator } from './design/RoomCreator'
export { WallCreator } from './design/WallCreator'
// Export media creators
export { default as ImageCreator } from './media/ImageCreator'

export { TextCreator } from './media/TextCreator'
export { ArcCreator } from './path/ArcCreator'
export { CubicCreator } from './path/CubicCreator'
// Export path creators
export { LineCreator } from './path/LineCreator'
export type { PathCreator } from './path/PathCreator'

export { PolylineCreator } from './path/PolylineCreator'
export { QuadraticCreator } from './path/QuadraticCreator'

export { EllipseCreator } from './shape/EllipseCreator'
export { PolygonCreator } from './shape/PolygonCreator'
// Export shape creators
export { RectangleCreator } from './shape/RectangleCreator'
// Export creator interfaces
export type { ShapeCreator } from './shape/ShapeCreator'

// Note: There might not be separate TriangleCreator or HexagonCreator classes,
// as PolygonCreator handles these cases based on the 'sides' parameter or 'type'.
// Similarly, CircleCreator might not exist if EllipseCreator handles the 'circle' type.
