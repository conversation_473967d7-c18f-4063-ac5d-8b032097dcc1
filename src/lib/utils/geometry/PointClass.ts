/**
 * Class for Representing and Manipulating 2D/3D Points.
 *
 * @remarks
 * This module provides the {@link PointClass}, a class for creating and working with
 * point objects in a Cartesian coordinate system. It includes methods for common
 * geometric calculations (like distance) and transformations (like cloning or adding points).
 * The class implements the {@link IPoint} (aliased from `PointData`) interface, ensuring
 * compatibility with the core point data structure.
 *
 * A factory function {@link createPoint} is also provided for convenient instantiation.
 *
 * @module lib/utils/geometry/PointClass
 * @see {@link IPoint} - The interface this class implements.
 * @see {@link PointData} - The core data structure for points.
 */

import type { PointData as IPoint } from '../../../types/core/element/geometry/point' // Use PointData and alias

/**
 * Represents a point in 2D or 3D space with `x`, `y`, and optional `z` coordinates.
 * Provides methods for common point operations.
 *
 * @implements IPoint
 */
export class PointClass implements IPoint {
  x: number
  y: number
  z?: number

  /**
   * Creates a new `PointClass` instance.
   * @param x - The x-coordinate of the point.
   * @param y - The y-coordinate of the point.
   * @param z - Optional: The z-coordinate of the point. Defaults to `undefined`.
   */
  constructor(x: number, y: number, z?: number) {
    this.x = x
    this.y = y
    this.z = z
  }

  /**
   * Creates and returns a new `PointClass` instance that is a deep copy of this point.
   * @returns A new {@link PointClass} instance with the same coordinates.
   */
  clone(): PointClass {
    return new PointClass(this.x, this.y, this.z)
  }

  /**
   * Checks if this point is equal to another point.
   *
   * @remarks
   * Two points are considered equal if their `x` and `y` coordinates are identical.
   * If both points have defined `z` coordinates, they must also be identical.
   * If one point has a `z` coordinate and the other does not, they are not considered equal
   * for the purpose of this strict equality check (unless both `z` are `undefined`).
   *
   * @param other - The other point (an object conforming to {@link IPoint}) to compare with.
   * @returns `true` if the points are equal, `false` otherwise. Returns `false` if `other` is null/undefined.
   */
  equals(other: IPoint): boolean {
    if (!other)
      return false
    if (this.z !== undefined && other.z !== undefined) {
      return this.x === other.x && this.y === other.y && this.z === other.z
    }
    return this.x === other.x && this.y === other.y && this.z === undefined && other.z === undefined
  }

  /**
   * Calculates the Euclidean distance from this point to another point.
   *
   * @remarks
   * If both points have `z` coordinates, the 3D distance is calculated. Otherwise, the 2D distance is calculated.
   * If one point has `z` and the other doesn't, `z` is treated as 0 for the one that has it for the 3D calculation.
   *
   * @param other - The other point (an object conforming to {@link IPoint}) to calculate the distance to.
   * @returns The Euclidean distance. Returns `NaN` if `other` is null/undefined.
   */
  distance(other: IPoint): number {
    if (!other)
      return Number.NaN

    const dx = this.x - other.x
    const dy = this.y - other.y

    if (this.z !== undefined && other.z !== undefined) {
      const dz = (this.z || 0) - (other.z || 0) // Ensure z is treated as 0 if undefined in one but not both
      return Math.sqrt(dx * dx + dy * dy + dz * dz)
    }

    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * Converts this `PointClass` instance to a plain JavaScript object representation
   * conforming to the {@link IPoint} structure.
   *
   * @returns A plain object with `x`, `y`, and optionally `z` coordinates.
   */
  toJson(): { x: number, y: number, z?: number } {
    const result: { x: number, y: number, z?: number } = {
      x: this.x,
      y: this.y,
    }

    if (this.z !== undefined) {
      result.z = this.z
    }

    return result
  }

  /**
   * Adds the coordinates of another point (or vector) to this point, returning a new `PointClass` instance.
   *
   * @remarks
   * If both this point and `other` have defined `z` coordinates, their `z` coordinates are added.
   * If only one has a `z` coordinate, that `z` coordinate is carried over to the result.
   * If neither has a `z` coordinate, the result will not have a `z` coordinate.
   *
   * @param other - The other point (an object conforming to {@link IPoint}) whose coordinates are to be added.
   * @returns A new {@link PointClass} instance representing the sum of the two points.
   */
  add(other: IPoint): PointClass {
    return new PointClass(
      this.x + other.x,
      this.y + other.y,
      this.z !== undefined && other.z !== undefined ? (this.z || 0) + (other.z || 0) : (this.z !== undefined ? this.z : other.z),
    )
  }

  /**
   * Calculates the distance to another point. This is an alias for the {@link PointClass.distance} method.
   *
   * @param other - The other point (an object conforming to {@link IPoint}).
   * @returns The Euclidean distance between this point and the `other` point.
   */
  distanceTo(other: IPoint): number {
    return this.distance(other)
  }

  /**
   * Creates a new `PointClass` instance from an array of numbers representing coordinates.
   *
   * @param coords - An array of numbers. Expected to be `[x, y]` or `[x, y, z]`.
   * @returns A new {@link PointClass} instance.
   * @throws {@link Error} if the `coords` array has fewer than 2 elements.
   */
  static fromArray(coords: number[]): PointClass {
    if (coords.length < 2) {
      throw new Error('Point coordinates array must have at least 2 elements')
    }
    return new PointClass(coords[0], coords[1], coords.length > 2 ? coords[2] : undefined)
  }

  /**
   * Creates a `PointClass` instance from a plain object conforming to the {@link IPoint} interface.
   *
   * @param obj - An object with `x`, `y`, and optional `z` properties.
   * @returns A new {@link PointClass} instance.
   */
  static fromObject(obj: IPoint): PointClass {
    return new PointClass(obj.x, obj.y, obj.z)
  }
}

/**
 * Creates a PointClass instance from coordinate values.
 *
 * Utility factory function to create a PointClass instance from an object
 * with x, y, and optional z coordinates. This is useful when you have coordinate
 * data in object form and need to convert it to a PointClass instance.
 *
 * @param coord - Object containing x and y properties, and an optional z property (must implement IPoint).
 * @returns A new PointClass instance with the specified coordinates.
 * @example
 * ```typescript
 * const coords2D = { x: 10, y: 20 };
 * const point2D = createPoint(coords2D); // Returns new PointClass(10, 20)
 *
 * const coords3D = { x: 10, y: 20, z: 30 };
 * const point3D = createPoint(coords3D); // Returns new PointClass(10, 20, 30)
 * ```
 */
export function createPoint(coord: IPoint): PointClass {
  return new PointClass(coord.x, coord.y, coord.z)
}
