// Internal Type Imports first, then value imports if needed for the same path
import type { SpecificLogicLayerPanelProps } from '@/components/layer/LayerProgress'
import type { InitialElementProperties } from '@/config/defaultElementSettings'
import type { DroppedElementData } from '@/hooks/useDragAndDrop'
import type { ShapesStoreState } from '@/store/shapesStore'
import type { CanvasDimensionChangeCallback, CanvasMouseEvent } from '@/types'
import type { PinnedAsset } from '@/types/core/assetTypes'
import type { PatternDefinition } from '@/types/core/element/elementPatternTypes'
import type Point from '@/types/core/element/geometry/point'
import type { ElementType as CoreElementType, ShapeElement as ShapeModel } from '@/types/core/elementDefinitions'
import type { MajorCategory, MinorCategory } from '@/types/core/majorMinorTypes'
import type { EventBus } from '@/types/services/events'
import type {
  ShapeCreationRequest,
  ShapeCreationResult,
  ShapeCreationService,
  ShapeDeleteService,
} from '@/types/services/shapes/shapeService'

// External Libraries (React first)
import React, { useCallback, useEffect, useRef, useState } from 'react'

// Internal Components & Logic (alphabetical by source path)
import Canvas, { CANVAS_PIXELS_PER_MM } from '@/components/canvas/Canvas'
import { SettingsPanel } from '@/components/dialogs/Settings'
import BottomAssetDrawer from '@/components/drawer/BottomAssetDrawer'
import { SpecificLogicLayerPanel } from '@/components/layer/LayerProgress'
import PropertySidebar from '@/components/property/PropertySidebar'
import Toolbar from '@/components/toolbar/Toolbar'
import { defaultPinnedBasicShapes } from '@/config/defaultPinnedAssets.config'
import { useCanvasPanZoom } from '@/hooks/canvas/useCanvasPanZoom'
import { useDragAndDrop } from '@/hooks/useDragAndDrop'
import { useElementActions } from '@/hooks/useElementActions'
import { useKeyboardShortcuts } from '@/hooks/useKeyboardShortcuts'
import { usePathDrawHandler } from '@/hooks/usePathDrawHandler'
import { usePersistentSettings } from '@/hooks/usePersistentSettings'
import useUndoRedo from '@/hooks/useUndoRedo'
import { eventBus, EventType } from '@/lib/utils/eventBus'
import { appEventBus } from '@/services/core/event-bus'
import { getService } from '@/services/core/registry'
import { getLoggerService } from '@/services/system/logging'
import { useLayerStore } from '@/store/layerStore'
import { useShapesStore, useShapesStoreEventSync } from '@/store/shapesStore'
import { predefinedElements } from '@/types/core/element/definitions/predefinedElements'
import { ElementType } from '@/types/core/elementDefinitions'
import { MajorCategory as MajorCatEnum } from '@/types/core/majorMinorTypes'
import { ServiceId } from '@/types/services/core/serviceIdentifier'
import { AppEventType } from '@/types/services/events'

/**
 * Interface defining the structure of design data for save/load operations
 */
interface DesignData {
  /** Array of shape elements in the design */
  shapes: ShapeModel[]
  /** Name of the project/design */
  projectName: string
}

/**
 * Wrapper component for the layer panel that provides logging and unique instance identification.
 *
 * This component ensures each layer panel instance has a unique render ID for debugging
 * and logging purposes, helping track component lifecycle and state changes.
 */
const DLPWrapper: React.FC<SpecificLogicLayerPanelProps & { logId: string }> = (props) => {
  const { logId, ...dlpProps } = props
  const instanceRenderId = `${logId}-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`
  return <SpecificLogicLayerPanel {...dlpProps} instanceRenderId={instanceRenderId} />
}

/**
 * Pixels per millimeter conversion constant, imported from Canvas for consistency.
 * This ensures the same scale is used throughout the application.
 */
const PIXELS_PER_MM = CANVAS_PIXELS_PER_MM

/**
 * Predefined zoom levels for the canvas.
 *
 * These levels provide discrete zoom steps that offer good coverage from
 * overview (0.1x) to detailed work (5.0x). The levels are designed to
 * provide smooth transitions and commonly used zoom ratios.
 */
const ZOOM_LEVELS = [
  0.01, // 1% - Far overview
  0.02, // 2% - Far overview
  0.03, // 3% - Far overview
  0.04, // 4% - Far overview
  0.05, // 5% - Far overview
  0.06, // 6% - Far overview
  0.07, // 7% - Far overview
  0.08, // 8% - Far overview
  0.09, // 9% - Far overview
  0.2, // 20%
  0.3, // 30%
  0.4, // 40%
  0.5, // 50% - Half size
  0.6, // 60%
  0.7, // 70%
  0.8, // 80%
  0.9, // 90%
  1.0, // 100% - Actual size
  1.1, // 110%
  1.2, // 120%
  1.3, // 130%
  1.4, // 140%
  1.5, // 150%
  1.6, // 160%
  1.7, // 170%
  1.8, // 180%
  1.9, // 190%
  2.0, // 200% - Double size
  2.5, // 250%
  3.0, // 300%
  3.5, // 350%
  4.0, // 400%
  4.5, // 450%
  5.0, // 500% - Maximum zoom
]

/**
 * Editor Layout Component
 *
 * The main application layout that orchestrates all editor functionality including
 * canvas interaction, tool management, asset handling, and UI state coordination.
 * This component serves as the central hub that connects all major subsystems.
 *
 * Features:
 * - Canvas management with zoom, pan, and drawing capabilities
 * - Multi-tool support (Select, Pan, Draw modes)
 * - Asset drag-and-drop from bottom drawer to canvas
 * - Path drawing with real-time preview and completion
 * - Element selection and manipulation
 * - Property editing through sidebar
 * - Layer management and Z-ordering
 * - Keyboard shortcuts and undo/redo
 * - Settings management and persistence
 * - Project state management
 *
 * Architecture:
 * - Uses Zustand stores for state management
 * - Event-driven communication between components
 * - Service-based architecture for business logic
 * - Hook-based composition for reusable functionality
 *
 * Layout Structure:
 * - Toolbar (top): Tool selection and project controls
 * - Canvas (center): Main drawing and interaction area
 * - Property Sidebar (right): Element property editing
 * - Layer Panel (right): Layer and progress management
 * - Bottom Asset Drawer: Asset library and quick access
 * - Dialogs: Settings, shortcuts, and other overlays
 *
 * @example
 * ```tsx
 * // Used as the main application layout
 * function App() {
 *   return (
 *     <div className="app">
 *       <EditorLayout />
 *     </div>
 *   );
 * }
 * ```
 */
const EditorLayout: React.FC = () => {
  const logger = getLoggerService() // Initialize logger instance
  useShapesStoreEventSync() // Add the hook call here

  const canvasRef = useRef<HTMLDivElement>(null)
  const editorLayoutRef = useRef<HTMLDivElement>(null) // Ref for the main editor layout div
  // Add missing refs needed for mouse operations
  const lastMouseDownCanvasPosition = useRef<Point | null>(null)
  const isDragging = useRef<boolean>(false)

  // --- Re-introduce State for Primed Asset (for click-to-add from BottomDrawer's expanded view) ---
  const [
    primedAssetTypeForCanvasClick,
    setPrimedAssetTypeForCanvasClick,
  ] = useState<ElementType | null>(null)

  // Calculate if asset is primed for adding based on primedAssetTypeForCanvasClick
  const isPrimedForAssetAdd = primedAssetTypeForCanvasClick !== null

  // --- Pan and Zoom Hook (moved to EditorLayout) ---
  const {
    pan,
    zoom,
    setPan,
    setZoom,
    handleWheel,
    startPan: startCanvasPan,
    continuePan: continueCanvasPan,
    endPan: endCanvasPan,
  }: {
    pan: Point
    zoom: number
    setPan: (newPan: Point) => void
    setZoom: (newZoom: number) => void
    handleWheel: (event: React.WheelEvent<SVGElement>) => void
    startPan: (event: React.MouseEvent<SVGElement>) => void
    continuePan: (event: React.MouseEvent<SVGElement>) => void
    endPan: () => void
  } = useCanvasPanZoom({
    initialPan: { x: 400, y: 300 }, // 初始平移位置，水平居中稍右，垂直居中
    initialZoom: 1.0, // 保持1.0的缩放比例，与Canvas.tsx保持一致
  })

  // --- UI States formerly in useCanvas ---
  // 强制设置showGrid为true，确保网格在应用启动时显示
  const [showGrid, setShowGrid] = useState<boolean>(true)
  const gridSize = 50 // This was a constant in useCanvas

  // --- Zustand Layer Store Hook (Individual Selectors) ---
  const layerModulesFromStore = useLayerStore(state => state.modules)
  const storeCurrentModuleId = useLayerStore(state => state.currentModuleId)
  const storeCurrentStepId = useLayerStore(state => state.currentStepId)
  const storeCurrentZLevelId = useLayerStore(state => state.currentZLevelId)
  // const selectLayerIdentifiers = useLayerStore(state => state.selectLayerIdentifiers) // REMOVE selectLayerIdentifiers
  // --- End Zustand Layer Store Hook ---

  // --- Zustand Shapes Store Hook ---
  const shapes: ShapeModel[] = useShapesStore(
    (state: ShapesStoreState) => state.shapes,
  ) // Simplified selector with explicit types

  // 使用useUndoRedo钩子获取稳定的引用
  const { undo: undoShapes, redo: redoShapes, canUndo: canUndoShapes, canRedo: canRedoShapes } = useUndoRedo()

  // --- Project Name State (Moved inside component) ---
  const [projectName, setProjectName] = useState<string>('New Design Plan (Editable)')

  // --- Settings Hook ---
  const {
    effectiveSettings,
    saveSettings,
    getSettingsForType,
    resetAllSettings,
  } = usePersistentSettings()
  // --- End Settings Hook ---

  // Element selection hook (integrates with Zustand store via useShapesStore)
  const {
    selectedElementIds,
    clearElementSelection,
    selectElement,
    selectElements,
    deleteShapes,
    updateElements,
  } = useElementActions()

  // Compute selectedElements directly from shapes and selectedElementIds
  const selectedElements: ShapeModel[] = shapes.filter((el: ShapeModel) =>
    selectedElementIds.includes(el.id),
  )

  // Drag and drop hook
  const dragAndDrop = useDragAndDrop()

  // --- New State for UI Toggles ---
  const [isShortcutsGuideOpen, setIsShortcutsGuideOpen] = useState(false)
  const [isSettingsPanelOpen, setIsSettingsPanelOpen] = useState(false)

  // --- New State for Asset Drawer Sheet ---
  const [isAssetSheetOpen, setIsAssetSheetOpen] = useState(false)
  // --- End New State for Asset Drawer Sheet ---

  // --- State for Canvas Dimensions (for Toolbar) ---
  const [formattedCanvasWidth, setFormattedCanvasWidth] = useState<string>('0px')
  const [formattedCanvasHeight, setFormattedCanvasHeight] = useState<string>('0px')

  // --- State for Current Tool ---
  // const [currentTool, setCurrentTool] = useState<EditorTool>('SELECT') // Old, EditorTool undefined
  const [currentTool, setCurrentTool] = useState<'SELECT' | 'PAN' | 'DRAW'>('SELECT') // Added DRAW tool
  // TODO: Integrate setCurrentTool with Toolbar actions

  // 使用事件总线已在顶部导入

  // Helper function to get descriptive names for path elements
  const getPathElementName = (elementType: ElementType): string => {
    switch (elementType) {
      case ElementType.LINE:
        return 'Line'
      case ElementType.POLYLINE:
        return 'Polyline'
      case ElementType.ARC:
        return 'Arc'
      case ElementType.QUADRATIC:
        return 'Quadratic Curve'
      case ElementType.CUBIC:
        return 'Cubic Curve'
      case ElementType.RECTANGLE:
        return 'Rectangle'
      case ElementType.SQUARE:
        return 'Square'
      case ElementType.ELLIPSE:
        return 'Ellipse'
      case ElementType.CIRCLE:
        return 'Circle'
      case ElementType.POLYGON:
        return 'Polygon'
      case ElementType.TRIANGLE:
        return 'Triangle'
      case ElementType.QUADRILATERAL:
        return 'Quadrilateral'
      case ElementType.PENTAGON:
        return 'Pentagon'
      case ElementType.HEXAGON:
        return 'Hexagon'
      case ElementType.HEPTAGON:
        return 'Heptagon'
      case ElementType.OCTAGON:
        return 'Octagon'
      case ElementType.NONAGON:
        return 'Nonagon'
      case ElementType.DECAGON:
        return 'Decagon'
      case ElementType.TEXT_LABEL:
        return 'Text Label'
      case ElementType.WALL:
        return 'Wall'
      case ElementType.DOOR:
        return 'Door'
      case ElementType.WINDOW:
        return 'Window'
      case ElementType.FURNITURE:
        return 'Furniture'
      case ElementType.FIXTURE:
        return 'Fixture'
      case ElementType.ROOM:
        return 'Room'
      case ElementType.LIGHT:
        return 'Light'
      case ElementType.FLOOR_AREA:
        return 'Floor Area'
      case ElementType.HANDRAIL:
        return 'Handrail'
      case ElementType.ELECTRICAL_OUTLET:
        return 'Electrical Outlet'
      case ElementType.ROOM_BOUNDARY:
        return 'Room Boundary'
      case ElementType.APPLIANCE:
        return 'Appliance'
      case ElementType.TEXT:
        return 'Text'
      case ElementType.IMAGE:
        return 'Image'
      case ElementType.GROUP:
        return 'Group'
      case ElementType.OPENING:
        return 'Opening'
      case ElementType.WALL_PAINT:
        return 'Wall Paint'
      case ElementType.WALL_PAPER:
        return 'Wall Paper'
      default:
        return 'Unknown Element'
    }
  }

  // Adapter function for the Canvas onElementAdd prop (e.g., for click-to-add primed asset)
  interface PrimedElementInfo {
    type: ElementType
    name?: string // Optional name for the element
    properties?: Partial<InitialElementProperties> // Default properties to apply
    majorCategory?: MajorCategory
    minorCategory?: MinorCategory
    zLevelId?: string
  }
  const onElementAddCanvas_Correct = useCallback(
    async (worldPosition: Point, activePrimedElement: PrimedElementInfo | null | undefined) => {
      // console.log('[EditorLayout] onElementAddCanvas_Correct triggered with:', worldPosition, activePrimedElement) // Added log

      if (activePrimedElement === null || activePrimedElement === undefined) {
        console.error('Attempted to add element via onElementAddCanvas_Correct, but activePrimedElement is null or undefined.')
        return
      }

      const shapeCreationService = getService<ShapeCreationService>(ServiceId.ElementCreationService as ServiceId)
      if (shapeCreationService == null) {
        console.error('ShapeCreationService not found for onElementAddCanvas_Correct')
        return
      }

      const defaultsForType = getSettingsForType(activePrimedElement.type)
      // Using type assertion for compatibility with InitialElementProperties

      const finalProperties: Partial<InitialElementProperties> = {
        ...defaultsForType,
        ...(activePrimedElement.properties || {}),
        name: activePrimedElement.name ?? 'Unnamed Primed Element',
        majorCategory: activePrimedElement.majorCategory ?? storeCurrentModuleId,
        minorCategory: activePrimedElement.minorCategory ?? storeCurrentStepId,
        zLevelId: activePrimedElement.zLevelId ?? storeCurrentZLevelId,
      } as Partial<InitialElementProperties> // Type assertion

      const creationRequest: ShapeCreationRequest = {
        elementType: activePrimedElement.type,
        position: { x: worldPosition.x, y: worldPosition.y },
        properties: finalProperties, // Now correctly typed
      }

      try {
        const result = await shapeCreationService.createShape(creationRequest)
        // console.log('[EditorLayout] onElementAddCanvas_Correct - shapeCreationService.createShape result:', result) // Added log
        if (result.success && result.data) {
          // console.log('[EditorLayout] Element added via onElementAddCanvas_Correct:', result.data) // Changed log level
          setPrimedAssetTypeForCanvasClick(null) // Clear the primed asset AFTER successful addition
        }
        else {
          console.error('[EditorLayout] Failed to add element via onElementAddCanvas_Correct:', result.error)
        }
      }
      catch (error) {
        console.error('[EditorLayout] Error during click-to-add shape creation:', error)
      }
    },
    [getSettingsForType, storeCurrentModuleId, storeCurrentStepId, storeCurrentZLevelId, setPrimedAssetTypeForCanvasClick], // Added setPrimedAssetTypeForCanvasClick
  )

  // 使用路径绘制处理器
  const pathDrawHandler = usePathDrawHandler({
    onDrawStart: (_pathType, _point) => {
      // Path drawing started
    },
    onDrawUpdate: (_startPoint: Point, _currentPoint: Point) => {
      // Path drawing updated
    },

    onDrawComplete: (points: Point[]) => {
      // 圆弧约束逻辑已删除
      const finalPoints = points

      // 确保有足够的点来创建路径元素
      if (finalPoints.length < 2) {
        return
      }

      // 获取当前的路径类型
      const currentPathType = pathDrawHandler.pathType
      if (currentPathType == null) {
        return
      }

      try {
        // 创建路径元素
        // 圆弧逻辑已删除，所有路径使用第一个点作为起点
        const elementPosition = finalPoints[0]

        void onElementAddCanvas_Correct(
          elementPosition,
          {
            type: currentPathType,
            name: getPathElementName(currentPathType),
            properties: {
              // 根据路径类型设置不同的属性
              ...(currentPathType === ElementType.LINE
                ? {
                    start: { x: 0, y: 0, z: 0 },
                    end: {
                      x: finalPoints[1].x - finalPoints[0].x,
                      y: finalPoints[1].y - finalPoints[0].y,
                      z: 0,
                    },
                    stroke: getSettingsForType(currentPathType).stroke ?? '#333333',
                    strokeWidth: getSettingsForType(currentPathType).strokeWidth ?? 2,
                    opacity: getSettingsForType(currentPathType).opacity ?? 1,
                    costUnitPrice: 1,
                    costMultiplierOrCount: 0,
                    costBasis: 'unit',
                  }
                : {}),
              ...(currentPathType === ElementType.POLYLINE
                ? {
                    points: finalPoints.map(point => ({
                      x: point.x - finalPoints[0].x,
                      y: point.y - finalPoints[0].y,
                      z: 0,
                    })),
                    stroke: getSettingsForType(currentPathType).stroke ?? '#333333',
                    strokeWidth: getSettingsForType(currentPathType).strokeWidth ?? 2,
                    opacity: getSettingsForType(currentPathType).opacity ?? 1,
                    costUnitPrice: 1,
                    costMultiplierOrCount: 0,
                    costBasis: 'unit',
                  }
                : {}),
              ...(currentPathType === ElementType.ARC && finalPoints.length >= 3
                ? {
                    // 圆弧需要三个点：圆心、起点、终点
                    // 第一个点是圆心，第二个点是起点，第三个点是终点
                    radius: Math.sqrt(
                      (finalPoints[1].x - finalPoints[0].x) ** 2
                      + (finalPoints[1].y - finalPoints[0].y) ** 2,
                    ),
                    startAngle: Math.atan2(
                      finalPoints[1].y - finalPoints[0].y,
                      finalPoints[1].x - finalPoints[0].x,
                    ) * 180 / Math.PI,
                    endAngle: Math.atan2(
                      finalPoints[2].y - finalPoints[0].y,
                      finalPoints[2].x - finalPoints[0].x,
                    ) * 180 / Math.PI,
                    counterClockwise: false,
                    closed: false,
                    stroke: getSettingsForType(currentPathType).stroke ?? '#333333',
                    strokeWidth: getSettingsForType(currentPathType).strokeWidth ?? 2,
                    opacity: getSettingsForType(currentPathType).opacity ?? 1,
                    costUnitPrice: 1,
                    costMultiplierOrCount: 0,
                    costBasis: 'unit',
                  }
                : {}),
              ...(currentPathType === ElementType.QUADRATIC
                ? {
                    // Quadratic需要三个点：起点、控制点、终点
                    start: { x: 0, y: 0, z: 0 },
                    control: finalPoints.length >= 2
                      ? {
                          x: finalPoints[1].x - finalPoints[0].x,
                          y: finalPoints[1].y - finalPoints[0].y,
                          z: 0,
                        }
                      : { x: 25, y: -25, z: 0 },
                    end: finalPoints.length >= 3
                      ? {
                          x: finalPoints[2].x - finalPoints[0].x,
                          y: finalPoints[2].y - finalPoints[0].y,
                          z: 0,
                        }
                      : { x: 50, y: 0, z: 0 },
                    stroke: getSettingsForType(currentPathType).stroke ?? '#333333',
                    strokeWidth: getSettingsForType(currentPathType).strokeWidth ?? 2,
                    opacity: getSettingsForType(currentPathType).opacity ?? 1,
                    costUnitPrice: 1,
                    costMultiplierOrCount: 0,
                    costBasis: 'unit',
                  }
                : {}),
              ...(currentPathType === ElementType.CUBIC
                ? {
                    // Cubic需要三个点：起点、第一个控制点、终点（第二个控制点自动计算）
                    start: { x: 0, y: 0, z: 0 },
                    control1: finalPoints.length >= 2
                      ? {
                          x: finalPoints[1].x - finalPoints[0].x,
                          y: finalPoints[1].y - finalPoints[0].y,
                          z: 0,
                        }
                      : { x: 25, y: -25, z: 0 },
                    control2: finalPoints.length >= 3
                      ? {
                          // 自动计算第二个控制点（与第一个控制点关于起点-终点连线对称）
                          x: finalPoints.length >= 3
                            ? ((finalPoints[0].x + finalPoints[2].x) / 2 + ((finalPoints[0].x + finalPoints[2].x) / 2 - finalPoints[1].x)) - finalPoints[0].x
                            : 25,
                          y: finalPoints.length >= 3
                            ? ((finalPoints[0].y + finalPoints[2].y) / 2 + ((finalPoints[0].y + finalPoints[2].y) / 2 - finalPoints[1].y)) - finalPoints[0].y
                            : 25,
                          z: 0,
                        }
                      : { x: 25, y: 25, z: 0 },
                    end: finalPoints.length >= 3
                      ? {
                          x: finalPoints[2].x - finalPoints[0].x,
                          y: finalPoints[2].y - finalPoints[0].y,
                          z: 0,
                        }
                      : { x: 50, y: 0, z: 0 },
                    stroke: getSettingsForType(currentPathType).stroke ?? '#333333',
                    strokeWidth: getSettingsForType(currentPathType).strokeWidth ?? 2,
                    opacity: getSettingsForType(currentPathType).opacity ?? 1,
                    costUnitPrice: 1,
                    costMultiplierOrCount: 0,
                    costBasis: 'unit',
                  }
                : {}),
            },
          },
        )
      }
      catch (error) {
        console.error('[EditorLayout] Error creating path element:', error)
      }

      // 绘制完成后重置工具和预选资产
      setCurrentTool('SELECT')
      setPrimedAssetTypeForCanvasClick(null)

      // 发布路径绘制完成事件到简单事件总线
      eventBus.publish(EventType.PATH_DRAWING_COMPLETED, {
        pathType: currentPathType,
        points: finalPoints,
      })
    },
    onDrawCancel: () => {
      // 绘制取消后重置工具和预选资产
      setCurrentTool('SELECT')
      setPrimedAssetTypeForCanvasClick(null)

      // 发布路径绘制取消事件
      eventBus.publish(EventType.PATH_DRAWING_COMPLETED, {
        pathType: primedAssetTypeForCanvasClick,
        canceled: true,
      })
    },
  })

  const handleCanvasDimensionChange: CanvasDimensionChangeCallback = useCallback((width: string, height: string) => {
    setFormattedCanvasWidth(width)
    setFormattedCanvasHeight(height)
  }, [])

  // NEW: Define handleCanvasWorldMouseMove
  const handleCanvasWorldMouseMove = useCallback((_worldPoint: Point) => {
    // Placeholder for now, can be used to display coordinates or other features
  }, [])

  // --- Toggle Functions ---
  const toggleShortcutsGuide = useCallback(() => {
    setIsShortcutsGuideOpen(prev => !prev)
  }, [])

  const toggleSettingsPanel = useCallback(() => {
    setIsSettingsPanelOpen(prev => !prev)
  }, [])

  // 保存showGrid状态到localStorage
  useEffect(() => {
    localStorage.setItem('RenoPilot.showGrid', showGrid.toString())
  }, [showGrid])

  // --- New Toggle Functions for Grid and PanMode ---
  const handleToggleGrid = useCallback(() => {
    setShowGrid(prev => !prev)
  }, [])

  const handleTogglePanMode = useCallback(() => {
    setCurrentTool(prev => prev === 'SELECT' ? 'PAN' : 'SELECT')
  }, [setCurrentTool])

  // --- New Handler for Drag Start from BottomAssetDrawer ---
  const handleBottomDrawerAssetDragStart = useCallback((
    _elementType: ElementType,
    _properties: InitialElementProperties,
  ) => {
    // console.log(`[EditorLayout] Asset drag started from Bottom Drawer: ${elementType}`, `Properties:`, JSON.stringify(properties),
    // ) // Line 188: Commented ou
    // The BottomAssetDrawer already sets event.dataTransfer.setData in its own handleDragStart.
    // This function is primarily for EditorLayout to react if needed (e.g., closing other UI).
    // For now, it can be simple.
  }, [])
  // --- End New Handler ---

  // --- New Handler for Asset Selection from Expanded Bottom Drawer ---
  const handleAssetSelectFromExpandedDrawer = useCallback((elementType: ElementType) => {
    // 特殊处理Polyline：如果已经在绘制Polyline，则完成绘制
    if (elementType === ElementType.POLYLINE && pathDrawHandler.isDrawing && pathDrawHandler.pathType === ElementType.POLYLINE) {
      if (typeof pathDrawHandler.finishPolyline === 'function') {
        (pathDrawHandler.finishPolyline as () => void)()
      }
      setPrimedAssetTypeForCanvasClick(null)
      setCurrentTool('SELECT')
      return
    }

    setPrimedAssetTypeForCanvasClick(elementType)
    setIsAssetSheetOpen(false) // Close the sheet

    // 如果是路径类型元素，切换到绘制模式
    if (
      elementType === ElementType.LINE
      || elementType === ElementType.POLYLINE
      || elementType === ElementType.ARC
      || elementType === ElementType.QUADRATIC
      || elementType === ElementType.CUBIC
    ) {
      setCurrentTool('DRAW')
    }
  }, [setPrimedAssetTypeForCanvasClick, setIsAssetSheetOpen, setCurrentTool, pathDrawHandler])
  // --- End New Handler ---

  // Create a new canvas
  const handleNewCanvas = useCallback(() => {
    void deleteShapes(shapes.map((s: ShapeModel) => s.id))
    setPan({ x: 300, y: 300 })
    setZoom(1)
    setCurrentTool('SELECT')
    setProjectName('New Design Plan (Editable)')
    // console.log('[EditorLayout] New canvas setup complete.'); // Commented out as per problems.md line 316 guideline
  }, [shapes, setPan, setZoom, setCurrentTool, setProjectName, deleteShapes])

  const handleSaveCanvas = useCallback(() => {
    const currentShapes: ShapeModel[] = useShapesStore.getState().shapes
    const currentProjectName = projectName // projectName is from useState in EditorLayout

    const designDataToSave: DesignData = {
      shapes: currentShapes,
      projectName: currentProjectName,
    }

    const eventBus = getService<EventBus>(ServiceId.EventBus as ServiceId)
    if (eventBus != null) {
      eventBus.publish({
        type: AppEventType.StorageSaveRequest, // Ensure this matches the imported AppEventType
        payload: {
          designId: 'current_design', // Or a more dynamic ID
          data: designDataToSave,
        },
      })
    }
    else {
      console.error('[EditorLayout] EventBus not available for save operation.')
    }
  }, [projectName]) // Add shapes and projectName to dependencies if they were props/state from other hooks

  // NEW: Handler for elements dropped via useDragAndDrop hook
  const handleElementDropFromHook = useCallback(async (data: DroppedElementData) => {
    // console.log('[EditorLayout] handleElementDropFromHook called with data:', data) // ADDED LOG
    const { elementType, position, properties: droppedProperties, id: assetId, name, elementSpecificData } = data

    const shapeCreationService = getService<ShapeCreationService>(ServiceId.ElementCreationService as ServiceId)
    if (shapeCreationService == null) {
      console.error('[EditorLayout] handleElementDropFromHook: ShapeCreationService not found') // ENHANCED LOG
      return
    }

    const defaultsForType = getSettingsForType(elementType)
    // Using a type assertion here is better than eslint-disable, assuming the structure is generally compatible
    const finalProperties: Partial<ShapeModel['properties']> = {
      ...defaultsForType, // Spread system defaults for the type
      ...droppedProperties, // Overlay properties from the drag operation (e.g., dimensions from predefined)
      name: name ?? 'Unnamed Element',
      majorCategory: storeCurrentModuleId ?? undefined,
      minorCategory: storeCurrentStepId ?? undefined,
      zLevelId: storeCurrentZLevelId ?? undefined,
      ...(elementSpecificData ?? {}), // Overlay elementSpecificData
    }

    if (assetId !== null && assetId !== undefined && assetId !== '') {
      // Safely assign predefinedId using Object.assign to avoid type assertion
      Object.assign(finalProperties, { predefinedId: assetId })
    }

    // Calculate offset to center the element
    let offsetX = 0
    let offsetY = 0

    // Given ShapeRenderer's logic:
    // - Each shape group is translated by element.position.
    // - Rectangles are drawn with x = -width/2, y = -height/2 within the group.
    // - Circles/Ellipses are drawn with cx = 0, cy = 0 within the group.
    // This means element.position IS the intended center for these shapes.
    // Therefore, offsetX and offsetY should be 0 for these types if we want the dropPosition to be the center.

    // Add cases for other element types as required by the linter, even if the logic is the same
    switch (elementType) {
      case ElementType.RECTANGLE:
      case ElementType.SQUARE:
      case ElementType.IMAGE:
      case ElementType.CIRCLE:
      case ElementType.ELLIPSE:
      case ElementType.POLYGON: // Added for exhaustiveness
      case ElementType.TRIANGLE: // Added for exhaustiveness
      case ElementType.QUADRILATERAL: // Added for exhaustiveness
      case ElementType.PENTAGON: // Added for exhaustiveness
      case ElementType.HEXAGON: // Added for exhaustiveness
      case ElementType.HEPTAGON: // Added for exhaustiveness
      case ElementType.OCTAGON: // Added for exhaustiveness
      case ElementType.NONAGON: // Added for exhaustiveness
      case ElementType.DECAGON: // Added for exhaustiveness
      case ElementType.LINE: // Added for exhaustiveness
      case ElementType.POLYLINE: // Added for exhaustiveness
      case ElementType.ARC: // Added for exhaustiveness
      case ElementType.QUADRATIC: // Added for exhaustiveness
      case ElementType.CUBIC: // Added for exhaustiveness
      case ElementType.TEXT_LABEL: // Added for exhaustiveness
      case ElementType.WALL: // Added for exhaustiveness
      case ElementType.DOOR: // Added for exhaustiveness
      case ElementType.WINDOW: // Added for exhaustiveness
      case ElementType.FURNITURE: // Added for exhaustiveness
      case ElementType.FIXTURE: // Added for exhaustiveness
      case ElementType.ROOM: // Added for exhaustiveness
      case ElementType.LIGHT: // Added for exhaustiveness
      case ElementType.FLOOR_AREA: // Added for exhaustiveness
      case ElementType.HANDRAIL: // Added for exhaustiveness
      case ElementType.ELECTRICAL_OUTLET: // Added for exhaustiveness
      case ElementType.ROOM_BOUNDARY: // Added for exhaustiveness
      case ElementType.APPLIANCE: // Added for exhaustiveness
      case ElementType.TEXT: // Added for exhaustiveness
      case ElementType.OPENING: // Added for exhaustiveness
      case ElementType.WALL_PAINT: // Added for exhaustiveness
      case ElementType.WALL_PAPER: // Added for exhaustiveness
      case ElementType.GROUP: // Added for exhaustiveness
        // No offset needed as element.position will be the center or anchor point.
        offsetX = 0
        offsetY = 0
        break
      default:
        logger.debug(
          `[EditorLayout] handleElementDropFromHook: No specific centering logic for ${String(elementType)}, using raw drop position as element.position.`,
        )
        // offsetX = 0; // already 0
        // offsetY = 0; // already 0
        break
    }

    const adjustedPosition: Point = {
      x: position.x - offsetX, // Will be position.x for centered types
      y: position.y - offsetY, // Will be position.y for centered types
    }

    const creationRequest: ShapeCreationRequest = {
      elementType, // This is the CoreElementType
      position: adjustedPosition, // Use the adjusted position for centering
      // The `properties` field in ShapeCreationRequest is Partial<InitialElementProperties> | undefined.
      // We constructed `finalProperties` as Partial<ShapeElement['properties']>.
      // Need to align this type. Let's assume ShapeElement['properties'] extends InitialElementProperties or is compatible.
      // Using Object.assign for safer type handling
      properties: Object.assign({}, finalProperties) as InitialElementProperties,
    }
    // console.log('[EditorLayout] handleElementDropFromHook: Creation Request (centered):', creationRequest)

    try {
      const result: ShapeCreationResult = await shapeCreationService.createShape(creationRequest)
      // console.log('[EditorLayout] handleElementDropFromHook: Shape creation service result:', result) // ADDED LOG
      if (result.success && result.data) {
        // console.log('[EditorLayout] handleElementDropFromHook: Shape created successfully:', result.data) // UNCOMMENTED & ENHANCED LOG
      }
      else {
        console.error('[EditorLayout] handleElementDropFromHook: Shape creation failed. Error:', result.error, 'Full Result:', result) // ENHANCED LOG
      }
    }
    catch (error) {
      console.error('[EditorLayout] Error during shapeCreationService.createShape:', error) // ENHANCED LOG
    }
  }, [getSettingsForType, storeCurrentModuleId, storeCurrentStepId, storeCurrentZLevelId, logger]) // Added logger to dependencies

  // Delete selected elements
  const handleDeleteElements = useCallback(() => {
    if (selectedElementIds.length === 0) {
      return // Added curly braces for single statement if block
    }

    // 获取ShapeDeleteService服务
    const shapeDeleteService = getService<ShapeDeleteService>(ServiceId.ElementDeleteService as ServiceId)
    if (shapeDeleteService == null) {
      console.error('[EditorLayout] ShapeDeleteService not found for handleDeleteElements')
      return
    }

    try {
      // 调用服务的deleteShapes方法删除选中的元素
      void shapeDeleteService.deleteShapes(selectedElementIds)
        .then((result) => {
          if (result.success) {
            // console.log('[EditorLayout] Elements deleted:', selectedElementIds) // Line 416: Commented ou

            // 清除选择状态
            void clearElementSelection()
          }
          else {
            console.error('[EditorLayout] Error deleting shapes:', result.error)
          }
        })
        .catch((error) => {
          console.error('[EditorLayout] Error in deleteShapes promise:', error)
        })
    }
    catch (error) {
      console.error('[EditorLayout] Error deleting shapes:', error)
    }
  }, [selectedElementIds, clearElementSelection])

  // Refactored handleUpdateElements
  // This function receives updates in the format:
  // Array<{ id: string, properties: Partial<ShapeElement>, majorCategory?: string, minorCategory?: string, zLevelId?: string, pattern?: PatternDefinition }>
  // where the 'properties' object contains the *correctly nested* properties to be updated
  // (e.g., { properties: { width: 100 }, position: { x: 50 } }).
  // This structure is assumed to be constructed by PropertySidebar.updateProperty now.
  const handleUpdateElements = useCallback((updates: Array<{ id: string, properties: Partial<ShapeModel>, majorCategory?: string, minorCategory?: string, zLevelId?: string, pattern?: PatternDefinition }>) => {
    // Iterate through each update item and call updateElements
    updates.forEach((update) => {
      try {
        const { id, properties, majorCategory, minorCategory, zLevelId, pattern } = update

        // 创建transformations对象，包含properties和顶层图层属性
        const transformations: Record<string, unknown> = { ...properties }

        // 特殊处理图层属性，确保它们同时存在于顶层和properties对象中
        if (majorCategory !== undefined) {
          transformations.majorCategory = majorCategory
        }

        if (minorCategory !== undefined) {
          transformations.minorCategory = minorCategory
        }

        if (zLevelId !== undefined) {
          transformations.zLevelId = zLevelId
        }

        // 特殊处理 Pattern 属性，确保它被正确传递
        if (pattern !== undefined) {
          transformations.pattern = pattern
        }

        // 调用updateElements，传递id数组和transformations对象
        void updateElements([id], transformations)
      }
      catch (err) {
        console.error('[EditorLayout] Error updating element', update.id, err)
      }
    })
  }, [updateElements])

  // 选择相关的快捷键处理
  const handleSelectAll = useCallback(() => {
    if (shapes.length > 0) {
      void selectElements(shapes.map((s: ShapeModel) => s.id))
    }
  }, [shapes, selectElements])

  const handleClearSelection = useCallback(() => {
    void clearElementSelection()
  }, [clearElementSelection])

  // 使用统一的快捷键系统
  useKeyboardShortcuts({
    // 编辑操作
    onUndo: undoShapes,
    onRedo: redoShapes,
    onDelete: handleDeleteElements,

    // 视图操作
    onZoomIn: () => {
      const newZoom = Math.min(zoom + 0.1, 5)
      setZoom(newZoom)
    },
    onZoomOut: () => {
      const newZoom = Math.max(zoom - 0.1, 0.1)
      setZoom(newZoom)
    },
    onResetZoom: () => setZoom(1),
    onTogglePanMode: handleTogglePanMode,

    // 选择操作
    onSelectAll: handleSelectAll,
    onClearSelection: handleClearSelection,

    // UI 切换
    onToggleGrid: handleToggleGrid,
  })

  // --- Sidebar Visibility State ---
  const [isLayerPanelOpen, setIsLayerPanelOpen] = useState(() => {
    // 默认根据屏幕尺寸决定初始状态
    return typeof window !== 'undefined' ? window.innerWidth >= 1024 : true
  })
  const [isPropertySidebarOpen, setIsPropertySidebarOpen] = useState(() => {
    // 默认根据屏幕尺寸决定初始状态
    return typeof window !== 'undefined' ? window.innerWidth >= 1024 : true
  })
  const [userToggledPanels, setUserToggledPanels] = useState({ layer: false, property: false }) // Track user manual toggles

  // --- Tutorial panel management ---
  useEffect(() => {
    const handleEnsurePanelsOpen = () => {
      setIsLayerPanelOpen(true)
      setIsPropertySidebarOpen(true)
    }

    appEventBus.on('tutorial:ensure-panels-open', handleEnsurePanelsOpen)

    return () => {
      appEventBus.off('tutorial:ensure-panels-open', handleEnsurePanelsOpen)
    }
  }, [])

  // --- Responsive sidebar management ---
  const handleLayerPanelChange = useCallback((open: boolean) => {
    setIsLayerPanelOpen(open)
  }, [])

  const handlePropertySidebarChange = useCallback((open: boolean) => {
    setIsPropertySidebarOpen(open)
  }, [])

  const handleResize = useCallback(() => {
    const width = window.innerWidth

    // Auto-close sidebars on small screens (below 1024px - lg breakpoint)
    // But only if user hasn't manually toggled them
    if (width < 1024) {
      if (!userToggledPanels.layer) {
        handleLayerPanelChange(false)
      }
      if (!userToggledPanels.property) {
        handlePropertySidebarChange(false)
      }
    }
    else {
      // Auto-open sidebars on large screens if user hasn't manually closed them
      if (!userToggledPanels.layer) {
        handleLayerPanelChange(true)
      }
      if (!userToggledPanels.property) {
        handlePropertySidebarChange(true)
      }
    }
  }, [userToggledPanels, handleLayerPanelChange, handlePropertySidebarChange])

  const handleMutualExclusion = useCallback(() => {
    const width = window.innerWidth
    const isVerySmallScreen = width < 768

    if (isVerySmallScreen && isLayerPanelOpen && isPropertySidebarOpen) {
      // Keep the layer panel open and close the property panel by default
      handlePropertySidebarChange(false)
    }
  }, [isLayerPanelOpen, isPropertySidebarOpen, handlePropertySidebarChange])

  useEffect(() => {
    // Initial check
    handleResize()

    // Add event listener
    window.addEventListener('resize', handleResize)

    // Cleanup
    return () => window.removeEventListener('resize', handleResize)
  }, [handleResize])

  // Separate effect for handling mutual exclusion on very small screens
  useEffect(() => {
    handleMutualExclusion()
  }, [handleMutualExclusion])

  // --- Toggle functions for sidebars ---
  const toggleLayerPanel = useCallback(() => {
    const isVerySmallScreen = window.innerWidth < 768 // md breakpoint

    setIsLayerPanelOpen((prev) => {
      const newValue = !prev

      // On very small screens, if opening layer panel, always close property panel
      if (isVerySmallScreen && newValue) {
        // Use a timeout to avoid direct setState call in setState
        setTimeout(() => setIsPropertySidebarOpen(false), 0)
      }

      return newValue
    })
    setUserToggledPanels(prev => ({ ...prev, layer: true }))
  }, [])

  const togglePropertySidebar = useCallback(() => {
    const isVerySmallScreen = window.innerWidth < 768 // md breakpoint

    setIsPropertySidebarOpen((prev) => {
      const newValue = !prev

      // On very small screens, if opening property panel, always close layer panel
      if (isVerySmallScreen && newValue) {
        // Use a timeout to avoid direct setState call in setState
        setTimeout(() => setIsLayerPanelOpen(false), 0)
      }

      return newValue
    })
    setUserToggledPanels(prev => ({ ...prev, property: true }))
  }, [])

  // --- Pinning State and Logic --- RESTORED BLOCK
  const [pinnedAssets, setPinnedAssets] = useState<PinnedAsset[]>(() => {
    let initialPins: PinnedAsset[] = []
    try {
      const storedPinnedAssets = localStorage.getItem('RenoPilot.pinnedAssets')
      if (storedPinnedAssets != null) {
        const parsedAssets = JSON.parse(storedPinnedAssets) as PinnedAsset[]
        initialPins = parsedAssets.map((pa) => {
          if (pa.isSpecific && pa.assetName != null && pa.assetName !== '') {
            const foundElement = predefinedElements.find(pe => pe.id === pa.assetName)
            if (foundElement) {
              // Add type information from the found predefined element
              return { ...pa, type: ElementType.IMAGE, predefinedElementData: foundElement }
            }
          }
          // Ensure type is present even if not specific
          return { ...pa, type: pa.type }
        })
      }
      else {
        initialPins = defaultPinnedBasicShapes.map((da) => {
          if (da.isSpecific && da.assetName != null && da.assetName !== '') {
            const foundElement = predefinedElements.find(pe => pe.id === da.assetName)
            if (foundElement) {
              // Ensure type is explicitly included
              return { ...da, type: da.type, predefinedElementData: foundElement } as PinnedAsset
            }
          }
          // Ensure type is explicitly included even if not specific
          return { ...da, type: da.type } as PinnedAsset
        })
      }
    }
    catch (error) {
      console.error('Error loading pinned assets:', error) // Keep this useful error log
      initialPins = defaultPinnedBasicShapes.map((da) => {
        if (da.isSpecific && da.assetName != null && da.assetName !== '') {
          const foundElement = predefinedElements.find(pe => pe.id === da.assetName)
          if (foundElement) {
            return { ...da, type: da.type, predefinedElementData: foundElement } as PinnedAsset
          }
        }
        return { ...da, type: da.type } as PinnedAsset
      })
    }
    return initialPins
  })

  useEffect(() => {
    try {
      localStorage.setItem('RenoPilot.pinnedAssets', JSON.stringify(pinnedAssets))
    }
    catch (error) {
      console.error('Error saving pinned assets to localStorage:', error) // Keep this useful error log
    }
  }, [pinnedAssets])

  const handleTogglePinAsset = useCallback(
    (elementType: CoreElementType, isSpecific: boolean, moduleId?: string, stepId?: string, assetName?: string) => {
      logger.info('[EditorLayout] handleTogglePinAsset called:', {
        elementType,
        isSpecific,
        moduleId,
        stepId,
        assetName,
        currentPinnedAssets: pinnedAssets.length,
      })

      setPinnedAssets((prevPinnedAssets) => {
        const existingPinIndex = prevPinnedAssets.findIndex(
          pin =>
            pin.type === elementType
            && pin.isSpecific === isSpecific
            && (isSpecific ? pin.moduleId === moduleId : true)
            && (isSpecific ? pin.stepId === stepId : true)
            && (isSpecific ? pin.assetName === assetName : true),
        )

        logger.info('[EditorLayout] handleTogglePinAsset search result:', {
          existingPinIndex,
          foundExisting: existingPinIndex > -1,
          searchCriteria: { elementType, isSpecific, moduleId, stepId, assetName },
        })

        if (existingPinIndex > -1) {
          logger.info('[EditorLayout] handleTogglePinAsset: Removing existing pin')
          return prevPinnedAssets.filter((_, index) => index !== existingPinIndex)
        }
        else {
          const newPin: PinnedAsset = {
            type: elementType, // Ensure type is included
            isSpecific,
            moduleId: isSpecific ? moduleId as MajorCategory : undefined,
            stepId: isSpecific ? stepId as MinorCategory : undefined,
            assetName: isSpecific ? assetName : undefined,
          }
          logger.info('[EditorLayout] handleTogglePinAsset: Adding new pin:', newPin)
          return [...prevPinnedAssets, newPin]
        }
      })
    },
    [logger, pinnedAssets.length],
  )

  const isAssetCurrentlyPinned = useCallback(
    (elementType: CoreElementType, isSpecific: boolean, moduleId?: string, stepId?: string, assetName?: string): boolean => {
      logger.info(`[EditorLayout] isAssetCurrentlyPinned ENTRY: ${elementType}, isSpecific: ${isSpecific}, moduleId: ${moduleId}, stepId: ${stepId}, assetName: ${assetName}`)
      logger.info(`[EditorLayout] Total pinned assets: ${pinnedAssets.length}`)

      if (pinnedAssets.length > 0) {
        logger.info('[EditorLayout] All pinned assets:')
        pinnedAssets.forEach((pin, index) => {
          logger.info(`  ${index}: type=${pin.type}, isSpecific=${pin.isSpecific}, moduleId=${pin.moduleId}, stepId=${pin.stepId}, assetName=${pin.assetName}`)
        })
      }

      const result = pinnedAssets.some(
        pin =>
          pin.type === elementType
          && pin.isSpecific === isSpecific
          && (isSpecific ? pin.moduleId === moduleId : true)
          && (isSpecific ? pin.stepId === stepId : true)
          && (isSpecific ? pin.assetName === assetName : true),
      )

      logger.info(`[EditorLayout] isAssetCurrentlyPinned RESULT: ${result}`)

      if (elementType === 'WALL' && isSpecific && assetName === 'Wall Structure') {
        logger.info('[EditorLayout] WALL STRUCTURE DEBUG:')
        pinnedAssets.forEach((pin, index) => {
          const typeMatch = pin.type === elementType
          const isSpecificMatch = pin.isSpecific === isSpecific
          const moduleIdMatch = isSpecific ? pin.moduleId === moduleId : true
          const stepIdMatch = isSpecific ? pin.stepId === stepId : true
          const assetNameMatch = isSpecific ? pin.assetName === assetName : true
          const overallMatch = typeMatch && isSpecificMatch && moduleIdMatch && stepIdMatch && assetNameMatch

          logger.info(`  Pin ${index}: type=${pin.type}, isSpecific=${pin.isSpecific}, moduleId=${pin.moduleId}, stepId=${pin.stepId}, assetName=${pin.assetName}`)
          logger.info(`    typeMatch=${typeMatch}, isSpecificMatch=${isSpecificMatch}, moduleIdMatch=${moduleIdMatch}, stepIdMatch=${stepIdMatch}, assetNameMatch=${assetNameMatch}, overallMatch=${overallMatch}`)
        })
      }

      return result
    },
    [pinnedAssets, logger],
  )
  // --- END Pinning State and Logic ---

  // --- START MODIFIED ZOOM HANDLERS (Version 3 - direct value setting) ---
  const handleZoomIn = useCallback(() => {
    let newZoomValue = zoom // Start with current zoom
    const currentIndex = ZOOM_LEVELS.findIndex(level => Math.abs(level - newZoomValue) < 0.001)
    if (currentIndex === -1) {
      const nextLevel = ZOOM_LEVELS.find(level => level > newZoomValue)
      newZoomValue = nextLevel !== undefined ? nextLevel : ZOOM_LEVELS[ZOOM_LEVELS.length - 1]
    }
    else if (currentIndex < ZOOM_LEVELS.length - 1) {
      newZoomValue = ZOOM_LEVELS[currentIndex + 1]
    }
    setZoom(newZoomValue) // Call setZoom with the direct new value
  }, [zoom, setZoom]) // Include zoom in dependencies

  const handleZoomOut = useCallback(() => {
    let newZoomValue = zoom // Start with current zoom
    const currentIndex = ZOOM_LEVELS.findIndex(level => Math.abs(level - newZoomValue) < 0.001)
    if (currentIndex === -1) {
      const prevLevel = ZOOM_LEVELS.slice().reverse().find(level => level < newZoomValue)
      newZoomValue = prevLevel !== undefined ? prevLevel : ZOOM_LEVELS[0]
    }
    else if (currentIndex > 0) {
      newZoomValue = ZOOM_LEVELS[currentIndex - 1]
    }
    setZoom(newZoomValue) // Call setZoom with the direct new value
  }, [zoom, setZoom]) // Include zoom in dependencies
  // --- END MODIFIED ZOOM HANDLERS ---

  // New adapter function for Canvas's onElementAdd prop
  const handleCanvasElementAddAdapter = useCallback(
    (
      asset: { elementType: string, properties?: Record<string, unknown>, id?: string, name?: string, elementSpecificData?: Record<string, unknown> },
      position: Point,
    ) => {
      const dropData: DroppedElementData = {
        elementType: asset.elementType as ElementType, // Cast needed as ElementType is more specific
        properties: Object.assign({}, asset.properties) as InitialElementProperties, // Ensure properties is not undefined
        position,
        elementSpecificData: asset.elementSpecificData,
        name: asset.name,
        id: asset.id,
      }

      // Add layer information from the store if not already present in the asset
      // This is crucial for assets dragged from generic sources like the bottom drawer
      // or pinned assets that might not have inherent layer context.

      logger.info('[EditorLayout ADAPTER PRE-CHECK] About to check/assign layer props. Incoming dropData.properties:', JSON.stringify(dropData.properties))
      // Explicitly check for null/undefined/empty string for store IDs
      logger.info('[EditorLayout ADAPTER PRE-CHECK] Current store selections:', {
        storeCurrentModuleId: storeCurrentModuleId ?? 'null/undefined/empty',
        storeCurrentStepId: storeCurrentStepId ?? 'null/undefined/empty',
        storeCurrentZLevelId: storeCurrentZLevelId ?? 'null/undefined/empty',
      })

      // Check if properties already have layer info OR if the store has valid layer info
      const hasExistingLayerInfo = dropData.properties.majorCategory !== undefined
        && dropData.properties.minorCategory !== undefined
        && dropData.properties.zLevelId !== undefined

      const hasValidStoreLayerInfo = storeCurrentModuleId !== undefined && storeCurrentModuleId !== null
        && storeCurrentStepId !== undefined && storeCurrentStepId !== null
        && storeCurrentZLevelId !== undefined && storeCurrentZLevelId !== null && storeCurrentZLevelId !== ''

      if (!hasExistingLayerInfo && hasValidStoreLayerInfo) {
        logger.info('[EditorLayout ADAPTER] Condition met: Missing majorCat, minorCat, or zLevelId in dropData.properties AND valid store info available. Attempting to use store values.')
        const currentModule = layerModulesFromStore.find(m => m.id === storeCurrentModuleId)
        const currentStep = currentModule?.steps.find(s => s.id === storeCurrentStepId)
        const currentZLevel = currentStep?.zLevels.find(zl => zl.id === storeCurrentZLevelId)

        logger.info('[EditorLayout ADAPTER] Store lookup results:', {
          currentModuleFound: !!currentModule,
          currentModuleIdFromLookup: currentModule?.id ?? 'undefined', // Added nullish coalescing
          currentStepFound: !!currentStep,
          currentStepIdFromLookup: currentStep?.id ?? 'undefined', // Added nullish coalescing
          currentZLevelFound: !!currentZLevel,
          currentZLevelIdFromLookup: currentZLevel?.id ?? 'undefined', // Added nullish coalescing
          currentZLevelObject: currentZLevel ? JSON.stringify(currentZLevel) : 'undefined',
        })

        // Only apply if all current selections are valid from the lookup
        if (currentModule && currentStep && currentZLevel) {
          logger.info('[EditorLayout ADAPTER] SUCCESS: Found currentModule, currentStep, and currentZLevel from store lookup. Assigning properties.')
          dropData.properties.majorCategory = currentModule.id // Corrected: Use module.id as MajorCategory
          dropData.properties.minorCategory = currentStep.id // Corrected: Use step.id as MinorCategory
          dropData.properties.zLevelId = currentZLevel.id

          // isFixedCategory is not available on TaskModule, TaskStep, or ZLevel types.
          // If needed, it must be sourced elsewhere or handled by creators/ShapeCreationService.
          // delete dropData.properties.isFixedCategory; // Or ensure it's not added if not sourced.

          logger.info('[EditorLayout ADAPTER] Assigned properties from store (major, minor, zLevelId):', JSON.stringify(dropData.properties))
        }
        else {
          logger.warn('[EditorLayout ADAPTER] FAILED: Could not find complete current layer context (module, step, or zLevel) during store lookup. Element will be created without explicit layer assignment from store. Check PRE-CHECK and Store lookup results above.', {
            storeCurrentModuleId,
            storeCurrentStepId,
            storeCurrentZLevelId,
            currentModuleFound: !!currentModule,
            currentStepFound: !!currentStep,
            currentZLevelFound: !!currentZLevel,
          })
          // Fallback to some very basic default if absolutely nothing is set
        }
      }
      else if (hasExistingLayerInfo) {
        logger.info('[EditorLayout ADAPTER] Element already has layer info in properties. Using existing.')
      }
      else {
        logger.warn('[EditorLayout ADAPTER] Neither element properties nor store has complete valid layer info. Element created without explicit layer assignment from store.')
      }

      void handleElementDropFromHook(dropData)
    },
    [handleElementDropFromHook, layerModulesFromStore, storeCurrentModuleId, storeCurrentStepId, storeCurrentZLevelId, logger], // Added logger to dependencies
  )

  // --- START CANVAS PROPS ---
  const canvasProps = {
    elements: shapes, // From Zustand store
    selectedElementIds: selectedElementIds ?? [], // From useElementActions
    isPanMode: currentTool === 'PAN', // Correct: Canvas isPanMode depends on currentTool
    isDrawMode: currentTool === 'DRAW', // Add draw mode flag
    showGrid, // From local state
    gridSize, // Constant
    zoom, // From useCanvasPanZoom
    pan, // From useCanvasPanZoom
    onMouseDown: handleCanvasMouseDown,
    onMouseMove: handleCanvasMouseMove,
    onMouseUp: handleCanvasMouseUp,
    onDragOver: dragAndDrop.onDragOver as React.DragEventHandler<Element>,
    onElementAdd: handleCanvasElementAddAdapter, // Corrected: Use the new adapter
    onWorldMouseMove: handleCanvasWorldMouseMove, // Assign the new handler
    onDimensionsChange: handleCanvasDimensionChange,
    isPropertySidebarOpen, // Pass this down for conditional rendering/logic
    onWheel: handleWheel, // Pass the wheel handler from useCanvasPanZoom
    doStartPan: startCanvasPan, // Pass pan handlers
    doContinuePan: continueCanvasPan, // Pass continue pan handler
    doEndPan: endCanvasPan,
    primedAssetTypeForCanvasClick, // 修正属性名
    // 传递路径绘制状态给Canvas组件
    pathDrawState: {
      isDrawing: pathDrawHandler.isDrawing,
      pathType: pathDrawHandler.pathType,
      startPoint: pathDrawHandler.getDrawState().startPoint,
      currentPoint: pathDrawHandler.currentPoint,
      allPoints: pathDrawHandler.getDrawState().points,
      clickCount: pathDrawHandler.getDrawState().clickCount,
    },
    // --- PATCH: selection handler ---
    onElementSelect: (
      elementIdOrIds: string | string[] | null,
      _isMultiSelect = false, // Default from Canvas/ShapeRenderer if not explicitly set by marquee
      selectionMode: 'replace' | 'toggle' | 'add' | 'clear' = 'replace', // Added 'add' mode
    ) => {
      logger.info('[EditorLayout onElementSelect] Received:', { elementIdOrIds, _isMultiSelect, selectionMode })

      if (selectionMode === 'clear') {
        void clearElementSelection()
        return
      }

      if (elementIdOrIds === null) {
        // This case should ideally be handled by selectionMode === 'clear'
        // If called with null and not 'clear', it implies a deselect of everything not covered by 'toggle'
        // For safety, treat as clear if not 'toggle' and not 'add'
        if (selectionMode !== 'toggle' && selectionMode !== 'add') {
          void clearElementSelection()
        }
        // If selectionMode is 'toggle' or 'add' and id is null, it means click on background with modifier: do nothing to current selection
        return
      }

      if (Array.isArray(elementIdOrIds)) { // Typically from Marquee
        if (selectionMode === 'toggle') {
          // Toggle each element in the array
          const currentSelected = new Set(selectedElementIds)
          elementIdOrIds.forEach((id) => {
            if (currentSelected.has(id)) {
              currentSelected.delete(id) // Remove if already selected
            }
            else {
              currentSelected.add(id) // Add if not selected
            }
          })
          void selectElements(Array.from(currentSelected))
        }
        else if (selectionMode === 'add') {
          // Add new elements to current selection
          const currentSelected = new Set(selectedElementIds)
          elementIdOrIds.forEach((id) => {
            currentSelected.add(id) // Add to selection (Set prevents duplicates)
          })
          void selectElements(Array.from(currentSelected))
        }
        else { // 'replace' mode for array
          void selectElements(elementIdOrIds)
        }
      }
      else { // Single element ID (string)
        if (selectionMode === 'toggle') {
          // selectElement with multi=true should handle toggling for a single element
          void selectElement(elementIdOrIds, true)
        }
        else if (selectionMode === 'add') {
          // Add single element to current selection
          const currentSelected = new Set(selectedElementIds)
          currentSelected.add(elementIdOrIds)
          void selectElements(Array.from(currentSelected))
        }
        else { // 'replace' mode for single element
          void selectElement(elementIdOrIds, false) // multi=false means replace
        }
      }
    },
    selectedElements, // Added selectedElements to dependencies for the memoized callback
  }
  // --- END CANVAS PROPS ---

  return (
    <div ref={editorLayoutRef} className="editor-layout flex flex-col h-screen w-screen overflow-hidden bg-background text-foreground select-none">
      <Toolbar
        onNew={handleNewCanvas}
        onSave={handleSaveCanvas}
        onExport={() => {
          const eventBus = getService<EventBus>(ServiceId.EventBus as ServiceId)
          if (eventBus != null) {
            eventBus.publish({ type: AppEventType.ModalExportOpen, payload: {} })
          }
        }}
        onUndo={undoShapes}
        onRedo={redoShapes}
        canUndo={canUndoShapes}
        canRedo={canRedoShapes}
        isPanMode={currentTool === 'PAN'} // Correct: Toolbar isPanMode depends on currentTool
        onToggleMode={handleTogglePanMode}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onResetZoom={() => setZoom(1)}
        currentZoom={zoom}
        formattedCanvasWidth={formattedCanvasWidth}
        formattedCanvasHeight={formattedCanvasHeight}
        projectName={projectName}
        onProjectNameChange={setProjectName}
        onOpenSettings={toggleSettingsPanel}
        isKeyboardShortcutsOpen={isShortcutsGuideOpen}
        onKeyboardShortcutsOpenChange={toggleShortcutsGuide}
        onToggleLayerPanel={toggleLayerPanel}
        isLayerPanelOpen={isLayerPanelOpen}
        onTogglePropertySidebar={togglePropertySidebar}
        isPropertySidebarOpen={isPropertySidebarOpen}
        showGrid={showGrid}
        onToggleGrid={handleToggleGrid}
      />

      <div className="flex flex-1 min-h-0">
        {/* Left Sidebar (Layer Progress Panel) */}
        {/* Wrapped with DLPWrapper to manage instanceRenderId for debugging/storybook */}
        <div data-tutorial="layer-panel">
          <DLPWrapper
            logId="EditorLayoutDLP"
            isLayerPanelOpen={isLayerPanelOpen}
            isBottomDrawerOpen={isAssetSheetOpen}
            // No explicit modules, currentModuleId, currentStepId, currentZLevelId passed here,
            // as DLP uses its own Zustand selectors for these.
          />
        </div>

        {/* Main Canvas Area */}
        <div ref={canvasRef} className="canvas-container flex-1 relative overflow-hidden bg-canvas-background" data-tutorial="canvas-container">
          <Canvas
            key={`${storeCurrentModuleId}-${storeCurrentStepId}-${storeCurrentZLevelId}`}
            {...canvasProps}
          />
        </div>

        {/* Right Sidebar (Property Panel) */}
        <div data-tutorial="property-sidebar">
          {/* Tutorial placeholder for property sidebar - always visible */}
          <div
            className="fixed right-2 z-50 w-[300px] h-[200px] bg-background border rounded-lg opacity-0 pointer-events-none"
            style={{
              top: `${56 + 8}px`, // 56px toolbar height + 8px gap
            }}
            data-tutorial="property-sidebar-placeholder"
          />

          <PropertySidebar
            selectedElements={selectedElements}
            onUpdateElements={handleUpdateElements}
            onDeleteElement={handleDeleteElements} // Connect to the new delete handler
            layerPanelModules={layerModulesFromStore} // Pass the modules fetched from the store
            isPropertySidebarOpen={isPropertySidebarOpen}
            pixelsPerMM={PIXELS_PER_MM}
            isBottomDrawerOpen={isAssetSheetOpen}
          />
        </div>
      </div>

      <div data-tutorial="bottom-drawer">
        <BottomAssetDrawer
          onAssetDragStart={handleBottomDrawerAssetDragStart}
          getSettingsForType={getSettingsForType}
          currentModuleId={storeCurrentModuleId ?? MajorCatEnum.BASE}
          currentStepId={storeCurrentStepId === null ? undefined : storeCurrentStepId}
          onAssetSelectFromExpanded={handleAssetSelectFromExpandedDrawer}
          onTogglePinAsset={handleTogglePinAsset}
          isAssetPinned={isAssetCurrentlyPinned}
          sheetOpen={isAssetSheetOpen}
          onSheetOpenChange={setIsAssetSheetOpen}
          pinnedAssets={pinnedAssets}
        />
      </div>

      <SettingsPanel
        isOpen={isSettingsPanelOpen}
        onClose={toggleSettingsPanel}
        effectiveSettings={effectiveSettings}
        saveSettings={saveSettings}
        getSettingsForType={getSettingsForType}
        resetAllSettings={resetAllSettings}
        pixelsPerMM={PIXELS_PER_MM}
      />
    </div>
  )

  function handleCanvasMouseDown(event: CanvasMouseEvent) {
    // 检查是否处于绘制模式
    if (currentTool === 'DRAW' && event.originalEvent != null && 'button' in event.originalEvent) {
      const mouseEvent = event.originalEvent as React.MouseEvent
      if (mouseEvent.button === 0 && primedAssetTypeForCanvasClick !== null) {
        // 如果是路径类型元素（LINE、POLYLINE等）
        if (
          primedAssetTypeForCanvasClick === ElementType.LINE
          || primedAssetTypeForCanvasClick === ElementType.POLYLINE
          || primedAssetTypeForCanvasClick === ElementType.ARC
          || primedAssetTypeForCanvasClick === ElementType.QUADRATIC
          || primedAssetTypeForCanvasClick === ElementType.CUBIC
        ) {
          // 如果已经在绘制中，则完成绘制
          if (pathDrawHandler.isDrawing) {
            pathDrawHandler.completeDrawing(event.worldPosition)
          }
          else {
            // 否则开始绘制
            pathDrawHandler.startDrawing(primedAssetTypeForCanvasClick, event.worldPosition)
          }
          return
        }
      }
    }

    // 检查是否有预选的资产类型，并且事件有原始事件和按钮属性
    if (isPrimedForAssetAdd && event.originalEvent != null
      && 'button' in event.originalEvent) {
      const mouseEvent = event.originalEvent as React.MouseEvent
      if (mouseEvent.button === 0 && primedAssetTypeForCanvasClick !== null) {
        // 安全地调用元素添加函数，使用void处理Promise
        void onElementAddCanvas_Correct(
          event.worldPosition,
          {
            type: primedAssetTypeForCanvasClick,
            name: getPathElementName(primedAssetTypeForCanvasClick),
            properties: {},
          },
        )
        return
      }
    }
    // 存储位置用于潜在的拖动操作
    lastMouseDownCanvasPosition.current = event.worldPosition
  }

  function handleCanvasMouseMove(event: CanvasMouseEvent) {
    // 检查是否处于绘制模式
    if (currentTool === 'DRAW' && event.originalEvent != null) {
      // 确保世界坐标有效
      const hasValidPosition = Boolean(event.worldPosition)
        && typeof event.worldPosition.x === 'number'
        && typeof event.worldPosition.y === 'number'

      if (hasValidPosition && primedAssetTypeForCanvasClick !== null) {
        // 检查是否是路径类型元素
        const isPathType = [
          ElementType.LINE,
          ElementType.POLYLINE,
          ElementType.ARC,
          ElementType.QUADRATIC,
          ElementType.CUBIC,
        ].includes(primedAssetTypeForCanvasClick)

        if (isPathType) {
          // 圆弧约束逻辑已删除，直接使用原始鼠标位置
          const effectivePosition = event.worldPosition

          // 使用原始位置更新绘制状态
          pathDrawHandler.updateDrawing(effectivePosition)

          // 如果正在绘制，阻止其他处理
          if (pathDrawHandler.isDrawing) {
            return
          }
        }
      }
    }

    // 检查是否有原始事件和按钮属性
    if (event.originalEvent != null && 'buttons' in event.originalEvent) {
      const nativeEvent = event.originalEvent as React.MouseEvent
      if (
        nativeEvent.buttons === 1
        && lastMouseDownCanvasPosition.current != null
        && selectedElementIds.length > 0
      ) {
        // 鼠标按下（拖动）且有选中的元素
        const dx = event.worldPosition.x - lastMouseDownCanvasPosition.current.x
        const dy = event.worldPosition.y - lastMouseDownCanvasPosition.current.y

        if (!isDragging.current && (Math.abs(dx) > 5 || Math.abs(dy) > 5)) {
          isDragging.current = true
        }

        if (isDragging.current) {
          // 使用updateElements函数来更新元素位置
          // 为每个选中的元素创建位置更新
          const shapesToUpdate = selectedElementIds.map((id) => {
            const shape = shapes.find(s => s.id === id)
            if (!shape) {
              return null
            }

            // 计算新位置
            const newPosition = {
              x: shape.position.x + dx,
              y: shape.position.y + dy,
              z: shape.position.z,
            }

            return {
              id,
              properties: {
                position: newPosition,
              },
            }
          }).filter(Boolean) as Array<{ id: string, properties: Partial<ShapeModel> }> // 移除null项

          // 批量更新元素位置
          if (shapesToUpdate.length > 0) {
            handleUpdateElements(shapesToUpdate)
          }

          // 更新鼠标位置以便计算下一次移动的偏移量
          lastMouseDownCanvasPosition.current = event.worldPosition
        }
      }
    }
  }

  function handleCanvasMouseUp(event: CanvasMouseEvent) {
    // Check if we have an originalEvent with button property
    if (event.originalEvent != null && 'button' in event.originalEvent) {
      const mouseEvent = event.originalEvent as React.MouseEvent
      if (mouseEvent.button === 0) {
        if (isDragging.current) {
          // Emit a state update event to record the drag operation in history
          const eventBus = getService<EventBus>(ServiceId.EventBus as ServiceId)
          if (eventBus != null) {
            eventBus.publish({
              type: AppEventType.StateUpdated,
              payload: { selectedElementIds }, // This should be fine if selectedElementIds is string[]
            })
          }

          isDragging.current = false
        }
        // Remove debug and selection code that references undefined functions
      }
    }

    // Clear position ref after handling the event
    lastMouseDownCanvasPosition.current = null
  }
}

export default EditorLayout
