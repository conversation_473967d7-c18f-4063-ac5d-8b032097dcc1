import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
// Import the module after mocking dependencies
import { setupEventHandlers } from '@/services/event-bus/handlers/EventHandlers'

import { appEventBus } from '@/services/event-bus/index'
import { AppEventType } from '@/types/services/events'

// Mock the getEventBus function to return our mock event bus
vi.mock('@/services/event-bus/index', () => ({
  appEventBus: {
    subscribe: vi.fn(),
    publish: vi.fn(),
    unsubscribe: vi.fn(),
    reset: vi.fn(),
    getSubscriptionCount: vi.fn(),
    setConfig: vi.fn(),
    getConfig: vi.fn(),
  },
}))

describe('eventHandlers', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks()

    // Spy on console methods
    vi.spyOn(console, 'log').mockImplementation(() => {})
    vi.spyOn(console, 'debug').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('setupEventHandlers', () => {
    it('should subscribe to canvas events', () => {
      setupEventHandlers()

      // Check for canvas event subscriptions
      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.CANVAS_CLEARED,
        expect.any(Function),
      )

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.CANVAS_RESIZED,
        expect.any(Function),
      )

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.CANVAS_MOUSE_MOVE,
        expect.any(Function),
      )
    })

    it('should subscribe to tool events', () => {
      setupEventHandlers()

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.TOOL_CHANGED,
        expect.any(Function),
      )
    })

    it('should subscribe to history events', () => {
      setupEventHandlers()

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.HISTORY_CHECKPOINT,
        expect.any(Function),
      )

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.HISTORY_UNDO,
        expect.any(Function),
      )

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.HISTORY_REDO,
        expect.any(Function),
      )
    })

    it('should subscribe to layer events', () => {
      setupEventHandlers()

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.LAYER_VISIBILITY_CHANGE,
        expect.any(Function),
      )

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.LAYER_LOCK_CHANGE,
        expect.any(Function),
      )

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.LAYER_ORDER_CHANGE,
        expect.any(Function),
      )
    })

    it('should subscribe to file events', () => {
      setupEventHandlers()

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.FILE_IMPORTED,
        expect.any(Function),
      )

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.FILE_EXPORTED,
        expect.any(Function),
      )
    })

    it('should subscribe to view events', () => {
      setupEventHandlers()

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.VIEW_ZOOMED,
        expect.any(Function),
      )

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.VIEW_PANNED,
        expect.any(Function),
      )
    })

    it('should subscribe to template events', () => {
      setupEventHandlers()

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.TEMPLATE_APPLY,
        expect.any(Function),
      )
    })

    it('should subscribe to notification events', () => {
      setupEventHandlers()

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.NOTIFICATION_ADD,
        expect.any(Function),
      )

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.TOAST_SHOW,
        expect.any(Function),
      )
    })

    it('should subscribe to sidebar events', () => {
      setupEventHandlers()

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.SIDEBAR_LEFT_TOGGLE,
        expect.any(Function),
      )

      expect(appEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.SIDEBAR_RIGHT_TOGGLE,
        expect.any(Function),
      )
    })
  })

  // Test event handlers by triggering them with mock events
  describe('event Handlers', () => {
    it('should log canvas cleared events', () => {
      setupEventHandlers()

      // Find the canvas cleared handler
      const canvasClearedHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.CANVAS_CLEARED,
      )?.[1]

      expect(canvasClearedHandler).toBeDefined()

      // Call the handler with a mock event
      canvasClearedHandler?.({
        type: AppEventType.CANVAS_CLEARED,
        payload: { canvasId: 'test-canvas' },
      })

      expect(console.log).toHaveBeenCalledWith('Canvas cleared:', 'test-canvas')
    })

    it('should log canvas resized events', () => {
      setupEventHandlers()

      const canvasResizedHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.CANVAS_RESIZED,
      )?.[1]

      expect(canvasResizedHandler).toBeDefined()

      canvasResizedHandler?.({
        type: AppEventType.CANVAS_RESIZED,
        payload: { width: 800, height: 600 },
      })

      expect(console.log).toHaveBeenCalledWith('Canvas resized:', 800, 600)
    })

    it('should log canvas mouse move events occasionally', () => {
      setupEventHandlers()

      const canvasMouseMoveHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.CANVAS_MOUSE_MOVE,
      )?.[1]

      expect(canvasMouseMoveHandler).toBeDefined()

      // Mock Math.random to return a value less than 0.01 to ensure logging
      const originalRandom = Math.random
      Math.random = vi.fn().mockReturnValue(0.005)

      canvasMouseMoveHandler?.({
        type: AppEventType.CANVAS_MOUSE_MOVE,
        payload: { x: 100, y: 200 },
      })

      expect(console.log).toHaveBeenCalledWith('Canvas mouse move:', 100, 200)

      // Restore Math.random
      Math.random = originalRandom
    })

    it('should log tool changed events', () => {
      setupEventHandlers()

      const toolChangedHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.TOOL_CHANGED,
      )?.[1]

      expect(toolChangedHandler).toBeDefined()

      toolChangedHandler?.({
        type: AppEventType.TOOL_CHANGED,
        payload: { tool: 'rectangle' },
      })

      expect(console.log).toHaveBeenCalledWith('Tool changed:', 'rectangle')
    })

    it('should log history checkpoint events', () => {
      setupEventHandlers()

      const historyCheckpointHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.HISTORY_CHECKPOINT,
      )?.[1]

      expect(historyCheckpointHandler).toBeDefined()

      const payload = { id: 'checkpoint-1', description: 'Test checkpoint' }
      historyCheckpointHandler?.({
        type: AppEventType.HISTORY_CHECKPOINT,
        payload,
      })

      expect(console.log).toHaveBeenCalledWith('History checkpoint:', payload)
    })

    it('should log history undo events', () => {
      setupEventHandlers()

      const historyUndoHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.HISTORY_UNDO,
      )?.[1]

      expect(historyUndoHandler).toBeDefined()

      const payload = { id: 'undo-1', description: 'Test undo' }
      historyUndoHandler?.({
        type: AppEventType.HISTORY_UNDO,
        payload,
      })

      expect(console.log).toHaveBeenCalledWith('History undo:', payload)
    })

    it('should log history redo events', () => {
      setupEventHandlers()

      const historyRedoHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.HISTORY_REDO,
      )?.[1]

      expect(historyRedoHandler).toBeDefined()

      const payload = { id: 'redo-1', description: 'Test redo' }
      historyRedoHandler?.({
        type: AppEventType.HISTORY_REDO,
        payload,
      })

      expect(console.log).toHaveBeenCalledWith('History redo:', payload)
    })

    it('should log layer visibility change events', () => {
      setupEventHandlers()

      const layerVisibilityHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.LAYER_VISIBILITY_CHANGE,
      )?.[1]

      expect(layerVisibilityHandler).toBeDefined()

      layerVisibilityHandler?.({
        type: AppEventType.LAYER_VISIBILITY_CHANGE,
        payload: { layerId: 'layer-1', visible: true },
      })

      expect(console.log).toHaveBeenCalledWith('Layer visibility change:', 'layer-1', true)
    })

    it('should log layer lock change events', () => {
      setupEventHandlers()

      const layerLockHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.LAYER_LOCK_CHANGE,
      )?.[1]

      expect(layerLockHandler).toBeDefined()

      layerLockHandler?.({
        type: AppEventType.LAYER_LOCK_CHANGE,
        payload: { layerId: 'layer-1', locked: true },
      })

      expect(console.log).toHaveBeenCalledWith('Layer lock change:', 'layer-1', true)
    })

    it('should log layer order change events', () => {
      setupEventHandlers()

      const layerOrderHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.LAYER_ORDER_CHANGE,
      )?.[1]

      expect(layerOrderHandler).toBeDefined()

      layerOrderHandler?.({
        type: AppEventType.LAYER_ORDER_CHANGE,
        payload: { layerId: 'layer-1', newOrder: 2 },
      })

      expect(console.log).toHaveBeenCalledWith('Layer order change:', 'layer-1', 2)
    })

    it('should log file imported events', () => {
      setupEventHandlers()

      const fileImportedHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.FILE_IMPORTED,
      )?.[1]

      expect(fileImportedHandler).toBeDefined()

      const event = {
        type: AppEventType.FILE_IMPORTED,
        payload: { filename: 'test.json', size: 1024 },
      }
      fileImportedHandler?.(event)

      expect(console.log).toHaveBeenCalledWith('File imported:', event)
    })

    it('should log file exported events', () => {
      setupEventHandlers()

      const fileExportedHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.FILE_EXPORTED,
      )?.[1]

      expect(fileExportedHandler).toBeDefined()

      const event = {
        type: AppEventType.FILE_EXPORTED,
        payload: { filename: 'export.json', format: 'json' },
      }
      fileExportedHandler?.(event)

      expect(console.log).toHaveBeenCalledWith('File exported:', event)
    })

    it('should log view zoomed events', () => {
      setupEventHandlers()

      const viewZoomedHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.VIEW_ZOOMED,
      )?.[1]

      expect(viewZoomedHandler).toBeDefined()

      viewZoomedHandler?.({
        type: AppEventType.VIEW_ZOOMED,
        payload: { scale: 1.5 },
      })

      expect(console.log).toHaveBeenCalledWith('View zoomed:', 1.5)
    })

    it('should log view panned events', () => {
      setupEventHandlers()

      const viewPannedHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.VIEW_PANNED,
      )?.[1]

      expect(viewPannedHandler).toBeDefined()

      viewPannedHandler?.({
        type: AppEventType.VIEW_PANNED,
        payload: { x: 100, y: 200 },
      })

      expect(console.log).toHaveBeenCalledWith('View panned:', 100, 200)
    })

    it('should log template apply events', () => {
      setupEventHandlers()

      const templateApplyHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.TEMPLATE_APPLY,
      )?.[1]

      expect(templateApplyHandler).toBeDefined()

      templateApplyHandler?.({
        type: AppEventType.TEMPLATE_APPLY,
        payload: { templateId: 'template-1' },
      })

      expect(console.log).toHaveBeenCalledWith('Template apply:', 'template-1')
    })

    it('should log notification add events', () => {
      setupEventHandlers()

      const notificationAddHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.NOTIFICATION_ADD,
      )?.[1]

      expect(notificationAddHandler).toBeDefined()

      notificationAddHandler?.({
        type: AppEventType.NOTIFICATION_ADD,
        payload: { message: 'Test notification', type: 'info' },
      })

      expect(console.log).toHaveBeenCalledWith('Notification add:', 'Test notification', 'info')
    })

    it('should log toast show events', () => {
      setupEventHandlers()

      const toastShowHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.TOAST_SHOW,
      )?.[1]

      expect(toastShowHandler).toBeDefined()

      toastShowHandler?.({
        type: AppEventType.TOAST_SHOW,
        payload: { message: 'Test toast', type: 'success', duration: 3000 },
      })

      expect(console.log).toHaveBeenCalledWith('Toast show:', 'Test toast', 'success', 3000)
    })

    it('should log sidebar left toggle events', () => {
      setupEventHandlers()

      const sidebarLeftToggleHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.SIDEBAR_LEFT_TOGGLE,
      )?.[1]

      expect(sidebarLeftToggleHandler).toBeDefined()

      sidebarLeftToggleHandler?.({
        type: AppEventType.SIDEBAR_LEFT_TOGGLE,
        payload: {},
      })

      expect(console.log).toHaveBeenCalledWith('Sidebar left toggle')
    })

    it('should log sidebar right toggle events', () => {
      setupEventHandlers()

      const sidebarRightToggleHandler = (appEventBus.subscribe as jest.Mock).mock.calls.find(
        call => call[0] === AppEventType.SIDEBAR_RIGHT_TOGGLE,
      )?.[1]

      expect(sidebarRightToggleHandler).toBeDefined()

      sidebarRightToggleHandler?.({
        type: AppEventType.SIDEBAR_RIGHT_TOGGLE,
        payload: {},
      })

      expect(console.log).toHaveBeenCalledWith('Sidebar right toggle')
    })
  })
})
