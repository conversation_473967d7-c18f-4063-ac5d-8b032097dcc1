/**
 * Provides functions for initializing and cleaning up the service layer.
 *
 * @remarks
 * This module ensures that all services are properly created, registered, and configured.
 */

// Import dependencies
import type { ElementFactory } from '@/core/factory'
import type { ShapeRepository } from '@/core/state/ShapeRepository'

import type { LoggerService } from '@/types/services/logging'
import { appEventBus, cleanupEventSystem, initializeEventSystem } from '@/services/core/event-bus' // Import appEventBus
import { registerValidationService } from '@/services/core/validation/registry'
import { registerElementServices } from '@/services/elements/element-actions'
import { registerKeyboardService } from '@/services/input/keyboard-service/registry'
import { registerStorageService } from '@/services/storage/registry'
import { registerErrorService } from '@/services/system/error-service/registry'
import { registerLoggerService } from '@/services/system/logging/registry'
// import { getServiceFactory } from './index'; // Unused import
import { ServiceId } from '@/types/services/core/serviceIdentifier'
import { getServiceRegistry } from './index'

/**
 * Initialize all services in the application.
 *
 * @remarks
 * This function ensures services are properly created, registered, and configured.
 * It should be called at application startup before any services are used.
 *
 * @param dependencies - Dependencies required for service initialization.
 * @param dependencies.elementFactory - Factory for creating shape elements.
 * @param dependencies.shapeRepository - Repository for managing shapes.
 * @param dependencies.logger - Logger service for logging operations.
 * @returns A Promise that resolves when all services are initialized.
 */
export async function initializeServices(dependencies: {
  elementFactory: ElementFactory
  shapeRepository: ShapeRepository
  logger: LoggerService
}): Promise<void> {
  const { elementFactory, shapeRepository, logger } = dependencies
  const registry = getServiceRegistry()

  try {
    // Initialize event system
    initializeEventSystem() // This initializes appEventBus
    registry.register(ServiceId.EventBus, appEventBus) // Register appEventBus instance

    // Register core services
    const loggerService = registerLoggerService(registry)
    registerValidationService(registry, loggerService)
    registerErrorService(registry, loggerService)

    // 注册ShapeRepository服务
    registry.register(ServiceId.ShapeRepository, shapeRepository)
    loggerService.info('ShapeRepository registered successfully')

    // Register business services
    registerElementServices(registry, elementFactory, shapeRepository, loggerService)
    registerKeyboardService(registry, loggerService)
    registerStorageService(registry, loggerService)

    logger.info('Services initialized successfully')
  }
  catch (error) {
    logger.error('Service initialization failed', error)
    throw error
  }
}

/**
 * Clean up all services in the application.
 *
 * @remarks
 * This function ensures services are properly shut down and resources are released.
 * It should be called at application shutdown before the application exits.
 *
 * @returns A Promise that resolves when all services are cleaned up.
 */
export async function cleanupServices(): Promise<void> {
  // Clean up event system
  cleanupEventSystem()

  // Clear service registry
  getServiceRegistry().clear()
}

/**
 * Get a service from the registry.
 *
 * @remarks
 * This is a convenience function for getting services from the registry.
 * It provides type safety and a more concise syntax.
 *
 * @param serviceId - The service identifier.
 * @returns The service instance.
 * @throws Error if the service is not registered.
 */
export function getService<T>(serviceId: ServiceId): T {
  return getServiceRegistry().getById<T>(serviceId)
}
