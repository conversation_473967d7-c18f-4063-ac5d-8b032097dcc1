import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import type { ShapeElement } from '@/types/core/elementDefinitions'
import { ElementType } from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'

// Mock the event bus and services
const mockEventBus = {
  publish: vi.fn(),
  subscribe: vi.fn(),
  unsubscribe: vi.fn(),
}

const mockShapeRepository = {
  getAll: vi.fn().mockReturnValue([]),
  getSelectedIds: vi.fn().mockReturnValue([]),
  setShapesFromExternal: vi.fn(),
}

vi.mock('@/services/core/event-bus', () => ({
  appEventBus: mockEventBus,
}))

vi.mock('@/services/core/registry', () => ({
  getService: vi.fn().mockReturnValue(mockShapeRepository),
}))

// Import after mocking
import { useShapesStore, type ShapesStoreState } from '@/store/shapesStore'

describe('shapesStore (Real Implementation)', () => {
  let testShape1: ShapeElement
  let testShape2: ShapeElement
  let testShape3: ShapeElement

  beforeEach(() => {
    // Reset store state
    useShapesStore.setState({
      shapes: [],
      selectedShapeIds: [],
    })

    // Test shapes
    testShape1 = {
      id: 'shape-1',
      type: ElementType.RECTANGLE,
      position: { x: 0, y: 0 },
      properties: { width: 100, height: 50 },
      majorCategory: MajorCategory.BASE,
      minorCategory: 'architecture',
      intraLayerZIndex: 0,
    }

    testShape2 = {
      id: 'shape-2',
      type: ElementType.ELLIPSE,
      position: { x: 50, y: 50 },
      properties: { radiusX: 30, radiusY: 20 },
      majorCategory: MajorCategory.FURNITURE,
      minorCategory: 'seating',
      intraLayerZIndex: 1,
    }

    testShape3 = {
      id: 'shape-3',
      type: ElementType.LINE,
      position: { x: 0, y: 0 },
      properties: { endX: 100, endY: 100 },
      majorCategory: MajorCategory.BASE,
      minorCategory: 'architecture',
      intraLayerZIndex: 2,
    }
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Initial State', () => {
    it('should have empty initial state', () => {
      const { result } = renderHook(() => useShapesStore())

      expect(result.current.shapes).toEqual([])
      expect(result.current.selectedShapeIds).toEqual([])
    })

    it('should provide all required actions', () => {
      const { result } = renderHook(() => useShapesStore())

      expect(typeof result.current.setShapesFromExternal).toBe('function')
      expect(typeof result.current.addShape).toBe('function')
      expect(typeof result.current.clearShapes).toBe('function')
      expect(typeof result.current.selectShape).toBe('function')
      expect(typeof result.current.selectShapes).toBe('function')
      expect(typeof result.current.deselectShape).toBe('function')
      expect(typeof result.current.clearSelection).toBe('function')
      expect(typeof result.current.bringToFrontInLayer).toBe('function')
      expect(typeof result.current.sendToBackInLayer).toBe('function')
    })
  })

  describe('Shape Management', () => {
    it('should add a shape', () => {
      const { result } = renderHook(() => useShapesStore())

      act(() => {
        result.current.addShape(testShape1)
      })

      expect(result.current.shapes).toHaveLength(1)
      expect(result.current.shapes[0]).toEqual(testShape1)
    })

    it('should add multiple shapes', () => {
      const { result } = renderHook(() => useShapesStore())

      act(() => {
        result.current.addShape(testShape1)
        result.current.addShape(testShape2)
        result.current.addShape(testShape3)
      })

      expect(result.current.shapes).toHaveLength(3)
      expect(result.current.shapes).toContain(testShape1)
      expect(result.current.shapes).toContain(testShape2)
      expect(result.current.shapes).toContain(testShape3)
    })

    it('should set shapes from external source', () => {
      const { result } = renderHook(() => useShapesStore())
      const shapes = [testShape1, testShape2]
      const selectedIds = ['shape-1']

      act(() => {
        result.current.setShapesFromExternal(shapes, selectedIds)
      })

      expect(result.current.shapes).toEqual(shapes)
      expect(result.current.selectedShapeIds).toEqual(selectedIds)
    })

    it('should clear all shapes', () => {
      const { result } = renderHook(() => useShapesStore())

      act(() => {
        result.current.addShape(testShape1)
        result.current.addShape(testShape2)
        result.current.clearShapes()
      })

      expect(result.current.shapes).toEqual([])
      expect(result.current.selectedShapeIds).toEqual([])
    })

    it('should handle duplicate shape additions', () => {
      const { result } = renderHook(() => useShapesStore())

      act(() => {
        result.current.addShape(testShape1)
        result.current.addShape(testShape1) // Add same shape again
      })

      expect(result.current.shapes).toHaveLength(2)
      expect(result.current.shapes.filter(s => s.id === testShape1.id)).toHaveLength(2)
    })
  })

  describe('Selection Management', () => {
    beforeEach(() => {
      const { result } = renderHook(() => useShapesStore())
      act(() => {
        result.current.addShape(testShape1)
        result.current.addShape(testShape2)
        result.current.addShape(testShape3)
      })
    })

    it('should select a shape', () => {
      const { result } = renderHook(() => useShapesStore())

      act(() => {
        result.current.selectShape(testShape1.id)
      })

      expect(result.current.selectedShapeIds).toContain(testShape1.id)
    })

    it('should select multiple shapes with multiSelect', () => {
      const { result } = renderHook(() => useShapesStore())

      act(() => {
        result.current.selectShape(testShape1.id)
        result.current.selectShape(testShape2.id, true) // multiSelect
      })

      expect(result.current.selectedShapeIds).toContain(testShape1.id)
      expect(result.current.selectedShapeIds).toContain(testShape2.id)
    })

    it('should replace selection when multiSelect is false', () => {
      const { result } = renderHook(() => useShapesStore())

      act(() => {
        result.current.selectShape(testShape1.id)
        result.current.selectShape(testShape2.id, false) // Replace selection
      })

      expect(result.current.selectedShapeIds).not.toContain(testShape1.id)
      expect(result.current.selectedShapeIds).toContain(testShape2.id)
      expect(result.current.selectedShapeIds).toHaveLength(1)
    })

    it('should select multiple shapes at once', () => {
      const { result } = renderHook(() => useShapesStore())
      const ids = [testShape1.id, testShape2.id]

      act(() => {
        result.current.selectShapes(ids)
      })

      expect(result.current.selectedShapeIds).toEqual(expect.arrayContaining(ids))
    })

    it('should clear existing selection when selecting shapes', () => {
      const { result } = renderHook(() => useShapesStore())

      act(() => {
        result.current.selectShape(testShape1.id)
        result.current.selectShapes([testShape2.id, testShape3.id], true) // clearExisting
      })

      expect(result.current.selectedShapeIds).not.toContain(testShape1.id)
      expect(result.current.selectedShapeIds).toContain(testShape2.id)
      expect(result.current.selectedShapeIds).toContain(testShape3.id)
    })

    it('should deselect a shape', () => {
      const { result } = renderHook(() => useShapesStore())

      act(() => {
        result.current.selectShape(testShape1.id)
        result.current.selectShape(testShape2.id, true)
        result.current.deselectShape(testShape1.id)
      })

      expect(result.current.selectedShapeIds).not.toContain(testShape1.id)
      expect(result.current.selectedShapeIds).toContain(testShape2.id)
    })

    it('should clear all selections', () => {
      const { result } = renderHook(() => useShapesStore())

      act(() => {
        result.current.selectShape(testShape1.id)
        result.current.selectShape(testShape2.id, true)
        result.current.clearSelection()
      })

      expect(result.current.selectedShapeIds).toEqual([])
    })

    it('should handle selection of non-existent shape', () => {
      const { result } = renderHook(() => useShapesStore())

      act(() => {
        result.current.selectShape('non-existent-id')
      })

      expect(result.current.selectedShapeIds).toContain('non-existent-id')
    })

    it('should not duplicate selections', () => {
      const { result } = renderHook(() => useShapesStore())

      act(() => {
        result.current.selectShape(testShape1.id)
        result.current.selectShape(testShape1.id, true) // Select again with multiSelect
      })

      expect(result.current.selectedShapeIds).toHaveLength(1)
      expect(result.current.selectedShapeIds).toContain(testShape1.id)
    })
  })

  describe('Z-Index Management', () => {
    beforeEach(() => {
      const { result } = renderHook(() => useShapesStore())
      act(() => {
        result.current.addShape(testShape1) // intraLayerZIndex: 0
        result.current.addShape(testShape2) // intraLayerZIndex: 1
        result.current.addShape(testShape3) // intraLayerZIndex: 2
      })
    })

    it('should publish event when bringing shape to front', () => {
      const { result } = renderHook(() => useShapesStore())

      act(() => {
        result.current.bringToFrontInLayer(testShape1.id)
      })

      expect(mockEventBus.publish).toHaveBeenCalled()
    })

    it('should publish event when sending shape to back', () => {
      const { result } = renderHook(() => useShapesStore())

      act(() => {
        result.current.sendToBackInLayer(testShape3.id)
      })

      expect(mockEventBus.publish).toHaveBeenCalled()
    })

    it('should handle z-index operations on non-existent shape', () => {
      const { result } = renderHook(() => useShapesStore())

      expect(() => {
        act(() => {
          result.current.bringToFrontInLayer('non-existent-id')
        })
      }).not.toThrow()

      expect(() => {
        act(() => {
          result.current.sendToBackInLayer('non-existent-id')
        })
      }).not.toThrow()
    })
  })

  describe('State Persistence', () => {
    it('should maintain state across re-renders', () => {
      const { result, rerender } = renderHook(() => useShapesStore())

      act(() => {
        result.current.addShape(testShape1)
        result.current.selectShape(testShape1.id)
      })

      rerender()

      expect(result.current.shapes).toHaveLength(1)
      expect(result.current.selectedShapeIds).toContain(testShape1.id)
    })
  })

  describe('Edge Cases', () => {
    it('should handle empty operations gracefully', () => {
      const { result } = renderHook(() => useShapesStore())

      expect(() => {
        act(() => {
          result.current.clearShapes()
          result.current.clearSelection()
          result.current.selectShapes([])
          result.current.setShapesFromExternal([])
        })
      }).not.toThrow()
    })

    it('should handle invalid shape data', () => {
      const { result } = renderHook(() => useShapesStore())
      const invalidShape = null as any

      expect(() => {
        act(() => {
          result.current.addShape(invalidShape)
        })
      }).not.toThrow()
    })

    it('should handle concurrent operations', () => {
      const { result } = renderHook(() => useShapesStore())

      act(() => {
        result.current.addShape(testShape1)
        result.current.selectShape(testShape1.id)
        result.current.bringToFrontInLayer(testShape1.id)
        result.current.deselectShape(testShape1.id)
      })

      expect(result.current.shapes).toHaveLength(1)
      expect(result.current.selectedShapeIds).toEqual([])
    })
  })
})
