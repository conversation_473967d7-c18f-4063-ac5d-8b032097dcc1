/**
 * Data Event Subscribers Module
 *
 * This module provides helper functions for subscribing to data-related events
 * from the application's event bus. It covers file operations, document state changes,
 * and configuration updates.
 *
 * All functions follow dependency injection principles by accepting an EventBus
 * instance as their first parameter.
 *
 * @module event-bus/helpers/subscribers/data-subscribers
 */

import type { EventBus } from '@/types/services/events'
import type {
  DataLoadEvent,
  ExportEvent,
  HistoryEvent,
  TemplateEvent,
} from '@/types/services/events/dataEvents'
import type { EventSubscriptionOptions } from '@/types/services/events/eventCore'
import { AppEventType } from '@/types/services/events'
import { typedSubscribe } from './utils'

// --- History Events ---

/**
 * Subscribe to history events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param action - The history action (undo, redo, checkpoint, error)
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToHistoryEvents(
  eventBus: EventBus,
  handler: (event: HistoryEvent) => void,
  action: 'undo' | 'redo' | 'checkpoint' | 'error',
  options?: EventSubscriptionOptions,
): () => void {
  let eventType: AppEventType

  switch (action) {
    case 'undo':
      eventType = AppEventType.HistoryUndo
      break
    case 'redo':
      eventType = AppEventType.HistoryRedo
      break
    case 'checkpoint':
      eventType = AppEventType.HistoryCheckpoint
      break
    case 'error':
      eventType = AppEventType.HistoryError
      break
    default:
      throw new Error(`Invalid history action: ${action as string}`)
  }

  return typedSubscribe<HistoryEvent>(eventBus, eventType, handler, options)
}

// --- Template Events ---

/**
 * Subscribe to template apply events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param phase - The phase of the process (request, validate, complete, error)
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToTemplateApplyEvents(
  eventBus: EventBus,
  handler: (event: TemplateEvent) => void,
  phase: 'apply' | 'request' | 'validate' | 'complete' | 'error',
  options?: EventSubscriptionOptions,
): () => void {
  let eventType: AppEventType

  switch (phase) {
    case 'apply':
      eventType = AppEventType.TemplateApply
      break
    case 'request':
      eventType = AppEventType.TemplateApplyRequest
      break
    case 'validate':
      eventType = AppEventType.TemplateApplyValidate
      break
    case 'complete':
      eventType = AppEventType.TemplateApplyComplete
      break
    case 'error':
      eventType = AppEventType.TemplateApplyError
      break
    default:
      throw new Error(`Invalid template apply phase: ${phase as string}`)
  }

  return typedSubscribe<TemplateEvent>(eventBus, eventType, handler, options)
}

// --- Export Events ---

/**
 * Subscribe to export events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param phase - The phase of the process (request, prepare, progress, complete, error)
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToExportEvents(
  eventBus: EventBus,
  handler: (event: ExportEvent) => void,
  phase: 'request' | 'prepare' | 'progress' | 'complete' | 'error',
  options?: EventSubscriptionOptions,
): () => void {
  let eventType: AppEventType

  switch (phase) {
    case 'request':
      eventType = AppEventType.ExportRequest
      break
    case 'prepare':
      eventType = AppEventType.ExportPrepare
      break
    case 'progress':
      eventType = AppEventType.ExportProgress
      break
    case 'complete':
      eventType = AppEventType.ExportComplete
      break
    case 'error':
      eventType = AppEventType.ExportError
      break
    default:
      throw new Error(`Invalid export phase: ${phase as string}`)
  }

  return typedSubscribe<ExportEvent>(eventBus, eventType, handler, options)
}

// --- Data Load Events ---

/**
 * Subscribe to data load events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param phase - The phase of the process (request, progress, complete, error)
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToDataLoadEvents(
  eventBus: EventBus,
  handler: (event: DataLoadEvent) => void,
  phase: 'request' | 'progress' | 'complete' | 'error',
  options?: EventSubscriptionOptions,
): () => void {
  let eventType: AppEventType

  switch (phase) {
    case 'request':
      eventType = AppEventType.DataLoadRequest
      break
    case 'progress':
      eventType = AppEventType.DataLoadProgress
      break
    case 'complete':
      eventType = AppEventType.DataLoadComplete
      break
    case 'error':
      eventType = AppEventType.DataLoadError
      break
    default:
      throw new Error(`Invalid data load phase: ${phase as string}`)
  }

  return typedSubscribe<DataLoadEvent>(eventBus, eventType, handler, options)
}
