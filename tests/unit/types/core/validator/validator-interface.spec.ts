import type { <PERSON><PERSON><PERSON>Validator, ValidatableShape, ValidationError, ValidationOptions, ValidationResult, ValidationWarning } from '@/types/core/validator/validator-interface'

import { describe, expect, it } from 'vitest'
import { ValidationErrorCode } from '@/types/core/validator/error-codes'

describe('validator Interface Types', () => {
  it('should allow declaration of ValidationOptions', () => {
    // Create a ValidationOptions object
    const options: ValidationOptions = {
      strict: true,
      requiredOnly: false,
      context: { maxSize: 1000 },
      recursive: true,
      maxDepth: 3,
    }

    expect(options.strict).toBe(true)
    expect(options.requiredOnly).toBe(false)
    expect(options.context).toEqual({ maxSize: 1000 })
    expect(options.recursive).toBe(true)
    expect(options.maxDepth).toBe(3)

    // Test with partial options
    const partialOptions: ValidationOptions = {
      strict: true,
    }

    expect(partialOptions.strict).toBe(true)
    expect(partialOptions.requiredOnly).toBeUndefined()
    expect(partialOptions.context).toBeUndefined()
  })

  it('should allow declaration of ValidationResult', () => {
    // Create a successful ValidationResult
    const successResult: ValidationResult = {
      valid: true,
    }

    expect(successResult.valid).toBe(true)
    expect(successResult.message).toBeUndefined()
    expect(successResult.errors).toBeUndefined()
    expect(successResult.warnings).toBeUndefined()

    // Create a failed ValidationResult with errors
    const errors: ValidationError[] = [
      {
        code: ValidationErrorCode.INVALID_DIMENSION,
        message: 'Width must be positive',
        path: 'width',
        value: -10,
      },
      {
        code: ValidationErrorCode.INVALID_FILL_COLOR,
        message: 'Invalid fill color format',
        path: 'fillColor',
        value: 'not-a-color',
      },
    ]

    const failResult: ValidationResult = {
      valid: false,
      message: 'Validation failed with 2 errors',
      errors,
    }

    expect(failResult.valid).toBe(false)
    expect(failResult.message).toBe('Validation failed with 2 errors')
    expect(failResult.errors).toEqual(errors)
    expect(failResult.errors?.[0].code).toBe(ValidationErrorCode.INVALID_DIMENSION)
    expect(failResult.errors?.[1].code).toBe(ValidationErrorCode.INVALID_FILL_COLOR)

    // Create a ValidationResult with warnings
    const warnings: ValidationWarning[] = [
      {
        code: ValidationErrorCode.VALIDATION_SPECIFIC_ERROR,
        message: 'Size is close to maximum allowed',
        path: 'width',
        value: 990,
      },
    ]

    const warningResult: ValidationResult = {
      valid: true,
      warnings,
    }

    expect(warningResult.valid).toBe(true)
    expect(warningResult.warnings).toEqual(warnings)
    expect(warningResult.warnings?.[0].code).toBe(ValidationErrorCode.VALIDATION_SPECIFIC_ERROR)
  })

  it('should allow declaration of ValidationError', () => {
    // Create a ValidationError
    const error: ValidationError = {
      code: ValidationErrorCode.INVALID_RADIUS,
      message: 'Radius must be positive',
      path: 'radius',
      value: 0,
    }

    expect(error.code).toBe(ValidationErrorCode.INVALID_RADIUS)
    expect(error.message).toBe('Radius must be positive')
    expect(error.path).toBe('radius')
    expect(error.value).toBe(0)

    // Create a ValidationError without optional properties
    const minimalError: ValidationError = {
      code: ValidationErrorCode.MISSING_OR_INVALID_ID,
      message: 'ID is required',
    }

    expect(minimalError.code).toBe(ValidationErrorCode.MISSING_OR_INVALID_ID)
    expect(minimalError.message).toBe('ID is required')
    expect(minimalError.path).toBeUndefined()
    expect(minimalError.value).toBeUndefined()
  })

  it('should allow declaration of ValidationWarning', () => {
    // Create a ValidationWarning
    const warning: ValidationWarning = {
      code: ValidationErrorCode.VALIDATION_SPECIFIC_ERROR,
      message: 'Size is close to maximum allowed',
      path: 'width',
      value: 990,
    }

    expect(warning.code).toBe(ValidationErrorCode.VALIDATION_SPECIFIC_ERROR)
    expect(warning.message).toBe('Size is close to maximum allowed')
    expect(warning.path).toBe('width')
    expect(warning.value).toBe(990)

    // Create a ValidationWarning without optional properties
    const minimalWarning: ValidationWarning = {
      code: ValidationErrorCode.VALIDATION_RULE_ERROR,
      message: 'Consider using a different shape type',
    }

    expect(minimalWarning.code).toBe(ValidationErrorCode.VALIDATION_RULE_ERROR)
    expect(minimalWarning.message).toBe('Consider using a different shape type')
    expect(minimalWarning.path).toBeUndefined()
    expect(minimalWarning.value).toBeUndefined()
  })

  it('should allow declaration of ValidatableShape', () => {
    // Create a ValidatableShape
    const shape: ValidatableShape = {
      type: 'rectangle',
      id: 'rect-1',
      strokeColor: '#000000',
      fillColor: '#FFFFFF',
    }

    expect(shape.type).toBe('rectangle')
    expect(shape.id).toBe('rect-1')
    expect(shape.strokeColor).toBe('#000000')
    expect(shape.fillColor).toBe('#FFFFFF')

    // Create a ValidatableShape with minimal properties
    const minimalShape: ValidatableShape = {
      type: 'circle',
      id: 'circle-1',
    }

    expect(minimalShape.type).toBe('circle')
    expect(minimalShape.id).toBe('circle-1')
    expect(minimalShape.strokeColor).toBeUndefined()
    expect(minimalShape.fillColor).toBeUndefined()
  })

  it('should allow implementation of ShapeValidator interface', () => {
    // Create a mock implementation of ShapeValidator
    const mockValidator: ShapeValidator = {
      validate: (shape: ValidatableShape, options?: ValidationOptions): ValidationResult => {
        // Simple validation logic for testing
        if (!shape.id || shape.id.trim() === '') {
          return {
            valid: false,
            message: 'ID is required',
            errors: [{
              code: ValidationErrorCode.MISSING_OR_INVALID_ID,
              message: 'ID is required',
            }],
          }
        }

        if (options?.strict && (!shape.strokeColor || !shape.fillColor)) {
          return {
            valid: false,
            message: 'Colors are required in strict mode',
            errors: [{
              code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
              message: 'Colors are required in strict mode',
            }],
          }
        }

        return { valid: true }
      },

      validateType: (type: string, options?: ValidationOptions): ValidationResult => {
        // Simple type validation logic for testing
        const validTypes = ['rectangle', 'circle', 'polygon']

        if (!validTypes.includes(type)) {
          return {
            valid: false,
            message: `Invalid type: ${type}`,
            errors: [{
              code: ValidationErrorCode.INVALID_SHAPE_TYPE,
              message: `Invalid type: ${type}`,
            }],
          }
        }

        return { valid: true }
      },
    }

    // Test the validate method
    const validShape: ValidatableShape = {
      type: 'rectangle',
      id: 'rect-1',
      strokeColor: '#000000',
      fillColor: '#FFFFFF',
    }

    const invalidShape: ValidatableShape = {
      type: 'rectangle',
      id: '',
      strokeColor: '#000000',
      fillColor: '#FFFFFF',
    }

    const incompleteShape: ValidatableShape = {
      type: 'rectangle',
      id: 'rect-2',
    }

    expect(mockValidator.validate(validShape).valid).toBe(true)
    expect(mockValidator.validate(invalidShape).valid).toBe(false)
    expect(mockValidator.validate(incompleteShape).valid).toBe(true)
    expect(mockValidator.validate(incompleteShape, { strict: true }).valid).toBe(false)

    // Test the validateType method
    expect(mockValidator.validateType('rectangle').valid).toBe(true)
    expect(mockValidator.validateType('circle').valid).toBe(true)
    expect(mockValidator.validateType('invalid-type').valid).toBe(false)
  })
})
