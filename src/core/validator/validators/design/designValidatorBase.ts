/**
 * @file designValidatorBase.ts
 * @description Provides an abstract base class for validators of design elements (e.g., Wall, Room, Furniture).
 * This class extends `BaseShapeValidator` and introduces common validation logic specific to design elements,
 * such as validating the `ElementType` and checking for required properties within the element's structure.
 *
 * @module core/validator/validators/design/designValidatorBase
 * @abstract
 * @extends BaseShapeValidator
 */

import type { Element as CoreElement, ElementType as CoreElementType, ShapeElement } from '@/types/core/elementDefinitions' // Renamed ElementType to CoreElementType
import type { ValidatableShape, ValidationError, ValidationResult } from '@/types/core/validator/validator-interface'
import { ValidationErrorCode } from '@/types/core/validator/error-codes'
import { BaseShapeValidator } from '../common/validatorBase'

/**
 * Base validator class for design elements.
 *
 * @abstract
 * @extends BaseShapeValidator
 */
export abstract class BaseDesignValidator extends BaseShapeValidator {
  /**
   * The expected ElementType for this validator.
   *
   * @remarks
   * Concrete classes must implement this.
   *
   * @abstract
   * @readonly
   */
  protected abstract readonly expectedElementType: CoreElementType

  /**
   * Validates that the element has the correct type.
   *
   * @remarks
   * This is typically called at the beginning of the concrete validator's validateSpecific method.
   *
   * @param shape - The element to validate.
   * @returns An array of errors if the type is incorrect, otherwise an empty array.
   * @protected
   */
  protected validateBaseType(shape: ValidatableShape): ValidationError[] {
    const errors: ValidationError[] = []
    if (shape.type !== this.expectedElementType) {
      errors.push({
        code: ValidationErrorCode.INVALID_SHAPE_TYPE,
        message: `Element type must be ${this.expectedElementType}, but got ${shape.type}`,
        path: 'type',
        value: shape.type,
      })
    }
    return errors
  }

  /**
   * Validates that the element has the required properties directly on the object,
   * within its 'properties' field, or within its 'metadata' field.
   *
   * @param shape - The element to validate.
   * @param requiredProperties - Array of required property names.
   * @param checkLocation - Specifies where to look for the properties: "root", "properties", or "metadata".
   * @returns A validation result object.
   * @protected
   */
  protected validateRequiredProperties(
    shape: ValidatableShape,
    requiredProperties: string[],
    checkLocation: 'root' | 'properties' | 'metadata' = 'root',
  ): ValidationResult {
    const errors: ValidationError[] = []
    let objectToCheck: Record<string, unknown> = shape as unknown as Record<string, unknown>
    let basePath = ''

    if (checkLocation === 'properties') {
      const shapeWithProperties = shape as ShapeElement // Assume it might have a properties field
      if (!shapeWithProperties.properties) {
        errors.push({
          code: ValidationErrorCode.MISSING_PROPERTY,
          message: `Element is missing the '${checkLocation}' object.`,
          path: checkLocation,
        })
        return { valid: false, errors }
      }
      objectToCheck = shapeWithProperties.properties
      basePath = 'properties.'
    }
    else if (checkLocation === 'metadata') {
      const elementWithMetadata = shape as CoreElement // CoreElement has metadata
      if (!elementWithMetadata.metadata) {
        errors.push({
          code: ValidationErrorCode.MISSING_PROPERTY,
          message: `Element is missing the '${checkLocation}' object.`,
          path: checkLocation,
        })
        return { valid: false, errors }
      }
      objectToCheck = elementWithMetadata.metadata as unknown as Record<string, unknown>
      basePath = 'metadata.'
    }

    for (const prop of requiredProperties) {
      if (objectToCheck[prop] === undefined) {
        errors.push({
          code: ValidationErrorCode.MISSING_PROPERTY,
          message: `Missing required property: ${prop}`,
          path: `${basePath}${prop}`,
        })
      }
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    }
  }

  // validateSpecific is inherited from BaseShapeValidator and must be implemented by concrete subclasses.
  // applySpecificRules is inherited from BaseShapeValidator and can be overridden by concrete subclasses.
}
