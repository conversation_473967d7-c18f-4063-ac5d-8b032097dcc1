/**
 * Creator for Wall Design Elements
 *
 * @remarks
 * This class extends {@link DesignCreator} to specialize in creating wall elements
 * ({@link CoreElementType.WALL}). Walls are fundamental structural and partitioning
 * elements in interior design, typically represented as linear paths with thickness
 * and height.
 *
 * The `createDefault` method initializes a wall with properties such as `wallType`
 * (e.g., {@link WallType.STANDARD}, {@link WallType.EXTERIOR}), `construction`
 * (e.g., {@link WallConstruction.FRAMED}), `thickness`, and `totalHeight`.
 * The wall's geometry is defined by a `startPoint` and `endPoint`, which are
 * stored within its `properties.path` (as a {@link Path.Line}). The element's
 * `position` is calculated as the midpoint of this centerline.
 *
 * The class also provides convenience methods like `createWall`, `createLoadBearingWall`,
 * and `createExteriorWall` that call `createDefault` with predefined or specific
 * properties for common wall types.
 *
 * Note: Placeholders for `DesignMaterial` and `DesignElementCategory` are used locally.
 *
 * @module core/factory/creators/design/WallCreator
 * @see {@link DesignCreator}
 * @see {@link CoreElementType.WALL}
 * @see {@link WallType}
 * @see {@link WallConstruction}
 * @see {@link Path.Line}
 */

import type { ShapeCreationParamsUnion } from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point' // Import PointData
import type {
  Path, // Import the Path namespace
  ShapeElement,
} from '@/types/core/elementDefinitions'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
import { ensureCompleteMetadata } from '@/lib/utils/element/elementUtils'
import { WallConstruction, WallType } from '@/types/core/element/design/wallDesignTypes'
import {
  ElementType as CoreElementType, // Import the Path namespace
} from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'
import { DesignCreator } from './DesignCreator'

/**
 * Placeholder if DesignMaterial and DesignElementCategory are not found elsewhere.
 * @private
 */
type DesignMaterial = string

/**
 * Design element categories.
 * @private
 */
const DesignElementCategory = {
  WALL: 'WALL',
} as const

// Wall types enum (already defined in wallDesignTypes.ts, but kept here if local overrides were intended)
// It's better to import WallType from wallDesignTypes.ts
// enum LocalWallType {
//   STANDARD = 'standard', STRUCTURAL = 'structural', EXTERIOR = 'exterior',
//   PARTITION = 'partition', RETAINING = 'retaining'
// }

/**
 * Creator for wall design elements.
 *
 * @extends DesignCreator
 */
export class WallCreator extends DesignCreator {
  /**
   * The category of design element this creator produces.
   * @protected
   * @readonly
   */
  protected readonly category = DesignElementCategory.WALL

  /**
   * The specific element type this creator produces.
   * @protected
   * @readonly
   */
  protected readonly elementType = CoreElementType.WALL

  /**
   * Creates a Wall element with default properties.
   * This method overrides the base `createDefault` from {@link DesignCreator}.
   *
   * @param id - The unique identifier for the new wall element.
   * @param position - The {@link PointData} representing the wall's nominal position. For walls,
   *                   this is often recalculated as the midpoint of its centerline defined by `startPoint` and `endPoint`.
   * @param majorCategoryOverride - Optional major category override.
   * @param _minorCategoryOverride - Optional minor category override (unused).
   * @param zLevelIdOverride - Optional z-level ID override.
   * @param isFixedCategoryOverride - Optional fixed category flag override.
   * @returns A Promise resolving to the created {@link ShapeElement} representing the wall.
   * @override
   */
  async createDefault(
    id: string,
    position: PointData, // Changed to PointData
    majorCategoryOverride?: MajorCategory,
    _minorCategoryOverride?: MinorCategory, // Prefixed with underscore, remove eslint-disable
    zLevelIdOverride?: string,
    isFixedCategoryOverride?: boolean,
  ): Promise<ShapeElement> {
    // For backward compatibility, handle old-style params if passed as majorCategoryOverride
    const params = majorCategoryOverride as unknown as {
      startPoint?: PointData
      endPoint?: PointData
      stroke?: string
      strokeWidth?: number
      material?: DesignMaterial
      properties?: {
        startPoint?: PointData
        endPoint?: PointData
        wallType?: WallType
        construction?: WallConstruction
        thickness?: number
        totalHeight?: number
        isLoadBearing?: boolean
        baseElevation?: number
        openingIds?: string[]
      }
    }
    const p = params // For easier access to optional properties

    // Define centerline points based on params or default
    const startPoint: PointData = p?.properties?.startPoint // Changed to PointData
      ?? p?.startPoint // Changed to PointData
      ?? { x: position.x - 1000, y: position.y, z: position.z }
    const endPoint: PointData = p?.properties?.endPoint // Changed to PointData
      ?? p?.endPoint // Changed to PointData
      ?? { x: position.x + 1000, y: position.y, z: position.z }

    // The 'position' for a wall element itself might be the midpoint of its centerline
    const wallMidPosition: PointData = { // Changed to PointData
      x: (startPoint.x + endPoint.x) / 2,
      y: (startPoint.y + endPoint.y) / 2,
      z: ((startPoint.z ?? 0) + (endPoint.z ?? 0)) / 2,
    }

    // Create base element using DesignCreator's createDefault, passing the calculated midpoint
    // Use proper MajorCategory and MinorCategory for super.createDefault
    const wallMajorCategory = MajorCategory.FURNITURE // Use FURNITURE as fallback since WALL doesn't exist
    const wallMinorCategory = 'base' as MinorCategory // Default to 'base', can be overridden

    const baseElement = await super.createDefault(id, wallMidPosition, wallMajorCategory, wallMinorCategory, zLevelIdOverride, isFixedCategoryOverride)

    const wallType = p?.properties?.wallType ?? WallType.STANDARD
    const construction = p?.properties?.construction ?? WallConstruction.FRAMED
    const thickness = p?.properties?.thickness ?? 100
    const totalHeight = p?.properties?.totalHeight ?? 2400

    baseElement.properties = {
      ...baseElement.properties,
      type: 'path', // Walls are often represented as paths (their centerline)
      // The 'path' property for a WallElement should conform to one of the Path types
      // For a simple straight wall, it's a Line.
      path: { // This structure should match Path.Line or a generic path definition
        type: CoreElementType.LINE, // Or a more generic 'path_segment'
        start: startPoint,
        end: endPoint,
        // Other properties of Path.Line if necessary
      } as Path.Line, // Cast to Path.Line
      stroke: p?.stroke ?? '#000000',
      strokeWidth: p?.strokeWidth ?? thickness / 10,
      fill: 'none',
      wallType,
      construction,
      thickness,
      height: totalHeight, // Using 'height' for wall height, consistent with WallProperties
      isLoadBearing: p?.properties?.isLoadBearing ?? false,
      baseElevation: p?.properties?.baseElevation ?? 0,
      openingIds: p?.properties?.openingIds ?? [],
      material: p?.material ?? 'drywall',
    }

    baseElement.metadata = ensureCompleteMetadata({
      ...baseElement.metadata,
      name: baseElement.metadata?.name ?? `Wall ${id}`,
      // elementType and category are already set by DesignCreator
      // material, thickness, totalHeight are now in properties
    })
    baseElement.type = CoreElementType.WALL

    return baseElement
  }

  /**
   * Convenience method to create a standard wall element between two specified points.
   *
   * @param id - The unique identifier for the wall.
   * @param startPoint - The {@link PointData} representing the start of the wall's centerline.
   * @param endPoint - The {@link PointData} representing the end of the wall's centerline.
   * @param params - Optional {@link ShapeCreationParamsUnion} for additional properties.
   * @returns A Promise resolving to the created {@link ShapeElement}.
   */
  async createWall(id: string, startPoint: PointData, endPoint: PointData, params?: ShapeCreationParamsUnion): Promise<ShapeElement> { // Changed to PointData
    const wallMidPosition: PointData = { x: (startPoint.x + endPoint.x) / 2, y: (startPoint.y + endPoint.y) / 2, z: ((startPoint.z ?? 0) + (endPoint.z ?? 0)) / 2 } // Changed to PointData
    const wallParams = {
      ...(params || {}),
      properties: {
        ...(params && 'properties' in params && params.properties ? params.properties : {}),
        startPoint,
        endPoint,
      },
    } as ShapeCreationParamsUnion
    return this.createDefault(id, wallMidPosition, wallParams as unknown as MajorCategory)
  }

  /**
   * Convenience method to create a load-bearing wall element between two specified points.
   *
   * @param id - The unique identifier for the load-bearing wall.
   * @param startPoint - The {@link PointData} representing the start of the wall's centerline.
   * @param endPoint - The {@link PointData} representing the end of the wall's centerline.
   * @param params - Optional {@link ShapeCreationParamsUnion} for additional properties.
   *                 Default properties for load-bearing walls (type, construction, thickness, material) will be applied.
   * @returns A Promise resolving to the created {@link ShapeElement}.
   */
  async createLoadBearingWall(id: string, startPoint: PointData, endPoint: PointData, params?: ShapeCreationParamsUnion): Promise<ShapeElement> { // Changed to PointData
    const p = params
    const wallParams = {
      ...p,
      properties: {
        ...p?.properties,
        startPoint,
        endPoint,
        isLoadBearing: true,
        wallType: WallType.LOAD_BEARING,
        construction: WallConstruction.MASONRY,
        thickness: 200,
        material: 'concrete' as DesignMaterial,
        strokeWidth: 8, // Example visual override
        stroke: '#444444',
      },
    } as ShapeCreationParamsUnion
    const wallMidPosition: PointData = { x: (startPoint.x + endPoint.x) / 2, y: (startPoint.y + endPoint.y) / 2, z: ((startPoint.z ?? 0) + (endPoint.z ?? 0)) / 2 } // Changed to PointData
    return this.createDefault(id, wallMidPosition, wallParams as unknown as MajorCategory)
  }

  /**
   * Convenience method to create an exterior wall element between two specified points.
   *
   * @param id - The unique identifier for the exterior wall.
   * @param startPoint - The {@link PointData} representing the start of the wall's centerline.
   * @param endPoint - The {@link PointData} representing the end of the wall's centerline.
   * @param params - Optional {@link ShapeCreationParamsUnion} for additional properties.
   *                 Default properties for exterior walls (type, thickness, ratings, material) will be applied.
   * @returns A Promise resolving to the created {@link ShapeElement}.
   */
  async createExteriorWall(id: string, startPoint: PointData, endPoint: PointData, params?: ShapeCreationParamsUnion): Promise<ShapeElement> { // Changed to PointData
    const p = params
    const wallParams = {
      ...p,
      properties: {
        ...p?.properties,
        startPoint,
        endPoint,
        wallType: WallType.EXTERIOR,
        thickness: 250,
        fireRating: 60,
        thermalRating: 2.5,
        material: 'brick' as DesignMaterial,
        strokeWidth: 7, // Example visual override
        stroke: '#222222',
      },
    } as ShapeCreationParamsUnion
    const wallMidPosition: PointData = { x: (startPoint.x + endPoint.x) / 2, y: (startPoint.y + endPoint.y) / 2, z: ((startPoint.z ?? 0) + (endPoint.z ?? 0)) / 2 } // Changed to PointData
    return this.createDefault(id, wallMidPosition, wallParams as unknown as MajorCategory)
  }
}
