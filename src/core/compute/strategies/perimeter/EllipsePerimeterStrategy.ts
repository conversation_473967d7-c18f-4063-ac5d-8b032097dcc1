/**
 * Perimeter Calculation Strategy for Ellipse and Circle Elements
 *
 * @remarks
 * This strategy implements the {@link PerimeterCalculatorStrategy} for calculating
 * the approximate perimeter of Ellipse ({@link CoreElementType.ELLIPSE}) and
 * Circle ({@link CoreElementType.CIRCLE}) elements.
 *
 * For Circles, it uses the standard formula: `2 * PI * radius`.
 * For Ellipses, it uses <PERSON><PERSON><PERSON>'s approximation for the perimeter, which provides
 * a good balance between accuracy and computational simplicity.
 *
 * The calculations are delegated to utility functions `calculateCirclePerimeter` and
 * `calculateEllipsePerimeter` from `../../../../lib/utils/geometry/ellipseUtils`.
 *
 * This strategy is registered for the `ELLIPSE` type but internally handles `CIRCLE`
 * as a special case. It validates that the element is of the correct type and that
 * its radii are valid non-negative numbers.
 *
 * @module core/compute/strategies/perimeter/EllipsePerimeterStrategy
 * @see {@link Shape.Ellipse}
 * @see {@link Shape.Circle}
 * @see {@link PerimeterCalculatorStrategy}
 * @see {@link calculateEllipsePerimeter}
 * @see {@link calculateCirclePerimeter}
 */
import type { PerimeterCalculatorStrategy } from '../../../../types/core/compute'
import type {
  Element,
  Shape,
  ShapeElement,
} from '../../../../types/core/elementDefinitions'
import { calculateCirclePerimeter, calculateEllipsePerimeter } from '../../../../lib/utils/geometry/ellipseUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

/**
 * Implements the {@link PerimeterCalculatorStrategy} for {@link CoreElementType.ELLIPSE}
 * and {@link CoreElementType.CIRCLE} elements.
 * It uses Ramanujan's approximation for ellipses and the standard formula for circles.
 */
export class EllipsePerimeterStrategy implements PerimeterCalculatorStrategy {
  /**
   * Calculates the approximate perimeter of an Ellipse or Circle element.
   *
   * @param element - The shape element, expected to be of type {@link ShapeElement} and
   *                  specifically {@link CoreElementType.ELLIPSE} or {@link CoreElementType.CIRCLE}.
   * @returns The calculated approximate perimeter.
   * @throws {@link CoreError} if the element type is not `ELLIPSE` or `CIRCLE`.
   * @throws {@link CoreError} if the element's radii (`radius` for circle, `radiusX`/`radiusY` for ellipse)
   *         are invalid (e.g., non-numeric or negative).
   */
  public calculatePerimeter(element: Element): number {
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement.type === CoreElementType.CIRCLE) {
      const circleElement = shapeElement as unknown as Shape.Circle // Added unknown
      const radius = circleElement.properties?.radius ?? 0 // Access through properties

      if (typeof radius !== 'number' || Number.isNaN(radius) || radius < 0) { // Allow radius = 0
        throw new CoreError(
          ErrorType.InvalidParameter,
          `Invalid radius for Circle (ID: ${element.id}). Radius: ${radius}.`,
          undefined,
          { component: 'EllipsePerimeterStrategy', operation: 'calculatePerimeter', target: element.id, metadata: { radius } },
        )
      }
      if (radius === 0)
        return 0
      return calculateCirclePerimeter(radius)
    }

    if (shapeElement.type === CoreElementType.ELLIPSE) {
      const ellipseElement = shapeElement as unknown as Shape.Ellipse // Added unknown
      const radiusX = typeof ellipseElement.properties?.radiusX === 'number' ? ellipseElement.properties.radiusX : 0 // Access through properties
      const radiusY = typeof ellipseElement.properties?.radiusY === 'number' ? ellipseElement.properties.radiusY : 0 // Access through properties

      if (typeof radiusX !== 'number' || typeof radiusY !== 'number' || Number.isNaN(radiusX) || Number.isNaN(radiusY) || radiusX < 0 || radiusY < 0) { // Allow 0 radii
        throw new CoreError(
          ErrorType.InvalidParameter,
          `Invalid radii for Ellipse (ID: ${element.id}). RadiusX: ${radiusX}, RadiusY: ${radiusY}.`,
          undefined,
          { component: 'EllipsePerimeterStrategy', operation: 'calculatePerimeter', target: element.id, metadata: { radiusX, radiusY } },
        )
      }
      if (radiusX === 0 && radiusY === 0)
        return 0
      if (radiusX === radiusY) { // It's effectively a circle
        return calculateCirclePerimeter(radiusX)
      }
      return calculateEllipsePerimeter(radiusX, radiusY) // This is an approximation
    }

    throw new CoreError(
      ErrorType.InvalidElementType,
      `EllipsePerimeterStrategy: Expected ELLIPSE or CIRCLE, got type: ${shapeElement?.type ?? 'unknown'} (ID: ${shapeElement?.id ?? 'unknown'}).`,
      undefined,
      { component: 'EllipsePerimeterStrategy', operation: 'calculatePerimeter', target: shapeElement.id },
    )
  }

  /**
   * Returns the primary element type this strategy is registered for.
   *
   * @remarks
   * This strategy handles both `ELLIPSE` and `CIRCLE` types, with `CIRCLE`
   * being a special case of `ELLIPSE`. It is typically registered under
   * {@link CoreElementType.ELLIPSE}.
   *
   * @returns {@link CoreElementType.ELLIPSE} - The ellipse element type that this strategy handles
   */
  public getElementType(): CoreElementType {
    return CoreElementType.ELLIPSE
  }
}
