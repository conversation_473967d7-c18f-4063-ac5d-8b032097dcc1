import { describe, expect, it } from 'vitest'

// Import individual exports to verify they're correctly re-exported
import { ValidationErrorCode } from '@/types/core/validator/error-codes'
// Import all exports from the validator index file
import * as validatorExports from '@/types/core/validator/validator'

describe('validator Index Exports', () => {
  it('should re-export ValidationErrorCode from error-codes.ts', () => {
    expect(validatorExports.ValidationErrorCode).toBe(ValidationErrorCode)

    // Check a few specific error codes to ensure they're correctly exported
    expect(validatorExports.ValidationErrorCode.MISSING_OR_INVALID_ID).toBe('MISSING_OR_INVALID_ID')
    expect(validatorExports.ValidationErrorCode.INVALID_DIMENSION).toBe('INVALID_DIMENSION')
    expect(validatorExports.ValidationErrorCode.INSUFFICIENT_POINTS).toBe('INSUFFICIENT_POINTS')
  })

  it('should re-export types from validator-interface.ts', () => {
    // Create objects using the re-exported types to verify they're correctly exported
    const options: validatorExports.ValidationOptions = {
      strict: true,
      requiredOnly: false,
    }

    expect(options.strict).toBe(true)
    expect(options.requiredOnly).toBe(false)

    const result: validatorExports.ValidationResult = {
      valid: true,
      message: 'Validation successful',
    }

    expect(result.valid).toBe(true)
    expect(result.message).toBe('Validation successful')

    const error: validatorExports.ValidationError = {
      code: validatorExports.ValidationErrorCode.INVALID_RADIUS,
      message: 'Radius must be positive',
    }

    expect(error.code).toBe(validatorExports.ValidationErrorCode.INVALID_RADIUS)
    expect(error.message).toBe('Radius must be positive')

    const warning: validatorExports.ValidationWarning = {
      code: validatorExports.ValidationErrorCode.VALIDATION_SPECIFIC_ERROR,
      message: 'Size is close to maximum allowed',
    }

    expect(warning.code).toBe(validatorExports.ValidationErrorCode.VALIDATION_SPECIFIC_ERROR)
    expect(warning.message).toBe('Size is close to maximum allowed')

    const shape: validatorExports.ValidatableShape = {
      type: 'rectangle',
      id: 'rect-1',
    }

    expect(shape.type).toBe('rectangle')
    expect(shape.id).toBe('rect-1')

    // Create a mock validator using the re-exported interface
    const mockValidator: validatorExports.ShapeValidator = {
      validate: (shape: validatorExports.ValidatableShape): validatorExports.ValidationResult => {
        return { valid: true }
      },
      validateType: (type: string): validatorExports.ValidationResult => {
        return { valid: true }
      },
    }

    expect(typeof mockValidator.validate).toBe('function')
    expect(typeof mockValidator.validateType).toBe('function')
  })
})
