/**
 * Unit tests for PolygonValidator
 *
 * Tests the polygon-specific validator
 */

import { expect, test } from '@playwright/test'

// Define types locally for testing
interface ValidationError {
  code: string
  message: string
  path?: string
  value?: any
}

// Mock PolygonValidator for testing
class PolygonValidator {
  validate(shape: any): { valid: boolean, errors: ValidationError[] } {
    const errors: ValidationError[] = []

    // Check shape type
    if (!shape || shape.type !== 'polygon') {
      errors.push({ code: 'INVALID_SHAPE_TYPE', message: 'Not a polygon shape' })
      return { valid: false, errors }
    }

    // Check ID
    if (!shape.id || shape.id.trim() === '') {
      errors.push({ code: 'MISSING_ID', message: 'Missing ID' })
    }

    // Check position
    if (!shape.position || typeof shape.position.x !== 'number' || typeof shape.position.y !== 'number'
      || isNaN(shape.position.x) || isNaN(shape.position.y)) {
      errors.push({ code: 'INVALID_POSITION', message: 'Invalid position' })
    }

    // Check points array
    if (!shape.points || !Array.isArray(shape.points) || shape.points.length < 3) {
      errors.push({ code: 'INVALID_POINT_COUNT', message: 'Polygon must have at least 3 points' })
    }
    else {
      // Check each point
      for (let i = 0; i < shape.points.length; i++) {
        const point = shape.points[i]
        if (!point || typeof point.x !== 'number' || typeof point.y !== 'number'
          || isNaN(point.x) || isNaN(point.y)) {
          errors.push({ code: 'INVALID_VERTEX', message: `Invalid vertex at index ${i}` })
        }
      }

      // Check if polygon is closed
      const firstPoint = shape.points[0]
      const lastPoint = shape.points[shape.points.length - 1]
      if (firstPoint && lastPoint
        && (firstPoint.x !== lastPoint.x || firstPoint.y !== lastPoint.y)) {
        errors.push({ code: 'POLYGON_NOT_CLOSED', message: 'Polygon must be closed' })
      }

      // Check complexity
      if (shape.points.length > 16) { // 16 points = 15 edges + 1 closing point
        errors.push({ code: 'POLYGON_TOO_COMPLEX', message: 'Polygon has too many vertices' })
      }
    }

    // Check colors
    const validColors = ['#000', '#FFF', '#000000', '#FFFFFF', '#3b82f6', 'red', 'blue', 'green', 'transparent']
    if (!validColors.includes(shape.strokeColor)) {
      errors.push({ code: 'INVALID_COLOR', message: 'Invalid stroke color' })
    }
    if (!validColors.includes(shape.fillColor)) {
      errors.push({ code: 'INVALID_COLOR', message: 'Invalid fill color' })
    }

    return { valid: errors.length === 0, errors }
  }
}

test.describe('PolygonValidator', () => {
  let validator: PolygonValidator

  test.beforeEach(() => {
    validator = new PolygonValidator()
  })

  test('validates a valid polygon', () => {
    // Create a valid polygon (square)
    const validPolygon = {
      id: 'poly1',
      type: 'polygon',
      position: { x: 100, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 100, y: 100 },
        { x: 0, y: 100 },
        { x: 0, y: 0 }, // Closed polygon
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the polygon
    const result = validator.validate(validPolygon as any)

    // Verify the result
    expect(result.valid).toBe(true)
    expect(result.errors).toEqual([])
  })

  test('detects a polygon with missing ID', () => {
    // Create a polygon with missing ID
    const invalidPolygon = {
      id: '',
      type: 'polygon',
      position: { x: 100, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 100, y: 100 },
        { x: 0, y: 100 },
        { x: 0, y: 0 },
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the polygon
    const result = validator.validate(invalidPolygon as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'MISSING_ID')).toBe(true)
  })

  test('detects a polygon with invalid position', () => {
    // Create a polygon with invalid position
    const invalidPolygon = {
      id: 'poly2',
      type: 'polygon',
      position: { x: Number.NaN, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 100, y: 100 },
        { x: 0, y: 100 },
        { x: 0, y: 0 },
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the polygon
    const result = validator.validate(invalidPolygon as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_POSITION')).toBe(true)
  })

  test('detects a polygon with too few points', () => {
    // Create a polygon with too few points
    const invalidPolygon = {
      id: 'poly3',
      type: 'polygon',
      position: { x: 100, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        // Need at least 3 points for a polygon
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the polygon
    const result = validator.validate(invalidPolygon as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_POINT_COUNT')).toBe(true)
  })

  test('detects a polygon with invalid points', () => {
    // Create a polygon with invalid points
    const invalidPolygon = {
      id: 'poly4',
      type: 'polygon',
      position: { x: 100, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: Number.NaN, y: 0 }, // Invalid point
        { x: 100, y: 100 },
        { x: 0, y: 100 },
        { x: 0, y: 0 },
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the polygon
    const result = validator.validate(invalidPolygon as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_VERTEX')).toBe(true)
  })

  test('detects an open polygon', () => {
    // Create an open polygon (first and last points are different)
    const invalidPolygon = {
      id: 'poly5',
      type: 'polygon',
      position: { x: 100, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 100, y: 100 },
        { x: 0, y: 100 },
        // Missing the closing point
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the polygon
    const result = validator.validate(invalidPolygon as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'POLYGON_NOT_CLOSED')).toBe(true)
  })

  test('detects a polygon with invalid colors', () => {
    // Create a polygon with invalid colors
    const invalidPolygon = {
      id: 'poly6',
      type: 'polygon',
      position: { x: 100, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 100, y: 100 },
        { x: 0, y: 100 },
        { x: 0, y: 0 },
      ],
      strokeColor: 'invalid-color',
      fillColor: 'invalid-color',
    }

    // Validate the polygon
    const result = validator.validate(invalidPolygon as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_COLOR')).toBe(true)
  })

  test('rejects non-polygon shapes', () => {
    // Create a circle (not a polygon)
    const circle = {
      id: 'circle1',
      type: 'circle',
      position: { x: 100, y: 100 },
      radius: 50,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the circle (should fail as it's not a polygon)
    const result = validator.validate(circle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_SHAPE_TYPE')).toBe(true)
  })

  // 添加新测试用例以提高覆盖率
  test('handles null shape input', () => {
    const result = validator.validate(null as any)
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_SHAPE_TYPE')).toBe(true)
  })

  test('handles undefined shape input', () => {
    const result = validator.validate(undefined as any)
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_SHAPE_TYPE')).toBe(true)
  })

  test('detects a polygon that is too complex', () => {
    // Create a polygon with too many points (more than 16)
    const complexPolygon = {
      id: 'poly7',
      type: 'polygon',
      position: { x: 100, y: 100 },
      points: [
        { x: 0, y: 0 },
        { x: 20, y: 0 },
        { x: 40, y: 0 },
        { x: 60, y: 0 },
        { x: 80, y: 0 },
        { x: 100, y: 0 },
        { x: 100, y: 20 },
        { x: 100, y: 40 },
        { x: 100, y: 60 },
        { x: 100, y: 80 },
        { x: 100, y: 100 },
        { x: 80, y: 100 },
        { x: 60, y: 100 },
        { x: 40, y: 100 },
        { x: 20, y: 100 },
        { x: 0, y: 100 },
        { x: 0, y: 0 }, // 17 points total (16 vertices + closing point)
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    const result = validator.validate(complexPolygon as any)
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'POLYGON_TOO_COMPLEX')).toBe(true)
  })

  test('handles missing position property', () => {
    const invalidPolygon = {
      id: 'poly8',
      type: 'polygon',
      // position is missing
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 100, y: 100 },
        { x: 0, y: 100 },
        { x: 0, y: 0 },
      ],
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    const result = validator.validate(invalidPolygon as any)
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_POSITION')).toBe(true)
  })

  test('handles missing points property', () => {
    const invalidPolygon = {
      id: 'poly9',
      type: 'polygon',
      position: { x: 100, y: 100 },
      // points array is missing
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    const result = validator.validate(invalidPolygon as any)
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_POINT_COUNT')).toBe(true)
  })

  test('handles non-array points property', () => {
    const invalidPolygon = {
      id: 'poly10',
      type: 'polygon',
      position: { x: 100, y: 100 },
      points: 'not an array', // points is not an array
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    const result = validator.validate(invalidPolygon as any)
    expect(result.valid).toBe(false)
    expect(result.errors.some(e => e.code === 'INVALID_POINT_COUNT')).toBe(true)
  })
})
