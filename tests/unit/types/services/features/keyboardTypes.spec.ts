import { describe, expect, it } from 'vitest'
import * as KeyboardTypesModule from '@/types/services/features/keyboardTypes'
import * as KeyboardModule from '@/types/services/keyboard'

describe('keyboard Types Feature Module', () => {
  it('should be defined as a module', () => {
    // Verify the module exists
    expect(KeyboardTypesModule).toBeDefined()
  })

  it('should re-export types from keyboard module', () => {
    // Check that the module has the same exports as the keyboard module
    const keyboardTypesExports = Object.keys(KeyboardTypesModule)
    const keyboardExports = Object.keys(KeyboardModule)

    // Verify that all exports from keyboard module are re-exported
    for (const exportName of keyboardExports) {
      expect(keyboardTypesExports).toContain(exportName)
    }
  })
})
