import type {
  KeyboardEventContext,
  KeyboardEventExt,
  KeyboardService,
  KeyboardServiceConfig,
  KeyboardShortcut,
  KeyCombination,
  KeyEventData,
  KeyModifiers,
} from '@/types/services/keyboard'
import { describe, expect, it, vi } from 'vitest'

describe('keyboard Types', () => {
  describe('keyModifiers', () => {
    it('should allow creating a key modifiers object', () => {
      const modifiers: KeyModifiers = {
        altKey: true,
        ctrlKey: true,
        shiftKey: false,
        metaKey: false,
      }

      expect(modifiers.altKey).toBe(true)
      expect(modifiers.ctrlKey).toBe(true)
      expect(modifiers.shiftKey).toBe(false)
      expect(modifiers.metaKey).toBe(false)
    })
  })

  describe('keyEventData', () => {
    it('should allow creating a key event data object', () => {
      const modifiers: KeyModifiers = {
        altKey: false,
        ctrlKey: true,
        shiftKey: false,
        metaKey: false,
      }

      const eventData: KeyEventData = {
        key: 'a',
        code: 'KeyA',
        modifiers,
        originalEvent: new KeyboardEvent('keydown', { key: 'a', ctrlKey: true }),
      }

      expect(eventData.key).toBe('a')
      expect(eventData.code).toBe('KeyA')
      expect(eventData.modifiers).toBe(modifiers)
      expect(eventData.originalEvent).toBeDefined()
    })
  })

  describe('keyCombination', () => {
    it('should allow creating a key combination with just a key', () => {
      const combination: KeyCombination = {
        key: 'a',
      }

      expect(combination.key).toBe('a')
      expect(combination.alt).toBeUndefined()
      expect(combination.ctrl).toBeUndefined()
      expect(combination.shift).toBeUndefined()
      expect(combination.meta).toBeUndefined()
    })

    it('should allow creating a key combination with modifiers', () => {
      const combination: KeyCombination = {
        key: 's',
        ctrl: true,
        shift: false,
      }

      expect(combination.key).toBe('s')
      expect(combination.ctrl).toBe(true)
      expect(combination.shift).toBe(false)
      expect(combination.alt).toBeUndefined()
      expect(combination.meta).toBeUndefined()
    })
  })

  describe('keyboardShortcut', () => {
    it('should allow creating a keyboard shortcut', () => {
      const shortcut: KeyboardShortcut = {
        id: 'save',
        description: 'Save document',
        keyCombination: {
          key: 's',
          ctrl: true,
        },
        action: 'SAVE_DOCUMENT',
        enabled: true,
      }

      expect(shortcut.id).toBe('save')
      expect(shortcut.description).toBe('Save document')
      expect(shortcut.keyCombination.key).toBe('s')
      expect(shortcut.keyCombination.ctrl).toBe(true)
      expect(shortcut.action).toBe('SAVE_DOCUMENT')
      expect(shortcut.enabled).toBe(true)
      expect(shortcut.context).toBeUndefined()
      expect(shortcut.priority).toBeUndefined()
    })

    it('should allow creating a shortcut with a function action', () => {
      const actionFn = vi.fn()
      const shortcut: KeyboardShortcut = {
        id: 'delete',
        description: 'Delete selected item',
        keyCombination: {
          key: 'Delete',
        },
        action: actionFn,
        enabled: true,
        context: ['editor'],
        priority: 10,
      }

      expect(shortcut.id).toBe('delete')
      expect(shortcut.description).toBe('Delete selected item')
      expect(shortcut.keyCombination.key).toBe('Delete')
      expect(shortcut.action).toBe(actionFn)
      expect(shortcut.enabled).toBe(true)
      expect(shortcut.context).toEqual(['editor'])
      expect(shortcut.priority).toBe(10)
    })
  })

  describe('keyboardEventContext', () => {
    it('should allow creating a keyboard event context', () => {
      const context: KeyboardEventContext = {
        source: 'canvas',
        mode: 'edit',
        tool: 'rectangle',
        activeElement: 'shape-123',
        customProperty: 'custom value',
      }

      expect(context.source).toBe('canvas')
      expect(context.mode).toBe('edit')
      expect(context.tool).toBe('rectangle')
      expect(context.activeElement).toBe('shape-123')
      expect(context.customProperty).toBe('custom value')
    })
  })

  describe('keyboardEventExt', () => {
    it('should extend the standard KeyboardEvent', () => {
      const context: KeyboardEventContext = {
        source: 'canvas',
        mode: 'edit',
      }

      const event = new KeyboardEvent('keydown', { key: 'a' }) as KeyboardEventExt
      event.appContext = context

      expect(event.key).toBe('a')
      expect(event.type).toBe('keydown')
      expect(event.appContext).toBe(context)
    })
  })

  describe('keyboardServiceConfig', () => {
    it('should allow creating a keyboard service configuration', () => {
      const config: KeyboardServiceConfig = {
        enabled: true,
        preventDefaultForRegistered: true,
        stopPropagationForRegistered: false,
        globalContextFilters: ['editor', 'canvas'],
      }

      expect(config.enabled).toBe(true)
      expect(config.preventDefaultForRegistered).toBe(true)
      expect(config.stopPropagationForRegistered).toBe(false)
      expect(config.globalContextFilters).toEqual(['editor', 'canvas'])
    })
  })

  describe('keyboardService', () => {
    it('should define all required methods', () => {
      // Create a mock implementation of KeyboardService
      const mockKeyboardService: KeyboardService = {
        registerShortcut: vi.fn(),
        unregisterShortcut: vi.fn(),
        getShortcut: vi.fn(),
        getAllShortcuts: vi.fn(),
        setContext: vi.fn(),
        getContext: vi.fn(),
        handleKeyEvent: vi.fn(),
        enableShortcut: vi.fn(),
        disableShortcut: vi.fn(),
        getConfig: vi.fn(),
        setConfig: vi.fn(),
        registerKeyBinding: vi.fn(),
        unregisterKeyBinding: vi.fn(),
        getKeyBindings: vi.fn(),
        addKeyDownListener: vi.fn(),
        addKeyUpListener: vi.fn(),
        initialize: vi.fn(),
        cleanup: vi.fn(),
      }

      expect(typeof mockKeyboardService.registerShortcut).toBe('function')
      expect(typeof mockKeyboardService.unregisterShortcut).toBe('function')
      expect(typeof mockKeyboardService.getShortcut).toBe('function')
      expect(typeof mockKeyboardService.getAllShortcuts).toBe('function')
      expect(typeof mockKeyboardService.setContext).toBe('function')
      expect(typeof mockKeyboardService.getContext).toBe('function')
      expect(typeof mockKeyboardService.handleKeyEvent).toBe('function')
      expect(typeof mockKeyboardService.enableShortcut).toBe('function')
      expect(typeof mockKeyboardService.disableShortcut).toBe('function')
      expect(typeof mockKeyboardService.getConfig).toBe('function')
      expect(typeof mockKeyboardService.setConfig).toBe('function')
      expect(typeof mockKeyboardService.registerKeyBinding).toBe('function')
      expect(typeof mockKeyboardService.unregisterKeyBinding).toBe('function')
      expect(typeof mockKeyboardService.getKeyBindings).toBe('function')
      expect(typeof mockKeyboardService.addKeyDownListener).toBe('function')
      expect(typeof mockKeyboardService.addKeyUpListener).toBe('function')
      expect(typeof mockKeyboardService.initialize).toBe('function')
      expect(typeof mockKeyboardService.cleanup).toBe('function')
    })
  })
})
