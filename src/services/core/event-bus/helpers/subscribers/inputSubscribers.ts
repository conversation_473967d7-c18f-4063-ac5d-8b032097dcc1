/**
 * Input Event Subscriber Module
 *
 * This module provides helper functions for subscribing to input-related events
 * from the application's event bus. It covers keyboard events and mouse events.
 *
 * All functions follow dependency injection principles by accepting an EventBus
 * instance as their first parameter.
 *
 * @module event-bus/helpers/subscribers/input-subscribers
 */

import type { CanvasMouseMoveEvent, EventBus } from '@/types/services/events'
import type { EventSubscriptionOptions } from '@/types/services/events/eventCore'
import type {
  CanvasClickEvent,
  CanvasMouseDownEvent,
  CanvasMouseUpEvent,
} from '@/types/services/events/eventRegistry'
import type { KeyPressedEvent, KeyReleasedEvent } from '@/types/services/events/keyboardEvents'
import { AppEventType } from '@/types/services/events'
import { typedSubscribe } from './utils'

// ===== Keyboard Events =====

/**
 * Subscribe to key pressed events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToKeyPressedEvents(
  eventBus: EventBus,
  handler: (event: KeyPressedEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<KeyPressedEvent>(
    eventBus,
    AppEventType.KeyPressed,
    handler,
    options,
  )
}

/**
 * Subscribe to key released events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToKeyReleasedEvents(
  eventBus: EventBus,
  handler: (event: KeyReleasedEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<KeyReleasedEvent>(
    eventBus,
    AppEventType.KeyReleased,
    handler,
    options,
  )
}

// ===== Canvas Mouse Events =====

/**
 * Subscribe to canvas click events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToCanvasClickEvents(
  eventBus: EventBus,
  handler: (event: CanvasClickEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<CanvasClickEvent>(
    eventBus,
    AppEventType.CanvasClicked,
    handler,
    options,
  )
}

/**
 * Subscribe to canvas double click events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToCanvasDoubleClickEvents(
  eventBus: EventBus,
  handler: (event: CanvasClickEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<CanvasClickEvent>(
    eventBus,
    AppEventType.CanvasDblClicked,
    handler,
    options,
  )
}

/**
 * Subscribe to canvas mouse down events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToCanvasMouseDownEvents(
  eventBus: EventBus,
  handler: (event: CanvasMouseDownEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<CanvasMouseDownEvent>(
    eventBus,
    AppEventType.CanvasMouseDown,
    handler,
    options,
  )
}

/**
 * Subscribe to canvas mouse move events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToCanvasMouseMoveEvents(
  eventBus: EventBus,
  handler: (event: CanvasMouseMoveEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<CanvasMouseMoveEvent>(
    eventBus,
    AppEventType.CanvasMouseMove,
    handler,
    options,
  )
}

/**
 * Subscribe to canvas mouse up events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToCanvasMouseUpEvents(
  eventBus: EventBus,
  handler: (event: CanvasMouseUpEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<CanvasMouseUpEvent>(
    eventBus,
    AppEventType.CanvasMouseUp,
    handler,
    options,
  )
}
