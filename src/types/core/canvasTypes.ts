/**
 * Core Types and Interfaces for Canvas Layer Management
 *
 * @remarks
 * This module defines enumerations, mappings, and interfaces crucial for managing
 * layers on the canvas within an interior design or architectural application.
 * It includes:
 * - {@link CanvasLayer}: An enumeration of all distinct canvas layers.
 * - {@link LayerGroup}: An enumeration for grouping related canvas layers.
 * - Mappings: Constants like {@link LayerGroupMapping}, {@link LayerDisplayNames},
 *   {@link LayerColors}, and {@link DefaultLayerVisibility} to associate layers
 *   with their groups, display names, default colors, and visibility states.
 * - {@link LayerRenderOrder}: An array defining the stacking order of layers.
 * - Helper functions: {@link getLayersInGroup}, {@link getLayerDisplayName}, {@link getLayerColor}.
 * - {@link LayerConfig}: An interface describing the complete configuration for a single layer.
 *
 * These types are fundamental for organizing visual elements and providing users
 * with controls to manage layer visibility and properties.
 *
 * @module types/core/canvasTypes
 * @see {@link ../elementDefinitions} for shape element definitions that reside on these layers.
 */

/**
 * Enumerates the distinct canvas layers available for organizing design elements.
 *
 * @remarks
 * Design elements are categorized into these logical layers to facilitate better
 * organization, visibility control, and management within the application.
 * Each layer typically corresponds to a specific aspect of the design,
 * such as structural components, furniture, or MEP systems.
 *
 * @see {@link LayerGroup} for how these layers are grouped in the UI.
 * @see {@link LayerGroupMapping} for the explicit mapping of layers to groups.
 */
export enum CanvasLayer {
  // Basic structure layers
  /** Floor layer, contains floor plans and basic structures. */
  FLOOR = 'FLOOR',

  /** Wall layer, contains all wall structures. */
  WALLS = 'WALLS',

  /** Ceiling layer, contains ceiling elements like lighting fixtures. */
  CEILING = 'CEILING',

  // Openings and door/window layers
  /** Door layer, contains all doors and their opening paths. */
  DOORS = 'DOORS',

  /** Window layer, contains all windows. */
  WINDOWS = 'WINDOWS',

  // Furniture and equipment layers
  /** Furniture layer, contains furniture objects and decorations. */
  FURNITURE = 'FURNITURE',

  /** Fixtures layer, contains bathroom, kitchen equipment, etc. */
  FIXTURES = 'FIXTURES',

  // MEP layers
  /** Electrical layer, contains outlets, switches, lighting fixtures, etc. */
  ELECTRICAL = 'ELECTRICAL',

  /** Plumbing layer, contains water pipes, heating, etc. */
  PLUMBING = 'PLUMBING',

  /** HVAC layer, contains ventilation ducts, air conditioning, etc. */
  HVAC = 'HVAC',

  // Decoration and annotation layers
  /** Decor layer, contains curtains, decorative items, etc. */
  DECOR = 'DECOR',

  /** Dimension layer, contains dimension lines and measurements. */
  DIMENSIONS = 'DIMENSIONS',

  /** Text and label layer, contains room names, annotations, etc. */
  TEXT = 'TEXT',

  // Special layers
  /** Areas layer, contains rooms, area divisions, etc. */
  AREAS = 'AREAS',

  /** Landscape layer, contains outdoor elements like plants, yards, etc. */
  LANDSCAPE = 'LANDSCAPE',
}

/**
 * Enumerates logical groupings for {@link CanvasLayer}s.
 *
 * @remarks
 * These groups are primarily used to organize layers within the user interface,
 * making it easier for users to manage and understand a potentially large number of layers.
 *
 * @see {@link CanvasLayer} for the individual layers that belong to these groups.
 * @see {@link LayerGroupMapping} for the specific mapping of each layer to its group.
 */
export enum LayerGroup {
  /** Basic structure group. */
  STRUCTURE = 'STRUCTURE',

  /** Openings group. */
  OPENINGS = 'OPENINGS',

  /** Furniture and fixtures group. */
  FURNITURE_FIXTURES = 'FURNITURE_FIXTURES',

  /**
   * MEP (Mechanical, Electrical, Plumbing) group.
   * This group includes layers related to building services.
   */
  MEP = 'MEP',

  /** Annotations and decorations group. */
  ANNOTATIONS = 'ANNOTATIONS',

  /** Special group for layers that don't fit other categories. */
  SPECIAL = 'SPECIAL',
}

/**
 * Maps each {@link CanvasLayer} to its corresponding {@link LayerGroup}.
 *
 * @remarks
 * This constant provides a clear association between individual layers and the
 * logical group they belong to. This is primarily used by UI components to
 * display layers in an organized, hierarchical manner.
 *
 * @see {@link CanvasLayer} for the definition of individual layers.
 * @see {@link LayerGroup} for the definition of layer groups.
 */
export const LayerGroupMapping: Record<CanvasLayer, LayerGroup> = {
  // Basic structure layers
  [CanvasLayer.FLOOR]: LayerGroup.STRUCTURE,
  [CanvasLayer.WALLS]: LayerGroup.STRUCTURE,
  [CanvasLayer.CEILING]: LayerGroup.STRUCTURE,

  // Openings and door/window layers
  [CanvasLayer.DOORS]: LayerGroup.OPENINGS,
  [CanvasLayer.WINDOWS]: LayerGroup.OPENINGS,

  // Furniture and equipment layers
  [CanvasLayer.FURNITURE]: LayerGroup.FURNITURE_FIXTURES,
  [CanvasLayer.FIXTURES]: LayerGroup.FURNITURE_FIXTURES,

  // MEP layers
  [CanvasLayer.ELECTRICAL]: LayerGroup.MEP,
  [CanvasLayer.PLUMBING]: LayerGroup.MEP,
  [CanvasLayer.HVAC]: LayerGroup.MEP,

  // Decoration and annotation layers
  [CanvasLayer.DECOR]: LayerGroup.ANNOTATIONS,
  [CanvasLayer.DIMENSIONS]: LayerGroup.ANNOTATIONS,
  [CanvasLayer.TEXT]: LayerGroup.ANNOTATIONS,

  // Special layers
  [CanvasLayer.AREAS]: LayerGroup.SPECIAL,
  [CanvasLayer.LANDSCAPE]: LayerGroup.SPECIAL,
}

/**
 * Maps {@link CanvasLayer} enum members to their human-readable display names.
 *
 * @remarks
 * This mapping is used to present layer names in the user interface
 * in a more user-friendly format than the raw enum keys.
 * For example, `CanvasLayer.FLOOR` would be displayed as "Floor".
 */
export const LayerDisplayNames: Record<CanvasLayer, string> = {
  [CanvasLayer.FLOOR]: 'Floor',
  [CanvasLayer.WALLS]: 'Walls',
  [CanvasLayer.CEILING]: 'Ceiling',
  [CanvasLayer.DOORS]: 'Doors',
  [CanvasLayer.WINDOWS]: 'Windows',
  [CanvasLayer.FURNITURE]: 'Furniture',
  [CanvasLayer.FIXTURES]: 'Fixtures',
  [CanvasLayer.ELECTRICAL]: 'Electrical',
  [CanvasLayer.PLUMBING]: 'Plumbing',
  [CanvasLayer.HVAC]: 'HVAC',
  [CanvasLayer.DECOR]: 'Decor',
  [CanvasLayer.DIMENSIONS]: 'Dimensions',
  [CanvasLayer.TEXT]: 'Text Labels',
  [CanvasLayer.AREAS]: 'Areas',
  [CanvasLayer.LANDSCAPE]: 'Landscape',
}

/**
 * Maps {@link CanvasLayer} enum members to their default color codes.
 *
 * @remarks
 * These colors are used for visual differentiation of layers on the canvas
 * or in UI elements representing layers. The colors are in hexadecimal RGB format.
 */
export const LayerColors: Record<CanvasLayer, string> = {
  [CanvasLayer.FLOOR]: '#8B4513', // Brown
  [CanvasLayer.WALLS]: '#808080', // Gray
  [CanvasLayer.CEILING]: '#DCDCDC', // Light Gray
  [CanvasLayer.DOORS]: '#A52A2A', // Brown
  [CanvasLayer.WINDOWS]: '#87CEEB', // Sky Blue
  [CanvasLayer.FURNITURE]: '#4682B4', // Steel Blue
  [CanvasLayer.FIXTURES]: '#20B2AA', // Light Sea Green
  [CanvasLayer.ELECTRICAL]: '#FFD700', // Gold
  [CanvasLayer.PLUMBING]: '#4169E1', // Royal Blue
  [CanvasLayer.HVAC]: '#32CD32', // Lime Green
  [CanvasLayer.DECOR]: '#DA70D6', // Orchid
  [CanvasLayer.DIMENSIONS]: '#000000', // Black
  [CanvasLayer.TEXT]: '#000000', // Black
  [CanvasLayer.AREAS]: '#98FB98', // Pale Green
  [CanvasLayer.LANDSCAPE]: '#228B22', // Forest Green
}

/**
 * Defines the default visibility state for each {@link CanvasLayer}.
 *
 * @remarks
 * This mapping determines whether a layer is visible by default when a new
 * design is created or when layer visibilities are reset.
 * `true` means visible, `false` means hidden.
 */
export const DefaultLayerVisibility: Record<CanvasLayer, boolean> = {
  [CanvasLayer.FLOOR]: true,
  [CanvasLayer.WALLS]: true,
  [CanvasLayer.CEILING]: true,
  [CanvasLayer.DOORS]: true,
  [CanvasLayer.WINDOWS]: true,
  [CanvasLayer.FURNITURE]: true,
  [CanvasLayer.FIXTURES]: true,
  [CanvasLayer.ELECTRICAL]: false,
  [CanvasLayer.PLUMBING]: false,
  [CanvasLayer.HVAC]: false,
  [CanvasLayer.DECOR]: true,
  [CanvasLayer.DIMENSIONS]: true,
  [CanvasLayer.TEXT]: true,
  [CanvasLayer.AREAS]: false,
  [CanvasLayer.LANDSCAPE]: false,
}

/**
 * Specifies the rendering order of {@link CanvasLayer}s on the canvas.
 *
 * @remarks
 * Layers are rendered from bottom to top based on their order in this array.
 * The first element in the array is rendered at the very bottom, and the last
 * element is rendered on top of all others. This order is crucial for correct
 * visual representation, e.g., ensuring floors are below walls, and text is above other elements.
 */
export const LayerRenderOrder: CanvasLayer[] = [
  CanvasLayer.FLOOR,
  CanvasLayer.AREAS,
  CanvasLayer.LANDSCAPE,
  CanvasLayer.PLUMBING,
  CanvasLayer.HVAC,
  CanvasLayer.WALLS,
  CanvasLayer.DOORS,
  CanvasLayer.WINDOWS,
  CanvasLayer.ELECTRICAL,
  CanvasLayer.FIXTURES,
  CanvasLayer.FURNITURE,
  CanvasLayer.DECOR,
  CanvasLayer.CEILING,
  CanvasLayer.DIMENSIONS,
  CanvasLayer.TEXT,
]

/**
 * Retrieves all {@link CanvasLayer}s that belong to a specific {@link LayerGroup}.
 * @param group - The {@link LayerGroup} to filter by.
 * @returns An array of {@link CanvasLayer} enum members belonging to the specified group.
 */
export function getLayersInGroup(group: LayerGroup): CanvasLayer[] {
  return Object.entries(LayerGroupMapping)
    .filter(([_, layerGroup]) => layerGroup === group)
    .map(([layer]) => layer as CanvasLayer)
}

/**
 * Retrieves the human-readable display name for a given {@link CanvasLayer}.
 * @param layer - The {@link CanvasLayer} for which to get the display name.
 * @returns The display name of the layer as a string. Defaults to the layer key if not found.
 */
export function getLayerDisplayName(layer: CanvasLayer): string {
  return LayerDisplayNames[layer] || layer
}

/**
 * Retrieves the default color code for a given {@link CanvasLayer}.
 * @param layer - The {@link CanvasLayer} for which to get the color.
 * @returns The color code of the layer as a string (hexadecimal format). Defaults to black ('#000000') if not found.
 */
export function getLayerColor(layer: CanvasLayer): string {
  return LayerColors[layer] || '#000000'
}

// Note: Implementation also available in src/lib/utils/canvas/layerUtils.ts

/**
 * Represents the visibility state for all canvas layers.
 *
 * @remarks
 * This type is a record where each key is a {@link CanvasLayer} and its value
 * is a boolean indicating whether that layer is currently visible (`true`) or
 * hidden (`false`).
 */
export type LayerVisibility = Record<CanvasLayer, boolean>

/**
 * Defines the complete configuration for a single canvas layer.
 *
 * @remarks
 * This interface consolidates all properties necessary for managing, displaying,
 * and rendering a layer within the application. It includes its unique identifier,
 * user-friendly name, current visibility state, associated color, logical group,
 * and its position in the rendering stack.
 */
export interface LayerConfig {
  /** The unique identifier of the layer, corresponding to a {@link CanvasLayer} enum member. */
  id: CanvasLayer

  /** The human-readable display name for the layer, used in UI elements. */
  name: string

  /** A boolean indicating whether the layer is currently visible on the canvas. */
  visible: boolean

  /**
   * An optional color code (hexadecimal string) for the layer.
   * If not provided, a default color may be used.
   */
  color?: string

  /** The {@link LayerGroup} to which this layer belongs. */
  group: LayerGroup

  /**
   * The rendering order of the layer. Lower numbers are rendered first (further back),
   * and higher numbers are rendered last (further forward).
   */
  renderOrder: number
}
