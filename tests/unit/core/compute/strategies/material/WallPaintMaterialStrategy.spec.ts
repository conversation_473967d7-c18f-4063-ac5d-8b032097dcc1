import { describe, expect, it, vi } from 'vitest'
import { WallPaintMaterialStrategy } from '@/core/compute/strategies/material/WallPaintMaterialStrategy'
import { CoreError } from '@/services/errors'

describe('wallPaintMaterialStrategy', () => {
  const strategy = new WallPaintMaterialStrategy()

  it('should return the correct shape type', () => {
    expect(strategy.getElementType()).toBe('rectangle')
  })

  it('should calculate paint amount correctly', () => {
    const mockElement = {
      id: 'wall-1',
      type: 'shape',
      compute: {
        area: vi.fn().mockReturnValue(20), // 20 square meters
      },
    }

    const result = strategy.calculateMaterialAmount(mockElement, 'paint', {
      coverage: 8, // 8 square meters per liter
      coats: 2,
      wastageRate: 10,
    })

    expect(result.amount).toBe(20)
    expect(result.unit).toBe('m²')
    expect(result.unitType).toBe('liter')
    expect(result.coats).toBe(2)

    // 计算预期的涂料量：面积 * 涂层数 / 覆盖率
    const expectedLiters = (20 * 2) / 8
    expect(result.unitCount).toBeCloseTo(expectedLiters)

    // 计算预期的含损耗面积：面积 * (1 + 损耗率)
    const expectedAreaWithWastage = 20 * (1 + 10 / 100)
    expect(result.amountWithWastage).toBeCloseTo(expectedAreaWithWastage)
  })

  it('should use default values when options are not provided', () => {
    const mockElement = {
      id: 'wall-1',
      type: 'shape',
      compute: {
        area: vi.fn().mockReturnValue(20), // 20 square meters
      },
    }

    const result = strategy.calculateMaterialAmount(mockElement, 'paint')

    expect(result.amount).toBe(20)
    expect(result.unit).toBe('m²')
    expect(result.unitType).toBe('liter')
    expect(result.coats).toBe(2) // Default coats

    // 使用默认值：覆盖率 10，涂层数 2，损耗率 10%
    const expectedLiters = (20 * 2) / 10
    expect(result.unitCount).toBeCloseTo(expectedLiters)

    const expectedAreaWithWastage = 20 * (1 + 10 / 100)
    expect(result.amountWithWastage).toBeCloseTo(expectedAreaWithWastage)
  })

  it('should throw error for invalid element type', () => {
    const mockElement = {
      id: 'invalid-1',
      type: 'invalid',
    }

    expect(() => strategy.calculateMaterialAmount(mockElement, 'paint')).toThrow(CoreError)
    expect(() => strategy.calculateMaterialAmount(mockElement, 'paint')).toThrow('WallPaintMaterialStrategy can only calculate materials for shape elements')
  })

  it('should throw error for invalid material type', () => {
    const mockElement = {
      id: 'wall-1',
      type: 'shape',
      compute: {
        area: vi.fn().mockReturnValue(20),
      },
    }

    expect(() => strategy.calculateMaterialAmount(mockElement, 'invalid')).toThrow(CoreError)
    expect(() => strategy.calculateMaterialAmount(mockElement, 'invalid')).toThrow('WallPaintMaterialStrategy only supports \'paint\' material type')
  })

  it('should throw error if element has no area calculation method', () => {
    const mockElement = {
      id: 'wall-1',
      type: 'shape',
      compute: {},
    }

    expect(() => strategy.calculateMaterialAmount(mockElement, 'paint')).toThrow(CoreError)
    expect(() => strategy.calculateMaterialAmount(mockElement, 'paint')).toThrow('Failed to calculate area for element')
  })

  it('should throw error if area calculation returns invalid value', () => {
    const mockElement = {
      id: 'wall-1',
      type: 'shape',
      compute: {
        area: vi.fn().mockReturnValue(-10), // Negative area
      },
    }

    expect(() => strategy.calculateMaterialAmount(mockElement, 'paint')).toThrow(CoreError)
    expect(() => strategy.calculateMaterialAmount(mockElement, 'paint')).toThrow('Invalid area value')
  })
})
