/**
 * Bounding Box Calculation Strategy for Ellipse and Circle Elements
 *
 * @remarks
 * This strategy implements the {@link BoundingBoxCalculatorStrategy} for calculating
 * the axis-aligned bounding box of Ellipse ({@link CoreElementType.ELLIPSE})
 * and Circle ({@link CoreElementType.CIRCLE}) elements.
 *
 * For Ellipse elements, it uses the `radiusX`, `radiusY`, and `position` (center) properties.
 * For Circle elements, it uses the `radius` and `position` (center) properties.
 * The actual bounding box calculation is delegated to utility functions
 * `calculateEllipseBoundingBoxUtil` and `calculateCircleBoundingBoxUtil` from
 * `../../../../lib/utils/geometry/ellipseUtils`.
 *
 * This strategy is registered for the `ELLIPSE` type but internally handles `CIRCLE`
 * as a special case. It validates element types and their geometric properties.
 *
 * @module core/compute/strategies/bounding-box/EllipseBoundingBoxStrategy
 * @see {@link Shape.Ellipse}
 * @see {@link Shape.Circle}
 * @see {@link BoundingBoxInterface}
 */
import type { BoundingBoxCalculatorStrategy } from '../../../../types/core/compute'
import type { BoundingBox as BoundingBoxInterface } from '../../../../types/core/element/geometry/bounding-box'
import type {
  Element,
  Shape,
  ShapeElement, // Added ShapeElement import
  // MetadataProperties // Not used
} from '../../../../types/core/elementDefinitions'
// Use PointData, alias as IPoint
import {
  calculateCircleBoundingBox as calculateCircleBoundingBoxUtil,
  calculateEllipseBoundingBox as calculateEllipseBoundingBoxUtil,
} from '../../../../lib/utils/geometry/ellipseUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType, // Added ShapeElement import
  // MetadataProperties // Not used
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'
// import { BoundingBoxClass } from '../../../../lib/utils/geometry/BoundingBoxClass'; // Not directly used

export class EllipseBoundingBoxStrategy implements BoundingBoxCalculatorStrategy {
  /**
   * Calculates the axis-aligned bounding box of an Ellipse or Circle element.
   *
   * @param element - The ellipse or circle element. Expected to be of type {@link Element}
   *                  and specifically {@link CoreElementType.ELLIPSE} or {@link CoreElementType.CIRCLE}.
   * @returns A {@link BoundingBoxInterface} object representing the bounding box.
   * @throws {@link CoreError} if the element is `null` or `undefined`.
   * @throws {@link CoreError} if the element type is not `ELLIPSE` or `CIRCLE`.
   * @throws {@link CoreError} if the element's geometric properties (center, radii) are invalid.
   */
  public calculateBoundingBox(element: Element): BoundingBoxInterface {
    if (element == null) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        'Element cannot be null or undefined.',
        undefined,
        { component: 'EllipseBoundingBoxStrategy', operation: 'calculateBoundingBox' },
      )
    }

    if (element.type === CoreElementType.CIRCLE) {
      const circle = element as unknown as Shape.Circle // Added unknown for type safety
      const radius = circle.radius // Access directly
      // Cast the original 'element' (which is known to be a CIRCLE here)
      // to ShapeElement to reliably access inherited properties like 'position'.
      const center = (element as ShapeElement).position

      if (typeof radius !== 'number' || Number.isNaN(radius) || radius < 0) { // Allow radius === 0
        throw new CoreError(
          ErrorType.InvalidParameter,
          `Invalid radius for Circle ID: ${element.id}`,
          undefined,
          { component: 'EllipseBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id, metadata: { radius } },
        )
      }
      if (center == null || typeof center.x !== 'number' || typeof center.y !== 'number') {
        throw new CoreError(
          ErrorType.InvalidParameter,
          `Invalid center position for Circle ID: ${element.id}`,
          undefined,
          { component: 'EllipseBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id, metadata: { center } },
        )
      }
      return calculateCircleBoundingBoxUtil(center, radius)
    }
    else if (element.type === CoreElementType.ELLIPSE) {
      const ellipse = element as unknown as Shape.Ellipse // Added unknown for type safety
      const rx = ellipse.radiusX // Access directly
      const ry = ellipse.radiusY // Access directly
      // Cast the original 'element' (which is known to be an ELLIPSE here)
      // to ShapeElement to reliably access inherited properties like 'position'.
      const center = (element as ShapeElement).position

      if (center == null || typeof center.x !== 'number' || typeof center.y !== 'number') {
        throw new CoreError(
          ErrorType.InvalidParameter,
          `Invalid center position for Ellipse ID: ${element.id}`,
          undefined,
          { component: 'EllipseBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id, metadata: { center } },
        )
      }
      if (typeof rx !== 'number' || typeof ry !== 'number' || Number.isNaN(rx) || Number.isNaN(ry) || rx < 0 || ry < 0) { // Allow radius === 0
        throw new CoreError(
          ErrorType.InvalidParameter,
          `Invalid radii for Ellipse ID: ${element.id}`,
          undefined,
          { component: 'EllipseBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id, metadata: { rx, ry } },
        )
      }
      return calculateEllipseBoundingBoxUtil(center, rx, ry)
    }
    else {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `Expected ELLIPSE or CIRCLE, got ${element.type}`,
        undefined,
        { component: 'EllipseBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }
  }

  /**
   * Returns the primary element type this strategy is registered for.
   *
   * @remarks
   * This strategy handles both `ELLIPSE` and `CIRCLE` types, with `CIRCLE`
   * being a special case of `ELLIPSE`. It is typically registered under
   * {@link CoreElementType.ELLIPSE}.
   *
   * @returns {@link CoreElementType.ELLIPSE} - The ellipse element type that this strategy handles
   */
  public getElementType(): CoreElementType {
    return CoreElementType.ELLIPSE
  }
}
