/**
 * Geometry Selector Component
 *
 * Selects and renders the appropriate geometry component based on element type.
 * This component acts as a router for different element-specific geometry layouts.
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'

import { ElementType } from '@/types/core/elementDefinitions'
import { ArcGeometry, CircleGeometry, CubicGeometry, EllipseGeometry, ImageGeometry, LineGeometry, PolygonGeometry, PolylineGeometry, QuadraticGeometry, RectangleGeometry, SquareGeometry, TextGeometry } from './index'

interface GeometrySelectorProps {
  /** The element being edited */
  element: ShapeElement
  /** Function to get input values with proper formatting */
  getValueForInput: (path: string) => string
  /** Function to get placeholder values */
  getPlaceholderForInput: (path: string, exampleValue?: number | string) => string | undefined
  /** Function to handle input changes */
  handleInputChange: (path: string, value: string, isNumeric?: boolean, convertToPixels?: boolean) => void
  /** Unit name for display (e.g., "mm", "cm") */
  unitName: string
  /** Function to convert internal units to display units */
  toDisplayUnit?: (value: number) => number
  /** Function to convert display units to internal units */
  toInternalUnit?: (value: number) => number | undefined
  /** Function to update element properties directly */
  updateProperty?: (path: string, value: unknown) => void
}

/**
 * GeometrySelector component that renders the appropriate geometry layout
 * based on the element type.
 *
 * This component implements the element-specific layouts defined in the
 * design document, ensuring consistent UI patterns across all element types.
 */
export function GeometrySelector({
  element,
  getValueForInput,
  getPlaceholderForInput,
  handleInputChange,
  unitName,
  toDisplayUnit,
  toInternalUnit,
  updateProperty,
}: GeometrySelectorProps) {
  // Create adapter functions to handle type mismatches between components
  const getPlaceholderForInputSimple = (path: string): string => {
    const result = getPlaceholderForInput(path)
    return result ?? ''
  }

  const toInternalUnitSafe = toInternalUnit
    ? (value: number): number => {
        const result = toInternalUnit(value)
        return result ?? value
      }
    : undefined

  // Select the appropriate geometry component based on element type
  switch (element.type) {
    case ElementType.RECTANGLE:
      return (
        <RectangleGeometry
          element={element}
          getValueForInput={getValueForInput}
          getPlaceholderForInput={getPlaceholderForInput}
          handleInputChange={handleInputChange}
          unitName={unitName}
        />
      )

    case ElementType.SQUARE:
      return (
        <SquareGeometry
          element={element}
          getValueForInput={getValueForInput}
          getPlaceholderForInput={getPlaceholderForInput}
          handleInputChange={handleInputChange}
          unitName={unitName}
        />
      )

    case ElementType.CIRCLE:
      return (
        <CircleGeometry
          element={element}
          getValueForInput={getValueForInput}
          getPlaceholderForInput={getPlaceholderForInputSimple}
          handleInputChange={handleInputChange}
          unitName={unitName}
        />
      )

    case ElementType.ELLIPSE:
      return (
        <EllipseGeometry
          element={element}
          getValueForInput={getValueForInput}
          getPlaceholderForInput={getPlaceholderForInputSimple}
          handleInputChange={handleInputChange}
          unitName={unitName}
        />
      )

    case ElementType.POLYGON:
    case ElementType.TRIANGLE:
    case ElementType.QUADRILATERAL:
    case ElementType.PENTAGON:
    case ElementType.HEXAGON:
    case ElementType.HEPTAGON:
    case ElementType.OCTAGON:
    case ElementType.NONAGON:
    case ElementType.DECAGON:
      return (
        <PolygonGeometry
          element={element}
          getValueForInput={getValueForInput}
          getPlaceholderForInput={getPlaceholderForInputSimple}
          handleInputChange={handleInputChange}
          unitName={unitName}
        />
      )

    case ElementType.TEXT:
    case ElementType.TEXT_LABEL:
      return (
        <TextGeometry
          element={element}
          getValueForInput={getValueForInput}
          getPlaceholderForInput={getPlaceholderForInputSimple}
          handleInputChange={handleInputChange}
          unitName={unitName}
        />
      )

    case ElementType.IMAGE:
      return (
        <ImageGeometry
          element={element}
          getValueForInput={getValueForInput}
          getPlaceholderForInput={getPlaceholderForInputSimple}
          handleInputChange={handleInputChange}
          unitName={unitName}
        />
      )

    case ElementType.LINE:
      return (
        <LineGeometry
          element={element}
          getValueForInput={getValueForInput}
          getPlaceholderForInput={getPlaceholderForInputSimple}
          handleInputChange={handleInputChange}
          unitName={unitName}
          toDisplayUnit={toDisplayUnit}
          toInternalUnit={toInternalUnitSafe}
          updateProperty={updateProperty}
        />
      )

    case ElementType.ARC:
      return (
        <ArcGeometry
          element={element}
          getValueForInput={getValueForInput}
          getPlaceholderForInput={getPlaceholderForInputSimple}
          handleInputChange={handleInputChange}
          unitName={unitName}
          toDisplayUnit={toDisplayUnit}
          toInternalUnit={toInternalUnitSafe}
          updateProperty={updateProperty}
        />
      )

    case ElementType.POLYLINE:
      return (
        <PolylineGeometry
          element={element}
          getValueForInput={getValueForInput}
          getPlaceholderForInput={getPlaceholderForInputSimple}
          handleInputChange={handleInputChange}
          unitName={unitName}
          toDisplayUnit={toDisplayUnit}
          toInternalUnit={toInternalUnitSafe}
          updateProperty={updateProperty}
        />
      )

    case ElementType.QUADRATIC:
      return (
        <QuadraticGeometry
          element={element}
          getValueForInput={getValueForInput}
          getPlaceholderForInput={getPlaceholderForInputSimple}
          handleInputChange={handleInputChange}
          unitName={unitName}
          toDisplayUnit={toDisplayUnit}
          toInternalUnit={toInternalUnitSafe}
          updateProperty={updateProperty}
        />
      )

    case ElementType.CUBIC:
      return (
        <CubicGeometry
          element={element}
          getValueForInput={getValueForInput}
          getPlaceholderForInput={getPlaceholderForInputSimple}
          handleInputChange={handleInputChange}
          unitName={unitName}
          toDisplayUnit={toDisplayUnit}
          toInternalUnit={toInternalUnitSafe}
          updateProperty={updateProperty}
        />
      )

      // All element types have been implemented!

    default:
      return (
        <div className="p-4 text-center text-muted-foreground">
          <p>
            Unknown element type:
            {' '}
            {element.type}
          </p>
          <p className="text-xs mt-2">Cannot render geometry controls.</p>
        </div>
      )
  }
}
