/**
 * Element Model Utilities
 *
 * @remarks
 * This module provides a collection of utility functions for working with element models
 * (specifically {@link ShapeModel} instances). These utilities include:
 * - Type guard functions (e.g., `isRectangle`, `isDoor`) to check if a given
 *   `ShapeModel` is of a specific {@link ElementType} and conforms to a more
 *   specialized interface (like `Rectangle` or `OpeningProperties`).
 * - Property getter functions (e.g., `getRectangleProps`, `getDoorProps`) that
 *   safely extract and return a subset of properties relevant to a specific element
 *   type from the `ShapeModel`'s `properties` object.
 *
 * These utilities help in safely accessing and interpreting element-specific data
 * stored within the generic `ShapeModel` structure.
 *
 * @module lib/utils/element/elementModelUtils
 */

// Import design element properties and enums
import type { OpeningProperties } from '@/types/core/element/design/openingDesignTypes'
import type { RoomProperties /* , RoomType */ } from '@/types/core/element/design/roomDesignTypes' // RoomType is unused
import type { WallProperties /* , WallType */ } // Assuming WallType is also exported if needed for checks // WallType is unused
  from '@/types/core/element/design/wallDesignTypes'

import type { PointData } from '@/types/core/element/geometry/point' // Import PointData
import type { Arc as ArcPath, Cubic as CubicPath, Line, Polyline as PolylinePath, Quadratic as QuadraticPath } from '@/types/core/element/path/path'
import type { Circle, Ellipse } from '@/types/core/element/shape/ellipseShapeTypes'
import type { Polygon } from '@/types/core/element/shape/polygonShapeTypes'

// Import specific shape and properties types from their definition files
import type { Rectangle } from '@/types/core/element/shape/rectangleShapeTypes'
import type {
  BaseStyleProperties,
  ShapeElement as ShapeModel,
} from '@/types/core/elementDefinitions'
import { OpeningType } from '@/types/core/element/design/openingDesignTypes'
import {
  ElementType,
} from '@/types/core/elementDefinitions'

// Import specific types for Text and Image if they exist, or define basic ones
// Assuming TextShape and ImageShape might be defined similar to Rectangle, Circle etc.
// If not, we'll rely on ElementType and the property interfaces.
// import { TextShape } from '@/types/core/element/shape/textShapeTypes'; // Example path
// import { ImageShape } from '@/types/core/element/shape/imageShapeTypes'; // Example path

// Define local interfaces for properties. These describe the expected structure
// within shape.properties for each element type when using the get*Props functions.
// These are helper types for the getter functions and are not meant to be core model definitions.

/** Expected structure for Rectangle properties within ShapeModel.properties. */
interface RectangleModelProps { width?: number, height?: number, cornerRadius?: number }
/** Expected structure for Circle properties within ShapeModel.properties. */
interface CircleModelProps { radius?: number }
/** Expected structure for Ellipse properties within ShapeModel.properties. */
interface EllipseModelProps { radiusX?: number, radiusY?: number }
/** Expected structure for Polygon properties within ShapeModel.properties. */
interface PolygonModelProps { points?: PointData[] } // Changed IPoint to PointData
/** Expected structure for Line properties within ShapeModel.properties. */
interface LineModelProps { start?: PointData, end?: PointData } // Changed IPoint to PointData
// Note: The following interfaces were removed as they were not being used:
// interface PolylineModelProps { points?: PointData[] }
// interface ArcModelProps { cx?: number, cy?: number, rx?: number, ry?: number, startAngle?: number, endAngle?: number, counterClockwise?: boolean, pathData?: string }
// interface QuadraticBezierModelProps { startPoint?: PointData, controlPoint1?: PointData, endPoint?: PointData, pathData?: string }
// interface CubicBezierModelProps { startPoint?: PointData, controlPoint1?: PointData, controlPoint2?: PointData, endPoint?: PointData, pathData?: string }
/** Expected structure for Text properties within ShapeModel.properties. */
interface TextModelProps { text?: string, content?: string, fontSize?: number, fontFamily?: string, textAlign?: string, textBaseline?: string }
/** Expected structure for Image properties within ShapeModel.properties. */
interface ImageModelProps { src?: string, width?: number, height?: number, alt?: string }
// For Door and Window, we'll use OpeningProperties and check openingType
/** Expected structure for Door properties within ShapeModel.properties, extending OpeningProperties. */
interface DoorModelProps extends OpeningProperties { doorType?: string, swingDirection?: string, swingAngle?: number, isOpen?: boolean }
/** Expected structure for Window properties within ShapeModel.properties, extending OpeningProperties. */
interface WindowModelProps extends OpeningProperties { windowType?: string, sillHeight?: number, operable?: boolean }
// For Room and Wall, we use their specific Properties interfaces (RoomProperties, WallProperties) directly.

// Type guards for shape models

/**
 * Type guard to check if a {@link ShapeModel} is a Rectangle or Square.
 * @param shape - The {@link ShapeModel} to check.
 * @returns `true` if the shape is a Rectangle or Square, `false` otherwise.
 */
export function isRectangle(shape: ShapeModel): shape is ShapeModel & Rectangle {
  return shape.type === ElementType.RECTANGLE || shape.type === ElementType.SQUARE
}

/**
 * Type guard to check if a {@link ShapeModel} is a Circle.
 * @param shape - The {@link ShapeModel} to check.
 * @returns `true` if the shape is a Circle, `false` otherwise.
 */
export function isCircle(shape: ShapeModel): shape is ShapeModel & Circle {
  return shape.type === ElementType.CIRCLE
}

/**
 * Type guard to check if a {@link ShapeModel} is an Ellipse.
 * @param shape - The {@link ShapeModel} to check.
 * @returns `true` if the shape is an Ellipse, `false` otherwise.
 */
export function isEllipse(shape: ShapeModel): shape is ShapeModel & Ellipse {
  return shape.type === ElementType.ELLIPSE
}

/**
 * Type guard to check if a {@link ShapeModel} is a Polygon or one of its subtypes (Triangle, Hexagon, etc.).
 * @param shape - The {@link ShapeModel} to check.
 * @returns `true` if the shape is a Polygon or a recognized subtype, `false` otherwise.
 */
export function isPolygon(shape: ShapeModel): shape is ShapeModel & Polygon {
  return (
    shape.type === ElementType.POLYGON
    || shape.type === ElementType.TRIANGLE
    || shape.type === ElementType.HEXAGON
    || shape.type === ElementType.QUADRILATERAL
    || shape.type === ElementType.PENTAGON
    || shape.type === ElementType.OCTAGON
    || shape.type === ElementType.NONAGON
    || shape.type === ElementType.DECAGON
  )
}

/**
 * Type guard to check if a {@link ShapeModel} is a Line.
 * @param shape - The {@link ShapeModel} to check.
 * @returns `true` if the shape is a Line, `false` otherwise.
 */
export function isLine(shape: ShapeModel): shape is ShapeModel & Line {
  return shape.type === ElementType.LINE
}

/**
 * Type guard to check if a {@link ShapeModel} is a Polyline.
 * @param shape - The {@link ShapeModel} to check.
 * @returns `true` if the shape is a Polyline, `false` otherwise.
 */
export function isPolyline(shape: ShapeModel): shape is ShapeModel & PolylinePath {
  return shape.type === ElementType.POLYLINE
}

/**
 * Type guard to check if a {@link ShapeModel} is an Arc.
 * @param shape - The {@link ShapeModel} to check.
 * @returns `true` if the shape is an Arc, `false` otherwise.
 */
export function isArc(shape: ShapeModel): shape is ShapeModel & ArcPath {
  return shape.type === ElementType.ARC
}

/**
 * Type guard to check if a {@link ShapeModel} is a Quadratic Bezier curve.
 * @param shape - The {@link ShapeModel} to check.
 * @returns `true` if the shape is a Quadratic Bezier curve, `false` otherwise.
 */
export function isQuadraticBezier(shape: ShapeModel): shape is ShapeModel & QuadraticPath {
  return shape.type === ElementType.QUADRATIC
}

/**
 * Type guard to check if a {@link ShapeModel} is a Cubic Bezier curve.
 * @param shape - The {@link ShapeModel} to check.
 * @returns `true` if the shape is a Cubic Bezier curve, `false` otherwise.
 */
export function isCubicBezier(shape: ShapeModel): shape is ShapeModel & CubicPath {
  return shape.type === ElementType.CUBIC
}

/**
 * Type guard to check if a {@link ShapeModel} is a Text element.
 * @param shape - The {@link ShapeModel} to check.
 * @returns `true` if the shape is a Text element, `false` otherwise.
 */
export function isText(shape: ShapeModel): shape is ShapeModel & { properties: TextModelProps } { // Assuming properties structure
  return shape.type === ElementType.TEXT
}

/**
 * Type guard to check if a {@link ShapeModel} is an Image element or represents an image-like entity.
 * @param shape - The {@link ShapeModel} to check.
 * @returns `true` if the shape is an Image element or one of the specified image-based types, `false` otherwise.
 */
export function isImage(shape: ShapeModel): shape is ShapeModel & { properties: ImageModelProps } { // Assuming properties structure
  return shape.type === ElementType.IMAGE
    || shape.type === ElementType.FURNITURE
    || shape.type === ElementType.FIXTURE
    || shape.type === ElementType.LIGHT
    || shape.type === ElementType.FLOOR_AREA
    || shape.type === ElementType.WALL_PAINT
    || shape.type === ElementType.WALL_PAPER
    || shape.type === ElementType.OPENING
}

/**
 * Type guard to check if a {@link ShapeModel} is a Door.
 * This also checks if its `properties.openingType` is a known door type.
 * @param shape - The {@link ShapeModel} to check.
 * @returns `true` if the shape is a Door with a valid door opening type, `false` otherwise.
 */
export function isDoor(shape: ShapeModel): shape is ShapeModel & { properties: OpeningProperties } {
  if (shape.type === ElementType.DOOR && shape.properties) {
    // Safe type conversion with property checking
    const props = shape.properties as unknown as OpeningProperties
    // Check if the required properties exist and have valid values
    if (props && typeof props.openingType === 'string') {
      // Check for door-specific opening types
      return [
        OpeningType.HINGED_DOOR,
        OpeningType.SLIDING_DOOR,
        OpeningType.POCKET_DOOR,
        OpeningType.BIFOLD_DOOR,
        OpeningType.FRENCH_DOOR,
        OpeningType.BARN_DOOR,
        OpeningType.REVOLVING_DOOR,
      ].includes(props.openingType)
    }
  }
  return false
}

/**
 * Type guard to check if a {@link ShapeModel} is a Window.
 * This also checks if its `properties.openingType` is a known window type.
 * @param shape - The {@link ShapeModel} to check.
 * @returns `true` if the shape is a Window with a valid window opening type, `false` otherwise.
 */
export function isWindow(shape: ShapeModel): shape is ShapeModel & { properties: OpeningProperties } {
  if (shape.type === ElementType.WINDOW && shape.properties) {
    // Safe type conversion with property checking
    const props = shape.properties as unknown as OpeningProperties
    // Check if the required properties exist and have valid values
    if (props && typeof props.openingType === 'string') {
      // Check for window-specific opening types
      return [
        OpeningType.CASEMENT_WINDOW,
        OpeningType.AWNING_WINDOW,
        OpeningType.HOPPER_WINDOW,
        OpeningType.SLIDING_WINDOW,
        OpeningType.DOUBLE_HUNG_WINDOW,
        OpeningType.FIXED_WINDOW,
        OpeningType.BAY_WINDOW,
        OpeningType.BOW_WINDOW,
        OpeningType.SKYLIGHT,
      ].includes(props.openingType)
    }
  }
  return false
}

/**
 * Type guard to check if a {@link ShapeModel} is a Room.
 * @param shape - The {@link ShapeModel} to check.
 * @returns `true` if the shape is a Room, `false` otherwise.
 */
export function isRoom(shape: ShapeModel): shape is ShapeModel & { properties: RoomProperties } {
  return shape.type === ElementType.ROOM
}

/**
 * Type guard to check if a {@link ShapeModel} is a Wall.
 * @param shape - The {@link ShapeModel} to check.
 * @returns `true` if the shape is a Wall, `false` otherwise.
 */
export function isWall(shape: ShapeModel): shape is ShapeModel & { properties: WallProperties } {
  return shape.type === ElementType.WALL
}

// Property getters

/**
 * Safely retrieves rectangle-specific properties from a {@link ShapeModel}'s `properties` object.
 * @param shape - The {@link ShapeModel} to extract properties from.
 * @returns A partial {@link RectangleModelProps} object if the shape is a rectangle and has properties, otherwise `undefined`.
 */
export function getRectangleProps(shape: ShapeModel): Partial<RectangleModelProps> | undefined {
  if (isRectangle(shape) && shape.properties) {
    const props = shape.properties as RectangleModelProps
    return {
      width: props.width,
      height: props.height,
      cornerRadius: props.cornerRadius,
    }
  }
  return undefined
}

/**
 * Safely retrieves circle-specific properties from a {@link ShapeModel}'s `properties` object.
 * @param shape - The {@link ShapeModel} to extract properties from.
 * @returns A partial {@link CircleModelProps} object if the shape is a circle and has properties, otherwise `undefined`.
 */
export function getCircleProps(shape: ShapeModel): Partial<CircleModelProps> | undefined {
  if (isCircle(shape) && shape.properties) {
    const props = shape.properties as CircleModelProps
    return {
      radius: props.radius,
    }
  }
  return undefined
}

/**
 * Safely retrieves ellipse-specific properties from a {@link ShapeModel}'s `properties` object.
 * @param shape - The {@link ShapeModel} to extract properties from.
 * @returns A partial {@link EllipseModelProps} object if the shape is an ellipse and has properties, otherwise `undefined`.
 */
export function getEllipseProps(shape: ShapeModel): Partial<EllipseModelProps> | undefined {
  if (isEllipse(shape) && shape.properties) {
    const props = shape.properties as EllipseModelProps
    return {
      radiusX: props.radiusX,
      radiusY: props.radiusY,
    }
  }
  return undefined
}

/**
 * Safely retrieves polygon-specific properties (like points) from a {@link ShapeModel}'s `properties` object.
 * @param shape - The {@link ShapeModel} to extract properties from.
 * @returns A partial {@link PolygonModelProps} object if the shape is a polygon and has properties, otherwise `undefined`.
 */
export function getPolygonProps(shape: ShapeModel): Partial<PolygonModelProps> | undefined {
  if (isPolygon(shape) && shape.properties) {
    const props = shape.properties as PolygonModelProps
    return {
      points: props.points,
    }
  }
  return undefined
}

/**
 * Safely retrieves line-specific properties from a {@link ShapeModel}'s `properties` object.
 * @param shape - The {@link ShapeModel} to extract properties from.
 * @returns A partial {@link LineModelProps} object if the shape is a line and has properties, otherwise `undefined`.
 */
export function getLineProps(shape: ShapeModel): Partial<LineModelProps> | undefined {
  if (isLine(shape) && shape.properties) {
    const props = shape.properties as LineModelProps
    return {
      start: props.start,
      end: props.end,
    }
  }
  return undefined
}

/**
 * Safely retrieves text-specific properties from a {@link ShapeModel}'s `properties` object.
 * @param shape - The {@link ShapeModel} to extract properties from.
 * @returns A partial {@link TextModelProps} object if the shape is a text element and has properties, otherwise `undefined`.
 */
export function getTextProps(shape: ShapeModel): Partial<TextModelProps> | undefined {
  if (isText(shape) && shape.properties) {
    const props = shape.properties as TextModelProps
    return {
      text: props.text ?? props.content, // Prefer text, fallback to content
      fontSize: props.fontSize,
      fontFamily: props.fontFamily,
      textAlign: props.textAlign,
      textBaseline: props.textBaseline,
    }
  }
  return undefined
}

/**
 * Safely retrieves image-specific properties from a {@link ShapeModel}'s `properties` object.
 * @param shape - The {@link ShapeModel} to extract properties from.
 * @returns A partial {@link ImageModelProps} object if the shape is an image element and has properties, otherwise `undefined`.
 */
export function getImageProps(shape: ShapeModel): Partial<ImageModelProps> | undefined {
  if (isImage(shape) && shape.properties) {
    const props = shape.properties as ImageModelProps
    return {
      src: props.src,
      width: props.width,
      height: props.height,
      alt: props.alt,
    }
  }
  return undefined
}

/**
 * Safely retrieves door-specific properties from a {@link ShapeModel}'s `properties` object.
 * @param shape - The {@link ShapeModel} to extract properties from.
 * @returns A partial {@link DoorModelProps} object (which extends {@link OpeningProperties})
 *          if the shape is a door and has properties, otherwise `undefined`.
 */
export function getDoorProps(shape: ShapeModel): Partial<DoorModelProps> | undefined {
  if (isDoor(shape) && shape.properties) {
    // shape.properties here is OpeningProperties, which should be compatible with DoorModelProps
    const props = shape.properties as DoorModelProps
    return {
      // Common OpeningProperties
      openingType: props.openingType,
      width: props.width,
      height: props.height,
      heightFromFloor: props.heightFromFloor,
      wallId: props.wallId,
      wallPosition: props.wallPosition,
      referencePoint: props.referencePoint,
      // Door specific from DoorModelProps (if they exist beyond OpeningProperties)
      doorType: props.doorType, // This might be redundant if openingType covers it
      swingDirection: props.swingDirection,
      swingAngle: props.swingAngle,
      isOpen: props.isOpen,
    }
  }
  return undefined
}

/**
 * Safely retrieves window-specific properties from a {@link ShapeModel}'s `properties` object.
 * @param shape - The {@link ShapeModel} to extract properties from.
 * @returns A partial {@link WindowModelProps} object (which extends {@link OpeningProperties})
 *          if the shape is a window and has properties, otherwise `undefined`.
 */
export function getWindowProps(shape: ShapeModel): Partial<WindowModelProps> | undefined {
  if (isWindow(shape) && shape.properties) {
    const props = shape.properties as WindowModelProps
    return {
      // Common OpeningProperties
      openingType: props.openingType,
      width: props.width,
      height: props.height,
      heightFromFloor: props.heightFromFloor,
      wallId: props.wallId,
      wallPosition: props.wallPosition,
      referencePoint: props.referencePoint,
      // Window specific from WindowModelProps
      windowType: props.windowType, // Might be redundant
      sillHeight: props.sillHeight,
      operable: props.operable,
    }
  }
  return undefined
}

/**
 * Safely retrieves room-specific properties from a {@link ShapeModel}'s `properties` object.
 * @param shape - The {@link ShapeModel} to extract properties from.
 * @returns A partial {@link RoomProperties} object if the shape is a room and has properties, otherwise `undefined`.
 */
export function getRoomProps(shape: ShapeModel): Partial<RoomProperties> | undefined {
  if (isRoom(shape) && shape.properties) {
    const props = shape.properties as RoomProperties
    return {
      // Explicitly list properties from RoomProperties to ensure type safety
      type: props.type, // from RoomProperties
      roomType: props.roomType,
      name: props.name,
      floorLevel: props.floorLevel,
      ceilingHeight: props.ceilingHeight,
      floorMaterial: props.floorMaterial,
      wallMaterial: props.wallMaterial,
      ceilingMaterial: props.ceilingMaterial,
      wallIds: props.wallIds,
      openingIds: props.openingIds,
      furnitureIds: props.furnitureIds,
      fixtureIds: props.fixtureIds,
      shape: props.shape, // This is the geometric shape of the room
      isOutdoor: props.isOutdoor,
      // Note: RoomModelProps defined locally had 'points' and 'wallHeight',
      // but RoomProperties has 'shape' and 'ceilingHeight'.
      // Adjusting to return properties defined in the imported RoomProperties.
    }
  }
  return undefined
}

/**
 * Safely retrieves wall-specific properties from a {@link ShapeModel}'s `properties` object.
 * @param shape - The {@link ShapeModel} to extract properties from.
 * @returns A partial {@link WallProperties} object if the shape is a wall and has properties, otherwise `undefined`.
 */
export function getWallProps(shape: ShapeModel): Partial<WallProperties> | undefined {
  if (isWall(shape) && shape.properties) {
    const props = shape.properties as WallProperties
    return {
      type: props.type, // from WallProperties
      wallType: props.wallType,
      thickness: props.thickness,
      height: props.height,
      path: props.path,
      openingIds: props.openingIds,
      // Note: Local WallModelProps had 'start' and 'end'. WallProperties has 'path'.
    }
  }
  return undefined
}

/**
 * Retrieves the base style properties directly from a {@link ShapeModel}.
 *
 * @remarks
 * {@link BaseStyleProperties} (fill, stroke, strokeWidth, opacity, strokeDasharray)
 * are top-level properties of the {@link ShapeModel} interface itself, not nested within `properties`.
 *
 * @param shape - The {@link ShapeModel} to extract style properties from.
 * @returns A partial {@link BaseStyleProperties} object containing the style properties of the shape.
 */
export function getStyleProps(shape: ShapeModel): Partial<BaseStyleProperties> {
  return {
    fill: shape.fill,
    stroke: shape.stroke,
    strokeWidth: shape.strokeWidth,
    opacity: shape.opacity,
  }
}
