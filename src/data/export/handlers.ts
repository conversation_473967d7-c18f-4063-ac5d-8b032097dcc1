import type { ShapeElement } from '@/types/core'
import type { ExportEvent } from '@/types/services/events'
import { appEventBus, DataEvents } from '@/services/'
import { toPdfBlob, toPngBlob, toSvgBlob } from './blob'

const blobFnMap: Record<string, (elements: ShapeElement[]) => Promise<Blob>> = {
  svg: toSvgBlob,
  png: toPngBlob,
  pdf: toPdfBlob,
}

export function handleExportRequest(event: ExportEvent) {
  console.log('[data export] Receive ExportEvent: ', event)
  try {
    DataEvents.publishExportRequest(appEventBus, 'prepare', event.payload)
  }
  catch (err) {
    DataEvents.publishExportRequest(appEventBus, 'error', {
      ...event.payload,
      error: `[EXPORT_REQUEST] Failed to prepare export: ${
        err instanceof Error ? err.message : String(err)}`,
    })
    console.error('[data export] Failed to request export: ', err)
  }
}

export async function handleExportProgress(event: ExportEvent) {
  console.log('[data export] Receive ExportProgress: ', event)
  const { format, elements } = event.payload
  console.log('[data export] Begin export: ', format, elements)
  try {
    if (!elements) {
      throw new Error('Missing export content')
    }
    const blobFn = blobFnMap[format]
    if (!blobFn) {
      throw new Error(`Unsupported export format: ${format}`)
    }
    const blob = await blobFn(elements)
    DataEvents.publishExportRequest(appEventBus, 'complete', {
      format,
      data: blob,
    })
  }
  catch (err) {
    DataEvents.publishExportRequest(appEventBus, 'error', {
      format,
      error: `[EXPORT_PROGRESS] Failed to export: ${
        err instanceof Error ? err.message : String(err)}`,
    })
    console.error('[data export] Failed to export: ', err)
  }
}
