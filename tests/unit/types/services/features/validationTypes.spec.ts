import { describe, expect, it } from 'vitest'
import * as ValidationTypesModule from '@/types/services/features/validationTypes'
import * as ValidationModule from '@/types/services/validation'

describe('validation Types Feature Module', () => {
  it('should be defined as a module', () => {
    // Verify the module exists
    expect(ValidationTypesModule).toBeDefined()
  })

  it('should re-export types from validation module', () => {
    // Check that the module has the same exports as the validation module
    const validationTypesExports = Object.keys(ValidationTypesModule)
    const validationExports = Object.keys(ValidationModule)

    // Verify that all exports from validation module are re-exported
    for (const exportName of validationExports) {
      expect(validationTypesExports).toContain(exportName)
    }
  })

  // Test specific enums that are defined in this module
  it('should define ValidationSeverity enum', () => {
    expect(ValidationTypesModule.ValidationSeverity).toBeDefined()
    expect(ValidationTypesModule.ValidationSeverity.INFO).toBe('info')
    expect(ValidationTypesModule.ValidationSeverity.WARNING).toBe('warning')
    expect(ValidationTypesModule.ValidationSeverity.ERROR).toBe('error')
  })
})
