import type { ExportEvent } from '@/types/services/events'
import { appEventBus, DataEvents } from '@/services/'
import { AppEventType } from '@/types/services/events'
import { useShapesStore } from './shapesStore'

/**
 * Handler for export prepare events
 * Prepares the shapes and publishes ExportProgress event
 */
export function handleExportPrepare(event: ExportEvent) {
  console.log('[store] Receive ExportEvent: ', event)
  try {
    const shapes = useShapesStore.getState().shapes
    console.log('[store] Shapes: ', shapes)
    const payload = {
      ...event.payload,
      elements: shapes,
    }
    appEventBus.publish({
      type: AppEventType.ExportProgress,
      payload,
    })
  }
  catch (err) {
    DataEvents.publishExportRequest(appEventBus, 'error', {
      ...event.payload,
      error: `[EXPORT_REQUEST] Failed to prepare export: ${
        err instanceof Error ? err.message : String(err)}`,
    })
    console.error('[store] Failed to prepare export: ', err)
  }
}
