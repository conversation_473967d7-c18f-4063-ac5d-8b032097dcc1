import type { CoreConfig } from '../config/index'
import type { ValidationIssue } from '../lib/utils/validationUtils'
import type { ElementCreateEvent, ElementCreateEventPayload } from '../services/elements/element-actions/elementCreationService'
import type { ErrorService } from '../services/system/error-service/errorService'
import type { ElementType, ShapeElement as ShapeModel } from '../types/core/elementDefinitions'
import type { ValidatableShape as ValidatorShape } from '../types/core/validator/validator-interface'
import type {
  BaseEvent,
  EventBus,
  ShapeCreateEvent,
  ShapeDeleteEvent,
  ShapeEditEvent,
} from '../types/services/events'
import type { AppEventMap } from '../types/services/events/eventRegistry'
import type { LoggerService } from '../types/services/logging'
import type {
  ElementEditService as IShapeEditService,
} from '../types/services/shapes/shapeService'
import type { ComputeFacade } from './compute/ComputeFacade'
import type { ElementFactory } from './factory/ElementFactory'
import type { ShapeRepository } from './state/ShapeRepository'
import type { ElementValidator } from './validator/ElementValidator'
import type { PatternDefinition } from '@/types/core/element/elementPatternTypes'

import { cleanPattern } from '@/lib/utils/element/cleanPattern'
import { useShapesStore } from '@/store/shapesStore'
import { createConfig, defaultCoreConfig } from '../config/index'
import { safeValidate } from '../lib/utils/validationUtils'

import { ElementCreationService } from '../services/elements/element-actions/elementCreationService'
import { ElementDeleteService } from '../services/elements/element-actions/elementDeleteService'
import { ElementEditServiceImpl } from '../services/elements/element-actions/elementEditService'
import { ElementSelectionService } from '../services/elements/element-actions/elementSelectionService'
import { CoreError } from '../services/system/error-service/coreError'
import { ErrorType } from '../types/services/errors'
import { AppEventType } from '../types/services/events'

/**
 * Core Coordinator for Application Logic
 *
 * Acts as the central hub for orchestrating core application logic.
 *
 * @remarks
 * The `CoreCoordinator`'s primary responsibilities include:
 * - Initializing and managing the core application configuration.
 * - Instantiating and providing access to core services such as the `ShapeRepository`,
 *   `ElementFactory`, and various shape action services (creation, editing, deletion, selection).
 * - Registering event handlers on the application's `EventBus` to delegate shape manipulation
 *   requests (e.g., `SHAPE_CREATE_REQUEST`, `SHAPE_EDIT_REQUEST`) to the appropriate action services.
 * - Coordinating data synchronization between the internal `ShapeRepository` and any external
 *   state management solutions or data stores.
 * - Providing centralized error handling mechanisms for core operations and publishing
 *   standardized error events.
 *
 * It typically relies on dependency injection for essential components like the
 * `ElementFactory` and `ShapeRepository`, while action services might be instantiated
 * internally or also injected, depending on the application's architecture.
 *
 * @module core/CoreCoordinator
 */
export class CoreCoordinator {
  /**
   * Flag to prevent feedback loop during store synchronization.
   *
   * @remarks
   * Set to true before publishing internal updates that will trigger store changes,
   * checked within store update handlers to ignore self-induced updates.
   * @private
   */
  private isInternalUpdate: boolean = false

  /**
   * Core configuration settings.
   * @private
   */
  private config: CoreConfig

  /**
   * Reference to the injected ShapeRepository instance.
   * @private
   * @readonly
   */
  private readonly repository: ShapeRepository

  /**
   * Reference to the injected ElementFactory instance.
   * @private
   * @readonly
   */
  private readonly factory: ElementFactory

  /**
   * Central application event bus.
   * @private
   */
  private eventBus: EventBus<AppEventMap>

  /**
   * Validator for element data.
   * @private
   */
  private validator: ElementValidator

  /**
   * Logger instance.
   * @private
   */
  private logger: LoggerService

  /**
   * Error service instance.
   * @private
   */
  private errorService: ErrorService

  /**
   * Service for handling element creation operations.
   * @private
   */
  private elementCreationService: ElementCreationService

  /**
   * Service for handling element editing operations.
   * @private
   */
  private elementEditService: IShapeEditService

  /**
   * Service for handling element deletion operations.
   * @private
   */
  private elementDeleteService: ElementDeleteService

  /**
   * Service for handling element selection operations.
   * @private
   */
  private elementSelectionService: ElementSelectionService

  /**
   * Undo/Redo history stacks
   */
  private past: ShapeModel[][] = [[]]
  private future: ShapeModel[][] = []

  /**
   * Optional compute facade dependency
   */
  private computeFacade?: ComputeFacade

  /**
   * Creates an instance of CoreCoordinator.
   *
   * @param eventBus - The central application event bus instance, conforming to the {@link EventBus} interface.
   * @param repository - An instance of {@link ShapeRepository} for managing the state of core shape elements.
   * @param validator - An instance of {@link ElementValidator} for validating element data.
   * @param factory - An instance of {@link ElementFactory} used for creating new shape elements.
   * @param logger - A logger service instance, conforming to the {@link LoggerService} interface.
   * @param errorService - An instance of {@link ErrorService} for centralized error management.
   * @param initialConfig - Optional partial configuration ({@link CoreConfig}) to override default settings.
   * @param computeFacade - Optional compute facade dependency
   */
  constructor(
    // Required dependencies first
    eventBus: EventBus<AppEventMap>,
    repository: ShapeRepository,
    validator: ElementValidator,
    factory: ElementFactory,
    logger: LoggerService, // Add logger parameter
    errorService: ErrorService,
    // Optional config last
    initialConfig?: Partial<CoreConfig>,
    computeFacade?: ComputeFacade, // 新增参数
  ) {
    console.warn('[CoreCoordinator] constructor called')
    // Assign dependencies
    this.eventBus = eventBus
    // Note: The 'repository' parameter is already an instance of ShapeRepository.
    // If it was being created here, we'd pass eventBus.
    // However, ShapeRepository is passed *into* CoreCoordinator's constructor.
    // The place where CoreCoordinator is instantiated (likely App.tsx) is where
    // ShapeRepository needs to be created with the eventBus.
    // We've already handled that in App.tsx.
    // So, no change needed for 'this.repository = repository;' line itself.
    // The key is that the 'repository' instance passed to CoreCoordinator
    // must have been created with the eventBus.
    this.repository = repository
    this.validator = validator
    this.factory = factory
    this.logger = logger // Assign logger
    this.errorService = errorService
    this.computeFacade = computeFacade

    // Initialize config
    this.config = createConfig(initialConfig || defaultCoreConfig)

    // Initialize all services with direct instantiation for better type safety
    this.elementCreationService = ElementCreationService.create(this.factory, this.logger)
    this.elementEditService = new ElementEditServiceImpl(this.eventBus, this.logger)
    this.elementDeleteService = new ElementDeleteService(this.eventBus, this.logger)
    this.elementSelectionService = new ElementSelectionService(this.eventBus, this.logger)

    // Register event handlers to delegate requests to services
    this.registerEventHandlers()

    this.logger.info('CoreCoordinator initialized and event handlers registered.')
  }

  /**
   * 判断是否应该触发几何计算
   * 只有在几何相关属性变更时才触发，避免成本属性变更时的不必要计算
   */
  private shouldTriggerGeometryCalculation(changes: Record<string, unknown>): boolean {
    // 几何相关的属性列表
    const geometryProperties = [
      'width',
      'height',
      'radius',
      'radiusX',
      'radiusY',
      'x',
      'y',
      'position',
      'start',
      'end',
      'control',
      'control1',
      'control2',
      'points',
      'startAngle',
      'endAngle',
      'rotation',
      'scaleX',
      'scaleY',
    ]

    // 成本相关的属性列表（这些属性变更不应该触发几何计算）
    const costProperties = [
      'costEnabled',
      'costUnitPrice',
      'costBasis',
      'costMultiplierOrCount',
      'costQuantityFactor',
      'costTotal',
      'costStatus',
      'costError',
      'computeCostEnabled', // 添加这个属性
    ]

    // 检查顶层属性
    for (const key of Object.keys(changes)) {
      if (costProperties.includes(key)) {
        console.warn(`[CoreCoordinator] 检测到成本属性变更: ${key}，跳过几何计算`)
        continue
      }
      if (geometryProperties.includes(key)) {
        console.warn(`[CoreCoordinator] 检测到几何属性变更: ${key}`)
        return true
      }
    }

    // 检查 properties 中的属性
    if (changes.properties && typeof changes.properties === 'object') {
      const props = changes.properties as Record<string, unknown>
      for (const key of Object.keys(props)) {
        if (costProperties.includes(key)) {
          console.warn(`[CoreCoordinator] 检测到 properties 中的成本属性变更: ${key}，跳过几何计算`)
          continue
        }
        if (geometryProperties.includes(key)) {
          console.warn(`[CoreCoordinator] 检测到 properties 中的几何属性变更: ${key}`)
          return true
        }
      }
    }

    console.warn(`[CoreCoordinator] 未检测到几何属性变更，跳过几何计算`)
    return false
  }

  /**
   * Returns a deep copy of the current core configuration.
   * @returns The current configuration.
   */
  public getConfig(): CoreConfig {
    return createConfig(this.config) // Return a deep copy
  }

  /**
   * Updates the core configuration with partial settings.
   *
   * @param partialConfig - An object containing configuration properties to update.
   */
  public updateConfig(partialConfig: Partial<CoreConfig>): void {
    this.config = createConfig({
      ...this.config,
      ...partialConfig,
    })

    // Check if this is an internal update to avoid circular updates
    if (!this.isInternalUpdate) {
      this.publishShapesUpdate() // Reference publishShapesUpdate to ensure it's used
    }

    this.eventBus.publish({
      type: AppEventType.ConfigUpdated,
      payload: { config: this.getConfig() },
    })
  }

  /**
   * Centralized error handler for the core module.
   *
   * @remarks
   * Logs the error and publishes an `ERROR_OCCURRED` event.
   *
   * @param error - The error object (can be `Error` or `CoreError`).
   * @param context - Optional additional context information about the error.
   */
  public handleError(error: Error | CoreError, context?: unknown): void {
    // handleError in ErrorService expects one argument.
    // If 'error' is already a CoreError with context, it will be used.
    // If 'error' is a standard Error, ErrorService will wrap it.
    // The 'context' object here was previously passed as a second argument.
    // We might need to enrich the 'error' object itself if this context is vital
    // or adjust ErrorService.handleError if it's meant to take a context separately.
    // For now, assuming ErrorService's single argument design is intended.
    if (error instanceof Error && !(error instanceof CoreError) && context != null) {
      // Attach context to a standard error if it's not a CoreError
      (error as Error & { _additionalContext?: unknown })._additionalContext = context
    }
    this.errorService.handleError(error)
  }

  /**
   * Registers event handlers to delegate actions to the appropriate services.
   * @private
   */
  private registerEventHandlers(): void {
    console.warn('[CoreCoordinator] registerEventHandlers called')
    this.logger.info('Registering CoreCoordinator event handlers...')

    // Shape Creation
    this.eventBus.subscribe(
      AppEventType.ShapeCreateRequest,
      (event: BaseEvent) => {
        if (event.type === AppEventType.ShapeCreateRequest) {
          const specificEvent = event as ShapeCreateEvent // This is ShapeCreateEvent from @/types/services/events
          this.logger.debug?.('CoreCoordinator received SHAPE_CREATE_REQUEST', specificEvent.payload)

          // Directly use the payload from the event published by useShapeManipulation
          // The payload from useShapeManipulation has: ElementType (string), position, properties
          if (specificEvent.payload?.ElementType != null && specificEvent.payload.position != null) {
            // Construct the payload that ShapeCreationService.handleRequest expects
            // ShapeCreationService's ShapeCreateEventPayload is { elementType: ElementType (enum), position, properties }
            const servicePayload: ElementCreateEventPayload = {
              elementType: specificEvent.payload.ElementType as ElementType, // Cast string to ElementType enum
              position: specificEvent.payload.position as { x: number, y: number }, // Explicit type assertion
              properties: specificEvent.payload.properties || {},
            }

            // Construct the event for ShapeCreationService.handleRequest
            const serviceEventForCreationService: ElementCreateEvent = {
              type: specificEvent.type, // Use the original event type string
              payload: servicePayload,
              timestamp: specificEvent.timestamp ?? Date.now(),
            }

            this.elementCreationService.handleRequest(serviceEventForCreationService)
              .catch((err: unknown) => {
                const errorToHandle = err instanceof Error ? err : new Error(String(err))
                this.handleError(errorToHandle)
              })
          }
          else {
            this.logger.warn('CoreCoordinator: Invalid or unexpected payload for SHAPE_CREATE_REQUEST', specificEvent.payload)
          }
        }
      },
    )

    // Shape Editing
    this.eventBus.subscribe(
      AppEventType.ShapeEditRequest,
      (event: BaseEvent) => {
        void (async () => {
          if (event.type === AppEventType.ShapeEditRequest) {
            const specificEvent = event as ShapeEditEvent
            this.logger.debug?.('CoreCoordinator received SHAPE_EDIT_REQUEST', specificEvent.payload)
            console.warn('[CoreCoordinator] ShapeEditRequest payload:', specificEvent.payload)

            console.warn('[CoreCoordinator] SHAPE_EDIT_REQUEST received with changes:', JSON.stringify(specificEvent.payload.changes, null, 2)) // Added logging for changes

            // 保存当前选择状态
            const currentSelectedIds = Array.from(this.repository.getSelectedIds())
            console.warn('[CoreCoordinator] Current selection before edit:', currentSelectedIds)

            // 获取形状ID和变更内容
            const shapeId = specificEvent.payload.shapeId
            const changes = specificEvent.payload.changes || {}

            // 获取要编辑的形状
            if (!shapeId || shapeId === '') {
              console.error(`[CoreCoordinator] ELEMENT_EDIT_REQUEST missing shapeId`)
              return
            }
            const shape = this.repository.getById(shapeId)
            if (!shape) {
              console.error(`[CoreCoordinator] Shape ${shapeId} not found`)
              return
            }

            // 创建更新对象
            const updates: Partial<ShapeModel> = {}

            console.warn(`[CoreCoordinator] Processing changes:`, JSON.stringify(changes, null, 2))

            // 处理所有属性
            for (const key in changes) {
              if (Object.prototype.hasOwnProperty.call(changes, key)) {
                // 特殊处理pattern字段
                if (key === 'pattern') {
                  console.warn(`[CoreCoordinator] Processing pattern field for shape ${shapeId}`)
                  console.warn(`[CoreCoordinator] Original pattern value:`, JSON.stringify(changes.pattern, null, 2))

                  if (changes.pattern === undefined || changes.pattern === null) {
                    console.warn(`[CoreCoordinator] Setting pattern to undefined/null for deletion`);
                    // 🔧 修复：正确处理pattern为undefined的情况
                    (updates as any).pattern = changes.pattern // 使用any类型避免类型检查问题
                  }
                  else {
                    const cleanedPattern = cleanPattern(changes.pattern) as PatternDefinition
                    console.warn(`[CoreCoordinator] Cleaned pattern:`, JSON.stringify(cleanedPattern, null, 2))
                    updates.pattern = cleanedPattern
                  }
                }
                // 特殊处理position字段
                else if (key === 'position' && typeof changes.position === 'object') {
                  updates.position = { ...shape.position, ...changes.position as { x: number, y: number } }
                  console.warn(`[CoreCoordinator] Merged position:`, updates.position)
                }
                // 处理properties字段
                else if (key === 'properties' && typeof changes.properties === 'object') {
                  // 确保properties存在
                  if (!updates.properties) {
                    updates.properties = { ...shape.properties }
                  }

                  // 合并properties
                  const changesProps = changes.properties as Record<string, unknown>
                  for (const propKey in changesProps) {
                    if (Object.prototype.hasOwnProperty.call(changesProps, propKey)) {
                      const propertiesObj = updates.properties
                      propertiesObj[propKey] = changesProps[propKey]
                      console.warn(`[CoreCoordinator] Setting property ${propKey} to`, changesProps[propKey])

                      // 特殊处理图层属性，确保同时更新到顶层
                      if (propKey === 'majorCategory' || propKey === 'minorCategory' || propKey === 'zLevelId') {
                        const updatesObj = updates as Record<string, unknown>
                        updatesObj[propKey] = changesProps[propKey]
                        console.warn(`[CoreCoordinator] Also setting top-level ${propKey} to`, changesProps[propKey])

                        // 添加额外的日志，确认图层属性已正确设置
                        setTimeout(() => {
                          const updatedShape = this.repository.getById(shapeId)
                          if (updatedShape) {
                            console.warn(`[CoreCoordinator] DELAYED CHECK: Shape ${shapeId} ${propKey} after update:`, updatedShape[propKey as keyof ShapeModel])
                          }
                        }, 500)
                      }
                    }
                  }
                }
                // 特殊处理图层属性
                else if (key === 'majorCategory' || key === 'minorCategory' || key === 'zLevelId') {
                  const updatesObj = updates as Record<string, unknown>
                  updatesObj[key] = changes[key]
                  // 确保properties存在
                  if (!updates.properties) {
                    updates.properties = { ...shape.properties }
                  }
                  // 同时更新properties中的图层属性
                  const propertiesObj = updates.properties
                  propertiesObj[key] = changes[key]
                  console.warn(`[CoreCoordinator] Setting layer property ${key} to both top-level and properties:`, changes[key])
                }
                // 🔧 特殊处理LINE元素的start和end属性
                else if ((key === 'start' || key === 'end') && shape.type === 'LINE') {
                  // 确保properties存在
                  if (!updates.properties) {
                    updates.properties = { ...shape.properties }
                  }

                  // properties中存储相对坐标
                  const propertiesObj = updates.properties
                  propertiesObj[key] = changes[key]

                  // 顶层存储绝对坐标：position + 相对坐标
                  const position = shape.position || { x: 0, y: 0, z: 0 }
                  const relativePoint = changes[key] as { x: number, y: number, z?: number }
                  const absolutePoint = {
                    x: position.x + relativePoint.x,
                    y: position.y + relativePoint.y,
                    z: (position.z || 0) + (relativePoint.z || 0),
                  }

                  const updatesObj = updates as Record<string, unknown>
                  updatesObj[key] = absolutePoint

                  console.warn(`[CoreCoordinator] Setting LINE ${key} property:`, {
                    relative: changes[key],
                    position,
                    absolute: absolutePoint,
                  })
                }
                // 🔧 特殊处理QUADRATIC元素的控制点属性
                else if ((key === 'start' || key === 'control' || key === 'end') && shape.type === 'QUADRATIC') {
                  // 确保properties存在
                  if (!updates.properties) {
                    updates.properties = { ...shape.properties }
                  }

                  // properties中存储相对坐标
                  const propertiesObj = updates.properties
                  propertiesObj[key] = changes[key]

                  console.warn(`[CoreCoordinator] Setting QUADRATIC ${key} property (relative):`, changes[key])
                }
                // 🔧 特殊处理CUBIC元素的控制点属性
                else if ((key === 'start' || key === 'control1' || key === 'control2' || key === 'end') && shape.type === 'CUBIC') {
                  // 确保properties存在
                  if (!updates.properties) {
                    updates.properties = { ...shape.properties }
                  }

                  // properties中存储相对坐标
                  const propertiesObj = updates.properties
                  propertiesObj[key] = changes[key]

                  console.warn(`[CoreCoordinator] Setting CUBIC ${key} property (relative):`, changes[key])
                }
                // 处理几何属性（需要同时设置到顶层和properties中）
                else if (['radius', 'points', 'width', 'height', 'sides', 'isRegular'].includes(key)) {
                  // 设置到顶层
                  const updatesObj = updates as Record<string, unknown>
                  updatesObj[key] = changes[key]
                  // 确保properties存在
                  if (!updates.properties) {
                    updates.properties = { ...shape.properties }
                  }
                  // 同时更新properties中的几何属性
                  const propertiesObj = updates.properties
                  propertiesObj[key] = changes[key]
                  console.warn(`[CoreCoordinator] Setting geometry property ${key} to both top-level and properties:`, changes[key])
                }
                // 处理样式属性（需要同时设置到顶层和properties中）
                else if (['fill', 'stroke', 'strokeWidth', 'opacity', 'strokeDasharray', 'strokeLinecap', 'strokeLinejoin', 'cornerRadius', 'rotation'].includes(key)) {
                  // 设置到顶层
                  const updatesObj = updates as Record<string, unknown>
                  updatesObj[key] = changes[key]
                  // 确保properties存在
                  if (!updates.properties) {
                    updates.properties = { ...shape.properties }
                  }
                  // 同时更新properties中的样式属性
                  const propertiesObj = updates.properties
                  propertiesObj[key] = changes[key]
                  console.warn(`[CoreCoordinator] Setting style property ${key} to both top-level and properties:`, changes[key])
                }
                // 处理其他属性
                else {
                  const updatesObj = updates as Record<string, unknown>
                  updatesObj[key] = changes[key]
                  console.warn(`[CoreCoordinator] Setting ${key} to`, changes[key])
                }
              }
            }

            // 确保关键属性被正确处理
            console.warn(`[CoreCoordinator] Final updates object:`, JSON.stringify(updates, null, 2))

            // --- Handle nested properties (including geometry and cost) ---
            // Ensure existing properties are spread first, then changes.properties are merged
            // IMPORTANT: Preserve any properties already set in updates.properties from style/geometry processing above
            if ('properties' in changes && typeof changes.properties === 'object' && changes.properties !== null) {
              // Merge in this order: shape.properties -> updates.properties (from style/geometry processing) -> changes.properties
              updates.properties = {
                ...shape.properties,
                ...(updates.properties || {}), // Preserve style/geometry properties set above
                ...changes.properties as Record<string, unknown>,
              }
              console.warn(`[CoreCoordinator] Merged nested properties for shape ${shapeId}:`, JSON.stringify(updates.properties, null, 2))
              console.warn(`[CoreCoordinator] Original shape.properties:`, JSON.stringify(shape.properties, null, 2))
              console.warn(`[CoreCoordinator] Changes.properties:`, JSON.stringify(changes.properties, null, 2))
            }
            else if (shape.properties) {
              // If no changes.properties but shape has properties, merge with existing updates.properties
              updates.properties = { ...shape.properties, ...(updates.properties || {}) }
              console.warn(`[CoreCoordinator] Merged existing shape.properties with updates.properties:`, JSON.stringify(updates.properties, null, 2))
            }

            // 确保成本相关属性正确地应用到元素上
            // 如果changes中包含成本相关属性，则使用changes中的值
            // 否则，使用shape中的现有值
            console.warn(`[CoreCoordinator] Starting cost properties processing for shape ${shapeId}`)

            if (shape.properties) {
              // 创建或获取现有的properties对象
              if (!updates.properties) {
                updates.properties = {}
              }

              // 安全地处理 changes.properties - 修复原始问题
              const changesProps = (changes && typeof changes === 'object' && 'properties' in changes && changes.properties != null && typeof changes.properties === 'object')
                ? (changes.properties as Record<string, unknown>)
                : {}

              console.warn(`[CoreCoordinator] changesProps extracted:`, JSON.stringify(changesProps, null, 2))

              const propertiesObj = updates.properties

              // 处理costUnitPrice - 明确检查是否在changes.properties中存在
              if ('costUnitPrice' in changesProps) {
                // 如果明确设置了值（即使是undefined），使用该值
                propertiesObj.costUnitPrice = changesProps.costUnitPrice
              }
              else if (shape.properties.costUnitPrice !== undefined) {
                // 如果changes.properties中没有明确设置，但shape中有值，则保留原值
                propertiesObj.costUnitPrice = shape.properties.costUnitPrice
              }
              else {
                // 如果都没有，设置默认值为1
                propertiesObj.costUnitPrice = 1
              }

              // 处理costBasis
              if ('costBasis' in changesProps) {
                // 如果明确设置了值（即使是undefined），使用该值
                propertiesObj.costBasis = changesProps.costBasis
              }
              else if (shape.properties.costBasis !== undefined) {
                // 如果changes.properties中没有明确设置，但shape中有值，则保留原值
                propertiesObj.costBasis = shape.properties.costBasis
              }
              else {
                // 如果都没有，设置默认值为'unit'
                propertiesObj.costBasis = 'unit'
              }

              // 处理costMultiplierOrCount
              if ('costMultiplierOrCount' in changesProps) {
                // 如果明确设置了值（即使是undefined），使用该值
                propertiesObj.costMultiplierOrCount = changesProps.costMultiplierOrCount
              }
              else if (shape.properties.costMultiplierOrCount !== undefined) {
                // 如果changes.properties中没有明确设置，但shape中有值，则保留原值
                propertiesObj.costMultiplierOrCount = shape.properties.costMultiplierOrCount
              }
              else {
                // 如果都没有，设置默认值为0，确保初始成本为0
                propertiesObj.costMultiplierOrCount = 0
              }

              console.warn(`[CoreCoordinator] Final cost properties for shape ${shapeId}:`, JSON.stringify({
                costUnitPrice: propertiesObj.costUnitPrice,
                costBasis: propertiesObj.costBasis,
                costMultiplierOrCount: propertiesObj.costMultiplierOrCount,
              }, null, 2))
            }
            console.warn(`[CoreCoordinator] Completed cost properties processing for shape ${shapeId}`)

            // 更新形状
            console.warn(`[CoreCoordinator] Updating shape ${shapeId} with:`, JSON.stringify(updates, null, 2))
            this.repository.update(shapeId, updates)
            await new Promise(resolve => setTimeout(resolve, 50)) // Add a small delay to allow repository update to propagate
            console.warn(`[CoreCoordinator] 已更新形状 ${shapeId} 的属性`)

            // 确保选择状态保持不变
            this.repository.setSelectedIds(currentSelectedIds)

            // Synchronize state from repository to Zustand store via event
            this.publishShapesUpdate()

            // 发布ShapeEditComplete事件
            this.eventBus.publish({
              type: AppEventType.ShapeEditComplete,
              timestamp: Date.now(),
              payload: {
                shape: this.repository.getById(shapeId), // 传递最新 shape 对象
                selectedIds: currentSelectedIds,
              },
            })

            // 确保UI更新显示正确的选择状态
            setTimeout(() => {
              this.eventBus.publish({
                type: AppEventType.SelectionChanged,
                timestamp: Date.now(),
                payload: { selectedIds: currentSelectedIds },
              })
            }, 50) // 增加延迟以确保选择状态正确应用

            // === 新增：属性变更后，调用 core 计算 area/perimeter/cost 并加日志 ===
            // 只有在几何相关属性变更时才触发几何计算，避免成本属性变更时的不必要计算
            const shouldTriggerGeometryCalculation = this.shouldTriggerGeometryCalculation(changes)
            console.warn(`[CoreCoordinator][compute] 是否需要触发几何计算: ${shouldTriggerGeometryCalculation}`)

            if (this.computeFacade && shouldTriggerGeometryCalculation) {
              try {
                console.warn(`[CoreCoordinator][compute] 开始计算 shapeId=${shapeId} 的面积、周长和成本...`)

                // 获取最新的shape对象，确保使用最新的属性值
                const shapeToCompute = this.repository.getById(shapeId)
                if (!shapeToCompute) {
                  console.warn(`[CoreCoordinator][compute] 无法找到形状 ${shapeId}`)
                  return
                }

                console.warn(`[CoreCoordinator][compute] 形状类型: ${shapeToCompute.type}`)

                // 计算面积
                console.warn(`[CoreCoordinator][compute] 计算面积...`)
                let area = 0
                let areaStatus = 'none' // ComputeStatus.NONE
                let areaError: string | undefined
                try {
                  area = await this.computeFacade.computeArea(shapeId)
                  console.warn(`[CoreCoordinator][compute] 面积计算成功: ${area}`)
                  areaStatus = 'success' // ComputeStatus.SUCCESS
                }
                catch (areaErr) {
                  console.error(`[CoreCoordinator][compute] 面积计算失败:`, areaErr)
                  areaStatus = 'error' // ComputeStatus.ERROR
                  areaError = areaErr instanceof Error ? areaErr.message : String(areaErr)
                }

                // 计算周长
                console.warn(`[CoreCoordinator][compute] 计算周长...`)
                let perimeter = 0
                let perimeterStatus = 'none' // ComputeStatus.NONE
                let perimeterError: string | undefined
                try {
                  perimeter = await this.computeFacade.computePerimeter(shapeId)
                  console.warn(`[CoreCoordinator][compute] 周长计算成功: ${perimeter}`)
                  perimeterStatus = 'success' // ComputeStatus.SUCCESS
                }
                catch (perimeterErr) {
                  console.error(`[CoreCoordinator][compute] 周长计算失败:`, perimeterErr)
                  perimeterStatus = 'error' // ComputeStatus.ERROR
                  perimeterError = perimeterErr instanceof Error ? perimeterErr.message : String(perimeterErr)
                }

                // 获取最新的shape对象的properties - 使用更新后的properties
                const shapeProps = updates.properties || shapeToCompute.properties || {}

                console.warn(`[CoreCoordinator][compute] USING properties from shape for cost computation:`, JSON.stringify(shapeProps, null, 2))

                // 从shape对象中提取成本计算参数
                // 默认单位成本为1而不是0
                const unitCost = typeof shapeProps.costUnitPrice === 'number' && !Number.isNaN(shapeProps.costUnitPrice) && shapeProps.costUnitPrice > 0
                  ? shapeProps.costUnitPrice
                  : 1
                console.warn(`[CoreCoordinator][compute] 单位成本 (costUnitPrice): ${unitCost}, 原始值: ${String(shapeProps.costUnitPrice)}, 类型: ${typeof shapeProps.costUnitPrice})`)

                // 获取乘数
                const multiplier = typeof shapeProps.costMultiplierOrCount === 'number' && !Number.isNaN(shapeProps.costMultiplierOrCount)
                  ? shapeProps.costMultiplierOrCount
                  : 1 // 🔧 修复：默认为1而不是0，避免总成本为0
                console.warn(`[CoreCoordinator][compute] 乘数 (costMultiplierOrCount): ${multiplier}, 原始值: ${String(shapeProps.costMultiplierOrCount)}, 类型: ${typeof shapeProps.costMultiplierOrCount})`)

                // 将 costBasis 映射到 costType
                let costType: 'area' | 'perimeter' | 'unit' | 'segment' | 'fixed' = 'unit'
                const costBasisToUse = shapeProps.costBasis as string | undefined

                if (typeof costBasisToUse === 'string' && ['area', 'perimeter', 'unit', 'segment', 'fixed'].includes(costBasisToUse)) {
                  costType = costBasisToUse as 'area' | 'perimeter' | 'unit' | 'segment' | 'fixed'
                }
                console.warn(`[CoreCoordinator][compute] 成本基准 (costType): ${costType}, 原始值: ${String(shapeProps.costBasis)}, 类型: ${typeof costBasisToUse})`)

                const quantity = typeof shapeProps.costMultiplierOrCount === 'number' ? shapeProps.costMultiplierOrCount : 0
                console.warn(`[CoreCoordinator][compute] 数量 (quantity): ${quantity}, 原始值: ${String(shapeProps.costMultiplierOrCount)}, 类型: ${typeof shapeProps.costMultiplierOrCount})`)

                // 组装计算选项
                const options = {
                  costType,
                  quantity,
                  multiplier, // 添加乘数到计算选项
                }
                console.warn(`[CoreCoordinator][compute] 计算选项: unitCost=${unitCost}, costType=${costType}, quantity=${quantity}, multiplier=${multiplier}`)

                // 计算成本
                console.warn(`[CoreCoordinator][compute] 计算成本...`)
                console.warn(`[CoreCoordinator][compute] Passing unitCost to computeFacade.computeCost: ${unitCost}, Type: ${typeof unitCost}`)
                let cost = 0
                try {
                  cost = await this.computeFacade.computeCost(shapeId, unitCost, options)
                  console.warn(`[CoreCoordinator][compute] 成本计算成功: ${cost}`)
                }
                catch (costErr) {
                  console.error(`[CoreCoordinator][compute] 成本计算失败:`, costErr)

                  // 手动计算成本
                  // 基于costType计算基础值
                  let baseValue = 1 // 默认为1单位
                  if (costType === 'area') {
                    try {
                      baseValue = await this.computeFacade.computeArea(shapeId)
                      console.warn(`[CoreCoordinator][compute] 面积计算成功: ${baseValue}`)
                    }
                    catch (areaErr) {
                      console.error(`[CoreCoordinator][compute] 面积计算失败:`, areaErr)
                    }
                  }
                  else if (costType === 'perimeter') {
                    try {
                      baseValue = await this.computeFacade.computePerimeter(shapeId)
                      console.warn(`[CoreCoordinator][compute] 周长计算成功: ${baseValue}`)
                    }
                    catch (perimeterErr) {
                      console.error(`[CoreCoordinator][compute] 周长计算失败:`, perimeterErr)
                    }
                  }

                  // 计算总成本
                  if (costType === 'unit' || costType === 'segment') {
                    // 对于按单位或线段计算的元素，使用quantity
                    cost = unitCost * baseValue * quantity
                    console.warn(`[CoreCoordinator][compute] 手动计算成本(按单位): ${unitCost} * ${baseValue} * ${quantity} = ${cost}`)
                  }
                  else {
                    // 对于按面积或周长计算的元素，使用multiplier
                    cost = unitCost * baseValue * multiplier
                    console.warn(`[CoreCoordinator][compute] 手动计算成本(按面积/周长): ${unitCost} * ${baseValue} * ${multiplier} = ${cost}`)
                  }
                }

                // 成本可以为0（当乘数为0时）
                console.warn(`[CoreCoordinator][compute] 最终计算成本: ${cost}`)

                console.warn(`[CoreCoordinator][compute] 计算结果: shapeId=${shapeId}, area=${area}, perimeter=${perimeter}, cost=${cost}`)

                // Update the shape's costTotal property and ensure other cost-related properties are included

                // 保存关键几何属性，确保不会被覆盖
                const { radius, width, height } = shapeProps

                const updatedProps = {
                  ...shapeProps, // Start with the properties used for computation
                  // 几何计算结果 - 持久化存储（支持14个基本元素）
                  computedArea: area,
                  computedPerimeter: perimeter,
                  computedLength: perimeter, // 对于路径类型，周长就是长度
                  computedAreaUnit: 'mm²', // 基础单位：平方毫米
                  computedPerimeterUnit: 'mm', // 基础单位：毫米
                  computedLengthUnit: 'mm', // 基础单位：毫米
                  // 计算状态信息
                  computedAreaStatus: areaStatus,
                  computedPerimeterStatus: perimeterStatus,
                  computedLengthStatus: perimeterStatus, // 对于路径类型，长度状态与周长状态相同
                  computedAreaError: areaError,
                  computedPerimeterError: perimeterError,
                  computedLengthError: perimeterError, // 对于路径类型，长度错误与周长错误相同
                  // 成本计算结果
                  costTotal: cost,
                  // 确保所有成本相关属性都被包含
                  costUnitPrice: unitCost,
                  // 🔧 修复：不要覆盖用户设置的costBasis！保持原有的值
                  // costBasis: costType, // 这行是错误的！会覆盖用户设置
                  // 不要覆盖用户设置的乘数值！保持原有的 costMultiplierOrCount
                  // costMultiplierOrCount: quantity, // 这行是错误的！
                }

                // 如果是圆形元素，确保保留原始的半径和直径
                if (shapeToCompute.type === 'circle' && radius !== undefined) {
                  console.warn(`[CoreCoordinator][compute] 保留圆形元素的原始半径: ${String(radius)}和直径: ${String(width)}x${String(height)}`)
                  // 使用明确的类型定义来避免TypeScript错误
                  const propsObj = updatedProps as Record<string, unknown>
                  propsObj.radius = radius
                  if (width !== undefined)
                    propsObj.width = width
                  if (height !== undefined)
                    propsObj.height = height
                }

                console.warn(`[CoreCoordinator][compute] 更新前的属性:`, JSON.stringify(shapeProps, null, 2))
                console.warn(`[CoreCoordinator][compute] 更新后的属性:`, JSON.stringify(updatedProps, null, 2))

                this.repository.update(shapeId, { properties: updatedProps })
                console.warn(`[CoreCoordinator][compute] 已更新形状 ${shapeId} 的属性`)

                // 触发 UI 层数据刷新
                const storeShapes = useShapesStore.getState().shapes
                const updatedShapes = storeShapes.map(s =>
                  s.id === shapeId
                    ? {
                        ...s,
                        properties: {
                          ...s.properties,
                          // 几何计算结果 - 持久化存储（支持14个基本元素）
                          computedArea: area,
                          computedPerimeter: perimeter,
                          computedLength: perimeter, // 对于路径类型，周长就是长度
                          computedAreaUnit: 'mm²',
                          computedPerimeterUnit: 'mm',
                          computedLengthUnit: 'mm',
                          // 计算状态信息
                          computedAreaStatus: areaStatus,
                          computedPerimeterStatus: perimeterStatus,
                          computedLengthStatus: perimeterStatus, // 对于路径类型，长度状态与周长状态相同
                          computedAreaError: areaError,
                          computedPerimeterError: perimeterError,
                          computedLengthError: perimeterError, // 对于路径类型，长度错误与周长错误相同
                          // 成本计算结果
                          costTotal: cost,
                          // Ensure UI update uses the correct cost properties
                          costUnitPrice: unitCost,
                          // 🔧 修复：不要覆盖用户设置的costBasis！保持原有的值
                          // costBasis: costType, // 这行是错误的！会覆盖用户设置
                          // 不要覆盖用户设置的乘数值！保持原有的 costMultiplierOrCount
                          // costMultiplierOrCount: quantity, // 这行是错误的！
                        },
                      }
                    : s,
                )
                useShapesStore.getState().setShapesFromExternal(updatedShapes, currentSelectedIds)
              }
              catch (err) {
                console.warn('[CoreCoordinator][compute] 计算 area/perimeter/cost 失败', err)
              }
            }
          }
        })()
      },
    )

    // Shape Deletion
    this.eventBus.subscribe(
      AppEventType.ShapeDeleteRequest,
      (event: BaseEvent) => {
        if (event.type === AppEventType.ShapeDeleteRequest) {
          const specificEvent = event as ShapeDeleteEvent
          this.logger.debug?.('CoreCoordinator received SHAPE_DELETE_REQUEST', specificEvent.payload)
          // ShapeDeleteService has handleRequest(event: ShapeDeleteEvent)
          if (specificEvent.payload !== null && specificEvent.payload !== undefined) {
            this.elementDeleteService.handleRequest(specificEvent)
              .catch((err: unknown) => this.handleError(err as Error))
          }
          else {
            this.logger.warn?.('SHAPE_DELETE_REQUEST received with invalid payload', specificEvent)
          }
        }
      },
    )

    // Shape Selection
    this.eventBus.subscribe(
      AppEventType.ShapeSelectRequest,
      (event: BaseEvent) => {
        if (event.type === AppEventType.ShapeSelectRequest) {
          // ...原有ShapeSelectRequest处理逻辑...
          this._handleShapeSelectRequest(event)
        }
      },
    )
    // 新增：监听ElementSelectRequest，复用同一处理逻辑
    this.eventBus.subscribe(
      AppEventType.ElementSelectRequest,
      (event: BaseEvent) => {
        if (event.type === AppEventType.ElementSelectRequest) {
          this._handleShapeSelectRequest(event)
        }
      },
    )

    // Shape Creation Complete (add new shape to store)
    this.eventBus.subscribe(
      AppEventType.ShapeCreateComplete,
      (event: BaseEvent) => {
        console.warn('[CoreCoordinator] ShapeCreateComplete event received:', event)
        if (event.type === AppEventType.ShapeCreateComplete) {
          // Strict payload validation
          if (event.payload !== null && event.payload !== undefined && typeof event.payload === 'object' && 'shape' in event.payload) {
            const shape = (event.payload as { shape?: Record<string, unknown> }).shape
            if (shape !== null && shape !== undefined && typeof shape === 'object' && typeof (shape as { id?: unknown }).id === 'string') {
              // --- PATCH: push current shapes to past, clear future ---
              const currentShapes = useShapesStore.getState().shapes
              this.past.push([...currentShapes])
              this.future = []
              // Debug: log zustand singleton debug id
              console.warn('[CoreCoordinator] useShapesStore singleton debug id:', (window as { __ZUSTAND_SHAPES_STORE_DEBUG_ID__?: string }).__ZUSTAND_SHAPES_STORE_DEBUG_ID__)

              // 获取当前选中的元素ID或使用事件中传递的选中IDs
              let currentSelectedIds: string[] = []

              // 从事件payload中获取选中IDs（如果有）
              if ('selectedIds' in event.payload && Array.isArray(event.payload.selectedIds)) {
                currentSelectedIds = event.payload.selectedIds as string[]
                console.warn('[CoreCoordinator] Using selectedIds from event payload:', currentSelectedIds)
              }
              else {
                // 否则从Zustand获取当前选中IDs
                currentSelectedIds = useShapesStore.getState().selectedShapeIds
                console.warn('[CoreCoordinator] Using selectedIds from Zustand store:', currentSelectedIds)
              }

              // 新功能：确保新形状在同层中显示在最顶层
              console.warn('[CoreCoordinator] Setting new shape to be on top of its layer')

              // 只在有图层信息时处理
              const shapeWithTypes = shape as Record<string, unknown> & {
                majorCategory?: string
                zLevelId?: string
                minorCategory?: string
                id?: string
                intraLayerZIndex?: number
              }
              if (shapeWithTypes.majorCategory !== null && shapeWithTypes.majorCategory !== undefined
                && shapeWithTypes.zLevelId !== null && shapeWithTypes.zLevelId !== undefined) {
                // 找到同一图层中的所有形状
                const shapesInSameLayer = currentShapes.filter((s: ShapeModel) =>
                  s.majorCategory === shapeWithTypes.majorCategory
                  && s.minorCategory === shapeWithTypes.minorCategory
                  && s.zLevelId === shapeWithTypes.zLevelId,
                )

                // 计算同层中的最大Z索引
                let maxZIndex = 0
                if (shapesInSameLayer.length > 0) {
                  maxZIndex = shapesInSameLayer.reduce(
                    (max: number, s: ShapeModel) => Math.max(max, s.intraLayerZIndex ?? 0),
                    0,
                  )
                }

                // 设置新形状的intraLayerZIndex为最大值+1
                shapeWithTypes.intraLayerZIndex = maxZIndex + 1
                console.warn(`[CoreCoordinator] Set new shape ${String(shapeWithTypes.id)} intraLayerZIndex to ${maxZIndex + 1}`)
              }

              // 关键修复：将新形状添加到ShapeRepository中
              console.warn('[CoreCoordinator] Adding shape to repository:', shape)
              this.repository.add(shape as unknown as ShapeModel)

              // 确保在repository中保持选中状态
              this.repository.setSelectedIds(currentSelectedIds)

              // Merge new shape into store - 使用深拷贝避免引用问题
              const newShapes = [...currentShapes.filter((s: ShapeModel) => s.id !== (shape as { id?: string }).id), { ...shape } as unknown as ShapeModel]

              // 使用现有的选中状态更新store
              useShapesStore.getState().setShapesFromExternal(newShapes, currentSelectedIds)
              this.logger.info?.('[CoreCoordinator] ShapeCreateComplete: Added new shape to store.', shape)

              // 延迟重新发布SelectionChanged事件，确保UI更新选中状态
              setTimeout(() => {
                console.warn('[CoreCoordinator] Re-publishing SelectionChanged after create to maintain selection:', currentSelectedIds)
                this.eventBus.publish({
                  type: AppEventType.SelectionChanged,
                  timestamp: Date.now(),
                  payload: { selectedIds: currentSelectedIds },
                })
              }, 0)

              // 自动触发几何计算，确保新创建的元素立即显示计算结果
              setTimeout(() => {
                const shapeId = (shape as { id: string }).id
                console.warn('[CoreCoordinator] Auto-triggering geometry calculation for newly created shape:', shapeId)
                this.eventBus.publish({
                  type: AppEventType.ComputeRequest,
                  timestamp: Date.now(),
                  payload: {
                    shapeId,
                    operation: 'all',
                    source: 'CoreCoordinator-AutoCompute',
                  },
                })
              }, 50) // 🔧 修复：减少延迟，确保计算更快完成
            }
            else {
              this.logger.warn?.('[CoreCoordinator] ShapeCreateComplete event payload.shape is missing or invalid.', event.payload)
            }
          }
          else {
            this.logger.error?.('[CoreCoordinator] ShapeCreateComplete event payload structure invalid. Expected { shape }, got:', event.payload)
          }
        }
      },
    )

    this.eventBus.subscribe(
      AppEventType.HistoryUndo,
      (event: BaseEvent) => {
        console.warn('[CoreCoordinator] HistoryUndo event received:', event)
        if (this.past.length > 0) {
          const currentShapes = useShapesStore.getState().shapes
          this.future.push([...currentShapes])
          const prev = this.past.pop()
          if (prev) {
            console.warn(`[CoreCoordinator] Undo: restoring ${prev.length} shapes from previous state. Current shapes: ${currentShapes.length}`)
            useShapesStore.getState().setShapesFromExternal([...prev])
            this.logger.info?.('[CoreCoordinator] Undo: restored previous shapes snapshot.')
          }
        }
        else {
          this.logger.info?.('[CoreCoordinator] Undo: no more history.')
        }
      },
    )
    this.eventBus.subscribe(
      AppEventType.HistoryRedo,
      (event: BaseEvent) => {
        console.warn('[CoreCoordinator] HistoryRedo event received:', event)
        if (this.future.length > 0) {
          const currentShapes = useShapesStore.getState().shapes
          this.past.push([...currentShapes])
          const next = this.future.pop()
          if (next) {
            useShapesStore.getState().setShapesFromExternal([...next])
            this.logger.info?.('[CoreCoordinator] Redo: restored next shapes snapshot.')
          }
        }
        else {
          this.logger.info?.('[CoreCoordinator] Redo: no more future.')
        }
      },
    )

    // Shape Editing Complete (add edit snapshot to history)
    this.eventBus.subscribe(
      AppEventType.ShapeEditComplete,
      (event: BaseEvent) => {
        console.warn('[CoreCoordinator] ShapeEditComplete event received:', event)
        if (event.type === AppEventType.ShapeEditComplete) {
          if (event.payload !== null && event.payload !== undefined && typeof event.payload === 'object' && 'shape' in event.payload) {
            const shape = (event.payload as { shape?: unknown }).shape
            if (shape !== null && shape !== undefined && typeof shape === 'object' && 'id' in shape && typeof (shape as { id: unknown }).id === 'string') {
              // 不再保存状态到past数组，因为已在preEdit中保存

              // 获取当前选中的元素ID或使用事件中传递的选中IDs
              let currentSelectedIds: string[] = []

              // 从事件payload中获取选中IDs（如果有）
              if ('selectedIds' in event.payload && Array.isArray(event.payload.selectedIds)) {
                currentSelectedIds = event.payload.selectedIds as string[]
                console.warn('[CoreCoordinator] Using selectedIds from event payload:', currentSelectedIds)
              }
              else {
                // 否则从Zustand获取当前选中IDs
                currentSelectedIds = useShapesStore.getState().selectedShapeIds
                console.warn('[CoreCoordinator] Using selectedIds from Zustand store:', currentSelectedIds)
              }

              const currentShapes = useShapesStore.getState().shapes
              console.warn('[CoreCoordinator] Current selection before update:', currentSelectedIds)

              // 使用深拷贝更新形状，避免引用问题
              const newShapes = currentShapes.map((s: ShapeModel) =>
                s.id === (shape as { id: string }).id ? JSON.parse(JSON.stringify(shape)) as ShapeModel : s,
              )

              // 更新Zustand store，同时保留选中状态
              useShapesStore.getState().setShapesFromExternal(newShapes, currentSelectedIds)

              // 确保repository中也更新形状
              this.repository.update((shape as { id: string }).id, JSON.parse(JSON.stringify(shape)) as Partial<Omit<ShapeModel, 'id'>>)

              // 确保repository中的选中状态也一致 - 关键修复：在更新后重新设置选中状态
              this.repository.setSelectedIds(currentSelectedIds)

              this.logger.info?.('[CoreCoordinator] ShapeEditComplete: Edited shape in store.', shape)

              // 延迟重新发布SelectionChanged事件，确保UI更新选中状态
              setTimeout(() => {
                console.warn('[CoreCoordinator] Re-publishing SelectionChanged to maintain selection:', currentSelectedIds)
                this.eventBus.publish({
                  type: AppEventType.SelectionChanged,
                  timestamp: Date.now(),
                  payload: { selectedIds: currentSelectedIds },
                })
              }, 0)
            }
            else {
              this.logger.warn?.('[CoreCoordinator] ShapeEditComplete event payload.shape is missing or invalid.', event.payload)
            }
          }
          else {
            this.logger.error?.('[CoreCoordinator] ShapeEditComplete event payload structure invalid. Expected { shape }, got:', event.payload)
          }
        }
      },
    )

    // Shape Deletion Complete (add delete snapshot to history)
    this.eventBus.subscribe(
      AppEventType.ShapeDeleteComplete,
      (event: BaseEvent) => {
        console.warn('[CoreCoordinator] ShapeDeleteComplete event received:', event)
        if (event.type === AppEventType.ShapeDeleteComplete) {
          if (event.payload !== null && event.payload !== undefined && typeof event.payload === 'object' && 'shapeId' in event.payload) {
            const shapeIdOrIds = (event.payload as { shapeId?: string | string[] }).shapeId

            if (shapeIdOrIds === undefined || shapeIdOrIds === null) {
              this.logger.warn?.('[CoreCoordinator] ShapeDeleteComplete event payload.shapeId is undefined or null.', event.payload)
              return
            }

            const currentShapes = useShapesStore.getState().shapes

            // 不再保存状态到past数组，因为已在preDelete中保存
            // 现在执行实际的删除操作
            if (Array.isArray(shapeIdOrIds)) {
              console.warn(`[CoreCoordinator] Deleting ${shapeIdOrIds.length} shapes from repository`)
              // 从repository中删除这些形状
              shapeIdOrIds.forEach((id) => {
                this.repository.remove(id)
              })
              // 更新store中的形状列表
              const newShapes = currentShapes.filter((s: ShapeModel) => !shapeIdOrIds.includes(s.id))
              useShapesStore.getState().setShapesFromExternal(newShapes)
            }
            else if (typeof shapeIdOrIds === 'string') {
              console.warn(`[CoreCoordinator] Deleting single shape ${shapeIdOrIds} from repository`)
              // 从repository删除单个形状
              this.repository.remove(shapeIdOrIds)
              // 更新store中的形状列表
              const newShapes = currentShapes.filter((s: ShapeModel) => s.id !== shapeIdOrIds)
              useShapesStore.getState().setShapesFromExternal(newShapes)
            }
            else {
              this.logger.warn?.('[CoreCoordinator] ShapeDeleteComplete event payload.shapeId has invalid type.', event.payload)
              return
            }

            this.logger.info?.(
              '[CoreCoordinator] Completed deletion of shapes from repository.',
              { shapeIdOrIds },
            )
          }
          else {
            this.logger.error?.('[CoreCoordinator] ShapeDeleteComplete event payload structure invalid. Expected { shapeId }, got:', event.payload)
          }
        }
      },
    )

    // Selection Changed (update selectedShapeIds in store)
    this.eventBus.subscribe(
      AppEventType.SelectionChanged,
      (event: BaseEvent) => {
        console.warn('[CoreCoordinator] SelectionChanged event received:', event)
        if (event.type === AppEventType.SelectionChanged) {
          if (event.payload !== null && event.payload !== undefined && typeof event.payload === 'object' && 'selectedIds' in event.payload) {
            const selectedShapeIds = (event.payload as { selectedIds?: string[] }).selectedIds
            if (Array.isArray(selectedShapeIds)) {
              // Only update selectedShapeIds, keep shapes unchanged
              const currentShapes = useShapesStore.getState().shapes
              useShapesStore.getState().setShapesFromExternal(currentShapes, selectedShapeIds)
              this.logger.info?.('[CoreCoordinator] SelectionChanged: Updated selectedShapeIds in store.', selectedShapeIds)
            }
            else {
              this.logger.warn?.('[CoreCoordinator] SelectionChanged event payload.selectedIds is missing or invalid.', event.payload)
            }
          }
          else {
            this.logger.error?.('[CoreCoordinator] SelectionChanged event payload structure invalid. Expected { selectedIds }, got:', event.payload)
          }
        }
      },
    )

    // 添加对StateUpdated事件的处理，特别是处理删除操作前的状态保存
    this.eventBus.subscribe(
      AppEventType.StateUpdated,
      (event: BaseEvent) => {
        console.warn('[CoreCoordinator] StateUpdated event received:', event)
        if (event.type === AppEventType.StateUpdated) {
          if (event.payload !== null && event.payload !== undefined && typeof event.payload === 'object') {
            // 如果是删除前的状态更新，保存当前状态到历史记录
            if ('action' in event.payload) {
              if (event.payload.action === 'preDelete') {
                console.warn('[CoreCoordinator] Saving state before delete operation')
                const currentShapes = useShapesStore.getState().shapes
                // 保存当前状态到past数组，清空future数组
                this.past.push([...currentShapes])
                this.future = []
                this.logger.info?.('[CoreCoordinator] Pre-delete state saved to history.', { elementCount: currentShapes.length })
              }
              else if (event.payload.action === 'preEdit') {
                console.warn('[CoreCoordinator] Saving state before edit operation')
                const currentShapes = useShapesStore.getState().shapes
                // 保存当前状态到past数组，清空future数组
                this.past.push([...currentShapes])
                this.future = []
                this.logger.info?.('[CoreCoordinator] Pre-edit state saved to history.', { elementCount: currentShapes.length, elementIds: 'elementIds' in event.payload ? event.payload.elementIds : 'unknown' })
              }
            }
          }
        }
      },
    )

    // 处理形状置顶请求
    this.eventBus.subscribe(
      AppEventType.ShapeBringToFrontRequest,
      (event: BaseEvent) => {
        if (event.type === AppEventType.ShapeBringToFrontRequest) {
          console.warn('[CoreCoordinator] ShapeBringToFrontRequest event received:', event)

          // 提取形状ID
          const shapeId = event.payload !== null && event.payload !== undefined && typeof event.payload === 'object' && 'shapeId' in event.payload
            ? (event.payload as { shapeId: string }).shapeId
            : undefined
          if (shapeId === null || shapeId === undefined || shapeId === '') {
            console.error('[CoreCoordinator] ShapeBringToFrontRequest: Missing shapeId in payload')
            return
          }

          // 保存当前选择状态
          const currentSelectedIds = Array.from(this.repository.getSelectedIds())
          console.warn('[CoreCoordinator] Current selection before bringToFront:', currentSelectedIds)

          // 执行置顶操作
          try {
            // 直接调用repository的方法，不再使用类型断言
            const updatedShape = this.repository.bringToFrontInLayer(shapeId)
            if (updatedShape) {
              console.warn(`[CoreCoordinator] Successfully brought shape ${shapeId} to front in layer, new intraLayerZIndex: ${updatedShape.intraLayerZIndex}`)

              // 确保选择状态保持不变
              this.repository.setSelectedIds(currentSelectedIds)

              // 发布完成事件
              this.eventBus.publish({
                type: AppEventType.ShapeBringToFrontComplete,
                timestamp: Date.now(),
                payload: {
                  shape: updatedShape,
                  selectedIds: currentSelectedIds,
                  source: 'CoreCoordinator',
                },
              })

              // 更新Zustand store
              this.publishShapesUpdate()
            }
            else {
              console.error(`[CoreCoordinator] Failed to bring shape ${shapeId} to front in layer`)
            }
          }
          catch (error) {
            console.error(`[CoreCoordinator] Error bringing shape ${shapeId} to front:`, error)
          }
        }
      },
    )

    // 处理计算请求
    this.eventBus.subscribe(
      AppEventType.ComputeRequest,
      (event: BaseEvent) => {
        void (async () => {
          if (event.type === AppEventType.ComputeRequest) {
            console.warn('[CoreCoordinator] ComputeRequest event received:', event)

            // 检查payload是否有效
            if (event.payload === null || event.payload === undefined || typeof event.payload !== 'object') {
              console.error('[CoreCoordinator] ComputeRequest: Invalid payload', event.payload)
              return
            }

            // 提取形状ID
            const payloadWithShapeId = event.payload as Record<string, unknown>
            const shapeId = 'shapeId' in payloadWithShapeId ? payloadWithShapeId.shapeId : undefined
            if (shapeId === null || shapeId === undefined || typeof shapeId !== 'string') {
              console.error('[CoreCoordinator] ComputeRequest: Missing shapeId in payload')
              return
            }

            // 提取操作类型
            const operation = 'operation' in payloadWithShapeId ? payloadWithShapeId.operation : undefined
            if (operation === null || operation === undefined || typeof operation !== 'string') {
              console.error('[CoreCoordinator] ComputeRequest: Missing operation in payload')
              return
            }

            // 保存当前选择状态
            const currentSelectedIds = Array.from(this.repository.getSelectedIds())

            // 处理单独的面积计算请求
            if (operation === 'area' && this.computeFacade) {
              try {
                console.warn(`[CoreCoordinator][computeRequest] 开始计算形状 ${shapeId} 的面积...`)

                // 获取最新的shape对象
                const shapeToCompute = this.repository.getById(shapeId)
                if (!shapeToCompute) {
                  console.warn(`[CoreCoordinator][computeRequest] 无法找到形状 ${shapeId}`)
                  return
                }

                // 计算面积
                const area = await this.computeFacade.computeArea(shapeId)
                console.warn(`[CoreCoordinator][computeRequest] 面积计算成功: ${area}`)

                // 更新形状的面积属性
                const shapeProps = shapeToCompute.properties || {}
                const updatedProps = {
                  ...shapeProps,
                  computedArea: area,
                  computedAreaUnit: 'mm²',
                }

                // 更新形状
                this.repository.update(shapeId, { properties: updatedProps })
                console.warn(`[CoreCoordinator][computeRequest] 已更新形状 ${shapeId} 的面积属性`)

                // 触发UI层数据刷新
                const storeShapes = useShapesStore.getState().shapes
                const updatedShapes = storeShapes.map((s) => {
                  if (s.id === shapeId) {
                    return {
                      ...s,
                      properties: {
                        ...s.properties,
                        computedArea: area,
                        computedAreaUnit: 'mm²',
                      },
                    }
                  }
                  return s
                })
                useShapesStore.getState().setShapesFromExternal(updatedShapes, currentSelectedIds)
              }
              catch (err) {
                console.warn('[CoreCoordinator][computeRequest] 计算面积失败', err)
              }
            }
            // 处理单独的周长计算请求
            else if (operation === 'perimeter' && this.computeFacade) {
              try {
                console.warn(`[CoreCoordinator][computeRequest] 开始计算形状 ${shapeId} 的周长...`)

                // 获取最新的shape对象
                const shapeToCompute = this.repository.getById(shapeId)
                if (!shapeToCompute) {
                  console.warn(`[CoreCoordinator][computeRequest] 无法找到形状 ${shapeId}`)
                  return
                }

                // 计算周长
                const perimeter = await this.computeFacade.computePerimeter(shapeId)
                console.warn(`[CoreCoordinator][computeRequest] 周长计算成功: ${perimeter}`)

                // 更新形状的周长属性
                const shapeProps = shapeToCompute.properties || {}
                const updatedProps = {
                  ...shapeProps,
                  computedPerimeter: perimeter,
                  computedPerimeterUnit: 'mm',
                }

                // 更新形状
                this.repository.update(shapeId, { properties: updatedProps })
                console.warn(`[CoreCoordinator][computeRequest] 已更新形状 ${shapeId} 的周长属性`)

                // 触发UI层数据刷新
                const storeShapes = useShapesStore.getState().shapes
                const updatedShapes = storeShapes.map((s) => {
                  if (s.id === shapeId) {
                    return {
                      ...s,
                      properties: {
                        ...s.properties,
                        computedPerimeter: perimeter,
                        computedPerimeterUnit: 'mm',
                      },
                    }
                  }
                  return s
                })
                useShapesStore.getState().setShapesFromExternal(updatedShapes, currentSelectedIds)
              }
              catch (err) {
                console.warn('[CoreCoordinator][computeRequest] 计算周长失败', err)
              }
            }
            // 处理成本计算请求
            else if (operation === 'cost' && this.computeFacade) {
              try {
                console.warn(`[CoreCoordinator][computeRequest] 开始计算形状 ${shapeId} 的成本...`)

                // 获取最新的shape对象
                const shapeToCompute = this.repository.getById(shapeId)
                if (!shapeToCompute) {
                  console.warn(`[CoreCoordinator][computeRequest] 无法找到形状 ${shapeId}`)
                  return
                }

                // 检查是否有costParams参数
                const costParams = 'costParams' in payloadWithShapeId ? payloadWithShapeId.costParams : undefined

                // 获取成本计算参数
                let unitCost = 1
                let costType: 'area' | 'perimeter' | 'unit' | 'segment' | 'fixed' = 'unit'
                let quantity = 0

                if (costParams !== null && costParams !== undefined && typeof costParams === 'object') {
                  const costParamsObj = costParams as Record<string, unknown>
                  // 使用传入的参数
                  unitCost = typeof costParamsObj.costUnitPrice === 'number' ? costParamsObj.costUnitPrice : 1

                  // 将costBasis映射到costType
                  if (typeof costParamsObj.costBasis === 'string') {
                    switch (costParamsObj.costBasis) {
                      case 'area':
                      case 'perimeter':
                      case 'unit':
                        costType = costParamsObj.costBasis
                        break
                      default:
                        costType = 'unit'
                    }
                  }

                  quantity = typeof costParamsObj.costMultiplierOrCount === 'number' ? costParamsObj.costMultiplierOrCount : 0
                }
                else {
                  // 使用shape的属性
                  const shapeProps = shapeToCompute.properties || {}
                  unitCost = typeof shapeProps.costUnitPrice === 'number' ? shapeProps.costUnitPrice : 1

                  // 将costBasis映射到costType
                  if (typeof shapeProps.costBasis === 'string') {
                    switch (shapeProps.costBasis) {
                      case 'area':
                      case 'perimeter':
                      case 'unit':
                        costType = shapeProps.costBasis
                        break
                      default:
                        costType = 'unit'
                    }
                  }

                  quantity = typeof shapeProps.costMultiplierOrCount === 'number' ? shapeProps.costMultiplierOrCount : 0
                }

                // 组装计算选项
                const options = { costType, quantity }
                console.warn(`[CoreCoordinator][computeRequest] 计算选项: unitCost=${unitCost}, costType=${costType}, quantity=${quantity}`)

                // 计算成本
                let cost = 0
                try {
                  cost = await this.computeFacade.computeCost(shapeId, unitCost, options)
                  console.warn(`[CoreCoordinator][computeRequest] 成本计算成功: ${cost}`)
                }
                catch (costErr) {
                  console.error(`[CoreCoordinator][computeRequest] 成本计算失败:`, costErr)
                  // 不要直接返回，而是使用基本计算
                  // 基于costType计算基础值
                  let baseValue = 0 // 默认为0，避免意外的成本计算
                  if (costType === 'area') {
                    try {
                      baseValue = await this.computeFacade.computeArea(shapeId)
                      console.warn(`[CoreCoordinator][computeRequest] 面积计算成功: ${baseValue}`)
                    }
                    catch (areaErr) {
                      console.error(`[CoreCoordinator][computeRequest] 面积计算失败:`, areaErr)
                    }
                  }
                  else if (costType === 'perimeter') {
                    try {
                      baseValue = await this.computeFacade.computePerimeter(shapeId)
                      console.warn(`[CoreCoordinator][computeRequest] 周长计算成功: ${baseValue}`)
                    }
                    catch (perimeterErr) {
                      console.error(`[CoreCoordinator][computeRequest] 周长计算失败:`, perimeterErr)
                    }
                  }
                  // 计算总成本
                  cost = unitCost * baseValue * quantity
                  console.warn(`[CoreCoordinator][computeRequest] 手动计算成本: ${unitCost} * ${baseValue} * ${quantity} = ${cost}`)
                }

                // 成本可以为0（当乘数为0时）
                console.warn(`[CoreCoordinator][computeRequest] 最终计算成本: ${cost}`)

                // 更新形状的costTotal属性
                const shapeProps = shapeToCompute.properties || {}

                // 保存关键几何属性，确保不会被覆盖
                const { radius, width, height } = shapeProps

                const updatedProps = {
                  ...shapeProps,
                  costTotal: cost,
                  costUnitPrice: unitCost,
                  // 🔧 修复：不要覆盖用户设置的costBasis！保持原有的值
                  // costBasis: costType, // 这行是错误的！会覆盖用户设置
                  // 不要覆盖用户设置的乘数值！保持原有的 costMultiplierOrCount
                  // costMultiplierOrCount: quantity, // 这行是错误的！
                }

                // 如果是圆形元素，确保保留原始的半径和直径
                if (shapeToCompute.type === 'circle' && radius !== undefined) {
                  console.warn(`[CoreCoordinator][computeRequest] 保留圆形元素的原始半径: ${String(radius)}和直径: ${String(width)}x${String(height)}`)

                  // 使用类型断言来避免TypeScript错误
                  // 确保半径和直径值保持一致关系：直径 = 2 * 半径
                  const propsWithGeometry = updatedProps as Record<string, unknown>
                  propsWithGeometry.radius = radius
                  const diameter = (typeof radius === 'number' ? radius : 0) * 2
                  if (width !== undefined)
                    propsWithGeometry.width = diameter
                  if (height !== undefined)
                    propsWithGeometry.height = diameter

                  // 确保properties存在
                  if (propsWithGeometry.properties === null || propsWithGeometry.properties === undefined || typeof propsWithGeometry.properties !== 'object')
                    propsWithGeometry.properties = {}

                  // 更新properties中的几何属性
                  const propertiesObj = propsWithGeometry.properties as Record<string, unknown>
                  propertiesObj.radius = radius
                  propertiesObj.width = diameter
                  propertiesObj.height = diameter
                }

                // 更新形状
                this.repository.update(shapeId, { properties: updatedProps })
                console.warn(`[CoreCoordinator][computeRequest] 已更新形状 ${shapeId} 的属性`)

                // 触发UI层数据刷新
                const storeShapes = useShapesStore.getState().shapes
                const updatedShapes = storeShapes.map((s) => {
                  if (s.id === shapeId) {
                    // 创建更新后的属性对象
                    const updatedProperties = {
                      ...s.properties,
                      costTotal: cost,
                      costUnitPrice: unitCost,
                      // 🔧 修复：不要覆盖用户设置的costBasis！保持原有的值
                      // costBasis: costType, // 这行是错误的！会覆盖用户设置
                      // 不要覆盖用户设置的乘数值！保持原有的 costMultiplierOrCount
                      // costMultiplierOrCount: quantity, // 这行是错误的！
                    }

                    // 如果是圆形元素，确保保留原始的半径和直径
                    if (s.type === 'circle' && radius !== undefined) {
                      console.warn(`[CoreCoordinator][UI更新] 保留圆形元素的原始半径: ${String(radius)}和直径: ${String(width)}x${String(height)}`)
                      const propertiesWithGeometry = updatedProperties as Record<string, unknown>
                      propertiesWithGeometry.radius = radius
                      if (width !== undefined)
                        propertiesWithGeometry.width = width
                      if (height !== undefined)
                        propertiesWithGeometry.height = height
                    }

                    return {
                      ...s,
                      properties: updatedProperties,
                    }
                  }
                  return s
                })
                useShapesStore.getState().setShapesFromExternal(updatedShapes, currentSelectedIds)
              }
              catch (err) {
                console.warn('[CoreCoordinator][computeRequest] 计算成本失败', err)
              }
            }
          }
        })()
      },
    )

    // 处理形状置底请求
    this.eventBus.subscribe(
      AppEventType.ShapeSendToBackRequest,
      (event: BaseEvent) => {
        if (event.type === AppEventType.ShapeSendToBackRequest) {
          console.warn('[CoreCoordinator] ShapeSendToBackRequest event received:', event)

          // 提取形状ID
          const shapeId = event.payload !== null && event.payload !== undefined && typeof event.payload === 'object' && 'shapeId' in event.payload
            ? (event.payload as { shapeId: string }).shapeId
            : undefined
          if (shapeId === null || shapeId === undefined || shapeId === '') {
            console.error('[CoreCoordinator] ShapeSendToBackRequest: Missing shapeId in payload')
            return
          }

          // 保存当前选择状态
          const currentSelectedIds = Array.from(this.repository.getSelectedIds())
          console.warn('[CoreCoordinator] Current selection before sendToBack:', currentSelectedIds)

          // 执行置底操作
          try {
            // 直接调用repository的方法，不再使用类型断言
            const updatedShape = this.repository.sendToBackInLayer(shapeId)
            if (updatedShape) {
              console.warn(`[CoreCoordinator] Successfully sent shape ${shapeId} to back in layer, new intraLayerZIndex: ${updatedShape.intraLayerZIndex}`)

              // 确保选择状态保持不变
              this.repository.setSelectedIds(currentSelectedIds)

              // 发布完成事件
              this.eventBus.publish({
                type: AppEventType.ShapeSendToBackComplete,
                timestamp: Date.now(),
                payload: {
                  shape: updatedShape,
                  selectedIds: currentSelectedIds,
                  source: 'CoreCoordinator',
                },
              })

              // 更新Zustand store
              this.publishShapesUpdate()
            }
            else {
              console.error(`[CoreCoordinator] Failed to send shape ${shapeId} to back in layer`)
            }
          }
          catch (error) {
            console.error(`[CoreCoordinator] Error sending shape ${shapeId} to back:`, error)
          }
        }
      },
    )

    this.logger.info('CoreCoordinator event handlers registered.')
  }

  // --- Repository Accessor Methods (Provide controlled access to repository state) ---

  /**
   * Retrieves a single shape model by its ID from the repository.
   *
   * @param id - The ID of the shape to retrieve.
   * @returns The shape model if found, otherwise `undefined`.
   */
  public getShapeById(id: string): ShapeModel | undefined {
    return this.repository.getById(id)
  }

  /**
   * Retrieves all shape models currently stored in the repository.
   *
   * @returns An array containing all shape models.
   */
  public getAllShapes(): ShapeModel[] {
    return this.repository.getAll()
  }

  /**
   * Retrieves the IDs of all currently selected shapes from the repository.
   *
   * @returns A `Set` containing the IDs of the selected shapes.
   */
  public getSelectedShapeIds(): Set<string> {
    return this.repository.getSelectedIds()
  }

  /**
   * Uses the validator and core resources to check shape model validity.
   *
   * @remarks
   * Throws an appropriate CoreError if validation fails.
   *
   * @param model - The shape model to validate.
   * @returns Promise that resolves to true if the model is valid.
   * @throws CoreError if validation fails or an unexpected error occurs.
   */
  public async validateShape(model: ShapeModel): Promise<boolean> {
    if (model?.id === null || model?.id === undefined || model?.id === '') {
      this.logger.warn('Invalid shape model provided for validation')
      throw new CoreError(
        ErrorType.InvalidParameter,
        'Invalid shape model: missing ID',
        undefined, // severity, defaults in CoreError constructor
        { metadata: { model } }, // context
      )
    }

    // Use the safe validation helper that handles all error cases
    return safeValidate(
      model as unknown as ValidatorShape,
      async (shape) => {
        const interfaceResult = await this.validator.validateElement(shape)
        // Convert ValidationResult from validator-interface.ts to ValidationResult from validationUtils.ts
        const utilErrors: ValidationIssue[] = (interfaceResult.errors || []).map(err => ({
          message: err.message,
          code: err.code.toString(), // Convert ValidationErrorCode enum to string
          path: err.path,
          value: err.value, // Map 'value' to the index signature if needed, or handle appropriately
        }))
        return {
          valid: interfaceResult.valid,
          errors: utilErrors,
          // data property is optional in validationUtils.ValidationResult, can be omitted or set if available
        }
      },
      this.errorService,
      'CoreCoordinator',
      'validateShape',
      model.id,
    )
  }

  /**
   * 检查变更是否需要触发计算
   * @param _changes 变更对象
   * @returns 是否需要触发计算
   */
  private _doesChangeRequireComputation(_changes: Record<string, unknown>): boolean {
    // 始终返回true，确保任何属性变更都触发计算
    return true

    /* 原始实现，现在不再使用
    // 检查顶层属性
    const computationTriggeringProperties = ['width', 'height', 'cornerRadius', 'points', 'controlPoints'];
    for (const prop of computationTriggeringProperties) {
      if (prop in changes) {
        return true;
      }
    }

    // 检查properties对象中的属性
    if ('properties' in changes && typeof changes.properties === 'object' && changes.properties !== null) {
      const props = changes.properties as Record<string, unknown>;
      for (const prop of computationTriggeringProperties) {
        if (prop in props) {
          return true;
        }
      }

      // 检查成本相关属性
      const costProperties = ['costUnitPrice', 'costMultiplierOrCount', 'costBasis'];
      for (const prop of costProperties) {
        if (prop in props) {
          return true;
        }
      }
    }

    return false;
    */
  }

  /**
   * 获取服务状态信息，用于调试和监控
   * @returns 服务状态信息
   */
  public getServiceStatus(): {
    editService: boolean
    selectionService: boolean
    computationEnabled: boolean
  } {
    return {
      editService: this.elementEditService !== null && this.elementEditService !== undefined,
      selectionService: this.elementSelectionService !== null && this.elementSelectionService !== undefined,
      computationEnabled: this._doesChangeRequireComputation({}),
    }
  }

  /**
   * Publishes shapes update event with current repository data.
   *
   * @remarks
   * Sets isInternalUpdate flag to prevent feedback loops when store subscribers react to the event.
   *
   * @private
   */
  private publishShapesUpdate(): void {
    try {
      // Set flag to prevent feedback loops
      this.isInternalUpdate = true

      // Get all shapes from repository
      const shapes = this.repository.getAll()
      const selectedIds = Array.from(this.repository.getSelectedIds())

      // Publish update event with current shape data
      this.eventBus.publish({
        type: AppEventType.DataUpdated,
        payload: { type: 'shapes', data: shapes },
      } as { type: AppEventType, payload: { type: string, data: ShapeModel[] } })

      // === 新增：强制刷新 UI store ===
      useShapesStore.getState().setShapesFromExternal(shapes, selectedIds)

      this.logger.debug?.('Published shapes update event')
    }
    catch (error) {
      this.handleError(error as Error, { context: 'publishShapesUpdate' })
    }
    finally {
      // Always reset flag to allow future external updates
      this.isInternalUpdate = false
    }
  }

  /**
   * 统一处理ShapeSelectRequest和ElementSelectRequest
   * @private
   */
  private _handleShapeSelectRequest(event: BaseEvent): void {
    console.warn('[CoreCoordinator] received SHAPE/ELEMENT_SELECT_REQUEST', event)
    const specificEvent = event as { payload?: unknown } // 兼容两种payload
    const selectPayload = specificEvent.payload
    this.logger.debug?.('CoreCoordinator received SHAPE/ELEMENT_SELECT_REQUEST', selectPayload)

    if (selectPayload !== null && selectPayload !== undefined) {
      try {
        let newSelectedIds: string[] = []
        const selectionMode = (selectPayload as { selectionMode?: string }).selectionMode
        const mode = (selectionMode !== null && selectionMode !== undefined ? selectionMode : '').toLowerCase()
        // 兼容 elementIds/shapeIds 字段
        const ids = (selectPayload as { elementIds?: string[] }).elementIds
          || (selectPayload as { shapeIds?: string[] }).shapeIds
          || []
        if (mode === 'replace') {
          this.repository.setSelectedIds(ids)
          newSelectedIds = Array.from(this.repository.getSelectedIds())
        }
        else if (mode === 'add') {
          this.repository.addToSelection(ids)
          newSelectedIds = Array.from(this.repository.getSelectedIds())
        }
        else if (mode === 'clear') {
          this.repository.clearSelection()
          newSelectedIds = []
        }
        else {
          // fallback: if ids present, replace selection
          this.repository.setSelectedIds(ids)
          newSelectedIds = Array.from(this.repository.getSelectedIds())
        }
        // Publish SelectionChanged event
        console.warn('[CoreCoordinator] publishing SelectionChanged', newSelectedIds)
        this.eventBus.publish({
          type: AppEventType.SelectionChanged,
          payload: { selectedIds: newSelectedIds },
        })
        console.warn('[CoreCoordinator] published SelectionChanged', newSelectedIds)
      }
      catch (err: unknown) {
        const errorToHandle = err instanceof Error ? err : new Error(String(err))
        this.handleError(errorToHandle)
      }
    }
    else {
      this.logger.warn?.('SHAPE/ELEMENT_SELECT_REQUEST received with invalid payload', event)
    }
  }
}
