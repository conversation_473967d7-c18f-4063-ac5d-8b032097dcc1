import { Download, FileDown, FileImage, FileText } from 'lucide-react'
import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'

interface ExportMenuProps {
  isOpen: boolean
  onClose: () => void
  position: { x: number, y: number }
  projectName: string
  exportAsSVG: (options: { fileName: string }) => void
  exportAsPNG: (options: { fileName: string }) => void
  exportAsPDF: (options: { fileName: string }) => void
}

const ExportMenu: React.FC<ExportMenuProps> = ({
  isOpen,
  onClose,
  position,
  projectName,
  exportAsSVG,
  exportAsPNG,
  exportAsPDF,
}) => {
  if (!isOpen)
    return null

  return (
    <>
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-transparent z-[100]"
        onClick={onClose}
      />

      {/* Menu */}
      <Card
        className="absolute z-[101] min-w-[240px] shadow-lg border animate-in fade-in-0 zoom-in-95 duration-200"
        style={{
          top: position.y,
          left: position.x,
        }}
      >
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <FileDown className="w-4 h-4" />
            Export Project
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0 pb-2">
          <div className="flex flex-col gap-1">
            <Button
              variant="ghost"
              size="sm"
              className="justify-between h-10 px-3 hover:bg-muted/80"
              onClick={() => {
                exportAsSVG({ fileName: projectName })
                onClose()
              }}
            >
              <div className="flex items-center gap-3">
                <Download className="w-4 h-4 text-muted-foreground" />
                <div className="flex flex-col items-start">
                  <span className="text-sm font-medium">SVG Vector</span>
                  <span className="text-xs text-muted-foreground">Scalable graphics</span>
                </div>
              </div>
              <Badge variant="secondary" className="text-xs">
                .svg
              </Badge>
            </Button>

            <Separator className="my-1" />

            <Button
              variant="ghost"
              size="sm"
              className="justify-between h-10 px-3 hover:bg-muted/80"
              onClick={() => {
                exportAsPNG({ fileName: projectName })
                onClose()
              }}
            >
              <div className="flex items-center gap-3">
                <FileImage className="w-4 h-4 text-muted-foreground" />
                <div className="flex flex-col items-start">
                  <span className="text-sm font-medium">PNG Image</span>
                  <span className="text-xs text-muted-foreground">High quality raster</span>
                </div>
              </div>
              <Badge variant="secondary" className="text-xs">
                .png
              </Badge>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="justify-between h-10 px-3 hover:bg-muted/80"
              onClick={() => {
                exportAsPDF({ fileName: projectName })
                onClose()
              }}
            >
              <div className="flex items-center gap-3">
                <FileText className="w-4 h-4 text-muted-foreground" />
                <div className="flex flex-col items-start">
                  <span className="text-sm font-medium">PDF Document</span>
                  <span className="text-xs text-muted-foreground">Print-ready format</span>
                </div>
              </div>
              <Badge variant="secondary" className="text-xs">
                .pdf
              </Badge>
            </Button>
          </div>
        </CardContent>
      </Card>
    </>
  )
}

export default ExportMenu
