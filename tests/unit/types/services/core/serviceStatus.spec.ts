import { describe, expect, it } from 'vitest'
import { ServiceStatus } from '@/types/services/core/serviceStatus'

describe('service Status Types', () => {
  describe('serviceStatus', () => {
    it('should define all expected status values', () => {
      expect(ServiceStatus.IDLE).toBeDefined()
      expect(ServiceStatus.INITIALIZING).toBeDefined()
      expect(ServiceStatus.READY).toBeDefined()
      expect(ServiceStatus.BUSY).toBeDefined()
      expect(ServiceStatus.ERROR).toBeDefined()
    })

    it('should have the correct string values', () => {
      expect(ServiceStatus.IDLE).toBe('idle')
      expect(ServiceStatus.INITIALIZING).toBe('initializing')
      expect(ServiceStatus.READY).toBe('ready')
      expect(ServiceStatus.BUSY).toBe('busy')
      expect(ServiceStatus.ERROR).toBe('error')
    })

    it('should be usable as a type', () => {
      // Create a function that accepts ServiceStatus
      const getStatusDescription = (status: ServiceStatus): string => {
        switch (status) {
          case ServiceStatus.IDLE:
            return 'Service is idle'
          case ServiceStatus.INITIALIZING:
            return 'Service is initializing'
          case ServiceStatus.READY:
            return 'Service is ready'
          case ServiceStatus.BUSY:
            return 'Service is busy'
          case ServiceStatus.ERROR:
            return 'Service has an error'
          default:
            return 'Unknown status'
        }
      }

      // Test the function with different status values
      expect(getStatusDescription(ServiceStatus.IDLE)).toBe('Service is idle')
      expect(getStatusDescription(ServiceStatus.INITIALIZING)).toBe('Service is initializing')
      expect(getStatusDescription(ServiceStatus.READY)).toBe('Service is ready')
      expect(getStatusDescription(ServiceStatus.BUSY)).toBe('Service is busy')
      expect(getStatusDescription(ServiceStatus.ERROR)).toBe('Service has an error')
    })

    it('should have unique values', () => {
      const statusValues = Object.values(ServiceStatus)
      const uniqueValues = new Set(statusValues)

      // If there are duplicates, the Set size will be smaller than the array length
      expect(uniqueValues.size).toBe(statusValues.length)
    })
  })
})
