
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/lib/utils/geometry/BoundingBoxClass.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">src/lib/utils/geometry</a> BoundingBoxClass.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/146</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/146</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * Class for Representing and Manipulating Axis-Aligned Bounding Boxes.
 *
 * @remarks
 * This module provides the {@link BoundingBoxClass}, a concrete implementation for
 * creating and working with axis-aligned bounding boxes (AABBs). A bounding box is
 * defined by its top-left corner's position (`x`, `y`) and its `width` and `height`.
 *
 * This class implements the {@link BoundingBoxProperties} interface (which itself
 * extends {@link ShapeElement}), meaning instances of `BoundingBoxClass` also carry
 * common element properties like `id`, `type` (defaulted to `RECTANGLE`), `position`, etc.
 * This allows bounding boxes to be treated similarly to other shape elements if needed,
 * though they are primarily used for geometric calculations and spatial queries.
 *
 * Key features include:
 * - Constructors for creating bounding boxes from coordinates or other inputs.
 * - Static factory methods: `fromObject`, `fromPointsArray`, `fromTwoPoints`, `fromCenter`.
 * - Instance methods: `containsPoint`, `intersects` (with another BoundingBox), `union`.
 * - A `points` getter that returns the 9 characteristic points of the bounding box (corners, midpoints, center).
 * - A `toJson` method for serialization.
 *
 * @module lib/utils/geometry/BoundingBoxClass
 * @see {@link BoundingBoxProperties} - The interface this class implements.
 * @see {@link PointClass} - Used for representing points.
 * @see {@link PointData} - Interface for point data.
 */
&nbsp;
import type { BoundingBox as BoundingBoxProperties } from '@/types/core/element/geometry/bounding-box'
import type { PointData } from '@/types/core/element/geometry/point'
import type { ShapeElement } from '@/types/core/elementDefinitions'
<span class="cstat-no" title="statement not covered" >import { ElementType } from '../../../types/core/elementDefinitions'</span>
<span class="cstat-no" title="statement not covered" >import { MajorCategory } from '../../../types/core/majorMinorTypes'</span>
<span class="cstat-no" title="statement not covered" >import { PointClass } from './PointClass'</span>
&nbsp;
/**
 * Represents an axis-aligned bounding box and provides methods for its manipulation.
 *
 * @remarks
 * The `position` property of this class (inherited from `ShapeElement` via `BoundingBoxProperties`)
 * represents the top-left corner of the bounding box.
 * The `points` getter calculates 9 characteristic points: top-left, top-center, top-right,
 * middle-left, center, middle-right, bottom-left, bottom-center, and bottom-right.
 *
 * @implements BoundingBoxProperties
 */
<span class="cstat-no" title="statement not covered" >export class BoundingBoxClass implements BoundingBoxProperties {</span>
  // Properties from BoundingBoxProperties
<span class="cstat-no" title="statement not covered" >  public width: number</span>
<span class="cstat-no" title="statement not covered" >  public height: number</span>
&nbsp;
  // Properties from ShapeElement (extended by BoundingBoxProperties)
<span class="cstat-no" title="statement not covered" >  public id: string</span>
<span class="cstat-no" title="statement not covered" >  public type: ElementType = ElementType.RECTANGLE // Defaulting to RECTANGLE as BBox is rectangular</span>
<span class="cstat-no" title="statement not covered" >  public visible: boolean = true</span>
<span class="cstat-no" title="statement not covered" >  public locked: boolean = false</span>
<span class="cstat-no" title="statement not covered" >  public metadata?: ShapeElement['metadata']</span>
<span class="cstat-no" title="statement not covered" >  public position: PointClass // Top-left corner, now PointClass</span>
<span class="cstat-no" title="statement not covered" >  public rotation: number = 0 // Bounding boxes are typically axis-aligned, so rotation is 0</span>
<span class="cstat-no" title="statement not covered" >  public selectable: boolean = false // Usually not selectable themselves</span>
<span class="cstat-no" title="statement not covered" >  public draggable: boolean = false // Usually not draggable themselves</span>
<span class="cstat-no" title="statement not covered" >  public showHandles: boolean = false // Usually don't show handles themselves</span>
<span class="cstat-no" title="statement not covered" >  public zIndex?: number</span>
<span class="cstat-no" title="statement not covered" >  public layer?: string</span>
<span class="cstat-no" title="statement not covered" >  public properties?: Record&lt;string, any&gt;</span>
<span class="cstat-no" title="statement not covered" >  public fill?: string = 'none' // Default to no fill</span>
<span class="cstat-no" title="statement not covered" >  public stroke?: string = '#00FFFF' // Default to cyan for visibility if rendered</span>
<span class="cstat-no" title="statement not covered" >  public strokeWidth?: number = 1</span>
<span class="cstat-no" title="statement not covered" >  public opacity?: number = 0.5</span>
<span class="cstat-no" title="statement not covered" >  public strokeDasharray?: string</span>
<span class="cstat-no" title="statement not covered" >  public majorCategory: MajorCategory = MajorCategory.FURNITURE // Default category</span>
&nbsp;
  // BoundingBoxProperties specific readonly points
  // This will be a getter to calculate points on demand or based on constructor.
  // The interface defines it as a tuple of 9 points.
<span class="cstat-no" title="statement not covered" >  public get points(): [PointClass, PointClass, PointClass, PointClass, PointClass, PointClass, PointClass, PointClass, PointClass] {</span>
<span class="cstat-no" title="statement not covered" >    return this._calculateControlPoints()</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a new `BoundingBoxClass` instance.
   *
   * @remarks
   * If `width` or `height` are negative, they are made positive, and the `x` or `y`
   * coordinate of the `position` (top-left corner) is adjusted accordingly to maintain
   * the intended spatial coverage.
   *
   * @param x - The x-coordinate of the top-left corner.
   * @param y - The y-coordinate of the top-left corner.
   * @param width - The width of the bounding box.
   * @param height - The height of the bounding box.
   * @param id - Optional: A unique identifier for the bounding box. Defaults to a generated ID.
   */
<span class="cstat-no" title="statement not covered" >  constructor(x: number, y: number, width: number, height: number, id?: string) {</span>
<span class="cstat-no" title="statement not covered" >    let adjustedX = x</span>
<span class="cstat-no" title="statement not covered" >    let adjustedY = y</span>
<span class="cstat-no" title="statement not covered" >    let adjustedWidth = width</span>
<span class="cstat-no" title="statement not covered" >    let adjustedHeight = height</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (adjustedWidth &lt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      adjustedX = adjustedX + adjustedWidth // Adjust x if width is negative</span>
<span class="cstat-no" title="statement not covered" >      adjustedWidth = -adjustedWidth</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    if (adjustedHeight &lt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      adjustedY = adjustedY + adjustedHeight // Adjust y if height is negative</span>
<span class="cstat-no" title="statement not covered" >      adjustedHeight = -adjustedHeight</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.position = new PointClass(adjustedX, adjustedY)</span>
<span class="cstat-no" title="statement not covered" >    this.width = adjustedWidth</span>
<span class="cstat-no" title="statement not covered" >    this.height = adjustedHeight</span>
<span class="cstat-no" title="statement not covered" >    this.id = id || `bbox_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`</span>
    // Note: `this.points` is a getter.
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  private _calculateControlPoints(): [PointClass, PointClass, PointClass, PointClass, PointClass, PointClass, PointClass, PointClass, PointClass] {</span>
<span class="cstat-no" title="statement not covered" >    const x = this.position.x</span>
<span class="cstat-no" title="statement not covered" >    const y = this.position.y</span>
<span class="cstat-no" title="statement not covered" >    const w = this.width</span>
<span class="cstat-no" title="statement not covered" >    const h = this.height</span>
<span class="cstat-no" title="statement not covered" >    const midX = x + w / 2</span>
<span class="cstat-no" title="statement not covered" >    const midY = y + h / 2</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return [</span>
<span class="cstat-no" title="statement not covered" >      new PointClass(x, y), // topLeft</span>
<span class="cstat-no" title="statement not covered" >      new PointClass(midX, y), // topCenter</span>
<span class="cstat-no" title="statement not covered" >      new PointClass(x + w, y), // topRight</span>
<span class="cstat-no" title="statement not covered" >      new PointClass(x, midY), // middleLeft</span>
<span class="cstat-no" title="statement not covered" >      new PointClass(midX, midY), // center</span>
<span class="cstat-no" title="statement not covered" >      new PointClass(x + w, midY), // middleRight</span>
<span class="cstat-no" title="statement not covered" >      new PointClass(x, y + h), // bottomLeft</span>
<span class="cstat-no" title="statement not covered" >      new PointClass(midX, y + h), // bottomCenter</span>
<span class="cstat-no" title="statement not covered" >      new PointClass(x + w, y + h), // bottomRight</span>
<span class="cstat-no" title="statement not covered" >    ]</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a `BoundingBoxClass` instance from a simple object containing `x`, `y`, `width`, and `height`.
   *
   * @param obj - An object with `x`, `y` (top-left corner), `width`, and `height` properties.
   * @returns A new {@link BoundingBoxClass} instance.
   */
<span class="cstat-no" title="statement not covered" >  static fromObject(obj: { x: number, y: number, width: number, height: number }): BoundingBoxClass {</span>
<span class="cstat-no" title="statement not covered" >    return new BoundingBoxClass(obj.x, obj.y, obj.width, obj.height)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a `BoundingBoxClass` instance that minimally encompasses an array of {@link PointData} objects.
   *
   * @param points - An array of {@link PointData} objects.
   * @returns A new {@link BoundingBoxClass} instance. If the `points` array is empty or null,
   *          a bounding box with zero width and height at position (0,0) is returned.
   */
<span class="cstat-no" title="statement not covered" >  static fromPointsArray(points: PointData[]): BoundingBoxClass {</span>
<span class="cstat-no" title="statement not covered" >    if (!points || points.length === 0) {</span>
<span class="cstat-no" title="statement not covered" >      return new BoundingBoxClass(0, 0, 0, 0)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    let minX = points[0].x</span>
<span class="cstat-no" title="statement not covered" >    let minY = points[0].y</span>
<span class="cstat-no" title="statement not covered" >    let maxX = points[0].x</span>
<span class="cstat-no" title="statement not covered" >    let maxY = points[0].y</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    for (let i = 1; i &lt; points.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >      const p = points[i]</span>
<span class="cstat-no" title="statement not covered" >      minX = Math.min(minX, p.x)</span>
<span class="cstat-no" title="statement not covered" >      minY = Math.min(minY, p.y)</span>
<span class="cstat-no" title="statement not covered" >      maxX = Math.max(maxX, p.x)</span>
<span class="cstat-no" title="statement not covered" >      maxY = Math.max(maxY, p.y)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return new BoundingBoxClass(minX, minY, maxX - minX, maxY - minY)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a `BoundingBoxClass` instance from two diagonally opposite points.
   * The order of the points does not matter.
   *
   * @param p1 - The first {@link PointData} object.
   * @param p2 - The second {@link PointData} object.
   * @returns A new {@link BoundingBoxClass} instance.
   */
<span class="cstat-no" title="statement not covered" >  static fromTwoPoints(p1: PointData, p2: PointData): BoundingBoxClass {</span>
<span class="cstat-no" title="statement not covered" >    const minX = Math.min(p1.x, p2.x)</span>
<span class="cstat-no" title="statement not covered" >    const minY = Math.min(p1.y, p2.y)</span>
<span class="cstat-no" title="statement not covered" >    const maxX = Math.max(p1.x, p2.x)</span>
<span class="cstat-no" title="statement not covered" >    const maxY = Math.max(p1.y, p2.y)</span>
<span class="cstat-no" title="statement not covered" >    return new BoundingBoxClass(minX, minY, maxX - minX, maxY - minY)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Alias for `fromTwoPoints`, potentially for compatibility or clearer semantics in certain contexts.
   * @deprecated Prefer {@link BoundingBoxClass.fromTwoPoints} or {@link BoundingBoxClass.fromPointsArray} for clarity.
   */
<span class="cstat-no" title="statement not covered" >  static fromPoints = BoundingBoxClass.fromTwoPoints</span>
&nbsp;
  /**
   * Creates a `BoundingBoxClass` instance from a center point, width, and height.
   *
   * @param center - The center {@link PointData} of the desired bounding box.
   * @param width - The width of the bounding box.
   * @param height - The height of the bounding box.
   * @returns A new {@link BoundingBoxClass} instance.
   */
<span class="cstat-no" title="statement not covered" >  static fromCenter(center: PointData, width: number, height: number): BoundingBoxClass {</span>
<span class="cstat-no" title="statement not covered" >    const x = center.x - width / 2</span>
<span class="cstat-no" title="statement not covered" >    const y = center.y - height / 2</span>
<span class="cstat-no" title="statement not covered" >    return new BoundingBoxClass(x, y, width, height)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Checks if this bounding box contains a given point (inclusive of boundaries).
   *
   * @param point - The {@link PointData} to check.
   * @returns `true` if the point is inside or on the boundary of this bounding box, `false` otherwise.
   */
<span class="cstat-no" title="statement not covered" >  containsPoint(point: PointData): boolean {</span>
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >      point.x &gt;= this.position.x</span>
<span class="cstat-no" title="statement not covered" >      &amp;&amp; point.x &lt;= this.position.x + this.width</span>
<span class="cstat-no" title="statement not covered" >      &amp;&amp; point.y &gt;= this.position.y</span>
<span class="cstat-no" title="statement not covered" >      &amp;&amp; point.y &lt;= this.position.y + this.height</span>
    )
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Checks if this bounding box intersects with another `BoundingBoxClass` instance.
   *
   * @param other - The other {@link BoundingBoxClass} instance to check for intersection.
   * @returns `true` if the two bounding boxes intersect (i.e., overlap), `false` otherwise.
   */
<span class="cstat-no" title="statement not covered" >  intersects(other: BoundingBoxClass): boolean {</span>
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >      this.position.x &lt; other.position.x + other.width</span>
<span class="cstat-no" title="statement not covered" >      &amp;&amp; this.position.x + this.width &gt; other.position.x</span>
<span class="cstat-no" title="statement not covered" >      &amp;&amp; this.position.y &lt; other.position.y + other.height</span>
<span class="cstat-no" title="statement not covered" >      &amp;&amp; this.position.y + this.height &gt; other.position.y</span>
    )
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a new `BoundingBoxClass` that is the union of this bounding box and another one.
   * The resulting bounding box will encompass both original bounding boxes.
   *
   * @param other - The other {@link BoundingBoxClass} instance to union with.
   * @returns A new {@link BoundingBoxClass} instance representing the union.
   */
<span class="cstat-no" title="statement not covered" >  union(other: BoundingBoxClass): BoundingBoxClass {</span>
<span class="cstat-no" title="statement not covered" >    const x1 = Math.min(this.position.x, other.position.x)</span>
<span class="cstat-no" title="statement not covered" >    const y1 = Math.min(this.position.y, other.position.y)</span>
<span class="cstat-no" title="statement not covered" >    const x2 = Math.max(this.position.x + this.width, other.position.x + other.width)</span>
<span class="cstat-no" title="statement not covered" >    const y2 = Math.max(this.position.y + this.height, other.position.y + other.height)</span>
<span class="cstat-no" title="statement not covered" >    return new BoundingBoxClass(x1, y1, x2 - x1, y2 - y1)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Returns a plain JavaScript object representation of the `BoundingBoxClass` instance,
   * conforming to a partial {@link BoundingBoxProperties} structure.
   *
   * @remarks
   * This is useful for serialization or when a plain object is needed instead of a class instance.
   * The `points` getter is typically not included in this minimal JSON representation.
   *
   * @returns A plain object representing the bounding box's properties.
   */
<span class="cstat-no" title="statement not covered" >  toJson(): Partial&lt;BoundingBoxProperties&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const json: Partial&lt;BoundingBoxProperties&gt; = {</span>
<span class="cstat-no" title="statement not covered" >      id: this.id,</span>
<span class="cstat-no" title="statement not covered" >      type: this.type,</span>
<span class="cstat-no" title="statement not covered" >      position: this.position.toJson(), // Serialize PointClass to PointData compatible object</span>
<span class="cstat-no" title="statement not covered" >      width: this.width,</span>
<span class="cstat-no" title="statement not covered" >      height: this.height,</span>
<span class="cstat-no" title="statement not covered" >      visible: this.visible,</span>
<span class="cstat-no" title="statement not covered" >      locked: this.locked,</span>
<span class="cstat-no" title="statement not covered" >      rotation: this.rotation,</span>
<span class="cstat-no" title="statement not covered" >      selectable: this.selectable,</span>
<span class="cstat-no" title="statement not covered" >      draggable: this.draggable,</span>
<span class="cstat-no" title="statement not covered" >      showHandles: this.showHandles,</span>
<span class="cstat-no" title="statement not covered" >      majorCategory: this.majorCategory, // Add majorCategory</span>
      // Optional properties from ShapeElement
<span class="cstat-no" title="statement not covered" >      ...(this.metadata &amp;&amp; { metadata: this.metadata }),</span>
<span class="cstat-no" title="statement not covered" >      ...(this.zIndex !== undefined &amp;&amp; { zIndex: this.zIndex }),</span>
<span class="cstat-no" title="statement not covered" >      ...(this.layer &amp;&amp; { layer: this.layer }),</span>
<span class="cstat-no" title="statement not covered" >      ...(this.properties &amp;&amp; { properties: this.properties }),</span>
<span class="cstat-no" title="statement not covered" >      ...(this.fill &amp;&amp; { fill: this.fill }),</span>
<span class="cstat-no" title="statement not covered" >      ...(this.stroke &amp;&amp; { stroke: this.stroke }),</span>
<span class="cstat-no" title="statement not covered" >      ...(this.strokeWidth !== undefined &amp;&amp; { strokeWidth: this.strokeWidth }),</span>
<span class="cstat-no" title="statement not covered" >      ...(this.opacity !== undefined &amp;&amp; { opacity: this.opacity }),</span>
<span class="cstat-no" title="statement not covered" >      ...(this.strokeDasharray &amp;&amp; { strokeDasharray: this.strokeDasharray }),</span>
      // 'points' is a getter and readonly in BoundingBoxProperties, typically not part of minimal JSON.
      // If needed: points: this.points.map(p =&gt; p.toJson()) as [PointData, PointData, PointData, PointData, PointData, PointData, PointData, PointData, PointData],
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return json</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:28:25.559Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    