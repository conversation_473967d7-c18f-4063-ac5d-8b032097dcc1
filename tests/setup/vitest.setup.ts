import { vi } from 'vitest'

// 全局 mock 设置
vi.mock('@/services/logger/LoggerService', () => ({
  default: class MockLogger {
    info = vi.fn()
    error = vi.fn()
    debug = vi.fn()
    warn = vi.fn()
  },
}))

// 可以添加其他全局 mock 或设置

// 扩展模拟对象类型
declare global {
  namespace Vi {
    interface Mocked<T> {
      _lastHandle?: any
    }
  }

  // 扩展事件总线类型
  interface MockEventBus {
    publish: ReturnType<typeof vi.fn>
    subscribe: ReturnType<typeof vi.fn>
    unsubscribe: ReturnType<typeof vi.fn>
    _lastHandle?: any
    clear?: ReturnType<typeof vi.fn>
    reset?: ReturnType<typeof vi.fn>
    getSubscriptions?: ReturnType<typeof vi.fn>
  }

  // 扩展全局测试工具
  interface TestUtils {
    createMockMouseEvent: (x?: number, y?: number, button?: number) => Partial<React.MouseEvent>
    createSvgRef: () => { current: SVGSVGElement }
  }

  // 声明全局变量
  var testUtils: TestUtils
}

export {}
