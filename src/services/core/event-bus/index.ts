/**
 * Serves as the central hub for the application's event system.
 *
 * @remarks
 * This module exports the event bus instance, factory methods for dependency injection,
 * event handlers, helper functions, and initialization/cleanup utilities.
 *
 * The event system uses a publish-subscribe pattern to enable decoupled
 * communication between components and services across the application.
 *
 * @module services/core/event-bus
 */

// Export the AppEventBusImpl class for type definitions and testing
// ----- SINGLETON INSTANCE (LEGACY) -----
// This is kept for backward compatibility while transitioning to DI
// New code should use the factory functions below
import { AppEventBusImpl } from './appEventBus'

export { AppEventBusImpl } from './appEventBus'

// Export event handlers for registration and management
export * from './handlers'
export const appEventBus = AppEventBusImpl.getInstance()

// ----- DEPENDENCY INJECTION FACTORY -----
/**
 * Singleton event bus instance for dependency injection.
 * This instance is shared across the application.
 */
let globalEventBusInstance: AppEventBusImpl | null = null

/**
 * Creates or returns the global event bus instance.
 * This enables dependency injection while ensuring a singleton instance.
 *
 * @returns The global EventBus instance.
 */
export function getEventBus(): AppEventBusImpl {
  if (!globalEventBusInstance) {
    globalEventBusInstance = appEventBus
  }
  return globalEventBusInstance
}

/**
 * Creates a new event bus instance (primarily for testing).
 * This should rarely be used in application code.
 *
 * @returns A new EventBus instance.
 */
export function createEventBus(): AppEventBusImpl {
  return AppEventBusImpl.getInstance()
}

// Export helper functions for publishing and subscribing to events
export * from './helpers'

// Export system initialization and cleanup functions
export { cleanupEventSystem, initializeEventSystem } from './initialize'

// Export core event system types from the types directory
export type {
  BaseEvent,
  EventBus,
  EventBusConfig,
  EventHandler,
  ExtendedEventBus,
} from '@/types/services/events'

// Export event type enums for type-safe event handling
export { AppEventType } from '@/types/services/events'
