/**
 * @file ShapeRepository.methods.spec.ts
 * @description Additional unit tests for ShapeRepository class methods
 */

import type { LoggerService } from '@/core/services/logger/LoggerService'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ShapeRepository } from '@/core/state/ShapeRepository'
import { Point } from '@/types/core/element/geometry/point'
import { ElementType } from '@/types/core/shape-type'

describe('shapeRepository - Methods Tests', () => {
  let repository: ShapeRepository
  let mockLogger: LoggerService

  beforeEach(() => {
    // Create mock logger
    mockLogger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    } as unknown as LoggerService

    repository = new ShapeRepository(mockLogger)
  })

  describe('getByType', () => {
    it('should return shapes of the specified type', () => {
      // Arrange
      const rect1 = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
      }

      const rect2 = {
        id: 'rect-2',
        type: ElementType.RECTANGLE,
        position: new Point(300, 300),
        width: 150,
        height: 75,
      }

      const circle1 = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: new Point(200, 200),
        radius: 50,
      }

      repository.add(rect1)
      repository.add(rect2)
      repository.add(circle1)

      // Act
      const rectangles = repository.getByType(ElementType.RECTANGLE)
      const circles = repository.getByType(ElementType.CIRCLE)
      const ellipses = repository.getByType(ElementType.ELLIPSE)

      // Assert
      expect(rectangles.length).toBe(2)
      expect(rectangles).toContain(rect1)
      expect(rectangles).toContain(rect2)

      expect(circles.length).toBe(1)
      expect(circles).toContain(circle1)

      expect(ellipses.length).toBe(0)
    })

    it('should return an empty array if no shapes of the specified type exist', () => {
      // Act
      const polygons = repository.getByType(ElementType.POLYGON)

      // Assert
      expect(polygons).toEqual([])
    })

    it('should handle case-insensitive type matching', () => {
      // Arrange
      const rect = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
      }

      repository.add(rect)

      // Act
      const rectangles = repository.getByType('rectangle' as ElementType)

      // Assert
      expect(rectangles.length).toBe(1)
      expect(rectangles).toContain(rect)
    })
  })

  describe('getByProperty', () => {
    it('should return shapes with the specified property value', () => {
      // Arrange
      const shape1 = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
        properties: {
          color: 'red',
          visible: true,
        },
      }

      const shape2 = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: new Point(200, 200),
        radius: 50,
        properties: {
          color: 'blue',
          visible: true,
        },
      }

      const shape3 = {
        id: 'rect-2',
        type: ElementType.RECTANGLE,
        position: new Point(300, 300),
        width: 150,
        height: 75,
        properties: {
          color: 'red',
          visible: false,
        },
      }

      repository.add(shape1)
      repository.add(shape2)
      repository.add(shape3)

      // Act
      const redShapes = repository.getByProperty('color', 'red')
      const visibleShapes = repository.getByProperty('visible', true)
      const blueShapes = repository.getByProperty('color', 'blue')
      const greenShapes = repository.getByProperty('color', 'green')

      // Assert
      expect(redShapes.length).toBe(2)
      expect(redShapes).toContain(shape1)
      expect(redShapes).toContain(shape3)

      expect(visibleShapes.length).toBe(2)
      expect(visibleShapes).toContain(shape1)
      expect(visibleShapes).toContain(shape2)

      expect(blueShapes.length).toBe(1)
      expect(blueShapes).toContain(shape2)

      expect(greenShapes.length).toBe(0)
    })

    it('should return an empty array if no shapes have the specified property value', () => {
      // Arrange
      const shape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
        properties: {
          color: 'red',
          visible: true,
        },
      }

      repository.add(shape)

      // Act
      const greenShapes = repository.getByProperty('color', 'green')
      const hiddenShapes = repository.getByProperty('visible', false)

      // Assert
      expect(greenShapes).toEqual([])
      expect(hiddenShapes).toEqual([])
    })

    it('should handle shapes without properties', () => {
      // Arrange
      const shape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
        // No properties
      }

      repository.add(shape)

      // Act
      const redShapes = repository.getByProperty('color', 'red')

      // Assert
      expect(redShapes).toEqual([])
    })

    it('should handle deep property paths', () => {
      // Arrange
      const shape1 = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
        properties: {
          style: {
            fill: {
              color: 'red',
              opacity: 0.5,
            },
          },
        },
      }

      const shape2 = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: new Point(200, 200),
        radius: 50,
        properties: {
          style: {
            fill: {
              color: 'blue',
              opacity: 0.8,
            },
          },
        },
      }

      repository.add(shape1)
      repository.add(shape2)

      // Act
      const redShapes = repository.getByProperty('style.fill.color', 'red')
      const highOpacityShapes = repository.getByProperty('style.fill.opacity', 0.8)

      // Assert
      expect(redShapes.length).toBe(1)
      expect(redShapes).toContain(shape1)

      expect(highOpacityShapes.length).toBe(1)
      expect(highOpacityShapes).toContain(shape2)
    })
  })

  describe('getByMetadata', () => {
    it('should return shapes with the specified metadata value', () => {
      // Arrange
      const shape1 = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
        metadata: {
          author: 'User A',
          createdAt: 1000,
        },
      }

      const shape2 = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: new Point(200, 200),
        radius: 50,
        metadata: {
          author: 'User B',
          createdAt: 2000,
        },
      }

      const shape3 = {
        id: 'rect-2',
        type: ElementType.RECTANGLE,
        position: new Point(300, 300),
        width: 150,
        height: 75,
        metadata: {
          author: 'User A',
          createdAt: 3000,
        },
      }

      repository.add(shape1)
      repository.add(shape2)
      repository.add(shape3)

      // Act
      const userAShapes = repository.getByMetadata('author', 'User A')
      const userBShapes = repository.getByMetadata('author', 'User B')
      const userCShapes = repository.getByMetadata('author', 'User C')

      // Assert
      expect(userAShapes.length).toBe(2)
      expect(userAShapes).toContain(shape1)
      expect(userAShapes).toContain(shape3)

      expect(userBShapes.length).toBe(1)
      expect(userBShapes).toContain(shape2)

      expect(userCShapes.length).toBe(0)
    })

    it('should return an empty array if no shapes have the specified metadata value', () => {
      // Arrange
      const shape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
        metadata: {
          author: 'User A',
          createdAt: 1000,
        },
      }

      repository.add(shape)

      // Act
      const userBShapes = repository.getByMetadata('author', 'User B')
      const taggedShapes = repository.getByMetadata('tags', ['important'])

      // Assert
      expect(userBShapes).toEqual([])
      expect(taggedShapes).toEqual([])
    })

    it('should handle shapes without metadata', () => {
      // Arrange
      const shape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
        // No metadata
      }

      repository.add(shape)

      // Act
      const userAShapes = repository.getByMetadata('author', 'User A')

      // Assert
      expect(userAShapes).toEqual([])
    })

    it('should handle deep metadata paths', () => {
      // Arrange
      const shape1 = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
        metadata: {
          history: {
            created: {
              by: 'User A',
              at: 1000,
            },
          },
        },
      }

      const shape2 = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: new Point(200, 200),
        radius: 50,
        metadata: {
          history: {
            created: {
              by: 'User B',
              at: 2000,
            },
          },
        },
      }

      repository.add(shape1)
      repository.add(shape2)

      // Act
      const userAShapes = repository.getByMetadata('history.created.by', 'User A')
      const recentShapes = repository.getByMetadata('history.created.at', 2000)

      // Assert
      expect(userAShapes.length).toBe(1)
      expect(userAShapes).toContain(shape1)

      expect(recentShapes.length).toBe(1)
      expect(recentShapes).toContain(shape2)
    })
  })

  describe('filter', () => {
    it('should filter shapes based on the provided predicate', () => {
      // Arrange
      const rect1 = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
      }

      const rect2 = {
        id: 'rect-2',
        type: ElementType.RECTANGLE,
        position: new Point(300, 300),
        width: 150,
        height: 75,
      }

      const circle1 = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: new Point(200, 200),
        radius: 50,
      }

      repository.add(rect1)
      repository.add(rect2)
      repository.add(circle1)

      // Act
      const rectangles = repository.filter(shape => shape.type === ElementType.RECTANGLE)
      const largeShapes = repository.filter((shape) => {
        if (shape.type === ElementType.RECTANGLE) {
          return shape.width >= 200
        }
        else if (shape.type === ElementType.CIRCLE) {
          return shape.radius >= 50
        }
        return false
      })
      const smallShapes = repository.filter((shape) => {
        if (shape.type === ElementType.RECTANGLE) {
          return shape.width < 200
        }
        else if (shape.type === ElementType.CIRCLE) {
          return shape.radius < 50
        }
        return false
      })

      // Assert
      expect(rectangles.length).toBe(2)
      expect(rectangles).toContain(rect1)
      expect(rectangles).toContain(rect2)

      expect(largeShapes.length).toBe(2)
      expect(largeShapes).toContain(rect1)
      expect(largeShapes).toContain(circle1)

      expect(smallShapes.length).toBe(1)
      expect(smallShapes).toContain(rect2)
    })

    it('should return an empty array if no shapes match the predicate', () => {
      // Arrange
      const rect = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
      }

      const circle = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: new Point(200, 200),
        radius: 50,
      }

      repository.add(rect)
      repository.add(circle)

      // Act
      const polygons = repository.filter(shape => shape.type === ElementType.POLYGON)
      const tinyShapes = repository.filter((shape) => {
        if (shape.type === ElementType.RECTANGLE) {
          return shape.width < 50
        }
        else if (shape.type === ElementType.CIRCLE) {
          return shape.radius < 10
        }
        return false
      })

      // Assert
      expect(polygons).toEqual([])
      expect(tinyShapes).toEqual([])
    })

    it('should handle an empty repository', () => {
      // Act
      const shapes = repository.filter(shape => true)

      // Assert
      expect(shapes).toEqual([])
    })
  })

  describe('find', () => {
    it('should find the first shape that matches the predicate', () => {
      // Arrange
      const rect1 = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
      }

      const rect2 = {
        id: 'rect-2',
        type: ElementType.RECTANGLE,
        position: new Point(300, 300),
        width: 150,
        height: 75,
      }

      const circle1 = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: new Point(200, 200),
        radius: 50,
      }

      repository.add(rect1)
      repository.add(rect2)
      repository.add(circle1)

      // Act
      const rectangle = repository.find(shape => shape.type === ElementType.RECTANGLE)
      const smallShape = repository.find((shape) => {
        if (shape.type === ElementType.RECTANGLE) {
          return shape.width < 200
        }
        else if (shape.type === ElementType.CIRCLE) {
          return shape.radius < 50
        }
        return false
      })
      const circle = repository.find(shape => shape.type === ElementType.CIRCLE)

      // Assert
      expect(rectangle).toBe(rect1) // First rectangle
      expect(smallShape).toBe(rect2) // Only small shape
      expect(circle).toBe(circle1) // Only circle
    })

    it('should return undefined if no shape matches the predicate', () => {
      // Arrange
      const rect = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
      }

      const circle = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: new Point(200, 200),
        radius: 50,
      }

      repository.add(rect)
      repository.add(circle)

      // Act
      const polygon = repository.find(shape => shape.type === ElementType.POLYGON)
      const tinyShape = repository.find((shape) => {
        if (shape.type === ElementType.RECTANGLE) {
          return shape.width < 50
        }
        else if (shape.type === ElementType.CIRCLE) {
          return shape.radius < 10
        }
        return false
      })

      // Assert
      expect(polygon).toBeUndefined()
      expect(tinyShape).toBeUndefined()
    })

    it('should handle an empty repository', () => {
      // Act
      const shape = repository.find(shape => true)

      // Assert
      expect(shape).toBeUndefined()
    })
  })

  describe('exists', () => {
    it('should return true if a shape with the given ID exists', () => {
      // Arrange
      const shape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
      }

      repository.add(shape)

      // Act
      const result = repository.exists('rect-1')

      // Assert
      expect(result).toBe(true)
    })

    it('should return false if a shape with the given ID does not exist', () => {
      // Act
      const result = repository.exists('non-existent')

      // Assert
      expect(result).toBe(false)
    })
  })

  describe('count', () => {
    it('should return the number of shapes in the repository', () => {
      // Arrange - Empty repository

      // Act & Assert - Empty repository
      expect(repository.count()).toBe(0)

      // Arrange - Add one shape
      const shape1 = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: new Point(100, 100),
        width: 200,
        height: 100,
      }
      repository.add(shape1)

      // Act & Assert - One shape
      expect(repository.count()).toBe(1)

      // Arrange - Add another shape
      const shape2 = {
        id: 'circle-1',
        type: ElementType.CIRCLE,
        position: new Point(200, 200),
        radius: 50,
      }
      repository.add(shape2)

      // Act & Assert - Two shapes
      expect(repository.count()).toBe(2)

      // Arrange - Remove a shape
      repository.remove('rect-1')

      // Act & Assert - After removal
      expect(repository.count()).toBe(1)

      // Arrange - Clear all shapes
      repository.clearAll()

      // Act & Assert - After clear
      expect(repository.count()).toBe(0)
    })
  })
})
