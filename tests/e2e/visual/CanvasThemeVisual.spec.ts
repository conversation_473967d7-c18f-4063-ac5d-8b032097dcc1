import { test, expect } from '@playwright/test';

/**
 * Visual regression test suite for the canvas application with different themes.
 * Tests the visual appearance of the canvas with different themes and shapes.
 */
test.describe('Canvas Theme Visual Tests', () => {
  /**
   * Setup for each test.
   * Navigates to the application before each test.
   */
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the canvas to be loaded
    await page.waitForSelector('.canvas-container');
  });

  /**
   * Tests the visual appearance of the canvas with the light theme.
   * Verifies that the canvas looks correct with the light theme.
   */
  test('Canvas appearance with light theme', async ({ page }) => {
    // Ensure light theme is active
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
      document.documentElement.classList.add('light');
    });
    
    // Take a screenshot of the canvas
    await expect(page.locator('.canvas-container')).toHaveScreenshot('canvas-light-theme.png');
  });

  /**
   * Tests the visual appearance of the canvas with the dark theme.
   * Verifies that the canvas looks correct with the dark theme.
   */
  test('Canvas appearance with dark theme', async ({ page }) => {
    // Switch to dark theme
    await page.evaluate(() => {
      document.documentElement.classList.remove('light');
      document.documentElement.classList.add('dark');
    });
    
    // Take a screenshot of the canvas
    await expect(page.locator('.canvas-container')).toHaveScreenshot('canvas-dark-theme.png');
  });

  /**
   * Tests the visual appearance of the canvas with shapes in the light theme.
   * Verifies that shapes are rendered correctly with the light theme.
   */
  test('Shapes appearance with light theme', async ({ page }) => {
    // Ensure light theme is active
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
      document.documentElement.classList.add('light');
    });
    
    // Create shapes
    await createTestShapes(page);
    
    // Take a screenshot of the canvas with shapes
    await expect(page.locator('.canvas-container')).toHaveScreenshot('shapes-light-theme.png');
  });

  /**
   * Tests the visual appearance of the canvas with shapes in the dark theme.
   * Verifies that shapes are rendered correctly with the dark theme.
   */
  test('Shapes appearance with dark theme', async ({ page }) => {
    // Switch to dark theme
    await page.evaluate(() => {
      document.documentElement.classList.remove('light');
      document.documentElement.classList.add('dark');
    });
    
    // Create shapes
    await createTestShapes(page);
    
    // Take a screenshot of the canvas with shapes
    await expect(page.locator('.canvas-container')).toHaveScreenshot('shapes-dark-theme.png');
  });

  /**
   * Tests the visual appearance of the canvas with selected shapes in the light theme.
   * Verifies that selected shapes are highlighted correctly with the light theme.
   */
  test('Selected shapes appearance with light theme', async ({ page }) => {
    // Ensure light theme is active
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
      document.documentElement.classList.add('light');
    });
    
    // Create shapes
    await createTestShapes(page);
    
    // Select a shape
    await page.click('svg rect');
    
    // Take a screenshot of the canvas with selected shape
    await expect(page.locator('.canvas-container')).toHaveScreenshot('selected-shape-light-theme.png');
  });

  /**
   * Tests the visual appearance of the canvas with selected shapes in the dark theme.
   * Verifies that selected shapes are highlighted correctly with the dark theme.
   */
  test('Selected shapes appearance with dark theme', async ({ page }) => {
    // Switch to dark theme
    await page.evaluate(() => {
      document.documentElement.classList.remove('light');
      document.documentElement.classList.add('dark');
    });
    
    // Create shapes
    await createTestShapes(page);
    
    // Select a shape
    await page.click('svg rect');
    
    // Take a screenshot of the canvas with selected shape
    await expect(page.locator('.canvas-container')).toHaveScreenshot('selected-shape-dark-theme.png');
  });

  /**
   * Tests the visual appearance of the canvas with the grid in the light theme.
   * Verifies that the grid is rendered correctly with the light theme.
   */
  test('Grid appearance with light theme', async ({ page }) => {
    // Ensure light theme is active
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
      document.documentElement.classList.add('light');
    });
    
    // Enable grid
    await page.click('[data-testid="toggle-grid"]');
    
    // Take a screenshot of the canvas with grid
    await expect(page.locator('.canvas-container')).toHaveScreenshot('grid-light-theme.png');
  });

  /**
   * Tests the visual appearance of the canvas with the grid in the dark theme.
   * Verifies that the grid is rendered correctly with the dark theme.
   */
  test('Grid appearance with dark theme', async ({ page }) => {
    // Switch to dark theme
    await page.evaluate(() => {
      document.documentElement.classList.remove('light');
      document.documentElement.classList.add('dark');
    });
    
    // Enable grid
    await page.click('[data-testid="toggle-grid"]');
    
    // Take a screenshot of the canvas with grid
    await expect(page.locator('.canvas-container')).toHaveScreenshot('grid-dark-theme.png');
  });

  /**
   * Tests the visual appearance of the canvas with the ruler in the light theme.
   * Verifies that the ruler is rendered correctly with the light theme.
   */
  test('Ruler appearance with light theme', async ({ page }) => {
    // Ensure light theme is active
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
      document.documentElement.classList.add('light');
    });
    
    // Enable ruler
    await page.click('[data-testid="toggle-ruler"]');
    
    // Take a screenshot of the canvas with ruler
    await expect(page.locator('.canvas-container')).toHaveScreenshot('ruler-light-theme.png');
  });

  /**
   * Tests the visual appearance of the canvas with the ruler in the dark theme.
   * Verifies that the ruler is rendered correctly with the dark theme.
   */
  test('Ruler appearance with dark theme', async ({ page }) => {
    // Switch to dark theme
    await page.evaluate(() => {
      document.documentElement.classList.remove('light');
      document.documentElement.classList.add('dark');
    });
    
    // Enable ruler
    await page.click('[data-testid="toggle-ruler"]');
    
    // Take a screenshot of the canvas with ruler
    await expect(page.locator('.canvas-container')).toHaveScreenshot('ruler-dark-theme.png');
  });
});

/**
 * Helper function to create test shapes on the canvas.
 * Creates a rectangle, ellipse, polygon, and line.
 */
async function createTestShapes(page) {
  const canvasContainer = page.locator('.canvas-container');
  const boundingBox = await canvasContainer.boundingBox();
  
  if (!boundingBox) {
    throw new Error('Canvas container not found');
  }
  
  // Create a rectangle
  await page.click('[data-tool="rectangle"]');
  await page.mouse.move(boundingBox.x + 100, boundingBox.y + 100);
  await page.mouse.down();
  await page.mouse.move(boundingBox.x + 300, boundingBox.y + 200);
  await page.mouse.up();
  
  // Create an ellipse
  await page.click('[data-tool="ellipse"]');
  await page.mouse.move(boundingBox.x + 500, boundingBox.y + 100);
  await page.mouse.down();
  await page.mouse.move(boundingBox.x + 700, boundingBox.y + 200);
  await page.mouse.up();
  
  // Create a polygon
  await page.click('[data-tool="polygon"]');
  await page.mouse.move(boundingBox.x + 300, boundingBox.y + 300);
  await page.mouse.down();
  await page.mouse.move(boundingBox.x + 400, boundingBox.y + 300);
  await page.mouse.up();
  
  // Create a line
  await page.click('[data-tool="line"]');
  await page.mouse.move(boundingBox.x + 500, boundingBox.y + 300);
  await page.mouse.down();
  await page.mouse.move(boundingBox.x + 700, boundingBox.y + 400);
  await page.mouse.up();
  
  // Wait for all shapes to be rendered
  await page.waitForTimeout(100);
}
