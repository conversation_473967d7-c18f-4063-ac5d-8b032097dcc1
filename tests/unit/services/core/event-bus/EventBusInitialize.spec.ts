import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { appEventBus } from '@/services/core/event-bus'
import { setupEventHandlers } from '@/services/core/event-bus/handlers/eventHandlers'
import { cleanupEventSystem, initializeEventSystem } from '@/services/core/event-bus/initialize'

// Mock dependencies
vi.mock('@/services/core/event-bus', () => ({
  appEventBus: {
    reset: vi.fn(),
    subscribe: vi.fn(),
    publish: vi.fn(),
    unsubscribe: vi.fn(),
    clear: vi.fn(),
  },
}))

vi.mock('@/services/core/event-bus/handlers/eventHandlers', () => ({
  setupEventHandlers: vi.fn(),
}))

describe('event Bus Initialize', () => {
  let mockConsoleWarn: ReturnType<typeof vi.fn>

  beforeEach(() => {
    vi.clearAllMocks()
    mockConsoleWarn = vi.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('initializeEventSystem', () => {
    it('should initialize event system successfully', () => {
      initializeEventSystem()

      expect(appEventBus.reset).toHaveBeenCalledTimes(1)
      expect(setupEventHandlers).toHaveBeenCalledTimes(1)
      expect(mockConsoleWarn).toHaveBeenCalledWith('Initializing event system...')
      expect(mockConsoleWarn).toHaveBeenCalledWith('Event system initialized with CoreCoordinator integration')
    })

    it('should call functions in correct order', () => {
      const callOrder: string[] = []

      vi.mocked(appEventBus.reset).mockImplementation(() => {
        callOrder.push('reset')
      })

      vi.mocked(setupEventHandlers).mockImplementation(() => {
        callOrder.push('setupEventHandlers')
      })

      initializeEventSystem()

      expect(callOrder).toEqual([
        'reset',
        'setupEventHandlers',
      ])
    })

    it('should handle setupEventHandlers errors gracefully', () => {
      const error = new Error('Setup failed')
      vi.mocked(setupEventHandlers).mockImplementation(() => {
        throw error
      })

      expect(() => initializeEventSystem()).toThrow(error)
      expect(appEventBus.reset).toHaveBeenCalledTimes(1)
      expect(setupEventHandlers).toHaveBeenCalledTimes(1)
    })

    it('should handle appEventBus.reset errors gracefully', () => {
      const error = new Error('Reset failed')
      vi.mocked(appEventBus.reset).mockImplementation(() => {
        throw error
      })

      expect(() => initializeEventSystem()).toThrow(error)
      expect(appEventBus.reset).toHaveBeenCalledTimes(1)
      expect(setupEventHandlers).not.toHaveBeenCalled()
    })

    it('should handle multiple initialization calls', () => {
      initializeEventSystem()
      initializeEventSystem()
      initializeEventSystem()

      expect(appEventBus.reset).toHaveBeenCalledTimes(3)
      expect(setupEventHandlers).toHaveBeenCalledTimes(3)
    })
  })

  describe('cleanupEventSystem', () => {
    it('should cleanup event system successfully', () => {
      cleanupEventSystem()

      expect(appEventBus.reset).toHaveBeenCalledTimes(1)
      expect(mockConsoleWarn).toHaveBeenCalledWith('Cleaning up event system...')
      expect(mockConsoleWarn).toHaveBeenCalledWith('Event system cleaned up')
    })

    it('should call functions in correct order', () => {
      const callOrder: string[] = []

      vi.mocked(appEventBus.reset).mockImplementation(() => {
        callOrder.push('reset')
      })

      cleanupEventSystem()

      expect(callOrder).toEqual(['reset'])
    })

    it('should handle appEventBus.reset errors gracefully', () => {
      const error = new Error('Reset failed')
      vi.mocked(appEventBus.reset).mockImplementation(() => {
        throw error
      })

      expect(() => cleanupEventSystem()).toThrow(error)
      expect(appEventBus.reset).toHaveBeenCalledTimes(1)
    })

    it('should handle multiple cleanup calls', () => {
      cleanupEventSystem()
      cleanupEventSystem()
      cleanupEventSystem()

      expect(appEventBus.reset).toHaveBeenCalledTimes(3)
    })
  })

  describe('integration scenarios', () => {
    it('should handle complete initialization and cleanup cycle', () => {
      // Initialize
      initializeEventSystem()

      expect(appEventBus.reset).toHaveBeenCalledTimes(1)
      expect(setupEventHandlers).toHaveBeenCalledTimes(1)

      // Cleanup
      cleanupEventSystem()

      expect(appEventBus.reset).toHaveBeenCalledTimes(2)
    })

    it('should handle initialization followed by cleanup', () => {
      initializeEventSystem()
      expect(appEventBus.reset).toHaveBeenCalledTimes(1)
      expect(setupEventHandlers).toHaveBeenCalledTimes(1)

      cleanupEventSystem()
      expect(appEventBus.reset).toHaveBeenCalledTimes(2)
    })
  })
})
