import { describe, expect, it } from 'vitest'

// Test basic array utility functions
describe('array Utils - Basic Tests', () => {
  it('should handle basic array operations', () => {
    const arr = [1, 2, 3, 4, 5]

    expect(arr.length).toBe(5)
    expect(arr[0]).toBe(1)
    expect(arr[4]).toBe(5)
    expect(arr.indexOf(3)).toBe(2)
    expect(arr.includes(4)).toBe(true)
    expect(arr.includes(6)).toBe(false)
  })

  it('should handle array modification', () => {
    const arr = [1, 2, 3]

    arr.push(4)
    expect(arr).toEqual([1, 2, 3, 4])

    const popped = arr.pop()
    expect(popped).toBe(4)
    expect(arr).toEqual([1, 2, 3])

    arr.unshift(0)
    expect(arr).toEqual([0, 1, 2, 3])

    const shifted = arr.shift()
    expect(shifted).toBe(0)
    expect(arr).toEqual([1, 2, 3])
  })

  it('should handle array slicing and splicing', () => {
    const arr = [1, 2, 3, 4, 5]

    expect(arr.slice(1, 3)).toEqual([2, 3])
    expect(arr.slice(2)).toEqual([3, 4, 5])

    const copy = [...arr]
    copy.splice(1, 2, 'a', 'b')
    expect(copy).toEqual([1, 'a', 'b', 4, 5])
  })

  it('should handle array iteration methods', () => {
    const arr = [1, 2, 3, 4, 5]

    const doubled = arr.map(x => x * 2)
    expect(doubled).toEqual([2, 4, 6, 8, 10])

    const evens = arr.filter(x => x % 2 === 0)
    expect(evens).toEqual([2, 4])

    const sum = arr.reduce((acc, x) => acc + x, 0)
    expect(sum).toBe(15)

    const hasEven = arr.some(x => x % 2 === 0)
    expect(hasEven).toBe(true)

    const allPositive = arr.every(x => x > 0)
    expect(allPositive).toBe(true)
  })

  it('should handle array searching', () => {
    const arr = [1, 2, 3, 4, 5]

    const found = arr.find(x => x > 3)
    expect(found).toBe(4)

    const foundIndex = arr.findIndex(x => x > 3)
    expect(foundIndex).toBe(3)
  })

  it('should handle array sorting', () => {
    const arr = [3, 1, 4, 1, 5, 9, 2, 6]
    const sorted = [...arr].sort((a, b) => a - b)

    expect(sorted).toEqual([1, 1, 2, 3, 4, 5, 6, 9])
  })

  it('should handle array concatenation and joining', () => {
    const arr1 = [1, 2, 3]
    const arr2 = [4, 5, 6]

    const combined = arr1.concat(arr2)
    expect(combined).toEqual([1, 2, 3, 4, 5, 6])

    const spread = [...arr1, ...arr2]
    expect(spread).toEqual([1, 2, 3, 4, 5, 6])

    const joined = arr1.join(', ')
    expect(joined).toBe('1, 2, 3')
  })
})
