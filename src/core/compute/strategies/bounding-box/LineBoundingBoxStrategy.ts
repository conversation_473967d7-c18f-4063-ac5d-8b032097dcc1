/**
 * Bounding Box Calculation Strategy for Line Elements
 *
 * @remarks
 * This strategy implements the {@link BoundingBoxCalculatorStrategy} for calculating
 * the axis-aligned bounding box of Line elements ({@link CoreElementType.LINE}).
 *
 * The calculation involves:
 * 1. Retrieving the `start` and `end` points of the line from the element's properties.
 * 2. Adjusting these points by the line element's `position` (inherited from
 *    {@link ShapeElement}) to get absolute coordinates, as line points are often relative.
 * 3. Delegating the core geometric calculation to the `calculateLineBoundingBoxUtil`
 *    function from `../../../../lib/utils/geometry/lineUtils`.
 *
 * The strategy validates that the element is of type `LINE` and that its
 * start and end points are valid.
 *
 * @module core/compute/strategies/bounding-box/LineBoundingBoxStrategy
 * @see {@link Path.Line} for the line element type definition.
 * @see {@link BoundingBoxInterface} for the bounding box structure.
 */
import type { BoundingBoxCalculatorStrategy } from '../../../../types/core/compute'
import type { BoundingBox as BoundingBoxInterface } from '../../../../types/core/element/geometry/bounding-box'
import type { PointData as IPoint } from '../../../../types/core/element/geometry/point' // Use PointData, alias as IPoint
import type {
  Element,
  ShapeElement, // Import ShapeElement
  // MetadataProperties // Not used
} from '../../../../types/core/elementDefinitions'
import { calculateLineBoundingBox as calculateLineBoundingBoxUtil } from '../../../../lib/utils/geometry/lineUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType, // Import ShapeElement
  // MetadataProperties // Not used
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

export class LineBoundingBoxStrategy implements BoundingBoxCalculatorStrategy {
  /**
   * Calculates the axis-aligned bounding box of a Line element.
   *
   * @param element - The line element, expected to be of type {@link Element}
   *                  and specifically {@link CoreElementType.LINE}, conforming to {@link Path.Line}.
   * @returns A {@link BoundingBoxInterface} object representing the bounding box.
   * @throws {@link CoreError} if the provided element is not of type `LINE`.
   * @throws {@link CoreError} if the line's `start` or `end` points are invalid.
   */
  public calculateBoundingBox(element: Element): BoundingBoxInterface {
    if (element.type !== CoreElementType.LINE) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `Expected LINE, got ${element.type} for ID: ${element.id}`,
        undefined,
        { component: 'LineBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    const shapeElement = element as ShapeElement
    const properties = shapeElement.properties || {}

    // Try to get start and end points from properties
    const startPoint = properties.start as IPoint | undefined
    const endPoint = properties.end as IPoint | undefined

    if (startPoint == null || endPoint == null
      || typeof startPoint.x !== 'number' || typeof startPoint.y !== 'number'
      || typeof endPoint.x !== 'number' || typeof endPoint.y !== 'number'
      || !Number.isFinite(startPoint.x) || !Number.isFinite(startPoint.y)
      || !Number.isFinite(endPoint.x) || !Number.isFinite(endPoint.y)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid points for Line ID: ${element.id}`,
        undefined,
        { component: 'LineBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    // The calculateLineBoundingBoxUtil expects absolute points.
    // Path.Line's start/end points are typically relative to its own position (from ShapeElement).
    // Access position from ShapeElement for reliability
    const elPosX = shapeElement.position?.x ?? 0
    const elPosY = shapeElement.position?.y ?? 0

    const absStart: IPoint = {
      x: startPoint.x + elPosX,
      y: startPoint.y + elPosY,
      z: startPoint.z,
    }
    const absEnd: IPoint = {
      x: endPoint.x + elPosX,
      y: endPoint.y + elPosY,
      z: endPoint.z,
    }

    // Delegate to the utility function which returns a BoundingBoxClass instance
    return calculateLineBoundingBoxUtil(absStart, absEnd)
  }

  /**
   * Returns the element type that this strategy is responsible for.
   * @returns The element type {@link CoreElementType.LINE}
   */
  public getElementType(): CoreElementType {
    return CoreElementType.LINE
  }
}
