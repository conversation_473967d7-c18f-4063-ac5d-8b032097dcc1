/**
 * S<PERSON>pe Renderer Component
 *
 * This component is responsible for rendering all shapes on the canvas.
 * It uses D3.js for efficient SVG manipulation and handles the rendering
 * of different shape types (rectangles, circles, ellipses, lines, polygons, etc.).
 *
 * The renderer dynamically applies visual styles to shapes, handles shape
 * selection indicators, and manages appropriate scaling based on the zoom level.
 * It also registers event handlers for shape interaction (selection, etc.).
 *
 * Key features:
 * - Type-specific rendering logic for each shape type
 * - Selection state visualization
 * - Event handling for shape interaction
 * - Zoom-aware stroke width scaling
 * - Position transformation and coordinate management
 *
 * This component does not render any direct React output, as it manipulates
 * the DOM through D3 selections for optimal performance with large numbers of shapes.
 *
 * @module components/canvas/ShapeRenderer
 */

import type { ShapeElement as ShapeModel } from '@types_core/elementDefinitions'
import type React from 'react'
import type { SpecialElementDetails } from '@/lib/utils/element/specialElementUtils'
import type { PointClass as Point } from '@/lib/utils/geometry/PointClass'
import type { AppEventBusImpl } from '@/services/core/event-bus'
import * as d3 from 'd3'
import { useCallback, useEffect } from 'react'

// Event bus imports for shape updates
import { extractKeyboardModifiers, isMultiSelectKeyPressed } from '@/lib/utils/canvas/selectionUtils'
import { cleanPattern } from '@/lib/utils/element/cleanPattern'
import {
  getStyleProps,
  // isArc, // No longer needed directly here
  // isCircle,
  // isCubicBezier,
  // isEllipse,
  // isImage,
  // isLine,
  // isPolygon,
  // isPolyline,
  // isQuadraticBezier,
  // isRectangle,
  // isText,
} from '@/lib/utils/element/elementModelUtils' // Removed unused imports, will be handled by type checks
import { determineSpecialElementInfo } from '@/lib/utils/element/specialElementUtils'
import { PointClass } from '@/lib/utils/geometry/PointClass'
import { getEventBus } from '@/services/core/event-bus'
import { ElementType as CoreElementType } from '@/types/core/elementDefinitions' // Added for iconManifest
import { AppEventType } from '@/types/services/events'

/* eslint-disable no-console */
// Console usage is intentional for debugging and development purposes in this file

// Type definitions for environment checking
interface ImportMetaWithEnv {
  env?: {
    DEV?: boolean
    NODE_ENV?: string
  }
}

interface WindowWithDev {
  __DEV__?: boolean
}

// Type definitions for shape points
interface ShapePoint {
  x: number
  y: number
  z?: number
}

// Type definitions for Zustand store
interface ZustandShapesStore {
  getState: () => {
    shapes: ShapeModel[]
    selectedShapeIds: string[]
  }
  setState: (state: {
    shapes: ShapeModel[]
    selectedShapeIds: string[]
  }) => void
}

interface WindowWithZustandStore {
  __ZUSTAND_SHAPES_STORE__?: ZustandShapesStore
}

// Type for shape updates
interface ShapeUpdates {
  position?: {
    x: number
    y: number
    z: number
  }
  width?: number
  height?: number
  radius?: number
  radiusX?: number
  radiusY?: number
  points?: ShapePoint[]
  properties?: Record<string, unknown>
  [key: string]: unknown
}

// Type definitions for D3.js event handling
interface D3DragEvent {
  x: number
  y: number
  sourceEvent: MouseEvent
}

// Type-safe D3 selection types
type D3SVGGSelection = d3.Selection<SVGGElement, unknown, null, undefined>

type D3SVGRectSelection = d3.Selection<SVGRectElement, unknown, null, undefined>
type D3ShapeGroupSelection = d3.Selection<SVGGElement, ShapeModel, d3.BaseType, unknown>

// Extended shape model interfaces for type-safe property access
interface ShapeModelWithRadii extends ShapeModel {
  radiusX?: number
  radiusY?: number
}

interface ShapeModelWithText extends ShapeModel {
  text?: string
  content?: string
  fontSize?: number
  fontFamily?: string
  fontStyle?: string
  fontWeight?: string | number
  textAlign?: string
}

interface ShapeModelWithPoints extends ShapeModel {
  points?: { x: number, y: number }[]
}

/**
 * Debug utility function to replace console.log in production
 */
function _debug(message: string, ...args: unknown[]) {
  // Use environment variable check without require
  try {
    // Check if we're in development mode using various methods
    // Use import.meta.env for Vite environments, fallback to NODE_ENV check
    const hasImportMeta = typeof import.meta !== 'undefined'
    const importMetaWithEnv = hasImportMeta ? (import.meta as ImportMetaWithEnv) : null
    const hasImportMetaEnv = importMetaWithEnv?.env !== undefined
    const isViteDev = hasImportMetaEnv && importMetaWithEnv?.env?.DEV === true

    // Skip process-based environment detection to avoid ESLint node/prefer-global/process rule
    // In browser environments, we rely on import.meta.env and window.__DEV__ instead
    const isNodeDev = false

    const hasWindow = typeof window !== 'undefined'
    const windowWithDev = hasWindow ? (window as WindowWithDev) : null
    const hasWindowDev = windowWithDev?.__DEV__ !== undefined && typeof windowWithDev.__DEV__ === 'boolean'
    const isWindowDev = hasWindowDev && windowWithDev?.__DEV__ === true

    const isDevelopment = isViteDev || isNodeDev || isWindowDev

    if (isDevelopment) {
      console.warn(`[ShapeRenderer Debug] ${message}`, ...args)
    }
  }
  catch {
    // Fallback for environments where process is not available
    // Only log in development mode
    const hasWindow = typeof window !== 'undefined'
    const windowWithDev = hasWindow ? (window as WindowWithDev) : null
    const hasWindowDev = windowWithDev?.__DEV__ !== undefined && typeof windowWithDev.__DEV__ === 'boolean'
    const isWindowDev = hasWindowDev && windowWithDev?.__DEV__ === true

    if (isWindowDev) {
      console.warn(`[ShapeRenderer Debug] ${message}`, ...args)
    }
  }
}

/**
 * Warning utility function for important warnings
 */
function _warn(message: string, ...args: unknown[]) {
  // Always show warnings, but prefix them appropriately
  console.warn(`[ShapeRenderer Warning] ${message}`, ...args)
}

/**
 * 计算元素的准确边界框
 * 返回相对于元素position的边界框，统一使用中心对齐的坐标系
 */
function calculateAccurateBoundingBox(shapeData: ShapeModel, _currentZoom: number): {
  x: number
  y: number
  width: number
  height: number
} {
  let bboxWidth = 100
  let bboxHeight = 100
  let bboxX = 0
  let bboxY = 0

  // 多边形类型集合
  const polygonTypes = [
    CoreElementType.POLYGON,
    CoreElementType.TRIANGLE,
    CoreElementType.QUADRILATERAL,
    CoreElementType.PENTAGON,
    CoreElementType.HEXAGON,
    CoreElementType.HEPTAGON,
    CoreElementType.OCTAGON,
    CoreElementType.NONAGON,
    CoreElementType.DECAGON,
  ]

  if (polygonTypes.includes(shapeData.type as CoreElementType)) {
    // 🔧 修复：多边形的points是相对于position的相对坐标，需要正确计算边界框
    let points: { x: number, y: number }[] | undefined

    // Type-safe access to points property
    const shapeWithPoints = shapeData as ShapeModel & { points?: { x: number, y: number }[] }
    if (Array.isArray(shapeWithPoints.points) && shapeWithPoints.points.length >= 3) {
      points = shapeWithPoints.points
    }
    else if (shapeData.properties && Array.isArray(shapeData.properties.points)
      && shapeData.properties.points.length >= 3
      && shapeData.properties.points.every((p: unknown): p is { x: number, y: number } =>
        typeof p === 'object' && p !== null
        && typeof (p as { x?: unknown, y?: unknown }).x === 'number'
        && typeof (p as { x?: unknown, y?: unknown }).y === 'number')) {
      points = shapeData.properties.points as { x: number, y: number }[]
    }

    if (points && points.length >= 3) {
      // 计算相对坐标的边界框
      let minX = Infinity
      let minY = Infinity
      let maxX = -Infinity
      let maxY = -Infinity
      for (const p of points) {
        if (typeof p.x === 'number' && typeof p.y === 'number') {
          minX = Math.min(minX, p.x)
          minY = Math.min(minY, p.y)
          maxX = Math.max(maxX, p.x)
          maxY = Math.max(maxY, p.y)
        }
      }

      if (minX !== Infinity && minY !== Infinity && maxX !== -Infinity && maxY !== -Infinity) {
        bboxWidth = maxX - minX
        bboxHeight = maxY - minY
        // 🔧 关键修复：边界框的位置应该相对于元素的position
        // 因为points是相对坐标，边界框也应该是相对坐标
        bboxX = minX
        bboxY = minY

        _debug(`🔧 [calculateAccurateBoundingBox] Polygon ${shapeData.type} bbox:`, {
          pointsCount: points.length,
          pointsRange: { minX, minY, maxX, maxY },
          bboxSize: { width: bboxWidth, height: bboxHeight },
          bboxPosition: { x: bboxX, y: bboxY },
        })
      }
      else {
        // 回退到默认值
        bboxWidth = 100
        bboxHeight = 100
        bboxX = -bboxWidth / 2
        bboxY = -bboxHeight / 2
        _warn(`🔧 [calculateAccurateBoundingBox] Polygon ${shapeData.type} using fallback bbox`)
      }
    }
    else {
      // 没有有效的points，使用默认值
      bboxWidth = 100
      bboxHeight = 100
      bboxX = -bboxWidth / 2
      bboxY = -bboxHeight / 2
      _warn(`🔧 [calculateAccurateBoundingBox] Polygon ${shapeData.type} no valid points, using default bbox`)
    }
  }
  else {
    switch (shapeData.type) {
      case CoreElementType.RECTANGLE:
      case CoreElementType.SQUARE:
        bboxWidth = (typeof shapeData.width === 'number' && Number.isFinite(shapeData.width))
          ? shapeData.width
          : (typeof shapeData.properties?.width === 'number' && Number.isFinite(shapeData.properties.width))
              ? shapeData.properties.width
              : 100
        bboxHeight = (typeof shapeData.height === 'number' && Number.isFinite(shapeData.height))
          ? shapeData.height
          : (typeof shapeData.properties?.height === 'number' && Number.isFinite(shapeData.properties.height))
              ? shapeData.properties.height
              : 100
        bboxX = -bboxWidth / 2
        bboxY = -bboxHeight / 2
        break
      case CoreElementType.CIRCLE: {
        // Type-safe access to radius property
        const shapeWithRadius = shapeData as ShapeModel & { radius?: number }
        const radius = (typeof shapeWithRadius.radius === 'number' && Number.isFinite(shapeWithRadius.radius))
          ? shapeWithRadius.radius
          : (typeof shapeData.properties?.radius === 'number' && Number.isFinite(shapeData.properties.radius))
              ? shapeData.properties.radius
              : 50
        bboxWidth = radius * 2
        bboxHeight = radius * 2
        bboxX = -bboxWidth / 2
        bboxY = -bboxHeight / 2
        break
      }
      case CoreElementType.ELLIPSE: {
        // Type-safe access to radiusX and radiusY properties
        const shapeWithRadii = shapeData as ShapeModel & { radiusX?: number, radiusY?: number }
        const radiusX = (typeof shapeWithRadii.radiusX === 'number' && Number.isFinite(shapeWithRadii.radiusX))
          ? shapeWithRadii.radiusX
          : (typeof shapeData.properties?.radiusX === 'number' && Number.isFinite(shapeData.properties.radiusX))
              ? shapeData.properties.radiusX
              : (typeof shapeData.width === 'number' && Number.isFinite(shapeData.width))
                  ? shapeData.width / 2
                  : (typeof shapeData.properties?.width === 'number' && Number.isFinite(shapeData.properties.width))
                      ? shapeData.properties.width / 2
                      : 50
        const radiusY = (typeof shapeWithRadii.radiusY === 'number' && Number.isFinite(shapeWithRadii.radiusY))
          ? shapeWithRadii.radiusY
          : (typeof shapeData.properties?.radiusY === 'number' && Number.isFinite(shapeData.properties.radiusY))
              ? shapeData.properties.radiusY
              : (typeof shapeData.height === 'number' && Number.isFinite(shapeData.height))
                  ? shapeData.height / 2
                  : (typeof shapeData.properties?.height === 'number' && Number.isFinite(shapeData.properties.height))
                      ? shapeData.properties.height / 2
                      : 50
        bboxWidth = radiusX * 2
        bboxHeight = radiusY * 2
        bboxX = -bboxWidth / 2
        bboxY = -bboxHeight / 2
        break
      }
      case CoreElementType.TEXT:
      case CoreElementType.TEXT_LABEL: {
        const textContent = (shapeData.properties?.text as string) || (shapeData.properties?.content as string) || 'Text'
        const fontSize = typeof (shapeData.properties?.fontSize as number) === 'number' ? (shapeData.properties?.fontSize as number) : 16
        const fontFamily = (shapeData.properties?.fontFamily as string) || 'Arial'

        // 更精确的字符宽度比例计算
        let charWidthRatio = 0.6
        const lowerFontFamily = fontFamily.toLowerCase()
        if (lowerFontFamily.includes('monospace') || lowerFontFamily.includes('courier')) {
          charWidthRatio = 0.6
        }
        else if (lowerFontFamily.includes('arial') || lowerFontFamily.includes('helvetica')) {
          charWidthRatio = 0.55
        }
        else if (lowerFontFamily.includes('times')) {
          charWidthRatio = 0.5
        }

        const lines = textContent.split('\n')
        const maxLineLength = Math.max(...lines.map(line => line.length))

        // 更精确的文本尺寸计算，减少额外空间
        const estimatedTextWidth = maxLineLength * fontSize * charWidthRatio
        const estimatedTextHeight = lines.length * fontSize * 1.2 // 减少行高倍数

        bboxWidth = (typeof shapeData.width === 'number' && Number.isFinite(shapeData.width))
          ? shapeData.width
          : (typeof shapeData.properties?.width === 'number' && Number.isFinite(shapeData.properties.width))
              ? shapeData.properties.width
              : Math.max(estimatedTextWidth, fontSize * 0.5) // 减少最小宽度
        bboxHeight = (typeof shapeData.height === 'number' && Number.isFinite(shapeData.height))
          ? shapeData.height
          : (typeof shapeData.properties?.height === 'number' && Number.isFinite(shapeData.properties.height))
              ? shapeData.properties.height
              : Math.max(estimatedTextHeight, fontSize) // 保持最小高度为fontSize
        bboxX = -bboxWidth / 2
        bboxY = -bboxHeight / 2
        break
      }
      case CoreElementType.IMAGE: {
        bboxWidth = (typeof shapeData.width === 'number' && Number.isFinite(shapeData.width))
          ? shapeData.width
          : (typeof shapeData.properties?.width === 'number' && Number.isFinite(shapeData.properties.width))
              ? shapeData.properties.width
              : 200
        bboxHeight = (typeof shapeData.height === 'number' && Number.isFinite(shapeData.height))
          ? shapeData.height
          : (typeof shapeData.properties?.height === 'number' && Number.isFinite(shapeData.properties.height))
              ? shapeData.properties.height
              : 200
        bboxX = -bboxWidth / 2
        bboxY = -bboxHeight / 2
        break
      }
      // Note: PATH type doesn't exist in ElementType enum, using path-related types instead
      case CoreElementType.QUADRATIC:
      case CoreElementType.CUBIC: {
        const pathData = (shapeData.properties?.d as string) || (shapeData.properties?.pathData as string) || ''
        if (pathData) {
          const pathBounds = estimatePathBounds(pathData)
          bboxWidth = pathBounds.width
          bboxHeight = pathBounds.height
          bboxX = -bboxWidth / 2
          bboxY = -bboxHeight / 2
        }
        else {
          bboxWidth = 100
          bboxHeight = 100
          bboxX = -bboxWidth / 2
          bboxY = -bboxHeight / 2
        }
        break
      }
      case CoreElementType.ARC: {
        const arcRadius = typeof (shapeData.properties?.radius as number) === 'number' ? (shapeData.properties?.radius as number) : 50
        const startAngle = typeof (shapeData.properties?.startAngle as number) === 'number' ? (shapeData.properties?.startAngle as number) : 0
        const endAngle = typeof (shapeData.properties?.endAngle as number) === 'number' ? (shapeData.properties?.endAngle as number) : 90
        const arcBounds = estimateArcBounds(arcRadius, startAngle, endAngle)
        bboxWidth = arcBounds.width
        bboxHeight = arcBounds.height
        bboxX = -bboxWidth / 2
        bboxY = -bboxHeight / 2
        break
      }
      default: {
        if (typeof shapeData.width === 'number' && Number.isFinite(shapeData.width)
          && typeof shapeData.height === 'number' && Number.isFinite(shapeData.height)) {
          bboxWidth = shapeData.width
          bboxHeight = shapeData.height
          bboxX = -bboxWidth / 2
          bboxY = -bboxHeight / 2
        }
        else if (shapeData.properties !== undefined && typeof shapeData.properties.width === 'number' && Number.isFinite(shapeData.properties.width)
          && typeof shapeData.properties.height === 'number' && Number.isFinite(shapeData.properties.height)) {
          bboxWidth = shapeData.properties.width
          bboxHeight = shapeData.properties.height
          bboxX = -bboxWidth / 2
          bboxY = -bboxHeight / 2
        }
        else {
          bboxWidth = 100
          bboxHeight = 100
          bboxX = -bboxWidth / 2
          bboxY = -bboxHeight / 2
        }
        break
      }
    }
  }
  // 移除padding，确保边界框与元素边界紧密相切
  return {
    x: bboxX,
    y: bboxY,
    width: bboxWidth,
    height: bboxHeight,
  }
}

/**
 * 精确计算路径元素的边界框
 */
function estimatePathBounds(pathData: string): { x: number, y: number, width: number, height: number } {
  // 更精确的路径边界框计算
  // 提取路径中的数字坐标，支持小数和负数
  const numbers = pathData.match(/-?\d*\.?\d+/g)
  if (!numbers || numbers.length < 2) {
    return { x: -50, y: -50, width: 100, height: 100 }
  }

  const coords = numbers.map(n => Number.parseFloat(n)).filter(n => !Number.isNaN(n))
  let minX = Infinity
  let minY = Infinity
  let maxX = -Infinity
  let maxY = -Infinity

  // 假设坐标是成对出现的 (x, y)
  for (let i = 0; i < coords.length - 1; i += 2) {
    const x = coords[i]
    const y = coords[i + 1]
    if (Number.isFinite(x) && Number.isFinite(y)) {
      minX = Math.min(minX, x)
      minY = Math.min(minY, y)
      maxX = Math.max(maxX, x)
      maxY = Math.max(maxY, y)
    }
  }

  if (minX === Infinity || maxX === -Infinity) {
    return { x: -50, y: -50, width: 100, height: 100 }
  }

  // 确保最小尺寸，避免零宽度或零高度
  const width = Math.max(maxX - minX, 1)
  const height = Math.max(maxY - minY, 1)

  return {
    x: minX,
    y: minY,
    width,
    height,
  }
}

/**
 * 精确计算弧形元素的边界框
 */
function estimateArcBounds(radius: number, startAngle: number, endAngle: number): { x: number, y: number, width: number, height: number } {
  // 将角度转换为弧度
  const startRad = (startAngle * Math.PI) / 180
  const endRad = (endAngle * Math.PI) / 180

  // 计算弧的起点和终点
  const startX = radius * Math.cos(startRad)
  const startY = radius * Math.sin(startRad)
  const endX = radius * Math.cos(endRad)
  const endY = radius * Math.sin(endRad)

  // 初始边界框包含起点和终点
  let minX = Math.min(startX, endX)
  let minY = Math.min(startY, endY)
  let maxX = Math.max(startX, endX)
  let maxY = Math.max(startY, endY)

  // 标准化角度到 [0, 360) 范围
  const normalizeAngle = (angle: number) => ((angle % 360) + 360) % 360
  const normStart = normalizeAngle(startAngle)
  const normEnd = normalizeAngle(endAngle)

  // 检查弧是否跨越关键角度点（0°, 90°, 180°, 270°）
  const checkAngle = (angle: number) => {
    if (normStart <= normEnd) {
      return angle >= normStart && angle <= normEnd
    }
    else {
      return angle >= normStart || angle <= normEnd
    }
  }

  // 检查是否跨越0度（右侧，x = radius）
  if (checkAngle(0)) {
    maxX = Math.max(maxX, radius)
  }
  // 检查是否跨越90度（顶部，y = radius）
  if (checkAngle(90)) {
    maxY = Math.max(maxY, radius)
  }
  // 检查是否跨越180度（左侧，x = -radius）
  if (checkAngle(180)) {
    minX = Math.min(minX, -radius)
  }
  // 检查是否跨越270度（底部，y = -radius）
  if (checkAngle(270)) {
    minY = Math.min(minY, -radius)
  }

  // 确保最小尺寸
  const width = Math.max(maxX - minX, 1)
  const height = Math.max(maxY - minY, 1)

  return {
    x: minX,
    y: minY,
    width,
    height,
  }
}

/**
 * 高性能缩放计算函数 - 支持比例保持和角度吸附
 */
function calculateSimpleResize(
  originalBounds: { x: number, y: number, width: number, height: number },
  handleId: string,
  dx: number,
  dy: number,
  maintainAspectRatio: boolean = false,
): { x: number, y: number, width: number, height: number } {
  let newX = originalBounds.x
  let newY = originalBounds.y
  let newWidth = originalBounds.width
  let newHeight = originalBounds.height

  // 如果需要保持比例，计算统一的缩放因子
  if (maintainAspectRatio && (handleId === 'nw' || handleId === 'ne' || handleId === 'sw' || handleId === 'se')) {
    // 对于角手柄，使用较大的变化量来保持比例
    const scaleX = (originalBounds.width + (handleId.includes('e') ? dx : -dx)) / originalBounds.width
    const scaleY = (originalBounds.height + (handleId.includes('s') ? dy : -dy)) / originalBounds.height
    const scale = Math.max(Math.abs(scaleX), Math.abs(scaleY))

    newWidth = originalBounds.width * scale
    newHeight = originalBounds.height * scale

    // 根据手柄位置调整位置
    switch (handleId) {
      case 'nw':
        newX = originalBounds.x + originalBounds.width - newWidth
        newY = originalBounds.y + originalBounds.height - newHeight
        break
      case 'ne':
        newY = originalBounds.y + originalBounds.height - newHeight
        break
      case 'sw':
        newX = originalBounds.x + originalBounds.width - newWidth
        break
      case 'se':
        // 位置不变
        break
    }
  }
  else {
    // 标准缩放逻辑
    switch (handleId) {
      case 'nw':
        newX = originalBounds.x + dx
        newY = originalBounds.y + dy
        newWidth = originalBounds.width - dx
        newHeight = originalBounds.height - dy
        break
      case 'ne':
        newY = originalBounds.y + dy
        newWidth = originalBounds.width + dx
        newHeight = originalBounds.height - dy
        break
      case 'sw':
        newX = originalBounds.x + dx
        newWidth = originalBounds.width - dx
        newHeight = originalBounds.height + dy
        break
      case 'se':
        newWidth = originalBounds.width + dx
        newHeight = originalBounds.height + dy
        break
      case 'n':
        // 对于旋转元素，'n' 手柄应该只沿着局部Y轴方向缩放
        // 在旋转坐标系中，dy 已经是正确的局部坐标
        newY = originalBounds.y + dy
        newHeight = originalBounds.height - dy
        break
      case 'e':
        // 对于旋转元素，'e' 手柄应该只沿着局部X轴方向缩放
        // 在旋转坐标系中，dx 已经是正确的局部坐标
        newWidth = originalBounds.width + dx
        break
      case 's':
        // 对于旋转元素，'s' 手柄应该只沿着局部Y轴方向缩放
        newHeight = originalBounds.height + dy
        break
      case 'w':
        // 对于旋转元素，'w' 手柄应该只沿着局部X轴方向缩放
        newX = originalBounds.x + dx
        newWidth = originalBounds.width - dx
        break
    }
  }

  // 确保最小尺寸
  const minSize = 10
  if (newWidth < minSize) {
    if (handleId.includes('w')) {
      newX = originalBounds.x + originalBounds.width - minSize
    }
    newWidth = minSize
  }
  if (newHeight < minSize) {
    if (handleId.includes('n')) {
      newY = originalBounds.y + originalBounds.height - minSize
    }
    newHeight = minSize
  }

  return { x: newX, y: newY, width: newWidth, height: newHeight }
}

/**
 * 计算相对坐标系统中的元素属性更新
 */
function calculateRelativeElementUpdates(
  element: ShapeModel,
  originalBounds: { x: number, y: number, width: number, height: number },
  newBounds: { x: number, y: number, width: number, height: number },
  handleId?: string,
): ShapeUpdates {
  const updates: ShapeUpdates = {}

  // 计算原始和新边界框中心
  const originalCenterX = originalBounds.x + originalBounds.width / 2
  const originalCenterY = originalBounds.y + originalBounds.height / 2
  const newCenterX = newBounds.x + newBounds.width / 2
  const newCenterY = newBounds.y + newBounds.height / 2

  // 计算中心点偏移量
  const centerOffsetX = newCenterX - originalCenterX
  const centerOffsetY = newCenterY - originalCenterY

  // Polygon types for scaling
  const polygonTypes = [
    CoreElementType.POLYGON,
    CoreElementType.TRIANGLE,
    CoreElementType.QUADRILATERAL,
    CoreElementType.PENTAGON,
    CoreElementType.HEXAGON,
    CoreElementType.HEPTAGON,
    CoreElementType.OCTAGON,
    CoreElementType.NONAGON,
    CoreElementType.DECAGON,
  ]

  // 更新位置（如果中心点发生偏移）
  if (centerOffsetX !== 0 || centerOffsetY !== 0) {
    // 获取元素的旋转角度
    const rotation = typeof element.rotation === 'number' && Number.isFinite(element.rotation) ? element.rotation : 0

    // 如果元素有旋转，需要将局部坐标系的偏移量转换到全局坐标系
    let globalOffsetX = centerOffsetX
    let globalOffsetY = centerOffsetY

    if (rotation !== 0) {
      // 将局部偏移量通过旋转矩阵转换到全局坐标系
      const rotationRadians = (rotation * Math.PI) / 180
      const cos = Math.cos(rotationRadians) // 正向旋转，因为要从局部转到全局
      const sin = Math.sin(rotationRadians)

      globalOffsetX = centerOffsetX * cos - centerOffsetY * sin
      globalOffsetY = centerOffsetX * sin + centerOffsetY * cos
    }

    updates.position = {
      x: (typeof element.position.x === 'number' ? element.position.x : 0) + globalOffsetX,
      y: (typeof element.position.y === 'number' ? element.position.y : 0) + globalOffsetY,
      z: typeof element.position.z === 'number' ? element.position.z : 0,
    }
  }

  // 处理多边形类型 - 使用基于固定边的等比例缩放
  if (polygonTypes.includes(element.type as CoreElementType) && element.properties && Array.isArray(element.properties.points)) {
    // 计算等比例缩放因子（使用较小的缩放比例以保持形状）
    const scaleX = newBounds.width / originalBounds.width
    const scaleY = newBounds.height / originalBounds.height
    const uniformScale = Math.min(scaleX, scaleY) // 使用较小的缩放比例保持等比例

    const origPoints = element.properties.points

    // 🔧 修复：基于固定边进行缩放，而不是基于中心点
    // 根据手柄ID确定固定边（参考点）
    let fixedX: number, fixedY: number

    switch (handleId) {
      case 'se': // 右下角：左边和上边固定
        fixedX = originalBounds.x
        fixedY = originalBounds.y
        break
      case 'nw': // 左上角：右边和下边固定
        fixedX = originalBounds.x + originalBounds.width
        fixedY = originalBounds.y + originalBounds.height
        break
      case 'ne': // 右上角：左边和下边固定
        fixedX = originalBounds.x
        fixedY = originalBounds.y + originalBounds.height
        break
      case 'sw': // 左下角：右边和上边固定
        fixedX = originalBounds.x + originalBounds.width
        fixedY = originalBounds.y
        break
      case undefined: // 明确处理 undefined 情况
      default:
        // 如果没有指定手柄ID，回退到中心点缩放
        fixedX = originalBounds.x + originalBounds.width / 2
        fixedY = originalBounds.y + originalBounds.height / 2
        break
    }

    // 相对于固定边进行缩放
    const scaledPoints = origPoints.map((p: ShapePoint) => {
      // 计算相对于固定点的偏移量
      const offsetX = p.x - fixedX
      const offsetY = p.y - fixedY

      // 缩放偏移量
      const scaledOffsetX = offsetX * uniformScale
      const scaledOffsetY = offsetY * uniformScale

      // 返回缩放后的绝对坐标
      return {
        x: fixedX + scaledOffsetX,
        y: fixedY + scaledOffsetY,
        z: p.z,
      }
    })

    // 🔧 关键修复：确保缩放后的points仍然以原点为中心
    // 计算缩放后points的几何中心
    const scaledCenterX = scaledPoints.reduce((sum: number, p: ShapePoint) => sum + p.x, 0) / scaledPoints.length
    const scaledCenterY = scaledPoints.reduce((sum: number, p: ShapePoint) => sum + p.y, 0) / scaledPoints.length

    // 将所有points平移，使几何中心回到原点
    const centeredScaledPoints = scaledPoints.map((p: ShapePoint) => ({
      x: p.x - scaledCenterX,
      y: p.y - scaledCenterY,
      z: p.z,
    }))

    // 计算缩放后的实际边界框尺寸
    const scaledWidth = originalBounds.width * uniformScale
    const scaledHeight = originalBounds.height * uniformScale

    // 🔧 计算新的radius：对于多边形，radius是外接圆半径
    // 使用原始radius乘以缩放因子
    const originalRadius = typeof (element.properties?.radius as number) === 'number'
      ? (element.properties?.radius as number)
      : typeof (element.radius as number) === 'number'
        ? (element.radius as number)
        : Math.min(originalBounds.width, originalBounds.height) / 2
    const newRadius = originalRadius * uniformScale

    // 🔧 计算新的position：由于固定边缩放，元素的中心位置会发生变化
    const newPositionX = (typeof element.position.x === 'number' ? element.position.x : 0) + scaledCenterX
    const newPositionY = (typeof element.position.y === 'number' ? element.position.y : 0) + scaledCenterY

    _debug(`🔧 [ShapeRenderer] Polygon fixed-edge scaling applied:`, {
      elementType: element.type,
      handleId,
      uniformScale,
      fixedPoint: { x: fixedX, y: fixedY },
      originalBounds,
      newBounds,
      scaledDimensions: { width: scaledWidth, height: scaledHeight },
      centerOffset: { x: scaledCenterX, y: scaledCenterY },
      newPosition: { x: newPositionX, y: newPositionY },
      radiusUpdate: { original: originalRadius, new: newRadius },
    })

    updates.points = centeredScaledPoints
    updates.width = scaledWidth
    updates.height = scaledHeight
    updates.radius = newRadius
    updates.position = {
      x: newPositionX,
      y: newPositionY,
      z: typeof element.position.z === 'number' ? element.position.z : 0,
    }
    updates.properties = {
      ...element.properties,
      points: centeredScaledPoints,
      width: scaledWidth,
      height: scaledHeight,
      radius: newRadius,
      creationRadius: newRadius, // 保持 radius 和 creationRadius 同步
    }

    return updates
  }

  // 处理其他元素类型的尺寸更新
  switch (element.type) {
    case CoreElementType.RECTANGLE: {
      updates.width = newBounds.width
      updates.height = newBounds.height
      updates.properties = {
        ...element.properties,
        width: newBounds.width,
        height: newBounds.height,
      }
      break
    }
    case CoreElementType.SQUARE: {
      const squareSize = Math.min(newBounds.width, newBounds.height)
      updates.width = squareSize
      updates.height = squareSize
      updates.properties = {
        ...element.properties,
        width: squareSize,
        height: squareSize,
      }
      break
    }
    case CoreElementType.CIRCLE: {
      const radius = Math.min(newBounds.width, newBounds.height) / 2
      updates.radius = radius
      updates.properties = {
        ...element.properties,
        radius,
      }
      break
    }
    case CoreElementType.ELLIPSE: {
      const radiusX = newBounds.width / 2
      const radiusY = newBounds.height / 2
      updates.radiusX = radiusX
      updates.radiusY = radiusY
      updates.width = newBounds.width
      updates.height = newBounds.height
      updates.properties = {
        ...element.properties,
        radiusX,
        radiusY,
        width: newBounds.width,
        height: newBounds.height,
      }
      break
    }
    case CoreElementType.TEXT:
    case CoreElementType.TEXT_LABEL: {
      // 🔧 修复：边界框缩放只影响边界框大小，不影响字体大小
      // 字体大小应该只通过 Typography 控件来控制
      updates.width = newBounds.width
      updates.height = newBounds.height
      updates.properties = {
        ...element.properties,
        width: newBounds.width,
        height: newBounds.height,
        // 不更新 fontSize，保持字体大小独立
      }

      _debug(`🔧 [ShapeRenderer] Text bounding box scaling applied (font size preserved):`, {
        elementType: element.type,
        handleId,
        originalBounds,
        newBounds,
        fontSizePreserved: true,
        centerOffset: { x: centerOffsetX, y: centerOffsetY },
      })

      break
    }
    case CoreElementType.IMAGE: {
      updates.width = newBounds.width
      updates.height = newBounds.height
      updates.properties = {
        ...element.properties,
        width: newBounds.width,
        height: newBounds.height,
      }
      break
    }
    default: {
      // 默认处理：更新宽度和高度
      updates.width = newBounds.width
      updates.height = newBounds.height
      updates.properties = {
        ...element.properties,
        width: newBounds.width,
        height: newBounds.height,
      }
      break
    }
  }

  return updates
}

/**
 * 渲染LINE元素的点位手柄
 */
function renderLinePointHandles(
  svgLayer: D3SVGGSelection,
  shapeData: ShapeModel,
  currentZoom: number,
  _onShapeSelected: (id: string, isMultiSelect: boolean) => void,
  isPanMode: boolean,
  _selectedIds: string[],
  _allShapes: ShapeModel[],
) {
  const props = shapeData.properties
  const start = props?.start as Point | undefined
  const end = props?.end as Point | undefined

  if (!start || !end) {
    _warn(`[renderLinePointHandles] Line ${shapeData.id} missing start or end points`)
    return
  }

  // 计算绝对坐标
  const startAbsolute = {
    x: shapeData.position.x + start.x,
    y: shapeData.position.y + start.y,
  }
  const endAbsolute = {
    x: shapeData.position.x + end.x,
    y: shapeData.position.y + end.y,
  }

  // 创建选择组
  const selectionGroup = svgLayer.append('g')
    .attr('class', 'selection-group line-point-handles')
    .attr('data-shape-id', shapeData.id)

  // 手柄样式
  const handleSize = 8 / currentZoom
  const handleStrokeWidth = 1.5 / currentZoom

  // 渲染起点手柄
  const startHandle = selectionGroup.append('circle')
    .attr('class', 'point-handle start-handle')
    .attr('cx', startAbsolute.x)
    .attr('cy', startAbsolute.y)
    .attr('r', handleSize / 2)
    .attr('fill', '#007bff')
    .attr('stroke', '#ffffff')
    .attr('stroke-width', handleStrokeWidth)
    .style('cursor', isPanMode ? 'grab' : 'move')

  // 渲染终点手柄
  const endHandle = selectionGroup.append('circle')
    .attr('class', 'point-handle end-handle')
    .attr('cx', endAbsolute.x)
    .attr('cy', endAbsolute.y)
    .attr('r', handleSize / 2)
    .attr('fill', '#007bff')
    .attr('stroke', '#ffffff')
    .attr('stroke-width', handleStrokeWidth)
    .style('cursor', isPanMode ? 'grab' : 'move')

  // 添加拖拽行为（如果不是平移模式）
  if (!isPanMode) {
    // 创建拖拽行为
    const dragBehavior = d3.drag<SVGCircleElement, unknown>()
      .on('start', function (event: D3DragEvent) {
        // 阻止事件冒泡
        event.sourceEvent.stopPropagation()
        // 高亮显示正在拖拽的手柄
        d3.select(this).attr('fill', '#ff6b35')
      })
      .on('drag', function (event: D3DragEvent) {
        const handle = d3.select(this)
        const isStartHandle = handle.classed('start-handle')

        // 更新手柄位置
        handle.attr('cx', event.x).attr('cy', event.y)

        // 计算新的相对坐标
        const newRelativePoint = {
          x: event.x - shapeData.position.x,
          y: event.y - shapeData.position.y,
        }

        _debug(`[renderLinePointHandles] Dragging ${isStartHandle ? 'start' : 'end'} handle:`, {
          absolutePosition: { x: event.x, y: event.y },
          shapePosition: shapeData.position,
          newRelativePoint,
        })

        // 更新线条的显示（实时预览）
        const shapeGroup = svgLayer.select(`g.shape-group[data-shape-id="${shapeData.id}"]`)
        if (!shapeGroup.empty()) {
          const currentStart = isStartHandle ? newRelativePoint : start
          const currentEnd = isStartHandle ? end : newRelativePoint

          // 更新线条路径（使用相对坐标，因为线条在shape-group中）
          shapeGroup.select('line')
            .attr('x1', currentStart.x)
            .attr('y1', currentStart.y)
            .attr('x2', currentEnd.x)
            .attr('y2', currentEnd.y)
        }
      })
      .on('end', function (event: D3DragEvent) {
        const handle = d3.select(this)
        const isStartHandle = handle.classed('start-handle')

        // 恢复手柄颜色
        handle.attr('fill', '#007bff')

        // 计算新的相对坐标
        const newRelativePoint = new PointClass(
          event.x - shapeData.position.x,
          event.y - shapeData.position.y,
        )

        // 更新形状数据 - 只更新properties中的start/end
        const updatedProperties = { ...shapeData.properties }
        const updatedShape = { ...shapeData }

        if (isStartHandle) {
          updatedProperties.start = newRelativePoint
        }
        else {
          updatedProperties.end = newRelativePoint
        }

        updatedShape.properties = updatedProperties

        // 🔧 直接更新Zustand store，确保立即同步
        if (typeof window !== 'undefined' && (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__) {
          const shapesStore = (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__
          if (shapesStore) {
            const state = shapesStore.getState()
            const newShapes = state.shapes.map((s: ShapeModel) => s.id === shapeData.id ? updatedShape : s)

            _debug(`[renderLinePointHandles] Directly updating store for shape ${shapeData.id}:`, {
              isStartHandle,
              newRelativePoint: newRelativePoint.toJson(),
              updatedShape: {
                id: updatedShape.id,
                properties: {
                  start: updatedShape.properties?.start,
                  end: updatedShape.properties?.end,
                },
              },
            })

            shapesStore.setState({
              ...state,
              shapes: newShapes,
              selectedShapeIds: state.selectedShapeIds, // 保持选中状态
            })

            // 🔧 触发ComputeRequest事件以重新渲染
            setTimeout(() => {
              const eventBus = getEventBus()
              eventBus.publish({
                type: AppEventType.ComputeRequest,
                timestamp: Date.now(),
                payload: {
                  shapeId: shapeData.id,
                  operation: 'all',
                  source: 'LinePointHandleDrag',
                },
              })
            }, 50)
          }
        }
        else {
          // 回退到事件发布方式
          _warn('[renderLinePointHandles] Store not available, using event fallback')
          const eventBus = getEventBus()
          eventBus.publish({
            type: AppEventType.ShapeEditRequest,
            timestamp: Date.now(),
            payload: {
              shapeId: shapeData.id,
              changes: {
                properties: updatedProperties,
                start: isStartHandle ? newRelativePoint : undefined,
                end: isStartHandle ? undefined : newRelativePoint,
              },
              source: 'LinePointHandles',
            },
          })
        }

        _debug(`[renderLinePointHandles] ${isStartHandle ? 'Start' : 'End'} point updated for line ${shapeData.id}`)
      })

    // 应用拖拽行为到手柄
    startHandle.call(dragBehavior)
    endHandle.call(dragBehavior)

    _debug(`[renderLinePointHandles] Point handles with drag behavior rendered for line ${shapeData.id}`)
  }
}

/**
 * 渲染POLYLINE元素的点位手柄
 */
function renderPolylinePointHandles(
  svgLayer: D3SVGGSelection,
  shapeData: ShapeModel,
  currentZoom: number,
  _onShapeSelected: (id: string, isMultiSelect: boolean) => void,
  isPanMode: boolean,
  _selectedIds: string[],
  _allShapes: ShapeModel[],
) {
  const props = shapeData.properties
  const points = props?.points as Point[] | undefined

  if (!points || points.length < 2) {
    _warn(`[renderPolylinePointHandles] Polyline ${shapeData.id} missing points or insufficient points`)
    return
  }

  // 创建选择组
  const selectionGroup = svgLayer.append('g')
    .attr('class', 'selection-group polyline-point-handles')
    .attr('data-shape-id', shapeData.id)

  // 手柄样式
  const handleSize = 8 / currentZoom
  const handleStrokeWidth = 1.5 / currentZoom

  // 为每个点渲染手柄
  points.forEach((point, index) => {
    // 计算绝对坐标
    const absolutePoint = {
      x: shapeData.position.x + point.x,
      y: shapeData.position.y + point.y,
    }

    // 渲染点位手柄
    const handle = selectionGroup.append('circle')
      .attr('class', `point-handle point-${index}`)
      .attr('cx', absolutePoint.x)
      .attr('cy', absolutePoint.y)
      .attr('r', handleSize / 2)
      .attr('fill', '#007bff')
      .attr('stroke', '#ffffff')
      .attr('stroke-width', handleStrokeWidth)
      .style('cursor', isPanMode ? 'grab' : 'move')

    // 添加拖拽行为（如果不是平移模式）
    if (!isPanMode) {
      const dragBehavior = d3.drag<SVGCircleElement, unknown>()
        .on('start', function (event: D3DragEvent) {
          event.sourceEvent.stopPropagation()
          d3.select(this).attr('fill', '#0056b3') // 深蓝色高亮
        })
        .on('drag', function (event: D3DragEvent) {
          const handle = d3.select(this)

          // 更新手柄位置
          handle.attr('cx', event.x).attr('cy', event.y)

          // 计算新的相对坐标
          const newRelativePoint = new PointClass(
            event.x - shapeData.position.x,
            event.y - shapeData.position.y,
          )

          _debug(`[renderPolylinePointHandles] Dragging point ${index}:`, {
            absolutePosition: { x: event.x, y: event.y },
            shapePosition: shapeData.position,
            newRelativePoint: newRelativePoint.toJson(),
          })

          // 更新polyline的显示（实时预览）
          const shapeGroup = svgLayer.select(`g.shape-group[data-shape-id="${shapeData.id}"]`)
          if (!shapeGroup.empty()) {
            // 创建更新后的点数组
            const updatedPoints = [...points]
            updatedPoints[index] = newRelativePoint

            // 更新polyline路径
            const pointsString = updatedPoints.map(p => `${p.x},${p.y}`).join(' ')
            shapeGroup.select('polyline')
              .attr('points', pointsString)
          }
        })
        .on('end', function (event: D3DragEvent) {
          const handle = d3.select(this)

          // 恢复手柄颜色
          handle.attr('fill', '#007bff')

          // 计算新的相对坐标
          const newRelativePoint = new PointClass(
            event.x - shapeData.position.x,
            event.y - shapeData.position.y,
          )

          // 更新形状数据 - 更新points数组
          const updatedPoints = [...points]
          updatedPoints[index] = newRelativePoint

          const updatedProperties = { ...shapeData.properties }
          updatedProperties.points = updatedPoints

          const updatedShape = { ...shapeData }
          updatedShape.properties = updatedProperties

          // 🔧 直接更新Zustand store，确保立即同步
          if (typeof window !== 'undefined' && (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__) {
            const shapesStore = (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__
            if (shapesStore) {
              const state = shapesStore.getState()
              const newShapes = state.shapes.map((s: ShapeModel) => s.id === shapeData.id ? updatedShape : s)

              _debug(`[renderPolylinePointHandles] Directly updating store for polyline ${shapeData.id}, point ${index}:`, {
                newRelativePoint,
                updatedPoints,
              })

              shapesStore.setState({
                ...state,
                shapes: newShapes,
                selectedShapeIds: state.selectedShapeIds, // 保持选中状态
              })

              // 🔧 触发ComputeRequest事件以重新渲染
              setTimeout(() => {
                const eventBus = getEventBus()
                eventBus.publish({
                  type: AppEventType.ComputeRequest,
                  timestamp: Date.now(),
                  payload: {
                    shapeId: shapeData.id,
                    operation: 'all',
                    source: 'PolylinePointHandleDrag',
                  },
                })
              }, 50)
            }
          }
          else {
            // 回退到事件发布方式
            _warn('[renderPolylinePointHandles] Store not available, using event fallback')
            const eventBus = getEventBus()
            eventBus.publish({
              type: AppEventType.ShapeEditRequest,
              timestamp: Date.now(),
              payload: {
                shapeId: shapeData.id,
                changes: {
                  properties: updatedProperties,
                },
                source: 'PolylinePointHandles',
              },
            })
          }

          _debug(`[renderPolylinePointHandles] Point ${index} updated for polyline ${shapeData.id}`)
        })

      // 应用拖拽行为到手柄
      handle.call(dragBehavior)
    }
  })

  _debug(`[renderPolylinePointHandles] ${points.length} point handles rendered for polyline ${shapeData.id}`)
}

/**
 * 渲染ARC元素的点位手柄
 */
function renderArcPointHandles(
  svgLayer: D3SVGGSelection,
  shapeData: ShapeModel,
  currentZoom: number,
  _onShapeSelected: (id: string, isMultiSelect: boolean) => void,
  isPanMode: boolean,
  _selectedIds: string[],
  _allShapes: ShapeModel[],
) {
  const props = shapeData.properties
  const radius = props?.radius as number | undefined
  const startAngle = props?.startAngle as number | undefined
  const endAngle = props?.endAngle as number | undefined
  const counterClockwise = props?.counterClockwise as boolean | undefined

  if (typeof radius !== 'number' || typeof startAngle !== 'number' || typeof endAngle !== 'number') {
    _warn(`[renderArcPointHandles] Arc ${shapeData.id} missing required properties:`, { radius, startAngle, endAngle })
    return
  }

  // 创建选择组
  const selectionGroup = svgLayer.append('g')
    .attr('class', 'selection-group arc-point-handles')
    .attr('data-shape-id', shapeData.id)

  // 手柄样式
  const handleSize = 8 / currentZoom
  const handleStrokeWidth = 1.5 / currentZoom

  // 中心点（position就是中心点）
  const center = {
    x: shapeData.position.x,
    y: shapeData.position.y,
  }

  // 计算起始点和结束点的绝对坐标
  const startRad = (startAngle * Math.PI) / 180
  const endRad = (endAngle * Math.PI) / 180

  const startPoint = {
    x: center.x + radius * Math.cos(startRad),
    y: center.y + radius * Math.sin(startRad),
  }

  const endPoint = {
    x: center.x + radius * Math.cos(endRad),
    y: center.y + radius * Math.sin(endRad),
  }

  // 1. 渲染中心点手柄
  const centerHandle = selectionGroup.append('circle')
    .attr('class', 'point-handle center-handle')
    .attr('cx', center.x)
    .attr('cy', center.y)
    .attr('r', handleSize / 2)
    .attr('fill', '#007bff') // 蓝色
    .attr('stroke', '#ffffff')
    .attr('stroke-width', handleStrokeWidth)
    .style('cursor', isPanMode ? 'grab' : 'move')

  // 2. 渲染起始点手柄
  const startHandle = selectionGroup.append('circle')
    .attr('class', 'point-handle start-handle')
    .attr('cx', startPoint.x)
    .attr('cy', startPoint.y)
    .attr('r', handleSize / 2)
    .attr('fill', '#007bff') // 蓝色
    .attr('stroke', '#ffffff')
    .attr('stroke-width', handleStrokeWidth)
    .style('cursor', isPanMode ? 'grab' : 'move')

  // 3. 渲染结束点手柄
  const endHandle = selectionGroup.append('circle')
    .attr('class', 'point-handle end-handle')
    .attr('cx', endPoint.x)
    .attr('cy', endPoint.y)
    .attr('r', handleSize / 2)
    .attr('fill', '#007bff') // 蓝色
    .attr('stroke', '#ffffff')
    .attr('stroke-width', handleStrokeWidth)
    .style('cursor', isPanMode ? 'grab' : 'move')

  // 4. 渲染半径缩放手柄（在圆弧中点位置）
  const midAngle = (startAngle + endAngle) / 2
  const midRad = (midAngle * Math.PI) / 180
  const radiusPoint = {
    x: center.x + radius * Math.cos(midRad),
    y: center.y + radius * Math.sin(midRad),
  }

  const radiusHandle = selectionGroup.append('circle')
    .attr('class', 'point-handle radius-handle')
    .attr('cx', radiusPoint.x)
    .attr('cy', radiusPoint.y)
    .attr('r', handleSize / 2)
    .attr('fill', '#007bff') // 蓝色
    .attr('stroke', '#ffffff')
    .attr('stroke-width', handleStrokeWidth)
    .style('cursor', isPanMode ? 'grab' : 'move')

  // 添加连接线（可选，帮助用户理解结构）
  // 起始点连接线
  selectionGroup.append('line')
    .attr('class', 'arc-guide-line start-line')
    .attr('x1', center.x)
    .attr('y1', center.y)
    .attr('x2', startPoint.x)
    .attr('y2', startPoint.y)
    .attr('stroke', '#007bff') // 蓝色
    .attr('stroke-width', 1 / currentZoom)
    .attr('stroke-dasharray', `${3 / currentZoom},${3 / currentZoom}`)
    .attr('opacity', 0.5)

  // 结束点连接线
  selectionGroup.append('line')
    .attr('class', 'arc-guide-line end-line')
    .attr('x1', center.x)
    .attr('y1', center.y)
    .attr('x2', endPoint.x)
    .attr('y2', endPoint.y)
    .attr('stroke', '#007bff') // 蓝色
    .attr('stroke-width', 1 / currentZoom)
    .attr('stroke-dasharray', `${3 / currentZoom},${3 / currentZoom}`)
    .attr('opacity', 0.5)

  // 半径控制连接线
  selectionGroup.append('line')
    .attr('class', 'arc-guide-line radius-line')
    .attr('x1', center.x)
    .attr('y1', center.y)
    .attr('x2', radiusPoint.x)
    .attr('y2', radiusPoint.y)
    .attr('stroke', '#007bff') // 蓝色
    .attr('stroke-width', 2 / currentZoom) // 稍微粗一点，用于区分半径线
    .attr('stroke-dasharray', `${5 / currentZoom},${3 / currentZoom}`)
    .attr('opacity', 0.7)

  // 添加拖拽行为（如果不是平移模式）
  if (!isPanMode) {
    // 中心点拖拽行为
    const centerDragBehavior = d3.drag<SVGCircleElement, unknown>()
      .on('start', function (event: D3DragEvent) {
        event.sourceEvent.stopPropagation()
        d3.select(this).attr('fill', '#0056b3') // 深蓝色高亮
      })
      .on('drag', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 更新手柄位置
        handle.attr('cx', event.x).attr('cy', event.y)

        // 更新连接线
        selectionGroup.selectAll('.arc-guide-line')
          .attr('x1', event.x)
          .attr('y1', event.y)

        // 重新计算起始点、结束点和半径点（使用当前角度值）
        const newStartPoint = {
          x: event.x + radius * Math.cos(startRad),
          y: event.y + radius * Math.sin(startRad),
        }
        const newEndPoint = {
          x: event.x + radius * Math.cos(endRad),
          y: event.y + radius * Math.sin(endRad),
        }
        const newRadiusPoint = {
          x: event.x + radius * Math.cos(midRad),
          y: event.y + radius * Math.sin(midRad),
        }

        // 更新所有手柄位置
        startHandle.attr('cx', newStartPoint.x).attr('cy', newStartPoint.y)
        endHandle.attr('cx', newEndPoint.x).attr('cy', newEndPoint.y)
        radiusHandle.attr('cx', newRadiusPoint.x).attr('cy', newRadiusPoint.y)

        // 更新连接线终点
        selectionGroup.select('.start-line')
          .attr('x2', newStartPoint.x)
          .attr('y2', newStartPoint.y)
        selectionGroup.select('.end-line')
          .attr('x2', newEndPoint.x)
          .attr('y2', newEndPoint.y)
        selectionGroup.select('.radius-line')
          .attr('x2', newRadiusPoint.x)
          .attr('y2', newRadiusPoint.y)

        _debug(`[renderArcPointHandles] Dragging center:`, {
          newCenter: { x: event.x, y: event.y },
          newStartPoint,
          newEndPoint,
        })

        // 更新arc的显示（实时预览）
        const shapeGroup = svgLayer.select(`g.shape-group[data-shape-id="${shapeData.id}"]`)
        if (!shapeGroup.empty()) {
          // 重新生成arc路径
          const newPathData = generateArcPath(
            { x: 0, y: 0 }, // 相对于新的group位置
            radius,
            startAngle,
            endAngle,
            counterClockwise === true,
          )

          shapeGroup.select('path')
            .attr('d', newPathData)

          // 更新group的transform
          shapeGroup.attr('transform', `translate(${event.x}, ${event.y})`)
        }
      })
      .on('end', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 恢复手柄颜色
        handle.attr('fill', '#007bff')

        // 更新形状数据 - 更新position（中心点）
        const newPosition = {
          x: event.x,
          y: event.y,
          z: typeof shapeData.position.z === 'number' ? shapeData.position.z : 0,
        }

        // 🔧 关键修复：重新生成pathData以确保正确渲染
        const updatedProperties = { ...shapeData.properties }
        const newPathData = generateArcPath(
          { x: 0, y: 0 },
          radius,
          startAngle,
          endAngle,
          counterClockwise === true,
        )
        updatedProperties.pathData = newPathData

        const updatedShape = { ...shapeData }
        updatedShape.position = newPosition
        updatedShape.properties = updatedProperties

        // 🔧 直接更新Zustand store，确保立即同步
        if (typeof window !== 'undefined' && (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__) {
          const shapesStore = (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__
          if (shapesStore) {
            const state = shapesStore.getState()
            const newShapes = state.shapes.map((s: ShapeModel) => s.id === shapeData.id ? updatedShape : s)

            _debug(`[renderArcPointHandles] Directly updating store for arc ${shapeData.id} center:`, {
              newPosition,
            })

            shapesStore.setState({
              ...state,
              shapes: newShapes,
              selectedShapeIds: state.selectedShapeIds, // 保持选中状态
            })

            // 🔧 触发ComputeRequest事件以重新渲染
            setTimeout(() => {
              const eventBus = getEventBus()
              eventBus.publish({
                type: AppEventType.ComputeRequest,
                timestamp: Date.now(),
                payload: {
                  shapeId: shapeData.id,
                  operation: 'all',
                  source: 'ArcCenterHandleDrag',
                },
              })
            }, 50)
          }
        }
        else {
          // 回退到事件发布方式
          _warn('[renderArcPointHandles] Store not available, using event fallback')
          const eventBus = getEventBus()
          eventBus.publish({
            type: AppEventType.ShapeEditRequest,
            timestamp: Date.now(),
            payload: {
              shapeId: shapeData.id,
              changes: {
                position: newPosition,
                properties: updatedProperties,
              },
              source: 'ArcCenterHandles',
            },
          })
        }

        _debug(`[renderArcPointHandles] Center updated for arc ${shapeData.id}`)
      })

    centerHandle.call(centerDragBehavior)

    // 起始点拖拽行为
    const startDragBehavior = d3.drag<SVGCircleElement, unknown>()
      .on('start', function (event: D3DragEvent) {
        event.sourceEvent.stopPropagation()
        d3.select(this).attr('fill', '#0056b3') // 深蓝色高亮
      })
      .on('drag', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 更新手柄位置
        handle.attr('cx', event.x).attr('cy', event.y)

        // 计算新的起始角度
        const dx = event.x - center.x
        const dy = event.y - center.y
        const newStartAngle = (Math.atan2(dy, dx) * 180 / Math.PI + 360) % 360

        // 重新计算半径手柄位置（角度变化了）
        const newMidAngle = (newStartAngle + endAngle) / 2
        const newMidRad = (newMidAngle * Math.PI) / 180
        const newRadiusPoint = {
          x: center.x + radius * Math.cos(newMidRad),
          y: center.y + radius * Math.sin(newMidRad),
        }

        // 更新半径手柄位置
        radiusHandle.attr('cx', newRadiusPoint.x).attr('cy', newRadiusPoint.y)

        // 更新连接线
        selectionGroup.select('.start-line')
          .attr('x2', event.x)
          .attr('y2', event.y)
        selectionGroup.select('.radius-line')
          .attr('x2', newRadiusPoint.x)
          .attr('y2', newRadiusPoint.y)

        _debug(`[renderArcPointHandles] Dragging start point:`, {
          newStartAngle,
          position: { x: event.x, y: event.y },
        })

        // 更新arc的显示（实时预览）
        const shapeGroup = svgLayer.select(`g.shape-group[data-shape-id="${shapeData.id}"]`)
        if (!shapeGroup.empty()) {
          const newPathData = generateArcPath(
            { x: 0, y: 0 },
            radius,
            newStartAngle,
            endAngle,
            counterClockwise === true,
          )

          shapeGroup.select('path')
            .attr('d', newPathData)
        }
      })
      .on('end', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 恢复手柄颜色
        handle.attr('fill', '#007bff') // 蓝色

        // 计算新的起始角度
        const dx = event.x - center.x
        const dy = event.y - center.y
        const newStartAngle = (Math.atan2(dy, dx) * 180 / Math.PI + 360) % 360

        // 更新形状数据
        const updatedProperties = { ...shapeData.properties }
        updatedProperties.startAngle = newStartAngle

        // 🔧 关键修复：重新生成pathData以覆盖旧的预计算路径
        const newPathData = generateArcPath(
          { x: 0, y: 0 },
          radius,
          newStartAngle,
          endAngle,
          counterClockwise === true,
        )
        updatedProperties.pathData = newPathData

        const updatedShape = { ...shapeData }
        updatedShape.properties = updatedProperties

        // 🔧 直接更新Zustand store
        if (typeof window !== 'undefined' && (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__) {
          const shapesStore = (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__
          if (shapesStore) {
            const state = shapesStore.getState()
            const newShapes = state.shapes.map((s: ShapeModel) => s.id === shapeData.id ? updatedShape : s)

            _debug(`[renderArcPointHandles] Directly updating store for arc ${shapeData.id} start angle:`, {
              newStartAngle,
            })

            shapesStore.setState({
              ...state,
              shapes: newShapes,
              selectedShapeIds: state.selectedShapeIds,
            })

            // 🔧 触发ComputeRequest事件以重新渲染
            setTimeout(() => {
              const eventBus = getEventBus()
              eventBus.publish({
                type: AppEventType.ComputeRequest,
                timestamp: Date.now(),
                payload: {
                  shapeId: shapeData.id,
                  operation: 'all',
                  source: 'ArcStartHandleDrag',
                },
              })
            }, 50)
          }
        }

        _debug(`[renderArcPointHandles] Start angle updated for arc ${shapeData.id}`)
      })

    // 结束点拖拽行为
    const endDragBehavior = d3.drag<SVGCircleElement, unknown>()
      .on('start', function (event: D3DragEvent) {
        event.sourceEvent.stopPropagation()
        d3.select(this).attr('fill', '#0056b3') // 深蓝色高亮
      })
      .on('drag', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 更新手柄位置
        handle.attr('cx', event.x).attr('cy', event.y)

        // 计算新的结束角度
        const dx = event.x - center.x
        const dy = event.y - center.y
        const newEndAngle = (Math.atan2(dy, dx) * 180 / Math.PI + 360) % 360

        // 重新计算半径手柄位置（角度变化了）
        const newMidAngle = (startAngle + newEndAngle) / 2
        const newMidRad = (newMidAngle * Math.PI) / 180
        const newRadiusPoint = {
          x: center.x + radius * Math.cos(newMidRad),
          y: center.y + radius * Math.sin(newMidRad),
        }

        // 更新半径手柄位置
        radiusHandle.attr('cx', newRadiusPoint.x).attr('cy', newRadiusPoint.y)

        // 更新连接线
        selectionGroup.select('.end-line')
          .attr('x2', event.x)
          .attr('y2', event.y)
        selectionGroup.select('.radius-line')
          .attr('x2', newRadiusPoint.x)
          .attr('y2', newRadiusPoint.y)

        _debug(`[renderArcPointHandles] Dragging end point:`, {
          newEndAngle,
          position: { x: event.x, y: event.y },
        })

        // 更新arc的显示（实时预览）
        const shapeGroup = svgLayer.select(`g.shape-group[data-shape-id="${shapeData.id}"]`)
        if (!shapeGroup.empty()) {
          const newPathData = generateArcPath(
            { x: 0, y: 0 },
            radius,
            startAngle,
            newEndAngle,
            counterClockwise === true,
          )

          shapeGroup.select('path')
            .attr('d', newPathData)
        }
      })
      .on('end', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 恢复手柄颜色
        handle.attr('fill', '#007bff') // 蓝色

        // 计算新的结束角度
        const dx = event.x - center.x
        const dy = event.y - center.y
        const newEndAngle = (Math.atan2(dy, dx) * 180 / Math.PI + 360) % 360

        // 更新形状数据
        const updatedProperties = { ...shapeData.properties }
        updatedProperties.endAngle = newEndAngle

        // 🔧 关键修复：重新生成pathData以覆盖旧的预计算路径
        const newPathData = generateArcPath(
          { x: 0, y: 0 },
          radius,
          startAngle,
          newEndAngle,
          counterClockwise === true,
        )
        updatedProperties.pathData = newPathData

        const updatedShape = { ...shapeData }
        updatedShape.properties = updatedProperties

        // 🔧 直接更新Zustand store
        if (typeof window !== 'undefined' && (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__) {
          const shapesStore = (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__
          if (shapesStore) {
            const state = shapesStore.getState()
            const newShapes = state.shapes.map((s: ShapeModel) => s.id === shapeData.id ? updatedShape : s)

            _debug(`[renderArcPointHandles] Directly updating store for arc ${shapeData.id} end angle:`, {
              newEndAngle,
            })

            shapesStore.setState({
              ...state,
              shapes: newShapes,
              selectedShapeIds: state.selectedShapeIds,
            })

            // 🔧 触发ComputeRequest事件以重新渲染
            setTimeout(() => {
              const eventBus = getEventBus()
              eventBus.publish({
                type: AppEventType.ComputeRequest,
                timestamp: Date.now(),
                payload: {
                  shapeId: shapeData.id,
                  operation: 'all',
                  source: 'ArcEndHandleDrag',
                },
              })
            }, 50)
          }
        }

        _debug(`[renderArcPointHandles] End angle updated for arc ${shapeData.id}`)
      })

    // 半径拖拽行为
    const radiusDragBehavior = d3.drag<SVGCircleElement, unknown>()
      .on('start', function (event: D3DragEvent) {
        event.sourceEvent.stopPropagation()
        d3.select(this).attr('fill', '#0056b3') // 深蓝色高亮
      })
      .on('drag', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 计算新的半径（从中心点到鼠标位置的距离）
        const dx = event.x - center.x
        const dy = event.y - center.y
        const newRadius = Math.sqrt(dx * dx + dy * dy)

        // 限制最小半径
        const minRadius = 10
        const clampedRadius = Math.max(minRadius, newRadius)

        // 重新计算所有点的位置
        const newStartPoint = {
          x: center.x + clampedRadius * Math.cos(startRad),
          y: center.y + clampedRadius * Math.sin(startRad),
        }
        const newEndPoint = {
          x: center.x + clampedRadius * Math.cos(endRad),
          y: center.y + clampedRadius * Math.sin(endRad),
        }
        const newRadiusPoint = {
          x: center.x + clampedRadius * Math.cos(midRad),
          y: center.y + clampedRadius * Math.sin(midRad),
        }

        // 更新所有手柄位置
        startHandle.attr('cx', newStartPoint.x).attr('cy', newStartPoint.y)
        endHandle.attr('cx', newEndPoint.x).attr('cy', newEndPoint.y)
        handle.attr('cx', newRadiusPoint.x).attr('cy', newRadiusPoint.y)

        // 更新连接线
        selectionGroup.select('.start-line')
          .attr('x2', newStartPoint.x)
          .attr('y2', newStartPoint.y)
        selectionGroup.select('.end-line')
          .attr('x2', newEndPoint.x)
          .attr('y2', newEndPoint.y)
        selectionGroup.select('.radius-line')
          .attr('x2', newRadiusPoint.x)
          .attr('y2', newRadiusPoint.y)

        console.log(`[renderArcPointHandles] Dragging radius:`, {
          newRadius: clampedRadius,
          position: { x: event.x, y: event.y },
        })

        // 更新arc的显示（实时预览）
        const shapeGroup = svgLayer.select(`g.shape-group[data-shape-id="${shapeData.id}"]`)
        if (!shapeGroup.empty()) {
          const newPathData = generateArcPath(
            { x: 0, y: 0 },
            clampedRadius,
            startAngle,
            endAngle,
            counterClockwise === true,
          )

          shapeGroup.select('path')
            .attr('d', newPathData)
        }
      })
      .on('end', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 恢复手柄颜色
        handle.attr('fill', '#007bff') // 蓝色

        // 计算新的半径
        const dx = event.x - center.x
        const dy = event.y - center.y
        const newRadius = Math.sqrt(dx * dx + dy * dy)

        // 限制最小半径
        const minRadius = 10
        const clampedRadius = Math.max(minRadius, newRadius)

        // 更新形状数据
        const updatedProperties = { ...shapeData.properties }
        updatedProperties.radius = clampedRadius

        // 🔧 关键修复：重新生成pathData以覆盖旧的预计算路径
        const newPathData = generateArcPath(
          { x: 0, y: 0 },
          clampedRadius,
          startAngle,
          endAngle,
          counterClockwise === true,
        )
        updatedProperties.pathData = newPathData

        const updatedShape = { ...shapeData }
        updatedShape.properties = updatedProperties

        // 🔧 直接更新Zustand store
        if (typeof window !== 'undefined' && (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__) {
          const shapesStore = (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__
          if (shapesStore) {
            const state = shapesStore.getState()
            const newShapes = state.shapes.map((s: ShapeModel) => s.id === shapeData.id ? updatedShape : s)

            console.log(`[renderArcPointHandles] Directly updating store for arc ${shapeData.id} radius:`, {
              newRadius: clampedRadius,
            })

            shapesStore.setState({
              ...state,
              shapes: newShapes,
              selectedShapeIds: state.selectedShapeIds,
            })

            // 🔧 触发ComputeRequest事件以重新渲染
            setTimeout(() => {
              const eventBus = getEventBus()
              eventBus.publish({
                type: AppEventType.ComputeRequest,
                timestamp: Date.now(),
                payload: {
                  shapeId: shapeData.id,
                  operation: 'all',
                  source: 'ArcRadiusHandleDrag',
                },
              })
            }, 50)
          }
        }

        console.log(`[renderArcPointHandles] Radius updated for arc ${shapeData.id}`)
      })

    startHandle.call(startDragBehavior)
    endHandle.call(endDragBehavior)
    radiusHandle.call(radiusDragBehavior)
  }

  console.log(`[renderArcPointHandles] Arc point handles rendered for arc ${shapeData.id}:`, {
    center: { x: center.x, y: center.y },
    radius,
    startAngle,
    endAngle,
    counterClockwise,
    startPoint: { x: startPoint.x, y: startPoint.y },
    endPoint: { x: endPoint.x, y: endPoint.y },
  })
}

// 🔧 新增：QUADRATIC元素点位手柄渲染函数
function renderQuadraticPointHandles(
  svgLayer: D3SVGGSelection,
  selectionGroup: D3SVGGSelection,
  shapeData: ShapeModel,
  currentZoom: number,
  isPanMode: boolean,
  eventBus: AppEventBusImpl,
) {
  const props = shapeData.properties
  const startPoint = props?.start as Point | undefined
  const controlPoint = props?.control as Point | undefined
  const endPoint = props?.end as Point | undefined

  if (!startPoint || !controlPoint || !endPoint) {
    _warn(`[renderQuadraticPointHandles] Quadratic ${shapeData.id} missing required points:`, { startPoint, controlPoint, endPoint })
    return
  }

  // 计算绝对坐标（相对于元素position）
  const center = shapeData.position !== undefined && shapeData.position !== null ? shapeData.position : { x: 0, y: 0 }
  const absoluteStart = {
    x: center.x + startPoint.x,
    y: center.y + startPoint.y,
  }
  const absoluteControl = {
    x: center.x + controlPoint.x,
    y: center.y + controlPoint.y,
  }
  const absoluteEnd = {
    x: center.x + endPoint.x,
    y: center.y + endPoint.y,
  }

  const handleSize = 8 / currentZoom
  const handleStrokeWidth = 1.5 / currentZoom

  // 1. 渲染起始点手柄
  const startHandle = selectionGroup.append('circle')
    .attr('class', 'point-handle start-handle')
    .attr('cx', absoluteStart.x)
    .attr('cy', absoluteStart.y)
    .attr('r', handleSize / 2)
    .attr('fill', '#007bff') // 蓝色
    .attr('stroke', '#ffffff')
    .attr('stroke-width', handleStrokeWidth)
    .style('cursor', isPanMode ? 'grab' : 'move')

  // 2. 渲染控制点手柄
  const controlHandle = selectionGroup.append('circle')
    .attr('class', 'point-handle control-handle')
    .attr('cx', absoluteControl.x)
    .attr('cy', absoluteControl.y)
    .attr('r', handleSize / 2)
    .attr('fill', '#007bff') // 蓝色
    .attr('stroke', '#ffffff')
    .attr('stroke-width', handleStrokeWidth)
    .style('cursor', isPanMode ? 'grab' : 'move')

  // 3. 渲染结束点手柄
  const endHandle = selectionGroup.append('circle')
    .attr('class', 'point-handle end-handle')
    .attr('cx', absoluteEnd.x)
    .attr('cy', absoluteEnd.y)
    .attr('r', handleSize / 2)
    .attr('fill', '#007bff') // 蓝色
    .attr('stroke', '#ffffff')
    .attr('stroke-width', handleStrokeWidth)
    .style('cursor', isPanMode ? 'grab' : 'move')

  // 添加连接线（帮助用户理解贝塞尔曲线结构）
  // 起始点到控制点的连接线
  selectionGroup.append('line')
    .attr('class', 'quadratic-guide-line start-control-line')
    .attr('x1', absoluteStart.x)
    .attr('y1', absoluteStart.y)
    .attr('x2', absoluteControl.x)
    .attr('y2', absoluteControl.y)
    .attr('stroke', '#007bff') // 蓝色
    .attr('stroke-width', 1 / currentZoom)
    .attr('stroke-dasharray', `${3 / currentZoom},${3 / currentZoom}`)
    .attr('opacity', 0.5)

  // 控制点到结束点的连接线
  selectionGroup.append('line')
    .attr('class', 'quadratic-guide-line control-end-line')
    .attr('x1', absoluteControl.x)
    .attr('y1', absoluteControl.y)
    .attr('x2', absoluteEnd.x)
    .attr('y2', absoluteEnd.y)
    .attr('stroke', '#007bff') // 蓝色
    .attr('stroke-width', 1 / currentZoom)
    .attr('stroke-dasharray', `${3 / currentZoom},${3 / currentZoom}`)
    .attr('opacity', 0.5)

  // 添加拖拽行为（如果不是平移模式）
  if (!isPanMode) {
    // 起始点拖拽行为
    const startDragBehavior = d3.drag<SVGCircleElement, unknown>()
      .on('start', function (event: D3DragEvent) {
        event.sourceEvent.stopPropagation()
        d3.select(this).attr('fill', '#0056b3') // 深蓝色高亮
      })
      .on('drag', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 更新手柄位置
        handle.attr('cx', event.x).attr('cy', event.y)

        // 更新连接线
        selectionGroup.select('.start-control-line')
          .attr('x1', event.x)
          .attr('y1', event.y)

        // 计算新的相对坐标
        const newRelativePoint = {
          x: event.x - center.x,
          y: event.y - center.y,
          z: typeof startPoint.z === 'number' ? startPoint.z : 0,
        }

        console.log(`[renderQuadraticPointHandles] Dragging start point:`, {
          newPosition: { x: event.x, y: event.y },
          newRelativePoint,
        })

        // 更新quadratic的显示（实时预览）
        const shapeGroup = svgLayer.select(`g.shape-group[data-shape-id="${shapeData.id}"]`)
        if (!shapeGroup.empty()) {
          // 重新生成quadratic路径
          const newPathData = `M ${newRelativePoint.x},${newRelativePoint.y} Q ${controlPoint.x},${controlPoint.y} ${endPoint.x},${endPoint.y}`

          shapeGroup.select('path')
            .attr('d', newPathData)
        }
      })
      .on('end', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 恢复手柄颜色
        handle.attr('fill', '#007bff') // 蓝色

        // 计算新的相对坐标
        const newRelativePoint = {
          x: event.x - center.x,
          y: event.y - center.y,
          z: typeof startPoint.z === 'number' ? startPoint.z : 0,
        }

        // 更新形状数据
        const updatedProperties = { ...shapeData.properties }
        updatedProperties.start = newRelativePoint

        const updatedShape = { ...shapeData }
        updatedShape.properties = updatedProperties

        // 🔧 直接更新Zustand store
        if (typeof window !== 'undefined' && (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__) {
          const shapesStore = (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__
          if (shapesStore) {
            const state = shapesStore.getState()
            const newShapes = state.shapes.map((s: ShapeModel) => s.id === shapeData.id ? updatedShape : s)

            console.log(`[renderQuadraticPointHandles] Directly updating store for quadratic ${shapeData.id} start point:`, {
              newRelativePoint,
            })

            shapesStore.setState({
              ...state,
              shapes: newShapes,
              selectedShapeIds: state.selectedShapeIds,
            })

            // 🔧 触发ComputeRequest事件以重新渲染
            setTimeout(() => {
              eventBus.publish({
                type: AppEventType.ComputeRequest,
                timestamp: Date.now(),
                payload: {
                  shapeId: shapeData.id,
                  operation: 'all',
                  source: 'QuadraticStartHandleDrag',
                },
              })
            }, 50)
          }
        }

        console.log(`[renderQuadraticPointHandles] Start point updated for quadratic ${shapeData.id}`)
      })

    // 控制点拖拽行为
    const controlDragBehavior = d3.drag<SVGCircleElement, unknown>()
      .on('start', function (event: D3DragEvent) {
        event.sourceEvent.stopPropagation()
        d3.select(this).attr('fill', '#0056b3') // 深蓝色高亮
      })
      .on('drag', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 更新手柄位置
        handle.attr('cx', event.x).attr('cy', event.y)

        // 更新连接线
        selectionGroup.select('.start-control-line')
          .attr('x2', event.x)
          .attr('y2', event.y)
        selectionGroup.select('.control-end-line')
          .attr('x1', event.x)
          .attr('y1', event.y)

        // 计算新的相对坐标
        const newRelativePoint = {
          x: event.x - center.x,
          y: event.y - center.y,
          z: typeof controlPoint.z === 'number' ? controlPoint.z : 0,
        }

        console.log(`[renderQuadraticPointHandles] Dragging control point:`, {
          newPosition: { x: event.x, y: event.y },
          newRelativePoint,
        })

        // 更新quadratic的显示（实时预览）
        const shapeGroup = svgLayer.select(`g.shape-group[data-shape-id="${shapeData.id}"]`)
        if (!shapeGroup.empty()) {
          // 重新生成quadratic路径
          const newPathData = `M ${startPoint.x},${startPoint.y} Q ${newRelativePoint.x},${newRelativePoint.y} ${endPoint.x},${endPoint.y}`

          shapeGroup.select('path')
            .attr('d', newPathData)
        }
      })
      .on('end', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 恢复手柄颜色
        handle.attr('fill', '#007bff') // 蓝色

        // 计算新的相对坐标
        const newRelativePoint = {
          x: event.x - center.x,
          y: event.y - center.y,
          z: typeof controlPoint.z === 'number' ? controlPoint.z : 0,
        }

        // 更新形状数据
        const updatedProperties = { ...shapeData.properties }
        updatedProperties.control = newRelativePoint

        const updatedShape = { ...shapeData }
        updatedShape.properties = updatedProperties

        // 🔧 直接更新Zustand store
        if (typeof window !== 'undefined' && (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__) {
          const shapesStore = (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__
          if (shapesStore) {
            const state = shapesStore.getState()
            const newShapes = state.shapes.map((s: ShapeModel) => s.id === shapeData.id ? updatedShape : s)

            console.log(`[renderQuadraticPointHandles] Directly updating store for quadratic ${shapeData.id} control point:`, {
              newRelativePoint,
            })

            shapesStore.setState({
              ...state,
              shapes: newShapes,
              selectedShapeIds: state.selectedShapeIds,
            })

            // 🔧 触发ComputeRequest事件以重新渲染
            setTimeout(() => {
              eventBus.publish({
                type: AppEventType.ComputeRequest,
                timestamp: Date.now(),
                payload: {
                  shapeId: shapeData.id,
                  operation: 'all',
                  source: 'QuadraticControlHandleDrag',
                },
              })
            }, 50)
          }
        }

        console.log(`[renderQuadraticPointHandles] Control point updated for quadratic ${shapeData.id}`)
      })

    // 结束点拖拽行为
    const endDragBehavior = d3.drag<SVGCircleElement, unknown>()
      .on('start', function (event: D3DragEvent) {
        event.sourceEvent.stopPropagation()
        d3.select(this).attr('fill', '#0056b3') // 深蓝色高亮
      })
      .on('drag', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 更新手柄位置
        handle.attr('cx', event.x).attr('cy', event.y)

        // 更新连接线
        selectionGroup.select('.control-end-line')
          .attr('x2', event.x)
          .attr('y2', event.y)

        // 计算新的相对坐标
        const newRelativePoint = {
          x: event.x - center.x,
          y: event.y - center.y,
          z: typeof endPoint.z === 'number' ? endPoint.z : 0,
        }

        console.log(`[renderQuadraticPointHandles] Dragging end point:`, {
          newPosition: { x: event.x, y: event.y },
          newRelativePoint,
        })

        // 更新quadratic的显示（实时预览）
        const shapeGroup = svgLayer.select(`g.shape-group[data-shape-id="${shapeData.id}"]`)
        if (!shapeGroup.empty()) {
          // 重新生成quadratic路径
          const newPathData = `M ${startPoint.x},${startPoint.y} Q ${controlPoint.x},${controlPoint.y} ${newRelativePoint.x},${newRelativePoint.y}`

          shapeGroup.select('path')
            .attr('d', newPathData)
        }
      })
      .on('end', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 恢复手柄颜色
        handle.attr('fill', '#007bff') // 蓝色

        // 计算新的相对坐标
        const newRelativePoint = {
          x: event.x - center.x,
          y: event.y - center.y,
          z: typeof endPoint.z === 'number' ? endPoint.z : 0,
        }

        // 更新形状数据
        const updatedProperties = { ...shapeData.properties }
        updatedProperties.end = newRelativePoint

        const updatedShape = { ...shapeData }
        updatedShape.properties = updatedProperties

        // 🔧 直接更新Zustand store
        if (typeof window !== 'undefined' && (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__) {
          const shapesStore = (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__
          if (shapesStore) {
            const state = shapesStore.getState()
            const newShapes = state.shapes.map((s: ShapeModel) => s.id === shapeData.id ? updatedShape : s)

            console.log(`[renderQuadraticPointHandles] Directly updating store for quadratic ${shapeData.id} end point:`, {
              newRelativePoint,
            })

            shapesStore.setState({
              ...state,
              shapes: newShapes,
              selectedShapeIds: state.selectedShapeIds,
            })

            // 🔧 触发ComputeRequest事件以重新渲染
            setTimeout(() => {
              eventBus.publish({
                type: AppEventType.ComputeRequest,
                timestamp: Date.now(),
                payload: {
                  shapeId: shapeData.id,
                  operation: 'all',
                  source: 'QuadraticEndHandleDrag',
                },
              })
            }, 50)
          }
        }

        console.log(`[renderQuadraticPointHandles] End point updated for quadratic ${shapeData.id}`)
      })

    startHandle.call(startDragBehavior)
    controlHandle.call(controlDragBehavior)
    endHandle.call(endDragBehavior)
  }

  console.log(`[renderQuadraticPointHandles] Quadratic point handles rendered for ${shapeData.id}:`, {
    startPoint: absoluteStart,
    controlPoint: absoluteControl,
    endPoint: absoluteEnd,
  })
}

// 🔧 新增：CUBIC元素点位手柄渲染函数
function renderCubicPointHandles(
  svgLayer: D3SVGGSelection,
  selectionGroup: D3SVGGSelection,
  shapeData: ShapeModel,
  currentZoom: number,
  isPanMode: boolean,
  eventBus: AppEventBusImpl,
) {
  const props = shapeData.properties
  const startPoint = props?.start as Point | undefined
  const control1Point = props?.control1 as Point | undefined
  const control2Point = props?.control2 as Point | undefined
  const endPoint = props?.end as Point | undefined

  if (!startPoint || !control1Point || !control2Point || !endPoint) {
    _warn(`[renderCubicPointHandles] Cubic ${shapeData.id} missing required points:`, { startPoint, control1Point, control2Point, endPoint })
    return
  }

  // 计算绝对坐标（相对于元素position）
  const center = shapeData.position !== undefined && shapeData.position !== null ? shapeData.position : { x: 0, y: 0 }
  const absoluteStart = {
    x: center.x + startPoint.x,
    y: center.y + startPoint.y,
  }
  const absoluteControl1 = {
    x: center.x + control1Point.x,
    y: center.y + control1Point.y,
  }
  const absoluteControl2 = {
    x: center.x + control2Point.x,
    y: center.y + control2Point.y,
  }
  const absoluteEnd = {
    x: center.x + endPoint.x,
    y: center.y + endPoint.y,
  }

  const handleSize = 8 / currentZoom
  const handleStrokeWidth = 1.5 / currentZoom

  // 1. 渲染起始点手柄
  const startHandle = selectionGroup.append('circle')
    .attr('class', 'point-handle start-handle')
    .attr('cx', absoluteStart.x)
    .attr('cy', absoluteStart.y)
    .attr('r', handleSize / 2)
    .attr('fill', '#007bff') // 蓝色
    .attr('stroke', '#ffffff')
    .attr('stroke-width', handleStrokeWidth)
    .style('cursor', isPanMode ? 'grab' : 'move')

  // 2. 渲染第一个控制点手柄
  const control1Handle = selectionGroup.append('circle')
    .attr('class', 'point-handle control1-handle')
    .attr('cx', absoluteControl1.x)
    .attr('cy', absoluteControl1.y)
    .attr('r', handleSize / 2)
    .attr('fill', '#007bff') // 蓝色
    .attr('stroke', '#ffffff')
    .attr('stroke-width', handleStrokeWidth)
    .style('cursor', isPanMode ? 'grab' : 'move')

  // 3. 渲染第二个控制点手柄
  const control2Handle = selectionGroup.append('circle')
    .attr('class', 'point-handle control2-handle')
    .attr('cx', absoluteControl2.x)
    .attr('cy', absoluteControl2.y)
    .attr('r', handleSize / 2)
    .attr('fill', '#007bff') // 蓝色
    .attr('stroke', '#ffffff')
    .attr('stroke-width', handleStrokeWidth)
    .style('cursor', isPanMode ? 'grab' : 'move')

  // 4. 渲染结束点手柄
  const endHandle = selectionGroup.append('circle')
    .attr('class', 'point-handle end-handle')
    .attr('cx', absoluteEnd.x)
    .attr('cy', absoluteEnd.y)
    .attr('r', handleSize / 2)
    .attr('fill', '#007bff') // 蓝色
    .attr('stroke', '#ffffff')
    .attr('stroke-width', handleStrokeWidth)
    .style('cursor', isPanMode ? 'grab' : 'move')

  // 添加连接线（帮助用户理解贝塞尔曲线结构）
  // 起始点到第一个控制点的连接线
  selectionGroup.append('line')
    .attr('class', 'cubic-guide-line start-control1-line')
    .attr('x1', absoluteStart.x)
    .attr('y1', absoluteStart.y)
    .attr('x2', absoluteControl1.x)
    .attr('y2', absoluteControl1.y)
    .attr('stroke', '#007bff') // 蓝色
    .attr('stroke-width', 1 / currentZoom)
    .attr('stroke-dasharray', `${3 / currentZoom},${3 / currentZoom}`)
    .attr('opacity', 0.5)

  // 第二个控制点到结束点的连接线
  selectionGroup.append('line')
    .attr('class', 'cubic-guide-line control2-end-line')
    .attr('x1', absoluteControl2.x)
    .attr('y1', absoluteControl2.y)
    .attr('x2', absoluteEnd.x)
    .attr('y2', absoluteEnd.y)
    .attr('stroke', '#007bff') // 蓝色
    .attr('stroke-width', 1 / currentZoom)
    .attr('stroke-dasharray', `${3 / currentZoom},${3 / currentZoom}`)
    .attr('opacity', 0.5)

  // 添加拖拽行为（如果不是平移模式）
  if (!isPanMode) {
    // 起始点拖拽行为
    const startDragBehavior = d3.drag<SVGCircleElement, unknown>()
      .on('start', function (event: D3DragEvent) {
        event.sourceEvent.stopPropagation()
        d3.select(this).attr('fill', '#0056b3') // 深蓝色高亮
      })
      .on('drag', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 更新手柄位置
        handle.attr('cx', event.x).attr('cy', event.y)

        // 更新连接线
        selectionGroup.select('.start-control1-line')
          .attr('x1', event.x)
          .attr('y1', event.y)

        // 计算新的相对坐标
        const newRelativePoint = {
          x: event.x - center.x,
          y: event.y - center.y,
          z: typeof startPoint.z === 'number' ? startPoint.z : 0,
        }

        console.log(`[renderCubicPointHandles] Dragging start point:`, {
          newPosition: { x: event.x, y: event.y },
          newRelativePoint,
        })

        // 更新cubic的显示（实时预览）
        const shapeGroup = svgLayer.select(`g.shape-group[data-shape-id="${shapeData.id}"]`)
        if (!shapeGroup.empty()) {
          // 重新生成cubic路径
          const newPathData = `M ${newRelativePoint.x},${newRelativePoint.y} C ${control1Point.x},${control1Point.y} ${control2Point.x},${control2Point.y} ${endPoint.x},${endPoint.y}`

          shapeGroup.select('path')
            .attr('d', newPathData)
        }
      })
      .on('end', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 恢复手柄颜色
        handle.attr('fill', '#007bff') // 蓝色

        // 计算新的相对坐标
        const newRelativePoint = {
          x: event.x - center.x,
          y: event.y - center.y,
          z: typeof startPoint.z === 'number' ? startPoint.z : 0,
        }

        // 更新形状数据
        const updatedProperties = { ...shapeData.properties }
        updatedProperties.start = newRelativePoint

        const updatedShape = { ...shapeData }
        updatedShape.properties = updatedProperties

        // 🔧 直接更新Zustand store
        if (typeof window !== 'undefined' && (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__) {
          const shapesStore = (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__
          if (shapesStore) {
            const state = shapesStore.getState()
            const newShapes = state.shapes.map((s: ShapeModel) => s.id === shapeData.id ? updatedShape : s)

            console.log(`[renderCubicPointHandles] Directly updating store for cubic ${shapeData.id} start point:`, {
              newRelativePoint,
            })

            shapesStore.setState({
              ...state,
              shapes: newShapes,
              selectedShapeIds: state.selectedShapeIds,
            })

            // 🔧 触发ComputeRequest事件以重新渲染
            setTimeout(() => {
              eventBus.publish({
                type: AppEventType.ComputeRequest,
                timestamp: Date.now(),
                payload: {
                  shapeId: shapeData.id,
                  operation: 'all',
                  source: 'CubicStartHandleDrag',
                },
              })
            }, 50)
          }
        }

        console.log(`[renderCubicPointHandles] Start point updated for cubic ${shapeData.id}`)
      })

    // 第一个控制点拖拽行为
    const control1DragBehavior = d3.drag<SVGCircleElement, unknown>()
      .on('start', function (event: D3DragEvent) {
        event.sourceEvent.stopPropagation()
        d3.select(this).attr('fill', '#0056b3') // 深蓝色高亮
      })
      .on('drag', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 更新手柄位置
        handle.attr('cx', event.x).attr('cy', event.y)

        // 更新连接线
        selectionGroup.select('.start-control1-line')
          .attr('x2', event.x)
          .attr('y2', event.y)

        // 计算新的相对坐标
        const newRelativePoint = {
          x: event.x - center.x,
          y: event.y - center.y,
          z: typeof control1Point.z === 'number' ? control1Point.z : 0,
        }

        console.log(`[renderCubicPointHandles] Dragging control1 point:`, {
          newPosition: { x: event.x, y: event.y },
          newRelativePoint,
        })

        // 更新cubic的显示（实时预览）
        const shapeGroup = svgLayer.select(`g.shape-group[data-shape-id="${shapeData.id}"]`)
        if (!shapeGroup.empty()) {
          // 重新生成cubic路径
          const newPathData = `M ${startPoint.x},${startPoint.y} C ${newRelativePoint.x},${newRelativePoint.y} ${control2Point.x},${control2Point.y} ${endPoint.x},${endPoint.y}`

          shapeGroup.select('path')
            .attr('d', newPathData)
        }
      })
      .on('end', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 恢复手柄颜色
        handle.attr('fill', '#007bff') // 蓝色

        // 计算新的相对坐标
        const newRelativePoint = {
          x: event.x - center.x,
          y: event.y - center.y,
          z: typeof control1Point.z === 'number' ? control1Point.z : 0,
        }

        // 更新形状数据
        const updatedProperties = { ...shapeData.properties }
        updatedProperties.control1 = newRelativePoint

        const updatedShape = { ...shapeData }
        updatedShape.properties = updatedProperties

        // 🔧 直接更新Zustand store
        if (typeof window !== 'undefined' && (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__) {
          const shapesStore = (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__
          if (shapesStore) {
            const state = shapesStore.getState()
            const newShapes = state.shapes.map((s: ShapeModel) => s.id === shapeData.id ? updatedShape : s)

            console.log(`[renderCubicPointHandles] Directly updating store for cubic ${shapeData.id} control1 point:`, {
              newRelativePoint,
            })

            shapesStore.setState({
              ...state,
              shapes: newShapes,
              selectedShapeIds: state.selectedShapeIds,
            })

            // 🔧 触发ComputeRequest事件以重新渲染
            setTimeout(() => {
              eventBus.publish({
                type: AppEventType.ComputeRequest,
                timestamp: Date.now(),
                payload: {
                  shapeId: shapeData.id,
                  operation: 'all',
                  source: 'CubicControl1HandleDrag',
                },
              })
            }, 50)
          }
        }

        console.log(`[renderCubicPointHandles] Control1 point updated for cubic ${shapeData.id}`)
      })

    // 第二个控制点拖拽行为
    const control2DragBehavior = d3.drag<SVGCircleElement, unknown>()
      .on('start', function (event: D3DragEvent) {
        event.sourceEvent.stopPropagation()
        d3.select(this).attr('fill', '#0056b3') // 深蓝色高亮
      })
      .on('drag', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 更新手柄位置
        handle.attr('cx', event.x).attr('cy', event.y)

        // 更新连接线
        selectionGroup.select('.control2-end-line')
          .attr('x1', event.x)
          .attr('y1', event.y)

        // 计算新的相对坐标
        const newRelativePoint = {
          x: event.x - center.x,
          y: event.y - center.y,
          z: typeof control2Point.z === 'number' ? control2Point.z : 0,
        }

        console.log(`[renderCubicPointHandles] Dragging control2 point:`, {
          newPosition: { x: event.x, y: event.y },
          newRelativePoint,
        })

        // 更新cubic的显示（实时预览）
        const shapeGroup = svgLayer.select(`g.shape-group[data-shape-id="${shapeData.id}"]`)
        if (!shapeGroup.empty()) {
          // 重新生成cubic路径
          const newPathData = `M ${startPoint.x},${startPoint.y} C ${control1Point.x},${control1Point.y} ${newRelativePoint.x},${newRelativePoint.y} ${endPoint.x},${endPoint.y}`

          shapeGroup.select('path')
            .attr('d', newPathData)
        }
      })
      .on('end', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 恢复手柄颜色
        handle.attr('fill', '#007bff') // 蓝色

        // 计算新的相对坐标
        const newRelativePoint = {
          x: event.x - center.x,
          y: event.y - center.y,
          z: typeof control2Point.z === 'number' ? control2Point.z : 0,
        }

        // 更新形状数据
        const updatedProperties = { ...shapeData.properties }
        updatedProperties.control2 = newRelativePoint

        const updatedShape = { ...shapeData }
        updatedShape.properties = updatedProperties

        // 🔧 直接更新Zustand store
        if (typeof window !== 'undefined' && (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__) {
          const shapesStore = (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__
          if (shapesStore) {
            const state = shapesStore.getState()
            const newShapes = state.shapes.map((s: ShapeModel) => s.id === shapeData.id ? updatedShape : s)

            console.log(`[renderCubicPointHandles] Directly updating store for cubic ${shapeData.id} control2 point:`, {
              newRelativePoint,
            })

            shapesStore.setState({
              ...state,
              shapes: newShapes,
              selectedShapeIds: state.selectedShapeIds,
            })

            // 🔧 触发ComputeRequest事件以重新渲染
            setTimeout(() => {
              eventBus.publish({
                type: AppEventType.ComputeRequest,
                timestamp: Date.now(),
                payload: {
                  shapeId: shapeData.id,
                  operation: 'all',
                  source: 'CubicControl2HandleDrag',
                },
              })
            }, 50)
          }
        }

        console.log(`[renderCubicPointHandles] Control2 point updated for cubic ${shapeData.id}`)
      })

    // 结束点拖拽行为
    const endDragBehavior = d3.drag<SVGCircleElement, unknown>()
      .on('start', function (event: D3DragEvent) {
        event.sourceEvent.stopPropagation()
        d3.select(this).attr('fill', '#0056b3') // 深蓝色高亮
      })
      .on('drag', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 更新手柄位置
        handle.attr('cx', event.x).attr('cy', event.y)

        // 更新连接线
        selectionGroup.select('.control2-end-line')
          .attr('x2', event.x)
          .attr('y2', event.y)

        // 计算新的相对坐标
        const newRelativePoint = {
          x: event.x - center.x,
          y: event.y - center.y,
          z: typeof endPoint.z === 'number' ? endPoint.z : 0,
        }

        console.log(`[renderCubicPointHandles] Dragging end point:`, {
          newPosition: { x: event.x, y: event.y },
          newRelativePoint,
        })

        // 更新cubic的显示（实时预览）
        const shapeGroup = svgLayer.select(`g.shape-group[data-shape-id="${shapeData.id}"]`)
        if (!shapeGroup.empty()) {
          // 重新生成cubic路径
          const newPathData = `M ${startPoint.x},${startPoint.y} C ${control1Point.x},${control1Point.y} ${control2Point.x},${control2Point.y} ${newRelativePoint.x},${newRelativePoint.y}`

          shapeGroup.select('path')
            .attr('d', newPathData)
        }
      })
      .on('end', function (event: D3DragEvent) {
        const handle = d3.select(this)

        // 恢复手柄颜色
        handle.attr('fill', '#007bff') // 蓝色

        // 计算新的相对坐标
        const newRelativePoint = {
          x: event.x - center.x,
          y: event.y - center.y,
          z: typeof endPoint.z === 'number' ? endPoint.z : 0,
        }

        // 更新形状数据
        const updatedProperties = { ...shapeData.properties }
        updatedProperties.end = newRelativePoint

        const updatedShape = { ...shapeData }
        updatedShape.properties = updatedProperties

        // 🔧 直接更新Zustand store
        if (typeof window !== 'undefined' && (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__) {
          const shapesStore = (window as WindowWithZustandStore).__ZUSTAND_SHAPES_STORE__
          if (shapesStore) {
            const state = shapesStore.getState()
            const newShapes = state.shapes.map((s: ShapeModel) => s.id === shapeData.id ? updatedShape : s)

            console.log(`[renderCubicPointHandles] Directly updating store for cubic ${shapeData.id} end point:`, {
              newRelativePoint,
            })

            shapesStore.setState({
              ...state,
              shapes: newShapes,
              selectedShapeIds: state.selectedShapeIds,
            })

            // 🔧 触发ComputeRequest事件以重新渲染
            setTimeout(() => {
              eventBus.publish({
                type: AppEventType.ComputeRequest,
                timestamp: Date.now(),
                payload: {
                  shapeId: shapeData.id,
                  operation: 'all',
                  source: 'CubicEndHandleDrag',
                },
              })
            }, 50)
          }
        }

        console.log(`[renderCubicPointHandles] End point updated for cubic ${shapeData.id}`)
      })

    startHandle.call(startDragBehavior)
    control1Handle.call(control1DragBehavior)
    control2Handle.call(control2DragBehavior)
    endHandle.call(endDragBehavior)
  }

  console.log(`[renderCubicPointHandles] Cubic point handles rendered for ${shapeData.id}:`, {
    startPoint: absoluteStart,
    control1Point: absoluteControl1,
    control2Point: absoluteControl2,
    endPoint: absoluteEnd,
  })
}

// 辅助函数：生成arc路径（与drawArc函数保持一致）
function generateArcPath(center: { x: number, y: number }, radius: number, startAngle: number, endAngle: number, counterClockwise: boolean = false): string {
  // Convert angles to radians
  const startRad = (startAngle * Math.PI) / 180
  const endRad = (endAngle * Math.PI) / 180

  // Calculate start and end points relative to center
  const startX = center.x + radius * Math.cos(startRad)
  const startY = center.y + radius * Math.sin(startRad)
  const endX = center.x + radius * Math.cos(endRad)
  const endY = center.y + radius * Math.sin(endRad)

  // Calculate sweep angle with proper direction handling
  let sweepAngle = endAngle - startAngle

  // Handle angle wrapping and direction
  if (counterClockwise) {
    // For counterclockwise, ensure negative sweep
    if (sweepAngle > 0)
      sweepAngle -= 360
    sweepAngle = Math.abs(sweepAngle) // Make positive for flag calculation
  }
  else {
    // For clockwise, ensure positive sweep
    if (sweepAngle < 0)
      sweepAngle += 360
  }

  // Determine if this is a large arc (> 180 degrees)
  const largeArcFlag = Math.abs(sweepAngle) > 180 ? 1 : 0

  // Sweep direction flag (0 = counterclockwise, 1 = clockwise)
  const sweepFlag = counterClockwise ? 0 : 1

  // Generate SVG arc path
  return `M ${startX} ${startY} A ${radius} ${radius} 0 ${largeArcFlag} ${sweepFlag} ${endX} ${endY}`
}

/**
 * 渲染单个形状的边界框
 */
function renderSingleBoundingBox(
  svgLayer: D3SVGGSelection,
  shapeData: ShapeModel,
  currentZoom: number,
  onShapeSelected: (id: string, isMultiSelect: boolean) => void,
  isPanMode: boolean,
  selectedIds: string[],
  allShapes: ShapeModel[],
) {
  // 🔧 新增：检查是否为路径类型元素，使用点位手柄而不是边界框
  const pathTypes = [CoreElementType.LINE, CoreElementType.POLYLINE, CoreElementType.ARC, CoreElementType.POLYGON, CoreElementType.QUADRATIC, CoreElementType.CUBIC]
  if (pathTypes.includes(shapeData.type as CoreElementType)) {
    // 为路径类型元素渲染点位手柄
    if (shapeData.type === CoreElementType.LINE) {
      renderLinePointHandles(svgLayer, shapeData, currentZoom, onShapeSelected, isPanMode, selectedIds, allShapes)
      return
    }
    if (shapeData.type === CoreElementType.POLYLINE) {
      renderPolylinePointHandles(svgLayer, shapeData, currentZoom, onShapeSelected, isPanMode, selectedIds, allShapes)
      return
    }
    if (shapeData.type === CoreElementType.ARC) {
      renderArcPointHandles(svgLayer, shapeData, currentZoom, onShapeSelected, isPanMode, selectedIds, allShapes)
      return
    }
    if (shapeData.type === CoreElementType.QUADRATIC) {
      // 创建选择组
      const selectionGroup = svgLayer.append('g')
        .attr('class', 'selection-group')
        .attr('data-shape-id', shapeData.id)

      renderQuadraticPointHandles(svgLayer, selectionGroup, shapeData, currentZoom, isPanMode, getEventBus())
      return
    }
    if (shapeData.type === CoreElementType.CUBIC) {
      // 创建选择组
      const selectionGroup = svgLayer.append('g')
        .attr('class', 'selection-group')
        .attr('data-shape-id', shapeData.id)

      renderCubicPointHandles(svgLayer, selectionGroup, shapeData, currentZoom, isPanMode, getEventBus())
      return
    }
    // TODO: 其他路径类型的点位手柄渲染
    // 暂时回退到边界框渲染，后续逐步实现
  }

  // 计算元素的实际边界框
  const boundingBox = calculateAccurateBoundingBox(shapeData, currentZoom)
  const rotation = typeof shapeData.rotation === 'number' && Number.isFinite(shapeData.rotation) ? shapeData.rotation : 0

  // selectionGroup transform 包含 translate 和 rotate，让边界框和手柄随元素一起旋转
  const selectionGroup = svgLayer.append('g')
    .attr('class', 'selection-group')
    .attr('data-shape-id', shapeData.id)
    .attr('transform', `translate(${shapeData.position.x}, ${shapeData.position.y}) rotate(${rotation})`)

  // 使用相对坐标系统 - 边界框相对于元素中心
  const relativeBounds = {
    x: boundingBox.x,
    y: boundingBox.y,
    width: boundingBox.width,
    height: boundingBox.height,
  }

  // 渲染边界框（随 selectionGroup 一起旋转）
  selectionGroup.append('rect')
    .attr('class', 'selection-indicator')
    .attr('x', relativeBounds.x)
    .attr('y', relativeBounds.y)
    .attr('width', relativeBounds.width)
    .attr('height', relativeBounds.height)
    .attr('fill', 'none')
    .attr('stroke', 'rgba(0, 123, 255, 0.7)')
    .attr('stroke-width', 1.5 / currentZoom)
    .attr('stroke-dasharray', `${4 / currentZoom},${2 / currentZoom}`)
    .style('pointer-events', 'none')

  // 添加透明的拖拽区域
  const dragArea = selectionGroup.append('rect')
    .attr('class', 'drag-area')
    .attr('x', relativeBounds.x)
    .attr('y', relativeBounds.y)
    .attr('width', relativeBounds.width)
    .attr('height', relativeBounds.height)
    .attr('fill', 'transparent')
    .attr('stroke', 'none')
    .style('cursor', 'move')
    .style('pointer-events', 'all')

  // 添加缩放控制手柄
  const handleSize = 8 / currentZoom
  const handleStrokeWidth = 1 / currentZoom

  // 检查是否为需要等比例缩放的元素类型（多边形、圆形、正方形）
  const proportionalScaleTypes = [
    // 多边形类型
    CoreElementType.POLYGON,
    CoreElementType.TRIANGLE,
    CoreElementType.QUADRILATERAL,
    CoreElementType.PENTAGON,
    CoreElementType.HEXAGON,
    CoreElementType.HEPTAGON,
    CoreElementType.OCTAGON,
    CoreElementType.NONAGON,
    CoreElementType.DECAGON,
    // 圆形和正方形也需要等比例缩放
    CoreElementType.CIRCLE,
    CoreElementType.SQUARE,
  ]
  const isProportionalScaleElement = proportionalScaleTypes.includes(shapeData.type as CoreElementType)

  // 根据元素类型决定显示哪些手柄
  const allHandles = isProportionalScaleElement
    ? [
        // 等比例缩放元素（多边形、圆形、正方形）只显示四角手柄
        { x: relativeBounds.x, y: relativeBounds.y, cursor: 'nw-resize', id: 'nw' },
        { x: relativeBounds.x + relativeBounds.width, y: relativeBounds.y, cursor: 'ne-resize', id: 'ne' },
        { x: relativeBounds.x, y: relativeBounds.y + relativeBounds.height, cursor: 'sw-resize', id: 'sw' },
        { x: relativeBounds.x + relativeBounds.width, y: relativeBounds.y + relativeBounds.height, cursor: 'se-resize', id: 'se' },
      ]
    : [
        // 其他元素显示所有8个手柄
        { x: relativeBounds.x, y: relativeBounds.y, cursor: 'nw-resize', id: 'nw' },
        { x: relativeBounds.x + relativeBounds.width, y: relativeBounds.y, cursor: 'ne-resize', id: 'ne' },
        { x: relativeBounds.x, y: relativeBounds.y + relativeBounds.height, cursor: 'sw-resize', id: 'sw' },
        { x: relativeBounds.x + relativeBounds.width, y: relativeBounds.y + relativeBounds.height, cursor: 'se-resize', id: 'se' },
        { x: relativeBounds.x + relativeBounds.width / 2, y: relativeBounds.y, cursor: 'ns-resize', id: 'n' },
        { x: relativeBounds.x + relativeBounds.width, y: relativeBounds.y + relativeBounds.height / 2, cursor: 'e-resize', id: 'e' },
        { x: relativeBounds.x + relativeBounds.width / 2, y: relativeBounds.y + relativeBounds.height, cursor: 's-resize', id: 's' },
        { x: relativeBounds.x, y: relativeBounds.y + relativeBounds.height / 2, cursor: 'w-resize', id: 'w' },
      ]

  allHandles.forEach((handle) => {
    // 对于非等比例缩放元素的顶部手柄，稍微向上调整位置
    const adjustedY = (!isProportionalScaleElement && handle.id === 'n') ? handle.y - handleSize * 0.5 : handle.y

    const handleElement = selectionGroup.append('rect')
      .attr('class', `resize-handle resize-handle-${handle.id}`)
      .attr('data-handle-type', handle.id)
      .attr('x', handle.x - handleSize / 2)
      .attr('y', adjustedY - handleSize / 2)
      .attr('width', handleSize)
      .attr('height', handleSize)
      .attr('fill', '#007bff') // All handles use blue color
      .attr('stroke', '#ffffff')
      .attr('stroke-width', handleStrokeWidth)
      .style('cursor', handle.cursor)
      .style('pointer-events', 'all')

    addResizeHandleEvents(handleElement, handle, shapeData, relativeBounds, currentZoom, selectionGroup)
  })

  // 只渲染一个蓝色的旋转手柄（顶部正中，随 selectionGroup 一起旋转）
  const rotationHandleDistance = Math.max(30, 40 / currentZoom)
  const rotationHandleX = relativeBounds.x + relativeBounds.width / 2
  const rotationHandleY = relativeBounds.y - rotationHandleDistance

  // 旋转手柄连接线
  selectionGroup.append('line')
    .attr('class', 'rotation-line')
    .attr('x1', relativeBounds.x + relativeBounds.width / 2)
    .attr('y1', relativeBounds.y)
    .attr('x2', rotationHandleX)
    .attr('y2', rotationHandleY)
    .attr('stroke', '#007bff')
    .attr('stroke-width', handleStrokeWidth)
    .style('pointer-events', 'none')

  // 只渲染一个蓝色圆点旋转手柄
  selectionGroup.append('circle')
    .attr('class', 'rotation-handle')
    .attr('cx', rotationHandleX)
    .attr('cy', rotationHandleY)
    .attr('r', handleSize * 0.7)
    .attr('fill', '#007bff')
    .attr('stroke', '#ffffff')
    .attr('stroke-width', handleStrokeWidth * 1.5)
    .style('cursor', 'grab')
    .style('pointer-events', 'all')

  // 添加旋转事件处理（传入 selectionGroup 和旋转手柄）
  addRotationHandleEvents(selectionGroup.select('.rotation-handle'), shapeData, currentZoom)

  // 添加拖动事件处理
  addDragEvents(dragArea, shapeData, svgLayer, currentZoom, selectedIds, allShapes)
}

/**
 * 添加缩放手柄事件处理 - 优化版本
 */
function addResizeHandleEvents(
  handleElement: D3SVGRectSelection,
  handle: { x: number, y: number, cursor: string, id: string },
  shapeData: ShapeModel,
  originalBounds: { x: number, y: number, width: number, height: number },
  currentZoom: number,
  selectionGroup: D3SVGGSelection,
) {
  let isResizing = false
  let startX = 0
  let startY = 0
  let animationFrameId: number | null = null
  let lastUpdateTime = 0
  const updateThrottle = 8 // 更高频率更新，约120FPS

  handleElement.on('mousedown', (event: MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()

    console.log(`🎯 [ShapeRenderer] Resize handle mousedown for shape ${shapeData.id}, handle ${handle.id}`)
    isResizing = true
    startX = event.clientX
    startY = event.clientY

    const handleMouseMove = (moveEvent: MouseEvent) => {
      if (!isResizing)
        return

      moveEvent.preventDefault()
      moveEvent.stopPropagation()

      // 使用requestAnimationFrame优化性能
      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId)
      }

      animationFrameId = requestAnimationFrame(() => {
        const currentTime = performance.now()

        // 节流更新，提高性能
        if (currentTime - lastUpdateTime >= updateThrottle) {
          lastUpdateTime = currentTime

          const screenDx = moveEvent.clientX - startX
          const screenDy = moveEvent.clientY - startY

          // 获取元素的旋转角度
          const rotation = typeof shapeData.rotation === 'number' && Number.isFinite(shapeData.rotation) ? shapeData.rotation : 0
          const rotationRadians = (rotation * Math.PI) / 180

          // 如果元素有旋转，需要将鼠标移动向量转换到旋转后的坐标系
          let svgDx = screenDx / currentZoom
          let svgDy = screenDy / currentZoom

          if (rotation !== 0) {
            // 将鼠标移动向量逆向旋转，转换到元素的局部坐标系
            const cos = Math.cos(-rotationRadians) // 注意是逆向旋转
            const sin = Math.sin(-rotationRadians)

            const rotatedDx = svgDx * cos - svgDy * sin
            const rotatedDy = svgDx * sin + svgDy * cos

            svgDx = rotatedDx
            svgDy = rotatedDy
          }

          // 检查是否为需要等比例缩放的元素类型（多边形、圆形、正方形）
          const proportionalScaleTypes = [
            // 多边形类型
            CoreElementType.POLYGON,
            CoreElementType.TRIANGLE,
            CoreElementType.QUADRILATERAL,
            CoreElementType.PENTAGON,
            CoreElementType.HEXAGON,
            CoreElementType.HEPTAGON,
            CoreElementType.OCTAGON,
            CoreElementType.NONAGON,
            CoreElementType.DECAGON,
            // 圆形和正方形也需要等比例缩放
            CoreElementType.CIRCLE,
            CoreElementType.SQUARE,
          ]
          const isProportionalScaleElement = proportionalScaleTypes.includes(shapeData.type as CoreElementType)

          // 对于等比例缩放元素，强制等比例缩放；对于其他元素，支持Shift键控制
          const maintainAspectRatio = isProportionalScaleElement || moveEvent.shiftKey
          const newBounds = calculateSimpleResize(originalBounds, handle.id, svgDx, svgDy, maintainAspectRatio)

          // 实时更新边界框显示（仅视觉预览，不触发事件）
          // 🔧 修复：传递旋转角度，确保预览框正确显示旋转状态
          updateRelativeBoundingBoxPreview(selectionGroup, newBounds, currentZoom, rotation)
        }
      })
    }

    const handleMouseUp = (upEvent: MouseEvent) => {
      if (!isResizing)
        return

      upEvent.preventDefault()
      upEvent.stopPropagation()

      isResizing = false

      // 清理动画帧
      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId)
        animationFrameId = null
      }

      const screenDx = upEvent.clientX - startX
      const screenDy = upEvent.clientY - startY

      // 获取元素的旋转角度
      const rotation = typeof shapeData.rotation === 'number' && Number.isFinite(shapeData.rotation) ? shapeData.rotation : 0
      const rotationRadians = (rotation * Math.PI) / 180

      // 如果元素有旋转，需要将鼠标移动向量转换到旋转后的坐标系
      let svgDx = screenDx / currentZoom
      let svgDy = screenDy / currentZoom

      if (rotation !== 0) {
        // 将鼠标移动向量逆向旋转，转换到元素的局部坐标系
        const cos = Math.cos(-rotationRadians) // 注意是逆向旋转
        const sin = Math.sin(-rotationRadians)

        const rotatedDx = svgDx * cos - svgDy * sin
        const rotatedDy = svgDx * sin + svgDy * cos

        svgDx = rotatedDx
        svgDy = rotatedDy
      }

      console.log(`📏 [ShapeRenderer] Resize completed for shape ${shapeData.id}: screenDx=${screenDx}, screenDy=${screenDy}`)

      if (Math.abs(screenDx) > 2 || Math.abs(screenDy) > 2) {
        console.log(`✅ [ShapeRenderer] Resize threshold met, processing update...`)

        // 检查是否为需要等比例缩放的元素类型（多边形、圆形、正方形）
        const proportionalScaleTypes = [
          // 多边形类型
          CoreElementType.POLYGON,
          CoreElementType.TRIANGLE,
          CoreElementType.QUADRILATERAL,
          CoreElementType.PENTAGON,
          CoreElementType.HEXAGON,
          CoreElementType.HEPTAGON,
          CoreElementType.OCTAGON,
          CoreElementType.NONAGON,
          CoreElementType.DECAGON,
          // 圆形和正方形也需要等比例缩放
          CoreElementType.CIRCLE,
          CoreElementType.SQUARE,
        ]
        const isProportionalScaleElement = proportionalScaleTypes.includes(shapeData.type as CoreElementType)
        console.log(`🔍 [ShapeRenderer] Element type: ${shapeData.type}, isProportionalScale: ${isProportionalScaleElement}`)

        // 对于等比例缩放元素，强制等比例缩放；对于其他元素，支持Shift键控制
        const maintainAspectRatio = isProportionalScaleElement || upEvent.shiftKey
        const finalBounds = calculateSimpleResize(originalBounds, handle.id, svgDx, svgDy, maintainAspectRatio)
        console.log(`📐 [ShapeRenderer] Final bounds:`, finalBounds)

        // 计算新的元素属性
        const updates = calculateRelativeElementUpdates(shapeData, originalBounds, finalBounds, handle.id)
        console.log(`🔄 [ShapeRenderer] Calculated updates:`, updates)

        // 发布形状更新事件（仅在操作完成时）
        const eventBus = getEventBus()
        console.log(`🚀 [ShapeRenderer] Publishing ShapeEditRequest for shape ${shapeData.id}`)
        eventBus.publish({
          type: AppEventType.ShapeEditRequest,
          payload: {
            shapeId: shapeData.id,
            changes: updates,
          },
        })
        console.log(`✅ [ShapeRenderer] ShapeEditRequest published successfully`)
      }
      else {
        console.log(`❌ [ShapeRenderer] Resize threshold not met (${Math.abs(screenDx)}, ${Math.abs(screenDy)} <= 2)`)
      }

      // 移除事件监听器
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    // 添加全局事件监听器
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  })
}

/**
 * 全新的简化旋转系统 - 解决0°到180°限制问题
 * 使用最简单可靠的角度计算方法，支持完整360°旋转
 */
function addRotationHandleEvents(
  rotationHandle: D3SVGGSelection,
  shapeData: ShapeModel,
  currentZoom: number,
) {
  let isRotating = false
  let frameId: number | null = null
  let startAngle = 0
  let initialRotation = 0
  let centerSVG = { x: 0, y: 0 } // 旋转中心点在SVG画布上的绝对坐标

  rotationHandle.on('mousedown', (event: MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()

    isRotating = true
    initialRotation = typeof shapeData.rotation === 'number' ? shapeData.rotation : 0

    // 获取SVG容器
    const svgElement = (event.target as SVGElement).ownerSVGElement
    if (!svgElement)
      return

    // 获取 selectionGroup 的 DOM 节点
    const selectionGroup = svgElement.querySelector(`g.selection-group[data-shape-id="${shapeData.id}"]`)
    if (!selectionGroup)
      return

    // 获取 group 的变换矩阵
    const ctm = (selectionGroup as SVGGraphicsElement).getScreenCTM()
    centerSVG = ctm ? { x: ctm.e, y: ctm.f } : { x: shapeData.position.x, y: shapeData.position.y }

    // 鼠标点用 screen 坐标
    const startMouseX = event.clientX
    const startMouseY = event.clientY
    startAngle = Math.atan2(startMouseY - centerSVG.y, startMouseX - centerSVG.x) * (180 / Math.PI)

    const handleMouseMove = (moveEvent: MouseEvent) => {
      if (!isRotating)
        return
      moveEvent.preventDefault()
      moveEvent.stopPropagation()
      if (frameId !== null) {
        cancelAnimationFrame(frameId)
      }
      frameId = requestAnimationFrame(() => {
        const currentMouseX = moveEvent.clientX
        const currentMouseY = moveEvent.clientY
        const currentAngle = Math.atan2(currentMouseY - centerSVG.y, currentMouseX - centerSVG.x) * (180 / Math.PI)
        let angleDiff = currentAngle - startAngle
        while (angleDiff > 180) angleDiff -= 360
        while (angleDiff < -180) angleDiff += 360
        const finalRotation = initialRotation + angleDiff
        let displayRotation = finalRotation
        if (!moveEvent.shiftKey) {
          const snapTo = 15
          const snapped = Math.round(finalRotation / snapTo) * snapTo
          if (Math.abs(finalRotation - snapped) < 3) {
            displayRotation = snapped
          }
        }
        const shapeTransformStr = `translate(${shapeData.position.x}, ${shapeData.position.y}) rotate(${displayRotation})`
        const shapeGroup = d3.select(`g.shape-group[data-shape-id="${shapeData.id}"]`)
        if (!shapeGroup.empty()) {
          shapeGroup.attr('transform', shapeTransformStr)
        }
        const selectionGroup = d3.select(`g.selection-group[data-shape-id="${shapeData.id}"]`)
        if (!selectionGroup.empty()) {
          // selectionGroup transform 应包含 translate 和 rotate
          selectionGroup.attr('transform', `translate(${shapeData.position.x}, ${shapeData.position.y}) rotate(${displayRotation})`)
          // 旋转手柄的相对位置：始终在边界框顶部正中
          const bounds = calculateAccurateBoundingBox(shapeData, currentZoom)
          const rotationHandleDistance = Math.max(30, 40 / currentZoom)
          const handleX = bounds.x + bounds.width / 2
          const handleY = bounds.y - rotationHandleDistance
          const rotationHandleGroup = selectionGroup.select('.rotation-handle-group')
          if (!rotationHandleGroup.empty()) {
            rotationHandleGroup.attr('transform', `translate(${handleX}, ${handleY})`)
          }
          const rotationLine = selectionGroup.select('.rotation-line')
          if (!rotationLine.empty()) {
            rotationLine
              .attr('x1', bounds.x + bounds.width / 2)
              .attr('y1', bounds.y)
              .attr('x2', handleX)
              .attr('y2', handleY)
          }
        }
      })
    }

    const handleMouseUp = (upEvent: MouseEvent) => {
      if (!isRotating)
        return
      upEvent.preventDefault()
      upEvent.stopPropagation()
      isRotating = false
      if (frameId !== null) {
        cancelAnimationFrame(frameId)
        frameId = null
      }
      const currentMouseX = upEvent.clientX
      const currentMouseY = upEvent.clientY
      const currentAngle = Math.atan2(currentMouseY - centerSVG.y, currentMouseX - centerSVG.x) * (180 / Math.PI)
      let angleDiff = currentAngle - startAngle
      while (angleDiff > 180) angleDiff -= 360
      while (angleDiff < -180) angleDiff += 360
      let finalRotation = initialRotation + angleDiff
      if (!upEvent.shiftKey) {
        const snapTo = 15
        const snapped = Math.round(finalRotation / snapTo) * snapTo
        if (Math.abs(finalRotation - snapped) < 3) {
          finalRotation = snapped
        }
      }
      const eventBus = getEventBus()
      eventBus.publish({
        type: AppEventType.ShapeEditRequest,
        payload: {
          shapeId: shapeData.id,
          changes: {
            rotation: finalRotation,
          },
        },
      })
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  })
}

/**
 * 添加拖动事件处理 - 支持多选拖拽的优化版本
 */
function addDragEvents(
  dragArea: D3SVGRectSelection,
  shapeData: ShapeModel,
  svgLayer: D3SVGGSelection,
  currentZoom: number,
  selectedIds: string[],
  allShapes: ShapeModel[],
) {
  let isDragging = false
  let startX = 0
  let startY = 0
  let totalDx = 0
  let totalDy = 0
  let animationFrameId: number | null = null
  let lastUpdateTime = 0
  const updateThrottle = 4 // 极高频率更新，约250FPS

  dragArea.on('mousedown', (event: MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()

    isDragging = true
    startX = event.clientX
    startY = event.clientY
    totalDx = 0
    totalDy = 0

    // 检查是否为多选状态，以及当前元素是否在选中列表中
    const isMultiSelect = selectedIds.length > 1 && selectedIds.includes(shapeData.id)
    const shapesToDrag = isMultiSelect
      ? allShapes.filter(shape => selectedIds.includes(shape.id))
      : [shapeData]

    console.log(`[ShapeRenderer] Drag started for ${shapesToDrag.length} shape(s):`, shapesToDrag.map(s => s.id))

    const handleMouseMove = (moveEvent: MouseEvent) => {
      if (!isDragging)
        return

      moveEvent.preventDefault()
      moveEvent.stopPropagation()

      // 使用requestAnimationFrame优化性能
      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId)
      }

      animationFrameId = requestAnimationFrame(() => {
        const currentTime = performance.now()

        // 极高频率节流更新，提高性能
        if (currentTime - lastUpdateTime >= updateThrottle) {
          lastUpdateTime = currentTime

          const screenDx = moveEvent.clientX - startX
          const screenDy = moveEvent.clientY - startY
          const svgDx = screenDx / currentZoom
          const svgDy = screenDy / currentZoom

          totalDx = svgDx
          totalDy = svgDy

          // 为所有需要拖拽的元素更新位置
          shapesToDrag.forEach((shape) => {
            const baseX = typeof shape.position.x === 'number' ? shape.position.x : 0
            const baseY = typeof shape.position.y === 'number' ? shape.position.y : 0
            const rotation = typeof shape.rotation === 'number' && Number.isFinite(shape.rotation) ? shape.rotation : 0

            const newX = baseX + totalDx
            const newY = baseY + totalDy

            // 高性能的变换更新 - 保持与旋转事件一致的transform分离
            const shapeGroup = svgLayer.select(`g.shape-group[data-shape-id="${shape.id}"]`)
            const selectionGroup = svgLayer.select(`g.selection-group[data-shape-id="${shape.id}"]`)

            // 🔧 修复：构建变换字符串 - 形状组和选择组都需要包含旋转
            const shapeTransformStr = rotation !== 0
              ? `translate(${newX}, ${newY}) rotate(${rotation})`
              : `translate(${newX}, ${newY})`
            const selectionTransformStr = rotation !== 0
              ? `translate(${newX}, ${newY}) rotate(${rotation})`
              : `translate(${newX}, ${newY})`

            // 批量更新变换，减少DOM操作
            if (!shapeGroup.empty()) {
              shapeGroup.attr('transform', shapeTransformStr)
            }
            if (!selectionGroup.empty()) {
              selectionGroup.attr('transform', selectionTransformStr)
            }
          })
        }
      })
    }

    const handleMouseUp = (upEvent: MouseEvent) => {
      if (!isDragging) {
        return
      }

      upEvent.preventDefault()
      upEvent.stopPropagation()

      isDragging = false

      // 清理动画帧
      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId)
        animationFrameId = null
      }

      if (Math.abs(totalDx) > 2 || Math.abs(totalDy) > 2) {
        const eventBus = getEventBus()

        // 为所有拖拽的元素发布位置更新事件
        shapesToDrag.forEach((shape) => {
          const finalX = (typeof shape.position.x === 'number' ? shape.position.x : 0) + totalDx
          const finalY = (typeof shape.position.y === 'number' ? shape.position.y : 0) + totalDy

          console.log(`[ShapeRenderer] Publishing position update for shape ${shape.id}: (${finalX}, ${finalY})`)

          eventBus.publish({
            type: AppEventType.ShapeEditRequest,
            payload: {
              shapeId: shape.id,
              changes: {
                position: {
                  x: finalX,
                  y: finalY,
                  z: typeof shape.position.z === 'number' ? shape.position.z : 0,
                },
              },
            },
          })
        })

        console.log(`[ShapeRenderer] Drag completed for ${shapesToDrag.length} shape(s)`)
      }

      // 移除事件监听器
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    // 添加全局事件监听器
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  })
}

/**
 * 更新相对坐标系统中的边界框预览显示 - 优化版本
 * 🔧 修复：支持旋转元素的正确预览显示
 */
function updateRelativeBoundingBoxPreview(
  selectionGroup: D3SVGGSelection,
  bounds: { x: number, y: number, width: number, height: number },
  currentZoom: number,
  rotation: number = 0, // 🔧 新增：旋转角度参数
) {
  // 批量更新DOM属性，减少重排重绘
  const handleSize = 8 / currentZoom
  const rotationHandleDistance = 25 / currentZoom
  const rotationHandleX = bounds.x + bounds.width / 2
  const rotationHandleY = bounds.y - rotationHandleDistance

  // 🔧 修复：如果元素有旋转，需要确保selectionGroup也应用相同的旋转
  if (rotation !== 0) {
    console.log(`🔧 [updateRelativeBoundingBoxPreview] Applying rotation ${rotation}° to preview`)
    // 注意：这里不直接修改selectionGroup的transform，因为它应该在外部已经设置了
    // 我们只需要确保边界框元素在旋转的坐标系中正确显示
  }

  // 使用一次性批量更新，避免多次DOM操作
  const updates = [
    // 边界框矩形
    {
      selector: '.selection-indicator',
      attrs: {
        x: bounds.x,
        y: bounds.y,
        width: bounds.width,
        height: bounds.height,
      },
    },
    // 拖拽区域
    {
      selector: '.drag-area',
      attrs: {
        x: bounds.x,
        y: bounds.y,
        width: bounds.width,
        height: bounds.height,
      },
    },
    // 旋转连接线
    {
      selector: '.rotation-line',
      attrs: {
        x1: bounds.x + bounds.width / 2,
        y1: bounds.y,
        x2: rotationHandleX,
        y2: rotationHandleY,
      },
    },
    // 旋转手柄
    {
      selector: '.rotation-handle',
      attrs: {
        cx: rotationHandleX,
        cy: rotationHandleY,
      },
    },
  ]

  // 批量应用更新
  updates.forEach((update) => {
    const element = selectionGroup.select(update.selector)
    if (!element.empty()) {
      Object.entries(update.attrs).forEach(([attr, value]) => {
        element.attr(attr, String(value))
      })
    }
  })

  // 更新缩放手柄位置（批量处理）
  const handles = [
    { id: 'nw', x: bounds.x, y: bounds.y },
    { id: 'ne', x: bounds.x + bounds.width, y: bounds.y },
    { id: 'sw', x: bounds.x, y: bounds.y + bounds.height },
    { id: 'se', x: bounds.x + bounds.width, y: bounds.y + bounds.height },
    { id: 'n', x: bounds.x + bounds.width / 2, y: bounds.y },
    { id: 'e', x: bounds.x + bounds.width, y: bounds.y + bounds.height / 2 },
    { id: 's', x: bounds.x + bounds.width / 2, y: bounds.y + bounds.height },
    { id: 'w', x: bounds.x, y: bounds.y + bounds.height / 2 },
  ]

  // 高性能批量更新手柄位置
  const handleUpdates = handles.map(handle => ({
    selector: `.resize-handle-${handle.id}`,
    attrs: {
      x: handle.x - handleSize / 2,
      y: handle.y - handleSize / 2,
    },
  }))

  // 批量应用手柄更新
  handleUpdates.forEach((update) => {
    const handleElement = selectionGroup.select(update.selector)
    if (!handleElement.empty()) {
      Object.entries(update.attrs).forEach(([attr, value]) => {
        handleElement.attr(attr, String(value))
      })
    }
  })
}

// Define rendering order for categories and layers
const MAJOR_CATEGORY_ORDER: Record<string, number> = {
  BASE: 1, // 最底层 (Z-index: 1)
  FURNITURE: 2, // 中间层 (Z-index: 2)
  CEILING: 3, // 最上层 (Z-index: 3)
}

const MINOR_CATEGORY_ORDER: Record<string, Record<string, number>> = {
  BASE: {
    ARCHITECTURE: 1, // 底层 (Z-index: 1)
    COVERINGS: 2, // 上层 (Z-index: 2)
  },
  CEILING: {
    UTILITIES: 1, // 底层 (Z-index: 1)
    LIGHTING: 2, // 上层 (Z-index: 2)
  },
  FURNITURE: {
    STORAGE: 1, // 最底层 (Z-index: 1)
    APPLIANCES: 2, // (Z-index: 2)
    BEDS: 3, // (Z-index: 3)
    TABLES: 4, // (Z-index: 4)
    SEATING: 5, // (Z-index: 5)
    DECOR: 6, // 最上层 (Z-index: 6)
  },
}

// Sorting function for shapes
function compareShapesForRendering(shapeA: ShapeModel, shapeB: ShapeModel): number {
  // 1. Compare by Major Category
  const majorA_UC = (shapeA.majorCategory ?? '').toUpperCase()
  const majorB_UC = (shapeB.majorCategory ?? '').toUpperCase()
  const orderA_Major = majorA_UC !== '' ? (typeof MAJOR_CATEGORY_ORDER[majorA_UC] === 'number' ? MAJOR_CATEGORY_ORDER[majorA_UC] : 999) : 999
  const orderB_Major = majorB_UC !== '' ? (typeof MAJOR_CATEGORY_ORDER[majorB_UC] === 'number' ? MAJOR_CATEGORY_ORDER[majorB_UC] : 999) : 999
  if (orderA_Major !== orderB_Major) {
    return orderA_Major - orderB_Major // 较小的值渲染在底部
  }

  // 2. Compare by Minor Category
  const minorA_UC = (shapeA.minorCategory ?? '').toUpperCase()
  const minorB_UC = (shapeB.minorCategory ?? '').toUpperCase()

  let orderA_Minor = 999
  if (majorA_UC !== '' && minorA_UC !== '') {
    const majorMapA = MINOR_CATEGORY_ORDER[majorA_UC]
    if (majorMapA != null && Object.prototype.hasOwnProperty.call(majorMapA, minorA_UC)) {
      orderA_Minor = majorMapA[minorA_UC] ?? 999
    }
  }

  let orderB_Minor = 999
  if (majorB_UC !== '' && minorB_UC !== '') {
    const majorMapB = MINOR_CATEGORY_ORDER[majorB_UC]
    if (majorMapB != null && Object.prototype.hasOwnProperty.call(majorMapB, minorB_UC)) {
      orderB_Minor = majorMapB[minorB_UC] ?? 999
    }
  }

  if (orderA_Minor !== orderB_Minor) {
    return orderA_Minor - orderB_Minor // 较小的值渲染在底部
  }

  // 3. Compare by zLevelId
  const zLevelA = (shapeA.zLevelId ?? '')
  const zLevelB = (shapeB.zLevelId ?? '')
  if (zLevelA !== zLevelB) {
    // 特殊处理：Default 图层始终在最底层
    if (zLevelA === 'Default') {
      return -1
    }
    if (zLevelB === 'Default') {
      return 1
    }
    // 处理Layer数字格式，例如Layer1、Layer2...
    const numA_match = zLevelA.match(/Layer(\d+)$/)
    const numB_match = zLevelB.match(/Layer(\d+)$/)

    if (numA_match && numB_match) {
      const numA = Number.parseInt(numA_match[1], 10)
      const numB = Number.parseInt(numB_match[1], 10)
      if (numA !== numB) {
        // 数字大的Layer显示在上面，符合Layer1 < Layer2 < Layer3的规则
        return numA - numB
      }
    }
    // 如果其中一个是Layer格式，另一个不是，则Layer格式应该显示在上面
    if (numA_match && !numB_match) {
      return 1
    }
    if (!numA_match && numB_match) {
      return -1
    }
    // Fallback to full string comparison
    return zLevelA.localeCompare(zLevelB)
  }

  // 4. Compare by intraLayerZIndex - 如果在同一图层内（相同的majorCategory、minorCategory和zLevelId）
  // 较大的intraLayerZIndex应该显示在较小的上面
  const intraLayerZIndexA = shapeA.intraLayerZIndex ?? 0
  const intraLayerZIndexB = shapeB.intraLayerZIndex ?? 0
  if (intraLayerZIndexA !== intraLayerZIndexB) {
    // 较大的值渲染在顶部，与前面的逻辑保持一致
    return intraLayerZIndexA - intraLayerZIndexB
  }

  // 5. Fallback: Compare by creation timestamp (older first)
  const createdAtA = shapeA.metadata?.createdAt ?? 0
  const createdAtB = shapeB.metadata?.createdAt ?? 0
  if (createdAtA !== createdAtB) {
    return createdAtA - createdAtB // 较早创建的在底部
  }

  // 6. Final fallback: ID comparison for absolute stability
  return shapeA.id.localeCompare(shapeB.id)
}

interface ShapeRendererProps {
  /**
   * The shapes layer group element ref (SVGGElement | null)
   * D3 selection will be performed internally.
   */
  shapesLayerRef: React.RefObject<SVGGElement | null>

  /**
   * Array of shapes to render
   */
  shapes: ShapeModel[]

  /**
   * IDs of selected shapes
   */
  selectedIds: string[]

  /**
   * Current zoom level
   */
  currentZoom: number

  /**
   * Handler for shape selection
   */
  onShapeSelected: (id: string, isMultiSelect: boolean) => void

  /**
   * Indicates if canvas is currently in pan mode
   */
  isPanMode: boolean
}

// Define an interface for individual shape rendering functions
interface ShapeTypeRenderer {
  (
    group: d3.Selection<SVGGElement, ShapeModel, d3.BaseType, unknown>,
    shapeData: ShapeModel,
    // Pass the full style properties object, and specific dimensions needed
    styleAttributes: { fill: string, stroke: string, strokeWidth: number, opacity: number },
    currentZoom: number,
    // Add DEFAULT_ICON_WIDTH/HEIGHT for consistency in fallbacks if needed by generic shapes
    defaultWidth: number,
    defaultHeight: number
  ): void
}

// Helper function to render special elements (icons or their fallbacks)
function renderSpecialElement(group: D3ShapeGroupSelection, shapeData: ShapeModel, specialElementInfo: SpecialElementDetails, styleAttributes: { fill: string, stroke: string, strokeWidth: number, opacity: number }, currentZoom: number, defaultWidth: number, defaultHeight: number): void {
  if (specialElementInfo.attemptedIconPath != null && specialElementInfo.attemptedIconPath !== '') {
    const imgWidth = typeof shapeData.properties?.width === 'number' ? shapeData.properties.width : defaultWidth
    const imgHeight = typeof shapeData.properties?.height === 'number' ? shapeData.properties.height : defaultHeight
    const imgX = 0 // Relative to the group's transform, assuming icon is anchored top-left
    const imgY = 0 // Relative to the group's transform

    group.append('image')
      .attr('xlink:href', specialElementInfo.attemptedIconPath)
      .attr('x', imgX)
      .attr('y', imgY)
      .attr('width', imgWidth)
      .attr('height', imgHeight)
      .attr('opacity', styleAttributes.opacity)
      .on('error', function (this: SVGImageElement) {
        _warn(`[ShapeRenderer] Icon load ERROR for ${shapeData.id} (${specialElementInfo.displayName}) at path ${specialElementInfo.attemptedIconPath}. Rendering fallback.`)
        d3.select(this).remove()
        group.append('rect')
          .attr('class', 'fallback-text-bg') // Keep original class for potential styling
          .attr('x', imgX)
          .attr('y', imgY)
          .attr('width', imgWidth)
          .attr('height', imgHeight)
          .attr('fill', '#eee')
          .attr('stroke', '#ccc')
          .attr('stroke-width', 1 / currentZoom) // Fallback stroke width might need adjustment
        group.append('text')
          .attr('class', 'fallback-text')
          .attr('x', imgX + imgWidth / 2)
          .attr('y', imgY + imgHeight / 2)
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'central')
          .attr('font-size', `${10 / currentZoom}px`)
          .attr('fill', '#555')
          .text(specialElementInfo.displayName)
      })
  }
  else { // isSpecial but no icon path, render text fallback directly
    const fallbackWidth = typeof shapeData.properties?.width === 'number' ? shapeData.properties.width : defaultWidth
    const fallbackHeight = typeof shapeData.properties?.height === 'number' ? shapeData.properties.height : defaultHeight
    const fallbackX = 0
    const fallbackY = 0

    group.append('rect')
      .attr('class', 'fallback-text-bg') // Keep original class
      .attr('x', fallbackX)
      .attr('y', fallbackY)
      .attr('width', fallbackWidth)
      .attr('height', fallbackHeight)
      .attr('fill', '#f0f0f0')
      .attr('stroke', '#ddd')
      .attr('stroke-width', 1 / currentZoom) // Fallback stroke width
    group.append('text')
      .attr('class', 'fallback-text')
      .attr('x', fallbackX + fallbackWidth / 2)
      .attr('y', fallbackY + fallbackHeight / 2)
      .attr('text-anchor', 'middle')
      .attr('dominant-baseline', 'central')
      .attr('font-size', `${10 / currentZoom}px`)
      .attr('fill', '#333')
      .text(specialElementInfo.displayName)
  }
}

// Individual shape rendering functions
const drawRectangle: ShapeTypeRenderer = (group, shapeData, styleAttributes, _currentZoom, defaultWidth, defaultHeight) => {
  // 日志输出
  // console.log('[ShapeRenderer][drawRectangle] id:', shapeData.id, 'width:', shapeData.width, 'height:', shapeData.height, 'rotation:', shapeData.rotation, 'cornerRadius:', shapeData.cornerRadius)
  // 优先读取顶层属性
  const cornerRadius = typeof shapeData.cornerRadius === 'number' && Number.isFinite(shapeData.cornerRadius)
    ? shapeData.cornerRadius
    : (typeof shapeData.properties?.cornerRadius === 'number' && Number.isFinite(shapeData.properties.cornerRadius)
        ? shapeData.properties.cornerRadius
        : 0)

  const width = typeof shapeData.width === 'number' && Number.isFinite(shapeData.width)
    ? shapeData.width
    : (typeof shapeData.properties?.width === 'number' && Number.isFinite(shapeData.properties.width)
        ? shapeData.properties.width
        : defaultWidth)

  const height = typeof shapeData.height === 'number' && Number.isFinite(shapeData.height)
    ? shapeData.height
    : (typeof shapeData.properties?.height === 'number' && Number.isFinite(shapeData.properties.height)
        ? shapeData.properties.height
        : defaultHeight)

  const rectX = -width / 2 // Center for rotation
  const rectY = -height / 2 // Center for rotation

  group.append('rect')
    .attr('x', rectX)
    .attr('y', rectY)
    .attr('width', width)
    .attr('height', height)
    .attr('rx', cornerRadius)
    .attr('ry', cornerRadius)
    .attr('fill', styleAttributes.fill)
    .attr('stroke', styleAttributes.stroke)
    .attr('stroke-width', styleAttributes.strokeWidth)
    .attr('opacity', styleAttributes.opacity)
}

const drawCircle: ShapeTypeRenderer = (group, shapeData, styleAttributes, _currentZoom, defaultWidth, _defaultHeight) => {
  // 日志输出
  // console.log('[ShapeRenderer][drawCircle] id:', shapeData.id, 'radius:', shapeData.radius, 'rotation:', shapeData.rotation)
  const radius = typeof shapeData.radius === 'number' && Number.isFinite(shapeData.radius)
    ? shapeData.radius
    : (typeof shapeData.properties?.radius === 'number' && Number.isFinite(shapeData.properties.radius)
        ? shapeData.properties.radius
        : defaultWidth / 2)

  group.append('circle')
    .attr('cx', 0) // Centered in group
    .attr('cy', 0) // Centered in group
    .attr('r', radius)
    .attr('fill', styleAttributes.fill)
    .attr('stroke', styleAttributes.stroke)
    .attr('stroke-width', styleAttributes.strokeWidth)
    .attr('opacity', styleAttributes.opacity)
}

const drawEllipse: ShapeTypeRenderer = (group, shapeData, styleAttributes, _currentZoom, defaultWidth, defaultHeight) => {
  // 日志输出
  // console.log('[ShapeRenderer][drawEllipse] id:', shapeData.id, 'radiusX:', shapeData.radiusX, 'radiusY:', shapeData.radiusY, 'width:', shapeData.width, 'height:', shapeData.height, 'rotation:', shapeData.rotation)

  // 优先使用 radiusX 和 radiusY 属性，如果没有则回退到 width/height
  const shapeWithRadii = shapeData as ShapeModelWithRadii
  const rx = (typeof shapeWithRadii.radiusX === 'number' && Number.isFinite(shapeWithRadii.radiusX))
    ? shapeWithRadii.radiusX
    : (typeof shapeData.properties?.radiusX === 'number' && Number.isFinite(shapeData.properties.radiusX))
        ? shapeData.properties.radiusX
        : (typeof shapeData.width === 'number' && Number.isFinite(shapeData.width))
            ? shapeData.width / 2
            : (typeof shapeData.properties?.width === 'number' && Number.isFinite(shapeData.properties.width))
                ? shapeData.properties.width / 2
                : defaultWidth / 2

  const ry = (typeof shapeWithRadii.radiusY === 'number' && Number.isFinite(shapeWithRadii.radiusY))
    ? shapeWithRadii.radiusY
    : (typeof shapeData.properties?.radiusY === 'number' && Number.isFinite(shapeData.properties.radiusY))
        ? shapeData.properties.radiusY
        : (typeof shapeData.height === 'number' && Number.isFinite(shapeData.height))
            ? shapeData.height / 2
            : (typeof shapeData.properties?.height === 'number' && Number.isFinite(shapeData.properties.height))
                ? shapeData.properties.height / 2
                : defaultHeight / 2

  group.append('ellipse')
    .attr('cx', 0)
    .attr('cy', 0)
    .attr('rx', rx)
    .attr('ry', ry)
    .attr('fill', styleAttributes.fill)
    .attr('stroke', styleAttributes.stroke)
    .attr('stroke-width', styleAttributes.strokeWidth)
    .attr('opacity', styleAttributes.opacity)
}

const drawLine: ShapeTypeRenderer = (group, shapeData, styleAttributes, _currentZoom, _defaultWidth, _defaultHeight) => {
  const props = shapeData.properties
  const start = props?.start as Point | undefined
  const end = props?.end as Point | undefined

  // console.log(`[drawLine] Rendering line ${shapeData.id}`)

  if (start && end) {
    // 🔧 修复：直接使用相对坐标，不进行额外的中心化
    // 因为start和end已经是相对于shapeData.position的坐标
    // console.log(`[drawLine] Drawing line ${shapeData.id} with coordinates: x1=${start.x}, y1=${start.y}, x2=${end.x}, y2=${end.y}`)

    group.append('line')
      .attr('x1', start.x)
      .attr('y1', start.y)
      .attr('x2', end.x)
      .attr('y2', end.y)
      .attr('stroke', styleAttributes.stroke)
      .attr('stroke-width', styleAttributes.strokeWidth)
      .attr('opacity', styleAttributes.opacity)
  }
  else {
    _warn(`[ShapeRenderer] Line ${shapeData.id} has insufficient points. Properties:`, props)
    _warn(`[ShapeRenderer] Line ${shapeData.id} full shape data:`, JSON.stringify(shapeData, null, 2))
  }
}

const drawPolyline: ShapeTypeRenderer = (group, shapeData, styleAttributes, _currentZoom, _defaultWidth, _defaultHeight) => {
  const props = shapeData.properties
  const points = props?.points as Point[] | undefined
  if (points && points.length >= 2) {
    // 🔧 修复：直接使用相对坐标，不进行额外的中心化
    // 因为points已经是相对于shapeData.position的坐标
    const pointsString = points.map(p => `${p.x},${p.y}`).join(' ')
    group.append('polyline')
      .attr('points', pointsString)
      .attr('fill', 'none')
      .attr('stroke', styleAttributes.stroke)
      .attr('stroke-width', styleAttributes.strokeWidth)
      .attr('opacity', styleAttributes.opacity)
  }
  else {
    _warn(`[ShapeRenderer] Polyline ${shapeData.id} has insufficient points.`)
  }
}

const drawPolygon: ShapeTypeRenderer = (group, shapeData, styleAttributes, _currentZoom, _defaultWidth, _defaultHeight) => {
  let points: Point[] | undefined
  const shapeWithPoints = shapeData as ShapeModelWithPoints
  if (shapeWithPoints.points && Array.isArray(shapeWithPoints.points) && shapeWithPoints.points.length >= 3) {
    points = shapeWithPoints.points as Point[]
  }
  else if (shapeData.properties !== undefined && shapeData.properties.points !== undefined && Array.isArray(shapeData.properties.points) && shapeData.properties.points.length >= 3) {
    points = shapeData.properties.points as Point[]
  }
  if (points && points.length >= 3) {
    // 计算几何中心
    const cx = points.reduce((sum, p) => sum + p.x, 0) / points.length
    const cy = points.reduce((sum, p) => sum + p.y, 0) / points.length
    // 中心化所有点
    const centeredPoints = points.map(p => ({ x: p.x - cx, y: p.y - cy }))
    const pointsString = centeredPoints.map(p => `${p.x},${p.y}`).join(' ')
    group.append('polygon')
      .attr('points', pointsString)
      .attr('fill', styleAttributes.fill)
      .attr('stroke', styleAttributes.stroke)
      .attr('stroke-width', styleAttributes.strokeWidth)
      .attr('opacity', styleAttributes.opacity)
  }
  else {
    _warn(`[ShapeRenderer] Polygon ${shapeData.id} has insufficient points. properties.points:`, shapeData.properties?.points)
  }
}

const drawArc: ShapeTypeRenderer = (group, shapeData, styleAttributes, _currentZoom, _defaultWidth, _defaultHeight) => {
  const props = shapeData.properties

  // 🔧 调试日志：检查ARC渲染时的数据
  console.log(`[drawArc] Rendering arc ${shapeData.id}:`, {
    position: shapeData.position,
    radius: props?.radius,
    startAngle: props?.startAngle,
    endAngle: props?.endAngle,
    counterClockwise: props?.counterClockwise,
    pathData: props?.pathData,
    hasPathData: typeof props?.pathData === 'string' && props?.pathData !== '',
  })

  // Check if pathData is provided
  const pathDataFromProps = props?.pathData
  if (typeof pathDataFromProps === 'string' && pathDataFromProps !== '') {
    // Use pre-calculated SVG path data
    console.log(`[drawArc] Using pre-calculated pathData for arc ${shapeData.id}:`, pathDataFromProps)
    group
      .append('path')
      .attr('d', pathDataFromProps)
      .attr('fill', 'none')
      .attr('stroke', styleAttributes.stroke)
      .attr('stroke-width', styleAttributes.strokeWidth)
      .attr('opacity', styleAttributes.opacity)
    return
  }

  // Fallback: generate arc from properties
  const radius = props?.radius
  const startAngle = props?.startAngle
  const endAngle = props?.endAngle
  const counterClockwise = props?.counterClockwise

  console.log(`[drawArc] No pathData, generating from properties for arc ${shapeData.id}:`, {
    radius,
    startAngle,
    endAngle,
    counterClockwise,
  })

  if (
    typeof radius === 'number' && radius > 0
    && typeof startAngle === 'number'
    && typeof endAngle === 'number'
  ) {
    // Generate arc path data manually
    // 圆心在group的原点(0,0)，因为shapeData.position已经设置为实际圆心位置
    const center = { x: 0, y: 0, z: 0 } // Relative to group origin

    // Convert angles to radians
    const startRad = (startAngle * Math.PI) / 180
    const endRad = (endAngle * Math.PI) / 180

    // Calculate start and end points relative to center (0,0)
    const startX = center.x + radius * Math.cos(startRad)
    const startY = center.y + radius * Math.sin(startRad)
    const endX = center.x + radius * Math.cos(endRad)
    const endY = center.y + radius * Math.sin(endRad)

    // Calculate sweep angle
    let sweepAngle = endAngle - startAngle
    const isCounterClockwise = counterClockwise === true
    if (isCounterClockwise) {
      if (sweepAngle > 0) {
        sweepAngle -= 360
      }
    }
    else {
      if (sweepAngle < 0) {
        sweepAngle += 360
      }
    }

    // Determine if this is a large arc (> 180 degrees)
    const largeArcFlag = Math.abs(sweepAngle) > 180 ? 1 : 0

    // Sweep direction flag (0 = counterclockwise, 1 = clockwise)
    const sweepFlag = isCounterClockwise ? 0 : 1

    // Generate SVG arc path
    const pathData = `M ${startX} ${startY} A ${radius} ${radius} 0 ${largeArcFlag} ${sweepFlag} ${endX} ${endY}`

    // Debug logging for arc rendering
    // console.warn(`[ShapeRenderer] Arc ${shapeData.id} rendering:`, {
    //   center, radius, startAngle, endAngle,
    //   startPoint: { x: startX, y: startY },
    //   endPoint: { x: endX, y: endY },
    //   pathData, shapePosition: shapeData.position
    // })

    group
      .append('path')
      .attr('d', pathData)
      .attr('fill', 'none')
      .attr('stroke', styleAttributes.stroke)
      .attr('stroke-width', styleAttributes.strokeWidth)
      .attr('opacity', styleAttributes.opacity)
  }
  else {
    _warn(`[ShapeRenderer] Arc ${shapeData.id} has invalid properties:`, { radius, startAngle, endAngle })
  }
}

const drawQuadraticBezier: ShapeTypeRenderer = (group, shapeData, styleAttributes, _currentZoom, _defaultWidth, _defaultHeight) => {
  const props = shapeData.properties
  const pathDataFromProps = props?.pathData
  if (typeof pathDataFromProps === 'string' && pathDataFromProps !== '') {
    group
      .append('path')
      .attr('d', pathDataFromProps)
      .attr('fill', 'none')
      .attr('stroke', styleAttributes.stroke)
      .attr('stroke-width', styleAttributes.strokeWidth)
      .attr('opacity', styleAttributes.opacity)
  }
  else if (
    props?.start != null
    && typeof props.start === 'object'
    && 'x' in props.start
    && typeof (props.start as Point).x === 'number'
    && 'y' in props.start
    && typeof (props.start as Point).y === 'number'
    && props.control != null
    && typeof props.control === 'object'
    && 'x' in props.control
    && typeof (props.control as Point).x === 'number'
    && 'y' in props.control
    && typeof (props.control as Point).y === 'number'
    && props.end != null
    && typeof props.end === 'object'
    && 'x' in props.end
    && typeof (props.end as Point).x === 'number'
    && 'y' in props.end
    && typeof (props.end as Point).y === 'number'
  ) {
    const p0 = props.start as Point
    const p1 = props.control as Point
    const p2 = props.end as Point
    // 🔧 修复：直接使用相对坐标，不进行额外的中心化
    const pathD = `M ${p0.x},${p0.y} Q ${p1.x},${p1.y} ${p2.x},${p2.y}`
    group.append('path')
      .attr('d', pathD)
      .attr('fill', 'none')
      .attr('stroke', styleAttributes.stroke)
      .attr('stroke-width', styleAttributes.strokeWidth)
      .attr('opacity', styleAttributes.opacity)
  }
  else {
    _warn(`[ShapeRenderer] Quadratic Bezier ${shapeData.id} has insufficient points.`)
  }
}

const drawCubicBezier: ShapeTypeRenderer = (group, shapeData, styleAttributes, _currentZoom, _defaultWidth, _defaultHeight) => {
  const props = shapeData.properties
  const pathDataFromProps = props?.pathData
  if (typeof pathDataFromProps === 'string' && pathDataFromProps !== '') {
    group
      .append('path')
      .attr('d', pathDataFromProps)
      .attr('fill', 'none')
      .attr('stroke', styleAttributes.stroke)
      .attr('stroke-width', styleAttributes.strokeWidth)
      .attr('opacity', styleAttributes.opacity)
  }
  else if (
    props?.start != null
    && typeof props.start === 'object'
    && 'x' in props.start
    && typeof (props.start as Point).x === 'number'
    && 'y' in props.start
    && typeof (props.start as Point).y === 'number'
    && props.control1 != null
    && typeof props.control1 === 'object'
    && 'x' in props.control1
    && typeof (props.control1 as Point).x === 'number'
    && 'y' in props.control1
    && typeof (props.control1 as Point).y === 'number'
    && props.control2 != null
    && typeof props.control2 === 'object'
    && 'x' in props.control2
    && typeof (props.control2 as Point).x === 'number'
    && 'y' in props.control2
    && typeof (props.control2 as Point).y === 'number'
    && props.end != null
    && typeof props.end === 'object'
    && 'x' in props.end
    && typeof (props.end as Point).x === 'number'
    && 'y' in props.end
    && typeof (props.end as Point).y === 'number'
  ) {
    const p0 = props.start as Point
    const p1 = props.control1 as Point
    const p2 = props.control2 as Point
    const p3 = props.end as Point
    // 🔧 修复：直接使用相对坐标，不进行额外的中心化
    const pathD = `M ${p0.x},${p0.y} C ${p1.x},${p1.y} ${p2.x},${p2.y} ${p3.x},${p3.y}`
    group.append('path')
      .attr('d', pathD)
      .attr('fill', 'none')
      .attr('stroke', styleAttributes.stroke)
      .attr('stroke-width', styleAttributes.strokeWidth)
      .attr('opacity', styleAttributes.opacity)
  }
  else {
    _warn(`[ShapeRenderer] Cubic Bezier ${shapeData.id} has insufficient points.`)
  }
}

const drawText: ShapeTypeRenderer = (group, shapeData, styleAttributes, _currentZoom, defaultWidth, defaultHeight) => {
  const props = shapeData.properties

  // 🔧 修复：支持多种文本内容属性路径（包括顶层属性）
  const textFromProps = props?.text
  const contentFromProps = props?.content
  const shapeWithText = shapeData as ShapeModelWithText
  const textFromTopLevel = shapeWithText.text
  const contentFromTopLevel = shapeWithText.content
  const textContent
    = typeof textFromProps === 'string' && textFromProps !== ''
      ? textFromProps
      : typeof contentFromProps === 'string' && contentFromProps !== ''
        ? contentFromProps
        : typeof textFromTopLevel === 'string' && textFromTopLevel !== ''
          ? textFromTopLevel
          : typeof contentFromTopLevel === 'string' && contentFromTopLevel !== ''
            ? contentFromTopLevel
            : 'Text'

  // 🔧 修复：支持fontSize属性更新（独立于边界框大小，包括顶层属性）
  const fontSizeFromProps = props?.fontSize
  const fontSizeFromTopLevel = shapeWithText.fontSize
  const fontSize = typeof fontSizeFromProps === 'number' && Number.isFinite(fontSizeFromProps)
    ? fontSizeFromProps
    : typeof fontSizeFromTopLevel === 'number' && Number.isFinite(fontSizeFromTopLevel)
      ? fontSizeFromTopLevel
      : 16
  // 🔧 修复：让文本随画布缩放，而不是保持固定大小
  const scaledFontSize = fontSize

  // 🔧 修复：获取边界框尺寸（独立于字体大小，不需要除以缩放）
  const boundingBoxWidth = typeof shapeData.width === 'number' && Number.isFinite(shapeData.width) ? shapeData.width : defaultWidth
  const boundingBoxHeight = typeof shapeData.height === 'number' && Number.isFinite(shapeData.height) ? shapeData.height : defaultHeight

  // 🔧 修复：支持fontFamily属性更新（优先从顶层获取，然后从properties）
  const fontFamilyFromProps = props?.fontFamily
  const fontFamilyFromTopLevel = shapeWithText.fontFamily
  const fontFamily = typeof fontFamilyFromTopLevel === 'string' && fontFamilyFromTopLevel !== ''
    ? fontFamilyFromTopLevel
    : typeof fontFamilyFromProps === 'string' && fontFamilyFromProps !== ''
      ? fontFamilyFromProps
      : 'sans-serif'

  // 🔧 修复：支持fontStyle和fontWeight属性（优先从顶层获取，然后从properties）
  const fontStyleFromProps = props?.fontStyle
  const fontStyleFromTopLevel = shapeWithText.fontStyle
  const fontStyle = typeof fontStyleFromTopLevel === 'string' && fontStyleFromTopLevel !== ''
    ? fontStyleFromTopLevel
    : typeof fontStyleFromProps === 'string' && fontStyleFromProps !== ''
      ? fontStyleFromProps
      : 'normal'

  const fontWeightFromProps = props?.fontWeight
  const fontWeightFromTopLevel = shapeWithText.fontWeight
  const fontWeight = typeof fontWeightFromTopLevel === 'string' || typeof fontWeightFromTopLevel === 'number'
    ? String(fontWeightFromTopLevel)
    : typeof fontWeightFromProps === 'string' || typeof fontWeightFromProps === 'number'
      ? String(fontWeightFromProps)
      : 'normal'

  // 🔧 修复：支持textAlign属性（优先从顶层获取，然后从properties）
  const textAlignFromProps = props?.textAlign
  const textAlignFromTopLevel = shapeWithText.textAlign
  const textAlign = typeof textAlignFromTopLevel === 'string' && textAlignFromTopLevel !== ''
    ? textAlignFromTopLevel
    : typeof textAlignFromProps === 'string' && textAlignFromProps !== ''
      ? textAlignFromProps
      : 'left'

  // 🔧 关键修复：根据textAlign计算文本在边界框内的x位置
  let textX = 0
  let textAnchor = 'middle'
  switch (textAlign) {
    case 'left':
      textX = -boundingBoxWidth / 2
      textAnchor = 'start'
      break
    case 'right':
      textX = boundingBoxWidth / 2
      textAnchor = 'end'
      break
    case 'center':
    default:
      textX = 0
      textAnchor = 'middle'
      break
  }

  // 🔧 修复：计算文本y位置，从边界框顶部开始而不是居中
  const textY = -boundingBoxHeight / 2 + fontSize * 0.8 // 从顶部开始，稍微向下偏移

  const strokeFromProps = props?.stroke
  const textStroke = typeof strokeFromProps === 'string' && strokeFromProps !== '' ? strokeFromProps : 'none'

  const strokeWidthFromProps = props?.strokeWidth
  const textStrokeWidth
    = (typeof strokeWidthFromProps === 'number' && Number.isFinite(strokeWidthFromProps) ? strokeWidthFromProps : 0)

  console.log(`🔧 [drawText] Rendering text with independent controls:`, {
    id: shapeData.id,
    textContent,
    fontSize,
    scaledFontSize,
    boundingBox: { width: boundingBoxWidth, height: boundingBoxHeight },
    fontFamily,
    fontStyle,
    fontWeight,
    textAlign,
    textX,
    textAnchor,
    groupExists: group !== null && group !== undefined,
    groupTagName: group?.node?.()?.tagName,
  })

  // 🔧 修复：移除总是显示的边界框，只在选中时通过选择逻辑显示边界框
  // 边界框现在只在元素被选中时由 renderSingleBoundingBox 函数处理

  // 🔧 关键修复：在边界框内渲染文本，支持独立的字体大小和对齐
  try {
    console.log(`🔧 [drawText] Adding text element...`)
    group.append('text')
      .attr('x', textX) // 🔧 根据textAlign计算的x位置
      .attr('y', textY) // 🔧 修复：从边界框顶部开始渲染
      .attr('font-family', fontFamily)
      .attr('font-size', `${scaledFontSize}px`) // 🔧 独立的字体大小
      .attr('font-style', fontStyle)
      .attr('font-weight', fontWeight)
      .attr('fill', styleAttributes.fill)
      .attr('stroke', textStroke)
      .attr('stroke-width', textStrokeWidth)
      .attr('opacity', styleAttributes.opacity)
      .style('dominant-baseline', 'hanging') // 🔧 修复：从顶部开始渲染
      .style('text-anchor', textAnchor) // 🔧 根据textAlign设置的水平对齐
      .text(textContent)
    console.log(`🔧 [drawText] Text element added successfully`)
  }
  catch (error) {
    _warn(`🔧 [drawText] Error adding text element:`, error)
  }
}

const drawImage: ShapeTypeRenderer = (group, shapeData, styleAttributes, currentZoom, defaultWidth, defaultHeight) => {
  const imageProps = shapeData.properties
  const srcFromProps = imageProps?.src
  const imageUrl = typeof srcFromProps === 'string' && srcFromProps !== '' ? srcFromProps : null

  const widthFromProps = imageProps?.width
  const imageWidth = typeof widthFromProps === 'number' && Number.isFinite(widthFromProps) ? widthFromProps : defaultWidth

  const heightFromProps = imageProps?.height
  const imageHeight = typeof heightFromProps === 'number' && Number.isFinite(heightFromProps) ? heightFromProps : defaultHeight

  // 将图片居中，而不是左上角对齐
  const imgX = -imageWidth / 2
  const imgY = -imageHeight / 2

  if (imageUrl != null && imageUrl !== '') {
    // 🗑️ 简化：只保留基础的图片渲染，删除有问题的边框和圆角
    group.append('image')
      .attr('xlink:href', imageUrl)
      .attr('x', imgX)
      .attr('y', imgY)
      .attr('width', imageWidth)
      .attr('height', imageHeight)
      .attr('opacity', styleAttributes.opacity)
      .on('error', function (this: SVGImageElement) {
        _warn(`[ShapeRenderer] Standard image load ERROR for ${shapeData.id} at ${imageUrl}.`)
        d3.select(this).remove()
        const nameFromMetadata = shapeData.metadata?.name as unknown
        const nameFromPropsRaw = imageProps?.name
        const nameFromProps = typeof nameFromPropsRaw === 'string' ? nameFromPropsRaw : undefined
        const errorText
          = typeof nameFromMetadata === 'string' && nameFromMetadata !== ''
            ? nameFromMetadata
            : (nameFromProps != null && nameFromProps !== '')
                ? nameFromProps
                : 'Error'
        // 获取旋转角度
        const rotation = shapeData.rotation ?? 0

        group
          .append('rect')
          .attr('x', imgX)
          .attr('y', imgY)
          .attr('width', imageWidth)
          .attr('height', imageHeight)
          .attr('fill', '#fee')
          .attr('stroke', 'red')
          .attr('stroke-width', 1 / currentZoom)
          .attr('transform', `rotate(${rotation})`) // 应用旋转变换
        group
          .append('text')
          .attr('x', 0) // 使用中心点坐标
          .attr('y', 0) // 使用中心点坐标
          .text(errorText.substring(0, 15))
          .attr('fill', 'red')
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'central')
          .attr('font-size', `${8 / currentZoom}px`)
          .attr('transform', `rotate(${rotation})`) // 应用旋转变换
      })
  }
  else {
    _warn(`[ShapeRenderer] Standard image type for ${shapeData.id} has no src. Rendering placeholder.`)
    const nameFromMetadata = shapeData.metadata?.name as unknown
    const nameFromPropsRaw = imageProps?.name
    const nameFromProps = typeof nameFromPropsRaw === 'string' ? nameFromPropsRaw : undefined
    const placeholderText
      = typeof nameFromMetadata === 'string' && nameFromMetadata !== ''
        ? nameFromMetadata
        : (nameFromProps != null && nameFromProps !== '')
            ? nameFromProps
            : 'image'

    // 获取旋转角度
    const rotation = shapeData.rotation ?? 0

    group
      .append('rect')
      .attr('x', imgX)
      .attr('y', imgY)
      .attr('width', imageWidth)
      .attr('height', imageHeight)
      .attr('fill', '#ddd')
      .attr('transform', `rotate(${rotation})`) // 应用旋转变换
    group
      .append('text')
      .attr('x', 0) // 使用中心点坐标
      .attr('y', 0) // 使用中心点坐标
      .text(placeholderText.substring(0, 15))
      .attr('fill', '#333')
      .attr('text-anchor', 'middle')
      .attr('dominant-baseline', 'central')
      .attr('font-size', `${8 / currentZoom}px`)
      .attr('transform', `rotate(${rotation})`) // 应用旋转变换
  }
}

// describeArcPathForRenderer 函数已删除

/**
 * Component for rendering shapes on the canvas
 */
export function ShapeRenderer({
  shapesLayerRef,
  shapes,
  selectedIds,
  currentZoom,
  onShapeSelected,
  isPanMode,
}: ShapeRendererProps): React.JSX.Element | null {
  // const { logger } = useServices();
  // const { iconManifest } = useConfig();

  const DEFAULT_ICON_WIDTH = 50
  const DEFAULT_ICON_HEIGHT = 50

  const shapeRenderers = useCallback((): Record<string, ShapeTypeRenderer> => {
    return {
      [CoreElementType.RECTANGLE]: drawRectangle,
      [CoreElementType.SQUARE]: drawRectangle,
      [CoreElementType.CIRCLE]: drawCircle,
      [CoreElementType.ELLIPSE]: drawEllipse,
      [CoreElementType.LINE]: drawLine,
      [CoreElementType.POLYLINE]: drawPolyline,
      [CoreElementType.POLYGON]: drawPolygon,
      [CoreElementType.TRIANGLE]: drawPolygon,
      [CoreElementType.QUADRILATERAL]: drawPolygon,
      [CoreElementType.PENTAGON]: drawPolygon,
      [CoreElementType.HEXAGON]: drawPolygon,
      [CoreElementType.ARC]: drawArc,
      [CoreElementType.QUADRATIC]: drawQuadraticBezier,
      [CoreElementType.CUBIC]: drawCubicBezier,
      [CoreElementType.TEXT]: drawText,
      [CoreElementType.IMAGE]: drawImage,
      [CoreElementType.FURNITURE]: drawImage,
      [CoreElementType.FIXTURE]: drawImage,
      [CoreElementType.LIGHT]: drawImage,
      [CoreElementType.FLOOR_AREA]: drawImage,
      [CoreElementType.WALL_PAINT]: drawImage,
      [CoreElementType.WALL_PAPER]: drawImage,
      [CoreElementType.OPENING]: drawImage,
      // Add other types as needed
    }
  }, [])

  useEffect(() => {
    if (!shapesLayerRef.current) {
      console.log('[ShapeRenderer] shapesLayerRef.current is null, returning')
      return
    }

    console.log('[ShapeRenderer] shapesLayerRef.current:', shapesLayerRef.current)
    console.log('[ShapeRenderer] shapesLayerRef.current.tagName:', shapesLayerRef.current.tagName)
    console.log('[ShapeRenderer] shapesLayerRef.current.parentElement:', shapesLayerRef.current.parentElement)

    // DEBUG: Log shapes received by ShapeRenderer
    console.log(`[ShapeRenderer] Processing ${shapes.length} shapes`)
    shapes.forEach((shape, i) => {
      console.log(`[ShapeRenderer] Shape ${i}: id=${shape.id}, hasPattern=${!!shape.pattern}`)
      if (shape.pattern) {
        console.log(`[ShapeRenderer] Pattern details:`, JSON.stringify(shape.pattern, null, 2))
      }
    })

    // --- 1. 先处理 pattern defs ---
    const parentElem = shapesLayerRef.current?.parentElement
    console.log('[ShapeRenderer] parentElem:', parentElem)
    console.log('[ShapeRenderer] parentElem.tagName:', parentElem?.tagName)

    let svgElemRaw: SVGSVGElement | undefined
    if (parentElem && 'ownerSVGElement' in parentElem && parentElem.ownerSVGElement instanceof window.SVGSVGElement) {
      svgElemRaw = parentElem.ownerSVGElement
      console.log('[ShapeRenderer] Found SVG via ownerSVGElement')
    }
    else if (parentElem instanceof window.SVGSVGElement) {
      svgElemRaw = parentElem
      console.log('[ShapeRenderer] parentElem is SVG')
    }
    else {
      console.log('[ShapeRenderer] No SVG found, returning')
      return
    }
    console.log('[ShapeRenderer] svgElemRaw:', svgElemRaw)
    const svgRoot = d3.select(svgElemRaw)
    let defs = svgRoot.select<SVGDefsElement>('defs')
    if (defs.empty()) {
      defs = svgRoot.insert<SVGDefsElement>('defs', ':first-child')
      console.log('[ShapeRenderer] Created new defs element')
    }
    defs.selectAll('*').remove() // 每次都彻底清空 pattern
    console.log('[ShapeRenderer] Cleared existing patterns from defs')

    // DEBUG: 检查SVG元素的实际状态
    console.log('[ShapeRenderer] SVG element check:')
    console.log('  SVG tagName:', svgElemRaw.tagName)
    console.log('  SVG children count:', svgElemRaw.children.length)
    console.log('  SVG id:', svgElemRaw.id)
    console.log('  SVG class:', svgElemRaw.getAttribute('class'))

    // --- 2. 为每个 shape 生成 pattern（如有） ---
    const patternIdMap: Record<string, string> = {}
    shapes.forEach((shape) => {
      console.log(`[ShapeRenderer] Processing shape ${shape.id} for pattern`)
      const safePattern = shape.pattern ? cleanPattern(shape.pattern) : undefined
      console.log(`[ShapeRenderer] SafePattern for ${shape.id}:`, safePattern)
      if (safePattern) {
        console.log(`[ShapeRenderer] Creating pattern for shape ${shape.id}, type: ${safePattern.type}`)
        if (safePattern.type === 'lines' && safePattern.linesOptions) {
          // 简化patternId，避免过长的ID导致引用问题
          const patternId = `${shape.id}-lines-pattern`
          patternIdMap[shape.id] = patternId
          // 解析 linesOptions，强制 number 类型
          const { size = 10, strokeWidth = 1, orientation = 'crosshatch', background = '#fff', stroke, color } = safePattern.linesOptions
          const cell = Math.max(4, Number(size))
          const lineWidth = Math.max(0.1, Number(strokeWidth))

          // 优先使用 color 属性，然后是 stroke 属性，最后是默认值
          let strokeColor = '#333'
          if (typeof color === 'string' && color) {
            strokeColor = color
          }
          else if (typeof stroke === 'string' && stroke) {
            strokeColor = stroke
          }

          // 生成 <pattern> 元素
          const pat = defs.append('pattern')
            .attr('id', patternId)
            .attr('patternUnits', 'userSpaceOnUse')
            .attr('width', cell)
            .attr('height', cell)

          pat.append('rect')
            .attr('width', cell)
            .attr('height', cell)
            .attr('fill', background)

          // orientation 支持 array/string
          const orientations = Array.isArray(orientation) ? orientation : [orientation]
          orientations.forEach((ori) => {
            if (ori === 'horizontal' || ori === 'crosshatch') {
              pat.append('line')
                .attr('x1', 0)
                .attr('y1', 0)
                .attr('x2', cell)
                .attr('y2', 0)
                .attr('stroke', strokeColor)
                .attr('stroke-width', lineWidth)
            }
            if (ori === 'vertical' || ori === 'crosshatch') {
              pat.append('line')
                .attr('x1', 0)
                .attr('y1', 0)
                .attr('x2', 0)
                .attr('y2', cell)
                .attr('stroke', strokeColor)
                .attr('stroke-width', lineWidth)
            }
            if (ori === 'diagonal') {
              pat.append('line')
                .attr('x1', 0)
                .attr('y1', 0)
                .attr('x2', cell)
                .attr('y2', cell)
                .attr('stroke', strokeColor)
                .attr('stroke-width', lineWidth)
            }
            if (ori === 'diagonal-sw-ne') {
              pat.append('line')
                .attr('x1', 0)
                .attr('y1', cell)
                .attr('x2', cell)
                .attr('y2', 0)
                .attr('stroke', strokeColor)
                .attr('stroke-width', lineWidth)
            }
            if (ori === 'anti-diagonal') {
              pat.append('line')
                .attr('x1', cell)
                .attr('y1', 0)
                .attr('x2', 0)
                .attr('y2', cell)
                .attr('stroke', strokeColor)
                .attr('stroke-width', lineWidth)
            }
            if (ori === 'radial') {
              // Radial: draw 8 rays from center
              const center = cell / 2
              const rays = 8
              for (let i = 0; i < rays; i++) {
                const angle = (2 * Math.PI * i) / rays
                const x2 = center + center * Math.cos(angle)
                const y2 = center + center * Math.sin(angle)
                pat.append('line')
                  .attr('x1', center)
                  .attr('y1', center)
                  .attr('x2', x2)
                  .attr('y2', y2)
                  .attr('stroke', strokeColor)
                  .attr('stroke-width', lineWidth)
              }
            }
          })
        }
        // 新增 circles pattern 渲染
        if (safePattern.type === 'circles' && safePattern.circlesOptions) {
          const patternId = `${shape.id}-circles-pattern`
          patternIdMap[shape.id] = patternId
          const { size = 10, radius = 5, background = '#fff', fill = '#333', shapeRendering = 'auto' } = safePattern.circlesOptions
          const cell = Math.max(4, Number(size))
          const circleRadius = Math.max(1, Number(radius))
          const circleFill = String(fill)
          const circleBackground = String(background)

          const pat = defs.append('pattern')
            .attr('id', patternId)
            .attr('patternUnits', 'userSpaceOnUse')
            .attr('width', cell)
            .attr('height', cell)

          pat.append('rect')
            .attr('width', cell)
            .attr('height', cell)
            .attr('fill', circleBackground)
            .attr('shape-rendering', shapeRendering)

          pat.append('circle')
            .attr('cx', cell / 2)
            .attr('cy', cell / 2)
            .attr('r', circleRadius)
            .attr('fill', circleFill)
            .attr('stroke', 'none')
            .attr('shape-rendering', shapeRendering)
        }
        // 新增 paths pattern 渲染
        if (safePattern.type === 'paths' && safePattern.pathsOptions) {
          const patternId = `${shape.id}-paths-pattern`
          patternIdMap[shape.id] = patternId
          const { size = 10, d = '', background = '#fff', fill = '#333', shapeRendering = 'auto' } = safePattern.pathsOptions
          const patternFill = String(fill)
          const patternBackground = String(background)
          const cell = Math.max(4, Number(size))

          const pat = defs.append('pattern')
            .attr('id', patternId)
            .attr('patternUnits', 'userSpaceOnUse')
            .attr('width', cell)
            .attr('height', cell)
          pat.append('rect')
            .attr('width', cell)
            .attr('height', cell)
            .attr('fill', patternBackground)
            .attr('shape-rendering', shapeRendering)
          // 关键字映射
          if (d === 'woven') {
            // woven: 横线和竖线交错，横线在上半格，竖线在下半格
            pat.append('line')
              .attr('x1', 0)
              .attr('y1', cell * 0.35)
              .attr('x2', cell)
              .attr('y2', cell * 0.35)
              .attr('stroke', patternFill)
              .attr('stroke-width', 1)
            pat.append('line')
              .attr('x1', 0)
              .attr('y1', cell * 0.65)
              .attr('x2', cell)
              .attr('y2', cell * 0.65)
              .attr('stroke', patternFill)
              .attr('stroke-width', 1)
            pat.append('line')
              .attr('x1', cell * 0.35)
              .attr('y1', 0)
              .attr('x2', cell * 0.35)
              .attr('y2', cell)
              .attr('stroke', patternFill)
              .attr('stroke-width', 1)
            pat.append('line')
              .attr('x1', cell * 0.65)
              .attr('y1', 0)
              .attr('x2', cell * 0.65)
              .attr('y2', cell)
              .attr('stroke', patternFill)
              .attr('stroke-width', 1)
          }
          else if (d === 'crosses') {
            // crosses: 十字交叉（中心点）
            pat.append('line')
              .attr('x1', 0)
              .attr('y1', cell / 2)
              .attr('x2', cell)
              .attr('y2', cell / 2)
              .attr('stroke', patternFill)
              .attr('stroke-width', 1)
            pat.append('line')
              .attr('x1', cell / 2)
              .attr('y1', 0)
              .attr('x2', cell / 2)
              .attr('y2', cell)
              .attr('stroke', patternFill)
              .attr('stroke-width', 1)
          }
          else {
            // 其他 keyword 或自定义 path
            const keywordPaths: Record<string, string> = {
              squares: `M2,2 h${cell - 4} v${cell - 4} h-${cell - 4} z`,
              waves: `M0,${cell / 2} Q${cell / 4},0 ${cell / 2},${cell / 2} T${cell},${cell / 2}`,
              caps: `M0,${cell} Q${cell / 2},0 ${cell},${cell}`,
              hexes: (() => {
                const r = cell / 2 - 1
                const cx = cell / 2
                const cy = cell / 2
                let d = ''
                for (let i = 0; i < 6; i++) {
                  const a0 = Math.PI / 3 * i
                  const x0 = cx + r * Math.cos(a0)
                  const y0 = cy + r * Math.sin(a0)
                  d += (i === 0 ? `M${x0},${y0}` : `L${x0},${y0}`)
                }
                d += 'Z'
                return d
              })(),
            }
            let pathD = d
            if (d && keywordPaths[d]) {
              pathD = keywordPaths[d]
            }
            if (pathD) {
              pat.append('path')
                .attr('d', pathD)
                .attr('stroke', patternFill)
                .attr('stroke-width', 1)
                .attr('fill', (d === 'squares' || d === 'hexes') ? patternFill : 'none')
                .attr('shape-rendering', shapeRendering)
            }
            else {
              // fallback: 小圆点
              pat.append('circle')
                .attr('cx', cell / 2)
                .attr('cy', cell / 2)
                .attr('r', Math.max(1, cell / 6))
                .attr('fill', patternFill)
            }
          }
        }
      }
      else {
        // 无 pattern 时，确保 patternIdMap 清理
        if (patternIdMap[shape.id]) {
          delete patternIdMap[shape.id]
        }
      }
    })

    // console.log(`[ShapeRenderer] Main rendering effect triggered with ${shapes.length} shapes`)

    // 先应用排序函数排序
    const sortedShapes = [...shapes].sort(compareShapesForRendering)

    // 记录各元素的层级信息以便调试
    if (sortedShapes.length > 0) {
      // console.log('[ShapeRenderer] Sorted shapes with layer info:')
      // sortedShapes.forEach((shape) => {
      //   console.log(`  Shape ${shape.id}: major=${shape.majorCategory != null ? shape.majorCategory : 'none'}, minor=${shape.minorCategory != null ? shape.minorCategory : 'none'}, zLevel=${shape.zLevelId != null && shape.zLevelId !== '' ? shape.zLevelId : 'none'}, intraLayerZIndex=${shape.intraLayerZIndex ?? 0}`)
      // })
    }

    const svgLayer = d3.select(shapesLayerRef.current)

    // 先清空现有内容，然后完全重建，以确保顺序正确
    svgLayer.selectAll('g.shape-group').remove()
    // 同时清理选择组，避免删除元素后边界框残留
    svgLayer.selectAll('g.selection-group').remove()

    // 在D3中，后添加的元素会显示在先添加的元素上方
    // compareShapesForRendering已经按照从下到上的顺序排序，
    // 所以sortedShapes中的顺序是从下层到上层，
    // 这正是我们希望的渲染顺序，不需要再reverse()
    const renderShapes = Array.isArray(sortedShapes) ? sortedShapes : []

    // 一次性创建所有形状组
    const shapeGroups = svgLayer
      .selectAll<SVGGElement, ShapeModel>('g.shape-group')
      .data(renderShapes, (d: ShapeModel) => `${d.id}-z${typeof d.intraLayerZIndex === 'number' ? d.intraLayerZIndex : 0}`)
      .enter()
      .append('g')
      .attr('class', 'shape-group')
      .attr('id', d => d.id)
      .attr('data-shape-id', d => d.id)
      .attr('data-major-category', d => d.majorCategory != null ? d.majorCategory : 'none')
      .attr('data-minor-category', d => d.minorCategory != null ? d.minorCategory : 'none')
      .attr('data-z-level-id', d => typeof d.zLevelId === 'string' ? d.zLevelId : 'none')
      .attr('data-intra-layer-z-index', d => typeof d.intraLayerZIndex === 'number' ? d.intraLayerZIndex : 0)
      .style('cursor', isPanMode ? 'grab' : 'pointer')
      .attr('transform', (d) => {
        const posX = d.position?.x ?? 0
        const posY = d.position?.y ?? 0
        const rotation = typeof d.rotation === 'number' && Number.isFinite(d.rotation) ? d.rotation : 0
        return rotation !== 0
          ? `translate(${posX},${posY}) rotate(${rotation})`
          : `translate(${posX},${posY})`
      })
      .on('mousedown', (event: MouseEvent, d) => {
        if (!isPanMode) {
          // 提取键盘修饰键并检查是否为多选模式
          const modifiers = extractKeyboardModifiers(event)
          const isMultiSelectKey = isMultiSelectKeyPressed(modifiers)

          console.log('[ShapeRenderer] Shape clicked:', {
            shapeId: d.id,
            modifiers,
            isMultiSelectKey,
          })

          onShapeSelected(d.id, isMultiSelectKey)
        }
      })

    // 为每个形状组创建内容
    shapeGroups.each(function (shapeData) {
      const group = d3.select<SVGGElement, ShapeModel>(this)
      const specialElementInfo = determineSpecialElementInfo(shapeData)
      const styleProps = getStyleProps(shapeData)
      const scaledStrokeWidth = (styleProps.strokeWidth ?? 1) / currentZoom

      const effectiveStyleAttributes = {
        fill: styleProps.fill !== undefined ? styleProps.fill : '#CCCCCC',
        stroke: styleProps.stroke !== undefined ? styleProps.stroke : '#333333',
        strokeWidth: scaledStrokeWidth,
        opacity: styleProps.opacity !== undefined ? styleProps.opacity : 1,
      }

      const renderer = shapeRenderers()[shapeData.type as CoreElementType]

      // --- 新增：如果有 lines/circles/paths pattern，强制 fill 为 pattern url ---
      const patchedStyle = { ...effectiveStyleAttributes }
      if (patternIdMap[shapeData.id] && shapeData.pattern) {
        const patternUrl = `url(#${patternIdMap[shapeData.id]})`
        patchedStyle.fill = patternUrl
      }
      if (typeof renderer === 'function') {
        if (specialElementInfo.isSpecial) {
          renderSpecialElement(
            group,
            shapeData,
            specialElementInfo,
            patchedStyle,
            currentZoom,
            DEFAULT_ICON_WIDTH,
            DEFAULT_ICON_HEIGHT,
          )
        }
        else {
          renderer(
            group,
            shapeData,
            patchedStyle,
            currentZoom,
            DEFAULT_ICON_WIDTH,
            DEFAULT_ICON_HEIGHT,
          )
        }
      }
      else {
        _warn(`[ShapeRenderer] No renderer found for shape type: ${shapeData.type} (ID: ${shapeData.id}). Rendering fallback.`)
        const fallbackWidth = typeof shapeData.properties?.width === 'number' ? shapeData.properties.width : DEFAULT_ICON_WIDTH
        const fallbackHeight = typeof shapeData.properties?.height === 'number' ? shapeData.properties.height : DEFAULT_ICON_HEIGHT
        const fallbackX = -fallbackWidth / 2
        const fallbackY = -fallbackHeight / 2

        group.append('rect')
          .attr('class', 'unknown-type-fallback-bg')
          .attr('x', fallbackX)
          .attr('y', fallbackY)
          .attr('width', fallbackWidth)
          .attr('height', fallbackHeight)
          .attr('fill', '#ccc')
          .attr('stroke', '#999')
          .attr('stroke-width', 1 / currentZoom)
        group.append('text')
          .attr('class', 'unknown-type-fallback-text')
          .attr('x', 0)
          .attr('y', 0)
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'central')
          .attr('font-size', `${10 / currentZoom}px`)
          .attr('fill', '#555')
          .text(`? ${shapeData.type}`)
      }

      if (selectedIds.includes(shapeData.id)) {
        // 使用新的边界框渲染函数
        renderSingleBoundingBox(svgLayer, shapeData, currentZoom, onShapeSelected, isPanMode, selectedIds, shapes)
      }
    })

    // 记录实际渲染顺序
    // console.log('[ShapeRenderer] Final render order:')
    // const renderedGroups = svgLayer.selectAll<SVGGElement, ShapeModel>('g.shape-group').nodes()
    // renderedGroups.forEach((group, index) => {
    //   const id = group.getAttribute('id')
    //   const major = group.getAttribute('data-major-category')
    //   const minor = group.getAttribute('data-minor-category')
    //   const zLevel = group.getAttribute('data-z-level-id')
    //   const zIndex = group.getAttribute('data-intra-layer-z-index')
    //   console.log(`  Render position ${index + 1}: Shape ${id}, major=${major}, minor=${minor}, zLevel=${zLevel}, intraLayerZIndex=${zIndex}`)
    // })
  }, [shapes, selectedIds, currentZoom, onShapeSelected, isPanMode, shapesLayerRef, shapeRenderers])

  // 监听形状更新事件，优化边界框重新渲染
  useEffect(() => {
    const eventBus = getEventBus()
    let updateTimeoutId: NodeJS.Timeout | null = null

    const renderBoundingBoxes = () => {
      if (!shapesLayerRef.current) {
        return
      }

      const svg = d3.select(shapesLayerRef.current)

      // 为每个选中的形状渲染边界框
      selectedIds.forEach((shapeId) => {
        const shapeData = shapes.find(s => s.id === shapeId)
        if (shapeData) {
          renderSingleBoundingBox(svg, shapeData, currentZoom, onShapeSelected, isPanMode, selectedIds, shapes)
        }
      })
    }

    const handleShapeUpdate = (event: { type: string }) => {
      if (event.type === AppEventType.ShapeEditComplete) {
        // 只在编辑完成时重新渲染边界框，避免频繁更新

        // 防抖处理，避免短时间内多次更新
        if (updateTimeoutId) {
          clearTimeout(updateTimeoutId)
        }

        updateTimeoutId = setTimeout(() => {
          if (shapesLayerRef.current) {
            const svg = d3.select(shapesLayerRef.current)
            svg.selectAll('.selection-group').remove()

            // 重新渲染边界框
            renderBoundingBoxes()
          }
        }, 50) // 50ms防抖
      }
    }

    // 只监听ShapeEditComplete事件，不监听ShapeEditRequest
    eventBus.subscribe(AppEventType.ShapeEditComplete, handleShapeUpdate)

    return () => {
      eventBus.unsubscribe(AppEventType.ShapeEditComplete, handleShapeUpdate)
      if (updateTimeoutId) {
        clearTimeout(updateTimeoutId)
      }
    }
  }, [shapesLayerRef, selectedIds, shapes, currentZoom, onShapeSelected, isPanMode])

  return null
}

// Export as named export only, no default export
