/**
 * Contains validation logic specific to Cubic Bezier curves.
 */

import type { ValidatableShape, ValidationError } from '@/types/core/validator/validator-interface' // Corrected
import { ElementType } from '@/types/core/elementDefinitions' // Import ElementType only
import { ValidationErrorCode } from '@/types/core/validator/error-codes' // Corrected
import { isValidPoint } from '../common/validationUtils'
import { BaseShapeValidator } from '../common/validatorBase'

/**
 * Concrete validator implementation for Cubic Bezier curves.
 *
 * @extends BaseShapeValidator
 */
export class CubicValidator extends BaseShapeValidator {
  /**
   * Validates properties specific to a Cubic Bezier curve element data structure.
   *
   * @param shape - The shape to validate.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected validateSpecific(shape: ValidatableShape): ValidationError[] {
    const errors: ValidationError[] = []

    if (shape.type !== ElementType.CUBIC) {
      errors.push({
        code: ValidationErrorCode.INVALID_SHAPE_TYPE,
        message: `Internal Error: CubicValidator received shape type ${shape.type}`,
        path: 'type',
      })
      return errors
    }

    const props = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined || {}
    const startPoint = props.start as { x: number, y: number } | undefined
    const control1Point = props.control1 as { x: number, y: number } | undefined
    const control2Point = props.control2 as { x: number, y: number } | undefined
    const endPoint = props.end as { x: number, y: number } | undefined

    // Validate start point
    if (startPoint == null || !isValidPoint(startPoint)) {
      errors.push({
        code: ValidationErrorCode.INVALID_POINT,
        message: `Cubic curve requires a valid 'start' point in properties. Received: ${JSON.stringify(startPoint)}`,
        path: 'properties.start',
        value: startPoint,
      })
    }

    // Validate control1 point
    if (control1Point == null || !isValidPoint(control1Point)) {
      errors.push({
        code: ValidationErrorCode.INVALID_POINT,
        message: `Cubic curve requires a valid 'control1' point in properties. Received: ${JSON.stringify(control1Point)}`,
        path: 'properties.control1',
        value: control1Point,
      })
    }

    // Validate control2 point
    if (control2Point == null || !isValidPoint(control2Point)) {
      errors.push({
        code: ValidationErrorCode.INVALID_POINT,
        message: `Cubic curve requires a valid 'control2' point in properties. Received: ${JSON.stringify(control2Point)}`,
        path: 'properties.control2',
        value: control2Point,
      })
    }

    // Validate end point
    if (endPoint == null || !isValidPoint(endPoint)) {
      errors.push({
        code: ValidationErrorCode.INVALID_POINT,
        message: `Cubic curve requires a valid 'end' point in properties. Received: ${JSON.stringify(endPoint)}`,
        path: 'properties.end',
        value: endPoint,
      })
    }

    return errors
  }

  /**
   * Applies cubic-specific business rules.
   *
   * @param _shape - The shape to validate against business rules.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected applySpecificRules(_shape: ValidatableShape): ValidationError[] {
    // No cubic-specific business rules defined currently beyond common ones.
    return []
  }
}
