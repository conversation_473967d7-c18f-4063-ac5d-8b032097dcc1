import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import {
  ComputeError,
  CoreError,
  DefaultErrorLogger,
  ElementNotFoundError,
  errorLogger,
  ErrorType,
  FactoryError,
  logError,
  UnsupportedElementTypeError,
  UnsupportedOperationError,
  ValidationResultError,
  ValidatorError,
} from '@/core/errors'

describe('core Errors', () => {
  describe('factoryError', () => {
    it('should create a FactoryError with the correct properties', () => {
      const error = new FactoryError('Failed to create element', { elementType: 'rectangle' })

      // Check properties instead of instanceof
      expect(error.name).toBe('FactoryError')
      expect(error.type).toBe(ErrorType.FACTORY_CREATION_FAILED)
      expect(error.message).toBe('Failed to create element')
      expect(error.metadata).toEqual({ elementType: 'rectangle' })
    })
  })

  describe('validatorError', () => {
    it('should create a ValidatorError with the correct properties', () => {
      const error = new ValidatorError('Validation failed', { field: 'width' })

      // Check properties instead of instanceof
      expect(error.name).toBe('ValidatorError')
      expect(error.type).toBe(ErrorType.VALIDATION_FAILED)
      expect(error.message).toBe('Validation failed')
      expect(error.metadata).toEqual({ field: 'width' })
    })
  })

  describe('computeError', () => {
    it('should create a ComputeError with the correct properties', () => {
      const error = new ComputeError('Failed to compute bounds', { shapeId: '123' })

      // Check properties instead of instanceof
      expect(error.name).toBe('ComputeError')
      expect(error.type).toBe(ErrorType.COMPUTE_BOUNDS_ERROR)
      expect(error.message).toBe('Failed to compute bounds')
      expect(error.metadata).toEqual({ shapeId: '123' })
    })
  })

  describe('validationResultError', () => {
    it('should create a ValidationResultError with the correct properties and message', () => {
      const errors = ['Width must be positive', 'Height must be positive']
      const error = new ValidationResultError(errors, 'rectangle', 'rect-123')

      // Check properties instead of instanceof
      expect(error.name).toBe('ValidationResultError')
      expect(error.type).toBe(ErrorType.VALIDATION_FAILED)
      expect(error.message).toBe('Validation failed for rectangle (ID: rect-123): Width must be positive, Height must be positive')
      expect(error.errors).toEqual(errors)
      expect(error.ElementType).toBe('rectangle')
      expect(error.shapeId).toBe('rect-123')
    })

    it('should handle missing shapeId', () => {
      const errors = ['Width must be positive']
      const error = new ValidationResultError(errors, 'rectangle')

      expect(error.message).toBe('Validation failed for rectangle: Width must be positive')
      expect(error.shapeId).toBeUndefined()
    })
  })

  describe('unsupportedElementTypeError', () => {
    it('should create an UnsupportedElementTypeError with the correct properties', () => {
      const error = new UnsupportedElementTypeError('star')

      // Check properties instead of instanceof
      expect(error.name).toBe('UnsupportedElementTypeError')
      expect(error.type).toBe(ErrorType.FACTORY_INVALID_TYPE)
      expect(error.message).toBe('Unsupported shape type: star')
      expect(error.ElementType).toBe('star')
    })
  })

  describe('unsupportedOperationError', () => {
    it('should create an UnsupportedOperationError with shape type', () => {
      const error = new UnsupportedOperationError('area', 'star')

      // Check properties instead of instanceof
      expect(error.name).toBe('UnsupportedOperationError')
      expect(error.type).toBe(ErrorType.STRATEGY_NOT_FOUND)
      expect(error.message).toBe('Unsupported operation: area for shape type: star')
      expect(error.operation).toBe('area')
      expect(error.ElementType).toBe('star')
    })

    it('should create an UnsupportedOperationError without shape type', () => {
      const error = new UnsupportedOperationError('transform')

      expect(error.message).toBe('Unsupported operation: transform')
      expect(error.operation).toBe('transform')
      expect(error.ElementType).toBeUndefined()
    })
  })

  describe('elementNotFoundError', () => {
    it('should create an ElementNotFoundError with the correct properties', () => {
      const error = new ElementNotFoundError('rect-123')

      // Check properties instead of instanceof
      expect(error.name).toBe('ElementNotFoundError')
      expect(error.type).toBe(ErrorType.COORDINATOR_SHAPE_NOT_FOUND)
      expect(error.message).toBe('Element not found: rect-123')
      expect(error.elementId).toBe('rect-123')
    })
  })

  describe('defaultErrorLogger', () => {
    // Spy on console.error
    let consoleErrorSpy: any

    beforeEach(() => {
      // Clear mocks before each test
      vi.clearAllMocks()
      // Create a new spy for each test to avoid issues with mock restoration
      consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    })

    afterEach(() => {
      // Reset mocks after each test
      vi.resetAllMocks()
      consoleErrorSpy.mockRestore()
    })

    it('should log error message and stack', () => {
      const logger = new DefaultErrorLogger()
      const error = new Error('Test error')

      logger.logError(error)

      expect(consoleErrorSpy).toHaveBeenCalledTimes(2)
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error:', 'Test error')
      expect(consoleErrorSpy).toHaveBeenCalledWith('Stack:', error.stack)
    })

    it('should log context if provided', () => {
      const logger = new DefaultErrorLogger()
      const error = new Error('Test error')
      const context = { operation: 'test' }

      logger.logError(error, context)

      expect(consoleErrorSpy).toHaveBeenCalledWith('Context:', context)
    })

    it('should log metadata for CoreError', () => {
      const logger = new DefaultErrorLogger()
      const metadata = { shapeId: '123' }
      const error = new CoreError(ErrorType.UNKNOWN, 'Test error', metadata)

      logger.logError(error)

      expect(consoleErrorSpy).toHaveBeenCalledWith('Metadata:', metadata)
    })
  })

  describe('errorLogger and logError', () => {
    // Spy on the errorLogger.logError method
    let logErrorSpy: any

    beforeEach(() => {
      vi.clearAllMocks()
      // Create a new spy for each test
      logErrorSpy = vi.spyOn(errorLogger, 'logError').mockImplementation(() => {})
    })

    afterEach(() => {
      logErrorSpy.mockRestore()
    })

    it('should call errorLogger.logError with the correct arguments', () => {
      const error = new Error('Test error')
      const context = { operation: 'test' }

      logError(error, context)

      expect(logErrorSpy).toHaveBeenCalledTimes(1)
      expect(logErrorSpy).toHaveBeenCalledWith(error, context)
    })
  })
})
