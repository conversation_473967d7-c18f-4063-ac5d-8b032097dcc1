import type Point from '@/types/core/element/geometry/point'
import { describe, expect, it } from 'vitest'

describe('point Interface', () => {
  it('should define a point with x and y coordinates', () => {
    // Create a valid Point object
    const point: Point = {
      x: 10,
      y: 20,
    }

    // Verify the object is valid
    expect(point.x).toBe(10)
    expect(point.y).toBe(20)
  })

  it('should allow creating points with different coordinate values', () => {
    // Create points with different values
    const origin: Point = { x: 0, y: 0 }
    const positivePoint: Point = { x: 100, y: 200 }
    const negativePoint: Point = { x: -50, y: -75 }
    const mixedPoint: Point = { x: 25, y: -30 }

    // Verify all points are valid
    expect(origin.x).toBe(0)
    expect(origin.y).toBe(0)

    expect(positivePoint.x).toBe(100)
    expect(positivePoint.y).toBe(200)

    expect(negativePoint.x).toBe(-50)
    expect(negativePoint.y).toBe(-75)

    expect(mixedPoint.x).toBe(25)
    expect(mixedPoint.y).toBe(-30)
  })

  it('should allow creating points with decimal coordinates', () => {
    // Create points with decimal values
    const decimalPoint: Point = { x: 10.5, y: 20.75 }
    const smallDecimalPoint: Point = { x: 0.01, y: 0.001 }

    // Verify the points are valid
    expect(decimalPoint.x).toBe(10.5)
    expect(decimalPoint.y).toBe(20.75)

    expect(smallDecimalPoint.x).toBe(0.01)
    expect(smallDecimalPoint.y).toBe(0.001)
  })

  it('should be usable in arrays and collections', () => {
    // Create an array of points
    const points: Point[] = [
      { x: 0, y: 0 },
      { x: 10, y: 0 },
      { x: 10, y: 10 },
      { x: 0, y: 10 },
    ]

    // Verify the array is valid
    expect(points.length).toBe(4)
    expect(points[0].x).toBe(0)
    expect(points[0].y).toBe(0)
    expect(points[1].x).toBe(10)
    expect(points[1].y).toBe(0)
    expect(points[2].x).toBe(10)
    expect(points[2].y).toBe(10)
    expect(points[3].x).toBe(0)
    expect(points[3].y).toBe(10)
  })

  it('should be usable as function parameters and return values', () => {
    // Define functions that use Point as parameter and return value
    function movePoint(point: Point, dx: number, dy: number): Point {
      return {
        x: point.x + dx,
        y: point.y + dy,
      }
    }

    function distanceBetweenPoints(p1: Point, p2: Point): number {
      const dx = p2.x - p1.x
      const dy = p2.y - p1.y
      return Math.sqrt(dx * dx + dy * dy)
    }

    // Test the functions
    const startPoint: Point = { x: 5, y: 5 }
    const movedPoint = movePoint(startPoint, 3, -2)

    expect(movedPoint.x).toBe(8)
    expect(movedPoint.y).toBe(3)

    const distance = distanceBetweenPoints({ x: 0, y: 0 }, { x: 3, y: 4 })
    expect(distance).toBe(5)
  })

  it('should be compatible with JSON serialization/deserialization', () => {
    // Create a point
    const point: Point = { x: 15, y: 25 }

    // Serialize to JSON and deserialize
    const serialized = JSON.stringify(point)
    const deserialized = JSON.parse(serialized) as Point

    // Verify the deserialized object is valid
    expect(deserialized.x).toBe(15)
    expect(deserialized.y).toBe(25)
  })
})
