/**
 * Material Calculation Strategy for Wall Paint
 *
 * @remarks
 * This strategy implements the {@link IMaterialCalculatorStrategy} to calculate
 * the required amount of paint specifically for wall surfaces.
 *
 * It relies on the area of the wall element (currently assuming the element
 * has a `getArea()` or `compute.area()` method - marked as TODO for refactoring).
 *
 * The core paint calculation logic is delegated to the `calculatePaintAmount`
 * utility function from `./materialUtils.ts`. This utility considers factors like
 * paint coverage per liter, number of coats, and wastage rate, which can be
 * provided via {@link MaterialCalculationOptions}.
 *
 * This strategy validates that the `materialType` is 'paint' or 'wall paint'.
 * The `calculateMaterialAmount` method returns a {@link MaterialCalculationResult}
 * where `amount` represents the raw wall area, `unit` is 'm²', `unitCount` is
 * the calculated liters of paint needed (rounded up), and `unitType` is 'liter'.
 *
 * The `getElementType()` method suggests this strategy is primarily for `RECTANGLE`
 * elements, as walls are often modeled as such.
 *
 * @module core/compute/strategies/material/WallPaintMaterialStrategy
 * @see {@link IMaterialCalculatorStrategy}
 * @see {@link MaterialCalculationOptions}
 * @see {@link MaterialCalculationResult}
 * @see {@link calculatePaintAmount}
 */
import type {
  MaterialCalculatorStrategy as IMaterialCalculatorStrategy,
  MaterialCalculationOptions,
  MaterialCalculationResult,
} from '@/types/core/compute' // Corrected import path
import type { Element } from '@/types/core/elementDefinitions'
import { calculatePaintAmount } from '@/core/compute/strategies/material/materialUtils' // Import the specific function
import { CoreError, ErrorType } from '@/services/system/error-service'
import { ElementType as CoreElementType } from '@/types/core/elementDefinitions'

export class WallPaintMaterialStrategy implements IMaterialCalculatorStrategy { // Renamed IMaterialCalculatorStrategy for consistency
  /**
   * Calculates the amount of paint material required for a wall element.
   *
   * @param element - The wall element for which paint is being calculated.
   *                  Expected to have a method to compute its area.
   * @param materialType - The type of material, expected to be 'paint' or 'wall paint'.
   * @param options - Optional {@link MaterialCalculationOptions} to customize the calculation,
   *                  such as `coveragePerLiter`, `coats`, and `wastageRate`.
   * @returns A {@link MaterialCalculationResult} detailing the raw area and the calculated paint quantity.
   * @throws {@link CoreError} if `materialType` is not 'paint'/'wall paint', or if area calculation fails or is invalid.
   */
  public calculateMaterialAmount(
    element: Element,
    materialType: string,
    options?: MaterialCalculationOptions,
  ): MaterialCalculationResult {
    // Element type validation can be more specific if this strategy only applies to certain shapes.
    // For now, relying on the presence of an area calculation method.
    // if (element.type !== CoreElementType.RECTANGLE && ...)

    if (materialType.toLowerCase() !== 'paint' && materialType.toLowerCase() !== 'wallpaint') { // Allow 'wallpaint' alias
      throw new CoreError(
        ErrorType.InvalidParameter,
        `WallPaintMaterialStrategy only supports 'paint' or 'wallpaint' material type, got '${materialType}'`,
      )
    }

    let area: number
    const computableElement = element as unknown as { compute?: { area?: () => number }, getArea?: () => number } // TODO: Refactor for type safety

    try {
      if (computableElement.compute?.area && typeof computableElement.compute.area === 'function') {
        area = computableElement.compute.area()
      }
      else if (computableElement.getArea !== null && computableElement.getArea !== undefined && typeof computableElement.getArea === 'function') {
        area = computableElement.getArea()
      }
      else {
        throw new Error('Element does not have a method to calculate area (compute.area or getArea)')
      }
    }
    catch (error) {
      throw new CoreError(
        ErrorType.ComputationError,
        `Failed to calculate area for element (ID: ${element.id}): ${error instanceof Error ? error.message : String(error)}`,
      )
    }

    if (typeof area !== 'number' || Number.isNaN(area) || area < 0) {
      throw new CoreError(ErrorType.ComputationError, `Invalid area value: ${area} for element ID: ${element.id}`)
    }
    if (area === 0) {
      console.warn(`[WallPaintMaterialStrategy] Area for element ${element.id} is 0. No paint needed.`)
      return { amount: 0, unit: 'm²', unitCount: 0, unitType: 'liter', amountWithWastage: 0, coats: options?.coats ?? 2 }
    }

    const coverage = options?.coveragePerLiter ?? options?.coverage ?? 10 // Prefer coveragePerLiter from options
    const coats = (options?.coats !== null && options?.coats !== undefined && options?.coats !== 0) ? options.coats : 2
    const wastageRate = (options?.wastageRate !== null && options?.wastageRate !== undefined && options?.wastageRate !== 0) ? options.wastageRate : 10 // Percentage

    // calculatePaintAmount expects wastageRate as a decimal (0.1 for 10%)
    const paintResult = calculatePaintAmount(area, coverage, coats, {
      wastageRate: wastageRate / 100,
    })

    // The calculatePaintAmount function returns a MaterialCalculationResult.
    // We need to ensure the final returned object matches the expected structure.
    return {
      amount: area, // Raw area calculated for the element
      unit: 'm²', // Unit for the raw area
      unitCount: paintResult.amount, // This is liters of paint needed (rounded up) from calculatePaintAmount
      unitType: 'liter',
      amountWithWastage: paintResult.amountWithWastage, // This is liters including wastage from calculatePaintAmount
      coats: paintResult.coats,
    }
  }

  /**
   * Returns the primary element type this strategy is typically associated with.
   * @remarks Walls are often modeled as rectangular surfaces.
   * @returns {@link CoreElementType.RECTANGLE} - The rectangle element type typically used for wall surfaces
   */
  public getElementType(): string {
    // This strategy is typically applied to wall surfaces, often rectangular.
    return CoreElementType.RECTANGLE
  }

  // getMaterialType is not part of the MaterialCalculatorStrategy interface.
  // The specific material type (e.g., "wallpaint_interior_latex") would be
  // passed in the `materialType` argument of `calculateMaterialAmount`.
  // This strategy specifically handles the generic 'paint'/'wallpaint' materialType logic for walls.
}
