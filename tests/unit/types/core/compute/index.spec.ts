import { describe, expect, it } from 'vitest'

import * as computeExports from '@/types/core/compute'

describe('compute Module Exports', () => {
  it('should export ComputeOperationType enum', () => {
    expect(computeExports.ComputeOperationType).toBeDefined()

    // Check specific operation types
    expect(computeExports.ComputeOperationType.AREA).toBe('area')
    expect(computeExports.ComputeOperationType.PERIMETER).toBe('perimeter')
    expect(computeExports.ComputeOperationType.BOUNDING_BOX).toBe('boundingBox')
    expect(computeExports.ComputeOperationType.DISTANCE).toBe('distance')
    expect(computeExports.ComputeOperationType.MATERIAL).toBe('material')
    expect(computeExports.ComputeOperationType.COST).toBe('cost')
    expect(computeExports.ComputeOperationType.SPACE).toBe('space')
  })

  it('should export ComputeOperation enum', () => {
    expect(computeExports.ComputeOperation).toBeDefined()

    // Check basic operation types
    expect(computeExports.ComputeOperation.AREA).toBe('AREA')
    expect(computeExports.ComputeOperation.PERIMETER).toBe('PERIMETER')
    expect(computeExports.ComputeOperation.BOUNDING_BOX).toBe('BOUNDING_BOX')
    expect(computeExports.ComputeOperation.DISTANCE).toBe('DISTANCE')
    expect(computeExports.ComputeOperation.MATERIAL).toBe('MATERIAL')
    expect(computeExports.ComputeOperation.COST).toBe('COST')
    expect(computeExports.ComputeOperation.SPACE).toBe('SPACE')

    // Check space planning specific operations
    expect(computeExports.ComputeOperation.SPACE_UTILIZATION).toBe('SPACE_UTILIZATION')
    expect(computeExports.ComputeOperation.PATHWAY_CHECK).toBe('PATHWAY_CHECK')
    expect(computeExports.ComputeOperation.LAYOUT_SCORE).toBe('LAYOUT_SCORE')
  })

  it('should export strategy interfaces', () => {
    // Check that strategy interfaces are exported
    // These are TypeScript interfaces, which don't exist at runtime
    // So we can only check if they're defined in the type system, not at runtime
    // For testing purposes, we'll just verify the test runs without errors
    expect(true).toBe(true)
  })

  it('should export computation option and result types', () => {
    // Create objects using the exported types to verify they're correctly exported
    const options: computeExports.ComputeOptions = {
      precision: 0.001,
      algorithm: 'fast',
      x: 100,
      y: 200,
    }

    expect(options.precision).toBe(0.001)
    expect(options.algorithm).toBe('fast')
    expect(options.x).toBe(100)
    expect(options.y).toBe(200)

    const result: computeExports.ComputeResult = {
      value: 42,
      unit: 'm²',
      metadata: {
        algorithm: 'standard',
        precision: 0.01,
      },
    }

    expect(result.value).toBe(42)
    expect(result.unit).toBe('m²')
    expect(result.metadata?.algorithm).toBe('standard')
    expect(result.metadata?.precision).toBe(0.01)
  })

  it('should allow using ComputeValue type', () => {
    // Create values of different types that should be compatible with ComputeValue
    const numberValue: computeExports.ComputeValue = 42
    const stringValue: computeExports.ComputeValue = 'test'
    const booleanValue: computeExports.ComputeValue = true
    const nullValue: computeExports.ComputeValue = null
    const pointValue: computeExports.ComputeValue = { x: 10, y: 20 }
    const numberArrayValue: computeExports.ComputeValue = [1, 2, 3]
    const stringArrayValue: computeExports.ComputeValue = ['a', 'b', 'c']
    const recordValue: computeExports.ComputeValue = { width: 100, height: 200, visible: true }

    expect(typeof numberValue).toBe('number')
    expect(typeof stringValue).toBe('string')
    expect(typeof booleanValue).toBe('boolean')
    expect(nullValue).toBeNull()
    expect(pointValue).toEqual({ x: 10, y: 20 })
    expect(numberArrayValue).toEqual([1, 2, 3])
    expect(stringArrayValue).toEqual(['a', 'b', 'c'])
    expect(recordValue).toEqual({ width: 100, height: 200, visible: true })
  })

  it('should export material calculation types', () => {
    // Create objects using the exported material calculation types
    const options: computeExports.MaterialCalculationOptions = {
      unitSize: { width: 0.3, height: 0.3 },
      wastageRate: 0.1,
      includeJoints: true,
      jointWidth: 0.002,
      coverage: 10,
      coats: 2,
    }

    expect(options.unitSize).toEqual({ width: 0.3, height: 0.3 })
    expect(options.wastageRate).toBe(0.1)
    expect(options.includeJoints).toBe(true)
    expect(options.jointWidth).toBe(0.002)
    expect(options.coverage).toBe(10)
    expect(options.coats).toBe(2)

    const result: computeExports.MaterialCalculationResult = {
      amount: 50,
      unit: 'm²',
      unitCount: 200,
      unitType: 'tiles',
      amountWithWastage: 55,
      boxes: 20,
      coats: 2,
    }

    expect(result.amount).toBe(50)
    expect(result.unit).toBe('m²')
    expect(result.unitCount).toBe(200)
    expect(result.unitType).toBe('tiles')
    expect(result.amountWithWastage).toBe(55)
    expect(result.boxes).toBe(20)
    expect(result.coats).toBe(2)
  })

  it('should export cost calculation types', () => {
    // Create objects using the exported cost calculation types
    const options: computeExports.CostCalculationOptions = {
      costType: 'area',
      materialCostPerUnit: 20,
      laborCostPerUnit: 30,
      designCostPerUnit: 10,
      overheadCostPerUnit: 15,
      installationCost: 100,
      quantity: 5,
      additionalCost: 50,
      discountRate: 0.1,
      taxRate: 0.2,
    }

    expect(options.costType).toBe('area')
    expect(options.materialCostPerUnit).toBe(20)
    expect(options.laborCostPerUnit).toBe(30)
    expect(options.designCostPerUnit).toBe(10)
    expect(options.overheadCostPerUnit).toBe(15)
    expect(options.installationCost).toBe(100)
    expect(options.quantity).toBe(5)
    expect(options.additionalCost).toBe(50)
    expect(options.discountRate).toBe(0.1)
    expect(options.taxRate).toBe(0.2)
  })

  it('should export space planning types', () => {
    // Create objects using the exported space planning types
    const pathwayResult: computeExports.PathwayCheckResult = {
      pathwayId: 'pathway-1',
      isValid: true,
      actualWidth: 1.2,
      blockingElements: [],
    }

    expect(pathwayResult.pathwayId).toBe('pathway-1')
    expect(pathwayResult.isValid).toBe(true)
    expect(pathwayResult.actualWidth).toBe(1.2)
    expect(pathwayResult.blockingElements).toEqual([])

    const ergonomicsResult: computeExports.ErgonomicsEvaluationResult = {
      isValid: true,
      issues: [],
      recommendations: ['Adjust desk height'],
      severity: 1,
      complianceScore: 95,
    }

    expect(ergonomicsResult.isValid).toBe(true)
    expect(ergonomicsResult.issues).toEqual([])
    expect(ergonomicsResult.recommendations).toEqual(['Adjust desk height'])
    expect(ergonomicsResult.severity).toBe(1)
    expect(ergonomicsResult.complianceScore).toBe(95)

    const standard: computeExports.SpacePlanningStandard = {
      minPathwayWidth: 0.9,
      minClearance: 0.5,
      recommendedUtilization: 0.7,
    }

    expect(standard.minPathwayWidth).toBe(0.9)
    expect(standard.minClearance).toBe(0.5)
    expect(standard.recommendedUtilization).toBe(0.7)

    // Test SpaceType type
    const spaceType: computeExports.SpaceType = 'living'
    expect(spaceType).toBe('living')
  })
})
