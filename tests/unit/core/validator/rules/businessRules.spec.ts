/**
 * Unit tests for business rules
 *
 * Tests the business rules used by validators
 */

import { expect, test } from '@playwright/test'

// Define types locally for testing
interface ValidationError {
  code: string
  message: string
  path?: string
  value?: any
}

// Mock business rules to avoid namespace issues
const sizeRule = {
  name: 'size',
  apply: (shape: any): ValidationError[] => {
    const errors: ValidationError[] = []

    if (shape.type === 'rectangle') {
      if (shape.width < 0) {
        errors.push({ code: 'INVALID_WIDTH', message: 'Width must be positive' })
      }
      if (shape.height < 0) {
        errors.push({ code: 'INVALID_HEIGHT', message: 'Height must be positive' })
      }
    }
    else if (shape.type === 'circle') {
      if (shape.radius < 0) {
        errors.push({ code: 'INVALID_RADIUS', message: 'Radius must be positive' })
      }
    }
    else if (shape.type === 'ellipse') {
      if (shape.radiusX < 0) {
        errors.push({ code: 'INVALID_RADIUS_X', message: 'X radius must be positive' })
      }
      if (shape.radiusY < 0) {
        errors.push({ code: 'INVALID_RADIUS_Y', message: 'Y radius must be positive' })
      }
    }

    return errors
  },
}

const pointCountRule = {
  name: 'pointCount',
  apply: (shape: any): ValidationError[] => {
    const errors: ValidationError[] = []

    if (shape.type === 'polygon' && shape.points) {
      if (shape.points.length < 3) {
        errors.push({ code: 'INVALID_POINT_COUNT', message: 'Polygon must have at least 3 points' })
      }
      else if (shape.points.length > 100) {
        errors.push({ code: 'INVALID_POINT_COUNT', message: 'Polygon has too many points' })
      }
    }

    return errors
  },
}

const polygonClosureRule = {
  name: 'polygonClosure',
  apply: (shape: any): ValidationError[] => {
    const errors: ValidationError[] = []

    if (shape.type === 'polygon' && shape.points && shape.points.length >= 3) {
      const firstPoint = shape.points[0]
      const lastPoint = shape.points[shape.points.length - 1]

      if (firstPoint.x !== lastPoint.x || firstPoint.y !== lastPoint.y) {
        errors.push({ code: 'POLYGON_NOT_CLOSED', message: 'Polygon must be closed' })
      }
    }

    return errors
  },
}

const idRule = {
  name: 'id',
  apply: (shape: any): ValidationError[] => {
    const errors: ValidationError[] = []

    if (!shape.id || shape.id.trim() === '') {
      errors.push({ code: 'MISSING_ID', message: 'Missing ID' })
    }

    return errors
  },
}

function applyBusinessRules(shape: any): ValidationError[] {
  const rules = [idRule, sizeRule, pointCountRule, polygonClosureRule]
  const errors: ValidationError[] = []

  for (const rule of rules) {
    const ruleErrors = rule.apply(shape)
    errors.push(...ruleErrors)
  }

  return errors
}

test.describe('Business Rules', () => {
  test.describe('sizeRule', () => {
    test('passes for shapes with valid dimensions', () => {
      // Rectangle with valid dimensions
      const validRectangle = {
        id: 'rect1',
        type: 'rectangle',
        position: { x: 100, y: 100 },
        width: 200,
        height: 150,
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      expect(sizeRule.apply(validRectangle as any)).toEqual([])

      // Circle with valid radius
      const validCircle = {
        id: 'circle1',
        type: 'circle',
        position: { x: 100, y: 100 },
        radius: 50,
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      expect(sizeRule.apply(validCircle as any)).toEqual([])

      // Ellipse with valid radii
      const validEllipse = {
        id: 'ellipse1',
        type: 'ellipse',
        position: { x: 100, y: 100 },
        radiusX: 100,
        radiusY: 50,
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      expect(sizeRule.apply(validEllipse as any)).toEqual([])
    })

    test('fails for shapes with invalid dimensions', () => {
      // Rectangle with negative width
      const invalidWidthRectangle = {
        id: 'rect2',
        type: 'rectangle',
        position: { x: 100, y: 100 },
        width: -200,
        height: 150,
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      const widthErrors = sizeRule.apply(invalidWidthRectangle as any)
      expect(widthErrors.length).toBeGreaterThan(0)
      expect(widthErrors.some(e => e.code === 'INVALID_WIDTH')).toBe(true)

      // Rectangle with negative height
      const invalidHeightRectangle = {
        id: 'rect3',
        type: 'rectangle',
        position: { x: 100, y: 100 },
        width: 200,
        height: -150,
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      const heightErrors = sizeRule.apply(invalidHeightRectangle as any)
      expect(heightErrors.length).toBeGreaterThan(0)
      expect(heightErrors.some(e => e.code === 'INVALID_HEIGHT')).toBe(true)

      // Circle with negative radius
      const invalidCircle = {
        id: 'circle2',
        type: 'circle',
        position: { x: 100, y: 100 },
        radius: -50,
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      const circleErrors = sizeRule.apply(invalidCircle as any)
      expect(circleErrors.length).toBeGreaterThan(0)
      expect(circleErrors.some(e => e.code === 'INVALID_RADIUS')).toBe(true)

      // Ellipse with negative radiusX
      const invalidEllipseX = {
        id: 'ellipse2',
        type: 'ellipse',
        position: { x: 100, y: 100 },
        radiusX: -100,
        radiusY: 50,
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      const ellipseXErrors = sizeRule.apply(invalidEllipseX as any)
      expect(ellipseXErrors.length).toBeGreaterThan(0)
      expect(ellipseXErrors.some(e => e.code === 'INVALID_RADIUS_X')).toBe(true)

      // Ellipse with negative radiusY
      const invalidEllipseY = {
        id: 'ellipse3',
        type: 'ellipse',
        position: { x: 100, y: 100 },
        radiusX: 100,
        radiusY: -50,
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      const ellipseYErrors = sizeRule.apply(invalidEllipseY as any)
      expect(ellipseYErrors.length).toBeGreaterThan(0)
      expect(ellipseYErrors.some(e => e.code === 'INVALID_RADIUS_Y')).toBe(true)
    })
  })

  test.describe('pointCountRule', () => {
    test('passes for polygons with valid point count', () => {
      // Polygon with valid number of points
      const validPolygon = {
        id: 'poly1',
        type: 'polygon',
        position: { x: 100, y: 100 },
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 100, y: 100 },
          { x: 0, y: 100 },
          { x: 0, y: 0 },
        ],
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      expect(pointCountRule.apply(validPolygon as any)).toEqual([])
    })

    test('fails for polygons with invalid point count', () => {
      // Polygon with too few points
      const tooFewPoints = {
        id: 'poly2',
        type: 'polygon',
        position: { x: 100, y: 100 },
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
        ],
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      const fewErrors = pointCountRule.apply(tooFewPoints as any)
      expect(fewErrors.length).toBeGreaterThan(0)
      expect(fewErrors.some(e => e.code === 'INVALID_POINT_COUNT')).toBe(true)

      // Create a polygon with too many points
      const tooManyPoints = {
        id: 'poly3',
        type: 'polygon',
        position: { x: 100, y: 100 },
        points: Array.from({ length: 101 }).fill(0).map((_, i) => ({
          x: Math.cos(i * Math.PI / 50) * 100,
          y: Math.sin(i * Math.PI / 50) * 100,
        })),
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      const manyErrors = pointCountRule.apply(tooManyPoints as any)
      expect(manyErrors.length).toBeGreaterThan(0)
      expect(manyErrors.some(e => e.code === 'INVALID_POINT_COUNT')).toBe(true)
    })

    test('ignores non-polygon shapes', () => {
      // Rectangle (not a polygon)
      const rectangle = {
        id: 'rect1',
        type: 'rectangle',
        position: { x: 100, y: 100 },
        width: 200,
        height: 150,
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      expect(pointCountRule.apply(rectangle as any)).toEqual([])
    })
  })

  test.describe('polygonClosureRule', () => {
    test('passes for closed polygons', () => {
      // Closed polygon (first and last points are the same)
      const closedPolygon = {
        id: 'poly1',
        type: 'polygon',
        position: { x: 100, y: 100 },
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 100, y: 100 },
          { x: 0, y: 100 },
          { x: 0, y: 0 }, // Same as first point
        ],
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      expect(polygonClosureRule.apply(closedPolygon as any)).toEqual([])
    })

    test('fails for open polygons', () => {
      // Open polygon (first and last points are different)
      const openPolygon = {
        id: 'poly2',
        type: 'polygon',
        position: { x: 100, y: 100 },
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 100, y: 100 },
          { x: 0, y: 100 },
          // Missing the closing point
        ],
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      const errors = polygonClosureRule.apply(openPolygon as any)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors.some(e => e.code === 'POLYGON_NOT_CLOSED')).toBe(true)
    })

    test('ignores non-polygon shapes', () => {
      // Rectangle (not a polygon)
      const rectangle = {
        id: 'rect1',
        type: 'rectangle',
        position: { x: 100, y: 100 },
        width: 200,
        height: 150,
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      expect(polygonClosureRule.apply(rectangle as any)).toEqual([])
    })
  })

  test.describe('idRule', () => {
    test('passes for shapes with valid IDs', () => {
      // Shape with valid ID
      const validShape = {
        id: 'shape1',
        type: 'rectangle',
        position: { x: 100, y: 100 },
        width: 200,
        height: 150,
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      expect(idRule.apply(validShape as any)).toEqual([])
    })

    test('fails for shapes with missing or empty IDs', () => {
      // Shape with empty ID
      const emptyIdShape = {
        id: '',
        type: 'rectangle',
        position: { x: 100, y: 100 },
        width: 200,
        height: 150,
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      const emptyErrors = idRule.apply(emptyIdShape as any)
      expect(emptyErrors.length).toBeGreaterThan(0)
      expect(emptyErrors.some(e => e.code === 'MISSING_ID')).toBe(true)

      // Shape with whitespace ID
      const whitespaceIdShape = {
        id: '   ',
        type: 'rectangle',
        position: { x: 100, y: 100 },
        width: 200,
        height: 150,
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      const whitespaceErrors = idRule.apply(whitespaceIdShape as any)
      expect(whitespaceErrors.length).toBeGreaterThan(0)
      expect(whitespaceErrors.some(e => e.code === 'MISSING_ID')).toBe(true)
    })
  })

  test.describe('applyBusinessRules', () => {
    test('applies all business rules to a shape', () => {
      // Invalid shape with multiple issues
      const invalidShape = {
        id: '',
        type: 'polygon',
        position: { x: 100, y: 100 },
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          // Too few points and not closed
        ],
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      const errors = applyBusinessRules(invalidShape as any)

      // Should have at least 2 errors: missing ID and invalid point count
      expect(errors.length).toBeGreaterThanOrEqual(2)
      expect(errors.some(e => e.code === 'MISSING_ID')).toBe(true)
      expect(errors.some(e => e.code === 'INVALID_POINT_COUNT')).toBe(true)
    })

    test('returns empty array for valid shapes', () => {
      // Valid rectangle
      const validRectangle = {
        id: 'rect1',
        type: 'rectangle',
        position: { x: 100, y: 100 },
        width: 200,
        height: 150,
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      expect(applyBusinessRules(validRectangle as any)).toEqual([])

      // Valid polygon
      const validPolygon = {
        id: 'poly1',
        type: 'polygon',
        position: { x: 100, y: 100 },
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 100, y: 100 },
          { x: 0, y: 100 },
          { x: 0, y: 0 }, // Closed polygon
        ],
        strokeColor: '#000000',
        fillColor: '#3b82f6',
      }
      expect(applyBusinessRules(validPolygon as any)).toEqual([])
    })
  })
})
