/**
 * Bounding Box Calculation Strategy for Polygon Elements
 *
 * @remarks
 * This strategy implements the {@link BoundingBoxCalculatorStrategy} for calculating
 * the axis-aligned bounding box of various polygon-like elements, including `POLYGON`,
 * `TRIANGLE`, `HEXAGON`, etc. (as defined in `isPolygonType` helper).
 *
 * The calculation involves:
 * 1. Retrieving the `points` (vertices) of the polygon from the element's properties.
 * 2. Adjusting these points by the polygon element's `position` (inherited from
 *    {@link ShapeElement}) to get absolute coordinates.
 * 3. Using the static method `BoundingBoxClass.fromPointsArray` to compute the
 *    bounding box from these absolute points.
 *
 * The strategy validates that the element is of a supported polygon type and
 * that it has at least one valid vertex after filtering.
 *
 * @module core/compute/strategies/bounding-box/PolygonBoundingBoxStrategy
 * @see {@link Shape.Polygon} for the polygon element type definition.
 * @see {@link BoundingBoxInterface} for the bounding box structure.
 * @see {@link BoundingBoxClass} for the utility class used.
 */
import type { BoundingBoxCalculatorStrategy } from '../../../../types/core/compute'
import type { BoundingBox as BoundingBoxInterface } from '../../../../types/core/element/geometry/bounding-box'
import type { PointData as IPoint } from '../../../../types/core/element/geometry/point' // Use PointData, alias as IPoint
import type {
  Element,
  Shape,
  ShapeElement,
  // MetadataProperties // Not used
} from '../../../../types/core/elementDefinitions'
import { BoundingBoxClass } from '../../../../lib/utils/geometry/BoundingBoxClass'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
  // MetadataProperties // Not used
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

export class PolygonBoundingBoxStrategy implements BoundingBoxCalculatorStrategy {
  private isPolygonType(type: string): boolean {
    return type === CoreElementType.POLYGON
      || type === CoreElementType.TRIANGLE
      || type === CoreElementType.HEXAGON
      || type === CoreElementType.QUADRILATERAL
      || type === CoreElementType.PENTAGON
      || type === CoreElementType.OCTAGON
      || type === CoreElementType.NONAGON
      || type === CoreElementType.DECAGON
  }

  /**
   * Calculates the axis-aligned bounding box of a polygon-like element.
   *
   * @param element - The polygon-like element, expected to be of type {@link ShapeElement}
   *                  and one of the supported polygon types (e.g., {@link CoreElementType.POLYGON}).
   * @returns A {@link BoundingBoxInterface} object representing the bounding box.
   * @throws {@link CoreError} if the element type is not a supported polygon type.
   * @throws {@link CoreError} if the polygon has no valid vertices after filtering,
   *         or if `relativePoints` is missing or not an array.
   */
  public calculateBoundingBox(element: Element): BoundingBoxInterface {
    if (!this.isPolygonType(element.type)) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `Expected a Polygon-like element, received type: ${element?.type}.`,
        undefined,
        { component: 'PolygonBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    const polygonElement = element as unknown as Shape.Polygon // Added unknown for type safety
    const relativePoints = polygonElement.points as IPoint[] | undefined

    if (!relativePoints || !Array.isArray(relativePoints) || relativePoints.length === 0) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Polygon ${element.id} has no vertices.`,
        undefined,
        { component: 'PolygonBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    const validPoints: IPoint[] = []
    const shapeElement = element as ShapeElement
    const elPosX = shapeElement.position?.x ?? 0
    const elPosY = shapeElement.position?.y ?? 0

    for (const p of relativePoints) {
      if (p != null && typeof p.x === 'number' && typeof p.y === 'number' && Number.isFinite(p.x) && Number.isFinite(p.y)) {
        // Convert to absolute points for BoundingBox calculation
        validPoints.push({ x: p.x + elPosX, y: p.y + elPosY, z: p.z })
      }
      else {
        // Optionally log or throw for invalid points within the array
        console.warn(`[PolygonBoundingBoxStrategy] Warning: Skipping invalid vertex data in polygon ${element.id}:`, p)
      }
    }

    if (validPoints.length < 1) { // Should be at least 1 for fromPointsArray, though 3 for a visual polygon
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Polygon ${element.id} contains no valid vertices after filtering.`,
        undefined,
        { component: 'PolygonBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    // Use BoundingBoxClass static method directly
    return BoundingBoxClass.fromPointsArray(validPoints)
  }

  /**
   * Returns the primary element type this strategy is registered for.
   *
   * @remarks
   * Although this strategy can calculate the bounding box for various n-sided polygons
   * (TRIANGLE, HEXAGON, etc.), it is typically registered under the general
   * {@link CoreElementType.POLYGON} type. The `calculateBoundingBox` method internally
   * uses `isPolygonType` to check for specific supported polygon types.
   *
   * @returns The element type {@link CoreElementType.POLYGON}
   */
  public getElementType(): CoreElementType {
    return CoreElementType.POLYGON
  }
}
