/**
 * Core Element Type Definitions and Foundational Interfaces
 *
 * @remarks
 * This module is a cornerstone of the type system, providing the fundamental definitions
 * for all elements within the application. It includes:
 * - {@link ElementType}: An enumeration of all distinct element types (e.g., RECTANGLE, LINE, WALL).
 * - {@link ElementCategories}: A constant object grouping related element types.
 * - Helper functions for element type classification (e.g., {@link getElementCategory}, {@link isPathType}).
 * - {@link BaseStyleProperties}: An interface for common styling attributes.
 * - {@link Element}: The most basic interface for any uniquely identifiable item in the system.
 * - {@link ShapeElement}: An extension of `Element` for visual shapes, incorporating styling,
 *   transformations, interaction properties, and layer information.
 * - {@link MetadataProperties}: An interface for common metadata like timestamps, names, and tags.
 *
 * Furthermore, this module re-exports all specific element type definitions (interfaces)
 * from its sub-namespaces: `Design`, `Shape`, `Path`, `Image`, and `Text`. These namespaces
 * correspond to subdirectories under `./element/` and contain detailed interfaces for
 * each element type (e.g., `Shape.Rectangle`, `Path.Line`, `Design.Wall`).
 *
 * @module types/core/elementDefinitions
 * @see {@link ./canvasTypes} for types related to canvas layers and interaction.
 * @see {@link ./element/geometry/point} for the `Point` (PointData) type definition.
 */

import type { PatternDefinition } from './element/elementPatternTypes'
import type Point from './element/geometry/point'
import type { MajorCategory, MinorCategory } from './majorMinorTypes'

// Import types from subdirectories
import * as Design from './element/design/design'
import * as Image from './element/image/image'
import * as Path from './element/path/path'
import * as Shape from './element/shape/shape'
import * as Text from './element/text/text'

/**
 * Enumerates all distinct types of elements that can exist within the application.
 *
 * @remarks
 * Each member of this enum represents a specific kind of visual or logical entity,
 * such as a geometric shape, a path, a design component (like a wall or door),
 * or a utility element (like a text label or light).
 * The `@see` tags often point to the conceptual location of the corresponding
 * detailed type definition (e.g., `Shape.Rectangle` for `RECTANGLE`).
 *
 * @see {@link ElementCategories} for how these types are grouped.
 */
export enum ElementType {
  // Basic shapes - Shape directory
  // Basic shapes - Defined in ./element/shape/
  /** A rectangle. @see {@link Shape.Rectangle} */
  RECTANGLE = 'RECTANGLE',
  /** A square, a special case of a rectangle. @see {@link Shape.Rectangle} */
  SQUARE = 'SQUARE',
  /** An ellipse. @see {@link Shape.Ellipse} */
  ELLIPSE = 'ELLIPSE',
  /** A circle, a special case of an ellipse. @see {@link Shape.Circle} */
  CIRCLE = 'CIRCLE',

  // Polygons - Defined in ./element/shape/
  /** A generic polygon with an arbitrary number of sides. @see {@link Shape.Polygon} */
  POLYGON = 'POLYGON',
  /** A triangle (3-sided polygon). @see {@link Shape.Triangle} */
  TRIANGLE = 'TRIANGLE',
  /** A quadrilateral (4-sided polygon). @see {@link Shape.Polygon} */
  QUADRILATERAL = 'QUADRILATERAL',
  /** A pentagon (5-sided polygon). @see {@link Shape.Polygon} */
  PENTAGON = 'PENTAGON',
  /** A hexagon (6-sided polygon). @see {@link Shape.Hexagon} */
  HEXAGON = 'HEXAGON',
  /** A heptagon (7-sided polygon). @see {@link Shape.Polygon} */
  HEPTAGON = 'HEPTAGON',
  /** An octagon (8-sided polygon). @see {@link Shape.Polygon} */
  OCTAGON = 'OCTAGON',
  /** A nonagon (9-sided polygon). @see {@link Shape.Polygon} */
  NONAGON = 'NONAGON',
  /** A decagon (10-sided polygon). @see {@link Shape.Polygon} */
  DECAGON = 'DECAGON',

  // Paths - Defined in ./element/path/
  /** A straight line segment. @see {@link Path.Line} */
  LINE = 'LINE',
  /** A sequence of connected line segments. @see {@link Path.Polyline} */
  POLYLINE = 'POLYLINE',
  /** An arc segment. @see {@link Path.Arc} */
  ARC = 'ARC',
  /** A quadratic Bezier curve. @see {@link Path.Quadratic} */
  QUADRATIC = 'QUADRATIC',
  /** A cubic Bezier curve. @see {@link Path.Cubic} */
  CUBIC = 'CUBIC',

  // Text - Defined in ./element/text/
  /** A text label or annotation. @see {@link Text.TextElement} */
  TEXT_LABEL = 'TEXT_LABEL',

  // Interior design elements - Defined in ./element/design/
  /** A wall structure. @see {@link Design.WallElement} */
  WALL = 'WALL',
  /** A door opening. @see {@link Design.DoorElement} */
  DOOR = 'DOOR',
  /** A window opening. @see {@link Design.WindowElement} */
  WINDOW = 'WINDOW',
  /** A furniture item. @see {@link Design.FurnitureElement} */
  FURNITURE = 'FURNITURE',
  /** A fixed fixture (e.g., sink, toilet). @see {@link Design.FixtureElement} */
  FIXTURE = 'FIXTURE',
  /** A room or defined space. @see {@link Design.RoomElement} */
  ROOM = 'ROOM',

  // Additional types for specific strategies or specialized elements
  /** Represents a light source or lighting fixture. @see {@link Design.FixtureElement} or a custom light type. */
  LIGHT = 'LIGHT',
  /** Represents a defined floor area, possibly for material or zoning. */
  FLOOR_AREA = 'FLOOR_AREA',
  /** Represents a handrail, often for stairs or accessibility. */
  HANDRAIL = 'HANDRAIL',
  /** Represents an electrical outlet. */
  ELECTRICAL_OUTLET = 'ELECTRICAL_OUTLET',
  /** Represents a boundary defining a room or space, potentially a conceptual element. */
  ROOM_BOUNDARY = 'ROOM_BOUNDARY',
  /** Represents a kitchen or other household appliance. @see {@link Design.FixtureElement} or a custom appliance type. */
  APPLIANCE = 'APPLIANCE',

  // Utility elements
  TEXT = 'TEXT', // Ensure consistent uppercase value
  IMAGE = 'IMAGE', // Ensure consistent uppercase value
  GROUP = 'group', // For grouping multiple elements

  // Design-specific subtypes that might be treated as distinct element types
  OPENING = 'OPENING', // For generic openings, could be Door or Window subtype
  WALL_PAINT = 'WALL_PAINT', // Represents a paint application/layer on a wall
  WALL_PAPER = 'WALL_PAPER', // Represents a wallpaper application/layer on a wall
}

/**
 * Defines logical groupings for {@link ElementType}s.
 *
 * @remarks
 * These categories are primarily used for organizing elements in the user interface,
 * such as in tool palettes or selection filters. They help users find and manage
 * different types of elements more easily.
 *
 * @see {@link ElementType} for individual element type definitions.
 */
export const ElementCategories = {
  BASIC_SHAPES: [
    ElementType.RECTANGLE,
    ElementType.SQUARE,
    ElementType.ELLIPSE,
    ElementType.CIRCLE,
  ],
  POLYGONS: [
    ElementType.POLYGON,
    ElementType.TRIANGLE,
    ElementType.QUADRILATERAL,
    ElementType.PENTAGON,
    ElementType.HEXAGON,
    ElementType.HEPTAGON,
    ElementType.OCTAGON,
    ElementType.NONAGON,
    ElementType.DECAGON,
  ],
  PATHS: [
    ElementType.LINE,
    ElementType.POLYLINE,
    ElementType.ARC,
    ElementType.QUADRATIC,
    ElementType.CUBIC,
  ],
  TEXT: [
    ElementType.TEXT_LABEL,
  ],
  INTERIOR_DESIGN: [
    ElementType.WALL,
    ElementType.DOOR,
    ElementType.WINDOW,
    ElementType.FURNITURE,
    ElementType.FIXTURE,
    ElementType.ROOM,
  ],
}

/**
 * Retrieves the category key for a given {@link ElementType}.
 *
 * @param type - The {@link ElementType} to categorize.
 * @returns The key of the category in {@link ElementCategories} to which the element type belongs,
 * or `undefined` if the type is not found in any category.
 */
export function getElementCategory(type: ElementType): keyof typeof ElementCategories | undefined {
  for (const [category, types] of Object.entries(ElementCategories)) {
    if (types.includes(type)) {
      return category as keyof typeof ElementCategories
    }
  }
  return undefined
}

/**
 * Checks if a given {@link ElementType} falls under the 'BASIC_SHAPES' or 'POLYGONS' categories.
 *
 * @param type - The {@link ElementType} to check.
 * @returns `true` if the element type is considered a basic shape or polygon, `false` otherwise.
 */
export function isElementType(type: ElementType): boolean {
  return [
    ...ElementCategories.BASIC_SHAPES,
    ...ElementCategories.POLYGONS,
  ].includes(type)
}

/**
 * Checks if a given {@link ElementType} falls under the 'PATHS' category.
 *
 * @param type - The {@link ElementType} to check.
 * @returns `true` if the element type is considered a path, `false` otherwise.
 */
export function isPathType(type: ElementType): boolean {
  return ElementCategories.PATHS.includes(type)
}

/**
 * Checks if a given {@link ElementType} falls under the 'TEXT' category.
 *
 * @param type - The {@link ElementType} to check.
 * @returns `true` if the element type is considered a text element, `false` otherwise.
 */
export function isTextType(type: ElementType): boolean {
  return ElementCategories.TEXT.includes(type)
}

/**
 * Checks if a given {@link ElementType} falls under the 'INTERIOR_DESIGN' category.
 *
 * @param type - The {@link ElementType} to check.
 * @returns `true` if the element type is considered an interior design element, `false` otherwise.
 */
export function isInteriorDesignType(type: ElementType): boolean {
  return ElementCategories.INTERIOR_DESIGN.includes(type)
}

// For backward compatibility with string literal types
export const ElementTypeValues = ElementType

/**
 * Defines common styling properties applicable to most visual elements.
 *
 * @remarks
 * These properties control the appearance of elements, such as their fill,
 * stroke, and opacity.
 */
export interface BaseStyleProperties {
  /**
   * The fill color or pattern of the element.
   * Can be a CSS color string (e.g., '#FF0000', 'blue') or a reference to a pattern/gradient.
   */
  fill?: string
  /**
   * The stroke (outline) color of the element.
   * Can be a CSS color string.
   */
  stroke?: string
  /** The width of the stroke in pixels. */
  strokeWidth?: number
  /** The opacity level of the element, ranging from 0 (fully transparent) to 1 (fully opaque). */
  opacity?: number
  /** Optional: A string representing the pattern of dashes and gaps used to stroke paths. E.g., "5, 5" */
  strokeDasharray?: string
}

/**
 * Defines the minimal set of properties required for any element in the system.
 *
 * @remarks
 * This is a foundational interface that other more specific element interfaces (like {@link ShapeElement}) extend.
 * It includes essential attributes like ID, type, visibility, lock status, and metadata.
 */
export interface Element {
  /** The unique identifier for this element instance. */
  id: string
  /**
   * A string identifier representing the type of the element.
   * This often corresponds to an {@link ElementType} member but can be more generic.
   */
  type: string
  /** Indicates whether the element is currently visible on the canvas or in the UI. */
  visible: boolean
  /** Indicates whether the element is locked and cannot be edited or moved. */
  locked: boolean
  /** Optional {@link MetadataProperties} associated with the element. */
  metadata?: MetadataProperties
}

/**
 * Extends the base {@link Element} interface with properties specific to visual shapes.
 *
 * @remarks
 * This interface includes attributes related to styling (fill, stroke),
 * transformations (position, rotation, scale), interaction (selectable, draggable),
 * and layer management.
 */
export interface ShapeElement extends Element, BaseStyleProperties { // Added BaseStyleProperties
  // Visual style properties from BaseStyleProperties are now inherited
  // fill?: string; // Already in BaseStyleProperties
  // stroke?: string; // Already in BaseStyleProperties
  // strokeWidth?: number; // Already in BaseStyleProperties
  // opacity?: number; // Already in BaseStyleProperties
  // strokeDasharray?: string; // Now inherited from BaseStyleProperties

  // Transform properties
  /** The geometric {@link Point} representing the shape's origin or primary position. */
  position: Point
  /** The rotation angle of the shape in degrees. */
  rotation: number

  // Interaction properties
  /** Indicates whether the shape can be selected by the user. */
  selectable: boolean
  /** Indicates whether the shape can be dragged by the user. */
  draggable: boolean
  /** Indicates whether control handles (for resizing, rotating, etc.) should be displayed for this shape when selected. */
  showHandles: boolean

  // Layer properties
  /**
   * The z-index for rendering order. Higher values are rendered on top.
   * This will be primarily derived from the selected Z-Level.
   */
  zIndex?: number
  /**
   * The identifier of the layer to which this shape belongs.
   * This typically corresponds to a {@link CanvasLayer} member.
   * @deprecated Prefer majorCategory, minorCategory, and zLevelId for finer-grained layer control.
   */
  layer?: string
  /** Optional: For any other custom or shape-specific properties not defined at the top level. */
  properties?: Record<string, unknown>

  // --- Layer Panel Integration ---
  /** The MajorCategory ID (Module ID from LayerPanel) this element belongs to. */
  majorCategory: MajorCategory
  /** The MinorCategory ID (Step ID from LayerPanel) this element belongs to. */
  minorCategory?: MinorCategory
  /** The ID of the specific ZLevel (user-defined layer from LayerPanel's TaskStep.zLevels) this element is assigned to. */
  zLevelId?: string
  /** Indicates if the element's major and minor categories are fixed (e.g. for special assets from specific asset panel categories) and should not be changed in the property editor. */
  isFixedCategory?: boolean
  /** The z-order of the element within its assigned Z-level. Higher values are rendered on top within that Z-level. */
  intraLayerZIndex?: number
  // --- End Layer Panel Integration ---

  pattern?: PatternDefinition

  /** Optional: The radius for rounded corners, applicable to shapes like rectangles. */
  cornerRadius?: number

  // Optional geometric properties, often also in `properties` but promoted for direct access
  /** Optional: Width of the element, if applicable (e.g., for Rectangles). */
  width?: number
  /** Optional: Height of the element, if applicable (e.g., for Rectangles). */
  height?: number
  /** Optional: Radius of the element, if applicable (e.g., for Circles). */
  radius?: number
  /** Optional: X-axis radius of the element, if applicable (e.g., for Ellipses). */
  radiusX?: number
  /** Optional: Y-axis radius of the element, if applicable (e.g., for Ellipses). */
  radiusY?: number

  // Cost calculation properties
  /** Whether cost calculation is enabled for this element */
  costEnabled?: boolean
  /** Unit price for cost calculation */
  costUnitPrice?: number
  /** Quantity factor for cost calculation */
  costQuantityFactor?: number
  /** Basis for cost calculation ('area' | 'perimeter' | 'length' | 'unit') */
  costBasis?: string
  /** Total calculated cost */
  costTotal?: number
}

/**
 * Defines a structure for common metadata associated with any element.
 *
 * @remarks
 * This interface allows for storing information like creation/update timestamps,
 * creator details, descriptive names, tags, and other custom data.
 */
export interface MetadataProperties {
  /** The timestamp (milliseconds since epoch) when the element was created. */
  createdAt: number
  /** The timestamp (milliseconds since epoch) when the element was last updated. */
  updatedAt: number
  /** An optional identifier for the user or process that created the element. */
  createdBy?: string
  /** An optional identifier for the user or process that last updated the element. */
  updatedBy?: string // Added for completeness
  /** Optional version number for the element's data structure or content. */
  version?: number // Added for completeness
  /** An optional display name for the element, used in UI or listings. */
  name?: string
  /** An optional longer description of the element. */
  description?: string
  /** An array of strings for categorizing or tagging the element. */
  tags?: string[]
  /**
   * An optional string to classify the element by its design type
   * (e.g., 'modern', 'classic', 'industrial').
   */
  designType?: string
  /**
   * An optional string to classify the element by its design category
   * (e.g., 'residential', 'commercial', 'furniture_accent').
   */
  designCategory?: string
  /** Optional layer identifier. */
  layer?: string
  /** Optional z-index for ordering within a layer or globally. */
  zIndex?: number
  /** Indicates whether the element is currently visible. This is also on Element interface. */
  visible?: boolean // Keep as optional here if it can be part of metadata overrides
  /** Indicates whether the element is locked. This is also on Element interface. */
  locked?: boolean // Keep as optional here if it can be part of metadata overrides
  /**
   * Allows for additional, arbitrary custom properties to be stored with the element.
   */
  customProperties?: Record<string, unknown>
  /** Optional: Age of the child for whom a room or element is designed, used in children's room planning. */
  childAge?: number
}

// Re-export types from submodules for unified imports
export {
  Design,
  Image,
  Path,
  Shape,
  Text,
}

// --- Re-export MajorCategory and MinorCategory using export type ---
export type { MajorCategory, MinorCategory }
// --- End Re-export ---
