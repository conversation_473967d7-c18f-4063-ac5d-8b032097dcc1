/**
 * Tutorial Trigger Component
 *
 * A button component that provides access to tutorial functionality.
 * Can be used to start tutorials, show tutorial menu, or display tutorial status.
 */

import { HelpCircle, Play } from 'lucide-react'
import React from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { useTutorialTrigger } from '@/hooks/useTutorial'

interface TutorialTriggerProps {
  /** Button variant */
  variant?: 'default' | 'outline' | 'ghost' | 'secondary'
  /** Button size */
  size?: 'default' | 'sm' | 'lg' | 'icon'
  /** Whether to show as dropdown menu */
  showMenu?: boolean
  /** Custom className */
  className?: string
}

/**
 * Tutorial trigger button component
 */
export const TutorialTrigger: React.FC<TutorialTriggerProps> = ({
  variant = 'ghost',
  size = 'icon',
  showMenu = true,
  className,
}) => {
  const { startTutorial, startTutorialByCategory } = useTutorialTrigger()

  /**
   * Handle tutorial selection
   */
  const handleTutorialSelect = (tutorialId: string) => {
    startTutorial(tutorialId)
  }

  /**
   * Handle category selection
   */
  const handleCategorySelect = (category: 'onboarding' | 'feature' | 'advanced') => {
    startTutorialByCategory(category)
  }

  if (!showMenu) {
    // Simple button that starts the main onboarding tutorial
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={variant}
              size={size}
              className={className}
              onClick={() => handleCategorySelect('onboarding')}
            >
              <HelpCircle className="h-4 w-4" />
              {size !== 'icon' && <span className="ml-2">Tutorial</span>}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Start Tutorial</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  return (
    <DropdownMenu>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <Button
                variant={variant}
                size={size}
                className={className}
                data-tutorial="tutorial-trigger"
              >
                <HelpCircle className="h-4 w-4" />
                {size !== 'icon' && <span className="ml-2">Tutorial</span>}
              </Button>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>Tutorial Menu</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel>
          <span>RenoPilot Tutorial Guide</span>
        </DropdownMenuLabel>

        <div className="px-2 py-1 text-xs text-muted-foreground">
          Interactive tutorials to help you learn RenoPilot's interface and tools
        </div>

        <DropdownMenuSeparator />

        {/* Essential Interface Tutorials */}
        <DropdownMenuItem onClick={() => handleTutorialSelect('app-overview')}>
          <Play className="w-4 h-4 mr-2" />
          <div className="flex flex-col">
            <span>Getting Started</span>
            <span className="text-xs text-muted-foreground">Overview of all interface areas and basic navigation</span>
          </div>
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => handleTutorialSelect('toolbar-features')}>
          <Play className="w-4 h-4 mr-2" />
          <div className="flex flex-col">
            <span>Toolbar Features</span>
            <span className="text-xs text-muted-foreground">Project management, editing tools, and workspace controls</span>
          </div>
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => handleTutorialSelect('layer-panel')}>
          <Play className="w-4 h-4 mr-2" />
          <div className="flex flex-col">
            <span>Project Progress</span>
            <span className="text-xs text-muted-foreground">Track tasks, manage modules, and organize layers</span>
          </div>
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => handleTutorialSelect('drawing-tools')}>
          <Play className="w-4 h-4 mr-2" />
          <div className="flex flex-col">
            <span>Drawing Tools</span>
            <span className="text-xs text-muted-foreground">Shapes, paths, images, and text tools for design</span>
          </div>
        </DropdownMenuItem>

      </DropdownMenuContent>
    </DropdownMenu>
  )
}

/**
 * Simple tutorial help button
 */
export const TutorialHelpButton: React.FC<{
  className?: string
  tutorialId?: string
}> = ({ className, tutorialId }) => {
  const { startTutorial, startTutorialByCategory } = useTutorialTrigger()

  const handleClick = () => {
    if (tutorialId !== null && tutorialId !== undefined && tutorialId.trim() !== '') {
      startTutorial(tutorialId)
    }
    else {
      startTutorialByCategory('onboarding')
    }
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      className={className}
      onClick={handleClick}
      title="Get Help"
    >
      <HelpCircle className="h-4 w-4" />
    </Button>
  )
}
