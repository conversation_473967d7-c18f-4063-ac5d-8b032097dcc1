/**
 * Application Event Bus Implementation
 *
 * Provides an advanced event bus for application events with comprehensive features
 * including priority-based handling, filtering, async processing, and type safety.
 *
 * @remarks
 * This implementation provides the following features:
 * - Priority-based event handling
 * - Event filtering capabilities
 * - Asynchronous event processing
 * - Debounced and throttled events
 * - Comprehensive logging support
 * - Type-safe event handling with TypeScript
 *
 * @example
 * ```typescript
 * const eventBus = new AppEventBusImpl();
 * eventBus.configure({ enableLogging: true });
 *
 * // Subscribe to events
 * const unsubscribe = eventBus.subscribe(AppEventType.ShapeCreated, (event) => {
 *   console.log('Shape created:', event.payload);
 * });
 *
 * // Publish events
 * eventBus.publish({
 *   type: AppEventType.ShapeCreated,
 *   payload: { shapeId: 'shape-1' }
 * });
 * ```
 *
 * @module services/core/event-bus/appEventBus
 */

import type {
  BaseEvent,
  EventBusConfig,
  EventHandler,
  EventSubscriptionOptions,
  ExtendedEventBus,
} from '@/types/services/events/eventCore'
import type { AppEventMap } from '@/types/services/events/eventRegistry'

/**
 * Default configuration for the event bus.
 *
 * @internal
 */
const DEFAULT_CONFIG: EventBusConfig = {
  enableLogging: false,
  defaultPriority: 0,
  maxAsyncHandlers: 100,
  defaultDebounceTime: 300,
  defaultThrottleTime: 300,
}

/**
 * Enhanced application event bus implementation with comprehensive type safety.
 *
 * @remarks
 * This class provides a robust event bus implementation that supports:
 * - Type-safe event subscription and publishing
 * - Advanced features like debouncing, throttling, and filtering
 * - Asynchronous event handling
 * - Comprehensive error handling and logging
 *
 * @public
 */
export class AppEventBusImpl implements ExtendedEventBus<AppEventMap> {
  /**
   * Singleton instance of AppEventBusImpl.
   */
  private static instance: AppEventBusImpl

  /**
   * Private constructor to prevent direct instantiation.
   */
  private constructor() { }

  /**
   * Provides the singleton instance of AppEventBusImpl.
   *
   * @returns The singleton instance of AppEventBusImpl.
   */
  public static getInstance(): AppEventBusImpl {
    if (!AppEventBusImpl.instance) {
      AppEventBusImpl.instance = new AppEventBusImpl()
    }
    return AppEventBusImpl.instance
  }

  /**
   * Resets the singleton instance (primarily for testing).
   *
   * @internal
   */
  public static resetInstance(): void {
    AppEventBusImpl.instance = undefined as any
  }

  /**
   * Map of event types to handlers with options.
   *
   * @remarks
   * Using keys of AppEventMap for stronger type checking.
   */
  private handlers: Map<keyof AppEventMap, Array<{
    handler: EventHandler<BaseEvent>
    options: EventSubscriptionOptions
  }>> = new Map()

  /**
   * Configuration options.
   */
  private config: EventBusConfig = { ...DEFAULT_CONFIG }

  /**
   * Map of debounced handlers.
   */
  private debouncedHandlers: Map<string, {
    handler: EventHandler
    timeout: NodeJS.Timeout
  }> = new Map()

  /**
   * Map of throttled handlers.
   */
  private throttledHandlers: Map<string, {
    handler: EventHandler
    lastCall: number
  }> = new Map()

  /**
   * Configures the event bus.
   *
   * @param config - Configuration options.
   */
  public configure(config: Partial<EventBusConfig>): void {
    this.config = { ...this.config, ...config }
  }

  /**
   * Subscribes to an event with type-safe handler.
   *
   * @param eventType - Event type key from AppEventMap.
   * @param handler - Event handler with correct event type based on eventType.
   * @param options - Subscription options.
   * @returns Unsubscribe function.
   */
  public subscribe<K extends keyof AppEventMap>(
    eventType: K,
    handler: EventHandler<AppEventMap[K]>,
    options: EventSubscriptionOptions = {},
  ): () => void {
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, [])
    }

    const handlers = this.handlers.get(eventType)
    if (!handlers) {
      return () => {}
    }

    handlers.push({
      handler: handler as EventHandler<BaseEvent>,
      options,
    })

    if (this.config.enableLogging === true) {
      console.warn(`[EventBus] Subscribed to ${String(eventType)}`, { handler, options })
    }

    return () => {
      this.unsubscribe(eventType, handler)
    }
  }

  /**
   * Alias for subscribe with type safety.
   */
  public on<K extends keyof AppEventMap>(
    eventType: K,
    handler: EventHandler<AppEventMap[K]>,
    options: EventSubscriptionOptions = {},
  ): () => void {
    return this.subscribe(eventType, handler, options)
  }

  /**
   * Publishes an event with type checking.
   *
   * @param event - Event data (must conform to AppEventMap entry for its type).
   * @returns True if the event was published.
   */
  public publish<E extends AppEventMap[keyof AppEventMap] & BaseEvent>(event: E): boolean {
    if (!this.isValidEvent(event)) {
      console.error('[EventBus] Invalid event format:', event)
      return false
    }

    const eventType = event.type as keyof AppEventMap
    const currentHandlers = this.handlers.get(eventType)

    if (!currentHandlers || currentHandlers.length === 0) {
      if (this.config.enableLogging === true) {
        console.warn(`[EventBus] No handlers for event ${String(eventType)}`)
      }
      return false
    }

    const eventWithTimestamp = event as BaseEvent & { timestamp?: number }
    if (typeof eventWithTimestamp.timestamp !== 'number' || eventWithTimestamp.timestamp === 0) {
      eventWithTimestamp.timestamp = Date.now()
    }

    if (this.config.enableLogging === true) {
      console.warn(`[EventBus] Publishing event ${String(eventType)}`, event)
    }

    const handlersToCall = currentHandlers.slice()
    const handlersToRemove: EventHandler<BaseEvent>[] = []
    const asyncHandlers: Promise<unknown>[] = []

    for (const { handler, options } of handlersToCall) {
      try {
        if (options.filter && !options.filter(event)) {
          continue
        }
        if (typeof options.debounce === 'number' && options.debounce > 0) {
          this.handleDebouncedEvent(eventType, handler, event, options)
          continue
        }
        if (typeof options.throttle === 'number' && options.throttle > 0) {
          this.handleThrottledEvent(eventType, handler, event, options)
          continue
        }

        const typedHandler = handler as EventHandler<typeof event>

        if (options.async === true) {
          const promise = Promise.resolve().then(() => {
            if (typeof options.context === 'object' && options.context !== null) {
              return typedHandler.call(options.context, event)
            }
            else {
              return typedHandler(event)
            }
          })
          asyncHandlers.push(promise)
        }
        else {
          if (typeof options.context === 'object' && options.context !== null) {
            typedHandler.call(options.context, event)
          }
          else {
            typedHandler(event)
          }
        }

        if (options.once) {
          handlersToRemove.push(handler)
        }
      }
      catch (error) {
        console.error(`Error in event handler for ${String(eventType)}:`, error)
      }
    }

    for (const handlerToRemove of handlersToRemove) {
      this.unsubscribe(eventType, handlerToRemove)
    }

    return true
  }

  /**
   * Publishes an event asynchronously with type checking.
   *
   * @param event - Event data.
   * @returns Promise that resolves when all async handlers have completed.
   */
  public async publishAsync<E extends AppEventMap[keyof AppEventMap] & BaseEvent>(event: E): Promise<unknown[]> {
    if (!this.isValidEvent(event)) {
      console.error('[EventBus] Invalid event format:', event)
      return []
    }

    const eventType = event.type as keyof AppEventMap
    const currentHandlers = this.handlers.get(eventType)

    if (!currentHandlers || currentHandlers.length === 0) {
      if (this.config.enableLogging === true) {
        console.warn(`[EventBus] No handlers for async event ${String(eventType)}`)
      }
      return []
    }

    const eventWithTimestamp = event as BaseEvent & { timestamp?: number }
    if (typeof eventWithTimestamp.timestamp !== 'number' || eventWithTimestamp.timestamp === 0) {
      eventWithTimestamp.timestamp = Date.now()
    }

    if (this.config.enableLogging === true) {
      console.warn(`[EventBus] Publishing async event ${String(eventType)}`, event)
    }

    const handlersToCall = currentHandlers.slice()
    const asyncHandlers: Promise<unknown>[] = []

    for (const { handler, options } of handlersToCall) {
      try {
        if (options.filter && !options.filter(event)) {
          continue
        }
        if (typeof options.debounce === 'number' && options.debounce > 0) {
          this.handleDebouncedEvent(eventType, handler, event, options)
          continue
        }
        if (typeof options.throttle === 'number' && options.throttle > 0) {
          this.handleThrottledEvent(eventType, handler, event, options)
          continue
        }

        const typedHandler = handler as EventHandler<typeof event>

        if (options.async === true) {
          const promise = Promise.resolve().then(() => {
            if (typeof options.context === 'object' && options.context !== null) {
              return typedHandler.call(options.context, event)
            }
            else {
              return typedHandler(event)
            }
          })
          asyncHandlers.push(promise)
        }
        else {
          if (typeof options.context === 'object' && options.context !== null) {
            typedHandler.call(options.context, event)
          }
          else {
            typedHandler(event)
          }
        }

        if (options.once) {
          this.unsubscribe(eventType, handler)
        }
      }
      catch (error) {
        console.error(`Error in async event handler for ${String(eventType)}:`, error)
        asyncHandlers.push(Promise.reject(error))
      }
    }

    const results = await Promise.allSettled(asyncHandlers)
    results.forEach((result) => {
      if (result.status === 'rejected') {
        console.error(`[EventBus] Async handler error for ${String(event.type)}:`, result.reason)
      }
    })
    return results
  }

  /**
   * Alias for publish.
   */
  public emit<E extends AppEventMap[keyof AppEventMap] & BaseEvent>(event: E): boolean {
    return this.publish(event)
  }

  /**
   * Alias for publishAsync.
   */
  public async emitAsync<E extends AppEventMap[keyof AppEventMap] & BaseEvent>(event: E): Promise<unknown[]> {
    return this.publishAsync(event)
  }

  /**
   * Handles debounced event with type safety.
   *
   * @param eventType - Event type.
   * @param handler - Event handler.
   * @param event - Event data.
   * @param options - Subscription options.
   */
  private handleDebouncedEvent<K extends keyof AppEventMap>(
    eventType: K,
    handler: EventHandler<BaseEvent>,
    event: AppEventMap[K] & BaseEvent,
    options: EventSubscriptionOptions,
  ): void {
    const debounceTime = (typeof options.debounce === 'number' && options.debounce > 0)
      ? options.debounce
      : (typeof this.config.defaultDebounceTime === 'number' ? this.config.defaultDebounceTime : 300)
    const handlerId = this.getHandlerId(eventType as string, handler)

    const existing = this.debouncedHandlers.get(handlerId)
    if (existing) {
      clearTimeout(existing.timeout)
    }

    const timeout = setTimeout(() => {
      try {
        if (typeof options.context === 'object' && options.context !== null) {
          handler.call(options.context, event)
        }
        else {
          handler(event)
        }

        this.debouncedHandlers.delete(handlerId)
      }
      catch (error) {
        console.error(`Error in debounced event handler for ${String(eventType)}:`, error)
      }
    }, debounceTime)

    this.debouncedHandlers.set(handlerId, { handler, timeout })
  }

  /**
   * Handles throttled event with type safety.
   *
   * @param eventType - Event type.
   * @param handler - Event handler.
   * @param event - Event data.
   * @param options - Subscription options.
   */
  private handleThrottledEvent<K extends keyof AppEventMap>(
    eventType: K,
    handler: EventHandler<BaseEvent>,
    event: AppEventMap[K] & BaseEvent,
    options: EventSubscriptionOptions,
  ): void {
    const throttleTime = (typeof options.throttle === 'number' && options.throttle > 0)
      ? options.throttle
      : (typeof this.config.defaultThrottleTime === 'number' && this.config.defaultThrottleTime > 0)
          ? this.config.defaultThrottleTime
          : 0
    const handlerId = this.getHandlerId(eventType as string, handler)
    const now = Date.now()

    const existing = this.throttledHandlers.get(handlerId)
    if (existing && now - existing.lastCall < throttleTime) {
      return
    }

    try {
      if (typeof options.context === 'object' && options.context !== null) {
        handler.call(options.context, event)
      }
      else {
        handler(event)
      }

      this.throttledHandlers.set(handlerId, { handler, lastCall: now })
    }
    catch (error) {
      console.error(`Error in throttled event handler for ${String(eventType)}:`, error)
    }
  }

  /**
   * Gets unique handler ID with type safety.
   *
   * @param eventType - Event type.
   * @param handler - Event handler.
   * @returns Handler ID.
   */
  private getHandlerId(eventType: string, handler: EventHandler): string {
    return `${eventType}-${handler.toString()}`
  }

  /**
   * Unsubscribes from an event.
   *
   * @param eventType - Event type to unsubscribe from.
   * @param handler - Event handler to unsubscribe.
   * @returns True if the handler was unsubscribed.
   */
  public unsubscribe<K extends keyof AppEventMap>(eventType: K, handler: EventHandler<AppEventMap[K]>): boolean {
    const handlers = this.handlers.get(eventType)
    if (!handlers) {
      return false
    }

    const originalLength = handlers.length
    const updatedHandlers = handlers.filter(h => h.handler !== (handler as EventHandler<BaseEvent>))

    if (updatedHandlers.length < originalLength) {
      if (updatedHandlers.length === 0) {
        this.handlers.delete(eventType)
      }
      else {
        this.handlers.set(eventType, updatedHandlers)
      }
      if (this.config.enableLogging === true) {
        console.warn(`[EventBus] Unsubscribed from ${String(eventType)}`)
      }
      return true
    }

    return false
  }

  /**
   * Alias for unsubscribe.
   */
  public off<K extends keyof AppEventMap>(eventType: K, handler: EventHandler<AppEventMap[K]>): boolean {
    return this.unsubscribe(eventType, handler)
  }

  /**
   * Unsubscribes all handlers for an event type.
   *
   * @param eventType - Event type to unsubscribe all handlers from.
   * @returns True if any handlers were unsubscribed.
   */
  public unsubscribeAll(eventType: keyof AppEventMap): boolean {
    if (this.handlers.has(eventType)) {
      this.handlers.delete(eventType)
      if (this.config.enableLogging === true) {
        console.warn(`[EventBus] Unsubscribed all handlers from ${String(eventType)}`)
      }
      return true
    }
    return false
  }

  /**
   * Clears all subscriptions.
   */
  public clear(): void {
    this.handlers.clear()
    this.debouncedHandlers.clear()
    this.throttledHandlers.clear()
    if (this.config.enableLogging === true) {
      console.warn('[EventBus] All subscriptions cleared')
    }
  }

  /**
   * Resets the event bus.
   */
  public reset(): void {
    this.clear()
    this.config = { ...DEFAULT_CONFIG }
    if (this.config.enableLogging === true) {
      console.warn('[EventBus] Reset')
    }
  }

  /**
   * Gets all subscriptions.
   *
   * @returns Map of event types to handlers.
   */
  public getSubscriptions(): Map<string, Array<{
    handler: EventHandler<BaseEvent>
    options: EventSubscriptionOptions
  }>> {
    return this.handlers as Map<string, Array<{
      handler: EventHandler<BaseEvent>
      options: EventSubscriptionOptions
    }>>
  }

  /**
   * Subscribes to an event once.
   *
   * @param eventType - Event type.
   * @param handler - Event handler.
   * @param options - Subscription options.
   * @returns Unsubscribe function.
   */
  public once<K extends keyof AppEventMap>(
    eventType: K,
    handler: EventHandler<AppEventMap[K]>,
    options: Omit<EventSubscriptionOptions, 'once'> = {},
  ): () => void {
    return this.subscribe(eventType, handler, { ...options, once: true })
  }

  private isValidEvent(event: unknown): boolean {
    return typeof event === 'object' && event !== null && 'type' in event && typeof (event as { type: unknown }).type !== 'undefined'
  }
}
