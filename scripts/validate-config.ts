#!/usr/bin/env tsx
/**
 * Configuration Validation Script
 * Validates environment configuration according to Context7 best practices
 */

import { getEnvironmentConfig, validateEnvironment } from '../src/config/environment.js'

function main() {
  console.log('🔍 Validating environment configuration...\n')
  const config = getEnvironmentConfig()
  const validation = validateEnvironment()

  // Display current configuration
  console.log('📋 Current Configuration:')
  console.log('========================')
  console.log(`Environment: ${config.environment}`)
  console.log(`App Name: ${config.appName}`)
  console.log(`App Version: ${config.appVersion}`)
  console.log(`API Base URL: ${config.apiBaseUrl}`)
  console.log(`Debug Mode: ${config.enableDebugMode ? '✅' : '❌'}`)
  console.log(`Log Level: ${config.logLevel}`)
  console.log(`Canvas Max Size: ${config.canvasMaxWidth}x${config.canvasMaxHeight}`)
  console.log('')

  // Display validation results
  if (validation.valid) {
    console.log('✅ Configuration is valid!')
    process.exit(0)
  } else {
    console.log('❌ Configuration validation failed:')
    validation.errors.forEach(error => {
      console.log(`   • ${error}`)
    })
    console.log('')
    console.log('Please check your .env file and fix the issues above.')
    process.exit(1)
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}
