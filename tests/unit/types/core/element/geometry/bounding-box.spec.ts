import type { BoundingBox } from '@/types/core/element/geometry/bounding-box'
import type Point from '@/types/core/element/geometry/point'
import type { ShapeElement } from '@/types/core/elementDefinitions'
import { describe, expect, it } from 'vitest'

describe('boundingBox Interface', () => {
  it('should define a bounding box with required properties', () => {
    // Create mock points for the bounding box
    const topLeft = { x: 0, y: 0 } as Point
    const topCenter = { x: 50, y: 0 } as Point
    const topRight = { x: 100, y: 0 } as Point
    const middleLeft = { x: 0, y: 50 } as Point
    const center = { x: 50, y: 50 } as Point
    const middleRight = { x: 100, y: 50 } as Point
    const bottomLeft = { x: 0, y: 100 } as Point
    const bottomCenter = { x: 50, y: 100 } as Point
    const bottomRight = { x: 100, y: 100 } as Point

    // Create a mock bounding box
    const bbox: BoundingBox = {
      id: 'bbox-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        topLeft,
        topCenter,
        topRight,
        middleLeft,
        center,
        middleRight,
        bottomLeft,
        bottomCenter,
        bottomRight,
      ] as [Point, Point, Point, Point, Point, Point, Point, Point, Point],
      width: 100,
      height: 100,
    }

    // Verify the bounding box is valid
    expect(bbox.id).toBe('bbox-1')
    expect(bbox.type).toBe('shape')
    expect(bbox.points.length).toBe(9)
    expect(bbox.width).toBe(100)
    expect(bbox.height).toBe(100)

    // Verify the points are in the correct order
    expect(bbox.points[0]).toBe(topLeft)
    expect(bbox.points[1]).toBe(topCenter)
    expect(bbox.points[2]).toBe(topRight)
    expect(bbox.points[3]).toBe(middleLeft)
    expect(bbox.points[4]).toBe(center)
    expect(bbox.points[5]).toBe(middleRight)
    expect(bbox.points[6]).toBe(bottomLeft)
    expect(bbox.points[7]).toBe(bottomCenter)
    expect(bbox.points[8]).toBe(bottomRight)
  })

  it('should extend ShapeElement interface', () => {
    // Create a mock bounding box
    const bbox: BoundingBox = {
      id: 'bbox-2',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 75 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      points: Array.from({ length: 9 }).fill({ x: 0, y: 0 }) as [Point, Point, Point, Point, Point, Point, Point, Point, Point],
      width: 200,
      height: 150,
    }

    // Verify the bounding box extends ShapeElement
    const shapeElement: ShapeElement = bbox
    expect(shapeElement.id).toBe('bbox-2')
    expect(shapeElement.type).toBe('shape')
    expect(shapeElement.visible).toBe(true)
    expect(shapeElement.locked).toBe(false)
    expect(shapeElement.position.x).toBe(100)
    expect(shapeElement.position.y).toBe(75)
  })

  it('should have readonly properties', () => {
    // Create a mock bounding box
    const bbox: BoundingBox = {
      id: 'bbox-3',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 150, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      points: Array.from({ length: 9 }).fill({ x: 0, y: 0 }) as [Point, Point, Point, Point, Point, Point, Point, Point, Point],
      width: 300,
      height: 200,
    }

    // In TypeScript, readonly is a compile-time feature, not a runtime feature
    // So we can only verify that the TypeScript compiler would prevent assignment
    // We can't actually test this at runtime

    // This is just to verify the test passes
    expect(bbox.points.length).toBe(9)
    expect(bbox.width).toBe(300)
    expect(bbox.height).toBe(200)
  })

  it('should be usable in functions that require BoundingBox', () => {
    // Define a function that uses BoundingBox
    function getBoundingBoxArea(bbox: BoundingBox): number {
      return bbox.width * bbox.height
    }

    // Create a mock bounding box
    const bbox: BoundingBox = {
      id: 'bbox-4',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 25, y: 15 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      points: Array.from({ length: 9 }).fill({ x: 0, y: 0 }) as [Point, Point, Point, Point, Point, Point, Point, Point, Point],
      width: 50,
      height: 30,
    }

    // Test the function
    const area = getBoundingBoxArea(bbox)
    expect(area).toBe(1500)
  })

  it('should be usable in arrays and collections', () => {
    // Create an array of bounding boxes
    const bboxes: BoundingBox[] = [
      {
        id: 'bbox-5',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 50, y: 50 },
        rotation: 0,

        selectable: true,
        draggable: true,
        showHandles: true,
        points: Array.from({ length: 9 }).fill({ x: 0, y: 0 }) as [Point, Point, Point, Point, Point, Point, Point, Point, Point],
        width: 100,
        height: 100,
      },
      {
        id: 'bbox-6',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 100, y: 75 },
        rotation: 0,

        selectable: true,
        draggable: true,
        showHandles: true,
        points: Array.from({ length: 9 }).fill({ x: 0, y: 0 }) as [Point, Point, Point, Point, Point, Point, Point, Point, Point],
        width: 200,
        height: 150,
      },
    ]

    // Verify the array is valid
    expect(bboxes.length).toBe(2)
    expect(bboxes[0].id).toBe('bbox-5')
    expect(bboxes[0].width).toBe(100)
    expect(bboxes[0].height).toBe(100)
    expect(bboxes[1].id).toBe('bbox-6')
    expect(bboxes[1].width).toBe(200)
    expect(bboxes[1].height).toBe(150)
  })
})
