/**
 * Scroll Area Components
 *
 * A custom scrollable container built on top of Radix UI's ScrollArea primitive.
 * Provides a styled scrollbar with smooth scrolling behavior and touch support.
 *
 * Features:
 * - Custom styled scrollbars
 * - Touch-friendly interaction
 * - Support for both vertical and horizontal scrolling
 * - Automatic scrollbar visibility
 */

import * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area'
import * as React from 'react'

import { cn } from '@/lib/utils/index'

/**
 * ScrollArea component that wraps content in a scrollable container
 *
 * @example
 * ```tsx
 * <ScrollArea className="h-[200px]">
 *   <div>Long content that needs scrolling...</div>
 * </ScrollArea>
 * ```
 */
function ScrollArea({ ref, className, children, ...props }: React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root> & { ref?: React.RefObject<React.ComponentRef<typeof ScrollAreaPrimitive.Root> | null> }) {
  return (
    <ScrollAreaPrimitive.Root
      ref={ref}
      className={cn('relative overflow-hidden', className)}
      {...props}
    >
      <ScrollAreaPrimitive.Viewport className="h-full w-full rounded-[inherit]">
        {children}
      </ScrollAreaPrimitive.Viewport>
      <ScrollBar />
      <ScrollAreaPrimitive.Corner />
    </ScrollAreaPrimitive.Root>
  )
}
ScrollArea.displayName = ScrollAreaPrimitive.Root.displayName

/**
 * ScrollBar component that provides the scrollbar UI
 * Can be used in both vertical and horizontal orientations
 *
 * @example
 * ```tsx
 * <ScrollBar orientation="horizontal" />
 * ```
 */
function ScrollBar({ ref, className, orientation = 'vertical', ...props }: React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar> & { ref?: React.RefObject<React.ComponentRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar> | null> }) {
  return (
    <ScrollAreaPrimitive.ScrollAreaScrollbar
      ref={ref}
      orientation={orientation}
      className={cn(
        'flex touch-none select-none transition-colors',
        orientation === 'vertical'
        && 'h-full w-2.5 border-l border-l-transparent p-[1px]',
        orientation === 'horizontal'
        && 'h-2.5 flex-col border-t border-t-transparent p-[1px]',
        className,
      )}
      {...props}
    >
      <ScrollAreaPrimitive.ScrollAreaThumb className="relative flex-1 rounded-full bg-border" />
    </ScrollAreaPrimitive.ScrollAreaScrollbar>
  )
}
ScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName

export { ScrollArea, ScrollBar }
