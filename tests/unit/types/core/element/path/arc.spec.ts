import type { Arc } from '@/types/core/element/path/arcPathTypes'
import type { ShapeElement } from '@/types/core/elementDefinitions'

import { describe, expect, it } from 'vitest'

describe('arc Interface', () => {
  it('should define an arc with required properties', () => {
    // Create a valid Arc object
    const arc: Arc = {
      id: 'arc-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      radius: 50,
      startAngle: 0,
      endAngle: 90,
      // Removed duplicate position: { x: 100, y: 100 }
    }

    // Verify the object is valid
    expect(arc.id).toBe('arc-1')
    expect(arc.type).toBe('shape')
    expect(arc.radius).toBe(50)
    expect(arc.startAngle).toBe(0)
    expect(arc.endAngle).toBe(90)
    expect(arc.position.x).toBe(100)
    expect(arc.position.y).toBe(100)
  })

  it('should extend ShapeElement interface', () => {
    // Create an arc
    const arc: Arc = {
      id: 'arc-2',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 200, y: 200 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      radius: 75,
      startAngle: 45,
      endAngle: 135,
      // Removed duplicate position: { x: 200, y: 200 }
    }

    // Verify the arc extends ShapeElement
    const shapeElement: ShapeElement = arc
    expect(shapeElement.id).toBe('arc-2')
    expect(shapeElement.type).toBe('shape')
  })

  it('should allow creating arcs with different properties', () => {
    // Create arcs with different properties
    const smallArc: Arc = {
      id: 'small-arc',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      radius: 25,
      startAngle: 0,
      endAngle: 45,
      // Removed duplicate position: { x: 50, y: 50 }
    }

    const largeArc: Arc = {
      id: 'large-arc',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 150, y: 150 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      radius: 100,
      startAngle: 0,
      endAngle: 270,
      // Removed duplicate position: { x: 150, y: 150 }
    }

    const fullCircle: Arc = {
      id: 'full-circle',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      radius: 50,
      startAngle: 0,
      endAngle: 360,
      // Removed duplicate position: { x: 100, y: 100 }
    }

    // Verify all arcs are valid
    expect(smallArc.radius).toBe(25)
    expect(smallArc.startAngle).toBe(0)
    expect(smallArc.endAngle).toBe(45)

    expect(largeArc.radius).toBe(100)
    expect(largeArc.startAngle).toBe(0)
    expect(largeArc.endAngle).toBe(270)

    expect(fullCircle.radius).toBe(50)
    expect(fullCircle.startAngle).toBe(0)
    expect(fullCircle.endAngle).toBe(360)
  })

  it('should be usable in arrays and collections', () => {
    // Create an array of arcs
    const arcs: Arc[] = [
      {
        id: 'arc-3',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 100, y: 100 },
        rotation: 0,

        selectable: true,
        draggable: true,
        showHandles: true,
        radius: 50,
        startAngle: 0,
        endAngle: 90,
        // Removed duplicate position: { x: 100, y: 100 }
      },
      {
        id: 'arc-4',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 200, y: 200 },
        rotation: 0,

        selectable: true,
        draggable: true,
        showHandles: true,
        radius: 75,
        startAngle: 90,
        endAngle: 180,
        // Removed duplicate position: { x: 200, y: 200 }
      },
    ]

    // Verify the array is valid
    expect(arcs.length).toBe(2)
    expect(arcs[0].id).toBe('arc-3')
    expect(arcs[0].radius).toBe(50)
    expect(arcs[0].startAngle).toBe(0)
    expect(arcs[0].endAngle).toBe(90)

    expect(arcs[1].id).toBe('arc-4')
    expect(arcs[1].radius).toBe(75)
    expect(arcs[1].startAngle).toBe(90)
    expect(arcs[1].endAngle).toBe(180)
  })

  it('should be usable in functions that require Arc', () => {
    // Define a function that uses Arc
    function calculateArcLength(arc: Arc): number {
      const angleSpan = Math.abs(arc.endAngle - arc.startAngle)
      const angleRadians = (angleSpan * Math.PI) / 180
      return arc.radius * angleRadians
    }

    // Create an arc
    const arc: Arc = {
      id: 'arc-5',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 150, y: 150 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      radius: 100,
      startAngle: 0,
      endAngle: 90,
      // Removed duplicate position: { x: 150, y: 150 }
    }

    // Test the function
    const length = calculateArcLength(arc)
    expect(length).toBeCloseTo((Math.PI / 2) * 100) // 90 degrees = π/2 radians
  })

  it('should be compatible with JSON serialization/deserialization', () => {
    // Create an arc
    const arc: Arc = {
      id: 'arc-6',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,

      selectable: true,
      draggable: true,
      showHandles: true,
      radius: 50,
      startAngle: 0,
      endAngle: 180,
      // Removed duplicate position: { x: 100, y: 100 }
    }

    // Serialize to JSON and deserialize
    const serialized = JSON.stringify(arc)
    const deserialized = JSON.parse(serialized) as Arc

    // Verify the deserialized object is valid
    expect(deserialized.id).toBe('arc-6')
    expect(deserialized.type).toBe('shape')
    expect(deserialized.radius).toBe(50)
    expect(deserialized.startAngle).toBe(0)
    expect(deserialized.endAngle).toBe(180)
    expect(deserialized.position.x).toBe(100)
    expect(deserialized.position.y).toBe(100)
  })
})
