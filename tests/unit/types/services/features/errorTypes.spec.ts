import { describe, expect, it } from 'vitest'
import * as ErrorsModule from '@/types/services/errors'
import * as ErrorTypesModule from '@/types/services/features/errorTypes'

describe('error Types Feature Module', () => {
  it('should be defined as a module', () => {
    // Verify the module exists
    expect(ErrorTypesModule).toBeDefined()
  })

  it('should re-export types from errors module', () => {
    // Check that the module has the same exports as the errors module
    const errorTypesExports = Object.keys(ErrorTypesModule)
    const errorsExports = Object.keys(ErrorsModule)

    // Verify that all exports from errors module are re-exported
    for (const exportName of errorsExports) {
      expect(errorTypesExports).toContain(exportName)
    }
  })
})
