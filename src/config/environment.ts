/**
 * Environment Configuration Management
 * Based on Context7 best practices for configuration management
 */

export interface EnvironmentConfig {
  // Application
  appName: string
  appVersion: string
  environment: 'development' | 'production' | 'test'

  // API
  apiBaseUrl: string
  apiTimeout: number

  // Features
  enableDebugMode: boolean
  enablePerformanceMonitoring: boolean
  enableErrorReporting: boolean

  // Logging
  logLevel: 'debug' | 'info' | 'warn' | 'error'
  logToConsole: boolean
  logToFile: boolean

  // Canvas
  canvasMaxWidth: number
  canvasMaxHeight: number
  canvasDefaultZoom: number

  // Security
  enableCSP: boolean
  enableHTTPS: boolean
}

/**
 * Get environment variable with type safety and default values
 * Works in both Vite (import.meta.env) and Node.js (process.env) environments
 */
function getEnvVar(key: string, defaultValue: string): string {
  // Check if we're in a Vite environment
  if (import.meta?.env) {
    return import.meta.env[key] ?? defaultValue
  }
  // Fallback to Node.js process.env
  return process.env[key] ?? defaultValue
}

function getEnvBoolean(key: string, defaultValue: boolean): boolean {
  let value: string | undefined

  // Check if we're in a Vite environment
  if (import.meta?.env) {
    value = import.meta.env[key]
  }
  else {
    // Fallback to Node.js process.env
    value = process.env[key]
  }

  if (value === undefined)
    return defaultValue
  return value === 'true' || value === '1'
}

function getEnvNumber(key: string, defaultValue: number): number {
  let value: string | undefined

  // Check if we're in a Vite environment
  if (import.meta?.env) {
    value = import.meta.env[key]
  }
  else {
    // Fallback to Node.js process.env
    value = process.env[key]
  }

  if (value === undefined)
    return defaultValue
  const parsed = Number.parseInt(value, 10)
  return Number.isNaN(parsed) ? defaultValue : parsed
}

/**
 * Environment configuration instance
 * Follows Context7 pattern for centralized configuration
 */
export const environment: EnvironmentConfig = {
  // Application
  appName: getEnvVar('VITE_APP_NAME', 'RenoPilot JS Shapes'),
  appVersion: getEnvVar('VITE_APP_VERSION', '1.0.0'),
  environment: getEnvVar('NODE_ENV', 'development') as EnvironmentConfig['environment'],

  // API
  apiBaseUrl: getEnvVar('VITE_API_BASE_URL', 'http://localhost:3000/api'),
  apiTimeout: getEnvNumber('VITE_API_TIMEOUT', 10000),

  // Features
  enableDebugMode: getEnvBoolean('VITE_ENABLE_DEBUG_MODE', true),
  enablePerformanceMonitoring: getEnvBoolean('VITE_ENABLE_PERFORMANCE_MONITORING', false),
  enableErrorReporting: getEnvBoolean('VITE_ENABLE_ERROR_REPORTING', false),

  // Logging
  logLevel: getEnvVar('VITE_LOG_LEVEL', 'debug') as EnvironmentConfig['logLevel'],
  logToConsole: getEnvBoolean('VITE_LOG_TO_CONSOLE', true),
  logToFile: getEnvBoolean('VITE_LOG_TO_FILE', false),

  // Canvas
  canvasMaxWidth: getEnvNumber('VITE_CANVAS_MAX_WIDTH', 2000),
  canvasMaxHeight: getEnvNumber('VITE_CANVAS_MAX_HEIGHT', 2000),
  canvasDefaultZoom: getEnvNumber('VITE_CANVAS_DEFAULT_ZOOM', 1),

  // Security
  enableCSP: getEnvBoolean('VITE_ENABLE_CSP', true),
  enableHTTPS: getEnvBoolean('VITE_ENABLE_HTTPS', false),
}

/**
 * Validate environment configuration
 */
export function validateEnvironment(): { valid: boolean, errors: string[] } {
  const errors: string[] = []

  // Validate required fields
  if (!environment.appName) {
    errors.push('App name is required')
  }

  if (!environment.appVersion) {
    errors.push('App version is required')
  }

  // Validate numeric ranges
  if (environment.canvasMaxWidth <= 0) {
    errors.push('Canvas max width must be positive')
  }

  if (environment.canvasMaxHeight <= 0) {
    errors.push('Canvas max height must be positive')
  }

  if (environment.apiTimeout <= 0) {
    errors.push('API timeout must be positive')
  }

  return {
    valid: errors.length === 0,
    errors,
  }
}

/**
 * Get configuration for specific environment
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  return environment
}

/**
 * Check if running in development mode
 */
export function isDevelopment(): boolean {
  return environment.environment === 'development'
}

/**
 * Check if running in production mode
 */
export function isProduction(): boolean {
  return environment.environment === 'production'
}

/**
 * Check if running in test mode
 */
export function isTest(): boolean {
  return environment.environment === 'test'
}
