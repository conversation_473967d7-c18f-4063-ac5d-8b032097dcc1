import type {
  AreaCalculatorStrategy,
  BoundingBox,
  ComputeOptions,
  ComputeResult,
  CostCalculationOptions,
  CostCalculatorStrategy,
  DistanceCalculatorStrategy,
  MaterialCalculationOptions,
  MaterialCalculationResult,
  MaterialCalculatorStrategy,
  PerimeterCalculatorStrategy,
  SpacePlanningStrategy,
} from '../../types/core/compute'
import type { Element, Path, ShapeElement } from '../../types/core/elementDefinitions'
import type { ElementFactory } from '../factory/ElementFactory'
import type { ShapeRepository } from '../state/ShapeRepository'
import type { StrategyRegistry } from './StrategyRegistry' // Removed StrategyTypeName import
import { CoreError } from '../../services/system/error-service/coreError'
import {
  ComputeOperation,
  ComputeOperationType,
} from '../../types/core/compute/index'
import { ElementType } from '../../types/core/elementDefinitions'
import { ErrorType } from '../../types/services/errors'

// Define the expected strategy type names here, matching StrategyRegistry's internal type
type ValidStrategyKey = 'area' | 'perimeter' | 'boundingBox' | 'distance' | 'cost' | 'material' | 'spacePlanning'
const VALID_STRATEGY_KEYS: ValidStrategyKey[] = ['area', 'perimeter', 'boundingBox', 'distance', 'cost', 'material', 'spacePlanning']

/**
 * Computation Facade
 *
 * Provides a unified interface (Facade design pattern) for performing various
 * computation operations on shape elements. This class delegates the actual
 * computation logic to specific strategy objects managed by the {@link StrategyRegistry}.
 *
 * @remarks
 * The `ComputeFacade` simplifies the process of:
 * - Calculating geometric properties (area, perimeter, bounding box).
 * - Determining relationships between shapes (distance).
 * - Estimating costs and material requirements.
 * - Performing space planning analyses.
 *
 * It interacts with the {@link ShapeRepository} to retrieve shape data and the
 * {@link ElementFactory} to potentially instantiate element objects if needed by
 * computation strategies. It also handles errors and ensures consistent result
 * formatting via the {@link ComputeResult} type.
 *
 * Custom computation operations can also be registered and invoked through this facade.
 *
 * @see {@link StrategyRegistry}
 * @see {@link ComputeResult}
 * @see {@link ComputeOperationType}
 * @module core/compute/ComputeFacade
 */
export class ComputeFacade {
  #customOperations: Map<string, (shapeIds: string[], options?: ComputeOptions) => Promise<unknown>> = new Map()
  private readonly strategyRegistry: StrategyRegistry
  private readonly shapeRepository: ShapeRepository
  private readonly elementFactory: ElementFactory

  constructor(
    strategyRegistry: StrategyRegistry,
    shapeRepository: ShapeRepository,
    elementFactory: ElementFactory,
  ) {
    this.strategyRegistry = strategyRegistry
    this.shapeRepository = shapeRepository
    this.elementFactory = elementFactory
  }

  /**
   * 将 ShapeElement 转换为 PathCreationOptionsUnion 格式
   */
  private convertModelToPathParams(model: ShapeElement, elementType: ElementType): any {
    const baseParams = {
      id: model.id,
      type: elementType,
      majorCategory: model.majorCategory,
      minorCategory: model.minorCategory,
      zLevelId: model.zLevelId,
      isFixedCategory: model.isFixedCategory,
      position: model.position, // 添加 position 字段
      metadata: model.metadata,
      visible: model.visible,
      locked: model.locked,
      rotation: model.rotation,
      selectable: model.selectable,
      draggable: model.draggable,
      showHandles: model.showHandles,
      zIndex: model.zIndex,
      properties: model.properties,
      fill: model.fill,
      stroke: model.stroke,
      strokeWidth: model.strokeWidth,
      opacity: model.opacity,
      strokeDasharray: model.strokeDasharray,
    }

    if (elementType === ElementType.LINE) {
      // 线段元素：从 model 或 model.properties 中获取 start 和 end
      const start = (model as any).start || (model as any).properties?.start
      const end = (model as any).end || (model as any).properties?.end

      return {
        ...baseParams,
        start: start || { x: 0, y: 0, z: 0 },
        end: end || { x: 100, y: 100, z: 0 },
        arrowStart: (model as any).arrowStart || (model as any).properties?.arrowStart,
        arrowEnd: (model as any).arrowEnd || (model as any).properties?.arrowEnd,
      }
    }
    else if (elementType === ElementType.POLYLINE) {
      // 折线元素：从 model 或 model.properties 中获取 points
      const points = (model as any).points || (model as any).properties?.points

      return {
        ...baseParams,
        points: points || [{ x: 0, y: 0, z: 0 }, { x: 50, y: 50, z: 0 }, { x: 100, y: 0, z: 0 }],
        curved: (model as any).curved || (model as any).properties?.curved,
        tension: (model as any).tension || (model as any).properties?.tension,
      }
    }
    else if (elementType === ElementType.ARC) {
      // 圆弧元素：从 model 或 model.properties 中获取圆弧参数
      const radius = (model as any).radius || (model as any).properties?.radius
      const startAngle = (model as any).startAngle || (model as any).properties?.startAngle
      const endAngle = (model as any).endAngle || (model as any).properties?.endAngle
      const closed = (model as any).closed || (model as any).properties?.closed

      return {
        ...baseParams,
        radius: radius || 50,
        startAngle: startAngle || 0,
        endAngle: endAngle || 90,
        closed: closed || false,
        counterClockwise: (model as any).counterClockwise || (model as any).properties?.counterClockwise || false,
      }
    }
    else if (elementType === ElementType.QUADRATIC) {
      // 二次曲线元素：从 model 或 model.properties 中获取控制点
      const start = (model as any).start || (model as any).properties?.start
      const control = (model as any).control || (model as any).properties?.control
      const end = (model as any).end || (model as any).properties?.end
      const closed = (model as any).closed || (model as any).properties?.closed

      return {
        ...baseParams,
        start: start || { x: 0, y: 0, z: 0 },
        control: control || { x: 50, y: -50, z: 0 },
        end: end || { x: 100, y: 0, z: 0 },
        closed: closed || false,
      }
    }
    else if (elementType === ElementType.CUBIC) {
      // 三次曲线元素：从 model 或 model.properties 中获取控制点
      const start = (model as any).start || (model as any).properties?.start
      const control1 = (model as any).control1 || (model as any).properties?.control1
      const control2 = (model as any).control2 || (model as any).properties?.control2
      const end = (model as any).end || (model as any).properties?.end
      const closed = (model as any).closed || (model as any).properties?.closed

      return {
        ...baseParams,
        start: start || { x: 0, y: 0, z: 0 },
        control1: control1 || { x: 30, y: -50, z: 0 },
        control2: control2 || { x: 70, y: 50, z: 0 },
        end: end || { x: 100, y: 0, z: 0 },
        closed: closed || false,
      }
    }

    throw new Error(`Unsupported path element type: ${elementType}`)
  }

  private async getElementInstance(shapeId: string): Promise<Element | undefined> {
    const model = this.shapeRepository.getById(shapeId)
    if (!model) {
      console.error(`[ComputeFacade][getElementInstance] 在仓库中找不到形状 ${shapeId}`)
      return undefined
    }

    try {
      // 根据元素类型选择正确的创建方法
      let element: Element
      const elementType = model.type as ElementType
      if (elementType === ElementType.LINE || elementType === ElementType.POLYLINE || elementType === ElementType.ARC || elementType === ElementType.QUADRATIC || elementType === ElementType.CUBIC) {
        // 将 ShapeModel 转换为 PathCreationOptionsUnion 格式
        const pathParams = this.convertModelToPathParams(model, elementType)
        element = await this.elementFactory.createPath(elementType, pathParams)
      }
      else {
        element = await this.elementFactory.createShape(elementType, model) as Element
      }

      if (element == null) {
        console.error(`[ComputeFacade][getElementInstance] ElementFactory返回了undefined`)
        throw new Error('ElementFactory returned undefined for a valid model.')
      }

      return element
    }
    catch (error: unknown) {
      console.error(`[ComputeFacade][getElementInstance] 创建元素实例时出错:`, {
        elementId: shapeId,
        elementType: model.type,
        error,
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        modelData: {
          'id': model.id,
          'type': model.type,
          'start': (model as any).start,
          'end': (model as any).end,
          'properties.start': (model as any).properties?.start,
          'properties.end': (model as any).properties?.end,
        },
      })
      throw new CoreError(
        ErrorType.FactoryFailed,
        `Failed to create element instance for computation (ID: ${shapeId}). Reason: ${(error as Error)?.message || String(error)}`,
        undefined,
        { component: 'ComputeFacade', operation: 'getElementInstance', target: shapeId, metadata: { originalError: error as Error, elementType: model.type } },
      )
    }
  }

  public async computeArea(shapeId: string): Promise<number> {
    let element: Element | undefined
    try {
      // console.warn(`[ComputeFacade][computeArea] 开始计算形状 ${shapeId} 的面积...`)
      element = await this.getElementInstance(shapeId)
      if (element == null) {
        console.error(`[ComputeFacade][computeArea] 找不到形状 ${shapeId}`)
        throw new CoreError(ErrorType.CoordinatorShapeNotFound, `Shape with ID ${shapeId} not found for area computation.`, undefined, { component: 'ComputeFacade', operation: 'computeArea', target: shapeId })
      }
      // console.warn(`[ComputeFacade][computeArea] 形状类型: ${element.type}, 属性:`, JSON.stringify(element, null, 2))

      // 获取策略
      // console.warn(`[ComputeFacade][computeArea] 获取面积计算策略...`)
      const strategy: AreaCalculatorStrategy = this.strategyRegistry.getAreaStrategy(element.type as ElementType)
      // console.warn(`[ComputeFacade][computeArea] 获取到面积计算策略: ${strategy != null ? '成功' : '失败'}`)

      // 计算面积
      // console.warn(`[ComputeFacade][computeArea] 计算面积...`)
      const area = strategy.calculateArea(element)
      // console.warn(`[ComputeFacade][computeArea] 面积计算结果: ${area}`)
      return area
    }
    catch (error: unknown) {
      if (error instanceof CoreError)
        throw error
      throw new CoreError(ErrorType.CoordinatorOperationFailed, `Failed to compute area for shape ${shapeId}. Reason: ${(error as Error)?.message || String(error)}`, undefined, { component: 'ComputeFacade', operation: 'computeArea', target: shapeId, metadata: { originalError: error as Error, elementType: element?.type } })
    }
  }

  public async computePerimeter(shapeId: string): Promise<number> {
    let element: Element | undefined
    try {
      element = await this.getElementInstance(shapeId)
      if (element == null) {
        console.error(`[ComputeFacade][computePerimeter] 找不到形状 ${shapeId}`)
        throw new CoreError(ErrorType.CoordinatorShapeNotFound, `Shape with ID ${shapeId} not found for perimeter computation.`, undefined, { component: 'ComputeFacade', operation: 'computePerimeter', target: shapeId })
      }

      // 获取策略
      const strategy: PerimeterCalculatorStrategy = this.strategyRegistry.getPerimeterStrategy(element.type as ElementType)

      // 计算周长
      const perimeter = strategy.calculatePerimeter(element)
      return perimeter
    }
    catch (error: unknown) {
      if (error instanceof CoreError)
        throw error
      throw new CoreError(ErrorType.CoordinatorOperationFailed, `Failed to compute perimeter for shape ${shapeId}. Reason: ${(error as Error)?.message || String(error)}`, undefined, { component: 'ComputeFacade', operation: 'computePerimeter', target: shapeId, metadata: { originalError: error as Error, elementType: element?.type } })
    }
  }

  public async computeBoundingBox(shapeId: string): Promise<BoundingBox> {
    let element: Element | undefined
    try {
      element = await this.getElementInstance(shapeId)
      if (element == null) {
        throw new CoreError(ErrorType.CoordinatorShapeNotFound, `Shape with ID ${shapeId} not found for bounding box computation.`, undefined, { component: 'ComputeFacade', operation: 'computeBoundingBox', target: shapeId })
      }
      const strategy = this.strategyRegistry.getBoundingBoxStrategy(element.type as ElementType)
      return strategy.calculateBoundingBox(element as ShapeElement)
    }
    catch (error: unknown) {
      if (error instanceof CoreError)
        throw error
      throw new CoreError(ErrorType.ComputeBoundsError, `Failed to compute bounding box for shape ${shapeId}. Reason: ${(error as Error)?.message || String(error)}`, undefined, { component: 'ComputeFacade', operation: 'computeBoundingBox', target: shapeId, metadata: { originalError: error as Error, elementType: element?.type } })
    }
  }

  public async computeDistance(shapeIdA: string, shapeIdB: string): Promise<number> {
    let elementA: Element | undefined
    let elementB: Element | undefined
    try {
      elementA = await this.getElementInstance(shapeIdA)
      elementB = await this.getElementInstance(shapeIdB)
      if (elementA == null) {
        throw new CoreError(ErrorType.CoordinatorShapeNotFound, `Shape A with ID ${shapeIdA} not found for distance computation.`, undefined, { component: 'ComputeFacade', operation: 'computeDistance', target: shapeIdA })
      }
      if (elementB == null) {
        throw new CoreError(ErrorType.CoordinatorShapeNotFound, `Shape B with ID ${shapeIdB} not found for distance computation.`, undefined, { component: 'ComputeFacade', operation: 'computeDistance', target: shapeIdB })
      }
      const strategy: DistanceCalculatorStrategy = this.strategyRegistry.getDistanceStrategy(elementA.type as ElementType)
      return strategy.calculateDistance(elementA as ShapeElement, elementB as ShapeElement)
    }
    catch (error: unknown) {
      if (error instanceof CoreError)
        throw error
      throw new CoreError(ErrorType.CoordinatorOperationFailed, `Failed to compute distance between shapes ${shapeIdA} and ${shapeIdB}. Reason: ${(error as Error)?.message || String(error)}`, undefined, { component: 'ComputeFacade', operation: 'computeDistance', metadata: { originalError: error as Error, shapeIdA, shapeIdB, elementTypeA: elementA?.type, elementTypeB: elementB?.type } })
    }
  }

  public async computeCost(shapeId: string, unitCost: number, options?: CostCalculationOptions): Promise<number> {
    let element: Element | undefined
    try {
      // 确保unitCost是有效的数字且大于0
      if (typeof unitCost !== 'number' || Number.isNaN(unitCost) || unitCost <= 0) {
        unitCost = 1
      }

      element = await this.getElementInstance(shapeId)
      if (element == null) {
        console.error(`[ComputeFacade][computeCost] 找不到形状 ${shapeId}`)
        throw new CoreError(ErrorType.CoordinatorShapeNotFound, `Shape with ID ${shapeId} not found for cost computation.`, undefined, { component: 'ComputeFacade', operation: 'computeCost', target: shapeId })
      }

      // 获取策略
      const strategy: CostCalculatorStrategy = this.strategyRegistry.getCostStrategy(element.type as ElementType)

      // 计算成本
      const cost = strategy.calculateCost(element, unitCost, options)
      return cost
    }
    catch (error: unknown) {
      if (error instanceof CoreError)
        throw error
      throw new CoreError(ErrorType.CoordinatorOperationFailed, `Failed to compute cost for shape ${shapeId}. Reason: ${(error as Error)?.message || String(error)}`, undefined, { component: 'ComputeFacade', operation: 'computeCost', target: shapeId, metadata: { originalError: error as Error, elementType: element?.type } })
    }
  }

  public async computeMaterial(shapeId: string, materialType: string, options?: MaterialCalculationOptions): Promise<MaterialCalculationResult> {
    let element: Element | undefined
    try {
      element = await this.getElementInstance(shapeId)
      if (element == null) {
        throw new CoreError(ErrorType.CoordinatorShapeNotFound, `Shape with ID ${shapeId} not found for material computation.`, undefined, { component: 'ComputeFacade', operation: 'computeMaterial', target: shapeId })
      }
      const strategy: MaterialCalculatorStrategy = this.strategyRegistry.getMaterialStrategy(element.type as ElementType)
      return strategy.calculateMaterialAmount(element, materialType, options)
    }
    catch (error: unknown) {
      if (error instanceof CoreError)
        throw error
      throw new CoreError(ErrorType.CoordinatorOperationFailed, `Failed to compute material for shape ${shapeId}. Reason: ${(error as Error)?.message || String(error)}`, undefined, { component: 'ComputeFacade', operation: 'computeMaterial', target: shapeId, metadata: { originalError: error as Error, elementType: element?.type } })
    }
  }

  public async computeSpacePlanning(shapeIds: string[], spaceType: string, operation: string, options?: ComputeOptions): Promise<unknown> {
    const elements: Element[] = []
    try {
      for (const shapeId of shapeIds) {
        const element = await this.getElementInstance(shapeId)
        if (element == null) {
          throw new CoreError(ErrorType.CoordinatorShapeNotFound, `Shape with ID ${shapeId} not found for space planning.`, undefined, { component: 'ComputeFacade', operation: 'computeSpacePlanning', target: shapeId, metadata: { spaceType } })
        }
        elements.push(element)
      }
      const roomBoundary = elements[0]
      if (roomBoundary == null) {
        throw new CoreError(ErrorType.InvalidParameter, 'Room boundary (first element) is required for space planning.', undefined, { component: 'ComputeFacade', operation: 'computeSpacePlanning', metadata: { spaceType } })
      }
      const strategy: SpacePlanningStrategy = this.strategyRegistry.getSpacePlanningStrategy(spaceType)
      const operationUpper = operation.toUpperCase()

      if (operationUpper === ComputeOperation.SPACE_UTILIZATION || operation === 'utilization') {
        return strategy.calculateSpaceUtilization(elements.slice(1), roomBoundary)
      }
      else if (operationUpper === ComputeOperation.PATHWAY_CHECK || operation === 'pathwayCheck') {
        const typedOptions = options as { pathways?: unknown[], minWidth?: number }
        if (typedOptions?.pathways == null || typedOptions?.minWidth == null) {
          throw new CoreError(ErrorType.InvalidPayload, 'Pathways and minimum width are required for pathway check.', undefined, { component: 'ComputeFacade', operation: 'computeSpacePlanning', metadata: { spaceType, subOperation: operation } })
        }
        return strategy.checkPathwayWidth(elements.slice(1), typedOptions.pathways as Path.Line[], typedOptions.minWidth)
      }
      else if (operationUpper === ComputeOperation.LAYOUT_SCORE || operation === 'layoutScore') {
        // Type-safe check for calculateLayoutScore method
        const extendedStrategy = strategy as SpacePlanningStrategy & { calculateLayoutScore?: (elements: Element[], roomBoundary: Element, options?: unknown) => unknown }
        if (typeof extendedStrategy.calculateLayoutScore === 'function') {
          return extendedStrategy.calculateLayoutScore(elements.slice(1), roomBoundary, options)
        }
        throw new CoreError(ErrorType.InvalidParameter, `Layout score calculation not implemented for space type: ${spaceType} via this strategy.`, undefined, { component: 'ComputeFacade', operation: 'computeSpacePlanning', metadata: { spaceType, subOperation: operation } })
      }
      else {
        throw new CoreError(ErrorType.InvalidPayload, `Unknown space planning operation: ${operation}`, undefined, { component: 'ComputeFacade', operation: 'computeSpacePlanning', metadata: { spaceType, subOperation: operation } })
      }
    }
    catch (error: unknown) {
      if (error instanceof CoreError)
        throw error
      throw new CoreError(ErrorType.CoordinatorOperationFailed, `Failed to compute space planning for ${spaceType}. Reason: ${(error as Error)?.message || String(error)}`, undefined, { component: 'ComputeFacade', operation: 'computeSpacePlanning', metadata: { originalError: error as Error, shapeIds, spaceType, operationName: operation, options } })
    }
  }

  public registerOperation(
    operation: string,
    computer: (shapeIds: string[], options?: ComputeOptions) => Promise<unknown>,
  ): void {
    const opLower = operation.toLowerCase()
    const opUpper = operation.toUpperCase()
    if (this.#customOperations.has(operation)
      || Object.values(ComputeOperationType).includes(opLower as ComputeOperationType)
      || Object.values(ComputeOperation).includes(opUpper as ComputeOperation)
    ) {
      // console.warn(`[ComputeFacade] Overwriting existing standard or custom operation: ${operation}`);
    }
    this.#customOperations.set(operation, computer)
  }

  public isComputationSupported(operation: ComputeOperationType | ComputeOperation | string, elementType: ElementType | string): boolean {
    try {
      const typeStr = elementType
      const opStr = String(operation)
      const opStrLower = opStr.toLowerCase()
      const opStrUpper = opStr.toUpperCase()

      if (Object.values(ComputeOperationType).includes(opStrLower as ComputeOperationType)) {
        if (opStrLower === ComputeOperationType.SPACE) {
          return this.strategyRegistry.hasStrategy(typeStr, 'spacePlanning')
        }
        if (VALID_STRATEGY_KEYS.includes(opStrLower as ValidStrategyKey)) {
          return this.strategyRegistry.hasStrategy(typeStr, opStrLower as ValidStrategyKey)
        }
      }

      if (Object.values(ComputeOperation).includes(opStrUpper as ComputeOperation)) {
        switch (opStrUpper as ComputeOperation) {
          case ComputeOperation.SPACE_UTILIZATION:
          case ComputeOperation.PATHWAY_CHECK:
          case ComputeOperation.LAYOUT_SCORE:
            return this.strategyRegistry.hasStrategy(typeStr, 'spacePlanning')
          case ComputeOperation.AREA:
            return this.strategyRegistry.hasStrategy(typeStr, 'area')
          case ComputeOperation.PERIMETER:
            return this.strategyRegistry.hasStrategy(typeStr, 'perimeter')
          case ComputeOperation.BOUNDING_BOX:
            return this.strategyRegistry.hasStrategy(typeStr, 'boundingBox')
          case ComputeOperation.DISTANCE:
            return this.strategyRegistry.hasStrategy(typeStr, 'distance')
          case ComputeOperation.MATERIAL:
            return this.strategyRegistry.hasStrategy(typeStr, 'material')
          case ComputeOperation.COST:
            return this.strategyRegistry.hasStrategy(typeStr, 'cost')
          case ComputeOperation.SPACE:
            return this.strategyRegistry.hasStrategy(typeStr, 'spacePlanning')
          default: {
            const correspondingLowercaseOp = opStrLower as ComputeOperationType
            if (Object.values(ComputeOperationType).includes(correspondingLowercaseOp)) {
              if (VALID_STRATEGY_KEYS.includes(correspondingLowercaseOp as ValidStrategyKey)) {
                return this.strategyRegistry.hasStrategy(typeStr, correspondingLowercaseOp as ValidStrategyKey)
              }
            }
            break
          }
        }
      }

      if (this.#customOperations.has(opStr)) {
        return true
      }

      if (VALID_STRATEGY_KEYS.includes(opStrLower as ValidStrategyKey)) {
        return this.strategyRegistry.hasStrategy(typeStr, opStrLower as ValidStrategyKey)
      }
      return false
    }
    catch {
      return false
    }
  }

  public async compute<T = unknown>(
    operation: ComputeOperationType | ComputeOperation | string,
    shapeIds: string[],
    options?: ComputeOptions,
  ): Promise<ComputeResult<T>> {
    if (shapeIds == null || shapeIds.length === 0) {
      const error = new CoreError(
        ErrorType.InvalidPayload,
        'At least one shape ID is required for computation.',
        undefined,
        { component: 'ComputeFacade', operation: 'compute', metadata: { operationName: String(operation) } },
      )
      throw error
    }

    const operationStr = String(operation)
    const shapeId = shapeIds[0]
    const executionStart = Date.now()

    try {
      let resultData: T | undefined
      const opLower = operationStr.toLowerCase()
      let effectiveOpType: ComputeOperationType | string = opLower

      if (opLower === ComputeOperation.SPACE_UTILIZATION.toLowerCase()
        || opLower === ComputeOperation.PATHWAY_CHECK.toLowerCase()
        || opLower === ComputeOperation.LAYOUT_SCORE.toLowerCase()) {
        effectiveOpType = ComputeOperationType.SPACE
      }

      switch (effectiveOpType) {
        case ComputeOperationType.AREA:
          resultData = await this.computeArea(shapeId) as T
          break
        case ComputeOperationType.PERIMETER:
          resultData = await this.computePerimeter(shapeId) as T
          break
        case ComputeOperationType.BOUNDING_BOX:
          resultData = await this.computeBoundingBox(shapeId) as T
          break
        case ComputeOperationType.DISTANCE: {
          if (shapeIds.length < 2 && (options?.targetShapeId == null)) {
            throw new CoreError(ErrorType.InvalidPayload, 'Target shape ID is required in options or as a second shapeId for DISTANCE operation.', undefined, { component: 'ComputeFacade', operation: 'compute', metadata: { operationName: operationStr, shapeId } })
          }
          const targetId = options?.targetShapeId ?? shapeIds[1]
          if (targetId == null) {
            throw new CoreError(ErrorType.InvalidPayload, 'Valid targetShapeId not provided for DISTANCE.', undefined, { component: 'ComputeFacade', operation: 'compute', metadata: { operationName: operationStr, shapeId } })
          }
          resultData = await this.computeDistance(shapeId, targetId) as T
          break
        }
        case ComputeOperationType.COST:
          if (typeof options?.unitCost !== 'number') {
            throw new CoreError(ErrorType.InvalidPayload, 'Unit cost is required in options for COST operation.', undefined, { component: 'ComputeFacade', operation: 'compute', metadata: { operationName: operationStr, shapeId } })
          }
          resultData = await this.computeCost(shapeId, options.unitCost, options as CostCalculationOptions) as T
          break
        case ComputeOperationType.MATERIAL:
          if (typeof options?.materialType !== 'string') {
            throw new CoreError(ErrorType.InvalidPayload, 'Material type is required in options for MATERIAL operation.', undefined, { component: 'ComputeFacade', operation: 'compute', metadata: { operationName: operationStr, shapeId } })
          }
          resultData = await this.computeMaterial(shapeId, options.materialType, options.materialOptions as MaterialCalculationOptions) as T
          break
        case ComputeOperationType.SPACE:
          if (typeof options?.spaceType !== 'string') {
            throw new CoreError(ErrorType.InvalidPayload, 'Space type is required in options for space planning operations.', undefined, { component: 'ComputeFacade', operation: 'compute', metadata: { operationName: operationStr, shapeIds } })
          }
          resultData = await this.computeSpacePlanning(shapeIds, options.spaceType, operationStr, options) as T
          break
        default: {
          const customComputer = this.#customOperations.get(operationStr)
          if (customComputer) {
            resultData = await customComputer(shapeIds, options) as T
          }
          else {
            throw new CoreError(ErrorType.InvalidParameter, `Unsupported compute operation: ${operationStr}`, undefined, { component: 'ComputeFacade', operation: 'compute', metadata: { operationName: operationStr } })
          }
        }
      }

      if (resultData === undefined) {
        throw new CoreError(ErrorType.ComputationError, `Compute operation '${operationStr}' completed but returned undefined result.`, undefined, { component: 'ComputeFacade', operation: 'compute', metadata: { operationName: operationStr, shapeIds } })
      }

      return {
        operation: operationStr,
        result: resultData,
        metadata: {
          executionTime: Date.now() - executionStart,
          shapeIds,
          ...(options?.precision != null && { precision: options.precision }),
        },
      }
    }
    catch (error: unknown) {
      if (error instanceof CoreError) {
        throw error
      }
      throw new CoreError(
        ErrorType.CoordinatorOperationFailed,
        `Generic compute operation '${operationStr}' failed. Reason: ${(error as Error)?.message || String(error)}`,
        undefined,
        { component: 'ComputeFacade', operation: 'compute', metadata: { originalError: error as Error, operationName: operationStr, shapeIds, options } },
      )
    }
  }
}
