import type { Text } from '@/types/core/element/text/textElementTypes'
import { describe, expect, it } from 'vitest'
import { ElementType } from '@/types/core/elementDefinitions'

describe('text Element Types Module', () => {
  describe('text Interface', () => {
    it('should define valid text element with required properties', () => {
      // Create a text element with required properties
      const textElement: Text = {
        // ShapeElement properties
        id: 'text-001',
        type: ElementType.TEXT,
        visible: true,
        locked: false,

        // Text-specific properties
        content: 'Hello, World!',
        position: { x: 50, y: 150 },
        fontSize: 24,
        fontFamily: 'Helvetica, Arial, sans-serif',
      }

      // Verify the text element has the expected properties
      expect(textElement.id).toBe('text-001')
      expect(textElement.type).toBe(ElementType.TEXT)
      expect(textElement.visible).toBe(true)
      expect(textElement.locked).toBe(false)

      expect(textElement.content).toBe('Hello, World!')
      expect(textElement.position).toEqual({ x: 50, y: 150 })
      expect(textElement.fontSize).toBe(24)
      expect(textElement.fontFamily).toBe('Helvetica, Arial, sans-serif')
    })

    it('should allow text element with empty content', () => {
      // Create a text element with empty content
      const textElement: Text = {
        // ShapeElement properties
        id: 'text-002',
        type: ElementType.TEXT,
        visible: true,
        locked: false,

        // Text-specific properties
        content: '',
        position: { x: 100, y: 100 },
        fontSize: 16,
        fontFamily: 'Arial',
      }

      // Verify the text element has the expected properties
      expect(textElement.id).toBe('text-002')
      expect(textElement.type).toBe(ElementType.TEXT)
      expect(textElement.visible).toBe(true)
      expect(textElement.locked).toBe(false)

      expect(textElement.content).toBe('')
      expect(textElement.position).toEqual({ x: 100, y: 100 })
      expect(textElement.fontSize).toBe(16)
      expect(textElement.fontFamily).toBe('Arial')
    })

    it('should allow text element with multiline content', () => {
      // Create a text element with multiline content
      const textElement: Text = {
        // ShapeElement properties
        id: 'text-003',
        type: ElementType.TEXT,
        visible: true,
        locked: false,

        // Text-specific properties
        content: 'Line 1\nLine 2\nLine 3',
        position: { x: 200, y: 200 },
        fontSize: 18,
        fontFamily: 'Times New Roman, serif',
      }

      // Verify the text element has the expected properties
      expect(textElement.id).toBe('text-003')
      expect(textElement.type).toBe(ElementType.TEXT)
      expect(textElement.visible).toBe(true)
      expect(textElement.locked).toBe(false)

      expect(textElement.content).toBe('Line 1\nLine 2\nLine 3')
      expect(textElement.position).toEqual({ x: 200, y: 200 })
      expect(textElement.fontSize).toBe(18)
      expect(textElement.fontFamily).toBe('Times New Roman, serif')
    })

    it('should allow text element with very small font size', () => {
      // Create a text element with very small font size
      const textElement: Text = {
        // ShapeElement properties
        id: 'text-004',
        type: ElementType.TEXT,
        visible: true,
        locked: false,

        // Text-specific properties
        content: 'Small Text',
        position: { x: 300, y: 300 },
        fontSize: 6, // Very small font size
        fontFamily: 'Courier New, monospace',
      }

      // Verify the text element has the expected properties
      expect(textElement.id).toBe('text-004')
      expect(textElement.type).toBe(ElementType.TEXT)
      expect(textElement.visible).toBe(true)
      expect(textElement.locked).toBe(false)

      expect(textElement.content).toBe('Small Text')
      expect(textElement.position).toEqual({ x: 300, y: 300 })
      expect(textElement.fontSize).toBe(6)
      expect(textElement.fontFamily).toBe('Courier New, monospace')
    })

    it('should allow text element with very large font size', () => {
      // Create a text element with very large font size
      const textElement: Text = {
        // ShapeElement properties
        id: 'text-005',
        type: ElementType.TEXT,
        visible: true,
        locked: false,

        // Text-specific properties
        content: 'Large Text',
        position: { x: 400, y: 400 },
        fontSize: 72, // Very large font size
        fontFamily: 'Impact, sans-serif',
      }

      // Verify the text element has the expected properties
      expect(textElement.id).toBe('text-005')
      expect(textElement.type).toBe(ElementType.TEXT)
      expect(textElement.visible).toBe(true)
      expect(textElement.locked).toBe(false)

      expect(textElement.content).toBe('Large Text')
      expect(textElement.position).toEqual({ x: 400, y: 400 })
      expect(textElement.fontSize).toBe(72)
      expect(textElement.fontFamily).toBe('Impact, sans-serif')
    })

    it('should allow text element with negative position', () => {
      // Create a text element with negative position coordinates
      const textElement: Text = {
        // ShapeElement properties
        id: 'text-006',
        type: ElementType.TEXT,
        visible: true,
        locked: false,

        // Text-specific properties
        content: 'Negative Position',
        position: { x: -50, y: -50 }, // Negative position
        fontSize: 14,
        fontFamily: 'Georgia, serif',
      }

      // Verify the text element has the expected properties
      expect(textElement.id).toBe('text-006')
      expect(textElement.type).toBe(ElementType.TEXT)
      expect(textElement.visible).toBe(true)
      expect(textElement.locked).toBe(false)

      expect(textElement.content).toBe('Negative Position')
      expect(textElement.position).toEqual({ x: -50, y: -50 })
      expect(textElement.fontSize).toBe(14)
      expect(textElement.fontFamily).toBe('Georgia, serif')
    })
  })
})
