/**
 * Service Result Types
 *
 * This file defines the result interfaces for services.
 *
 * @module types/services/core
 */

/**
 * Error object for service operations
 */
export interface ServiceError {
  /** Error code */
  code: string
  /** Error message */
  message: string
  /** Optional additional details */
  details?: unknown
}

/**
 * Generic result returned by service operations.
 */
export interface ServiceResult<T> {
  /** Whether the operation was successful */
  success: boolean
  /** Result data (if successful) */
  data?: T
  /** Error information (if unsuccessful) - can be string (for backwards compatibility) or ServiceError */
  error?: string | ServiceError
  /** Timestamp when the result was generated */
  timestamp?: number
}

/**
 * Generic async result type for service operations.
 */
export type AsyncServiceResult<T> = Promise<ServiceResult<T>>
