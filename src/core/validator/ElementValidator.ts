/**
 * @file ElementValidator.ts
 * @description Provides static methods for validating element data objects.
 * It manages a cache of validator instances and dynamically imports validator modules
 * based on the element's `type` property. This allows for efficient and extensible validation logic.
 *
 * @module core/validator/ElementValidator
 * @implements {IElementValidator}
 */

import type { IElementValidator } from './IElementValidator'
import type { IBaseShapeValidator } from './validators/common/validatorBase'
import type {
  ValidationResult,
  ValidatableShape as ValidatorShape,
} from '@/types/core/validator/validator-interface'
// CoreError and ErrorType imports removed as they are no longer used
import { ElementType } from '@/types/core/elementDefinitions'
import { ValidationErrorCode } from '@/types/core/validator/error-codes'

/**
 * Type definition for validator constructor signature.
 *
 * @remarks
 * Used for dynamically imported validator classes.
 */
type ValidatorConstructor = new () => IBaseShapeValidator

/**
 * Provides static methods for validating element data objects.
 *
 * @remarks
 * It manages a cache of validator instances and dynamically imports validator modules
 * based on the element's `type` property. This allows for efficient and extensible validation logic.
 *
 * @implements {IElementValidator}
 */
export class ElementValidator implements IElementValidator {
  /**
   * Cache for instantiated validator instances, keyed by ElementType.
   * @private
   * @static
   */
  private static validators: Map<string, IBaseShapeValidator> = new Map()

  /**
   * Map storing promises for dynamically importing validator modules, keyed by ElementType.
   *
   * @remarks
   * Allows asynchronous loading of validator code.
   * @private
   * @static
   */
  private static validatorModules: Map<string, Promise<ValidatorConstructor>> = new Map()

  /**
   * Initializes the map of validator module promises.
   *
   * @remarks
   * This method populates the map with dynamic import() calls for each known ElementType,
   * allowing validators to be loaded on demand. It only runs once.
   *
   * @public
   * @static
   */
  public static initializeValidatorModules(): void {
    if (this.validatorModules.size > 0) {
      console.warn('[ElementValidator initializeValidatorModules] Already initialized. Module keys:', Array.from(this.validatorModules.keys()))
      return
    }
    console.warn('[ElementValidator initializeValidatorModules] Initializing for the first time...')

    const validatorPaths: Partial<Record<ElementType, string>> = {
      [ElementType.RECTANGLE]: './validators/shape/rectangleValidator',
      [ElementType.SQUARE]: './validators/shape/rectangleValidator',
      [ElementType.ELLIPSE]: './validators/shape/ellipseValidator',
      [ElementType.CIRCLE]: './validators/shape/ellipseValidator',
      [ElementType.POLYGON]: './validators/shape/polygonValidator',
      [ElementType.TRIANGLE]: './validators/shape/polygonValidator',
      [ElementType.QUADRILATERAL]: './validators/shape/polygonValidator',
      [ElementType.PENTAGON]: './validators/shape/polygonValidator',
      [ElementType.HEXAGON]: './validators/shape/polygonValidator',
      [ElementType.TEXT]: './validators/media/TextValidator',
      [ElementType.IMAGE]: './validators/media/ImageValidator',
      [ElementType.LINE]: './validators/path/lineValidator',
      [ElementType.POLYLINE]: './validators/path/polylineValidator',
      [ElementType.ARC]: './validators/path/arcValidator',
      [ElementType.QUADRATIC]: './validators/path/quadraticValidator',
      [ElementType.CUBIC]: './validators/path/cubicValidator',
      [ElementType.FURNITURE]: './validators/media/ImageValidator',
      [ElementType.FIXTURE]: './validators/media/ImageValidator',
      [ElementType.LIGHT]: './validators/media/ImageValidator',
      [ElementType.FLOOR_AREA]: './validators/media/ImageValidator',
      [ElementType.WALL_PAINT]: './validators/media/ImageValidator',
      [ElementType.WALL_PAPER]: './validators/media/ImageValidator',
      [ElementType.OPENING]: './validators/media/ImageValidator',
    }

    Object.entries(validatorPaths).forEach(([typeEnumKey, path]) => {
      if (path && typeEnumKey) {
        this.validatorModules.set(typeEnumKey, import(/* @vite-ignore */ path).then((module: Record<string, unknown>) => {
          if (module != null && typeof module === 'object' && 'default' in module && module.default != null) {
            return module.default as ValidatorConstructor
          }
          if (module != null && typeof module === 'object') {
            const firstExport = Object.values(module)[0]
            if (firstExport != null && typeof firstExport === 'function') {
              return firstExport as ValidatorConstructor
            }
          }
          throw new Error(`Validator module at ${path} does not have a valid export.`)
        }))
      }
      else {
        console.warn(`[ElementValidator initializeValidatorModules] Skipping validator path for type: ${typeEnumKey} as path or type itself is missing/empty.`)
      }
    })
    console.warn('[ElementValidator initializeValidatorModules] Finished populating. Registered module keys:', Array.from(this.validatorModules.keys()))
  }

  /**
   * Manually registers or overrides a validator instance for a specific element type.
   *
   * @remarks
   * This allows for custom validator injection or overriding default validators.
   *
   * @param elementType - The element type to register the validator for.
   * @param validator - The validator instance to register.
   * @static
   */
  public static registerValidator(elementType: ElementType, validator: IBaseShapeValidator): void {
    this.validators.set(elementType.toString(), validator) // Use string value of enum as key
  }

  /**
   * Checks if a validator exists for a specific element type.
   *
   * @param elementType - The element type to check.
   * @returns True if a validator exists; otherwise, false.
   * @static
   */
  public static hasValidator(elementType: ElementType): boolean {
    return this.validators.has(elementType.toString()) || this.validatorModules.has(elementType.toString())
  }

  /**
   * Retrieves the validator instance for a specific element type.
   *
   * @remarks
   * It first checks the cache. If not found, it ensures modules are initialized,
   * awaits the dynamic import promise, instantiates the validator,
   * caches it, and then returns it.
   *
   * @param elementType - The element type for which to get the validator.
   * @returns A Promise resolving to the validator instance for the given type.
   * @throws Error if a validator module cannot be found for the specified element type.
   * @private
   * @static
   */
  private static async getValidator(elementType: string): Promise<IBaseShapeValidator> {
    let validator = this.validators.get(elementType)
    if (validator)
      return validator

    this.initializeValidatorModules()

    const validatorModulePromise = this.validatorModules.get(elementType)
    if (!validatorModulePromise) {
      console.error(`[ElementValidator getValidator] No import promise found for type ${elementType}. Available module keys:`, Array.from(this.validatorModules.keys()))
      throw new Error(`Validator module not found for type ${elementType}`)
    }

    try {
      const ValidatorClass = await validatorModulePromise
      validator = new ValidatorClass()
      this.validators.set(elementType, validator)
      return validator
    }
    catch (error) {
      console.error(`[ElementValidator getValidator] Error loading validator module for type ${elementType}:`, error)
      throw new Error(`Failed to load validator module for type ${elementType}: ${(error as Error).message}`)
    }
  }

  /**
   * Formats an error object into a string message.
   *
   * @remarks
   * Handles standard \`Error\` objects and other types.
   *
   * @param error - The error object or value.
   * @returns A string representation of the error message.
   * @private
   * @static
   */
  private static formatErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message
    }
    return typeof error === 'string' ? error : 'Unknown error'
  }

  /**
   * Validates a single shape element data object asynchronously.
   *
   * @remarks
   * Retrieves the appropriate validator based on the element's \`type\` (handling aliases),
   * dynamically loading it if necessary, and then calls its \`validate\` method.
   * Catches and formats errors during the process, returning a \`ValidationResult\`.
   *
   * @param element - The shape data object to validate.
   * @returns A Promise resolving to a ValidationResult object.
   *          \`result.valid\` indicates success or failure.
   *          \`result.errors\` contains specific validation error details on failure.
   *          \`result.warnings\` may contain non-critical issues.
   * @static
   */
  public static async validateElement(element: ValidatorShape): Promise<ValidationResult> {
    console.warn(`[ElementValidator] Validating element ID: ${element.id}, Type: ${element.type}`)
    try {
      const validator = await this.getValidator(element.type)
      console.warn(`[ElementValidator] Using validator for type: ${validator.constructor.name} to validate ${element.type}`)
      const result = validator.validate(element)
      console.warn(`[ElementValidator] Validation result for ${element.id}: Valid=${result.valid}, Errors=${result.errors?.length ?? 0}, Warnings=${result.warnings?.length ?? 0}`)
      return result
    }
    catch (error: unknown) {
      console.error(`[ElementValidator] Error during validation for element ID: ${element.id} (Type: ${element.type}):`, error)
      const errorMessage = this.formatErrorMessage(error)
      const errorToLog = error instanceof Error ? error : new Error(errorMessage)
      // Log error details for debugging
      console.error('[ElementValidator] Validation error details:', {
        elementId: element.id,
        elementType: element.type,
        context: 'validateElement',
        originalError: errorToLog,
      })
      return {
        valid: false,
        message: `Validation failed for element ${element.id}: ${errorMessage}`,
        errors: [{
          code: ValidationErrorCode.VALIDATION_SPECIFIC_ERROR,
          message: errorMessage,
          path: 'element',
          value: element,
        }],
      }
    }
  }

  /**
   * Validates an array of shape element data objects asynchronously.
   *
   * @remarks
   * Iterates through the array and calls \`validateElement\` for each element, collecting all results.
   * Validation proceeds even if some elements fail validation.
   *
   * @param elements - An array of shape data objects to validate.
   * @returns A Promise resolving to an array of ValidationResult objects,
   *          one corresponding to each element in the input array, in the same order.
   * @static
   */
  public static async validateElements(elements: ValidatorShape[]): Promise<ValidationResult[]> {
    console.warn(`[ElementValidator] Validating batch of ${elements.length} elements.`)
    const results = await Promise.all(elements.map(async element => this.validateElement(element)))
    console.warn(`[ElementValidator] Batch validation complete.`)
    return results
  }

  /**
   * Checks if all elements in a given array are valid according to their respective validators.
   *
   * @remarks
   * This is a convenience method that calls \`validateElement\` internally and then checks
   * the \`valid\` status of each result. It short-circuits if any element is found invalid during validation.
   *
   * @param elements - An array of shape data objects to check.
   * @returns A Promise resolving to \`true\` if all elements in the array pass validation, \`false\` otherwise.
   * @static
   */
  public static async areAllElementsValid(elements: ValidatorShape[]): Promise<boolean> {
    console.warn(`[ElementValidator] Checking validity of ${elements.length} elements.`)
    for (const element of elements) {
      const result = await this.validateElement(element)
      if (!result.valid) {
        console.warn(`[ElementValidator] Validity check failed for element: ${element.id}`)
        return false
      }
    }
    console.warn(`[ElementValidator] All ${elements.length} elements passed validity check.`)
    return true
  }

  // INSTANCE METHODS (delegating to static methods, required by IElementValidator)

  /**
   * Instance method for registering validators.
   *
   * @remarks
   * Delegates to static method. Required by IElementValidator interface.
   *
   * @param elementType - The element type to register the validator for.
   * @param validator - The validator instance to register.
   */
  public registerValidator(elementType: ElementType, validator: IBaseShapeValidator): void {
    ElementValidator.registerValidator(elementType, validator)
  }

  /**
   * Instance method for checking if a validator exists.
   *
   * @remarks
   * Delegates to static method. Required by IElementValidator interface.
   *
   * @param elementType - The element type to check.
   * @returns True if a validator exists; otherwise, false.
   */
  public hasValidator(elementType: ElementType): boolean {
    return ElementValidator.hasValidator(elementType)
  }

  /**
   * Instance method for validating a single element.
   *
   * @remarks
   * Delegates to static method. Required by IElementValidator interface.
   *
   * @param element - The element to validate.
   * @returns A Promise resolving to a ValidationResult object.
   */
  public async validateElement(element: ValidatorShape): Promise<ValidationResult> {
    return ElementValidator.validateElement(element)
  }

  /**
   * Instance method for validating multiple elements.
   *
   * @remarks
   * Delegates to static method. Required by IElementValidator interface.
   *
   * @param elements - The elements to validate.
   * @returns A Promise resolving to an array of ValidationResult objects.
   */
  public async validateElements(elements: ValidatorShape[]): Promise<ValidationResult[]> {
    return ElementValidator.validateElements(elements)
  }

  /**
   * Instance method for checking if all elements are valid.
   *
   * @remarks
   * Delegates to static method. Required by IElementValidator interface.
   *
   * @param elements - The elements to check.
   * @returns A Promise resolving to true if all elements are valid; otherwise, false.
   */
  public async areAllElementsValid(elements: ValidatorShape[]): Promise<boolean> {
    return ElementValidator.areAllElementsValid(elements)
  }
}
