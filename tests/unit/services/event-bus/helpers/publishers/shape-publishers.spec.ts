import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  publishSelectionChanged,
  publishShapeCreateRequest,
  publishShapeDeleteRequest,
  publishShapeDeselected,
  publishShapeDuplicationRequest,
  publishShapeEditRequest,
  publishShapeSelectRequest,
} from '@/services/event-bus/helpers/publishers/shape-publishers'
import { Point } from '@/types/core/element/geometry/point'
import { AppEventType } from '@/types/services/events'
import { SelectionMode } from '@/types/services/events/shapeEvents'

describe('shape Publishers', () => {
  let mockEventBus: any

  beforeEach(() => {
    mockEventBus = {
      publish: vi.fn(),
    }
  })

  describe('shape Create Publishers', () => {
    it('should publish shape create request event correctly', () => {
      const ElementType = 'rectangle'
      const position = new Point(100, 200)
      const properties = { width: 50, height: 30 }

      publishShapeCreateRequest(mockEventBus, ElementType, position, properties)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: {
          ElementType,
          position,
          properties,
        },
      })
    })

    it('should handle position as Point instance', () => {
      const ElementType = 'rectangle'
      const position = new Point(100, 200)
      const properties = { width: 50, height: 30 }

      publishShapeCreateRequest(mockEventBus, ElementType, position, properties)

      expect(mockEventBus.publish).toHaveBeenCalled()
      const call = mockEventBus.publish.mock.calls[0][0]
      expect(call.type).toBe(AppEventType.SHAPE_CREATE_REQUEST)
      expect(call.payload.position).toBeInstanceOf(Point)
      expect(call.payload.position.x).toBe(100)
      expect(call.payload.position.y).toBe(200)
    })
  })

  describe('shape Edit Publishers', () => {
    it('should publish shape edit request event correctly', () => {
      const shapeId = 'shape-123'
      const changes = { width: 100, height: 80 }

      publishShapeEditRequest(mockEventBus, shapeId, changes)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_EDIT_REQUEST,
        timestamp: expect.any(Number),
        payload: {
          shapeId,
          changes,
        },
      })
    })
  })

  describe('shape Delete Publishers', () => {
    it('should publish shape delete request event correctly', () => {
      const shapeIds = ['shape-123']

      publishShapeDeleteRequest(mockEventBus, shapeIds)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: {
          shapeIds,
        },
      })
    })
  })

  describe('shape Duplication Publishers', () => {
    it('should publish shape duplication request event correctly', () => {
      const originalId = 'shape-123'
      const position = new Point(100, 200)

      publishShapeDuplicationRequest(mockEventBus, originalId, position)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_DUPLICATE_REQUEST,
        payload: {
          originalId,
          position,
        },
      })
    })
  })

  describe('selection Publishers', () => {
    it('should publish selection changed event correctly', () => {
      const selectedIds = ['shape-1', 'shape-2']

      publishSelectionChanged(mockEventBus, selectedIds)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SELECTION_CHANGED,
        payload: {
          selectedIds,
        },
      })
    })

    it('should handle empty selection', () => {
      const selectedIds: string[] = []

      publishSelectionChanged(mockEventBus, selectedIds)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SELECTION_CHANGED,
        payload: {
          selectedIds: [],
        },
      })
    })

    it('should publish shape select request event correctly', () => {
      const shapeIds = ['shape-1', 'shape-2']
      const mode = SelectionMode.ADD

      publishShapeSelectRequest(mockEventBus, shapeIds, mode)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SHAPE_SELECT_REQUEST,
        payload: {
          shapeIds,
          selectionMode: mode,
        },
      })
    })

    it('should publish shape deselected event correctly', () => {
      publishShapeDeselected(mockEventBus)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SELECTION_CHANGED,
        payload: {
          selectedIds: [],
        },
      })
    })
  })
})
