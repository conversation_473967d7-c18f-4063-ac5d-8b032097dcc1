/**
 * @file objectUtils.spec.ts
 * @description Unit tests for object utility functions
 */

import { describe, expect, it } from 'vitest'
import {
  deepClone,
  deepMerge,
  getNestedProperty,
  hasNestedProperty,
  isEmptyObject,
  isObject,
  omit,
  pick,
  setNestedProperty,
} from '@/core/utils/objectUtils'

describe('objectUtils', () => {
  describe('deepClone', () => {
    it('should create a deep copy of an object', () => {
      // Arrange
      const original = {
        name: 'Test',
        details: {
          age: 30,
          address: {
            city: 'Test City',
            country: 'Test Country',
          },
        },
        tags: ['tag1', 'tag2'],
      }

      // Act
      const clone = deepClone(original)

      // Assert
      expect(clone).toEqual(original)
      expect(clone).not.toBe(original)
      expect(clone.details).not.toBe(original.details)
      expect(clone.details.address).not.toBe(original.details.address)
      expect(clone.tags).not.toBe(original.tags)
    })

    it('should handle primitive values', () => {
      expect(deepClone(42)).toBe(42)
      expect(deepClone('test')).toBe('test')
      expect(deepClone(true)).toBe(true)
      expect(deepClone(null)).toBe(null)
      expect(deepClone(undefined)).toBe(undefined)
    })

    it('should handle arrays', () => {
      // Arrange
      const original = [1, 2, { a: 3 }]

      // Act
      const clone = deepClone(original)

      // Assert
      expect(clone).toEqual(original)
      expect(clone).not.toBe(original)
      expect(clone[2]).not.toBe(original[2])
    })

    it('should handle Date objects', () => {
      // Arrange
      const original = new Date()

      // Act
      const clone = deepClone(original)

      // Assert
      expect(clone).toEqual(original)
      expect(clone).not.toBe(original)
    })

    it('should handle circular references', () => {
      // Arrange
      const original: any = { name: 'Test' }
      original.self = original

      // Act & Assert
      expect(() => deepClone(original)).not.toThrow()
      const clone = deepClone(original)
      expect(clone.name).toBe('Test')
      expect(clone.self).toBe(clone)
    })
  })

  describe('deepMerge', () => {
    it('should merge two objects deeply', () => {
      // Arrange
      const target = {
        name: 'Target',
        details: {
          age: 30,
          address: {
            city: 'Target City',
          },
        },
        tags: ['tag1'],
      }

      const source = {
        name: 'Source',
        details: {
          gender: 'M',
          address: {
            country: 'Source Country',
          },
        },
        tags: ['tag2'],
      }

      // Act
      const result = deepMerge(target, source)

      // Assert
      expect(result).toEqual({
        name: 'Source',
        details: {
          age: 30,
          gender: 'M',
          address: {
            city: 'Target City',
            country: 'Source Country',
          },
        },
        tags: ['tag2'],
      })
    })

    it('should handle arrays by replacing them', () => {
      // Arrange
      const target = { items: [1, 2, 3] }
      const source = { items: [4, 5] }

      // Act
      const result = deepMerge(target, source)

      // Assert
      expect(result.items).toEqual([4, 5])
    })

    it('should handle null and undefined values', () => {
      // Arrange
      const target = { a: 1, b: 2, c: { d: 3 } }
      const source = { a: null, b: undefined, c: null }

      // Act
      const result = deepMerge(target, source)

      // Assert
      expect(result).toEqual({ a: null, b: 2, c: null })
    })

    it('should not modify the original objects', () => {
      // Arrange
      const target = { a: 1, b: { c: 2 } }
      const source = { b: { d: 3 } }

      // Act
      const result = deepMerge(target, source)

      // Assert
      expect(target).toEqual({ a: 1, b: { c: 2 } })
      expect(source).toEqual({ b: { d: 3 } })
      expect(result).toEqual({ a: 1, b: { c: 2, d: 3 } })
    })

    it('should handle multiple sources', () => {
      // Arrange
      const target = { a: 1 }
      const source1 = { b: 2 }
      const source2 = { c: 3 }

      // Act
      const result = deepMerge(target, source1, source2)

      // Assert
      expect(result).toEqual({ a: 1, b: 2, c: 3 })
    })
  })

  describe('getNestedProperty', () => {
    it('should get a nested property using dot notation', () => {
      // Arrange
      const obj = {
        a: {
          b: {
            c: 'value',
          },
        },
      }

      // Act & Assert
      expect(getNestedProperty(obj, 'a.b.c')).toBe('value')
    })

    it('should return undefined for non-existent properties', () => {
      // Arrange
      const obj = { a: { b: 1 } }

      // Act & Assert
      expect(getNestedProperty(obj, 'a.c')).toBeUndefined()
      expect(getNestedProperty(obj, 'x.y.z')).toBeUndefined()
    })

    it('should handle arrays in the path', () => {
      // Arrange
      const obj = {
        users: [
          { name: 'Alice', age: 30 },
          { name: 'Bob', age: 25 },
        ],
      }

      // Act & Assert
      expect(getNestedProperty(obj, 'users.0.name')).toBe('Alice')
      expect(getNestedProperty(obj, 'users.1.age')).toBe(25)
    })

    it('should handle empty or invalid paths', () => {
      // Arrange
      const obj = { a: 1 }

      // Act & Assert
      expect(getNestedProperty(obj, '')).toBe(obj)
      expect(getNestedProperty(obj, null as any)).toBe(obj)
      expect(getNestedProperty(obj, undefined as any)).toBe(obj)
    })

    it('should handle null or undefined objects', () => {
      // Act & Assert
      expect(getNestedProperty(null as any, 'a.b')).toBeUndefined()
      expect(getNestedProperty(undefined as any, 'a.b')).toBeUndefined()
    })
  })

  describe('setNestedProperty', () => {
    it('should set a nested property using dot notation', () => {
      // Arrange
      const obj = {
        a: {
          b: {},
        },
      }

      // Act
      setNestedProperty(obj, 'a.b.c', 'value')

      // Assert
      expect(obj.a.b).toHaveProperty('c', 'value')
    })

    it('should create intermediate objects if they do not exist', () => {
      // Arrange
      const obj = {}

      // Act
      setNestedProperty(obj, 'a.b.c', 'value')

      // Assert
      expect(obj).toEqual({ a: { b: { c: 'value' } } })
    })

    it('should handle arrays in the path', () => {
      // Arrange
      const obj = {
        users: [
          { name: 'Alice' },
          { name: 'Bob' },
        ],
      }

      // Act
      setNestedProperty(obj, 'users.0.age', 30)
      setNestedProperty(obj, 'users.1.age', 25)

      // Assert
      expect(obj.users[0]).toEqual({ name: 'Alice', age: 30 })
      expect(obj.users[1]).toEqual({ name: 'Bob', age: 25 })
    })

    it('should create arrays for numeric path segments', () => {
      // Arrange
      const obj = {}

      // Act
      setNestedProperty(obj, 'users.0.name', 'Alice')

      // Assert
      expect(obj).toEqual({ users: [{ name: 'Alice' }] })
    })

    it('should handle empty paths', () => {
      // Arrange
      const obj = { a: 1 }

      // Act
      setNestedProperty(obj, '', 'value')

      // Assert
      expect(obj).toEqual({ a: 1 }) // No change
    })

    it('should handle null or undefined objects', () => {
      // Act & Assert
      expect(() => setNestedProperty(null as any, 'a.b', 'value')).not.toThrow()
      expect(() => setNestedProperty(undefined as any, 'a.b', 'value')).not.toThrow()
    })
  })

  describe('hasNestedProperty', () => {
    it('should check if a nested property exists using dot notation', () => {
      // Arrange
      const obj = {
        a: {
          b: {
            c: 'value',
          },
        },
      }

      // Act & Assert
      expect(hasNestedProperty(obj, 'a')).toBe(true)
      expect(hasNestedProperty(obj, 'a.b')).toBe(true)
      expect(hasNestedProperty(obj, 'a.b.c')).toBe(true)
      expect(hasNestedProperty(obj, 'a.b.d')).toBe(false)
      expect(hasNestedProperty(obj, 'x')).toBe(false)
    })

    it('should handle arrays in the path', () => {
      // Arrange
      const obj = {
        users: [
          { name: 'Alice', age: 30 },
          { name: 'Bob', age: 25 },
        ],
      }

      // Act & Assert
      expect(hasNestedProperty(obj, 'users')).toBe(true)
      expect(hasNestedProperty(obj, 'users.0')).toBe(true)
      expect(hasNestedProperty(obj, 'users.0.name')).toBe(true)
      expect(hasNestedProperty(obj, 'users.2')).toBe(false)
      expect(hasNestedProperty(obj, 'users.0.address')).toBe(false)
    })

    it('should handle empty paths', () => {
      // Arrange
      const obj = { a: 1 }

      // Act & Assert
      expect(hasNestedProperty(obj, '')).toBe(true) // The object itself exists
    })

    it('should handle null or undefined objects', () => {
      // Act & Assert
      expect(hasNestedProperty(null as any, 'a.b')).toBe(false)
      expect(hasNestedProperty(undefined as any, 'a.b')).toBe(false)
    })

    it('should handle null or undefined values in the path', () => {
      // Arrange
      const obj = {
        a: {
          b: null,
          c: undefined,
        },
      }

      // Act & Assert
      expect(hasNestedProperty(obj, 'a.b')).toBe(true) // null is a value
      expect(hasNestedProperty(obj, 'a.c')).toBe(true) // undefined is a value if the key exists
      expect(hasNestedProperty(obj, 'a.b.d')).toBe(false) // can't access properties of null
    })
  })

  describe('isObject', () => {
    it('should return true for objects', () => {
      expect(isObject({})).toBe(true)
      expect(isObject({ a: 1 })).toBe(true)
      expect(isObject(new Object())).toBe(true)
    })

    it('should return false for non-objects', () => {
      expect(isObject(null)).toBe(false)
      expect(isObject(undefined)).toBe(false)
      expect(isObject(42)).toBe(false)
      expect(isObject('string')).toBe(false)
      expect(isObject(true)).toBe(false)
      expect(isObject([])).toBe(false)
      expect(isObject(new Date())).toBe(false)
      expect(isObject(() => {})).toBe(false)
    })
  })

  describe('isEmptyObject', () => {
    it('should return true for empty objects', () => {
      expect(isEmptyObject({})).toBe(true)
      expect(isEmptyObject(new Object())).toBe(true)
    })

    it('should return false for non-empty objects', () => {
      expect(isEmptyObject({ a: 1 })).toBe(false)
      expect(isEmptyObject({ a: undefined })).toBe(false)
    })

    it('should return false for non-objects', () => {
      expect(isEmptyObject(null)).toBe(false)
      expect(isEmptyObject(undefined)).toBe(false)
      expect(isEmptyObject(42)).toBe(false)
      expect(isEmptyObject('string')).toBe(false)
      expect(isEmptyObject(true)).toBe(false)
      expect(isEmptyObject([])).toBe(false)
    })
  })

  describe('pick', () => {
    it('should pick specified properties from an object', () => {
      // Arrange
      const obj = { a: 1, b: 2, c: 3, d: 4 }

      // Act
      const result = pick(obj, ['a', 'c'])

      // Assert
      expect(result).toEqual({ a: 1, c: 3 })
    })

    it('should ignore non-existent properties', () => {
      // Arrange
      const obj = { a: 1, b: 2 }

      // Act
      const result = pick(obj, ['a', 'c'])

      // Assert
      expect(result).toEqual({ a: 1 })
    })

    it('should return an empty object if no properties are specified', () => {
      // Arrange
      const obj = { a: 1, b: 2 }

      // Act
      const result = pick(obj, [])

      // Assert
      expect(result).toEqual({})
    })

    it('should handle null or undefined objects', () => {
      // Act & Assert
      expect(pick(null as any, ['a'])).toEqual({})
      expect(pick(undefined as any, ['a'])).toEqual({})
    })
  })

  describe('omit', () => {
    it('should omit specified properties from an object', () => {
      // Arrange
      const obj = { a: 1, b: 2, c: 3, d: 4 }

      // Act
      const result = omit(obj, ['b', 'd'])

      // Assert
      expect(result).toEqual({ a: 1, c: 3 })
    })

    it('should ignore non-existent properties', () => {
      // Arrange
      const obj = { a: 1, b: 2 }

      // Act
      const result = omit(obj, ['c', 'd'])

      // Assert
      expect(result).toEqual({ a: 1, b: 2 })
    })

    it('should return a copy of the object if no properties are specified', () => {
      // Arrange
      const obj = { a: 1, b: 2 }

      // Act
      const result = omit(obj, [])

      // Assert
      expect(result).toEqual({ a: 1, b: 2 })
      expect(result).not.toBe(obj)
    })

    it('should handle null or undefined objects', () => {
      // Act & Assert
      expect(omit(null as any, ['a'])).toEqual({})
      expect(omit(undefined as any, ['a'])).toEqual({})
    })
  })
})
