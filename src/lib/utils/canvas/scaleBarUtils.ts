// 共享常量和计算函数
export const PHYSICAL_LENGTHS_MM = [
  50,
  100,
  200,
  500,
  1000,
  2000,
  5000,
  10000,
  20000,
  50000,
  100000,
]

/**
 * 计算比例尺信息的函数
 * 根据当前缩放级别和像素密度计算最合适的比例尺长度和单位
 *
 * @param zoom 当前缩放级别
 * @param pixelsPerMM 每毫米对应的像素数
 * @param canvasWidth 画布宽度
 * @returns 包含像素长度、单位值和单位的对象
 */
export function calculateScaleBarInfo(zoom: number, pixelsPerMM: number, canvasWidth: number) {
  const MIN_PIXEL_LENGTH = 100
  const MAX_PIXEL_LENGTH = Math.min(300, canvasWidth / 2)

  const effectivePixelsPerMM = pixelsPerMM * zoom
  // Find the best matching physical length for the current zoom and pixel density
  let best = {
    physicalLengthMM: PHYSICAL_LENGTHS_MM[0],
    pixelLength: PHYSICAL_LENGTHS_MM[0] * effectivePixelsPerMM,
    diff: Math.abs(PHYSICAL_LENGTHS_MM[0] * effectivePixelsPerMM - (MIN_PIXEL_LENGTH + MAX_PIXEL_LENGTH) / 2),
  }
  for (const len of PHYSICAL_LENGTHS_MM) {
    const px = len * effectivePixelsPerMM
    const diff = Math.abs(px - (MIN_PIXEL_LENGTH + MAX_PIXEL_LENGTH) / 2)
    if (px >= MIN_PIXEL_LENGTH && px <= MAX_PIXEL_LENGTH && diff < best.diff) {
      best = { physicalLengthMM: len, pixelLength: px, diff }
    }
  }
  // If no suitable length found, pick the closest (either min or max)
  if (best.pixelLength < MIN_PIXEL_LENGTH) {
    // Use the largest
    const len = PHYSICAL_LENGTHS_MM[PHYSICAL_LENGTHS_MM.length - 1]
    best = {
      physicalLengthMM: len,
      pixelLength: len * effectivePixelsPerMM,
      diff: Math.abs(len * effectivePixelsPerMM - (MIN_PIXEL_LENGTH + MAX_PIXEL_LENGTH) / 2),
    }
  }
  if (best.pixelLength > MAX_PIXEL_LENGTH) {
    // Use the smallest
    const len = PHYSICAL_LENGTHS_MM[0]
    best = {
      physicalLengthMM: len,
      pixelLength: len * effectivePixelsPerMM,
      diff: Math.abs(len * effectivePixelsPerMM - (MIN_PIXEL_LENGTH + MAX_PIXEL_LENGTH) / 2),
    }
  }
  // Format the unit for display
  let unit = 'mm'
  let value = best.physicalLengthMM
  if (value >= 1000 && value % 1000 === 0) {
    unit = 'm'
    value = value / 1000
  }
  else if (value >= 10 && value % 10 === 0) {
    unit = 'cm'
    value = value / 10
  }
  // Keep decimals for meters and centimeters
  if (unit === 'm') {
    value = Number(value.toFixed(2))
  }
  if (unit === 'cm') {
    value = Number(value.toFixed(1))
  }
  return {
    pixelLength: best.pixelLength,
    unitValue: value,
    unit,
  }
}
