import type {
  ErrorContext,
  ErrorDetails,
  ErrorService,
} from '@/types/services/core/errorService'
import { describe, expect, it, vi } from 'vitest'
import {
  ErrorSeverity,
} from '@/types/services/core/errorService'

describe('error Service Types', () => {
  describe('errorContext', () => {
    it('should allow creating an error context with all properties', () => {
      const context: ErrorContext = {
        source: 'TestComponent',
        metadata: { key: 'value', count: 42 },
        stack: 'Error stack trace',
      }

      expect(context.source).toBe('TestComponent')
      expect(context.metadata?.key).toBe('value')
      expect(context.metadata?.count).toBe(42)
      expect(context.stack).toBe('Error stack trace')
    })

    it('should allow creating an error context with minimal properties', () => {
      const minimalContext: ErrorContext = {
        source: 'MinimalComponent',
      }

      expect(minimalContext.source).toBe('MinimalComponent')
      expect(minimalContext.metadata).toBeUndefined()
      expect(minimalContext.stack).toBeUndefined()
    })
  })

  describe('errorSeverity', () => {
    it('should define all expected severity levels', () => {
      expect(ErrorSeverity.DEBUG).toBeDefined()
      expect(ErrorSeverity.INFO).toBeDefined()
      expect(ErrorSeverity.WARNING).toBeDefined()
      expect(ErrorSeverity.ERROR).toBeDefined()
      expect(ErrorSeverity.CRITICAL).toBeDefined()
    })

    it('should have the correct string values', () => {
      expect(ErrorSeverity.DEBUG).toBe('debug')
      expect(ErrorSeverity.INFO).toBe('info')
      expect(ErrorSeverity.WARNING).toBe('warning')
      expect(ErrorSeverity.ERROR).toBe('error')
      expect(ErrorSeverity.CRITICAL).toBe('critical')
    })
  })

  describe('errorDetails', () => {
    it('should allow creating error details with all properties', () => {
      const originalError = new Error('Original error')
      const context: ErrorContext = {
        source: 'TestComponent',
        metadata: { key: 'value' },
      }

      const details: ErrorDetails = {
        message: 'Test error message',
        code: 'TEST_ERROR',
        severity: ErrorSeverity.ERROR,
        context,
        originalError,
        timestamp: Date.now(),
      }

      expect(details.message).toBe('Test error message')
      expect(details.code).toBe('TEST_ERROR')
      expect(details.severity).toBe(ErrorSeverity.ERROR)
      expect(details.context).toBe(context)
      expect(details.originalError).toBe(originalError)
      expect(details.timestamp).toBeGreaterThan(0)
    })

    it('should allow creating error details with only required properties', () => {
      const details: ErrorDetails = {
        message: 'Minimal error message',
      }

      expect(details.message).toBe('Minimal error message')
      expect(details.code).toBeUndefined()
      expect(details.severity).toBeUndefined()
      expect(details.context).toBeUndefined()
      expect(details.originalError).toBeUndefined()
      expect(details.timestamp).toBeUndefined()
    })
  })

  describe('errorService', () => {
    it('should define all required methods', () => {
      // Create a mock implementation of ErrorService
      const mockErrorService: ErrorService = {
        createError: vi.fn(),
        logError: vi.fn(),
        reportError: vi.fn(),
        handleError: vi.fn(),
      }

      expect(typeof mockErrorService.createError).toBe('function')
      expect(typeof mockErrorService.logError).toBe('function')
      expect(typeof mockErrorService.reportError).toBe('function')
      expect(typeof mockErrorService.handleError).toBe('function')
    })

    it('should allow calling methods with different parameters', () => {
      // Create a mock implementation of ErrorService
      const mockErrorService: ErrorService = {
        createError: vi.fn().mockReturnValue(new Error('Test error')),
        logError: vi.fn(),
        reportError: vi.fn(),
        handleError: vi.fn(),
      }

      // Call with ErrorDetails
      const details: ErrorDetails = {
        message: 'Test error',
        code: 'TEST_ERROR',
        severity: ErrorSeverity.ERROR,
      }

      const error = mockErrorService.createError(details)
      expect(mockErrorService.createError).toHaveBeenCalledWith(details)
      expect(error.message).toBe('Test error')

      // Call with Error object
      const errorObj = new Error('Error object')
      mockErrorService.logError(errorObj)
      expect(mockErrorService.logError).toHaveBeenCalledWith(errorObj)

      // Call with ErrorDetails object
      mockErrorService.reportError(details)
      expect(mockErrorService.reportError).toHaveBeenCalledWith(details)

      // Call handleError
      mockErrorService.handleError(errorObj)
      expect(mockErrorService.handleError).toHaveBeenCalledWith(errorObj)
    })
  })
})
