import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { AppEventBusImpl } from '@/services/core/event-bus/appEventBus'
import { AppEventType } from '@/types/services/events'

describe('appEventBusImpl', () => {
  let eventBus: AppEventBusImpl
  let consoleSpy: any

  beforeEach(() => {
    // Reset singleton instance before each test
    AppEventBusImpl.resetInstance()
    eventBus = AppEventBusImpl.getInstance()
    consoleSpy = {
      log: vi.spyOn(console, 'log').mockImplementation(() => {}),
      error: vi.spyOn(console, 'error').mockImplementation(() => {}),
    }
  })

  afterEach(() => {
    vi.clearAllMocks()
    // Reset singleton instance after each test
    AppEventBusImpl.resetInstance()
  })

  describe('basic subscription and publishing', () => {
    it('should subscribe to an event and call handler when published', () => {
      const handler = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      eventBus.publish(event)

      expect(handler).toHaveBeenCalledWith(event)
    })

    it('should support alias methods on() and emit()', () => {
      const handler = vi.fn()

      eventBus.on(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      eventBus.emit(event)

      expect(handler).toHaveBeenCalledWith(event)
    })

    it('should not call handlers for different event types', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler1)
      eventBus.subscribe(AppEventType.SHAPE_DELETE_REQUEST, handler2)

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      eventBus.publish(event)

      expect(handler1).toHaveBeenCalledWith(event)
      expect(handler2).not.toHaveBeenCalled()
    })

    it('should add timestamp to events if not present', () => {
      const handler = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      eventBus.publish(event)

      expect(handler).toHaveBeenCalled()
      const calledEvent = handler.mock.calls[0][0]
      expect(calledEvent.timestamp).toBeDefined()
      expect(typeof calledEvent.timestamp).toBe('number')
    })
  })

  describe('unsubscribing', () => {
    it('should unsubscribe from an event using returned function', () => {
      const handler = vi.fn()

      const unsubscribe = eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      unsubscribe()
      eventBus.publish(event)

      expect(handler).not.toHaveBeenCalled()
    })

    it('should unsubscribe from an event using unsubscribe method', () => {
      const handler = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)
      eventBus.unsubscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      eventBus.publish(event)

      expect(handler).not.toHaveBeenCalled()
    })

    it('should support alias method off()', () => {
      const handler = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)
      eventBus.off(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      eventBus.publish(event)

      expect(handler).not.toHaveBeenCalled()
    })

    it('should unsubscribe all handlers for an event type', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler1)
      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler2)

      eventBus.unsubscribeAll(AppEventType.SHAPE_CREATE_REQUEST)

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      eventBus.publish(event)

      expect(handler1).not.toHaveBeenCalled()
      expect(handler2).not.toHaveBeenCalled()
    })

    it('should clear all subscriptions', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler1)
      eventBus.subscribe(AppEventType.SHAPE_DELETE_REQUEST, handler2)

      eventBus.clear()

      eventBus.publish({
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      })

      eventBus.publish({
        type: AppEventType.SHAPE_DELETE_REQUEST,
        payload: { shapeIds: ['shape-123'] },
      })

      expect(handler1).not.toHaveBeenCalled()
      expect(handler2).not.toHaveBeenCalled()
    })
  })

  describe('subscription options', () => {
    it('should support once option', () => {
      const handler = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler, { once: true })

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      eventBus.publish(event)
      eventBus.publish(event)

      expect(handler).toHaveBeenCalledTimes(1)
    })

    it('should support once() method', () => {
      const handler = vi.fn()

      eventBus.once(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      eventBus.publish(event)
      eventBus.publish(event)

      expect(handler).toHaveBeenCalledTimes(1)
    })

    it('should support filter option', () => {
      const handler = vi.fn()
      const filter = (event: any) => event.payload.ElementType === 'circle'

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler, { filter })

      const rectangleEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      const circleEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'circle', position: { x: 100, y: 100 } },
      }

      eventBus.publish(rectangleEvent)
      eventBus.publish(circleEvent)

      expect(handler).toHaveBeenCalledTimes(1)
      expect(handler).toHaveBeenCalledWith(circleEvent)
    })

    it('should support context option', () => {
      const context = { value: 42 }
      const handler = vi.fn(function (this: any, event: any) {
        return this.value
      })

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler, { context })

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      eventBus.publish(event)

      expect(handler).toHaveBeenCalledWith(event)
      expect(handler.mock.results[0].value).toBe(42)
    })
  })

  describe('async publishing', () => {
    it('should support async handlers', async () => {
      const handler = vi.fn().mockResolvedValue('result')

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler, { async: true })

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      await eventBus.publishAsync(event)

      expect(handler).toHaveBeenCalledWith(event)
    })

    it('should support emitAsync alias', async () => {
      const handler = vi.fn().mockResolvedValue('result')

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler, { async: true })

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      await eventBus.emitAsync(event)

      expect(handler).toHaveBeenCalledWith(event)
    })

    it('should handle errors in async handlers', async () => {
      const error = new Error('Async error')
      const handler = vi.fn().mockRejectedValue(error)

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler, { async: true })

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      const results = await eventBus.publishAsync(event)

      expect(handler).toHaveBeenCalledWith(event)
      expect(results[0].status).toBe('rejected')
      expect(consoleSpy.error).toHaveBeenCalled()
    })
  })

  describe('error handling', () => {
    it('should catch errors in event handlers', () => {
      const error = new Error('Handler error')
      const handler = vi.fn().mockImplementation(() => {
        throw error
      })

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      // Should not throw
      expect(() => eventBus.publish(event)).not.toThrow()

      expect(handler).toHaveBeenCalledWith(event)
      expect(consoleSpy.error).toHaveBeenCalled()
    })

    it('should handle invalid events', () => {
      const handler = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const invalidEvent = null
      const result = eventBus.publish(invalidEvent)

      expect(result).toBe(false)
      expect(handler).not.toHaveBeenCalled()
      expect(consoleSpy.error).toHaveBeenCalled()
    })
  })

  describe('configuration', () => {
    it('should configure the event bus', () => {
      eventBus.configure({
        enableLogging: true,
        defaultPriority: 10,
        maxAsyncHandlers: 50,
        defaultDebounceTime: 500,
        defaultThrottleTime: 500,
      })

      const handler = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      eventBus.publish(event)

      expect(handler).toHaveBeenCalledWith(event)
      expect(consoleSpy.log).toHaveBeenCalled()
    })

    it('should reset the event bus', () => {
      const handler = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)
      eventBus.configure({ enableLogging: true })

      eventBus.reset()

      const event = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle', position: { x: 100, y: 100 } },
      }

      eventBus.publish(event)

      expect(handler).not.toHaveBeenCalled()
    })
  })

  describe('utility methods', () => {
    it('should get all subscriptions', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler1)
      eventBus.subscribe(AppEventType.SHAPE_DELETE_REQUEST, handler2)

      const subscriptions = eventBus.getSubscriptions()

      expect(subscriptions.size).toBe(2)
      expect(subscriptions.has(AppEventType.SHAPE_CREATE_REQUEST)).toBe(true)
      expect(subscriptions.has(AppEventType.SHAPE_DELETE_REQUEST)).toBe(true)
    })
  })
})
