```mermaid
flowchart LR
    subgraph UserInteraction["User Interaction Layer"]
        direction TB
        MouseInput["Mouse/Keyboard Input (e.g., Draw, Select, Move)"] -->|Triggers Action| CanvasComponent["Canvas Component (src/components/Canvas)"]
        ToolbarInteraction["Toolbar Interaction (e.g., Select Tool, Color Change)"] -->|Updates State/Triggers Action| ToolbarComponent["Toolbar Component (src/components/Toolbar)"]
    end

    subgraph ApplicationLogic["Application Logic Layer"]
        direction TB
        CanvasComponent -->|Calls Action| ShapeActions["Shape Actions (in Zustand Store / Core Logic)"]
        ToolbarComponent -->|Calls Action| UIActions["UI Actions (in Zustand Store)"]
        
        ShapeActions -->|Modifies State| ZustandShapeStore["Zustand Shape Store (src/store/useShapeStore)"]
        UIActions -->|Modifies State| ZustandUIStore["Zustand UI Store (src/store/useUIStore)"]
        
        ZustandShapeStore -- "Notifies Subscribers" --> RenderingEngine
        ZustandUIStore -- "Notifies Subscribers" --> RenderingEngine
        
        ShapeActions -->|May use| CoreShapeLogic["Core Shape Logic (src/core/shapes, transformations)"]
        CoreShapeLogic -->|Returns Data| ShapeActions

        subgraph HistoryManagement["Undo/Redo Logic"]
            ShapeActions -->|Records Action| HistoryService["History Service (src/services/historyService)"]
            HistoryService -->|Updates State| ZustandHistoryStore["Zustand History Store (src/store/useHistoryStore)"]
            ToolbarInteraction["Undo/Redo Buttons"] -->|Triggers| HistoryService
            ZustandHistoryStore -- "Notifies Subscribers" --> RenderingEngine
        end
    end

    subgraph RenderingLayer["Rendering Layer"]
        direction TB
        RenderingEngine["React Rendering Engine"] -->|Updates DOM| BrowserDisplay["Browser Display"]
    end

    subgraph DataPersistence["Data Persistence (Optional)"]
        direction TB
        ZustandShapeStore -->|On Save Action| LocalStorageService["Local Storage Service (src/services/storageService)"]
        LocalStorageService -->|Writes/Reads| BrowserLocalStorage["Browser Local Storage"]
        BrowserLocalStorage -->|On Load Action| LocalStorageService
        LocalStorageService -->|Restores State| ZustandShapeStore
    end

    %% Styling
    classDef component fill:#61DAFB,stroke:#333,stroke-width:2px;
    classDef store fill:#FFD700,stroke:#333,stroke-width:2px;
    classDef service fill:#4CAF50,stroke:#333,stroke-width:2px;
    classDef logic fill:#3178C6,stroke:#FFF,stroke-width:2px,color:#FFF;
    classDef browser fill:#E44D26,stroke:#333,stroke-width:2px;

    class MouseInput,ToolbarInteraction,CanvasComponent,ToolbarComponent component;
    class ShapeActions,UIActions,ZustandShapeStore,ZustandUIStore,ZustandHistoryStore store;
    class HistoryService,LocalStorageService service;
    class CoreShapeLogic logic;
    class RenderingEngine,BrowserDisplay,BrowserLocalStorage browser;
```