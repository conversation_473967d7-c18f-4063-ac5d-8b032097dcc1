import type { DistanceCalculatorStrategy } from '@/types/core/compute/distanceComputeTypes'
import type { Element } from '@/types/core/elementDefinitions'
import { describe, expect, it } from 'vitest'

describe('distance Calculator Strategy Interface', () => {
  it('should define the required methods for distance calculation strategies', () => {
    // Create a mock implementation of DistanceCalculatorStrategy
    class MockDistanceCalculator implements DistanceCalculatorStrategy {
      calculateDistance(elementA: Element, elementB: Element): number {
        // Simple mock implementation
        return 100
      }

      getElementType(): string {
        return 'mock-element'
      }
    }

    const calculator = new MockDistanceCalculator()

    // Test the interface implementation
    expect(typeof calculator.calculateDistance).toBe('function')
    expect(typeof calculator.getElementType).toBe('function')

    // Test the mock implementation
    const mockElementA = { id: 'element-a' } as Element
    const mockElementB = { id: 'element-b' } as Element
    expect(calculator.calculateDistance(mockElementA, mockElementB)).toBe(100)
    expect(calculator.getElementType()).toBe('mock-element')
  })

  it('should allow implementation for different shape types', () => {
    // Create a mock rectangle distance calculator
    class MockRectangleDistanceCalculator implements DistanceCalculatorStrategy {
      calculateDistance(elementA: Element, elementB: Element): number {
        // Mock implementation for rectangles
        return 50
      }

      getElementType(): string {
        return 'rectangle'
      }
    }

    // Create a mock circle distance calculator
    class MockCircleDistanceCalculator implements DistanceCalculatorStrategy {
      calculateDistance(elementA: Element, elementB: Element): number {
        // Mock implementation for circles
        return 75
      }

      getElementType(): string {
        return 'circle'
      }
    }

    const rectangleCalculator = new MockRectangleDistanceCalculator()
    const circleCalculator = new MockCircleDistanceCalculator()

    // Test the implementations
    const mockElementA = { id: 'element-a' } as Element
    const mockElementB = { id: 'element-b' } as Element

    expect(rectangleCalculator.calculateDistance(mockElementA, mockElementB)).toBe(50)
    expect(rectangleCalculator.getElementType()).toBe('rectangle')

    expect(circleCalculator.calculateDistance(mockElementA, mockElementB)).toBe(75)
    expect(circleCalculator.getElementType()).toBe('circle')
  })

  it('should allow for complex distance calculation implementations', () => {
    // Create a mock implementation with more complex logic
    class MockComplexDistanceCalculator implements DistanceCalculatorStrategy {
      calculateDistance(elementA: Element, elementB: Element): number {
        // Mock implementation that simulates calculating distance based on positions
        const positionA = { x: 0, y: 0 } // Simulated position for elementA
        const positionB = { x: 30, y: 40 } // Simulated position for elementB

        // Calculate Euclidean distance
        const dx = positionB.x - positionA.x
        const dy = positionB.y - positionA.y
        return Math.sqrt(dx * dx + dy * dy)
      }

      getElementType(): string {
        return 'complex'
      }
    }

    const complexCalculator = new MockComplexDistanceCalculator()

    // Test the implementation
    const mockElementA = { id: 'element-a' } as Element
    const mockElementB = { id: 'element-b' } as Element

    // Should calculate distance as sqrt(30^2 + 40^2) = 50
    expect(complexCalculator.calculateDistance(mockElementA, mockElementB)).toBe(50)
    expect(complexCalculator.getElementType()).toBe('complex')
  })

  it('should allow for error handling in implementations', () => {
    // Create a mock distance calculator with error handling
    class MockErrorHandlingCalculator implements DistanceCalculatorStrategy {
      calculateDistance(elementA: Element, elementB: Element): number {
        // Check if elements have required properties
        if (!elementA?.id) {
          throw new Error('Invalid first element')
        }

        if (!elementB?.id) {
          throw new Error('Invalid second element')
        }

        // Check if elements are the same
        if (elementA.id === elementB.id) {
          throw new Error('Cannot calculate distance between the same element')
        }

        // Mock implementation
        return 25
      }

      getElementType(): string {
        return 'error-handling-element'
      }
    }

    const errorHandlingCalculator = new MockErrorHandlingCalculator()

    // Test valid case
    const validElementA = { id: 'element-a' } as Element
    const validElementB = { id: 'element-b' } as Element
    expect(errorHandlingCalculator.calculateDistance(validElementA, validElementB)).toBe(25)

    // Test error cases
    const invalidElement = {} as Element
    expect(() => errorHandlingCalculator.calculateDistance(invalidElement, validElementB)).toThrow('Invalid first element')
    expect(() => errorHandlingCalculator.calculateDistance(validElementA, invalidElement)).toThrow('Invalid second element')
    expect(() => errorHandlingCalculator.calculateDistance(validElementA, validElementA)).toThrow('Cannot calculate distance between the same element')
  })
})
