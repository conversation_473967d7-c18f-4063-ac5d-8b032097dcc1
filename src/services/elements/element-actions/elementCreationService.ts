// cspell:ignore uuidv4
import type { ElementFactory, PathCreationOptionsUnion, ShapeCreationParamsUnion } from '@/core/factory'
import type { PointData } from '@/types/core/element/geometry/point'
import type { ShapeElement as ShapeModel } from '@/types/core/elementDefinitions'
import type { ValidatableShape } from '@/types/core/validator/validator-interface'
import type { ErrorContext as CoreErrorContext } from '@/types/services/errors'
import type { AppEventMap, EventBus } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import type {
  ElementCreationRequest,
  ElementCreationResult,
  ElementCreationService as IElementCreationService,
} from '@/types/services/shapes'

import { v4 as uuidv4 } from 'uuid'

import { getDefaultSettingsForElementType } from '@/config/defaultElementSettings' // InitialElementProperties removed as unused
import { ElementValidator } from '@/core/validator'
import { safeExecuteAsync } from '@/lib/utils/errorUtils'
import { PointClass } from '@/lib/utils/geometry/PointClass'
import { getService, ServiceId } from '@/services/core/registry'
import { ElementType } from '@/types/core/elementDefinitions'
import { ErrorType as CoreErrorType } from '@/types/services/errors'
import { AppEventType } from '@/types/services/events'

/**
 * Defines the payload for an element creation request event.
 * @interface ElementCreateEventPayload
 */
export interface ElementCreateEventPayload {
  /** The type of element to create. */
  elementType: ElementType
  /** The initial position for the new element. Can be a simple object or a PointClass instance. */
  position: { x: number, y: number } | PointClass
  /** Optional additional properties for the element. */
  properties?: Record<string, unknown>
}

/**
 * Defines the structure of an event that requests element creation.
 * @interface ElementCreateEvent
 */
export interface ElementCreateEvent {
  /** The type of the event, typically corresponding to an {@link AppEventType}. */
  type: string
  /** The payload containing details for element creation. */
  payload: ElementCreateEventPayload
  /** Optional timestamp of when the event was created. */
  timestamp?: number
}

/**
 * Enumerates specific error types that can occur during element creation.
 * @enum {string}
 */
enum ElementCreationErrorType {
  /** Indicates that the payload provided for element creation was invalid or incomplete. */
  InvalidPayload = 'INVALID_PAYLOAD',
  /** Indicates that the element data failed validation against predefined rules. */
  ValidationFailed = 'VALIDATION_FAILED',
  /** Indicates an error occurred within the ElementFactory during element instantiation. */
  FactoryCreationFailed = 'FACTORY_CREATION_FAILED',
  /** Indicates a general failure in the coordination of the element creation process. */
  CoordinatorOperationFailed = 'COORDINATOR_OPERATION_FAILED',
}

// Removed unused ShapeCreationError class
// enum ElementCreationErrorType is used for error codes in emitError

/**
 * Service responsible for orchestrating the creation of new shapes.
 *
 * @remarks
 * This service handles the entire lifecycle of shape creation, including:
 * 1. Validating the input data for the new shape.
 * 2. Using the {@link ElementValidator} to prepare and validate shape data against specific rules.
 * 3. Employing the {@link ElementFactory} to instantiate the {@link ShapeModel}.
 * 4. Adding the newly created shape to the {@link ShapeRepository}.
 * 5. Publishing success or error events via the {@link EventBus}.
 *
 * The service supports various shape types including rectangles, circles, ellipses, polygons,
 * and custom paths. It ensures proper validation, default property application, and event
 * publishing for successful shape creation or error handling.
 *
 * @example
 * ```typescript
 * const service = ElementCreationService.create(factory, logger);
 * await service.handleRequest({
 *   type: 'SHAPE_CREATE',
 *   payload: {
 *     elementType: ElementType.RECTANGLE,
 *     position: { x: 100, y: 100 },
 *     properties: { width: 200, height: 150 }
 *   }
 * });
 * ```
 *
 * @see {@link ElementFactory}
 * @see {@link ShapeRepository}
 * @see {@link ElementValidator}
 * @see {@link EventBus}
 * @see {@link LoggerService}
 *
 * @module services/elements/element-actions/elementCreationService
 */
export class ElementCreationService implements IElementCreationService {
  readonly serviceId: string = ServiceId.ElementCreationService as string
  /**
   * Creates an instance of ElementCreationService.
   *
   * @param {ElementFactory} factory - The factory instance used for creating shape elements.
   * @param {EventBus} eventBus - The application's event bus for publishing creation events.
   * @param {LoggerService} logger - The logger service for recording service activities.
   */
  constructor(
    private factory: ElementFactory,
    private eventBus: EventBus<AppEventMap>,
    private logger: LoggerService,
  ) {
    this.logger.info('[ElementCreationService] Initialized.')
    this.logger.debug('[ElementCreationService CONSTRUCTOR] Initialized with factory, eventBus, logger.')
  }

  /**
   * Factory method to create a {@link ElementCreationService} instance.
   *
   * @remarks
   * This method attempts to resolve dependencies like {@link EventBus} and {@link LoggerService}
   * from the service registry. The {@link ElementFactory} must be provided.
   *
   * @param {ElementFactory} [factory] - The element factory instance. Must be provided.
   * @param {LoggerService} [logger] - Optional logger service. If not provided, it's retrieved from the registry.
   * @returns {ElementCreationService} A new instance of the ElementCreationService.
   * @throws {Error} If `factory` is not provided, or if dependencies cannot be resolved.
   */
  public static create(
    factory?: ElementFactory,
    logger?: LoggerService,
  ): ElementCreationService {
    const tempLogger = logger || getService<LoggerService>(ServiceId.Logger)
    tempLogger.debug('[ElementCreationService CREATE_STATIC] Attempting to create instance.')
    try {
      const eventBus = getService<EventBus<AppEventMap>>(ServiceId.EventBus)
      const loggerService = logger || getService<LoggerService>(ServiceId.Logger)

      if (!factory) {
        throw new Error('ElementFactory must be provided to create ElementCreationService.')
      }
      loggerService.debug('[ElementCreationService CREATE_STATIC] Dependencies resolved. Creating new instance.')
      return new ElementCreationService(factory, eventBus, loggerService)
    }
    catch (error) {
      const serviceName = ElementCreationService.name
      tempLogger.error(`[${serviceName} CREATE_STATIC] Failed to create instance:`, error instanceof Error ? error.message : String(error))
      throw new Error(`[${serviceName}] Failed to create instance: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * Handles an incoming request to create a new shape via an event.
   *
   * @param {ElementCreateEvent} event - The event object containing the details for the shape to be created.
   * @returns {Promise<void>} A promise that resolves when the request has been processed.
   */
  public async handleRequest(
    event: ElementCreateEvent,
  ): Promise<void> {
    const operation = 'handleElementCreationRequest'
    this.logger.debug(`[ElementCreationService handleRequest] Received event type: ${event.type}, payload:`, JSON.stringify(event.payload, null, 2))
    const { payload } = event

    const safeExecuteErrorContext: CoreErrorContext = {
      component: 'ElementCreationService',
      operation,
      metadata: { eventType: event.type, payload },
    }

    await safeExecuteAsync(async () => {
      if (payload?.elementType === undefined || payload?.elementType === null || payload?.position === undefined || payload?.position === null) {
        this.logger.error('Invalid or missing payload in ElementCreateEvent', payload)
        this.logger.debug('[ElementCreationService handleRequest] Invalid or missing payload:', JSON.stringify(payload, null, 2))
        this.emitError(ElementCreationErrorType.InvalidPayload, 'Shape creation payload is invalid.', safeExecuteErrorContext)
        return
      }

      const creationRequest: ElementCreationRequest = {
        elementType: payload.elementType,
        position: payload.position,
        properties: payload.properties,
      }

      const result = await this.createShapeInternal(creationRequest, operation, payload.elementType.toString())
      if (result.success === false) {
        const errorMessage = typeof result.error === 'string' ? result.error : result.error?.message
        this.logger.error(`Failed to handle shape creation request due to internal error: ${errorMessage}`, result.error)
        this.logger.error(`[ElementCreationService handleRequest] createShapeInternal failed: ${errorMessage}`, result.error !== undefined ? JSON.stringify(result.error, null, 2) : 'Unknown error')
      }
    }, safeExecuteErrorContext)
  }

  private async createShapeInternal(
    request: ElementCreationRequest,
    operationName: string,
    triggeringEventType?: string,
  ): Promise<ElementCreationResult> {
    this.logger.debug(`[ElementCreationService createShapeInternal ENTER] Operation: ${operationName}, EventType: ${triggeringEventType}, Request:`, JSON.stringify(request, null, 2))
    const { elementType: originalElementType, position, properties: rawProperties } = request

    // Ensure elementType is treated as uppercase for consistency with ElementType enum keys
    const elementType = originalElementType.toUpperCase() as ElementType
    this.logger.debug(`[ElementCreationService createShapeInternal] Original ElementType: ${originalElementType}, Uppercased ElementType: ${elementType}`)

    const id = (typeof rawProperties?.id === 'string' && rawProperties.id.length > 0) ? rawProperties.id : `${elementType}-${uuidv4()}`
    this.logger.debug(`[ElementCreationService createShapeInternal] Generated ID: ${id} for elementType: ${elementType}`)

    const currentDefaults = getDefaultSettingsForElementType(elementType)
    this.logger.debug(`[ElementCreationService createShapeInternal] Defaults loaded EARLY for ${elementType}:`, JSON.stringify(currentDefaults, null, 2))

    const baseContext: CoreErrorContext = {
      component: 'ElementCreationService',
      operation: operationName,
      metadata: { elementType, targetId: id, triggeringEventType, requestProperties: rawProperties },
    }

    try {
      this.logger.debug?.(`[${operationName}] ENTERING createShapeInternal. elementType: ${elementType}, rawProperties:`, JSON.stringify(rawProperties))
      this.logger.debug?.(`[${operationName}] Creating shape: ${elementType} with id: ${id}`, request)

      let positionInstance: PointClass
      try {
        positionInstance = position instanceof PointClass
          ? position
          : new PointClass(position.x, position.y)
        // Log plain object for PointClass
        this.logger.debug(`[ElementCreationService createShapeInternal] PositionInstance created:`, JSON.stringify({ x: positionInstance.x, y: positionInstance.y, z: positionInstance.z }, null, 2))
      }
      catch (posError: unknown) {
        const errorMessage = posError instanceof Error ? posError.message : String(posError)
        this.logger.error(`[${operationName}] Invalid position data for shape ${id}:`, posError)
        this.logger.error(`[ElementCreationService createShapeInternal] Invalid position data for shape ${id}:`, errorMessage)
        this.emitError(ElementCreationErrorType.ValidationFailed, `Invalid position data: ${errorMessage}`, { ...baseContext, metadata: { ...baseContext.metadata, originalError: posError } })
        return { success: false, error: { code: ElementCreationErrorType.ValidationFailed, message: `Invalid position data: ${errorMessage}`, details: posError }, timestamp: Date.now() }
      }

      // MOVED baseParams INITIALIZATION BLOCK
      const baseParams: Record<string, unknown> = {
        id,
        metadata: (rawProperties?.metadata !== undefined && rawProperties.metadata !== null) ? rawProperties.metadata : { createdAt: Date.now(), updatedAt: Date.now() },
        visible: rawProperties?.visible ?? currentDefaults.visible ?? true,
        locked: rawProperties?.locked ?? currentDefaults.locked ?? false,
        rotation: rawProperties?.rotation ?? currentDefaults.rotation ?? 0,
        selectable: rawProperties?.selectable ?? currentDefaults.selectable ?? true,
        draggable: rawProperties?.draggable ?? currentDefaults.draggable ?? true,
        showHandles: rawProperties?.showHandles ?? currentDefaults.showHandles ?? true,
        layer: rawProperties?.layer ?? currentDefaults.layer, // Layer can be undefined
        zIndex: rawProperties?.zIndex, // Use zIndex from rawProperties if available (EditorLayout provides this)
        opacity: rawProperties?.opacity ?? currentDefaults.opacity ?? 1,
        // Style properties - get from raw or defaults, then delete if undefined to let creator defaults apply
        fill: rawProperties?.fill ?? currentDefaults.fill,
        stroke: rawProperties?.stroke ?? currentDefaults.stroke,
        strokeWidth: rawProperties?.strokeWidth ?? currentDefaults.strokeWidth,
        strokeDasharray: rawProperties?.strokeDasharray ?? currentDefaults.strokeDasharray,
        // --- Add Layer Panel Integration Properties ---
        majorCategory: rawProperties?.majorCategory, // Get from rawProperties
        minorCategory: rawProperties?.minorCategory, // Get from rawProperties
        zLevelId: rawProperties?.zLevelId, // Get from rawProperties
        isFixedCategory: rawProperties?.isFixedCategory, // Get from rawProperties
        // Add other common base properties here, following the rawProperties ?? currentDefaults.property ?? fallback pattern
      }

      // Log rawProperties and the just-formed baseParams for layer prop check
      this.logger.debug(`[ElementCreationService createShapeInternal] CHECKING rawProperties for layer props:`, JSON.stringify({
        majorCategory: String(rawProperties?.majorCategory ?? 'undefined'),
        minorCategory: String(rawProperties?.minorCategory ?? 'undefined'),
        zLevelId: String(rawProperties?.zLevelId ?? 'undefined'),
        isFixedCategory: String(rawProperties?.isFixedCategory ?? 'undefined'),
        zIndex: String(rawProperties?.zIndex ?? 'undefined'),
        fill: String(rawProperties?.fill ?? 'undefined'), // for comparison
      }, null, 2))
      this.logger.debug(`[ElementCreationService createShapeInternal] CHECKING baseParams for layer props:`, JSON.stringify({
        majorCategory: String(baseParams?.majorCategory ?? 'undefined'),
        minorCategory: String(baseParams?.minorCategory ?? 'undefined'),
        zLevelId: String(baseParams?.zLevelId ?? 'undefined'),
        isFixedCategory: String(baseParams?.isFixedCategory ?? 'undefined'),
        zIndex: String(baseParams?.zIndex ?? 'undefined'),
        fill: String(baseParams?.fill ?? 'undefined'), // for comparison
      }, null, 2))

      // If zIndex is still undefined after checking rawProperties, then consider currentDefaults
      if (baseParams.zIndex === undefined && currentDefaults.zIndex !== undefined) {
        baseParams.zIndex = currentDefaults.zIndex
      }

      // If style properties are still undefined after checking raw and defaults, remove them
      // so that creator-specific defaults can take precedence.
      if (baseParams.fill === undefined)
        delete baseParams.fill
      if (baseParams.stroke === undefined)
        delete baseParams.stroke
      if (baseParams.strokeWidth === undefined)
        delete baseParams.strokeWidth
      if (baseParams.strokeDasharray === undefined)
        delete baseParams.strokeDasharray
      if (baseParams.layer === undefined)
        delete baseParams.layer // explicitly remove if still undefined
      if (baseParams.zIndex === undefined)
        delete baseParams.zIndex // explicitly remove if still undefined
      // END OF MOVED baseParams INITIALIZATION BLOCK

      // Initialize propertiesForValidation, ensuring it's an object.
      // Order: Start with defaults from currentDefaults.properties,
      // then override with anything from a nested rawProperties.properties,
      // finally override with any flat properties from rawProperties.
      const propertiesForValidation: Record<string, unknown> = {
        ...(currentDefaults.properties !== undefined && currentDefaults.properties !== null ? currentDefaults.properties : {}),
        ...(rawProperties?.properties !== undefined && rawProperties?.properties !== null ? rawProperties.properties as Record<string, unknown> : {}),
        ...(rawProperties !== undefined && rawProperties !== null ? rawProperties : {}),
      }
      // Remove common top-level style properties from propertiesForValidation if they were duplicated by spreading rawProperties,
      // as they are handled by baseParams.
      delete propertiesForValidation.fill
      delete propertiesForValidation.stroke
      delete propertiesForValidation.strokeWidth
      delete propertiesForValidation.opacity
      delete propertiesForValidation.strokeDasharray
      // Also remove transform/interaction properties that are top-level on ShapeModel
      delete propertiesForValidation.position
      delete propertiesForValidation.rotation
      delete propertiesForValidation.visible
      delete propertiesForValidation.locked
      delete propertiesForValidation.selectable
      delete propertiesForValidation.draggable
      delete propertiesForValidation.showHandles
      delete propertiesForValidation.layer
      delete propertiesForValidation.zIndex
      delete propertiesForValidation.id
      delete propertiesForValidation.type
      delete propertiesForValidation.metadata
      delete propertiesForValidation.majorCategory
      delete propertiesForValidation.minorCategory

      // Add specific geometric properties from rawProperties to propertiesForValidation
      if (elementType === ElementType.ELLIPSE || elementType === ElementType.CIRCLE) {
        propertiesForValidation.radiusX = rawProperties?.radiusX ?? currentDefaults.radiusX
        propertiesForValidation.radiusY = rawProperties?.radiusY ?? currentDefaults.radiusY

        if (elementType === ElementType.CIRCLE) {
          const circleRadius = (typeof rawProperties?.radius === 'number') ? rawProperties.radius : (typeof currentDefaults.radius === 'number') ? currentDefaults.radius : undefined
          propertiesForValidation.radius = circleRadius
          // Ensure radiusX and radiusY are consistent with radius for Circle if they are also used by validator/creator
          if (propertiesForValidation.radiusX === undefined && circleRadius !== undefined)
            propertiesForValidation.radiusX = circleRadius
          if (propertiesForValidation.radiusY === undefined && circleRadius !== undefined)
            propertiesForValidation.radiusY = circleRadius
          // If radiusX/Y were defined but radius wasn't, and it's a circle, make them equal
          if (circleRadius === undefined && propertiesForValidation.radiusX !== undefined && propertiesForValidation.radiusY === undefined)
            propertiesForValidation.radiusY = propertiesForValidation.radiusX
          if (circleRadius === undefined && propertiesForValidation.radiusY !== undefined && propertiesForValidation.radiusX === undefined)
            propertiesForValidation.radiusX = propertiesForValidation.radiusY
          // If both radiusX and Y defined but not radius, for a circle they must be equal. Default to radiusX if different.
          if (circleRadius === undefined && propertiesForValidation.radiusX !== propertiesForValidation.radiusY) {
            this.logger.warn(`[${operationName}] Circle ${id} has differing radiusX/radiusY from defaults/raw. Using radiusX.`)
            propertiesForValidation.radiusY = propertiesForValidation.radiusX
          }
          if (propertiesForValidation.radius === undefined && propertiesForValidation.radiusX !== undefined) { // If radius still not set, but radiusX is
            propertiesForValidation.radius = propertiesForValidation.radiusX
          }

          if (propertiesForValidation.radius === undefined) {
            this.logger.warn(`[${operationName}] Circle radius is undefined for ${id} after checking rawProperties and defaults. Validator might fail.`)
          }
        }
        if (propertiesForValidation.radiusX === undefined && elementType === ElementType.ELLIPSE) {
          this.logger.warn(`[${operationName}] Ellipse radiusX is undefined for ${id}. Validator might fail.`)
        }
        if (propertiesForValidation.radiusY === undefined && elementType === ElementType.ELLIPSE) {
          this.logger.warn(`[${operationName}] Ellipse radiusY is undefined for ${id}. Validator might fail.`)
        }
      }
      else if (elementType === ElementType.RECTANGLE || elementType === ElementType.SQUARE) {
        propertiesForValidation.width = rawProperties?.width ?? currentDefaults.width
        propertiesForValidation.height = rawProperties?.height ?? currentDefaults.height

        if (elementType === ElementType.SQUARE) {
          const side = propertiesForValidation.width ?? propertiesForValidation.height
          if (side !== undefined) {
            propertiesForValidation.width = side
            propertiesForValidation.height = side
          }
          if (propertiesForValidation.width === undefined) { // Still undefined implies both were undefined
            this.logger.warn(`[${operationName}] Square side length is undefined for ${id} after checking rawProperties and defaults. Validator might fail.`)
          }
        }
        propertiesForValidation.cornerRadius = rawProperties?.cornerRadius ?? currentDefaults.cornerRadius ?? 0 // Keep this fallback

        if (propertiesForValidation.width === undefined && elementType === ElementType.RECTANGLE) {
          this.logger.warn(`[${operationName}] Rectangle width is undefined for ${id}. Validator might fail.`)
        }
        if (propertiesForValidation.height === undefined && elementType === ElementType.RECTANGLE) {
          this.logger.warn(`[${operationName}] Rectangle height is undefined for ${id}. Validator might fail.`)
        }
      }
      else if (elementType === ElementType.POLYGON || elementType === ElementType.TRIANGLE || elementType === ElementType.QUADRILATERAL || elementType === ElementType.PENTAGON || elementType === ElementType.HEXAGON) {
        this.logger.debug?.(`[${operationName}] Polygon-like type. Raw: sides=${String(rawProperties?.sides ?? 'undefined')}, radius=${String(rawProperties?.radius ?? 'undefined')}, isRegular=${String(rawProperties?.isRegular ?? 'undefined')}, points_provided=${rawProperties?.points !== undefined && rawProperties?.points !== null}. Defaults: sides=${String(currentDefaults.sides ?? 'undefined')}, radius=${String(currentDefaults.radius ?? 'undefined')}, isRegular=${String(currentDefaults.isRegular ?? 'undefined')}`)

        let validationSides: number
        if (rawProperties?.sides !== undefined) {
          validationSides = Number(rawProperties.sides)
          this.logger.debug?.(`[${operationName}] Validation sides: Using rawProperties.sides: ${validationSides} for ${elementType}`)
        }
        else if (currentDefaults.sides !== undefined) {
          validationSides = Number(currentDefaults.sides)
          this.logger.debug?.(`[${operationName}] Validation sides: No rawProperties.sides, using currentDefaults.sides: ${validationSides} for ${elementType}`)
        }
        else {
          // Fallback if neither rawProperties nor currentDefaults define sides
          validationSides = (elementType === ElementType.TRIANGLE)
            ? 3
            : (elementType === ElementType.QUADRILATERAL)
                ? 4
                : (elementType === ElementType.PENTAGON)
                    ? 5
                    : (elementType === ElementType.HEXAGON) ? 6 : 4 // Default to 4 for generic POLYGON or other
          this.logger.warn(`[${operationName}] Validation sides: ${elementType} missing rawProperties.sides AND currentDefaults.sides. Defaulting to ${validationSides} sides.`)
        }
        propertiesForValidation.sides = validationSides

        propertiesForValidation.radius = rawProperties?.radius ?? currentDefaults.radius ?? 50 // Use currentDefaults for specific type (e.g. Triangle)
        propertiesForValidation.isRegular = rawProperties?.isRegular ?? currentDefaults.isRegular ?? true
        const startAngleRad = (typeof rawProperties?.startAngle === 'number') ? rawProperties.startAngle : (typeof currentDefaults.startAngle === 'number') ? currentDefaults.startAngle : undefined

        if (rawProperties?.points !== undefined && rawProperties?.points !== null && Array.isArray(rawProperties.points) && rawProperties.points.length > 0) {
          this.logger.debug(`[${operationName}] Using provided points for polygon ${id}. Length: ${rawProperties.points.length}`)
          propertiesForValidation.points = rawProperties.points.map((p: PointData) => ({ x: p.x, y: p.y, z: p.z }))
          // Infer sides from points if not explicitly set or if different (e.g. for closed polygons)
          const points = rawProperties.points as PointData[]
          const inferredSides = points.length > 1
            && points[0].x === points[points.length - 1].x
            && points[0].y === points[points.length - 1].y
            ? points.length - 1
            : points.length
          if (propertiesForValidation.sides === undefined || propertiesForValidation.sides !== inferredSides) {
            this.logger.debug(`[${operationName}] Inferring sides (${inferredSides}) from provided points for ${id}. Original sides: ${String(propertiesForValidation.sides)}`)
            propertiesForValidation.sides = inferredSides
          }
          // If points are provided, isRegular might be implicitly false unless specified
          if (rawProperties.isRegular === undefined && currentDefaults.isRegular === undefined) {
            propertiesForValidation.isRegular = false // Default to false if points are manually given and no explicit isRegular
          }
        }
        else if (propertiesForValidation.sides !== undefined && propertiesForValidation.sides !== null && propertiesForValidation.radius !== undefined && propertiesForValidation.radius !== null) {
          this.logger.debug(`[${operationName}] Generating points for polygon ${id} from sides=${String(propertiesForValidation.sides)}, radius=${String(propertiesForValidation.radius)}, isRegular=${String(propertiesForValidation.isRegular)}`)
          const numSides = Number(propertiesForValidation.sides)
          const rad = Number(propertiesForValidation.radius)
          const tempPoints: PointData[] = []
          const angleStep = (2 * Math.PI) / numSides
          // Use the startAngle for point generation if available (from defaults or raw), else default to -PI/2 (point up)
          const effectiveStartAngle = (typeof startAngleRad === 'number') ? (startAngleRad * Math.PI / 180) : -Math.PI / 2

          // 使用鼠标落点位置作为多边形的中心点
          const tempCenter = {
            x: positionInstance.x, // 直接使用鼠标落点的位置作为中心点
            y: positionInstance.y,
            z: positionInstance.z,
          }

          // 记录中心点位置，便于调试
          this.logger.debug('[ElementCreationService] 多边形中心点(鼠标落点位置):', tempCenter)

          // 首先生成相对于原点(0,0)的顶点坐标
          for (let i = 0; i < numSides; i++) {
            const angle = effectiveStartAngle + (i * angleStep)
            tempPoints.push({
              x: rad * Math.cos(angle),
              y: rad * Math.sin(angle),
              z: tempCenter.z,
            })
          }

          // 计算几何中心
          let sumX = 0
          let sumY = 0
          for (const point of tempPoints) {
            sumX += point.x
            sumY += point.y
          }
          const centroidX = sumX / numSides
          const centroidY = sumY / numSides

          this.logger.debug(`[ElementCreationService] 原始几何中心: (${centroidX}, ${centroidY})`)

          // 调整顶点坐标，使几何中心与原点(0,0)重合
          const adjustedPoints: PointData[] = []
          for (const point of tempPoints) {
            adjustedPoints.push({
              x: point.x - centroidX,
              y: point.y - centroidY,
              z: point.z,
            })
          }

          // 使用调整后的点替换原始点
          tempPoints.length = 0
          for (const point of adjustedPoints) {
            tempPoints.push(point)
          }

          this.logger.debug(`[ElementCreationService] 调整后的点，确保几何中心在(0,0)：`, tempPoints)
          // Close the polygon for validation if it's meant to be closed (usually true for these types)
          if (tempPoints.length > 0) {
            tempPoints.push({ ...tempPoints[0] }) // Ensure a new object for the closing point
          }
          propertiesForValidation.points = tempPoints
          this.logger.debug?.(`[${operationName}] Generated points for validation (closed):`, JSON.stringify(tempPoints))
        }
        else {
          this.logger.warn(`[${operationName}] Polygon ${id} lacks points and sufficient (sides/radius) to generate them for validation. Sides: ${String(propertiesForValidation.sides)}, Radius: ${String(propertiesForValidation.radius)}`)
        }
        if (propertiesForValidation.sides === undefined) { // Should be caught by earlier logic, but as a safeguard
          this.logger.warn(`[${operationName}] Polygon sides is undefined for ${id} AT THE END OF VALIDATION PREP. Validator might fail.`)
        }
      }
      else if (elementType === ElementType.LINE) {
        this.logger.debug(`[${operationName}] Populating/defaulting properties for LINE. Raw: start=${JSON.stringify(rawProperties?.start)}, end=${JSON.stringify(rawProperties?.end)}, Defaults: start=${JSON.stringify(currentDefaults.start)}, end=${JSON.stringify(currentDefaults.end)}`)
        propertiesForValidation.start = rawProperties?.start ?? currentDefaults.start
        propertiesForValidation.end = rawProperties?.end ?? currentDefaults.end
        propertiesForValidation.arrowStart = rawProperties?.arrowStart ?? currentDefaults.arrowStart ?? false
        propertiesForValidation.arrowEnd = rawProperties?.arrowEnd ?? currentDefaults.arrowEnd ?? false
        if (propertiesForValidation.start === undefined || propertiesForValidation.start === null || propertiesForValidation.end === undefined || propertiesForValidation.end === null) {
          this.logger.warn(`[${operationName}] LINE ${id} is missing start or end points for validation.`)
          // Potentially create default points if validator strictly needs them
          if (propertiesForValidation.start === undefined || propertiesForValidation.start === null)
            propertiesForValidation.start = { x: positionInstance.x, y: positionInstance.y, z: positionInstance.z }
          if (propertiesForValidation.end === undefined || propertiesForValidation.end === null)
            propertiesForValidation.end = { x: positionInstance.x + (currentDefaults.width ?? 100), y: positionInstance.y, z: positionInstance.z } // Default length of 100 if end missing
        }
      }
      else if (elementType === ElementType.POLYLINE) {
        const rawPointsLength = Array.isArray(rawProperties?.points) ? rawProperties?.points?.length ?? 0 : 'undefined'
        const defaultPointsLength = Array.isArray(currentDefaults.points) ? currentDefaults.points.length : 'undefined'
        this.logger.debug(`[${operationName}] Populating/defaulting properties for POLYLINE. Raw points count: ${rawPointsLength}, Default points count: ${defaultPointsLength}`)
        propertiesForValidation.points = rawProperties?.points ?? currentDefaults.points
        if (propertiesForValidation.points === undefined || propertiesForValidation.points === null || !Array.isArray(propertiesForValidation.points) || propertiesForValidation.points.length < 2) {
          this.logger.warn(`[${operationName}] POLYLINE ${id} has insufficient points for validation.`)
          // Create default points if missing/insufficient
          propertiesForValidation.points = currentDefaults.points && currentDefaults.points.length >= 2
            ? currentDefaults.points
            : [
                { x: positionInstance.x, y: positionInstance.y, z: positionInstance.z },
                { x: positionInstance.x + 50, y: positionInstance.y + 50, z: positionInstance.z },
              ]
        }
        // For Polyline, 'points' are fundamental.
        if ((propertiesForValidation.points === undefined || propertiesForValidation.points === null) && currentDefaults.points !== undefined && currentDefaults.points !== null) {
          propertiesForValidation.points = currentDefaults.points
          this.logger.debug?.(`[${operationName}] Polyline 'points' defaulted.`)
        }
        // Ensure 'curved' and 'tension' are present for validation if defined in defaults
        if (propertiesForValidation.curved === undefined && currentDefaults.curved !== undefined) {
          propertiesForValidation.curved = currentDefaults.curved
          this.logger.debug?.(`[${operationName}] Polyline 'curved' defaulted to ${currentDefaults.curved} for validation/factory.`)
        }
        if (propertiesForValidation.tension === undefined && currentDefaults.tension !== undefined) {
          propertiesForValidation.tension = currentDefaults.tension
          this.logger.debug?.(`[${operationName}] Polyline 'tension' defaulted to ${currentDefaults.tension} for validation/factory.`)
        }
      }
      else if (elementType === ElementType.ARC) {
        this.logger.debug(`[${operationName}] Populating/defaulting properties for ARC. Raw: radius=${String(rawProperties?.radius)}, startAngle=${String(rawProperties?.startAngle)}, endAngle=${String(rawProperties?.endAngle)}. Defaults: radius=${String(currentDefaults.radius)}, startAngle=${String(currentDefaults.startAngle)}, endAngle=${String(currentDefaults.endAngle)}`)
        propertiesForValidation.radius = rawProperties?.radius ?? currentDefaults.radius
        propertiesForValidation.startAngle = rawProperties?.startAngle ?? currentDefaults.startAngle
        propertiesForValidation.endAngle = rawProperties?.endAngle ?? currentDefaults.endAngle
        propertiesForValidation.counterClockwise = rawProperties?.counterClockwise ?? currentDefaults.counterClockwise ?? false
        if (propertiesForValidation.radius === undefined || propertiesForValidation.startAngle === undefined || propertiesForValidation.endAngle === undefined) {
          this.logger.warn(`[${operationName}] ARC ${id} is missing radius, startAngle, or endAngle for validation.`)
        }
      }
      else if (elementType === ElementType.QUADRATIC) {
        this.logger.debug(`[${operationName}] Populating/defaulting properties for QUADRATIC.`)
        propertiesForValidation.start = rawProperties?.start ?? currentDefaults.start
        propertiesForValidation.control = rawProperties?.control ?? currentDefaults.control
        propertiesForValidation.end = rawProperties?.end ?? currentDefaults.end
        if (propertiesForValidation.start === undefined || propertiesForValidation.start === null || propertiesForValidation.control === undefined || propertiesForValidation.control === null || propertiesForValidation.end === undefined || propertiesForValidation.end === null) {
          this.logger.warn(`[${operationName}] QUADRATIC ${id} is missing start, control, or end points for validation.`)
        }
      }
      else if (elementType === ElementType.CUBIC) {
        this.logger.debug?.(`[${operationName}] Populating/defaulting properties for CUBIC.`)
        propertiesForValidation.start = rawProperties?.start ?? currentDefaults.start
        propertiesForValidation.control1 = rawProperties?.control1 ?? currentDefaults.control1
        propertiesForValidation.control2 = rawProperties?.control2 ?? currentDefaults.control2
        propertiesForValidation.end = rawProperties?.end ?? currentDefaults.end
        if (propertiesForValidation.start === undefined || propertiesForValidation.start === null || propertiesForValidation.control1 === undefined || propertiesForValidation.control1 === null || propertiesForValidation.control2 === undefined || propertiesForValidation.control2 === null || propertiesForValidation.end === undefined || propertiesForValidation.end === null) {
          this.logger.warn(`[${operationName}] CUBIC ${id} is missing start, control1, control2, or end points for validation.`)
        }
      }
      else if (elementType === ElementType.IMAGE) {
        this.logger.debug?.(`[${operationName}] Populating/defaulting IMAGE properties into propertiesForValidation.`)
        propertiesForValidation.src = rawProperties?.src ?? currentDefaults.src ?? '/public/icon/image.svg'
        propertiesForValidation.width = rawProperties?.width ?? currentDefaults.width ?? 100
        propertiesForValidation.height = rawProperties?.height ?? currentDefaults.height ?? 100
        propertiesForValidation.alt = rawProperties?.alt ?? currentDefaults.alt ?? 'Image Placeholder'
      }
      // propertiesForValidation should now correctly contain the specific sub-properties for the element type.

      this.logger.debug?.(`[${operationName}] Data for validation for ${id} will use properties:`, JSON.stringify(propertiesForValidation))

      const validatableData: Record<string, unknown> = {
        type: elementType,
        id,
        position: { x: positionInstance.x, y: positionInstance.y, z: positionInstance.z }, // Use initial drop position
        properties: propertiesForValidation, // Use the populated propertiesForValidation
      }

      this.logger.debug?.(`[${operationName}] Data prepared for validation for ${id}:`, JSON.stringify(validatableData, null, 2))
      const validationResult = await ElementValidator.validateElement(validatableData as unknown as ValidatableShape)

      if (validationResult.valid === false) {
        const errorMsg = `Shape validation failed: ${validationResult.errors?.join(', ')}`
        this.logger.warn(`[${operationName}] Validation failed for shape ${id}:`, validationResult.errors)
        this.logger.error(`[ElementCreationService createShapeInternal] Validation Errors for ${elementType} ${id}:`, validationResult.errors)
        this.emitError(ElementCreationErrorType.ValidationFailed, errorMsg, { component: 'ElementCreationService', operation: operationName, metadata: { errors: validationResult.errors } })
        return {
          success: false,
          error: {
            code: ElementCreationErrorType.ValidationFailed.toString(),
            message: errorMsg,
            details: validationResult.errors,
          },
          timestamp: Date.now(),
        }
      }
      this.logger.info(`[${operationName}] Validation successful for shape ${id}`)

      let specificParams: Record<string, unknown> = { position: positionInstance } // Default position

      switch (elementType) {
        case ElementType.LINE:
          specificParams = {
            ...specificParams,
            start: rawProperties?.start ?? currentDefaults.start ?? propertiesForValidation.start ?? { x: positionInstance.x, y: positionInstance.y, z: positionInstance.z },
            end: rawProperties?.end ?? currentDefaults.end ?? propertiesForValidation.end ?? { x: positionInstance.x + (currentDefaults.width ?? 100), y: positionInstance.y, z: positionInstance.z },
            arrowStart: rawProperties?.arrowStart ?? currentDefaults.arrowStart ?? false,
            arrowEnd: rawProperties?.arrowEnd ?? currentDefaults.arrowEnd ?? false,
          }
          this.logger.debug(`[${operationName}] LINE specificParams for factory:`, specificParams)
          break
        case ElementType.RECTANGLE:
        case ElementType.SQUARE: {
          let width = (typeof rawProperties?.width === 'number') ? rawProperties.width : (typeof currentDefaults.width === 'number') ? currentDefaults.width : 100
          let height = (typeof rawProperties?.height === 'number') ? rawProperties.height : (typeof currentDefaults.height === 'number') ? currentDefaults.height : 100
          if (elementType === ElementType.SQUARE) {
            const side = (typeof width === 'number') ? width : (typeof height === 'number') ? height : 100
            width = side
            height = side
          }
          specificParams = {
            ...specificParams,
            width,
            height,
            cornerRadius: rawProperties?.cornerRadius ?? currentDefaults.cornerRadius ?? 0,
          }
          this.logger.debug(`[${operationName}] RECT/SQUARE specificParams for factory:`, specificParams)
          break
        }
        case ElementType.ELLIPSE:
        case ElementType.CIRCLE: {
          let radiusX, radiusY, radius
          if (elementType === ElementType.CIRCLE) {
            const rP_radius = (typeof rawProperties?.radius === 'number') ? rawProperties.radius : undefined
            const cD_radius = (typeof currentDefaults.radius === 'number') ? currentDefaults.radius : undefined
            const rP_radiusX = (typeof rawProperties?.radiusX === 'number') ? rawProperties.radiusX : undefined
            const cD_radiusX = (typeof currentDefaults.radiusX === 'number') ? currentDefaults.radiusX : undefined
            const rP_radiusY = (typeof rawProperties?.radiusY === 'number') ? rawProperties.radiusY : undefined
            const cD_radiusY = (typeof currentDefaults.radiusY === 'number') ? currentDefaults.radiusY : undefined
            radius = rP_radius ?? cD_radius ?? rP_radiusX ?? cD_radiusX ?? rP_radiusY ?? cD_radiusY ?? 50
            radiusX = radius
            radiusY = radius
            specificParams = {
              ...specificParams,
              radius,
              radiusX,
              radiusY,
            }
          }
          else { // ELLIPSE
            radiusX = (typeof rawProperties?.radiusX === 'number') ? rawProperties.radiusX : (typeof currentDefaults.radiusX === 'number') ? currentDefaults.radiusX : 50
            radiusY = (typeof rawProperties?.radiusY === 'number') ? rawProperties.radiusY : (typeof currentDefaults.radiusY === 'number') ? currentDefaults.radiusY : 30
            specificParams = {
              ...specificParams,
              radiusX,
              radiusY,
            }
          }
          this.logger.debug(`[${operationName}] ELLIPSE/CIRCLE specificParams for factory:`, specificParams)
          break
        }
        case ElementType.POLYGON:
        case ElementType.TRIANGLE:
        case ElementType.QUADRILATERAL:
        case ElementType.PENTAGON:
        case ElementType.HEXAGON:
        case ElementType.HEPTAGON:
        case ElementType.OCTAGON:
        case ElementType.NONAGON:
        case ElementType.DECAGON: {
          let sides: number
          if (rawProperties?.sides !== undefined) {
            sides = Number(rawProperties.sides)
          }
          else if (currentDefaults.sides !== undefined) {
            sides = Number(currentDefaults.sides)
          }
          else {
            sides = (elementType === ElementType.TRIANGLE)
              ? 3
              : (elementType === ElementType.QUADRILATERAL)
                  ? 4
                  : (elementType === ElementType.PENTAGON)
                      ? 5
                      : (elementType === ElementType.HEXAGON) ? 6 : 4
          }
          const radius = (typeof rawProperties?.radius === 'number') ? rawProperties.radius : (typeof currentDefaults.radius === 'number') ? currentDefaults.radius : 50
          const isRegular = (typeof rawProperties?.isRegular === 'boolean') ? rawProperties.isRegular : (typeof currentDefaults.isRegular === 'boolean') ? currentDefaults.isRegular : true
          const startAngle = (typeof rawProperties?.startAngle === 'number') ? rawProperties.startAngle : (typeof currentDefaults.startAngle === 'number') ? currentDefaults.startAngle : undefined
          const center: PointData | undefined = (rawProperties?.center !== undefined && rawProperties?.center !== null) ? rawProperties.center as PointData : (currentDefaults.center !== undefined && currentDefaults.center !== null) ? currentDefaults.center : undefined
          specificParams = {
            ...specificParams,
            sides,
            radius,
            isRegular,
            // 确保将position传递给PolygonCreator，这样多边形的中心点就是鼠标落点位置
            position: positionInstance,
            points: (rawProperties?.points !== undefined && rawProperties?.points !== null && Array.isArray(rawProperties.points) && rawProperties.points.length > 0)
              ? rawProperties.points
              : (propertiesForValidation.points !== undefined && propertiesForValidation.points !== null && Array.isArray(propertiesForValidation.points) && propertiesForValidation.sides === sides
                  ? propertiesForValidation.points
                  : undefined),
          }

          // 确保points属性也在properties中，因为渲染器和验证器期望在properties中找到points
          if (specificParams.points !== undefined && Array.isArray(specificParams.points) && specificParams.points.length > 0) {
            if (specificParams.properties === undefined || specificParams.properties === null) {
              specificParams.properties = {}
            }
            const properties = specificParams.properties as Record<string, unknown>
            properties.points = specificParams.points
          }
          if (startAngle !== undefined)
            specificParams.startAngle = startAngle
          if (center !== undefined)
            specificParams.center = center
          this.logger.debug(`[${operationName}] POLYGON/TRIANGLE/HEXAGON specificParams for factory (Sides: ${sides}, Radius: ${radius}):`, specificParams)
          break
        }
        case ElementType.ARC: {
          const radius = (typeof rawProperties?.radius === 'number') ? rawProperties.radius : (typeof currentDefaults.radius === 'number') ? currentDefaults.radius : 50
          const startAngle = (typeof rawProperties?.startAngle === 'number') ? rawProperties.startAngle : (typeof currentDefaults.startAngle === 'number') ? currentDefaults.startAngle : 0
          const endAngle = (typeof rawProperties?.endAngle === 'number') ? rawProperties.endAngle : (typeof currentDefaults.endAngle === 'number') ? currentDefaults.endAngle : 90
          const counterClockwise = (typeof rawProperties?.counterClockwise === 'boolean') ? rawProperties.counterClockwise : (typeof currentDefaults.counterClockwise === 'boolean') ? currentDefaults.counterClockwise : false
          const closed = (typeof rawProperties?.closed === 'boolean') ? rawProperties.closed : (typeof currentDefaults.closed === 'boolean') ? currentDefaults.closed : false
          specificParams = {
            ...specificParams,
            radius,
            startAngle,
            endAngle,
            counterClockwise,
            closed,
          }
          this.logger.debug(`[${operationName}] ARC specificParams for factory:`, specificParams)
          break
        }
        case ElementType.QUADRATIC: {
          const start = (rawProperties?.start as PointData) ?? (currentDefaults.start as PointData) ?? (propertiesForValidation.start !== undefined ? propertiesForValidation.start as PointData : undefined) ?? { x: positionInstance.x, y: positionInstance.y, z: positionInstance.z }
          const control = (rawProperties?.control as PointData) ?? (currentDefaults.control as PointData) ?? (propertiesForValidation.control !== undefined ? propertiesForValidation.control as PointData : undefined) ?? { x: positionInstance.x + 50, y: positionInstance.y - 50, z: positionInstance.z }
          const end = (rawProperties?.end as PointData) ?? (currentDefaults.end as PointData) ?? (propertiesForValidation.end !== undefined ? propertiesForValidation.end as PointData : undefined) ?? { x: positionInstance.x + 100, y: positionInstance.y, z: positionInstance.z }
          specificParams = {
            ...specificParams,
            start,
            control,
            end,
          }
          this.logger.debug(`[${operationName}] QUADRATIC specificParams for factory:`, specificParams)
          break
        }
        case ElementType.CUBIC: {
          const start = (rawProperties?.start as PointData) ?? (currentDefaults.start as PointData) ?? (propertiesForValidation.start !== undefined ? propertiesForValidation.start as PointData : undefined) ?? { x: positionInstance.x, y: positionInstance.y, z: positionInstance.z }
          const control1 = (rawProperties?.control1 as PointData) ?? (currentDefaults.control1 as PointData) ?? (propertiesForValidation.control1 !== undefined ? propertiesForValidation.control1 as PointData : undefined) ?? { x: positionInstance.x + 30, y: positionInstance.y - 50, z: positionInstance.z }
          const control2 = (rawProperties?.control2 as PointData) ?? (currentDefaults.control2 as PointData) ?? (propertiesForValidation.control2 !== undefined ? propertiesForValidation.control2 as PointData : undefined) ?? { x: positionInstance.x + 70, y: positionInstance.y + 50, z: positionInstance.z }
          const end = (rawProperties?.end as PointData) ?? (currentDefaults.end as PointData) ?? (propertiesForValidation.end !== undefined ? propertiesForValidation.end as PointData : undefined) ?? { x: positionInstance.x + 100, y: positionInstance.y, z: positionInstance.z }
          specificParams = {
            ...specificParams,
            start,
            control1,
            control2,
            end,
          }
          this.logger.debug(`[${operationName}] CUBIC specificParams for factory:`, specificParams)
          break
        }
        case ElementType.POLYLINE: {
          let points = (rawProperties?.points as PointData[]) ?? (currentDefaults.points as PointData[]) ?? (propertiesForValidation.points !== undefined ? propertiesForValidation.points as PointData[] : undefined)
          if (points === undefined || points === null || (Array.isArray(points) && points.length < 2)) {
            points = [
              { x: positionInstance.x, y: positionInstance.y, z: positionInstance.z },
              { x: positionInstance.x + 50, y: positionInstance.y + 20, z: positionInstance.z },
              { x: positionInstance.x + 100, y: positionInstance.y, z: positionInstance.z },
            ]
          }
          specificParams = {
            ...specificParams,
            points,
            curved: rawProperties?.curved ?? currentDefaults.curved ?? false,
            tension: rawProperties?.tension ?? currentDefaults.tension ?? 0.5,
          }
          this.logger.debug(`[${operationName}] POLYLINE specificParams for factory:`, specificParams)
          break
        }
        case ElementType.TEXT: {
          specificParams = {
            ...specificParams, // Contains position
            text: rawProperties?.text ?? currentDefaults.text ?? 'Text',
            fontSize: rawProperties?.fontSize ?? currentDefaults.fontSize ?? 16,
            fontFamily: rawProperties?.fontFamily ?? currentDefaults.fontFamily ?? 'Arial',
            fill: rawProperties?.fill ?? currentDefaults.fill, // Text-specific fill
            textAlign: rawProperties?.textAlign ?? currentDefaults.textAlign ?? 'left',
            textBaseline: rawProperties?.textBaseline ?? currentDefaults.textBaseline ?? 'top',
            fontWeight: rawProperties?.fontWeight ?? currentDefaults.fontWeight ?? 'normal',
            fontStyle: rawProperties?.fontStyle ?? currentDefaults.fontStyle ?? 'normal',
            lineHeight: rawProperties?.lineHeight ?? currentDefaults.lineHeight,
          }
          // Clean up undefined properties to allow creator defaults if not specified here
          Object.keys(specificParams).forEach((key) => {
            if (specificParams[key] === undefined) {
              delete specificParams[key]
            }
          })
          this.logger.debug(`[${operationName}] TEXT specificParams for factory:`, specificParams)
          break
        }
        case ElementType.IMAGE: {
          const src = (typeof rawProperties?.src === 'string') ? rawProperties.src : (typeof currentDefaults.src === 'string') ? currentDefaults.src : ''
          const width = (typeof rawProperties?.width === 'number') ? rawProperties.width : (typeof currentDefaults.width === 'number') ? currentDefaults.width : 100
          const height = (typeof rawProperties?.height === 'number') ? rawProperties.height : (typeof currentDefaults.height === 'number') ? currentDefaults.height : 100
          specificParams = {
            ...specificParams,
            src,
            width,
            height,
          }
          this.logger.debug(`[${operationName}] IMAGE specificParams for factory:`, specificParams)
          break
        }
        case ElementType.TEXT_LABEL:
        case ElementType.WALL:
        case ElementType.DOOR:
        case ElementType.WINDOW:
        case ElementType.FURNITURE:
        case ElementType.FIXTURE:
        case ElementType.ROOM:
        case ElementType.LIGHT:
        case ElementType.FLOOR_AREA:
        case ElementType.HANDRAIL:
        case ElementType.ELECTRICAL_OUTLET:
        case ElementType.ROOM_BOUNDARY:
        case ElementType.APPLIANCE:
        case ElementType.GROUP:
        case ElementType.OPENING:
        case ElementType.WALL_PAINT:
        case ElementType.WALL_PAPER:
          // These element types use default parameters for now
          this.logger.debug(`[${operationName}] ${elementType} using default parameters`)
          break
        default:
          this.logger.warn(`[${operationName}] Unhandled element type: ${String(elementType)}. Using default parameters.`)
          break
      }
      this.logger.debug(`[ElementCreationService createShapeInternal] Populated specificParams for ${elementType}:`, JSON.stringify(specificParams, null, 2))

      // 准备properties对象，确保包含所有必要的属性
      // 首先从默认设置中获取成本相关属性，然后用rawProperties覆盖
      const mergedProperties = {
        // 从默认设置中获取成本相关属性
        costUnitPrice: currentDefaults.costUnitPrice ?? 1,
        costMultiplierOrCount: currentDefaults.costMultiplierOrCount ?? 0,
        costBasis: currentDefaults.costBasis ?? 'unit',
        // 然后合并其他属性
        ...(rawProperties || {}),
      }

      // 对于多边形类型，确保points属性在properties中
      const isPolygonType = [
        ElementType.POLYGON,
        ElementType.TRIANGLE,
        ElementType.QUADRILATERAL,
        ElementType.PENTAGON,
        ElementType.HEXAGON,
      ].includes(elementType)

      if (isPolygonType
        && specificParams.points !== undefined
        && Array.isArray(specificParams.points)
        && specificParams.points.length > 0) {
        (mergedProperties as any).points = specificParams.points
      }

      const paramsForFactory = {
        ...baseParams,
        ...specificParams,
        type: elementType,
        // Ensure all original custom properties from the request are passed to the factory
        // under the 'properties' field.
        properties: mergedProperties,
      } as unknown as ShapeCreationParamsUnion

      this.logger.debug?.(`[${operationName}] FINAL Params being passed to factory:`, JSON.stringify(paramsForFactory, null, 2))

      let createdElementModel: ShapeModel

      const pathTypes: string[] = [
        ElementType.LINE.toString(),
        ElementType.POLYLINE.toString(),
        ElementType.ARC.toString(),
        ElementType.QUADRATIC.toString(),
        ElementType.CUBIC.toString(),
      ]

      if (pathTypes.includes(elementType.toString())) {
        this.logger.info?.(`[${operationName}] Element type ${elementType} is a Path. Calling factory.createPath().`)
        createdElementModel = await this.factory.createPath(elementType, paramsForFactory as PathCreationOptionsUnion) as ShapeModel
        this.logger.debug(`[ElementCreationService createShapeInternal] Path model RECEIVED FROM FACTORY (ID: ${createdElementModel.id}):`, JSON.stringify(createdElementModel, null, 2))
      }
      else {
        this.logger.info?.(`[${operationName}] Element type ${elementType} is a Shape. Calling factory.createShape().`)
        createdElementModel = await this.factory.createShape(elementType, paramsForFactory)
        this.logger.debug(`[ElementCreationService createShapeInternal] Shape model RECEIVED FROM FACTORY (ID: ${createdElementModel.id}):`, JSON.stringify(createdElementModel, null, 2))
        this.logger.debug(`[ElementCreationService createShapeInternal] DETAILED CHECK of Shape model from factory:`, JSON.stringify({
          id: createdElementModel.id,
          type: createdElementModel.type,
          majorCategory: createdElementModel.majorCategory,
          minorCategory: createdElementModel.minorCategory,
          zLevelId: createdElementModel.zLevelId,
          isFixedCategory: createdElementModel.isFixedCategory,
          zIndex: createdElementModel.zIndex,
          position: createdElementModel.position,
        }, null, 2))
      }

      this.logger.debug?.(`[${operationName}] Created element model:`, createdElementModel)

      this.logger.info(`[${operationName}] Shape "${id}" created (not added to repository here). Emitting event.`)
      this.emitSuccess(createdElementModel)
      return { success: true, data: createdElementModel, timestamp: Date.now() }
    }
    catch (creationError) {
      const err = creationError instanceof Error ? creationError : new Error(String(creationError))
      this.logger.error(`[${operationName}] Error creating or adding shape ${id}:`, err)
      this.emitError(
        ElementCreationErrorType.FactoryCreationFailed,
        `Failed to create or add shape: ${err.message}`,
        { ...baseContext, metadata: { ...baseContext.metadata, originalError: err } },
      )
      return {
        success: false,
        error: {
          code: ElementCreationErrorType.FactoryCreationFailed.toString(),
          message: err.message,
          details: err,
        },
        timestamp: Date.now(),
      }
    }
  }

  /**
   * Emits an error event for shape creation failures.
   * @private
   * @param {ElementCreationErrorType} errorType - The type of shape creation error.
   * @param {string} message - A detailed error message.
   * @param {CoreErrorContext} [context] - Contextual information about the error.
   */
  private emitError(errorType: ElementCreationErrorType, message: string, context?: CoreErrorContext): void {
    const errorId = uuidv4()
    const fullMessage = `[${errorType}] ${message}${(context?.operation !== undefined && context.operation !== null && context.operation !== '') ? ` in ${context.operation}` : ''}`
    this.logger.error(fullMessage, { errorId, ...(context?.metadata || {}) })
    this.logger.error(`[ElementCreationService emitError] Publishing AppEventType.EventError. ErrorType: ${errorType}, Message: ${message}, Context:`, JSON.stringify(context, null, 2))

    this.eventBus.publish({
      type: AppEventType.EventError,
      timestamp: Date.now(),
      payload: {
        error: {
          code: errorType.toString(),
          message: fullMessage,
          details: context?.metadata,
          errorId,
        },
        context,
      },
    })
  }

  /**
   * Emits a success event after a shape is successfully created.
   * @private
   * @param {ShapeModel} shapeModel - The successfully created shape model.
   */
  private emitSuccess(shapeModel: ShapeModel): void {
    this.logger.info(`Successfully created shape: ${shapeModel.id} of type ${shapeModel.type}`)
    this.logger.debug(`[ElementCreationService emitSuccess] Publishing AppEventType.ShapeCreateComplete for shape ID: ${shapeModel.id}, Type: ${shapeModel.type}, Shape:`, JSON.stringify(shapeModel, null, 2))
    this.eventBus.publish({
      type: AppEventType.ShapeCreateComplete,
      timestamp: Date.now(),
      payload: {
        shape: shapeModel,
        source: ElementCreationService.name,
      },
    })

    this.logger.debug?.(`[ElementCreationService] Emitted AppEventType.ShapeCreateComplete for shape: ${shapeModel.id} (emitted a clone)`)
  }

  // --- Methods to satisfy IElementCreationService ---

  /**
   * Creates a new shape based on the provided request.
   * @param {ElementCreationRequest} request - Parameters for creating the shape.
   * @returns {Promise<ElementCreationResult>} A promise that resolves with the result of the creation attempt.
   */
  public async createShape(request: ElementCreationRequest): Promise<ElementCreationResult> {
    this.logger.info('[ElementCreationService] createShape called directly.', request)
    return this.createShapeInternal(request, 'createShape', request.elementType.toString())
  }

  /**
   * Duplicates an existing shape with an optional offset.
   * @param {string} shapeId - The ID of the shape to duplicate.
   * @param {{ x: number; y: number }} [offset] - Optional offset for the new shape's position.
   * @returns {Promise<ElementCreationResult>} A promise that resolves with the result of the duplication attempt.
   */
  public async duplicateShape(shapeId: string, offset?: { x: number, y: number }): Promise<ElementCreationResult> {
    this.logger.info(`[ElementCreationService] duplicateShape called for ID: ${shapeId}`, offset)
    // No direct repository access; duplication should be handled via event-driven flow or by CoreCoordinator
    this.emitError(ElementCreationErrorType.CoordinatorOperationFailed, 'Direct duplication not supported in event-driven mode.', {
      component: 'ElementCreationService',
      operation: 'duplicateShape',
      metadata: { shapeId },
    })
    return {
      success: false,
      error: { code: CoreErrorType.NotFound.toString(), message: 'Direct duplication not supported in event-driven mode.' },
      timestamp: Date.now(),
    }
  }
}
