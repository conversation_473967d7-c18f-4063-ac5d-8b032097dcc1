/**
 * Unit tests for validation rule interface
 *
 * Tests the rule interface and rule factory
 */

import type { ValidationError } from '@/types/core/element/validator'
import { describe, expect, it } from 'vitest'
import { createValidationRule } from '@/core/validator/rules/ruleInterface'

describe('validation Rule Interface', () => {
  it('createValidationRule creates a rule with the correct structure', () => {
    // Create a simple test rule
    const testRule = createValidationRule('test-rule', (_shape): ValidationError[] => {
      return [{ code: 'TEST_ERROR', message: 'Test error message' }]
    })

    // Verify rule structure
    expect(testRule).toBeDefined()
    expect(testRule.name).toBe('test-rule')
    expect(typeof testRule.apply).toBe('function')
  })

  it('rule.apply returns the expected errors', () => {
    // Create a rule that checks if a shape has an ID
    const idRule = createValidationRule('id-rule', (shape): ValidationError[] => {
      const errors: ValidationError[] = []
      if (!shape.id) {
        errors.push({ code: 'MISSING_ID', message: 'Missing ID' })
      }
      return errors
    })

    // Test with a shape that has an ID
    const validShape = {
      id: 'test-shape',
      type: 'rectangle',
      position: { x: 0, y: 0 },
      width: 100,
      height: 100,
      strokeColor: '#000',
      fillColor: '#FFF',
    }
    const validResult = idRule.apply(validShape as any)
    expect(validResult).toEqual([])

    // Test with a shape that doesn't have an ID
    const invalidShape = {
      id: '',
      type: 'rectangle',
      position: { x: 0, y: 0 },
      width: 100,
      height: 100,
      strokeColor: '#000',
      fillColor: '#FFF',
    }
    const invalidResult = idRule.apply(invalidShape as any)
    expect(invalidResult).toHaveLength(1)
    expect(invalidResult[0].code).toBe('MISSING_ID')
  })

  it('multiple rules can be combined', () => {
    // Create a rule that checks if a shape has an ID
    const idRule = createValidationRule('id-rule', (shape): ValidationError[] => {
      const errors: ValidationError[] = []
      if (!shape.id) {
        errors.push({ code: 'MISSING_ID', message: 'Missing ID' })
      }
      return errors
    })

    // Create a rule that checks if a shape has valid colors
    const colorRule = createValidationRule('color-rule', (shape): ValidationError[] => {
      const errors: ValidationError[] = []
      if (shape.strokeColor !== '#000') {
        errors.push({ code: 'INVALID_STROKE_COLOR', message: 'Invalid stroke color' })
      }
      if (shape.fillColor !== '#FFF') {
        errors.push({ code: 'INVALID_FILL_COLOR', message: 'Invalid fill color' })
      }
      return errors
    })

    // Apply both rules to a shape
    const shape = {
      id: '',
      type: 'rectangle',
      position: { x: 0, y: 0 },
      width: 100,
      height: 100,
      strokeColor: 'red',
      fillColor: 'blue',
    }

    const idErrors = idRule.apply(shape as any)
    const colorErrors = colorRule.apply(shape as any)
    const allErrors = [...idErrors, ...colorErrors]

    // Verify that all errors are reported
    expect(allErrors).toHaveLength(3)
    expect(allErrors.some(e => e.code === 'MISSING_ID')).toBe(true)
    expect(allErrors.some(e => e.code === 'INVALID_STROKE_COLOR')).toBe(true)
    expect(allErrors.some(e => e.code === 'INVALID_FILL_COLOR')).toBe(true)
  })
})
