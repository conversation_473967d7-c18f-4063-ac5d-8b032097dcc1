import { expect, test } from '@playwright/test'
import { appEventBus } from '@/services/event-bus/AppEventBus'
import { AppEventType } from '@/types/services/events'

test('Shape Mirroring Events', async ({ page }) => {
  let publishedEvents: any[] = []

  // Mock the event bus publish method
  const originalPublish = appEventBus.publish
  appEventBus.publish = function (event: any) {
    publishedEvents.push(event)
    return true
  }

  // Test horizontal mirror
  const mockShape = {
    id: 'test-rect-1',
    type: 'rectangle',
    position: { x: 100, y: 100 },
    boundary: { width: 100, height: 50 },
  }

  // Trigger horizontal mirror
  appEventBus.publish({
    type: AppEventType.COMPUTE_TRANSFORM,
    payload: {
      type: 'mirror',
      direction: 'horizontal',
      shapes: [mockShape],
    },
  })

  // Verify the event was published
  expect(publishedEvents.length).toBe(1)
  expect(publishedEvents[0].type).toBe(AppEventType.COMPUTE_TRANSFORM)
  expect(publishedEvents[0].payload.type).toBe('mirror')
  expect(publishedEvents[0].payload.direction).toBe('horizontal')

  // Reset events array for next test
  publishedEvents = []

  // Test vertical mirror
  appEventBus.publish({
    type: AppEventType.COMPUTE_TRANSFORM,
    payload: {
      type: 'mirror',
      direction: 'vertical',
      shapes: [mockShape],
    },
  })

  // Verify vertical mirror event
  expect(publishedEvents.length).toBe(1)
  expect(publishedEvents[0].type).toBe(AppEventType.COMPUTE_TRANSFORM)
  expect(publishedEvents[0].payload.type).toBe('mirror')
  expect(publishedEvents[0].payload.direction).toBe('vertical')

  // Restore original publish method
  appEventBus.publish = originalPublish
})
