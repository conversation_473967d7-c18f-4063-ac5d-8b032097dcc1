/**
 * Compute Event Publishers
 *
 * Provides helper functions for publishing computation-related events to the application's event bus.
 * This module covers geometric calculations, transformations, and other computational operations on shapes.
 *
 * @remarks
 * All functions in this module follow dependency injection principles by accepting an EventBus instance
 * as their first parameter. This ensures testability and flexibility in different contexts.
 *
 * @example
 * ```typescript
 * import { publishComputeRequest } from '@/services/core/event-bus/helpers/publishers/computePublishers';
 *
 * // Request area calculation for a shape
 * publishComputeRequest(eventBus, ComputeOperationType.AREA, ['shape-1']);
 * ```
 *
 * @module services/core/event-bus/helpers/publishers/computePublishers
 */

import type {
  ComputeOperationType, // Import ComputeOperationType
  // ComputeOperation // Removed unused import
} from '@/types/core/compute'
import type { EventBus } from '@/types/services/events'
import type {
  ComputeEvent,
  TransformEvent,
  TransformOptions, // Import TransformOptions
} from '@/types/services/events/computeEvents'
import { AppEventType } from '@/types/services/events'
// Removed local definitions of ComputeOperationType, ComputeOperation, and TransformOptions

/**
 * Publishes a compute request event.
 *
 * @remarks
 * Requests computation of geometric properties for one or more shapes.
 *
 * @param eventBus - The EventBus instance to use for publishing.
 * @param operation - Type of computation to perform (area, perimeter, etc.).
 * @param shapeIds - Array of shape IDs to compute properties for.
 * @param options - Optional configuration for the computation.
 */
export function publishComputeRequest(
  eventBus: EventBus,
  operation: ComputeOperationType,
  shapeIds: string[],
  options?: Record<string, unknown>,
): void {
  const operationString = operation.toLowerCase() as ComputeEvent['payload']['operation']
  const event: ComputeEvent = {
    type: AppEventType.ComputeRequest,
    payload: {
      operation: operationString,
      elementId: shapeIds.length === 1 ? shapeIds[0] : undefined,
      options: options ? { shapeIds, ...options } : { shapeIds },
    },
  }
  eventBus.publish(event)
}

/**
 * Publishes a compute transform event.
 *
 * @remarks
 * Requests geometric transformation of one or more shapes.
 *
 * @param eventBus - The EventBus instance to use for publishing.
 * @param shapeIds - Array of shape IDs to transform.
 * @param transformOptions - Transformation options (rotation, scale, translation).
 */
export function publishComputeTransform(
  eventBus: EventBus,
  shapeIds: string[],
  transformOptions: TransformOptions,
): void {
  const event: TransformEvent = {
    type: AppEventType.ComputeTransform,
    payload: { shapeIds, transformOptions },
  }
  eventBus.publish(event)
}
