/**
 * Cost Calculation Strategy for Polygon Elements
 *
 * @remarks
 * This strategy implements the {@link CostCalculatorStrategy} for calculating costs
 * for polygon elements, including triangles, pentagons, hexagons, and other n-sided polygons.
 *
 * It supports various cost calculation bases, determined by the `costBasis` property:
 * - `'area'`: Cost is based on the polygon's area.
 * - `'perimeter'`: Cost is based on the polygon's perimeter.
 * - `'unit'`: Cost is a fixed amount per polygon.
 * - `'segment'`: Cost is based on the number of sides/segments in the polygon.
 * - `'fixed'`: Cost is a fixed amount.
 *
 * The strategy extracts the polygon's points from either the element's properties or
 * directly from the element, calculates the appropriate measure (area, perimeter, etc.),
 * and then applies the unit cost and multiplier to determine the total cost.
 *
 * @module core/compute/strategies/cost/PolygonCostStrategy
 * @see {@link CostCalculatorStrategy}
 * @see {@link CostCalculationOptions}
 * @see {@link Shape.Polygon}
 */
import type { CostCalculationOptions } from '../../../../types/core/compute'
import type { PointData as IPoint } from '../../../../types/core/element/geometry/point'
import type { Element } from '../../../../types/core/elementDefinitions'

import { AreaBasedCostStrategy } from './AreaBasedCostStrategy'

export class PolygonCostStrategy extends AreaBasedCostStrategy {
  /**
   * Calculates the cost for a polygon element based on the specified cost basis.
   *
   * @param element - The polygon element.
   * @param unitCost - The base cost per unit (square meter for area, meter for perimeter, etc.).
   * @param options - Optional {@link CostCalculationOptions}.
   * @returns The total calculated cost.
   */
  public calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
    // 优先从 properties 取值，兼容顶层
    const el = element as { properties?: Record<string, unknown>, points?: IPoint[], costUnitPrice?: number, costMultiplierOrCount?: number, costBasis?: string }
    const props = el.properties ?? {} as Record<string, unknown>

    // Type guard for points validation
    function isValidPointArray(value: unknown): value is IPoint[] {
      return Array.isArray(value) && value.every(point =>
        point != null
        && typeof point === 'object'
        && 'x' in point
        && 'y' in point
        && typeof (point as IPoint).x === 'number'
        && typeof (point as IPoint).y === 'number',
      )
    }

    // 获取点数据
    let points: IPoint[] = []
    if (isValidPointArray(props.points)) {
      points = props.points
    }
    else if (isValidPointArray(el.points)) {
      points = el.points
    }
    // 兼容 costUnitPrice、costMultiplierOrCount、costBasis
    let costUnitPrice = 1
    if (typeof props.costUnitPrice === 'number') {
      costUnitPrice = props.costUnitPrice
    }
    else if (typeof el.costUnitPrice === 'number') {
      costUnitPrice = el.costUnitPrice
    }
    else if (typeof unitCost === 'number') {
      costUnitPrice = unitCost
    }

    let costMultiplierOrCount = 0 // 默认为0，确保初始成本为0
    if (typeof props.costMultiplierOrCount === 'number') {
      costMultiplierOrCount = props.costMultiplierOrCount
    }
    else if (typeof el.costMultiplierOrCount === 'number') {
      costMultiplierOrCount = el.costMultiplierOrCount
    }
    else if (typeof options?.quantity === 'number') {
      costMultiplierOrCount = options.quantity
    }

    const costBasis = typeof props.costBasis === 'string'
      ? props.costBasis
      : (typeof el.costBasis === 'string'
          ? el.costBasis
          : (typeof options?.costType === 'string' ? options.costType : 'area'))

    // 容错
    if (!Array.isArray(points) || points.length < 3) {
      console.error(`[PolygonCostStrategy][calculateCost] 无效的点数据: ${JSON.stringify(points)}`)
      return 0
    }

    let measure = 0
    switch (costBasis) {
      case 'area':
        // 使用父类的计算方法
        return super.calculateCost(element, costUnitPrice, {
          ...options,
          costType: 'area',
          multiplier: costMultiplierOrCount,
        })
      case 'perimeter':
        // 计算多边形周长
        for (let i = 0; i < points.length; i++) {
          const p1 = points[i]
          const p2 = points[(i + 1) % points.length] // 下一个点，循环到第一个点
          const dx = p2.x - p1.x
          const dy = p2.y - p1.y
          measure += Math.sqrt(dx * dx + dy * dy)
        }
        break
      case 'unit':
        measure = 1
        break
      case 'segment':
        measure = points.length // 多边形的边数等于点的数量
        break
      case 'fixed':
        measure = 1
        break
      default:
        console.error(`[PolygonCostStrategy][calculateCost] 未知的成本基准: ${costBasis}`)
        return 0
    }

    // 计算总成本
    const totalCost = measure * Number(costUnitPrice) * Number(costMultiplierOrCount)

    // 更新元素的 costTotal 属性
    if (el.properties && typeof el.properties === 'object') {
      el.properties.costTotal = totalCost
    }

    return totalCost
  }

  /**
   * Returns the primary element type this strategy supports.
   * @returns A string identifier for polygon-based elements.
   */
  public getElementType(): string {
    return 'polygon_based' // Generic identifier for polygon-based cost calculation
  }
}
