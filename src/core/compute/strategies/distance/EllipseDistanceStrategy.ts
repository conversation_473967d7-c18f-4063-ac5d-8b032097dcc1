/**
 * Distance Calculation Strategy for Ellipse/Circle Elements
 *
 * @remarks
 * This strategy implements the {@link DistanceCalculatorStrategy} for calculating
 * the distance between an Ellipse or Circle element ({@link CoreElementType.ELLIPSE},
 * {@link CoreElementType.CIRCLE}) and another {@link ShapeElement}.
 *
 * It supports calculating distances to:
 * - Other Ellipses or Circles.
 * - Rectangles or Squares ({@link CoreElementType.RECTANGLE}, {@link CoreElementType.SQUARE}).
 * - Polygons ({@link CoreElementType.POLYGON}, {@link CoreElementType.TRIANGLE}, etc.).
 * - Lines ({@link CoreElementType.LINE}).
 *
 * The calculations involve determining the closest points between the ellipse/circle
 * and the other shape's geometry. For complex interactions (e.g., ellipse to polygon/line),
 * it often simplifies by finding the distance from the ellipse's center to the closest
 * point/segment on the other shape and then adjusting for the ellipse's radius at that angle.
 *
 * A local helper function `calculatePointToLineSegmentDistance` is used, which is
 * marked with a FIXME to be moved to a general geometry utility file.
 *
 * The strategy validates element types and their geometric properties.
 *
 * @module core/compute/strategies/distance/EllipseDistanceStrategy
 * @see {@link Shape.Ellipse}
 * @see {@link Shape.Circle}
 * @see {@link DistanceCalculatorStrategy}
 */
import type { DistanceCalculatorStrategy } from '../../../../types/core/compute'
import type { PointData as PointInterface } from '../../../../types/core/element/geometry/point'
import type {
  Path,
  Shape,
  ShapeElement,
} from '../../../../types/core/elementDefinitions'
import { PointClass } from '../../../../lib/utils/geometry/PointClass'
import { calculateDistance as calculatePointToPointDistance } from '../../../../lib/utils/geometry/pointUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

// FIXME: This function should be moved to a geometry utility file (e.g., src/lib/utils/geometry/distanceUtils.ts or pointUtils.ts)
/**
 * Calculates the shortest distance from a point to a line segment.
 * @param p - The point.
 * @param a - Start point of the line segment.
 * @param b - End point of the line segment.
 * @returns The shortest distance.
 */
function calculatePointToLineSegmentDistance(p: PointInterface, a: PointInterface, b: PointInterface): number {
  const l2 = (a.x - b.x) ** 2 + (a.y - b.y) ** 2
  if (l2 === 0)
    return calculatePointToPointDistance(new PointClass(p.x, p.y, p.z), new PointClass(a.x, a.y, a.z))

  let t = ((p.x - a.x) * (b.x - a.x) + (p.y - a.y) * (b.y - a.y)) / l2
  t = Math.max(0, Math.min(1, t))

  const closestPoint: PointInterface = {
    x: a.x + t * (b.x - a.x),
    y: a.y + t * (b.y - a.y),
    z: (a.z !== undefined && b.z !== undefined) ? (a.z + t * (b.z - a.z)) : (a.z ?? b.z ?? p.z),
  }

  return calculatePointToPointDistance(new PointClass(p.x, p.y, p.z), new PointClass(closestPoint.x, closestPoint.y, closestPoint.z))
}

/**
 * Implements the {@link DistanceCalculatorStrategy} for {@link CoreElementType.ELLIPSE}
 * and {@link CoreElementType.CIRCLE} elements.
 */
export class EllipseDistanceStrategy implements DistanceCalculatorStrategy {
  /**
   * Calculates the distance between an Ellipse or Circle (element1) and another {@link ShapeElement} (element2).
   *
   * @param element1 - The first shape element, which must be of type {@link CoreElementType.ELLIPSE} or {@link CoreElementType.CIRCLE}.
   * @param element2 - The second shape element.
   * @returns The calculated distance between the two elements.
   * @throws {@link CoreError} if `element1` is not an ELLIPSE or CIRCLE.
   * @throws {@link CoreError} if `element2` is of an unsupported type for distance calculation with an ellipse/circle.
   * @throws {@link CoreError} if either element has invalid or missing geometric properties.
   */
  public calculateDistance(element1: ShapeElement, element2: ShapeElement): number {
    if (element1.type !== CoreElementType.ELLIPSE && element1.type !== CoreElementType.CIRCLE) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `EllipseDistanceStrategy's first element must be ELLIPSE or CIRCLE, got ${element1.type}`,
        undefined,
        { component: 'EllipseDistanceStrategy', operation: 'calculateDistance', target: element1.id },
      )
    }

    const ellipseElement1 = element1 as unknown as (Shape.Ellipse | Shape.Circle) // Added unknown
    const center1: PointInterface = element1.position // Get position from ShapeElement

    let radiusX1: number
    let radiusY1: number

    if (element1.type === CoreElementType.CIRCLE) { // Check type on element1 (ShapeElement)
      const circle1 = ellipseElement1 as unknown as Shape.Circle // Added unknown
      radiusX1 = typeof circle1.properties.radius === 'number' ? circle1.properties.radius : 0
      radiusY1 = typeof circle1.properties.radius === 'number' ? circle1.properties.radius : 0
    }
    else { // ELLIPSE
      const ellipse1 = ellipseElement1 as unknown as Shape.Ellipse // Added unknown
      radiusX1 = typeof ellipse1.properties.radiusX === 'number' ? ellipse1.properties.radiusX : 0
      radiusY1 = typeof ellipse1.properties.radiusY === 'number' ? ellipse1.properties.radiusY : 0
    }

    if (center1 === null || center1 === undefined || typeof center1.x !== 'number' || typeof center1.y !== 'number' || !Number.isFinite(center1.x) || !Number.isFinite(center1.y)
      || typeof radiusX1 !== 'number' || radiusX1 < 0 || !Number.isFinite(radiusX1)
      || typeof radiusY1 !== 'number' || radiusY1 < 0 || !Number.isFinite(radiusY1)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid ellipse/circle parameters for element ID ${element1.id}`,
        undefined,
        { component: 'EllipseDistanceStrategy', operation: 'calculateDistance', target: element1.id, metadata: { center1, radiusX1, radiusY1 } },
      )
    }

    switch (element2.type) {
      case CoreElementType.CIRCLE:
      case CoreElementType.ELLIPSE: {
        const ellipseElement2 = element2 as unknown as (Shape.Ellipse | Shape.Circle) // Added unknown
        const center2: PointInterface = element2.position // Get position from ShapeElement
        let radiusX2: number
        let radiusY2: number

        if (element2.type === CoreElementType.CIRCLE) { // Check type on element2 (ShapeElement)
          const circle2 = ellipseElement2 as unknown as Shape.Circle // Added unknown
          radiusX2 = typeof circle2.properties.radius === 'number' ? circle2.properties.radius : 0
          radiusY2 = typeof circle2.properties.radius === 'number' ? circle2.properties.radius : 0
        }
        else {
          const ellipse2 = ellipseElement2 as unknown as Shape.Ellipse // Added unknown
          radiusX2 = typeof ellipse2.properties.radiusX === 'number' ? ellipse2.properties.radiusX : 0
          radiusY2 = typeof ellipse2.properties.radiusY === 'number' ? ellipse2.properties.radiusY : 0
        }

        if (center2 === null || center2 === undefined || typeof center2.x !== 'number' || typeof center2.y !== 'number' || !Number.isFinite(center2.x) || !Number.isFinite(center2.y)
          || typeof radiusX2 !== 'number' || radiusX2 < 0 || !Number.isFinite(radiusX2)
          || typeof radiusY2 !== 'number' || radiusY2 < 0 || !Number.isFinite(radiusY2)) {
          throw new CoreError(
            ErrorType.InvalidParameter,
            `Invalid target ellipse/circle parameters for element ID ${element2.id}`,
            undefined,
            { component: 'EllipseDistanceStrategy', operation: 'calculateDistance', target: element2.id, metadata: { center2, radiusX2, radiusY2 } },
          )
        }

        const centerDistance = calculatePointToPointDistance(new PointClass(center1.x, center1.y, center1.z), new PointClass(center2.x, center2.y, center2.z))
        const effectiveRadius1 = (radiusX1 + radiusY1) / 2
        const effectiveRadius2 = (radiusX2 + radiusY2) / 2
        return Math.max(0, centerDistance - effectiveRadius1 - effectiveRadius2)
      }

      case CoreElementType.RECTANGLE:
      case CoreElementType.SQUARE: {
        const rectElement = element2 as unknown as Shape.Rectangle // Added unknown
        const rectX = element2.position.x // Assuming position is top-left, from ShapeElement
        const rectY = element2.position.y
        const rectWidth = rectElement.properties && typeof rectElement.properties.width === 'number' ? rectElement.properties.width : 0
        const rectHeight = rectElement.properties && typeof rectElement.properties.height === 'number' ? rectElement.properties.height : 0

        if (typeof rectWidth !== 'number' || typeof rectHeight !== 'number' || rectWidth < 0 || rectHeight < 0) {
          throw new CoreError(
            ErrorType.InvalidParameter,
            `Invalid dimensions for target rectangle (ID: ${element2.id})`,
            undefined,
            { component: 'EllipseDistanceStrategy', operation: 'calculateDistance', target: element2.id, metadata: { rectWidth, rectHeight } },
          )
        }

        const closestX = Math.max(rectX, Math.min(center1.x, rectX + rectWidth))
        const closestY = Math.max(rectY, Math.min(center1.y, rectY + rectHeight))
        const closestPointOnRect: PointInterface = { x: closestX, y: closestY, z: center1.z }

        const distToRectSurface = calculatePointToPointDistance(new PointClass(center1.x, center1.y, center1.z), new PointClass(closestPointOnRect.x, closestPointOnRect.y, closestPointOnRect.z))

        const angleToClosest = Math.atan2(closestPointOnRect.y - center1.y, closestPointOnRect.x - center1.x)
        const cosAngle = Math.cos(angleToClosest)
        const sinAngle = Math.sin(angleToClosest)
        const ellipseRadiusAtAngle = (radiusX1 * radiusY1) / Math.sqrt(
          (radiusX1 * sinAngle) ** 2 + (radiusY1 * cosAngle) ** 2,
        )

        return Math.max(0, distToRectSurface - ellipseRadiusAtAngle)
      }

      case CoreElementType.POLYGON:
      case CoreElementType.TRIANGLE:
      case CoreElementType.HEXAGON: {
        const polygonElement = element2 as unknown as Shape.Polygon // Added unknown
        const polyPoints_rel = polygonElement.properties?.points as Array<{ x: number, y: number, z?: number }> | undefined

        if (polyPoints_rel === null || polyPoints_rel === undefined || !Array.isArray(polyPoints_rel) || polyPoints_rel.length < 2) {
          throw new CoreError(
            ErrorType.InvalidParameter,
            `Target polygon (ID: ${element2.id}) has insufficient points.`,
            undefined,
            { component: 'EllipseDistanceStrategy', operation: 'calculateDistance', target: element2.id, metadata: { numPoints: Array.isArray(polyPoints_rel) ? polyPoints_rel.length : 0 } },
          )
        }
        let minDistance = Number.POSITIVE_INFINITY
        const el2PosX = element2.position.x // From ShapeElement
        const el2PosY = element2.position.y // From ShapeElement

        for (let i = 0; i < polyPoints_rel.length; i++) {
          const p1_rel = polyPoints_rel[i]
          const p2_rel = polyPoints_rel[(i + 1) % polyPoints_rel.length]
          if (p1_rel !== null && p1_rel !== undefined && typeof p1_rel.x === 'number' && typeof p1_rel.y === 'number'
            && p2_rel !== null && p2_rel !== undefined && typeof p2_rel.x === 'number' && typeof p2_rel.y === 'number') {
            const p1_abs: PointInterface = { x: p1_rel.x + el2PosX, y: p1_rel.y + el2PosY, z: typeof p1_rel.z === 'number' ? p1_rel.z : 0 }
            const p2_abs: PointInterface = { x: p2_rel.x + el2PosX, y: p2_rel.y + el2PosY, z: typeof p2_rel.z === 'number' ? p2_rel.z : 0 }
            const distCenterToSegment = calculatePointToLineSegmentDistance(center1, p1_abs, p2_abs)
            minDistance = Math.min(minDistance, distCenterToSegment)
          }
        }
        const avgRadius = (radiusX1 + radiusY1) / 2
        return Math.max(0, minDistance - avgRadius)
      }

      case CoreElementType.LINE: {
        const lineElement = element2 as unknown as Path.Line // Added unknown
        const lineStart_rel = lineElement.properties.start
        const lineEnd_rel = lineElement.properties.end
        const el2Pos = element2.position // From ShapeElement

        if (lineStart_rel === null || lineStart_rel === undefined || lineEnd_rel === null || lineEnd_rel === undefined || el2Pos === null || el2Pos === undefined
          || typeof lineStart_rel.x !== 'number' || typeof lineStart_rel.y !== 'number'
          || typeof lineEnd_rel.x !== 'number' || typeof lineEnd_rel.y !== 'number'
          || typeof el2Pos.x !== 'number' || typeof el2Pos.y !== 'number') {
          throw new CoreError(
            ErrorType.InvalidParameter,
            `Target line element (ID: ${element2.id}) has invalid start/end points or position.`,
            undefined,
            { component: 'EllipseDistanceStrategy', operation: 'calculateDistance', target: element2.id, metadata: { lineStart_rel, lineEnd_rel, el2Pos } },
          )
        }
        const lineStart_abs: PointInterface = { x: lineStart_rel.x + el2Pos.x, y: lineStart_rel.y + el2Pos.y, z: typeof lineStart_rel.z === 'number' ? lineStart_rel.z : 0 }
        const lineEnd_abs: PointInterface = { x: lineEnd_rel.x + el2Pos.x, y: lineEnd_rel.y + el2Pos.y, z: typeof lineEnd_rel.z === 'number' ? lineEnd_rel.z : 0 }

        const distCenterToSegment = calculatePointToLineSegmentDistance(center1, lineStart_abs, lineEnd_abs)
        const avgRadius = (radiusX1 + radiusY1) / 2
        return Math.max(0, distCenterToSegment - avgRadius)
      }

      default:
        throw new CoreError(
          ErrorType.InvalidElementType,
          `Distance calculation between ${element1.type} and ${element2.type} is not supported by EllipseDistanceStrategy.`,
          undefined,
          { component: 'EllipseDistanceStrategy', operation: 'calculateDistance', metadata: { type1: element1.type, type2: element2.type } },
        )
    }
  }

  /**
   * Returns the primary element type that this strategy handles as the first argument.
   * @remarks This strategy handles both `ELLIPSE` and `CIRCLE` types.
   * @returns {@link CoreElementType.ELLIPSE} - The ellipse element type that this strategy handles
   */
  public getElementType(): CoreElementType {
    return CoreElementType.ELLIPSE
  }
}
