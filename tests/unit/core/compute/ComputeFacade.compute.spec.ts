/**
 * @file ComputeFacade.compute.spec.ts
 * @description Unit tests for the ComputeFacade compute methods
 */

import type { StrategyRegistry } from '@/core/compute/StrategyRegistry'
import type { ElementFactory } from '@/core/factory/ElementFactory'
import type { ShapeRepository } from '@/core/state/ShapeRepository'
import type { Element } from '@/types/core/element/element'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ComputeFacade } from '@/core/compute/ComputeFacade'
import { CoreError, ErrorType } from '@/core/errors'

// Mock console methods
const mockConsoleWarn = vi.spyOn(console, 'warn').mockImplementation(() => {})
const mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => {})

describe('computeFacade - Compute Methods', () => {
  let computeFacade: ComputeFacade
  let mockStrategyRegistry: StrategyRegistry
  let mockShapeRepository: ShapeRepository
  let mockElementFactory: ElementFactory
  let mockAreaStrategy: any
  let mockPerimeterStrategy: any
  let mockBoundingBoxStrategy: any
  let mockIsPointInsideStrategy: any

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()

    // Create mock dependencies
    mockStrategyRegistry = {
      getAreaStrategy: vi.fn(),
      getPerimeterStrategy: vi.fn(),
      getBoundingBoxStrategy: vi.fn(),
      getIsPointInsideStrategy: vi.fn(),
      getTransformStrategy: vi.fn(),
      hasStrategy: vi.fn(),
      getSupportedElementTypes: vi.fn(),
      registerStrategies: vi.fn(),
    } as unknown as StrategyRegistry

    mockShapeRepository = {
      getById: vi.fn(),
      getAll: vi.fn(),
      add: vi.fn(),
      update: vi.fn(),
      remove: vi.fn(),
      clear: vi.fn(),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
    } as unknown as ShapeRepository

    mockElementFactory = {
      createShape: vi.fn(),
      createDefaultShape: vi.fn(),
    } as unknown as ElementFactory

    mockAreaStrategy = {
      calculateArea: vi.fn(),
      getElementType: vi.fn(() => 'rectangle'),
    }

    mockPerimeterStrategy = {
      calculatePerimeter: vi.fn(),
      getElementType: vi.fn(() => 'rectangle'),
    }

    mockBoundingBoxStrategy = {
      calculateBoundingBox: vi.fn(),
      getElementType: vi.fn(() => 'rectangle'),
    }

    mockIsPointInsideStrategy = {
      isPointInside: vi.fn(),
      getElementType: vi.fn(() => 'rectangle'),
    }

    // Create ComputeFacade instance with mocks
    computeFacade = new ComputeFacade(
      mockStrategyRegistry,
      mockShapeRepository,
      mockElementFactory,
    )
  })

  describe('calculateArea', () => {
    it('should calculate area correctly', async () => {
      // Setup mock data
      const shapeId = 'rect-1'

      // Setup mock repository to return shape model
      mockShapeRepository.getById.mockReturnValue({
        id: shapeId,
        type: 'rectangle',
        position: { x: 100, y: 100 },
        properties: {
          type: 'rectangle',
          width: 50,
          height: 30,
        },
      })

      // Setup mock factory to create element instance
      const mockElement = {
        id: shapeId,
        type: 'rectangle',
        position: { x: 100, y: 100 },
        width: 50,
        height: 30,
        getSubType: () => 'rectangle',
      } as unknown as Element

      mockElementFactory.createShape.mockResolvedValue(mockElement)

      // Setup mock strategy registry to return area strategy
      mockStrategyRegistry.getAreaStrategy.mockReturnValue(mockAreaStrategy)

      // Setup mock area strategy to return area
      mockAreaStrategy.calculateArea.mockReturnValue(1500)

      // Call the method
      const area = await computeFacade.computeArea(shapeId)

      // Verify repository was called to get shape
      expect(mockShapeRepository.getById).toHaveBeenCalledWith(shapeId)

      // Verify factory was called to create element
      expect(mockElementFactory.createShape).toHaveBeenCalled()

      // Verify strategy registry was called to get area strategy
      expect(mockStrategyRegistry.getAreaStrategy).toHaveBeenCalledWith('rectangle')

      // Verify area strategy was called to calculate area
      expect(mockAreaStrategy.calculateArea).toHaveBeenCalledWith(mockElement)

      // Verify the result
      expect(area).toBe(1500)
    })

    it('should throw CoreError when shape not found', async () => {
      // Setup mock data
      const shapeId = 'unknown-id'

      // Setup mock repository to return null (shape not found)
      mockShapeRepository.getById.mockReturnValue(null)

      // Call the method and expect it to throw
      await expect(computeFacade.computeArea(shapeId))
        .rejects
        .toThrow(CoreError)

      // Verify the error type
      await expect(computeFacade.computeArea(shapeId))
        .rejects
        .toMatchObject({
          type: ErrorType.COORDINATOR_SHAPE_NOT_FOUND,
        })
    })

    it('should propagate CoreError from getElementInstance', async () => {
      // Setup mock data
      const shapeId = 'rect-1'

      // Setup mock repository to return shape model
      mockShapeRepository.getById.mockReturnValue({
        id: shapeId,
        type: 'rectangle',
      })

      // Setup mock factory to throw CoreError
      mockElementFactory.createShape.mockImplementation(() => {
        throw new CoreError(ErrorType.FACTORY_FAILED, 'Factory error')
      })

      // Call the method and expect it to throw
      await expect(computeFacade.computeArea(shapeId))
        .rejects
        .toThrow(CoreError)

      // Verify the error type
      await expect(computeFacade.computeArea(shapeId))
        .rejects
        .toMatchObject({
          type: ErrorType.FACTORY_FAILED,
        })
    })

    it('should wrap non-CoreError exceptions', async () => {
      // Setup mock data
      const shapeId = 'rect-1'

      // Setup mock repository to return shape model
      mockShapeRepository.getById.mockReturnValue({
        id: shapeId,
        type: 'rectangle',
      })

      // Setup mock factory to throw regular Error
      mockElementFactory.createShape.mockImplementation(() => {
        throw new Error('Factory error')
      })

      // Call the method and expect it to throw
      await expect(computeFacade.computeArea(shapeId))
        .rejects
        .toThrow(CoreError)

      // Verify the error type
      await expect(computeFacade.computeArea(shapeId))
        .rejects
        .toMatchObject({
          type: ErrorType.FACTORY_FAILED,
        })
    })
  })

  describe('calculatePerimeter', () => {
    it('should calculate perimeter correctly', async () => {
      // Setup mock data
      const shapeId = 'rect-1'

      // Setup mock repository to return shape model
      mockShapeRepository.getById.mockReturnValue({
        id: shapeId,
        type: 'rectangle',
        position: { x: 100, y: 100 },
        properties: {
          type: 'rectangle',
          width: 50,
          height: 30,
        },
      })

      // Setup mock factory to create element instance
      const mockElement = {
        id: shapeId,
        type: 'rectangle',
        position: { x: 100, y: 100 },
        width: 50,
        height: 30,
        getSubType: () => 'rectangle',
      } as unknown as Element

      mockElementFactory.createShape.mockResolvedValue(mockElement)

      // Setup mock strategy registry to return perimeter strategy
      mockStrategyRegistry.getPerimeterStrategy.mockReturnValue(mockPerimeterStrategy)

      // Setup mock perimeter strategy to return perimeter
      mockPerimeterStrategy.calculatePerimeter.mockReturnValue(160)

      // Call the method
      const perimeter = await computeFacade.computePerimeter(shapeId)

      // Verify repository was called to get shape
      expect(mockShapeRepository.getById).toHaveBeenCalledWith(shapeId)

      // Verify factory was called to create element
      expect(mockElementFactory.createShape).toHaveBeenCalled()

      // Verify strategy registry was called to get perimeter strategy
      expect(mockStrategyRegistry.getPerimeterStrategy).toHaveBeenCalledWith('rectangle')

      // Verify perimeter strategy was called to calculate perimeter
      expect(mockPerimeterStrategy.calculatePerimeter).toHaveBeenCalledWith(mockElement)

      // Verify the result
      expect(perimeter).toBe(160)
    })

    it('should throw CoreError when shape not found', async () => {
      // Setup mock data
      const shapeId = 'unknown-id'

      // Setup mock repository to return null (shape not found)
      mockShapeRepository.getById.mockReturnValue(null)

      // Call the method and expect it to throw
      await expect(computeFacade.computePerimeter(shapeId))
        .rejects
        .toThrow(CoreError)

      // Verify the error type
      await expect(computeFacade.computePerimeter(shapeId))
        .rejects
        .toMatchObject({
          type: ErrorType.COORDINATOR_SHAPE_NOT_FOUND,
        })
    })
  })

  describe('calculateBoundingBox', () => {
    it('should calculate bounding box correctly', async () => {
      // Setup mock data
      const shapeId = 'rect-1'

      // Setup mock repository to return shape model
      mockShapeRepository.getById.mockReturnValue({
        id: shapeId,
        type: 'rectangle',
        position: { x: 100, y: 100 },
        properties: {
          type: 'rectangle',
          width: 50,
          height: 30,
        },
      })

      // Setup mock factory to create element instance
      const mockElement = {
        id: shapeId,
        type: 'rectangle',
        position: { x: 100, y: 100 },
        width: 50,
        height: 30,
        getSubType: () => 'rectangle',
      } as unknown as Element

      mockElementFactory.createShape.mockResolvedValue(mockElement)

      // Setup mock strategy registry to return bounding box strategy
      mockStrategyRegistry.getBoundingBoxStrategy.mockReturnValue(mockBoundingBoxStrategy)

      // Setup mock bounding box strategy to return bounding box
      const mockBoundingBox = { x: 100, y: 100, width: 50, height: 30 }
      mockBoundingBoxStrategy.calculateBoundingBox.mockReturnValue(mockBoundingBox)

      // Call the method
      const boundingBox = await computeFacade.computeBoundingBox(shapeId)

      // Verify repository was called to get shape
      expect(mockShapeRepository.getById).toHaveBeenCalledWith(shapeId)

      // Verify factory was called to create element
      expect(mockElementFactory.createShape).toHaveBeenCalled()

      // Verify strategy registry was called to get bounding box strategy
      expect(mockStrategyRegistry.getBoundingBoxStrategy).toHaveBeenCalledWith('rectangle')

      // Verify bounding box strategy was called to calculate bounding box
      expect(mockBoundingBoxStrategy.calculateBoundingBox).toHaveBeenCalledWith(mockElement)

      // Verify the result
      expect(boundingBox).toBe(mockBoundingBox)
    })
  })

  describe('isPointInside', () => {
    it('should check if point is inside correctly', async () => {
      // Setup mock data
      const shapeId = 'rect-1'
      const x = 120
      const y = 110

      // Setup mock repository to return shape model
      mockShapeRepository.getById.mockReturnValue({
        id: shapeId,
        type: 'rectangle',
        position: { x: 100, y: 100 },
        properties: {
          type: 'rectangle',
          width: 50,
          height: 30,
        },
      })

      // Setup mock factory to create element instance
      const mockElement = {
        id: shapeId,
        type: 'rectangle',
        position: { x: 100, y: 100 },
        width: 50,
        height: 30,
        getSubType: () => 'rectangle',
      } as unknown as Element

      mockElementFactory.createShape.mockResolvedValue(mockElement)

      // Setup mock strategy registry to return is-point-inside strategy
      mockStrategyRegistry.getIsPointInsideStrategy.mockReturnValue(mockIsPointInsideStrategy)

      // Setup mock is-point-inside strategy to return true
      mockIsPointInsideStrategy.isPointInside.mockReturnValue(true)

      // Call the method
      const isInside = await computeFacade.isPointInside(shapeId, x, y)

      // Verify repository was called to get shape
      expect(mockShapeRepository.getById).toHaveBeenCalledWith(shapeId)

      // Verify factory was called to create element
      expect(mockElementFactory.createShape).toHaveBeenCalled()

      // Verify strategy registry was called to get is-point-inside strategy
      expect(mockStrategyRegistry.getIsPointInsideStrategy).toHaveBeenCalledWith('rectangle')

      // Verify is-point-inside strategy was called to check if point is inside
      expect(mockIsPointInsideStrategy.isPointInside).toHaveBeenCalledWith(mockElement, x, y)

      // Verify the result
      expect(isInside).toBe(true)
    })

    it('should return false when point is outside', async () => {
      // Setup mock data
      const shapeId = 'rect-1'
      const x = 200
      const y = 200

      // Setup mock repository to return shape model
      mockShapeRepository.getById.mockReturnValue({
        id: shapeId,
        type: 'rectangle',
        position: { x: 100, y: 100 },
        properties: {
          type: 'rectangle',
          width: 50,
          height: 30,
        },
      })

      // Setup mock factory to create element instance
      const mockElement = {
        id: shapeId,
        type: 'rectangle',
        position: { x: 100, y: 100 },
        width: 50,
        height: 30,
        getSubType: () => 'rectangle',
      } as unknown as Element

      mockElementFactory.createShape.mockResolvedValue(mockElement)

      // Setup mock strategy registry to return is-point-inside strategy
      mockStrategyRegistry.getIsPointInsideStrategy.mockReturnValue(mockIsPointInsideStrategy)

      // Setup mock is-point-inside strategy to return false
      mockIsPointInsideStrategy.isPointInside.mockReturnValue(false)

      // Call the method
      const isInside = await computeFacade.isPointInside(shapeId, x, y)

      // Verify the result
      expect(isInside).toBe(false)
    })
  })

  describe('getElementInstance', () => {
    it('should get element instance correctly', async () => {
      // Setup mock data
      const shapeId = 'rect-1'

      // Setup mock repository to return shape model
      const mockShapeModel = {
        id: shapeId,
        type: 'rectangle',
        position: { x: 100, y: 100 },
        properties: {
          type: 'rectangle',
          width: 50,
          height: 30,
        },
      }
      mockShapeRepository.getById.mockReturnValue(mockShapeModel)

      // Setup mock factory to create element instance
      const mockElement = {
        id: shapeId,
        type: 'rectangle',
        position: { x: 100, y: 100 },
        width: 50,
        height: 30,
      } as unknown as Element

      mockElementFactory.createShape.mockResolvedValue(mockElement)

      // Call the method
      const element = await computeFacade.getElementInstance(shapeId)

      // Verify repository was called to get shape
      expect(mockShapeRepository.getById).toHaveBeenCalledWith(shapeId)

      // Verify factory was called to create element
      expect(mockElementFactory.createShape).toHaveBeenCalledWith('rectangle', mockShapeModel)

      // Verify the result
      expect(element).toBe(mockElement)
    })

    it('should return null when shape not found', async () => {
      // Setup mock data
      const shapeId = 'unknown-id'

      // Setup mock repository to return null (shape not found)
      mockShapeRepository.getById.mockReturnValue(null)

      // Call the method
      const element = await computeFacade.getElementInstance(shapeId)

      // Verify the result
      expect(element).toBeUndefined()
    })
  })
})
