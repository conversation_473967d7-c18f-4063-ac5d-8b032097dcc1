/**
 * Centralized export for media creator classes.
 *
 * @remarks
 * This index file serves as the public entry point for all media creator implementations
 * within the `@/core/factory/creators/media` directory. It re-exports the concrete
 * creator classes for various media types, such as {@link ImageCreator} and {@link TextCreator}.
 *
 * @module core/factory/creators/media/index
 */

// Export concrete media creator implementations
export { default as ImageCreator } from './ImageCreator'
export { TextCreator } from './TextCreator'
