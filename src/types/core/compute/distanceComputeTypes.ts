/**
 * Defines the Strategy Interface for Distance Calculation
 *
 * @remarks
 * This module introduces the {@link DistanceCalculatorStrategy} interface,
 * a core component of the strategy design pattern applied to geometric computations.
 * This pattern allows for the encapsulation of different distance calculation algorithms,
 * which can be tailored to various {@link Element | elements} or specific scenarios,
 * such as calculating the distance between two elements or from an element to a point.
 *
 * Concrete implementations of this interface will provide the specific logic for
 * these distance computations.
 *
 * @module types/core/compute/distanceComputeTypes
 * @see {@link Element} from '@/types/core/elementDefinitions'
 * @see {@link ../computeInterfaces} for other computation strategy interfaces.
 */
import type { ShapeElement } from '@/types/core/elementDefinitions'

/**
 * Defines the contract for a distance calculation strategy.
 *
 * @remarks
 * Any class implementing this interface is responsible for providing a specific
 * algorithm to calculate distances involving one or more {@link Element | elements}.
 * Concrete strategies might specialize in point-to-element, element-to-element,
 * or other types of distance measurements, potentially for specific
 * {@link ElementType | element types}.
 *
 * @see {@link Element} from '@/types/core/elementDefinitions'
 * @see {@link ElementType} from '@/types/core/elementDefinitions'
 */
export interface DistanceCalculatorStrategy {
  /**
   * Calculates the shortest distance between two given elements.
   *
   * @param elementA - The first {@link ShapeElement} from which to measure the distance.
   * @param elementB - The second {@link ShapeElement} to which to measure the distance.
   * @returns The shortest distance between the two elements as a `number`.
   *          The units of the distance are implicitly defined by the units used
   *          in the elements' coordinates and dimensions.
   */
  calculateDistance: (elementA: ShapeElement, elementB: ShapeElement) => number

  /**
   * Gets the specific element type (or types) that this strategy is primarily designed to handle or process.
   *
   * @returns A string identifier (or an array of string identifiers) corresponding to
   *          one or more {@link ElementType | ElementTypes} that this strategy supports or focuses on.
   * @remarks Consider returning `ElementType` or `ElementType[]` for enhanced type safety in future revisions.
   */
  getElementType: () => string | string[]
}
