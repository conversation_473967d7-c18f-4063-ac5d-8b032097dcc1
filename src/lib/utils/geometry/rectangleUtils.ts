/**
 * Utility Functions for Rectangle Geometry
 *
 * @remarks
 * This module provides utility functions for calculating geometric properties of
 * rectangles, specifically their area and perimeter, given their width and height.
 *
 * @module lib/utils/geometry/rectangleUtils
 */

// import type { IPoint } from '../../../types/core/element/geometry/point'; // If needed for future functions
// import type { BoundingBox as IGeometryBoundingBox } from '../../../types/core/element/geometry/bounding-box'; // If needed

/**
 * Calculates the area of a rectangle.
 *
 * @param width - The width of the rectangle. Must be a non-negative number.
 * @param height - The height of the rectangle. Must be a non-negative number.
 * @returns The area of the rectangle. Returns `NaN` if `width` or `height` is negative or not a number.
 */
export function calculateRectangleArea(width: number, height: number): number {
  if (typeof width !== 'number' || typeof height !== 'number' || isNaN(width) || isNaN(height) || width < 0 || height < 0) {
    console.warn('calculateRectangleArea: Width and height must be non-negative numbers. Returning NaN.')
    return Number.NaN
  }
  return width * height
}

/**
 * Calculates the perimeter of a rectangle.
 *
 * @remarks
 * If negative values are provided for width or height, their absolute values are used
 * for the calculation, though a warning is logged as dimensions should ideally be non-negative.
 *
 * @param width - The width of the rectangle.
 * @param height - The height of the rectangle.
 * @returns The perimeter of the rectangle. Returns `NaN` if `width` or `height` is not a valid number.
 */
export function calculateRectanglePerimeter(width: number, height: number): number {
  if (typeof width !== 'number' || typeof height !== 'number' || isNaN(width) || isNaN(height)) {
    console.warn('calculateRectanglePerimeter: Width and height must be valid numbers. Returning NaN.')
    return Number.NaN
  }
  // Perimeter calculation usually uses absolute values if negative dimensions are possible,
  // but for area, negative dimensions are typically treated as invalid.
  // For consistency with area, let's assume width/height should be non-negative for perimeter too.
  if (width < 0 || height < 0) {
    console.warn('calculateRectanglePerimeter: Width and height should ideally be non-negative. Calculating with absolute values.')
  }
  const absWidth = Math.abs(width)
  const absHeight = Math.abs(height)
  return 2 * (absWidth + absHeight)
}

// TODO: Add calculateRectangleBoundingBox if it's different from the rectangle itself.
// For an axis-aligned rectangle, its bounding box is itself.
// If it can be rotated, then a bounding box calculation is needed.
// The one in `src/lib/utils/index.ts` (calculateRectangleBoundingBox from './geometry')
// was an alias to BoundingBox.fromPoints, which implies it might be for a rotated one or from points.
