import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/core/factory/creators/ShapeCreator'
import type { ShapeCreationParamsUnion } from '@/core/factory/ElementFactory'
import type { ShapeModel } from '@/types/core/models'
import { describe, expect, it, vi } from 'vitest'
import { CoreError, ErrorType } from '@/core/errors'
import { Point } from '@/types/core/element/geometry/point'
import { ElementType } from '@/types/core/shape-type'

describe('shapeCreator', () => {
  // 创建一个实现 ShapeCreator 接口的模拟类
  class MockShapeCreator implements ShapeCreator {
    create = vi.fn().mockImplementation(async (params: ShapeCreationParamsUnion): Promise<ShapeModel> => {
      return {
        id: params.id || 'mock-id',
        type: params.type,
        position: { x: 0, y: 0 },
        properties: {
          type: params.type,
        },
        selected: false,
        visible: true,
        metadata: {
          createdAt: Date.now(),
          updatedAt: Date.now(),
        },
      }
    })

    createDefault = vi.fn().mockImplementation(async (id: string, position: Point): Promise<ShapeModel> => {
      return {
        id,
        type: ElementType.RECTANGLE,
        position: { x: position.x, y: position.y },
        properties: {
          type: ElementType.RECTANGLE,
        },
        selected: false,
        visible: true,
        metadata: {
          createdAt: Date.now(),
          updatedAt: Date.now(),
        },
      }
    })
  }

  describe('接口实现', () => {
    it('应该能够创建一个实现 ShapeCreator 接口的类', () => {
      const creator = new MockShapeCreator()
      expect(creator).toBeDefined()
      expect(typeof creator.create).toBe('function')
      expect(typeof creator.createDefault).toBe('function')
    })

    it('create 方法应该接受 ShapeCreationParamsUnion 参数并返回 Promise<ShapeModel>', async () => {
      const creator = new MockShapeCreator()
      const params = {
        id: 'test-id',
        type: ElementType.RECTANGLE,
      }

      const result = await creator.create(params)

      expect(creator.create).toHaveBeenCalledWith(params)
      expect(result).toEqual({
        id: 'test-id',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: {
          type: ElementType.RECTANGLE,
        },
        selected: false,
        visible: true,
        metadata: expect.objectContaining({
          createdAt: expect.any(Number),
          updatedAt: expect.any(Number),
        }),
      })
    })

    it('createDefault 方法应该接受 id 和 position 参数并返回 Promise<ShapeModel>', async () => {
      const creator = new MockShapeCreator()
      const id = 'default-id'
      const position = new Point(10, 20)

      const result = await creator.createDefault(id, position)

      expect(creator.createDefault).toHaveBeenCalledWith(id, position)
      expect(result).toEqual({
        id: 'default-id',
        type: ElementType.RECTANGLE,
        position: { x: 10, y: 20 },
        properties: {
          type: ElementType.RECTANGLE,
        },
        selected: false,
        visible: true,
        metadata: expect.objectContaining({
          createdAt: expect.any(Number),
          updatedAt: expect.any(Number),
        }),
      })
    })

    it('create 方法应该处理没有 ID 的情况并生成一个 ID', async () => {
      const creator = new MockShapeCreator()
      const params = {
        type: ElementType.RECTANGLE,
      }

      const result = await creator.create(params)

      expect(result.id).toBe('mock-id')
    })

    it('create 方法应该处理错误情况', async () => {
      const creator = new MockShapeCreator()
      creator.create = vi.fn().mockRejectedValue(new Error('Test error'))

      const params = {
        id: 'test-id',
        type: ElementType.RECTANGLE,
      }

      await expect(creator.create(params)).rejects.toThrow('Test error')
    })

    it('create 方法应该处理 CoreError 错误', async () => {
      const creator = new MockShapeCreator()
      const coreError = new CoreError(ErrorType.INVALID_PAYLOAD, 'Invalid payload')
      creator.create = vi.fn().mockRejectedValue(coreError)

      const params = {
        id: 'test-id',
        type: ElementType.RECTANGLE,
      }

      await expect(creator.create(params)).rejects.toThrow(coreError)
    })

    it('createDefault 方法应该处理错误情况', async () => {
      const creator = new MockShapeCreator()
      creator.createDefault = vi.fn().mockRejectedValue(new Error('Test error'))

      const id = 'default-id'
      const position = new Point(10, 20)

      await expect(creator.createDefault(id, position)).rejects.toThrow('Test error')
    })

    it('createDefault 方法应该处理 CoreError 错误', async () => {
      const creator = new MockShapeCreator()
      const coreError = new CoreError(ErrorType.INVALID_PAYLOAD, 'Invalid payload')
      creator.createDefault = vi.fn().mockRejectedValue(coreError)

      const id = 'default-id'
      const position = new Point(10, 20)

      await expect(creator.createDefault(id, position)).rejects.toThrow(coreError)
    })
  })
})
