/**
 * Service Identifier Types
 *
 * This file defines the interface and types for service identifiers used in the application.
 *
 * @module types/services/core
 */

/**
 * Service identifier type - branded string to ensure type safety.
 */
export type ServiceIdType = string & { __brand: 'ServiceId' }

/**
 * Service identifier enum with all available service identifiers.
 *
 * These identifiers are used for service registration and retrieval
 * through the service registry.
 */
export enum ServiceId {
  // Core services
  EventBus = 'event-bus',
  Logger = 'logger',
  ErrorService = 'error-service',
  ValidationService = 'validation-service',
  ShapeRepository = 'shape-repository',

  // Element action services
  ElementCreationService = 'element-creation-service',
  ElementEditService = 'element-edit-service',
  ElementDeleteService = 'element-delete-service',
  ElementSelectionService = 'element-selection-service',

  // Other services
  KeyboardService = 'keyboard-service',
  StorageService = 'storage-service',
}

/**
 * Creates a branded service ID for type safety.
 *
 * @param id - The service ID string
 * @returns A branded service ID
 */
export function createServiceId(id: string): ServiceIdType {
  return id as ServiceIdType
}

/**
 * Gets a branded service ID from a ServiceId enum value.
 *
 * @param id - The service ID from the ServiceId enum
 * @returns A branded service ID
 */
export function getServiceId(id: ServiceId): ServiceIdType {
  return createServiceId(id)
}
