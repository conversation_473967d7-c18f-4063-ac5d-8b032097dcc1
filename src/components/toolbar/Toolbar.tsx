/**
 * Toolbar Component
 *
 * A comprehensive toolbar component that provides access to application controls
 * and tools. Includes the application name, file operations, undo/redo,
 * interaction modes, zoom controls, and sidebar toggles.
 *
 * Features:
 * - Application name display
 * - File operations (new, save, export)
 * - Undo/redo functionality
 * - Interaction mode switching (select, pan)
 * - Zoom and view controls (zoom in/out, reset zoom, fullscreen)
 * - Sidebar toggles, keyboard shortcuts, and settings access
 * - Tooltips for all controls
 */

'use client'

import {
  Download,
  FileIcon,
  Grid3x3,
  Hand,
  Keyboard,
  MousePointer,
  PanelRightClose,
  PanelRightOpen,
  Redo2,
  RefreshCw,
  Settings,
  SidebarClose,
  SidebarOpen,
  Undo2,
  ZoomIn,
  ZoomOut,
} from 'lucide-react'
import React, { useEffect, useRef, useState } from 'react'
// import { ElementType as CoreElementType } from '@/types/core/elementDefinitions'; // Unused
import { KeyboardShortcutsContent } from '@/components/dialogs/KeyboardShortcutsGuide'
import { TutorialTrigger } from '@/components/tutorial/TutorialTrigger'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Separator } from '@/components/ui/separator'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { useExport } from '@/hooks/useExport'
import useKeyboardShortcuts, { ToolbarShortcutUtils } from '@/hooks/useKeyboardShortcuts'
import ExportMenu from './ExportMenu'

// Define toolbar settings type
interface ToolbarSettings {
  showFileOperations: boolean
  showUndoRedo: boolean
  showInteractionModes: boolean
  showZoomControls: boolean
  showCanvasInfo: boolean
  showGridToggle: boolean
  showKeyboardShortcuts: boolean
  showTutorial: boolean
  showSettings: boolean
}

// Define default toolbar settings
const defaultToolbarSettings: ToolbarSettings = {
  showFileOperations: true,
  showUndoRedo: true,
  showInteractionModes: true,
  showZoomControls: true,
  showCanvasInfo: true,
  showGridToggle: true,
  showKeyboardShortcuts: true,
  showTutorial: true,
  showSettings: true,
}

// Type guard for toolbar settings
function isValidToolbarSettings(value: unknown): value is Partial<ToolbarSettings> {
  return (
    typeof value === 'object'
    && value !== null
    && Object.keys(value).every(key =>
      key in defaultToolbarSettings
      && typeof (value as Record<string, unknown>)[key] === 'boolean',
    )
  )
}

/**
 * Props for the Toolbar component.
 */
interface ToolbarProps {
  /** Callback function triggered when the 'New' action is invoked. */
  onNew: () => void
  /** Callback function triggered when the 'Save' action is invoked. */
  onSave: () => void
  /** Callback function triggered when the 'Export' action is invoked. */
  onExport: () => void
  /** Callback function triggered when the 'Undo' action is invoked. */
  onUndo: () => void
  /** Callback function triggered when the 'Redo' action is invoked. */
  onRedo: () => void
  /** Boolean indicating if the 'Undo' action is currently available. */
  canUndo: boolean
  /** Boolean indicating if the 'Redo' action is currently available. */
  canRedo: boolean
  /** Boolean indicating if canvas pan mode is active. */
  isPanMode: boolean
  /** Callback function to toggle canvas interaction mode (e.g., pan/select). */
  onToggleMode: () => void
  /** Callback function to zoom in on the canvas. */
  onZoomIn: () => void
  /** Callback function to zoom out on the canvas. */
  onZoomOut: () => void
  /** Callback function to reset the canvas zoom to default. */
  onResetZoom: () => void
  /** Current zoom level of the canvas. */
  currentZoom: number
  /** Boolean indicating if the layer panel (left sidebar) is open. */
  isLayerPanelOpen: boolean
  /** Boolean indicating if the property sidebar (right sidebar) is open. */
  isPropertySidebarOpen: boolean
  /** Callback function to toggle the visibility of the layer panel. */
  onToggleLayerPanel: () => void
  /** Callback function to toggle the visibility of the property sidebar. */
  onTogglePropertySidebar: () => void
  /** Boolean indicating if the keyboard shortcuts guide dialog is open. */
  isKeyboardShortcutsOpen: boolean
  /** Callback function triggered when the keyboard shortcuts guide dialog open state changes. */
  onKeyboardShortcutsOpenChange: (open: boolean) => void
  /** Optional callback function to open the settings dialog/panel. */
  onOpenSettings?: () => void
  /** Optional string representing the formatted physical width of the canvas. */
  formattedCanvasWidth?: string
  /** Optional string representing the formatted physical height of the canvas. */
  formattedCanvasHeight?: string
  /** The current name of the project. */
  projectName: string
  /** Callback function triggered when the project name changes. */
  onProjectNameChange: (newName: string) => void
  /** @deprecated Use `isLayerPanelOpen` or similar specific props instead. Optional boolean indicating if a generic left sidebar is open. */
  isLeftSidebarOpen?: boolean
  /** @deprecated Use `isPropertySidebarOpen` or similar specific props instead. Optional boolean indicating if a generic right sidebar is open. */
  isRightSidebarOpen?: boolean
  /** @deprecated Use `onToggleLayerPanel` or similar specific props instead. Optional callback to toggle a generic left sidebar. */
  onToggleLeftSidebar?: () => void
  /** @deprecated Use `onTogglePropertySidebar` or similar specific props instead. Optional callback to toggle a generic right sidebar. */
  onToggleRightSidebar?: () => void

  // Add missing props related to grid
  showGrid: boolean
  onToggleGrid: () => void
}

/**
 * Top toolbar component providing editing controls, view adjustments, and application actions.
 * It allows users to perform file operations, undo/redo changes, switch interaction modes,
 * control zoom levels, manage project name, and access help/settings.
 */
const Toolbar: React.FC<ToolbarProps> = ({
  onNew,
  onSave,
  onExport,
  onUndo,
  onRedo,
  canUndo,
  canRedo,
  isPanMode,
  onToggleMode,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  currentZoom,
  isLayerPanelOpen,
  isPropertySidebarOpen,
  onToggleLayerPanel,
  onTogglePropertySidebar,
  isKeyboardShortcutsOpen,
  onKeyboardShortcutsOpenChange,
  onOpenSettings,
  formattedCanvasWidth,
  formattedCanvasHeight,
  projectName,
  onProjectNameChange,
  showGrid,
  onToggleGrid,
}) => {
  // Determine operating system for shortcut display
  // Using navigator.userAgent instead of deprecated navigator.platform
  const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.userAgent)

  // Load toolbar visibility settings with type safety
  const [toolbarSettings, setToolbarSettings] = useState<ToolbarSettings>(() => {
    const saved = localStorage.getItem('RenoPilot.toolbarSettings')
    if (saved !== null && saved.trim() !== '') {
      try {
        const parsed: unknown = JSON.parse(saved)
        // Validate that parsed object has the expected structure
        if (isValidToolbarSettings(parsed)) {
          return { ...defaultToolbarSettings, ...parsed }
        }
      }
      catch (error) {
        console.warn('Failed to parse toolbar settings from localStorage:', error)
      }
    }
    return defaultToolbarSettings
  })

  // Listen for changes to toolbar settings
  useEffect(() => {
    const handleStorageChange = () => {
      const saved = localStorage.getItem('RenoPilot.toolbarSettings')
      if (saved !== null && saved.trim() !== '') {
        try {
          const parsed: unknown = JSON.parse(saved)
          if (isValidToolbarSettings(parsed)) {
            setToolbarSettings({ ...defaultToolbarSettings, ...parsed })
          }
        }
        catch (error) {
          console.warn('Failed to parse toolbar settings from localStorage:', error)
        }
      }
    }

    // Listen for storage events (changes from other tabs/windows)
    window.addEventListener('storage', handleStorageChange)

    // Also listen for custom events (changes from same tab)
    window.addEventListener('toolbarSettingsChanged', handleStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('toolbarSettingsChanged', handleStorageChange)
    }
  }, [])

  // 使用统一的快捷键系统
  useKeyboardShortcuts({
    // 编辑操作
    onUndo: () => {
      if (canUndo) {
        onUndo()
      }
    },
    onRedo: () => {
      if (canRedo) {
        onRedo()
      }
    },
    onDelete: () => {}, // Toolbar 不处理删除，由 EditorLayout 处理

    // 视图操作
    onZoomIn,
    onZoomOut,
    onResetZoom,
    onTogglePanMode: onToggleMode,

    // 文件操作
    onNew,
    onSave,
    onExport,

    // UI 切换
    onToggleLeftPanel: onToggleLayerPanel,
    onToggleRightPanel: onTogglePropertySidebar,
    onToggleGrid,
    onOpenSettings,
    onOpenShortcutsGuide: () => onKeyboardShortcutsOpenChange(true),
  })
  const [exportMenuOpen, setExportMenuOpen] = useState(false)
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 })
  const { exportAsSVG, exportAsPNG, exportAsPDF } = useExport()
  const exportBtnRef = useRef<HTMLButtonElement>(null)

  return (
    <div className="toolbar bg-background border-b p-2 flex items-center gap-1 sm:gap-2 select-none min-h-[56px] overflow-hidden">
      {/* Application Name / Project Name */}
      <div className="flex items-center gap-1 mr-2 min-w-0" data-tutorial="project-name">
        <span className="text-lg font-semibold whitespace-nowrap">RenoPilot -</span>
        <input
          type="text"
          value={projectName}
          onChange={e => onProjectNameChange(e.target.value)}
          className="text-lg font-semibold bg-transparent border-none focus:ring-0 focus:outline-none p-0 w-[80px] xs:w-[100px] sm:w-[140px] md:w-[180px] lg:w-[220px] xl:w-[250px] overflow-hidden text-ellipsis hover:bg-muted/50 focus:bg-muted/60 rounded-sm px-1"
          title={projectName}
        />
      </div>

      <TooltipProvider>
        {/* File operations group */}
        {toolbarSettings.showFileOperations && (
          <>
            <div className="hidden lg:flex items-center gap-1" data-tutorial="file-operations">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" onClick={onNew}>
                    <FileIcon className="h-4 w-4" />
                    <span className="sr-only">New</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>{ToolbarShortcutUtils.getFullTooltipText('New Document', 'new', isMac)}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    ref={exportBtnRef}
                    variant="ghost"
                    size="icon"
                    onClick={(_e) => {
                      const rect = exportBtnRef.current?.getBoundingClientRect()
                      if (rect) {
                        setMenuPosition({
                          x: rect.left,
                          y: rect.bottom + 5,
                        })
                        setExportMenuOpen(true)
                      }
                    }}
                  >
                    <Download className="h-4 w-4" />
                    <span className="sr-only">Export</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>{ToolbarShortcutUtils.getFullTooltipText('Export Document', 'export', isMac)}</p>
                </TooltipContent>
              </Tooltip>
            </div>

            <Separator orientation="vertical" className="h-6 hidden lg:block" />
          </>
        )}

        {/* Undo/Redo group */}
        {toolbarSettings.showUndoRedo && (
          <>
            <div className="hidden lg:flex items-center gap-1" data-tutorial="undo-redo">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      onUndo()
                    }}
                    disabled={!canUndo}
                  >
                    <Undo2 className="h-4 w-4" />
                    <span className="sr-only">Undo</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>{ToolbarShortcutUtils.getFullTooltipText('Undo', 'undo', isMac)}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      onRedo()
                    }}
                    disabled={!canRedo}
                  >
                    <Redo2 className="h-4 w-4" />
                    <span className="sr-only">Redo</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>{ToolbarShortcutUtils.getFullTooltipText('Redo', 'redo', isMac)}</p>
                </TooltipContent>
              </Tooltip>
            </div>

            <Separator orientation="vertical" className="h-6 hidden md:block" />
          </>
        )}

        {/* Interaction mode group */}
        {toolbarSettings.showInteractionModes && (
          <>
            <div className="hidden md:flex items-center gap-1" data-tutorial="interaction-modes">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={!isPanMode ? 'secondary' : 'ghost'}
                    size="icon"
                    onClick={isPanMode
                      ? () => {
                          onToggleMode()
                        }
                      : undefined}
                  >
                    <MousePointer className="h-4 w-4" />
                    <span className="sr-only">Select</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>{ToolbarShortcutUtils.getFullTooltipText('Selection Mode', 'panMode', isMac)}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={isPanMode ? 'secondary' : 'ghost'}
                    size="icon"
                    onClick={!isPanMode
                      ? () => {
                          onToggleMode()
                        }
                      : undefined}
                  >
                    <Hand className="h-4 w-4" />
                    <span className="sr-only">Pan</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>{ToolbarShortcutUtils.getFullTooltipText('Pan Mode', 'panMode', isMac)}</p>
                </TooltipContent>
              </Tooltip>
            </div>

            <Separator orientation="vertical" className="h-6 hidden xl:block" />
          </>
        )}

        {/* Zoom and View controls group */}
        {toolbarSettings.showZoomControls && (
          <>
            <div className="hidden xl:flex items-center gap-1" data-tutorial="zoom-controls">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={onZoomIn}
                  >
                    <ZoomIn className="h-4 w-4" />
                    <span className="sr-only">Zoom In</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>{ToolbarShortcutUtils.getFullTooltipText('Zoom In', 'zoomIn', isMac)}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={onZoomOut}
                  >
                    <ZoomOut className="h-4 w-4" />
                    <span className="sr-only">Zoom Out</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>{ToolbarShortcutUtils.getFullTooltipText('Zoom Out', 'zoomOut', isMac)}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={onResetZoom}
                  >
                    <RefreshCw className="h-4 w-4" />
                    <span className="sr-only">Reset Zoom</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>{ToolbarShortcutUtils.getFullTooltipText('Reset Zoom', 'resetZoom', isMac)}</p>
                </TooltipContent>
              </Tooltip>
            </div>

            <Separator orientation="vertical" className="h-6 mx-2 hidden xl:block" />
          </>
        )}

        {/* Canvas Info Display - New Section */}
        {toolbarSettings.showCanvasInfo && (typeof formattedCanvasWidth === 'string' && formattedCanvasWidth.length > 0 && typeof formattedCanvasHeight === 'string' && formattedCanvasHeight.length > 0) && (
          <div className="hidden xl:flex items-center gap-2 text-sm text-muted-foreground" data-tutorial="canvas-info-display">
            <span className="font-medium text-foreground">Zoom:</span>
            <span className="inline-block min-w-[3ch] text-right text-foreground">{Math.round(currentZoom * 100)}</span>
            <span className="text-foreground">%</span>
            <Separator orientation="vertical" className="h-4 mx-1" />
            <span className="font-medium text-foreground">Canvas:</span>
            <span className="text-foreground">
              {formattedCanvasWidth}
              {' '}
              ×
              {' '}
              {formattedCanvasHeight}
            </span>
          </div>
        )}

        {/* Spacer to push right-aligned items */}
        <div className="flex-grow"></div>

        {/* Right-aligned tools and sidebar toggles */}
        <div className="ml-auto flex items-center gap-1 flex-shrink-0">
          {/* Grid Toggle Button */}
          {toolbarSettings.showGridToggle && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" onClick={onToggleGrid} data-tutorial="grid-toggle" className="hidden md:flex">
                  <Grid3x3 className={`h-4 w-4 ${showGrid ? 'text-accent-foreground' : 'text-muted-foreground'}`} />
                  <span className="sr-only">Toggle Grid</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>
                  {ToolbarShortcutUtils.getFullTooltipText(
                    `Toggle Grid (${showGrid ? 'On' : 'Off'})`,
                    'grid',
                    isMac,
                  )}
                </p>
              </TooltipContent>
            </Tooltip>
          )}

          {/* Panel Toggle Buttons Group */}
          <div data-tutorial="panel-toggles" className="flex items-center gap-1">
            {/* Left Panel Toggle */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    onToggleLayerPanel()
                  }}
                  data-tutorial="left-panel-toggle"
                >
                  {isLayerPanelOpen
                    ? <SidebarClose className="h-4 w-4" />
                    : <SidebarOpen className="h-4 w-4" />}
                  <span className="sr-only">Toggle Layer Panel</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>
                  {ToolbarShortcutUtils.getFullTooltipText(
                    `${isLayerPanelOpen ? 'Hide' : 'Show'} Layer Panel`,
                    'leftPanel',
                    isMac,
                  )}
                </p>
              </TooltipContent>
            </Tooltip>

            {/* Right Panel Toggle */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    onTogglePropertySidebar()
                  }}
                  data-tutorial="right-panel-toggle"
                >
                  {isPropertySidebarOpen
                    ? <PanelRightClose className="h-4 w-4" />
                    : <PanelRightOpen className="h-4 w-4" />}
                  <span className="sr-only">Toggle Property Sidebar</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>
                  {ToolbarShortcutUtils.getFullTooltipText(
                    `${isPropertySidebarOpen ? 'Hide' : 'Show'} Property Sidebar`,
                    'rightPanel',
                    isMac,
                  )}
                </p>
              </TooltipContent>
            </Tooltip>
          </div>

          {/* Keyboard Shortcuts Guide */}
          {toolbarSettings.showKeyboardShortcuts && (
            <Popover open={isKeyboardShortcutsOpen} onOpenChange={onKeyboardShortcutsOpenChange}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" data-tutorial="keyboard-shortcuts" className="hidden md:flex">
                      <Keyboard className="h-4 w-4" />
                      <span className="sr-only">Keyboard Shortcuts</span>
                    </Button>
                  </PopoverTrigger>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>{ToolbarShortcutUtils.getFullTooltipText('Keyboard Shortcuts', 'shortcuts', isMac)}</p>
                </TooltipContent>
              </Tooltip>
              <PopoverContent align="end" className="w-96 p-0">
                <KeyboardShortcutsContent />
              </PopoverContent>
            </Popover>
          )}

          {/* Tutorial Trigger */}
          {toolbarSettings.showTutorial && (
            <TutorialTrigger variant="ghost" size="icon" />
          )}

          {/* Settings Button */}
          {toolbarSettings.showSettings && onOpenSettings && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    onOpenSettings()
                  }}
                  data-tutorial="settings-button"
                >
                  <Settings className="h-4 w-4" />
                  <span className="sr-only">Settings</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>{ToolbarShortcutUtils.getFullTooltipText('Settings', 'settings', isMac)}</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </TooltipProvider>
      {/* 导出菜单 */}
      {exportMenuOpen && (
        <ExportMenu
          isOpen={exportMenuOpen}
          onClose={() => setExportMenuOpen(false)}
          position={menuPosition}
          projectName={projectName}
          exportAsSVG={exportAsSVG}
          exportAsPNG={exportAsPNG}
          exportAsPDF={exportAsPDF}
        />
      )}
    </div>
  )
}

export default Toolbar

// Also add named export for backward compatibility
export { Toolbar }
