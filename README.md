# RenoPilot.JS.Shapes2

A modern, interactive 2D vector graphics editor built with React, TypeScript, and Vite. RenoPilot.JS.Shapes2 allows users to create, manipulate, and manage various geometric shapes and paths with a focus on ease of use and extensibility.

## 🚀 Quick Start

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/your-username/RenoPilot.JS.Shapes2.git
    cd RenoPilot.JS.Shapes2
    ```
2.  **Install dependencies:**
    ```bash
    npm install
    ```
3.  **Run the development server:**
    ```bash
    npm run dev
    ```
    The application will be available at `http://localhost:3000`.

## 📋 Project Overview

RenoPilot.JS.Shapes2 is a feature-rich 2D vector graphics editor designed for creating and editing geometric shapes, paths, and other visual elements. It provides an intuitive user interface for drawing, modifying properties (like color, stroke, fill), and managing layers. The project aims to offer a performant and customizable solution for web-based graphics editing, suitable for diagramming, simple design tasks, and as a base for more complex graphical applications.

Key features include:

*   **Shape Creation:** Draw various shapes like rectangles, ellipses, polygons, lines, and freehand paths.
*   **Element Manipulation:** Move, resize, rotate, and group elements.
*   **Property Editing:** Modify fill color, stroke color, stroke width, opacity, and other visual attributes.
*   **Layer Management:** Organize elements into layers for better control and visibility.
*   **Undo/Redo:** Robust undo and redo functionality for non-destructive editing.
*   **Zoom & Pan:** Navigate the canvas efficiently.
*   **Templating:** Start new projects from predefined templates or a blank canvas.
*   **Export:** (Functionality to be detailed based on implementation - e.g., SVG, PNG, JSON)
*   **Interactive Tutorial:** Guided tours to help users learn the application.

## 🛠️ Technology Stack

*   **Frontend Framework:** React 19
*   **Language:** TypeScript 5
*   **Build Tool:** Vite 6
*   **State Management:** Zustand 5
*   **Styling:** Tailwind CSS 3, PostCSS
*   **UI Components:** Radix UI, Lucide React Icons
*   **Drag and Drop:** @dnd-kit
*   **Routing:** (Implicitly via UI state, no dedicated router library identified for multi-page navigation beyond initial template selection)
*   **Testing:** Playwright (for E2E, integration, and unit tests), Vitest
*   **Linting & Formatting:** ESLint, Prettier
*   **Version Control Hooks:** Husky, lint-staged
*   **Graphics & Interaction:** D3.js (potentially for complex calculations or SVG manipulation, though primary rendering is React-based), Textures.js
*   **Utilities:** Lodash-es, clsx, tailwind-merge, uuid

## 📦 Installation Guide

### Environmental Requirements

*   Node.js (Version 18+ recommended, as per Vite compatibility <mcreference link="https://vite.dev/guide/" index="2">2</mcreference>)
*   npm (comes with Node.js) or Yarn

### Detailed Steps

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/your-username/RenoPilot.JS.Shapes2.git
    cd RenoPilot.JS.Shapes2
    ```
2.  **Install dependencies:**
    Using npm:
    ```bash
    npm install
    ```
    Or using Yarn:
    ```bash
    yarn install
    ```
3.  **Set up environment variables (if any):**
    Currently, the project uses `src/config/environment.ts`. If specific `.env` files are introduced later for sensitive or environment-specific configurations, create a `.env.local` file in the project root and add necessary variables. Example:
    ```env
    VITE_API_URL=https://api.example.com
    ```

## 🔧 Configuration

*   **Vite Configuration:** `vite.config.ts` handles the build process, development server, and plugin configurations.
*   **TypeScript Configuration:** `tsconfig.json` and related files (`tsconfig.paths.json`, `tsconfig.test.json`, etc.) define TypeScript compiler options and path aliases.
*   **Tailwind CSS Configuration:** `tailwind.config.js` and `postcss.config.cjs` are used to customize Tailwind CSS and PostCSS processing. <mcreference link="https://medium.com/@pushpendrapal_/how-to-setup-react-typescript-and-tailwind-css-with-vite-in-a-project-8d9b0b51d1bd" index="1">1</mcreference>
*   **ESLint Configuration:** `eslint.config.ts` (or `.eslintrc.js/.json`) defines linting rules.
*   **Prettier Configuration:** Typically a `.prettierrc.json` or similar, or configured in `package.json`.
*   **Application Configuration:** Core application settings, default values, and feature flags can be found in the `src/config/` directory (e.g., `assetConfig.tsx`, `defaultElementSettings.ts`, `environment.ts`).
*   **Playwright Configuration:** `playwright.config.ts` configures the Playwright test runner.
*   **Vitest Configuration:** `vitest.config.ts` configures the Vitest test runner.

No specific environment variables are strictly required to run the application in its current state for development, but check `src/config/environment.ts` for any environment-dependent logic.

## 📚 Documentation

Comprehensive documentation for the RenoPilot.JS.Shapes2 project can be found in the `docs` directory. This includes detailed information about the architecture, API (if applicable), development practices, user guidance, and troubleshooting.

### Key Documentation Sections:

- **[System Architecture Overview](./docs/architecture/overview.md)**: High-level design, tech stack, and core components.
- **[Data Flow](./docs/architecture/data-flow.md)**: How data moves through the application.
- **[Module Design](./docs/architecture/modules.md)**: Breakdown of key modules and their interactions.
- **[Development Setup](./docs/development/setup.md)**: Setting up your development environment.
- **[Coding Standards](./docs/development/coding-standards.md)**: Guidelines for writing code.
- **[Testing Guide](./docs/development/testing.md)**: Information on running and writing tests.
- **[Deployment Guide](./docs/development/deployment.md)**: How to deploy the application.
- **[User Quick Start](./docs/user-guide/quick-start.md)**: A brief tutorial for new users.

For a complete list of all documents, please see the **[Full Documentation Index](./docs/README.md)**.

## 📖 User Manual

(This section would typically link to more detailed API documentation or provide comprehensive usage examples. For now, it outlines the main interaction areas.)

### Main Interface Overview

*   **Toolbar:** Contains tools for selecting, drawing shapes (rectangle, ellipse, path, etc.), text, and other actions.
*   **Canvas:** The main drawing area where elements are created and manipulated.
*   **Properties Panel:** Displays and allows editing of properties for selected elements (e.g., color, size, position, stroke, fill).
*   **Layers Panel:** Shows the list of elements and layers, allowing for selection, reordering, and visibility control.
*   **Menu Bar:** Provides access to file operations (New, Open, Save - to be detailed), edit actions (Undo, Redo, Copy, Paste), view options, and help.

### Core Functionalities

*   **Creating Shapes:** Select a shape tool from the toolbar and click/drag on the canvas.
*   **Selecting Elements:** Use the selection tool to click on an element or drag a marquee to select multiple elements.
*   **Modifying Properties:** Once an element is selected, its properties will appear in the Properties Panel. Edit them as needed.
*   **Transforming Elements:** Selected elements can be moved by dragging, resized using handles, and rotated (if applicable).
*   **Using Layers:** Create new layers, move elements between layers, and toggle layer visibility in the Layers Panel.
*   **Undo/Redo:** Use `Ctrl+Z` (or `Cmd+Z`) for undo and `Ctrl+Y` (or `Cmd+Y`) for redo.
*   **Templates:** On application start, choose to begin with a blank canvas or a pre-defined template.
*   **Tutorial:** An interactive tutorial (`driver.js`) guides new users through the main features.

## 🏗️ Project Architecture

### Directory Structure Overview

```
RenoPilot.JS.Shapes2
├── .github/         # GitHub specific workflows and templates
├── public/          # Static assets
├── scripts/         # Build and utility scripts
├── src/
│   ├── App.tsx      # Main application component, handles template selection and layout
│   ├── main.tsx     # Application entry point, initializes services and renders App
│   ├── assets/      # (Implicit, if any static assets are directly imported)
│   ├── components/  # React UI components (canvas, dialogs, layout, toolbar, etc.)
│   │   ├── canvas/
│   │   ├── dialogs/
│   │   ├── layout/    # Main editor layout (EditorLayout.tsx)
│   │   ├── property/  # Property inspector panels
│   │   └── ui/        # Generic UI elements (buttons, inputs, etc.)
│   ├── config/      # Application configuration (default settings, environment)
│   ├── core/        # Core application logic, independent of UI
│   │   ├── CoreCoordinator.ts # Central orchestrator for core logic
│   │   ├── compute/   # Calculation strategies (area, perimeter, cost)
│   │   ├── factory/   # Element creation factory
│   │   ├── state/     # Shape repository (in-memory data store)
│   │   └── validator/ # Element validation logic
│   ├── data/        # Data handling (export, storage, versioning)
│   ├── hooks/       # Custom React hooks for reusable logic
│   ├── lib/         # General utility functions
│   ├── services/    # Business logic services (elements, input, storage, etc.)
│   │   ├── core/      # Core services (event bus, service registry)
│   │   ├── elements/  # Services for element actions (create, edit, delete)
│   │   └── system/    # System services (error handling, logging)
│   ├── store/       # Zustand state management stores (canvas, shapes, UI)
│   │   ├── shapesStore.ts # Manages the state of all shapes, includes undo/redo
│   │   └── uiStore.ts   # Manages UI-related state (e.g., selected tools, panel visibility)
│   ├── styles/      # Global CSS styles
│   └── types/       # TypeScript type definitions
├── tests/
│   ├── e2e/         # End-to-end tests (Playwright)
│   ├── integration/ # Integration tests (Playwright)
│   └── unit/        # Unit tests (Playwright/Vitest)
├── vite.config.ts   # Vite build and development server configuration
├── package.json     # Project metadata and dependencies
└── README.md        # This file
```

### Architectural Style

The project appears to follow a **component-based architecture** (thanks to React) with a clear separation of concerns. 

*   **UI Layer (`src/components`, `src/App.tsx`):** Responsible for rendering the user interface and handling user interactions. Uses Zustand for state management, passing data and actions down to components.
*   **State Management Layer (`src/store`):** Zustand stores (`shapesStore`, `uiStore`, `canvasStore`) act as the single source of truth for different parts of the application state. `shapesStore` is particularly central, managing the core data (the shapes themselves) and incorporating undo/redo capabilities via middleware (`zundo`).
*   **Service Layer (`src/services`):** Encapsulates business logic and interactions with external systems or the core logic. Examples include `templateService`, `elementCreationService`, `errorService`.
*   **Core Logic Layer (`src/core`):** Contains the fundamental domain logic, independent of the UI framework. `CoreCoordinator` orchestrates operations, `ShapeRepository` manages the in-memory storage of shapes, `ElementFactory` creates new elements, and `ComputeFacade` (with its strategies) handles calculations. This layer aims to be framework-agnostic.
*   **Configuration (`src/config`):** Centralized configuration for various aspects of the application.
*   **Hooks (`src/hooks`):** Reusable React hooks to encapsulate component logic and side effects, often interacting with stores or services.

### Data Flow

1.  **User Interaction:** User interacts with UI components (e.g., clicks a button in the toolbar).
2.  **Component Handler:** The component's event handler is triggered.
3.  **Hook/Service Call:** The handler might call a custom hook or a service function, or directly dispatch an action to a Zustand store.
4.  **Store Update:** Zustand store updates its state. For shape manipulations, this often involves `shapesStore`.
5.  **Core Logic (Optional):** For complex operations, services or store actions might delegate to `CoreCoordinator` or other `src/core` modules, which perform business logic and update the `ShapeRepository`.
6.  **Event Bus:** An event bus (`appEventBus`) is used for decoupled communication between different parts of the application, particularly between services and the core.
7.  **State Propagation:** Zustand notifies subscribed components about state changes.
8.  **UI Re-render:** Components re-render with the new state, reflecting the changes to the user.

### Key Design Patterns & Concepts

*   **Single Source of Truth:** Zustand stores provide a centralized state.
*   **Separation of Concerns:** Clear distinction between UI, state, services, and core logic.
*   **Dependency Injection:** Services and core components (like `CoreCoordinator`, `ShapeRepository`) are often instantiated with their dependencies (e.g., logger, event bus, factory) in `main.tsx` or higher-level components/services.
*   **Strategy Pattern:** Used in `src/core/compute` for different calculation methods (area, perimeter, cost) for various element types.
*   **Factory Pattern:** `ElementFactory` in `src/core/factory` for creating shape elements.
*   **Event-Driven Architecture (Partial):** Use of an event bus (`appEventBus`) for decoupled communication.
*   **Composition over Inheritance:** React's component model inherently favors composition. Utility classes (Tailwind) also promote a compositional approach to styling.
*   **Hooks:** Extensive use of React hooks for stateful logic and side effects in functional components.

## 🤝 Contribution Guidelines

We welcome contributions to RenoPilot.JS.Shapes2! Please follow these guidelines:

1.  **Fork the repository.**
2.  **Create a new branch** for your feature or bug fix: `git checkout -b feature/your-feature-name` or `git checkout -b fix/your-bug-fix`.
3.  **Make your changes.** Ensure your code adheres to the existing style and linting rules (`npm run lint` and `npm run format`).
4.  **Write tests** for your changes (unit, integration, or E2E as appropriate). Run tests with `npm test` or specific test scripts like `npm run test:unit`.
5.  **Commit your changes** with a descriptive commit message (consider Conventional Commits: `feat: Add new shape tool`).
6.  **Push your branch** to your fork: `git push origin feature/your-feature-name`.
7.  **Open a Pull Request** to the `main` branch of the original repository.
    *   Provide a clear title and description for your PR.
    *   Link any relevant issues.

### Development Scripts

*   `npm run dev`: Starts the development server.
*   `npm run build`: Builds the application for production.
*   `npm run lint`: Lints the codebase.
*   `npm run lint:fix`: Lints and automatically fixes issues.
*   `npm run format`: Formats the code using Prettier.
*   `npm test`: Runs all Playwright tests.
*   `npm run test:ui`: Runs Playwright tests with the UI mode.
*   `npm run test:vitest`: Runs Vitest unit tests.

## 📄 License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

