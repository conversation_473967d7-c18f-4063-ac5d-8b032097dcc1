import type {
  CreatePolygonParams,
} from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point'
import type {
  Shape,
} from '@/types/core/elementDefinitions'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
import { ensureCompleteMetadata } from '@/lib/utils/element/elementUtils'
/**
 * Creator for Polygon Shape Elements
 *
 * @remarks
 * This class implements the {@link ShapeCreator} interface to specialize in creating
 * Polygon shape elements ({@link CoreElementType.POLYGON}, {@link CoreElementType.TRIANGLE},
 * {@link CoreElementType.HEXAGON}, etc.). Polygons can be defined either by an explicit
 * array of vertex points or as regular polygons specified by a center, radius, and number of sides.
 *
 * The `create` method takes {@link CreatePolygonParams} and constructs a {@link ShapeElement}
 * that conforms to the {@link Shape.Polygon} interface. It handles both custom and regular
 * polygon creation, normalizes point data, calculates the centroid for positioning,
 * and ensures all necessary properties are correctly initialized.
 *
 * The `createDefault` method provides a simple way to create a default hexagon.
 * Local helper functions `normalizeToPointData` and `createRegularPolygonPointsInternal`
 * assist in point normalization and regular polygon vertex generation.
 *
 * @module core/factory/creators/shape/PolygonCreator
 * @see {@link ShapeCreator}
 * @see {@link CoreElementType.POLYGON}
 * @see {@link Shape.Polygon}
 * @see {@link CreatePolygonParams}
 */
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'
import { ShapeCreator } from './ShapeCreator'

/**
 * Helper function to normalize a point input to {@link PointData}.
 *
 * @param pointInput - The point input, can be a PointData-like object or an array of numbers.
 * @param paramName - The name of the parameter for error reporting purposes.
 * @returns A normalized {@link PointData} object.
 * @throws {@link CoreError} If the point input is invalid or undefined.
 * @private
 */
function normalizeToPointData(pointInput: PointData | [number, number, number?] | [number, number] | undefined, paramName: string): PointData {
  if (!pointInput) {
    throw new CoreError(ErrorType.InvalidParameter, `${paramName} point input is undefined.`)
  }
  if (Array.isArray(pointInput)) {
    if (pointInput.length >= 2 && typeof pointInput[0] === 'number' && typeof pointInput[1] === 'number') {
      return { x: pointInput[0], y: pointInput[1], z: pointInput.length > 2 && typeof pointInput[2] === 'number' ? pointInput[2] : undefined }
    }
    throw new CoreError(ErrorType.InvalidParameter, `Invalid array for ${paramName} point input.`)
  }
  else if (typeof pointInput.x === 'number' && typeof pointInput.y === 'number') {
    return { x: pointInput.x, y: pointInput.y, z: pointInput.z } // z can be undefined
  }
  throw new CoreError(ErrorType.InvalidParameter, `Invalid object for ${paramName} point input.`)
}

/**
 * Creates an array of {@link PointData} objects representing the vertices of a regular polygon.
 *
 * @param center - The center {@link PointData} of the polygon.
 * @param radius - The radius of the circumcircle of the polygon.
 * @param sides - The number of sides (must be >= 3).
 * @param startAngleRad - Optional starting angle in radians for the first vertex (default: 0, along the positive x-axis).
 * @returns An array of {@link PointData} objects forming the regular polygon.
 * @throws {@link CoreError} If `sides` is less than 3 or `radius` is not positive and finite.
 * @private
 */
/**
 * Creates an array of points for a regular polygon.
 *
 * @remarks
 * This function generates points for a regular polygon with the specified number of sides,
 * centered at the given center point and with the given radius. The points are generated
 * in counter-clockwise order starting from the angle specified by startAngleRad.
 *
 * For different polygon types, we use different starting angles to ensure the polygon
 * is oriented correctly:
 * - Triangle (3 sides): Start with a point at the top (startAngleRad = -Math.PI/2)
 * - Square/Rectangle (4 sides): Start with a point at the top-right (startAngleRad = -Math.PI/4)
 * - Pentagon (5 sides): Start with a point at the top (startAngleRad = -Math.PI/2)
 * - Hexagon (6 sides): Start with a point at the right (startAngleRad = 0)
 *
 * @param center - The center point of the polygon
 * @param radius - The radius of the polygon (distance from center to vertices)
 * @param sides - The number of sides of the polygon
 * @param startAngleRad - The starting angle in radians (default: 0)
 * @returns An array of points representing the vertices of the polygon
 */
function createRegularPolygonPointsInternal(center: PointData, radius: number, sides: number, startAngleRad: number = 0): PointData[] {
  if (sides < 3) {
    throw new CoreError(ErrorType.InvalidPayload, 'Regular polygon must have at least 3 sides.', undefined, { metadata: { sides } })
  }
  if (radius <= 0 || !Number.isFinite(radius)) {
    throw new CoreError(ErrorType.InvalidPayload, 'Regular polygon radius must be positive and finite.', undefined, { metadata: { radius } })
  }

  // 根据多边形类型调整起始角度，使多边形朝向更自然
  let adjustedStartAngle = startAngleRad
  if (startAngleRad === 0 || startAngleRad === undefined) {
    // 如果没有指定起始角度，根据边数自动调整
    if (sides === 3) {
      // 三角形：顶点朝上
      adjustedStartAngle = -Math.PI / 2
    }
    else if (sides === 4) {
      // 四边形：一个角在右上方
      adjustedStartAngle = -Math.PI / 4
    }
    else if (sides === 5) {
      // 五边形：一个顶点朝上
      adjustedStartAngle = -Math.PI / 2
    }
    // 六边形和其他多边形使用默认的0度（右侧开始）
  }

  console.warn(`[createRegularPolygonPointsInternal] Creating ${sides}-sided polygon with radius ${radius} and adjusted start angle ${adjustedStartAngle}`)

  // 生成相对于原点(0,0)的顶点坐标
  const points: PointData[] = []
  const angleStep = (2 * Math.PI) / sides

  for (let i = 0; i < sides; i++) {
    const angle = adjustedStartAngle + i * angleStep
    const x = radius * Math.cos(angle)
    const y = radius * Math.sin(angle)
    points.push({ x, y, z: center.z })
  }

  // 计算几何中心
  let sumX = 0
  let sumY = 0
  for (const point of points) {
    sumX += point.x
    sumY += point.y
  }
  const centroidX = sumX / sides
  const centroidY = sumY / sides

  console.warn(`[createRegularPolygonPointsInternal] Original centroid: (${centroidX}, ${centroidY})`)

  // 调整顶点坐标，使几何中心与原点(0,0)重合
  const adjustedPoints: PointData[] = []
  for (const point of points) {
    adjustedPoints.push({
      x: point.x - centroidX,
      y: point.y - centroidY,
      z: point.z,
    })
  }

  console.warn(`[createRegularPolygonPointsInternal] Adjusted points to ensure centroid is at (0,0)`)
  console.warn(`[createRegularPolygonPointsInternal] Generated ${adjustedPoints.length} points:`, adjustedPoints)

  return adjustedPoints
}

/**
 * Creator class for instantiating Polygon shape elements, including regular polygons
 * like triangles and hexagons, as well as custom polygons defined by a list of vertices.
 * It implements the {@link ShapeCreator} interface.
 *
 * @implements {ShapeCreator}
 */
export class PolygonCreator extends ShapeCreator<Shape.Polygon, CreatePolygonParams> {
  /**
   * Creates a Polygon {@link ShapeElement} based on the provided parameters.
   *
   * @param params - A {@link CreatePolygonParams} object
   *                 containing information for polygon creation.
   * @returns A Promise resolving to the created {@link Shape.Polygon}.
   */
  public async create(params: CreatePolygonParams): Promise<Shape.Polygon> {
    const allowedTypes: string[] = [
      CoreElementType.POLYGON,
      CoreElementType.TRIANGLE,
      CoreElementType.QUADRILATERAL,
      CoreElementType.PENTAGON,
      CoreElementType.HEXAGON,
      'polygon',
      'triangle',
      'quadrilateral',
      'pentagon',
      'hexagon',
    ]

    if (!allowedTypes.includes(params.type as string)) {
      throw new CoreError(ErrorType.InvalidPayload, `PolygonCreator cannot create type: ${params.type}`)
    }

    const {
      id,
      type: elementType,
      points: inputPoints,
      center: inputCenter,
      sides: paramSides,
      radius: paramRadius,
      isRegular: directIsRegular,
      properties: customProperties,
      startAngleRad,
    } = params

    const startAngleForCreation = startAngleRad

    if (!id)
      throw new CoreError(ErrorType.InvalidPayload, 'Polygon requires an ID.')

    let finalPoints: PointData[]
    let calculatedCenter: PointData
    let finalIsRegular: boolean
    let finalSides: number

    const isIntendedRegular = typeof paramSides === 'number' && paramSides >= 3
      && typeof paramRadius === 'number' && paramRadius > 0
      && inputCenter && directIsRegular === true

    if (isIntendedRegular) {
      // 对于正多边形，我们使用传入的position作为多边形的位置
      // 这确保了多边形的中心点就是鼠标落点位置
      if (params.position) {
        // 如果有传入position（从Canvas.tsx的handleSvgDrop方法传入），优先使用它
        calculatedCenter = params.position
        console.warn('[PolygonCreator][create] Using provided position as center:', calculatedCenter)
      }
      else {
        // 否则使用传入的center
        calculatedCenter = normalizeToPointData(inputCenter as PointData | [number, number, number?], 'center (for regular polygon)')
        console.warn('[PolygonCreator][create] Using provided center:', calculatedCenter)
      }

      // 生成相对于原点(0,0)的顶点
      // 这样当ShapeRenderer将整个多边形组平移到position位置时，多边形的中心点就是position
      finalPoints = createRegularPolygonPointsInternal({ x: 0, y: 0, z: calculatedCenter.z }, paramRadius, paramSides, startAngleForCreation)

      finalIsRegular = true
      finalSides = paramSides

      console.warn('[PolygonCreator][create] Created regular polygon with center:', calculatedCenter, 'and points:', finalPoints)
    }
    else if (inputPoints != null && Array.isArray(inputPoints) && inputPoints.length >= 3) {
      const processedPoints = inputPoints.map((p, index) => normalizeToPointData(p as PointData | [number, number, number?], `points[${index}]`))

      if (processedPoints.length > 1
        && processedPoints[0].x === processedPoints[processedPoints.length - 1].x
        && processedPoints[0].y === processedPoints[processedPoints.length - 1].y
        && (processedPoints[0].z === processedPoints[processedPoints.length - 1].z || (processedPoints[0].z === undefined && processedPoints[processedPoints.length - 1].z === undefined))) {
        finalPoints = processedPoints.slice(0, -1)
      }
      else {
        finalPoints = processedPoints
      }

      finalSides = finalPoints.length

      if (inputCenter) {
        calculatedCenter = normalizeToPointData(inputCenter as PointData | [number, number, number?], 'center (from input with points)')
      }
      else {
        let sumX = 0
        let sumY = 0
        let sumZ = 0
        let countZ = 0
        finalPoints.forEach((p) => {
          sumX += p.x
          sumY += p.y
          if (p.z !== undefined) {
            sumZ += p.z
            countZ++
          }
        })
        const avgZ = countZ > 0 ? sumZ / countZ : undefined
        calculatedCenter = { x: sumX / finalPoints.length, y: sumY / finalPoints.length, z: avgZ }
      }

      if (directIsRegular === false) {
        finalIsRegular = false
      }
      else {
        finalIsRegular = (customProperties as unknown as { isRegular?: boolean })?.isRegular ?? false
      }
    }
    else {
      throw new CoreError(ErrorType.InvalidPayload, 'Polygon creation requires either valid regular polygon params (sides, radius, center, isRegular=true) OR a valid points array (>=3).')
    }

    // 确保position是中心点，这对于正确渲染和边界框计算非常重要
    // 对于从抽屉拖拽到画布上的多边形，position就是鼠标落点位置
    const creationParamsForCommonProps = {
      ...params,
      // 如果params.position存在（从ShapeCreationService传入），优先使用它
      // 否则使用calculatedCenter
      position: params.position || calculatedCenter,
    }

    // 记录位置信息，便于调试
    console.warn('[PolygonCreator][create] Setting polygon position to:', creationParamsForCommonProps.position)

    const commonProps = this.createCommonProperties(id, creationParamsForCommonProps)

    // 记录创建多边形的关键信息
    console.warn('[PolygonCreator][create] Creating polygon with:', {
      id,
      type: elementType,
      sides: finalSides,
      isRegular: finalIsRegular,
      pointsCount: finalPoints.length,
    })

    // 计算多边形的边界框以获取width和height
    const boundingBox = this.calculatePolygonBoundingBox(finalPoints)

    const polygonElement: Shape.Polygon = {
      ...commonProps,
      type: elementType as CoreElementType.POLYGON | CoreElementType.TRIANGLE | CoreElementType.QUADRILATERAL | CoreElementType.PENTAGON | CoreElementType.HEXAGON,
      metadata: ensureCompleteMetadata({
        ...(commonProps.metadata ?? {}),
        name: commonProps.metadata?.name ?? `${elementType} ${id.substring(0, 8)}`,
      }),
      points: finalPoints,
      sides: finalSides,
      isRegular: finalIsRegular,
      // 添加计算出的width和height属性，用于属性栏显示
      width: boundingBox.width,
      height: boundingBox.height,
      properties: {
        // 设置默认的成本相关属性
        costUnitPrice: 1,
        costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1
        costBasis: 'unit', // 🔧 修复：默认使用单位计算，而不是面积
        // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
        computedAreaStatus: 'none',
        computedPerimeterStatus: 'none',
        computedLengthStatus: 'none',
        // 然后合并其他属性
        ...(customProperties || {}),
        // 确保points属性在properties中，因为渲染器和验证器期望在properties中找到points
        points: finalPoints,
        // 确保sides和isRegular属性在properties中，因为PropertyUpdateService需要这些属性
        sides: finalSides,
        isRegular: finalIsRegular,
        // 添加计算出的width和height到properties中，确保属性栏能够访问
        width: boundingBox.width,
        height: boundingBox.height,
        ...(isIntendedRegular && paramRadius != null && inputCenter != null
          ? {
              creationRadius: paramRadius,
              creationCenter: inputCenter,
            }
          : {}),
      },
    }

    // 记录创建的多边形元素
    console.warn('[PolygonCreator][create] Created polygon element:', {
      id: polygonElement.id,
      type: polygonElement.type,
      pointsCount: polygonElement.points.length,
      propertiesPointsCount: Array.isArray(polygonElement.properties?.points) ? polygonElement.properties?.points.length : 0,
    })

    return polygonElement
  }

  /**
   * Creates a default Polygon element (specifically, a Hexagon).
   *
   * @param id - The unique identifier for the default polygon.
   * @param position - The center {@link PointData} for the default polygon.
   * @param majorCategoryOverride - Optional override for majorCategory.
   * @param minorCategoryOverride - Optional override for minorCategory.
   * @param zLevelIdOverride - Optional override for zLevelId.
   * @param isFixedCategoryOverride - Optional override for isFixedCategory.
   * @returns A Promise resolving to the created default {@link Shape.Polygon}.
   */
  public async createDefault(
    id: string,
    position: PointData,
    majorCategoryOverride?: MajorCategory,
    minorCategoryOverride?: MinorCategory,
    zLevelIdOverride?: string,
    isFixedCategoryOverride?: boolean,
  ): Promise<Shape.Polygon> {
    const sides = 6
    const radius = 50

    const params: CreatePolygonParams = {
      id,
      type: CoreElementType.HEXAGON,
      majorCategory: majorCategoryOverride ?? MajorCategory.FURNITURE,
      minorCategory: minorCategoryOverride,
      zLevelId: zLevelIdOverride,
      isFixedCategory: isFixedCategoryOverride,
      center: position,
      sides,
      radius,
      isRegular: true,
      metadata: { name: `Default Hexagon ${id}` },
      fill: '#E0E0E0',
      stroke: '#555555',
      strokeWidth: 2,
      visible: true,
      locked: false,
      rotation: 0,
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [],
      properties: {
        // 设置默认的成本相关属性
        costUnitPrice: 1,
        costMultiplierOrCount: 0,
        costBasis: 'unit',
      },
    }
    return this.create(params)
  }

  /**
   * 计算多边形的边界框
   * @param points - 多边形的顶点数组
   * @returns 边界框对象，包含width和height
   */
  private calculatePolygonBoundingBox(points: PointData[]): { width: number, height: number } {
    if (!points || points.length === 0) {
      return { width: 0, height: 0 }
    }

    let minX = points[0].x
    let minY = points[0].y
    let maxX = points[0].x
    let maxY = points[0].y

    for (let i = 1; i < points.length; i++) {
      const p = points[i]
      minX = Math.min(minX, p.x)
      minY = Math.min(minY, p.y)
      maxX = Math.max(maxX, p.x)
      maxY = Math.max(maxY, p.y)
    }

    return {
      width: maxX - minX,
      height: maxY - minY,
    }
  }
}
