/**
 * Perimeter Calculation Strategy for Rectangle and Square Elements
 *
 * @remarks
 * This strategy implements the {@link PerimeterCalculatorStrategy} for calculating
 * the perimeter of Rectangle ({@link CoreElementType.RECTANGLE}) and Square
 * ({@link CoreElementType.SQUARE}) elements.
 *
 * The perimeter is calculated using the standard formula: `2 * (width + height)`.
 * The calculation is delegated to the `calculateRectanglePerimeterUtil` function
 * from `../../../../lib/utils/geometry/rectangleUtils`.
 *
 * The strategy validates that the element is of the correct type and that its
 * `width` and `height` properties are valid, finite numbers.
 *
 * @module core/compute/strategies/perimeter/RectanglePerimeterStrategy
 * @see {@link Shape.Rectangle}
 * @see {@link Shape.Square}
 * @see {@link PerimeterCalculatorStrategy}
 * @see {@link calculateRectanglePerimeterUtil}
 */
import type { PerimeterCalculatorStrategy } from '../../../../types/core/compute'
import type {
  Element,
  Shape,
} from '../../../../types/core/elementDefinitions'
import { calculateRectanglePerimeter as calculateRectanglePerimeterUtil } from '../../../../lib/utils/geometry/rectangleUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

/**
 * Implements the {@link PerimeterCalculatorStrategy} for {@link CoreElementType.RECTANGLE}
 * and {@link CoreElementType.SQUARE} elements.
 */
export class RectanglePerimeterStrategy implements PerimeterCalculatorStrategy {
  /**
   * Calculates the perimeter of a Rectangle or Square element.
   *
   * @param element - The rectangle or square element, expected to be of type {@link ShapeElement}
   *                  and specifically {@link CoreElementType.RECTANGLE} or {@link CoreElementType.SQUARE},
   *                  conforming to {@link Shape.Rectangle}.
   * @returns The calculated perimeter.
   * @throws {@link CoreError} if the element is `null` or `undefined`.
   * @throws {@link CoreError} if the element type is not `RECTANGLE` or `SQUARE`.
   * @throws {@link CoreError} if the `width` or `height` properties are not finite numbers,
   *         or if `calculateRectanglePerimeterUtil` returns `NaN` (e.g. for negative dimensions).
   */
  public calculatePerimeter(element: Element): number {
    if (element == null) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        'Invalid element: null or undefined',
        undefined,
        { component: 'RectanglePerimeterStrategy', operation: 'calculatePerimeter' },
      )
    }

    if (element.type !== CoreElementType.RECTANGLE && element.type !== CoreElementType.SQUARE) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `Expected element type RECTANGLE or SQUARE, got '${element.type}' for ID ${element.id}`,
        undefined,
        { component: 'RectanglePerimeterStrategy', operation: 'calculatePerimeter', target: element.id },
      )
    }

    const rectElement = element as unknown as Shape.Rectangle // Added unknown for type safety

    // Access width and height from the nested properties object with proper typing
    const width = rectElement.properties?.width as number | undefined
    const height = rectElement.properties?.height as number | undefined

    if (typeof width !== 'number' || typeof height !== 'number'
      || !Number.isFinite(width) || !Number.isFinite(height)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Rectangle element (ID: ${element.id}) must have finite width and height. Received width: ${width}, height: ${height}`,
        undefined,
        { component: 'RectanglePerimeterStrategy', operation: 'calculatePerimeter', target: element.id, metadata: { width: width as number, height: height as number } },
      )
    }

    // Delegate to the utility function
    const perimeter = calculateRectanglePerimeterUtil(width, height)
    // The utility function already handles invalid inputs (e.g., negative) by returning NaN.
    if (Number.isNaN(perimeter)) {
      throw new CoreError(
        ErrorType.ComputationError, // Or INVALID_PARAMETER if util should not have received invalid values
        `Rectangle element (ID: ${element.id}) resulted in NaN perimeter. Received width: ${width}, height: ${height}`,
        undefined,
        { component: 'RectanglePerimeterStrategy', operation: 'calculatePerimeter', target: element.id, metadata: { width, height } },
      )
    }
    return perimeter
  }

  /**
   * Returns the primary element type this strategy is registered for.
   *
   * @remarks
   * This strategy handles both `RECTANGLE` and `SQUARE` types, as a square
   * is a special case of a rectangle. It is typically registered under
   * {@link CoreElementType.RECTANGLE}.
   *
   * @returns {@link CoreElementType.RECTANGLE} - The rectangle element type that this strategy handles
   */
  public getElementType(): CoreElementType {
    return CoreElementType.RECTANGLE
  }
}
