# Configuration Guide

This guide describes any user-configurable settings available within the RenoPilot.JS.Shapes2 application. Configuration options allow users to customize the application's behavior or appearance to suit their preferences or workflow.

## Accessing Configuration Settings

Typically, configuration settings can be found in a dedicated "Settings," "Preferences," or "Options" menu within the application. This might be accessible via:

*   A gear icon (⚙️) in the toolbar or header.
*   A menu item (e.g., "File > Settings" or "Edit > Preferences").

*(Note: As of the current analysis, specific user-configurable settings beyond basic tool interactions are not explicitly detailed in the project structure. This section outlines common types of configurations that *could* be present or added in the future.)*

## Potential Configuration Options

While the current project information doesn't specify user-facing configuration settings, here are examples of what might be available or could be implemented in a drawing application like RenoPilot.JS.Shapes2:

### 1. Canvas Settings

*   **Grid:**
    *   **Show/Hide Grid:** Toggle the visibility of a background grid on the canvas.
    *   **Grid Size:** Adjust the spacing of grid lines.
    *   **Snap to Grid:** Enable/disable snapping of shapes and points to the grid lines for precise alignment.
*   **Background Color:** Change the background color of the drawing canvas.
*   **Units:** Set preferred units of measurement (e.g., pixels, inches, centimeters) if the application supports scaled drawing.

### 2. Tool Behavior

*   **Default Shape Properties:**
    *   **Default Fill Color:** Set a default fill color for newly drawn shapes.
    *   **Default Stroke Color:** Set a default stroke (outline) color.
    *   **Default Stroke Width:** Set a default stroke thickness.
*   **Selection Behavior:**
    *   Configure how selection works (e.g., click vs. area select sensitivity).

### 3. UI Preferences

*   **Theme:**
    *   **Light/Dark Mode:** Switch between a light and dark user interface theme.
*   **Toolbar Customization:** (Advanced) Allow users to show/hide or reorder tools in the toolbar.
*   **Language:** If the application supports multiple languages, allow the user to select their preferred language.

### 4. Performance/Storage

*   **Autosave:**
    *   **Enable/Disable Autosave:** Toggle automatic saving of work at regular intervals (e.g., to browser local storage).
    *   **Autosave Interval:** Set the frequency of autosaves.
*   **Local Storage Management:**
    *   Option to clear locally stored data or manage saved drawings if the application uses browser local storage extensively.

## How to Configure

If such settings are available:

1.  **Locate the Settings Menu:** Find the entry point for configuration as described above.
2.  **Navigate Options:** The settings panel might be organized into tabs or sections (e.g., "General," "Canvas," "Appearance").
3.  **Modify Settings:** Use checkboxes, dropdown menus, sliders, or input fields to change the settings.
4.  **Apply/Save Changes:** Some settings might apply immediately, while others may require clicking an "Apply" or "Save" button.

## Current Status

Based on the initial codebase analysis, RenoPilot.JS.Shapes2 primarily focuses on core drawing functionalities. Extensive user-configurable settings might not be implemented yet. The application's configuration is more likely to be at the code level (e.g., `src/config`) for developers rather than through a user-facing UI.

As the project evolves, more user-configurable options may be added. This document will be updated accordingly to reflect new features.

If you are a developer, refer to `src/config/config.ts` (or similar files) for any compile-time or developer-configurable parameters.