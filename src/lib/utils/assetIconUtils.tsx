import type React from 'react'
import type { PredefinedElement } from '@/types/core/element/definitions/predefinedElements'
import {
  Archive,
  Armchair,
  Bath,
  Bed,
  BookMarked,
  BookOpen,
  CircleDot,
  CupSoda,
  Fan,
  Frame,
  Heater,
  HelpCircle,
  ImageIcon as ImageIconLucide,
  Lamp,
  Layers,
  LayoutDashboard,
  LayoutGrid,
  Lightbulb,
  Maximize,
  Microwave,
  Package,
  Refrigerator,
  RollerCoaster,
  Sofa,
  Sprout,
  Table,
  UtilityPole,
  Wallpaper,
  Warehouse,
} from 'lucide-react'
import { MajorCategory } from '@/types/core/majorMinorTypes'

// Helper function to get an icon for a PredefinedElement
export function getIconForPredefinedElement(element: PredefinedElement): React.ReactNode {
  const nameLower = element.name.toLowerCase()
  const minorCat = element.minorCategory

  // Furniture
  if (element.majorCategory === MajorCategory.FURNITURE) {
    switch (minorCat) {
      case 'beds': return <Bed className="h-5 w-5" />
      case 'seating':
        if (nameLower.includes('sofa'))
          return <Sofa className="h-5 w-5" />
        if (nameLower.includes('armchair') || nameLower.includes('chair'))
          return <Armchair className="h-5 w-5" />
        if (nameLower.includes('stool'))
          return <CupSoda className="h-5 w-5" />
        if (nameLower.includes('ottoman') || nameLower.includes('bench'))
          return <Maximize className="h-5 w-5" />
        return <Armchair className="h-5 w-5" />
      case 'tables':
        if (nameLower.includes('coffee table'))
          return <Table className="h-5 w-5" />
        if (nameLower.includes('desk'))
          return <LayoutGrid className="h-5 w-5" />
        return <Table className="h-5 w-5" />
      case 'storage':
        if (nameLower.includes('bookshelf') || nameLower.includes('bookcase'))
          return <BookOpen className="h-5 w-5" />
        if (nameLower.includes('cabinet'))
          return <Warehouse className="h-5 w-5" />
        if (nameLower.includes('dresser'))
          return <LayoutDashboard className="h-5 w-5" />
        if (nameLower.includes('nightstand'))
          return <Archive className="h-5 w-5" />
        if (nameLower.includes('wardrobe') || nameLower.includes('armoire'))
          return <Archive className="h-5 w-5" />
        if (nameLower.includes('shelf'))
          return <BookMarked className="h-5 w-5" />
        return <Package className="h-5 w-5" />
      case 'appliances':
        if (nameLower.includes('refrigerator') || nameLower.includes('fridge'))
          return <Refrigerator className="h-5 w-5" />
        if (nameLower.includes('oven') || nameLower.includes('range') || nameLower.includes('cooktop') || nameLower.includes('stove'))
          return <Heater className="h-5 w-5" />
        if (nameLower.includes('microwave'))
          return <Microwave className="h-5 w-5" />
        if (nameLower.includes('dishwasher'))
          return <Bath className="h-5 w-5" />
        if (nameLower.includes('sink'))
          return <Layers className="h-5 w-5" />
        if (nameLower.includes('toilet'))
          return <CircleDot className="h-5 w-5" />
        if (nameLower.includes('bathtub'))
          return <Bath className="h-5 w-5" />
        if (nameLower.includes('shower'))
          return <CircleDot className="h-5 w-5" />
        if (nameLower.includes('bidet'))
          return <CircleDot className="h-5 w-5" />
        if (nameLower.includes('washing machine') || nameLower.includes('dryer'))
          return <Layers className="h-5 w-5" />
        if (nameLower.includes('treadmill') || nameLower.includes('bike') || nameLower.includes('fitness'))
          return <RollerCoaster className="h-5 w-5" />
        return <Layers className="h-5 w-5" />
      case 'decor':
        if (nameLower.includes('lamp'))
          return <Lamp className="h-5 w-5" />
        if (nameLower.includes('plant') || nameLower.includes('greenery'))
          return <Sprout className="h-5 w-5" />
        if (nameLower.includes('mirror'))
          return <Frame className="h-5 w-5" />
        if (nameLower.includes('art') || nameLower.includes('picture') || nameLower.includes('frame'))
          return <ImageIconLucide className="h-5 w-5" />
        if (nameLower.includes('curtain') || nameLower.includes('drapes'))
          return <Wallpaper className="h-5 w-5" />
        if (nameLower.includes('rug'))
          return <RollerCoaster className="h-5 w-5" />
        if (nameLower.includes('piano'))
          return <HelpCircle className="h-5 w-5" />
        if (nameLower.includes('umbrella stand'))
          return <Archive className="h-5 w-5" />
        return <Frame className="h-5 w-5" />
    }
  }
  if (element.majorCategory === MajorCategory.CEILING) {
    switch (minorCat) {
      case 'utilities':
        if (nameLower.includes('vent'))
          return <Heater className="h-5 w-5" />
        if (nameLower.includes('detector') || nameLower.includes('alarm'))
          return <UtilityPole className="h-5 w-5" />
        if (nameLower.includes('sprinkler'))
          return <CircleDot className="h-5 w-5" />
        return <UtilityPole className="h-5 w-5" />
      case 'lighting':
        if (nameLower.includes('fan'))
          return <Fan className="h-5 w-5" />
        return <Lightbulb className="h-5 w-5" />
      default: return <Lightbulb className="h-5 w-5" />
    }
  }
  if (element.majorCategory === MajorCategory.BASE) {
    switch (minorCat) {
      case 'coverings':
        return <Wallpaper className="h-5 w-5" />
      case 'decor':
        if (nameLower.includes('rug'))
          return <RollerCoaster className="h-5 w-5" />
        return <Frame className="h-5 w-5" />
      default: return <Wallpaper className="h-5 w-5" />
    }
  }
  return <HelpCircle className="h-5 w-5" />
}
