/**
 * UI Store
 *
 * This module defines the global state for the application's user interface,
 * including sidebar visibility, interaction modes, and drawing state.
 *
 * The store uses Zustand for state management and provides actions to
 * toggle UI elements and set interaction modes.
 *
 * @module store/uiStore
 */

// Import Point as default export
import { create } from 'zustand'
import { useCanvasStore } from './canvasStore' // ADDED: Import canvas store

/**
 * Available interaction modes for the canvas
 * Defines how the user interacts with the canvas (select, draw, etc.)
 */
export type InteractionMode = 'select' | 'panTool' | 'drawRect' | 'drawEllipse' | 'drawLine' // MODIFIED: Renamed pan, removed zoom

/**
 * 错误通知接口
 */
export interface ErrorNotification {
  /** 错误消息 */
  message: string
  /** 错误类型 */
  type: 'warning' | 'error'
  /** 错误代码 */
  code?: string
  /** 时间戳 */
  timestamp?: number
}

/**
 * UI State interface defining the state and actions for UI management
 */
export interface UIState {
  /** Whether the left sidebar is open */
  isLeftSidebarOpen: boolean

  /** Whether the right sidebar is open */
  isRightSidebarOpen: boolean

  /** Current interaction mode */
  interactionMode: InteractionMode

  /** 当前错误通知 */
  errorNotification: ErrorNotification | null

  /**
   * Toggle the left sidebar open/closed
   */
  toggleLeftSidebar: () => void

  /**
   * Toggle the right sidebar open/closed
   */
  toggleRightSidebar: () => void

  /**
   * Set the current interaction mode
   * @param mode - The new interaction mode
   */
  setInteractionMode: (mode: InteractionMode) => void

  /**
   * 设置错误通知
   * @param notification - 错误通知信息
   */
  setErrorNotification: (notification: ErrorNotification) => void

  /**
   * 清除错误通知
   */
  clearErrorNotification: () => void
}

/**
 * UI Store hook for accessing and manipulating UI state
 */
export const useUIStore = create<UIState>(set => ({
  isLeftSidebarOpen: true,
  isRightSidebarOpen: true,
  interactionMode: 'select', // Default mode
  errorNotification: null, // 初始化错误通知

  toggleLeftSidebar: () => set(state => ({ isLeftSidebarOpen: !state.isLeftSidebarOpen })),
  toggleRightSidebar: () => set(state => ({ isRightSidebarOpen: !state.isRightSidebarOpen })),
  setInteractionMode: mode => set((state) => {
    // ADDED: Synchronize with canvasStore.viewMode
    if (mode === 'panTool') {
      useCanvasStore.getState().setViewMode('pan')
    }
    else if (mode === 'select' || mode.startsWith('draw')) {
      useCanvasStore.getState().setViewMode('select')
    }

    if (state.interactionMode !== mode && mode !== 'drawLine') {
      // console.log('[uiStore] Interaction mode changed, consider resetting lineStartPoint in shapesStore if applicable.')
    }
    return { interactionMode: mode }
  }),

  // 错误通知方法
  setErrorNotification: notification => set({
    errorNotification: {
      ...notification,
      timestamp: Date.now(),
    },
  }),
  clearErrorNotification: () => set({ errorNotification: null }),
}))
