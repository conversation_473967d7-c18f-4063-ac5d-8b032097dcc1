import type { CreateQuadraticParams } from '@/core/factory/ElementFactory'
import type { PointData } from '@/types/core/element/geometry/point'
import type {
  Path,
} from '@/types/core/elementDefinitions'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
/**
 * Creator for Quadratic Bezier Curve Path Elements
 *
 * @remarks
 * This class extends {@link PathCreator} to specialize in creating
 * Quadratic Bezier curve ({@link CoreElementType.QUADRATIC}) path elements.
 * It uses `createCommonProperties` for base attributes and then adds curve-specific properties.
 *
 * @module core/factory/creators/path/QuadraticCreator
 */
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'
import { PathCreator } from './PathCreator'

// Removed EnrichedCreateQuadraticParams

export class QuadraticCreator extends PathCreator<Path.Quadratic, CreateQuadraticParams> {
  constructor() {
    super(CoreElementType.QUADRATIC)
  }

  /**
   * Creates a Quadratic Bezier curve {@link Path.Quadratic} element based on the provided parameters.
   *
   * @param params - A {@link CreateQuadraticParams} object
   *                 containing all necessary information for creation.
   * @returns A Promise resolving to the created {@link Path.Quadratic} element.
   */
  public async create(params: CreateQuadraticParams): Promise<Path.Quadratic> {
    if (params.type !== CoreElementType.QUADRATIC && params.type !== 'quadratic') {
      throw new CoreError(ErrorType.InvalidPayload, `QuadraticCreator cannot create type: ${String(params.type)}`)
    }

    const { id, start, control, end } = params
    if (!id)
      throw new CoreError(ErrorType.InvalidPayload, 'Quadratic curve requires an ID.')
    if (start == null || control == null || end == null) {
      throw new CoreError(ErrorType.InvalidPayload, 'Quadratic curve requires start, control, and end points.')
    }

    // PathCreator.createCommonProperties will use params.start if params.position is not set.
    const commonProps = this.createCommonProperties(id, params)

    // Ensure points are clean PointData
    const startPoint: PointData = { x: start.x, y: start.y, z: start.z ?? 0 }
    const controlPoint: PointData = { x: control.x, y: control.y, z: control.z ?? 0 }
    const endPoint: PointData = { x: end.x, y: end.y, z: end.z ?? 0 }

    const quadraticElement: Path.Quadratic = {
      ...commonProps,
      // type: CoreElementType.QUADRATIC, // from commonProps
      // position: commonProps.position, // from commonProps, often derived from params.start
      start: startPoint,
      control: controlPoint,
      end: endPoint,
      properties: {
        // 设置默认的成本相关属性
        costUnitPrice: 1,
        costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1
        costBasis: 'unit',
        // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
        computedAreaStatus: 'none',
        computedPerimeterStatus: 'none',
        computedLengthStatus: 'none',
        // 然后合并其他属性
        ...(params.properties || {}),
        start: startPoint,
        control: controlPoint,
        end: endPoint,
      },
    }

    return quadraticElement
  }

  /**
   * Creates a default Quadratic Bezier curve element.
   *
   * @param id - The unique identifier for the default quadratic curve.
   * @param position - The {@link PointData} for the default curve's start position.
   * @param majorCategoryOverride - Optional override for majorCategory.
   * @param minorCategoryOverride - Optional override for minorCategory.
   * @param zLevelIdOverride - Optional override for zLevelId.
   * @param isFixedCategoryOverride - Optional override for isFixedCategory.
   * @returns A Promise resolving to the created default {@link Path.Quadratic}.
   */
  public async createDefault(
    id: string,
    position: PointData, // Used as the start point
    majorCategoryOverride?: MajorCategory,
    minorCategoryOverride?: MinorCategory,
    zLevelIdOverride?: string,
    isFixedCategoryOverride?: boolean,
  ): Promise<Path.Quadratic> {
    // Get default settings for QUADRATIC elements
    const { getDefaultSettingsForElementType } = await import('@/config/defaultElementSettings')
    const defaults = getDefaultSettingsForElementType('QUADRATIC' as any)

    const paramsForCreate: CreateQuadraticParams = {
      id,
      type: CoreElementType.QUADRATIC,
      majorCategory: majorCategoryOverride ?? MajorCategory.FURNITURE, // Default
      minorCategory: minorCategoryOverride,
      zLevelId: zLevelIdOverride,
      isFixedCategory: isFixedCategoryOverride,
      start: position,
      control: { x: position.x + 25, y: position.y - 50, z: position.z },
      end: { x: position.x + 50, y: position.y, z: position.z },
      metadata: { name: 'Quadratic Curve' },
      stroke: defaults.stroke ?? '#333333',
      strokeWidth: defaults.strokeWidth ?? 2,
      opacity: defaults.opacity ?? 1,
      visible: true,
      locked: false,
      properties: {
        // 设置默认的成本相关属性
        costUnitPrice: 1,
        costMultiplierOrCount: 1, // 🔧 修复：默认乘数改为1
        costBasis: 'unit', // 默认使用单位计算
        // 🔧 修复：设置初始计算状态为NONE，避免显示错误的WARNING状态
        computedAreaStatus: 'none',
        computedPerimeterStatus: 'none',
        computedLengthStatus: 'none',
      },
    }
    return this.create(paramsForCreate)
  }
}
