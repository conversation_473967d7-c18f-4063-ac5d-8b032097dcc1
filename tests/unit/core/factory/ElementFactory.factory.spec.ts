import type { ShapeCreator } from '@/core/factory/creators/ShapeCreator'
import type { ShapeModel } from '@/types/core/models'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ElementFactory } from '@/core/factory/ElementFactory'
import { ElementType } from '@/types/core/shape-type'

// 模拟 ShapeCreator 类
class MockShapeCreator implements ShapeCreator {
  create = vi.fn().mockImplementation(async (params) => {
    return Promise.resolve({
      id: params.id || 'mock-id',
      type: params.type,
      ...params,
    } as ShapeModel)
  })
}

describe('elementFactory', () => {
  let factory: ElementFactory
  let mockRectangleCreator: MockShapeCreator
  let mockCircleCreator: MockShapeCreator
  let mockPolygonCreator: MockShapeCreator
  let mockLineCreator: MockShapeCreator
  let mockPolylineCreator: MockShapeCreator

  beforeEach(() => {
    // 创建新的工厂实例
    factory = new ElementFactory()

    // 创建模拟的创建器
    mockRectangleCreator = new MockShapeCreator()
    mockCircleCreator = new MockShapeCreator()
    mockPolygonCreator = new MockShapeCreator()
    mockLineCreator = new MockShapeCreator()
    mockPolylineCreator = new MockShapeCreator()

    // 注册模拟的创建器
    factory.registerCreator(ElementType.RECTANGLE, mockRectangleCreator)
    factory.registerCreator(ElementType.CIRCLE, mockCircleCreator)
    factory.registerCreator(ElementType.POLYGON, mockPolygonCreator)
    factory.registerCreator(ElementType.LINE, mockLineCreator)
    factory.registerCreator(ElementType.POLYLINE, mockPolylineCreator)
  })

  describe('createShape', () => {
    it('应该使用正确的创建器创建矩形', async () => {
      const params = {
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 50,
      }

      await factory.createShape(ElementType.RECTANGLE, params)

      expect(mockRectangleCreator.create).toHaveBeenCalledWith(params)
    })

    it('应该使用正确的创建器创建圆形', async () => {
      const params = {
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
        radius: 50,
      }

      await factory.createShape(ElementType.CIRCLE, params)

      expect(mockCircleCreator.create).toHaveBeenCalledWith(params)
    })

    it('应该使用正确的创建器创建多边形', async () => {
      const params = {
        type: ElementType.POLYGON,
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 100, y: 100 },
        ],
      }

      await factory.createShape(ElementType.POLYGON, params)

      expect(mockPolygonCreator.create).toHaveBeenCalledWith(params)
    })

    it('应该使用正确的创建器创建线段', async () => {
      const params = {
        type: ElementType.LINE,
        start: { x: 0, y: 0 },
        end: { x: 100, y: 100 },
      }

      await factory.createShape(ElementType.LINE, params)

      expect(mockLineCreator.create).toHaveBeenCalledWith(params)
    })

    it('应该使用正确的创建器创建折线', async () => {
      const params = {
        type: ElementType.POLYLINE,
        points: [
          { x: 0, y: 0 },
          { x: 50, y: 50 },
          { x: 100, y: 0 },
        ],
      }

      await factory.createShape(ElementType.POLYLINE, params)

      expect(mockPolylineCreator.create).toHaveBeenCalledWith(params)
    })

    it('当没有注册创建器时应该抛出错误', async () => {
      const params = {
        type: 'unknown-shape' as ElementType,
        position: { x: 0, y: 0 },
      }

      await expect(factory.createShape('unknown-shape' as ElementType, params)).rejects.toThrow()
    })
  })

  describe('createPath', () => {
    it('应该使用注册的工厂函数创建路径', async () => {
      const mockPathFactory = vi.fn().mockReturnValue({ id: 'path-1', type: 'arc' })
      factory.registerPathFactory('arc', mockPathFactory)

      const params = {
        type: 'arc',
        position: { x: 0, y: 0 },
        radius: 50,
        startAngle: 0,
        endAngle: 90,
      }

      await factory.createPath('arc', params)

      expect(mockPathFactory).toHaveBeenCalledWith(params)
    })

    it('当没有注册路径工厂函数时应该抛出错误', async () => {
      const params = {
        type: 'unknown-path',
        position: { x: 0, y: 0 },
      } as any

      await expect(factory.createPath('unknown-path', params)).rejects.toThrow()
    })
  })

  describe('registerCreator', () => {
    it('应该注册新的创建器', async () => {
      const newCreator = new MockShapeCreator()
      factory.registerCreator('new-shape', newCreator)

      const params = {
        type: 'new-shape' as ElementType,
        position: { x: 0, y: 0 },
      }

      await factory.createShape('new-shape' as ElementType, params)

      expect(newCreator.create).toHaveBeenCalledWith(params)
    })

    it('应该覆盖现有的创建器', async () => {
      const newRectangleCreator = new MockShapeCreator()
      factory.registerCreator(ElementType.RECTANGLE, newRectangleCreator)

      const params = {
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        width: 100,
        height: 50,
      }

      await factory.createShape(ElementType.RECTANGLE, params)

      expect(newRectangleCreator.create).toHaveBeenCalledWith(params)
      expect(mockRectangleCreator.create).not.toHaveBeenCalled()
    })
  })

  describe('registerPathFactory', () => {
    it('应该注册新的路径工厂函数', async () => {
      const mockPathFactory = vi.fn().mockReturnValue({ id: 'path-1', type: 'new-path' })
      factory.registerPathFactory('new-path', mockPathFactory)

      const params = {
        type: 'new-path',
        position: { x: 0, y: 0 },
      } as any

      await factory.createPath('new-path', params)

      expect(mockPathFactory).toHaveBeenCalledWith(params)
    })

    it('应该覆盖现有的路径工厂函数', async () => {
      const mockPathFactory1 = vi.fn().mockReturnValue({ id: 'path-1', type: 'arc' })
      const mockPathFactory2 = vi.fn().mockReturnValue({ id: 'path-2', type: 'arc' })

      factory.registerPathFactory('arc', mockPathFactory1)
      factory.registerPathFactory('arc', mockPathFactory2)

      const params = {
        type: 'arc',
        position: { x: 0, y: 0 },
      } as any

      await factory.createPath('arc', params)

      expect(mockPathFactory2).toHaveBeenCalledWith(params)
      expect(mockPathFactory1).not.toHaveBeenCalled()
    })
  })
})
