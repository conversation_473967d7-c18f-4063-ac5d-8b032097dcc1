/**
 * Provides logging services for the application.
 *
 * @remarks
 * This module exports the ConsoleLoggerService and utility functions for logging.
 */

import type { LoggerService } from '@/types/services/logging'
import { getService, ServiceId } from '@/services/core/registry'

// Export the ConsoleLoggerService
export { ConsoleLoggerService } from './consoleLoggerService'

/**
 * Gets the logger service from the registry.
 *
 * @returns The logger service instance.
 */
export function getLoggerService(): LoggerService {
  try {
    return getService<LoggerService>(ServiceId.Logger)
  }
  catch {
    // If the logger service is not registered, create a new one
    console.warn('Logger service not found in registry, creating a new one')

    // Import dynamically to avoid circular dependencies
    // eslint-disable-next-line ts/no-require-imports, ts/no-unsafe-assignment
    const { ConsoleLoggerService } = require('./consoleLoggerService')
    // eslint-disable-next-line ts/no-unsafe-return, ts/no-unsafe-call, ts/no-unsafe-member-access
    return ConsoleLoggerService.create()
  }
}
