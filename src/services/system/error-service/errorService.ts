/**
 * Centralized service for handling errors, determining their severity,
 * logging them, and publishing error events via the application's event bus.
 *
 * @remarks
 * This service aims to standardize error processing throughout the application.
 * It converts standard `Error` objects into {@link CoreError} instances,
 * assigns a severity level based on the {@link ErrorTypes.ErrorType}, logs the error
 * using the provided {@link LoggerService}, and then publishes an
 * {@link AppEventType.ERROR_OCCURRED} event.
 *
 * @module services/system/error-service/errorService
 */

import type { ErrorDetails as ErrorDetailsType, ErrorService as ErrorServiceInterface } from '../../../types/services/core/errorService'
import type { ErrorContext as CoreErrorContext } from '@/types/services/errors'
import type { AppEventMap, EventBus } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import { ErrorSeverity, ErrorType } from '@/types/services/errors' // Import specific types
import { AppEventType } from '@/types/services/events/eventTypes'
import { mergeErrorMetadata } from '../../../lib/utils/errorUtils' // Removed unused isError
import { CoreError } from './coreError' // Import CoreError from the local file

/**
 * Service responsible for centralized error management.
 * It integrates with the {@link EventBus} to publish error events and uses a
 * {@link LoggerService} for recording error details.
 */
export class ErrorService implements ErrorServiceInterface {
  /**
   * Creates an instance of ErrorService.
   *
   * @param {EventBus} eventBus - The application's event bus instance.
   * @param {LoggerService} logger - The logger service instance.
   */
  constructor(
    protected eventBus: EventBus<AppEventMap>,
    protected logger: LoggerService,
  ) {
    this.logger.info('[ErrorService] Initialized.')
  }

  /**
   * Handles a given error by converting it to a {@link CoreError} if necessary,
   * determining its severity, logging it, and publishing an error event.
   *
   * @param errorOrDetails - The error object to handle. Can be a standard `Error` or a {@link ErrorDetailsType}.
   */
  public handleError(errorOrDetails: Error | ErrorDetailsType): void {
    // TODO: Implement actual error handling logic
    this.logger.info('[ErrorService] handleError called (implementation pending).', errorOrDetails)
    // Example: A more robust implementation would convert to CoreError, determine context,
    // call determineSeverity, logErrorInternal, and publishErrorEvent.
    // For now, this satisfies the interface.
    if (errorOrDetails instanceof Error) {
      const errorInstance = errorOrDetails // Type assertion for clarity
      const contextForCoreError: CoreErrorContext = {
        metadata: { originalError: errorInstance, originalErrorName: errorInstance.name },
        stack: errorInstance.stack,
        // component and operation can be part of optionalContext if provided, or defaults
      }
      this.publishErrorEvent(
        errorOrDetails instanceof CoreError ? errorOrDetails : new CoreError(ErrorType.Runtime, errorInstance.message, ErrorSeverity.Medium, contextForCoreError),
        { component: 'handleError', operation: 'directError', ...contextForCoreError }, // Pass a more complete context
        ErrorSeverity.Medium,
      )
    }
    else {
      const coreErr = this.createError(errorOrDetails) as CoreError
      this.publishErrorEvent(
        coreErr,
        { component: errorOrDetails.context?.component ?? 'handleError', operation: 'errorDetails' },
        coreErr.severity ?? ErrorSeverity.Medium,
      )
    }
  }

  /**
   * Determines the severity of an error based on its {@link ErrorType}.
   *
   * @param {ErrorType} errorType - The type of the error.
   * @returns {ErrorSeverity} The determined severity level for the error.
   * @protected
   */
  protected determineSeverity(errorType: ErrorType): ErrorSeverity {
    switch (errorType) {
      case ErrorType.Fatal:
      case ErrorType.Configuration:
      case ErrorType.CoordinatorOperationFailed:
        return ErrorSeverity.Critical

      case ErrorType.Runtime:
      case ErrorType.Network:
      case ErrorType.ComputationError:
      case ErrorType.FactoryFailed:
      case ErrorType.ComputeBoundsError:
      case ErrorType.InvalidElementType:
        return ErrorSeverity.High

      case ErrorType.Validation:
      case ErrorType.Warning:
      case ErrorType.InvalidParameter:
      case ErrorType.InvalidPayload:
      case ErrorType.CoordinatorShapeNotFound:
        return ErrorSeverity.Medium

      case ErrorType.Info:
        return ErrorSeverity.Low

      case ErrorType.NotFound:
      case ErrorType.UnknownError:
        return ErrorSeverity.Medium

      default:
        // For any unmapped error types, or if a more generic fallback is needed.
        // This part depends on how exhaustively ErrorType is defined and used.
        // If ErrorType can have values not listed, this default is important.
        this.logger.warn(`Unknown error type encountered in determineSeverity: ${String(errorType)}. Defaulting to High.`)
        return ErrorSeverity.High
    }
  }

  /**
   * Logs an error using the configured {@link LoggerService}.
   *
   * @remarks
   * The log level (error, warn, info) is determined by the `severity` parameter.
   *
   * @param {CoreError} error - The {@link CoreError} instance to log.
   * @param {CoreErrorContext} context - Contextual information about the error.
   * @param {ErrorSeverity} severity - The severity level of the error.
   * @protected
   */
  protected logErrorInternal(
    error: CoreError,
    context: CoreErrorContext,
    severity: ErrorSeverity,
  ): void {
    const componentName = context.component ?? error.context?.component ?? 'UnknownComponent'
    const operationName = context.operation ?? error.context?.operation ?? 'UnknownOperation'
    const message = `[${componentName}:${operationName}] Error(${error.type}): ${error.message}`

    const logData = {
      errorType: error.type,
      component: componentName,
      operation: operationName,
      target: context.target ?? error.context?.target,
      // Merge metadata from the CoreError's context and the handling context
      metadata: mergeErrorMetadata(error.context?.metadata, context.metadata),
    }

    switch (severity) {
      case ErrorSeverity.Critical:
        this.logger.error(message, logData)
        break
      case ErrorSeverity.High:
        this.logger.error(message, logData)
        break
      case ErrorSeverity.Medium:
        this.logger.warn(message, logData)
        break
      case ErrorSeverity.Low:
        this.logger.info(message, logData)
        break
      default: // Should not happen if severity is always one of the enum values
        this.logger.error(`Unknown severity level '${String(severity)}' for error: ${message}`, logData)
        break
    }
  }

  /**
   * Publishes an {@link AppEventType.ERROR_OCCURRED} event via the {@link EventBus}.
   *
   * @param {CoreError} error - The {@link CoreError} instance.
   * @param {CoreErrorContext} context - Contextual information about the error.
   * @param {ErrorSeverity} severity - The severity level of the error.
   * @protected
   */
  protected publishErrorEvent(
    error: CoreError,
    context: CoreErrorContext,
    severity: ErrorSeverity,
  ): void {
    this.eventBus.publish({
      type: AppEventType.ErrorOccurred,
      payload: {
        code: error.type.toString(),
        message: error.message,
        severity, // This is ErrorTypes.ErrorSeverity
        details: { // Construct details based on available context
          component: context.component ?? error.context?.component,
          operation: context.operation ?? error.context?.operation,
          target: context.target ?? error.context?.target,
          recoverable: context.recoverable ?? (error.context?.recoverable ?? false),
          stack: error.stack,
          metadata: mergeErrorMetadata(error.context?.metadata, context.metadata),
        },
        // userMessage can be part of context.metadata if needed, or constructed
        userMessage: (context.metadata?.userMessage as string) || `An operation failed: ${error.message}`,
      },
      timestamp: Date.now(),
    })
  }

  // --- Methods to satisfy ErrorServiceInterface ---
  public createError(details: ErrorDetailsType): Error {
    this.logger.warn('[ErrorService] createError: Placeholder implementation.', details)
    // CoreError constructor: type, message, severity?, context?
    const contextForConstructor: CoreErrorContext = {
      component: details.context?.component ?? 'createError', // Use component, source is removed
      operation: details.context?.operation ?? 'createError',
      target: details.context?.target,
      metadata: { ...details.context?.metadata }, // Clone metadata
      recoverable: details.context?.recoverable,
      stack: details.context?.stack,
    }
    if (details.originalError) {
      if (!contextForConstructor.metadata)
        contextForConstructor.metadata = {}
      contextForConstructor.metadata.originalError = details.originalError
    }
    return new CoreError(
      (details.code as ErrorType) ?? ErrorType.Runtime,
      details.message ?? 'Error created from details',
      (details.severity as ErrorSeverity) ?? ErrorSeverity.Medium,
      contextForConstructor,
    )
  }

  public reportError(errorOrDetails: Error | ErrorDetailsType): void {
    this.logger.warn('[ErrorService] reportError: Placeholder implementation.', errorOrDetails)
    // Delegate to handleError, which now takes one argument.
    // Pass a context, as handleError was originally designed to take one,
    // even if the interface version doesn't. The implementation of handleError
    // now expects an optional second argument.
    this.handleError(errorOrDetails) // handleError now takes only one argument
  }

  public logError(errorOrDetails: Error | ErrorDetailsType): void {
    this.logger.warn('[ErrorService] public logError: Placeholder implementation.', errorOrDetails)
    // This public method needs to adapt the call to the protected logErrorInternal.
    // This is a simplified adaptation.
    let errorToLog: CoreError
    let contextToUse: CoreErrorContext
    let severityToUse: ErrorSeverity

    if (errorOrDetails instanceof CoreError) {
      errorToLog = errorOrDetails
      contextToUse = errorOrDetails.context ?? { component: 'logErrorPublic', operation: 'CoreError' }
      severityToUse = errorOrDetails.severity ?? this.determineSeverity(errorOrDetails.type)
    }
    else if (errorOrDetails instanceof Error) {
      contextToUse = {
        component: 'logErrorPublic',
        operation: 'ErrorInstance',
        metadata: { originalErrorName: (errorOrDetails).name, originalError: errorOrDetails }, // Include originalError
        stack: (errorOrDetails).stack,
      }
      errorToLog = new CoreError(ErrorType.Runtime, (errorOrDetails).message, ErrorSeverity.Medium, contextToUse)
      severityToUse = ErrorSeverity.Medium
    }
    else { // ErrorDetailsType
      errorToLog = this.createError(errorOrDetails) as CoreError // Use public createError
      contextToUse = (errorOrDetails.context as CoreErrorContext) ?? { component: 'logErrorPublic', operation: 'ErrorDetails' }
      severityToUse = (errorOrDetails.severity as ErrorSeverity) ?? this.determineSeverity(errorToLog.type)
    }
    this.logErrorInternal(errorToLog, contextToUse, severityToUse)
  }
} // This is the actual end of the ErrorService class
