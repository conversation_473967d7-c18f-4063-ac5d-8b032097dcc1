/**
 * @file geometryUtils.additional.spec.ts
 * @description Additional unit tests for geometry utility functions
 */

import { describe, expect, it } from 'vitest'
import { CoreError } from '@/core/errors'
import { ensurePointInstance } from '@/core/utils/geometryUtils'
import { Point } from '@/types/core/element/geometry/point'

describe('geometryUtils - Additional Tests', () => {
  describe('ensurePointInstance', () => {
    it('should return the same Point instance if provided', () => {
      // Arrange
      const point = new Point(10, 20)

      // Act
      const result = ensurePointInstance(point)

      // Assert
      expect(result).toBe(point)
      expect(result.x).toBe(10)
      expect(result.y).toBe(20)
    })

    it('should create a Point instance from an object with x and y properties', () => {
      // Arrange
      const pointLike = { x: 10, y: 20 }

      // Act
      const result = ensurePointInstance(pointLike)

      // Assert
      expect(result).toBeInstanceOf(Point)
      expect(result.x).toBe(10)
      expect(result.y).toBe(20)
    })

    it('should create a Point instance from an array of two numbers', () => {
      // Arrange
      const pointArray = [10, 20]

      // Act
      const result = ensurePointInstance(pointArray)

      // Assert
      expect(result).toBeInstanceOf(Point)
      expect(result.x).toBe(10)
      expect(result.y).toBe(20)
    })

    it('should handle zero values correctly', () => {
      // Arrange
      const pointZero = { x: 0, y: 0 }

      // Act
      const result = ensurePointInstance(pointZero)

      // Assert
      expect(result).toBeInstanceOf(Point)
      expect(result.x).toBe(0)
      expect(result.y).toBe(0)
    })

    it('should handle negative values correctly', () => {
      // Arrange
      const pointNegative = { x: -10, y: -20 }

      // Act
      const result = ensurePointInstance(pointNegative)

      // Assert
      expect(result).toBeInstanceOf(Point)
      expect(result.x).toBe(-10)
      expect(result.y).toBe(-20)
    })

    it('should throw an error for null input', () => {
      // Act & Assert
      expect(() => ensurePointInstance(null)).toThrow(CoreError)
      expect(() => ensurePointInstance(null)).toThrow('Missing position data')
    })

    it('should throw an error for undefined input', () => {
      // Act & Assert
      expect(() => ensurePointInstance(undefined)).toThrow(CoreError)
      expect(() => ensurePointInstance(undefined)).toThrow('Missing position data')
    })

    it('should throw an error for invalid object input', () => {
      // Arrange
      const invalidObject = { foo: 'bar' }

      // Act & Assert
      expect(() => ensurePointInstance(invalidObject)).toThrow(CoreError)
      expect(() => ensurePointInstance(invalidObject)).toThrow('Invalid position format')
    })

    it('should throw an error for object with non-numeric coordinates', () => {
      // Arrange
      const invalidCoords = { x: 'ten', y: 'twenty' }

      // Act & Assert
      expect(() => ensurePointInstance(invalidCoords as any)).toThrow(CoreError)
      expect(() => ensurePointInstance(invalidCoords as any)).toThrow('Invalid coordinates in object')
    })

    it('should throw an error for object with non-finite coordinates', () => {
      // Arrange
      const infiniteCoords = { x: Infinity, y: Number.NaN }

      // Act & Assert
      expect(() => ensurePointInstance(infiniteCoords)).toThrow(CoreError)
      expect(() => ensurePointInstance(infiniteCoords)).toThrow('Invalid coordinates in object')
    })

    it('should throw an error for array with wrong length', () => {
      // Arrange
      const shortArray = [10]
      const longArray = [10, 20, 30]

      // Act & Assert
      expect(() => ensurePointInstance(shortArray)).toThrow(CoreError)
      expect(() => ensurePointInstance(shortArray)).toThrow('Invalid position format')

      expect(() => ensurePointInstance(longArray)).toThrow(CoreError)
      expect(() => ensurePointInstance(longArray)).toThrow('Invalid position format')
    })

    it('should throw an error for array with non-numeric elements', () => {
      // Arrange
      const invalidArray = ['ten', 'twenty']

      // Act & Assert
      expect(() => ensurePointInstance(invalidArray as any)).toThrow(CoreError)
      expect(() => ensurePointInstance(invalidArray as any)).toThrow('Invalid coordinates in array')
    })

    it('should throw an error for array with non-finite elements', () => {
      // Arrange
      const infiniteArray = [Infinity, Number.NaN]

      // Act & Assert
      expect(() => ensurePointInstance(infiniteArray)).toThrow(CoreError)
      expect(() => ensurePointInstance(infiniteArray)).toThrow('Invalid coordinates in array')
    })

    it('should throw an error for Point instance with non-finite coordinates', () => {
      // Arrange
      const invalidPoint = new Point(Number.NaN, Infinity)

      // Act & Assert
      expect(() => ensurePointInstance(invalidPoint)).toThrow(CoreError)
      expect(() => ensurePointInstance(invalidPoint)).toThrow('Invalid coordinates in provided Point instance')
    })

    it('should include custom context in error messages', () => {
      // Act & Assert
      expect(() => ensurePointInstance(null, 'center point')).toThrow('Missing center point data')
      expect(() => ensurePointInstance({ foo: 'bar' }, 'center point')).toThrow('Invalid center point format')
    })

    it('should throw an error for primitive values', () => {
      // Act & Assert
      expect(() => ensurePointInstance(42)).toThrow(CoreError)
      expect(() => ensurePointInstance('point')).toThrow(CoreError)
      expect(() => ensurePointInstance(true)).toThrow(CoreError)
    })
  })
})
