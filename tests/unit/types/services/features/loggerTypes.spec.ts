import { describe, expect, it } from 'vitest'
import * as LoggerTypesModule from '@/types/services/features/loggerTypes'
import * as LoggingModule from '@/types/services/logging'

describe('logger Types Feature Module', () => {
  it('should be defined as a module', () => {
    // Verify the module exists
    expect(LoggerTypesModule).toBeDefined()
  })

  it('should re-export types from logging module', () => {
    // Check that the module has the same exports as the logging module
    const loggerTypesExports = Object.keys(LoggerTypesModule)
    const loggingExports = Object.keys(LoggingModule)

    // Verify that all exports from logging module are re-exported
    for (const exportName of loggingExports) {
      expect(loggerTypesExports).toContain(exportName)
    }
  })
})
