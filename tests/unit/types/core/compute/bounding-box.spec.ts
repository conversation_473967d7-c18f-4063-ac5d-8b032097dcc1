import type { BoundingBoxCalculatorStrategy } from '@/types/core/compute/boundingBoxComputeTypes'

import type { BoundingBox } from '@/types/core/element/geometry/bounding-box'
import type { Element } from '@/types/core/elementDefinitions'
import { describe, expect, it } from 'vitest'

describe('bounding Box Calculator Strategy Interface', () => {
  it('should define the required methods for bounding box calculation strategies', () => {
    // Create a mock implementation of IBoundingBoxCalculatorStrategy
    class MockBoundingBoxCalculator implements BoundingBoxCalculatorStrategy {
      calculateBoundingBox(element: Element): BoundingBox {
        // Simple mock implementation returning a minimal bounding box
        return {
          id: 'bbox-1',
          type: 'bounding-box',
          position: { x: 0, y: 0 },
          points: [
            { x: 0, y: 0 },
            { x: 50, y: 0 },
            { x: 100, y: 0 },
            { x: 0, y: 50 },
            { x: 50, y: 50 },
            { x: 100, y: 50 },
            { x: 0, y: 100 },
            { x: 50, y: 100 },
            { x: 100, y: 100 },
          ],
          width: 100,
          height: 100,
          visible: true,
          locked: false,
          rotation: 0,
          scale: { x: 1, y: 1 },
          fill: 'none',
          stroke: '#000000',
          strokeWidth: 1,
          opacity: 1,
          selectable: true,
          draggable: true,
          resizable: true,
          getSubType: () => 'bounding-box',
          move: () => {},
          toJson: () => ({ id: 'bbox-1', type: 'bounding-box' }),
        }
      }

      getElementType(): string {
        return 'mock-element'
      }
    }

    const calculator = new MockBoundingBoxCalculator()

    // Test the interface implementation
    expect(typeof calculator.calculateBoundingBox).toBe('function')
    expect(typeof calculator.getElementType).toBe('function')

    // Test the mock implementation
    const mockElement = { id: 'test-element' } as Element
    const boundingBox = calculator.calculateBoundingBox(mockElement)

    expect(boundingBox.id).toBe('bbox-1')
    expect(boundingBox.type).toBe('bounding-box')
    expect(boundingBox.width).toBe(100)
    expect(boundingBox.height).toBe(100)
    expect(calculator.getElementType()).toBe('mock-element')
  })

  it('should allow implementation for different shape types', () => {
    // Create a mock rectangle bounding box calculator
    class MockRectangleBoundingBoxCalculator implements BoundingBoxCalculatorStrategy {
      calculateBoundingBox(element: Element): BoundingBox {
        // Mock implementation for rectangle
        return {
          id: 'bbox-rect',
          type: 'bounding-box',
          position: { x: 10, y: 20 },
          points: [
            { x: 10, y: 20 },
            { x: 60, y: 20 },
            { x: 110, y: 20 },
            { x: 10, y: 70 },
            { x: 60, y: 70 },
            { x: 110, y: 70 },
            { x: 10, y: 120 },
            { x: 60, y: 120 },
            { x: 110, y: 120 },
          ],
          width: 100,
          height: 100,
          visible: true,
          locked: false,
          rotation: 0,
          scale: { x: 1, y: 1 },
          fill: 'none',
          stroke: '#000000',
          strokeWidth: 1,
          opacity: 1,
          selectable: true,
          draggable: true,
          resizable: true,
          getSubType: () => 'bounding-box',
          move: () => {},
          toJson: () => ({ id: 'bbox-rect', type: 'bounding-box' }),
        }
      }

      getElementType(): string {
        return 'rectangle'
      }
    }

    // Create a mock circle bounding box calculator
    class MockCircleBoundingBoxCalculator implements BoundingBoxCalculatorStrategy {
      calculateBoundingBox(element: Element): BoundingBox {
        // Mock implementation for circle
        return {
          id: 'bbox-circle',
          type: 'bounding-box',
          position: { x: 50, y: 50 },
          points: [
            { x: 0, y: 0 },
            { x: 50, y: 0 },
            { x: 100, y: 0 },
            { x: 0, y: 50 },
            { x: 50, y: 50 },
            { x: 100, y: 50 },
            { x: 0, y: 100 },
            { x: 50, y: 100 },
            { x: 100, y: 100 },
          ],
          width: 100,
          height: 100,
          visible: true,
          locked: false,
          rotation: 0,
          scale: { x: 1, y: 1 },
          fill: 'none',
          stroke: '#000000',
          strokeWidth: 1,
          opacity: 1,
          selectable: true,
          draggable: true,
          resizable: true,
          getSubType: () => 'bounding-box',
          move: () => {},
          toJson: () => ({ id: 'bbox-circle', type: 'bounding-box' }),
        }
      }

      getElementType(): string {
        return 'circle'
      }
    }

    const rectangleCalculator = new MockRectangleBoundingBoxCalculator()
    const circleCalculator = new MockCircleBoundingBoxCalculator()

    // Test the implementations
    const mockElement = { id: 'test-element' } as Element

    const rectBoundingBox = rectangleCalculator.calculateBoundingBox(mockElement)
    expect(rectBoundingBox.id).toBe('bbox-rect')
    expect(rectBoundingBox.position.x).toBe(10)
    expect(rectBoundingBox.position.y).toBe(20)
    expect(rectangleCalculator.getElementType()).toBe('rectangle')

    const circleBoundingBox = circleCalculator.calculateBoundingBox(mockElement)
    expect(circleBoundingBox.id).toBe('bbox-circle')
    expect(circleBoundingBox.position.x).toBe(50)
    expect(circleBoundingBox.position.y).toBe(50)
    expect(circleCalculator.getElementType()).toBe('circle')
  })

  it('should allow for error handling in implementations', () => {
    // Create a mock bounding box calculator with error handling
    class MockErrorHandlingCalculator implements BoundingBoxCalculatorStrategy {
      calculateBoundingBox(element: Element): BoundingBox {
        // Check if element has required properties
        if (!element?.id) {
          throw new Error('Invalid element')
        }

        // Mock implementation
        return {
          id: 'bbox-error',
          type: 'bounding-box',
          position: { x: 0, y: 0 },
          points: [
            { x: 0, y: 0 },
            { x: 50, y: 0 },
            { x: 100, y: 0 },
            { x: 0, y: 50 },
            { x: 50, y: 50 },
            { x: 100, y: 50 },
            { x: 0, y: 100 },
            { x: 50, y: 100 },
            { x: 100, y: 100 },
          ],
          width: 100,
          height: 100,
          visible: true,
          locked: false,
          rotation: 0,
          scale: { x: 1, y: 1 },
          fill: 'none',
          stroke: '#000000',
          strokeWidth: 1,
          opacity: 1,
          selectable: true,
          draggable: true,
          resizable: true,
          getSubType: () => 'bounding-box',
          move: () => {},
          toJson: () => ({ id: 'bbox-error', type: 'bounding-box' }),
        }
      }

      getElementType(): string {
        return 'error-handling-element'
      }
    }

    const errorHandlingCalculator = new MockErrorHandlingCalculator()

    // Test valid case
    const validElement = { id: 'test-element' } as Element
    const boundingBox = errorHandlingCalculator.calculateBoundingBox(validElement)
    expect(boundingBox.id).toBe('bbox-error')

    // Test error case
    const invalidElement = {} as Element
    expect(() => errorHandlingCalculator.calculateBoundingBox(invalidElement)).toThrow('Invalid element')
  })
})
