import { describe, expect, it } from 'vitest'
import { AppEventType } from '@/types/services/events/eventTypes'
import { createEvent } from '@/types/services/events/eventUtils'

describe('event Utilities', () => {
  describe('createEvent', () => {
    it('should create an event with the specified type and payload', () => {
      const type = AppEventType.SHAPE_CREATE_REQUEST
      const payload = { ElementType: 'rectangle' }

      const event = createEvent(type, payload)

      expect(event.type).toBe(type)
      expect(event.payload).toEqual(payload)
    })

    it('should generate a unique id for each event', () => {
      const event1 = createEvent(AppEventType.SHAPE_CREATE_REQUEST, {})
      const event2 = createEvent(AppEventType.SHAPE_CREATE_REQUEST, {})

      expect(event1.id).toBeDefined()
      expect(event2.id).toBeDefined()
      expect(event1.id).not.toBe(event2.id)
    })

    it('should set a timestamp if not provided', () => {
      const now = Date.now()
      const event = createEvent(AppEventType.SHAPE_CREATE_REQUEST, {})

      expect(event.timestamp).toBeDefined()
      expect(typeof event.timestamp).toBe('number')
      expect(event.timestamp).toBeGreaterThanOrEqual(now - 100) // Allow for small timing differences
      expect(event.timestamp).toBeLessThanOrEqual(now + 100)
    })

    it('should use the provided timestamp if specified', () => {
      const timestamp = 1620000000000
      const event = createEvent(AppEventType.SHAPE_CREATE_REQUEST, {}, timestamp)

      expect(event.timestamp).toBe(timestamp)
    })

    it('should create events with different types', () => {
      const createEvent1 = createEvent(AppEventType.SHAPE_CREATE_REQUEST, {})
      const editEvent = createEvent(AppEventType.SHAPE_EDIT_REQUEST, {})
      const deleteEvent = createEvent(AppEventType.SHAPE_DELETE_REQUEST, {})

      expect(createEvent1.type).toBe(AppEventType.SHAPE_CREATE_REQUEST)
      expect(editEvent.type).toBe(AppEventType.SHAPE_EDIT_REQUEST)
      expect(deleteEvent.type).toBe(AppEventType.SHAPE_DELETE_REQUEST)
    })

    it('should handle complex payload objects', () => {
      const complexPayload = {
        shapeId: 'shape-123',
        changes: {
          position: { x: 100, y: 200 },
          properties: {
            width: 50,
            height: 30,
            fill: '#ff0000',
            stroke: '#000000',
            strokeWidth: 2,
          },
        },
      }

      const event = createEvent(AppEventType.SHAPE_EDIT_REQUEST, complexPayload)

      expect(event.payload).toEqual(complexPayload)
    })

    it('should handle array payloads', () => {
      const arrayPayload = ['item1', 'item2', 'item3']

      const event = createEvent(AppEventType.SHAPE_DELETE_REQUEST, arrayPayload)

      expect(event.payload).toEqual(arrayPayload)
    })

    it('should handle primitive payloads', () => {
      const stringPayload = 'test-string'
      const numberPayload = 42
      const booleanPayload = true

      const stringEvent = createEvent(AppEventType.SHAPE_CREATE_REQUEST, stringPayload)
      const numberEvent = createEvent(AppEventType.SHAPE_EDIT_REQUEST, numberPayload)
      const booleanEvent = createEvent(AppEventType.SHAPE_DELETE_REQUEST, booleanPayload)

      expect(stringEvent.payload).toBe(stringPayload)
      expect(numberEvent.payload).toBe(numberPayload)
      expect(booleanEvent.payload).toBe(booleanPayload)
    })

    it('should handle null and undefined payloads', () => {
      const nullEvent = createEvent(AppEventType.SHAPE_CREATE_REQUEST, null)
      const undefinedEvent = createEvent(AppEventType.SHAPE_EDIT_REQUEST, undefined)

      expect(nullEvent.payload).toBeNull()
      expect(undefinedEvent.payload).toBeUndefined()
    })
  })
})
