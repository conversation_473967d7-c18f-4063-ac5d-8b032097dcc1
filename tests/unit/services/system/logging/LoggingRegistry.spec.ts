import type { ServiceRegistry } from '@/services/core/registry'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { getServiceFactory } from '@/services/core/registry'
import { registerLoggerService } from '@/services/system/logging/registry'
import { ServiceId } from '@/types/services/core/serviceIdentifier'

// Mock dependencies
vi.mock('@/services/core/registry', () => ({
  getServiceFactory: vi.fn(),
}))

describe('logging Registry', () => {
  let mockRegistry: ServiceRegistry
  let mockLoggerService: LoggerService
  let mockServiceFactory: any

  beforeEach(() => {
    mockLoggerService = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    }

    mockServiceFactory = {
      createLogger: vi.fn().mockReturnValue(mockLoggerService),
    }

    mockRegistry = {
      register: vi.fn(),
      get: vi.fn(),
      has: vi.fn(),
      unregister: vi.fn(),
      clear: vi.fn(),
      getAll: vi.fn(),
    }

    vi.mocked(getServiceFactory).mockReturnValue(mockServiceFactory)
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('registerLoggerService', () => {
    it('should register logger service successfully', () => {
      const result = registerLoggerService(mockRegistry)

      expect(getServiceFactory).toHaveBeenCalledTimes(1)
      expect(mockServiceFactory.createLogger).toHaveBeenCalledTimes(1)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.Logger,
        mockLoggerService,
      )
      expect(result).toBe(mockLoggerService)
    })

    it('should return the created logger service instance', () => {
      const result = registerLoggerService(mockRegistry)

      expect(result).toBe(mockLoggerService)
      expect(result).toBeDefined()
      expect(typeof result.info).toBe('function')
      expect(typeof result.warn).toBe('function')
      expect(typeof result.error).toBe('function')
      expect(typeof result.debug).toBe('function')
    })

    it('should call functions in correct order', () => {
      const callOrder: string[] = []

      vi.mocked(getServiceFactory).mockImplementation(() => {
        callOrder.push('getServiceFactory')
        return mockServiceFactory
      })

      mockServiceFactory.createLogger.mockImplementation(() => {
        callOrder.push('createLogger')
        return mockLoggerService
      })

      mockRegistry.register.mockImplementation(() => {
        callOrder.push('register')
      })

      registerLoggerService(mockRegistry)

      expect(callOrder).toEqual([
        'getServiceFactory',
        'createLogger',
        'register',
      ])
    })

    it('should handle service factory errors gracefully', () => {
      const error = new Error('Factory creation failed')
      vi.mocked(getServiceFactory).mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerLoggerService(mockRegistry)
      }).toThrow(error)

      expect(getServiceFactory).toHaveBeenCalledTimes(1)
      expect(mockRegistry.register).not.toHaveBeenCalled()
    })

    it('should handle logger creation errors gracefully', () => {
      const error = new Error('Logger creation failed')
      mockServiceFactory.createLogger.mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerLoggerService(mockRegistry)
      }).toThrow(error)

      expect(getServiceFactory).toHaveBeenCalledTimes(1)
      expect(mockServiceFactory.createLogger).toHaveBeenCalledTimes(1)
      expect(mockRegistry.register).not.toHaveBeenCalled()
    })

    it('should handle registry registration errors gracefully', () => {
      const error = new Error('Registration failed')
      mockRegistry.register.mockImplementation(() => {
        throw error
      })

      expect(() => {
        registerLoggerService(mockRegistry)
      }).toThrow(error)

      expect(mockServiceFactory.createLogger).toHaveBeenCalledTimes(1)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.Logger,
        mockLoggerService,
      )
    })

    it('should work with different registry implementations', () => {
      const customRegistry = {
        register: vi.fn(),
        get: vi.fn(),
        has: vi.fn(),
        unregister: vi.fn(),
        clear: vi.fn(),
        getAll: vi.fn(),
        size: vi.fn(), // Additional method
      }

      const result = registerLoggerService(customRegistry)

      expect(customRegistry.register).toHaveBeenCalledWith(
        ServiceId.Logger,
        mockLoggerService,
      )
      expect(result).toBe(mockLoggerService)
    })

    it('should handle multiple registration calls', () => {
      const result1 = registerLoggerService(mockRegistry)
      const result2 = registerLoggerService(mockRegistry)

      expect(getServiceFactory).toHaveBeenCalledTimes(2)
      expect(mockServiceFactory.createLogger).toHaveBeenCalledTimes(2)
      expect(mockRegistry.register).toHaveBeenCalledTimes(2)
      expect(result1).toBe(mockLoggerService)
      expect(result2).toBe(mockLoggerService)
    })

    it('should create new logger service instance for each registration', () => {
      const logger1 = { info: vi.fn(), warn: vi.fn(), error: vi.fn(), debug: vi.fn(), id: 'logger1' }
      const logger2 = { info: vi.fn(), warn: vi.fn(), error: vi.fn(), debug: vi.fn(), id: 'logger2' }

      mockServiceFactory.createLogger
        .mockReturnValueOnce(logger1)
        .mockReturnValueOnce(logger2)

      const result1 = registerLoggerService(mockRegistry)
      const result2 = registerLoggerService(mockRegistry)

      expect(result1).toBe(logger1)
      expect(result2).toBe(logger2)
      expect(mockRegistry.register).toHaveBeenNthCalledWith(1, ServiceId.Logger, logger1)
      expect(mockRegistry.register).toHaveBeenNthCalledWith(2, ServiceId.Logger, logger2)
    })

    it('should use correct service identifier', () => {
      registerLoggerService(mockRegistry)

      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.Logger,
        mockLoggerService,
      )

      const registrationCall = mockRegistry.register.mock.calls[0]
      expect(registrationCall[0]).toBe(ServiceId.Logger)
      expect(typeof registrationCall[0]).toBe('string')
    })

    it('should handle service factory returning null or undefined', () => {
      mockServiceFactory.createLogger.mockReturnValue(null)

      const result = registerLoggerService(mockRegistry)

      expect(mockRegistry.register).toHaveBeenCalledWith(ServiceId.Logger, null)
      expect(result).toBeNull()

      // Test with undefined
      vi.clearAllMocks()
      mockServiceFactory.createLogger.mockReturnValue(undefined)

      const result2 = registerLoggerService(mockRegistry)

      expect(mockRegistry.register).toHaveBeenCalledWith(ServiceId.Logger, undefined)
      expect(result2).toBeUndefined()
    })

    it('should handle complex logger service objects', () => {
      const complexLoggerService = {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
        trace: vi.fn(),
        setLevel: vi.fn(),
        getLevel: vi.fn(),
        metadata: {
          version: '1.0.0',
          capabilities: ['console', 'file'],
        },
      } as unknown as LoggerService

      mockServiceFactory.createLogger.mockReturnValue(complexLoggerService)

      const result = registerLoggerService(mockRegistry)

      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.Logger,
        complexLoggerService,
      )
      expect(result).toBe(complexLoggerService)
    })

    it('should handle edge cases with service factory', () => {
      // Test with factory that returns different types
      const testCases = [
        { info: vi.fn(), warn: vi.fn(), error: vi.fn(), debug: vi.fn() },
        { info: () => {}, warn: () => {}, error: () => {}, debug: () => {} },
        { info: console.log, warn: console.warn, error: console.error, debug: console.debug },
      ]

      testCases.forEach((testCase, index) => {
        vi.clearAllMocks()
        mockServiceFactory.createLogger.mockReturnValue(testCase)

        const result = registerLoggerService(mockRegistry)

        expect(result).toBe(testCase)
        expect(mockRegistry.register).toHaveBeenCalledWith(ServiceId.Logger, testCase)
      })
    })

    it('should preserve service factory call parameters', () => {
      registerLoggerService(mockRegistry)

      expect(mockServiceFactory.createLogger).toHaveBeenCalledWith()
      expect(mockServiceFactory.createLogger).toHaveBeenCalledTimes(1)

      const createLoggerCall = mockServiceFactory.createLogger.mock.calls[0]
      expect(createLoggerCall).toHaveLength(0) // No parameters expected
    })

    it('should handle service factory with additional methods', () => {
      const extendedFactory = {
        createLogger: vi.fn().mockReturnValue(mockLoggerService),
        createService: vi.fn(),
        getConfig: vi.fn(),
      }

      vi.mocked(getServiceFactory).mockReturnValue(extendedFactory)

      const result = registerLoggerService(mockRegistry)

      expect(extendedFactory.createLogger).toHaveBeenCalledTimes(1)
      expect(extendedFactory.createService).not.toHaveBeenCalled()
      expect(extendedFactory.getConfig).not.toHaveBeenCalled()
      expect(result).toBe(mockLoggerService)
    })
  })

  describe('integration scenarios', () => {
    it('should work in a typical application initialization flow', () => {
      const result = registerLoggerService(mockRegistry)

      // Verify the complete flow
      expect(getServiceFactory).toHaveBeenCalledTimes(1)
      expect(mockServiceFactory.createLogger).toHaveBeenCalledTimes(1)
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.Logger,
        mockLoggerService,
      )
      expect(result).toBe(mockLoggerService)
    })

    it('should handle service replacement scenarios', () => {
      const logger1 = { info: vi.fn(), warn: vi.fn(), error: vi.fn(), debug: vi.fn(), id: 'logger1' }
      const logger2 = { info: vi.fn(), warn: vi.fn(), error: vi.fn(), debug: vi.fn(), id: 'logger2' }

      mockServiceFactory.createLogger
        .mockReturnValueOnce(logger1)
        .mockReturnValueOnce(logger2)

      // Initial registration
      const result1 = registerLoggerService(mockRegistry)
      expect(result1).toBe(logger1)

      // Service replacement
      const result2 = registerLoggerService(mockRegistry)
      expect(result2).toBe(logger2)

      expect(mockRegistry.register).toHaveBeenCalledTimes(2)
      expect(mockRegistry.register).toHaveBeenNthCalledWith(1, ServiceId.Logger, logger1)
      expect(mockRegistry.register).toHaveBeenNthCalledWith(2, ServiceId.Logger, logger2)
    })

    it('should work with real-world service dependencies', () => {
      // Simulate more realistic scenario
      const result = registerLoggerService(mockRegistry)

      // Verify that the service is properly configured
      expect(result).toBeDefined()
      expect(mockServiceFactory.createLogger).toHaveBeenCalledTimes(1)

      // Verify that the service can be retrieved from registry
      expect(mockRegistry.register).toHaveBeenCalledWith(
        ServiceId.Logger,
        result,
      )

      // Verify logger interface
      expect(typeof result.info).toBe('function')
      expect(typeof result.warn).toBe('function')
      expect(typeof result.error).toBe('function')
      expect(typeof result.debug).toBe('function')
    })

    it('should handle concurrent registration attempts', async () => {
      // Simulate concurrent calls
      const promises = [
        Promise.resolve(registerLoggerService(mockRegistry)),
        Promise.resolve(registerLoggerService(mockRegistry)),
        Promise.resolve(registerLoggerService(mockRegistry)),
      ]

      return Promise.all(promises).then((results) => {
        expect(results).toHaveLength(3)
        results.forEach((result) => {
          expect(result).toBe(mockLoggerService)
        })
        expect(getServiceFactory).toHaveBeenCalledTimes(3)
        expect(mockRegistry.register).toHaveBeenCalledTimes(3)
      })
    })
  })
})
