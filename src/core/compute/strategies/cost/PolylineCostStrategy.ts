/**
 * Cost Calculation Strategy for Polyline Elements
 *
 * @remarks
 * This strategy implements the {@link CostCalculatorStrategy} to calculate costs
 * for Polyline elements ({@link CoreElementType.POLYLINE}).
 *
 * It supports various cost calculation bases, determined by the `costType`
 * property in the {@link CostCalculationOptions}:
 * - `'perimeter'`: Cost is based on the polyline's length. Delegates to {@link PolylinePerimeterStrategy}.
 * - `'area'`: Cost is based on the area enclosed by the polyline (if it's closed).
 *   Delegates to {@link PolylineAreaStrategy}. Returns 0 if the polyline is not closed.
 * - `'segment'`: Cost is based on the number of segments in the polyline, multiplied
 *   by a `segmentUnitCost` (or `unitCost` fallback) and `segmentFactor`.
 * - `'fixed'`: Cost is based on a `baseCost` defined in options.
 *
 * The strategy internally instantiates and uses `PolylinePerimeterStrategy` and
 * `PolylineAreaStrategy` for geometric calculations. It also applies general cost
 * adjustment factors from {@link CostCalculationOptions} (like `materialFactor`,
 * `additionalCost`, `discountRate`, `taxRate`).
 *
 * @module core/compute/strategies/cost/PolylineCostStrategy
 * @see {@link CostCalculatorStrategy}
 * @see {@link CostCalculationOptions}
 * @see {@link Path.Polyline}
 * @see {@link PolylinePerimeterStrategy}
 * @see {@link PolylineAreaStrategy}
 */
import type { CostCalculationOptions, CostCalculatorStrategy } from '../../../../types/core/compute'
import type {
  Element,
  Path,
  ShapeElement, // Import ShapeElement
} from '../../../../types/core/elementDefinitions'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType, // Import ShapeElement
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

import { PolylineAreaStrategy } from '../area' // Assumes index.ts in ../area exports this
import { PolylinePerimeterStrategy } from '../perimeter' // Assumes index.ts in ../perimeter exports this

/**
 * Implements the {@link CostCalculatorStrategy} for {@link CoreElementType.POLYLINE} elements,
 * allowing for cost calculation based on perimeter, area (if closed), segment count, or a fixed base.
 *
 * @implements CostCalculatorStrategy
 */
export class PolylineCostStrategy implements CostCalculatorStrategy {
  /**
   * Strategy for calculating polyline perimeter.
   * @private
   */
  private polylinePerimeterStrategy: PolylinePerimeterStrategy

  /**
   * Strategy for calculating polyline area.
   * @private
   */
  private polylineAreaStrategy: PolylineAreaStrategy

  /**
   * Creates an instance of PolylineCostStrategy.
   */
  constructor() {
    this.polylinePerimeterStrategy = new PolylinePerimeterStrategy()
    this.polylineAreaStrategy = new PolylineAreaStrategy()
  }

  /**
   * Calculates the cost for a Polyline element based on the specified `costType`.
   *
   * @param element - The polyline element ({@link CoreElementType.POLYLINE}).
   * @param unitCost - The base cost per unit (meter for perimeter, sq. meter for area, or per segment if `segmentUnitCost` is not in options).
   *                   Not directly used if `costType` is 'fixed'.
   * @param options - Optional {@link CostCalculationOptions}. Key properties include:
   *                  `costType` (default: 'perimeter'; options: 'perimeter', 'area', 'segment', 'fixed'),
   *                  `segmentFactor`, `materialFactor`, `segmentUnitCost` (for 'segment' type),
   *                  `baseCost` (for 'fixed' type), `additionalCost`, `discountRate`, `taxRate`.
   * @returns The total calculated cost for the polyline element.
   * @throws {@link CoreError} if the element type is not `POLYLINE`.
   * @throws {@link CoreError} if `unitCost` is invalid (e.g., negative) when applicable.
   * @throws {@link CoreError} if `costType` is invalid.
   * @throws {@link CoreError} if the polyline has fewer than 2 points.
   * @throws {@link CoreError} if an underlying area or perimeter calculation fails.
   */
  public calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
    if (element.type !== CoreElementType.POLYLINE) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `PolylineCostStrategy can only calculate cost for POLYLINE elements, got ${element.type}`,
        undefined,
        { component: 'PolylineCostStrategy', operation: 'calculateCost', target: element.id },
      )
    }

    if (typeof unitCost !== 'number' || unitCost < 0) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Unit cost must be a non-negative number, got ${unitCost}`,
        undefined,
        { component: 'PolylineCostStrategy', operation: 'calculateCost', target: element.id, metadata: { unitCost } },
      )
    }

    // 获取元素的成本相关属性
    const el = element as { properties?: Record<string, unknown>, costMultiplierOrCount?: number }
    const props = el.properties ?? {}

    // 获取乘数值，优先从 properties 中获取
    let costMultiplierOrCount = 0 // 默认为0
    if (typeof props.costMultiplierOrCount === 'number') {
      costMultiplierOrCount = props.costMultiplierOrCount
    }
    else if (typeof el.costMultiplierOrCount === 'number') {
      costMultiplierOrCount = el.costMultiplierOrCount
    }

    const costType = options?.costType ?? 'unit' // 默认使用 unit 类型，与其他元素保持一致
    // Note: segmentFactor and materialFactor are available in options but not currently used in this implementation

    const polylineElement = element as unknown as Path.Polyline // Added unknown for type safety
    const points = polylineElement.properties?.points

    if (!Array.isArray(points) || points.length < 2) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid polyline points for element ID ${element.id}: must have at least 2 points. Found: ${points?.length || 0}`,
        undefined,
        { component: 'PolylineCostStrategy', operation: 'calculateCost', target: element.id, metadata: { pointsLength: points?.length || 0 } },
      )
    }

    const segmentCount = points.length - 1
    let measure = 0

    try {
      switch (costType) {
        case 'perimeter':
          measure = this.polylinePerimeterStrategy.calculatePerimeter(polylineElement as unknown as ShapeElement)
          break
        case 'area':
          // PolylineAreaStrategy returns 0 if not closed, which is correct for cost.
          // polylineElement is Path.Polyline, which should extend Element via ShapeElement.
          // The calculateArea method of PolylineAreaStrategy expects an Element.
          measure = this.polylineAreaStrategy.calculateArea(polylineElement as unknown as Element)
          break
        case 'segment': {
          const segmentUnitCost = options?.segmentUnitCost ?? unitCost // Use specific segment unit cost or fallback
          // For segment cost, adjustments like additionalCost, discount, tax are applied later
          measure = segmentCount * segmentUnitCost // Base measure for segment cost
          // Factors like segmentFactor and materialFactor will be applied below
          break
        }
        case 'unit': {
          const unitBaseCost = options?.baseCost ?? unitCost
          measure = unitBaseCost
          break
        }
        case 'fixed': {
          const baseCost = options?.baseCost ?? 100 // Example fixed base cost
          // For fixed cost, adjustments are applied later
          measure = baseCost // Base measure for fixed cost
          // Factors like segmentFactor and materialFactor will be applied below
          break
        }
        default:
          throw new CoreError(
            ErrorType.InvalidParameter,
            `Invalid cost type: ${String(costType)} for PolylineCostStrategy`,
            undefined,
            { component: 'PolylineCostStrategy', operation: 'calculateCost', target: element.id, metadata: { costType } },
          )
      }
    }
    catch (error) {
      const errMessage = error instanceof Error ? error.message : String(error)
      throw new CoreError(
        ErrorType.ComputationError,
        `Failed to get ${costType} for polyline element (ID: ${element.id}): ${errMessage}`,
        undefined,
        { component: 'PolylineCostStrategy', operation: 'calculateCost', target: element.id, metadata: { originalError: error, costType } },
      )
    }

    if (typeof measure !== 'number' || Number.isNaN(measure) || measure < 0) {
      // This case should ideally be caught by sub-strategies throwing errors for invalid calculations.
      // However, as a fallback, or if 'fixed'/'segment' calculation somehow yields NaN.
      console.warn(`[PolylineCostStrategy] Invalid measure (${costType}: ${measure}) for element ${element.id}. Defaulting to 0 for cost calculation. This might indicate an issue in a sub-strategy or options.`)
      measure = 0
    }

    // 计算总成本，使用与其他元素一致的逻辑
    let calculatedCost: number
    if (costType === 'unit' || costType === 'segment' || costType === 'fixed') {
      // 对于单位、线段或固定成本，使用 measure * unitCost * costMultiplierOrCount
      calculatedCost = measure * unitCost * costMultiplierOrCount
    }
    else {
      // 对于周长或面积成本，也使用相同的逻辑
      calculatedCost = measure * unitCost * costMultiplierOrCount
    }

    // Apply common adjustments
    if (options?.additionalCost != null && typeof options.additionalCost === 'number' && options.additionalCost > 0) {
      calculatedCost += options.additionalCost
    }
    if (options?.discountRate != null && typeof options.discountRate === 'number' && options.discountRate >= 0 && options.discountRate <= 100) {
      calculatedCost *= (1 - (options.discountRate / 100))
    }
    if (options?.taxRate != null && typeof options.taxRate === 'number' && options.taxRate >= 0) {
      calculatedCost *= (1 + (options.taxRate / 100))
    }

    return calculatedCost
  }

  /**
   * Returns the element type that this strategy is responsible for.
   * @returns The element type {@link CoreElementType.POLYLINE}
   */
  public getElementType(): CoreElementType {
    return CoreElementType.POLYLINE
  }
}
