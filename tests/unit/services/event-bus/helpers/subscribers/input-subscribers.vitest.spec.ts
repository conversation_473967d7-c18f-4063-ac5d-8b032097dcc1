import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  subscribeToCanvasClickEvents,
  subscribeToCanvasDoubleClickEvents,
  subscribeToCanvasMouseDownEvents,
  subscribeToCanvasMouseMoveEvents,
  subscribeToCanvasMouseUpEvents,
  subscribeToCommandExecutedEvents,
  subscribeToInputFocusChangedEvents,
  subscribeToKeyPressedEvents,
  subscribeToKeyReleasedEvents,
  subscribeToModeChangedEvents,
  subscribeToShortcutTriggeredEvents,
  subscribeToToolSelectedEvents,
} from '@/services/event-bus/helpers/subscribers/input-subscribers'
import * as utils from '@/services/event-bus/helpers/subscribers/utils'
import { AppEventType } from '@/types/services/events'

describe('input Subscribers', () => {
  let mockEventBus: any
  let mockHandler: any
  let mockOptions: any
  let typedSubscribeSpy: any

  beforeEach(() => {
    mockEventBus = {
      subscribe: vi.fn().mockReturnValue(() => {}),
    }
    mockHandler = vi.fn()
    mockOptions = { once: true }

    // Spy on the typedSubscribe function
    typedSubscribeSpy = vi.spyOn(utils, 'typedSubscribe').mockReturnValue(() => {})
  })

  describe('keyboard Events', () => {
    it('should subscribe to key pressed events correctly', () => {
      const unsubscribe = subscribeToKeyPressedEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.KEY_PRESSED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should subscribe to key released events correctly', () => {
      const unsubscribe = subscribeToKeyReleasedEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.KEY_RELEASED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('canvas Mouse Events', () => {
    it('should subscribe to canvas click events correctly', () => {
      const unsubscribe = subscribeToCanvasClickEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.CANVAS_CLICKED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should subscribe to canvas double click events correctly', () => {
      const unsubscribe = subscribeToCanvasDoubleClickEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.CANVAS_DBL_CLICKED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should subscribe to canvas mouse down events correctly', () => {
      const unsubscribe = subscribeToCanvasMouseDownEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.CANVAS_MOUSE_DOWN,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should subscribe to canvas mouse move events correctly', () => {
      const unsubscribe = subscribeToCanvasMouseMoveEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.CANVAS_MOUSE_MOVE,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should subscribe to canvas mouse up events correctly', () => {
      const unsubscribe = subscribeToCanvasMouseUpEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.CANVAS_MOUSE_UP,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('tool and Mode Events', () => {
    it('should subscribe to tool selected events correctly', () => {
      const unsubscribe = subscribeToToolSelectedEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.TOOL_SELECTED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should subscribe to mode changed events correctly', () => {
      const unsubscribe = subscribeToModeChangedEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.MODE_CHANGED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('command and Shortcut Events', () => {
    it('should subscribe to command executed events correctly', () => {
      const unsubscribe = subscribeToCommandExecutedEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.COMMAND_EXECUTED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should subscribe to shortcut triggered events correctly', () => {
      const unsubscribe = subscribeToShortcutTriggeredEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.SHORTCUT_TRIGGERED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('focus Events', () => {
    it('should subscribe to input focus changed events correctly', () => {
      const unsubscribe = subscribeToInputFocusChangedEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.INPUT_FOCUS_CHANGED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })
})
