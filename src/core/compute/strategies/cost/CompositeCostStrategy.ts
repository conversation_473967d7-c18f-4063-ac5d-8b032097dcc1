/**
 * Composite Cost Calculation Strategy
 *
 * @remarks
 * This strategy implements the {@link CostCalculatorStrategy} to calculate comprehensive
 * costs for elements, potentially encompassing material, labor, design, and overhead
 * components. The cost basis (area, perimeter, or fixed) can vary depending on the
 * element type.
 *
 * **Important Dependency:** This strategy currently assumes that the provided `element`
 * object may have methods like `compute.area()` and `compute.perimeter()` to retrieve
 * geometric properties. This is a point of coupling that might be refactored to use
 * dedicated calculation services (like {@link ComputeFacade}) or to receive these
 * geometric values as direct inputs, promoting better separation of concerns.
 *
 * The strategy uses various cost rates provided in {@link CostCalculationOptions}
 * (e.g., `materialCostPerUnit`, `laborCostPerUnit`) or falls back to calculations
 * based on the `unitCost` parameter. It also applies adjustments like additional costs,
 * discounts, and taxes.
 *
 * The `getElementType()` method returns a generic string 'composite_all', indicating
 * its potential applicability to many element types, rather than being tied to a
 * specific {@link ElementType}. The {@link StrategyRegistry} would need to handle
 * how such a generic strategy is mapped or prioritized.
 *
 * @module core/compute/strategies/cost/CompositeCostStrategy
 * @see {@link CostCalculatorStrategy}
 * @see {@link CostCalculationOptions}
 */
import type { CostCalculationOptions, CostCalculatorStrategy } from '../../../../types/core/compute'
import type { Element } from '../../../../types/core/elementDefinitions'
import { CoreError } from '../../../../services/system/error-service/coreError'
import { ElementType as CoreElementType } from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

export class CompositeCostStrategy implements CostCalculatorStrategy {
  /**
   * Calculates the composite cost for an element, considering various factors like
   * material, labor, design, and overheads, based on area, perimeter, or fixed rates.
   *
   * @param element - The element for which to calculate the cost. It's assumed this element
   *                  might have `compute.area()` and `compute.perimeter()` methods.
   * @param unitCost - A base unit cost. This might be used as a fallback if specific
   *                   cost rates (e.g., `materialCostPerUnit`) are not provided in `options`.
   * @param options - Optional {@link CostCalculationOptions} that can include specific rates
   *                  for material, labor, design, overhead, as well as quantity,
   *                  additional costs, discount, and tax rates.
   * @returns The total calculated composite cost.
   * @throws {@link CoreError} if `unitCost` is invalid (e.g., negative).
   * @throws {@link CoreError} if internal area or perimeter calculations (if attempted via `element.compute`) fail.
   */
  public calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
    if (typeof unitCost !== 'number' || unitCost < 0) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Unit cost must be a non-negative number, got ${unitCost}`,
        undefined,
        { component: 'CompositeCostStrategy', operation: 'calculateCost', target: element.id, metadata: { unitCost } },
      )
    }

    let area = 0
    let perimeter = 0
    // TODO: Refactor to decouple geometric calculations from the element instance.
    // This strategy should ideally use AreaCalculatorStrategy/PerimeterCalculatorStrategy or receive these values.

    // Type-safe interface for elements with compute methods
    interface ComputeCapableElement {
      compute?: {
        area?: () => number
        perimeter?: () => number
      }
    }

    const computableElement = element as unknown as ComputeCapableElement

    try {
      // Type-safe access to compute methods
      if (computableElement.compute != null && typeof computableElement.compute === 'object') {
        if (typeof computableElement.compute.area === 'function') {
          // FIXME: This direct call bypasses ComputeFacade and geometry utils if compute.area() is implemented differently.
          area = computableElement.compute.area()
        }
        if (typeof computableElement.compute.perimeter === 'function') {
          // FIXME: This direct call bypasses ComputeFacade and geometry utils if compute.perimeter() is implemented differently.
          perimeter = computableElement.compute.perimeter()
        }
      }
    }
    catch (error) {
      throw new CoreError(
        ErrorType.ComputationError,
        `Failed to calculate area or perimeter for element (ID: ${element.id}): ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        { component: 'CompositeCostStrategy', operation: 'calculateCost', target: element.id, metadata: { originalError: error } },
      )
    }

    // Ensure area and perimeter are valid numbers, default to 0 if not.
    if (typeof area !== 'number' || Number.isNaN(area) || area < 0)
      area = 0
    if (typeof perimeter !== 'number' || Number.isNaN(perimeter) || perimeter < 0)
      perimeter = 0

    const materialCostPerUnit = options?.materialCostPerUnit ?? unitCost // Fallback to unitCost if specific material cost not provided
    const laborCostPerUnit = options?.laborCostPerUnit ?? (unitCost * 0.3) // Example default
    const designCostPerUnit = options?.designCostPerUnit ?? (unitCost * 0.1) // Example default
    const overheadCostPerUnit = options?.overheadCostPerUnit ?? (unitCost * 0.15) // Example default

    let materialCost = 0
    let laborCost = 0
    let designCost = 0
    let overheadCost = 0

    // Determine cost basis (area or perimeter)
    switch (element.type as CoreElementType) { // Cast to CoreElementType for switch
      // Area-based cost calculation
      case CoreElementType.RECTANGLE:
      case CoreElementType.SQUARE:
      case CoreElementType.CIRCLE:
      case CoreElementType.ELLIPSE:
      case CoreElementType.POLYGON:
      case CoreElementType.TRIANGLE:
      case CoreElementType.HEXAGON:
      case CoreElementType.QUADRILATERAL:
      case CoreElementType.PENTAGON:
      case CoreElementType.HEPTAGON:
      case CoreElementType.OCTAGON:
      case CoreElementType.NONAGON:
      case CoreElementType.DECAGON:
      case CoreElementType.ROOM:
      case CoreElementType.FLOOR_AREA:
      case CoreElementType.ROOM_BOUNDARY:
        materialCost = area * materialCostPerUnit
        laborCost = area * laborCostPerUnit
        designCost = area * designCostPerUnit
        overheadCost = area * overheadCostPerUnit
        break

      // Perimeter-based cost calculation
      case CoreElementType.LINE:
      case CoreElementType.POLYLINE:
      case CoreElementType.ARC:
      case CoreElementType.QUADRATIC:
      case CoreElementType.CUBIC:
      case CoreElementType.LINE:
      case CoreElementType.POLYLINE:
      case CoreElementType.ARC:
      case CoreElementType.QUADRATIC:
      case CoreElementType.CUBIC:
      case CoreElementType.WALL:
      case CoreElementType.HANDRAIL:
        materialCost = perimeter * materialCostPerUnit
        laborCost = perimeter * laborCostPerUnit
        designCost = perimeter * designCostPerUnit
        overheadCost = perimeter * overheadCostPerUnit
        break

      // Fixed cost calculation for specific elements
      case CoreElementType.DOOR:
      case CoreElementType.WINDOW:
      case CoreElementType.FURNITURE:
      case CoreElementType.FIXTURE:
      case CoreElementType.LIGHT:
      case CoreElementType.ELECTRICAL_OUTLET:
      case CoreElementType.APPLIANCE:
      case CoreElementType.TEXT_LABEL:
      case CoreElementType.TEXT:
      case CoreElementType.IMAGE:
      case CoreElementType.GROUP:
      case CoreElementType.OPENING:
      case CoreElementType.WALL_PAINT:
      case CoreElementType.WALL_PAPER:
        materialCost = materialCostPerUnit
        laborCost = laborCostPerUnit
        designCost = designCostPerUnit
        overheadCost = overheadCostPerUnit
        break
    }

    const quantity = options?.quantity ?? 1
    materialCost *= quantity
    laborCost *= quantity
    designCost *= quantity
    overheadCost *= quantity

    let totalCost = materialCost + laborCost + designCost + overheadCost

    // Apply adjustments
    if (options?.additionalCost != null && typeof options.additionalCost === 'number' && options.additionalCost > 0) {
      totalCost += options.additionalCost
    }
    if (options?.discountRate != null && typeof options.discountRate === 'number' && options.discountRate >= 0 && options.discountRate <= 100) {
      totalCost *= (1 - options.discountRate / 100)
    }
    if (options?.taxRate != null && typeof options.taxRate === 'number' && options.taxRate >= 0) {
      totalCost *= (1 + options.taxRate / 100)
    }

    return totalCost
  }

  /**
   * Returns a string indicating the general applicability of this composite strategy.
   *
   * @remarks
   * This strategy is designed to be versatile and can apply to various element types
   * by considering different cost components and bases (area, perimeter, fixed).
   * It returns a generic identifier string 'composite_all' rather than a specific
   * {@link ElementType}. The {@link StrategyRegistry} would determine how to map
   * or use such a broadly applicable strategy.
   *
   * @returns The string 'composite_all'.
   */
  public getElementType(): string {
    return 'composite_all' // Indicates general applicability across multiple element types
  }
}
