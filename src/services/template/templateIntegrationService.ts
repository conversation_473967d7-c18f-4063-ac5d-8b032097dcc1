/**
 * Template Integration Service
 *
 * Service for integrating template data with the application's shape store.
 * Handles loading template shapes and applying them to the current design.
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'
import type { MajorCategory, MinorCategory } from '@/types/core/majorMinorTypes'
import { appEventBus } from '@/services/core/event-bus'
import { useShapesStore } from '@/store/shapesStore'

/**
 * Convert template data to ensure compatibility with ShapeElement interface
 */
function normalizeTemplateShape(shape: any): ShapeElement {
  // Map old minorCategory values to new ones
  const minorCategoryMap: Record<string, MinorCategory> = {
    TABLE: 'tables',
    CHAIR: 'seating',
    BED: 'beds',
    STORAGE: 'storage',
    APPLIANCE: 'appliances',
    architecture: 'architecture',
    coverings: 'coverings',
    decor: 'decor',
    utilities: 'utilities',
    lighting: 'lighting',
    tables: 'tables',
    seating: 'seating',
    beds: 'beds',
    storage: 'storage',
    appliances: 'appliances',
  }

  // Ensure majorCategory is valid
  const validMajorCategories = ['BASE', 'CEILING', 'FURNITURE']
  const majorCategory = validMajorCategories.includes(shape.majorCategory)
    ? shape.majorCategory as MajorCategory
    : 'BASE' as MajorCategory

  // Map minorCategory
  const minorCategory = shape.minorCategory
    ? minorCategoryMap[shape.minorCategory] || 'architecture' as MinorCategory
    : 'architecture' as MinorCategory

  // Ensure required fields exist
  const normalizedShape: ShapeElement = {
    ...shape,
    majorCategory,
    minorCategory,
    // Ensure required fields have defaults
    position: shape.position || { x: 0, y: 0, z: 0 },
    rotation: shape.rotation || 0,
    visible: shape.visible !== undefined ? shape.visible : true,
    locked: shape.locked !== undefined ? shape.locked : false,
    selectable: shape.selectable !== undefined ? shape.selectable : true,
    draggable: shape.draggable !== undefined ? shape.draggable : true,
    showHandles: shape.showHandles !== undefined ? shape.showHandles : true,
    metadata: shape.metadata || {
      createdAt: Date.now(),
      updatedAt: Date.now(),
    },
  }

  return normalizedShape
}

/**
 * Template integration service implementation
 */
class TemplateIntegrationServiceImpl {
  private isInitialized = false

  /**
   * Initialize the service and set up event listeners
   */
  initialize(): void {
    if (this.isInitialized) {
      return
    }

    // Listen for template application events
    appEventBus.on('shapes:load-template', this.handleLoadTemplate)
    appEventBus.on('shapes:clear-all', this.handleClearAll)

    this.isInitialized = true
    console.log('Template Integration Service initialized')
  }

  /**
   * Handle loading template shapes into the store
   */
  private handleLoadTemplate = (event: any) => {
    try {
      // Get the shapes store
      const shapesStore = useShapesStore.getState()

      // Clear existing shapes first
      shapesStore.clearShapes()

      // Add template shapes using setShapesFromExternal for better persistence
      if (event.shapes && event.shapes.length > 0) {
        // Normalize template shapes to ensure compatibility
        const normalizedShapes = event.shapes.map((shape: any) => normalizeTemplateShape(shape))

        // Use setShapesFromExternal instead of adding shapes one by one
        // This ensures better persistence and triggers the persist middleware correctly
        shapesStore.setShapesFromExternal(normalizedShapes, event.selectedShapeIds)

        // Force save to localStorage to ensure persistence
        this.forcePersistToLocalStorage()

        // Also schedule a delayed save to ensure persistence
        setTimeout(() => {
          this.forcePersistToLocalStorage()
        }, 500)

        // Set selected shapes if provided
        if (event.selectedShapeIds && event.selectedShapeIds.length > 0) {
          // Note: This would need to be handled by the element actions hook
          // For now, we'll just store the selection
        }
      }

      console.log('Template shapes loaded successfully')

      // Emit completion event
      appEventBus.emit({
        type: 'template:shapes-loaded',
        payload: {
          shapesCount: event.shapes.length,
          selectedCount: event.selectedShapeIds.length,
        },
        timestamp: Date.now()
      })
    }
    catch (error) {
      console.error('Failed to load template shapes:', error)
      appEventBus.emit({
        type: 'template:shapes-load-error',
        payload: { error },
        timestamp: Date.now()
      })
    }
  }

  /**
   * Force persist current store state to localStorage
   */
  private forcePersistToLocalStorage(): void {
    try {
      const storeState = useShapesStore.getState()
      const dataToSave = {
        shapes: storeState.shapes,
        selectedShapeIds: storeState.selectedShapeIds,
      }

      // Save to localStorage with the correct key
      localStorage.setItem('reno-pilot-shapes-storage', JSON.stringify(dataToSave))
    }
    catch (error) {
      console.error('Error during force persist:', error)
    }
  }

  /**
   * Handle clearing all shapes
   */
  private handleClearAll = () => {
    try {
      console.log('Clearing all shapes from store')

      // Get the shapes store
      const shapesStore = useShapesStore.getState()

      // Clear all shapes
      shapesStore.clearShapes()

      console.log('All shapes cleared successfully')

      // Emit completion event
      appEventBus.emit({
        type: 'template:shapes-cleared',
        payload: {},
        timestamp: Date.now()
      })
    }
    catch (error) {
      console.error('Failed to clear shapes:', error)
    }
  }

  /**
   * Manually load template data from localStorage
   */
  loadStoredTemplate(): boolean {
    try {
      const storedTemplate = localStorage.getItem('renopilot-current-template')
      const templateLoaded = localStorage.getItem('renopilot-template-loaded')

      if (templateLoaded === 'true' && storedTemplate) {
        const templateData = JSON.parse(storedTemplate)

        // Normalize shapes before loading
        const normalizedShapes = (templateData.shapes || []).map((shape: any) => normalizeTemplateShape(shape))

        this.handleLoadTemplate({
          shapes: normalizedShapes,
          selectedShapeIds: templateData.selectedShapeIds || [],
        })

        console.log('Stored template loaded:', templateData.metadata?.name)
        return true
      }

      return false
    }
    catch (error) {
      console.error('Failed to load stored template:', error)
      return false
    }
  }

  /**
   * Get current template info from localStorage
   */
  getCurrentTemplateInfo(): { name: string, shapesCount: number } | null {
    try {
      const storedTemplate = localStorage.getItem('renopilot-current-template')
      const templateLoaded = localStorage.getItem('renopilot-template-loaded')
      if (templateLoaded === 'true' && storedTemplate) {
        const templateData = JSON.parse(storedTemplate)
        return {
          name: templateData.metadata?.name || 'Unknown Template',
          shapesCount: templateData.shapes?.length || 0,
        }
      }
      return null
    }
    catch (error) {
      console.error('Failed to get current template info:', error)
      return null
    }
  }

  /**
   * Clear stored template data
   */
  clearStoredTemplate(): void {
    localStorage.removeItem('renopilot-current-template')
    localStorage.removeItem('renopilot-template-loaded')
    localStorage.removeItem('renopilot-applied-template-state')
    console.log('Stored template data cleared')
  }

  /**
   * Cleanup service
   */
  cleanup(): void {
    if (this.isInitialized) {
      appEventBus.off('shapes:load-template', this.handleLoadTemplate)
      appEventBus.off('shapes:clear-all', this.handleClearAll)
      this.isInitialized = false
      console.log('Template Integration Service cleaned up')
    }
  }
}

// Export singleton instance
export const templateIntegrationService = new TemplateIntegrationServiceImpl()

// Auto-initialize the service
templateIntegrationService.initialize()
