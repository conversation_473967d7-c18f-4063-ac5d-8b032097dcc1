/**
 * Unit tests for base validator
 *
 * Tests the base validator class
 */

import type { ValidationError, ValidatorShape } from '@/types/core/element/validator'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { BaseShapeValidator } from '@/core/validator/common/validatorBase'
import { ElementType } from '@/types/core/shape-type'

// Create a concrete implementation of BaseShapeValidator for testing
class TestValidator extends BaseShapeValidator {
  constructor() {
    super()
  }

  // Override abstract methods
  protected validateSpecific(shape: ValidatorShape): ValidationError[] {
    // Simple implementation for testing
    if (shape.id === 'invalid-shape') {
      return [{ code: 'TEST_ERROR', message: 'Test error message' }]
    }
    return []
  }
}

describe('baseShapeValidator', () => {
  let validator: TestValidator
  let consoleSpy: any

  beforeEach(() => {
    validator = new TestValidator()
    consoleSpy = {
      warn: vi.spyOn(console, 'warn').mockImplementation(() => {}),
      error: vi.spyOn(console, 'error').mockImplementation(() => {}),
    }
    vi.clearAllMocks()
  })

  describe('validate', () => {
    it('should return valid result for valid shape', () => {
      const shape: ValidatorShape = {
        id: 'valid-shape',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
        width: 200,
        height: 100,
      }

      const result = validator.validate(shape)

      expect(result.valid).toBe(true)
    })

    it('should validate stroke color', () => {
      // Create a shape with invalid stroke color
      const shape: ValidatorShape = {
        id: 'shape-with-invalid-stroke',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
        strokeColor: 'invalid-color',
      }

      // Mock the isValidColor function to return false for 'invalid-color'
      const isValidColorMock = vi.fn().mockImplementation(color => color !== 'invalid-color')

      // Replace the isValidColor function in the validator
      const originalIsValidColor = (validator as any).isValidColor;
      (validator as any).isValidColor = isValidColorMock

      try {
        // Call validate
        const result = validator.validate(shape)

        // Verify the result
        expect(result.valid).toBe(false)
        expect(result.errors?.some(e => e.code === 'INVALID_STROKE_COLOR')).toBe(true)
      }
      finally {
        // Restore the original isValidColor function
        (validator as any).isValidColor = originalIsValidColor
      }
    })

    it('should validate fill color', () => {
      // Create a shape with invalid fill color
      const shape: ValidatorShape = {
        id: 'shape-with-invalid-fill',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
        fillColor: 'invalid-color',
      }

      // Mock the isValidColor function to return false for 'invalid-color'
      const isValidColorMock = vi.fn().mockImplementation(color => color !== 'invalid-color')

      // Replace the isValidColor function in the validator
      const originalIsValidColor = (validator as any).isValidColor;
      (validator as any).isValidColor = isValidColorMock

      try {
        // Call validate
        const result = validator.validate(shape)

        // Verify the result
        expect(result.valid).toBe(false)
        expect(result.errors?.some(e => e.code === 'INVALID_FILL_COLOR')).toBe(true)
      }
      finally {
        // Restore the original isValidColor function
        (validator as any).isValidColor = originalIsValidColor
      }
    })

    it('should return invalid result for missing shape ID', () => {
      const shape: ValidatorShape = {
        id: '',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
      }

      const result = validator.validate(shape)

      expect(result.valid).toBe(false)
      expect(result.errors).toBeDefined()
      expect(result.errors?.some(e => e.code === 'MISSING_OR_INVALID_ID')).toBe(true)
    })

    it('should return invalid result from specific validator rules', () => {
      const shape: ValidatorShape = {
        id: 'invalid-shape', // This ID triggers an error in our test validator
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
      }

      const result = validator.validate(shape)

      expect(result.valid).toBe(false)
      expect(result.errors).toBeDefined()
      expect(result.errors?.some(e => e.code === 'TEST_ERROR')).toBe(true)
    })

    it('should handle null and undefined shapes', () => {
      // We need to wrap the validate call in a function to catch the error
      expect(() => validator.validate(null as any)).toThrow()
      expect(() => validator.validate(undefined as any)).toThrow()
    })

    it('should handle errors in validateSpecific', () => {
      const shape: ValidatorShape = {
        id: 'shape-with-error',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
      }

      // Mock validateSpecific to throw an error
      const validateSpecificSpy = vi.spyOn(validator as any, 'validateSpecific')
        .mockImplementation(() => {
          throw new Error('Test error in validateSpecific')
        })

      const result = validator.validate(shape)

      // Verify that the error was caught and handled
      expect(result.valid).toBe(false)
      expect(result.errors?.some(e => e.code === 'VALIDATION_SPECIFIC_ERROR')).toBe(true)
      expect(consoleSpy.error).toHaveBeenCalled()

      // Restore the original implementation
      validateSpecificSpy.mockRestore()
    })

    it('should handle errors in applyAllRules', () => {
      const shape: ValidatorShape = {
        id: 'shape-with-error',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
      }

      // Mock applyAllRules to throw an error
      const applyAllRulesSpy = vi.spyOn(validator as any, 'applyAllRules')
        .mockImplementation(() => {
          throw new Error('Test error in applyAllRules')
        })

      const result = validator.validate(shape)

      // Verify that the error was caught and handled
      expect(result.valid).toBe(false)
      expect(result.errors?.some(e => e.code === 'VALIDATION_RULE_ERROR')).toBe(true)
      expect(consoleSpy.error).toHaveBeenCalled()

      // Restore the original implementation
      applyAllRulesSpy.mockRestore()
    })
  })

  describe('applyAllRules', () => {
    it('should call applySpecificRules', () => {
      // Create a test shape
      const shape: ValidatorShape = {
        id: 'test-shape',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
      }

      // Mock applySpecificRules to return some errors
      const applySpecificRulesSpy = vi.spyOn(validator as any, 'applySpecificRules')
        .mockReturnValue([{ code: 'SPECIFIC_RULE_ERROR', message: 'Specific rule error' }]);

      // Call applyAllRules
      (validator as any).applyAllRules(shape)

      // Verify that applySpecificRules was called
      expect(applySpecificRulesSpy).toHaveBeenCalledWith(shape)
    })
  })

  describe('applySpecificRules', () => {
    it('should return empty array by default', () => {
      // Create a test shape
      const shape: ValidatorShape = {
        id: 'test-shape',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
      }

      // Call applySpecificRules
      const result = (validator as any).applySpecificRules(shape)

      // Verify that it returns an empty array
      expect(result).toEqual([])
    })
  })

  describe('validateSpecific', () => {
    it('should be called during validation', () => {
      // Create a test shape
      const shape: ValidatorShape = {
        id: 'test-shape',
        type: ElementType.RECTANGLE,
        position: { x: 100, y: 100 },
      }

      // Spy on validateSpecific
      const validateSpecificSpy = vi.spyOn(validator as any, 'validateSpecific')

      // Call validate
      validator.validate(shape)

      // Verify that validateSpecific was called
      expect(validateSpecificSpy).toHaveBeenCalledWith(shape)

      // Restore the original implementation
      validateSpecificSpy.mockRestore()
    })
  })
})
