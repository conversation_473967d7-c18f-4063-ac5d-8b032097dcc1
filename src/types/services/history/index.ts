/**
 * History Service Types
 *
 * This module exports types related to history tracking and undo/redo functionality.
 *
 * @module types/services/history
 */

// Core types
import type { ServiceResult } from '../core/serviceResult'
/**
 * History Entry Interface
 *
 * Represents an individual history entry's data.
 */
export interface HistoryEntry {
  /** Entry identifier */
  id: string
  /** Timestamp when the entry was created */
  timestamp: number
  /** Action type (e.g., 'create', 'edit', 'delete') */
  action: string // This field is crucial for understanding the operation
  /** Target identifier (e.g., element ID) */
  targetId?: string
  /** State before the action */
  before?: unknown
  /** State after the action */
  after?: unknown
  /** User identifier who made the change */
  userId?: string
  /** Optional description of the change */
  description?: string // Description is important for user understanding
}

/**
 * History Action Interface
 *
 * Represents an executable action in the history, combining data and operations.
 */
export interface HistoryAction extends HistoryEntry {
  /** Executes the undo operation for this action. */
  undo: () => Promise<void>
  /** Executes the redo operation for this action. */
  redo: () => Promise<void>
}

/**
 * History State Interface
 *
 * Represents the state of the history service.
 */
export interface HistoryState {
  /** Current history entries (as data, not executable actions) */
  entries: HistoryEntry[]
  /** Current position in the history stack (index of the last action in undo stack) */
  current: number
  /** Maximum number of history entries */
  maxEntries: number
  /** Whether undo is available */
  canUndo: boolean
  /** Whether redo is available */
  canRedo: boolean
  /** Last action timestamp */
  lastActionTimestamp: number
  /** History grouping identifier */
  batchId?: string
}

/**
 * History Service Interface
 *
 * Defines the API for the history service.
 */
export interface HistoryService {
  /**
   * Add an entry to the history.
   * The input should contain all necessary data for HistoryEntry
   * and the undo/redo functions.
   */
  addEntry: (entryData: Omit<HistoryAction, 'id' | 'timestamp'>) => Promise<ServiceResult<HistoryEntry>>

  /** Undo the last action */
  undo: () => Promise<ServiceResult<HistoryEntry>>

  /** Redo the previously undone action */
  redo: () => Promise<ServiceResult<HistoryEntry>>

  /** Clear the history */
  clear: () => ServiceResult<void> // Assuming clear is synchronous, adjust if it becomes async

  /** Get the current history state */
  getState: () => HistoryState

  /** Start a batch of operations */
  startBatch: (batchId?: string) => void

  /** End a batch of operations */
  endBatch: () => void

  /** Check if undo is available */
  canUndo: () => boolean

  /** Check if redo is available */
  canRedo: () => boolean
}
