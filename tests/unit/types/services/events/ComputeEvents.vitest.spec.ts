import type {
  ComputeCompleteEvent,
  ComputeErrorEvent,
  ComputeProgressEvent,
  ComputeRequestEvent,
  ComputeTransformEvent,
  ValidateShapeEvent,
} from '@/types/services/events/computeEvents'
import { describe, expect, it } from 'vitest'
import { ComputeOperation } from '@/types/core/element/compute'
import { AppEventType } from '@/types/services/events/eventTypes'

describe('computeEvents', () => {
  describe('computeRequestEvent', () => {
    it('should have correct structure', () => {
      const event: ComputeRequestEvent = {
        type: AppEventType.COMPUTE_REQUEST,
        payload: {
          operation: ComputeOperation.AREA,
          shapeIds: ['shape-123', 'shape-456'],
          computeConfig: {
            precision: 2,
          },
        },
      }

      expect(event.type).toBe(AppEventType.COMPUTE_REQUEST)
      expect(event.payload.operation).toBe(ComputeOperation.AREA)
      expect(event.payload.shapeIds).toEqual(['shape-123', 'shape-456'])
      expect(event.payload.computeConfig).toEqual({ precision: 2 })
    })

    it('should handle single shape ID', () => {
      const event: ComputeRequestEvent = {
        type: AppEventType.COMPUTE_REQUEST,
        payload: {
          operation: ComputeOperation.PERIMETER,
          shapeIds: 'shape-123',
        },
      }

      expect(event.type).toBe(AppEventType.COMPUTE_REQUEST)
      expect(event.payload.operation).toBe(ComputeOperation.PERIMETER)
      expect(event.payload.shapeIds).toBe('shape-123')
      expect(event.payload.computeConfig).toBeUndefined()
    })

    it('should handle different compute operations', () => {
      const event: ComputeRequestEvent = {
        type: AppEventType.COMPUTE_REQUEST,
        payload: {
          operation: ComputeOperation.INTERSECTION,
          shapeIds: ['shape-123', 'shape-456'],
        },
      }

      expect(event.type).toBe(AppEventType.COMPUTE_REQUEST)
      expect(event.payload.operation).toBe(ComputeOperation.INTERSECTION)
    })
  })

  describe('computeProgressEvent', () => {
    it('should have correct structure', () => {
      const event: ComputeProgressEvent = {
        type: AppEventType.COMPUTE_PROGRESS,
        payload: {
          computeId: 'compute-123',
          progress: 0.5,
          status: 'processing',
          metadata: {
            timeElapsed: 500,
            estimatedTimeRemaining: 500,
          },
        },
      }

      expect(event.type).toBe(AppEventType.COMPUTE_PROGRESS)
      expect(event.payload.computeId).toBe('compute-123')
      expect(event.payload.progress).toBe(0.5)
      expect(event.payload.status).toBe('processing')
      expect(event.payload.metadata).toEqual({
        timeElapsed: 500,
        estimatedTimeRemaining: 500,
      })
    })

    it('should handle minimal payload', () => {
      const event: ComputeProgressEvent = {
        type: AppEventType.COMPUTE_PROGRESS,
        payload: {
          computeId: 'compute-123',
          progress: 0.75,
        },
      }

      expect(event.type).toBe(AppEventType.COMPUTE_PROGRESS)
      expect(event.payload.computeId).toBe('compute-123')
      expect(event.payload.progress).toBe(0.75)
      expect(event.payload.status).toBeUndefined()
      expect(event.payload.metadata).toBeUndefined()
    })
  })

  describe('computeCompleteEvent', () => {
    it('should have correct structure', () => {
      const event: ComputeCompleteEvent = {
        type: AppEventType.COMPUTE_COMPLETE,
        payload: {
          computeId: 'compute-123',
          result: {
            area: 15000,
            perimeter: 500,
          },
          metadata: {
            timeElapsed: 1200,
            operation: ComputeOperation.AREA,
          },
        },
      }

      expect(event.type).toBe(AppEventType.COMPUTE_COMPLETE)
      expect(event.payload.computeId).toBe('compute-123')
      expect(event.payload.result).toEqual({
        area: 15000,
        perimeter: 500,
      })
      expect(event.payload.metadata).toEqual({
        timeElapsed: 1200,
        operation: ComputeOperation.AREA,
      })
    })

    it('should handle minimal payload', () => {
      const event: ComputeCompleteEvent = {
        type: AppEventType.COMPUTE_COMPLETE,
        payload: {
          computeId: 'compute-123',
          result: {
            intersectionPoints: [
              { x: 100, y: 100 },
              { x: 200, y: 200 },
            ],
          },
        },
      }

      expect(event.type).toBe(AppEventType.COMPUTE_COMPLETE)
      expect(event.payload.computeId).toBe('compute-123')
      expect(event.payload.result).toEqual({
        intersectionPoints: [
          { x: 100, y: 100 },
          { x: 200, y: 200 },
        ],
      })
      expect(event.payload.metadata).toBeUndefined()
    })
  })

  describe('computeErrorEvent', () => {
    it('should have correct structure', () => {
      const error = new Error('Computation failed')
      const event: ComputeErrorEvent = {
        type: AppEventType.COMPUTE_ERROR,
        payload: {
          computeId: 'compute-123',
          error,
          metadata: {
            operation: ComputeOperation.INTERSECTION,
            shapeIds: ['shape-123', 'shape-456'],
          },
        },
      }

      expect(event.type).toBe(AppEventType.COMPUTE_ERROR)
      expect(event.payload.computeId).toBe('compute-123')
      expect(event.payload.error).toBe(error)
      expect(event.payload.metadata).toEqual({
        operation: ComputeOperation.INTERSECTION,
        shapeIds: ['shape-123', 'shape-456'],
      })
    })

    it('should handle minimal payload', () => {
      const error = new Error('Computation failed')
      const event: ComputeErrorEvent = {
        type: AppEventType.COMPUTE_ERROR,
        payload: {
          computeId: 'compute-123',
          error,
        },
      }

      expect(event.type).toBe(AppEventType.COMPUTE_ERROR)
      expect(event.payload.computeId).toBe('compute-123')
      expect(event.payload.error).toBe(error)
      expect(event.payload.metadata).toBeUndefined()
    })
  })

  describe('computeTransformEvent', () => {
    it('should have correct structure', () => {
      const event: ComputeTransformEvent = {
        type: AppEventType.COMPUTE_TRANSFORM,
        payload: {
          shapeIds: ['shape-123', 'shape-456'],
          transformOptions: {
            rotation: 45,
            scale: { x: 1.5, y: 1.5 },
            translation: { x: 10, y: 20 },
          },
        },
      }

      expect(event.type).toBe(AppEventType.COMPUTE_TRANSFORM)
      expect(event.payload.shapeIds).toEqual(['shape-123', 'shape-456'])
      expect(event.payload.transformOptions).toEqual({
        rotation: 45,
        scale: { x: 1.5, y: 1.5 },
        translation: { x: 10, y: 20 },
      })
    })

    it('should handle single shape ID', () => {
      const event: ComputeTransformEvent = {
        type: AppEventType.COMPUTE_TRANSFORM,
        payload: {
          shapeIds: 'shape-123',
          transformOptions: {
            rotation: 90,
          },
        },
      }

      expect(event.type).toBe(AppEventType.COMPUTE_TRANSFORM)
      expect(event.payload.shapeIds).toBe('shape-123')
      expect(event.payload.transformOptions).toEqual({
        rotation: 90,
      })
    })

    it('should handle partial transform options', () => {
      const event: ComputeTransformEvent = {
        type: AppEventType.COMPUTE_TRANSFORM,
        payload: {
          shapeIds: ['shape-123', 'shape-456'],
          transformOptions: {
            translation: { x: 10, y: 20 },
          },
        },
      }

      expect(event.type).toBe(AppEventType.COMPUTE_TRANSFORM)
      expect(event.payload.transformOptions).toEqual({
        translation: { x: 10, y: 20 },
      })
    })
  })

  describe('validateShapeEvent', () => {
    it('should have correct structure', () => {
      const event: ValidateShapeEvent = {
        type: AppEventType.VALIDATE_SHAPE,
        payload: {
          shapeId: 'shape-123',
          validationRules: ['dimensions', 'position'],
          validationContext: {
            strictMode: true,
          },
        },
      }

      expect(event.type).toBe(AppEventType.VALIDATE_SHAPE)
      expect(event.payload.shapeId).toBe('shape-123')
      expect(event.payload.validationRules).toEqual(['dimensions', 'position'])
      expect(event.payload.validationContext).toEqual({
        strictMode: true,
      })
    })

    it('should handle minimal payload', () => {
      const event: ValidateShapeEvent = {
        type: AppEventType.VALIDATE_SHAPE,
        payload: {
          shapeId: 'shape-123',
        },
      }

      expect(event.type).toBe(AppEventType.VALIDATE_SHAPE)
      expect(event.payload.shapeId).toBe('shape-123')
      expect(event.payload.validationRules).toBeUndefined()
      expect(event.payload.validationContext).toBeUndefined()
    })
  })
})
