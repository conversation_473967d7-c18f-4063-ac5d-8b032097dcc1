/**
 * Creator for Room Design Elements
 *
 * @remarks
 * This class extends {@link DesignCreator} to specialize in creating room elements
 * ({@link CoreElementType.ROOM}). Rooms are fundamental a_spatial units in interior design,
 * defined by a boundary (typically a polygon) and various properties like room type,
 * floor/wall/ceiling materials, and associated elements (walls, openings, furniture).
 *
 * The `createDefault` method initializes a room with default properties based on its
 * `roomType` (e.g., {@link RoomType.LIVING_ROOM}, {@link RoomType.BEDROOM}), including
 * default dimensions (if no boundary points are provided) and materials.
 *
 * The `createRoomWithBoundary` method allows creating a room with a specific polygonal
 * boundary, automatically calculating its centroid for positioning, as well as its
 * area and perimeter (though the units for these calculations are currently assumed
 * and might need standardization).
 *
 * Helper methods are included for generating default room names and calculating
 * centroids, areas, and perimeters from a set of points. These geometric calculations
 * might be better suited for dedicated utility modules.
 *
 * Note: Placeholders for `DesignMaterial` and `DesignElementCategory` are used locally.
 *
 * @module core/factory/creators/design/RoomCreator
 * @see {@link DesignCreator}
 * @see {@link CoreElementType.ROOM}
 * @see {@link RoomType}
 * @see {@link RoomProperties}
 */

import type { ShapeCreationParamsUnion } from '@/core/factory/ElementFactory'
import type { RoomProperties } from '@/types/core/element/design/roomDesignTypes'
import type { PointData } from '@/types/core/element/geometry/point' // Changed to PointData
import type {
  ShapeElement,
} from '@/types/core/elementDefinitions'
import type { MinorCategory } from '@/types/core/majorMinorTypes'
import { ensureCompleteMetadata } from '@/lib/utils/element/elementUtils'
import { RoomType } from '@/types/core/element/design/roomDesignTypes'
import {
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'
import { DesignCreator } from './DesignCreator'
// Point class for centroid calculation is implemented directly

/**
 * Placeholder type for design material.
 * @private
 * @remarks TODO: Define or import a proper DesignMaterial type/enum.
 */
type DesignMaterial = string

/**
 * Placeholder for DesignElementCategory. The base DesignCreator expects this.
 * For this RoomCreator, the category is 'ROOM'.
 * @private
 * @remarks TODO: Ideally, this would be an enum shared across design creators.
 */
const DesignElementCategory = {
  ROOM: 'ROOM',
  // Other categories like WALL, OPENING, FURNITURE, FIXTURE would be part of this enum.
} as const

/**
 * Creator class for instantiating Room elements.
 * It extends the base {@link DesignCreator}.
 *
 * @extends DesignCreator
 */
export class RoomCreator extends DesignCreator {
  /**
   * The category of design element this creator produces.
   * @protected
   * @readonly
   */
  protected readonly category = DesignElementCategory.ROOM

  /**
   * The specific element type this creator produces.
   * @protected
   * @readonly
   */
  protected readonly elementType = CoreElementType.ROOM

  /**
   * Creates a Room element with default properties based on its type.
   * This method overrides the base `createDefault` from {@link DesignCreator}.
   *
   * @param id - The unique identifier for the new room element.
   * @param position - The {@link PointData} representing the room's nominal position (often its centroid or a reference point).
   * @param majorCategoryOverride - Optional major category override.
   * @param _minorCategoryOverride - Optional minor category override (unused).
   * @param zLevelIdOverride - Optional z-level ID override.
   * @param isFixedCategoryOverride - Optional fixed category flag override.
   * @returns A Promise resolving to the created {@link ShapeElement} representing the room.
   * @override
   */
  async createDefault(
    id: string,
    position: PointData,
    majorCategoryOverride?: MajorCategory,
    _minorCategoryOverride?: MinorCategory,
    zLevelIdOverride?: string,
    isFixedCategoryOverride?: boolean,
  ): Promise<ShapeElement> {
    // For backward compatibility, handle old-style params if passed as majorCategoryOverride
    const params = majorCategoryOverride as unknown as {
      roomType?: RoomType
      fill?: string
      stroke?: string
      strokeWidth?: number
      opacity?: number
      properties?: {
        roomType?: RoomType
        points?: PointData[]
      }
    }

    // Use proper MajorCategory and MinorCategory for super.createDefault
    const roomMajorCategory = MajorCategory.FURNITURE // Use FURNITURE as fallback since ROOM doesn't exist
    const roomMinorCategory = 'living' as MinorCategory // Default to 'living', can be overridden

    const baseElement = await super.createDefault(id, position, roomMajorCategory, roomMinorCategory, zLevelIdOverride, isFixedCategoryOverride)
    const p = params // For easier access to optional properties

    const roomType = p?.properties?.roomType
      ?? p?.roomType // Fallback if passed at top level
      ?? RoomType.LIVING_ROOM

    const isWetArea = [RoomType.BATHROOM, RoomType.KITCHEN, RoomType.LAUNDRY].includes(roomType)
    let floorMaterial: DesignMaterial = 'hardwood'
    if (isWetArea)
      floorMaterial = 'tile'
    else if (roomType === RoomType.BEDROOM)
      floorMaterial = 'carpet'

    const ceilingHeight = roomType === RoomType.LIVING_ROOM ? 2700 : 2400

    // Room-specific properties
    const roomProperties: Partial<RoomProperties> = {
      roomType,
      name: this.getRoomDefaultName(roomType),
      floorLevel: 0,
      ceilingHeight,
      floorMaterial,
      wallMaterial: 'drywall' as DesignMaterial,
      ceilingMaterial: 'drywall' as DesignMaterial,
      wallIds: [],
      openingIds: [],
      furnitureIds: [],
      fixtureIds: [],
      isOutdoor: roomType === RoomType.BALCONY || roomType === RoomType.PATIO,
      // Area and perimeter will be calculated if boundary points are provided
    }

    baseElement.properties = {
      ...baseElement.properties, // Keep properties from DesignCreator
      ...roomProperties, // Add/override with room specific properties
      // Default visual representation for a room (e.g., a large rectangle)
      // This might be overridden by createRoomWithBoundary or if params provide points
      type: 'polygon', // Rooms are often polygons
      points: p?.properties?.points ?? [ // Default points if not provided
        { x: position.x - 2000, y: position.y - 1500 },
        { x: position.x + 2000, y: position.y - 1500 },
        { x: position.x + 2000, y: position.y + 1500 },
        { x: position.x - 2000, y: position.y + 1500 },
      ],
      fill: p?.fill ?? '#F5F5F5',
      stroke: p?.stroke ?? '#CCCCCC',
      strokeWidth: p?.strokeWidth ?? 1,
      opacity: p?.opacity ?? 0.5,
    }

    // Metadata should be general, specific functional props go into `properties`
    baseElement.metadata = ensureCompleteMetadata({
      ...baseElement.metadata, // Keep metadata from DesignCreator
      name: roomProperties.name, // Ensure name is set from roomProperties
      // Add any other room-specific metadata if necessary
    })

    baseElement.type = CoreElementType.ROOM // Ensure correct final type

    return baseElement
  }

  /**
   * Creates a Room element defined by a specific boundary (array of points).
   * Calculates the centroid for positioning and computes area/perimeter.
   *
   * @param id - The unique identifier for the new room element.
   * @param roomType - The {@link RoomType} of the room to create.
   * @param boundary - An array of {@link PointData} objects defining the room's boundary vertices.
   * @param params - Optional {@link ShapeCreationParamsUnion} for additional properties.
   * @returns A Promise resolving to the created {@link ShapeElement} representing the room.
   */
  async createRoomWithBoundary(id: string, roomType: RoomType, boundary: PointData[], params?: ShapeCreationParamsUnion): Promise<ShapeElement> { // Changed Point to PointData
    const centroid = this.calculateCentroid(boundary)
    // Pass boundary points via params to createDefault
    const roomParams = {
      ...(params || {}),
      properties: {
        ...(params && 'properties' in params && params.properties ? params.properties : {}),
        points: boundary,
        roomType, // Ensure roomType is passed to createDefault's logic
      },
    } as ShapeCreationParamsUnion

    // Convert roomParams to MajorCategory for createDefault
    const room = await this.createDefault(id, centroid, roomParams as unknown as MajorCategory)

    const area = this.calculateArea(boundary)
    const perimeter = this.calculatePerimeter(boundary)

    if (room.properties) {
      room.properties.area = area
      room.properties.perimeter = perimeter
    }
    else {
      room.properties = { area, perimeter }
    }

    // Update metadata if area/perimeter are also stored there (though properties is better)
    // room.metadata = ensureCompleteMetadata({ ...room.metadata, area, perimeter });

    return room
  }

  /**
   * Gets a default name for a room based on its type.
   *
   * @param roomType - The type of room.
   * @returns A formatted name string.
   * @private
   */
  private getRoomDefaultName(roomType: RoomType): string {
    const name = roomType.toString().replace(/_/g, ' ').toLowerCase()
    return name.charAt(0).toUpperCase() + name.slice(1)
  }

  /**
   * Calculates the centroid (geometric center) of a polygon defined by an array of points.
   *
   * @param points - An array of {@link PointData} objects representing the vertices of the polygon.
   * @returns A {@link PointData} object representing the centroid. Returns `{x:0, y:0}` if no points are provided.
   * @private
   * @remarks TODO: This geometric utility should ideally be moved to a `geometryUtils` module.
   */
  private calculateCentroid(points: PointData[]): PointData { // Changed Point to PointData
    if (points == null || points.length === 0)
      return { x: 0, y: 0 }
    let sumX = 0
    let sumY = 0
    for (const point of points) {
      sumX += point.x
      sumY += point.y
    }
    return { x: sumX / points.length, y: sumY / points.length }
  }

  /**
   * Calculates the area of a polygon defined by an array of points using the shoelace formula.
   *
   * @param points - An array of {@link PointData} objects representing the vertices of the polygon.
   * @returns The area of the polygon. Assumes input points are in millimeters and returns area in square meters.
   * @private
   * @remarks TODO: This geometric utility (and its unit conversion assumption) should ideally be moved to a `geometryUtils` module.
   */
  private calculateArea(points: PointData[]): number { // Changed Point to PointData
    if (points == null || points.length < 3)
      return 0
    let area = 0
    const n = points.length
    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n
      area += points[i].x * points[j].y
      area -= points[j].x * points[i].y
    }
    return Math.abs(area) / 2 / 1000000 // Assuming points are in mm, convert to m^2. This assumption should be documented or handled more robustly.
  }

  /**
   * Calculates the perimeter of a polygon defined by an array of points.
   *
   * @param points - An array of {@link PointData} objects representing the vertices of the polygon.
   * @returns The perimeter of the polygon. Assumes input points are in millimeters and returns perimeter in meters.
   * @private
   * @remarks TODO: This geometric utility (and its unit conversion assumption) should ideally be moved to a `geometryUtils` module.
   */
  private calculatePerimeter(points: PointData[]): number { // Changed Point to PointData
    if (points == null || points.length < 2)
      return 0
    let perimeter = 0
    const n = points.length
    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n // For the last segment, j will be 0, connecting back to start if closed
      const p1 = points[i]
      const p2 = points[j]
      const dx = p2.x - p1.x
      const dy = p2.y - p1.y
      perimeter += Math.sqrt(dx * dx + dy * dy)
    }
    // If it's an open polyline representing a room boundary that isn't closed by this calculation,
    // this will be the length of the segments. If it's meant to be a closed polygon,
    // the last point should ideally be the same as the first, or this loop handles it.
    return perimeter / 1000 // Assuming points are in mm, convert to m
  }
}
