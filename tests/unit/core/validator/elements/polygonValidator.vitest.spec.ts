import type { ValidatorShape } from '@/types/core/element/validator'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { PolygonValidator } from '@/core/validator/elements/polygonValidator'
import { ElementType } from '@/types/core/shape-type'

describe('polygonValidator', () => {
  let validator: PolygonValidator

  beforeEach(() => {
    validator = new PolygonValidator()
  })

  describe('validateSpecific', () => {
    it('should return error for invalid shape type', () => {
      const invalidPolygon: ValidatorShape = {
        id: 'polygon-1',
        type: ElementType.CIRCLE,
        position: { x: 0, y: 0 },
        radius: 50,
      }

      const errors = (validator as any).validateSpecific(invalidPolygon)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_SHAPE_TYPE')
    })
    it('should validate a valid polygon', () => {
      // For a valid polygon, we need to make sure it's closed
      const validPolygon: ValidatorShape = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 50, y: 100 },
          { x: 0, y: 0 }, // Closing point
        ],
      }

      const errors = (validator as any).validateSpecific(validPolygon)
      expect(errors).toEqual([])
    })

    it('should return error for missing points', () => {
      const invalidPolygon: ValidatorShape = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
      }

      const errors = (validator as any).validateSpecific(invalidPolygon)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('MISSING_POLYGON_DEFINITION')
    })

    it('should return error for invalid points (not an array)', () => {
      const invalidPolygon: any = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: 'triangle',
      }

      const errors = (validator as any).validateSpecific(invalidPolygon)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('MISSING_POLYGON_DEFINITION')
    })

    it('should return error for too few points', () => {
      const invalidPolygon: ValidatorShape = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
        ],
      }

      const errors = (validator as any).validateSpecific(invalidPolygon)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INSUFFICIENT_POINTS')
    })

    it('should return error for invalid point in array', () => {
      const invalidPolygon: any = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          'invalid',
          { x: 50, y: 100 },
        ],
      }

      const errors = (validator as any).validateSpecific(invalidPolygon)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_POINT')
    })

    it('should return error for point missing x coordinate', () => {
      const invalidPolygon: any = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          { y: 0 },
          { x: 50, y: 100 },
        ],
      }

      const errors = (validator as any).validateSpecific(invalidPolygon)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_POINT')
    })

    it('should return error for point missing y coordinate', () => {
      const invalidPolygon: any = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          { x: 100 },
          { x: 50, y: 100 },
        ],
      }

      const errors = (validator as any).validateSpecific(invalidPolygon)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_POINT')
    })

    it('should return error for non-numeric point coordinates', () => {
      const invalidPolygon: any = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          { x: '100', y: 0 },
          { x: 50, y: 100 },
        ],
      }

      const errors = (validator as any).validateSpecific(invalidPolygon)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_POINT')
    })

    it('should return error for duplicate consecutive points', () => {
      const invalidPolygon: ValidatorShape = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 100, y: 0 }, // Duplicate
          { x: 50, y: 100 },
          { x: 0, y: 0 }, // Closing point
        ],
      }

      const errors = (validator as any).validateSpecific(invalidPolygon)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('DUPLICATE_POINTS')
    })

    it('should return error for unclosed polygon', () => {
      const invalidPolygon: ValidatorShape = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 50, y: 100 },
          // Missing closing point
        ],
      }

      const errors = (validator as any).validateSpecific(invalidPolygon)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('POLYGON_NOT_CLOSED')
    })

    it('should validate a polygon defined by sides', () => {
      const validPolygon: ValidatorShape = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        sides: 6,
        radius: 50,
      }

      const errors = (validator as any).validateSpecific(validPolygon)
      expect(errors).toEqual([])
    })

    it('should return error for invalid sides value', () => {
      const invalidPolygon: ValidatorShape = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        sides: 2, // Less than 3
      }

      const errors = (validator as any).validateSpecific(invalidPolygon)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('INVALID_SIDES')
    })
  })

  describe('applySpecificRules', () => {
    it('should apply specific rules for polygons', () => {
      // This is a protected method, so we need to cast to any
      const applySpecificRulesSpy = vi.spyOn(validator as any, 'applySpecificRules')

      const validPolygon: ValidatorShape = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 50, y: 100 },
          { x: 0, y: 0 }, // Closing point
        ],
      }

      validator.validate(validPolygon)
      expect(applySpecificRulesSpy).toHaveBeenCalled()
    })

    it('should return error for polygon with too many sides', () => {
      const complexPolygon: ValidatorShape = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        sides: 20, // More than the max of 12
      }

      const errors = (validator as any).applySpecificRules(complexPolygon)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors[0].code).toBe('POLYGON_TOO_COMPLEX')
    })

    it('should handle triangle type correctly', () => {
      const triangle: ValidatorShape = {
        id: 'triangle-1',
        type: ElementType.TRIANGLE,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 50, y: 100 },
          { x: 0, y: 0 }, // Closing point
        ],
      }

      const errors = (validator as any).applySpecificRules(triangle)
      expect(errors).toEqual([])
    })

    it('should handle hexagon type correctly', () => {
      const hexagon: ValidatorShape = {
        id: 'hexagon-1',
        type: ElementType.HEXAGON,
        position: { x: 0, y: 0 },
        sides: 6,
      }

      const errors = (validator as any).applySpecificRules(hexagon)
      expect(errors).toEqual([])
    })
  })

  describe('validate', () => {
    it('should call validateSpecific', () => {
      const validPolygon: ValidatorShape = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 50, y: 100 },
          { x: 0, y: 0 }, // Closing point
        ],
      }

      const validateSpecificSpy = vi.spyOn(validator as any, 'validateSpecific').mockReturnValue([])

      const result = validator.validate(validPolygon)

      expect(validateSpecificSpy).toHaveBeenCalledWith(validPolygon)
      expect(result.valid).toBe(true)
      expect(result.errors).toEqual([])

      validateSpecificSpy.mockRestore()
    })

    it('should return invalid result when errors are found', () => {
      const invalidPolygon: ValidatorShape = {
        id: 'polygon-1',
        type: ElementType.POLYGON,
        position: { x: 0, y: 0 },
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          // Not enough points
        ],
      }

      const result = validator.validate(invalidPolygon)

      expect(result.valid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
      expect(result.errors.some(error => error.code === 'INSUFFICIENT_POINTS')).toBe(true)
    })
  })
})
