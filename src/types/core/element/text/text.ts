/**
 * Provides centralized exports for text-related type definitions.
 *
 * @remarks
 * This module serves as the barrel file for text element types,
 * re-exporting interfaces and types related to text rendering, styling, and layout.
 *
 * @module types/core/element/text
 * @see {@link Text} for the main text element interface.
 * @see {@link TextStyle} for text styling properties.
 * @see {@link TextLayout} for text layout configuration.
 *
 * @example
 * ```typescript
 * import { Text, TextStyle } from '@/types/core/element/text'; // Assuming path alias
 * ```
 */

/** Re-exports all types from the `./text` module. */
export * from './textElementTypes'
