/**
 * Element Creation Factory
 *
 * @remarks
 * This factory class is responsible for instantiating various types of elements,
 * including geometric shapes (rectangles, ellipses, polygons, etc.) and paths
 * (lines, arcs, Bezier curves). It utilizes a registry of creator classes,
 * each specialized in constructing a specific element type.
 *
 * The factory provides methods like `createShape` and `createPath` that take
 * element type and creation parameters, then delegate to the appropriate registered
 * creator. It also includes helper methods for normalizing input parameters (e.g., position)
 * and ensuring metadata completeness.
 *
 * Default creators for standard element types are registered upon instantiation.
 * Custom creators can also be registered for extending the factory's capabilities.
 *
 * The factory defines various `Create...Params` interfaces for type-safe parameter
 * passing to creators and `User...InputParams` types for more flexible user-facing APIs.
 *
 * @module core/factory/ElementFactory
 * @see {@link IElementFactory}
 * @see {@link ShapeCreator}
 * @see {@link PathCreator}
 * @see {@link ShapeCreationParamsUnion}
 * @see {@link PathCreationOptionsUnion}
 */

import type { PathCreator } from './creators/path/PathCreator'
import type { PointData } from '@/types/core/element/geometry/point' // Changed import
import type {
  BaseStyleProperties,
  Element,
  MetadataProperties,
  ShapeElement as ShapeModel,
} from '@/types/core/elementDefinitions'
import type { MajorCategory, MinorCategory } from '@/types/core/majorMinorTypes' // Added import
import { ensureCompleteMetadata } from '@/lib/utils/element/elementUtils'
// import { PointClass } from '@/lib/utils/geometry'; // PointClass is not directly used as a value
import { CoreError, ErrorType } from '@/services/system/error-service' // Added import

import {
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'
import * as creators from './creators'
import ImageCreator from './creators/media/ImageCreator' // Default import from media directory
import { TextCreator } from './creators/media/TextCreator' // Named import from media directory
import { ShapeCreator } from './creators/shape/ShapeCreator'

/**
 * Base parameters for creating any element.
 * Includes common properties like ID, type, metadata, visibility, styling, etc.
 */
export interface BaseElementCreationParams extends BaseStyleProperties {
  id: string
  type: CoreElementType | string // Allow string for flexibility with custom types
  majorCategory?: MajorCategory
  minorCategory?: MinorCategory
  zLevelId?: string
  isFixedCategory?: boolean
  metadata?: Partial<MetadataProperties>
  visible?: boolean
  locked?: boolean
  rotation?: number
  selectable?: boolean
  draggable?: boolean
  showHandles?: boolean
  layer?: string
  zIndex?: number
  properties?: Record<string, unknown>
}

/** Parameters for creating Rectangle or Square elements. */
export interface CreateRectangleParams extends BaseElementCreationParams {
  type: CoreElementType.RECTANGLE | CoreElementType.SQUARE | 'rectangle' | 'square'
  position: PointData // Changed type
  width: number
  height: number
  cornerRadius?: number
}

/** Parameters for creating Ellipse elements. */
export interface CreateEllipseParams extends BaseElementCreationParams {
  type: CoreElementType.ELLIPSE | 'ellipse'
  position: PointData // Changed type
  radiusX: number
  radiusY: number
}

/** Parameters for creating Circle elements. */
export interface CreateCircleParams extends BaseElementCreationParams {
  type: CoreElementType.CIRCLE | 'circle'
  position: PointData // Changed type
  radius: number
}

/** Parameters for creating Polygon-like elements (Polygon, Triangle, Hexagon, etc.). */
export interface CreatePolygonParams extends BaseElementCreationParams {
  type: CoreElementType.POLYGON | CoreElementType.TRIANGLE | CoreElementType.HEXAGON | CoreElementType.QUADRILATERAL | CoreElementType.PENTAGON | 'polygon' | 'triangle' | 'hexagon' | 'quadrilateral' | 'pentagon' // Allow specific polygon types
  points: PointData[] // Changed type
  sides?: number // For regular polygons created by sides/radius
  radius?: number // For regular polygons
  center?: PointData // Changed type, for regular polygons
  position?: PointData // Added: Position for the polygon (used for mouse drop position)
  isRegular?: boolean // Added: Indicates if the polygon should be regular
  startAngleRad?: number // Added: Optional starting angle in radians for the first vertex of a regular polygon
}

/** Parameters for creating Line elements. */
export interface CreateLineParams extends BaseElementCreationParams {
  type: CoreElementType.LINE | 'line'
  start: PointData // Changed type
  end: PointData // Changed type
  arrowStart?: boolean
  arrowEnd?: boolean
}

/** Parameters for creating Polyline elements. */
export interface CreatePolylineParams extends BaseElementCreationParams {
  type: CoreElementType.POLYLINE | 'polyline'
  points: PointData[] // Changed type
  curved?: boolean // If the polyline should be rendered with curves (e.g., Catmull-Rom)
  tension?: number // Tension for curved polylines
}

/** Type alias for creating an element directly from an existing ShapeModel. */
export type CreateElementFromModelParams = ShapeModel

/** Parameters for creating Arc elements. */
export interface CreateArcParams extends BaseElementCreationParams {
  type: CoreElementType.ARC | 'arc'
  position: PointData // Changed type (center of the arc)
  radius: number
  startAngle: number // In degrees
  endAngle: number // In degrees
  closed?: boolean // Whether the arc forms a sector
  counterClockwise?: boolean // Direction of drawing. Defaults to false (clockwise).
}

/** Parameters for creating Quadratic Bezier Curve elements. */
export interface CreateQuadraticParams extends BaseElementCreationParams {
  type: CoreElementType.QUADRATIC | 'quadratic'
  start: PointData // Changed type
  control: PointData // Changed type
  end: PointData // Changed type
}

/** Parameters for creating Cubic Bezier Curve elements. */
export interface CreateCubicParams extends BaseElementCreationParams {
  type: CoreElementType.CUBIC | 'cubic'
  start: PointData // Changed type
  control1: PointData // Changed type
  control2: PointData // Changed type
  end: PointData // Changed type
}

/** Parameters for creating Text elements. */
export interface CreateTextParams extends BaseElementCreationParams {
  type: CoreElementType.TEXT | 'text'
  position: PointData // Position of the text (e.g., top-left, baseline start)
  // Text-specific properties are now direct optional properties:
  text?: string
  fontSize?: number
  fontFamily?: string
  fontWeight?: string | number // Keep flexible as per defaultElementSettings
  fontStyle?: string
  textAlign?: CanvasTextAlign
  textBaseline?: CanvasTextBaseline
  lineHeight?: number
  fill?: string // Text color can be distinct
  // properties?: Record<string, any>; // Retain for any other custom/nested properties if needed
}

/** Parameters for creating Image elements. */
export interface CreateImageParams extends BaseElementCreationParams {
  type: CoreElementType.IMAGE | 'image'
  position: PointData // Position of the image (e.g., top-left corner)
  // Specific image properties are now direct optional properties:
  src?: string
  width?: number
  height?: number
  alt?: string
  sourceType?: 'url' | 'svg_inline_data'
  // properties?: Record<string, any>; // Retain for any other custom/nested properties if needed
}

/** Union type for all possible shape creation parameter objects. */
export type ShapeCreationParamsUnion =
  | CreateRectangleParams | CreateEllipseParams | CreateCircleParams | CreatePolygonParams
  | CreateLineParams | CreatePolylineParams | CreateArcParams
  | CreateQuadraticParams | CreateCubicParams | CreateElementFromModelParams
  | CreateTextParams | CreateImageParams

/** Union type for all possible path creation parameter objects. */
export type PathCreationOptionsUnion =
  | CreateArcParams | CreateQuadraticParams | CreateCubicParams
  | CreateLineParams | CreatePolylineParams

/** Common user input parameters for creating elements, allowing partial metadata and base style/shape properties. */
type CommonUserInputParams = {
  majorCategory: MajorCategory // Added: majorCategory is now required
  minorCategory?: MinorCategory // Added: optional minorCategory
  zLevelId?: string // Added: optional zLevelId
  isFixedCategory?: boolean // Added: optional isFixedCategory
  metadata?: Partial<MetadataProperties>
} & Partial<BaseStyleProperties> & Partial<Pick<ShapeModel, 'visible' | 'locked' | 'rotation' | 'selectable' | 'draggable' | 'showHandles' | 'layer' | 'zIndex' | 'properties'>>

/** User input parameters for creating a Rectangle. Position can be a PointData object or a coordinate array. */
export type UserRectInputParams = CommonUserInputParams &
  { width: number, height: number, position?: PointData | [number, number, number?], cornerRadius?: number }

/** User input parameters for creating a Square. Position can be a PointData object or a coordinate array. */
export type UserSquareInputParams = CommonUserInputParams &
  { size: number, position?: PointData | [number, number, number?], cornerRadius?: number }

/** User input parameters for creating an Ellipse. Position can be a PointData object or a coordinate array. */
export type UserEllipseInputParams = CommonUserInputParams &
  { radiusX: number, radiusY: number, position?: PointData | [number, number, number?] }

/** User input parameters for creating a Circle. Position can be a PointData object or a coordinate array. */
export type UserCircleInputParams = CommonUserInputParams &
  { radius: number, position?: PointData | [number, number, number?] }

/** User input parameters for creating a Line. Start/end points can be PointData objects or coordinate arrays. */
export type UserLineInputParams = CommonUserInputParams &
  { start: PointData | [number, number, number?], end: PointData | [number, number, number?], arrowStart?: boolean, arrowEnd?: boolean }

/** User input parameters for creating a Polyline. Points can be PointData objects or coordinate arrays. */
export type UserPolylineInputParams = CommonUserInputParams &
  { points: (PointData | [number, number, number?])[], curved?: boolean, tension?: number }

/** User input parameters for creating a regular Polygon (defined by sides and radius). Center can be a PointData object or a coordinate array. */
export type UserRegularPolygonInputParams = CommonUserInputParams &
  { sides: number, radius: number, center?: PointData | [number, number, number?] }

/** User input parameters for creating a custom Polygon (defined by an array of points). Points can be PointData objects or coordinate arrays. */
export type UserCustomPolygonInputParams = CommonUserInputParams &
  { points: (PointData | [number, number, number?])[] }

/** User input parameters for creating an Arc. Position can be a PointData object or a coordinate array. */
export type UserArcInputParams = CommonUserInputParams &
  { radius: number, startAngle: number, endAngle: number, position?: PointData | [number, number, number?], closed?: boolean }

/**
 * Defines the contract for an element factory.
 */
export interface IElementFactory {
  /**
   * Creates a shape element of the specified type with the given parameters.
   * @param type - The {@link CoreElementType} or string identifier of the shape to create.
   * @param params - A {@link ShapeCreationParamsUnion} object containing creation parameters.
   * @returns A Promise resolving to the created {@link ShapeModel}.
   */
  createShape: (type: CoreElementType | string, params: ShapeCreationParamsUnion) => Promise<ShapeModel>

  /**
   * Creates a path element of the specified type with the given parameters.
   * @param type - The {@link CoreElementType} or string identifier of the path to create.
   * @param params - A {@link PathCreationOptionsUnion} object containing creation parameters.
   * @returns A Promise resolving to the created {@link Element} (typically a more specific Path type).
   */
  createPath: (type: CoreElementType | string, params: PathCreationOptionsUnion) => Promise<Element>

  /**
   * Registers a creator instance for a specific element type.
   * @param type - The {@link CoreElementType} or string identifier to associate with the creator.
   * @param creator - An instance of {@link ShapeCreator} or {@link PathCreator}.
   */
  registerCreator: (type: CoreElementType | string, creator: ShapeCreator<ShapeModel, BaseElementCreationParams> | PathCreator<ShapeModel, BaseElementCreationParams>) => void
}

/**
 * Factory class responsible for creating various types of geometric elements,
 * including shapes and paths, using registered creator instances.
 *
 * @implements {IElementFactory}
 */
export class ElementFactory implements IElementFactory {
  /**
   * Map of registered creators indexed by element type.
   * @private
   */
  private creators: Map<string, ShapeCreator<ShapeModel, BaseElementCreationParams> | PathCreator<ShapeModel, BaseElementCreationParams>> = new Map()

  constructor() {
    console.warn('[ElementFactory CONSTRUCTOR] Initializing and registering default creators.')
    this.registerDefaultCreators()
  }

  /**
   * Generates a simple UUID for element identification.
   *
   * @returns A string representing a UUID.
   * @private
   */
  private generateUUID(): string {
    return Math.random().toString(36).substring(2, 9) + Math.random().toString(36).substring(2, 9)
  }

  /**
   * Normalizes various position input formats (object, array, or undefined)
   * to a standard {@link PointData} interface.
   *
   * @param positionInput - The position input, which can be a `PointData`-like object,
   *                        a 2-element array `[x, y]`, a 3-element array `[x, y, z]`, or `undefined`.
   * @returns A standardized {@link PointData} object (e.g., `{ x: number, y: number, z: number }`).
   *          Defaults to `{ x: 0, y: 0, z: 0 }` if input is invalid or undefined.
   * @private
   */
  private normalizePositionInput(positionInput: PointData | [number, number] | [number, number, number?] | undefined): PointData { // Changed type
    if (positionInput == null)
      return { x: 0, y: 0, z: 0 }
    if (Array.isArray(positionInput)) {
      if (positionInput.length >= 2 && typeof positionInput[0] === 'number' && typeof positionInput[1] === 'number') {
        return { x: positionInput[0], y: positionInput[1], z: positionInput.length > 2 && typeof positionInput[2] === 'number' ? positionInput[2] : 0 }
      }
      console.warn('[ElementFactory] Invalid array for position, defaulting to (0,0,0). Array:', positionInput)
      return { x: 0, y: 0, z: 0 }
    }
    else if (positionInput != null && typeof positionInput.x === 'number' && typeof positionInput.y === 'number') {
      return { x: positionInput.x, y: positionInput.y, z: positionInput.z ?? 0 }
    }
    console.warn('[ElementFactory] Invalid position object, defaulting to (0,0,0). Position:', positionInput)
    return { x: 0, y: 0, z: 0 }
  }

  /**
   * Registers all default shape and path creators.
   *
   * @private
   */
  private registerDefaultCreators(): void {
    // Shape Creators
    console.warn('[ElementFactory registerDefaultCreators] Registering Shape Creators...')
    const rectangleCreatorInstance = new creators.RectangleCreator()
    this.registerCreator(CoreElementType.RECTANGLE, rectangleCreatorInstance)
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.RECTANGLE}`)
    this.registerCreator(CoreElementType.SQUARE, rectangleCreatorInstance)
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.SQUARE}`)

    const ellipseCreatorInstance = new creators.EllipseCreator()
    this.registerCreator(CoreElementType.ELLIPSE, ellipseCreatorInstance)
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.ELLIPSE}`)
    this.registerCreator(CoreElementType.CIRCLE, ellipseCreatorInstance)
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.CIRCLE}`)

    // For PolygonCreator, it seems its constructor uses the elementType to specialize.
    // So, we instantiate it for each specific polygon type it handles.
    this.registerCreator(CoreElementType.POLYGON, new creators.PolygonCreator(CoreElementType.POLYGON))
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.POLYGON}`)
    this.registerCreator(CoreElementType.TRIANGLE, new creators.PolygonCreator(CoreElementType.TRIANGLE))
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.TRIANGLE}`)
    this.registerCreator(CoreElementType.QUADRILATERAL, new creators.PolygonCreator(CoreElementType.QUADRILATERAL))
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.QUADRILATERAL}`)
    this.registerCreator(CoreElementType.PENTAGON, new creators.PolygonCreator(CoreElementType.PENTAGON))
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.PENTAGON}`)
    this.registerCreator(CoreElementType.HEXAGON, new creators.PolygonCreator(CoreElementType.HEXAGON))
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.HEXAGON}`)

    this.registerCreator(CoreElementType.TEXT, new TextCreator())
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.TEXT}`)
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.TEXT]}`)

    const imageCreator = new ImageCreator() // Default import
    this.registerCreator(CoreElementType.IMAGE, imageCreator)
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.IMAGE]}`)

    // Register creators for new entity types, using ImageCreator as a base
    // as these will often be represented by icons/images and share similar basic properties.
    this.registerCreator(CoreElementType.FURNITURE, imageCreator)
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.FURNITURE]} (using ImageCreator)`)
    this.registerCreator(CoreElementType.FIXTURE, imageCreator)
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.FIXTURE]} (using ImageCreator)`)
    this.registerCreator(CoreElementType.LIGHT, imageCreator)
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.LIGHT]} (using ImageCreator)`)
    this.registerCreator(CoreElementType.FLOOR_AREA, imageCreator) // For items like rugs, tile patterns
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.FLOOR_AREA]} (using ImageCreator)`)
    this.registerCreator(CoreElementType.WALL_PAINT, imageCreator)
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.WALL_PAINT]} (using ImageCreator)`)
    this.registerCreator(CoreElementType.WALL_PAPER, imageCreator)
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.WALL_PAPER]} (using ImageCreator)`)
    this.registerCreator(CoreElementType.OPENING, imageCreator) // For doors, windows
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.OPENING]} (using ImageCreator)`)

    // Path Creators
    console.warn('[ElementFactory registerDefaultCreators] Registering Path Creators...')
    const lineCreator = new creators.LineCreator()
    this.registerCreator(CoreElementType.LINE, lineCreator)
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.LINE}`)
    this.registerCreator(CoreElementType.POLYLINE, new creators.PolylineCreator())
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.POLYLINE}`)
    this.registerCreator(CoreElementType.ARC, new creators.ArcCreator())
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.ARC}`)
    this.registerCreator(CoreElementType.QUADRATIC, new creators.QuadraticCreator())
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.QUADRATIC}`)
    this.registerCreator(CoreElementType.CUBIC, new creators.CubicCreator())
    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.CUBIC}`)

    // Other element types if any (e.g., Text, Image, Group)
    // this.registerCreator(CoreElementType.TEXT, new creators.TextCreator());
    // this.registerCreator(CoreElementType.IMAGE, new creators.ImageCreator());
    // this.registerCreator(CoreElementType.GROUP, new creators.GroupCreator());

    console.warn('[ElementFactory registerDefaultCreators] All default creators registered.')
  }

  /**
   * Creates a shape element using the appropriate registered {@link ShapeCreator}.
   *
   * @param type - The {@link CoreElementType} or string identifier of the shape to create.
   * @param params - A {@link ShapeCreationParamsUnion} object containing parameters for shape creation.
   *                 This method ensures common base properties are defaulted if not provided.
   * @returns A Promise resolving to the created {@link ShapeModel}.
   * @throws {@link Error} if no valid creator is registered for the specified type.
   */
  public async createShape(type: CoreElementType | string, params: ShapeCreationParamsUnion): Promise<ShapeModel> {
    const typeKey = type.toString()
    const creatorInstance = this.creators.get(typeKey)

    if (creatorInstance == null) {
      throw new CoreError(ErrorType.FactoryFailed, `No creator registered for shape type: ${typeKey}`)
    }

    if (creatorInstance instanceof ShapeCreator) {
      return creatorInstance.create(params)
    }
    else {
      throw new CoreError(ErrorType.FactoryFailed, `Registered creator for shape type ${typeKey} is not a ShapeCreator.`)
    }
  }

  /**
   * Creates a path element using the appropriate registered {@link PathCreator}.
   *
   * @param type - The {@link CoreElementType} or string identifier of the path to create.
   * @param params - A {@link PathCreationOptionsUnion} object containing parameters for path creation.
   *                 This method ensures common base properties are defaulted and normalizes point data.
   * @returns A Promise resolving to the created {@link Element} (which will be a specific Path type).
   * @throws {@link Error} if no valid creator is registered for the specified type.
   */
  public async createPath(type: CoreElementType | string, params: PathCreationOptionsUnion): Promise<Element> {
    const typeKey = type.toString()
    const creatorInstance = this.creators.get(typeKey) as PathCreator<ShapeModel, BaseElementCreationParams>

    if (creatorInstance == null) {
      throw new CoreError(ErrorType.FactoryFailed, `No creator registered for path type: ${typeKey}`)
    }

    return creatorInstance.create(params)
  }

  /**
   * Registers a {@link ShapeCreator} or {@link PathCreator} for a specific element type.
   *
   * @param type - The {@link CoreElementType} or string identifier for which to register the creator.
   * @param creator - The creator instance (either a `ShapeCreator` or `PathCreator`).
   *                  If a creator for the type already exists, it will be overridden with a warning.
   */
  public registerCreator(type: CoreElementType | string, creator: ShapeCreator<ShapeModel, BaseElementCreationParams> | PathCreator<ShapeModel, BaseElementCreationParams>): void {
    const typeKey = type.toString()
    if (this.creators.has(typeKey)) {
      // console.warn(`[ElementFactory] Creator for type ${type} is being overwritten.`); // Optional: useful for debugging registration issues
    }
    console.warn(`[ElementFactory registerCreator] Registering creator ${creator.constructor.name} for type ${type.toString()}`)
    this.creators.set(typeKey, creator)
  }

  /**
   * Prepares base parameters for element creation from common user input.
   * This ensures that metadata is complete and common optional properties are defaulted.
   *
   * @param userParams - The {@link CommonUserInputParams} provided by the user.
   * @param defaultNamePrefix - A prefix to use for generating a default element name if not provided in `userParams.metadata`.
   * @returns An object containing base element creation parameters, excluding `id` and `type` (which are added by the calling method).
   * @private
   */
  private _prepareBaseParams(
    userParams: CommonUserInputParams, // Use CommonUserInputParams
    defaultNamePrefix: string, // Keep for naming if userParams.metadata.name is not set
    // id is now part of BaseElementCreationParams, not needed here directly
  ): Omit<BaseElementCreationParams, 'id' | 'type'> { // id and type are handled by the caller
    // const id = this.generateUUID(); // ID generation moved to specific create methods or ShapeCreator
    const metadata = ensureCompleteMetadata({
      ...(userParams.metadata || {}),
      name: userParams.metadata?.name ?? `${defaultNamePrefix} Element`, // Generic name
    })

    return {
      // id, // ID is handled by the specific creator or passed in params
      // type: CoreElementType.UNKNOWN, // Type is set by the specific creator
      majorCategory: userParams.majorCategory, // Pass through majorCategory
      minorCategory: userParams.minorCategory, // Pass through minorCategory
      zLevelId: userParams.zLevelId, // Pass through zLevelId
      isFixedCategory: userParams.isFixedCategory, // Pass through isFixedCategory
      metadata,
      visible: userParams.visible ?? true,
      locked: userParams.locked ?? false,
      rotation: userParams.rotation ?? 0,
      selectable: userParams.selectable ?? true,
      draggable: userParams.draggable ?? true,
      showHandles: userParams.showHandles ?? true,
      // Layer is deprecated but still used in some places
      // We're using a spread operator to avoid direct property assignment
      // which would trigger the TypeScript deprecation warning
      ...(userParams.layer !== undefined ? { layer: userParams.layer } : {}),
      zIndex: userParams.zIndex,
      properties: userParams.properties,
      fill: userParams.fill,
      stroke: userParams.stroke,
      strokeWidth: userParams.strokeWidth,
      opacity: userParams.opacity,
      strokeDasharray: userParams.strokeDasharray,
    }
  }

  /**
   * Creates a Rectangle element from user-provided parameters.
   *
   * @param userParams - {@link UserRectInputParams} specifying the rectangle's properties.
   * @returns A Promise resolving to the created {@link ShapeModel} (Rectangle).
   */
  public async createRectangle(userParams: UserRectInputParams): Promise<ShapeModel> {
    const id = this.generateUUID()
    const type = (userParams.cornerRadius != null && userParams.cornerRadius > 0) ? CoreElementType.SQUARE : CoreElementType.RECTANGLE
    const position = this.normalizePositionInput(userParams.position)
    const baseParams = this._prepareBaseParams(userParams, type === CoreElementType.SQUARE ? 'Square' : 'Rectangle')

    const params: CreateRectangleParams = {
      ...baseParams,
      id,
      type,
      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed
      minorCategory: userParams.minorCategory,
      zLevelId: userParams.zLevelId,
      isFixedCategory: userParams.isFixedCategory,
      position,
      width: userParams.width,
      height: userParams.height,
      cornerRadius: userParams.cornerRadius,
    }
    return this.createShape(type, params)
  }

  /**
   * Creates a Square element from user-provided parameters.
   *
   * @param userParams - {@link UserSquareInputParams} specifying the square's properties.
   * @returns A Promise resolving to the created {@link ShapeModel} (Square, treated as a Rectangle).
   */
  public async createSquare(userParams: UserSquareInputParams): Promise<ShapeModel> {
    const id = this.generateUUID()
    const position = this.normalizePositionInput(userParams.position)
    const baseParams = this._prepareBaseParams(userParams, 'Square')

    const params: CreateRectangleParams = {
      ...baseParams,
      id,
      type: CoreElementType.SQUARE,
      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed
      minorCategory: userParams.minorCategory,
      zLevelId: userParams.zLevelId,
      isFixedCategory: userParams.isFixedCategory,
      position,
      width: userParams.size,
      height: userParams.size,
      cornerRadius: userParams.cornerRadius,
    }
    return this.createShape(CoreElementType.SQUARE, params)
  }

  /**
   * Creates an Ellipse element from user-provided parameters.
   *
   * @param userParams - {@link UserEllipseInputParams} specifying the ellipse's properties.
   * @returns A Promise resolving to the created {@link ShapeModel} (Ellipse).
   */
  public async createEllipse(userParams: UserEllipseInputParams): Promise<ShapeModel> {
    const id = this.generateUUID()
    const position = this.normalizePositionInput(userParams.position)
    const baseParams = this._prepareBaseParams(userParams, 'Ellipse')

    const params: CreateEllipseParams = {
      ...baseParams,
      id,
      type: CoreElementType.ELLIPSE,
      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed
      minorCategory: userParams.minorCategory,
      zLevelId: userParams.zLevelId,
      isFixedCategory: userParams.isFixedCategory,
      position,
      radiusX: userParams.radiusX,
      radiusY: userParams.radiusY,
    }
    return this.createShape(CoreElementType.ELLIPSE, params)
  }

  /**
   * Creates a Circle element from user-provided parameters.
   *
   * @param userParams - {@link UserCircleInputParams} specifying the circle's properties.
   * @returns A Promise resolving to the created {@link ShapeModel} (Circle, treated as an Ellipse).
   */
  public async createCircle(userParams: UserCircleInputParams): Promise<ShapeModel> {
    const id = this.generateUUID()
    const position = this.normalizePositionInput(userParams.position)
    const baseParams = this._prepareBaseParams(userParams, 'Circle')

    const params: CreateCircleParams = {
      ...baseParams,
      id,
      type: CoreElementType.CIRCLE,
      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed
      minorCategory: userParams.minorCategory,
      zLevelId: userParams.zLevelId,
      isFixedCategory: userParams.isFixedCategory,
      position,
      radius: userParams.radius,
    }
    return this.createShape(CoreElementType.CIRCLE, params)
  }

  /**
   * Creates a Line element from user-provided parameters.
   *
   * @param userParams - {@link UserLineInputParams} specifying the line's properties.
   * @returns A Promise resolving to the created {@link ShapeModel} (Line).
   *          Note: The return type is `ShapeModel` for consistency, though Line is a Path.
   *          The `createPath` method returns `Element`. This might need alignment.
   */
  public async createLine(userParams: UserLineInputParams): Promise<ShapeModel> {
    const id = this.generateUUID()
    const start = this.normalizePositionInput(userParams.start)
    const end = this.normalizePositionInput(userParams.end)
    const baseParams = this._prepareBaseParams(userParams, 'Line')

    const params: CreateLineParams = {
      ...baseParams,
      id,
      type: CoreElementType.LINE,
      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed
      minorCategory: userParams.minorCategory,
      zLevelId: userParams.zLevelId,
      isFixedCategory: userParams.isFixedCategory,
      start,
      end,
      arrowStart: userParams.arrowStart,
      arrowEnd: userParams.arrowEnd,
    }
    return this.createPath(CoreElementType.LINE, params) as Promise<ShapeModel>
  }

  /**
   * Creates a polyline element from user parameters.
   *
   * @param userParams - User input parameters for polyline creation.
   * @returns A promise resolving to the created polyline model.
   */
  public async createPolyline(userParams: UserPolylineInputParams): Promise<ShapeModel> {
    const id = this.generateUUID()
    const points = userParams.points.map(p => this.normalizePositionInput(p))
    const baseParams = this._prepareBaseParams(userParams, 'Polyline')

    const params: CreatePolylineParams = {
      ...baseParams,
      id,
      type: CoreElementType.POLYLINE,
      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed
      minorCategory: userParams.minorCategory,
      zLevelId: userParams.zLevelId,
      isFixedCategory: userParams.isFixedCategory,
      points,
      curved: userParams.curved,
      tension: userParams.tension,
    }
    return this.createPath(CoreElementType.POLYLINE, params) as Promise<ShapeModel>
  }

  /**
   * Creates a regular polygon element from user parameters.
   *
   * @param userParams - User input parameters for regular polygon creation.
   * @returns A promise resolving to the created polygon model.
   */
  public async createRegularPolygon(userParams: UserRegularPolygonInputParams): Promise<ShapeModel> {
    const id = this.generateUUID()
    const center = this.normalizePositionInput(userParams.center)
    const baseParams = this._prepareBaseParams(userParams, 'Polygon')

    const params: CreatePolygonParams = {
      ...baseParams,
      id,
      type: CoreElementType.POLYGON, // Or more specific if derivable
      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed
      minorCategory: userParams.minorCategory,
      zLevelId: userParams.zLevelId,
      isFixedCategory: userParams.isFixedCategory,
      points: [], // Will be calculated by the creator
      sides: userParams.sides,
      radius: userParams.radius,
      center,
    }
    return this.createShape(CoreElementType.POLYGON, params)
  }

  /**
   * Creates a custom polygon element from user parameters.
   *
   * @param userParams - User input parameters for custom polygon creation.
   * @returns A promise resolving to the created polygon model.
   */
  public async createCustomPolygon(userParams: UserCustomPolygonInputParams): Promise<ShapeModel> {
    const id = this.generateUUID()
    const points = userParams.points.map(p => this.normalizePositionInput(p))
    const baseParams = this._prepareBaseParams(userParams, 'Polygon')

    const params: CreatePolygonParams = {
      ...baseParams,
      id,
      type: CoreElementType.POLYGON,
      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed
      minorCategory: userParams.minorCategory,
      zLevelId: userParams.zLevelId,
      isFixedCategory: userParams.isFixedCategory,
      points,
      isRegular: false,
    }
    return this.createShape(CoreElementType.POLYGON, params)
  }

  /**
   * Creates an arc element from user parameters.
   *
   * @param userParams - User input parameters for arc creation.
   * @returns A promise resolving to the created arc element.
   */
  public async createArc(userParams: UserArcInputParams): Promise<Element> {
    const id = this.generateUUID()
    const position = this.normalizePositionInput(userParams.position)
    const baseParams = this._prepareBaseParams(userParams, 'Arc')

    const params: CreateArcParams = {
      ...baseParams,
      id,
      type: CoreElementType.ARC,
      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed
      minorCategory: userParams.minorCategory,
      zLevelId: userParams.zLevelId,
      isFixedCategory: userParams.isFixedCategory,
      position,
      radius: userParams.radius,
      startAngle: userParams.startAngle,
      endAngle: userParams.endAngle,
      closed: userParams.closed,
    }
    return this.createPath(CoreElementType.ARC, params)
  }

  /**
   * Creates a default shape of the specified type at the given position.
   *
   * @param type - The type of shape to create.
   * @param id - Unique identifier for the shape.
   * @param positionInput - Position for the shape in various formats.
   * @param majorCategory - The major category of the shape.
   * @param minorCategory - The minor category of the shape.
   * @param zLevelId - The zLevelId of the shape.
   * @param isFixedCategory - Whether the shape is fixed in its category.
   * @returns A promise resolving to the created shape model.
   */
  public async createDefaultShape(
    type: CoreElementType | string,
    id: string, // This ID is used for naming and as the element's ID directly
    positionInput: PointData | [number, number] | [number, number, number?],
    // Added layer parameters
    majorCategory: MajorCategory,
    minorCategory?: MinorCategory,
    zLevelId?: string,
    isFixedCategory?: boolean,
  ): Promise<ShapeModel> {
    const position = this.normalizePositionInput(positionInput)
    const defaultName = `Default ${type.toString()} ${id}`

    // Construct CommonUserInputParams for _prepareBaseParams
    const userInputParams: CommonUserInputParams = {
      majorCategory,
      minorCategory,
      zLevelId,
      isFixedCategory,
      metadata: { name: defaultName },
      // Provide some default style properties
      fill: '#CCCCCC',
      stroke: '#000000',
      strokeWidth: 1,
      opacity: 1,
      // position is not part of CommonUserInputParams, it's handled by specific creators/params
    }

    const preparedBase = this._prepareBaseParams(userInputParams, `Default ${type.toString()}`)

    let params: ShapeCreationParamsUnion

    const commonProps = { // Properties that are part of BaseElementCreationParams
      id, // Use the provided id directly
      majorCategory: preparedBase.majorCategory, // Take from preparedBase
      minorCategory: preparedBase.minorCategory,
      zLevelId: preparedBase.zLevelId,
      isFixedCategory: preparedBase.isFixedCategory,
      metadata: preparedBase.metadata,
      visible: preparedBase.visible,
      locked: preparedBase.locked,
      rotation: preparedBase.rotation,
      selectable: preparedBase.selectable,
      draggable: preparedBase.draggable,
      showHandles: preparedBase.showHandles,
      // Layer is deprecated but still used in some places
      // We're using a computed property name to avoid direct property assignment
      // which would trigger the TypeScript deprecation warning
      ...(preparedBase.layer !== undefined ? { layer: preparedBase.layer } : {}),
      zIndex: preparedBase.zIndex,
      properties: preparedBase.properties,
      fill: preparedBase.fill,
      stroke: preparedBase.stroke,
      strokeWidth: preparedBase.strokeWidth,
      opacity: preparedBase.opacity,
      strokeDasharray: preparedBase.strokeDasharray,
    }

    switch (type) {
      case CoreElementType.RECTANGLE:
        params = { ...commonProps, type: CoreElementType.RECTANGLE, position, width: 100, height: 50 }
        break
      case CoreElementType.SQUARE:
        params = { ...commonProps, type: CoreElementType.SQUARE, position, width: 50, height: 50 }
        break
      case CoreElementType.CIRCLE:
        params = { ...commonProps, type: CoreElementType.CIRCLE, position, radius: 30 }
        break
      case CoreElementType.ELLIPSE:
        params = { ...commonProps, type: CoreElementType.ELLIPSE, position, radiusX: 50, radiusY: 30 }
        break
      case CoreElementType.LINE:
        params = {
          ...commonProps,
          type: CoreElementType.LINE,
          start: position,
          end: { x: position.x + 100, y: position.y + 50, z: position.z },
        } as CreateLineParams
        break
      case CoreElementType.POLYLINE:
        params = {
          ...commonProps,
          type: CoreElementType.POLYLINE,
          points: [
            position,
            { x: position.x + 50, y: position.y + 20, z: position.z },
            { x: position.x + 100, y: position.y, z: position.z },
          ],
        } as CreatePolylineParams
        break
      case CoreElementType.ARC:
        params = {
          ...commonProps,
          type: CoreElementType.ARC,
          position,
          radius: 50,
          startAngle: 0,
          endAngle: 90,
          closed: false,
        } as CreateArcParams
        break
      default:
        params = {
          ...commonProps,
          type: CoreElementType.RECTANGLE,
          position,
          width: 50,
          height: 50,
        } as CreateRectangleParams
        console.warn(`ElementFactory: Creating generic default (rectangle) for type ${type}`)
    }

    // 根据元素类型选择正确的创建方法
    if (type === CoreElementType.LINE || type === CoreElementType.POLYLINE || type === CoreElementType.ARC) {
      // 确保 params 是路径类型的参数
      const pathParams = params as PathCreationOptionsUnion
      return this.createPath(type, pathParams) as Promise<ShapeModel>
    }
    else {
      return this.createShape(type, params)
    }
  }
}
