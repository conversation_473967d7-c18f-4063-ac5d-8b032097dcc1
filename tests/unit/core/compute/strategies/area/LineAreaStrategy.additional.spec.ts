import type { Element } from '@/types/core/elementDefinitions'
import { describe, expect, it } from 'vitest'
import { LineAreaStrategy } from '@/core/compute/strategies/area/LineAreaStrategy'
import { CoreError } from '@/services/system/error-service/coreError'
import { ElementType } from '@/types/core/elementDefinitions'

/**
 * Additional unit tests for the LineAreaStrategy class to improve coverage
 */
describe('lineAreaStrategy - Additional Tests', () => {
  let strategy: LineAreaStrategy

  beforeEach(() => {
    strategy = new LineAreaStrategy()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe(ElementType.LINE)
  })

  it('should always return 0 for line area (lines have no area)', () => {
    const line: Element = {
      id: 'test-line',
      type: ElementType.LINE,
      visible: true,
      locked: false,
      metadata: {},
      properties: {
        start: { x: 0, y: 0 },
        end: { x: 10, y: 10 },
      },
    }

    const area = strategy.calculateArea(line)
    expect(area).toBe(0)
  })

  it('should return 0 for horizontal line', () => {
    const line: Element = {
      id: 'horizontal-line',
      type: ElementType.LINE,
      visible: true,
      locked: false,
      metadata: {},
      properties: {
        start: { x: 0, y: 5 },
        end: { x: 10, y: 5 },
      },
    }

    const area = strategy.calculateArea(line)
    expect(area).toBe(0)
  })

  it('should return 0 for vertical line', () => {
    const line: Element = {
      id: 'vertical-line',
      type: ElementType.LINE,
      visible: true,
      locked: false,
      metadata: {},
      properties: {
        start: { x: 5, y: 0 },
        end: { x: 5, y: 10 },
      },
    }

    const area = strategy.calculateArea(line)
    expect(area).toBe(0)
  })

  it('should return 0 for diagonal line', () => {
    const line: Element = {
      id: 'diagonal-line',
      type: ElementType.LINE,
      visible: true,
      locked: false,
      metadata: {},
      properties: {
        start: { x: 0, y: 0 },
        end: { x: 3, y: 4 },
      },
    }

    const area = strategy.calculateArea(line)
    expect(area).toBe(0)
  })

  it('should return 0 for zero-length line (same start and end)', () => {
    const line: Element = {
      id: 'zero-line',
      type: ElementType.LINE,
      visible: true,
      locked: false,
      metadata: {},
      properties: {
        start: { x: 5, y: 5 },
        end: { x: 5, y: 5 },
      },
    }

    const area = strategy.calculateArea(line)
    expect(area).toBe(0)
  })

  it('should return 0 even with invalid coordinates', () => {
    const line: Element = {
      id: 'invalid-line',
      type: ElementType.LINE,
      visible: true,
      locked: false,
      metadata: {},
      properties: {
        start: { x: Number.NaN, y: Number.NaN },
        end: { x: Number.NaN, y: Number.NaN },
      },
    }

    const area = strategy.calculateArea(line)
    expect(area).toBe(0)
  })

  it('should return 0 for very long line', () => {
    const line: Element = {
      id: 'long-line',
      type: ElementType.LINE,
      visible: true,
      locked: false,
      metadata: {},
      properties: {
        start: { x: -1000, y: -1000 },
        end: { x: 1000, y: 1000 },
      },
    }

    const area = strategy.calculateArea(line)
    expect(area).toBe(0)
  })

  it('should throw error for wrong element type', () => {
    const rectangle: Element = {
      id: 'not-line',
      type: ElementType.RECTANGLE,
      visible: true,
      locked: false,
      metadata: {},
    }

    expect(() => strategy.calculateArea(rectangle)).toThrow(CoreError)
    expect(() => strategy.calculateArea(rectangle)).toThrow('LineAreaStrategy can only calculate area for LINE elements, got RECTANGLE')
  })

  it('should throw error for null element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    expect(() => strategy.calculateArea(null)).toThrow(CoreError)
    expect(() => strategy.calculateArea(null)).toThrow('Invalid element provided to LineAreaStrategy (null or undefined).')
  })

  it('should throw error for undefined element', () => {
    // @ts-ignore - Testing runtime behavior with invalid input
    expect(() => strategy.calculateArea(undefined)).toThrow(CoreError)
    expect(() => strategy.calculateArea(undefined)).toThrow('Invalid element provided to LineAreaStrategy (null or undefined).')
  })

  it('should return 0 for line with missing properties', () => {
    const line: Element = {
      id: 'missing-props-line',
      type: ElementType.LINE,
      visible: true,
      locked: false,
      metadata: {},
      properties: {},
    }

    const area = strategy.calculateArea(line)
    expect(area).toBe(0)
  })

  it('should return 0 for line with null properties', () => {
    const line: Element = {
      id: 'null-props-line',
      type: ElementType.LINE,
      visible: true,
      locked: false,
      metadata: {},
      properties: {
        start: null,
        end: null,
      },
    }

    const area = strategy.calculateArea(line)
    expect(area).toBe(0)
  })
})
