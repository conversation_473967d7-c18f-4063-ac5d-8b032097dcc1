# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: Build and deploy Node.js app to Azure Web App - app-shapes2-prod-001

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:

  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read #This is required for actions/checkout

    steps:
      - uses: actions/checkout@v4
      - name: Set up Node.js version
        uses: actions/setup-node@v3
        with:
          node-version: 22.x

      - name: Install Dependencies
        run: npm install

      - name: Run Lint Check
        run: npm run lint

      - name: Run Format Check
        run: npm run format -- --check

      - name: Run Vitest Unit & Integration Tests
        run: npm run test:vitest

      # Optional: Add coverage check if needed (might need custom script for threshold)
      # - name: Check Vitest Coverage
      #   run: npm run test:vitest:coverage

      - name: Run Build
        run: npm run build --if-present

      # Note: Playwright tests need a running server.
      # Running them here requires starting the server in the background or
      # moving E2E tests to a separate job after build/deploy or using a preview environment.
      # Simple execution (may fail if build output needs serving differently):
      # - name: Run Playwright E2E Tests
      #   run: npm run test

      - name: Zip artifact for deployment
        run: zip release.zip ./* -r

      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
          name: node-app
          path: release.zip

  deploy:
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: 'Production'
      url: ${{ steps.deploy-to-webapp.outputs.webapp-url }}
    permissions:
      id-token: write #This is required for requesting the JWT
      contents: read #This is required for actions/checkout
    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: node-app

      - name: Unzip artifact for deployment
        run: unzip release.zip
      
      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID_E2233213B0A241FCBC4F4BDAB07C8C0E }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID_B0EB73E9E8D240418B051BD4B8298A9A }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID_82EA01A284A54C7CBE2A2CD201F13FCE }}

      - name: 'Deploy to Azure Web App'
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: 'app-shapes2-prod-001'
          slot-name: 'Production'
          package: .
         