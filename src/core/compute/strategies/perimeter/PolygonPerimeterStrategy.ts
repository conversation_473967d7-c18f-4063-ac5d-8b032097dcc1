/**
 * Perimeter Calculation Strategy for Polygon Elements
 *
 * @remarks
 * This strategy implements the {@link PerimeterCalculatorStrategy} for calculating
 * the perimeter of various polygon-like elements, including `POLYGON`, `TRIANGLE`,
 * `HEXAGON`, `QUAD<PERSON><PERSON><PERSON><PERSON>L`, `<PERSON><PERSON><PERSON><PERSON><PERSON>`, `<PERSON><PERSON><PERSON><PERSON>`, `<PERSON><PERSON><PERSON><PERSON>`, and `<PERSON><PERSON><PERSON><PERSON>`.
 *
 * The calculation relies on the `points` property (vertices) of the polygon element
 * and uses the `calculatePolygonPerimeterInternal` (aliased from `calculatePerimeter`
 * in `../../../../lib/utils/geometry/polygonUtils`) function. This utility typically
 * sums the lengths of the segments connecting the vertices in order.
 *
 * The strategy validates that the element is of a supported polygon type and that
 * its `points` array is valid and contains valid point data. The points are assumed
 * to be relative to the polygon's `position` if it's a `ShapeElement`.
 *
 * @module core/compute/strategies/perimeter/PolygonPerimeterStrategy
 * @see {@link Shape.Polygon} for the polygon element type definition.
 * @see {@link PerimeterCalculatorStrategy}
 * @see {@link calculatePolygonPerimeterInternal}
 */
import type { PerimeterCalculatorStrategy } from '../../../../types/core/compute'
import type { PointData as IPoint } from '../../../../types/core/element/geometry/point' // Use PointData, alias as IPoint
import type {
  Element,
  Shape,
  ShapeElement,
} from '../../../../types/core/elementDefinitions'
import { calculatePerimeter as calculatePolygonPerimeterInternal } from '../../../../lib/utils/geometry/polygonUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

/**
 * Implements the {@link PerimeterCalculatorStrategy} for polygon-like elements
 * (e.g., {@link CoreElementType.POLYGON}, {@link CoreElementType.TRIANGLE}).
 * It calculates the perimeter by summing the lengths of the segments forming the polygon's boundary.
 */
export class PolygonPerimeterStrategy implements PerimeterCalculatorStrategy {
  /**
   * Checks if the given element type is considered a polygon type for this strategy.
   * @param type - The element type string.
   * @returns True if the type is a recognized polygon type, false otherwise.
   */
  private isPolygonType(type: string): boolean {
    return type === CoreElementType.POLYGON
      || type === CoreElementType.TRIANGLE
      || type === CoreElementType.HEXAGON
      || type === CoreElementType.QUADRILATERAL
      || type === CoreElementType.PENTAGON
      || type === CoreElementType.OCTAGON
      || type === CoreElementType.NONAGON
      || type === CoreElementType.DECAGON
  }

  /**
   * Calculates the perimeter of a polygon-like element.
   *
   * @param element - The polygon-like shape element, expected to be of type {@link ShapeElement}
   *                  and one of the supported polygon types.
   * @returns The calculated perimeter.
   * @throws {@link CoreError} if the element is `null` or `undefined`.
   * @throws {@link CoreError} if the element type is not a supported polygon type.
   * @throws {@link CoreError} if the polygon's `points` property is invalid or contains invalid point data.
   */
  public calculatePerimeter(element: Element): number {
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement === null || shapeElement === undefined) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `[PolygonPerimeterStrategy] Invalid element provided (null or undefined).`,
        undefined,
        { component: 'PolygonPerimeterStrategy', operation: 'calculatePerimeter' },
      )
    }

    if (!this.isPolygonType(shapeElement.type)) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `[PolygonPerimeterStrategy] Expected a polygon-like element, but received type: ${shapeElement?.type ?? 'unknown'} (ID: ${shapeElement?.id ?? 'unknown'}).`,
        undefined,
        { component: 'PolygonPerimeterStrategy', operation: 'calculatePerimeter', target: shapeElement.id },
      )
    }

    const polygonElement = shapeElement as unknown as Shape.Polygon // Added unknown for type safety
    const points = polygonElement.points as IPoint[] | undefined // Points are directly on Shape.Polygon

    // calculatePolygonPerimeterInternal in polygonUtils handles cases with < 2 points by returning 0.
    // It handles 2 points as the length of the segment.
    // It handles 3+ points as the sum of segment lengths (perimeter of the polygon).
    if (!points || !Array.isArray(points)) { // Should not happen if type is correct, but good check
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Polygon (ID: ${shapeElement.id}) has invalid points property.`,
        undefined,
        { component: 'PolygonPerimeterStrategy', operation: 'calculatePerimeter', target: shapeElement.id },
      )
    }
    if (points.length > 0) { // Only validate if there are points to avoid error on empty array for length check
      for (let i = 0; i < points.length; i++) {
        const point = points[i]
        if (point === null || point === undefined || typeof point.x !== 'number' || typeof point.y !== 'number'
          || !Number.isFinite(point.x) || !Number.isFinite(point.y)) {
          throw new CoreError(
            ErrorType.InvalidParameter,
            `[PolygonPerimeterStrategy] Invalid point data in polygon ${shapeElement.id} at index ${i}.`,
            undefined,
            { component: 'PolygonPerimeterStrategy', operation: 'calculatePerimeter', target: shapeElement.id, metadata: { pointIndex: i, pointData: point } },
          )
        }
      }
    }

    // 多边形的points是相对于position的，所以我们需要转换为绝对坐标来计算周长
    // 创建一个新的点数组，每个点都加上element的position
    const absolutePoints = points.map((point) => {
      const positionX = (shapeElement.position?.x !== null && shapeElement.position?.x !== undefined) ? shapeElement.position.x : 0
      const positionY = (shapeElement.position?.y !== null && shapeElement.position?.y !== undefined) ? shapeElement.position.y : 0
      return {
        x: point.x + positionX,
        y: point.y + positionY,
        z: point.z,
      }
    })

    // 使用绝对坐标计算周长
    return calculatePolygonPerimeterInternal(absolutePoints)
  }

  /**
   * Returns all element types this strategy supports.
   *
   * @remarks
   * This strategy can calculate the perimeter for various n-sided polygons
   * (TRIANGLE, PENTAGON, HEXAGON, etc.). It returns an array of all supported
   * polygon types to be registered with the strategy registry.
   *
   * @returns An array of supported polygon element types that this strategy handles
   */
  public getElementType(): CoreElementType[] {
    return [
      CoreElementType.POLYGON,
      CoreElementType.TRIANGLE,
      CoreElementType.PENTAGON,
      CoreElementType.HEXAGON,
      CoreElementType.QUADRILATERAL,
      CoreElementType.OCTAGON,
      CoreElementType.NONAGON,
      CoreElementType.DECAGON,
    ]
  }
}
