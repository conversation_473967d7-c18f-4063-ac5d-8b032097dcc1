/**
 * Shape Edit Service Implementation
 *
 * Provides functionality for editing existing shapes.
 * Implements the ElementEditService interface.
 * Includes property update functionality for consistent property updates.
 *
 * @module services/shapes/shape-actions
 */

import type { ShapeRepository } from '@/core/state/ShapeRepository'
import type { ShapeElement } from '@/types/core'
import type { PatternDefinition } from '@/types/core/element/elementPatternTypes'
import type { ServiceResult } from '@/types/services/core/serviceResult'
import type { EventBus } from '@/types/services/events'
import type { AppEventMap } from '@/types/services/events/eventRegistry'
import type { LoggerService } from '@/types/services/logging'
import type {
  ElementEditResult,
} from '@/types/services/shapes'
import type { ElementEditService } from '@/types/services/shapes/shapeService'

import { v4 as uuidv4 } from 'uuid'

import { getService } from '@/services/core/registry'
import { getLoggerService } from '@/services/system/logging'
import { ServiceId } from '@/types/services/core/serviceIdentifier'
import { AppEventType } from '@/types/services/events'

// 本地类型定义以解决类型推断问题
interface LocalElementEditRequest {
  /** Element ID to edit */
  id: string
  /** New position */
  position?: { x: number, y: number }
  /** New style properties */
  style?: Record<string, unknown>
  /** Other property updates */
  properties?: Record<string, unknown>
  /** Whether to select the element after editing */
  selectAfterEdit?: boolean
  /** Major category for layer organization (e.g., 'floor', 'ceiling', 'furniture') */
  majorCategory?: string
  /** Minor category within the major category (e.g., 'coverings', 'lighting') */
  minorCategory?: string
  /** Z-level identifier for ordering within a layer */
  zLevelId?: string
}

// Style properties that can exist at both top level and in properties
export const STYLE_PROPERTIES = [
  'fill',
  'stroke',
  'strokeWidth', // 数值类型
  'opacity', // 数值类型
  'strokeDasharray',
  'strokeLinecap',
  'strokeLinejoin',
  'fontStyle', // 🔧 添加文本样式属性
  'fontWeight', // 🔧 添加文本粗细属性
  'fontSize', // 🔧 添加文本大小属性
  'fontFamily', // 🔧 添加文本字体属性
  'textAlign', // 🔧 添加文本对齐属性
] as const

// Numeric properties that need special handling
export const NUMERIC_PROPERTIES = [
  'width',
  'height',
  'rotation',
  'cornerRadius',
  'costUnitPrice',
  'costMultiplierOrCount',
  'strokeWidth', // 添加到数值属性列表
  'opacity', // 添加到数值属性列表
  'creationRadius', // 添加多边形创建半径属性
  'radius', // 添加圆形半径属性
  'radiusX', // 🔧 添加椭圆X半径属性
  'radiusY', // 🔧 添加椭圆Y半径属性
  'startAngle', // 🔧 添加圆弧起始角度属性
  'endAngle', // 🔧 添加圆弧结束角度属性
  'fontSize', // 🔧 添加文本大小属性（数值类型）
] as const

// Cost-related properties
export const COST_PROPERTIES = [
  'costUnitPrice',
  'costMultiplierOrCount',
  'costBasis',
  'costTotal',
  'computeCostEnabled', // 🔧 添加成本计算开关属性
] as const

// Properties that exist at both top level and in properties
export const DUAL_LEVEL_PROPERTIES = [
  ...STYLE_PROPERTIES,
  ...NUMERIC_PROPERTIES,
  'majorCategory',
  'minorCategory',
  'zLevelId',
  'name',
] as const

export type StyleProperty = typeof STYLE_PROPERTIES[number]
export type NumericProperty = typeof NUMERIC_PROPERTIES[number]
export type CostProperty = typeof COST_PROPERTIES[number]
export type DualLevelProperty = typeof DUAL_LEVEL_PROPERTIES[number]

/**
 * Creates a property update patch for a single element.
 *
 * @remarks
 * This function handles various property types including nested properties,
 * position coordinates, numeric values, and style properties. It ensures
 * proper validation and maintains consistency between top-level and nested
 * property values.
 *
 * @param element - The element to create a patch for
 * @param propertyPath - The path to the property to update (e.g., 'width', 'position.x', 'properties.fill')
 * @param value - The new value for the property
 * @returns A patch object that can be used to update the element, or null if the update is invalid
 *
 * @example
 * ```typescript
 * const patch = createPropertyPatch(element, 'width', 100);
 * if (patch) {
 *   // Apply patch to element
 * }
 * ```
 */
export function createPropertyPatch(
  element: ShapeElement,
  propertyPath: string,
  value: unknown,
): { id: string, properties: Record<string, unknown>, [key: string]: unknown } | null {
  const logger = getLoggerService()

  // Special case for pattern property - handle it first before initializing patch
  if (propertyPath === 'pattern') {
    logger.debug('[createPropertyPatch] Processing pattern property for element:', element.id)
    logger.debug('[createPropertyPatch] Pattern value:', JSON.stringify(value, null, 2))

    const isValid = isValidPatternValue(value)

    if (isValid) {
      if (value === undefined) {
        // 使用特殊标记来表示删除操作，因为JSON.stringify会过滤掉undefined值
        const patch: { id: string, properties: Record<string, unknown>, [key: string]: unknown } = {
          id: element.id,
          properties: { ...(element.properties || {}) },
          __deletePattern: true, // 特殊标记表示要删除pattern属性
        }
        logger.debug('[createPropertyPatch] Pattern set to undefined for deletion')
        return patch
      }
      else {
        // 正常情况下包含pattern
        const patch: { id: string, properties: Record<string, unknown>, [key: string]: unknown } = {
          id: element.id,
          properties: { ...(element.properties || {}) },
          pattern: value as PatternDefinition,
        }
        logger.debug('[createPropertyPatch] Pattern validation passed, patch created:', JSON.stringify(patch, null, 2))
        return patch
      }
    }

    logger.warn('[createPropertyPatch] Pattern validation failed for element:', element.id, 'value:', JSON.stringify(value, null, 2))
    return null
  }

  // Initialize patch with element ID and properties for non-pattern properties
  const patch: { id: string, properties: Record<string, unknown>, [key: string]: unknown } = {
    id: element.id,
    properties: { ...(element.properties || {}) },
  }

  // Handle properties.* paths (nested properties)
  if (propertyPath.startsWith('properties.')) {
    const parts = propertyPath.split('.')
    if (parts.length < 2) {
      return null
    }

    // Remove 'properties.' prefix
    const propertyKey = parts.slice(1).join('.')
    logger.debug(`[PropertyUpdateService] Processing properties.* path: ${propertyPath} -> propertyKey: ${propertyKey}`)
    return updateNestedProperty(element, patch, propertyKey, value)
  }

  // Handle position.x and position.y
  if (propertyPath === 'position.x' || propertyPath === 'position.y') {
    return updatePositionProperty(element, patch, propertyPath, value)
  }

  // 特殊处理元数据名称属性 (metadata.name)
  if (propertyPath === 'metadata.name') {
    // 确保 metadata 对象存在
    if (typeof patch.metadata === 'undefined') {
      patch.metadata = {}
    }
    (patch.metadata as any).name = value

    // 同时更新 properties.metadata.name
    if (typeof patch.properties === 'undefined') {
      patch.properties = {}
    }
    if (typeof patch.properties.metadata === 'undefined') {
      patch.properties.metadata = {}
    }
    (patch.properties.metadata as any).name = value

    logger.debug(`[PropertyUpdateService] Updated metadata.name to: ${String(value)}`)
    return patch
  }

  // 特殊处理名称属性 (name)
  if (propertyPath === 'name') {
    // 更新顶层 name 属性
    patch.name = value

    // 同时更新 properties.name
    if (typeof patch.properties === 'undefined') {
      patch.properties = {}
    }
    patch.properties.name = value

    logger.debug(`[PropertyUpdateService] Updated name to: ${String(value)}`)
    return patch
  }

  // 特殊处理图层属性 (majorCategory, minorCategory, zLevelId)
  if (propertyPath === 'majorCategory' || propertyPath === 'minorCategory' || propertyPath === 'zLevelId') {
    // 同时更新顶层和properties中的图层属性
    patch[propertyPath] = value
    if (typeof patch.properties === 'undefined') {
      patch.properties = {}
    }
    patch.properties[propertyPath] = value

    // 添加更多日志
    logger.debug(`[PropertyUpdateService] Updated layer property ${propertyPath} to: ${String(value)}`)
    logger.debug(`[PropertyUpdateService] Element before update: ${element.id}`)
    logger.debug(`[PropertyUpdateService] Patch created for ${propertyPath}`)

    return patch
  }

  // Handle direct properties (width, height, etc.) - only for non-nested paths
  if (!propertyPath.includes('.') && NUMERIC_PROPERTIES.includes(propertyPath as NumericProperty)) {
    return updateNumericProperty(element, patch, propertyPath, value)
  }

  // Handle cost properties - only for non-nested paths
  if (!propertyPath.includes('.') && COST_PROPERTIES.includes(propertyPath as CostProperty)) {
    return updateCostProperty(element, patch, propertyPath, value)
  }

  // 特殊处理 strokeWidth 和 opacity
  if (propertyPath === 'strokeWidth' || propertyPath === 'opacity') {
    if (typeof value === 'number') {
      patch[propertyPath] = value
      if (typeof patch.properties === 'undefined') {
        patch.properties = {}
      }
      patch.properties[propertyPath] = value
      return patch
    }
    else if (typeof value === 'string') {
      const numValue = Number.parseFloat(value)
      if (!Number.isNaN(numValue)) {
        patch[propertyPath] = numValue
        if (typeof patch.properties === 'undefined') {
          patch.properties = {}
        }
        patch.properties[propertyPath] = numValue
        return patch
      }
    }
    logger.warn(`[PropertyUpdateService] Invalid value for ${propertyPath}: ${String(value)}`)
    return null
  }

  // 处理其他样式属性
  if (STYLE_PROPERTIES.includes(propertyPath as StyleProperty)) {
    return updateStyleProperty(element, patch, propertyPath, value)
  }

  // Handle other top-level properties
  if (propertyPath in element) {
    patch[propertyPath] = value
    patch.properties[propertyPath] = value
    return patch
  }

  // For any other property, just update it in properties
  patch.properties[propertyPath] = value
  return patch
}

/**
 * Validates a pattern value to ensure it meets the required structure.
 *
 * @param value - The value to validate as a pattern
 * @returns True if the value is a valid pattern or undefined, false otherwise
 */
function isValidPatternValue(value: unknown): boolean {
  const logger = getLoggerService()

  logger.debug('[isValidPatternValue] Validating pattern value:', JSON.stringify(value, null, 2))

  if (value === undefined) {
    logger.debug('[isValidPatternValue] Pattern is undefined - valid for removal')
    return true
  }
  if (value === null) {
    logger.debug('[isValidPatternValue] Pattern is null - invalid')
    return false
  }

  if (typeof value === 'object' && !Array.isArray(value)) {
    const pattern = value as Partial<PatternDefinition>
    const hasId = typeof pattern.id === 'string'
    const hasType = typeof pattern.type === 'string'
    const hasTextureType = typeof pattern.textureType === 'string'

    // Check if type values are valid
    const validTypes = ['lines', 'circles', 'paths', 'texture-lines', 'texture-circles', 'texture-paths']
    const typeIsValid = hasType && validTypes.includes(pattern.type!)
    const textureTypeIsValid = hasTextureType && validTypes.includes(pattern.textureType!)

    logger.debug('[isValidPatternValue] Pattern validation details:', {
      hasId,
      hasType,
      hasTextureType,
      typeIsValid,
      textureTypeIsValid,
      id: pattern.id,
      type: pattern.type,
      textureType: pattern.textureType,
    })

    // Accept pattern if it has id and either a valid type or valid textureType
    const isValid = hasId && (typeIsValid || textureTypeIsValid)
    logger.debug('[isValidPatternValue] Pattern validation result:', isValid)

    return isValid
  }

  logger.debug('[isValidPatternValue] Pattern is not a valid object - invalid')
  return false
}

/**
 * Updates a nested property in the patch
 */
function updateNestedProperty(
  element: ShapeElement,
  patch: { id: string, properties: Record<string, unknown>, [key: string]: unknown },
  propertyKey: string,
  value: unknown,
): { id: string, properties: Record<string, unknown>, [key: string]: unknown } | null {
  const logger = getLoggerService()
  logger.debug(`[PropertyUpdateService] updateNestedProperty called with propertyKey: ${propertyKey}, value: ${String(value)}`)

  // Handle nested properties (e.g., controlPoint.x)
  if (propertyKey.includes('.')) {
    const parts = propertyKey.split('.')
    let current = patch.properties

    // Navigate to the correct nesting level
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i]
      if (current[part] === undefined || current[part] === null || typeof current[part] !== 'object') {
        current[part] = {}
      }
      current = current[part] as Record<string, unknown>
    }

    const finalKey = parts[parts.length - 1]

    // Handle numeric properties specially
    if (NUMERIC_PROPERTIES.includes(finalKey as NumericProperty)) {
      const numValue = parseNumericValue(value)
      if (numValue !== null) {
        current[finalKey] = numValue
      }
      else if (value === '' || value === null || value === undefined) {
        current[finalKey] = getDefaultValueForNumericProperty(finalKey as NumericProperty)
      }
      else {
        logger.warn(`[PropertyUpdateService] Invalid numeric value for ${propertyKey}: ${String(value)}`)
        return null
      }
    }
    else {
      // For non-numeric properties, just set the value
      current[finalKey] = value
    }

    return patch
  }

  // Handle direct properties in properties object
  // 特殊处理多边形的creationRadius/radius更新，需要重新生成点
  if (propertyKey === 'creationRadius' || propertyKey === 'radius') {
    const numValue = parseNumericValue(value)
    logger.debug(`[PropertyUpdateService] Processing ${propertyKey} update: value=${String(value)}, numValue=${String(numValue)}`)

    if (numValue !== null && numValue > 0) {
      patch.properties[propertyKey] = numValue

      // 对于多边形，同时更新 radius 和 creationRadius 以保持一致性
      if (propertyKey === 'radius') {
        patch.properties.creationRadius = numValue
      }
      else if (propertyKey === 'creationRadius') {
        patch.properties.radius = numValue
      }

      // 检查是否是多边形类型
      const isPolygonType = [
        'POLYGON',
        'TRIANGLE',
        'QUADRILATERAL',
        'PENTAGON',
        'HEXAGON',
        'polygon',
        'triangle',
        'quadrilateral',
        'pentagon',
        'hexagon',
      ].includes(element.type)

      logger.debug(`[PropertyUpdateService] Element type: ${element.type}, isPolygonType: ${isPolygonType}`)
      logger.debug(`[PropertyUpdateService] Element properties: sides=${String(element.properties?.sides)}, isRegular=${String(element.properties?.isRegular)}`)

      logger.debug(`[PropertyUpdateService] Checking polygon regeneration conditions: willRegenerate=${isPolygonType && Boolean(element.properties?.sides) && Boolean(element.properties?.isRegular)}`)

      if (isPolygonType && element.properties?.sides !== undefined && element.properties?.isRegular !== undefined) {
        // 重新生成多边形的点
        const sides = Number(element.properties.sides)
        const radius = numValue

        logger.debug(`[PropertyUpdateService] Regenerating polygon points: sides=${sides}, radius=${radius}, isRegular=${String(element.properties.isRegular)}`)

        if (sides >= 3) {
          // 生成相对于原点(0,0)的顶点坐标
          const newPoints = []
          const angleStep = (2 * Math.PI) / sides
          const startAngle = -Math.PI / 2 // 默认起始角度，顶点向上

          for (let i = 0; i < sides; i++) {
            const angle = startAngle + i * angleStep
            const x = radius * Math.cos(angle)
            const y = radius * Math.sin(angle)
            newPoints.push({ x, y, z: 0 })
          }

          // 计算几何中心并调整点，确保几何中心在原点
          let sumX = 0
          let sumY = 0
          for (const point of newPoints) {
            sumX += point.x
            sumY += point.y
          }
          const centroidX = sumX / sides
          const centroidY = sumY / sides

          // 调整点坐标
          const adjustedPoints = newPoints.map(p => ({
            x: p.x - centroidX,
            y: p.y - centroidY,
            z: p.z,
          }))

          // 更新points属性
          patch.properties.points = adjustedPoints

          logger.debug(`[PropertyUpdateService] Successfully regenerated polygon points: radius=${radius}, sides=${sides}, newPointsCount=${adjustedPoints.length}`)
        }
        else {
          logger.warn(`[PropertyUpdateService] Invalid sides count for polygon: ${sides}`)
        }
      }
      else {
        logger.debug(`[PropertyUpdateService] Skipping point regeneration: isPolygonType=${isPolygonType}`)
      }

      // Also update top-level property if it exists there
      if (propertyKey in element) {
        patch[propertyKey] = numValue
      }
    }
    else {
      logger.warn(`[PropertyUpdateService] Invalid creationRadius/radius value: ${String(value)}`)
      return null
    }
  }
  else if (NUMERIC_PROPERTIES.includes(propertyKey as NumericProperty)) {
    const numValue = parseNumericValue(value)
    if (numValue !== null) {
      patch.properties[propertyKey] = numValue
      // Also update top-level property if it exists there
      if (propertyKey in element) {
        patch[propertyKey] = numValue
      }
    }
    else if (value === '' || value === null || value === undefined) {
      patch.properties[propertyKey] = getDefaultValueForNumericProperty(propertyKey as NumericProperty)
      if (propertyKey in element) {
        patch[propertyKey] = getDefaultValueForNumericProperty(propertyKey as NumericProperty)
      }
    }
    else {
      logger.warn(`[PropertyUpdateService] Invalid numeric value for ${propertyKey}: ${String(value)}`)
      return null
    }
  }
  else if (COST_PROPERTIES.includes(propertyKey as CostProperty)) {
    if (propertyKey === 'costBasis') {
      patch.properties[propertyKey] = value
      // 同时更新顶层属性（如果存在）
      if (propertyKey in element) {
        patch[propertyKey] = value
      }
    }
    else {
      const numValue = parseNumericValue(value)
      if (numValue !== null) {
        patch.properties[propertyKey] = numValue
        // 同时更新顶层属性（如果存在）
        if (propertyKey in element) {
          patch[propertyKey] = numValue
        }
      }
      else if (value === '' || value === null || value === undefined) {
        const defaultValue = getDefaultValueForCostProperty(propertyKey as CostProperty)
        patch.properties[propertyKey] = defaultValue
        // 同时更新顶层属性（如果存在）
        if (propertyKey in element) {
          patch[propertyKey] = defaultValue
        }
      }
      else {
        logger.warn(`[PropertyUpdateService] Invalid numeric value for ${propertyKey}: ${String(value)}`)
        return null
      }
    }
  }
  else if (propertyKey === 'majorCategory' || propertyKey === 'minorCategory' || propertyKey === 'zLevelId') {
    // 特殊处理图层属性，确保同时更新顶层和properties中的值
    patch.properties[propertyKey] = value
    patch[propertyKey] = value
    logger.debug(`[PropertyUpdateService] Updated nested layer property ${propertyKey} to: ${String(value)}`)
  }
  else if (STYLE_PROPERTIES.includes(propertyKey as StyleProperty)) {
    patch.properties[propertyKey] = value
    // Also update top-level property if it exists there
    if (propertyKey in element) {
      patch[propertyKey] = value
    }
  }
  else if (propertyKey === 'start' || propertyKey === 'end') {
    // 特殊处理LINE元素的start和end属性
    // properties中存储相对坐标，顶层存储绝对坐标
    patch.properties[propertyKey] = value

    // 计算绝对坐标：position + 相对坐标
    const position = element.position || { x: 0, y: 0, z: 0 }
    const relativePoint = value as { x: number, y: number, z?: number }
    const absolutePoint = {
      x: position.x + relativePoint.x,
      y: position.y + relativePoint.y,
      z: (position.z || 0) + (relativePoint.z || 0),
    }
    patch[propertyKey] = absolutePoint

    logger.debug(`[PropertyUpdateService] Updated LINE ${propertyKey}:`, {
      relative: value,
      position,
      absolute: absolutePoint,
    })
  }
  else {
    // For any other property, just set it
    patch.properties[propertyKey] = value
    // Also update top-level property if it exists there
    if (propertyKey in element) {
      patch[propertyKey] = value
    }
  }

  return patch
}

/**
 * Updates a position property in the patch
 */
function updatePositionProperty(
  element: ShapeElement,
  patch: { id: string, properties: Record<string, unknown>, [key: string]: unknown },
  propertyPath: string,
  value: unknown,
): { id: string, properties: Record<string, unknown>, [key: string]: unknown } | null {
  const logger = getLoggerService()
  const key = propertyPath.split('.')[1] as 'x' | 'y'
  const prevPosition = element.position ?? { x: 0, y: 0 }

  const numValue = parseNumericValue(value)
  if (numValue !== null) {
    const newPosition = { ...prevPosition, [key]: numValue }
    patch.position = newPosition
    patch.properties.position = newPosition
  }
  else if (value === '' || value === null || value === undefined) {
    // Keep existing position for empty values
    patch.position = prevPosition
    patch.properties.position = prevPosition
  }
  else {
    logger.warn(`[PropertyUpdateService] Invalid value for position.${key}: ${String(value)}`)
    return null
  }

  return patch
}

/**
 * Updates a numeric property in the patch
 */
function updateNumericProperty(
  element: ShapeElement,
  patch: { id: string, properties: Record<string, unknown>, [key: string]: unknown },
  propertyPath: string,
  value: unknown,
): { id: string, properties: Record<string, unknown>, [key: string]: unknown } | null {
  const logger = getLoggerService()
  const key = propertyPath as NumericProperty

  const numValue = parseNumericValue(value)
  if (numValue !== null) {
    if (key in element) {
      patch[key] = numValue
    }
    patch.properties[key] = numValue

    // Special handling for squares: keep width and height synchronized
    if (element.type === 'SQUARE' && (key === 'width' || key === 'height')) {
      logger.debug(`[PropertyUpdateService] Square ${key} updated to ${numValue}, synchronizing other dimension`)
      const otherDimension = key === 'width' ? 'height' : 'width'

      // Update the other dimension to the same value
      if (otherDimension in element) {
        patch[otherDimension] = numValue
      }
      patch.properties[otherDimension] = numValue

      logger.debug(`[PropertyUpdateService] Square dimensions synchronized: ${key}=${numValue}, ${otherDimension}=${numValue}`)
    }

    // Special handling for ellipses: keep radiusX/radiusY synchronized with width/height
    if (element.type === 'ELLIPSE') {
      // 🔧 修复：从store获取最新的元素状态，确保获取到最新的半径值
      let currentElement = element
      if (typeof window !== 'undefined' && (window as any).__ZUSTAND_SHAPES_STORE__) {
        const shapesStore = (window as any).__ZUSTAND_SHAPES_STORE__
        const state = shapesStore.getState()
        const storeElement = state.shapes.find((s: any) => s.id === element.id)
        if (storeElement) {
          currentElement = storeElement
        }
      }

      if (key === 'radiusX') {
        const width = numValue * 2
        patch.width = width
        patch.properties.width = width
        // 🔧 修复：从最新的元素状态获取radiusY值
        const currentRadiusY = currentElement.properties?.radiusY ?? (currentElement as any).radiusY ?? 80
        patch.properties.radiusY = currentRadiusY
        patch.radiusY = currentRadiusY
        patch.height = currentRadiusY * 2
        patch.properties.height = currentRadiusY * 2
        logger.debug(`[PropertyUpdateService] Ellipse radiusX updated to ${numValue}, synchronized width to ${width}, preserved radiusY: ${currentRadiusY}`)
      }
      else if (key === 'radiusY') {
        const height = numValue * 2
        patch.height = height
        patch.properties.height = height
        // 🔧 修复：从最新的元素状态获取radiusX值
        const currentRadiusX = currentElement.properties?.radiusX ?? (currentElement as any).radiusX ?? 120
        patch.properties.radiusX = currentRadiusX
        patch.radiusX = currentRadiusX
        patch.width = currentRadiusX * 2
        patch.properties.width = currentRadiusX * 2
        logger.debug(`[PropertyUpdateService] Ellipse radiusY updated to ${numValue}, synchronized height to ${height}, preserved radiusX: ${currentRadiusX}`)
      }
      else if (key === 'width') {
        const radiusX = numValue / 2
        patch.radiusX = radiusX
        patch.properties.radiusX = radiusX
        // 🔧 修复：从最新的元素状态获取radiusY值
        const currentRadiusY = currentElement.properties?.radiusY ?? (currentElement as any).radiusY ?? 80
        patch.properties.radiusY = currentRadiusY
        patch.radiusY = currentRadiusY
        patch.height = currentRadiusY * 2
        patch.properties.height = currentRadiusY * 2
        logger.debug(`[PropertyUpdateService] Ellipse width updated to ${numValue}, synchronized radiusX to ${radiusX}, preserved radiusY: ${currentRadiusY}`)
      }
      else if (key === 'height') {
        const radiusY = numValue / 2
        patch.radiusY = radiusY
        patch.properties.radiusY = radiusY
        // 🔧 修复：从最新的元素状态获取radiusX值
        const currentRadiusX = currentElement.properties?.radiusX ?? (currentElement as any).radiusX ?? 120
        patch.properties.radiusX = currentRadiusX
        patch.radiusX = currentRadiusX
        patch.width = currentRadiusX * 2
        patch.properties.width = currentRadiusX * 2
        logger.debug(`[PropertyUpdateService] Ellipse height updated to ${numValue}, synchronized radiusY to ${radiusY}, preserved radiusX: ${currentRadiusX}`)
      }
    }

    // Special handling for polygons: regenerate points when radius changes
    if ((key === 'radius' || key === 'creationRadius')) {
      // 对于多边形，同时更新 radius 和 creationRadius 以保持一致性
      if (key === 'radius') {
        patch.properties.creationRadius = numValue
        if ('creationRadius' in element) {
          patch.creationRadius = numValue
        }
      }
      else if (key === 'creationRadius') {
        patch.properties.radius = numValue
        if ('radius' in element) {
          patch.radius = numValue
        }
      }

      // 检查是否是多边形类型
      const isPolygonType = [
        'POLYGON',
        'TRIANGLE',
        'QUADRILATERAL',
        'PENTAGON',
        'HEXAGON',
        'polygon',
        'triangle',
        'quadrilateral',
        'pentagon',
        'hexagon',
      ].includes(element.type)

      logger.debug(`[PropertyUpdateService] Element type: ${element.type}, isPolygonType: ${isPolygonType}`)
      logger.debug(`[PropertyUpdateService] Element properties: sides=${String(element.properties?.sides)}, isRegular=${String(element.properties?.isRegular)}`)

      if (isPolygonType && element.properties?.sides !== undefined && element.properties?.isRegular !== undefined) {
        // 重新生成多边形的点
        const sides = Number(element.properties.sides)
        const radius = numValue

        logger.debug(`[PropertyUpdateService] Regenerating polygon points: sides=${sides}, radius=${radius}, isRegular=${String(element.properties.isRegular)}`)

        if (sides >= 3) {
          // 生成相对于原点(0,0)的顶点坐标
          const newPoints = []
          const angleStep = (2 * Math.PI) / sides
          const startAngle = -Math.PI / 2 // 默认起始角度，顶点向上

          for (let i = 0; i < sides; i++) {
            const angle = startAngle + i * angleStep
            const x = radius * Math.cos(angle)
            const y = radius * Math.sin(angle)
            newPoints.push({ x, y, z: 0 })
          }

          // 计算几何中心并调整点，确保几何中心在原点
          let sumX = 0
          let sumY = 0
          for (const point of newPoints) {
            sumX += point.x
            sumY += point.y
          }
          const centroidX = sumX / sides
          const centroidY = sumY / sides

          // 调整点坐标
          const adjustedPoints = newPoints.map(p => ({
            x: p.x - centroidX,
            y: p.y - centroidY,
            z: p.z,
          }))

          // 更新points属性
          patch.properties.points = adjustedPoints

          logger.debug(`[PropertyUpdateService] Successfully regenerated polygon points: radius=${radius}, sides=${sides}, newPointsCount=${adjustedPoints.length}`)
        }
        else {
          logger.warn(`[PropertyUpdateService] Invalid sides count for polygon: ${sides}`)
        }
      }
      else {
        logger.debug(`[PropertyUpdateService] Skipping point regeneration: isPolygonType=${isPolygonType}`)
      }
    }
  }
  else if (value === '' || value === null || value === undefined) {
    const defaultValue = getDefaultValueForNumericProperty(key)
    if (key in element) {
      patch[key] = defaultValue
    }
    patch.properties[key] = defaultValue

    // Special handling for squares: keep width and height synchronized with default values
    if (element.type === 'SQUARE' && (key === 'width' || key === 'height')) {
      logger.debug(`[PropertyUpdateService] Square ${key} reset to default ${defaultValue}, synchronizing other dimension`)
      const otherDimension = key === 'width' ? 'height' : 'width'

      // Update the other dimension to the same default value
      if (otherDimension in element) {
        patch[otherDimension] = defaultValue
      }
      patch.properties[otherDimension] = defaultValue

      logger.debug(`[PropertyUpdateService] Square dimensions synchronized with defaults: ${key}=${defaultValue}, ${otherDimension}=${defaultValue}`)
    }
  }
  else {
    logger.warn(`[PropertyUpdateService] Invalid value for ${propertyPath}: ${String(value)}`)
    return null
  }

  return patch
}

/**
 * Updates a cost property in the patch
 */
function updateCostProperty(
  _element: ShapeElement,
  patch: { id: string, properties: Record<string, unknown>, [key: string]: unknown },
  propertyPath: string,
  value: unknown,
): { id: string, properties: Record<string, unknown>, [key: string]: unknown } | null {
  const logger = getLoggerService()
  const key = propertyPath as CostProperty

  // 🔧 特殊处理 computeCostEnabled 布尔值
  if (key === 'computeCostEnabled') {
    if (typeof value === 'boolean') {
      patch.properties[key] = value
      logger.debug(`[PropertyUpdateService] Updated computeCostEnabled to: ${value}`)
      return patch
    }
    else {
      logger.warn(`[PropertyUpdateService] Invalid boolean value for computeCostEnabled: ${String(value)}`)
      return null
    }
  }

  if (key === 'costBasis') {
    patch.properties[key] = value
    return patch
  }

  const numValue = parseNumericValue(value)
  if (numValue !== null) {
    patch.properties[key] = numValue
  }
  else if (value === '' || value === null || value === undefined) {
    patch.properties[key] = getDefaultValueForCostProperty(key)
  }
  else {
    logger.warn(`[PropertyUpdateService] Invalid value for ${propertyPath}: ${String(value)}`)
    return null
  }

  return patch
}

/**
 * Updates a style property in the patch
 */
function updateStyleProperty(
  element: ShapeElement,
  patch: { id: string, properties: Record<string, unknown>, [key: string]: unknown },
  propertyPath: string,
  value: unknown,
): { id: string, properties: Record<string, unknown>, [key: string]: unknown } | null {
  const logger = getLoggerService()
  const key = propertyPath as StyleProperty

  // 特殊处理 strokeWidth 和 opacity，它们应该是数字类型
  if (key === 'strokeWidth' || key === 'opacity') {
    if (typeof value === 'number') {
      if (key in element) {
        patch[key] = value
      }
      patch.properties[key] = value
    }
    else if (typeof value === 'string') {
      const numValue = Number.parseFloat(value)
      if (!Number.isNaN(numValue)) {
        if (key in element) {
          patch[key] = numValue
        }
        patch.properties[key] = numValue
      }
      else {
        logger.warn(`[PropertyUpdateService] Invalid numeric value for style property ${propertyPath}: ${String(value)}`)
        return null
      }
    }
    else {
      logger.warn(`[PropertyUpdateService] Invalid type for style property ${propertyPath}: ${String(value)}`)
      return null
    }
  }
  else if (typeof value === 'string' || value === undefined || value === null) {
    // 其他样式属性应该是字符串类型
    if (key in element) {
      patch[key] = value
    }
    patch.properties[key] = value
  }
  else {
    logger.warn(`[PropertyUpdateService] Invalid value for style property ${propertyPath}: ${String(value)}`)
    return null
  }

  return patch
}

/**
 * Parses a value as a number, returning null if it's not a valid number
 */
export function parseNumericValue(value: unknown): number | null {
  if (typeof value === 'number' && Number.isFinite(value)) {
    return value
  }

  if (typeof value === 'string') {
    // Clean the string value (remove non-numeric characters except decimal point and minus sign)
    const cleanedValue = value.replace(/[^\d.-]/g, '').replace(/^-+/, '-')
    const numValue = Number.parseFloat(cleanedValue)

    if (Number.isFinite(numValue)) {
      return numValue
    }
  }

  return null
}

/**
 * Gets the default value for a numeric property
 */
export function getDefaultValueForNumericProperty(property: NumericProperty): number {
  switch (property) {
    case 'rotation':
      return 0
    case 'width':
    case 'height':
      return 100
    case 'cornerRadius':
      return 0
    case 'strokeWidth':
      return 1
    case 'opacity':
      return 1
    case 'creationRadius':
    case 'radius':
      return 50
    case 'radiusX':
      return 120 // 椭圆默认X半径
    case 'radiusY':
      return 80 // 椭圆默认Y半径
    case 'startAngle':
      return 0 // 圆弧默认起始角度
    case 'endAngle':
      return 90 // 圆弧默认结束角度
    case 'costUnitPrice':
      return 1 // 默认单位成本为1而不是0
    case 'costMultiplierOrCount':
      return 1 // 🔧 修复：默认乘数为1，确保成本计算正常工作
    default:
      return 0
  }
}

/**
 * Gets the default value for a cost property
 */
export function getDefaultValueForCostProperty(property: CostProperty): number | string | boolean {
  switch (property) {
    case 'costUnitPrice':
      return 1 // 默认单位成本为1
    case 'costMultiplierOrCount':
      return 1 // 🔧 修复：默认乘数为1，确保成本计算正常工作
    case 'costBasis':
      return 'unit' // 默认成本基础为unit
    case 'costTotal':
      return 0
    case 'computeCostEnabled':
      return true // 🔧 默认启用成本计算
    default:
      return 0
  }
}

/**
 * Implements ElementEditService interface
 * @implements {ElementEditService}
 */
export class ElementEditServiceImpl implements ElementEditService {
  readonly serviceId: string = ServiceId.ElementEditService as string

  /**
   * Creates a new instance of the ElementEditServiceImpl
   * @param eventBus - Event bus for publishing events
   * @param logger - Logger service for logging
   * @param repository - Repository for accessing shapes
   */
  constructor(
    private eventBus: EventBus<AppEventMap>,
    private logger: LoggerService,
    private repository?: ShapeRepository,
  ) {
    this.logger.info('[ElementEditServiceImpl] Initialized.')
  }

  /**
   * Factory method to create a ElementEditServiceImpl instance
   * @param logger - Optional logger service
   * @returns A new ElementEditServiceImpl instance
   */
  public static create(
    logger?: LoggerService,
  ): ElementEditServiceImpl {
    try {
      const eventBus = getService<EventBus<AppEventMap>>(ServiceId.EventBus)
      const loggerService = logger || getService<LoggerService>(ServiceId.Logger)
      // 临时删除存储库获取，因为ServiceId没有ShapeRepository枚举值
      // const repository = getService<ShapeRepository>(ServiceId.ShapeRepository)

      return new ElementEditServiceImpl(eventBus, loggerService)
    }
    catch (error) {
      throw new Error(`Failed to create ElementEditServiceImpl: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 从ElementEditRequest中提取需要发送到事件中的changes
   * 使用PropertyUpdateService的逻辑来确保一致的属性更新
   */
  private extractChanges(request: LocalElementEditRequest): Record<string, unknown> {
    // 直接使用 request，因为类型已经确定
    const typedRequest = request

    // 创建一个基本的元素对象，用于创建补丁
    const baseElement: Partial<ShapeElement> = {
      id: typedRequest.id,
      type: 'unknown', // 类型不重要，因为我们只关心属性更新
      position: { x: 0, y: 0 },
      properties: {},
    }

    // 使用现有属性更新baseElement
    if (typedRequest.position !== undefined && typedRequest.position !== null) {
      baseElement.position = { ...typedRequest.position }
    }

    // 合并所有属性到一个补丁对象
    const patch: Record<string, unknown> = { id: typedRequest.id }

    // 处理样式信息
    if (typedRequest.style !== undefined && typedRequest.style !== null) {
      Object.entries(typedRequest.style).forEach(([key, value]) => {
        const propertyPatch = createPropertyPatch(baseElement as ShapeElement, key, value)
        if (propertyPatch !== null) {
          Object.entries(propertyPatch).forEach(([patchKey, patchValue]) => {
            if (patchKey !== 'id') {
              patch[patchKey] = patchValue
            }
          })
        }
      })
    }

    // 处理图层信息
    if (typedRequest.majorCategory !== undefined) {
      this.logger.debug(`[ElementEditServiceImpl] Processing majorCategory: ${String(typedRequest.majorCategory)}`)
      // 直接设置，不需要通过 createPropertyPatch
      patch.majorCategory = typedRequest.majorCategory
      if (patch.properties === undefined || patch.properties === null)
        patch.properties = {}
      ;(patch.properties as Record<string, unknown>).majorCategory = typedRequest.majorCategory
      this.logger.debug(`[ElementEditServiceImpl] Set majorCategory to: ${String(typedRequest.majorCategory)}`)
      this.logger.debug(`[ElementEditServiceImpl] Current patch after setting majorCategory:`, JSON.stringify(patch, null, 2))
    }

    if (typedRequest.minorCategory !== undefined) {
      this.logger.debug(`[ElementEditServiceImpl] Processing minorCategory: ${String(typedRequest.minorCategory)}`)
      // 直接设置，不需要通过 createPropertyPatch
      patch.minorCategory = typedRequest.minorCategory
      if (patch.properties === undefined || patch.properties === null)
        patch.properties = {}
      ;(patch.properties as Record<string, unknown>).minorCategory = typedRequest.minorCategory
      this.logger.debug(`[ElementEditServiceImpl] Set minorCategory to: ${String(typedRequest.minorCategory)}`)
      this.logger.debug(`[ElementEditServiceImpl] Current patch after setting minorCategory:`, JSON.stringify(patch, null, 2))
    }

    if (typedRequest.zLevelId !== undefined) {
      this.logger.debug(`[ElementEditServiceImpl] Processing zLevelId: ${String(typedRequest.zLevelId)}`)
      // 直接设置，不需要通过 createPropertyPatch
      patch.zLevelId = typedRequest.zLevelId
      if (patch.properties === undefined || patch.properties === null)
        patch.properties = {}
      ;(patch.properties as Record<string, unknown>).zLevelId = typedRequest.zLevelId
      this.logger.debug(`[ElementEditServiceImpl] Set zLevelId to: ${String(typedRequest.zLevelId)}`)
      this.logger.debug(`[ElementEditServiceImpl] Current patch after setting zLevelId:`, JSON.stringify(patch, null, 2))
    }

    // 确保图层属性已正确设置
    if (typedRequest.majorCategory !== undefined || typedRequest.minorCategory !== undefined || typedRequest.zLevelId !== undefined) {
      this.logger.debug(`[ElementEditServiceImpl] Final layer properties in patch:`, {
        majorCategory: patch.majorCategory,
        minorCategory: patch.minorCategory,
        zLevelId: patch.zLevelId,
        propertiesMajorCategory: (patch.properties as Record<string, unknown>)?.majorCategory,
        propertiesMinorCategory: (patch.properties as Record<string, unknown>)?.minorCategory,
        propertiesZLevelId: (patch.properties as Record<string, unknown>)?.zLevelId,
      })
    }

    // 处理其他属性
    if (typedRequest.properties !== undefined && typedRequest.properties !== null) {
      Object.entries(typedRequest.properties).forEach(([key, value]) => {
        const propertyPatch = createPropertyPatch(baseElement as ShapeElement, key, value)
        if (propertyPatch !== null) {
          // 合并属性
          if (propertyPatch.properties !== undefined && propertyPatch.properties !== null) {
            if (patch.properties === undefined || patch.properties === null)
              patch.properties = {}
            Object.entries(propertyPatch.properties).forEach(([propKey, propValue]) => {
              ;(patch.properties as Record<string, unknown>)[propKey] = propValue
            })
          }

          // 合并顶层属性（除了id和properties）
          Object.entries(propertyPatch).forEach(([patchKey, patchValue]) => {
            if (patchKey !== 'id' && patchKey !== 'properties') {
              patch[patchKey] = patchValue
            }
          })
        }
      })
    }

    return patch
  }

  /**
   * Edit a shape based on the provided request
   * @param request - Shape edit request
   * @returns Promise resolving to a ElementEditResult
   */
  async editShape(request: unknown): Promise<ElementEditResult> {
    this.logger.debug('[ElementEditServiceImpl] Edit request details:', request)
    try {
      // 运行时类型检查和转换
      if (request === null || request === undefined || typeof request !== 'object' || !('id' in request) || typeof (request as { id: unknown }).id !== 'string') {
        throw new Error('Invalid ElementEditRequest: missing or invalid id property')
      }

      // 转换为本地类型以确保类型安全
      const requestObj = request as Record<string, unknown>
      const typedRequest: LocalElementEditRequest = {
        id: requestObj.id as string,
        position: requestObj.position as { x: number, y: number } | undefined,
        style: requestObj.style as Record<string, unknown> | undefined,
        properties: requestObj.properties as Record<string, unknown> | undefined,
        selectAfterEdit: requestObj.selectAfterEdit as boolean | undefined,
        majorCategory: requestObj.majorCategory as string | undefined,
        minorCategory: requestObj.minorCategory as string | undefined,
        zLevelId: requestObj.zLevelId as string | undefined,
      }

      // 发布StateUpdated事件以记录编辑前的状态（用于撤销/重做）
      this.logger.debug('[ElementEditServiceImpl] Publishing StateUpdated event with preEdit action')
      this.eventBus.publish({
        type: AppEventType.StateUpdated,
        timestamp: Date.now(),
        payload: {
          action: 'preEdit',
          elementIds: [typedRequest.id],
        },
      })

      return await new Promise<ElementEditResult>((resolve) => {
        if (!typedRequest.id || typeof typedRequest.id !== 'string') {
          this.emitError('SHAPE_EDIT_ERROR', 'Missing shape ID in edit request', { request: typedRequest })
          resolve({
            success: false,
            error: {
              code: 'SHAPE_EDIT_ERROR',
              message: 'Missing shape ID in edit request',
              details: { request: typedRequest },
            },
            timestamp: Date.now(),
          })
          return
        }

        // 获取正在编辑的形状ID
        const shapeId = typedRequest.id

        // 构建编辑后的数据对象 - 返回undefined，因为实际的形状数据由repository管理
        const editedShapeData: ShapeElement | undefined = undefined // ElementEditResult期望ShapeElement类型

        // 发布编辑事件
        this.eventBus.publish({
          type: AppEventType.ShapeEditRequest,
          timestamp: Date.now(),
          payload: {
            shapeId,
            changes: this.extractChanges(typedRequest),
            source: 'ElementEditServiceImpl',
          },
        })

        resolve({ success: true, data: editedShapeData, timestamp: Date.now() })

        // 发布编辑完成事件，传递更新后的shape对象（通过 ShapeEditComplete 事件）
        if (this.repository && shapeId) {
          // 获取更新后的shape对象
          const updatedShape = this.repository.getById(shapeId)
          if (updatedShape) {
            // 获取当前选中状态以便在事件处理时使用
            const selectedIds = Array.from(this.repository.getSelectedIds())

            // 发布ShapeEditComplete事件
            this.logger.debug('[ElementEditServiceImpl] Publishing ShapeEditComplete event. Selected IDs:', selectedIds)
            this.eventBus.publish({
              type: AppEventType.ShapeEditComplete,
              timestamp: Date.now(),
              payload: {
                shape: updatedShape,
                selectedIds, // 传递当前选中ID数组
              },
            })
          }
          else {
            this.logger.warn(`[ElementEditServiceImpl] Cannot find updated shape with ID ${shapeId} in repository`)
          }
        }
      })
    }
    catch (error) {
      const errorDetails = { request: request as Record<string, unknown>, error }
      this.emitError('SHAPE_EDIT_ERROR', `Error editing shape: ${error instanceof Error ? error.message : String(error)}`, errorDetails)
      return {
        success: false,
        error: {
          code: 'SHAPE_EDIT_ERROR',
          message: `Error editing shape: ${error instanceof Error ? error.message : String(error)}`,
          details: errorDetails,
        },
        timestamp: Date.now(),
      }
    }
  }

  /**
   * Emits an error event for shape edit failures.
   */
  private emitError(errorType: string, message: string, context?: Record<string, unknown>): void {
    const errorId = uuidv4()
    const fullMessage = `[${errorType}] ${message}`
    this.logger.error(fullMessage, { errorId, ...(context || {}) })
    this.eventBus.publish({
      type: AppEventType.EventError,
      timestamp: Date.now(),
      payload: {
        error: {
          code: errorType,
          message: fullMessage,
          details: context,
          errorId,
        },
        context,
      },
    })
  }

  /**
   * Transforms one or more shapes by publishing edit events for each.
   *
   * @param shapeIds Array of shape IDs to transform
   * @param transformations Transform operations to apply
   * @returns Result containing success or error
   */
  async transformShapes(
    shapeIds: string[],
    transformations: Record<string, unknown>,
  ): Promise<ServiceResult<ShapeElement[]>> {
    try {
      this.logger.debug(`[ElementEditServiceImpl] Publishing transform events for ${shapeIds.length} shapes`, { shapeIds, transformations })
      this.logger.debug(`[ElementEditServiceImpl] transformShapes called with transformations:`, JSON.stringify(transformations, null, 2))

      // 先发布StateUpdated事件，确保当前状态被记录到历史中，以便后续可以撤销
      this.logger.debug('[ElementEditServiceImpl] Publishing StateUpdated event with preEdit action for batch transform')
      this.eventBus.publish({
        type: AppEventType.StateUpdated,
        timestamp: Date.now(),
        payload: {
          action: 'preEdit',
          elementIds: shapeIds,
        },
      })

      // 轻微等待，确保StateUpdated事件已被处理
      await new Promise(resolve => setTimeout(resolve, 10))

      // 然后为每个shapeId发布编辑请求事件
      for (const shapeId of shapeIds) {
        // 获取当前形状，以便保留现有属性
        const currentShape = this.repository?.getById(shapeId)

        // 创建变更对象，确保保留现有属性
        const changes: Record<string, unknown> = {}

        // 如果找到了当前形状，先复制其所有属性
        if (currentShape) {
          // 确保properties对象存在
          if (changes.properties === undefined || changes.properties === null) {
            changes.properties = { ...(currentShape.properties || {}) }
          }
        }

        // 处理properties对象
        if ('properties' in transformations && typeof transformations.properties === 'object' && transformations.properties !== null) {
          // 确保changes.properties存在
          if (changes.properties === undefined || changes.properties === null) {
            changes.properties = {}
          }

          // 将transformations.properties中的属性合并到changes.properties中
          const properties = transformations.properties as Record<string, unknown>
          for (const key in properties) {
            if (Object.prototype.hasOwnProperty.call(properties, key)) {
              ;(changes.properties as Record<string, unknown>)[key] = properties[key]
            }
          }
        }

        // 处理其他顶层属性
        for (const key in transformations) {
          if (key !== 'properties' && Object.prototype.hasOwnProperty.call(transformations, key)) {
            changes[key] = transformations[key]
          }
        }

        this.logger.debug(`[ElementEditServiceImpl] Publishing ShapeEditRequest for shape ${shapeId} with changes:`, JSON.stringify(changes, null, 2))

        this.eventBus.publish({
          type: AppEventType.ShapeEditRequest,
          timestamp: Date.now(),
          payload: {
            shapeId,
            changes,
            source: ElementEditServiceImpl.name,
          },
        })
      }
      return {
        success: true,
        data: [], // No direct data, as store update is handled by CoreCoordinator
        timestamp: Date.now(),
      }
    }
    catch (error) {
      this.emitError('SHAPE_TRANSFORM_ERROR', error instanceof Error ? error.message : 'Unknown error', { shapeIds, transformations })
      return {
        success: false,
        error: {
          code: 'SHAPE_TRANSFORM_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          details: error,
        },
        timestamp: Date.now(),
      }
    }
  }
}
