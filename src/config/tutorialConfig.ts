/**
 * Tutorial Configuration
 *
 * Defines the tutorial steps and modules for the application's onboarding system.
 * Each module covers different aspects of the application functionality.
 */

import type { TutorialConfig } from '@/types/tutorial'

/**
 * Main tutorial configuration for the RenoPilot application
 */
export const tutorialConfig: TutorialConfig = {
  modules: {
    // Main application overview tutorial
    'app-overview': {
      id: 'app-overview',
      name: 'Application Overview',
      description: 'Learn about RenoPilot\'s main features and interface layout',
      category: 'onboarding',
      steps: [
        {
          element: '.toolbar',
          title: 'Welcome to RenoPilot!',
          description: 'Welcome to RenoPilot, your comprehensive design and renovation planning tool! This tutorial will guide you through the five main interface areas. Let\'s explore your workspace step by step.',
          position: 'bottom',
          showProgress: true,
          allowClose: true,
          showButtons: ['next', 'close'],
        },
        {
          element: '[data-tutorial="layer-panel-content"]',
          title: 'Project Organization',
          description: 'This left area helps you organize your design project. It shows modules (major project phases), steps (detailed tasks), and z-levels (layer depth). You can track your progress and manage layers here.',
          position: 'right',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="canvas-container"]',
          title: 'Canvas - Your Design Workspace',
          description: 'This is your main design workspace where you create and edit elements. Use the mouse wheel to zoom, spacebar + drag to pan around, and click elements to select them. The canvas is infinite for any scale project.',
          position: 'top',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="property-sidebar-content"]',
          title: 'Properties Panel',
          description: 'Edit properties of selected elements including geometry, appearance, identity information, and cost calculations. This panel is on the right side and adapts based on your selection.',
          position: 'left',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="bottom-drawer-content"]',
          title: 'Asset Drawer - Design Toolkit',
          description: 'Your toolkit for adding elements to your design. Contains basic shapes, drawing tools, text tools, and when expanded - images and special elements. Drag elements directly onto the canvas or click drawing tools to activate them. This drawer is currently collapsed - click the expand button to see more options.',
          position: 'top',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="tutorial-trigger"]',
          title: 'Tutorial Menu - Continue Learning',
          description: 'To explore specific features in detail, click this tutorial menu anytime! You can access tutorials for the toolbar, layer panel, drawing tools, and more. Each tutorial focuses on a specific area to help you master RenoPilot efficiently.',
          position: 'bottom',
          showProgress: true,
          showButtons: ['previous', 'close'],
        },
      ],
    },

    // Toolbar specific tutorial
    'toolbar-features': {
      id: 'toolbar-features',
      name: 'Toolbar Features',
      description: 'Learn about the various toolbar functions in detail',
      category: 'feature',
      steps: [
        {
          element: '[data-tutorial="project-name"]',
          title: 'Project Name - Organize Your Work',
          description: 'Keep your projects organized with descriptive names. Click directly on the project name to edit it, type your new name, and press Enter to save. Use descriptive names like "Kitchen Renovation 2024" or include client names. The project name appears in exports and saved files, so choose something meaningful!',
          position: 'bottom',
          showProgress: true,
          showButtons: ['next', 'close'],
        },
        {
          element: '[data-tutorial="file-operations"]',
          title: 'File Operations - Project Management',
          description: 'Essential tools for managing your design projects. New Project creates a fresh canvas (save first!), Save Project preserves your work and settings, and Export lets you share as images, PDFs, or CAD files. Save frequently and use descriptive filenames to keep your work organized and professional.',
          position: 'bottom',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="undo-redo"]',
          title: 'Undo/Redo - Mistake-Free Design',
          description: 'Never worry about making mistakes! Undo reverses your last action (Ctrl+Z), and Redo restores what you undone (Ctrl+Y). Both work for all operations and have multiple levels. Use keyboard shortcuts for faster workflow and experiment freely - you can always undo! The button states show when undo/redo is available.',
          position: 'bottom',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="interaction-modes"]',
          title: 'Interaction Modes - Control Your Workflow',
          description: 'Switch between interaction modes to optimize your workflow. Select Mode (default) lets you click, drag, and resize elements. Pan Mode lets you navigate without accidentally selecting things. Hold spacebar for quick temporary panning in any mode. The active mode is highlighted in the toolbar - switch based on your current task!',
          position: 'bottom',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="zoom-controls"]',
          title: 'Zoom Controls - Perfect Your View',
          description: 'Get the perfect view with zoom controls! Zoom In (+) for detailed work, Zoom Out (-) to see more at once, and Reset (100%) for standard view. Use Ctrl + Plus/Minus/0 for keyboard shortcuts, or mouse wheel to zoom at cursor position. Find the perfect zoom level for any task - from tiny details to big-picture planning!',
          position: 'bottom',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="canvas-info-display"]',
          title: 'Canvas Information Display',
          description: 'The toolbar displays your current zoom level (as a percentage) and canvas dimensions. These indicators help you understand your workspace scale and size. The zoom percentage shows how magnified your view is, while dimensions show the total canvas area available for your design. This information is visible on larger screens.',
          position: 'bottom',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },

        {
          element: '[data-tutorial="grid-toggle"]',
          title: 'Grid Toggle - Alignment Helper',
          description: 'Toggle the grid on and off to help with precise alignment. The grid provides visual guides for positioning elements accurately. When active, elements can snap to grid points for perfect alignment. Great for creating organized layouts and maintaining consistent spacing.',
          position: 'bottom',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="panel-toggles"]',
          title: 'Panel Toggle Controls - Workspace Management',
          description: 'These two buttons control your workspace layout. The left button toggles the project organization area, and the right button toggles the properties area. Use them to maximize canvas space when needed or bring back panels for project management and element editing.',
          position: 'bottom',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="keyboard-shortcuts"]',
          title: 'Keyboard Shortcuts - Speed Up Your Workflow',
          description: 'Access the complete keyboard shortcuts guide here. Learn hotkeys for common actions like Ctrl+Z (undo), Ctrl+S (save), Space (pan mode), and many more. Keyboard shortcuts dramatically speed up your design workflow!',
          position: 'bottom',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="tutorial-trigger"]',
          title: 'Tutorial Access - Learn Anytime',
          description: 'Access tutorials and help whenever you need them. Click here to restart this tutorial, explore specific features, or get help with advanced techniques. Learning never stops!',
          position: 'bottom',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="settings-button"]',
          title: 'Settings - Customize Your Experience',
          description: 'Access application settings to customize your workspace. Adjust preferences, configure defaults, and personalize RenoPilot to match your workflow. Make the tool work exactly how you want it!',
          position: 'bottom',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
      ],
    },

    // Canvas interaction tutorial
    'canvas-interaction': {
      id: 'canvas-interaction',
      name: 'Canvas Interaction',
      description: 'Learn how to perform various operations on the canvas',
      category: 'feature',
      steps: [
        {
          element: '[data-tutorial="canvas-container"]',
          title: 'Canvas Navigation - Master Your Movement',
          description: 'Navigate your design space like a pro! Use mouse wheel to zoom at cursor position, spacebar + drag for temporary panning, and arrow keys to nudge selected elements. The canvas is infinite, so explore freely! Grid lines help with alignment, and you can use toolbar zoom controls for precise levels.',
          position: 'top',
          showProgress: true,
          showButtons: ['next', 'close'],
        },
        {
          element: '[data-tutorial="canvas-container"]',
          title: 'Element Selection - Choose Your Focus',
          description: 'Master element selection! Single click to select, Ctrl + click for multi-select, drag rectangle for area selection. Selected elements show blue outlines with handles for resizing, moving, and rotating. Use Ctrl + A to select all, Escape to clear, and Tab to cycle through overlapping elements. Selected elements show properties in the right panel.',
          position: 'top',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="canvas-container"]',
          title: 'Creating Elements - Bring Ideas to Life',
          description: 'Add elements to your design in multiple ways! Drag from the bottom drawer for instant placement, or click tools then click canvas for precise positioning. Lines need start and end points, polylines use multiple clicks (double-click to finish), and shapes can be sized by dragging. Use the grid for alignment and zoom in for precision!',
          position: 'top',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
      ],
    },

    // Layer panel tutorial
    'layer-panel': {
      id: 'layer-panel',
      name: 'Project Progress',
      description: 'Learn to track and organize your project progress',
      category: 'feature',
      steps: [
        {
          element: '[data-tutorial="project-progress-overview"]',
          title: 'Project Progress Overview',
          description: 'This section shows your overall project completion with a progress bar and statistics. Track how many steps you\'ve completed across all modules. The percentage gives you a quick overview of your project status.',
          position: 'right',
          showProgress: true,
          showButtons: ['next', 'close'],
        },
        {
          element: '[data-tutorial="module-tabs"]',
          title: 'Module Progress Tracking',
          description: 'These tabs represent major phases of your project (Base, Electrical, Plumbing). Click on different modules to switch your focus and see related elements. Completed modules show a green checkmark, and the next suggested module pulses to guide your workflow.',
          position: 'right',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="step-list"]',
          title: 'Step Progress Management',
          description: 'Within each module, these steps break down the work into manageable tasks. Check off completed steps using the checkboxes. Click on any step to make it active and see its layers. The "Next" badge shows your recommended next task.',
          position: 'right',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="z-levels"]',
          title: 'Layer Depth Control',
          description: 'Z-levels control which elements appear in front of others. Lower numbers appear behind higher numbers. This is crucial for layering different systems (like putting electrical over base plans). Use the eye icons to show/hide layers and organize your design elements.',
          position: 'right',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
      ],
    },

    // Drawing tools tutorial
    'drawing-tools': {
      id: 'drawing-tools',
      name: 'Drawing Tools',
      description: 'Learn to use various drawing tools to create elements',
      category: 'feature',
      steps: [
        {
          element: '[data-tutorial="basic-shapes"]',
          title: 'Basic Shapes - Essential Building Blocks',
          description: 'These are the fundamental shapes for your designs: rectangles for rooms and furniture, circles for round tables and fixtures. These basic shapes are always visible in the collapsed drawer for quick access. Drag directly to canvas or click tool then canvas for precision placement.',
          position: 'top',
          showProgress: true,
          showButtons: ['next', 'close'],
        },
        {
          element: '[data-tutorial="path-tools"]',
          title: 'Basic Paths - Lines and Drawing Tools',
          description: 'Create custom paths and complex shapes! Line Tool: click start, then end point (hold Shift for straight lines). Polyline Tool: click multiple points, double-click to finish. Curve Tools: create smooth curves with control points. Arc Tool: perfect for rounded corners. These tools let you draw precise custom elements.',
          position: 'top',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="image-elements"]',
          title: 'Image Elements - Visual Enhancement',
          description: 'Add visual elements to enhance your designs! You can drag photos, reference images, or decorative graphics directly to the canvas. Images can be resized, positioned, and layered with other elements. Perfect for adding context, textures, or visual references to your architectural plans. Note: This section is only visible when the drawer is expanded.',
          position: 'top',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="text-tool"]',
          title: 'Text Tool - Labels and Annotations',
          description: 'Add clear communication to your designs! The text tool is visible in the collapsed drawer. Drag to canvas or click tool then canvas for precise placement. Start typing immediately when text is placed. Perfect for room labels, dimensions, notes, and titles.',
          position: 'top',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="drawer-expand"]',
          title: 'Expand Drawer - Access More Tools',
          description: 'Click this expand button to reveal additional tools and elements! The expanded drawer contains image elements, advanced shapes, measurement tools, and specialized architectural components. Expand to access the full toolkit.',
          position: 'top',
          showProgress: true,
          showButtons: ['previous', 'close'],
        },
      ],
    },

    // Property editing tutorial
    'property-editing': {
      id: 'property-editing',
      name: 'Property Editing',
      description: 'Learn how to edit element properties',
      category: 'feature',
      prerequisites: ['app-overview'],
      steps: [
        {
          element: '[data-tutorial="geometry-tab"]',
          title: 'Geometry Properties',
          description: 'Adjust element position, size, rotation, and other geometric properties. Use these controls to precisely position and scale your elements.',
          position: 'left',
          showProgress: true,
          showButtons: ['next', 'close'],
        },
        {
          element: '[data-tutorial="appearance-tab"]',
          title: 'Appearance Properties',
          description: 'Modify element colors, borders, transparency, and other visual properties. Customize the look and feel of your design elements.',
          position: 'left',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="drawer-expand"]',
          title: 'More Elements - Expand for Advanced Tools',
          description: 'Click this expand button to access more elements! The expanded drawer reveals additional architectural symbols, measurement tools, dimension lines, and specialized shapes. Each element type has unique properties designed for specific design tasks.',
          position: 'left',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="identity-tab"]',
          title: 'Identity Information',
          description: 'Set element names, categories, and layer information. This helps organize and manage your design elements effectively.',
          position: 'left',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="special-elements"]',
          title: 'Special Elements - Advanced Components',
          description: 'These are specialized elements for your current project module! They include furniture, fixtures, appliances, and other architectural components specific to your workflow. Drag these elements to add professional-grade components to your design.',
          position: 'right',
          showProgress: true,
          showButtons: ['previous', 'next', 'close'],
        },
        {
          element: '[data-tutorial="cost-calculation"]',
          title: 'Cost Calculation',
          description: 'Configure cost calculation parameters and view calculation results. Track project costs and material requirements.',
          position: 'left',
          showProgress: true,
          showButtons: ['previous', 'close'],
        },
      ],
    },
  },

  defaults: {
    animationDuration: 300,
    showProgress: true,
    allowClose: true,
    overlayOpacity: 0.75,
  },

  completion: {
    storageKey: 'renopilot-tutorial-completion',
    enabled: true,
  },
}
