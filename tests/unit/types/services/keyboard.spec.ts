import type { KeyboardService, KeyEventData, KeyModifiers } from '@/types/services/keyboard'
import { describe, expect, it } from 'vitest'

describe('keyboard Service Types', () => {
  describe('keyModifiers', () => {
    it('should define all required modifier properties', () => {
      const modifiers: KeyModifiers = {
        altKey: false,
        ctrlKey: true,
        shiftKey: false,
        metaKey: true,
      }

      expect(modifiers).toHaveProperty('altKey')
      expect(modifiers).toHaveProperty('ctrlKey')
      expect(modifiers).toHaveProperty('shiftKey')
      expect(modifiers).toHaveProperty('metaKey')

      expect(typeof modifiers.altKey).toBe('boolean')
      expect(typeof modifiers.ctrlKey).toBe('boolean')
      expect(typeof modifiers.shiftKey).toBe('boolean')
      expect(typeof modifiers.metaKey).toBe('boolean')
    })
  })

  describe('keyEventData', () => {
    it('should define all required properties', () => {
      const keyEventData: KeyEventData = {
        key: 'a',
        code: 'KeyA',
        modifiers: {
          altKey: false,
          ctrlKey: false,
          shiftKey: false,
          metaKey: false,
        },
      }

      expect(keyEventData).toHaveProperty('key')
      expect(keyEventData).toHaveProperty('code')
      expect(keyEventData).toHaveProperty('modifiers')

      expect(typeof keyEventData.key).toBe('string')
      expect(typeof keyEventData.code).toBe('string')
      expect(typeof keyEventData.modifiers).toBe('object')
    })

    it('should allow optional originalEvent property', () => {
      // Mock KeyboardEvent
      const mockEvent = new KeyboardEvent('keydown', {
        key: 'a',
        code: 'KeyA',
      })

      const keyEventData: KeyEventData = {
        key: 'a',
        code: 'KeyA',
        modifiers: {
          altKey: false,
          ctrlKey: false,
          shiftKey: false,
          metaKey: false,
        },
        originalEvent: mockEvent,
      }

      expect(keyEventData).toHaveProperty('originalEvent')
      expect(keyEventData.originalEvent).toBeInstanceOf(KeyboardEvent)
    })
  })

  describe('keyboardService', () => {
    it('should define all required methods', () => {
      // Create a mock implementation of KeyboardService
      const keyboardService: KeyboardService = {
        initialize: vi.fn(),
        cleanup: vi.fn(),
        registerKeyBinding: vi.fn(),
        unregisterKeyBinding: vi.fn(),
        getKeyBindings: vi.fn(),
        addKeyDownListener: vi.fn(),
        addKeyUpListener: vi.fn(),
      }

      expect(keyboardService).toHaveProperty('initialize')
      expect(keyboardService).toHaveProperty('cleanup')
      expect(keyboardService).toHaveProperty('registerKeyBinding')
      expect(keyboardService).toHaveProperty('unregisterKeyBinding')
      expect(keyboardService).toHaveProperty('getKeyBindings')
      expect(keyboardService).toHaveProperty('addKeyDownListener')
      expect(keyboardService).toHaveProperty('addKeyUpListener')

      expect(typeof keyboardService.initialize).toBe('function')
      expect(typeof keyboardService.cleanup).toBe('function')
      expect(typeof keyboardService.registerKeyBinding).toBe('function')
      expect(typeof keyboardService.unregisterKeyBinding).toBe('function')
      expect(typeof keyboardService.getKeyBindings).toBe('function')
      expect(typeof keyboardService.addKeyDownListener).toBe('function')
      expect(typeof keyboardService.addKeyUpListener).toBe('function')
    })

    it('should handle key binding registration with options', () => {
      // Create a mock implementation
      const registerKeyBindingSpy = vi.fn().mockReturnValue(() => {})

      const keyboardService: KeyboardService = {
        initialize: vi.fn(),
        cleanup: vi.fn(),
        registerKeyBinding: registerKeyBindingSpy,
        unregisterKeyBinding: vi.fn(),
        getKeyBindings: vi.fn(),
        addKeyDownListener: vi.fn(),
        addKeyUpListener: vi.fn(),
      }

      // Call with different options
      const unregister1 = keyboardService.registerKeyBinding('a', () => {})
      const unregister2 = keyboardService.registerKeyBinding('Ctrl+S', () => {}, {
        preventDefault: true,
        stopPropagation: true,
      })
      const unregister3 = keyboardService.registerKeyBinding('Shift+Delete', () => {}, {
        onKeyUp: true,
        description: 'Delete selected item',
      })

      // Verify calls
      expect(registerKeyBindingSpy).toHaveBeenCalledTimes(3)

      // Check first call
      const firstCall = registerKeyBindingSpy.mock.calls[0]
      expect(firstCall[0]).toBe('a')
      expect(typeof firstCall[1]).toBe('function')

      // Check second call
      const secondCall = registerKeyBindingSpy.mock.calls[1]
      expect(secondCall[0]).toBe('Ctrl+S')
      expect(typeof secondCall[1]).toBe('function')
      expect(secondCall[2]).toEqual({
        preventDefault: true,
        stopPropagation: true,
      })

      // Check third call
      const thirdCall = registerKeyBindingSpy.mock.calls[2]
      expect(thirdCall[0]).toBe('Shift+Delete')
      expect(typeof thirdCall[1]).toBe('function')
      expect(thirdCall[2]).toEqual({
        onKeyUp: true,
        description: 'Delete selected item',
      })

      // Verify return values
      expect(typeof unregister1).toBe('function')
      expect(typeof unregister2).toBe('function')
      expect(typeof unregister3).toBe('function')
    })

    it('should handle key binding map retrieval', () => {
      // Create a mock binding map
      const mockBindings = new Map()
      mockBindings.set('a', {
        callback: () => {},
        options: {
          preventDefault: false,
          stopPropagation: false,
          onKeyUp: false,
        },
      })
      mockBindings.set('Ctrl+S', {
        callback: () => {},
        options: {
          preventDefault: true,
          stopPropagation: true,
          onKeyUp: false,
          description: 'Save',
        },
      })

      // Create a mock implementation
      const getKeyBindingsSpy = vi.fn().mockReturnValue(mockBindings)

      const keyboardService: KeyboardService = {
        initialize: vi.fn(),
        cleanup: vi.fn(),
        registerKeyBinding: vi.fn(),
        unregisterKeyBinding: vi.fn(),
        getKeyBindings: getKeyBindingsSpy,
        addKeyDownListener: vi.fn(),
        addKeyUpListener: vi.fn(),
      }

      // Get bindings
      const bindings = keyboardService.getKeyBindings()

      // Verify
      expect(getKeyBindingsSpy).toHaveBeenCalledTimes(1)
      expect(bindings).toBe(mockBindings)
      expect(bindings.size).toBe(2)
      expect(bindings.has('a')).toBe(true)
      expect(bindings.has('Ctrl+S')).toBe(true)

      const aBinding = bindings.get('a')
      expect(aBinding).toHaveProperty('callback')
      expect(aBinding).toHaveProperty('options')
      expect(aBinding!.options.preventDefault).toBe(false)

      const ctrlSBinding = bindings.get('Ctrl+S')
      expect(ctrlSBinding).toHaveProperty('callback')
      expect(ctrlSBinding).toHaveProperty('options')
      expect(ctrlSBinding!.options.preventDefault).toBe(true)
      expect(ctrlSBinding!.options.description).toBe('Save')
    })
  })
})
