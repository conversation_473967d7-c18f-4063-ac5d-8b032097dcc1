import type { ShapeElement } from '@/types/core'
import jsPDF from 'jspdf'
import { svgRenderersMap } from './render'

// Canvas size and position Define
const WIDTH: number = 2000
const HEIGHT: number = 1125
const START_X: number = -400
const START_Y: number = -300

/**
 * Export elements as SVG string
 * @param elements All shapes elements on the current canvas
 * @returns SVG string
 */
async function toSvgString(elements: ShapeElement[]): Promise<string> {
  const svgHeader
    = `<svg xmlns="http://www.w3.org/2000/svg" width="${WIDTH}" height="${HEIGHT}"`
      + ` viewBox="${START_X} ${START_Y} ${WIDTH} ${HEIGHT}">`
  const svgFooter = `</svg>`

  const elementToSvg = async (e: ShapeElement): Promise<string> => {
    const key = e.type as keyof typeof svgRenderersMap
    if (key in svgRenderersMap) {
      const render = svgRenderersMap[key]
      return render(e as any)
    }
    return ''
  }

  const svgElements = await Promise.all(elements.map(elementToSvg))
  const svgContent = svgElements.join('\n')

  // Check if arrow marker is needed
  const needArrowMarker = svgContent.includes('url(#arrowhead)')
  const markerDefs = needArrowMarker
    ? `<defs>
  <marker id="arrowhead" markerWidth="10" markerHeight="7"
          refX="10" refY="3.5" orient="auto" markerUnits="strokeWidth">
    <polygon points="0 0, 10 3.5, 0 7" fill="currentColor" />
  </marker>
</defs>\n`
    : ''

  return `${svgHeader}\n${markerDefs}${svgContent}\n${svgFooter}`
}

/**
 * Convert elements to SVG Blob
 * @param elements All shape elements on the current canvas
 * @returns SVG Blob
 */
export async function toSvgBlob(elements: ShapeElement[]): Promise<Blob> {
  const svgContent = await toSvgString(elements)
  return new Blob([svgContent], { type: 'image/svg+xml' })
}

/**
 * Convert elements to PNG Blob asynchronously
 * @param elements All shape elements on the current canvas
 * @returns Promise<Blob> PNG Blob
 */
export async function toPngBlob(elements: ShapeElement[]): Promise<Blob> {
  // Generate SVG string from elements
  const svgString = await toSvgString(elements)
  const img = new window.Image()
  const svgDataUrl = `data:image/svg+xml;charset=utf-8,${
    encodeURIComponent(svgString)}`

  // Wait for the image to load SVG data
  await new Promise<void>((resolve, reject) => {
    img.onload = () => resolve()
    img.onerror = () => reject(new Error('Image load error'))
    img.src = svgDataUrl
  })

  // Draw the loaded SVG image onto a canvas
  const canvas = document.createElement('canvas')
  canvas.width = WIDTH
  canvas.height = HEIGHT
  const ctx = canvas.getContext('2d')
  if (!ctx) {
    throw new Error('Canvas 2D context not available')
  }
  ctx.fillStyle = '#fff'
  ctx.fillRect(0, 0, WIDTH, HEIGHT) // Fill with white background
  ctx.drawImage(img, 0, 0, WIDTH, HEIGHT)

  // Export the canvas content as a PNG Blob
  return new Promise<Blob>((resolve, reject) => {
    canvas.toBlob((blob) => {
      if (blob)
        resolve(blob)
      else reject(new Error('Failed to create PNG blob'))
    }, 'image/png')
  })
}

/**
 * Convert elements to PDF Blob asynchronously by first rendering to PNG,
 * then embedding the PNG into a PDF document.
 *
 * @param elements All shape elements on the current canvas
 * @returns Promise<Blob> PDF Blob containing the rendered image
 */
export async function toPdfBlob(elements: ShapeElement[]): Promise<Blob> {
  // Generate PNG Blob from elements
  const pngBlob = await toPngBlob(elements)

  // Convert the PNG Blob to a DataURL
  const dataUrl = await new Promise<string>((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = reject
    reader.readAsDataURL(pngBlob)
  })

  // Create a PDF document and embed the PNG image
  const pdf = new jsPDF({
    orientation: WIDTH > HEIGHT ? 'landscape' : 'portrait',
    unit: 'pt',
    format: [WIDTH, HEIGHT],
  })
  pdf.addImage(dataUrl, 'PNG', 0, 0, WIDTH, HEIGHT)

  return pdf.output('blob')
}
