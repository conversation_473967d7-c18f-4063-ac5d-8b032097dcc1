import { beforeEach, describe, expect, it, vi } from 'vitest'
import { CubicPerimeterStrategy } from '@/core/compute/strategies/perimeter/CubicPerimeterStrategy'
import { CoreError } from '@/services/system/error-service'
import { ElementType } from '@/types/core/elementDefinitions'

// Mock the utility functions
vi.mock('@/lib/utils/geometry/bezierUtils', () => ({
  calculateCubicBezierLengthGauss: vi.fn((p0, p1, p2, p3) => {
    // Simple mock implementation: approximate length based on control points
    const dx = p3.x - p0.x
    const dy = p3.y - p0.y
    return Math.sqrt(dx * dx + dy * dy) * 1.5 // Rough approximation
  }),
}))

vi.mock('@/lib/utils/geometry/pointUtils', () => ({
  calculateDistance: vi.fn((p1, p2) => {
    const dx = p2.x - p1.x
    const dy = p2.y - p1.y
    return Math.sqrt(dx * dx + dy * dy)
  }),
}))

describe('cubicPerimeterStrategy', () => {
  let strategy: CubicPerimeterStrategy

  beforeEach(() => {
    strategy = new CubicPerimeterStrategy()
    vi.clearAllMocks()
  })

  describe('constructor and Basic Properties', () => {
    it('should be defined and instantiated', () => {
      expect(strategy).toBeDefined()
      expect(strategy).toBeInstanceOf(CubicPerimeterStrategy)
    })

    it('should return correct element type', () => {
      expect(strategy.getElementType()).toBe(ElementType.CUBIC)
    })

    it('should have required methods', () => {
      expect(typeof strategy.calculatePerimeter).toBe('function')
      expect(typeof strategy.getElementType).toBe('function')
    })
  })

  describe('perimeter Calculation', () => {
    it('should calculate perimeter for open cubic curve correctly', () => {
      const cubicElement = {
        id: 'cubic-1',
        type: ElementType.CUBIC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(cubicElement as any)

      // Expected: curve length only = sqrt(20^2 + 0^2) * 1.5 = 30
      expect(perimeter).toBe(30)
    })

    it('should calculate perimeter for closed cubic curve correctly', () => {
      const cubicElement = {
        id: 'cubic-2',
        type: ElementType.CUBIC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      const perimeter = strategy.calculatePerimeter(cubicElement as any)

      // Expected: curve length + closing distance = 30 + 20 = 50
      expect(perimeter).toBe(50)
    })

    it('should handle missing closed property (defaults to false)', () => {
      const cubicElement = {
        id: 'cubic-no-closed',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          // closed property missing
        },
      }

      const perimeter = strategy.calculatePerimeter(cubicElement as any)

      // Should be treated as open curve
      expect(perimeter).toBe(30)
    })

    it('should call bezier length calculation with correct parameters', () => {
      const { calculateCubicBezierLengthGauss } = require('@/lib/utils/geometry/bezierUtils')

      const cubicElement = {
        id: 'cubic-test',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      strategy.calculatePerimeter(cubicElement as any)

      expect(calculateCubicBezierLengthGauss).toHaveBeenCalledWith(
        { x: 0, y: 0 },
        { x: 5, y: -5 },
        { x: 15, y: -5 },
        { x: 20, y: 0 },
      )
    })

    it('should call distance calculation for closed curve', () => {
      const { calculateDistance } = require('@/lib/utils/geometry/pointUtils')

      const cubicElement = {
        id: 'cubic-closed-test',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      strategy.calculatePerimeter(cubicElement as any)

      expect(calculateDistance).toHaveBeenCalledWith(
        { x: 0, y: 0 },
        { x: 20, y: 0 },
      )
    })
  })

  describe('error Handling', () => {
    it('should throw error for non-cubic element', () => {
      const rectangleElement = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        properties: {},
      }

      expect(() => strategy.calculatePerimeter(rectangleElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculatePerimeter(rectangleElement as any))
        .toThrow('CubicPerimeterStrategy can only calculate perimeter for CUBIC elements')
    })

    it('should throw error for missing start point', () => {
      const cubicElement = {
        id: 'cubic-no-start',
        type: ElementType.CUBIC,
        properties: {
          start: null,
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(cubicElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculatePerimeter(cubicElement as any))
        .toThrow('Invalid cubic curve points')
    })

    it('should throw error for invalid start point coordinates', () => {
      const cubicElement = {
        id: 'cubic-invalid-start',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 'invalid', y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(cubicElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for missing control1 point', () => {
      const cubicElement = {
        id: 'cubic-no-control1',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: undefined,
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(cubicElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for invalid control2 point coordinates', () => {
      const cubicElement = {
        id: 'cubic-invalid-control2',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: Number.NaN },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(cubicElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for missing end point', () => {
      const cubicElement = {
        id: 'cubic-no-end',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: null,
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(cubicElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for invalid end point coordinates', () => {
      const cubicElement = {
        id: 'cubic-invalid-end',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: Infinity, y: 0 },
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(cubicElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for points with missing coordinates', () => {
      const cubicElement = {
        id: 'cubic-missing-coords',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0 }, // missing y
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(cubicElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for infinite coordinates', () => {
      const cubicElement = {
        id: 'cubic-infinite-coords',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: Infinity, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(cubicElement as any))
        .toThrow(CoreError)
    })
  })

  describe('edge Cases', () => {
    it('should handle points with zero coordinates', () => {
      const cubicElement = {
        id: 'cubic-zero-coords',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 0, y: 0 },
          control2: { x: 0, y: 0 },
          end: { x: 0, y: 0 },
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(cubicElement as any)
      expect(perimeter).toBe(0) // All points are the same
    })

    it('should handle negative coordinates', () => {
      const cubicElement = {
        id: 'cubic-negative-coords',
        type: ElementType.CUBIC,
        properties: {
          start: { x: -10, y: -10 },
          control1: { x: -5, y: -15 },
          control2: { x: 5, y: -15 },
          end: { x: 10, y: -10 },
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(cubicElement as any)
      expect(perimeter).toBeGreaterThan(0)
    })

    it('should handle very large coordinates', () => {
      const cubicElement = {
        id: 'cubic-large-coords',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 1000000, y: 1000000 },
          control1: { x: 1000005, y: 999995 },
          control2: { x: 1000015, y: 999995 },
          end: { x: 1000020, y: 1000000 },
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(cubicElement as any)
      expect(perimeter).toBeGreaterThan(0)
      expect(Number.isFinite(perimeter)).toBe(true)
    })

    it('should handle very small coordinates', () => {
      const cubicElement = {
        id: 'cubic-small-coords',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0.001, y: 0.001 },
          control1: { x: 0.005, y: -0.004 },
          control2: { x: 0.015, y: -0.004 },
          end: { x: 0.020, y: 0.001 },
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(cubicElement as any)
      expect(perimeter).toBeGreaterThan(0)
      expect(perimeter).toBeLessThan(1)
    })

    it('should handle collinear points', () => {
      const cubicElement = {
        id: 'cubic-collinear',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: 0 },
          control2: { x: 15, y: 0 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(cubicElement as any)
      expect(perimeter).toBe(30) // Should approximate to straight line
    })

    it('should handle closed curve with same start and end points', () => {
      const cubicElement = {
        id: 'cubic-same-endpoints',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: 10 },
          control2: { x: -5, y: 10 },
          end: { x: 0, y: 0 },
          closed: true,
        },
      }

      const perimeter = strategy.calculatePerimeter(cubicElement as any)
      expect(perimeter).toBe(0) // Curve length + 0 closing distance
    })
  })
})
