/**
 * Types related to the Layer Panel structure and state.
 */
import type { MajorCategory, MinorCategory } from './majorMinorTypes'

export enum TaskStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  LOCKED = 'locked',
}

// Z-axis level
export interface ZLevel {
  id: string
  name: string
  active: boolean
  zIndex: number
}

// Step within a module
export interface TaskStep {
  id: MinorCategory
  name: string
  status: TaskStatus
  order: number // Step order within the module
  targetMajorCategory: MajorCategory // For linking to elements
  targetMinorCategory: MinorCategory // For linking to elements
  zLevels: ZLevel[]
}

// Main module definition (corresponds to MajorCategory)
export interface TaskModule {
  id: MajorCategory // Module ID is the MajorCategory enum value
  name: string // Display name (e.g., "Floor", "Ceiling")
  status: TaskStatus
  steps: TaskStep[]
  progress: number // Completion percentage (0-100)
  order: number // Module display order
}
