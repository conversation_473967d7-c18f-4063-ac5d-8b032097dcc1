/**
 * UI Event Publishers Module
 *
 * This module provides helper functions for publishing UI-related events
 * to the application's event bus. It covers view operations (pan, zoom),
 * tool changes, notifications, toasts, and modal dialogs.
 *
 * All functions follow dependency injection principles by accepting an EventBus
 * instance as their first parameter.
 *
 * @module services/core/event-bus/helpers/publishers/uiPublishers
 */

import type { EventBus } from '@/types/services/events'
import type {
  NotificationAddEvent,
  ToastShowEvent,
  ToolChangeEvent,
} from '@/types/services/events/uiEvents' // Corrected to camelCase based on user feedback
import type { ViewZoomedEvent } from '@/types/services/events/viewEvents' // Corrected to camelCase
import { AppEventType } from '@/types/services/events'

// --- View Events ---

/**
 * Publish view zoomed event
 *
 * Notifies the system that the view zoom level has changed.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param scale - New zoom scale factor
 */
export function publishViewZoomed(eventBus: EventBus, scale: number): void {
  const event: ViewZoomedEvent = {
    type: AppEventType.ViewZoomed,
    payload: { scale },
  }
  eventBus.publish(event)
}

/**
 * Publish view panned event
 *
 * Notifies the system that the view has been panned to a new position.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param options - Object containing the new view center coordinates
 * @param options.x - The x coordinate of the new view center
 * @param options.y - The y coordinate of the new view center
 */
export function publishViewPanned(eventBus: EventBus, options: { x: number, y: number }): void {
  eventBus.publish({
    type: AppEventType.ViewPanned,
    payload: { x: options.x, y: options.y },
  })
}

// --- Tool Events ---

/**
 * Publish tool changed event
 *
 * Notifies the system that the currently active tool has changed.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param tool - Identifier of the newly selected tool
 */
export function publishToolChanged(eventBus: EventBus, tool: string): void {
  const event: ToolChangeEvent = {
    type: AppEventType.ToolChanged,
    payload: { tool },
  }
  eventBus.publish(event)
}

// --- Notification Events ---

/**
 * Publish toast notification event
 *
 * Displays a temporary toast notification to the user.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param message - The message to display
 * @param type - The type of toast (info, success, warning, error)
 * @param duration - How long the toast should be displayed (ms)
 */
export function publishToastNotification(
  eventBus: EventBus,
  message: string,
  type: 'info' | 'success' | 'warning' | 'error' = 'info',
  duration: number = 3000,
): void {
  const event: ToastShowEvent = {
    type: AppEventType.ToastShow,
    payload: { message, type, duration },
  }
  eventBus.publish(event)
}

/**
 * Publish notification event
 *
 * Adds a notification to the notification center.
 *
 * @param eventBus - The EventBus instance to use for publishing
 * @param message - The notification message
 * @param type - The type of notification (info, success, warning, error)
 */
export function publishNotification(
  eventBus: EventBus,
  message: string,
  type: 'info' | 'success' | 'warning' | 'error' = 'info',
): void {
  const event: NotificationAddEvent = {
    type: AppEventType.NotificationAdd,
    payload: { message, type },
  }
  eventBus.publish(event)
}
