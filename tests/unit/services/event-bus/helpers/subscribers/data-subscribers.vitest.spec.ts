import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  subscribeToDataLoadEvents,
  subscribeToExportEvents,
  subscribeToHistoryEvents,
  subscribeToTemplateApplyEvents,
} from '@/services/event-bus/helpers/subscribers/data-subscribers'
import * as utils from '@/services/event-bus/helpers/subscribers/utils'
import { AppEventType } from '@/types/services/events'

describe('data Subscribers', () => {
  let mockEventBus: any
  let mockHandler: any
  let mockOptions: any
  let typedSubscribeSpy: any

  beforeEach(() => {
    mockEventBus = {
      subscribe: vi.fn().mockReturnValue(() => {}),
    }
    mockHandler = vi.fn()
    mockOptions = { once: true }

    // Spy on the typedSubscribe function
    typedSubscribeSpy = vi.spyOn(utils, 'typedSubscribe').mockReturnValue(() => {})
  })

  describe('subscribeToHistoryEvents', () => {
    it('should call typedSubscribe with correct parameters for undo action', () => {
      const unsubscribe = subscribeToHistoryEvents(
        mockEventBus,
        mockHandler,
        'undo',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.HISTORY_UNDO,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for redo action', () => {
      const unsubscribe = subscribeToHistoryEvents(
        mockEventBus,
        mockHandler,
        'redo',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.HISTORY_REDO,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for checkpoint action', () => {
      const unsubscribe = subscribeToHistoryEvents(
        mockEventBus,
        mockHandler,
        'checkpoint',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.HISTORY_CHECKPOINT,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for error action', () => {
      const unsubscribe = subscribeToHistoryEvents(
        mockEventBus,
        mockHandler,
        'error',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.HISTORY_ERROR,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should throw error for invalid history action', () => {
      expect(() => subscribeToHistoryEvents(
        mockEventBus,
        mockHandler,
        'invalid' as any,
        mockOptions,
      )).toThrow('Invalid history action: invalid')
    })
  })

  describe('subscribeToTemplateApplyEvents', () => {
    it('should call typedSubscribe with correct parameters for apply phase', () => {
      const unsubscribe = subscribeToTemplateApplyEvents(
        mockEventBus,
        mockHandler,
        'apply',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.TEMPLATE_APPLY,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for request phase', () => {
      const unsubscribe = subscribeToTemplateApplyEvents(
        mockEventBus,
        mockHandler,
        'request',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.TEMPLATE_APPLY_REQUEST,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for validate phase', () => {
      const unsubscribe = subscribeToTemplateApplyEvents(
        mockEventBus,
        mockHandler,
        'validate',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.TEMPLATE_APPLY_VALIDATE,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for complete phase', () => {
      const unsubscribe = subscribeToTemplateApplyEvents(
        mockEventBus,
        mockHandler,
        'complete',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.TEMPLATE_APPLY_COMPLETE,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for error phase', () => {
      const unsubscribe = subscribeToTemplateApplyEvents(
        mockEventBus,
        mockHandler,
        'error',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.TEMPLATE_APPLY_ERROR,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should throw error for invalid template apply phase', () => {
      expect(() => subscribeToTemplateApplyEvents(
        mockEventBus,
        mockHandler,
        'invalid' as any,
        mockOptions,
      )).toThrow('Invalid template apply phase: invalid')
    })
  })

  describe('subscribeToExportEvents', () => {
    it('should call typedSubscribe with correct parameters for request phase', () => {
      const unsubscribe = subscribeToExportEvents(
        mockEventBus,
        mockHandler,
        'request',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.EXPORT_REQUEST,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for prepare phase', () => {
      const unsubscribe = subscribeToExportEvents(
        mockEventBus,
        mockHandler,
        'prepare',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.EXPORT_PREPARE,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for progress phase', () => {
      const unsubscribe = subscribeToExportEvents(
        mockEventBus,
        mockHandler,
        'progress',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.EXPORT_PROGRESS,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for complete phase', () => {
      const unsubscribe = subscribeToExportEvents(
        mockEventBus,
        mockHandler,
        'complete',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.EXPORT_COMPLETE,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for error phase', () => {
      const unsubscribe = subscribeToExportEvents(
        mockEventBus,
        mockHandler,
        'error',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.EXPORT_ERROR,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should throw error for invalid export phase', () => {
      expect(() => subscribeToExportEvents(
        mockEventBus,
        mockHandler,
        'invalid' as any,
        mockOptions,
      )).toThrow('Invalid export phase: invalid')
    })
  })

  describe('subscribeToDataLoadEvents', () => {
    it('should call typedSubscribe with correct parameters for request phase', () => {
      const unsubscribe = subscribeToDataLoadEvents(
        mockEventBus,
        mockHandler,
        'request',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.DATA_LOAD_REQUEST,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for progress phase', () => {
      const unsubscribe = subscribeToDataLoadEvents(
        mockEventBus,
        mockHandler,
        'progress',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.DATA_LOAD_PROGRESS,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for complete phase', () => {
      const unsubscribe = subscribeToDataLoadEvents(
        mockEventBus,
        mockHandler,
        'complete',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.DATA_LOAD_COMPLETE,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should call typedSubscribe with correct parameters for error phase', () => {
      const unsubscribe = subscribeToDataLoadEvents(
        mockEventBus,
        mockHandler,
        'error',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.DATA_LOAD_ERROR,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should throw error for invalid data load phase', () => {
      expect(() => subscribeToDataLoadEvents(
        mockEventBus,
        mockHandler,
        'invalid' as any,
        mockOptions,
      )).toThrow('Invalid data load phase: invalid')
    })
  })
})
