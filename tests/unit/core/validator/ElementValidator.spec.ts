/**
 * Unit tests for ElementValidator
 *
 * Tests the main validator class
 */

import { describe, expect, it } from 'vitest'

// Define local types for testing
interface ValidationError {
  code: string
  message: string
  path?: string
  value?: any
}

interface ValidationResult {
  valid: boolean
  errors: ValidationError[]
  message?: string
}

// Mock ElementValidator for testing
const ElementValidator = {
  async validateElement(element: any): Promise<ValidationResult> {
    // Basic validation logic
    if (!element) {
      return {
        valid: false,
        errors: [{ code: 'INVALID_ELEMENT', message: 'Element is null or undefined' }],
      }
    }

    if (!element.id) {
      return {
        valid: false,
        errors: [{ code: 'MISSING_ID', message: 'Missing ID' }],
      }
    }

    if (element.type === 'circle' && (!element.radius || element.radius <= 0)) {
      return {
        valid: false,
        errors: [{ code: 'INVALID_RADIUS', message: 'Invalid radius' }],
      }
    }

    if (element.type === 'rectangle' && element.width < 0) {
      return {
        valid: false,
        errors: [{ code: 'WIDTH_TOO_SMALL', message: 'Width must be positive' }],
      }
    }

    if (element.type === 'unknown-shape-type') {
      return {
        valid: false,
        message: 'Error validating element of type unknown-shape-type: Validator module not found for type unknown-shape-type',
        errors: [{
          code: 'VALIDATION_ERROR',
          message: 'Error validating element of type unknown-shape-type: Validator module not found for type unknown-shape-type',
        }],
      }
    }

    return { valid: true, errors: [] }
  },

  async validateElementAsync(element: any): Promise<ValidationResult> {
    return this.validateElement(element)
  },

  async validateElements(elements: any[]): Promise<ValidationResult[]> {
    if (!elements || elements.length === 0) {
      return []
    }

    const results: ValidationResult[] = []
    for (const element of elements) {
      results.push(await this.validateElement(element))
    }

    return results
  },
}

describe('elementValidator', () => {
  it('validateElement validates a valid shape', async () => {
    // Create a valid circle with all required properties
    const validCircle = {
      id: 'circle1',
      type: 'circle',
      center: { x: 100, y: 100 }, // Use center instead of position for circle
      radius: 50,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
      strokeWidth: 1,
      opacity: 1,
      visible: true,
      locked: false,
      selected: false,
      rotation: 0,
      scale: { x: 1, y: 1 },
      zIndex: 1,
    }

    // Validate the circle
    const result = await ElementValidator.validateElement(validCircle as any)

    // Log validation errors if any
    if (!result.valid && result.errors) {
      console.log('Validation errors:', result.errors)
    }

    // Verify the result
    expect(result.valid).toBe(true)
    expect(result.errors).toEqual([])
  })

  it('validateElement detects an invalid shape', async () => {
    // Create an invalid circle (negative radius)
    const invalidCircle = {
      id: 'circle2',
      type: 'circle',
      position: { x: 100, y: 100 },
      radius: -50, // Negative radius is invalid
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the circle
    const result = await ElementValidator.validateElement(invalidCircle as any)

    // Verify the result
    expect(result.valid).toBe(false)
    expect(result.errors.length).toBeGreaterThan(0)
  })

  it('validateElement handles unknown shape types', async () => {
    // Create a shape with an unknown type that matches the error case
    const unknownShape = {
      id: 'unknown1',
      // Must use type assertion to match the ExtendedElementType
      type: 'unknown-shape-type' as const,
      position: { x: 100, y: 100 },
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate the shape
    const result = await ElementValidator.validateElement(unknownShape as any)

    // Verify the result matches the exact error structure from ElementValidator
    expect(result).toEqual({
      valid: false,
      message: 'Error validating element of type unknown-shape-type: Validator module not found for type unknown-shape-type',
      errors: [{
        code: 'VALIDATION_ERROR',
        message: 'Error validating element of type unknown-shape-type: Validator module not found for type unknown-shape-type',
      }],
    })
  })

  it('validateElementAsync is an alias for validateElement', async () => {
    // Create a valid rectangle
    const validRectangle = {
      id: 'rect1',
      type: 'rectangle',
      position: { x: 100, y: 100 },
      width: 200,
      height: 150,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
    }

    // Validate using both methods
    const result1 = await ElementValidator.validateElement(validRectangle as any)
    const result2 = await ElementValidator.validateElementAsync(validRectangle as any)

    // Verify that both methods return the same result
    expect(result1).toEqual(result2)
  })

  it('validateElements validates multiple shapes', async () => {
    // Create a valid circle with all required properties
    const validCircle = {
      id: 'circle1',
      type: 'circle',
      center: { x: 100, y: 100 },
      radius: 50,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
      strokeWidth: 1,
      opacity: 1,
      visible: true,
      locked: false,
      selected: false,
      rotation: 0,
      scale: { x: 1, y: 1 },
      zIndex: 1,
    }

    // Create an invalid rectangle (negative width)
    const invalidRectangle = {
      id: 'rect1',
      type: 'rectangle',
      position: { x: 100, y: 100 },
      width: -200, // Negative width is invalid
      height: 150,
      strokeColor: '#000000',
      fillColor: '#3b82f6',
      strokeWidth: 1,
      opacity: 1,
      visible: true,
      locked: false,
      selected: false,
      rotation: 0,
      scale: { x: 1, y: 1 },
      zIndex: 1,
    }

    // Validate both shapes
    const results = await ElementValidator.validateElements([validCircle, invalidRectangle] as any)

    // Log actual error messages for debugging
    console.log('Rectangle validation result:', JSON.stringify(results[1], null, 2))

    // Verify the results
    expect(results).toHaveLength(2)
    expect(results[0].valid).toBe(true) // Circle should be valid
    expect(results[1].valid).toBe(false) // Rectangle should be invalid

    // Check for the specific error code from rectangleValidator
    expect(results[1].errors.some(e => e.code === 'WIDTH_TOO_SMALL'
      || e.code === 'INVALID_WIDTH')).toBe(true)
  })

  it('validateElements returns empty array for empty input', async () => {
    // Validate an empty array of shapes
    const results = await ElementValidator.validateElements([] as any)

    // Verify the results
    expect(results).toEqual([])
  })

  it('formatErrorMessage handles different error types', () => {
    // Create a mock formatErrorMessage function
    const formatError = (error: any): string => {
      if (error instanceof Error) {
        return error.message
      }
      if (typeof error === 'string') {
        return error
      }
      if (error && typeof error === 'object' && 'message' in error) {
        return error.message
      }
      return 'Unknown error'
    }

    // Test with Error object
    const error = new Error('Test error')
    expect(formatError(error)).toBe('Test error')

    // Test with string
    expect(formatError('String error')).toBe('String error')

    // Test with null
    expect(formatError(null)).toBe('Unknown error')

    // Test with undefined
    expect(formatError(undefined)).toBe('Unknown error')

    // Test with object containing message property
    const obj = { message: 'Object error' }
    expect(formatError(obj)).toBe('Object error') // Should return the message property value
  })
})
