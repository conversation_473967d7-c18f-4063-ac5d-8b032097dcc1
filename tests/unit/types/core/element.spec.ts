import type {
  Element,
  MetadataProperties,
  ShapeElement,
} from '@/types/core/elementDefinitions'
import { describe, expect, it } from 'vitest'
import {
  ElementType,
  getElementCategory,
  isElementType,
  isInteriorDesignType,
  isPathType,
  isTextType,
} from '@/types/core/elementDefinitions'

describe('elementType Enum', () => {
  it('should define all expected element types', () => {
    // Basic shapes
    expect(ElementType.RECTANGLE).toBe('RECTANGLE')
    expect(ElementType.SQUARE).toBe('SQUARE')
    expect(ElementType.ELLIPSE).toBe('ELLIPSE')
    expect(ElementType.CIRCLE).toBe('CIRCLE')

    // Polygons
    expect(ElementType.POLYGON).toBe('POLYGON')
    expect(ElementType.TRIANGLE).toBe('TRIANGLE')
    expect(ElementType.QUADRILATERAL).toBe('QUADRILATERAL')
    expect(ElementType.PENTAGON).toBe('PENTAGON')
    expect(ElementType.HEXAGON).toBe('HEXAGON')
    expect(ElementType.HEPTAGON).toBe('HEPTAGON')
    expect(ElementType.OCTAGON).toBe('OCTAGON')
    expect(ElementType.NONAGON).toBe('NONAGON')
    expect(ElementType.DECAGON).toBe('DECAGON')

    // Paths
    expect(ElementType.LINE).toBe('LINE')
    expect(ElementType.PATH).toBe('PATH')
    expect(ElementType.POLYLINE).toBe('POLYLINE')
    expect(ElementType.ARC).toBe('ARC')
    expect(ElementType.QUADRATIC).toBe('QUADRATIC')
    expect(ElementType.CUBIC).toBe('CUBIC')

    // Text
    expect(ElementType.TEXT_LABEL).toBe('TEXT_LABEL')

    // Interior design elements
    expect(ElementType.WALL).toBe('WALL')
    expect(ElementType.DOOR).toBe('DOOR')
    expect(ElementType.WINDOW).toBe('WINDOW')
    expect(ElementType.FURNITURE).toBe('FURNITURE')
    expect(ElementType.FIXTURE).toBe('FIXTURE')
    expect(ElementType.ROOM).toBe('ROOM')
  })
})

describe('element Category Functions', () => {
  it('should correctly categorize element types', () => {
    // Test basic shapes
    expect(getElementCategory(ElementType.RECTANGLE)).toBe('BASIC_SHAPES')
    expect(getElementCategory(ElementType.CIRCLE)).toBe('BASIC_SHAPES')

    // Test polygons
    expect(getElementCategory(ElementType.TRIANGLE)).toBe('POLYGONS')
    expect(getElementCategory(ElementType.HEXAGON)).toBe('POLYGONS')

    // Test paths
    expect(getElementCategory(ElementType.LINE)).toBe('PATHS')
    expect(getElementCategory(ElementType.CUBIC)).toBe('PATHS')

    // Test text
    expect(getElementCategory(ElementType.TEXT_LABEL)).toBe('TEXT')

    // Test interior design
    expect(getElementCategory(ElementType.WALL)).toBe('INTERIOR_DESIGN')
    expect(getElementCategory(ElementType.FURNITURE)).toBe('INTERIOR_DESIGN')
  })

  it('should correctly identify element types', () => {
    // Test basic shapes and polygons
    expect(isElementType(ElementType.RECTANGLE)).toBe(true)
    expect(isElementType(ElementType.CIRCLE)).toBe(true)
    expect(isElementType(ElementType.TRIANGLE)).toBe(true)
    expect(isElementType(ElementType.LINE)).toBe(false)
    expect(isElementType(ElementType.WALL)).toBe(false)

    // Test paths
    expect(isPathType(ElementType.LINE)).toBe(true)
    expect(isPathType(ElementType.CUBIC)).toBe(true)
    expect(isPathType(ElementType.RECTANGLE)).toBe(false)
    expect(isPathType(ElementType.WALL)).toBe(false)

    // Test text
    expect(isTextType(ElementType.TEXT_LABEL)).toBe(true)
    expect(isTextType(ElementType.RECTANGLE)).toBe(false)
    expect(isTextType(ElementType.LINE)).toBe(false)

    // Test interior design
    expect(isInteriorDesignType(ElementType.WALL)).toBe(true)
    expect(isInteriorDesignType(ElementType.FURNITURE)).toBe(true)
    expect(isInteriorDesignType(ElementType.RECTANGLE)).toBe(false)
    expect(isInteriorDesignType(ElementType.LINE)).toBe(false)
  })
})

describe('interface Type Definitions', () => {
  describe('element Interface', () => {
    it('should define a valid element', () => {
      const element: Element = {
        id: 'element-1',
        type: 'shape',
        visible: true,
        locked: false,
        metadata: {
          createdAt: Date.now(),
          updatedAt: Date.now(),
          name: 'Test Element',
          description: 'A test element',
          tags: ['test', 'element'],
        },
      }

      expect(element.id).toBe('element-1')
      expect(element.type).toBe('shape')
      expect(element.visible).toBe(true)
      expect(element.locked).toBe(false)
      expect(element.metadata?.name).toBe('Test Element')
      expect(element.metadata?.description).toBe('A test element')
      expect(element.metadata?.tags).toContain('test')
      expect(element.metadata?.tags).toContain('element')
    })

    it('should allow elements without metadata', () => {
      const element: Element = {
        id: 'element-2',
        type: 'shape',
        visible: true,
        locked: false,
      }

      expect(element.id).toBe('element-2')
      expect(element.type).toBe('shape')
      expect(element.visible).toBe(true)
      expect(element.locked).toBe(false)
      expect(element.metadata).toBeUndefined()
    })
  })

  describe('shapeElement Interface', () => {
    it('should define a valid shape element', () => {
      const shapeElement: ShapeElement = {
        id: 'shape-element-1',
        type: 'rectangle',
        visible: true,
        locked: false,
        fill: '#ff0000',
        stroke: '#000000',
        strokeWidth: 2,
        opacity: 0.8,
        position: { x: 100, y: 100 },
        rotation: 45,

        selectable: true,
        draggable: true,
        showHandles: true,
        zIndex: 1,
        layer: 'default',
      }

      expect(shapeElement.id).toBe('shape-element-1')
      expect(shapeElement.type).toBe('rectangle')
      expect(shapeElement.visible).toBe(true)
      expect(shapeElement.locked).toBe(false)
      expect(shapeElement.fill).toBe('#ff0000')
      expect(shapeElement.stroke).toBe('#000000')
      expect(shapeElement.strokeWidth).toBe(2)
      expect(shapeElement.opacity).toBe(0.8)
      expect(shapeElement.position.x).toBe(100)
      expect(shapeElement.position.y).toBe(100)
      expect(shapeElement.rotation).toBe(45)

      expect(shapeElement.selectable).toBe(true)
      expect(shapeElement.draggable).toBe(true)
      expect(shapeElement.showHandles).toBe(true)
      expect(shapeElement.zIndex).toBe(1)
      expect(shapeElement.layer).toBe('default')
    })

    it('should allow shape elements with minimal properties', () => {
      const shapeElement: ShapeElement = {
        id: 'shape-element-2',
        type: 'circle',
        visible: true,
        locked: false,
        position: { x: 50, y: 50 },
        rotation: 0,

        selectable: true,
        draggable: true,
        showHandles: true,
      }

      expect(shapeElement.id).toBe('shape-element-2')
      expect(shapeElement.type).toBe('circle')
      expect(shapeElement.visible).toBe(true)
      expect(shapeElement.locked).toBe(false)
      expect(shapeElement.position.x).toBe(50)
      expect(shapeElement.position.y).toBe(50)
      expect(shapeElement.rotation).toBe(0)

      expect(shapeElement.selectable).toBe(true)
      expect(shapeElement.draggable).toBe(true)
      expect(shapeElement.showHandles).toBe(true)
      expect(shapeElement.fill).toBeUndefined()
      expect(shapeElement.stroke).toBeUndefined()
      expect(shapeElement.strokeWidth).toBeUndefined()
      expect(shapeElement.opacity).toBeUndefined()
      expect(shapeElement.zIndex).toBeUndefined()
      expect(shapeElement.layer).toBeUndefined()
    })
  })

  describe('metadataProperties Interface', () => {
    it('should define valid metadata properties', () => {
      const now = Date.now()
      const metadata: MetadataProperties = {
        createdAt: now - 1000,
        updatedAt: now,
        createdBy: 'user-1',
        name: 'Test Metadata',
        description: 'A test metadata object',
        tags: ['test', 'metadata'],
        designType: 'modern',
        designCategory: 'residential',
        customProperty: 'custom value',
      }

      expect(metadata.createdAt).toBe(now - 1000)
      expect(metadata.updatedAt).toBe(now)
      expect(metadata.createdBy).toBe('user-1')
      expect(metadata.name).toBe('Test Metadata')
      expect(metadata.description).toBe('A test metadata object')
      expect(metadata.tags).toContain('test')
      expect(metadata.tags).toContain('metadata')
      expect(metadata.designType).toBe('modern')
      expect(metadata.designCategory).toBe('residential')
      expect(metadata.customProperty).toBe('custom value')
    })

    it('should allow metadata with only required properties', () => {
      const now = Date.now()
      const metadata: MetadataProperties = {
        createdAt: now - 1000,
        updatedAt: now,
      }

      expect(metadata.createdAt).toBe(now - 1000)
      expect(metadata.updatedAt).toBe(now)
      expect(metadata.createdBy).toBeUndefined()
      expect(metadata.name).toBeUndefined()
      expect(metadata.description).toBeUndefined()
      expect(metadata.tags).toBeUndefined()
      expect(metadata.designType).toBeUndefined()
      expect(metadata.designCategory).toBeUndefined()
    })

    it('should allow adding custom properties', () => {
      const metadata: MetadataProperties = {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        customString: 'string value',
        customNumber: 42,
        customBoolean: true,
        customObject: { key: 'value' },
        customArray: [1, 2, 3],
      }

      expect(metadata.customString).toBe('string value')
      expect(metadata.customNumber).toBe(42)
      expect(metadata.customBoolean).toBe(true)
      expect(metadata.customObject).toEqual({ key: 'value' })
      expect(metadata.customArray).toEqual([1, 2, 3])
    })
  })
})
