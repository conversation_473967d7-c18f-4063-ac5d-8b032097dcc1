import type { CoreConfig } from '@/core/config'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  publishConfigUpdated,
  publishExportRequest,
  publishHistoryRedo,
  publishHistoryUndo,
  publishTemplateApply,
} from '@/services/event-bus/helpers/publishers/data-publishers'
import { AppEventType } from '@/types/services/events'

describe('data Publishers', () => {
  let mockEventBus: any

  beforeEach(() => {
    mockEventBus = {
      publish: vi.fn(),
    }
  })

  describe('history Events', () => {
    it('should publish history undo event correctly', () => {
      publishHistoryUndo(mockEventBus)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.HISTORY_UNDO,
        payload: {},
      })
    })

    it('should publish history redo event correctly', () => {
      publishHistoryRedo(mockEventBus)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.HISTORY_REDO,
        payload: {},
      })
    })
  })

  describe('template Events', () => {
    it('should publish template apply event correctly', () => {
      const templateId = 'template-123'

      publishTemplateApply(mockEventBus, templateId)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.TEMPLATE_APPLY,
        payload: { templateId },
      })
    })
  })

  describe('export Events', () => {
    it('should publish export request event correctly', () => {
      const format = 'png'

      publishExportRequest(mockEventBus, format)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.EXPORT_REQUEST,
        payload: { format },
      })
    })

    it('should handle different export formats', () => {
      publishExportRequest(mockEventBus, 'svg')

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.EXPORT_REQUEST,
        payload: { format: 'svg' },
      })

      publishExportRequest(mockEventBus, 'json')

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.EXPORT_REQUEST,
        payload: { format: 'json' },
      })
    })
  })

  describe('config Events', () => {
    it('should publish config updated event correctly', () => {
      const config: Partial<CoreConfig> = {
        gridSize: 20,
        snapToGrid: true,
      }

      publishConfigUpdated(mockEventBus, config)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.CONFIG_UPDATED,
        payload: { config },
      })
    })

    it('should handle empty config object', () => {
      const config: Partial<CoreConfig> = {}

      publishConfigUpdated(mockEventBus, config)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.CONFIG_UPDATED,
        payload: { config: {} },
      })
    })
  })
})
