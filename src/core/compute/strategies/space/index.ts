/**
 * Space Planning Strategies Index
 *
 * @remarks
 * This module serves as a barrel export for all concrete strategy implementations
 * related to space planning and analysis. It re-exports the `SpacePlanningStrategy`
 * interface (and other relevant space computation types) from
 * `@/types/core/compute/spaceComputeTypes`, and then exports all specific
 * space planning strategy classes defined within this directory (e.g.,
 * {@link KitchenPlanningStrategy}, {@link BedroomPlanningStrategy}).
 *
 * This allows for a centralized import point for accessing various space
 * planning and analysis strategies.
 *
 * @module core/compute/strategies/space/index
 */

// Export concrete strategy implementations
export * from './AccessibleRoomPlanningStrategy'

export * from './BalconyPlanningStrategy'
export * from './BathroomPlanningStrategy'
export * from './BedroomPlanningStrategy'
export * from './ChildrenRoomPlanningStrategy'
export * from './ElderlyFriendlyRoomPlanningStrategy'
export * from './KitchenPlanningStrategy'
export * from './LivingRoomPlanningStrategy'
export * from './MultifunctionRoomPlanningStrategy'
export * from './OutdoorPlanningStrategy'
export * from './StorageRoomPlanningStrategy'
export * from './StudyRoomPlanningStrategy'
// Export interface first for better code organization
export * from '@/types/core/compute/spaceComputeTypes'
