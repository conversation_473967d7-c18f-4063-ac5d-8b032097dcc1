{"name": "renopilot-js-shapes2", "type": "module", "version": "1.0.0", "private": true, "scripts": {"dev": "npm run validate:config && vite --port 3000", "build": "npm run validate:config && tsc -b && vite build", "validate:config": "tsx scripts/validate-config.ts", "lint": "eslint .", "preview": "vite preview", "lint:fix": "eslint . --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "prepare": "husky", "test": "playwright test", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:unit": "playwright test tests/unit --project=chromium", "test:core": "playwright test tests/unit/core --project=chromium", "test:components": "playwright test tests/unit/components --project=chromium", "test:services": "playwright test tests/unit/services --project=chromium", "test:types": "playwright test tests/unit/types --project=chromium", "test:utils": "playwright test tests/unit/utils --project=chromium", "test:integration": "playwright test tests/integration --project=chromium", "test:e2e": "playwright test tests/e2e --project=chromium", "test:all": "bash scripts/run-all-tests.sh", "test:vitest": "vitest", "test:vitest:ui": "vitest --ui", "test:vitest:coverage": "vitest run --coverage", "test:hooks": "vitest run tests/unit/hooks --coverage"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.0", "@tailwindcss/forms": "^0.5.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.9.0", "driver.js": "^1.3.6", "jspdf": "^3.0.1", "lodash-es": "^4.17.21", "lucide-react": "^0.503.0", "mcp-proxy": "^2.14.2", "mitt": "^3.0.1", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.1.0", "textures": "^1.2.3", "tw-animate-css": "^1.2.5", "use-sync-external-store": "^1.5.0", "uuid": "^11.1.0", "zundo": "^2.3.0", "zustand": "^5.0.3"}, "devDependencies": {"@antfu/eslint-config": "^4.13.1", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@commitlint/types": "^19.8.0", "@eslint-react/eslint-plugin": "^1.49.0", "@eslint/js": "^9.21.0", "@playwright/test": "^1.51.1", "@tailwindcss/node": "^4.1.1", "@tailwindcss/postcss": "^4.0.15", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/d3": "^7.4.3", "@types/eslint": "^9.6.1", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.2", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/uuid": "^10.0.0", "@types/wicg-file-system-access": "^2023.10.6", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "@vitejs/plugin-react": "^4.5.0", "@vitest/coverage-istanbul": "^3.1.2", "@vitest/coverage-v8": "^3.1.2", "@vitest/ui": "^3.1.2", "autoprefixer": "^10.4.21", "c8": "^10.1.3", "eslint": "^9.23.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "glob": "^10.3.10", "globals": "^16.0.0", "husky": "^9.1.7", "jiti": "^2.4.2", "jsdom": "^26.1.0", "lint-staged": "^15.5.0", "nyc": "^17.1.0", "postcss": "^8.5.3", "postcss-custom-properties": "^14.0.4", "postcss-nested": "^7.0.2", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.4", "typescript": "^5.0.0", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.2", "vitest-mock-extended": "^3.1.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,html,css}": ["prettier --write"]}}