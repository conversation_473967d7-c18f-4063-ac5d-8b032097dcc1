/**
 * Template Hook
 *
 * React hook for managing template functionality and state.
 * Provides easy access to template loading, filtering, and application.
 */

import type {
  Template,
  TemplateFilter,
  TemplateSelectionState,
  TemplateSortBy,
  TemplateSortOrder,
  UseTemplateReturn,
} from '@/types/template'
import { useCallback, useEffect, useState } from 'react'
import { appEventBus } from '@/services/core/event-bus'
import { templateService } from '@/services/template/templateService'
import type { BaseEvent } from '@/types/services/events'

/**
 * Custom hook for template functionality
 */
export function useTemplate(): UseTemplateReturn {
  const [state, setState] = useState<TemplateSelectionState>({
    templates: [],
    selectedTemplate: null,
    isLoading: false,
    error: null,
    filter: {},
    sort: {
      by: 'name',
      order: 'asc',
    },
    showBlankOption: true,
  })

  /**
   * Load all available templates
   */
  const loadTemplates = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      const templates = await templateService.getTemplates()
      setState(prev => ({
        ...prev,
        templates: sortTemplates(templates, prev.sort.by, prev.sort.order),
        isLoading: false,
      }))
    }
    catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load templates',
        isLoading: false,
      }))
    }
  }, [])

  /**
   * Select a template
   */
  const selectTemplate = useCallback((template: Template | null) => {
    setState(prev => ({ ...prev, selectedTemplate: template }))

    if (template) {
      appEventBus.emit({
        type: 'template:selected',
        payload: { template },
        timestamp: Date.now()
      })
    }
  }, [])

  /**
   * Apply filter to templates
   */
  const applyFilter = useCallback(async (filter: TemplateFilter) => {
    setState(prev => ({ ...prev, filter, isLoading: true, error: null }))

    try {
      const filteredTemplates = await templateService.getFilteredTemplates(filter)
      setState(prev => ({
        ...prev,
        templates: sortTemplates(filteredTemplates, prev.sort.by, prev.sort.order),
        isLoading: false,
      }))
    }
    catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to filter templates',
        isLoading: false,
      }))
    }
  }, [])

  /**
   * Change sort order
   */
  const changeSort = useCallback((by: TemplateSortBy, order: TemplateSortOrder) => {
    setState(prev => ({
      ...prev,
      sort: { by, order },
      templates: sortTemplates(prev.templates, by, order),
    }))
  }, [])

  /**
   * Start with selected template
   */
  const startWithTemplate = useCallback(async () => {
    if (!state.selectedTemplate) {
      throw new Error('No template selected')
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      await templateService.loadTemplate(state.selectedTemplate)
      setState(prev => ({ ...prev, isLoading: false }))
    }
    catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load template',
        isLoading: false,
      }))
      throw error
    }
  }, [state.selectedTemplate])

  /**
   * Start with blank canvas
   */
  const startBlank = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      // Emit event to clear current state and start fresh
      appEventBus.emit({
        type: 'template:start-blank',
        payload: {},
        timestamp: Date.now()
      })
      setState(prev => ({ ...prev, isLoading: false }))
    }
    catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to start blank canvas',
        isLoading: false,
      }))
      throw error
    }
  }, [])

  /**
   * Load templates on mount
   */
  useEffect(() => {
    loadTemplates()
  }, [loadTemplates])

  return {
    state,
    loadTemplates,
    selectTemplate,
    applyFilter,
    changeSort,
    startWithTemplate,
    startBlank,
  }
}

/**
 * Sort templates by specified criteria
 */
function sortTemplates(
  templates: Template[],
  by: TemplateSortBy,
  order: TemplateSortOrder,
): Template[] {
  const sorted = [...templates].sort((a, b) => {
    let comparison = 0

    switch (by) {
      case 'name':
        comparison = a.metadata.name.localeCompare(b.metadata.name)
        break
      case 'createdAt':
        comparison = a.metadata.createdAt.getTime() - b.metadata.createdAt.getTime()
        break
      case 'updatedAt':
        comparison = a.metadata.updatedAt.getTime() - b.metadata.updatedAt.getTime()
        break
      case 'difficulty':
        const difficultyOrder = { beginner: 1, intermediate: 2, advanced: 3 }
        comparison = difficultyOrder[a.metadata.difficulty] - difficultyOrder[b.metadata.difficulty]
        break
      case 'estimatedTime':
        const timeA = a.metadata.estimatedTime || 0
        const timeB = b.metadata.estimatedTime || 0
        comparison = timeA - timeB
        break
      case 'featured':
        const featuredA = a.metadata.featured ? 1 : 0
        const featuredB = b.metadata.featured ? 1 : 0
        comparison = featuredB - featuredA // Featured first
        break
      default:
        comparison = 0
    }

    return order === 'asc' ? comparison : -comparison
  })

  return sorted
}

/**
 * Hook for template integration with application state
 */
export function useTemplateIntegration() {
  const [isTemplateMode, setIsTemplateMode] = useState(false)

  /**
   * Handle template application events
   */
  useEffect(() => {
    const handleTemplateApply = (event: BaseEvent) => {
      // Apply template data to the application state
      setIsTemplateMode(true)
      console.log('Template applied:', event)

      // Extract data from event payload
      const payload = event.payload as { shapes: any[], selectedShapeIds: string[] }

      // Store the template data for immediate use
      try {
        const templateState = {
          shapes: payload.shapes,
          selectedShapeIds: payload.selectedShapeIds,
          appliedAt: new Date().toISOString(),
        }
        localStorage.setItem('renopilot-applied-template-state', JSON.stringify(templateState))

        // Emit a more specific event for the shapes store to listen to
        appEventBus.emit({
          type: 'shapes:load-template',
          payload: {
            shapes: payload.shapes,
            selectedShapeIds: payload.selectedShapeIds,
          },
          timestamp: Date.now()
        })

        console.log('Template state stored and shapes:load-template event emitted')
      }
      catch (error) {
        console.error('Failed to store template state:', error)
      }
    }

    const handleStartBlank = () => {
      // Clear current state and start fresh
      console.log('Starting with blank canvas')
      setIsTemplateMode(false)

      // Clear any stored template data
      localStorage.removeItem('renopilot-current-template')
      localStorage.removeItem('renopilot-template-loaded')
      localStorage.removeItem('renopilot-applied-template-state')

      // Emit event to clear shapes
      appEventBus.emit({
        type: 'shapes:clear-all',
        payload: {},
        timestamp: Date.now()
      })
    }

    const unsubscribeApply = appEventBus.on('template:apply', handleTemplateApply)
    const unsubscribeBlank = appEventBus.on('template:start-blank', handleStartBlank)

    return () => {
      unsubscribeApply()
      unsubscribeBlank()
    }
  }, [])

  /**
   * Check if template was loaded on startup
   */
  useEffect(() => {
    const checkStoredTemplate = () => {
      const templateLoaded = localStorage.getItem('renopilot-template-loaded')
      const storedTemplate = localStorage.getItem('renopilot-current-template')

      if (templateLoaded === 'true' && storedTemplate) {
        try {
          const templateData = JSON.parse(storedTemplate)
          console.log('Found stored template data:', templateData.metadata?.name)

          // Apply the stored template data
          appEventBus.emit({
            type: 'template:apply',
            payload: {
              shapes: templateData.shapes,
              selectedShapeIds: templateData.selectedShapeIds || [],
            },
            timestamp: Date.now()
          })
        }
        catch (error) {
          console.error('Failed to parse stored template data:', error)
          localStorage.removeItem('renopilot-current-template')
          localStorage.removeItem('renopilot-template-loaded')
        }
      }
    }

    // Check for stored template after a short delay to ensure other systems are ready
    const timeoutId = setTimeout(checkStoredTemplate, 500)

    return () => clearTimeout(timeoutId)
  }, [])

  /**
   * Show template selection
   */
  const showTemplateSelection = useCallback(() => {
    setIsTemplateMode(true)
  }, [])

  /**
   * Hide template selection
   */
  const hideTemplateSelection = useCallback(() => {
    setIsTemplateMode(false)
  }, [])

  return {
    isTemplateMode,
    showTemplateSelection,
    hideTemplateSelection,
  }
}
