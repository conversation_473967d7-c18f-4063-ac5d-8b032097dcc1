/**
 * Contains validation logic specific to Arc paths.
 */

import type { ValidatableShape, ValidationError } from '@/types/core/validator/validator-interface' // Corrected: Use ValidatableShape
import { ElementType } from '@/types/core/elementDefinitions' // Import ElementType only
import { ValidationErrorCode } from '@/types/core/validator/error-codes'
import { isValidPoint } from '../common/validationUtils'
import { BaseShapeValidator } from '../common/validatorBase'

/**
 * Concrete validator implementation for Arc paths.
 *
 * @extends BaseShapeValidator
 */
export class ArcValidator extends BaseShapeValidator {
  /**
   * Validates properties specific to an Arc element data structure.
   *
   * @param shape - The shape to validate.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected validateSpecific(shape: ValidatableShape): ValidationError[] { // Parameter type is ValidatableShape
    const errors: ValidationError[] = []

    if (shape.type !== ElementType.ARC) {
      errors.push({
        code: ValidationErrorCode.INVALID_SHAPE_TYPE,
        message: `Internal Error: ArcValidator received shape type ${shape.type}`,
        path: 'type',
      })
      return errors
    }

    // Type assertion to access properties if they exist
    const props = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined || {}
    const radius = props.radius as number | undefined
    const startAngle = props.startAngle as number | undefined
    const endAngle = props.endAngle as number | undefined
    // Position is a top-level property of ValidatableShape

    // Validate radius
    if (radius == null || typeof radius !== 'number' || !Number.isFinite(radius) || radius <= 0) {
      errors.push({
        code: ValidationErrorCode.INVALID_RADIUS,
        message: `Arc requires a positive finite number for 'radius' in properties. Received: ${radius}`,
        path: 'properties.radius',
        value: radius,
      })
    }

    // Validate startAngle
    if (startAngle == null || typeof startAngle !== 'number' || !Number.isFinite(startAngle)) {
      errors.push({
        code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
        message: `Arc requires a finite number for 'startAngle' in properties. Received: ${startAngle}`,
        path: 'properties.startAngle',
        value: startAngle,
      })
    }

    // Validate endAngle
    if (endAngle == null || typeof endAngle !== 'number' || !Number.isFinite(endAngle)) {
      errors.push({
        code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
        message: `Arc requires a finite number for 'endAngle' in properties. Received: ${endAngle}`,
        path: 'properties.endAngle',
        value: endAngle,
      })
    }

    // Validate position (from ValidatableShape)
    if (shape.position == null || !isValidPoint(shape.position)) {
      errors.push({
        code: ValidationErrorCode.INVALID_POSITION,
        message: `Arc requires a valid 'position' ({x, y} with finite numbers). Received: ${JSON.stringify(shape.position)}`,
        path: 'position',
        value: shape.position,
      })
    }

    return errors
  }

  /**
   * Applies arc-specific business rules.
   *
   * @param _shape - The shape to validate against business rules.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected applySpecificRules(_shape: ValidatableShape): ValidationError[] {
    // No arc-specific business rules defined currently beyond common ones.
    // If BaseShapeValidator.applySpecificRules expects a parameter, add it here.
    // For now, assuming it doesn't or this override is correct.
    return []
  }
}
