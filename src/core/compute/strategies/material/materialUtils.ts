/**
 * Material Calculation Utilities
 *
 * @remarks
 * This module provides comprehensive utility functions for calculating quantities
 * of various materials needed for interior design and construction projects.
 * These functions support calculations based on area, perimeter, dimensions,
 * and other specific parameters for materials like paint, tiles, flooring,
 * wallpaper, molding, and concrete.
 *
 * These utilities aim to standardize material calculations and provide accurate
 * estimates. They are used by various `MaterialCalculatorStrategy` implementations.
 *
 * @module core/compute/strategies/material/materialUtils
 */

import type { MaterialCalculationResult } from '@/types/core/compute' // Import from types
import type { PointData as IPoint } from '@/types/core/element/geometry/point'
import { calculateArea, calculatePerimeter } from '@/lib/utils/geometry/polygonUtils'

// -----------------------------------------------------------------------------
// Basic Material Calculations
// -----------------------------------------------------------------------------

/**
 * Calculates the amount of paint needed for a surface
 *
 * This function calculates the volume of paint needed based on the surface area,
 * the coverage rate of the paint, and the number of coats to be applied. This is
 * useful for estimating paint quantities for walls, ceilings, or other surfaces.
 *
 * @param area - Area of the surface in square meters
 * @param coveragePerLiter - Coverage per liter of paint in square meters
 * @param coats - Number of coats to apply (default: 2)
 * @param options - Additional calculation options
 * @param options.wastageRate - Percentage of extra material to account for waste (default: 0.1)
 * @returns The amount of paint needed in liters
 * @example
 * // Calculate paint needed for a 20 square meter wall
 * // Paint coverage: 8 square meters per liter, 2 coats
 * const paintNeeded = calculatePaintAmount(20, 8, 2);
 * // Returns 5 liters (20 × 2 ÷ 8)
 */
export function calculatePaintAmount(
  area: number,
  coveragePerLiter: number = 10,
  coats: number = 2,
  options?: {
    wastageRate?: number // Wastage rate (decimal between 0-1)
  },
): MaterialCalculationResult {
  if (area <= 0) {
    throw new Error('Surface area must be positive')
  }

  if (coveragePerLiter <= 0) {
    throw new Error('Coverage per liter must be positive')
  }

  if (coats <= 0) {
    throw new Error('Number of coats must be positive')
  }

  // Default options
  const {
    wastageRate = 0.1, // Default 10% wastage
  } = options || {}

  // Calculate required liters (considering number of coats)
  const requiredLiters = (area * coats) / coveragePerLiter

  // Add wastage
  const litersWithWastage = requiredLiters * (1 + wastageRate)

  // Paint typically sold in whole liters or cans, round up to nearest integer
  const roundedLiters = Math.ceil(litersWithWastage)

  return {
    amount: roundedLiters,
    unit: 'liters',
    amountWithWastage: litersWithWastage,
    coats,
    unitType: 'paint',
  }
}

/**
 * Calculates the number of tiles needed for a floor or wall
 *
 * This function calculates how many tiles are needed to cover a specified area,
 * taking into account the dimensions of each tile and adding a percentage for wastage.
 * The result is rounded up to ensure sufficient tiles are ordered.
 *
 * @param area - Area to be tiled in square meters
 * @param tileWidth - Width of a single tile in meters
 * @param tileHeight - Height of a single tile in meters
 * @param options - Additional calculation options
 * @param options.wastageRate - Percentage of extra material to account for waste (default: 0.1)
 * @param options.includeJoints - Whether to include joint spacing in calculations (default: false)
 * @param options.jointWidth - Width of joints between tiles in meters (default: 0.002)
 * @param options.unitsPerBox - Number of tiles per box for packaging calculations
 * @returns The tile calculation result
 * @example
 * // Calculate tiles needed for a 15 square meter bathroom floor
 * // Tile size: 30cm × 30cm (0.3m × 0.3m), 10% wastage
 * const tilesNeeded = calculateTileCount(15, 0.3, 0.3, { wastageRate: 0.1 });
 * // Tile area = 0.09 square meters
 * // Base tiles needed = 15 ÷ 0.09 = 166.67
 * // With wastage = 166.67 × 1.1 = 183.33
 * // Rounded up = 184 tiles
 */
export function calculateTileCount(
  area: number,
  tileWidth: number,
  tileHeight: number,
  options?: {
    wastageRate?: number // Wastage rate (decimal between 0-1)
    includeJoints?: boolean // Whether to consider joints
    jointWidth?: number // Joint width (meters)
    unitsPerBox?: number // Number of tiles per box
  },
): MaterialCalculationResult {
  if (area <= 0) {
    throw new Error('Area must be positive')
  }

  if (tileWidth <= 0 || tileHeight <= 0) {
    throw new Error('Tile dimensions must be positive')
  }

  // Default options
  const {
    wastageRate = 0.1, // Default 10% wastage
    includeJoints = false,
    jointWidth = 0.003, // Default 3mm joints
    unitsPerBox = 0,
  } = options || {}

  const tileArea = tileWidth * tileHeight
  let tileCount = 0

  // If considering joints, adjust required area
  if (includeJoints && jointWidth > 0) {
    // Calculate effective area of each tile including joints
    const effectiveWidth = tileWidth + jointWidth
    const effectiveHeight = tileHeight + jointWidth
    const effectiveUnitArea = effectiveWidth * effectiveHeight

    // Calculate required tile count
    tileCount = Math.ceil(area / effectiveUnitArea)
  }
  else {
    // Not considering joints, directly calculate required tile count
    tileCount = Math.ceil(area / tileArea)
  }

  // Apply wastage
  tileCount = Math.ceil(tileCount * (1 + wastageRate))

  // Calculate number of boxes
  const boxes = unitsPerBox > 0 ? Math.ceil(tileCount / unitsPerBox) : undefined

  // Calculate area with wastage
  const areaWithWastage = area * (1 + wastageRate)

  return {
    amount: tileCount,
    unit: 'pieces',
    unitCount: tileCount,
    unitType: 'tiles',
    amountWithWastage: areaWithWastage,
    boxes,
  }
}

/**
 * Calculates the amount of flooring material needed
 *
 * This function calculates the total amount of flooring material required for a given area,
 * including additional material for wastage. This is useful for estimating quantities
 * of hardwood, laminate, vinyl, carpet, or other flooring materials.
 *
 * @param area - Area of the floor in square meters
 * @param materialType - Type of flooring material (e.g., 'hardwood', 'laminate', 'vinyl', 'tile')
 * @param options - Additional calculation options including unit size, wastage rate, and joint considerations
 * @param options.unitSize - Flooring unit size (width and length, in meters)
 * @param options.unitSize.width - Width of each flooring unit in meters
 * @param options.unitSize.height - Height of each flooring unit in meters
 * @param options.wastageRate - Wastage rate (decimal between 0-1)
 * @param options.includeJoints - Whether to consider joints
 * @param options.jointWidth - Joint width (meters)
 * @param options.unitsPerBox - Number of flooring units per box
 * @returns The flooring material calculation result
 * @example
 * // Calculate flooring material for a 25 square meter room with 15% wastage
 * const flooringNeeded = calculateFlooringAmount(25, 'hardwood', { wastageRate: 0.15 });
 */
export function calculateFlooringAmount(
  area: number,
  materialType: string = 'generic',
  options?: {
    unitSize?: { width: number, height: number } // Flooring unit size (width and length, in meters)
    wastageRate?: number // Wastage rate (decimal between 0-1)
    includeJoints?: boolean // Whether to consider joints
    jointWidth?: number // Joint width (meters)
    unitsPerBox?: number // Number of flooring units per box
  },
): MaterialCalculationResult {
  if (area <= 0) {
    throw new Error('Floor area must be positive')
  }

  // Default options
  const {
    unitSize,
    wastageRate = 0.1, // Default 10% wastage
    includeJoints = false,
    jointWidth = 0.003, // Default 3mm joints
    unitsPerBox = 0,
  } = options || {}

  const areaWithWastage = area * (1 + wastageRate)

  // For specific material types with unit sizes
  if (unitSize && unitSize.width > 0 && unitSize.height > 0
    && ['hardwood', 'laminate', 'vinyl', 'tile'].includes(materialType.toLowerCase())) {
    const unitArea = unitSize.width * unitSize.height
    let unitCount = 0

    // If considering joints, adjust required area
    if (includeJoints && jointWidth > 0) {
      // Calculate effective area of each unit including joints
      const effectiveWidth = unitSize.width + jointWidth
      const effectiveHeight = unitSize.height + jointWidth
      const effectiveUnitArea = effectiveWidth * effectiveHeight

      // Calculate required unit count
      unitCount = Math.ceil(area / effectiveUnitArea)
    }
    else {
      // Not considering joints, directly calculate required unit count
      unitCount = Math.ceil(area / unitArea)
    }

    // Apply wastage
    unitCount = Math.ceil(unitCount * (1 + wastageRate))

    // Calculate number of boxes
    const boxes = unitsPerBox > 0 ? Math.ceil(unitCount / unitsPerBox) : undefined

    return {
      amount: areaWithWastage,
      unit: 'square meters',
      unitCount,
      unitType: materialType.toLowerCase(),
      amountWithWastage: areaWithWastage,
      boxes,
    }
  }

  // For carpet or generic flooring
  return {
    amount: areaWithWastage,
    unit: 'square meters',
    unitType: materialType.toLowerCase(),
    amountWithWastage: areaWithWastage,
  }
}

/**
 * Calculates the number of wallpaper rolls needed
 *
 * This function calculates how many wallpaper rolls are needed to cover the walls
 * of a room, taking into account the wall dimensions, roll dimensions, and adding
 * a percentage for wastage. The result is rounded up to ensure sufficient wallpaper
 * is ordered.
 *
 * @param wallHeight - Height of the wall in meters
 * @param perimeter - Perimeter of the room in meters
 * @param rollWidth - Width of the wallpaper roll in meters
 * @param rollLength - Length of the wallpaper roll in meters
 * @param options - Additional calculation options including wastage rate and pattern repeat
 * @param options.wastageRate - Wastage rate (decimal between 0-1)
 * @param options.patternRepeat - Pattern repeat height (meters)
 * @returns The wallpaper calculation result
 * @example
 * // Calculate wallpaper rolls for a room with:
 * // - Wall height: 2.4m
 * // - Room perimeter: 14m
 * // - Wallpaper roll: 0.53m wide × 10m long
 * // - 15% wastage
 * const rollsNeeded = calculateWallpaperRolls(2.4, 14, 0.53, 10, { wastageRate: 0.15 });
 */
export function calculateWallpaperRolls(
  wallHeight: number,
  perimeter: number,
  rollWidth: number,
  rollLength: number,
  options?: {
    wastageRate?: number // Wastage rate (decimal between 0-1)
    patternRepeat?: number // Pattern repeat height (meters)
  },
): MaterialCalculationResult {
  if (wallHeight <= 0 || perimeter <= 0) {
    throw new Error('Wall dimensions must be positive')
  }

  if (rollWidth <= 0 || rollLength <= 0) {
    throw new Error('Roll dimensions must be positive')
  }

  // Default options
  const {
    wastageRate = 0.1, // Default 10% wastage
    patternRepeat = 0, // Default no pattern repeat
  } = options || {}

  // Calculate how many strips we can get from each roll
  let stripsPerRoll = Math.floor(rollLength / wallHeight)

  // If there's a pattern repeat, adjust the number of strips
  if (patternRepeat > 0) {
    // Calculate how many full pattern repeats fit in the wall height
    const repeatsPerWall = Math.ceil(wallHeight / patternRepeat)
    // Adjust wall height to account for pattern matching
    const adjustedWallHeight = repeatsPerWall * patternRepeat
    // Recalculate strips per roll
    stripsPerRoll = Math.floor(rollLength / adjustedWallHeight)
  }

  // Calculate how many strips we need for the perimeter
  const stripsNeeded = Math.ceil(perimeter / rollWidth)

  // Calculate how many rolls we need
  let rollsNeeded = Math.ceil(stripsNeeded / stripsPerRoll)

  // Apply wastage
  rollsNeeded = Math.ceil(rollsNeeded * (1 + wastageRate))

  return {
    amount: rollsNeeded,
    unit: 'rolls',
    unitType: 'wallpaper',
    amountWithWastage: rollsNeeded,
  }
}

/**
 * Calculate the amount of baseboard or crown molding needed
 *
 * This function calculates the total length of baseboard or crown molding needed
 * for a room perimeter, including additional material for wastage and joints.
 *
 * @param perimeter - Perimeter of the room in meters
 * @param options - Additional calculation options including wastage rate, unit length, and units per box
 * @param options.wastageRate - Wastage rate (decimal between 0-1)
 * @param options.unitLength - Length of each molding piece (meters)
 * @param options.unitsPerBox - Number of pieces per box
 * @returns The molding calculation result
 * @example
 * // Calculate baseboard needed for a room with 18m perimeter and 10% wastage
 * const moldingNeeded = calculateMoldingAmount(18, { wastageRate: 0.1 });
 */
export function calculateMoldingAmount(
  perimeter: number,
  options?: {
    wastageRate?: number // Wastage rate (decimal between 0-1)
    unitLength?: number // Length of each molding piece (meters)
    unitsPerBox?: number // Number of pieces per box
  },
): MaterialCalculationResult {
  if (perimeter <= 0) {
    throw new Error('Perimeter must be positive')
  }

  // Default options
  const {
    wastageRate = 0.1, // Default 10% wastage
    unitLength = 0,
    unitsPerBox = 0,
  } = options || {}

  const amountWithWastage = perimeter * (1 + wastageRate)
  let unitCount: number | undefined
  let boxes: number | undefined

  // If molding comes in standard lengths
  if (unitLength > 0) {
    unitCount = Math.ceil(amountWithWastage / unitLength)

    // Calculate number of boxes
    if (unitsPerBox > 0) {
      boxes = Math.ceil(unitCount / unitsPerBox)
    }
  }

  return {
    amount: perimeter,
    unit: 'meters',
    unitCount,
    unitType: 'molding',
    amountWithWastage,
    boxes,
  }
}

/**
 * Calculate the amount of concrete needed for a slab
 *
 * This function calculates the volume of concrete required for a slab
 * based on its area and thickness.
 *
 * @param area - Area of the slab in square meters
 * @param thickness - Thickness of the slab in meters
 * @param options - Additional calculation options including wastage rate
 * @param options.wastageRate - Wastage rate (decimal between 0-1)
 * @returns The concrete calculation result
 * @example
 * // Calculate concrete for a 30 square meter slab with 0.15m thickness
 * const concreteNeeded = calculateConcreteAmount(30, 0.15, { wastageRate: 0.05 });
 */
export function calculateConcreteAmount(
  area: number,
  thickness: number,
  options?: {
    wastageRate?: number // Wastage rate (decimal between 0-1)
  },
): MaterialCalculationResult {
  if (area <= 0) {
    throw new Error('Area must be positive')
  }

  if (thickness <= 0) {
    throw new Error('Thickness must be positive')
  }

  // Default options
  const {
    wastageRate = 0.05, // Default 5% wastage
  } = options || {}

  const volume = area * thickness
  const volumeWithWastage = volume * (1 + wastageRate)

  return {
    amount: volume,
    unit: 'cubic meters',
    unitType: 'concrete',
    amountWithWastage: volumeWithWastage,
  }
}

/**
 * Calculate the amount of material needed for a polygon-shaped area
 *
 * This function calculates the area of an irregular polygon defined by its vertices,
 * then determines the amount of material needed including wastage.
 *
 * @param vertices - Array of vertices defining the polygon
 * @param materialType - Type of material (e.g., 'tile', 'concrete', 'generic')
 * @param options - Additional calculation options including wastage rate and thickness
 * @param options.wastageRate - Wastage rate (decimal between 0-1)
 * @param options.thickness - Material thickness (for volume calculations)
 * @returns The material calculation result
 * @example
 * // Calculate material for a triangular area with vertices at (0,0), (4,0), and (2,3)
 * const vertices = [
 *   { x: 0, y: 0 },
 *   { x: 4, y: 0 },
 *   { x: 2, y: 3 }
 * ];
 * const materialNeeded = calculatePolygonMaterialAmount(vertices, 'tile', { wastageRate: 0.1 });
 */
export function calculatePolygonMaterialAmount(
  vertices: IPoint[],
  materialType: string = 'generic',
  options?: {
    wastageRate?: number // Wastage rate (decimal between 0-1)
    thickness?: number // Material thickness (for volume calculations)
  },
): MaterialCalculationResult {
  if (vertices == null || vertices.length < 3) {
    throw new Error('A polygon must have at least 3 vertices')
  }

  // Default options
  const {
    wastageRate = 0.1, // Default 10% wastage
    thickness = 0,
  } = options || {}

  const area = calculateArea(vertices)
  const areaWithWastage = area * (1 + wastageRate)

  // If thickness is provided, calculate volume
  if (thickness > 0) {
    const volume = area * thickness
    const volumeWithWastage = volume * (1 + wastageRate)

    return {
      amount: volume,
      unit: 'cubic meters',
      unitType: materialType,
      amountWithWastage: volumeWithWastage,
    }
  }

  return {
    amount: area,
    unit: 'square meters',
    unitType: materialType,
    amountWithWastage: areaWithWastage,
  }
}

/**
 * Calculate the amount of material needed for a perimeter
 *
 * This function calculates the perimeter length of a polygon defined by its vertices,
 * then determines the amount of material needed including wastage.
 *
 * @param vertices - Array of vertices defining the perimeter
 * @param materialType - Type of material (e.g., 'baseboard', 'molding', 'generic')
 * @param options - Additional calculation options including wastage rate, unit length, and units per box
 * @param options.wastageRate - Wastage rate (decimal between 0-1)
 * @param options.unitLength - Length of each material piece (meters)
 * @param options.unitsPerBox - Number of pieces per box
 * @returns The material calculation result
 * @example
 * // Calculate material for a rectangular perimeter
 * const vertices = [
 *   { x: 0, y: 0 },
 *   { x: 4, y: 0 },
 *   { x: 4, y: 3 },
 *   { x: 0, y: 3 }
 * ];
 * const materialNeeded = calculatePerimeterMaterialAmount(vertices, 'baseboard', { wastageRate: 0.1 });
 */
export function calculatePerimeterMaterialAmount(
  vertices: IPoint[],
  materialType: string = 'generic',
  options?: {
    wastageRate?: number // Wastage rate (decimal between 0-1)
    unitLength?: number // Length of each material piece (meters)
    unitsPerBox?: number // Number of pieces per box
  },
): MaterialCalculationResult {
  if (vertices == null || vertices.length < 2) {
    throw new Error('A perimeter must have at least 2 vertices')
  }

  // Default options
  const {
    wastageRate = 0.1, // Default 10% wastage
    unitLength = 0,
    unitsPerBox = 0,
  } = options || {}

  const perimeter = calculatePerimeter(vertices)
  const perimeterWithWastage = perimeter * (1 + wastageRate)

  let unitCount: number | undefined
  let boxes: number | undefined

  // If material comes in standard lengths
  if (unitLength > 0) {
    unitCount = Math.ceil(perimeterWithWastage / unitLength)

    // Calculate number of boxes
    if (unitsPerBox > 0) {
      boxes = Math.ceil(unitCount / unitsPerBox)
    }
  }

  return {
    amount: perimeter,
    unit: 'meters',
    unitCount,
    unitType: materialType,
    amountWithWastage: perimeterWithWastage,
    boxes,
  }
}

/**
 * Material utilities namespace
 * Contains all material calculation functions organized by category
 */
export const MaterialUtils = {
  // Paint calculations
  calculatePaintAmount,

  // Tile calculations
  calculateTileCount,

  // Flooring calculations
  calculateFlooringAmount,

  // Wall covering calculations
  calculateWallpaperRolls,

  // Molding calculations
  calculateMoldingAmount,

  // Concrete calculations
  calculateConcreteAmount,

  // Polygon-based calculations
  calculatePolygonMaterialAmount,
  calculatePerimeterMaterialAmount,
}
