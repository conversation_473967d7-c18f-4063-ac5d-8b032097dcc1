import type { CostCalculationOptions } from '@/types/core/compute'
import type { Element, ShapeElement } from '@/types/core/elementDefinitions'

import React from 'react'
import ReactDOM from 'react-dom/client'

import { ComputeFacade } from '@/core/compute/ComputeFacade'
import { EllipseAreaStrategy } from '@/core/compute/strategies/area/EllipseAreaStrategy'
import { PolygonAreaStrategy } from '@/core/compute/strategies/area/PolygonAreaStrategy'
import { RectangleAreaStrategy } from '@/core/compute/strategies/area/RectangleAreaStrategy'
import { AreaBasedCostStrategy } from '@/core/compute/strategies/cost/AreaBasedCostStrategy'
import { PerimeterBasedCostStrategy } from '@/core/compute/strategies/cost/PerimeterBasedCostStrategy'
import { PolygonCostStrategy } from '@/core/compute/strategies/cost/PolygonCostStrategy'
import { PolylineCostStrategy } from '@/core/compute/strategies/cost/PolylineCostStrategy'
import { UnitBasedCostStrategy } from '@/core/compute/strategies/cost/UnitBasedCostStrategy'
import { ArcPerimeterStrategy } from '@/core/compute/strategies/perimeter/ArcPerimeterStrategy'
import { CubicPerimeterStrategy } from '@/core/compute/strategies/perimeter/CubicPerimeterStrategy'
import { EllipsePerimeterStrategy } from '@/core/compute/strategies/perimeter/EllipsePerimeterStrategy'
import { LinePerimeterStrategy } from '@/core/compute/strategies/perimeter/LinePerimeterStrategy'
import { PolygonPerimeterStrategy } from '@/core/compute/strategies/perimeter/PolygonPerimeterStrategy'
import { PolylinePerimeterStrategy } from '@/core/compute/strategies/perimeter/PolylinePerimeterStrategy'
import { QuadraticPerimeterStrategy } from '@/core/compute/strategies/perimeter/QuadraticPerimeterStrategy'
import { RectanglePerimeterStrategy } from '@/core/compute/strategies/perimeter/RectanglePerimeterStrategy'
import { StrategyRegistry } from '@/core/compute/StrategyRegistry'
import { CoreCoordinator } from '@/core/CoreCoordinator'
import { ElementFactory } from '@/core/factory'
import { ShapeRepository } from '@/core/state/ShapeRepository'
import { ElementValidator } from '@/core/validator'
import { appEventBus } from '@/services/core/event-bus'
import { initializeServices } from '@/services/core/registry'
import { ErrorService } from '@/services/system/error-service/errorService'
import { ConsoleLoggerService } from '@/services/system/logging/consoleLoggerService'
import { ElementType } from '@/types/core/elementDefinitions'
import { CoreError, ErrorType } from '@/services/system/error-service'

import App from './App.tsx'
import './styles/main.css'

async function main() {
  // Instantiate dependencies for service initialization
  const logger = ConsoleLoggerService.create()
  const elementFactory = new ElementFactory()

  // Create ShapeRepository. It will hold a reference to the appEventBus singleton.
  // This singleton instance will be reset and configured by initializeServices.
  const shapeRepository = new ShapeRepository(logger, appEventBus)

  try {
    logger.warn('[main.tsx] Initializing services (core call)... this will init/reset event bus')
    await initializeServices({
      elementFactory,
      shapeRepository, // Pass the created ShapeRepository instance
      logger,
    })
    logger.warn('[main.tsx] Services initialized successfully (core call).')
  }
  catch (error) {
    logger.error('[main.tsx] Failed to initialize services (core call):', error instanceof Error ? error.message : String(error))
    // Render an error message or a fallback UI if services fail to initialize
    // For now, we'll log the error and proceed, but in a real app, you might stop here.
  }

  // Instantiate validator and errorService for CoreCoordinator
  const validator = new ElementValidator()
  const errorService = new ErrorService(appEventBus, logger)

  // === 注册计算策略 ===
  const strategyRegistry = new StrategyRegistry()

  // 注册面积计算策略
  logger.warn('[main.tsx] 注册面积计算策略...')
  logger.warn('[main.tsx] - 注册矩形面积计算策略')
  strategyRegistry.registerAreaStrategy(new RectangleAreaStrategy())

  // 正方形的面积计算策略
  class SquareAreaStrategy extends RectangleAreaStrategy {
    public getElementType(): ElementType {
      return ElementType.SQUARE
    }
  }

  logger.warn('[main.tsx] - 注册正方形面积计算策略')
  strategyRegistry.registerAreaStrategy(new SquareAreaStrategy())

  logger.warn('[main.tsx] - 注册椭圆和圆形面积计算策略')
  strategyRegistry.registerAreaStrategy(new EllipseAreaStrategy())

  // 图片的面积计算策略（重写计算方法以支持IMAGE类型）
  class ImageAreaStrategy extends RectangleAreaStrategy {
    public getElementType(): ElementType {
      return ElementType.IMAGE
    }

    public calculateArea(element: Element): number {
      if (element == null) {
        throw new CoreError(
          ErrorType.InvalidParameter,
          'Invalid element: null or undefined',
          undefined,
          { component: 'ImageAreaStrategy', operation: 'calculateArea' },
        )
      }

      if (element.type !== ElementType.IMAGE) {
        throw new CoreError(
          ErrorType.InvalidElementType,
          `Expected element type IMAGE, got '${element.type}' for ID ${element.id}`,
          undefined,
          { component: 'ImageAreaStrategy', operation: 'calculateArea', target: element.id },
        )
      }

      // 图片元素使用与矩形相同的属性结构
      const imageElement = element as { properties?: { width?: number, height?: number } }
      const width: unknown = imageElement.properties?.width
      const height: unknown = imageElement.properties?.height

      if (typeof width !== 'number' || typeof height !== 'number'
        || !Number.isFinite(width) || !Number.isFinite(height) || width < 0 || height < 0) {
        throw new CoreError(
          ErrorType.InvalidParameter,
          `Image element (ID: ${element.id}) must have finite, non-negative width and height in its properties. Received width: ${String(width)}, height: ${String(height)}`,
          undefined,
          { component: 'ImageAreaStrategy', operation: 'calculateArea', target: element.id, metadata: { width: String(width), height: String(height) } },
        )
      }

      // 计算面积：width × height
      const area = width * height

      if (Number.isNaN(area)) {
        throw new CoreError(
          ErrorType.ComputationError,
          `Image element (ID: ${element.id}) resulted in NaN area calculation. Received width: ${String(width)}, height: ${String(height)}`,
          undefined,
          { component: 'ImageAreaStrategy', operation: 'calculateArea', target: element.id, metadata: { width: String(width), height: String(height) } },
        )
      }

      return area
    }
  }

  logger.warn('[main.tsx] - 注册图片面积计算策略')
  strategyRegistry.registerAreaStrategy(new ImageAreaStrategy())

  // 注册周长计算策略
  logger.warn('[main.tsx] 注册周长计算策略...')
  logger.warn('[main.tsx] - 注册矩形周长计算策略')
  strategyRegistry.registerPerimeterStrategy(new RectanglePerimeterStrategy())

  // 正方形的周长计算策略
  class SquarePerimeterStrategy extends RectanglePerimeterStrategy {
    public getElementType(): ElementType {
      return ElementType.SQUARE
    }
  }

  logger.warn('[main.tsx] - 注册正方形周长计算策略')
  strategyRegistry.registerPerimeterStrategy(new SquarePerimeterStrategy())

  // 椭圆的周长计算策略
  class EllipsePerimeterStrategyImpl extends EllipsePerimeterStrategy {
    public getElementType(): ElementType {
      return ElementType.ELLIPSE
    }
  }

  // 圆形的周长计算策略
  class CirclePerimeterStrategyImpl extends EllipsePerimeterStrategy {
    public getElementType(): ElementType {
      return ElementType.CIRCLE
    }
  }

  logger.warn('[main.tsx] - 注册椭圆周长计算策略')
  strategyRegistry.registerPerimeterStrategy(new EllipsePerimeterStrategyImpl())

  logger.warn('[main.tsx] - 注册圆形周长计算策略')
  strategyRegistry.registerPerimeterStrategy(new CirclePerimeterStrategyImpl())

  // 注册多边形周长计算策略（包括三角形、五边形和六边形）
  logger.warn('[main.tsx] - 注册多边形周长计算策略')
  strategyRegistry.registerPerimeterStrategy(new PolygonPerimeterStrategy())

  // 图片的周长计算策略（重写计算方法以支持IMAGE类型）
  class ImagePerimeterStrategy extends RectanglePerimeterStrategy {
    public getElementType(): ElementType {
      return ElementType.IMAGE
    }

    public calculatePerimeter(element: Element): number {
      if (element == null) {
        throw new CoreError(
          ErrorType.InvalidParameter,
          'Invalid element: null or undefined',
          undefined,
          { component: 'ImagePerimeterStrategy', operation: 'calculatePerimeter' },
        )
      }

      if (element.type !== ElementType.IMAGE) {
        throw new CoreError(
          ErrorType.InvalidElementType,
          `Expected element type IMAGE, got '${element.type}' for ID ${element.id}`,
          undefined,
          { component: 'ImagePerimeterStrategy', operation: 'calculatePerimeter', target: element.id },
        )
      }

      // 图片元素使用与矩形相同的属性结构
      const imageElement = element as { properties?: { width?: number, height?: number } }
      const width: unknown = imageElement.properties?.width
      const height: unknown = imageElement.properties?.height

      if (typeof width !== 'number' || typeof height !== 'number'
        || !Number.isFinite(width) || !Number.isFinite(height) || width < 0 || height < 0) {
        throw new CoreError(
          ErrorType.InvalidParameter,
          `Image element (ID: ${element.id}) must have finite, non-negative width and height in its properties. Received width: ${String(width)}, height: ${String(height)}`,
          undefined,
          { component: 'ImagePerimeterStrategy', operation: 'calculatePerimeter', target: element.id, metadata: { width: String(width), height: String(height) } },
        )
      }

      // 计算周长：2 × (width + height)
      const perimeter = 2 * (width + height)

      if (Number.isNaN(perimeter)) {
        throw new CoreError(
          ErrorType.ComputationError,
          `Image element (ID: ${element.id}) resulted in NaN perimeter calculation. Received width: ${String(width)}, height: ${String(height)}`,
          undefined,
          { component: 'ImagePerimeterStrategy', operation: 'calculatePerimeter', target: element.id, metadata: { width: String(width), height: String(height) } },
        )
      }

      return perimeter
    }
  }

  logger.warn('[main.tsx] - 注册图片周长计算策略')
  strategyRegistry.registerPerimeterStrategy(new ImagePerimeterStrategy())

  // 注册路径元素的周长计算策略
  logger.warn('[main.tsx] - 注册线段长度计算策略')
  strategyRegistry.registerPerimeterStrategy(new LinePerimeterStrategy())

  logger.warn('[main.tsx] - 注册折线长度计算策略')
  strategyRegistry.registerPerimeterStrategy(new PolylinePerimeterStrategy())

  logger.warn('[main.tsx] - 注册弧线长度计算策略')
  strategyRegistry.registerPerimeterStrategy(new ArcPerimeterStrategy())

  strategyRegistry.registerPerimeterStrategy(new QuadraticPerimeterStrategy())
  strategyRegistry.registerPerimeterStrategy(new CubicPerimeterStrategy())

  // 注册成本计算策略
  logger.warn('[main.tsx] 注册成本计算策略...')

  // 矩形的成本计算策略
  class RectangleCostStrategy extends AreaBasedCostStrategy {
    public calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
      // 优先从 properties 取值，兼容顶层
      const el = element as {
        properties?: Record<string, unknown>
        width?: number
        height?: number
        costUnitPrice?: number
        costMultiplierOrCount?: number
        costBasis?: string
      }
      const props = el.properties ?? {} as Record<string, unknown>
      const width = typeof props.width === 'number' ? props.width : (typeof el.width === 'number' ? el.width : undefined)
      const height = typeof props.height === 'number' ? props.height : (typeof el.height === 'number' ? el.height : undefined)

      // 兼容 costUnitPrice、costMultiplierOrCount、costBasis
      // 确保 costUnitPrice 和 costMultiplierOrCount 始终有有效的数值
      let costUnitPrice = 1
      if (typeof props.costUnitPrice === 'number') {
        costUnitPrice = props.costUnitPrice
      }
      else if (typeof el.costUnitPrice === 'number') {
        costUnitPrice = el.costUnitPrice
      }
      else if (typeof unitCost === 'number') {
        costUnitPrice = unitCost
      }

      let costMultiplierOrCount = 1 // 🔧 修复：默认为1，避免总成本为0
      if (typeof props.costMultiplierOrCount === 'number') {
        costMultiplierOrCount = props.costMultiplierOrCount
      }
      else if (typeof el.costMultiplierOrCount === 'number') {
        costMultiplierOrCount = el.costMultiplierOrCount
      }
      else if (typeof options?.quantity === 'number') {
        costMultiplierOrCount = options.quantity
      }

      const costBasis = typeof props.costBasis === 'string' ? props.costBasis : (typeof el.costBasis === 'string' ? el.costBasis : (typeof options?.costType === 'string' ? options.costType : 'area'))

      // 容错
      if (typeof width !== 'number' || typeof height !== 'number' || width <= 0 || height <= 0) {
        logger.error(`[RectangleCostStrategy][calculateCost] 无效的宽度或高度: ${width}, ${height}`)
        return 0
      }

      let measure = 0
      switch (costBasis) {
        case 'area':
          measure = width * height
          break
        case 'perimeter':
          measure = 2 * (width + height)
          break
        case 'unit':
          measure = 1
          break
        case 'segment':
          measure = 4
          break
        case 'fixed':
          measure = 1
          break
        default:
          logger.error(`[RectangleCostStrategy][calculateCost] 未知的成本基准: ${costBasis}`)
          return 0
      }

      // 计算总成本
      const totalCost = measure * Number(costUnitPrice) * Number(costMultiplierOrCount)

      // 更新元素的 costTotal 属性
      if (el.properties) {
        el.properties.costTotal = totalCost
      }

      return totalCost
    }

    getElementType() {
      return ElementType.RECTANGLE
    }
  }

  // 正方形的成本计算策略
  class SquareCostStrategy extends RectangleCostStrategy {
    getElementType() {
      return ElementType.SQUARE
    }
  }

  // 注册矩形的成本计算策略
  logger.warn('[main.tsx] 注册矩形的成本计算策略...')
  strategyRegistry.registerCostStrategy(new RectangleCostStrategy())

  // 注册正方形的成本计算策略
  logger.warn('[main.tsx] 注册正方形的成本计算策略...')
  strategyRegistry.registerCostStrategy(new SquareCostStrategy())

  // 椭圆的成本计算策略
  class EllipseCostStrategy extends AreaBasedCostStrategy {
    public calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
      // 优先从 properties 取值，兼容顶层
      const el = element as {
        properties?: Record<string, unknown>
        radiusX?: number
        radiusY?: number
        radius?: number
        costUnitPrice?: number
        costMultiplierOrCount?: number
        costBasis?: string
      }
      const props = el.properties ?? {}

      // 对于圆形元素，可能只有radius属性，需要同时处理radius和radiusX/radiusY
      let radiusX: number | undefined
      let radiusY: number | undefined

      // 先检查是否有radius属性（圆形元素）
      const radius = typeof props.radius === 'number' ? props.radius : (typeof el.radius === 'number' ? el.radius : undefined)

      if (typeof radius === 'number' && radius > 0) {
        // 如果有radius属性，则radiusX和radiusY都等于radius
        radiusX = radius
        radiusY = radius
      }
      else {
        // 否则尝试获取radiusX和radiusY
        radiusX = typeof props.radiusX === 'number' ? props.radiusX : (typeof el.radiusX === 'number' ? el.radiusX : undefined)
        radiusY = typeof props.radiusY === 'number' ? props.radiusY : (typeof el.radiusY === 'number' ? el.radiusY : undefined)
      }

      // 兼容 costUnitPrice、costMultiplierOrCount、costBasis
      // 确保 costUnitPrice 和 costMultiplierOrCount 始终有有效的数值
      let costUnitPrice = 1
      if (typeof props.costUnitPrice === 'number') {
        costUnitPrice = props.costUnitPrice
      }
      else if (typeof el.costUnitPrice === 'number') {
        costUnitPrice = el.costUnitPrice
      }
      else if (typeof unitCost === 'number') {
        costUnitPrice = unitCost
      }

      let costMultiplierOrCount = 1 // 🔧 修复：默认为1，避免总成本为0
      if (typeof props.costMultiplierOrCount === 'number') {
        costMultiplierOrCount = props.costMultiplierOrCount
      }
      else if (typeof el.costMultiplierOrCount === 'number') {
        costMultiplierOrCount = el.costMultiplierOrCount
      }
      else if (typeof options?.quantity === 'number') {
        costMultiplierOrCount = options.quantity
      }

      const costBasis = typeof props.costBasis === 'string' ? props.costBasis : (typeof el.costBasis === 'string' ? el.costBasis : (typeof options?.costType === 'string' ? options.costType : 'unit'))

      // 容错
      if (typeof radiusX !== 'number' || typeof radiusY !== 'number' || radiusX <= 0 || radiusY <= 0) {
        logger.error(`[EllipseCostStrategy][calculateCost] 无效的半径: ${radiusX}, ${radiusY}`)
        return 0
      }

      let measure = 0
      switch (costBasis) {
        case 'area':
          measure = Math.PI * radiusX * radiusY
          break
        case 'perimeter': {
          // 椭圆周长近似公式
          const a = Math.max(radiusX, radiusY)
          const b = Math.min(radiusX, radiusY)
          measure = 2 * Math.PI * Math.sqrt((a * a + b * b) / 2)
          break
        }
        case 'unit':
          measure = 1
          break
        case 'segment':
          measure = 1
          break
        case 'fixed':
          measure = 1
          break
        default:
          logger.error(`[EllipseCostStrategy][calculateCost] 未知的成本基准: ${costBasis}`)
          return 0
      }

      // 计算总成本
      const totalCost = measure * Number(costUnitPrice) * Number(costMultiplierOrCount)

      // 更新元素的 costTotal 属性
      if (el.properties) {
        el.properties.costTotal = totalCost
      }

      return totalCost
    }

    getElementType() {
      return ElementType.ELLIPSE
    }
  }

  // 圆形的成本计算策略
  class CircleCostStrategy extends EllipseCostStrategy {
    getElementType() { return ElementType.CIRCLE }
  }

  // 注册椭圆的成本计算策略
  logger.warn('[main.tsx] 注册椭圆的成本计算策略...')
  strategyRegistry.registerCostStrategy(new EllipseCostStrategy())

  // 注册圆形的成本计算策略
  logger.warn('[main.tsx] 注册圆形的成本计算策略...')
  strategyRegistry.registerCostStrategy(new CircleCostStrategy())

  // 文本元素的成本计算策略
  class TextCostStrategy extends UnitBasedCostStrategy {
    getElementType() { return ElementType.TEXT }
  }

  // 注册文本元素的成本计算策略
  logger.warn('[main.tsx] 注册文本元素的成本计算策略...')
  strategyRegistry.registerCostStrategy(new TextCostStrategy())

  // 图片的成本计算策略（使用矩形策略）
  class ImageCostStrategy extends RectangleCostStrategy {
    getElementType() {
      return ElementType.IMAGE
    }
  }

  // 注册图片的成本计算策略
  logger.warn('[main.tsx] 注册图片的成本计算策略...')
  strategyRegistry.registerCostStrategy(new ImageCostStrategy())

  // 注册多边形面积计算策略（包括三角形、五边形和六边形）
  logger.warn('[main.tsx] - 注册多边形面积计算策略')
  strategyRegistry.registerAreaStrategy(new PolygonAreaStrategy())

  // 注册多边形的成本计算策略（包括三角形、五边形和六边形）
  logger.warn('[main.tsx] - 注册多边形成本计算策略')
  strategyRegistry.registerCostStrategy(new PolygonCostStrategy())

  // 注册路径元素的成本计算策略
  // 线段的成本计算策略（支持多种成本基准）
  class LineCostStrategy extends PerimeterBasedCostStrategy {
    public calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
      // 优先从 properties 取值，兼容顶层
      const el = element as {
        properties?: Record<string, unknown>
        costUnitPrice?: number
        costMultiplierOrCount?: number
        costBasis?: string
      }
      const props = el.properties ?? {}

      // 兼容 costUnitPrice、costMultiplierOrCount、costBasis
      let costUnitPrice = 1
      if (typeof props.costUnitPrice === 'number') {
        costUnitPrice = props.costUnitPrice
      }
      else if (typeof el.costUnitPrice === 'number') {
        costUnitPrice = el.costUnitPrice
      }
      else if (typeof unitCost === 'number') {
        costUnitPrice = unitCost
      }

      let costMultiplierOrCount = 1 // 🔧 修复：默认为1，避免总成本为0
      if (typeof props.costMultiplierOrCount === 'number') {
        costMultiplierOrCount = props.costMultiplierOrCount
      }
      else if (typeof el.costMultiplierOrCount === 'number') {
        costMultiplierOrCount = el.costMultiplierOrCount
      }
      // 不要从 options.quantity 获取乘数值！这会导致乘数被几何量覆盖
      // else if (typeof options?.quantity === 'number') {
      //   costMultiplierOrCount = options.quantity
      // }

      const costBasis = typeof props.costBasis === 'string' ? props.costBasis : (typeof el.costBasis === 'string' ? el.costBasis : (typeof options?.costType === 'string' ? options.costType : 'perimeter'))

      let measure = 0
      switch (costBasis) {
        case 'perimeter': {
          // 直接调用线段长度计算策略
          try {
            measure = this.linePerimeterStrategy.calculatePerimeter(element as ShapeElement)
          }
          catch (error) {
            logger.error(`[LineCostStrategy][calculateCost] 线段长度计算失败: ${error}`)
            return 0
          }
          break
        }
        case 'unit':
          measure = 1
          break
        case 'segment':
          measure = 1
          break
        case 'fixed':
          measure = 1
          break
        default:
          logger.error(`[LineCostStrategy][calculateCost] 未知的成本基准: ${costBasis}`)
          return 0
      }

      // 计算总成本
      const totalCost = measure * Number(costUnitPrice) * Number(costMultiplierOrCount)

      // 更新元素的 costTotal 属性
      if (el.properties) {
        el.properties.costTotal = totalCost
      }

      return totalCost
    }

    getElementType() {
      return ElementType.LINE
    }
  }

  // 弧线的成本计算策略（支持多种成本基准）
  class ArcCostStrategy extends PerimeterBasedCostStrategy {
    public calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
      // 优先从 properties 取值，兼容顶层
      const el = element as {
        properties?: Record<string, unknown>
        radius?: number
        startAngle?: number
        endAngle?: number
        costUnitPrice?: number
        costMultiplierOrCount?: number
        costBasis?: string
      }
      const props = el.properties ?? {}

      // 兼容 costUnitPrice、costMultiplierOrCount、costBasis
      let costUnitPrice = 1
      if (typeof props.costUnitPrice === 'number') {
        costUnitPrice = props.costUnitPrice
      }
      else if (typeof el.costUnitPrice === 'number') {
        costUnitPrice = el.costUnitPrice
      }
      else if (typeof unitCost === 'number') {
        costUnitPrice = unitCost
      }

      let costMultiplierOrCount = 1 // 🔧 修复：默认为1，避免总成本为0
      if (typeof props.costMultiplierOrCount === 'number') {
        costMultiplierOrCount = props.costMultiplierOrCount
      }
      else if (typeof el.costMultiplierOrCount === 'number') {
        costMultiplierOrCount = el.costMultiplierOrCount
      }
      // 不要从 options.quantity 获取乘数值！这会导致乘数被几何量覆盖
      // else if (typeof options?.quantity === 'number') {
      //   costMultiplierOrCount = options.quantity
      // }

      const costBasis = typeof props.costBasis === 'string' ? props.costBasis : (typeof el.costBasis === 'string' ? el.costBasis : (typeof options?.costType === 'string' ? options.costType : 'perimeter'))

      let measure = 0
      switch (costBasis) {
        case 'perimeter': {
          // 直接调用圆弧周长计算策略
          try {
            measure = this.arcPerimeterStrategy.calculatePerimeter(element as ShapeElement)
          }
          catch (error) {
            logger.error(`[ArcCostStrategy][calculateCost] 圆弧周长计算失败: ${error}`)
            return 0
          }
          break
        }
        case 'unit':
          measure = 1
          break
        case 'segment':
          measure = 1
          break
        case 'fixed':
          measure = 1
          break
        default:
          logger.error(`[ArcCostStrategy][calculateCost] 未知的成本基准: ${costBasis}`)
          return 0
      }

      // 计算总成本
      const totalCost = measure * Number(costUnitPrice) * Number(costMultiplierOrCount)

      // 更新元素的 costTotal 属性
      if (el.properties) {
        el.properties.costTotal = totalCost
      }

      return totalCost
    }

    getElementType() {
      return ElementType.ARC
    }
  }

  logger.warn('[main.tsx] - 注册线段成本计算策略')
  strategyRegistry.registerCostStrategy(new LineCostStrategy())

  logger.warn('[main.tsx] - 注册折线成本计算策略')
  strategyRegistry.registerCostStrategy(new PolylineCostStrategy())

  logger.warn('[main.tsx] - 注册弧线成本计算策略')
  strategyRegistry.registerCostStrategy(new ArcCostStrategy())

  // 二次曲线的成本计算策略（支持多种成本基准）
  class QuadraticCostStrategy extends PerimeterBasedCostStrategy {
    public calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
      // 优先从 properties 取值，兼容顶层
      const el = element as {
        properties?: Record<string, unknown>
        costUnitPrice?: number
        costMultiplierOrCount?: number
        costBasis?: string
      }
      const props = el.properties ?? {}

      // 兼容 costUnitPrice、costMultiplierOrCount、costBasis
      let costUnitPrice = 1
      if (typeof props.costUnitPrice === 'number') {
        costUnitPrice = props.costUnitPrice
      }
      else if (typeof el.costUnitPrice === 'number') {
        costUnitPrice = el.costUnitPrice
      }
      else if (typeof unitCost === 'number') {
        costUnitPrice = unitCost
      }

      let costMultiplierOrCount = 1 // 🔧 修复：默认为1，避免总成本为0
      if (typeof props.costMultiplierOrCount === 'number') {
        costMultiplierOrCount = props.costMultiplierOrCount
      }
      else if (typeof el.costMultiplierOrCount === 'number') {
        costMultiplierOrCount = el.costMultiplierOrCount
      }
      // 不要从 options.quantity 获取乘数值！这会导致乘数被几何量覆盖
      // else if (typeof options?.quantity === 'number') {
      //   costMultiplierOrCount = options.quantity
      // }

      const costBasis = typeof props.costBasis === 'string' ? props.costBasis : (typeof el.costBasis === 'string' ? el.costBasis : (typeof options?.costType === 'string' ? options.costType : 'perimeter'))

      let measure = 0
      switch (costBasis) {
        case 'perimeter': {
          // 直接调用二次曲线长度计算策略
          try {
            measure = this.quadraticPerimeterStrategy.calculatePerimeter(element as ShapeElement)
          }
          catch (error) {
            logger.error(`[QuadraticCostStrategy][calculateCost] 二次曲线长度计算失败: ${error}`)
            return 0
          }
          break
        }
        case 'unit':
          measure = 1
          break
        case 'segment':
          measure = 1
          break
        case 'fixed':
          measure = 1
          break
        default:
          logger.error(`[QuadraticCostStrategy][calculateCost] 未知的成本基准: ${costBasis}`)
          return 0
      }

      // 计算总成本
      const totalCost = measure * Number(costUnitPrice) * Number(costMultiplierOrCount)

      // 更新元素的 costTotal 属性
      if (el.properties) {
        el.properties.costTotal = totalCost
      }

      return totalCost
    }

    getElementType() {
      return ElementType.QUADRATIC
    }
  }

  // 三次曲线的成本计算策略（支持多种成本基准）
  class CubicCostStrategy extends PerimeterBasedCostStrategy {
    public calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
      // 优先从 properties 取值，兼容顶层
      const el = element as {
        properties?: Record<string, unknown>
        costUnitPrice?: number
        costMultiplierOrCount?: number
        costBasis?: string
      }
      const props = el.properties ?? {}

      // 兼容 costUnitPrice、costMultiplierOrCount、costBasis
      let costUnitPrice = 1
      if (typeof props.costUnitPrice === 'number') {
        costUnitPrice = props.costUnitPrice
      }
      else if (typeof el.costUnitPrice === 'number') {
        costUnitPrice = el.costUnitPrice
      }
      else if (typeof unitCost === 'number') {
        costUnitPrice = unitCost
      }

      let costMultiplierOrCount = 1 // 🔧 修复：默认为1，避免总成本为0
      if (typeof props.costMultiplierOrCount === 'number') {
        costMultiplierOrCount = props.costMultiplierOrCount
      }
      else if (typeof el.costMultiplierOrCount === 'number') {
        costMultiplierOrCount = el.costMultiplierOrCount
      }
      // 不要从 options.quantity 获取乘数值！这会导致乘数被几何量覆盖
      // else if (typeof options?.quantity === 'number') {
      //   costMultiplierOrCount = options.quantity
      // }

      const costBasis = typeof props.costBasis === 'string' ? props.costBasis : (typeof el.costBasis === 'string' ? el.costBasis : (typeof options?.costType === 'string' ? options.costType : 'perimeter'))

      let measure = 0
      switch (costBasis) {
        case 'perimeter': {
          // 直接调用三次曲线长度计算策略
          try {
            measure = this.cubicPerimeterStrategy.calculatePerimeter(element as ShapeElement)
          }
          catch (error) {
            logger.error(`[CubicCostStrategy][calculateCost] 三次曲线长度计算失败: ${error}`)
            return 0
          }
          break
        }
        case 'unit':
          measure = 1
          break
        case 'segment':
          measure = 1
          break
        case 'fixed':
          measure = 1
          break
        default:
          logger.error(`[CubicCostStrategy][calculateCost] 未知的成本基准: ${costBasis}`)
          return 0
      }

      // 计算总成本
      const totalCost = measure * Number(costUnitPrice) * Number(costMultiplierOrCount)

      // 更新元素的 costTotal 属性
      if (el.properties) {
        el.properties.costTotal = totalCost
      }

      return totalCost
    }

    getElementType() {
      return ElementType.CUBIC
    }
  }

  strategyRegistry.registerCostStrategy(new QuadraticCostStrategy())
  strategyRegistry.registerCostStrategy(new CubicCostStrategy())

  // === 创建 ComputeFacade 并注入 registry ===
  const computeFacade = new ComputeFacade(strategyRegistry, shapeRepository, elementFactory)

  // Instantiate CoreCoordinator，传入 computeFacade
  const coreCoordinator = new CoreCoordinator(
    appEventBus,
    shapeRepository,
    validator,
    elementFactory,
    logger,
    errorService,
    undefined, // initialConfig
    computeFacade, // 注入 computeFacade
  )
  logger.warn('[main.tsx] CoreCoordinator instantiated:', coreCoordinator)

  const rootElement = document.getElementById('root')
  if (rootElement) {
    ReactDOM.createRoot(rootElement).render(
      <React.StrictMode>
        <App />
      </React.StrictMode>,
    )
  }
  else {
    logger.error('[main.tsx] Root element not found')
  }
}

// 导入调试工具（仅在开发环境）
// 注释掉调试导入，因为 debug 目录不存在
// if (process.env.NODE_ENV === 'development') {
//   import('./debug/test-rectangle-computation')
//   import('./debug/test-cost-properties')
//   import('./debug/test-cost-basis-issue')
//   import('./debug/test-checkbox-issue')
//   import('./debug/test-checkbox-realtime')
// }

// Handle the promise returned by main()
main().catch((error) => {
  console.error('[main.tsx] Unhandled error in main function:', error instanceof Error ? error.message : String(error))
})
