
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/lib/utils/style/styleUtils.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">src/lib/utils/style</a> styleUtils.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/120</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/120</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * Style Calculation Utility Functions
 *
 * @remarks
 * This module provides a collection of common utility functions for style calculations
 * and manipulations, such as merging CSS class names, creating dynamic visual effects
 * like shadows and gradients, and processing color values.
 *
 * Key features include:
 * - Merging CSS class names (e.g., with `cn` function, intended for use with `clsx` and `tailwind-merge`).
 * - Generating CSS for floating shadows (`createFloatingShadow`).
 * - Creating transparent colors and adjusting color brightness (`createTransparentColor`, `adjustColorBrightness`, `lightenColor`, `darkenColor`).
 * - Generating linear gradient backgrounds (`createGradientBackground`).
 * - Converting numbers to hex strings (`toHex`).
 * - Processing color strings to standard formats (`processColor`).
 * - Creating CSS styles for glassmorphism effects (`createGlassEffect`).
 *
 * @module lib/utils/style/styleUtils
 */
&nbsp;
// 假设 ClassValue, twMerge, clsx 将在需要时导入或全局可用。
// import { type ClassValue, clsx } from "clsx";
// import { twMerge } from "tailwind-merge";
&nbsp;
/**
 * Merges CSS class names using a simplified approach.
 *
 * @remarks
 * This is a simplified mock implementation. A production-ready version should typically
 * use libraries like `clsx` for conditional class joining and `tailwind-merge` (if using Tailwind CSS)
 * for resolving conflicting Tailwind utility classes.
 *
 * The current implementation filters out falsy values and joins the remaining inputs with a space.
 *
 * @param inputs - An array of class values (strings, arrays, objects). `any[]` is used for simplicity here.
 * @returns A string of combined CSS class names.
 *
 * @example
 * ```typescript
 * cn("class1", "class2", { class3: true, class4: false }, ["class5", "class6"]);
 * // Simplified output: "class1 class2 class3 class5 class6" (if object/array handling was complete)
 * // Current mock output: "class1 class2 [object Object] class5,class6"
 * ```
 */
<span class="cstat-no" title="statement not covered" >export function cn(...inputs: any[]): string {</span>
  // For a proper implementation, consider:
  // import { type ClassValue, clsx } from "clsx";
  // import { twMerge } from "tailwind-merge";
  // return twMerge(clsx(inputs));
<span class="cstat-no" title="statement not covered" >  return inputs.filter(Boolean).join(' ') // Simplified mock implementation</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Creates a CSS `box-shadow` string for a floating shadow effect.
 *
 * @param intensity - The intensity of the shadow, ranging from 1 to 5. Defaults to 3.
 *                    Higher values produce a more pronounced shadow.
 * @returns A CSS `box-shadow` string.
 * @throws {@link Error} if `intensity` is not a number.
 */
<span class="cstat-no" title="statement not covered" >export function createFloatingShadow(intensity = 3): string {</span>
<span class="cstat-no" title="statement not covered" >  if (typeof intensity !== 'number') {</span>
<span class="cstat-no" title="statement not covered" >    throw new TypeError('Intensity must be a number')</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  const clampedIntensity = Math.max(1, Math.min(intensity, 5))</span>
<span class="cstat-no" title="statement not covered" >  const yOffset = clampedIntensity * 2</span>
<span class="cstat-no" title="statement not covered" >  const blur = clampedIntensity * 4</span>
<span class="cstat-no" title="statement not covered" >  const spread = clampedIntensity * -1</span>
<span class="cstat-no" title="statement not covered" >  const opacity = 0.1 + clampedIntensity * 0.02</span>
<span class="cstat-no" title="statement not covered" >  return `${spread}px ${yOffset}px ${blur}px rgba(0,0,0,${opacity.toFixed(2)})`</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Converts a hexadecimal color string to an `rgba()` string with a specified alpha (transparency).
 *
 * @param color - The hexadecimal color string (e.g., '#FF0000', '#F00').
 * @param alpha - The alpha transparency value, ranging from 0 (fully transparent) to 1 (fully opaque).
 *                Defaults to 0.5.
 * @returns An `rgba()` color string. If the input `color` is not a valid hex format,
 *          a warning is logged, and the original color string is returned.
 * @throws {@link Error} if the `color` parameter is missing.
 */
<span class="cstat-no" title="statement not covered" >export function createTransparentColor(color: string, alpha = 0.5): string {</span>
<span class="cstat-no" title="statement not covered" >  if (!color) {</span>
<span class="cstat-no" title="statement not covered" >    throw new Error('Color parameter is required')</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  if (!/^#([0-9A-F]{3}){1,2}$/i.test(color)) {</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`createTransparentColor: Non-hex color "${color}" provided. Alpha transparency might not apply as expected.`)</span>
<span class="cstat-no" title="statement not covered" >    return color</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  const hex = color.replace('#', '')</span>
<span class="cstat-no" title="statement not covered" >  const r = Number.parseInt(hex.substring(0, 2), 16)</span>
<span class="cstat-no" title="statement not covered" >  const g = Number.parseInt(hex.substring(2, 4), 16)</span>
<span class="cstat-no" title="statement not covered" >  const b = Number.parseInt(hex.substring(4, 6), 16)</span>
<span class="cstat-no" title="statement not covered" >  return `rgba(${r}, ${g}, ${b}, ${alpha})`</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Adjusts the brightness of a hexadecimal color by a given factor.
 *
 * @param color - The hexadecimal color string (e.g., '#FF0000', '#F00').
 * @param factor - The brightness adjustment factor. Values greater than 1 lighten the color,
 *                 values less than 1 darken it. A factor of 1 leaves the color unchanged.
 *                 Negative factors are not recommended and might lead to black.
 * @returns A new hexadecimal color string with adjusted brightness.
 *          If the input `color` is not a valid hex format, a warning is logged,
 *          and the original color string is returned.
 * @throws {@link Error} if the `color` parameter is missing.
 */
<span class="cstat-no" title="statement not covered" >export function adjustColorBrightness(color: string, factor: number): string {</span>
<span class="cstat-no" title="statement not covered" >  if (!color) {</span>
<span class="cstat-no" title="statement not covered" >    throw new Error('Color parameter is required')</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  if (!/^#([0-9A-F]{3}){1,2}$/i.test(color)) {</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`adjustColorBrightness: Non-hex color "${color}" provided. Brightness adjustment might not apply as expected.`)</span>
<span class="cstat-no" title="statement not covered" >    return color</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  const hex = color.replace('#', '')</span>
<span class="cstat-no" title="statement not covered" >  let r = Number.parseInt(hex.substring(0, 2), 16)</span>
<span class="cstat-no" title="statement not covered" >  let g = Number.parseInt(hex.substring(2, 4), 16)</span>
<span class="cstat-no" title="statement not covered" >  let b = Number.parseInt(hex.substring(4, 6), 16)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  r = Math.max(0, Math.min(255, Math.round(r * factor)))</span>
<span class="cstat-no" title="statement not covered" >  g = Math.max(0, Math.min(255, Math.round(g * factor)))</span>
<span class="cstat-no" title="statement not covered" >  b = Math.max(0, Math.min(255, Math.round(b * factor)))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Lightens a hexadecimal color by a specified amount.
 *
 * @param color - The hexadecimal color string.
 * @param amount - The amount to lighten the color by, as a decimal between 0 and 1 (e.g., 0.2 for 20% lighter).
 *                 Defaults to 0.2. The absolute value of `amount` is used.
 * @returns A new, lightened hexadecimal color string.
 */
<span class="cstat-no" title="statement not covered" >export function lightenColor(color: string, amount: number = 0.2): string {</span>
<span class="cstat-no" title="statement not covered" >  const positiveAmount = Math.abs(amount) // Ensure amount is positive for lightening</span>
<span class="cstat-no" title="statement not covered" >  return adjustColorBrightness(color, 1 + positiveAmount)</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Darkens a hexadecimal color by a specified amount.
 *
 * @param color - The hexadecimal color string.
 * @param amount - The amount to darken the color by, as a decimal between 0 and 1 (e.g., 0.2 for 20% darker).
 *                 Defaults to 0.2. The absolute value of `amount` is used.
 * @returns A new, darkened hexadecimal color string.
 */
<span class="cstat-no" title="statement not covered" >export function darkenColor(color: string, amount: number = 0.2): string {</span>
<span class="cstat-no" title="statement not covered" >  const positiveAmount = Math.abs(amount) // Ensure amount is positive for darkening factor</span>
<span class="cstat-no" title="statement not covered" >  return adjustColorBrightness(color, 1 - positiveAmount)</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Creates a CSS `linear-gradient` background string.
 *
 * @param startColor - The starting color of the gradient.
 * @param endColor - The ending color of the gradient.
 * @param direction - The direction of the gradient in degrees. Defaults to 135.
 * @param opacity - The overall opacity of the gradient layer (0-1). Defaults to 1.
 *                  If less than 1, an `opacity` CSS property might be more appropriate
 *                  unless a semi-transparent gradient overlay is intended.
 *                  The current implementation appends an `opacity` style if `&lt; 1`, which is unusual for `linear-gradient`.
 *                  Consider applying opacity to the element itself or using rgba colors.
 * @returns A CSS `linear-gradient()` string. If opacity is less than 1, it appends an `opacity` style,
 *          which might not be standard for a background image property.
 * @throws {@link Error} if `startColor` or `endColor` parameters are missing.
 */
<span class="cstat-no" title="statement not covered" >export function createGradientBackground(</span>
<span class="cstat-no" title="statement not covered" >  startColor: string,</span>
<span class="cstat-no" title="statement not covered" >  endColor: string,</span>
<span class="cstat-no" title="statement not covered" >  direction: number = 135,</span>
<span class="cstat-no" title="statement not covered" >  opacity: number = 1,</span>
<span class="cstat-no" title="statement not covered" >): string {</span>
<span class="cstat-no" title="statement not covered" >  if (!startColor || !endColor) {</span>
<span class="cstat-no" title="statement not covered" >    throw new Error('Both startColor and endColor parameters are required')</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  const safeOpacity = Math.max(0, Math.min(1, opacity))</span>
<span class="cstat-no" title="statement not covered" >  return `linear-gradient(${direction}deg, ${startColor}, ${endColor}) ${safeOpacity &lt; 1 ? `opacity: ${safeOpacity}` : ''}`.trim()</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Converts a number (0-255) to its two-digit hexadecimal string representation.
 * @param c - The number to convert.
 * @returns A two-digit hexadecimal string (e.g., 10 -&gt; "0a", 255 -&gt; "ff").
 */
<span class="cstat-no" title="statement not covered" >export function toHex(c: number): string {</span>
<span class="cstat-no" title="statement not covered" >  const hex = Math.max(0, Math.min(255, Math.round(c))).toString(16) // Ensure c is in 0-255 range</span>
<span class="cstat-no" title="statement not covered" >  return hex.length === 1 ? `0${hex}` : hex</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Processes a color string (hex format) and applies an opacity, returning an `rgba` or hex string.
 *
 * @param color - The color string, expected in hex format (e.g., '#RGB', '#RRGGBB', 'RGB', 'RRGGBB').
 * @param opacity - The opacity level, from 0 (transparent) to 1 (opaque). Defaults to 1.
 * @returns An `rgba(r,g,b,a)` string if `opacity` is less than 1, otherwise a full hex string (e.g., '#RRGGBB').
 *          Returns the original color string with a warning if the input `color` is not a valid hex format.
 */
<span class="cstat-no" title="statement not covered" >export function processColor(color: string, opacity: number = 1): string {</span>
<span class="cstat-no" title="statement not covered" >  const hex = color.startsWith('#') ? color.substring(1) : color</span>
<span class="cstat-no" title="statement not covered" >  const validOpacity = Math.max(0, Math.min(1, opacity))</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (!/^[0-9A-F]{3}$|^[0-9A-F]{6}$/i.test(hex)) {</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`processColor: Invalid hex color format: ${color}. Returning original color.`)</span>
<span class="cstat-no" title="statement not covered" >    return color</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  let r: number, g: number, b: number</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (hex.length === 3) {</span>
<span class="cstat-no" title="statement not covered" >    r = Number.parseInt(hex[0] + hex[0], 16)</span>
<span class="cstat-no" title="statement not covered" >    g = Number.parseInt(hex[1] + hex[1], 16)</span>
<span class="cstat-no" title="statement not covered" >    b = Number.parseInt(hex[2] + hex[2], 16)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  else {</span>
<span class="cstat-no" title="statement not covered" >    r = Number.parseInt(hex.substring(0, 2), 16)</span>
<span class="cstat-no" title="statement not covered" >    g = Number.parseInt(hex.substring(2, 4), 16)</span>
<span class="cstat-no" title="statement not covered" >    b = Number.parseInt(hex.substring(4, 6), 16)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return validOpacity &lt; 1</span>
<span class="cstat-no" title="statement not covered" >    ? `rgba(${r}, ${g}, ${b}, ${validOpacity})`</span>
<span class="cstat-no" title="statement not covered" >    : `#${hex.length === 3 ? `${hex[0]}${hex[0]}${hex[1]}${hex[1]}${hex[2]}${hex[2]}` : hex}`</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Creates a CSS style object for a glassmorphism effect.
 *
 * @param opacity - The opacity of the background color (value between 0 and 1). Defaults to 0.8.
 * @param blur - The blur radius in pixels for the `backdrop-filter`. Defaults to 4.
 *               Negative values will be converted to positive.
 * @returns A style object compatible with React's `style` prop (or for direct CSS use),
 *          containing `backgroundColor`, `backdropFilter`, `border`, and `boxShadow` properties.
 * @throws {@link Error} if `opacity` or `blur` is not a valid number (e.g., NaN).
 */
<span class="cstat-no" title="statement not covered" >export function createGlassEffect(opacity = 0.8, blur = 4): Record&lt;string, string&gt; {</span>
<span class="cstat-no" title="statement not covered" >  if (typeof opacity !== 'number') {</span>
<span class="cstat-no" title="statement not covered" >    throw new TypeError('Opacity must be a number')</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  if (isNaN(opacity)) {</span>
<span class="cstat-no" title="statement not covered" >    throw new TypeError('Opacity cannot be NaN')</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  const validOpacity = Math.max(0, Math.min(1, opacity))</span>
<span class="cstat-no" title="statement not covered" >  if (validOpacity !== opacity) {</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`Opacity value ${opacity} was clamped to ${validOpacity} (valid range: 0-1)`)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (typeof blur !== 'number') {</span>
<span class="cstat-no" title="statement not covered" >    throw new TypeError('Blur must be a number')</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  if (isNaN(blur)) {</span>
<span class="cstat-no" title="statement not covered" >    throw new TypeError('Blur cannot be NaN')</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  if (blur &lt; 0) {</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`Negative blur value ${blur} was converted to positive ${Math.abs(blur)}`)</span>
<span class="cstat-no" title="statement not covered" >    blur = Math.abs(blur)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return {</span>
<span class="cstat-no" title="statement not covered" >    backgroundColor: `rgba(255, 255, 255, ${validOpacity})`,</span>
<span class="cstat-no" title="statement not covered" >    backdropFilter: `blur(${blur}px)`,</span>
<span class="cstat-no" title="statement not covered" >    border: '1px solid rgba(255, 255, 255, 0.2)',</span>
<span class="cstat-no" title="statement not covered" >    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.05), 0 2px 8px rgba(0, 0, 0, 0.05)',</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Alias for the {@link createGlassEffect} function.
 */
<span class="cstat-no" title="statement not covered" >export const glassEffect = createGlassEffect</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:28:25.559Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    