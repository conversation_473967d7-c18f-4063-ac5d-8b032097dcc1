import { describe, it, expect, vi, beforeEach, beforeAll } from 'vitest';
import { ExportEvent, AppEventType } from '@/types/services/events';

// 打桩 data/export 以外的所有非类型模块
vi.mock('@/services/', () => ({
  appEventBus: {},
  EventBus: class { },
  DataEvents: {
    publishExportRequest: vi.fn(),
  },
  DataSubscribers: {
    subscribeToExportEvents: vi.fn(),
  },
}));
vi.mock('@/data/export/blob', () => ({
  toSvgBlob: vi.fn(() => 'svg-blob'),
  toPngBlob: vi.fn(() => 'png-blob'),
  toPdfBlob: vi.fn(() => 'pdf-blob'),
}));

// 重新引入被测模块
import '@/data/export/handlers';
import { DataEvents, DataSubscribers, appEventBus } from '@/services/';
import { toSvgBlob, toPngBlob, toPdfBlob } from '@/data/export/blob';

let handleExportRequest: (event: ExportEvent) => void;
let handleExportProgress: (event: ExportEvent) => void;
const rectangle = { type: 'RECTANGLE', position: { x: 0, y: 0 }, width: 10, height: 10 };

describe('handler.ts', () => {
  beforeAll(async () => {
    handleExportRequest = (DataSubscribers.subscribeToExportEvents as any).mock.calls[0][1];
    handleExportProgress = (DataSubscribers.subscribeToExportEvents as any).mock.calls[1][1];
  });

  beforeEach(() => {
    vi.resetAllMocks();
  });

  describe('handleExportRequest', () => {
    it('should publish prepare event', () => {
      const event: ExportEvent = {
        type: AppEventType.ExportRequest,
        payload: {
          format: 'svg',
          elements: [],
        }
      };
      handleExportRequest(event);
      expect(DataEvents.publishExportRequest).toHaveBeenCalledWith(
        appEventBus,
        'prepare',
        event.payload
      );
    });

    it('should publish error event if exception thrown', () => {
      (DataEvents.publishExportRequest as any).mockImplementationOnce(() => { throw new Error('fail'); });
      const event: ExportEvent = {
        type: AppEventType.ExportRequest,
        payload: {
          format: 'svg',
          elements: [],
        }
      };
      handleExportRequest(event);
      expect(DataEvents.publishExportRequest).toHaveBeenCalledWith(
        appEventBus,
        'error',
        expect.objectContaining({
          format: 'svg',
          elements: [],
          error: expect.stringContaining('Failed to prepare export')
        })
      );
    });
  });

  describe('handleExportProgress', () => {
    it('should publish complete event for svg', () => {
      const event: ExportEvent = {
        type: AppEventType.ExportProgress,
        payload: {
          format: 'svg',
          elements: [rectangle as any]
        }
      };
      handleExportProgress(event);

      expect(toSvgBlob).toHaveBeenCalledWith([rectangle]);
      expect(DataEvents.publishExportRequest).toHaveBeenCalledWith(
        appEventBus,
        'complete',
        expect.objectContaining({
          data: 'svg-blob'
        })
      );
    });

    it('should publish complete event for png', () => {
      const event: ExportEvent = {
        type: AppEventType.ExportProgress,
        payload: {
          format: 'png',
          elements: [rectangle as any]
        }
      };
      handleExportProgress(event);

      expect(toPngBlob).toHaveBeenCalledWith([rectangle]);
      expect(DataEvents.publishExportRequest).toHaveBeenCalledWith(
        appEventBus,
        'complete',
        expect.objectContaining({
          data: 'png-blob'
        })
      );
    });

    it('should publish complete event for pdf', () => {
      const event: ExportEvent = {
        type: AppEventType.ExportProgress,
        payload: {
          format: 'pdf',
          elements: [rectangle as any]
        }
      };
      handleExportProgress(event);

      expect(toPdfBlob).toHaveBeenCalledWith([rectangle]);
      expect(DataEvents.publishExportRequest).toHaveBeenCalledWith(
        appEventBus,
        'complete',
        expect.objectContaining({
          data: 'pdf-blob'
        })
      );
    });

    it('should publish error if elements missing', () => {
      const event: ExportEvent = {
        type: AppEventType.ExportProgress,
        payload: {
          format: 'svg'
          // Missing 'elements'
        }
      };
      handleExportProgress(event);

      expect(DataEvents.publishExportRequest).toHaveBeenCalledWith(
        appEventBus,
        'error',
        expect.objectContaining({
          error: expect.stringContaining('Missing export content')
        })
      );
    });

    it('should publish error if format not supported', () => {
      const rectangle = { type: 'RECTANGLE', position: { x: 0, y: 0 }, width: 10, height: 10 }
      const event: ExportEvent = {
        type: AppEventType.ExportProgress,
        payload: {
          format: 'txt',
          elements: [rectangle as any]
        }
      };
      handleExportProgress(event);

      expect(DataEvents.publishExportRequest).toHaveBeenCalledWith(
        appEventBus,
        'error',
        expect.objectContaining({
          error: expect.stringContaining('Unsupported export format')
        })
      );
    });

    it('should publish error if blob function throws', () => {
      (toSvgBlob as any).mockImplementationOnce(() => { throw new Error('blob fail'); });
      const event = {
        payload: {
          format: 'svg',
          content: { elements: [1], width: 1, height: 1 }
        }
      } as unknown as ExportEvent;
      handleExportProgress(event);
      expect(DataEvents.publishExportRequest).toHaveBeenCalledWith(
        appEventBus,
        'error',
        expect.objectContaining({
          error: expect.stringContaining('Failed to export')
        })
      );
    });
  });
});