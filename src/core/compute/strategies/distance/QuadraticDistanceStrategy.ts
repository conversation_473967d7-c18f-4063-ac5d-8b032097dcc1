/**
 * Distance Calculation Strategy for Quadratic Bezier Curve Elements
 *
 * @remarks
 * This strategy implements the {@link DistanceCalculatorStrategy} for calculating
 * the distance between a Quadratic Bezier curve element ({@link CoreElementType.QUADRATIC})
 * and another {@link ShapeElement}.
 *
 * It currently supports calculating the distance between:
 * - A Quadratic Bezier curve and a Line element ({@link CoreElementType.LINE}).
 *   The actual calculation for this case is marked as FIXME, as the utility function
 *   `calculateQuadraticToLineDistance` is not currently implemented or imported.
 *
 * The strategy validates the types of the input elements and their geometric
 * properties (e.g., control points, start/end points, position).
 * It converts relative points to absolute coordinates before attempting distance calculation.
 *
 * @module core/compute/strategies/distance/QuadraticDistanceStrategy
 * @see {@link Path.Quadratic}
 * @see {@link Path.Line}
 * @see {@link DistanceCalculatorStrategy}
 */
// import type { PointData as IPoint } from '../../../../types/core/element/geometry/point'; // Unused
import type { DistanceCalculatorStrategy } from '../../../../types/core/compute'
import type {
  Path,
  ShapeElement,
} from '../../../../types/core/elementDefinitions'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'
// FIXME: calculateQuadraticToLineDistance function is assumed to be in bezierUtils but is not currently exported.
// import { calculateQuadraticToLineDistance } from '../../../../lib/utils/geometry/bezierUtils';

export class QuadraticDistanceStrategy implements DistanceCalculatorStrategy {
  /**
   * Calculates the distance between a Quadratic Bezier curve element and another {@link ShapeElement}.
   *
   * @param element1 - The first shape element, which must be of type {@link CoreElementType.QUADRATIC}.
   * @param element2 - The second shape element. Currently supports {@link CoreElementType.LINE}.
   * @returns The calculated distance. Returns `Number.MAX_VALUE` as a placeholder if the
   *          specific distance calculation (e.g., Quadratic-to-Line) is not yet implemented.
   * @throws {@link CoreError} if `element1` is not a QUADRATIC curve.
   * @throws {@link CoreError} if `element2` is of an unsupported type for distance calculation with a QUADRATIC curve.
   * @throws {@link CoreError} if either element has invalid or missing geometric properties.
   */
  public calculateDistance(element1: ShapeElement, element2: ShapeElement): number {
    if (element1.type !== CoreElementType.QUADRATIC) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `QuadraticDistanceStrategy's first element must be QUADRATIC, got ${element1.type}`,
        undefined,
        { component: 'QuadraticDistanceStrategy', operation: 'calculateDistance', target: element1.id },
      )
    }

    const quadraticElement = element1 as unknown as Path.Quadratic // Added unknown for type safety
    const p0_rel = quadraticElement.properties.start
    const p1_rel = quadraticElement.properties.control
    const p2_rel = quadraticElement.properties.end

    if (p0_rel === null || p0_rel === undefined || p1_rel === null || p1_rel === undefined || p2_rel === null || p2_rel === undefined
      || typeof p0_rel.x !== 'number' || typeof p0_rel.y !== 'number' || !Number.isFinite(p0_rel.x) || !Number.isFinite(p0_rel.y)
      || typeof p1_rel.x !== 'number' || typeof p1_rel.y !== 'number' || !Number.isFinite(p1_rel.x) || !Number.isFinite(p1_rel.y)
      || typeof p2_rel.x !== 'number' || typeof p2_rel.y !== 'number' || !Number.isFinite(p2_rel.x) || !Number.isFinite(p2_rel.y)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid quadratic curve points for element ID ${element1.id}`,
        undefined,
        { component: 'QuadraticDistanceStrategy', operation: 'calculateDistance', target: element1.id, metadata: { p0_rel, p1_rel, p2_rel } },
      )
    }

    // const el1Pos = element1.position; // Unused
    // const p0_abs: IPoint = { x: p0_rel.x + el1Pos.x, y: p0_rel.y + el1Pos.y, z: p0_rel.z };
    // const p1_abs: IPoint = { x: p1_rel.x + el1Pos.x, y: p1_rel.y + el1Pos.y, z: p1_rel.z };
    // const p2_abs: IPoint = { x: p2_rel.x + el1Pos.x, y: p2_rel.y + el1Pos.y, z: p2_rel.z };

    switch (element2.type) {
      case CoreElementType.LINE: {
        const lineElement = element2 as unknown as Path.Line // Added unknown for type safety
        const lineStart_rel = lineElement.properties.start
        const lineEnd_rel = lineElement.properties.end
        const el2Pos = element2.position

        if (lineStart_rel === null || lineStart_rel === undefined || lineEnd_rel === null || lineEnd_rel === undefined || el2Pos === null || el2Pos === undefined
          || typeof lineStart_rel.x !== 'number' || typeof lineStart_rel.y !== 'number' || !Number.isFinite(lineStart_rel.x) || !Number.isFinite(lineStart_rel.y)
          || typeof lineEnd_rel.x !== 'number' || typeof lineEnd_rel.y !== 'number' || !Number.isFinite(lineEnd_rel.x) || !Number.isFinite(lineEnd_rel.y)
          || typeof el2Pos.x !== 'number' || typeof el2Pos.y !== 'number' || !Number.isFinite(el2Pos.x) || !Number.isFinite(el2Pos.y)) {
          throw new CoreError(
            ErrorType.InvalidParameter,
            `Target line element (ID: ${element2.id}) has invalid start/end points or position.`,
            undefined,
            { component: 'QuadraticDistanceStrategy', operation: 'calculateDistance', target: element2.id, metadata: { lineStart_rel, lineEnd_rel, el2Pos } },
          )
        }
        // const lineStart_abs: IPoint = { x: lineStart_rel.x + el2Pos.x, y: lineStart_rel.y + el2Pos.y, z: lineStart_rel.z };
        // const lineEnd_abs: IPoint = { x: lineEnd_rel.x + el2Pos.x, y: lineEnd_rel.y + el2Pos.y, z: lineEnd_rel.z };

        // FIXME: calculateQuadraticToLineDistance function is not currently available in bezierUtils.
        // This will cause a runtime error if not implemented and exported.
        // Returning a placeholder or throwing a specific "not implemented" error.
        console.warn(`[QuadraticDistanceStrategy] FIXME: 'calculateQuadraticToLineDistance' is not implemented or imported from geometry utils.`)
        // return calculateQuadraticToLineDistance(p0_abs, p1_abs, p2_abs, lineStart_abs, lineEnd_abs);
        return Number.MAX_VALUE // Placeholder for not implemented
      }

      default:
        throw new CoreError(
          ErrorType.InvalidElementType,
          `Distance calculation between QUADRATIC and ${element2.type} is not supported by QuadraticDistanceStrategy.`,
          undefined,
          { component: 'QuadraticDistanceStrategy', operation: 'calculateDistance', metadata: { type1: element1.type, type2: element2.type } },
        )
    }
  }

  /**
   * Returns the primary element type that this strategy handles as the first argument.
   * @returns {@link CoreElementType.QUADRATIC} - The quadratic Bezier curve element type
   */
  public getElementType(): CoreElementType {
    return CoreElementType.QUADRATIC
  }
}
