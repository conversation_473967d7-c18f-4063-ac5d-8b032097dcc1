import { beforeEach, describe, expect, it, vi } from 'vitest'
import { CubicAreaStrategy } from '@/core/compute/strategies/area/CubicAreaStrategy'
import { CoreError } from '@/services/system/error-service'
import { ElementType } from '@/types/core/elementDefinitions'

// Mock the utility functions
vi.mock('@/lib/utils/geometry/bezierUtils', () => ({
  sampleCubicBezier: vi.fn((p0, p1, p2, p3, numSegments) => {
    // Simple mock: return a square-like polygon
    return [
      { x: 0, y: 0 },
      { x: 10, y: 0 },
      { x: 10, y: 10 },
      { x: 0, y: 10 },
    ]
  }),
}))

vi.mock('@/lib/utils/geometry/polygonUtils', () => ({
  calculateArea: vi.fn((points) => {
    // Simple mock: return area of 100 for the square
    return 100
  }),
}))

describe('cubicAreaStrategy', () => {
  let strategy: CubicAreaStrategy

  beforeEach(() => {
    strategy = new CubicAreaStrategy()
    vi.clearAllMocks()
  })

  describe('constructor and Basic Properties', () => {
    it('should be defined and instantiated', () => {
      expect(strategy).toBeDefined()
      expect(strategy).toBeInstanceOf(CubicAreaStrategy)
    })

    it('should return correct element type', () => {
      expect(strategy.getElementType()).toBe(ElementType.CUBIC)
    })

    it('should have required methods', () => {
      expect(typeof strategy.calculateArea).toBe('function')
      expect(typeof strategy.getElementType).toBe('function')
    })
  })

  describe('area Calculation', () => {
    it('should calculate area for closed cubic curve correctly', () => {
      const cubicElement = {
        id: 'cubic-1',
        type: ElementType.CUBIC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      const area = strategy.calculateArea(cubicElement as any)
      expect(area).toBe(100) // From our mock
    })

    it('should return 0 for open cubic curve', () => {
      const cubicElement = {
        id: 'cubic-2',
        type: ElementType.CUBIC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: false,
        },
      }

      const area = strategy.calculateArea(cubicElement as any)
      expect(area).toBe(0)
    })

    it('should handle missing closed property (defaults to false)', () => {
      const cubicElement = {
        id: 'cubic-no-closed',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          // closed property missing
        },
      }

      const area = strategy.calculateArea(cubicElement as any)
      expect(area).toBe(0)
    })

    it('should call bezier sampling with correct parameters', () => {
      const { sampleCubicBezier } = require('@/lib/utils/geometry/bezierUtils')

      const cubicElement = {
        id: 'cubic-test',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      strategy.calculateArea(cubicElement as any)

      expect(sampleCubicBezier).toHaveBeenCalledWith(
        { x: 0, y: 0 },
        { x: 5, y: -5 },
        { x: 15, y: -5 },
        { x: 20, y: 0 },
        30, // numSegments
      )
    })
  })

  describe('error Handling', () => {
    it('should throw error for non-cubic element', () => {
      const rectangleElement = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        properties: {},
      }

      expect(() => strategy.calculateArea(rectangleElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculateArea(rectangleElement as any))
        .toThrow('CubicAreaStrategy can only calculate area for CUBIC elements')
    })

    it('should throw error for missing start point', () => {
      const cubicElement = {
        id: 'cubic-no-start',
        type: ElementType.CUBIC,
        properties: {
          start: null,
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(cubicElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculateArea(cubicElement as any))
        .toThrow('Invalid cubic curve points')
    })

    it('should throw error for invalid start point coordinates', () => {
      const cubicElement = {
        id: 'cubic-invalid-start',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 'invalid', y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(cubicElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for missing control1 point', () => {
      const cubicElement = {
        id: 'cubic-no-control1',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: undefined,
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(cubicElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for invalid control2 point coordinates', () => {
      const cubicElement = {
        id: 'cubic-invalid-control2',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: Number.NaN },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(cubicElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for missing end point', () => {
      const cubicElement = {
        id: 'cubic-no-end',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: null,
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(cubicElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for invalid end point coordinates', () => {
      const cubicElement = {
        id: 'cubic-invalid-end',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: Infinity, y: 0 },
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(cubicElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for points with missing coordinates', () => {
      const cubicElement = {
        id: 'cubic-missing-coords',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0 }, // missing y
          control1: { x: 5, y: -5 },
          control2: { x: 15, y: -5 },
          end: { x: 20, y: 0 },
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(cubicElement as any))
        .toThrow(CoreError)
    })
  })

  describe('edge Cases', () => {
    it('should handle points with zero coordinates', () => {
      const cubicElement = {
        id: 'cubic-zero-coords',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0, y: 0 },
          control1: { x: 0, y: 0 },
          control2: { x: 0, y: 0 },
          end: { x: 0, y: 0 },
          closed: true,
        },
      }

      const area = strategy.calculateArea(cubicElement as any)
      expect(area).toBe(100) // From our mock
    })

    it('should handle negative coordinates', () => {
      const cubicElement = {
        id: 'cubic-negative-coords',
        type: ElementType.CUBIC,
        properties: {
          start: { x: -10, y: -10 },
          control1: { x: -5, y: -15 },
          control2: { x: 5, y: -15 },
          end: { x: 10, y: -10 },
          closed: true,
        },
      }

      const area = strategy.calculateArea(cubicElement as any)
      expect(area).toBe(100) // From our mock
    })

    it('should handle very large coordinates', () => {
      const cubicElement = {
        id: 'cubic-large-coords',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 1000000, y: 1000000 },
          control1: { x: 1000005, y: 999995 },
          control2: { x: 1000015, y: 999995 },
          end: { x: 1000020, y: 1000000 },
          closed: true,
        },
      }

      const area = strategy.calculateArea(cubicElement as any)
      expect(area).toBe(100) // From our mock
    })

    it('should handle very small coordinates', () => {
      const cubicElement = {
        id: 'cubic-small-coords',
        type: ElementType.CUBIC,
        properties: {
          start: { x: 0.001, y: 0.001 },
          control1: { x: 0.005, y: -0.004 },
          control2: { x: 0.015, y: -0.004 },
          end: { x: 0.020, y: 0.001 },
          closed: true,
        },
      }

      const area = strategy.calculateArea(cubicElement as any)
      expect(area).toBe(100) // From our mock
    })
  })
})
