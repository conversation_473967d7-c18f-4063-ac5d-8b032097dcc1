import type { ShapeElement } from '@/types/core/element/element'
import type {
  Decagon,
  Heptagon,
  Hexagon,
  Nonagon,
  Octagon,
  Pentagon,
  Polygon,
  Quadrilateral,
  Triangle,
} from '@/types/core/element/shape/shape'
import { describe, expect, it } from 'vitest'

describe('polygon Interface', () => {
  it('should define a polygon with required properties', () => {
    // Create a valid Polygon object
    const polygon: Polygon = {
      id: 'polygon-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 100, y: 100 },
        { x: 0, y: 100 },
      ],
      sides: 4,
      isRegular: false,
    }

    // Verify the object is valid
    expect(polygon.id).toBe('polygon-1')
    expect(polygon.type).toBe('shape')
    expect(polygon.points.length).toBe(4)
    expect(polygon.sides).toBe(4)
    expect(polygon.isRegular).toBe(false)
  })

  it('should extend ShapeElement interface', () => {
    // Create a polygon
    const polygon: Polygon = {
      id: 'polygon-2',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 100, y: 100 },
        { x: 0, y: 100 },
      ],
      sides: 4,
      isRegular: true,
    }

    // Verify the polygon extends ShapeElement
    const shapeElement: ShapeElement = polygon
    expect(shapeElement.id).toBe('polygon-2')
    expect(shapeElement.type).toBe('shape')
    expect(shapeElement.visible).toBe(true)
    expect(shapeElement.locked).toBe(false)
    expect(shapeElement.position.x).toBe(100)
    expect(shapeElement.position.y).toBe(100)
  })

  it('should allow creating regular and irregular polygons', () => {
    // Create a regular polygon
    const regularPolygon: Polygon = {
      id: 'regular',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 100, y: 0 },
        { x: 150, y: 25 },
        { x: 150, y: 75 },
        { x: 100, y: 100 },
        { x: 50, y: 75 },
        { x: 50, y: 25 },
      ],
      sides: 6,
      isRegular: true,
    }

    // Create an irregular polygon
    const irregularPolygon: Polygon = {
      id: 'irregular',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 150, y: 50 },
        { x: 100, y: 100 },
        { x: 0, y: 100 },
      ],
      sides: 5,
      isRegular: false,
    }

    // Verify both polygons are valid
    expect(regularPolygon.isRegular).toBe(true)
    expect(regularPolygon.sides).toBe(6)
    expect(regularPolygon.points.length).toBe(6)

    expect(irregularPolygon.isRegular).toBe(false)
    expect(irregularPolygon.sides).toBe(5)
    expect(irregularPolygon.points.length).toBe(5)
  })

  it('should be usable in arrays and collections', () => {
    // Create an array of polygons
    const polygons: Polygon[] = [
      {
        id: 'polygon-3',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 100, y: 100 },
        rotation: 0,
        scale: { x: 1, y: 1 },
        selectable: true,
        draggable: true,
        showHandles: true,
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 100, y: 100 },
          { x: 0, y: 100 },
        ],
        sides: 4,
        isRegular: true,
      },
      {
        id: 'polygon-4',
        type: 'shape',
        visible: true,
        locked: false,
        position: { x: 200, y: 200 },
        rotation: 0,
        scale: { x: 1, y: 1 },
        selectable: true,
        draggable: true,
        showHandles: true,
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 0 },
          { x: 150, y: 50 },
          { x: 100, y: 100 },
          { x: 0, y: 100 },
        ],
        sides: 5,
        isRegular: false,
      },
    ]

    // Verify the array is valid
    expect(polygons.length).toBe(2)
    expect(polygons[0].id).toBe('polygon-3')
    expect(polygons[0].sides).toBe(4)
    expect(polygons[0].isRegular).toBe(true)

    expect(polygons[1].id).toBe('polygon-4')
    expect(polygons[1].sides).toBe(5)
    expect(polygons[1].isRegular).toBe(false)
  })

  it('should be usable in functions that require Polygon', () => {
    // Define a function that uses Polygon
    function calculatePolygonPerimeter(polygon: Polygon): number {
      let perimeter = 0
      const points = polygon.points

      for (let i = 0; i < points.length; i++) {
        const currentPoint = points[i]
        const nextPoint = points[(i + 1) % points.length]

        const dx = nextPoint.x - currentPoint.x
        const dy = nextPoint.y - currentPoint.y

        perimeter += Math.sqrt(dx * dx + dy * dy)
      }

      return perimeter
    }

    // Create a polygon
    const polygon: Polygon = {
      id: 'polygon-5',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 100, y: 100 },
        { x: 0, y: 100 },
      ],
      sides: 4,
      isRegular: true,
    }

    // Test the function
    const perimeter = calculatePolygonPerimeter(polygon)
    expect(perimeter).toBe(400) // 100 + 100 + 100 + 100 = 400
  })

  it('should be compatible with JSON serialization/deserialization', () => {
    // Create a polygon
    const polygon: Polygon = {
      id: 'polygon-6',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 100, y: 100 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 100, y: 100 },
        { x: 0, y: 100 },
      ],
      sides: 4,
      isRegular: true,
    }

    // Serialize to JSON and deserialize
    const serialized = JSON.stringify(polygon)
    const deserialized = JSON.parse(serialized) as Polygon

    // Verify the deserialized object is valid
    expect(deserialized.id).toBe('polygon-6')
    expect(deserialized.type).toBe('shape')
    expect(deserialized.points.length).toBe(4)
    expect(deserialized.sides).toBe(4)
    expect(deserialized.isRegular).toBe(true)
  })
})

describe('triangle Interface', () => {
  it('should define a triangle with required properties', () => {
    // Create a valid Triangle object
    const triangle: Triangle = {
      id: 'triangle-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 50, y: 100 },
      ],
      sides: 3,
      isRegular: false,
    }

    // Verify the object is valid
    expect(triangle.id).toBe('triangle-1')
    expect(triangle.type).toBe('shape')
    expect(triangle.points.length).toBe(3)
    expect(triangle.sides).toBe(3)
    expect(triangle.isRegular).toBe(false)
  })

  it('should extend Polygon interface', () => {
    // Create a triangle
    const triangle: Triangle = {
      id: 'triangle-2',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 50, y: 100 },
      ],
      sides: 3,
      isRegular: true,
    }

    // Verify the triangle extends Polygon
    const polygon: Polygon = triangle
    expect(polygon.id).toBe('triangle-2')
    expect(polygon.points.length).toBe(3)
    expect(polygon.sides).toBe(3)
    expect(polygon.isRegular).toBe(true)
  })
})

describe('quadrilateral Interface', () => {
  it('should define a quadrilateral with required properties', () => {
    // Create a valid Quadrilateral object
    const quadrilateral: Quadrilateral = {
      id: 'quad-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 100, y: 100 },
        { x: 0, y: 100 },
      ],
      sides: 4,
      isRegular: true,
    }

    // Verify the object is valid
    expect(quadrilateral.id).toBe('quad-1')
    expect(quadrilateral.type).toBe('shape')
    expect(quadrilateral.points.length).toBe(4)
    expect(quadrilateral.sides).toBe(4)
    expect(quadrilateral.isRegular).toBe(true)
  })

  it('should extend Polygon interface', () => {
    // Create a quadrilateral
    const quadrilateral: Quadrilateral = {
      id: 'quad-2',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 0, y: 0 },
        { x: 100, y: 0 },
        { x: 100, y: 100 },
        { x: 0, y: 100 },
      ],
      sides: 4,
      isRegular: true,
    }

    // Verify the quadrilateral extends Polygon
    const polygon: Polygon = quadrilateral
    expect(polygon.id).toBe('quad-2')
    expect(polygon.points.length).toBe(4)
    expect(polygon.sides).toBe(4)
    expect(polygon.isRegular).toBe(true)
  })
})

describe('pentagon Interface', () => {
  it('should define a pentagon with required properties', () => {
    // Create a valid Pentagon object
    const pentagon: Pentagon = {
      id: 'pentagon-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 50, y: 0 },
        { x: 100, y: 25 },
        { x: 75, y: 100 },
        { x: 25, y: 100 },
        { x: 0, y: 25 },
      ],
      sides: 5,
      isRegular: true,
    }

    // Verify the object is valid
    expect(pentagon.id).toBe('pentagon-1')
    expect(pentagon.type).toBe('shape')
    expect(pentagon.points.length).toBe(5)
    expect(pentagon.sides).toBe(5)
    expect(pentagon.isRegular).toBe(true)
  })
})

describe('hexagon Interface', () => {
  it('should define a hexagon with required properties', () => {
    // Create a valid Hexagon object
    const hexagon: Hexagon = {
      id: 'hexagon-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 50, y: 0 },
        { x: 100, y: 25 },
        { x: 100, y: 75 },
        { x: 50, y: 100 },
        { x: 0, y: 75 },
        { x: 0, y: 25 },
      ],
      sides: 6,
      isRegular: true,
    }

    // Verify the object is valid
    expect(hexagon.id).toBe('hexagon-1')
    expect(hexagon.type).toBe('shape')
    expect(hexagon.points.length).toBe(6)
    expect(hexagon.sides).toBe(6)
    expect(hexagon.isRegular).toBe(true)
  })
})

describe('heptagon Interface', () => {
  it('should define a heptagon with required properties', () => {
    // Create a valid Heptagon object
    const heptagon: Heptagon = {
      id: 'heptagon-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 50, y: 0 },
        { x: 90, y: 20 },
        { x: 100, y: 60 },
        { x: 75, y: 90 },
        { x: 25, y: 90 },
        { x: 0, y: 60 },
        { x: 10, y: 20 },
      ],
      sides: 7,
      isRegular: true,
    }

    // Verify the object is valid
    expect(heptagon.id).toBe('heptagon-1')
    expect(heptagon.type).toBe('shape')
    expect(heptagon.points.length).toBe(7)
    expect(heptagon.sides).toBe(7)
    expect(heptagon.isRegular).toBe(true)
  })
})

describe('octagon Interface', () => {
  it('should define an octagon with required properties', () => {
    // Create a valid Octagon object
    const octagon: Octagon = {
      id: 'octagon-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 50, y: 0 },
        { x: 85, y: 15 },
        { x: 100, y: 50 },
        { x: 85, y: 85 },
        { x: 50, y: 100 },
        { x: 15, y: 85 },
        { x: 0, y: 50 },
        { x: 15, y: 15 },
      ],
      sides: 8,
      isRegular: true,
    }

    // Verify the object is valid
    expect(octagon.id).toBe('octagon-1')
    expect(octagon.type).toBe('shape')
    expect(octagon.points.length).toBe(8)
    expect(octagon.sides).toBe(8)
    expect(octagon.isRegular).toBe(true)
  })
})

describe('nonagon Interface', () => {
  it('should define a nonagon with required properties', () => {
    // Create a valid Nonagon object
    const nonagon: Nonagon = {
      id: 'nonagon-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 50, y: 0 },
        { x: 80, y: 10 },
        { x: 95, y: 35 },
        { x: 95, y: 65 },
        { x: 80, y: 90 },
        { x: 50, y: 100 },
        { x: 20, y: 90 },
        { x: 5, y: 65 },
        { x: 5, y: 35 },
      ],
      sides: 9,
      isRegular: true,
    }

    // Verify the object is valid
    expect(nonagon.id).toBe('nonagon-1')
    expect(nonagon.type).toBe('shape')
    expect(nonagon.points.length).toBe(9)
    expect(nonagon.sides).toBe(9)
    expect(nonagon.isRegular).toBe(true)
  })
})

describe('decagon Interface', () => {
  it('should define a decagon with required properties', () => {
    // Create a valid Decagon object
    const decagon: Decagon = {
      id: 'decagon-1',
      type: 'shape',
      visible: true,
      locked: false,
      position: { x: 50, y: 50 },
      rotation: 0,
      scale: { x: 1, y: 1 },
      selectable: true,
      draggable: true,
      showHandles: true,
      points: [
        { x: 50, y: 0 },
        { x: 75, y: 10 },
        { x: 90, y: 30 },
        { x: 95, y: 55 },
        { x: 85, y: 80 },
        { x: 65, y: 95 },
        { x: 35, y: 95 },
        { x: 15, y: 80 },
        { x: 5, y: 55 },
        { x: 10, y: 30 },
      ],
      sides: 10,
      isRegular: true,
    }

    // Verify the object is valid
    expect(decagon.id).toBe('decagon-1')
    expect(decagon.type).toBe('shape')
    expect(decagon.points.length).toBe(10)
    expect(decagon.sides).toBe(10)
    expect(decagon.isRegular).toBe(true)
  })
})
