import { beforeEach, describe, expect, it } from 'vitest'
import { PolylinePerimeterStrategy } from '@/core/compute/strategies/perimeter/PolylinePerimeterStrategy'
import { Point } from '@/types/core/element/geometry/point'
import { Polyline } from '@/types/core/element/path/polylinePathTypes'

// Mock Polyline class
class MockPolyline {
  id: string = 'mock-polyline-id'
  points: Point[] = []

  constructor(points: Point[] = []) {
    this.points = points
  }

  getSubType() {
    return 'polyline'
  }
}

// Make MockPolyline instances pass the instanceof Polyline check
Object.setPrototypeOf(MockPolyline.prototype, Polyline.prototype)

// Mock non-polyline element
class MockRectangle {
  id: string = 'mock-rectangle'

  getSubType() {
    return 'rectangle'
  }
}

describe('polylinePerimeterStrategy', () => {
  let strategy: PolylinePerimeterStrategy

  beforeEach(() => {
    strategy = new PolylinePerimeterStrategy()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('polyline')
  })

  it('should calculate perimeter correctly for a horizontal line', () => {
    const polyline = new MockPolyline([
      new Point(0, 0),
      new Point(100, 0),
    ])
    const perimeter = strategy.calculatePerimeter(polyline as any)
    expect(perimeter).toBe(100)
  })

  it('should calculate perimeter correctly for a vertical line', () => {
    const polyline = new MockPolyline([
      new Point(0, 0),
      new Point(0, 100),
    ])
    const perimeter = strategy.calculatePerimeter(polyline as any)
    expect(perimeter).toBe(100)
  })

  it('should calculate perimeter correctly for a diagonal line', () => {
    const polyline = new MockPolyline([
      new Point(0, 0),
      new Point(3, 4),
    ])
    const perimeter = strategy.calculatePerimeter(polyline as any)
    expect(perimeter).toBe(5)
  })

  it('should calculate perimeter correctly for a multi-segment polyline', () => {
    const polyline = new MockPolyline([
      new Point(0, 0),
      new Point(100, 0),
      new Point(100, 100),
      new Point(0, 100),
    ])
    const perimeter = strategy.calculatePerimeter(polyline as any)
    expect(perimeter).toBe(300)
  })

  it('should handle polyline with fewer than 2 points', () => {
    const polyline = new MockPolyline([new Point(0, 0)])
    const perimeter = strategy.calculatePerimeter(polyline as any)
    expect(perimeter).toBe(0)
  })

  it('should handle empty points array', () => {
    const polyline = new MockPolyline([])
    const perimeter = strategy.calculatePerimeter(polyline as any)
    expect(perimeter).toBe(0)
  })

  it('should return NaN if element is not a polyline', () => {
    const rectangle = new MockRectangle()
    const perimeter = strategy.calculatePerimeter(rectangle as any)
    expect(isNaN(perimeter)).toBe(true)
  })

  it('should handle invalid points', () => {
    // Create a mock that looks like a real Polyline instance with invalid points
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      points: [
        new Point(0, 0),
        new Point(100, 0),
        null,
        new Point(100, 100),
      ],
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const perimeter = strategy.calculatePerimeter(realPolyline as any)
    // The implementation calculates the first segment (100) and the third segment (100) for a total of 200
    expect(perimeter).toBe(200)
  })

  it('should handle real Polyline instance', () => {
    // Create a mock that looks like a real Polyline instance
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      points: [
        new Point(0, 0),
        new Point(100, 0),
        new Point(100, 100),
      ],
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const perimeter = strategy.calculatePerimeter(realPolyline as any)
    expect(perimeter).toBe(200)
  })

  it('should handle null points array in real Polyline instance', () => {
    // Create a mock that looks like a real Polyline instance with null points
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      points: null,
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const perimeter = strategy.calculatePerimeter(realPolyline as any)
    expect(perimeter).toBe(0)
  })

  it('should handle points with NaN coordinates', () => {
    // Create a mock that looks like a real Polyline instance with NaN coordinates
    const realPolyline = {
      id: 'real-polyline-id',
      constructor: { name: 'Polyline' },
      points: [
        new Point(0, 0),
        new Point(Number.NaN, 100),
        new Point(100, 100),
      ],
      getSubType: () => 'polyline',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realPolyline, Polyline.prototype)

    const perimeter = strategy.calculatePerimeter(realPolyline as any)
    // The implementation skips segments with NaN coordinates
    expect(perimeter).toBe(0)
  })
})
