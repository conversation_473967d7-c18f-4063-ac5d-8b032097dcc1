/**
 * Event Registry
 *
 * This file provides a centralized registry for all event types and their payloads.
 * It improves type safety when publishing and subscribing to events.
 */

import type {
  BatchEvent,
  ComputeEvent,
  TransformEvent,
  ValidationEvent,
} from './computeEvents'
import type {
  DataLoadEvent,
  DataStoreEvent,
  ExportEvent,
  HistoryEvent,
  StorageEvent,
  TemplateEvent,
} from './dataEvents'

// Import base types
import type { BaseEvent, TypedEvent } from './eventCore'

import type { AppEventType } from './eventTypes'

import type {
  GridColorChangeEvent,
  GridEnableEvent,
  GridSizeChangeEvent,
  GridSnapChangeEvent,
} from './gridEvents'

import type {
  KeyPressedEvent,
  KeyReleasedEvent,
} from './keyboardEvents'

import type {
  LayerEvent,
  LayerLockChangeEvent,
  LayerOrderChangeEvent,
  LayerVisibilityChangeEvent,
  RenderEvent,
} from './renderEvents'

// Import event types from domain-specific files
import type {
  SelectionChangedEvent,
  <PERSON>ha<PERSON>AddEvent,
  ShapeCreateEvent,
  ShapeDeleteEvent,
  ShapeDuplicateEvent,
  ShapeEditEvent,
  ShapeSelectionEvent,
  ShapeSelectRequestEvent,
  ShapeTransformEvent,
  ShapeUpdateRequestEvent,
  ShapeUpdateSuccessEvent,
} from './shapeEvents'

import type {
  EventBusEvent,
  ShapeFactoryEvent,
} from './systemEvents'

import type {
  TemplateAppliedEvent,
  TemplateCreatedEvent,
  TemplateDefaultChangedEvent,
  TemplateDeletedEvent,
  TemplateErrorEvent,
  TemplatesImportedEvent,
  TemplateUpdatedEvent,
} from './templateEvents'

import type {
  CanvasMouseMoveEvent,
  ModalExportOpenEvent,
  ModalTemplateOpenEvent,
  NotificationAddEvent,
  SelectionModeChangeEvent,
  ToastShowEvent,
  ToolChangeEvent,
  ViewPanToggleEvent,
  ViewZoomEvent,
} from './uiEvents'

import type { ViewPanEvent, ViewZoomedEvent } from './viewEvents'

import type { ShapeElement } from '@/types/core/elementDefinitions'

// Temporary placeholder for CoreConfig
interface CoreConfig { [key: string]: unknown }

// We no longer re-export everything from individual event files because it causes naming conflicts
// Instead, we define a union type below that includes all events

/**
 * Union type of all application events
 * This provides a comprehensive type that includes all possible event types
 */
export type AppEventRegistry =
  | EventBusEvent
  | RenderEvent
  | ShapeCreateEvent
  | ShapeEditEvent
  | ShapeDeleteEvent
  | ShapeDuplicateEvent
  | ShapeSelectionEvent
  | ShapeAddEvent
  | ShapeTransformEvent
  | DataLoadEvent
  | HistoryEvent
  | TemplateEvent
  | ExportEvent
  | ToolChangeEvent
  | ViewPanToggleEvent
  | ViewZoomEvent
  | SelectionModeChangeEvent
  | CanvasMouseMoveEvent
  | ToastShowEvent
  | NotificationAddEvent
  | ModalExportOpenEvent
  | ModalTemplateOpenEvent
  | DataStoreEvent
  | ShapeFactoryEvent
  | ValidationEvent
  | ComputeEvent
  | LayerEvent
  | LayerVisibilityChangeEvent
  | LayerLockChangeEvent
  | LayerOrderChangeEvent
  | StorageEvent
  | BatchEvent
  | KeyPressedEvent
  | KeyReleasedEvent
  | SelectionChangedEvent
  | GridEnableEvent
  | GridSizeChangeEvent
  | GridColorChangeEvent
  | GridSnapChangeEvent
  | ShapeSelectRequestEvent
  | ViewPanEvent
  | ViewZoomedEvent
  | TemplateErrorEvent
  | TemplateCreatedEvent
  | TemplateUpdatedEvent
  | TemplateDeletedEvent
  | TemplateDefaultChangedEvent
  | TemplatesImportedEvent
  | TemplateAppliedEvent

// Define a full AppEvent type that combines BaseEvent with all possible event types
export type AppEvent = BaseEvent & AppEventRegistry

// --- Define StateValue ---
export type StateValue =
  | string
  | number
  | boolean
  | null
  | { [key: string]: StateValue }
  | StateValue[]
// --- End Define StateValue ---

// --- Define DataUpdatedPayload ---
export interface DataUpdatedPayload {
  shapes: ShapeElement[]
  selectedIds: string[]
  source: string
  [key: string]: unknown // Index signature workaround
}
// --- End Define DataUpdatedPayload ---

/**
 * Payload for canvas mouse events.
 */
export interface CanvasMouseEventPayload {
  /** The position of the mouse event on the canvas. */
  position: { x: number, y: number }
  /** The button number that was pressed (if applicable). */
  button?: number // For down/up events
  /** The original browser MouseEvent. */
  originalEvent?: MouseEvent
}

export interface CanvasClickEvent extends BaseEvent {
  type: AppEventType.CanvasClicked | AppEventType.CanvasDblClicked | AppEventType.CanvasContextMenu
  payload: CanvasMouseEventPayload & { isDoubleClick?: boolean }
}

export interface CanvasMouseDownEvent extends BaseEvent {
  type: AppEventType.CanvasMouseDown
  payload: CanvasMouseEventPayload
}

export interface CanvasMouseUpEvent extends BaseEvent {
  type: AppEventType.CanvasMouseUp
  payload: CanvasMouseEventPayload
}

/**
 * File data type for imported files
 */
export interface FileData {
  content: string | ArrayBuffer
  type: string
  name: string
  size: number
  lastModified?: number
}

/**
 * Error details type
 */
export interface ErrorDetails {
  code?: string
  timestamp?: number
  context?: Record<string, string | number | boolean>
  stack?: string
  originalError?: Error
}

/**
 * Event Map for stricter typing in EventBus implementation
 * Maps event type strings to their corresponding event interfaces
 * This is used to enforce type safety when publishing and subscribing to events
 */
export interface AppEventMap {
  // System events
  [AppEventType.EventRegister]: EventBusEvent
  [AppEventType.EventUnregister]: EventBusEvent
  [AppEventType.EventError]: EventBusEvent
  [AppEventType.FactoryRegister]: ShapeFactoryEvent
  [AppEventType.FactoryUnregister]: ShapeFactoryEvent
  [AppEventType.FactoryError]: ShapeFactoryEvent

  // Shape events
  [AppEventType.ShapeCreateRequest]: ShapeCreateEvent
  [AppEventType.ShapeCreateValidate]: ShapeCreateEvent
  [AppEventType.ShapeCreateComplete]: ShapeCreateEvent
  [AppEventType.ShapeCreateError]: ShapeCreateEvent
  [AppEventType.ShapeEditRequest]: ShapeEditEvent
  [AppEventType.ShapeEditCompute]: ShapeEditEvent
  [AppEventType.ShapeEditComplete]: ShapeEditEvent
  [AppEventType.ShapeEditError]: ShapeEditEvent
  [AppEventType.ShapeDeleteRequest]: ShapeDeleteEvent
  [AppEventType.ShapeDeleteComplete]: ShapeDeleteEvent
  [AppEventType.ShapeDeleteError]: ShapeDeleteEvent
  [AppEventType.ShapeSelected]: ShapeSelectionEvent
  [AppEventType.ShapeAdd]: ShapeAddEvent
  [AppEventType.ShapeDuplicateRequest]: ShapeDuplicateEvent
  [AppEventType.ShapeDuplicateComplete]: ShapeDuplicateEvent
  [AppEventType.ShapeUpdateRequest]: ShapeUpdateRequestEvent
  [AppEventType.ShapeUpdateSuccess]: ShapeUpdateSuccessEvent
  [AppEventType.ShapeSelectRequest]: ShapeSelectRequestEvent
  [AppEventType.ShapeSelectionSuccess]: ShapeSelectionEvent

  // Shape transform events
  [AppEventType.ShapeTransformRequest]: ShapeTransformEvent
  [AppEventType.ShapeTransformUpdate]: ShapeTransformEvent
  [AppEventType.ShapeTransformComplete]: ShapeTransformEvent

  // UI events
  [AppEventType.ToolChanged]: ToolChangeEvent
  [AppEventType.ViewPanToggle]: ViewPanToggleEvent
  [AppEventType.ViewZoomIn]: ViewZoomEvent
  [AppEventType.ViewZoomOut]: ViewZoomEvent
  [AppEventType.ViewZoomReset]: ViewZoomEvent
  [AppEventType.SelectionModeChange]: SelectionModeChangeEvent
  [AppEventType.CanvasMouseMove]: CanvasMouseMoveEvent
  [AppEventType.ToastShow]: ToastShowEvent
  [AppEventType.NotificationAdd]: NotificationAddEvent
  [AppEventType.ModalExportOpen]: ModalExportOpenEvent
  [AppEventType.ModalTemplateOpen]: ModalTemplateOpenEvent

  // Canvas events
  [AppEventType.CanvasCleared]: TypedEvent<{ canvasId: string }>
  [AppEventType.CanvasResized]: TypedEvent<{ width: number, height: number }>

  // Tool events
  // Removed duplicate definition

  // View events
  [AppEventType.ViewZoomed]: ViewZoomedEvent
  [AppEventType.ViewPanned]: ViewPanEvent

  // History events
  [AppEventType.HistoryCheckpoint]: HistoryEvent
  [AppEventType.HistoryUndo]: HistoryEvent
  [AppEventType.HistoryRedo]: HistoryEvent
  [AppEventType.HistoryError]: HistoryEvent
  [AppEventType.HistoryUndoPerformed]: HistoryEvent
  [AppEventType.HistoryRedoPerformed]: HistoryEvent
  [AppEventType.HistoryBatchOperationPerformed]: HistoryEvent

  // Render events
  [AppEventType.RenderTrigger]: RenderEvent
  [AppEventType.RenderStart]: RenderEvent
  [AppEventType.RenderComplete]: RenderEvent
  [AppEventType.RenderError]: RenderEvent

  // Layer events
  [AppEventType.LayerVisibilityChange]: LayerVisibilityChangeEvent
  [AppEventType.LayerLockChange]: LayerLockChangeEvent
  [AppEventType.LayerOrderChange]: LayerOrderChangeEvent
  [AppEventType.LayerCreate]: LayerEvent
  [AppEventType.LayerUpdate]: LayerEvent
  [AppEventType.LayerDelete]: LayerEvent
  [AppEventType.LayerOrder]: LayerEvent

  // File events
  [AppEventType.FileImported]: TypedEvent<{ filename: string, data: FileData }>
  [AppEventType.FileExported]: TypedEvent<{ filename: string, format: string }>
  [AppEventType.ExportRequest]: ExportEvent
  [AppEventType.ExportPrepare]: ExportEvent
  [AppEventType.ExportProgress]: ExportEvent
  [AppEventType.ExportComplete]: ExportEvent
  [AppEventType.ExportError]: ExportEvent

  // Template events
  [AppEventType.TemplateApply]: TemplateEvent
  [AppEventType.TemplateApplyRequest]: TemplateEvent
  [AppEventType.TemplateApplyValidate]: TemplateEvent
  [AppEventType.TemplateApplyComplete]: TemplateEvent
  [AppEventType.TemplateApplyError]: TemplateEvent
  [AppEventType.TemplateError]: TemplateErrorEvent
  [AppEventType.TemplateCreated]: TemplateCreatedEvent
  [AppEventType.TemplateUpdated]: TemplateUpdatedEvent
  [AppEventType.TemplateDeleted]: TemplateDeletedEvent
  [AppEventType.TemplateDefaultChanged]: TemplateDefaultChangedEvent
  [AppEventType.TemplatesImported]: TemplatesImportedEvent
  [AppEventType.TemplateApplied]: TemplateAppliedEvent

  // Compute events
  [AppEventType.ComputeRequest]: ComputeEvent
  [AppEventType.ComputeProgress]: ComputeEvent
  [AppEventType.ComputeComplete]: ComputeEvent
  [AppEventType.ComputeError]: ComputeEvent
  [AppEventType.ComputeTransform]: TransformEvent
  [AppEventType.ValidateShape]: ValidationEvent
  [AppEventType.ValidateTemplate]: ValidationEvent
  [AppEventType.ValidateOperation]: ValidationEvent
  [AppEventType.ValidateConstraint]: ValidationEvent
  [AppEventType.BatchStart]: BatchEvent
  [AppEventType.BatchProgress]: BatchEvent
  [AppEventType.BatchComplete]: BatchEvent
  [AppEventType.BatchError]: BatchEvent

  // State events
  [AppEventType.StateUpdated]: TypedEvent<{ path: string, value: StateValue }>
  [AppEventType.DataUpdated]: TypedEvent<DataUpdatedPayload>
  [AppEventType.SelectionChanged]: SelectionChangedEvent
  [AppEventType.ConfigUpdated]: TypedEvent<{ config: CoreConfig }>
  [AppEventType.ErrorOccurred]: TypedEvent<{
    message: string
    code?: string
    details?: ErrorDetails
  }>
  [AppEventType.DataLoadRequest]: DataLoadEvent
  [AppEventType.DataLoadProgress]: DataLoadEvent
  [AppEventType.DataLoadComplete]: DataLoadEvent
  [AppEventType.DataLoadError]: DataLoadEvent
  [AppEventType.StorageSaveRequest]: StorageEvent
  [AppEventType.StorageSaveComplete]: StorageEvent
  [AppEventType.StorageLoadRequest]: StorageEvent
  [AppEventType.StorageLoadComplete]: StorageEvent
  [AppEventType.StorageBatch]: StorageEvent
  [AppEventType.StorageError]: StorageEvent
  [AppEventType.StoreStateChange]: DataStoreEvent
  [AppEventType.StoreStateSync]: DataStoreEvent
  [AppEventType.StoreStateError]: DataStoreEvent

  // Grid events
  [AppEventType.GridEnabled]: GridEnableEvent
  [AppEventType.GridSizeChanged]: GridSizeChangeEvent
  [AppEventType.GridColorChanged]: GridColorChangeEvent
  [AppEventType.GridSnapChanged]: GridSnapChangeEvent

  // Sidebar events
  [AppEventType.SidebarLeftToggle]: TypedEvent<{ visible: boolean }>
  [AppEventType.SidebarRightToggle]: TypedEvent<{ visible: boolean }>

  // Keyboard events
  [AppEventType.KeyPressed]: KeyPressedEvent
  [AppEventType.KeyReleased]: KeyReleasedEvent

  // Add mappings for new canvas mouse events
  [AppEventType.CanvasClicked]: CanvasClickEvent
  [AppEventType.CanvasDblClicked]: CanvasClickEvent
  [AppEventType.CanvasMouseDown]: CanvasMouseDownEvent
  [AppEventType.CanvasMouseUp]: CanvasMouseUpEvent
  [AppEventType.CanvasMouseMove]: CanvasMouseMoveEvent
  [AppEventType.CanvasContextMenu]: CanvasClickEvent

  // Validation events
  [AppEventType.ValidateShape]: ValidationEvent
  [AppEventType.ValidateTemplate]: ValidationEvent
  [AppEventType.ValidateOperation]: ValidationEvent
  [AppEventType.ValidateConstraint]: ValidationEvent

  // Batch events
  [AppEventType.BatchStart]: BatchEvent
  [AppEventType.BatchProgress]: BatchEvent
  [AppEventType.BatchComplete]: BatchEvent
  [AppEventType.BatchError]: BatchEvent

  // Index signature to satisfy the Record<string, BaseEvent> constraint
  [key: string]: BaseEvent
}

/**
 * Export queue event payload
 */
export interface ExportQueuePayload {
  /** Queue length */
  queueLength: number
  /** Task ID */
  taskId?: string
  /** Action type */
  action: 'added' | 'cleared' | 'canceled' | 'paused' | 'resumed'
  /** Number of completed tasks */
  completedTasks: number
  /** Progress */
  progress: number
}
