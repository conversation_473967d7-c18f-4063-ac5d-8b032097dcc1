import type { CreateRectangleParams } from '@/core/factory/ElementFactory'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { RectangleCreator } from '@/core/factory/creators/RectangleCreator'
import { Point } from '@/types/core/element/geometry/point'
import { Rectangle } from '@/types/core/element/shape/rectangleShapeTypes'
import { ElementType } from '@/types/core/shape-type'

// Mock the Rectangle class constructor
vi.mock('@/types/core/element/shape/rectangle', () => {
  return {
    Rectangle: vi.fn().mockImplementation((id, pos, props, opts) => ({
      id,
      position: pos,
      properties: props,
      options: opts,
      type: props.type,
      width: props.width,
      height: props.height,
      cornerRadius: props.cornerRadius,
      getType: () => props.type,
      // Add toJson method to fix the test
      toJson: () => ({
        id,
        type: props.type,
        properties: props,
        position: pos,
        options: opts,
      }),
    })),
  }
})

describe('rectangleCreator', () => {
  let creator: RectangleCreator

  beforeEach(() => {
    creator = new RectangleCreator()
    vi.clearAllMocks() // Clear mocks before each test
  })

  it('should create a basic Rectangle', async () => {
    const params: CreateRectangleParams = {
      id: 'rect-1',
      type: ElementType.RECTANGLE,
      position: { x: 100, y: 150 },
      width: 200,
      height: 100,
      strokeWidth: 3,
      stroke: '#ff0000',
      fill: '#00ff00',
    }

    const rectangle = await creator.create(params)

    expect(Rectangle).toHaveBeenCalledTimes(1)
    expect(Rectangle).toHaveBeenCalledWith(
      'rect-1',
      { x: 100, y: 150 },
      expect.objectContaining({
        type: ElementType.RECTANGLE,
        width: 200,
        height: 100,
        cornerRadius: undefined,
        stroke: '#ff0000',
        strokeWidth: 3,
        fill: '#00ff00',
      }),
      expect.objectContaining({}),
    )

    expect(rectangle).toBeDefined()
    expect(rectangle.id).toBe('rect-1')
    expect(rectangle.type).toBe(ElementType.RECTANGLE)
    expect((rectangle.properties as any).width).toBe(200)
    expect((rectangle.properties as any).height).toBe(100)
  })

  it('should create a Square when type is SQUARE', async () => {
    const params: CreateRectangleParams = {
      id: 'square-1',
      type: ElementType.SQUARE,
      position: { x: 50, y: 50 },
      width: 150,
      height: 150,
      cornerRadius: 10,
      fill: '#ffff00',
    }

    const square = await creator.create(params)

    expect(Rectangle).toHaveBeenCalledTimes(1)
    expect(Rectangle).toHaveBeenCalledWith(
      'square-1',
      { x: 50, y: 50 },
      {
        type: ElementType.SQUARE,
        width: 150,
        height: 150,
        cornerRadius: 10,
        fill: '#ffff00',
        stroke: undefined,
        strokeWidth: undefined,
        opacity: undefined,
        strokeDasharray: undefined,
      },
      {
        draggable: undefined,
        label: undefined,
        layer: undefined,
        metadata: undefined,
        name: undefined,
        resizable: undefined,
        selectable: undefined,
        visible: undefined,
        zIndex: undefined,
      },
    )

    expect(square).toBeDefined()
    expect(square.id).toBe('square-1')
    expect(square.type).toBe(ElementType.SQUARE)
    expect((square.properties as any).width).toBe(150)
    expect((square.properties as any).height).toBe(150)
    expect((square.properties as any).cornerRadius).toBe(10)
  })

  it('should create a Rectangle with cornerRadius', async () => {
    const params: CreateRectangleParams = {
      id: 'rect-corner',
      type: ElementType.RECTANGLE,
      position: { x: 20, y: 30 },
      width: 80,
      height: 60,
      cornerRadius: 5,
    }

    const rectangle = await creator.create(params)

    expect(Rectangle).toHaveBeenCalledTimes(1)
    expect(Rectangle).toHaveBeenCalledWith(
      'rect-corner',
      { x: 20, y: 30 },
      expect.objectContaining({
        type: ElementType.RECTANGLE,
        width: 80,
        height: 60,
        cornerRadius: 5,
      }),
      expect.objectContaining({}),
    )
    expect((rectangle.properties as any).cornerRadius).toBe(5)
  })

  it('should throw error for incorrect shape type', async () => {
    const params = {
      id: 'wrong-type',
      type: ElementType.CIRCLE,
      position: { x: 10, y: 10 },
      radius: 50,
    } as any

    await expect(creator.create(params)).rejects.toThrow(
      'RectangleCreator received incorrect shape type: circle',
    )
    expect(Rectangle).not.toHaveBeenCalled()
  })

  it('should throw error for invalid dimensions (width <= 0)', async () => {
    const params: CreateRectangleParams = {
      id: 'invalid-width',
      type: ElementType.RECTANGLE,
      position: { x: 10, y: 10 },
      width: 0,
      height: 50,
    }

    await expect(creator.create(params)).rejects.toThrow(
      'Rectangle creation requires valid positive width and height.',
    )
    expect(Rectangle).not.toHaveBeenCalled()
  })

  it('should throw error for invalid dimensions (height <= 0)', async () => {
    const params: CreateRectangleParams = {
      id: 'invalid-height',
      type: ElementType.RECTANGLE,
      position: { x: 10, y: 10 },
      width: 50,
      height: -10,
    }

    await expect(creator.create(params)).rejects.toThrow(
      'Rectangle creation requires valid positive width and height.',
    )
    expect(Rectangle).not.toHaveBeenCalled()
  })

  it('should create a default Rectangle', async () => {
    const id = 'default-rect'
    const position = new Point(500, 500)
    const defaultWidth = 100
    const defaultHeight = 60

    const rectangle = await creator.createDefault(id, position)

    expect(Rectangle).toHaveBeenCalledTimes(1)
    expect(Rectangle).toHaveBeenCalledWith(
      id,
      position,
      {
        type: ElementType.RECTANGLE,
        width: defaultWidth,
        height: defaultHeight,
        fill: '#FFFFFF',
        stroke: '#000000',
        strokeWidth: 2,
        cornerRadius: undefined,
        opacity: undefined,
        strokeDasharray: undefined,
      },
      {},
    )

    expect(rectangle).toBeDefined()
    expect(rectangle.id).toBe(id)
    expect(rectangle.type).toBe(ElementType.RECTANGLE)
    expect((rectangle.properties as any).width).toBe(defaultWidth)
    expect((rectangle.properties as any).height).toBe(defaultHeight)
  })
})
