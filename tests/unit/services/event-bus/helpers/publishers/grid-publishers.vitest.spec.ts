import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  publishGridColorChanged,
  publishGridEnabled,
  publishGridSizeChanged,
  publishGridSnapChanged,
} from '@/services/event-bus/helpers/publishers/grid-publishers'
import { AppEventType } from '@/types/services/events'

describe('grid Publishers', () => {
  let mockEventBus: any

  beforeEach(() => {
    mockEventBus = {
      publish: vi.fn(),
    }
  })

  describe('grid Visibility', () => {
    it('should publish grid enabled event correctly', () => {
      publishGridEnabled(mockEventBus, true)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.GRID_ENABLED,
        payload: { enabled: true },
      })
    })

    it('should publish grid disabled event correctly', () => {
      publishGridEnabled(mockEventBus, false)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.GRID_ENABLED,
        payload: { enabled: false },
      })
    })
  })

  describe('grid Size', () => {
    it('should publish grid size changed event correctly', () => {
      const size = 20

      publishGridSizeChanged(mockEventBus, size)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.GRID_SIZE_CHANGED,
        payload: { size },
      })
    })
  })

  describe('grid Color', () => {
    it('should publish grid color changed event correctly', () => {
      const color = '#cccccc'

      publishGridColorChanged(mockEventBus, color)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.GRID_COLOR_CHANGED,
        payload: { color },
      })
    })
  })

  describe('grid Snap', () => {
    it('should publish grid snap enabled event correctly', () => {
      publishGridSnapChanged(mockEventBus, true)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.GRID_SNAP_CHANGED,
        payload: { snapToGrid: true },
      })
    })

    it('should publish grid snap disabled event correctly', () => {
      publishGridSnapChanged(mockEventBus, false)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.GRID_SNAP_CHANGED,
        payload: { snapToGrid: false },
      })
    })
  })
})
