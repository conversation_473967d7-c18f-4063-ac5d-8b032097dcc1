
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/core/compute/strategies/space/KitchenPlanningStrategy.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> / <a href="index.html">src/core/compute/strategies/space</a> KitchenPlanningStrategy.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/83</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/83</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import type {
  ErgonomicsEvaluationResult,
  PathwayCheckResult,
  SpacePlanningStrategy,
  SpaceType,
} from '@/types/core/compute'
import type Point from '@/types/core/element/geometry/point' // Point interface (IPoint)
/**
 * Kitchen Space Planning Strategy
 * Provides planning functionality for kitchen spaces, including work triangle check, pathway width check, etc.
 */
import type {
  Element,
  Shape,
  // Path, // Unused
  ShapeElement, // For accessing properties
} from '@/types/core/elementDefinitions'
<span class="cstat-no" title="statement not covered" >import { CoreError, ErrorType } from '@/services/system/error-service'</span>
<span class="cstat-no" title="statement not covered" >import {</span>
  ElementType as CoreElementType, // For accessing properties
} from '@/types/core/elementDefinitions'
// import {
//     calculateSpaceUtilization,
//     checkPathwaysWidth,
//     checkWorkTriangle as checkWorkTriangleUtil // Renamed to avoid conflict
// } from '@/lib/utils/space/spacePlanning'; // Module not found
// import { BoundingBoxClass } from '@/lib/utils/geometry'; // Unused
// import { PointClass } from '@/lib/utils/geometry'; // PointClass is exported from here // Unused
&nbsp;
// Helper to get BoundingBoxClass from an Element (Unused in this file)
// function getElementBoundingBoxClass(element: Element): BoundingBoxClass | null {
//     if (!element) return null;
//     if (element.type === CoreElementType.RECTANGLE || element.type === CoreElementType.SQUARE) {
//         const shape = element as Shape.Rectangle;
//         if (shape.position &amp;&amp; typeof shape.width === 'number' &amp;&amp; typeof shape.height === 'number') {
//             return new BoundingBoxClass(shape.position.x - shape.width / 2, shape.position.y - shape.height / 2, shape.width, shape.height);
//         }
//     }
//      if (element.type === CoreElementType.CIRCLE) {
//         const shape = element as Shape.Circle;
//         if (shape.position &amp;&amp; typeof shape.radius === 'number') {
//             return new BoundingBoxClass(shape.position.x - shape.radius, shape.position.y - shape.radius, shape.radius * 2, shape.radius * 2);
//         }
//     }
//     console.warn(`[KitchenPlanningStrategy] getElementBoundingBoxClass: Bounding box for type ${element.type} not fully implemented here.`);
//     return null;
// }
&nbsp;
/**
 * Kitchen Space Planning Strategy
 */
<span class="cstat-no" title="statement not covered" >export class KitchenPlanningStrategy implements SpacePlanningStrategy {</span>
<span class="cstat-no" title="statement not covered" >  public calculateSpaceUtilization(/* elements: Element[], roomBoundaryElement: Element */): number { // Params unused due to commented out body</span>
    // const roomBBoxClass = getElementBoundingBoxClass(roomBoundaryElement);
    // if (!roomBBoxClass) {
    //   throw new CoreError( ErrorType.INVALID_PARAMETER, 'Room boundary must be an element from which a bounding box can be derived.');
    // }
    // return calculateSpaceUtilization(elements, roomBBoxClass);
<span class="cstat-no" title="statement not covered" >    console.warn('[KitchenPlanningStrategy] calculateSpaceUtilization is temporarily disabled due to missing dependencies.')</span>
<span class="cstat-no" title="statement not covered" >    return 0</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  public checkPathwayWidth(/* elements: Element[], pathways: Path.Line[], minWidth: number */): PathwayCheckResult[] { // Params unused due to commented out body</span>
    // if (!Array.isArray(elements)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Elements must be an array');
    // if (!Array.isArray(pathways)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Pathways must be an array');
    // if (typeof minWidth !== 'number' || minWidth &lt;= 0) {
    //   throw new CoreError(ErrorType.INVALID_PARAMETER, 'Minimum width must be a positive number');
    // }
&nbsp;
    // const formattedPathways = pathways.map((path, index) =&gt; {
    //     const lineForUtil = {
    //         id: path.id ?? `pathway-${index}`,
    //         start: path.start as Point,
    //         end: path.end as Point,
    //         type: CoreElementType.LINE,
    //         getSubType: () =&gt; CoreElementType.LINE,
    //     } as any;
    //     return { id: path.id ?? `pathway-${index + 1}`, line: lineForUtil as import('@/lib/utils/element/path/lineImplementation').Line };
    // });
&nbsp;
    // return checkPathwaysWidth(elements, formattedPathways, minWidth);
<span class="cstat-no" title="statement not covered" >    console.warn('[KitchenPlanningStrategy] checkPathwayWidth is temporarily disabled due to missing dependencies.')</span>
<span class="cstat-no" title="statement not covered" >    return []</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  public checkWorkTriangle(</span>
    /* elements: Element[], sinkElement: Element, stoveElement: Element, refrigeratorElement: Element */
<span class="cstat-no" title="statement not covered" >  ): { isValid: boolean, perimeter: number, message: string } { // Params unused due to commented out body</span>
    // if (!sinkElement || !stoveElement || !refrigeratorElement) {
    //   throw new CoreError(ErrorType.INVALID_PARAMETER, 'Sink, stove, and refrigerator elements must be provided.');
    // }
&nbsp;
    // const sinkPos = (sinkElement as ShapeElement).position as Point; // Assuming these are ShapeElements with a position
    // const stovePos = (stoveElement as ShapeElement).position as Point;
    // const fridgePos = (refrigeratorElement as ShapeElement).position as Point;
&nbsp;
    // if (!sinkPos || !stovePos || !fridgePos) {
    //     throw new CoreError(ErrorType.INVALID_PARAMETER, 'One or more work triangle elements lack a position.');
    // }
&nbsp;
    // // The utility function expects PointClass instances
    // return checkWorkTriangleUtil(
    //   new PointClass(sinkPos.x, sinkPos.y),
    //   new PointClass(stovePos.x, stovePos.y),
    //   new PointClass(fridgePos.x, fridgePos.y),
    //   elements
    // );
<span class="cstat-no" title="statement not covered" >    console.warn('[KitchenPlanningStrategy] checkWorkTriangle is temporarily disabled due to missing dependencies.')</span>
<span class="cstat-no" title="statement not covered" >    return { isValid: false, perimeter: 0, message: 'Work triangle check disabled.' }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  public evaluateErgonomics(</span>
<span class="cstat-no" title="statement not covered" >    elements: Element[],</span>
<span class="cstat-no" title="statement not covered" >    deskElement: Element, // In kitchen context, this might be a primary work counter</span>
    // chairElement: Element // Or a stool // Parameter 'chairElement' is declared but its value is never read.
<span class="cstat-no" title="statement not covered" >  ): ErgonomicsEvaluationResult {</span>
<span class="cstat-no" title="statement not covered" >    const issues: string[] = []</span>
<span class="cstat-no" title="statement not covered" >    const recommendations: string[] = []</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!deskElement?.type) { // chairElement might be optional for some kitchen workspaces</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(ErrorType.InvalidParameter, 'Desk/work counter element must be provided and have a type.')</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const workSurface = deskElement as unknown as Shape.Rectangle // Assuming work surface is rectangular</span>
<span class="cstat-no" title="statement not covered" >    const counterHeight = workSurface.height ?? 0.9 // Default to 0.9m if not specified</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (counterHeight &lt; 0.85 || counterHeight &gt; 0.95) {</span>
<span class="cstat-no" title="statement not covered" >      issues.push(`Counter height of ${counterHeight.toFixed(2)}m is outside the recommended range (0.85-0.95m).`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
<span class="cstat-no" title="statement not covered" >      recommendations.push(`Counter height of ${counterHeight.toFixed(2)}m is within standard range.`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const triangleElements = this.findWorkTriangleElements(elements)</span>
<span class="cstat-no" title="statement not covered" >    if (triangleElements.sink &amp;&amp; triangleElements.stove &amp;&amp; triangleElements.refrigerator) {</span>
<span class="cstat-no" title="statement not covered" >      const workSurfaceShapeElement = workSurface as unknown as ShapeElement</span>
<span class="cstat-no" title="statement not covered" >      const workSurfacePos = workSurfaceShapeElement.position</span>
<span class="cstat-no" title="statement not covered" >      if (workSurfacePos !== null &amp;&amp; workSurfacePos !== undefined) {</span>
<span class="cstat-no" title="statement not covered" >        const sinkPos = (triangleElements.sink as ShapeElement).position</span>
<span class="cstat-no" title="statement not covered" >        const stovePos = (triangleElements.stove as ShapeElement).position</span>
<span class="cstat-no" title="statement not covered" >        const fridgePos = (triangleElements.refrigerator as ShapeElement).position</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (this.isPointInTriangle(workSurfacePos, sinkPos, stovePos, fridgePos)) {</span>
<span class="cstat-no" title="statement not covered" >          issues.push('Primary workspace is within the main kitchen work triangle, potentially causing workflow disruption.')</span>
<span class="cstat-no" title="statement not covered" >          recommendations.push('Consider positioning primary workspace adjacent to, but not inside, the work triangle.')</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    recommendations.push('Ensure adequate lighting for all work surfaces.')</span>
<span class="cstat-no" title="statement not covered" >    recommendations.push('Consider toe-kick space under base cabinets (approx. 10cm high, 7.5cm deep).')</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return {</span>
<span class="cstat-no" title="statement not covered" >      isValid: issues.length === 0,</span>
<span class="cstat-no" title="statement not covered" >      issues,</span>
<span class="cstat-no" title="statement not covered" >      recommendations,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  private findWorkTriangleElements(elements: Element[]): {</span>
    sink?: Element
    stove?: Element
    refrigerator?: Element
<span class="cstat-no" title="statement not covered" >  } {</span>
<span class="cstat-no" title="statement not covered" >    let sink, stove, refrigerator</span>
<span class="cstat-no" title="statement not covered" >    for (const element of elements) {</span>
<span class="cstat-no" title="statement not covered" >      const el = element as ShapeElement // Assume these are ShapeElements with properties</span>
<span class="cstat-no" title="statement not covered" >      const elementType = el.type as CoreElementType</span>
<span class="cstat-no" title="statement not covered" >      const elementCategory = el.properties?.category as string || ''</span>
<span class="cstat-no" title="statement not covered" >      const customType = el.properties?.customType as string || ''</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (elementType === CoreElementType.FIXTURE || elementCategory === 'fixture' || customType === 'sink')</span>
<span class="cstat-no" title="statement not covered" >        sink = element</span>
<span class="cstat-no" title="statement not covered" >      if (elementType === CoreElementType.APPLIANCE || elementCategory === 'appliance' || customType === 'stove' || customType === 'cooktop')</span>
<span class="cstat-no" title="statement not covered" >        stove = element</span>
<span class="cstat-no" title="statement not covered" >      if (elementType === CoreElementType.APPLIANCE || elementCategory === 'appliance' || customType === 'refrigerator')</span>
<span class="cstat-no" title="statement not covered" >        refrigerator = element</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return { sink, stove, refrigerator }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  private isPointInTriangle(p: Point, a: Point, b: Point, c: Point): boolean {</span>
<span class="cstat-no" title="statement not covered" >    const areaABC = Math.abs((a.x * (b.y - c.y) + b.x * (c.y - a.y) + c.x * (a.y - b.y))) // Doubled area</span>
<span class="cstat-no" title="statement not covered" >    const areaPAB = Math.abs((p.x * (a.y - b.y) + a.x * (b.y - p.y) + b.x * (p.y - a.y)))</span>
<span class="cstat-no" title="statement not covered" >    const areaPBC = Math.abs((p.x * (b.y - c.y) + b.x * (c.y - p.y) + c.x * (p.y - b.y)))</span>
<span class="cstat-no" title="statement not covered" >    const areaPCA = Math.abs((p.x * (c.y - a.y) + c.x * (a.y - p.y) + a.x * (p.y - c.y)))</span>
<span class="cstat-no" title="statement not covered" >    return Math.abs(areaABC - (areaPAB + areaPBC + areaPCA)) &lt; 0.001 // Check if sum of small triangle areas equals big one</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  public getSpaceType(): SpaceType {</span>
<span class="cstat-no" title="statement not covered" >    return 'kitchen'</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:58:01.486Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    