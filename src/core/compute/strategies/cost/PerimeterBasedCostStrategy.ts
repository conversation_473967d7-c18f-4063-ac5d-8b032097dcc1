/**
 * Cost Calculation Strategy Based on Element Perimeter/Length
 *
 * @remarks
 * This strategy implements the {@link CostCalculatorStrategy} to calculate costs
 * for elements where the primary cost driver is their perimeter or length
 * (e.g., baseboards, trim, fencing, linear features).
 *
 * It handles various element types:
 * - Basic shapes (Rectangle, Square, Circle, Ellipse, Polygon): Uses specific utility
 *   functions from `../../../../lib/utils/geometry/` to calculate their perimeters.
 * - Path elements (Line, Arc, Polyline, Quadratic Bezier, Cubi<PERSON>): Delegates
 *   perimeter/length calculation to their respective dedicated perimeter strategies
 *   (e.g., {@link ArcPerimeterStrategy}, {@link LinePerimeterStrategy} via `calculateLineLength`).
 *
 * Once the perimeter/length (`measure`) is determined, the cost is calculated using
 * the `calculatePerimeterBasedCost` utility function from `../../../../lib/utils/cost/costUtils`,
 * applying the `unitCost` and other optional cost adjustments from {@link CostCalculationOptions}.
 *
 * The `getElementType()` method returns a generic string 'perimeter_calculable',
 * indicating its applicability to any element type for which a perimeter/length
 * can be determined.
 *
 * @module core/compute/strategies/cost/PerimeterBasedCostStrategy
 * @see {@link CostCalculatorStrategy}
 * @see {@link CostCalculationOptions}
 * @see {@link calculatePerimeterBasedCost}
 */
import type { CostCalculationOptions, CostCalculatorStrategy } from '../../../../types/core/compute'
import type {
  Element,
  Shape,
  ShapeElement,
} from '../../../../types/core/elementDefinitions'
import { calculateCirclePerimeter, calculateEllipsePerimeter } from '../../../../lib/utils/geometry/ellipseUtils'

import { calculatePerimeter as calculatePolygonPerimeter } from '../../../../lib/utils/geometry/polygonUtils'
// Import perimeter calculation utilities from lib/utils/geometry
import { calculateRectanglePerimeter } from '../../../../lib/utils/geometry/rectangleUtils'
// TODO: Cannot find module '../../../../lib/utils/cost/costUtils' or its corresponding type declarations.
// import { calculatePerimeterBasedCost } from '../../../../lib/utils/cost/costUtils';
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

// Import perimeter strategies
import {
  ArcPerimeterStrategy,
  CubicPerimeterStrategy,
  LinePerimeterStrategy,
  PolylinePerimeterStrategy,
  QuadraticPerimeterStrategy,
} from '../perimeter' // Assumes index.ts in ../perimeter exports these

export class PerimeterBasedCostStrategy implements CostCalculatorStrategy {
  protected arcPerimeterStrategy: ArcPerimeterStrategy
  protected linePerimeterStrategy: LinePerimeterStrategy
  protected polylinePerimeterStrategy: PolylinePerimeterStrategy
  protected quadraticPerimeterStrategy: QuadraticPerimeterStrategy
  protected cubicPerimeterStrategy: CubicPerimeterStrategy

  constructor() {
    this.arcPerimeterStrategy = new ArcPerimeterStrategy()
    this.linePerimeterStrategy = new LinePerimeterStrategy()
    this.polylinePerimeterStrategy = new PolylinePerimeterStrategy()
    this.quadraticPerimeterStrategy = new QuadraticPerimeterStrategy()
    this.cubicPerimeterStrategy = new CubicPerimeterStrategy()
  }

  /**
   * Calculates the cost for an element based on its perimeter or length and a specified unit cost.
   *
   * @param element - The element for which to calculate the cost.
   * @param unitCost - The cost per unit of perimeter/length.
   * @param options - Optional {@link CostCalculationOptions} such as tax rate, discount rate, or additional fixed costs.
   * @returns The total calculated cost.
   * @throws {@link CoreError} if the element type is unsupported for perimeter calculation by this strategy.
   * @throws {@link CoreError} if an underlying perimeter/length calculation fails or returns an invalid value.
   */
  public calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
    let perimeter: number

    try {
      switch (element.type as CoreElementType) {
        case CoreElementType.RECTANGLE:
        case CoreElementType.SQUARE: {
          const rect = element as unknown as Shape.Rectangle
          perimeter = calculateRectanglePerimeter(rect.width, rect.height)
          break
        }
        case CoreElementType.CIRCLE: {
          const circle = element as unknown as Shape.Circle
          perimeter = calculateCirclePerimeter(circle.radius ?? 0)
          break
        }
        case CoreElementType.ELLIPSE: {
          const ellipse = element as unknown as Shape.Ellipse
          perimeter = calculateEllipsePerimeter(ellipse.radiusX ?? 0, ellipse.radiusY ?? 0)
          break
        }
        case CoreElementType.POLYGON:
        case CoreElementType.TRIANGLE:
        case CoreElementType.HEXAGON: {
        // Add other specific polygon types if they have direct perimeter calculation
          const polygon = element as unknown as Shape.Polygon
          if (polygon.points == null || polygon.points.length < 2) { // Perimeter needs at least 2 points for a line, 3 for a closed polygon
            throw new CoreError(
              ErrorType.InvalidParameter,
              `Polygon (ID: ${element.id}) has insufficient points to calculate perimeter. Found: ${polygon.points?.length || 0}`,
              undefined,
              { component: 'PerimeterBasedCostStrategy', operation: 'calculateCost', target: element.id },
            )
          }
          perimeter = calculatePolygonPerimeter(polygon.points)
          break
        }
        case CoreElementType.LINE:
          perimeter = this.linePerimeterStrategy.calculatePerimeter(element as ShapeElement)
          break
        case CoreElementType.ARC:
          perimeter = this.arcPerimeterStrategy.calculatePerimeter(element as ShapeElement)
          break
        case CoreElementType.POLYLINE:
          perimeter = this.polylinePerimeterStrategy.calculatePerimeter(element as ShapeElement)
          break
        case CoreElementType.QUADRATIC:
          perimeter = this.quadraticPerimeterStrategy.calculatePerimeter(element as ShapeElement)
          break
        case CoreElementType.CUBIC:
          perimeter = this.cubicPerimeterStrategy.calculatePerimeter(element as ShapeElement)
          break
        case CoreElementType.QUADRILATERAL:
        case CoreElementType.PENTAGON:
        case CoreElementType.HEPTAGON:
        case CoreElementType.OCTAGON:
        case CoreElementType.NONAGON:
        case CoreElementType.DECAGON:
        case CoreElementType.LINE:
        case CoreElementType.POLYLINE:
        case CoreElementType.ARC:
        case CoreElementType.QUADRATIC:
        case CoreElementType.CUBIC:
        case CoreElementType.TEXT_LABEL:
        case CoreElementType.WALL:
        case CoreElementType.DOOR:
        case CoreElementType.WINDOW:
        case CoreElementType.FURNITURE:
        case CoreElementType.FIXTURE:
        case CoreElementType.ROOM:
        case CoreElementType.LIGHT:
        case CoreElementType.FLOOR_AREA:
        case CoreElementType.HANDRAIL:
        case CoreElementType.ELECTRICAL_OUTLET:
        case CoreElementType.ROOM_BOUNDARY:
        case CoreElementType.APPLIANCE:
        case CoreElementType.TEXT:
        case CoreElementType.IMAGE:
        case CoreElementType.GROUP:
        case CoreElementType.OPENING:
        case CoreElementType.WALL_PAINT:
        case CoreElementType.WALL_PAPER:
        default:
          throw new CoreError(
            ErrorType.InvalidElementType,
            `Perimeter calculation not implemented for type: ${element.type}. Element ID: ${element.id}`,
            undefined,
            { component: 'PerimeterBasedCostStrategy', operation: 'calculateCost', target: element.id },
          )
      }
    }
    catch (error) {
      const errMessage = error instanceof Error ? error.message : String(error)
      throw new CoreError(
        ErrorType.ComputationError,
        `Failed to calculate perimeter for element (ID: ${element.id}): ${errMessage}`,
        undefined,
        { component: 'PerimeterBasedCostStrategy', operation: 'calculateCost', target: element.id, metadata: { originalError: error } },
      )
    }

    if (typeof perimeter !== 'number' || Number.isNaN(perimeter) || perimeter < 0) {
      throw new CoreError(
        ErrorType.InvalidParameter, // Changed from INVALID_RESULT
        `Invalid perimeter value calculated: ${perimeter} for element ID: ${element.id}, type: ${element.type}. Perimeter must be a non-negative number.`,
        undefined,
        { component: 'PerimeterBasedCostStrategy', operation: 'calculateCost', target: element.id, metadata: { computedPerimeter: perimeter } },
      )
    }

    // return calculatePerimeterBasedCost(perimeter, unitCost, options);
    // 计算总成本
    // 基本成本 = 周长 * 单位成本
    let totalCost = perimeter * unitCost

    // 应用乘数（如果有）
    if (options?.quantity != null && typeof options.quantity === 'number' && !Number.isNaN(options.quantity)) {
      totalCost *= options.quantity
    }

    // 应用额外成本（如果有）
    if (options?.additionalCost != null && typeof options.additionalCost === 'number' && !Number.isNaN(options.additionalCost)) {
      totalCost += options.additionalCost
    }
    if (options?.discountRate != null && typeof options.discountRate === 'number') {
      totalCost *= (1 - (options.discountRate / 100)) // Assuming discountRate is a percentage
    }
    if (options?.taxRate != null && typeof options.taxRate === 'number') {
      totalCost *= (1 + (options.taxRate / 100)) // Assuming taxRate is a percentage
    }
    return totalCost
  }

  /**
   * Returns a string indicating the general applicability of this strategy.
   *
   * @remarks
   * This strategy can calculate perimeter-based costs for a wide variety of element types.
   * It returns a generic identifier 'perimeter_calculable'. The {@link StrategyRegistry}
   * would determine how to map specific {@link ElementType}s to this strategy, or it
   * could be used as a default for elements where perimeter is the primary cost driver.
   *
   * @returns The string 'perimeter_calculable'.
   */
  public getElementType(): string {
    return 'perimeter_calculable' // Special identifier for this generic perimeter-based cost strategy
  }
}
