import { expect, test } from '@playwright/test'

test.describe('Event Subscribers', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('about:blank')

    await page.evaluate(() => {
      const events = []
      const subscribers = new Map()

      Object.defineProperty(window, 'appEventBus', {
        value: {
          publish(event) {
            events.push(event)
            const handlers = subscribers.get(event.type) || []
            handlers.forEach(h => h(event))
          },
          getEvents() {
            return events
          },
          events,
          subscribe(eventType, handler) {
            if (!subscribers.has(eventType)) {
              subscribers.set(eventType, [])
            }
            subscribers.get(eventType).push(handler)
            return () => {
              const handlers = subscribers.get(eventType)
              if (handlers) {
                const index = handlers.indexOf(handler)
                if (index > -1) {
                  handlers.splice(index, 1)
                }
              }
            }
          },
        },
        configurable: true,
        writable: true,
      })
    })
  })

  test('should subscribe to shape create request event', async ({ page }) => {
    const result = await page.evaluate(() => {
      let receivedData = null;

      (window.appEventBus as any).subscribe('SHAPE_CREATE_REQUEST', (event) => {
        receivedData = event
      })

      window.appEventBus.publish({
        type: 'SHAPE_CREATE_REQUEST',
        timestamp: Date.now(),
        payload: {
          ElementType: 'rectangle',
          position: { x: 100, y: 200 },
          properties: { width: 50, height: 30 },
        },
      })

      return receivedData
    })

    expect(result).toBeDefined()
    expect(result.type).toBe('SHAPE_CREATE_REQUEST')
    expect(result.payload).toEqual({
      ElementType: 'rectangle',
      position: { x: 100, y: 200 },
      properties: { width: 50, height: 30 },
    })
  })
})
