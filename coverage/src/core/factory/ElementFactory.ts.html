
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/core/factory/ElementFactory.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">src/core/factory</a> ElementFactory.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/422</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/422</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a>
<a name='L618'></a><a href='#L618'>618</a>
<a name='L619'></a><a href='#L619'>619</a>
<a name='L620'></a><a href='#L620'>620</a>
<a name='L621'></a><a href='#L621'>621</a>
<a name='L622'></a><a href='#L622'>622</a>
<a name='L623'></a><a href='#L623'>623</a>
<a name='L624'></a><a href='#L624'>624</a>
<a name='L625'></a><a href='#L625'>625</a>
<a name='L626'></a><a href='#L626'>626</a>
<a name='L627'></a><a href='#L627'>627</a>
<a name='L628'></a><a href='#L628'>628</a>
<a name='L629'></a><a href='#L629'>629</a>
<a name='L630'></a><a href='#L630'>630</a>
<a name='L631'></a><a href='#L631'>631</a>
<a name='L632'></a><a href='#L632'>632</a>
<a name='L633'></a><a href='#L633'>633</a>
<a name='L634'></a><a href='#L634'>634</a>
<a name='L635'></a><a href='#L635'>635</a>
<a name='L636'></a><a href='#L636'>636</a>
<a name='L637'></a><a href='#L637'>637</a>
<a name='L638'></a><a href='#L638'>638</a>
<a name='L639'></a><a href='#L639'>639</a>
<a name='L640'></a><a href='#L640'>640</a>
<a name='L641'></a><a href='#L641'>641</a>
<a name='L642'></a><a href='#L642'>642</a>
<a name='L643'></a><a href='#L643'>643</a>
<a name='L644'></a><a href='#L644'>644</a>
<a name='L645'></a><a href='#L645'>645</a>
<a name='L646'></a><a href='#L646'>646</a>
<a name='L647'></a><a href='#L647'>647</a>
<a name='L648'></a><a href='#L648'>648</a>
<a name='L649'></a><a href='#L649'>649</a>
<a name='L650'></a><a href='#L650'>650</a>
<a name='L651'></a><a href='#L651'>651</a>
<a name='L652'></a><a href='#L652'>652</a>
<a name='L653'></a><a href='#L653'>653</a>
<a name='L654'></a><a href='#L654'>654</a>
<a name='L655'></a><a href='#L655'>655</a>
<a name='L656'></a><a href='#L656'>656</a>
<a name='L657'></a><a href='#L657'>657</a>
<a name='L658'></a><a href='#L658'>658</a>
<a name='L659'></a><a href='#L659'>659</a>
<a name='L660'></a><a href='#L660'>660</a>
<a name='L661'></a><a href='#L661'>661</a>
<a name='L662'></a><a href='#L662'>662</a>
<a name='L663'></a><a href='#L663'>663</a>
<a name='L664'></a><a href='#L664'>664</a>
<a name='L665'></a><a href='#L665'>665</a>
<a name='L666'></a><a href='#L666'>666</a>
<a name='L667'></a><a href='#L667'>667</a>
<a name='L668'></a><a href='#L668'>668</a>
<a name='L669'></a><a href='#L669'>669</a>
<a name='L670'></a><a href='#L670'>670</a>
<a name='L671'></a><a href='#L671'>671</a>
<a name='L672'></a><a href='#L672'>672</a>
<a name='L673'></a><a href='#L673'>673</a>
<a name='L674'></a><a href='#L674'>674</a>
<a name='L675'></a><a href='#L675'>675</a>
<a name='L676'></a><a href='#L676'>676</a>
<a name='L677'></a><a href='#L677'>677</a>
<a name='L678'></a><a href='#L678'>678</a>
<a name='L679'></a><a href='#L679'>679</a>
<a name='L680'></a><a href='#L680'>680</a>
<a name='L681'></a><a href='#L681'>681</a>
<a name='L682'></a><a href='#L682'>682</a>
<a name='L683'></a><a href='#L683'>683</a>
<a name='L684'></a><a href='#L684'>684</a>
<a name='L685'></a><a href='#L685'>685</a>
<a name='L686'></a><a href='#L686'>686</a>
<a name='L687'></a><a href='#L687'>687</a>
<a name='L688'></a><a href='#L688'>688</a>
<a name='L689'></a><a href='#L689'>689</a>
<a name='L690'></a><a href='#L690'>690</a>
<a name='L691'></a><a href='#L691'>691</a>
<a name='L692'></a><a href='#L692'>692</a>
<a name='L693'></a><a href='#L693'>693</a>
<a name='L694'></a><a href='#L694'>694</a>
<a name='L695'></a><a href='#L695'>695</a>
<a name='L696'></a><a href='#L696'>696</a>
<a name='L697'></a><a href='#L697'>697</a>
<a name='L698'></a><a href='#L698'>698</a>
<a name='L699'></a><a href='#L699'>699</a>
<a name='L700'></a><a href='#L700'>700</a>
<a name='L701'></a><a href='#L701'>701</a>
<a name='L702'></a><a href='#L702'>702</a>
<a name='L703'></a><a href='#L703'>703</a>
<a name='L704'></a><a href='#L704'>704</a>
<a name='L705'></a><a href='#L705'>705</a>
<a name='L706'></a><a href='#L706'>706</a>
<a name='L707'></a><a href='#L707'>707</a>
<a name='L708'></a><a href='#L708'>708</a>
<a name='L709'></a><a href='#L709'>709</a>
<a name='L710'></a><a href='#L710'>710</a>
<a name='L711'></a><a href='#L711'>711</a>
<a name='L712'></a><a href='#L712'>712</a>
<a name='L713'></a><a href='#L713'>713</a>
<a name='L714'></a><a href='#L714'>714</a>
<a name='L715'></a><a href='#L715'>715</a>
<a name='L716'></a><a href='#L716'>716</a>
<a name='L717'></a><a href='#L717'>717</a>
<a name='L718'></a><a href='#L718'>718</a>
<a name='L719'></a><a href='#L719'>719</a>
<a name='L720'></a><a href='#L720'>720</a>
<a name='L721'></a><a href='#L721'>721</a>
<a name='L722'></a><a href='#L722'>722</a>
<a name='L723'></a><a href='#L723'>723</a>
<a name='L724'></a><a href='#L724'>724</a>
<a name='L725'></a><a href='#L725'>725</a>
<a name='L726'></a><a href='#L726'>726</a>
<a name='L727'></a><a href='#L727'>727</a>
<a name='L728'></a><a href='#L728'>728</a>
<a name='L729'></a><a href='#L729'>729</a>
<a name='L730'></a><a href='#L730'>730</a>
<a name='L731'></a><a href='#L731'>731</a>
<a name='L732'></a><a href='#L732'>732</a>
<a name='L733'></a><a href='#L733'>733</a>
<a name='L734'></a><a href='#L734'>734</a>
<a name='L735'></a><a href='#L735'>735</a>
<a name='L736'></a><a href='#L736'>736</a>
<a name='L737'></a><a href='#L737'>737</a>
<a name='L738'></a><a href='#L738'>738</a>
<a name='L739'></a><a href='#L739'>739</a>
<a name='L740'></a><a href='#L740'>740</a>
<a name='L741'></a><a href='#L741'>741</a>
<a name='L742'></a><a href='#L742'>742</a>
<a name='L743'></a><a href='#L743'>743</a>
<a name='L744'></a><a href='#L744'>744</a>
<a name='L745'></a><a href='#L745'>745</a>
<a name='L746'></a><a href='#L746'>746</a>
<a name='L747'></a><a href='#L747'>747</a>
<a name='L748'></a><a href='#L748'>748</a>
<a name='L749'></a><a href='#L749'>749</a>
<a name='L750'></a><a href='#L750'>750</a>
<a name='L751'></a><a href='#L751'>751</a>
<a name='L752'></a><a href='#L752'>752</a>
<a name='L753'></a><a href='#L753'>753</a>
<a name='L754'></a><a href='#L754'>754</a>
<a name='L755'></a><a href='#L755'>755</a>
<a name='L756'></a><a href='#L756'>756</a>
<a name='L757'></a><a href='#L757'>757</a>
<a name='L758'></a><a href='#L758'>758</a>
<a name='L759'></a><a href='#L759'>759</a>
<a name='L760'></a><a href='#L760'>760</a>
<a name='L761'></a><a href='#L761'>761</a>
<a name='L762'></a><a href='#L762'>762</a>
<a name='L763'></a><a href='#L763'>763</a>
<a name='L764'></a><a href='#L764'>764</a>
<a name='L765'></a><a href='#L765'>765</a>
<a name='L766'></a><a href='#L766'>766</a>
<a name='L767'></a><a href='#L767'>767</a>
<a name='L768'></a><a href='#L768'>768</a>
<a name='L769'></a><a href='#L769'>769</a>
<a name='L770'></a><a href='#L770'>770</a>
<a name='L771'></a><a href='#L771'>771</a>
<a name='L772'></a><a href='#L772'>772</a>
<a name='L773'></a><a href='#L773'>773</a>
<a name='L774'></a><a href='#L774'>774</a>
<a name='L775'></a><a href='#L775'>775</a>
<a name='L776'></a><a href='#L776'>776</a>
<a name='L777'></a><a href='#L777'>777</a>
<a name='L778'></a><a href='#L778'>778</a>
<a name='L779'></a><a href='#L779'>779</a>
<a name='L780'></a><a href='#L780'>780</a>
<a name='L781'></a><a href='#L781'>781</a>
<a name='L782'></a><a href='#L782'>782</a>
<a name='L783'></a><a href='#L783'>783</a>
<a name='L784'></a><a href='#L784'>784</a>
<a name='L785'></a><a href='#L785'>785</a>
<a name='L786'></a><a href='#L786'>786</a>
<a name='L787'></a><a href='#L787'>787</a>
<a name='L788'></a><a href='#L788'>788</a>
<a name='L789'></a><a href='#L789'>789</a>
<a name='L790'></a><a href='#L790'>790</a>
<a name='L791'></a><a href='#L791'>791</a>
<a name='L792'></a><a href='#L792'>792</a>
<a name='L793'></a><a href='#L793'>793</a>
<a name='L794'></a><a href='#L794'>794</a>
<a name='L795'></a><a href='#L795'>795</a>
<a name='L796'></a><a href='#L796'>796</a>
<a name='L797'></a><a href='#L797'>797</a>
<a name='L798'></a><a href='#L798'>798</a>
<a name='L799'></a><a href='#L799'>799</a>
<a name='L800'></a><a href='#L800'>800</a>
<a name='L801'></a><a href='#L801'>801</a>
<a name='L802'></a><a href='#L802'>802</a>
<a name='L803'></a><a href='#L803'>803</a>
<a name='L804'></a><a href='#L804'>804</a>
<a name='L805'></a><a href='#L805'>805</a>
<a name='L806'></a><a href='#L806'>806</a>
<a name='L807'></a><a href='#L807'>807</a>
<a name='L808'></a><a href='#L808'>808</a>
<a name='L809'></a><a href='#L809'>809</a>
<a name='L810'></a><a href='#L810'>810</a>
<a name='L811'></a><a href='#L811'>811</a>
<a name='L812'></a><a href='#L812'>812</a>
<a name='L813'></a><a href='#L813'>813</a>
<a name='L814'></a><a href='#L814'>814</a>
<a name='L815'></a><a href='#L815'>815</a>
<a name='L816'></a><a href='#L816'>816</a>
<a name='L817'></a><a href='#L817'>817</a>
<a name='L818'></a><a href='#L818'>818</a>
<a name='L819'></a><a href='#L819'>819</a>
<a name='L820'></a><a href='#L820'>820</a>
<a name='L821'></a><a href='#L821'>821</a>
<a name='L822'></a><a href='#L822'>822</a>
<a name='L823'></a><a href='#L823'>823</a>
<a name='L824'></a><a href='#L824'>824</a>
<a name='L825'></a><a href='#L825'>825</a>
<a name='L826'></a><a href='#L826'>826</a>
<a name='L827'></a><a href='#L827'>827</a>
<a name='L828'></a><a href='#L828'>828</a>
<a name='L829'></a><a href='#L829'>829</a>
<a name='L830'></a><a href='#L830'>830</a>
<a name='L831'></a><a href='#L831'>831</a>
<a name='L832'></a><a href='#L832'>832</a>
<a name='L833'></a><a href='#L833'>833</a>
<a name='L834'></a><a href='#L834'>834</a>
<a name='L835'></a><a href='#L835'>835</a>
<a name='L836'></a><a href='#L836'>836</a>
<a name='L837'></a><a href='#L837'>837</a>
<a name='L838'></a><a href='#L838'>838</a>
<a name='L839'></a><a href='#L839'>839</a>
<a name='L840'></a><a href='#L840'>840</a>
<a name='L841'></a><a href='#L841'>841</a>
<a name='L842'></a><a href='#L842'>842</a>
<a name='L843'></a><a href='#L843'>843</a>
<a name='L844'></a><a href='#L844'>844</a>
<a name='L845'></a><a href='#L845'>845</a>
<a name='L846'></a><a href='#L846'>846</a>
<a name='L847'></a><a href='#L847'>847</a>
<a name='L848'></a><a href='#L848'>848</a>
<a name='L849'></a><a href='#L849'>849</a>
<a name='L850'></a><a href='#L850'>850</a>
<a name='L851'></a><a href='#L851'>851</a>
<a name='L852'></a><a href='#L852'>852</a>
<a name='L853'></a><a href='#L853'>853</a>
<a name='L854'></a><a href='#L854'>854</a>
<a name='L855'></a><a href='#L855'>855</a>
<a name='L856'></a><a href='#L856'>856</a>
<a name='L857'></a><a href='#L857'>857</a>
<a name='L858'></a><a href='#L858'>858</a>
<a name='L859'></a><a href='#L859'>859</a>
<a name='L860'></a><a href='#L860'>860</a>
<a name='L861'></a><a href='#L861'>861</a>
<a name='L862'></a><a href='#L862'>862</a>
<a name='L863'></a><a href='#L863'>863</a>
<a name='L864'></a><a href='#L864'>864</a>
<a name='L865'></a><a href='#L865'>865</a>
<a name='L866'></a><a href='#L866'>866</a>
<a name='L867'></a><a href='#L867'>867</a>
<a name='L868'></a><a href='#L868'>868</a>
<a name='L869'></a><a href='#L869'>869</a>
<a name='L870'></a><a href='#L870'>870</a>
<a name='L871'></a><a href='#L871'>871</a>
<a name='L872'></a><a href='#L872'>872</a>
<a name='L873'></a><a href='#L873'>873</a>
<a name='L874'></a><a href='#L874'>874</a>
<a name='L875'></a><a href='#L875'>875</a>
<a name='L876'></a><a href='#L876'>876</a>
<a name='L877'></a><a href='#L877'>877</a>
<a name='L878'></a><a href='#L878'>878</a>
<a name='L879'></a><a href='#L879'>879</a>
<a name='L880'></a><a href='#L880'>880</a>
<a name='L881'></a><a href='#L881'>881</a>
<a name='L882'></a><a href='#L882'>882</a>
<a name='L883'></a><a href='#L883'>883</a>
<a name='L884'></a><a href='#L884'>884</a>
<a name='L885'></a><a href='#L885'>885</a>
<a name='L886'></a><a href='#L886'>886</a>
<a name='L887'></a><a href='#L887'>887</a>
<a name='L888'></a><a href='#L888'>888</a>
<a name='L889'></a><a href='#L889'>889</a>
<a name='L890'></a><a href='#L890'>890</a>
<a name='L891'></a><a href='#L891'>891</a>
<a name='L892'></a><a href='#L892'>892</a>
<a name='L893'></a><a href='#L893'>893</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * Element Creation Factory
 *
 * @remarks
 * This factory class is responsible for instantiating various types of elements,
 * including geometric shapes (rectangles, ellipses, polygons, etc.) and paths
 * (lines, arcs, Bezier curves). It utilizes a registry of creator classes,
 * each specialized in constructing a specific element type.
 *
 * The factory provides methods like `createShape` and `createPath` that take
 * element type and creation parameters, then delegate to the appropriate registered
 * creator. It also includes helper methods for normalizing input parameters (e.g., position)
 * and ensuring metadata completeness.
 *
 * Default creators for standard element types are registered upon instantiation.
 * Custom creators can also be registered for extending the factory's capabilities.
 *
 * The factory defines various `Create...Params` interfaces for type-safe parameter
 * passing to creators and `User...InputParams` types for more flexible user-facing APIs.
 *
 * @module core/factory/ElementFactory
 * @see {@link IElementFactory}
 * @see {@link ShapeCreator}
 * @see {@link PathCreator}
 * @see {@link ShapeCreationParamsUnion}
 * @see {@link PathCreationOptionsUnion}
 */
&nbsp;
import type { PathCreator } from './creators/path/PathCreator'
import type { PointData } from '@/types/core/element/geometry/point' // Changed import
import type {
  BaseStyleProperties,
  Element,
  MetadataProperties,
  ShapeElement as ShapeModel,
} from '@/types/core/elementDefinitions'
import type { MajorCategory, MinorCategory } from '@/types/core/majorMinorTypes' // Added import
<span class="cstat-no" title="statement not covered" >import { ensureCompleteMetadata } from '@/lib/utils/element/elementUtils'</span>
// import { PointClass } from '@/lib/utils/geometry'; // PointClass is not directly used as a value
<span class="cstat-no" title="statement not covered" >import { CoreError, ErrorType } from '@/services/system/error-service' // Added import</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >import {</span>
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'
<span class="cstat-no" title="statement not covered" >import * as creators from './creators'</span>
<span class="cstat-no" title="statement not covered" >import ImageCreator from './creators/media/ImageCreator' // Default import from media directory</span>
<span class="cstat-no" title="statement not covered" >import { TextCreator } from './creators/media/TextCreator' // Named import from media directory</span>
<span class="cstat-no" title="statement not covered" >import { ShapeCreator } from './creators/shape/ShapeCreator'</span>
&nbsp;
/**
 * Base parameters for creating any element.
 * Includes common properties like ID, type, metadata, visibility, styling, etc.
 */
export interface BaseElementCreationParams extends BaseStyleProperties {
  id: string
  type: CoreElementType | string // Allow string for flexibility with custom types
  majorCategory?: MajorCategory
  minorCategory?: MinorCategory
  zLevelId?: string
  isFixedCategory?: boolean
  metadata?: Partial&lt;MetadataProperties&gt;
  visible?: boolean
  locked?: boolean
  rotation?: number
  selectable?: boolean
  draggable?: boolean
  showHandles?: boolean
  layer?: string
  zIndex?: number
  properties?: Record&lt;string, unknown&gt;
}
&nbsp;
/** Parameters for creating Rectangle or Square elements. */
export interface CreateRectangleParams extends BaseElementCreationParams {
  type: CoreElementType.RECTANGLE | CoreElementType.SQUARE | 'rectangle' | 'square'
  position: PointData // Changed type
  width: number
  height: number
  cornerRadius?: number
}
&nbsp;
/** Parameters for creating Ellipse elements. */
export interface CreateEllipseParams extends BaseElementCreationParams {
  type: CoreElementType.ELLIPSE | 'ellipse'
  position: PointData // Changed type
  radiusX: number
  radiusY: number
}
&nbsp;
/** Parameters for creating Circle elements. */
export interface CreateCircleParams extends BaseElementCreationParams {
  type: CoreElementType.CIRCLE | 'circle'
  position: PointData // Changed type
  radius: number
}
&nbsp;
/** Parameters for creating Polygon-like elements (Polygon, Triangle, Hexagon, etc.). */
export interface CreatePolygonParams extends BaseElementCreationParams {
  type: CoreElementType.POLYGON | CoreElementType.TRIANGLE | CoreElementType.HEXAGON | CoreElementType.QUADRILATERAL | CoreElementType.PENTAGON | 'polygon' | 'triangle' | 'hexagon' | 'quadrilateral' | 'pentagon' // Allow specific polygon types
  points: PointData[] // Changed type
  sides?: number // For regular polygons created by sides/radius
  radius?: number // For regular polygons
  center?: PointData // Changed type, for regular polygons
  position?: PointData // Added: Position for the polygon (used for mouse drop position)
  isRegular?: boolean // Added: Indicates if the polygon should be regular
  startAngleRad?: number // Added: Optional starting angle in radians for the first vertex of a regular polygon
}
&nbsp;
/** Parameters for creating Line elements. */
export interface CreateLineParams extends BaseElementCreationParams {
  type: CoreElementType.LINE | 'line'
  start: PointData // Changed type
  end: PointData // Changed type
  arrowStart?: boolean
  arrowEnd?: boolean
}
&nbsp;
/** Parameters for creating Polyline elements. */
export interface CreatePolylineParams extends BaseElementCreationParams {
  type: CoreElementType.POLYLINE | 'polyline'
  points: PointData[] // Changed type
  curved?: boolean // If the polyline should be rendered with curves (e.g., Catmull-Rom)
  tension?: number // Tension for curved polylines
}
&nbsp;
/** Type alias for creating an element directly from an existing ShapeModel. */
export type CreateElementFromModelParams = ShapeModel
&nbsp;
/** Parameters for creating Arc elements. */
export interface CreateArcParams extends BaseElementCreationParams {
  type: CoreElementType.ARC | 'arc'
  position: PointData // Changed type (center of the arc)
  radius: number
  startAngle: number // In degrees
  endAngle: number // In degrees
  closed?: boolean // Whether the arc forms a sector
  counterClockwise?: boolean // Direction of drawing. Defaults to false (clockwise).
}
&nbsp;
/** Parameters for creating Quadratic Bezier Curve elements. */
export interface CreateQuadraticParams extends BaseElementCreationParams {
  type: CoreElementType.QUADRATIC | 'quadratic'
  start: PointData // Changed type
  control: PointData // Changed type
  end: PointData // Changed type
}
&nbsp;
/** Parameters for creating Cubic Bezier Curve elements. */
export interface CreateCubicParams extends BaseElementCreationParams {
  type: CoreElementType.CUBIC | 'cubic'
  start: PointData // Changed type
  control1: PointData // Changed type
  control2: PointData // Changed type
  end: PointData // Changed type
}
&nbsp;
/** Parameters for creating Text elements. */
export interface CreateTextParams extends BaseElementCreationParams {
  type: CoreElementType.TEXT | 'text'
  position: PointData // Position of the text (e.g., top-left, baseline start)
  // Text-specific properties are now direct optional properties:
  text?: string
  fontSize?: number
  fontFamily?: string
  fontWeight?: string | number // Keep flexible as per defaultElementSettings
  fontStyle?: string
  textAlign?: CanvasTextAlign
  textBaseline?: CanvasTextBaseline
  lineHeight?: number
  fill?: string // Text color can be distinct
  // properties?: Record&lt;string, any&gt;; // Retain for any other custom/nested properties if needed
}
&nbsp;
/** Parameters for creating Image elements. */
export interface CreateImageParams extends BaseElementCreationParams {
  type: CoreElementType.IMAGE | 'image'
  position: PointData // Position of the image (e.g., top-left corner)
  // Specific image properties are now direct optional properties:
  src?: string
  width?: number
  height?: number
  alt?: string
  sourceType?: 'url' | 'svg_inline_data'
  // properties?: Record&lt;string, any&gt;; // Retain for any other custom/nested properties if needed
}
&nbsp;
/** Union type for all possible shape creation parameter objects. */
export type ShapeCreationParamsUnion =
  | CreateRectangleParams | CreateEllipseParams | CreateCircleParams | CreatePolygonParams
  | CreateLineParams | CreatePolylineParams | CreateArcParams
  | CreateQuadraticParams | CreateCubicParams | CreateElementFromModelParams
  | CreateTextParams | CreateImageParams
&nbsp;
/** Union type for all possible path creation parameter objects. */
export type PathCreationOptionsUnion =
  | CreateArcParams | CreateQuadraticParams | CreateCubicParams
  | CreateLineParams | CreatePolylineParams
&nbsp;
/** Common user input parameters for creating elements, allowing partial metadata and base style/shape properties. */
type CommonUserInputParams = {
  majorCategory: MajorCategory // Added: majorCategory is now required
  minorCategory?: MinorCategory // Added: optional minorCategory
  zLevelId?: string // Added: optional zLevelId
  isFixedCategory?: boolean // Added: optional isFixedCategory
  metadata?: Partial&lt;MetadataProperties&gt;
} &amp; Partial&lt;BaseStyleProperties&gt; &amp; Partial&lt;Pick&lt;ShapeModel, 'visible' | 'locked' | 'rotation' | 'selectable' | 'draggable' | 'showHandles' | 'layer' | 'zIndex' | 'properties'&gt;&gt;
&nbsp;
/** User input parameters for creating a Rectangle. Position can be a PointData object or a coordinate array. */
export type UserRectInputParams = CommonUserInputParams &amp;
  { width: number, height: number, position?: PointData | [number, number, number?], cornerRadius?: number }
&nbsp;
/** User input parameters for creating a Square. Position can be a PointData object or a coordinate array. */
export type UserSquareInputParams = CommonUserInputParams &amp;
  { size: number, position?: PointData | [number, number, number?], cornerRadius?: number }
&nbsp;
/** User input parameters for creating an Ellipse. Position can be a PointData object or a coordinate array. */
export type UserEllipseInputParams = CommonUserInputParams &amp;
  { radiusX: number, radiusY: number, position?: PointData | [number, number, number?] }
&nbsp;
/** User input parameters for creating a Circle. Position can be a PointData object or a coordinate array. */
export type UserCircleInputParams = CommonUserInputParams &amp;
  { radius: number, position?: PointData | [number, number, number?] }
&nbsp;
/** User input parameters for creating a Line. Start/end points can be PointData objects or coordinate arrays. */
export type UserLineInputParams = CommonUserInputParams &amp;
  { start: PointData | [number, number, number?], end: PointData | [number, number, number?], arrowStart?: boolean, arrowEnd?: boolean }
&nbsp;
/** User input parameters for creating a Polyline. Points can be PointData objects or coordinate arrays. */
export type UserPolylineInputParams = CommonUserInputParams &amp;
  { points: (PointData | [number, number, number?])[], curved?: boolean, tension?: number }
&nbsp;
/** User input parameters for creating a regular Polygon (defined by sides and radius). Center can be a PointData object or a coordinate array. */
export type UserRegularPolygonInputParams = CommonUserInputParams &amp;
  { sides: number, radius: number, center?: PointData | [number, number, number?] }
&nbsp;
/** User input parameters for creating a custom Polygon (defined by an array of points). Points can be PointData objects or coordinate arrays. */
export type UserCustomPolygonInputParams = CommonUserInputParams &amp;
  { points: (PointData | [number, number, number?])[] }
&nbsp;
/** User input parameters for creating an Arc. Position can be a PointData object or a coordinate array. */
export type UserArcInputParams = CommonUserInputParams &amp;
  { radius: number, startAngle: number, endAngle: number, position?: PointData | [number, number, number?], closed?: boolean }
&nbsp;
/**
 * Defines the contract for an element factory.
 */
export interface IElementFactory {
  /**
   * Creates a shape element of the specified type with the given parameters.
   * @param type - The {@link CoreElementType} or string identifier of the shape to create.
   * @param params - A {@link ShapeCreationParamsUnion} object containing creation parameters.
   * @returns A Promise resolving to the created {@link ShapeModel}.
   */
  createShape: (type: CoreElementType | string, params: ShapeCreationParamsUnion) =&gt; Promise&lt;ShapeModel&gt;
&nbsp;
  /**
   * Creates a path element of the specified type with the given parameters.
   * @param type - The {@link CoreElementType} or string identifier of the path to create.
   * @param params - A {@link PathCreationOptionsUnion} object containing creation parameters.
   * @returns A Promise resolving to the created {@link Element} (typically a more specific Path type).
   */
  createPath: (type: CoreElementType | string, params: PathCreationOptionsUnion) =&gt; Promise&lt;Element&gt;
&nbsp;
  /**
   * Registers a creator instance for a specific element type.
   * @param type - The {@link CoreElementType} or string identifier to associate with the creator.
   * @param creator - An instance of {@link ShapeCreator} or {@link PathCreator}.
   */
  registerCreator: (type: CoreElementType | string, creator: ShapeCreator&lt;ShapeModel, BaseElementCreationParams&gt; | PathCreator&lt;ShapeModel, BaseElementCreationParams&gt;) =&gt; void
}
&nbsp;
/**
 * Factory class responsible for creating various types of geometric elements,
 * including shapes and paths, using registered creator instances.
 *
 * @implements {IElementFactory}
 */
<span class="cstat-no" title="statement not covered" >export class ElementFactory implements IElementFactory {</span>
  /**
   * Map of registered creators indexed by element type.
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private creators: Map&lt;string, ShapeCreator&lt;ShapeModel, BaseElementCreationParams&gt; | PathCreator&lt;ShapeModel, BaseElementCreationParams&gt;&gt; = new Map()</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  constructor() {</span>
<span class="cstat-no" title="statement not covered" >    console.warn('[ElementFactory CONSTRUCTOR] Initializing and registering default creators.')</span>
<span class="cstat-no" title="statement not covered" >    this.registerDefaultCreators()</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Generates a simple UUID for element identification.
   *
   * @returns A string representing a UUID.
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private generateUUID(): string {</span>
<span class="cstat-no" title="statement not covered" >    return Math.random().toString(36).substring(2, 9) + Math.random().toString(36).substring(2, 9)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Normalizes various position input formats (object, array, or undefined)
   * to a standard {@link PointData} interface.
   *
   * @param positionInput - The position input, which can be a `PointData`-like object,
   *                        a 2-element array `[x, y]`, a 3-element array `[x, y, z]`, or `undefined`.
   * @returns A standardized {@link PointData} object (e.g., `{ x: number, y: number, z: number }`).
   *          Defaults to `{ x: 0, y: 0, z: 0 }` if input is invalid or undefined.
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private normalizePositionInput(positionInput: PointData | [number, number] | [number, number, number?] | undefined): PointData { // Changed type</span>
<span class="cstat-no" title="statement not covered" >    if (positionInput == null)</span>
<span class="cstat-no" title="statement not covered" >      return { x: 0, y: 0, z: 0 }</span>
<span class="cstat-no" title="statement not covered" >    if (Array.isArray(positionInput)) {</span>
<span class="cstat-no" title="statement not covered" >      if (positionInput.length &gt;= 2 &amp;&amp; typeof positionInput[0] === 'number' &amp;&amp; typeof positionInput[1] === 'number') {</span>
<span class="cstat-no" title="statement not covered" >        return { x: positionInput[0], y: positionInput[1], z: positionInput.length &gt; 2 &amp;&amp; typeof positionInput[2] === 'number' ? positionInput[2] : 0 }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      console.warn('[ElementFactory] Invalid array for position, defaulting to (0,0,0). Array:', positionInput)</span>
<span class="cstat-no" title="statement not covered" >      return { x: 0, y: 0, z: 0 }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else if (positionInput != null &amp;&amp; typeof positionInput.x === 'number' &amp;&amp; typeof positionInput.y === 'number') {</span>
<span class="cstat-no" title="statement not covered" >      return { x: positionInput.x, y: positionInput.y, z: positionInput.z ?? 0 }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    console.warn('[ElementFactory] Invalid position object, defaulting to (0,0,0). Position:', positionInput)</span>
<span class="cstat-no" title="statement not covered" >    return { x: 0, y: 0, z: 0 }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Registers all default shape and path creators.
   *
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private registerDefaultCreators(): void {</span>
    // Shape Creators
<span class="cstat-no" title="statement not covered" >    console.warn('[ElementFactory registerDefaultCreators] Registering Shape Creators...')</span>
<span class="cstat-no" title="statement not covered" >    const rectangleCreatorInstance = new creators.RectangleCreator()</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.RECTANGLE, rectangleCreatorInstance)</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.RECTANGLE}`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.SQUARE, rectangleCreatorInstance)</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.SQUARE}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const ellipseCreatorInstance = new creators.EllipseCreator()</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.ELLIPSE, ellipseCreatorInstance)</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.ELLIPSE}`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.CIRCLE, ellipseCreatorInstance)</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.CIRCLE}`)</span>
&nbsp;
    // For PolygonCreator, it seems its constructor uses the elementType to specialize.
    // So, we instantiate it for each specific polygon type it handles.
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.POLYGON, new creators.PolygonCreator(CoreElementType.POLYGON))</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.POLYGON}`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.TRIANGLE, new creators.PolygonCreator(CoreElementType.TRIANGLE))</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.TRIANGLE}`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.QUADRILATERAL, new creators.PolygonCreator(CoreElementType.QUADRILATERAL))</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.QUADRILATERAL}`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.PENTAGON, new creators.PolygonCreator(CoreElementType.PENTAGON))</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.PENTAGON}`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.HEXAGON, new creators.PolygonCreator(CoreElementType.HEXAGON))</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.HEXAGON}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.TEXT, new TextCreator())</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.TEXT}`)</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.TEXT]}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const imageCreator = new ImageCreator() // Default import</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.IMAGE, imageCreator)</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.IMAGE]}`)</span>
&nbsp;
    // Register creators for new entity types, using ImageCreator as a base
    // as these will often be represented by icons/images and share similar basic properties.
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.FURNITURE, imageCreator)</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.FURNITURE]} (using ImageCreator)`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.FIXTURE, imageCreator)</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.FIXTURE]} (using ImageCreator)`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.LIGHT, imageCreator)</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.LIGHT]} (using ImageCreator)`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.FLOOR_AREA, imageCreator) // For items like rugs, tile patterns</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.FLOOR_AREA]} (using ImageCreator)`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.WALL_PAINT, imageCreator)</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.WALL_PAINT]} (using ImageCreator)`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.WALL_PAPER, imageCreator)</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.WALL_PAPER]} (using ImageCreator)`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.OPENING, imageCreator) // For doors, windows</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType[CoreElementType.OPENING]} (using ImageCreator)`)</span>
&nbsp;
    // Path Creators
<span class="cstat-no" title="statement not covered" >    console.warn('[ElementFactory registerDefaultCreators] Registering Path Creators...')</span>
<span class="cstat-no" title="statement not covered" >    const lineCreator = new creators.LineCreator()</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.LINE, lineCreator)</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.LINE}`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.POLYLINE, new creators.PolylineCreator())</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.POLYLINE}`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.ARC, new creators.ArcCreator())</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.ARC}`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.QUADRATIC, new creators.QuadraticCreator())</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.QUADRATIC}`)</span>
<span class="cstat-no" title="statement not covered" >    this.registerCreator(CoreElementType.CUBIC, new creators.CubicCreator())</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerDefaultCreators] Registered: ${CoreElementType.CUBIC}`)</span>
&nbsp;
    // Other element types if any (e.g., Text, Image, Group)
    // this.registerCreator(CoreElementType.TEXT, new creators.TextCreator());
    // this.registerCreator(CoreElementType.IMAGE, new creators.ImageCreator());
    // this.registerCreator(CoreElementType.GROUP, new creators.GroupCreator());
&nbsp;
<span class="cstat-no" title="statement not covered" >    console.warn('[ElementFactory registerDefaultCreators] All default creators registered.')</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a shape element using the appropriate registered {@link ShapeCreator}.
   *
   * @param type - The {@link CoreElementType} or string identifier of the shape to create.
   * @param params - A {@link ShapeCreationParamsUnion} object containing parameters for shape creation.
   *                 This method ensures common base properties are defaulted if not provided.
   * @returns A Promise resolving to the created {@link ShapeModel}.
   * @throws {@link Error} if no valid creator is registered for the specified type.
   */
<span class="cstat-no" title="statement not covered" >  public async createShape(type: CoreElementType | string, params: ShapeCreationParamsUnion): Promise&lt;ShapeModel&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const typeKey = type.toString()</span>
<span class="cstat-no" title="statement not covered" >    const creatorInstance = this.creators.get(typeKey)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (creatorInstance == null) {</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(ErrorType.FactoryFailed, `No creator registered for shape type: ${typeKey}`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (creatorInstance instanceof ShapeCreator) {</span>
<span class="cstat-no" title="statement not covered" >      return creatorInstance.create(params)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(ErrorType.FactoryFailed, `Registered creator for shape type ${typeKey} is not a ShapeCreator.`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a path element using the appropriate registered {@link PathCreator}.
   *
   * @param type - The {@link CoreElementType} or string identifier of the path to create.
   * @param params - A {@link PathCreationOptionsUnion} object containing parameters for path creation.
   *                 This method ensures common base properties are defaulted and normalizes point data.
   * @returns A Promise resolving to the created {@link Element} (which will be a specific Path type).
   * @throws {@link Error} if no valid creator is registered for the specified type.
   */
<span class="cstat-no" title="statement not covered" >  public async createPath(type: CoreElementType | string, params: PathCreationOptionsUnion): Promise&lt;Element&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const typeKey = type.toString()</span>
<span class="cstat-no" title="statement not covered" >    const creatorInstance = this.creators.get(typeKey) as PathCreator&lt;ShapeModel, BaseElementCreationParams&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (creatorInstance == null) {</span>
<span class="cstat-no" title="statement not covered" >      throw new CoreError(ErrorType.FactoryFailed, `No creator registered for path type: ${typeKey}`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return creatorInstance.create(params)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Registers a {@link ShapeCreator} or {@link PathCreator} for a specific element type.
   *
   * @param type - The {@link CoreElementType} or string identifier for which to register the creator.
   * @param creator - The creator instance (either a `ShapeCreator` or `PathCreator`).
   *                  If a creator for the type already exists, it will be overridden with a warning.
   */
<span class="cstat-no" title="statement not covered" >  public registerCreator(type: CoreElementType | string, creator: ShapeCreator&lt;ShapeModel, BaseElementCreationParams&gt; | PathCreator&lt;ShapeModel, BaseElementCreationParams&gt;): void {</span>
<span class="cstat-no" title="statement not covered" >    const typeKey = type.toString()</span>
<span class="cstat-no" title="statement not covered" >    if (this.creators.has(typeKey)) {</span>
      // console.warn(`[ElementFactory] Creator for type ${type} is being overwritten.`); // Optional: useful for debugging registration issues
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    console.warn(`[ElementFactory registerCreator] Registering creator ${creator.constructor.name} for type ${type.toString()}`)</span>
<span class="cstat-no" title="statement not covered" >    this.creators.set(typeKey, creator)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Prepares base parameters for element creation from common user input.
   * This ensures that metadata is complete and common optional properties are defaulted.
   *
   * @param userParams - The {@link CommonUserInputParams} provided by the user.
   * @param defaultNamePrefix - A prefix to use for generating a default element name if not provided in `userParams.metadata`.
   * @returns An object containing base element creation parameters, excluding `id` and `type` (which are added by the calling method).
   * @private
   */
<span class="cstat-no" title="statement not covered" >  private _prepareBaseParams(</span>
<span class="cstat-no" title="statement not covered" >    userParams: CommonUserInputParams, // Use CommonUserInputParams</span>
<span class="cstat-no" title="statement not covered" >    defaultNamePrefix: string, // Keep for naming if userParams.metadata.name is not set</span>
    // id is now part of BaseElementCreationParams, not needed here directly
<span class="cstat-no" title="statement not covered" >  ): Omit&lt;BaseElementCreationParams, 'id' | 'type'&gt; { // id and type are handled by the caller</span>
    // const id = this.generateUUID(); // ID generation moved to specific create methods or ShapeCreator
<span class="cstat-no" title="statement not covered" >    const metadata = ensureCompleteMetadata({</span>
<span class="cstat-no" title="statement not covered" >      ...(userParams.metadata || {}),</span>
<span class="cstat-no" title="statement not covered" >      name: userParams.metadata?.name ?? `${defaultNamePrefix} Element`, // Generic name</span>
<span class="cstat-no" title="statement not covered" >    })</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return {</span>
      // id, // ID is handled by the specific creator or passed in params
      // type: CoreElementType.UNKNOWN, // Type is set by the specific creator
<span class="cstat-no" title="statement not covered" >      majorCategory: userParams.majorCategory, // Pass through majorCategory</span>
<span class="cstat-no" title="statement not covered" >      minorCategory: userParams.minorCategory, // Pass through minorCategory</span>
<span class="cstat-no" title="statement not covered" >      zLevelId: userParams.zLevelId, // Pass through zLevelId</span>
<span class="cstat-no" title="statement not covered" >      isFixedCategory: userParams.isFixedCategory, // Pass through isFixedCategory</span>
<span class="cstat-no" title="statement not covered" >      metadata,</span>
<span class="cstat-no" title="statement not covered" >      visible: userParams.visible ?? true,</span>
<span class="cstat-no" title="statement not covered" >      locked: userParams.locked ?? false,</span>
<span class="cstat-no" title="statement not covered" >      rotation: userParams.rotation ?? 0,</span>
<span class="cstat-no" title="statement not covered" >      selectable: userParams.selectable ?? true,</span>
<span class="cstat-no" title="statement not covered" >      draggable: userParams.draggable ?? true,</span>
<span class="cstat-no" title="statement not covered" >      showHandles: userParams.showHandles ?? true,</span>
      // Layer is deprecated but still used in some places
      // We're using a spread operator to avoid direct property assignment
      // which would trigger the TypeScript deprecation warning
<span class="cstat-no" title="statement not covered" >      ...(userParams.layer !== undefined ? { layer: userParams.layer } : {}),</span>
<span class="cstat-no" title="statement not covered" >      zIndex: userParams.zIndex,</span>
<span class="cstat-no" title="statement not covered" >      properties: userParams.properties,</span>
<span class="cstat-no" title="statement not covered" >      fill: userParams.fill,</span>
<span class="cstat-no" title="statement not covered" >      stroke: userParams.stroke,</span>
<span class="cstat-no" title="statement not covered" >      strokeWidth: userParams.strokeWidth,</span>
<span class="cstat-no" title="statement not covered" >      opacity: userParams.opacity,</span>
<span class="cstat-no" title="statement not covered" >      strokeDasharray: userParams.strokeDasharray,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a Rectangle element from user-provided parameters.
   *
   * @param userParams - {@link UserRectInputParams} specifying the rectangle's properties.
   * @returns A Promise resolving to the created {@link ShapeModel} (Rectangle).
   */
<span class="cstat-no" title="statement not covered" >  public async createRectangle(userParams: UserRectInputParams): Promise&lt;ShapeModel&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const id = this.generateUUID()</span>
<span class="cstat-no" title="statement not covered" >    const type = (userParams.cornerRadius != null &amp;&amp; userParams.cornerRadius &gt; 0) ? CoreElementType.SQUARE : CoreElementType.RECTANGLE</span>
<span class="cstat-no" title="statement not covered" >    const position = this.normalizePositionInput(userParams.position)</span>
<span class="cstat-no" title="statement not covered" >    const baseParams = this._prepareBaseParams(userParams, type === CoreElementType.SQUARE ? 'Square' : 'Rectangle')</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const params: CreateRectangleParams = {</span>
<span class="cstat-no" title="statement not covered" >      ...baseParams,</span>
<span class="cstat-no" title="statement not covered" >      id,</span>
<span class="cstat-no" title="statement not covered" >      type,</span>
<span class="cstat-no" title="statement not covered" >      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed</span>
<span class="cstat-no" title="statement not covered" >      minorCategory: userParams.minorCategory,</span>
<span class="cstat-no" title="statement not covered" >      zLevelId: userParams.zLevelId,</span>
<span class="cstat-no" title="statement not covered" >      isFixedCategory: userParams.isFixedCategory,</span>
<span class="cstat-no" title="statement not covered" >      position,</span>
<span class="cstat-no" title="statement not covered" >      width: userParams.width,</span>
<span class="cstat-no" title="statement not covered" >      height: userParams.height,</span>
<span class="cstat-no" title="statement not covered" >      cornerRadius: userParams.cornerRadius,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return this.createShape(type, params)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a Square element from user-provided parameters.
   *
   * @param userParams - {@link UserSquareInputParams} specifying the square's properties.
   * @returns A Promise resolving to the created {@link ShapeModel} (Square, treated as a Rectangle).
   */
<span class="cstat-no" title="statement not covered" >  public async createSquare(userParams: UserSquareInputParams): Promise&lt;ShapeModel&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const id = this.generateUUID()</span>
<span class="cstat-no" title="statement not covered" >    const position = this.normalizePositionInput(userParams.position)</span>
<span class="cstat-no" title="statement not covered" >    const baseParams = this._prepareBaseParams(userParams, 'Square')</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const params: CreateRectangleParams = {</span>
<span class="cstat-no" title="statement not covered" >      ...baseParams,</span>
<span class="cstat-no" title="statement not covered" >      id,</span>
<span class="cstat-no" title="statement not covered" >      type: CoreElementType.SQUARE,</span>
<span class="cstat-no" title="statement not covered" >      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed</span>
<span class="cstat-no" title="statement not covered" >      minorCategory: userParams.minorCategory,</span>
<span class="cstat-no" title="statement not covered" >      zLevelId: userParams.zLevelId,</span>
<span class="cstat-no" title="statement not covered" >      isFixedCategory: userParams.isFixedCategory,</span>
<span class="cstat-no" title="statement not covered" >      position,</span>
<span class="cstat-no" title="statement not covered" >      width: userParams.size,</span>
<span class="cstat-no" title="statement not covered" >      height: userParams.size,</span>
<span class="cstat-no" title="statement not covered" >      cornerRadius: userParams.cornerRadius,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return this.createShape(CoreElementType.SQUARE, params)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates an Ellipse element from user-provided parameters.
   *
   * @param userParams - {@link UserEllipseInputParams} specifying the ellipse's properties.
   * @returns A Promise resolving to the created {@link ShapeModel} (Ellipse).
   */
<span class="cstat-no" title="statement not covered" >  public async createEllipse(userParams: UserEllipseInputParams): Promise&lt;ShapeModel&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const id = this.generateUUID()</span>
<span class="cstat-no" title="statement not covered" >    const position = this.normalizePositionInput(userParams.position)</span>
<span class="cstat-no" title="statement not covered" >    const baseParams = this._prepareBaseParams(userParams, 'Ellipse')</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const params: CreateEllipseParams = {</span>
<span class="cstat-no" title="statement not covered" >      ...baseParams,</span>
<span class="cstat-no" title="statement not covered" >      id,</span>
<span class="cstat-no" title="statement not covered" >      type: CoreElementType.ELLIPSE,</span>
<span class="cstat-no" title="statement not covered" >      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed</span>
<span class="cstat-no" title="statement not covered" >      minorCategory: userParams.minorCategory,</span>
<span class="cstat-no" title="statement not covered" >      zLevelId: userParams.zLevelId,</span>
<span class="cstat-no" title="statement not covered" >      isFixedCategory: userParams.isFixedCategory,</span>
<span class="cstat-no" title="statement not covered" >      position,</span>
<span class="cstat-no" title="statement not covered" >      radiusX: userParams.radiusX,</span>
<span class="cstat-no" title="statement not covered" >      radiusY: userParams.radiusY,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return this.createShape(CoreElementType.ELLIPSE, params)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a Circle element from user-provided parameters.
   *
   * @param userParams - {@link UserCircleInputParams} specifying the circle's properties.
   * @returns A Promise resolving to the created {@link ShapeModel} (Circle, treated as an Ellipse).
   */
<span class="cstat-no" title="statement not covered" >  public async createCircle(userParams: UserCircleInputParams): Promise&lt;ShapeModel&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const id = this.generateUUID()</span>
<span class="cstat-no" title="statement not covered" >    const position = this.normalizePositionInput(userParams.position)</span>
<span class="cstat-no" title="statement not covered" >    const baseParams = this._prepareBaseParams(userParams, 'Circle')</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const params: CreateCircleParams = {</span>
<span class="cstat-no" title="statement not covered" >      ...baseParams,</span>
<span class="cstat-no" title="statement not covered" >      id,</span>
<span class="cstat-no" title="statement not covered" >      type: CoreElementType.CIRCLE,</span>
<span class="cstat-no" title="statement not covered" >      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed</span>
<span class="cstat-no" title="statement not covered" >      minorCategory: userParams.minorCategory,</span>
<span class="cstat-no" title="statement not covered" >      zLevelId: userParams.zLevelId,</span>
<span class="cstat-no" title="statement not covered" >      isFixedCategory: userParams.isFixedCategory,</span>
<span class="cstat-no" title="statement not covered" >      position,</span>
<span class="cstat-no" title="statement not covered" >      radius: userParams.radius,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return this.createShape(CoreElementType.CIRCLE, params)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a Line element from user-provided parameters.
   *
   * @param userParams - {@link UserLineInputParams} specifying the line's properties.
   * @returns A Promise resolving to the created {@link ShapeModel} (Line).
   *          Note: The return type is `ShapeModel` for consistency, though Line is a Path.
   *          The `createPath` method returns `Element`. This might need alignment.
   */
<span class="cstat-no" title="statement not covered" >  public async createLine(userParams: UserLineInputParams): Promise&lt;ShapeModel&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const id = this.generateUUID()</span>
<span class="cstat-no" title="statement not covered" >    const start = this.normalizePositionInput(userParams.start)</span>
<span class="cstat-no" title="statement not covered" >    const end = this.normalizePositionInput(userParams.end)</span>
<span class="cstat-no" title="statement not covered" >    const baseParams = this._prepareBaseParams(userParams, 'Line')</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const params: CreateLineParams = {</span>
<span class="cstat-no" title="statement not covered" >      ...baseParams,</span>
<span class="cstat-no" title="statement not covered" >      id,</span>
<span class="cstat-no" title="statement not covered" >      type: CoreElementType.LINE,</span>
<span class="cstat-no" title="statement not covered" >      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed</span>
<span class="cstat-no" title="statement not covered" >      minorCategory: userParams.minorCategory,</span>
<span class="cstat-no" title="statement not covered" >      zLevelId: userParams.zLevelId,</span>
<span class="cstat-no" title="statement not covered" >      isFixedCategory: userParams.isFixedCategory,</span>
<span class="cstat-no" title="statement not covered" >      start,</span>
<span class="cstat-no" title="statement not covered" >      end,</span>
<span class="cstat-no" title="statement not covered" >      arrowStart: userParams.arrowStart,</span>
<span class="cstat-no" title="statement not covered" >      arrowEnd: userParams.arrowEnd,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return this.createPath(CoreElementType.LINE, params) as Promise&lt;ShapeModel&gt;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a polyline element from user parameters.
   *
   * @param userParams - User input parameters for polyline creation.
   * @returns A promise resolving to the created polyline model.
   */
<span class="cstat-no" title="statement not covered" >  public async createPolyline(userParams: UserPolylineInputParams): Promise&lt;ShapeModel&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const id = this.generateUUID()</span>
<span class="cstat-no" title="statement not covered" >    const points = userParams.points.map(p =&gt; this.normalizePositionInput(p))</span>
<span class="cstat-no" title="statement not covered" >    const baseParams = this._prepareBaseParams(userParams, 'Polyline')</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const params: CreatePolylineParams = {</span>
<span class="cstat-no" title="statement not covered" >      ...baseParams,</span>
<span class="cstat-no" title="statement not covered" >      id,</span>
<span class="cstat-no" title="statement not covered" >      type: CoreElementType.POLYLINE,</span>
<span class="cstat-no" title="statement not covered" >      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed</span>
<span class="cstat-no" title="statement not covered" >      minorCategory: userParams.minorCategory,</span>
<span class="cstat-no" title="statement not covered" >      zLevelId: userParams.zLevelId,</span>
<span class="cstat-no" title="statement not covered" >      isFixedCategory: userParams.isFixedCategory,</span>
<span class="cstat-no" title="statement not covered" >      points,</span>
<span class="cstat-no" title="statement not covered" >      curved: userParams.curved,</span>
<span class="cstat-no" title="statement not covered" >      tension: userParams.tension,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return this.createPath(CoreElementType.POLYLINE, params) as Promise&lt;ShapeModel&gt;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a regular polygon element from user parameters.
   *
   * @param userParams - User input parameters for regular polygon creation.
   * @returns A promise resolving to the created polygon model.
   */
<span class="cstat-no" title="statement not covered" >  public async createRegularPolygon(userParams: UserRegularPolygonInputParams): Promise&lt;ShapeModel&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const id = this.generateUUID()</span>
<span class="cstat-no" title="statement not covered" >    const center = this.normalizePositionInput(userParams.center)</span>
<span class="cstat-no" title="statement not covered" >    const baseParams = this._prepareBaseParams(userParams, 'Polygon')</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const params: CreatePolygonParams = {</span>
<span class="cstat-no" title="statement not covered" >      ...baseParams,</span>
<span class="cstat-no" title="statement not covered" >      id,</span>
<span class="cstat-no" title="statement not covered" >      type: CoreElementType.POLYGON, // Or more specific if derivable</span>
<span class="cstat-no" title="statement not covered" >      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed</span>
<span class="cstat-no" title="statement not covered" >      minorCategory: userParams.minorCategory,</span>
<span class="cstat-no" title="statement not covered" >      zLevelId: userParams.zLevelId,</span>
<span class="cstat-no" title="statement not covered" >      isFixedCategory: userParams.isFixedCategory,</span>
<span class="cstat-no" title="statement not covered" >      points: [], // Will be calculated by the creator</span>
<span class="cstat-no" title="statement not covered" >      sides: userParams.sides,</span>
<span class="cstat-no" title="statement not covered" >      radius: userParams.radius,</span>
<span class="cstat-no" title="statement not covered" >      center,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return this.createShape(CoreElementType.POLYGON, params)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a custom polygon element from user parameters.
   *
   * @param userParams - User input parameters for custom polygon creation.
   * @returns A promise resolving to the created polygon model.
   */
<span class="cstat-no" title="statement not covered" >  public async createCustomPolygon(userParams: UserCustomPolygonInputParams): Promise&lt;ShapeModel&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const id = this.generateUUID()</span>
<span class="cstat-no" title="statement not covered" >    const points = userParams.points.map(p =&gt; this.normalizePositionInput(p))</span>
<span class="cstat-no" title="statement not covered" >    const baseParams = this._prepareBaseParams(userParams, 'Polygon')</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const params: CreatePolygonParams = {</span>
<span class="cstat-no" title="statement not covered" >      ...baseParams,</span>
<span class="cstat-no" title="statement not covered" >      id,</span>
<span class="cstat-no" title="statement not covered" >      type: CoreElementType.POLYGON,</span>
<span class="cstat-no" title="statement not covered" >      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed</span>
<span class="cstat-no" title="statement not covered" >      minorCategory: userParams.minorCategory,</span>
<span class="cstat-no" title="statement not covered" >      zLevelId: userParams.zLevelId,</span>
<span class="cstat-no" title="statement not covered" >      isFixedCategory: userParams.isFixedCategory,</span>
<span class="cstat-no" title="statement not covered" >      points,</span>
<span class="cstat-no" title="statement not covered" >      isRegular: false,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return this.createShape(CoreElementType.POLYGON, params)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates an arc element from user parameters.
   *
   * @param userParams - User input parameters for arc creation.
   * @returns A promise resolving to the created arc element.
   */
<span class="cstat-no" title="statement not covered" >  public async createArc(userParams: UserArcInputParams): Promise&lt;Element&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const id = this.generateUUID()</span>
<span class="cstat-no" title="statement not covered" >    const position = this.normalizePositionInput(userParams.position)</span>
<span class="cstat-no" title="statement not covered" >    const baseParams = this._prepareBaseParams(userParams, 'Arc')</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const params: CreateArcParams = {</span>
<span class="cstat-no" title="statement not covered" >      ...baseParams,</span>
<span class="cstat-no" title="statement not covered" >      id,</span>
<span class="cstat-no" title="statement not covered" >      type: CoreElementType.ARC,</span>
<span class="cstat-no" title="statement not covered" >      majorCategory: userParams.majorCategory, // Ensure majorCategory is passed</span>
<span class="cstat-no" title="statement not covered" >      minorCategory: userParams.minorCategory,</span>
<span class="cstat-no" title="statement not covered" >      zLevelId: userParams.zLevelId,</span>
<span class="cstat-no" title="statement not covered" >      isFixedCategory: userParams.isFixedCategory,</span>
<span class="cstat-no" title="statement not covered" >      position,</span>
<span class="cstat-no" title="statement not covered" >      radius: userParams.radius,</span>
<span class="cstat-no" title="statement not covered" >      startAngle: userParams.startAngle,</span>
<span class="cstat-no" title="statement not covered" >      endAngle: userParams.endAngle,</span>
<span class="cstat-no" title="statement not covered" >      closed: userParams.closed,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return this.createPath(CoreElementType.ARC, params)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  /**
   * Creates a default shape of the specified type at the given position.
   *
   * @param type - The type of shape to create.
   * @param id - Unique identifier for the shape.
   * @param positionInput - Position for the shape in various formats.
   * @param majorCategory - The major category of the shape.
   * @param minorCategory - The minor category of the shape.
   * @param zLevelId - The zLevelId of the shape.
   * @param isFixedCategory - Whether the shape is fixed in its category.
   * @returns A promise resolving to the created shape model.
   */
<span class="cstat-no" title="statement not covered" >  public async createDefaultShape(</span>
<span class="cstat-no" title="statement not covered" >    type: CoreElementType | string,</span>
<span class="cstat-no" title="statement not covered" >    id: string, // This ID is used for naming and as the element's ID directly</span>
<span class="cstat-no" title="statement not covered" >    positionInput: PointData | [number, number] | [number, number, number?],</span>
    // Added layer parameters
<span class="cstat-no" title="statement not covered" >    majorCategory: MajorCategory,</span>
<span class="cstat-no" title="statement not covered" >    minorCategory?: MinorCategory,</span>
<span class="cstat-no" title="statement not covered" >    zLevelId?: string,</span>
<span class="cstat-no" title="statement not covered" >    isFixedCategory?: boolean,</span>
<span class="cstat-no" title="statement not covered" >  ): Promise&lt;ShapeModel&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const position = this.normalizePositionInput(positionInput)</span>
<span class="cstat-no" title="statement not covered" >    const defaultName = `Default ${type.toString()} ${id}`</span>
&nbsp;
    // Construct CommonUserInputParams for _prepareBaseParams
<span class="cstat-no" title="statement not covered" >    const userInputParams: CommonUserInputParams = {</span>
<span class="cstat-no" title="statement not covered" >      majorCategory,</span>
<span class="cstat-no" title="statement not covered" >      minorCategory,</span>
<span class="cstat-no" title="statement not covered" >      zLevelId,</span>
<span class="cstat-no" title="statement not covered" >      isFixedCategory,</span>
<span class="cstat-no" title="statement not covered" >      metadata: { name: defaultName },</span>
      // Provide some default style properties
<span class="cstat-no" title="statement not covered" >      fill: '#CCCCCC',</span>
<span class="cstat-no" title="statement not covered" >      stroke: '#000000',</span>
<span class="cstat-no" title="statement not covered" >      strokeWidth: 1,</span>
<span class="cstat-no" title="statement not covered" >      opacity: 1,</span>
      // position is not part of CommonUserInputParams, it's handled by specific creators/params
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const preparedBase = this._prepareBaseParams(userInputParams, `Default ${type.toString()}`)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    let params: ShapeCreationParamsUnion</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const commonProps = { // Properties that are part of BaseElementCreationParams</span>
<span class="cstat-no" title="statement not covered" >      id, // Use the provided id directly</span>
<span class="cstat-no" title="statement not covered" >      majorCategory: preparedBase.majorCategory, // Take from preparedBase</span>
<span class="cstat-no" title="statement not covered" >      minorCategory: preparedBase.minorCategory,</span>
<span class="cstat-no" title="statement not covered" >      zLevelId: preparedBase.zLevelId,</span>
<span class="cstat-no" title="statement not covered" >      isFixedCategory: preparedBase.isFixedCategory,</span>
<span class="cstat-no" title="statement not covered" >      metadata: preparedBase.metadata,</span>
<span class="cstat-no" title="statement not covered" >      visible: preparedBase.visible,</span>
<span class="cstat-no" title="statement not covered" >      locked: preparedBase.locked,</span>
<span class="cstat-no" title="statement not covered" >      rotation: preparedBase.rotation,</span>
<span class="cstat-no" title="statement not covered" >      selectable: preparedBase.selectable,</span>
<span class="cstat-no" title="statement not covered" >      draggable: preparedBase.draggable,</span>
<span class="cstat-no" title="statement not covered" >      showHandles: preparedBase.showHandles,</span>
      // Layer is deprecated but still used in some places
      // We're using a computed property name to avoid direct property assignment
      // which would trigger the TypeScript deprecation warning
<span class="cstat-no" title="statement not covered" >      ...(preparedBase.layer !== undefined ? { layer: preparedBase.layer } : {}),</span>
<span class="cstat-no" title="statement not covered" >      zIndex: preparedBase.zIndex,</span>
<span class="cstat-no" title="statement not covered" >      properties: preparedBase.properties,</span>
<span class="cstat-no" title="statement not covered" >      fill: preparedBase.fill,</span>
<span class="cstat-no" title="statement not covered" >      stroke: preparedBase.stroke,</span>
<span class="cstat-no" title="statement not covered" >      strokeWidth: preparedBase.strokeWidth,</span>
<span class="cstat-no" title="statement not covered" >      opacity: preparedBase.opacity,</span>
<span class="cstat-no" title="statement not covered" >      strokeDasharray: preparedBase.strokeDasharray,</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    switch (type) {</span>
<span class="cstat-no" title="statement not covered" >      case CoreElementType.RECTANGLE:</span>
<span class="cstat-no" title="statement not covered" >        params = { ...commonProps, type: CoreElementType.RECTANGLE, position, width: 100, height: 50 }</span>
<span class="cstat-no" title="statement not covered" >        break</span>
<span class="cstat-no" title="statement not covered" >      case CoreElementType.SQUARE:</span>
<span class="cstat-no" title="statement not covered" >        params = { ...commonProps, type: CoreElementType.SQUARE, position, width: 50, height: 50 }</span>
<span class="cstat-no" title="statement not covered" >        break</span>
<span class="cstat-no" title="statement not covered" >      case CoreElementType.CIRCLE:</span>
<span class="cstat-no" title="statement not covered" >        params = { ...commonProps, type: CoreElementType.CIRCLE, position, radius: 30 }</span>
<span class="cstat-no" title="statement not covered" >        break</span>
<span class="cstat-no" title="statement not covered" >      case CoreElementType.ELLIPSE:</span>
<span class="cstat-no" title="statement not covered" >        params = { ...commonProps, type: CoreElementType.ELLIPSE, position, radiusX: 50, radiusY: 30 }</span>
<span class="cstat-no" title="statement not covered" >        break</span>
<span class="cstat-no" title="statement not covered" >      case CoreElementType.LINE:</span>
<span class="cstat-no" title="statement not covered" >        params = {</span>
<span class="cstat-no" title="statement not covered" >          ...commonProps,</span>
<span class="cstat-no" title="statement not covered" >          type: CoreElementType.LINE,</span>
<span class="cstat-no" title="statement not covered" >          start: position,</span>
<span class="cstat-no" title="statement not covered" >          end: { x: position.x + 100, y: position.y + 50, z: position.z },</span>
<span class="cstat-no" title="statement not covered" >        } as CreateLineParams</span>
<span class="cstat-no" title="statement not covered" >        break</span>
<span class="cstat-no" title="statement not covered" >      case CoreElementType.POLYLINE:</span>
<span class="cstat-no" title="statement not covered" >        params = {</span>
<span class="cstat-no" title="statement not covered" >          ...commonProps,</span>
<span class="cstat-no" title="statement not covered" >          type: CoreElementType.POLYLINE,</span>
<span class="cstat-no" title="statement not covered" >          points: [</span>
<span class="cstat-no" title="statement not covered" >            position,</span>
<span class="cstat-no" title="statement not covered" >            { x: position.x + 50, y: position.y + 20, z: position.z },</span>
<span class="cstat-no" title="statement not covered" >            { x: position.x + 100, y: position.y, z: position.z },</span>
<span class="cstat-no" title="statement not covered" >          ],</span>
<span class="cstat-no" title="statement not covered" >        } as CreatePolylineParams</span>
<span class="cstat-no" title="statement not covered" >        break</span>
<span class="cstat-no" title="statement not covered" >      case CoreElementType.ARC:</span>
<span class="cstat-no" title="statement not covered" >        params = {</span>
<span class="cstat-no" title="statement not covered" >          ...commonProps,</span>
<span class="cstat-no" title="statement not covered" >          type: CoreElementType.ARC,</span>
<span class="cstat-no" title="statement not covered" >          position,</span>
<span class="cstat-no" title="statement not covered" >          radius: 50,</span>
<span class="cstat-no" title="statement not covered" >          startAngle: 0,</span>
<span class="cstat-no" title="statement not covered" >          endAngle: 90,</span>
<span class="cstat-no" title="statement not covered" >          closed: false,</span>
<span class="cstat-no" title="statement not covered" >        } as CreateArcParams</span>
<span class="cstat-no" title="statement not covered" >        break</span>
<span class="cstat-no" title="statement not covered" >      default:</span>
<span class="cstat-no" title="statement not covered" >        params = {</span>
<span class="cstat-no" title="statement not covered" >          ...commonProps,</span>
<span class="cstat-no" title="statement not covered" >          type: CoreElementType.RECTANGLE,</span>
<span class="cstat-no" title="statement not covered" >          position,</span>
<span class="cstat-no" title="statement not covered" >          width: 50,</span>
<span class="cstat-no" title="statement not covered" >          height: 50,</span>
<span class="cstat-no" title="statement not covered" >        } as CreateRectangleParams</span>
<span class="cstat-no" title="statement not covered" >        console.warn(`ElementFactory: Creating generic default (rectangle) for type ${type}`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // 根据元素类型选择正确的创建方法
<span class="cstat-no" title="statement not covered" >    if (type === CoreElementType.LINE || type === CoreElementType.POLYLINE || type === CoreElementType.ARC) {</span>
      // 确保 params 是路径类型的参数
<span class="cstat-no" title="statement not covered" >      const pathParams = params as PathCreationOptionsUnion</span>
<span class="cstat-no" title="statement not covered" >      return this.createPath(type, pathParams) as Promise&lt;ShapeModel&gt;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    else {</span>
<span class="cstat-no" title="statement not covered" >      return this.createShape(type, params)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:58:01.486Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    