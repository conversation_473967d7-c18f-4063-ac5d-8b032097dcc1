import type {
  ComputeOptions,
  ComputeResult,
  ComputeShape,
  TransformOptions,
} from '@/types/core/compute/compute-interface'
import type Point from '@/types/core/element/geometry/point'
import { describe, expect, it } from 'vitest'

describe('compute Interface Types', () => {
  it('should define TransformOptions interface correctly', () => {
    // Create valid TransformOptions objects
    const translateOptions: TransformOptions = {
      type: 'translate',
      x: 10,
      y: 20,
    }

    const rotateOptions: TransformOptions = {
      type: 'rotate',
      angle: 45,
      originX: 100,
      originY: 100,
    }

    const scaleOptions: TransformOptions = {
      type: 'scale',
      scaleX: 1.5,
      scaleY: 2.0,
      originX: 0,
      originY: 0,
    }

    const matrixOptions: TransformOptions = {
      type: 'matrix',
      matrix: [1, 0, 0, 1, 10, 20],
      originX: 0,
      originY: 0,
    }

    // Verify the objects are valid
    expect(translateOptions.type).toBe('translate')
    expect(translateOptions.x).toBe(10)
    expect(translateOptions.y).toBe(20)

    expect(rotateOptions.type).toBe('rotate')
    expect(rotateOptions.angle).toBe(45)
    expect(rotateOptions.originX).toBe(100)
    expect(rotateOptions.originY).toBe(100)

    expect(scaleOptions.type).toBe('scale')
    expect(scaleOptions.scaleX).toBe(1.5)
    expect(scaleOptions.scaleY).toBe(2.0)
    expect(scaleOptions.originX).toBe(0)
    expect(scaleOptions.originY).toBe(0)

    expect(matrixOptions.type).toBe('matrix')
    expect(matrixOptions.matrix).toEqual([1, 0, 0, 1, 10, 20])
    expect(matrixOptions.originX).toBe(0)
    expect(matrixOptions.originY).toBe(0)
  })

  it('should define ComputeOptions interface correctly', () => {
    // Create a valid ComputeOptions object
    const options: ComputeOptions = {
      precision: 0.001,
      algorithm: 'fast',
      x: 100,
      y: 200,
      targetShapeId: 'shape-123',
      unitCost: 50,
      costOptions: { type: 'area', taxRate: 0.1 },
      materialType: 'wood',
      materialOptions: { wastage: 0.05 },
      spaceType: 'living',
      spacePlanningOptions: { minPathwayWidth: 0.9 },
    }

    // Verify the object is valid
    expect(options.precision).toBe(0.001)
    expect(options.algorithm).toBe('fast')
    expect(options.x).toBe(100)
    expect(options.y).toBe(200)
    expect(options.targetShapeId).toBe('shape-123')
    expect(options.unitCost).toBe(50)
    expect(options.costOptions).toEqual({ type: 'area', taxRate: 0.1 })
    expect(options.materialType).toBe('wood')
    expect(options.materialOptions).toEqual({ wastage: 0.05 })
    expect(options.spaceType).toBe('living')
    expect(options.spacePlanningOptions).toEqual({ minPathwayWidth: 0.9 })
  })

  it('should define ComputeResult interface correctly', () => {
    // Create valid ComputeResult objects
    const areaResult: ComputeResult<number> = {
      operation: 'area',
      result: 150.5,
      metadata: {
        executionTime: 5,
        precision: 0.01,
        shapeId: 'shape-123',
      },
    }

    const distanceResult: ComputeResult<number> = {
      operation: 'distance',
      result: 42.5,
      metadata: {
        executionTime: 3,
        point: { x: 10, y: 20 },
        shapeIds: ['shape-1', 'shape-2'],
      },
    }

    const transformResult: ComputeResult<any> = {
      operation: 'transform',
      result: { id: 'shape-123', type: 'rectangle', x: 10, y: 20 },
      metadata: {
        executionTime: 8,
        transformType: 'rotate',
        algorithmUsed: 'matrix',
      },
    }

    // Verify the objects are valid
    expect(areaResult.operation).toBe('area')
    expect(areaResult.result).toBe(150.5)
    expect(areaResult.metadata?.executionTime).toBe(5)
    expect(areaResult.metadata?.precision).toBe(0.01)
    expect(areaResult.metadata?.shapeId).toBe('shape-123')

    expect(distanceResult.operation).toBe('distance')
    expect(distanceResult.result).toBe(42.5)
    expect(distanceResult.metadata?.executionTime).toBe(3)
    expect(distanceResult.metadata?.point).toEqual({ x: 10, y: 20 })
    expect(distanceResult.metadata?.shapeIds).toEqual(['shape-1', 'shape-2'])

    expect(transformResult.operation).toBe('transform')
    expect(transformResult.result).toEqual({ id: 'shape-123', type: 'rectangle', x: 10, y: 20 })
    expect(transformResult.metadata?.executionTime).toBe(8)
    expect(transformResult.metadata?.transformType).toBe('rotate')
    expect(transformResult.metadata?.algorithmUsed).toBe('matrix')
  })

  it('should define ComputeShape interface correctly', () => {
    // Create a mock implementation of ComputeShape
    class MockShape implements ComputeShape {
      getSubType(): string {
        return 'mockShape'
      }

      getPosition(): Point {
        return { x: 10, y: 20 } as Point
      }
    }

    const shape = new MockShape()

    // Verify the implementation is valid
    expect(shape.getSubType()).toBe('mockShape')
    expect(shape.getPosition()).toEqual({ x: 10, y: 20 })
  })
})
