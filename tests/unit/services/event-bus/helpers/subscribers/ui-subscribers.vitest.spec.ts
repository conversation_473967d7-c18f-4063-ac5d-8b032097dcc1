import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  subscribeToNotificationEvents,
  subscribeToToastEvents,
  subscribeToToolChangedEvents,
  subscribeToViewPannedEvents,
  subscribeToViewZoomEvents,
} from '@/services/event-bus/helpers/subscribers/ui-subscribers'
import * as utils from '@/services/event-bus/helpers/subscribers/utils'
import { AppEventType } from '@/types/services/events'

describe('uI Subscribers', () => {
  let mockEventBus: any
  let mockHandler: any
  let mockOptions: any
  let typedSubscribeSpy: any

  beforeEach(() => {
    mockEventBus = {
      subscribe: vi.fn().mockReturnValue(() => {}),
    }
    mockHandler = vi.fn()
    mockOptions = { once: true }

    // Spy on the typedSubscribe function
    typedSubscribeSpy = vi.spyOn(utils, 'typedSubscribe').mockReturnValue(() => {})
  })

  describe('tool Events', () => {
    it('should subscribe to tool changed events correctly', () => {
      const unsubscribe = subscribeToToolChangedEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.TOOL_CHANGED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('view Events', () => {
    it('should subscribe to view zoom in events correctly', () => {
      const unsubscribe = subscribeToViewZoomEvents(
        mockEventBus,
        mockHandler,
        'in',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.VIEW_ZOOM_IN,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should subscribe to view zoom out events correctly', () => {
      const unsubscribe = subscribeToViewZoomEvents(
        mockEventBus,
        mockHandler,
        'out',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.VIEW_ZOOM_OUT,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should subscribe to view zoom reset events correctly', () => {
      const unsubscribe = subscribeToViewZoomEvents(
        mockEventBus,
        mockHandler,
        'reset',
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.VIEW_ZOOM_RESET,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should subscribe to view panned events correctly', () => {
      const unsubscribe = subscribeToViewPannedEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.VIEW_PANNED,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('notification Events', () => {
    it('should subscribe to toast events correctly', () => {
      const unsubscribe = subscribeToToastEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.TOAST_SHOW,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })

    it('should subscribe to notification events correctly', () => {
      const unsubscribe = subscribeToNotificationEvents(
        mockEventBus,
        mockHandler,
        mockOptions,
      )

      expect(typedSubscribeSpy).toHaveBeenCalledWith(
        mockEventBus,
        AppEventType.NOTIFICATION_ADD,
        mockHandler,
        mockOptions,
      )
      expect(typeof unsubscribe).toBe('function')
    })
  })
})
