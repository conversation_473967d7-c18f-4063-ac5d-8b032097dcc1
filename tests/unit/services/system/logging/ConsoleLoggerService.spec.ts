import type { LoggerConfig } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { ConsoleLoggerService } from '@/services/system/logging/consoleLoggerService'
import { LogLevel } from '@/types/services/logging'

describe('consoleLoggerService', () => {
  let logger: ConsoleLoggerService
  let consoleSpy: {
    info: ReturnType<typeof vi.spyOn>
    warn: ReturnType<typeof vi.spyOn>
    error: ReturnType<typeof vi.spyOn>
    debug: ReturnType<typeof vi.spyOn>
  }

  beforeEach(() => {
    // Mock console methods
    consoleSpy = {
      info: vi.spyOn(console, 'info').mockImplementation(() => {}),
      warn: vi.spyOn(console, 'warn').mockImplementation(() => {}),
      error: vi.spyOn(console, 'error').mockImplementation(() => {}),
      debug: vi.spyOn(console, 'debug').mockImplementation(() => {}),
    }

    logger = new ConsoleLoggerService()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('constructor', () => {
    it('should initialize with default config', () => {
      const config = logger.getConfig()
      expect(config.minLevel).toBe(LogLevel.Debug)
      expect(config.includeTimestamp).toBe(true)
      expect(config.enableConsole).toBe(true)
      expect(config.includeContext).toBe(true)
    })

    it('should log initialization message', () => {
      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining('ConsoleLoggerService initialized.'),
      )
    })
  })

  describe('info', () => {
    it('should log info messages', () => {
      logger.info('Test info message')

      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining('[INFO] Test info message'),
      )
    })

    it('should include timestamp in message', () => {
      logger.info('Test message')

      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringMatching(/\[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z\] \[INFO\] Test message/),
      )
    })

    it('should log with additional parameters', () => {
      const obj = { key: 'value' }
      logger.info('Test message', obj, 'extra param')

      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining('[INFO] Test message'),
        obj,
        'extra param',
      )
    })
  })

  describe('warn', () => {
    it('should log warning messages', () => {
      logger.warn('Test warning')

      expect(consoleSpy.warn).toHaveBeenCalledWith(
        expect.stringContaining('[WARN] Test warning'),
      )
    })
  })

  describe('error', () => {
    it('should log error messages', () => {
      logger.error('Test error')

      expect(consoleSpy.error).toHaveBeenCalledWith(
        expect.stringContaining('[ERROR] Test error'),
      )
    })
  })

  describe('debug', () => {
    it('should not log debug messages by default (commented out)', () => {
      logger.debug('Test debug')

      // Debug is commented out in the implementation
      expect(consoleSpy.debug).not.toHaveBeenCalled()
    })
  })

  describe('setContext', () => {
    it('should set logging context', () => {
      const context = { userId: '123', action: 'test' }
      logger.setContext(context)

      // Context should be included in subsequent logs
      logger.info('Test with context')

      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining('[INFO] Test with context'),
        context,
      )
    })

    it('should merge context with existing context', () => {
      logger.setContext({ key1: 'value1' })
      logger.setContext({ key2: 'value2' })

      logger.info('Test message')

      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining('[INFO] Test message'),
        { key1: 'value1', key2: 'value2' },
      )
    })

    it('should overwrite existing context keys', () => {
      logger.setContext({ key: 'original' })
      logger.setContext({ key: 'updated' })

      logger.info('Test message')

      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining('[INFO] Test message'),
        { key: 'updated' },
      )
    })
  })

  describe('getConfig', () => {
    it('should return a copy of the config', () => {
      const config1 = logger.getConfig()
      const config2 = logger.getConfig()

      expect(config1).toEqual(config2)
      expect(config1).not.toBe(config2) // Different objects
    })

    it('should not allow direct modification of internal config', () => {
      const config = logger.getConfig()
      config.minLevel = LogLevel.Error

      const newConfig = logger.getConfig()
      expect(newConfig.minLevel).toBe(LogLevel.Debug) // Should remain unchanged
    })
  })

  describe('setConfig', () => {
    it('should update configuration', () => {
      const newConfig: Partial<LoggerConfig> = {
        minLevel: LogLevel.Error,
        includeTimestamp: false,
      }

      logger.setConfig(newConfig)

      const config = logger.getConfig()
      expect(config.minLevel).toBe(LogLevel.Error)
      expect(config.includeTimestamp).toBe(false)
      expect(config.enableConsole).toBe(true) // Should remain unchanged
    })

    it('should merge with existing config', () => {
      logger.setConfig({ minLevel: LogLevel.Warn })

      const config = logger.getConfig()
      expect(config.minLevel).toBe(LogLevel.Warn)
      expect(config.includeTimestamp).toBe(true) // Should remain unchanged
    })
  })

  describe('log level filtering', () => {
    beforeEach(() => {
      // Clear the initialization log call
      consoleSpy.info.mockClear()
    })

    it('should respect minimum log level', () => {
      logger.setConfig({ minLevel: LogLevel.Warn })

      logger.debug('Debug message')
      logger.info('Info message')
      logger.warn('Warn message')
      logger.error('Error message')

      expect(consoleSpy.debug).not.toHaveBeenCalled()
      expect(consoleSpy.info).not.toHaveBeenCalled()
      expect(consoleSpy.warn).toHaveBeenCalledTimes(1)
      expect(consoleSpy.error).toHaveBeenCalledTimes(1)
    })

    it('should log all levels when set to Debug', () => {
      logger.setConfig({ minLevel: LogLevel.Debug })

      logger.info('Info message')
      logger.warn('Warn message')
      logger.error('Error message')

      expect(consoleSpy.info).toHaveBeenCalledTimes(1)
      expect(consoleSpy.warn).toHaveBeenCalledTimes(1)
      expect(consoleSpy.error).toHaveBeenCalledTimes(1)
    })

    it('should only log errors when set to Error level', () => {
      logger.setConfig({ minLevel: LogLevel.Error })

      logger.debug('Debug message')
      logger.info('Info message')
      logger.warn('Warn message')
      logger.error('Error message')

      expect(consoleSpy.debug).not.toHaveBeenCalled()
      expect(consoleSpy.info).not.toHaveBeenCalled()
      expect(consoleSpy.warn).not.toHaveBeenCalled()
      expect(consoleSpy.error).toHaveBeenCalledTimes(1)
    })
  })

  describe('timestamp formatting', () => {
    it('should include timestamp when enabled', () => {
      logger.setConfig({ includeTimestamp: true })
      logger.info('Test message')

      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringMatching(/^\[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z\]/),
      )
    })

    it('should exclude timestamp when disabled', () => {
      logger.setConfig({ includeTimestamp: false })
      logger.info('Test message')

      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.not.stringMatching(/^\[\d{4}-\d{2}-\d{2}T/),
      )
      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringContaining('[INFO] Test message'),
      )
    })
  })

  describe('static create method', () => {
    it('should create a new instance', () => {
      const newLogger = ConsoleLoggerService.create()
      expect(newLogger).toBeInstanceOf(ConsoleLoggerService)
      expect(newLogger).not.toBe(logger)
    })
  })

  describe('error handling', () => {
    it('should handle errors in logging gracefully', () => {
      // Mock console.info to throw an error
      consoleSpy.info.mockImplementation(() => {
        throw new Error('Console error')
      })

      // Should not throw
      expect(() => logger.info('Test message')).not.toThrow()
    })

    it('should handle errors in setContext gracefully', () => {
      // This should not throw even with invalid context
      expect(() => logger.setContext(null as any)).not.toThrow()
    })

    it('should handle errors in setConfig gracefully', () => {
      // This should not throw even with invalid config
      expect(() => logger.setConfig(null as any)).not.toThrow()
    })
  })
})
