/**
 * UI Event Subscribers Module
 *
 * This module provides type-safe helper functions for subscribing to UI-related events
 * on the application's event bus. It covers tools, views, modals, notifications, and more.
 *
 * All functions follow dependency injection principles by accepting an EventBus
 * instance as their first parameter.
 *
 * @module event-bus/helpers/subscribers/ui-subscribers
 */

import type { EventBus, EventSubscriptionOptions } from '@/types/services/events'
import type {
  NotificationAddEvent,
  ToastShowEvent,
  ToolChangeEvent,
  ViewZoomEvent,
} from '@/types/services/events/uiEvents' // Corrected to camelCase based on user feedback
import { AppEventType } from '@/types/services/events'
import { typedSubscribe } from './utils'

/**
 * Subscribe to tool changed events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToToolChangedEvents(
  eventBus: EventBus,
  handler: (event: ToolChangeEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<ToolChangeEvent>(
    eventBus,
    AppEventType.ToolChanged,
    handler,
    options,
  )
}

/**
 * Subscribe to view zoom events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param action - The zoom action (in, out, reset)
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToViewZoomEvents(
  eventBus: EventBus,
  handler: (event: ViewZoomEvent) => void,
  action: 'in' | 'out' | 'reset',
  options?: EventSubscriptionOptions,
): () => void {
  const eventType = action === 'in'
    ? AppEventType.ViewZoomIn
    : action === 'out'
      ? AppEventType.ViewZoomOut
      : AppEventType.ViewZoomReset

  return typedSubscribe<ViewZoomEvent>(
    eventBus,
    eventType,
    handler,
    options,
  )
}

/**
 * Subscribe to view panned events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToViewPannedEvents(
  eventBus: EventBus,
  handler: (event: ViewZoomEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<ViewZoomEvent>(
    eventBus,
    AppEventType.ViewPanned,
    handler,
    options,
  )
}

/**
 * Subscribe to toast notification events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToToastEvents(
  eventBus: EventBus,
  handler: (event: ToastShowEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<ToastShowEvent>(
    eventBus,
    AppEventType.ToastShow,
    handler,
    options,
  )
}

/**
 * Subscribe to notification events
 *
 * @param eventBus - The EventBus instance to use for subscribing
 * @param handler - The event handler function
 * @param options - Subscription options
 * @returns An unsubscribe function
 */
export function subscribeToNotificationEvents(
  eventBus: EventBus,
  handler: (event: NotificationAddEvent) => void,
  options?: EventSubscriptionOptions,
): () => void {
  return typedSubscribe<NotificationAddEvent>(
    eventBus,
    AppEventType.NotificationAdd,
    handler,
    options,
  )
}
