/**
 * Error Service Types
 *
 * This file defines types for the error handling service.
 *
 * @module types/services/core
 */

import type { ErrorSeverity } from '../errors'

/**
 * Error context information
 */
export interface ErrorContext {
  /** Component or module where the error occurred */
  component?: string // Changed from source for consistency
  /** Specific operation or function name where the error occurred */
  operation?: string
  /** Identifier of the target entity related to the error, if any (e.g., element ID) */
  target?: string
  /** Additional metadata about the error context */
  metadata?: Record<string, unknown> // Using unknown for type safety
  /** Indicates if the error is considered recoverable */
  recoverable?: boolean
  /** Timestamp when the error context was created or the error occurred */
  timestamp?: number // Added for completeness
  /** Stack trace information */
  stack?: string
}

// ErrorSeverity enum is now imported from '../errors'

/**
 * Error details interface
 */
export interface ErrorDetails {
  /** Error message */
  message: string
  /** Error code for identification */
  code?: string
  /** Severity level */
  severity?: ErrorSeverity
  /** Context information */
  context?: ErrorContext
  /** Original error object */
  originalError?: Error
  /** Timestamp when the error occurred */
  timestamp?: number
}

/**
 * Error service interface
 */
export interface ErrorService {
  /**
   * Creates a new error with the given details
   */
  createError: (details: ErrorDetails) => Error

  /**
   * Logs an error
   */
  logError: (error: Error | ErrorDetails) => void

  /**
   * Reports an error
   */
  reportError: (error: Error | ErrorDetails) => void

  /**
   * Handles an error
   */
  handleError: (error: Error | ErrorDetails) => void
}
