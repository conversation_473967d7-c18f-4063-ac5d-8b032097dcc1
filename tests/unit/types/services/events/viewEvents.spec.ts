import type {
  ViewPanEvent,
  ViewTransform,
  ViewZoomedEvent,
} from '@/types/services/events/viewEvents'
import { describe, expect, it } from 'vitest'
import { AppEventType } from '@/types/services/events/eventTypes'

describe('view Events', () => {
  describe('view Event Types', () => {
    it('should define all expected event types in AppEventType', () => {
      expect(AppEventType.VIEW_PANNED).toBeDefined()
      expect(AppEventType.VIEW_ZOOMED).toBeDefined()
    })

    it('should have the correct string values', () => {
      expect(AppEventType.VIEW_PANNED).toBe('view.panned')
      expect(AppEventType.VIEW_ZOOMED).toBe('view.zoomed')
    })
  })

  describe('viewPanEvent', () => {
    it('should allow creating a view pan event', () => {
      const event: ViewPanEvent = {
        type: AppEventType.VIEW_PANNED,
        payload: {
          x: 100,
          y: 200,
        },
      }

      expect(event.type).toBe(AppEventType.VIEW_PANNED)
      expect(event.payload.x).toBe(100)
      expect(event.payload.y).toBe(200)
    })
  })

  describe('viewZoomedEvent', () => {
    it('should allow creating a view zoomed event', () => {
      const event: ViewZoomedEvent = {
        type: AppEventType.VIEW_ZOOMED,
        payload: {
          scale: 1.5,
        },
      }

      expect(event.type).toBe(AppEventType.VIEW_ZOOMED)
      expect(event.payload.scale).toBe(1.5)
    })
  })

  describe('viewTransform', () => {
    it('should allow creating a view transform object', () => {
      const transform: ViewTransform = {
        x: 100,
        y: 200,
        k: 1.5,
      }

      expect(transform.x).toBe(100)
      expect(transform.y).toBe(200)
      expect(transform.k).toBe(1.5)
    })
  })
})
