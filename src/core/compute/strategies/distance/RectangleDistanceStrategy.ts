/**
 * Distance Calculation Strategy for Rectangle/Square Elements via Bounding Boxes
 *
 * @remarks
 * This strategy implements the {@link DistanceCalculatorStrategy} for calculating
 * the distance between a Rectangle or Square element ({@link CoreElementType.RECTANGLE},
 * {@link CoreElementType.SQUARE}) and another {@link ShapeElement}.
 *
 * The core approach of this strategy is to:
 * 1. Obtain the axis-aligned bounding box for both `elementA` (the rectangle/square)
 *    and `elementB` (the other shape element) using a private helper method
 *    `getBoundingBoxClass`. This helper correctly interprets the `position` and
 *    dimensions of various supported shapes (Rectangle, Square, Circle, Ellipse)
 *    to construct their {@link BoundingBoxClass} instances.
 * 2. Delegate the actual distance calculation between these two bounding boxes to the
 *    `calculateBoundingBoxDistance` utility function from
 *    `../../../../lib/utils/geometry/distance`.
 *
 * This strategy effectively simplifies distance calculation to a bounding box comparison,
 * which might be an approximation for non-rectangular shapes but is computationally efficient.
 *
 * @module core/compute/strategies/distance/RectangleDistanceStrategy
 * @see {@link Shape.Rectangle}
 * @see {@link Shape.Square}
 * @see {@link DistanceCalculatorStrategy}
 * @see {@link BoundingBoxClass}
 * @see {@link calculateBoundingBoxDistance}
 */
import type { DistanceCalculatorStrategy } from '../../../../types/core/compute'
import type {
  Shape,
  ShapeElement,
} from '../../../../types/core/elementDefinitions'
import { BoundingBoxClass } from '../../../../lib/utils/geometry/BoundingBoxClass'
import { calculateBoundingBoxDistance } from '../../../../lib/utils/geometry/distance' // Corrected: Import directly from distance.ts
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'
// PointInterface or PointData is not directly used as type hint here, element properties are used.

/**
 * Implements the {@link DistanceCalculatorStrategy} for cases where the primary
 * element (`elementA`) is a Rectangle or Square. The distance to `elementB`
 * is determined by calculating the distance between their respective bounding boxes.
 */
export class RectangleDistanceStrategy implements DistanceCalculatorStrategy {
  /**
   * Generates a {@link BoundingBoxClass} instance for a given {@link ShapeElement}.
   *
   * @remarks
   * This helper method correctly interprets the `position` property based on the element type
   * (e.g., top-left for Rectangles, center for Circles/Ellipses) to construct an
   * axis-aligned bounding box. It handles `RECTANGLE`, `SQUARE`, `CIRCLE`, and `ELLIPSE` types.
   *
   * @param element - The shape element for which to create a bounding box.
   * @returns A `BoundingBoxClass` instance.
   * @throws {@link CoreError} if the element's position or relevant dimensions are invalid,
   *         or if the element type is not supported by this helper.
   * @private
   */
  private getBoundingBoxClass(element: ShapeElement): BoundingBoxClass {
    const elPos = element.position
    if (elPos === null || elPos === undefined || typeof elPos.x !== 'number' || typeof elPos.y !== 'number' || !Number.isFinite(elPos.x) || !Number.isFinite(elPos.y)) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Element (ID: ${element.id}, Type: ${element.type}) has invalid position for BBox creation.`,
        undefined,
        { component: 'RectangleDistanceStrategy', operation: 'getBoundingBoxClass', target: element.id, metadata: { position: elPos } },
      )
    }

    // eslint-disable-next-line ts/switch-exhaustiveness-check
    switch (element.type as CoreElementType) {
      case CoreElementType.RECTANGLE:
      case CoreElementType.SQUARE: {
        const rect = element as unknown as Shape.Rectangle // Added unknown
        const width = typeof rect.properties?.width === 'number' ? rect.properties.width : 0
        const height = typeof rect.properties?.height === 'number' ? rect.properties.height : 0

        if (typeof width !== 'number' || typeof height !== 'number'
          || !Number.isFinite(width) || !Number.isFinite(height)
          || width < 0 || height < 0) {
          throw new CoreError(
            ErrorType.InvalidParameter,
            `Rectangle/Square (ID: ${element.id}) has invalid dimensions. Width: ${width}, Height: ${height}`,
            undefined,
            { component: 'RectangleDistanceStrategy', operation: 'getBoundingBoxClass', target: element.id, metadata: { width, height } },
          )
        }
        // For Rectangle/Square, ShapeElement.position is top-left.
        return new BoundingBoxClass(elPos.x, elPos.y, width, height, `bbox-${element.id}`)
      }

      case CoreElementType.CIRCLE: {
        const circle = element as unknown as Shape.Circle // Added unknown
        const radius = circle.properties?.radius ?? 0
        if (typeof radius !== 'number' || !Number.isFinite(radius) || radius < 0) {
          throw new CoreError(
            ErrorType.InvalidParameter,
            `Circle (ID: ${element.id}) has invalid radius: ${radius}`,
            undefined,
            { component: 'RectangleDistanceStrategy', operation: 'getBoundingBoxClass', target: element.id, metadata: { radius } },
          )
        }
        // For Circle, ShapeElement.position is center. BoundingBoxClass expects top-left.
        return new BoundingBoxClass(
          elPos.x - radius,
          elPos.y - radius,
          radius * 2,
          radius * 2,
          `bbox-${element.id}`,
        )
      }

      case CoreElementType.ELLIPSE: {
        const ellipse = element as unknown as Shape.Ellipse // Added unknown
        const radiusX = typeof ellipse.properties?.radiusX === 'number' ? ellipse.properties.radiusX : 0
        const radiusY = typeof ellipse.properties?.radiusY === 'number' ? ellipse.properties.radiusY : 0
        if (typeof radiusX !== 'number' || typeof radiusY !== 'number'
          || !Number.isFinite(radiusX) || !Number.isFinite(radiusY) || radiusX < 0 || radiusY < 0) {
          throw new CoreError(
            ErrorType.InvalidParameter,
            `Ellipse (ID: ${element.id}) has invalid radii. RX: ${radiusX}, RY: ${radiusY}`,
            undefined,
            { component: 'RectangleDistanceStrategy', operation: 'getBoundingBoxClass', target: element.id, metadata: { radiusX, radiusY } },
          )
        }
        // For Ellipse, ShapeElement.position is center. BoundingBoxClass expects top-left.
        return new BoundingBoxClass(
          elPos.x - radiusX,
          elPos.y - radiusY,
          radiusX * 2,
          radiusY * 2,
          `bbox-${element.id}`,
        )
      }

      default:
        throw new CoreError(
          ErrorType.InvalidElementType,
          `Bounding box creation for type ${element.type} (ID: ${element.id}) is not specifically handled by getBoundingBoxClass in RectangleDistanceStrategy.`,
          undefined,
          { component: 'RectangleDistanceStrategy', operation: 'getBoundingBoxClass', target: element.id },
        )
    }
  }

  /**
   * Calculates the distance between a Rectangle/Square (elementA) and another {@link ShapeElement} (elementB)
   * by determining the distance between their respective bounding boxes.
   *
   * @param elementA - The first shape element, which must be of type {@link CoreElementType.RECTANGLE} or {@link CoreElementType.SQUARE}.
   * @param elementB - The second shape element (can be RECTANGLE, SQUARE, CIRCLE, or ELLIPSE, as handled by `getBoundingBoxClass`).
   * @returns The calculated distance between the bounding boxes of the two elements.
   * @throws {@link CoreError} if `elementA` is not a RECTANGLE or SQUARE.
   * @throws {@link CoreError} if bounding box creation fails for either element due to invalid properties.
   */
  public calculateDistance(elementA: ShapeElement, elementB: ShapeElement): number {
    if (elementA.type !== CoreElementType.RECTANGLE && elementA.type !== CoreElementType.SQUARE) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `RectangleDistanceStrategy's first element must be RECTANGLE or SQUARE, got ${elementA.type}`,
        undefined,
        { component: 'RectangleDistanceStrategy', operation: 'calculateDistance', target: elementA.id },
      )
    }

    const bboxA = this.getBoundingBoxClass(elementA)
    const bboxB = this.getBoundingBoxClass(elementB)

    return calculateBoundingBoxDistance(bboxA, bboxB)
  }

  /**
   * Returns the primary element type that this strategy handles as the first argument.
   * @remarks This strategy handles both `RECTANGLE` and `SQUARE` types.
   * @returns {@link CoreElementType.RECTANGLE} - The rectangle element type that this strategy handles
   */
  public getElementType(): CoreElementType {
    return CoreElementType.RECTANGLE
  }
}
