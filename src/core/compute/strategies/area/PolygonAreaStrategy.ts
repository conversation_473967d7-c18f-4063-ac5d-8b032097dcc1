/**
 * Area Calculation Strategy for Polygon Elements
 *
 * @remarks
 * This strategy implements the {@link AreaCalculatorStrategy} for calculating the
 * area of various polygon-like elements, including `POLYGON`, `TRIANGLE`, `HEXAGON`,
 * `QUAD<PERSON>LATERAL`, `<PERSON><PERSON><PERSON><PERSON><PERSON>`, `<PERSON><PERSON><PERSON><PERSON>`, `<PERSON><PERSON><PERSON><PERSON>`, and `DECAGON`.
 *
 * The calculation relies on the `points` property of the polygon element and uses
 * the `calculatePolygonAreaUtil` function from `../../../../lib/utils/geometry/polygonUtils`
 * (which typically implements the shoelace formula or a similar algorithm).
 *
 * The strategy validates that the element is of a supported polygon type and
 * that it has at least 3 valid points.
 *
 * @module core/compute/strategies/area/PolygonAreaStrategy
 * @see {@link Shape.Polygon} for the polygon element type definition.
 */
import type { AreaCalculatorStrategy } from '../../../../types/core/compute'
import type { Element, Shape, ShapeElement } from '../../../../types/core/elementDefinitions'
import { calculateArea as calculatePolygonAreaUtil } from '../../../../lib/utils/geometry/polygonUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import { ElementType as CoreElementType } from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

export class PolygonAreaStrategy implements AreaCalculatorStrategy {
  /**
   * Calculates the area of a polygon-like element.
   *
   * @param element - The polygon-like element. Expected to be one of the supported
   *                  polygon types (e.g., {@link CoreElementType.POLYGON}, {@link CoreElementType.TRIANGLE})
   *                  and conform to {@link Shape.Polygon}.
   * @returns The calculated area of the polygon.
   * @throws {@link CoreError} if the element is `null` or `undefined`.
   * @throws {@link CoreError} if the element type is not a supported polygon type.
   * @throws {@link CoreError} if the polygon element has fewer than 3 points or if any point data is invalid.
   */
  public calculateArea(element: Element): number {
    // 首先将Element转换为ShapeElement类型
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement == null) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        'Invalid element: null or undefined',
        undefined,
        { component: 'PolygonAreaStrategy', operation: 'calculateArea' },
      )
    }

    const validTypes = [
      CoreElementType.POLYGON,
      CoreElementType.TRIANGLE,
      CoreElementType.HEXAGON,
      CoreElementType.QUADRILATERAL,
      CoreElementType.PENTAGON,
      CoreElementType.OCTAGON,
      CoreElementType.NONAGON,
      CoreElementType.DECAGON,
    ]

    if (!validTypes.includes(shapeElement.type as CoreElementType)) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `PolygonAreaStrategy can only calculate area for polygon-like elements, got ${shapeElement.type}`,
        undefined,
        { component: 'PolygonAreaStrategy', operation: 'calculateArea', target: shapeElement.id },
      )
    }

    const polygonElement = shapeElement as unknown as Shape.Polygon // Added unknown for type safety
    const points = polygonElement.points // Points are directly on the Polygon element

    if (points === null || points === undefined || !Array.isArray(points) || points.length < 3) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Polygon element (ID: ${shapeElement.id}) must have at least 3 points. Found: ${points?.length || 0}`,
        undefined,
        { component: 'PolygonAreaStrategy', operation: 'calculateArea', target: shapeElement.id, metadata: { pointsLength: points?.length || 0 } },
      )
    }

    // Ensure all points are valid
    for (let i = 0; i < points.length; i++) {
      const p = points[i]
      if (p === null || p === undefined || typeof p.x !== 'number' || typeof p.y !== 'number' || !Number.isFinite(p.x) || !Number.isFinite(p.y)) {
        throw new CoreError(
          ErrorType.InvalidParameter,
          `Polygon element (ID: ${shapeElement.id}) contains invalid point data at index ${i}.`,
          undefined,
          { component: 'PolygonAreaStrategy', operation: 'calculateArea', target: shapeElement.id, metadata: { pointIndex: i, pointData: p } },
        )
      }
    }
    // 多边形的points是相对于position的，所以我们需要转换为绝对坐标来计算面积
    // 创建一个新的点数组，每个点都加上element的position
    const absolutePoints = points.map((point) => {
      const positionX = shapeElement.position?.x ?? 0
      const positionY = shapeElement.position?.y ?? 0
      return {
        x: point.x + positionX,
        y: point.y + positionY,
        z: point.z,
      }
    })

    // 使用绝对坐标计算面积
    return calculatePolygonAreaUtil(absolutePoints)
  }

  /**
   * Returns the element types this strategy is registered for.
   *
   * @remarks
   * This strategy can calculate the area for various n-sided polygons
   * (TRIANGLE, PENTAGON, HEXAGON, etc.). It returns an array of all supported
   * polygon types to be registered with the strategy registry.
   *
   * @returns An array of supported polygon element types that this strategy handles
   */
  public getElementType(): CoreElementType[] {
    // Return all supported polygon types
    return [
      CoreElementType.POLYGON,
      CoreElementType.TRIANGLE,
      CoreElementType.PENTAGON,
      CoreElementType.HEXAGON,
      CoreElementType.QUADRILATERAL,
      CoreElementType.OCTAGON,
      CoreElementType.NONAGON,
      CoreElementType.DECAGON,
    ]
  }
}
