/**
 * Canvas Setup Hook
 *
 * This module provides a React hook for setting up the canvas SVG structure.
 * It handles the creation and configuration of SVG elements, groups, and
 * definitions required for the canvas rendering system.
 *
 * @module hooks/canvas/useCanvasSetup
 */

import type React from 'react'
import * as d3 from 'd3'
import { useEffect, useRef } from 'react'

interface UseCanvasSetupProps {
  svgRef: React.RefObject<SVGSVGElement | null>
}

interface CanvasSetupRefs {
  mainGroupRef: React.RefObject<SVGGElement | null>
  shapesLayerRef: React.RefObject<SVGGElement | null>
  interactionLayerRef: React.RefObject<SVGGElement | null>
}

export function useCanvasSetup({ svgRef }: UseCanvasSetupProps): CanvasSetupRefs {
  const mainGroupRef = useRef<SVGGElement | null>(null)
  const shapesLayerRef = useRef<SVGGElement | null>(null)
  const interactionLayerRef = useRef<SVGGElement | null>(null)

  useEffect(() => {
    if (svgRef.current === null) {
      return
    }
    const d3Select = d3.select

    const svgNode = d3Select(svgRef.current)

    const mainG = svgNode
      .selectAll('g.main-transform-group')
      .data([null])
      .join(
        enter => enter.append('g').attr('class', 'main-transform-group'),
        update => update,
      )
    mainGroupRef.current = mainG.node() as SVGGElement

    if (mainGroupRef.current !== null) {
      const parentG = d3Select(mainGroupRef.current)

      const shapesG = parentG
        .selectAll('g.shapes-layer')
        .data([null])
        .join(enter => enter.append('g').attr('class', 'shapes-layer'))
      shapesLayerRef.current = shapesG.node() as SVGGElement

      const interactionG = parentG
        .selectAll('g.interaction-layer')
        .data([null])
        .join(enter => enter.append('g').attr('class', 'interaction-layer').style('pointer-events', 'none'))
      interactionLayerRef.current = interactionG.node() as SVGGElement

      parentG
        .selectAll('g.temp-layer')
        .data([null])
        .join(enter => enter.append('g').attr('class', 'temp-layer'))
    }
  }, [svgRef])

  return { mainGroupRef, shapesLayerRef, interactionLayerRef }
}
