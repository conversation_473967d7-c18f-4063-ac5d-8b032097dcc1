import type { Image } from '@/types/core/element/image/imageElementTypes'
import { describe, expect, it } from 'vitest'
import { ElementType } from '@/types/core/elementDefinitions'

describe('image Element Types Module', () => {
  describe('image Interface', () => {
    it('should define valid image element with URL source type', () => {
      // Create an image element with URL source type
      const imageElement: Image = {
        // ShapeElement properties
        id: 'img-001',
        type: ElementType.IMAGE,
        visible: true,
        locked: false,

        // Image-specific properties
        src: 'https://example.com/image.jpg',
        sourceType: 'url',
        alt: 'Example image',
        position: { x: 100, y: 100 },
        width: 200,
        height: 150,
      }

      // Verify the image element has the expected properties
      expect(imageElement.id).toBe('img-001')
      expect(imageElement.type).toBe(ElementType.IMAGE)
      expect(imageElement.visible).toBe(true)
      expect(imageElement.locked).toBe(false)

      expect(imageElement.src).toBe('https://example.com/image.jpg')
      expect(imageElement.sourceType).toBe('url')
      expect(imageElement.alt).toBe('Example image')
      expect(imageElement.position).toEqual({ x: 100, y: 100 })
      expect(imageElement.width).toBe(200)
      expect(imageElement.height).toBe(150)
    })

    it('should define valid image element with SVG inline data source type', () => {
      // Create an image element with SVG inline data source type
      const svgContent = '<svg width="100" height="100"><circle cx="50" cy="50" r="40" fill="red" /></svg>'
      const imageElement: Image = {
        // ShapeElement properties
        id: 'img-002',
        type: ElementType.IMAGE,
        visible: true,
        locked: false,

        // Image-specific properties
        src: svgContent,
        sourceType: 'svg_inline_data',
        position: { x: 50, y: 50 },
        width: 100,
        height: 100,
      }

      // Verify the image element has the expected properties
      expect(imageElement.id).toBe('img-002')
      expect(imageElement.type).toBe(ElementType.IMAGE)
      expect(imageElement.visible).toBe(true)
      expect(imageElement.locked).toBe(false)

      expect(imageElement.src).toBe(svgContent)
      expect(imageElement.sourceType).toBe('svg_inline_data')
      expect(imageElement.alt).toBeUndefined() // alt is optional
      expect(imageElement.position).toEqual({ x: 50, y: 50 })
      expect(imageElement.width).toBe(100)
      expect(imageElement.height).toBe(100)
    })

    it('should allow image element with local file path', () => {
      // Create an image element with a local file path
      const imageElement: Image = {
        // ShapeElement properties
        id: 'img-003',
        type: ElementType.IMAGE,
        visible: true,
        locked: false,

        // Image-specific properties
        src: './assets/images/logo.png',
        sourceType: 'url',
        position: { x: 0, y: 0 },
        width: 150,
        height: 75,
      }

      // Verify the image element has the expected properties
      expect(imageElement.id).toBe('img-003')
      expect(imageElement.type).toBe(ElementType.IMAGE)
      expect(imageElement.visible).toBe(true)
      expect(imageElement.locked).toBe(false)

      expect(imageElement.src).toBe('./assets/images/logo.png')
      expect(imageElement.sourceType).toBe('url')
      expect(imageElement.position).toEqual({ x: 0, y: 0 })
      expect(imageElement.width).toBe(150)
      expect(imageElement.height).toBe(75)
    })

    it('should handle image element with zero dimensions', () => {
      // Create an image element with zero dimensions
      // Note: In a real implementation, this might throw an error or be invalid
      const imageElement: Image = {
        // ShapeElement properties
        id: 'img-004',
        type: ElementType.IMAGE,
        visible: true,
        locked: false,

        // Image-specific properties
        src: 'https://example.com/pixel.png',
        sourceType: 'url',
        position: { x: 200, y: 200 },
        width: 0, // Zero width
        height: 0, // Zero height
      }

      // Verify the image element has the expected properties
      expect(imageElement.id).toBe('img-004')
      expect(imageElement.type).toBe(ElementType.IMAGE)
      expect(imageElement.visible).toBe(true)
      expect(imageElement.locked).toBe(false)

      expect(imageElement.src).toBe('https://example.com/pixel.png')
      expect(imageElement.sourceType).toBe('url')
      expect(imageElement.position).toEqual({ x: 200, y: 200 })
      expect(imageElement.width).toBe(0)
      expect(imageElement.height).toBe(0)
    })

    it('should handle image element with negative position', () => {
      // Create an image element with negative position coordinates
      const imageElement: Image = {
        // ShapeElement properties
        id: 'img-005',
        type: ElementType.IMAGE,
        visible: true,
        locked: false,

        // Image-specific properties
        src: 'https://example.com/background.jpg',
        sourceType: 'url',
        position: { x: -50, y: -50 }, // Negative position
        width: 300,
        height: 200,
      }

      // Verify the image element has the expected properties
      expect(imageElement.id).toBe('img-005')
      expect(imageElement.type).toBe(ElementType.IMAGE)
      expect(imageElement.visible).toBe(true)
      expect(imageElement.locked).toBe(false)

      expect(imageElement.src).toBe('https://example.com/background.jpg')
      expect(imageElement.sourceType).toBe('url')
      expect(imageElement.position).toEqual({ x: -50, y: -50 })
      expect(imageElement.width).toBe(300)
      expect(imageElement.height).toBe(200)
    })
  })
})
