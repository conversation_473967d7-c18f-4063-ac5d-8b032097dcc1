/**
 * Separator Component
 *
 * A visual divider that can be used to separate content.
 * Built on top of Radix UI's Separator primitive with custom styling.
 *
 * Features:
 * - Horizontal or vertical orientation
 * - Customizable appearance
 * - Proper ARIA attributes for accessibility
 * - Optional decorative mode
 */

import type * as React from 'react'
import * as SeparatorPrimitive from '@radix-ui/react-separator'

import { cn } from '@/lib/utils'

/**
 * A visual divider component that can be used to separate content
 *
 * @param props - The component props
 * @param props.className - Additional CSS classes to apply
 * @param props.orientation - The orientation of the separator ('horizontal' or 'vertical')
 * @param props.decorative - Whether the separator is purely decorative
 *
 * @example
 * ```tsx
 * <Separator className="my-4" />
 * <Separator orientation="vertical" className="mx-2 h-4" />
 * ```
 */
function Separator({
  className,
  orientation = 'horizontal',
  decorative = true,
  ...props
}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {
  return (
    <SeparatorPrimitive.Root
      data-slot="separator-root"
      decorative={decorative}
      orientation={orientation}
      className={cn(
        'bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px',
        className,
      )}
      {...props}
    />
  )
}

export { Separator }
