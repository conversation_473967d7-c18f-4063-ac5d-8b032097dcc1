import type { LoggerService } from '@/types/services/logging'
import { describe, expect, it } from 'vitest'

describe('loggerService', () => {
  it('should define the required methods', () => {
    // Create a mock implementation of LoggerService
    const logger: LoggerService = {
      info: (message: string, ...optionalParams: any[]) => {},
      warn: (message: string, ...optionalParams: any[]) => {},
      error: (message: string, ...optionalParams: any[]) => {},
      debug: (message: string, ...optionalParams: any[]) => {},
    }

    // Verify that the logger has all required methods
    expect(logger).toHaveProperty('info')
    expect(logger).toHaveProperty('warn')
    expect(logger).toHaveProperty('error')
    expect(logger).toHaveProperty('debug')

    // Verify that the methods are functions
    expect(typeof logger.info).toBe('function')
    expect(typeof logger.warn).toBe('function')
    expect(typeof logger.error).toBe('function')
    expect(typeof logger.debug).toBe('function')
  })

  it('should allow creating a logger without debug method', () => {
    // Create a mock implementation of LoggerService without debug method
    const logger: LoggerService = {
      info: (message: string, ...optionalParams: any[]) => {},
      warn: (message: string, ...optionalParams: any[]) => {},
      error: (message: string, ...optionalParams: any[]) => {},
    }

    // Verify that the logger has all required methods
    expect(logger).toHaveProperty('info')
    expect(logger).toHaveProperty('warn')
    expect(logger).toHaveProperty('error')

    // Verify that debug is undefined
    expect(logger.debug).toBeUndefined()
  })

  it('should accept messages and optional parameters', () => {
    // Create a mock implementation with spies
    const infoSpy = vi.fn()
    const warnSpy = vi.fn()
    const errorSpy = vi.fn()
    const debugSpy = vi.fn()

    const logger: LoggerService = {
      info: infoSpy,
      warn: warnSpy,
      error: errorSpy,
      debug: debugSpy,
    }

    // Call methods with different parameters
    logger.info('Info message')
    logger.info('Info with params', { key: 'value' }, 123)

    logger.warn('Warning message')
    logger.warn('Warning with params', new Error('Test error'))

    logger.error('Error message')
    logger.error('Error with params', new Error('Test error'), { context: 'test' })

    logger.debug('Debug message')
    logger.debug('Debug with params', { debug: true }, [1, 2, 3])

    // Verify calls
    expect(infoSpy).toHaveBeenCalledTimes(2)
    expect(infoSpy).toHaveBeenCalledWith('Info message')
    expect(infoSpy).toHaveBeenCalledWith('Info with params', { key: 'value' }, 123)

    expect(warnSpy).toHaveBeenCalledTimes(2)
    expect(warnSpy).toHaveBeenCalledWith('Warning message')
    expect(warnSpy).toHaveBeenCalledWith('Warning with params', expect.any(Error))

    expect(errorSpy).toHaveBeenCalledTimes(2)
    expect(errorSpy).toHaveBeenCalledWith('Error message')
    expect(errorSpy).toHaveBeenCalledWith('Error with params', expect.any(Error), { context: 'test' })

    expect(debugSpy).toHaveBeenCalledTimes(2)
    expect(debugSpy).toHaveBeenCalledWith('Debug message')
    expect(debugSpy).toHaveBeenCalledWith('Debug with params', { debug: true }, [1, 2, 3])
  })
})
