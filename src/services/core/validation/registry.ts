/**
 * Validation Service Registration
 *
 * This module provides functions for registering the validation service with the service registry.
 *
 * @module services/core/validation
 */

import type { ServiceRegistry } from '../registry'
import type { LoggerService } from '@/types/services/logging'
import { ServiceId } from '@/types/services/core/serviceIdentifier'
import { getServiceFactory } from '../registry' // Corrected path and added getServiceFactory
// import type { ElementValidator } from '@/core/validator'; // Removed unused type import

/**
 * 注册验证服务到服务注册表
 *
 * @param registry 服务注册表
 * @param logger 日志服务，用于记录验证错误
 */
export function registerValidationService(
  registry: ServiceRegistry,
  logger: LoggerService,
): void {
  // Create validation service instance using the factory
  const factory = getServiceFactory()
  const validationService = factory.createValidationService(logger)

  // 如果需要，可以使用日志服务配置验证器
  // 注意：当前ElementValidator构造函数不接受logger参数，
  // 但未来可能会扩展此功能

  // 注册到注册表
  registry.register(ServiceId.ValidationService, validationService)

  logger.info('验证服务注册成功')
}
