import { describe, it, expect, vi, beforeEach } from 'vitest';
import * as shapeModule from '@/data/export/render/shape';

vi.mock('@/data/export/render/general', () => ({
  getShapeStyle: vi.fn(() => ({
    fill: '#f0f',
    stroke: '#0f0',
    strokeWidth: 5,
  })),
  getTransform: vi.fn((rotation, pos) =>
    rotation ? ` transform="rotate(${rotation},${pos.x},${pos.y})"` : ''
  ),
}));

describe('render/shape', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renderRectangle should render rectangle with correct attributes', () => {
    const rect = {
      position: { x: 50, y: 60 },
      width: 40,
      height: 20,
      cornerRadius: 8,
      rotation: 30,
    };
    const result = shapeModule.renderRectangle(rect as any);
    expect(result).toContain('<rect');
    expect(result).toContain('x="30"'); // 50 - 40/2
    expect(result).toContain('y="50"'); // 60 - 20/2
    expect(result).toContain('width="40"');
    expect(result).toContain('height="20"');
    expect(result).toContain('rx="8"');
    expect(result).toContain('ry="8"');
    expect(result).toContain('fill="#f0f"');
    expect(result).toContain('stroke="#0f0"');
    expect(result).toContain('stroke-width="5"');
    expect(result).toContain('transform="rotate(30,50,60)"');
  });

  it('renderRectangle should omit rx/ry if no cornerRadius', () => {
    const rect = {
      position: { x: 0, y: 0 },
      width: 10,
      height: 10,
      rotation: 0,
    };
    const result = shapeModule.renderRectangle(rect as any);
    expect(result).not.toContain('rx=');
    expect(result).not.toContain('ry=');
  });

  it('renderEllipse should render ellipse with correct attributes', () => {
    const ellipse = {
      position: { x: 10, y: 20 },
      radiusX: 15,
      radiusY: 25,
      rotation: 45,
    };
    const result = shapeModule.renderEllipse(ellipse as any);
    expect(result).toContain('<ellipse');
    expect(result).toContain('cx="10"');
    expect(result).toContain('cy="20"');
    expect(result).toContain('rx="15"');
    expect(result).toContain('ry="25"');
    expect(result).toContain('fill="#f0f"');
    expect(result).toContain('stroke="#0f0"');
    expect(result).toContain('stroke-width="5"');
    expect(result).toContain('transform="rotate(45,10,20)"');
  });

  it('renderPolygon should render polygon with correct points and attributes', () => {
    const polygon = {
      points: [{ x: 1, y: 2 }, { x: 3, y: 4 }, { x: 5, y: 6 }],
      position: { x: 7, y: 8 },
      rotation: 90,
    };
    const result = shapeModule.renderPolygon(polygon as any);
    expect(result).toContain('<polygon');
    expect(result).toContain('points="1,2 3,4 5,6"');
    expect(result).toContain('fill="#f0f"');
    expect(result).toContain('stroke="#0f0"');
    expect(result).toContain('stroke-width="5"');
    expect(result).toContain('transform="rotate(90,7,8)"');
  });

  it('renderPolygon should handle empty points', () => {
    const polygon = {
      points: [],
      position: { x: 0, y: 0 },
      rotation: 0,
    };
    const result = shapeModule.renderPolygon(polygon as any);
    expect(result).toContain('points=""');
  });
});