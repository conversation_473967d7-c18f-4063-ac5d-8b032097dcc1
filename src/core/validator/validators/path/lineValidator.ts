/**
 * Contains validation logic specific to Line shapes.
 */

import type { ValidatableShape, ValidationError } from '@/types/core/validator/validator-interface' // Corrected
import { ElementType } from '@/types/core/elementDefinitions' // Import ElementType only
import { ValidationErrorCode } from '@/types/core/validator/error-codes' // Corrected
import { isValidPoint } from '../common/validationUtils'
import { BaseShapeValidator } from '../common/validatorBase'

/**
 * Concrete validator implementation for Line shapes.
 *
 * @extends BaseShapeValidator
 */
export class LineValidator extends BaseShapeValidator {
  /**
   * Validates properties specific to a Line element data structure.
   *
   * @param shape - The shape to validate.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected validateSpecific(shape: ValidatableShape): ValidationError[] {
    const errors: ValidationError[] = []

    if (shape.type !== ElementType.LINE) {
      errors.push({
        code: ValidationErrorCode.INVALID_SHAPE_TYPE,
        message: `Internal Error: LineValidator received shape type ${shape.type}`,
        path: 'type',
      })
      return errors
    }

    const props = (shape as unknown as Record<string, unknown>)?.properties as Record<string, unknown> | undefined || {}
    const startPoint = props.start as { x: number, y: number } | undefined
    const endPoint = props.end as { x: number, y: number } | undefined

    let startValid = false
    let endValid = false

    // Validate start point
    if (startPoint == null || !isValidPoint(startPoint)) {
      errors.push({
        code: ValidationErrorCode.INVALID_POINT,
        message: `Line requires a valid 'start' point in properties. Received: ${JSON.stringify(startPoint)}`,
        path: 'properties.start',
        value: startPoint,
      })
    }
    else {
      startValid = true
    }

    // Validate end point
    if (endPoint == null || !isValidPoint(endPoint)) {
      errors.push({
        code: ValidationErrorCode.INVALID_POINT,
        message: `Line requires a valid 'end' point in properties. Received: ${JSON.stringify(endPoint)}`,
        path: 'properties.end',
        value: endPoint,
      })
    }
    else {
      endValid = true
    }

    // Only check for identical points if both points were individually valid
    if (startValid && endValid && startPoint != null && endPoint != null && this.isSamePoint(startPoint, endPoint)) {
      errors.push({
        code: ValidationErrorCode.INVALID_PROPERTY_VALUE,
        message: 'Line start and end points cannot be the same',
        path: 'properties.start, properties.end',
      })
    }

    return errors
  }

  /**
   * Applies line-specific business rules.
   *
   * @param _shape - The shape to validate against business rules.
   * @returns An array of validation errors.
   * @protected
   * @override
   */
  protected applySpecificRules(_shape: ValidatableShape): ValidationError[] {
    // No line-specific business rules defined currently beyond common ones.
    return []
  }

  /**
   * Checks if two point-like objects represent the same coordinates.
   *
   * @param p1 - The first point.
   * @param p1.x - The x coordinate of the first point.
   * @param p1.y - The y coordinate of the first point.
   * @param p2 - The second point.
   * @param p2.x - The x coordinate of the second point.
   * @param p2.y - The y coordinate of the second point.
   * @returns True if the points have the same coordinates, false otherwise.
   * @private
   */
  private isSamePoint(p1: { x: number, y: number }, p2: { x: number, y: number }): boolean {
    return p1.x === p2.x && p1.y === p2.y
  }
}
