/**
 * Path Drawing Handler Hook
 *
 * This module provides a React hook for managing path drawing operations
 * on the canvas. It handles different path types (lines, polylines, curves),
 * manages drawing state, and provides methods for interactive path creation.
 *
 * @module hooks/usePathDrawHandler
 */

import type PointData from '@/types/core/element/geometry/point'

import { useCallback, useRef, useState } from 'react'

import { ElementType } from '@/types/core/elementDefinitions'

// 使用PointData作为Point类型
type Point = PointData

/**
 * 路径绘制状态
 */
export interface PathDrawState {
  isDrawing: boolean
  pathType: ElementType
  startPoint: Point | null
  currentPoint: Point | null
  points: Point[]
  clickCount: number // 新增：记录点击次数
}

/**
 * 路径绘制处理器配置
 */
export interface PathDrawHandlerConfig {
  onDrawStart?: (pathType: ElementType, point: Point) => void
  onDrawUpdate?: (startPoint: Point, currentPoint: Point) => void
  onDrawComplete?: (points: Point[]) => void
  onDrawCancel?: () => void
  onSegmentComplete?: (pathType: ElementType, points: Point[]) => void // 新增：每个线段完成时的回调
}

/**
 * 路径绘制处理器
 */
export interface PathDrawHandler {
  isDrawing: boolean
  pathType: ElementType | null
  currentPoint: Point | null
  startDrawing: (pathType: ElementType, startPoint: Point) => void
  updateDrawing: (currentPoint: Point) => void
  completeDrawing: (endPoint: Point) => void
  finishPolyline: () => void
  cancelDrawing: () => void
  getDrawState: () => PathDrawState
}

/**
 * 用于处理路径元素（线段、多段线、曲线等）的交互式绘制
 */
export function usePathDrawHandler({
  onDrawStart,
  onDrawUpdate,
  onDrawComplete,
  onDrawCancel,
  onSegmentComplete: _onSegmentComplete,
}: PathDrawHandlerConfig = {}): PathDrawHandler {
  // 绘制状态
  const [isDrawing, setIsDrawing] = useState(false)
  const [pathType, setPathType] = useState<ElementType | null>(null)
  const [currentPoint, setCurrentPoint] = useState<Point | null>(null)
  const [clickCount, setClickCount] = useState(0) // 新增：点击计数
  const startPointRef = useRef<Point | null>(null)
  const pointsRef = useRef<Point[]>([])

  // 双击检测
  const lastClickTimeRef = useRef<number>(0)
  const clickThresholdRef = useRef<number>(300) // 毫秒，用于判断双击

  // 开始绘制
  const startDrawing = useCallback((type: ElementType, point: Point) => {
    // 确保点有有效的坐标
    if (typeof point.x !== 'number' || typeof point.y !== 'number') {
      console.error('[usePathDrawHandler] startDrawing: Invalid point coordinates', point)
      return
    }

    // 创建新的点对象，避免引用问题
    const newPoint = {
      x: point.x,
      y: point.y,
      z: point.z ?? 0,
    }

    // 添加调试信息
    console.warn('[usePathDrawHandler] startDrawing:', {
      type,
      point: newPoint,
    })

    setIsDrawing(true)
    setPathType(type)
    startPointRef.current = newPoint
    setCurrentPoint(newPoint)
    pointsRef.current = [newPoint]
    setClickCount(1) // 第一次点击

    // 绘制开始事件
    // 注意：事件总线服务暂未实现

    // 调用回调
    if (onDrawStart) {
      onDrawStart(type, newPoint)
    }
  }, [onDrawStart])

  // 更新绘制
  const updateDrawing = useCallback((point: Point) => {
    // 确保点有有效的坐标
    if (typeof point.x !== 'number' || typeof point.y !== 'number') {
      console.warn('[usePathDrawHandler] updateDrawing: Invalid point coordinates', point)
      return
    }

    // 创建一个新的点对象，避免引用问题
    const newCurrentPoint = {
      x: point.x,
      y: point.y,
      z: point.z ?? 0,
    }

    setCurrentPoint(newCurrentPoint)

    // 绘制更新事件
    // 注意：事件总线服务暂未实现

    // 调用回调 - 即使没有开始绘制也要调用，以便实时预览
    if (onDrawUpdate) {
      const effectiveStartPoint = startPointRef.current || newCurrentPoint
      onDrawUpdate(effectiveStartPoint, newCurrentPoint)
    }
  }, [onDrawUpdate])

  // 完成绘制
  const completeDrawing = useCallback((endPoint: Point) => {
    if (!isDrawing || startPointRef.current === null || pathType === null) {
      console.warn('[usePathDrawHandler] completeDrawing: Not in drawing state or missing data', {
        isDrawing,
        startPoint: startPointRef.current,
        pathType,
      })
      return
    }

    // 确保点有有效的坐标
    if (typeof endPoint.x !== 'number' || typeof endPoint.y !== 'number') {
      console.error('[usePathDrawHandler] completeDrawing: Invalid point coordinates', endPoint)
      return
    }

    // 创建新的点对象，避免引用问题
    const newEndPoint = {
      x: endPoint.x,
      y: endPoint.y,
      z: endPoint.z ?? 0,
    }

    // 增加点击计数
    const newClickCount = clickCount + 1
    setClickCount(newClickCount)

    // 添加调试信息
    console.warn('[usePathDrawHandler] completeDrawing:', {
      pathType,
      clickCount: newClickCount,
      startPoint: startPointRef.current,
      endPoint: newEndPoint,
    })

    // 对于Polyline，累积点但不立即创建元素
    if (pathType === ElementType.POLYLINE) {
      // 添加点到总点数组
      pointsRef.current.push(newEndPoint)

      // 检查是否是双击（完成多段线绘制）
      const now = Date.now()
      const isDoubleClick = now - lastClickTimeRef.current < clickThresholdRef.current

      if (isDoubleClick && pointsRef.current.length >= 2) {
        // 双击完成多段线绘制
        console.warn('[usePathDrawHandler] POLYLINE: Double click detected, completing polyline with points:', pointsRef.current.length)

        // 调用回调，完成Polyline绘制
        if (onDrawComplete) {
          onDrawComplete([...pointsRef.current])
        }

        // 重置状态
        setIsDrawing(false)
        setPathType(null)
        startPointRef.current = null
        setCurrentPoint(null)
        pointsRef.current = []
        setClickCount(0)
        return
      }

      // 单击继续添加点
      lastClickTimeRef.current = now
      // 将当前终点设为新的起点，继续绘制
      startPointRef.current = newEndPoint
      // 不设置currentPoint，让它继续跟随鼠标
      // 不重置状态，继续绘制
      console.warn('[usePathDrawHandler] POLYLINE: Added point, total points:', pointsRef.current.length)
      return
    }

    // 添加点到数组
    pointsRef.current.push(newEndPoint)

    // 检查是否需要更多点
    const needsMorePoints = (
      (pathType === ElementType.ARC && newClickCount < 3)
      || (pathType === ElementType.QUADRATIC && newClickCount < 3)
      || (pathType === ElementType.CUBIC && newClickCount < 3)
    )

    if (needsMorePoints) {
      // 继续绘制，等待更多点
      setCurrentPoint(newEndPoint)
      return
    }

    // 绘制完成事件
    // 注意：事件总线服务暂未实现

    // 调用回调
    console.warn('[usePathDrawHandler] 即将调用onDrawComplete，参数:', [...pointsRef.current])
    if (onDrawComplete) {
      onDrawComplete([...pointsRef.current])
      console.warn('[usePathDrawHandler] onDrawComplete已调用')
    }
    else {
      console.warn('[usePathDrawHandler] onDrawComplete回调为undefined')
    }

    // 重置状态
    setIsDrawing(false)
    setPathType(null)
    startPointRef.current = null
    setCurrentPoint(null)
    pointsRef.current = []
    setClickCount(0)
  }, [isDrawing, pathType, clickCount, onDrawComplete])

  // 完成Polyline绘制（当用户再次点击Polyline按钮时）
  const finishPolyline = useCallback(() => {
    if (!isDrawing || pathType !== ElementType.POLYLINE) {
      return
    }

    // 调用回调，完成Polyline绘制
    if (onDrawComplete && pointsRef.current.length >= 2) {
      onDrawComplete([...pointsRef.current])
    }

    // 重置状态
    setIsDrawing(false)
    setPathType(null)
    startPointRef.current = null
    setCurrentPoint(null)
    pointsRef.current = []
    setClickCount(0)
  }, [isDrawing, pathType, onDrawComplete])

  // 取消绘制
  const cancelDrawing = useCallback(() => {
    if (!isDrawing) {
      return
    }

    // 绘制取消事件
    // 注意：事件总线服务暂未实现

    // 调用回调
    if (onDrawCancel) {
      onDrawCancel()
    }

    // 重置状态
    setIsDrawing(false)
    setPathType(null)
    startPointRef.current = null
    setCurrentPoint(null)
    pointsRef.current = []
    setClickCount(0)
  }, [isDrawing, onDrawCancel])

  // 获取当前绘制状态
  const getDrawState = useCallback((): PathDrawState => {
    return {
      isDrawing,
      pathType: pathType ?? ElementType.LINE,
      startPoint: startPointRef.current,
      currentPoint,
      points: [...pointsRef.current],
      clickCount,
    }
  }, [isDrawing, pathType, currentPoint, clickCount])

  return {
    isDrawing,
    pathType,
    currentPoint,
    startDrawing,
    updateDrawing,
    completeDrawing,
    finishPolyline,
    cancelDrawing,
    getDrawState,
  }
}
