import { test, expect } from '@playwright/test';

/**
 * End-to-end test suite for the Canvas Application.
 * These tests verify the functionality of the entire application from a user's perspective.
 */
test.describe('Canvas Application', () => {
  /**
   * Setup for each test.
   * Navigates to the application before each test.
   */
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the canvas to be loaded
    await page.waitForSelector('#canvas-container');
  });

  /**
   * Tests creating a rectangle.
   * Verifies that a rectangle can be created on the canvas.
   */
  test('Create a rectangle', async ({ page }) => {
    // Select the rectangle tool
    await page.click('.tool-button[data-tool="rectangle"]');
    
    // Draw a rectangle on the canvas
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Verify that the rectangle was created
    const rectangle = await page.locator('rect');
    await expect(rectangle).toBeVisible();
    
    // Verify rectangle attributes
    const x = await rectangle.getAttribute('x');
    const y = await rectangle.getAttribute('y');
    const width = await rectangle.getAttribute('width');
    const height = await rectangle.getAttribute('height');
    
    expect(parseInt(x)).toBe(100);
    expect(parseInt(y)).toBe(100);
    expect(parseInt(width)).toBe(200); // 300 - 100
    expect(parseInt(height)).toBe(150); // 250 - 100
  });

  /**
   * Tests creating an ellipse.
   * Verifies that an ellipse can be created on the canvas.
   */
  test('Create an ellipse', async ({ page }) => {
    // Select the ellipse tool
    await page.click('.tool-button[data-tool="ellipse"]');
    
    // Draw an ellipse on the canvas
    await page.mouse.move(200, 200);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Verify that the ellipse was created
    const ellipse = await page.locator('ellipse');
    await expect(ellipse).toBeVisible();
    
    // Verify ellipse attributes
    const cx = await ellipse.getAttribute('cx');
    const cy = await ellipse.getAttribute('cy');
    const rx = await ellipse.getAttribute('rx');
    const ry = await ellipse.getAttribute('ry');
    
    expect(parseInt(cx)).toBe(200);
    expect(parseInt(cy)).toBe(200);
    expect(parseInt(rx)).toBe(100); // (300 - 200) / 2
    expect(parseInt(ry)).toBe(50); // (250 - 200) / 2
  });

  /**
   * Tests creating a polygon.
   * Verifies that a polygon can be created on the canvas.
   */
  test('Create a polygon', async ({ page }) => {
    // Select the polygon tool
    await page.click('.tool-button[data-tool="polygon"]');
    
    // Draw a polygon on the canvas
    await page.mouse.move(100, 100);
    await page.mouse.click();
    
    await page.mouse.move(200, 100);
    await page.mouse.click();
    
    await page.mouse.move(150, 200);
    await page.mouse.click();
    
    // Double-click to finish the polygon
    await page.mouse.move(100, 100);
    await page.mouse.dblclick();
    
    // Verify that the polygon was created
    const polygon = await page.locator('polygon');
    await expect(polygon).toBeVisible();
    
    // Verify polygon attributes
    const points = await polygon.getAttribute('points');
    const pointArray = points.split(' ');
    
    expect(pointArray.length).toBe(3); // 3 points
    expect(pointArray[0]).toBe('100,100');
    expect(pointArray[1]).toBe('200,100');
    expect(pointArray[2]).toBe('150,200');
  });

  /**
   * Tests selecting and moving a shape.
   * Verifies that a shape can be selected and moved on the canvas.
   */
  test('Select and move a shape', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Select the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Click on the rectangle to select it
    await page.mouse.move(200, 175);
    await page.mouse.click();
    
    // Verify that the rectangle is selected
    const selectedRectangle = await page.locator('rect.selected');
    await expect(selectedRectangle).toBeVisible();
    
    // Move the rectangle
    await page.mouse.move(200, 175);
    await page.mouse.down();
    await page.mouse.move(300, 275);
    await page.mouse.up();
    
    // Verify that the rectangle was moved
    const rectangle = await page.locator('rect');
    const x = await rectangle.getAttribute('x');
    const y = await rectangle.getAttribute('y');
    
    expect(parseInt(x)).toBe(200); // 100 + (300 - 200)
    expect(parseInt(y)).toBe(200); // 100 + (275 - 175)
  });

  /**
   * Tests resizing a shape.
   * Verifies that a shape can be resized on the canvas.
   */
  test('Resize a shape', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Select the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Click on the rectangle to select it
    await page.mouse.move(200, 175);
    await page.mouse.click();
    
    // Wait for the resize handles to appear
    await page.waitForSelector('.resize-handle');
    
    // Resize the rectangle using the bottom-right handle
    const bottomRightHandle = await page.locator('.resize-handle.bottom-right');
    const handleBox = await bottomRightHandle.boundingBox();
    
    await page.mouse.move(handleBox.x + handleBox.width / 2, handleBox.y + handleBox.height / 2);
    await page.mouse.down();
    await page.mouse.move(handleBox.x + 100, handleBox.y + 50);
    await page.mouse.up();
    
    // Verify that the rectangle was resized
    const rectangle = await page.locator('rect');
    const width = await rectangle.getAttribute('width');
    const height = await rectangle.getAttribute('height');
    
    expect(parseInt(width)).toBeGreaterThan(200); // Original width
    expect(parseInt(height)).toBeGreaterThan(150); // Original height
  });

  /**
   * Tests rotating a shape.
   * Verifies that a shape can be rotated on the canvas.
   */
  test('Rotate a shape', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Select the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Click on the rectangle to select it
    await page.mouse.move(200, 175);
    await page.mouse.click();
    
    // Wait for the rotation handle to appear
    await page.waitForSelector('.rotation-handle');
    
    // Rotate the rectangle using the rotation handle
    const rotationHandle = await page.locator('.rotation-handle');
    const handleBox = await rotationHandle.boundingBox();
    
    await page.mouse.move(handleBox.x + handleBox.width / 2, handleBox.y + handleBox.height / 2);
    await page.mouse.down();
    await page.mouse.move(handleBox.x + 50, handleBox.y - 50);
    await page.mouse.up();
    
    // Verify that the rectangle was rotated
    const rectangle = await page.locator('rect');
    const transform = await rectangle.getAttribute('transform');
    
    expect(transform).toContain('rotate(');
  });

  /**
   * Tests deleting a shape.
   * Verifies that a shape can be deleted from the canvas.
   */
  test('Delete a shape', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Verify that the rectangle was created
    let rectangle = await page.locator('rect');
    await expect(rectangle).toBeVisible();
    
    // Select the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Click on the rectangle to select it
    await page.mouse.move(200, 175);
    await page.mouse.click();
    
    // Press the delete key
    await page.keyboard.press('Delete');
    
    // Verify that the rectangle was deleted
    rectangle = await page.locator('rect');
    await expect(rectangle).not.toBeVisible();
  });

  /**
   * Tests changing shape properties.
   * Verifies that shape properties can be changed using the properties panel.
   */
  test('Change shape properties', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Select the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Click on the rectangle to select it
    await page.mouse.move(200, 175);
    await page.mouse.click();
    
    // Wait for the properties panel to show the shape properties
    await page.waitForSelector('.properties-panel input[name="fill"]');
    
    // Change the fill color
    await page.fill('.properties-panel input[name="fill"]', '#00ff00');
    await page.press('.properties-panel input[name="fill"]', 'Enter');
    
    // Verify that the rectangle's fill color was changed
    const rectangle = await page.locator('rect');
    const fill = await rectangle.getAttribute('fill');
    
    expect(fill).toBe('#00ff00');
  });

  /**
   * Tests creating a group.
   * Verifies that shapes can be grouped on the canvas.
   */
  test('Create a group', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(200, 200);
    await page.mouse.up();
    
    // Create an ellipse
    await page.click('.tool-button[data-tool="ellipse"]');
    await page.mouse.move(300, 300);
    await page.mouse.down();
    await page.mouse.move(350, 350);
    await page.mouse.up();
    
    // Select the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Select both shapes (using shift key for multi-select)
    await page.mouse.move(150, 150);
    await page.mouse.click();
    
    await page.keyboard.down('Shift');
    await page.mouse.move(325, 325);
    await page.mouse.click();
    await page.keyboard.up('Shift');
    
    // Group the shapes
    await page.keyboard.press('Control+G');
    
    // Verify that a group was created
    const group = await page.locator('g.group');
    await expect(group).toBeVisible();
    
    // Verify that the shapes are in the group
    const rectInGroup = await page.locator('g.group rect');
    const ellipseInGroup = await page.locator('g.group ellipse');
    
    await expect(rectInGroup).toBeVisible();
    await expect(ellipseInGroup).toBeVisible();
  });

  /**
   * Tests ungrouping shapes.
   * Verifies that grouped shapes can be ungrouped on the canvas.
   */
  test('Ungroup shapes', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(200, 200);
    await page.mouse.up();
    
    // Create an ellipse
    await page.click('.tool-button[data-tool="ellipse"]');
    await page.mouse.move(300, 300);
    await page.mouse.down();
    await page.mouse.move(350, 350);
    await page.mouse.up();
    
    // Select the select tool
    await page.click('.tool-button[data-tool="select"]');
    
    // Select both shapes (using shift key for multi-select)
    await page.mouse.move(150, 150);
    await page.mouse.click();
    
    await page.keyboard.down('Shift');
    await page.mouse.move(325, 325);
    await page.mouse.click();
    await page.keyboard.up('Shift');
    
    // Group the shapes
    await page.keyboard.press('Control+G');
    
    // Verify that a group was created
    let group = await page.locator('g.group');
    await expect(group).toBeVisible();
    
    // Select the group
    await page.mouse.move(200, 200);
    await page.mouse.click();
    
    // Ungroup the shapes
    await page.keyboard.press('Control+Shift+G');
    
    // Verify that the group was removed
    group = await page.locator('g.group');
    await expect(group).not.toBeVisible();
    
    // Verify that the shapes still exist
    const rectangle = await page.locator('rect');
    const ellipse = await page.locator('ellipse');
    
    await expect(rectangle).toBeVisible();
    await expect(ellipse).toBeVisible();
  });

  /**
   * Tests undo and redo functionality.
   * Verifies that actions can be undone and redone on the canvas.
   */
  test('Undo and redo actions', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Verify that the rectangle was created
    let rectangle = await page.locator('rect');
    await expect(rectangle).toBeVisible();
    
    // Undo the creation
    await page.keyboard.press('Control+Z');
    
    // Verify that the rectangle was removed
    rectangle = await page.locator('rect');
    await expect(rectangle).not.toBeVisible();
    
    // Redo the creation
    await page.keyboard.press('Control+Y');
    
    // Verify that the rectangle was restored
    rectangle = await page.locator('rect');
    await expect(rectangle).toBeVisible();
  });

  /**
   * Tests saving and loading the canvas state.
   * Verifies that the canvas state can be saved and loaded.
   */
  test('Save and load canvas state', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Create an ellipse
    await page.click('.tool-button[data-tool="ellipse"]');
    await page.mouse.move(400, 300);
    await page.mouse.down();
    await page.mouse.move(450, 350);
    await page.mouse.up();
    
    // Verify that the shapes were created
    const rectangle = await page.locator('rect');
    const ellipse = await page.locator('ellipse');
    
    await expect(rectangle).toBeVisible();
    await expect(ellipse).toBeVisible();
    
    // Save the canvas state
    await page.click('.save-button');
    
    // Clear the canvas
    await page.click('.clear-button');
    
    // Verify that the shapes were cleared
    await expect(rectangle).not.toBeVisible();
    await expect(ellipse).not.toBeVisible();
    
    // Load the saved state
    await page.click('.load-button');
    
    // Verify that the shapes were restored
    const restoredRectangle = await page.locator('rect');
    const restoredEllipse = await page.locator('ellipse');
    
    await expect(restoredRectangle).toBeVisible();
    await expect(restoredEllipse).toBeVisible();
  });

  /**
   * Tests exporting the canvas as an image.
   * Verifies that the canvas can be exported as an image.
   */
  test('Export canvas as image', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Click the export button
    await page.click('.export-button');
    
    // Wait for the download dialog
    const downloadPromise = page.waitForEvent('download');
    
    // Click the export as PNG button
    await page.click('.export-png-button');
    
    // Wait for the download to start
    const download = await downloadPromise;
    
    // Verify that the download started
    expect(download.suggestedFilename()).toContain('.png');
  });

  /**
   * Tests importing an image to the canvas.
   * Verifies that an image can be imported to the canvas.
   */
  test('Import image to canvas', async ({ page }) => {
    // Click the import button
    await page.click('.import-button');
    
    // Set up the file input
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('.import-file-input');
    
    const fileChooser = await fileChooserPromise;
    
    // Select a test image file
    await fileChooser.setFiles('./test-assets/test-image.png');
    
    // Wait for the image to be imported
    await page.waitForSelector('image');
    
    // Verify that the image was imported
    const image = await page.locator('image');
    await expect(image).toBeVisible();
  });

  /**
   * Tests the zoom functionality.
   * Verifies that the canvas can be zoomed in and out.
   */
  test('Zoom canvas', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Get the initial transform of the canvas
    const initialTransform = await page.evaluate(() => {
      return document.querySelector('#canvas-container svg').getAttribute('transform') || '';
    });
    
    // Zoom in
    await page.click('.zoom-in-button');
    
    // Get the new transform of the canvas
    const zoomInTransform = await page.evaluate(() => {
      return document.querySelector('#canvas-container svg').getAttribute('transform') || '';
    });
    
    // Verify that the transform changed
    expect(zoomInTransform).not.toBe(initialTransform);
    expect(zoomInTransform).toContain('scale(');
    
    // Zoom out
    await page.click('.zoom-out-button');
    
    // Get the new transform of the canvas
    const zoomOutTransform = await page.evaluate(() => {
      return document.querySelector('#canvas-container svg').getAttribute('transform') || '';
    });
    
    // Verify that the transform changed again
    expect(zoomOutTransform).not.toBe(zoomInTransform);
  });

  /**
   * Tests the pan functionality.
   * Verifies that the canvas can be panned.
   */
  test('Pan canvas', async ({ page }) => {
    // Create a rectangle
    await page.click('.tool-button[data-tool="rectangle"]');
    await page.mouse.move(100, 100);
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    // Get the initial transform of the canvas
    const initialTransform = await page.evaluate(() => {
      return document.querySelector('#canvas-container svg').getAttribute('transform') || '';
    });
    
    // Select the pan tool
    await page.click('.tool-button[data-tool="pan"]');
    
    // Pan the canvas
    await page.mouse.move(200, 200);
    await page.mouse.down();
    await page.mouse.move(300, 300);
    await page.mouse.up();
    
    // Get the new transform of the canvas
    const panTransform = await page.evaluate(() => {
      return document.querySelector('#canvas-container svg').getAttribute('transform') || '';
    });
    
    // Verify that the transform changed
    expect(panTransform).not.toBe(initialTransform);
    expect(panTransform).toContain('translate(');
  });
});
