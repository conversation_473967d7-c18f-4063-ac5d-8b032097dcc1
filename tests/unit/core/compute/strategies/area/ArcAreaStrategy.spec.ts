import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ArcAreaStrategy } from '@/core/compute/strategies/area/ArcAreaStrategy'
import { CoreError } from '@/services/system/error-service'
import { ElementType } from '@/types/core/elementDefinitions'

// Mock the utility functions
vi.mock('@/lib/utils/geometry/areaUtils', () => ({
  calculateArcArea: vi.fn((radius: number, angleRad: number) => {
    // Simple mock implementation: sector area = 0.5 * radius^2 * angle
    return 0.5 * radius * radius * angleRad
  }),
}))

vi.mock('@/lib/utils/math', () => ({
  toRadians: vi.fn((degrees: number) => degrees * Math.PI / 180),
}))

describe('arcAreaStrategy', () => {
  let strategy: ArcAreaStrategy

  beforeEach(() => {
    strategy = new ArcAreaStrategy()
    vi.clearAllMocks()
  })

  describe('constructor and Basic Properties', () => {
    it('should be defined and instantiated', () => {
      expect(strategy).toBeDefined()
      expect(strategy).toBeInstanceOf(ArcAreaStrategy)
    })

    it('should return correct element type', () => {
      expect(strategy.getElementType()).toBe(ElementType.ARC)
    })

    it('should have required methods', () => {
      expect(typeof strategy.calculateArea).toBe('function')
      expect(typeof strategy.getElementType).toBe('function')
    })
  })

  describe('area Calculation', () => {
    it('should calculate area for closed arc correctly', () => {
      const arcElement = {
        id: 'arc-1',
        type: ElementType.ARC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          radius: 10,
          startAngle: 0,
          endAngle: 90,
          closed: true,
        },
      }

      const area = strategy.calculateArea(arcElement as any)

      // Expected: 0.5 * 10^2 * (π/2) = 0.5 * 100 * (π/2) = 25π/2
      expect(area).toBeCloseTo(39.27, 2)
    })

    it('should return 0 for open arc', () => {
      const arcElement = {
        id: 'arc-2',
        type: ElementType.ARC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          radius: 10,
          startAngle: 0,
          endAngle: 90,
          closed: false,
        },
      }

      const area = strategy.calculateArea(arcElement as any)
      expect(area).toBe(0)
    })

    it('should calculate area for full circle arc', () => {
      const arcElement = {
        id: 'arc-full',
        type: ElementType.ARC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          radius: 5,
          startAngle: 0,
          endAngle: 360,
          closed: true,
        },
      }

      const area = strategy.calculateArea(arcElement as any)

      // Expected: 0.5 * 5^2 * 2π = 0.5 * 25 * 2π = 25π
      expect(area).toBeCloseTo(78.54, 2)
    })

    it('should handle negative angle differences', () => {
      const arcElement = {
        id: 'arc-negative',
        type: ElementType.ARC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          radius: 8,
          startAngle: 270,
          endAngle: 90,
          closed: true,
        },
      }

      const area = strategy.calculateArea(arcElement as any)
      expect(area).toBeGreaterThan(0)
    })

    it('should handle small arc angles', () => {
      const arcElement = {
        id: 'arc-small',
        type: ElementType.ARC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          radius: 15,
          startAngle: 0,
          endAngle: 1,
          closed: true,
        },
      }

      const area = strategy.calculateArea(arcElement as any)
      expect(area).toBeGreaterThan(0)
      expect(area).toBeLessThan(1)
    })
  })

  describe('error Handling', () => {
    it('should throw error for non-arc element', () => {
      const rectangleElement = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        properties: {},
      }

      expect(() => strategy.calculateArea(rectangleElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculateArea(rectangleElement as any))
        .toThrow('ArcAreaStrategy can only calculate area for ARC elements')
    })

    it('should throw error for invalid radius', () => {
      const arcElement = {
        id: 'arc-invalid-radius',
        type: ElementType.ARC,
        properties: {
          radius: 0,
          startAngle: 0,
          endAngle: 90,
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(arcElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculateArea(arcElement as any))
        .toThrow('Invalid radius')
    })

    it('should throw error for negative radius', () => {
      const arcElement = {
        id: 'arc-negative-radius',
        type: ElementType.ARC,
        properties: {
          radius: -5,
          startAngle: 0,
          endAngle: 90,
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(arcElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for infinite radius', () => {
      const arcElement = {
        id: 'arc-infinite-radius',
        type: ElementType.ARC,
        properties: {
          radius: Infinity,
          startAngle: 0,
          endAngle: 90,
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(arcElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for NaN radius', () => {
      const arcElement = {
        id: 'arc-nan-radius',
        type: ElementType.ARC,
        properties: {
          radius: Number.NaN,
          startAngle: 0,
          endAngle: 90,
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(arcElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for invalid start angle', () => {
      const arcElement = {
        id: 'arc-invalid-start',
        type: ElementType.ARC,
        properties: {
          radius: 10,
          startAngle: Number.NaN,
          endAngle: 90,
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(arcElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculateArea(arcElement as any))
        .toThrow('Invalid angles')
    })

    it('should throw error for invalid end angle', () => {
      const arcElement = {
        id: 'arc-invalid-end',
        type: ElementType.ARC,
        properties: {
          radius: 10,
          startAngle: 0,
          endAngle: Infinity,
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(arcElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for non-numeric angles', () => {
      const arcElement = {
        id: 'arc-string-angle',
        type: ElementType.ARC,
        properties: {
          radius: 10,
          startAngle: '0' as any,
          endAngle: 90,
          closed: true,
        },
      }

      expect(() => strategy.calculateArea(arcElement as any))
        .toThrow(CoreError)
    })
  })

  describe('edge Cases', () => {
    it('should handle missing closed property (defaults to false)', () => {
      const arcElement = {
        id: 'arc-no-closed',
        type: ElementType.ARC,
        properties: {
          radius: 10,
          startAngle: 0,
          endAngle: 90,
          // closed property missing
        },
      }

      const area = strategy.calculateArea(arcElement as any)
      expect(area).toBe(0)
    })

    it('should handle closed property as false explicitly', () => {
      const arcElement = {
        id: 'arc-explicit-false',
        type: ElementType.ARC,
        properties: {
          radius: 10,
          startAngle: 0,
          endAngle: 90,
          closed: false,
        },
      }

      const area = strategy.calculateArea(arcElement as any)
      expect(area).toBe(0)
    })

    it('should handle very large radius', () => {
      const arcElement = {
        id: 'arc-large-radius',
        type: ElementType.ARC,
        properties: {
          radius: 1000000,
          startAngle: 0,
          endAngle: 1,
          closed: true,
        },
      }

      const area = strategy.calculateArea(arcElement as any)
      expect(area).toBeGreaterThan(0)
      expect(Number.isFinite(area)).toBe(true)
    })

    it('should handle very small radius', () => {
      const arcElement = {
        id: 'arc-small-radius',
        type: ElementType.ARC,
        properties: {
          radius: 0.001,
          startAngle: 0,
          endAngle: 90,
          closed: true,
        },
      }

      const area = strategy.calculateArea(arcElement as any)
      expect(area).toBeGreaterThan(0)
      expect(area).toBeLessThan(0.001)
    })
  })
})
