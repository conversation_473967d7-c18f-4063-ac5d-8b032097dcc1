import type {
  CanvasClickedEvent,
  CanvasMouseDownEvent,
  CanvasMouseMoveEvent,
  CanvasMouseOptions,
  CanvasMouseUpEvent,
} from '@/types/services/events/CanvasEvents'
import { describe, expect, it } from 'vitest'
import { AppEventType } from '@/types/services/events/eventTypes'

describe('canvasEvents', () => {
  describe('canvasMouseOptions', () => {
    it('should define the required properties', () => {
      const options: CanvasMouseOptions = {
        x: 100,
        y: 200,
        button: 0,
        altKey: false,
        ctrlKey: true,
        shiftKey: false,
        metaKey: false,
      }

      expect(options).toHaveProperty('x')
      expect(options).toHaveProperty('y')
      expect(options).toHaveProperty('button')
      expect(options).toHaveProperty('altKey')
      expect(options).toHaveProperty('ctrlKey')
      expect(options).toHaveProperty('shiftKey')
      expect(options).toHaveProperty('metaKey')
      expect(options.x).toBe(100)
      expect(options.y).toBe(200)
      expect(options.button).toBe(0)
      expect(options.altKey).toBe(false)
      expect(options.ctrlKey).toBe(true)
      expect(options.shiftKey).toBe(false)
      expect(options.metaKey).toBe(false)
    })
  })

  describe('canvasClickedEvent', () => {
    it('should define the required properties', () => {
      const options: CanvasMouseOptions = {
        x: 100,
        y: 200,
        button: 0,
        altKey: false,
        ctrlKey: true,
        shiftKey: false,
        metaKey: false,
      }

      const event: CanvasClickedEvent = {
        type: AppEventType.CANVAS_CLICKED,
        payload: options,
      }

      expect(event).toHaveProperty('type')
      expect(event).toHaveProperty('payload')
      expect(event.type).toBe(AppEventType.CANVAS_CLICKED)
      expect(event.payload).toBe(options)
    })
  })

  describe('canvasMouseDownEvent', () => {
    it('should define the required properties', () => {
      const options: CanvasMouseOptions = {
        x: 100,
        y: 200,
        button: 0,
        altKey: false,
        ctrlKey: true,
        shiftKey: false,
        metaKey: false,
      }

      const event: CanvasMouseDownEvent = {
        type: AppEventType.CANVAS_MOUSE_DOWN,
        payload: options,
      }

      expect(event).toHaveProperty('type')
      expect(event).toHaveProperty('payload')
      expect(event.type).toBe(AppEventType.CANVAS_MOUSE_DOWN)
      expect(event.payload).toBe(options)
    })
  })

  describe('canvasMouseMoveEvent', () => {
    it('should define the required properties', () => {
      const event: CanvasMouseMoveEvent = {
        type: AppEventType.CANVAS_MOUSE_MOVE,
        payload: {
          x: 100,
          y: 200,
        },
      }

      expect(event).toHaveProperty('type')
      expect(event).toHaveProperty('payload')
      expect(event.type).toBe(AppEventType.CANVAS_MOUSE_MOVE)
      expect(event.payload).toHaveProperty('x')
      expect(event.payload).toHaveProperty('y')
      expect(event.payload.x).toBe(100)
      expect(event.payload.y).toBe(200)
    })
  })

  describe('canvasMouseUpEvent', () => {
    it('should define the required properties', () => {
      const options: CanvasMouseOptions = {
        x: 100,
        y: 200,
        button: 0,
        altKey: false,
        ctrlKey: true,
        shiftKey: false,
        metaKey: false,
      }

      const event: CanvasMouseUpEvent = {
        type: AppEventType.CANVAS_MOUSE_UP,
        payload: options,
      }

      expect(event).toHaveProperty('type')
      expect(event).toHaveProperty('payload')
      expect(event.type).toBe(AppEventType.CANVAS_MOUSE_UP)
      expect(event.payload).toBe(options)
    })
  })
})
