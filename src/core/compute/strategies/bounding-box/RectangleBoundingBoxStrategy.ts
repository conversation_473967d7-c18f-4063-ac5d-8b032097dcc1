/**
 * Bounding Box Calculation Strategy for Rectangle and Square Elements
 *
 * @remarks
 * This strategy implements the {@link BoundingBoxCalculatorStrategy} for calculating
 * the axis-aligned bounding box of Rectangle ({@link CoreElementType.RECTANGLE})
 * and Square ({@link CoreElementType.SQUARE}) elements.
 *
 * For an axis-aligned rectangle, its bounding box is defined by its `position`
 * (typically top-left corner) and its `width` and `height`.
 * The strategy validates that the element is of the correct type and that its
 * position and dimensions are valid, finite, non-negative numbers.
 *
 * It instantiates a {@link BoundingBoxClass} to represent the result.
 *
 * @module core/compute/strategies/bounding-box/RectangleBoundingBoxStrategy
 * @see {@link Shape.Rectangle}
 * @see {@link Shape.Square}
 * @see {@link BoundingBoxInterface}
 * @see {@link BoundingBoxClass}
 */
import type { BoundingBoxCalculatorStrategy } from '../../../../types/core/compute'
// import type { IPoint } from '../../../../types/core/element/geometry/point'; // Not directly used
import type { BoundingBox as BoundingBoxInterface } from '../../../../types/core/element/geometry/bounding-box'
import type {
  Element,
  Shape,
  ShapeElement,
  // MetadataProperties // Not used
} from '../../../../types/core/elementDefinitions'
// import { ensureCompleteMetadata } from '../../../../lib/utils/element/elementUtils'; // Not used
import { BoundingBoxClass } from '../../../../lib/utils/geometry/BoundingBoxClass'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  // Element, // ShapeElement is used
  ElementType as CoreElementType,
  // MetadataProperties // Not used
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

export class RectangleBoundingBoxStrategy implements BoundingBoxCalculatorStrategy {
  /**
   * Calculates the axis-aligned bounding box of a Rectangle or Square element.
   *
   * @param element - The rectangle or square element, expected to be of type {@link ShapeElement}
   *                  and specifically {@link CoreElementType.RECTANGLE} or {@link CoreElementType.SQUARE},
   *                  conforming to {@link Shape.Rectangle}.
   * @returns A {@link BoundingBoxInterface} object representing the bounding box.
   * @throws {@link CoreError} if the element type is not `RECTANGLE` or `SQUARE`.
   * @throws {@link CoreError} if the element's `position`, `width`, or `height`
   *         are invalid, not finite numbers, or if width/height are negative.
   */
  public calculateBoundingBox(element: Element): BoundingBoxInterface {
    if (element.type !== CoreElementType.RECTANGLE && element.type !== CoreElementType.SQUARE) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `Expected RECTANGLE or SQUARE, got ${element.type} for ID: ${element.id}`,
        undefined,
        { component: 'RectangleBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    const rectElement = element as unknown as Shape.Rectangle // Added unknown for type safety
    const shapeElement = element as ShapeElement
    const width = rectElement.width // Access directly
    const height = rectElement.height // Access directly

    if (shapeElement.position == null || typeof shapeElement.position.x !== 'number' || typeof shapeElement.position.y !== 'number'
      || typeof width !== 'number' || typeof height !== 'number'
      || !Number.isFinite(shapeElement.position.x) || !Number.isFinite(shapeElement.position.y)
      || !Number.isFinite(width) || !Number.isFinite(height)
      || width < 0 || height < 0) { // Allow 0 width/height for a point-like bbox
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid position or dimensions for Rectangle/Square ID: ${element.id}. Position: ${JSON.stringify(shapeElement.position)}, Width: ${width}, Height: ${height}`,
        undefined,
        { component: 'RectangleBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id, metadata: { position: shapeElement.position, width, height } },
      )
    }

    // For an axis-aligned rectangle, its position is typically top-left.
    // If element.position is center, adjust to top-left for BoundingBoxClass constructor.
    // Assuming element.position IS top-left for a RECTANGLE ShapeElement as per ShapeElement.position doc.
    // If it were center, it would be:
    // const topLeftX = element.position.x - width / 2;
    // const topLeftY = element.position.y - height / 2;
    // Using element.position directly as top-left:
    const topLeftX = shapeElement.position.x
    const topLeftY = shapeElement.position.y

    const bbox = new BoundingBoxClass(topLeftX, topLeftY, width, height, `bbox-${element.id}`)

    // BoundingBoxClass constructor handles defaults for ShapeElement properties.
    // Visibility can be copied if needed: bbox.visible = element.visible;
    return bbox
  }

  /**
   * Returns the primary element type this strategy is registered for.
   *
   * @remarks
   * This strategy handles both `RECTANGLE` and `SQUARE` types, as a square
   * is a special case of a rectangle. It is typically registered under
   * {@link CoreElementType.RECTANGLE}.
   *
   * @returns The element type {@link CoreElementType.RECTANGLE}
   */
  public getElementType(): CoreElementType {
    return CoreElementType.RECTANGLE
  }
}
