/**
 * Event Bus Hook
 *
 * This hook provides access to the application event bus. It allows components
 * to subscribe to and publish events in a React-friendly way.
 *
 * @module hooks/core/useEventBus
 */

import type {
  BaseEvent,
  EventSubscriptionOptions,
} from '@/types/services/events'
import { useCallback, useRef } from 'react'
import { appEventBus } from '@/services/core/event-bus'

/**
 * Hook for accessing the application event bus
 *
 * @returns Object with subscribe and publish methods
 */
export function useEventBus() {
  const subscriptionsRef = useRef<Array<{ eventType: string, handler: (event: any) => void }>>([])

  const subscribe = useCallback(<K extends string>(
    eventType: K,
    handler: (event: any) => void,
    options?: EventSubscriptionOptions,
  ) => {
    const unsubscribe = appEventBus.subscribe(eventType, handler, options)

    // Store subscription for cleanup
    subscriptionsRef.current.push({ eventType, handler })

    return () => {
      // Remove from subscriptions list
      subscriptionsRef.current = subscriptionsRef.current.filter(
        sub => !(sub.eventType === eventType && sub.handler === handler),
      )

      // Unsubscribe from event bus
      unsubscribe()
    }
  }, [])

  const publish = useCallback((event: BaseEvent) => {
    appEventBus.publish(event)
  }, [])

  const publishEvent = useCallback(<K extends string>(
    eventType: K,
    payload: any,
  ) => {
    appEventBus.publish({
      type: eventType,
      payload,
    })
  }, [])

  return {
    subscribe,
    publish,
    publishEvent,
  }
}

export default useEventBus
