/**
 * Utility Functions for Line Segments
 *
 * @remarks
 * This module provides utility functions for calculations related to line segments,
 * specifically calculating their length and axis-aligned bounding box.
 * It uses the {@link IPoint} (aliased from `PointData`) type for point representations
 * and {@link BoundingBoxClass} for bounding box results.
 *
 * @module lib/utils/geometry/lineUtils
 */

import type { PointData as IPoint } from '../../../types/core/element/geometry/point' // Use PointData and alias
import { BoundingBoxClass } from './BoundingBoxClass'
import { calculateDistance } from './pointUtils' // Assuming calculateDistance is exported from pointUtils

/**
 * Calculates the length of a line segment defined by two points.
 *
 * @remarks
 * This function delegates to `calculateDistance` from `./pointUtils`.
 *
 * @param p1 - The start {@link IPoint} of the line segment.
 * @param p2 - The end {@link IPoint} of the line segment.
 * @returns The length of the line segment.
 */
export function calculateLineLength(p1: IPoint, p2: IPoint): number {
  return calculateDistance(p1, p2)
}

/**
 * Calculates the axis-aligned bounding box of a line segment defined by two points.
 *
 * @param p1 - The start {@link IPoint} of the line segment.
 * @param p2 - The end {@link IPoint} of the line segment.
 * @returns A {@link BoundingBoxClass} instance. Returns a 0x0 BBox at origin if points are invalid.
 */
export function calculateLineBoundingBox(p1: IPoint, p2: IPoint): BoundingBoxClass {
  if (!p1 || !p2) {
    console.warn('calculateLineBoundingBox: Invalid points provided. Returning 0x0 BBox at origin.')
    return new BoundingBoxClass(0, 0, 0, 0)
  }
  const minX = Math.min(p1.x, p2.x)
  const minY = Math.min(p1.y, p2.y)
  const maxX = Math.max(p1.x, p2.x)
  const maxY = Math.max(p1.y, p2.y)
  return new BoundingBoxClass(minX, minY, maxX - minX, maxY - minY)
}
