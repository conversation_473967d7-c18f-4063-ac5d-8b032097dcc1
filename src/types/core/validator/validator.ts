/**
 * Validator Module Exports
 *
 * @remarks
 * This file serves as the central export point for all validator-related
 * types, interfaces and enums used throughout the application.
 *
 * @module types/core/validator
 * @see {@link ./error-codes} for validation error code definitions
 * @see {@link ./validator-interface} for core validator interfaces
 */

export * from './error-codes'
export * from './validator-interface'
export type { ValidatableShape as ValidatorShape } from './validator-interface' // Add alias
