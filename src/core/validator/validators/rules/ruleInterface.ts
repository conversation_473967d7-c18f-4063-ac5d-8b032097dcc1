/**
 * Defines the core interface for reusable validation logic and a factory function to create rule instances.
 */

import type { ValidationError, ValidatableShape as ValidatorShape } from '@/types/core/validator/validator-interface'

/**
 * Defines the contract for a reusable validation rule that can be applied to shape data.
 *
 * @remarks
 * Each rule encapsulates a specific check (e.g., size limits, point counts, geometric properties).
 * Rules are typically applied within the validation process managed by BaseShapeValidator.
 */
export interface IValidationRule {
  /**
   * A unique identifier for the validation rule (e.g., 'size', 'minSize', 'polygonClosure').
   */
  name: string

  /**
   * Applies the validation rule logic to the given shape.
   *
   * @param shape - The shape object to validate against this rule.
   * @returns An array of ValidationError objects. An empty array signifies that the rule passed.
   */
  apply: (shape: ValidatorShape) => ValidationError[]
}

/**
 * Factory function to create an instance of a validation rule.
 *
 * @param name - The unique name for the rule.
 * @param applyFn - The function that implements the rule's validation logic. It takes a ValidatorShape
 *                  and returns an array of ValidationError objects.
 * @returns An object conforming to the IValidationRule interface.
 */
export function createValidationRule(name: string, applyFn: (shape: ValidatorShape) => ValidationError[]): IValidationRule {
  return {
    name,
    apply: applyFn,
  }
}
