/**
 * Square Geometry Component
 *
 * Implements the standardized geometry layout for Square elements
 * according to the design document specifications.
 *
 * Layout:
 * - Position & Transform (2 columns): Position X, Position Y
 * - Dimensions (1 column): Size (maintains square ratio)
 * - Element Properties (1 column): Corner Radius
 * - Transform (1 column): Rotation
 * - Calculation & Cost (unified layout)
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'

import { ElementType } from '@/types/core/elementDefinitions'
import { GeometrySection, PropertyField } from '../shared'

interface SquareGeometryProps {
  /** The square element being edited */
  element: ShapeElement
  /** Function to get input values with proper formatting */
  getValueForInput: (path: string) => string
  /** Function to get placeholder values */
  getPlaceholderForInput: (path: string, exampleValue?: number | string) => string | undefined
  /** Function to handle input changes */
  handleInputChange: (path: string, value: string, isNumeric?: boolean, convertToPixels?: boolean) => void
  /** Unit name for display (e.g., "mm", "cm") */
  unitName: string
}

/**
 * SquareGeometry component for editing square properties.
 *
 * Provides a standardized layout for square geometry editing
 * with proper grouping and responsive design. Uses a single
 * size control that maintains the square aspect ratio.
 */
export function SquareGeometry({
  element,
  getValueForInput,
  getPlaceholderForInput,
  handleInputChange,
  unitName,
}: SquareGeometryProps) {
  // Verify this is a square element
  if (element.type !== ElementType.SQUARE) {
    return null
  }

  // Handle size change - update both width and height to maintain square ratio
  const handleSizeChange = (value: string) => {
    handleInputChange('width', value)
    handleInputChange('height', value)
  }

  return (
    <>
      {/* Position (2 columns) */}
      <GeometrySection type="position" columns={2}>
        <PropertyField
          label={`Position X (${unitName})`}
          type="number"
          value={getValueForInput('position.x')}
          onChange={value => handleInputChange('position.x', value)}
          placeholder={getPlaceholderForInput('position.x')}
        />
        <PropertyField
          label={`Position Y (${unitName})`}
          type="number"
          value={getValueForInput('position.y')}
          onChange={value => handleInputChange('position.y', value)}
          placeholder={getPlaceholderForInput('position.y')}
        />
      </GeometrySection>

      {/* Scale (1 column) */}
      <GeometrySection type="dimensions" columns={1}>
        <PropertyField
          label={`Size (${unitName})`}
          type="number"
          value={getValueForInput('width')} // Use width as the size reference
          onChange={handleSizeChange}
          placeholder="0"
          helpText="Maintains square proportions"
        />
      </GeometrySection>

      {/* Transform (1 column) */}
      <GeometrySection type="transform" columns={1}>
        <PropertyField
          label={`Corner Radius (${unitName})`}
          type="number"
          value={getValueForInput('cornerRadius')}
          onChange={value => handleInputChange('cornerRadius', value)}
          placeholder="0"
        />
        <PropertyField
          label="Rotation (°)"
          type="number"
          value={getValueForInput('rotation')}
          onChange={value => handleInputChange('rotation', value, true, false)}
          placeholder="0"
        />
      </GeometrySection>
    </>
  )
}
