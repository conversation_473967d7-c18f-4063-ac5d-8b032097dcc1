import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { AppEventBusImpl } from '@/services/core/event-bus/appEventBus'
import { setupEventHandlers } from '@/services/core/event-bus/handlers/eventHandlers'
import { AppEventType } from '@/types/services/events'

// Mock AppEventBusImpl
vi.mock('@/services/core/event-bus/appEventBus', () => {
  const mockEventBus = {
    subscribe: vi.fn(),
    publish: vi.fn(),
    unsubscribe: vi.fn(),
    clear: vi.fn(),
  }

  return {
    AppEventBusImpl: vi.fn(() => mockEventBus),
  }
})

describe('event Handlers', () => {
  let mockEventBus: any

  beforeEach(() => {
    vi.clearAllMocks()
    // Get the mocked event bus instance
    mockEventBus = new AppEventBusImpl()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('setupEventHandlers', () => {
    it('should set up all event handlers', () => {
      setupEventHandlers()

      // Verify that subscribe was called for all expected event types
      const expectedEventTypes = [
        // Canvas events
        AppEventType.CanvasCleared,
        AppEventType.CanvasResized,
        AppEventType.CanvasMouseMove,

        // Tool events
        AppEventType.ToolChanged,

        // History events
        AppEventType.HistoryCheckpoint,
        AppEventType.HistoryUndo,
        AppEventType.HistoryRedo,

        // Layer events
        AppEventType.LayerVisibilityChange,
        AppEventType.LayerLockChange,
        AppEventType.LayerOrderChange,

        // File events
        AppEventType.FileImported,
        AppEventType.FileExported,

        // View events
        AppEventType.ViewZoomed,
        AppEventType.ViewPanned,

        // Template events
        AppEventType.TemplateApply,

        // Notification events
        AppEventType.NotificationAdd,
        AppEventType.ToastShow,

        // Sidebar events
        AppEventType.SidebarLeftToggle,
        AppEventType.SidebarRightToggle,
      ]

      expect(mockEventBus.subscribe).toHaveBeenCalledTimes(expectedEventTypes.length)

      // Verify each event type was subscribed to
      expectedEventTypes.forEach((eventType) => {
        expect(mockEventBus.subscribe).toHaveBeenCalledWith(
          eventType,
          expect.any(Function),
        )
      })
    })

    it('should register handlers with correct function signatures', () => {
      setupEventHandlers()

      // Get all the subscribe calls
      const subscribeCalls = mockEventBus.subscribe.mock.calls

      // Verify each handler is a function
      subscribeCalls.forEach(([eventType, handler]) => {
        expect(typeof handler).toBe('function')
        expect(eventType).toBeTypeOf('string')
      })
    })

    it('should handle multiple calls to setupEventHandlers', () => {
      setupEventHandlers()
      const firstCallCount = mockEventBus.subscribe.mock.calls.length

      setupEventHandlers()
      const secondCallCount = mockEventBus.subscribe.mock.calls.length

      // Should register handlers again
      expect(secondCallCount).toBe(firstCallCount * 2)
    })
  })

  describe('event handler functionality', () => {
    beforeEach(() => {
      setupEventHandlers()
    })

    it('should register canvas event handlers', () => {
      const canvasEventTypes = [
        AppEventType.CanvasCleared,
        AppEventType.CanvasResized,
        AppEventType.CanvasMouseMove,
      ]

      canvasEventTypes.forEach((eventType) => {
        expect(mockEventBus.subscribe).toHaveBeenCalledWith(
          eventType,
          expect.any(Function),
        )
      })
    })

    it('should register tool event handlers', () => {
      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.ToolChanged,
        expect.any(Function),
      )
    })

    it('should register history event handlers', () => {
      const historyEventTypes = [
        AppEventType.HistoryCheckpoint,
        AppEventType.HistoryUndo,
        AppEventType.HistoryRedo,
      ]

      historyEventTypes.forEach((eventType) => {
        expect(mockEventBus.subscribe).toHaveBeenCalledWith(
          eventType,
          expect.any(Function),
        )
      })
    })

    it('should register layer event handlers', () => {
      const layerEventTypes = [
        AppEventType.LayerVisibilityChange,
        AppEventType.LayerLockChange,
        AppEventType.LayerOrderChange,
      ]

      layerEventTypes.forEach((eventType) => {
        expect(mockEventBus.subscribe).toHaveBeenCalledWith(
          eventType,
          expect.any(Function),
        )
      })
    })

    it('should register file event handlers', () => {
      const fileEventTypes = [
        AppEventType.FileImported,
        AppEventType.FileExported,
      ]

      fileEventTypes.forEach((eventType) => {
        expect(mockEventBus.subscribe).toHaveBeenCalledWith(
          eventType,
          expect.any(Function),
        )
      })
    })

    it('should register view event handlers', () => {
      const viewEventTypes = [
        AppEventType.ViewZoomed,
        AppEventType.ViewPanned,
      ]

      viewEventTypes.forEach((eventType) => {
        expect(mockEventBus.subscribe).toHaveBeenCalledWith(
          eventType,
          expect.any(Function),
        )
      })
    })

    it('should register template event handlers', () => {
      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.TemplateApply,
        expect.any(Function),
      )
    })

    it('should register notification event handlers', () => {
      const notificationEventTypes = [
        AppEventType.NotificationAdd,
        AppEventType.ToastShow,
      ]

      notificationEventTypes.forEach((eventType) => {
        expect(mockEventBus.subscribe).toHaveBeenCalledWith(
          eventType,
          expect.any(Function),
        )
      })
    })

    it('should register sidebar event handlers', () => {
      const sidebarEventTypes = [
        AppEventType.SidebarLeftToggle,
        AppEventType.SidebarRightToggle,
      ]

      sidebarEventTypes.forEach((eventType) => {
        expect(mockEventBus.subscribe).toHaveBeenCalledWith(
          eventType,
          expect.any(Function),
        )
      })
    })
  })

  describe('handler execution', () => {
    beforeEach(() => {
      setupEventHandlers()
    })

    it('should execute handlers without throwing errors', () => {
      // Get all registered handlers
      const subscribeCalls = mockEventBus.subscribe.mock.calls

      subscribeCalls.forEach(([eventType, handler]) => {
        const mockEvent = {
          type: eventType,
          timestamp: Date.now(),
          payload: getDefaultPayloadForEventType(eventType),
        }

        // Handler should not throw when called
        expect(() => handler(mockEvent)).not.toThrow()
      })
    })

    it('should handle events with empty payload', () => {
      const subscribeCalls = mockEventBus.subscribe.mock.calls

      subscribeCalls.forEach(([eventType, handler]) => {
        const mockEvent = {
          type: eventType,
          timestamp: Date.now(),
          payload: {}, // Empty payload instead of missing
        }

        // Handler should not throw with empty payload
        expect(() => handler(mockEvent)).not.toThrow()
      })
    })

    it('should handle events with valid structure', () => {
      const subscribeCalls = mockEventBus.subscribe.mock.calls

      subscribeCalls.forEach(([eventType, handler]) => {
        const mockEvent = {
          type: eventType,
          timestamp: Date.now(),
          payload: getDefaultPayloadForEventType(eventType),
        }

        // Handler should not throw with valid event structure
        expect(() => handler(mockEvent)).not.toThrow()
      })
    })
  })

  // Helper function to get default payload for event types
  function getDefaultPayloadForEventType(eventType: string): any {
    switch (eventType) {
      case AppEventType.CanvasCleared:
        return { canvasId: 'test-canvas' }
      case AppEventType.CanvasResized:
        return { width: 800, height: 600 }
      case AppEventType.CanvasMouseMove:
        return { x: 100, y: 200 }
      case AppEventType.ToolChanged:
        return { tool: 'rectangle' }
      case AppEventType.HistoryCheckpoint:
        return { action: 'test_action' }
      case AppEventType.HistoryUndo:
      case AppEventType.HistoryRedo:
        return {}
      case AppEventType.LayerVisibilityChange:
        return { layerId: 'layer-1', visible: true }
      case AppEventType.LayerLockChange:
        return { layerId: 'layer-1', locked: false }
      case AppEventType.LayerOrderChange:
        return { layerId: 'layer-1', newOrder: 1 }
      case AppEventType.FileImported:
      case AppEventType.FileExported:
        return { filename: 'test.json' }
      case AppEventType.ViewZoomed:
        return { scale: 1.5 }
      case AppEventType.ViewPanned:
        return { x: 100, y: 200 }
      case AppEventType.TemplateApply:
        return { templateId: 'template-1' }
      case AppEventType.NotificationAdd:
        return { message: 'Test notification', type: 'info' }
      case AppEventType.ToastShow:
        return { message: 'Test toast', type: 'info', duration: 3000 }
      case AppEventType.SidebarLeftToggle:
      case AppEventType.SidebarRightToggle:
        return { visible: true }
      default:
        return {}
    }
  }
})
