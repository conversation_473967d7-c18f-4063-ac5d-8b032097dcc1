/**
 * Core Error Implementation
 *
 * This file implements the core error types defined in types/services/errors.
 */

import type {
  ErrorContext,
  ErrorType,
  ICoreError,
} from '@/types/services/errors'
import {
  ErrorSeverity,
} from '@/types/services/errors'

/**
 * Represents a standardized core error within the application.
 *
 * @remarks
 * This class extends the native `Error` and implements the {@link ICoreError}
 * interface, providing structured properties for error type, severity, and context.
 *
 * @see {@link ErrorType}
 * @see {@link ErrorSeverity}
 * @see {@link ErrorContext}
 * @see {@link ICoreError}
 */
export class CoreError extends Error implements ICoreError {
  public readonly type: ErrorType
  public readonly severity: ErrorSeverity
  public readonly context?: ErrorContext
  public name: string // Explicitly declare name
  /**
   * Creates an instance of CoreError.
   *
   * @param {ErrorType} type - The type of the error (e.g., VALIDATION, RUNTIME).
   * @param {string} message - A human-readable description of the error.
   * @param {ErrorSeverity} [severity] - The severity of the error.
   * @param {ErrorContext} [context] - Optional contextual information about the error.
   */
  constructor(
    type: ErrorType, // Remove public here as it's declared above
    message: string,
    severity: ErrorSeverity = ErrorSeverity.Medium, // Remove public here, use PascalCase for enum member
    context?: ErrorContext, // Remove public here
  ) {
    super(message)
    this.type = type
    this.severity = severity
    this.context = context
    this.name = 'CoreError' // Standardize the error name
    // Ensure prototype chain is set correctly for extending Error
    Object.setPrototypeOf(this, CoreError.prototype)
  }
}

/**
 * Factory function to create a new {@link CoreError} instance.
 *
 * @param {ErrorType} type - The type of the error.
 * @param {string} message - A human-readable description of the error.
 * @param {ErrorSeverity} [severity] - The severity of the error.
 * @param {ErrorContext} [context] - Optional contextual information about the error.
 * @returns {CoreError} A new CoreError instance.
 */
export function createError(
  type: ErrorType,
  message: string,
  severity?: ErrorSeverity,
  context?: ErrorContext,
): CoreError {
  return new CoreError(type, message, severity, context)
}
