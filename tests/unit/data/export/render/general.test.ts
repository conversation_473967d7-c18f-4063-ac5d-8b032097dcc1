import { describe, it, expect } from 'vitest';
import { getShapeStyle, getTransform } from '@/data/export/render/general';

describe('getShapeStyle', () => {
  it('should return default style if no style provided', () => {
    const shape = {};
    expect(getShapeStyle(shape as any)).toEqual({
      fill: '#ccc',
      stroke: '#333',
      strokeWidth: 1,
    });
  });

  it('should return provided style values', () => {
    const shape = { fill: '#fff', stroke: '#000', strokeWidth: 5 };
    expect(getShapeStyle(shape as any)).toEqual({
      fill: '#fff',
      stroke: '#000',
      strokeWidth: 5,
    });
  });

  it('should fallback to default for missing fields', () => {
    const shape = { fill: '#abc' };
    expect(getShapeStyle(shape as any)).toEqual({
      fill: '#abc',
      stroke: '#333',
      strokeWidth: 1,
    });
  });
});

describe('getTransform', () => {
  it('should return empty string if rotation is falsy', () => {
    expect(getTransform(0, { x: 10, y: 20 })).toBe('');
    expect(getTransform(undefined as any, { x: 10, y: 20 })).toBe('');
    expect(getTransform(null as any, { x: 10, y: 20 })).toBe('');
  });

  it('should return correct transform string if rotation is provided', () => {
    expect(getTransform(45, { x: 10, y: 20 })).toBe(' transform="rotate(45,10,20)"');
    expect(getTransform(-30, { x: 0, y: 0 })).toBe(' transform="rotate(-30,0,0)"');
  });
});