import type { Polyline } from '@/types/core/element/path/polylinePathTypes'
import type { ShapeElement } from '@/types/core/elementDefinitions'

import { describe, expect, it } from 'vitest'

describe('polyline Interface', () => {
  it('should define a polyline with required properties', () => {
    // Create a valid Polyline object
    const polyline: Polyline = {
      id: 'polyline-1',
      type: 'shape',

      points: [
        { x: 10, y: 20 },
        { x: 50, y: 50 },
        { x: 100, y: 20 },
      ],
      curved: false,
      tension: 0.5,
    }

    // Verify the object is valid
    expect(polyline.id).toBe('polyline-1')
    expect(polyline.type).toBe('shape')

    expect(polyline.points.length).toBe(3)
    expect(polyline.curved).toBe(false)
    expect(polyline.tension).toBe(0.5)
  })

  it('should extend ShapeElement interface', () => {
    // Create a polyline
    const polyline: Polyline = {
      id: 'polyline-2',
      type: 'shape',

      points: [
        { x: 0, y: 0 },
        { x: 50, y: 50 },
      ],
      curved: false,
      tension: 0.5,
    }

    // Verify the polyline extends ShapeElement
    const shapeElement: ShapeElement = polyline
    expect(shapeElement.id).toBe('polyline-2')
    expect(shapeElement.type).toBe('shape')
  })

  it('should allow creating polylines with different point values', () => {
    // Create polylines with different values
    const straightLine: Polyline = {
      id: 'straight',
      type: 'shape',

      points: [
        { x: 0, y: 0 },
        { x: 100, y: 100 },
      ],
      curved: false,
      tension: 0.5,
    }

    const zigzagLine: Polyline = {
      id: 'zigzag',
      type: 'shape',

      points: [
        { x: 0, y: 0 },
        { x: 50, y: 50 },
        { x: 100, y: 0 },
        { x: 150, y: 50 },
      ],
      curved: false,
      tension: 0.5,
    }

    const curvedLine: Polyline = {
      id: 'curved',
      type: 'shape',

      points: [
        { x: 0, y: 0 },
        { x: 50, y: 50 },
        { x: 100, y: 0 },
      ],
      curved: true,
      tension: 0.7,
    }

    // Verify all polylines are valid
    expect(straightLine.points.length).toBe(2)
    expect(straightLine.curved).toBe(false)

    expect(zigzagLine.points.length).toBe(4)
    expect(zigzagLine.curved).toBe(false)

    expect(curvedLine.points.length).toBe(3)
    expect(curvedLine.curved).toBe(true)
    expect(curvedLine.tension).toBe(0.7)
  })

  it('should be usable in arrays and collections', () => {
    // Create an array of polylines
    const polylines: Polyline[] = [
      {
        id: 'polyline-3',
        type: 'shape',

        points: [
          { x: 0, y: 0 },
          { x: 100, y: 100 },
        ],
        curved: false,
        tension: 0.5,
      },
      {
        id: 'polyline-4',
        type: 'shape',

        points: [
          { x: 0, y: 0 },
          { x: 50, y: 50 },
          { x: 100, y: 0 },
        ],
        curved: true,
        tension: 0.8,
      },
    ]

    // Verify the array is valid
    expect(polylines.length).toBe(2)
    expect(polylines[0].id).toBe('polyline-3')
    expect(polylines[0].points.length).toBe(2)
    expect(polylines[0].curved).toBe(false)

    expect(polylines[1].id).toBe('polyline-4')
    expect(polylines[1].points.length).toBe(3)
    expect(polylines[1].curved).toBe(true)
    expect(polylines[1].tension).toBe(0.8)
  })

  it('should be usable in functions that require Polyline', () => {
    // Define a function that uses Polyline
    function calculatePolylineLength(polyline: Polyline): number {
      let length = 0
      for (let i = 0; i < polyline.points.length - 1; i++) {
        const p1 = polyline.points[i]
        const p2 = polyline.points[i + 1]
        const dx = p2.x - p1.x
        const dy = p2.y - p1.y
        length += Math.sqrt(dx * dx + dy * dy)
      }
      return length
    }

    // Create a polyline
    const polyline: Polyline = {
      id: 'polyline-5',
      type: 'shape',

      points: [
        { x: 0, y: 0 },
        { x: 3, y: 0 },
        { x: 3, y: 4 },
      ],
      curved: false,
      tension: 0.5,
    }

    // Test the function
    const length = calculatePolylineLength(polyline)
    expect(length).toBe(7) // 3 + 4 = 7
  })

  it('should be compatible with JSON serialization/deserialization', () => {
    // Create a polyline
    const polyline: Polyline = {
      id: 'polyline-6',
      type: 'shape',

      points: [
        { x: 10, y: 20 },
        { x: 30, y: 40 },
        { x: 50, y: 20 },
      ],
      curved: true,
      tension: 0.6,
    }

    // Serialize to JSON and deserialize
    const serialized = JSON.stringify(polyline)
    const deserialized = JSON.parse(serialized) as Polyline

    // Verify the deserialized object is valid
    expect(deserialized.id).toBe('polyline-6')
    expect(deserialized.type).toBe('shape')

    expect(deserialized.points.length).toBe(3)
    expect(deserialized.points[0].x).toBe(10)
    expect(deserialized.points[0].y).toBe(20)
    expect(deserialized.points[1].x).toBe(30)
    expect(deserialized.points[1].y).toBe(40)
    expect(deserialized.points[2].x).toBe(50)
    expect(deserialized.points[2].y).toBe(20)
    expect(deserialized.curved).toBe(true)
    expect(deserialized.tension).toBe(0.6)
  })

  it('should enforce minimum of 2 points for a valid polyline', () => {
    // Define a type guard function to check if a polyline is valid
    function isValidPolyline(polyline: Polyline): boolean {
      return polyline.points.length >= 2
    }

    // Create polylines with different numbers of points
    const validPolyline: Polyline = {
      id: 'valid',
      type: 'shape',

      points: [
        { x: 0, y: 0 },
        { x: 100, y: 100 },
      ],
      curved: false,
      tension: 0.5,
    }

    const invalidPolyline: Polyline = {
      id: 'invalid',
      type: 'shape',

      points: [
        { x: 0, y: 0 },
      ],
      curved: false,
      tension: 0.5,
    }

    // Test the validation function
    expect(isValidPolyline(validPolyline)).toBe(true)
    expect(isValidPolyline(invalidPolyline)).toBe(false)
  })
})
