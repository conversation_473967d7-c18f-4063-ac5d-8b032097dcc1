import type { EventBus } from '@/types/services/events'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import {
  publishNotification,
  publishToastNotification,
  publishToolChanged,
  publishViewPanned,
  publishViewZoomed,
} from '@/services/core/event-bus/helpers/publishers/uiPublishers'
import { AppEventType } from '@/types/services/events'

describe('uI Publishers', () => {
  let mockEventBus: EventBus
  let publishSpy: ReturnType<typeof vi.fn>

  beforeEach(() => {
    publishSpy = vi.fn()
    mockEventBus = {
      publish: publishSpy,
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
      clear: vi.fn(),
    } as unknown as EventBus
  })

  describe('publishViewZoomed', () => {
    it('should publish view zoomed event with correct structure', () => {
      const scale = 1.5

      publishViewZoomed(mockEventBus, scale)

      expect(publishSpy).toHaveBeenCalledTimes(1)
      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ViewZoomed,
        payload: { scale },
      })
    })

    it('should handle different zoom scales', () => {
      const scales = [0.5, 1.0, 1.5, 2.0, 5.0]

      scales.forEach((scale) => {
        publishViewZoomed(mockEventBus, scale)
      })

      expect(publishSpy).toHaveBeenCalledTimes(5)
      scales.forEach((scale, index) => {
        expect(publishSpy.mock.calls[index][0]).toEqual({
          type: AppEventType.ViewZoomed,
          payload: { scale },
        })
      })
    })

    it('should handle zero scale', () => {
      publishViewZoomed(mockEventBus, 0)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ViewZoomed,
        payload: { scale: 0 },
      })
    })

    it('should handle negative scale', () => {
      publishViewZoomed(mockEventBus, -1)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ViewZoomed,
        payload: { scale: -1 },
      })
    })

    it('should handle very small scale values', () => {
      const smallScale = 0.001

      publishViewZoomed(mockEventBus, smallScale)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ViewZoomed,
        payload: { scale: smallScale },
      })
    })

    it('should handle very large scale values', () => {
      const largeScale = 1000

      publishViewZoomed(mockEventBus, largeScale)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ViewZoomed,
        payload: { scale: largeScale },
      })
    })
  })

  describe('publishViewPanned', () => {
    it('should publish view panned event with correct structure', () => {
      const options = { x: 100, y: 200 }

      publishViewPanned(mockEventBus, options)

      expect(publishSpy).toHaveBeenCalledTimes(1)
      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ViewPanned,
        payload: { x: 100, y: 200 },
      })
    })

    it('should handle different coordinate values', () => {
      const coordinates = [
        { x: 0, y: 0 },
        { x: -100, y: -200 },
        { x: 500, y: 300 },
        { x: 1.5, y: 2.7 },
      ]

      coordinates.forEach((coords) => {
        publishViewPanned(mockEventBus, coords)
      })

      expect(publishSpy).toHaveBeenCalledTimes(4)
      coordinates.forEach((coords, index) => {
        expect(publishSpy.mock.calls[index][0]).toEqual({
          type: AppEventType.ViewPanned,
          payload: { x: coords.x, y: coords.y },
        })
      })
    })

    it('should handle very large coordinate values', () => {
      const largeCoords = { x: 999999, y: -999999 }

      publishViewPanned(mockEventBus, largeCoords)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ViewPanned,
        payload: { x: 999999, y: -999999 },
      })
    })

    it('should handle decimal coordinate values', () => {
      const decimalCoords = { x: 123.456, y: -789.012 }

      publishViewPanned(mockEventBus, decimalCoords)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ViewPanned,
        payload: { x: 123.456, y: -789.012 },
      })
    })
  })

  describe('publishToolChanged', () => {
    it('should publish tool changed event with correct structure', () => {
      const tool = 'rectangle'

      publishToolChanged(mockEventBus, tool)

      expect(publishSpy).toHaveBeenCalledTimes(1)
      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ToolChanged,
        payload: { tool },
      })
    })

    it('should handle different tool types', () => {
      const tools = ['rectangle', 'circle', 'line', 'text', 'select', 'pan', 'zoom']

      tools.forEach((tool) => {
        publishToolChanged(mockEventBus, tool)
      })

      expect(publishSpy).toHaveBeenCalledTimes(7)
      tools.forEach((tool, index) => {
        expect(publishSpy.mock.calls[index][0]).toEqual({
          type: AppEventType.ToolChanged,
          payload: { tool },
        })
      })
    })

    it('should handle empty tool string', () => {
      publishToolChanged(mockEventBus, '')

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ToolChanged,
        payload: { tool: '' },
      })
    })

    it('should handle tool names with special characters', () => {
      const specialTool = 'custom-tool_v2.0@beta'

      publishToolChanged(mockEventBus, specialTool)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ToolChanged,
        payload: { tool: specialTool },
      })
    })

    it('should handle unicode tool names', () => {
      const unicodeTool = '工具-🔧-αβγ'

      publishToolChanged(mockEventBus, unicodeTool)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ToolChanged,
        payload: { tool: unicodeTool },
      })
    })
  })

  describe('publishToastNotification', () => {
    it('should publish toast notification with default values', () => {
      const message = 'Test message'

      publishToastNotification(mockEventBus, message)

      expect(publishSpy).toHaveBeenCalledTimes(1)
      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ToastShow,
        payload: {
          message,
          type: 'info',
          duration: 3000,
        },
      })
    })

    it('should publish toast notification with custom type', () => {
      const message = 'Success message'
      const type = 'success'

      publishToastNotification(mockEventBus, message, type)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ToastShow,
        payload: {
          message,
          type,
          duration: 3000,
        },
      })
    })

    it('should publish toast notification with custom duration', () => {
      const message = 'Custom duration message'
      const type = 'warning'
      const duration = 5000

      publishToastNotification(mockEventBus, message, type, duration)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ToastShow,
        payload: {
          message,
          type,
          duration,
        },
      })
    })

    it('should handle all toast types', () => {
      const types: Array<'info' | 'success' | 'warning' | 'error'> = ['info', 'success', 'warning', 'error']

      types.forEach((type) => {
        publishToastNotification(mockEventBus, `${type} message`, type)
      })

      expect(publishSpy).toHaveBeenCalledTimes(4)
      types.forEach((type, index) => {
        expect(publishSpy.mock.calls[index][0]).toEqual({
          type: AppEventType.ToastShow,
          payload: {
            message: `${type} message`,
            type,
            duration: 3000,
          },
        })
      })
    })

    it('should handle empty message', () => {
      publishToastNotification(mockEventBus, '')

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ToastShow,
        payload: {
          message: '',
          type: 'info',
          duration: 3000,
        },
      })
    })

    it('should handle very long messages', () => {
      const longMessage = 'A'.repeat(1000)

      publishToastNotification(mockEventBus, longMessage)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ToastShow,
        payload: {
          message: longMessage,
          type: 'info',
          duration: 3000,
        },
      })
    })

    it('should handle zero duration', () => {
      publishToastNotification(mockEventBus, 'Test', 'info', 0)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ToastShow,
        payload: {
          message: 'Test',
          type: 'info',
          duration: 0,
        },
      })
    })

    it('should handle negative duration', () => {
      publishToastNotification(mockEventBus, 'Test', 'info', -1000)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.ToastShow,
        payload: {
          message: 'Test',
          type: 'info',
          duration: -1000,
        },
      })
    })
  })

  describe('publishNotification', () => {
    it('should publish notification with default type', () => {
      const message = 'Test notification'

      publishNotification(mockEventBus, message)

      expect(publishSpy).toHaveBeenCalledTimes(1)
      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.NotificationAdd,
        payload: {
          message,
          type: 'info',
        },
      })
    })

    it('should publish notification with custom type', () => {
      const message = 'Error notification'
      const type = 'error'

      publishNotification(mockEventBus, message, type)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.NotificationAdd,
        payload: {
          message,
          type,
        },
      })
    })

    it('should handle all notification types', () => {
      const types: Array<'info' | 'success' | 'warning' | 'error'> = ['info', 'success', 'warning', 'error']

      types.forEach((type) => {
        publishNotification(mockEventBus, `${type} notification`, type)
      })

      expect(publishSpy).toHaveBeenCalledTimes(4)
      types.forEach((type, index) => {
        expect(publishSpy.mock.calls[index][0]).toEqual({
          type: AppEventType.NotificationAdd,
          payload: {
            message: `${type} notification`,
            type,
          },
        })
      })
    })

    it('should handle empty message', () => {
      publishNotification(mockEventBus, '')

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.NotificationAdd,
        payload: {
          message: '',
          type: 'info',
        },
      })
    })

    it('should handle messages with special characters', () => {
      const specialMessage = 'Message with @#$%^&*()_+-=[]{}|;:,.<>?'

      publishNotification(mockEventBus, specialMessage)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.NotificationAdd,
        payload: {
          message: specialMessage,
          type: 'info',
        },
      })
    })

    it('should handle unicode messages', () => {
      const unicodeMessage = '通知消息 🚀 αβγ'

      publishNotification(mockEventBus, unicodeMessage)

      expect(publishSpy).toHaveBeenCalledWith({
        type: AppEventType.NotificationAdd,
        payload: {
          message: unicodeMessage,
          type: 'info',
        },
      })
    })
  })

  describe('error handling', () => {
    it('should propagate eventBus errors for all publishers', () => {
      const error = new Error('EventBus publish failed')
      publishSpy.mockImplementation(() => {
        throw error
      })

      expect(() => publishViewZoomed(mockEventBus, 1.0)).toThrow(error)
      expect(() => publishViewPanned(mockEventBus, { x: 0, y: 0 })).toThrow(error)
      expect(() => publishToolChanged(mockEventBus, 'tool')).toThrow(error)
      expect(() => publishToastNotification(mockEventBus, 'message')).toThrow(error)
      expect(() => publishNotification(mockEventBus, 'message')).toThrow(error)
    })

    it('should handle eventBus returning false', () => {
      publishSpy.mockReturnValue(false)

      // These should not throw even if eventBus returns false
      expect(() => publishViewZoomed(mockEventBus, 1.0)).not.toThrow()
      expect(() => publishViewPanned(mockEventBus, { x: 0, y: 0 })).not.toThrow()
      expect(() => publishToolChanged(mockEventBus, 'tool')).not.toThrow()
      expect(() => publishToastNotification(mockEventBus, 'message')).not.toThrow()
      expect(() => publishNotification(mockEventBus, 'message')).not.toThrow()
    })
  })

  describe('edge cases', () => {
    it('should handle rapid successive calls', () => {
      for (let i = 0; i < 50; i++) {
        publishViewZoomed(mockEventBus, i * 0.1)
      }

      expect(publishSpy).toHaveBeenCalledTimes(50)
    })

    it('should handle mixed publisher calls', () => {
      publishViewZoomed(mockEventBus, 1.5)
      publishViewPanned(mockEventBus, { x: 100, y: 200 })
      publishToolChanged(mockEventBus, 'rectangle')
      publishToastNotification(mockEventBus, 'Toast message', 'success')
      publishNotification(mockEventBus, 'Notification message', 'warning')

      expect(publishSpy).toHaveBeenCalledTimes(5)
      expect(publishSpy.mock.calls[0][0].type).toBe(AppEventType.ViewZoomed)
      expect(publishSpy.mock.calls[1][0].type).toBe(AppEventType.ViewPanned)
      expect(publishSpy.mock.calls[2][0].type).toBe(AppEventType.ToolChanged)
      expect(publishSpy.mock.calls[3][0].type).toBe(AppEventType.ToastShow)
      expect(publishSpy.mock.calls[4][0].type).toBe(AppEventType.NotificationAdd)
    })
  })
})
