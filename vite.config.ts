import path from 'node:path'
import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'
import tsconfigPaths from 'vite-tsconfig-paths'

// https://vite.dev/config/
export default defineConfig({
  server: {
    port: 3000,
  },
  plugins: [
    react(),
    tsconfigPaths(),
  ],
  optimizeDeps: {
    include: [
      'zustand',
      'zustand/traditional',
      'use-sync-external-store/shim/with-selector',
    ],
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@core': path.resolve(__dirname, './src/core'), // Corrected @core alias
      '@types_core': path.resolve(__dirname, './src/types/core'), // Added @types_core alias
      '@components': path.resolve(__dirname, './src/components'),
      '@ui': path.resolve(__dirname, './src/components/ui'),
      '@toolbar': path.resolve(__dirname, './src/components/ui/toolbar'),
      '@panel': path.resolve(__dirname, './src/components/ui/panel'),
      '@status': path.resolve(__dirname, './src/components/ui/status'),
      '@modals': path.resolve(__dirname, './src/components/ui/modals'),
      '@notifications': path.resolve(
        __dirname,
        './src/components/ui/notifications',
      ),
    },
  },
  build: {
    outDir: 'build', // Optional: customize output directory
  },
})
