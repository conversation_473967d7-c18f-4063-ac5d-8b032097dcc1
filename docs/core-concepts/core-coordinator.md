# CoreCoordinator

`CoreCoordinator.ts` is a crucial component in the application's architecture, acting as the central nervous system for core functionalities. This document outlines its primary responsibilities, interactions, and overall role within the system.

## Responsibilities

1.  **Initialization and Configuration:**
    *   Loads and manages application-wide configurations, potentially including settings for element behavior, default properties, and integration points.
    *   Initializes and holds references to essential core services and factories.

2.  **Service Orchestration:**
    *   Instantiates and coordinates various core services such as:
        *   `ShapeRepository`: Manages the collection of all shape data.
        *   `ElementFactory`: Responsible for creating instances of different shape types.
        *   `ElementValidator`: Validates shape data before creation or modification.
        *   `ElementCreationService`, `ElementEditService`, `ElementDeleteService`, `ElementSelectionService`: Handle specific actions related to element lifecycle and user interactions.
        *   `ComputeFacade`: Provides a unified interface for geometric calculations (area, perimeter, etc.).
        *   `ErrorService`: Centralized error handling and reporting.
        *   `LoggerService`: Manages application logging.

3.  **Event Handling and Dispatching:**
    *   Subscribes to and handles events published on the application's `EventBus`.
    *   These events typically represent user intentions or system triggers for actions like creating, modifying, deleting, or selecting shapes.
    *   Delegates these events to the appropriate services for processing.

4.  **Data Synchronization and Integrity:**
    *   Plays a role in ensuring data consistency between different parts of the core system, for example, by coordinating updates between the `ShapeRepository` and other services that depend on shape data.

5.  **Centralized Logic:**
    *   Acts as a single point of entry for many core operations, simplifying the interactions between UI components and the underlying business logic.
    *   Encapsulates complex coordination logic that might otherwise be spread across multiple components or services.

## Key Interactions

*   **`main.tsx` / Application Entry Point:** `CoreCoordinator` is typically instantiated and configured early in the application's lifecycle, often within `main.tsx` or a similar setup file.
*   **`EventBus`:** Heavily relies on the `EventBus` to receive commands and to potentially publish events about core system state changes.
*   **Zustand Stores (e.g., `shapesStore`):** While `CoreCoordinator` itself might not directly interact with Zustand stores (as stores are often managed at the UI/service layer), the services it coordinates (like `ShapeRepository` or element action services) will likely interact with stores to update application state.
*   **UI Components (Indirectly):** UI components trigger actions that are eventually handled by services orchestrated by the `CoreCoordinator`. The flow is typically: UI Event -> Service Call -> `EventBus` -> `CoreCoordinator` -> Specific Service Execution.

## Design Considerations

*   **Single Responsibility (Orchestration):** Its primary role is to coordinate, not to implement the detailed logic of each operation. This logic resides in the specialized services it manages.
*   **Dependency Injection:** Dependencies (like services, factories, event bus) are typically injected into the `CoreCoordinator`'s constructor, promoting loose coupling and testability.
*   **Testability:** By centralizing coordination and using dependency injection, `CoreCoordinator` can be unit-tested effectively by mocking its dependencies.

## Example Flow: Creating a Shape

1.  User interacts with the canvas to draw a shape.
2.  A UI component or hook dispatches an event (e.g., `REQUEST_CREATE_SHAPE`) to the `EventBus` with shape data.
3.  `CoreCoordinator`'s handler for `REQUEST_CREATE_SHAPE` is invoked.
4.  `CoreCoordinator` validates the input (possibly via `ElementValidator`).
5.  It then calls the `ElementCreationService` to create the shape, which might involve the `ElementFactory` and updating the `ShapeRepository`.
6.  The `ElementCreationService` (or `ShapeRepository`) updates the relevant Zustand store (e.g., `shapesStore`), triggering a UI re-render.

This centralized approach ensures that core operations are handled consistently and that the concerns of UI, state management, and business logic are well-separated.