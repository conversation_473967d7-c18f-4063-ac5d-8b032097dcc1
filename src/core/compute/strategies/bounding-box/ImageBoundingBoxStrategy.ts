/**
 * Bounding Box Calculation Strategy for Image Elements
 *
 * @remarks
 * This strategy implements the {@link BoundingBoxCalculatorStrategy} for calculating
 * the axis-aligned bounding box of Image elements ({@link CoreElementType.IMAGE}).
 *
 * The calculation involves:
 * 1. Retrieving the width and height from the element's properties.
 * 2. Using the element's position as the center point for the image.
 * 3. Creating a bounding box that encompasses the image area.
 *
 * Images are treated as rectangular elements with their position representing
 * the center point of the image.
 *
 * @module core/compute/strategies/bounding-box/ImageBoundingBoxStrategy
 */

import type { BoundingBoxCalculatorStrategy } from '../../../../types/core/compute'
import type { BoundingBox as BoundingBoxInterface } from '../../../../types/core/element/geometry/bounding-box'
import type { Element, ShapeElement } from '../../../../types/core/elementDefinitions'
import { BoundingBoxClass } from '../../../../lib/utils/geometry/BoundingBoxClass'
import { CoreError } from '../../../../services/system/error-service/coreError'
import { ElementType as CoreElementType } from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

/**
 * Strategy for calculating bounding boxes of image elements.
 *
 * @implements {BoundingBoxCalculatorStrategy}
 */
export class ImageBoundingBoxStrategy implements BoundingBoxCalculatorStrategy {
  /**
   * Returns the element type(s) this strategy handles.
   * @returns The IMAGE element type.
   */
  public getElementType(): CoreElementType {
    return CoreElementType.IMAGE
  }

  /**
   * Calculates the axis-aligned bounding box of an image element.
   *
   * @param element - The image element, expected to be of type {@link ShapeElement}
   *                  and of type {@link CoreElementType.IMAGE}.
   * @returns A {@link BoundingBoxInterface} object representing the bounding box.
   * @throws {@link CoreError} if the element type is not IMAGE.
   * @throws {@link CoreError} if the image element has invalid properties.
   */
  public calculateBoundingBox(element: Element): BoundingBoxInterface {
    if (element.type !== CoreElementType.IMAGE) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `Expected IMAGE, got ${element.type} for ID: ${element.id}`,
        undefined,
        { component: 'ImageBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    const shapeElement = element as ShapeElement
    const position = shapeElement.position

    if (position == null || typeof position.x !== 'number' || typeof position.y !== 'number') {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid position for Image element ID: ${element.id}`,
        undefined,
        { component: 'ImageBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    // Extract image dimensions from properties
    const properties = shapeElement.properties || {}

    // Try to get width and height from various possible locations
    let width: number
    let height: number

    // Check properties first
    if (typeof properties.width === 'number' && properties.width > 0) {
      width = properties.width
    }
    else if (typeof shapeElement.width === 'number' && shapeElement.width > 0) {
      width = shapeElement.width
    }
    else {
      // Default width if not specified
      width = 200
    }

    if (typeof properties.height === 'number' && properties.height > 0) {
      height = properties.height
    }
    else if (typeof shapeElement.height === 'number' && shapeElement.height > 0) {
      height = shapeElement.height
    }
    else {
      // Default height if not specified
      height = 200
    }

    // Validate dimensions
    if (!Number.isFinite(width) || !Number.isFinite(height) || width <= 0 || height <= 0) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Invalid dimensions for Image element ID: ${element.id} (width: ${width}, height: ${height})`,
        undefined,
        { component: 'ImageBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    // Image position is typically the center point
    // Calculate top-left corner for bounding box
    const topLeftX = position.x - width / 2
    const topLeftY = position.y - height / 2

    return new BoundingBoxClass(
      topLeftX,
      topLeftY,
      width,
      height,
      `bbox-${element.id}`,
    )
  }
}
