/**
 * Defines Standard Material Types for Design Elements
 *
 * @remarks
 * This module provides an enumeration of common material types used in interior
 * design and architectural visualization. These types can be associated with
 * various {@link Element | elements} to define their physical composition, appearance,
 * and properties for rendering or calculation purposes (e.g., cost or quantity estimation).
 *
 * The {@link MaterialType} enum lists materials typically used for walls, floors,
 * furniture, and other surfaces.
 *
 * @module types/core/element/elementMaterialTypes
 * @see {@link Element} from '@/types/core/elementDefinitions'
 * @see {@link MaterialCalculationOptions} from '@/types/core/compute/materialComputeTypes'
 * @see {@link MaterialCalculatorStrategy} from '@/types/core/compute/materialComputeTypes'
 */

/**
 * Enumerates common material types used in interior design and architectural contexts.
 *
 * @remarks
 * This enumeration provides a standardized list of materials that can be assigned
 * to design elements. These material types can influence rendering, cost calculations,
 * and other material-specific computations.
 */
export enum MaterialType {
  /** Represents brick, commonly used for walls and exteriors. */
  BRICK = 'brick',

  /** Represents concrete, used for structural elements, walls, and floors. */
  CONCRETE = 'concrete',

  /** Represents drywall (plasterboard), commonly used for interior walls and ceilings. */
  DRYWALL = 'drywall',

  /** Represents wood, used for various applications including walls, floors, and furniture. */
  WOOD = 'wood',

  /** Represents glass, used for windows, partitions, and decorative elements. */
  GLASS = 'glass',

  /** Represents hardwood, a premium flooring material. */
  HARDWOOD = 'hardwood',

  /** Represents laminate flooring, a synthetic multi-layer product. */
  LAMINATE = 'laminate',

  /** Represents ceramic, porcelain, or other types of tiles, used for floors and walls. */
  TILE = 'tile',

  /** Represents carpet, a textile floor covering. */
  CARPET = 'carpet',

  /** Represents vinyl flooring, a resilient flooring option. */
  VINYL = 'vinyl',

  /** Represents natural stone materials like marble, granite, or slate, used for floors and surfaces. */
  STONE = 'stone',

  /** Represents various types of metal, used for structural, decorative, or functional elements. */
  METAL = 'metal',

  /** Represents various types of plastic, used for fixtures, furniture, and components. */
  PLASTIC = 'plastic',

  /** Represents fabric materials, used for upholstery, curtains, and decor. */
  FABRIC = 'fabric',

  /** Represents leather, used for upholstery and luxury finishes. */
  LEATHER = 'leather',

  /** Represents a user-defined or non-standard material type. */
  CUSTOM = 'custom',
}
