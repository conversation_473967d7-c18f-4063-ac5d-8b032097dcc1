import type { ValidationError, ValidatorShape } from '@/types/core/element/validator'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { BaseShapeValidator } from '@/core/validator/common/validatorBase'
import { ElementType } from '@/types/core/shape-type'

// Create a test implementation of BaseShapeValidator
class TestValidator extends BaseShapeValidator {
  constructor() {
    super(ElementType.RECTANGLE)
  }

  // Implement the abstract method required by BaseShapeValidator
  protected validateSpecific(shape: ValidatorShape): ValidationError[] {
    if (!shape.properties?.width || !shape.properties.height) {
      return [
        { code: 'INVALID_PROPERTIES', message: 'Missing required properties', path: 'properties' },
      ]
    }
    return []
  }
}

describe('baseShapeValidator', () => {
  let validator: TestValidator
  let consoleSpy: any

  beforeEach(() => {
    validator = new TestValidator()
    consoleSpy = {
      warn: vi.spyOn(console, 'warn').mockImplementation(() => {}),
      error: vi.spyOn(console, 'error').mockImplementation(() => {}),
    }
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('validate', () => {
    it('should validate a valid shape', () => {
      // Mock applyAllRules to return empty array (no errors)
      const applyAllRulesSpy = vi.spyOn(validator as any, 'applyAllRules')
      applyAllRulesSpy.mockReturnValueOnce([])

      const validShape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: { width: 100, height: 50 },
      }

      const result = validator.validate(validShape)
      expect(result.valid).toBe(true)
    })

    it('should invalidate a shape with missing id', () => {
      const invalidShape = {
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: { width: 100, height: 50 },
      } as any

      const result = validator.validate(invalidShape)
      expect(result.valid).toBe(false)
      expect(result.errors).toBeDefined()
      expect(result.errors?.some(e => e.code === 'MISSING_OR_INVALID_ID')).toBe(true)
    })

    it('should invalidate a shape with invalid stroke color', () => {
      const invalidShape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: { width: 100, height: 50 },
        strokeColor: 'not-a-color',
      }

      const result = validator.validate(invalidShape)
      expect(result.valid).toBe(false)
      expect(result.errors).toBeDefined()
      expect(result.errors?.some(e => e.code === 'INVALID_STROKE_COLOR')).toBe(true)
    })

    it('should invalidate a shape with invalid fill color', () => {
      const invalidShape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: { width: 100, height: 50 },
        fillColor: 'not-a-color',
      }

      const result = validator.validate(invalidShape)
      expect(result.valid).toBe(false)
      expect(result.errors).toBeDefined()
      expect(result.errors?.some(e => e.code === 'INVALID_FILL_COLOR')).toBe(true)
    })

    it('should invalidate a shape with invalid specific properties', () => {
      const invalidShape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: {},
      }

      const result = validator.validate(invalidShape)
      expect(result.valid).toBe(false)
      expect(result.errors).toBeDefined()
      expect(result.errors?.some(e => e.code === 'INVALID_PROPERTIES')).toBe(true)
    })

    it('should handle multiple validation errors', () => {
      const invalidShape = {
        type: ElementType.RECTANGLE,
        properties: {},
      } as any

      const result = validator.validate(invalidShape)
      expect(result.valid).toBe(false)
      expect(result.errors).toBeDefined()
      expect(result.errors!.length).toBeGreaterThan(1)
    })

    it('should handle errors during validation', () => {
      // Create a spy that throws an error
      const validateSpecificSpy = vi.spyOn(validator, 'validateSpecific' as any)
      validateSpecificSpy.mockImplementationOnce(() => {
        throw new Error('Test error')
      })

      const shape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: { width: 100, height: 50 },
      }

      const result = validator.validate(shape)
      expect(result.valid).toBe(false)
      expect(result.errors).toBeDefined()
      expect(result.errors?.some(e => e.code === 'VALIDATION_SPECIFIC_ERROR')).toBe(true)
      expect(consoleSpy.error).toHaveBeenCalled()
    })
  })

  describe('applyAllRules', () => {
    it('should apply business rules', () => {
      // Mock the applyBusinessRules function
      const applyBusinessRulesSpy = vi.spyOn(validator as any, 'applyAllRules')
      applyBusinessRulesSpy.mockReturnValueOnce([])

      const shape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: { width: 100, height: 50 },
      }

      const result = validator.validate(shape)
      expect(result.valid).toBe(true)
      expect(applyBusinessRulesSpy).toHaveBeenCalled()
    })

    it('should handle errors during rule application', () => {
      // Create a spy that throws an error
      const applyAllRulesSpy = vi.spyOn(validator as any, 'applyAllRules')
      applyAllRulesSpy.mockImplementationOnce(() => {
        throw new Error('Rule error')
      })

      const shape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: { width: 100, height: 50 },
      }

      const result = validator.validate(shape)
      expect(result.valid).toBe(false)
      expect(result.errors).toBeDefined()
      expect(result.errors?.some(e => e.code === 'VALIDATION_RULE_ERROR')).toBe(true)
      expect(consoleSpy.error).toHaveBeenCalled()
    })
  })

  describe('applySpecificRules', () => {
    it('should return empty array by default', () => {
      const applySpecificRulesSpy = vi.spyOn(validator as any, 'applySpecificRules')

      const shape = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: { width: 100, height: 50 },
      }

      validator.validate(shape)
      expect(applySpecificRulesSpy).toHaveBeenCalled()
      expect(applySpecificRulesSpy.mock.results[0].value).toEqual([])
    })
  })
})
