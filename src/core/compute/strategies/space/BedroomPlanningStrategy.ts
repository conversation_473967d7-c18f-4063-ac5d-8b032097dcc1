// import { PointData as PointIPoint } from '@/types/core/element/geometry/point'; // IPoint interface // Unused
import type {
  ErgonomicsEvaluationResult,
  PathwayCheckResult,
  SpacePlanningStrategy,
  SpaceType,
} from '@/types/core/compute'
/**
 * Bedroom Space Planning Strategy
 * Provides planning functionality for bedrooms, including furniture layout, pathway width checks, etc.
 */
import type {
  Element,
  // ElementType as CoreElementType, // Unused
  Shape, // Restored import for Shape
  ShapeElement, // Import ShapeElement
  // Path // Unused
} from '@/types/core/elementDefinitions'
import { CoreError, ErrorType } from '@/services/system/error-service'
// import {
//     calculateSpaceUtilization,
//     checkPathwaysWidth,
//     evaluateFurnitureLayout as evaluateFurnitureLayoutUtil
// } from '@/lib/utils/space/spacePlanning'; // Module not found
// import { BoundingBoxClass } from '@/lib/utils/geometry'; // BoundingBoxClass is exported from here // Unused
// import { PointClass } from '@/lib/utils/geometry'; // PointClass is exported from here // Unused

// Helper to get BoundingBoxClass from an Element // Unused
// function getElementBoundingBoxClass(element: Element): BoundingBoxClass | null {
//     if (!element) return null;
//     if (element.type === CoreElementType.RECTANGLE || element.type === CoreElementType.SQUARE) {
//         const shape = element as Shape.Rectangle;
//         if (shape.position && typeof shape.width === 'number' && typeof shape.height === 'number') {
//             return new BoundingBoxClass(shape.position.x - shape.width / 2, shape.position.y - shape.height / 2, shape.width, shape.height);
//         }
//     }
//      if (element.type === CoreElementType.CIRCLE) {
//         const shape = element as Shape.Circle;
//         if (shape.position && typeof shape.radius === 'number') {
//             return new BoundingBoxClass(shape.position.x - shape.radius, shape.position.y - shape.radius, shape.radius * 2, shape.radius * 2);
//         }
//     }
//     console.warn(`[BedroomPlanningStrategy] getElementBoundingBoxClass: Bounding box for type ${element.type} not directly handled.`);
//     return null;
// }

/**
 * Bedroom Space Planning Strategy
 */
export class BedroomPlanningStrategy implements SpacePlanningStrategy {
  // getElementBoundingBoxClass is defined outside the class now, or could be a private static method.
  // For simplicity, assuming it's accessible as defined above.

  public calculateSpaceUtilization(/* elements: Element[], roomBoundaryElement: Element */): number { // Params unused due to commented out body
    // const roomBBoxClass = getElementBoundingBoxClass(roomBoundaryElement);
    // if (!roomBBoxClass) {
    //   throw new CoreError( ErrorType.INVALID_PARAMETER, 'Room boundary must be an element from which a bounding box can be derived.');
    // }
    // return calculateSpaceUtilization(elements, roomBBoxClass);
    console.warn('[BedroomPlanningStrategy] calculateSpaceUtilization is temporarily disabled due to missing dependencies.')
    return 0
  }

  public checkPathwayWidth(/* elements: Element[], pathways: Path.Line[], minWidth: number */): PathwayCheckResult[] { // Params unused due to commented out body
    // if (!Array.isArray(elements)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Elements must be an array');
    // if (!Array.isArray(pathways)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Pathways must be an array');
    // if (typeof minWidth !== 'number' || minWidth <= 0) {
    //   throw new CoreError(ErrorType.INVALID_PARAMETER, 'Minimum width must be a positive number');
    // }

    // const formattedPathways = pathways.map((path, index) => {
    //     const lineForUtil = {
    //         id: path.id ?? `pathway-${index}`,
    //         start: path.start as PointIPoint,
    //         end: path.end as PointIPoint,
    //         type: CoreElementType.LINE,
    //         getSubType: () => CoreElementType.LINE,
    //     } as any;
    //     return { id: path.id ?? `pathway-${index + 1}`, line: lineForUtil as import('@/lib/utils/element/path/lineImplementation').Line };
    // });

    // return checkPathwaysWidth(elements, formattedPathways, minWidth);
    console.warn('[BedroomPlanningStrategy] checkPathwayWidth is temporarily disabled due to missing dependencies.')
    return []
  }

  public evaluateBedroomFurnitureLayout(
    /* elements: Element[], roomBoundaryElement: Element, bedElement: Element */
  ): { score: number, suggestions: string[] } { // Params unused due to commented out body
    // const roomBBoxClass = getElementBoundingBoxClass(roomBoundaryElement);
    // if (!roomBBoxClass) {
    //   throw new CoreError(ErrorType.INVALID_PARAMETER, 'Room boundary for furniture layout must be derivable to a BoundingBox.');
    // }
    // if (!bedElement || !bedElement.type ) {
    //   throw new CoreError(ErrorType.INVALID_PARAMETER, 'Bed element must be provided and have a type.');
    // }

    // const bedShape = bedElement as Shape.Rectangle;
    // const bedPosition = bedShape.position;
    // const bedWidth = bedShape.width;
    // const bedHeight = bedShape.height;

    // if (!bedPosition || typeof bedWidth !== 'number' || bedWidth <= 0 || typeof bedHeight !== 'number' || bedHeight <= 0) {
    //   throw new CoreError( ErrorType.INVALID_PARAMETER, `Bed element (ID: ${bedElement.id}) must have valid position, width, and height.`);
    // }

    // return evaluateFurnitureLayoutUtil(
    //   elements.filter(el => el.id !== bedElement.id),
    //   roomBBoxClass,
    //   {
    //     position: new PointClass(bedPosition.x, bedPosition.y), // Convert IPoint to PointClass instance
    //     width: bedWidth,
    //     height: bedHeight
    //   }
    // );
    console.warn('[BedroomPlanningStrategy] evaluateBedroomFurnitureLayout is temporarily disabled due to missing dependencies.')
    return { score: 0, suggestions: ['Evaluation temporarily disabled.'] }
  }

  public evaluateErgonomics(
    _elements: Element[],
    deskElement: Element,
    chairElement: Element,
  ): ErgonomicsEvaluationResult {
    const issues: string[] = []
    const recommendations: string[] = []

    if (deskElement?.type === null || deskElement?.type === undefined || chairElement?.type === null || chairElement?.type === undefined) {
      throw new CoreError(ErrorType.InvalidParameter, 'Desk and chair elements must be provided and have a type.')
    }

    // Use double assertion to inform TypeScript about the specific shape type
    const deskShape = deskElement as unknown as Shape.Rectangle
    const chairShape = chairElement as unknown as Shape.Rectangle

    // Accessing height directly from Shape.Rectangle (which extends ShapeElement)
    const deskHeight = typeof deskShape.height === 'number' ? deskShape.height : 0
    if (deskHeight < 0.7 || deskHeight > 0.8) {
      issues.push(`Desk height of ${deskHeight.toFixed(2)}m is outside the recommended range (0.7-0.8m). Consider desk surface height.`)
    }
    else {
      recommendations.push(`Desk height of ${deskHeight.toFixed(2)}m is noted.`)
    }

    // Accessing properties from Shape.Rectangle (which extends ShapeElement)
    // Need to cast to ShapeElement to access properties if direct access on Shape.Rectangle fails.
    const chairShapeAsShapeElement = chairShape as unknown as ShapeElement
    const chairProperties = chairShapeAsShapeElement.properties as { seatHeight?: number } | undefined
    const chairSeatHeight = chairProperties?.seatHeight ?? (typeof chairShape.height === 'number' ? chairShape.height * 0.6 : 0.45)
    if (chairSeatHeight < 0.4 || chairSeatHeight > 0.55) {
      issues.push(`Chair seat height of ${chairSeatHeight.toFixed(2)}m is outside the recommended range (0.4-0.55m).`)
    }
    else {
      recommendations.push(`Chair seat height of ${chairSeatHeight.toFixed(2)}m is within recommended range.`)
    }

    recommendations.push('Position desk near natural light source if possible.')
    recommendations.push('Ensure adequate legroom under the desk (approx 0.7m height, 0.6m depth).')
    recommendations.push('Consider an adjustable chair with good lumbar support.')

    return {
      isValid: issues.length === 0,
      issues,
      recommendations,
    }
  }

  public getSpaceType(): SpaceType {
    return 'bedroom'
  }
}
