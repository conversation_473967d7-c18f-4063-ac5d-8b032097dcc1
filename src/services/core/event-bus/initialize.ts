/**
 * Provides functions to initialize and clean up the application's event system.
 *
 * @remarks
 * This module sets up core event handlers and configures development logging when appropriate.
 * The event system is a central part of the application architecture, facilitating
 * communication between decoupled components through a publish-subscribe pattern.
 *
 * @module services/core/event-bus/initialize
 */

import type { AppEventMap } from '@/types/services/events'
import { AppEventType } from '@/types/services/events'
import { setupEventHandlers } from './handlers/eventHandlers' // Corrected to camelCase
import { appEventBus } from './index'

/**
 * Initializes the event system.
 *
 * @remarks
 * This function performs the following operations:
 * 1. Resets the event bus to clear any existing handlers
 * 2. Sets up standard event handlers through the setupEventHandlers function
 * 3. Configures development-mode logging if applicable
 *
 * Note: After initialization, the CoreCoordinator and other components
 * will register their own specific event handlers separately.
 */
export function initializeEventSystem(): void {
  console.warn('Initializing event system...')

  // Reset the event bus
  appEventBus.reset()

  // Set up event handlers
  setupEventHandlers()

  // Set up development logging
  setupDevelopmentLogging()

  // The CoreCoordinator registers its own event handlers
  // Other components should register their handlers directly

  console.warn('Event system initialized with CoreCoordinator integration')
}

/**
 * Cleans up the event system.
 *
 * @remarks
 * This function resets the event bus, removing all registered handlers
 * and clearing any pending debounced or throttled events. It should be
 * called when shutting down the application or before re-initializing
 * the event system.
 */
export function cleanupEventSystem(): void {
  console.warn('Cleaning up event system...')

  // Reset the event bus
  appEventBus.reset()

  console.warn('Event system cleaned up')
}

/**
 * Sets up development logging for the event system.
 *
 * @remarks
 * When running in development mode, this function registers handlers
 * for all event types to log them to the console. These handlers run
 * at a low priority to ensure they execute after all other handlers.
 *
 * This provides visibility into the event flow during development
 * without affecting production performance.
 *
 * @private
 */
function setupDevelopmentLogging(): void {
  // Example: Log all events in development
  // Use dynamic import for node:process
  import('node:process').then((processModule) => {
    const nodeEnv = processModule.env?.NODE_ENV ?? 'production'
    if (nodeEnv === 'development') {
      // Subscribe to all events with generic typing
      Object.values(AppEventType).forEach((eventType) => {
        // Using type assertion to resolve type mismatch
        appEventBus.subscribe(eventType as keyof AppEventMap, (event: AppEventMap[keyof AppEventMap]) => {
          console.warn(`Event: ${eventType}`, event)
        }, { priority: -100 }) // Low priority to run last
      })
    }
  }).catch(() => {
    // Fallback for environments without process module
    // Do nothing in production-like environments
  })
}
