/**
 * @file IElementValidator.ts
 * @description Defines the interface contract for element validators.
 * This interface outlines the standard methods that all element validator implementations
 * must provide, ensuring a consistent approach to element validation across the application.
 *
 * @module core/validator/IElementValidator
 */

import type { IBaseShapeValidator } from './validators/common/validatorBase'
import type { ElementType } from '@/types/core/elementDefinitions'
import type {
  ValidationResult,
  ValidatableShape as ValidatorShape,
} from '@/types/core/validator/validator-interface'

/**
 * Defines the interface contract for element validators.
 *
 * @remarks
 * Provides standard methods for validating shape element data objects,
 * including both synchronous and asynchronous validation capabilities.
 */
export interface IElementValidator {
  /**
   * Asynchronously validates a single shape element data object.
   *
   * @param element - The shape data object to validate.
   * @returns A Promise containing the validation result.
   */
  validateElement: (element: ValidatorShape) => Promise<ValidationResult>

  /**
   * Asynchronously validates an array of shape element data objects.
   *
   * @param elements - The array of shape data objects to validate.
   * @returns A Promise containing an array of validation results.
   */
  validateElements: (elements: ValidatorShape[]) => Promise<ValidationResult[]>

  /**
   * Checks if all elements in the array are valid.
   *
   * @param elements - The array of shape data objects to check.
   * @returns True if all elements are valid; otherwise, false.
   */
  areAllElementsValid: (elements: ValidatorShape[]) => Promise<boolean>

  /**
   * Registers a validator instance for a specific element type.
   *
   * @param elementType - The element type to register the validator for.
   * @param validator - The validator instance to register.
   */
  registerValidator: (elementType: ElementType, validator: IBaseShapeValidator) => void

  /**
   * Checks if a validator exists for a specific element type.
   *
   * @param elementType - The element type to check.
   * @returns True if a validator exists; otherwise, false.
   */
  hasValidator: (elementType: ElementType) => boolean
}
