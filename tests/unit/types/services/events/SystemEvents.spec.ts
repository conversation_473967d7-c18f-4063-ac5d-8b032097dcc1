// Import the interfaces
import type {
  EventBusEvent,
  ShapeFactoryEvent,
} from '@/types/services/events/systemEvents'

import { describe, expect, it } from 'vitest'

// Import AppEventType for type checking
import { AppEventType } from '@/types/services/events/eventTypes'

describe('system Event Types', () => {
  it('should allow declaration using EventBusEvent interface', () => {
    const event: EventBusEvent | undefined = undefined
    expect(event).toBeUndefined()

    // Example usage
    const registerEvent: EventBusEvent = {
      type: AppEventType.EVENT_REGISTER,
      payload: { eventType: AppEventType.SHAPE_SELECTED, handler: () => {} },
    }
    expect(registerEvent.type).toBe(AppEventType.EVENT_REGISTER)
    expect(registerEvent.payload.eventType).toBe(AppEventType.SHAPE_SELECTED)
    expect(typeof registerEvent.payload.handler).toBe('function')

    const errorEvent: EventBusEvent = {
      type: AppEventType.EVENT_ERROR,
      payload: { eventType: AppEventType.RENDER_COMPLETE, error: 'Failed to render' },
    }
    expect(errorEvent.type).toBe(AppEventType.EVENT_ERROR)
    expect(errorEvent.payload.error).toBe('Failed to render')
  })

  it('should allow declaration using ShapeFactoryEvent interface', () => {
    const event: ShapeFactoryEvent | undefined = undefined
    expect(event).toBeUndefined()

    // Example usage
    const registerEvent: ShapeFactoryEvent = {
      type: AppEventType.FACTORY_REGISTER,
      payload: {
        ElementType: 'custom-shape',
        factory: { creator: () => ({}), validator: () => true },
      },
    }
    expect(registerEvent.type).toBe(AppEventType.FACTORY_REGISTER)
    expect(registerEvent.payload.ElementType).toBe('custom-shape')
    expect(typeof registerEvent.payload.factory?.creator).toBe('function')

    const errorEvent: ShapeFactoryEvent = {
      type: AppEventType.FACTORY_ERROR,
      payload: { ElementType: 'invalid-shape', error: 'Registration failed' },
    }
    expect(errorEvent.type).toBe(AppEventType.FACTORY_ERROR)
    expect(errorEvent.payload.error).toBe('Registration failed')
  })
})
