import { beforeEach, describe, expect, it, vi } from 'vitest'
import { PolylineValidator } from '@/core/validator/elements/polylineValidator'
import { ElementType } from '@/types/core/shape-type'

describe('polylineValidator', () => {
  let validator: PolylineValidator

  beforeEach(() => {
    validator = new PolylineValidator()
    vi.clearAllMocks()
  })

  describe('validateSpecific', () => {
    it('应该验证有效的折线', () => {
      const validPolyline = {
        id: 'polyline1',
        type: ElementType.POLYLINE,
        points: [
          { x: 0, y: 0 },
          { x: 50, y: 50 },
          { x: 100, y: 0 },
        ],
      }

      const errors = (validator as any).validateSpecific(validPolyline)
      expect(errors).toEqual([])
    })

    it('应该检测到无效的形状类型', () => {
      const invalidType = {
        id: 'not-a-polyline',
        type: ElementType.LINE,
        points: [
          { x: 0, y: 0 },
          { x: 100, y: 100 },
        ],
      }

      const errors = (validator as any).validateSpecific(invalidType)
      expect(errors.length).toBe(1)
      expect(errors[0].code).toBe('INVALID_SHAPE_TYPE')
    })

    it('应该检测到缺少点数组', () => {
      const missingPoints = {
        id: 'missing-points',
        type: ElementType.POLYLINE,
      }

      const errors = (validator as any).validateSpecific(missingPoints)
      expect(errors.length).toBe(1)
      expect(errors[0].code).toBe('MISSING_POINTS')
    })

    it('应该检测到点数不足', () => {
      const insufficientPoints = {
        id: 'insufficient-points',
        type: ElementType.POLYLINE,
        points: [{ x: 0, y: 0 }],
      }

      const errors = (validator as any).validateSpecific(insufficientPoints)
      expect(errors.length).toBe(1)
      expect(errors[0].code).toBe('INSUFFICIENT_POINTS')
    })

    it('应该检测到无效的点', () => {
      const invalidPoints = {
        id: 'invalid-points',
        type: ElementType.POLYLINE,
        points: [
          { x: 0, y: 0 },
          { x: 'invalid', y: 50 },
          { x: 100, y: 0 },
        ],
      }

      const errors = (validator as any).validateSpecific(invalidPoints)
      expect(errors.length).toBe(1)
      expect(errors[0].code).toBe('INVALID_POINT')
      expect(errors[0].path).toBe('points[1]')
    })

    it('应该检测到无效的curved属性', () => {
      const invalidCurved = {
        id: 'invalid-curved',
        type: ElementType.POLYLINE,
        points: [
          { x: 0, y: 0 },
          { x: 50, y: 50 },
        ],
        curved: 'not-a-boolean',
      }

      const errors = (validator as any).validateSpecific(invalidCurved)
      expect(errors.length).toBe(1)
      expect(errors[0].code).toBe('INVALID_CURVED_PROPERTY')
    })

    it('应该检测到无效类型的tension属性', () => {
      const invalidTensionType = {
        id: 'invalid-tension-type',
        type: ElementType.POLYLINE,
        points: [
          { x: 0, y: 0 },
          { x: 50, y: 50 },
        ],
        tension: 'not-a-number',
      }

      const errors = (validator as any).validateSpecific(invalidTensionType)
      expect(errors.length).toBe(1)
      expect(errors[0].code).toBe('INVALID_TENSION_TYPE')
    })

    it('应该检测到超出范围的tension值', () => {
      const outOfRangeTension = {
        id: 'out-of-range-tension',
        type: ElementType.POLYLINE,
        points: [
          { x: 0, y: 0 },
          { x: 50, y: 50 },
        ],
        tension: 1.5,
      }

      const errors = (validator as any).validateSpecific(outOfRangeTension)
      expect(errors.length).toBe(1)
      expect(errors[0].code).toBe('INVALID_TENSION_RANGE')
    })

    it('应该接受有效的curved和tension属性', () => {
      const validProperties = {
        id: 'valid-properties',
        type: ElementType.POLYLINE,
        points: [
          { x: 0, y: 0 },
          { x: 50, y: 50 },
        ],
        curved: true,
        tension: 0.5,
      }

      const errors = (validator as any).validateSpecific(validProperties)
      expect(errors).toEqual([])
    })
  })

  describe('validate', () => {
    it('应该验证完整的有效折线', () => {
      const validPolyline = {
        id: 'polyline1',
        type: ElementType.POLYLINE,
        points: [
          { x: 0, y: 0 },
          { x: 50, y: 50 },
          { x: 100, y: 0 },
        ],
        curved: true,
        tension: 0.5,
      }

      const result = validator.validate(validPolyline)
      expect(result.valid).toBe(true)
      expect(result.errors).toEqual([])
    })
  })
})
