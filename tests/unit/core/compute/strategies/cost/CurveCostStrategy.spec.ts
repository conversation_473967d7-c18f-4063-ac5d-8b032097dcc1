import { beforeEach, describe, expect, it, vi } from 'vitest'
import { CurveCostStrategy } from '@/core/compute/strategies/cost/CurveCostStrategy'
import { CoreError } from '@/services/errors'

describe('curveCostStrategy', () => {
  let strategy: CurveCostStrategy
  let mockArcElement: any
  let mockQuadraticElement: any
  let mockCubicElement: any

  beforeEach(() => {
    strategy = new CurveCostStrategy()

    // 创建模拟弧形元素
    mockArcElement = {
      id: 'test-arc',
      type: 'arc',
      getSubType: () => 'arc',

      getPerimeter: vi.fn().mockReturnValue(10), // 10米周长
      radius: 2, // 2米半径
      startAngle: 0,
      endAngle: Math.PI,
    }

    // 创建模拟二次贝塞尔曲线元素
    mockQuadraticElement = {
      id: 'test-quadratic',
      type: 'quadratic',
      getSubType: () => 'quadratic',

      getPerimeter: vi.fn().mockReturnValue(8), // 8米周长
      start: { x: 0, y: 0 },
      control: { x: 2, y: 2 },
      end: { x: 4, y: 0 },
    }

    // 创建模拟三次贝塞尔曲线元素
    mockCubicElement = {
      id: 'test-cubic',
      type: 'cubic',
      getSubType: () => 'cubic',

      getPerimeter: vi.fn().mockReturnValue(12), // 12米周长
      start: { x: 0, y: 0 },
      control1: { x: 2, y: 2 },
      control2: { x: 4, y: -2 },
      end: { x: 6, y: 0 },
    }
  })

  it('should be defined', () => {
    expect(strategy).toBeDefined()
  })

  it('should return the correct shape types', () => {
    expect(strategy.getElementType()).toBe('curve')
  })

  it('should calculate arc cost correctly', () => {
    const result = strategy.calculateCost(mockArcElement, 100)

    expect(result).toBeDefined()
    expect(result).toBe(10 * 100) // 周长 * 单价
  })

  it('should calculate quadratic curve cost correctly', () => {
    const result = strategy.calculateCost(mockQuadraticElement, 120)

    expect(result).toBeDefined()
    expect(result).toBe(8 * 120) // 周长 * 单价
  })

  it('should calculate cubic curve cost correctly', () => {
    const result = strategy.calculateCost(mockCubicElement, 150)

    expect(result).toBeDefined()
    expect(result).toBe(12 * 150) // 周长 * 单价
  })

  it('should use default options when not provided', () => {
    const result = strategy.calculateCost(mockArcElement, 50)

    expect(result).toBeDefined()
    expect(result).toBe(10 * 50)
  })

  it('should throw error for invalid element type', () => {
    const invalidElement = { ...mockArcElement, getSubType: () => 'invalid' }

    expect(() => {
      strategy.calculateCost(invalidElement)
    }).toThrow(CoreError)
  })

  it('should handle different curve complexities', () => {
    // 创建更复杂的曲线
    const complexCubic = {
      ...mockCubicElement,

      getPerimeter: vi.fn().mockReturnValue(20), // 20米周长
    }

    const simpleResult = strategy.calculateCost(mockCubicElement, 100)

    const complexResult = strategy.calculateCost(complexCubic, 100)

    expect(complexResult).toBe(20 * 100)
    expect(simpleResult).toBe(12 * 100)
  })
})
