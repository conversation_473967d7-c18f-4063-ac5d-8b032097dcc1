/**
 * Material Calculation Strategy for Wallpaper
 *
 * @remarks
 * This strategy implements the {@link IMaterialCalculatorStrategy} to calculate
 * the required amount of wallpaper for a given room or wall surface element.
 *
 * It first determines the wall perimeter and surface area based on the input `element`'s
 * type (supporting `RECTANGLE`, `SQUARE`, `POLYGON` and its variants, and closed `POLYLINE`s).
 * A `roomHeight` must be provided in {@link MaterialCalculationOptions}.
 *
 * Key inputs from {@link MaterialCalculationOptions} for wallpaper calculation include:
 * - `roomHeight`: The height of the walls to be wallpapered.
 * - `rollWidth`: Width of a single wallpaper roll.
 * - `rollLength`: Length of a single wallpaper roll.
 * - `patternRepeat`: Height of the wallpaper's pattern repeat (if any).
 * - `wastageRate`: Percentage for wallpaper wastage.
 * - `adhesivePerSqmWallpaper`: Adhesive needed per square meter.
 *
 * The strategy calculates the number of strips needed, strips per roll (accounting for
 * pattern repeat), total rolls needed (including wastage), total purchased wallpaper area,
 * and adhesive quantity.
 *
 * The `calculateMaterialAmount` method returns a summarized {@link MaterialCalculationResult},
 * where `amount` represents the total purchased wallpaper area and `unitCount` is the
 * number of rolls. A private `calculateDetailedMaterial` method provides a more
 * granular breakdown.
 *
 * @module core/compute/strategies/material/WallpaperMaterialStrategy
 * @see {@link IMaterialCalculatorStrategy}
 * @see {@link MaterialCalculationOptions}
 * @see {@link MaterialCalculationResult}
 */
import type {
  MaterialCalculatorStrategy as IMaterialCalculatorStrategy, // Renamed for consistency
  MaterialCalculationOptions,
  MaterialCalculationResult,
} from '@/types/core/compute' // Corrected import path
// IPoint interface
import type {
  Element,
  Path,
  Shape,
  ShapeElement,
} from '@/types/core/elementDefinitions'
// Import strategies from their new location via the re-exporting index
import { PolylinePerimeterStrategy } from '@/core/compute/strategies/perimeter'
import { calculatePerimeter as calculatePolygonPerimeter } from '@/lib/utils/geometry/polygonUtils'

// Import perimeter and area calculation utilities
import { calculateRectanglePerimeter } from '@/lib/utils/geometry/rectangleUtils' // calculatePerimeter is exported as calculateRectanglePerimeter
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  ElementType as CoreElementType,
} from '@/types/core/elementDefinitions'
// PolylineAreaStrategy might not be needed if wallpaper is only based on perimeter and height

export class WallpaperMaterialStrategy implements IMaterialCalculatorStrategy { // Renamed IMaterialCalculatorStrategy for consistency
  private polylinePerimeterStrategy: PolylinePerimeterStrategy

  constructor() {
    this.polylinePerimeterStrategy = new PolylinePerimeterStrategy()
  }

  /**
   * Calculates the amount of wallpaper material required.
   *
   * @param element - The element representing the room or wall surface (e.g., RECTANGLE, POLYGON).
   * @param materialType - The type of material, expected to be 'wallpaper'.
   * @param options - Optional {@link MaterialCalculationOptions}. Must include `roomHeight`.
   *                  Can also include `rollWidth`, `rollLength`, `patternRepeat`, `wastageRate`, etc.
   * @returns A {@link MaterialCalculationResult} summarizing the wallpaper quantity (rolls and total area).
   * @throws {@link CoreError} if `materialType` is not 'wallpaper', if `roomHeight` is invalid,
   *         if wall dimensions cannot be determined, or if roll dimensions are invalid.
   */
  public calculateMaterialAmount(element: Element, materialType: string, options?: MaterialCalculationOptions): MaterialCalculationResult {
    const detailedResult = this.calculateDetailedMaterial(element, materialType, options)

    return {
      amount: detailedResult.totalArea, // Represents total purchased wallpaper area
      unit: 'm²',
      unitCount: detailedResult.rolls,
      unitType: 'roll',
      amountWithWastage: detailedResult.totalArea, // Already includes wastage
    }
  }

  /**
   * Calculates a detailed breakdown of wallpaper and related materials.
   * @private
   * @param element - The element representing the room/wall.
   * @param materialType - Expected to be 'wallpaper'.
   * @param options - Optional {@link MaterialCalculationOptions}.
   * @returns A record containing quantities for rolls, adhesive, total purchased area, etc.
   * @throws {@link CoreError} as described in `calculateMaterialAmount`.
   */
  private calculateDetailedMaterial(element: Element, materialType: string, options?: MaterialCalculationOptions): Record<string, number> {
    if (materialType.toLowerCase() !== 'wallpaper') {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `WallpaperMaterialStrategy can only calculate for 'wallpaper' material type, got ${materialType}`,
      )
    }

    let wallSurfaceArea: number
    let wallPerimeter: number
    const roomHeight = options?.roomHeight ?? 2.4 // Default room height
    if (roomHeight <= 0)
      throw new CoreError(ErrorType.InvalidParameter, 'Room height must be positive.')

    try {
      switch (element.type) {
        case CoreElementType.RECTANGLE:
        case CoreElementType.SQUARE: {
          const rect = element as unknown as Shape.Rectangle // Added unknown
          const width = typeof rect.properties?.width === 'number' ? rect.properties.width : 0
          const height = typeof rect.properties?.height === 'number' ? rect.properties.height : 0
          wallPerimeter = calculateRectanglePerimeter(width, height)
          wallSurfaceArea = wallPerimeter * roomHeight
          break
        }
        case CoreElementType.POLYGON:
        case CoreElementType.TRIANGLE:
        case CoreElementType.HEXAGON: {
          const polygon = element as unknown as Shape.Polygon // Added unknown
          if (polygon.points === null || polygon.points === undefined || !Array.isArray(polygon.points) || polygon.points.length < 3) {
            throw new CoreError(ErrorType.InvalidParameter, `Polygon (ID: ${element.id}) has insufficient points.`)
          }
          wallPerimeter = calculatePolygonPerimeter(polygon.points)
          wallSurfaceArea = wallPerimeter * roomHeight
          break
        }
        case CoreElementType.POLYLINE: {
          const polyline = element as unknown as Path.Polyline // Added unknown
          if (polyline.properties?.closed !== true)
            throw new CoreError(ErrorType.InvalidParameter, 'Wallpaper can only be applied to closed Polyline areas representing room outlines.')
          wallPerimeter = this.polylinePerimeterStrategy.calculatePerimeter(element as unknown as ShapeElement) // Added unknown
          wallSurfaceArea = wallPerimeter * roomHeight
          break
        }
        default:
          throw new CoreError(
            ErrorType.InvalidElementType,
            `WallpaperMaterialStrategy cannot determine wall dimensions for element type: ${element.type}`,
          )
      }
    }
    catch (error) {
      throw new CoreError(
        ErrorType.ComputationError,
        `Failed to calculate wall dimensions for element (ID: ${element.id}): ${error instanceof Error ? error.message : String(error)}`,
      )
    }

    if (wallSurfaceArea <= 0 || wallPerimeter <= 0) {
      throw new CoreError(ErrorType.ComputationError, `Invalid wall dimensions for element ${element.id}: Area=${wallSurfaceArea}, Perimeter=${wallPerimeter}`)
    }

    const rollWidth = options?.rollWidth ?? 0.53
    const rollLength = options?.rollLength ?? 10.0
    const patternRepeat = options?.patternRepeat ?? 0
    const wastageRate = options?.wastageRate ?? 10 // Percentage

    if (rollWidth <= 0 || rollLength <= 0) {
      throw new CoreError(ErrorType.InvalidParameter, 'Roll dimensions must be positive.')
    }

    // Calculate strips needed based on perimeter
    const stripsNeededForPerimeter = Math.ceil(wallPerimeter / rollWidth)

    // Calculate length of each strip, considering pattern repeat
    let stripLengthActual = roomHeight
    if (patternRepeat > 0) {
      stripLengthActual = Math.ceil(roomHeight / patternRepeat) * patternRepeat
    }
    if (stripLengthActual <= 0)
      throw new CoreError(ErrorType.InvalidParameter, 'Actual strip length must be positive.')

    // Calculate how many full strips can be cut from one roll
    const stripsPerRoll = Math.floor(rollLength / stripLengthActual)
    if (stripsPerRoll <= 0) { // Cannot even get one full strip from a roll
      throw new CoreError(ErrorType.InvalidParameter, `Roll length (${rollLength}m) is too short for the required strip length (${stripLengthActual}m including pattern repeat).`)
    }

    // Calculate total rolls needed
    const rollsNeededRaw = Math.ceil(stripsNeededForPerimeter / stripsPerRoll)
    const rollsNeededWithWastage = Math.ceil(rollsNeededRaw * (1 + wastageRate / 100))

    // Total area of wallpaper purchased (rolls * area_per_roll)
    const totalPurchasedWallpaperArea = rollsNeededWithWastage * (rollWidth * rollLength)

    const adhesivePerSqm = options?.adhesivePerSqmWallpaper ?? 0.2
    const adhesiveNeeded = wallSurfaceArea * adhesivePerSqm

    return {
      rolls: rollsNeededWithWastage,
      adhesive: adhesiveNeeded,
      totalArea: totalPurchasedWallpaperArea, // This is the total area of wallpaper rolls purchased
      stripsCount: stripsNeededForPerimeter,
      wallSurfaceArea,
    }
  }

  /**
   * Returns a generic string identifier for the type of element this strategy applies to.
   * @remarks Wallpaper is applied to wall surfaces, which can be represented by various shapes
   *          defining the room's boundary.
   * @returns The string 'wall-surface-boundary'.
   */
  public getElementType(): string {
    return 'wall-surface-boundary' // Generic identifier for elements defining wall perimeters
  }

  /**
   * Returns the specific material type this strategy calculates.
   * @returns The string 'wallpaper'.
   */
  public getMaterialType(): string {
    return 'wallpaper'
  }
}
