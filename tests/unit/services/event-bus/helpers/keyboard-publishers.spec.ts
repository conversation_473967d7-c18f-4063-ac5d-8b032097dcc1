import type { publishKeyPressed, publishKeyReleased } from '@/services/event-bus/helpers/keyboard-publishers'
import { expect, test } from '@playwright/test'
import { AppEventType } from '@/types/services/events'

declare global {
  interface Window {
    appEventBus: {
      publish: (event: any) => void
      getEvents: () => any[]
      events: any[]
    }
    publishKeyPressed: typeof publishKeyPressed
    publishKeyReleased: typeof publishKeyReleased
    AppEventType: typeof AppEventType
  }
}

test.describe('Keyboard Publishers', () => {
  test.beforeEach(async ({ page }) => {
    await page.evaluate((AppEventType) => {
      window.AppEventType = AppEventType
      window.appEventBus = {
        events: [],
        publish(event: any) {
          this.events.push({
            ...event,
            type: event.type.toString(), // 确保类型是字符串
          })
          return true
        },
        getEvents() {
          return this.events
        },
      }

      window.publishKeyPressed = function (key: string, code: string, modifiers: any, originalEvent?: any) {
        window.appEventBus.publish({
          type: window.AppEventType.KEY_PRESSED.toString(), // 转换为字符串
          timestamp: Date.now(),
          payload: {
            key,
            code,
            modifiers,
            originalEvent,
          },
        })
      }

      window.publishKeyReleased = function (key: string, code: string, modifiers: any, originalEvent?: any) {
        window.appEventBus.publish({
          type: window.AppEventType.KEY_RELEASED.toString(), // 转换为字符串
          timestamp: Date.now(),
          payload: {
            key,
            code,
            modifiers,
            originalEvent,
          },
        })
      }
    }, AppEventType)
  })

  test('should publish key pressed event correctly', async ({ page }) => {
    const events = await page.evaluate(() => {
      const mockKeyboardEvent = {
        key: 'a',
        code: 'KeyA',
        altKey: false,
        ctrlKey: true,
        shiftKey: false,
        metaKey: false,
        preventDefault: () => {},
        stopPropagation: () => {},
      }

      window.publishKeyPressed(
        mockKeyboardEvent.key,
        mockKeyboardEvent.code,
        {
          altKey: mockKeyboardEvent.altKey,
          ctrlKey: mockKeyboardEvent.ctrlKey,
          shiftKey: mockKeyboardEvent.shiftKey,
          metaKey: mockKeyboardEvent.metaKey,
        },
        mockKeyboardEvent as KeyboardEvent,
      )

      return window.appEventBus.getEvents()
    })

    expect(events.length).toBe(1)
    expect(events[0].type).toBe(AppEventType.KEY_PRESSED.toString()) // 修改这里
    expect(events[0].payload.key).toBe('a')
    expect(events[0].payload.code).toBe('KeyA')
    expect(events[0].payload.modifiers.ctrlKey).toBe(true)
    expect(events[0].payload.modifiers.altKey).toBe(false)
    expect(events[0].payload.modifiers.shiftKey).toBe(false)
    expect(events[0].payload.modifiers.metaKey).toBe(false)
  })

  test('should publish key released event correctly', async ({ page }) => {
    const events = await page.evaluate(() => {
      const mockKeyboardEvent = {
        key: 'b',
        code: 'KeyB',
        altKey: true,
        ctrlKey: false,
        shiftKey: true,
        metaKey: false,
        preventDefault: () => {},
        stopPropagation: () => {},
      }

      window.publishKeyReleased(
        mockKeyboardEvent.key,
        mockKeyboardEvent.code,
        {
          altKey: mockKeyboardEvent.altKey,
          ctrlKey: mockKeyboardEvent.ctrlKey,
          shiftKey: mockKeyboardEvent.shiftKey,
          metaKey: mockKeyboardEvent.metaKey,
        },
        mockKeyboardEvent as KeyboardEvent,
      )

      return window.appEventBus.getEvents()
    })

    expect(events.length).toBe(1)
    expect(events[0].type).toBe('keyboard.key.released')
    expect(events[0].payload.key).toBe('b')
    expect(events[0].payload.code).toBe('KeyB')
    expect(events[0].payload.modifiers.ctrlKey).toBe(false)
    expect(events[0].payload.modifiers.altKey).toBe(true)
    expect(events[0].payload.modifiers.shiftKey).toBe(true)
    expect(events[0].payload.modifiers.metaKey).toBe(false)
  })
})
