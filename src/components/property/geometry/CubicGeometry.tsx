/**
 * Cubic Geometry Component
 *
 * Implements the standardized geometry layout for Cubic Bézier curve elements
 * according to the design document specifications.
 *
 * Layout:
 * - Key Points: Start, Control1, Control2, and End points with canvas position editing
 * - Calculation & Cost (unified layout)
 *
 * Note: Cubic elements use control points instead of standard position controls.
 * They don't support rotation as the curve orientation is defined by the control points.
 */

import type Point from '@/types/core/element/geometry/point'
import type { ShapeElement } from '@/types/core/elementDefinitions'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ElementType } from '@/types/core/elementDefinitions'
import { GeometrySection } from '../shared'

// Type guard functions for safe property access
function isPoint(value: unknown): value is Point {
  return (
    typeof value === 'object'
    && value !== null
    && typeof (value as Point).x === 'number'
    && typeof (value as Point).y === 'number'
  )
}

function getPointProperty(properties: Record<string, unknown> | undefined, key: string, defaultValue: Point): Point {
  const value = properties?.[key]
  return isPoint(value) ? value : defaultValue
}

function getPosition(element: ShapeElement): Point {
  return isPoint(element.position) ? element.position : { x: 0, y: 0 }
}

// Stable function references to avoid infinite render loops
const identityFunction = (value: number) => value

// Type definitions for store access
interface WindowWithStore {
  __ZUSTAND_SHAPES_STORE__?: {
    getState: () => {
      shapes: ShapeElement[]
      selectedShapeIds: string[]
    }
    setState: (state: {
      shapes: ShapeElement[]
      selectedShapeIds: string[]
    }) => void
  }
}

interface CubicGeometryProps {
  /** The cubic curve element being edited */
  element: ShapeElement
  /** Function to get input values with proper formatting */
  getValueForInput: (path: string) => string
  /** Function to get placeholder values */
  getPlaceholderForInput: (path: string) => string
  /** Function to handle input changes */
  handleInputChange: (path: string, value: string, isNumeric?: boolean, convertToPixels?: boolean) => void
  /** Unit name for display (e.g., "mm", "cm") */
  unitName: string
  /** Function to convert internal units to display units */
  toDisplayUnit?: (value: number) => number
  /** Function to convert display units to internal units */
  toInternalUnit?: (value: number) => number
  /** Function to update element properties directly */
  updateProperty?: (path: string, value: unknown) => void
}

/**
 * CubicGeometry component for editing cubic Bézier curve properties.
 *
 * Provides a standardized layout for cubic curve geometry editing
 * with proper grouping and responsive design. Cubic curves use
 * four control points: start, two control points, and end point with canvas position editing.
 */
export function CubicGeometry({
  element,
  getValueForInput: _getValueForInput,
  getPlaceholderForInput: _getPlaceholderForInput,
  handleInputChange: _handleInputChange,
  unitName,
  toDisplayUnit = identityFunction,
  toInternalUnit = identityFunction,
  updateProperty,
}: CubicGeometryProps) {
  // Verify this is a cubic element
  if (element.type !== ElementType.CUBIC) {
    return null
  }

  // Note: We don't need to calculate coordinates here since getValueForInput
  // and handleInputChange already handle the coordinate conversion in GeometryTransformTab

  return (
    <>
      {/* Key Points - Canvas Position */}
      <GeometrySection type="special" title="Key Points" columns={1}>
        <div className="space-y-2">
          {/* Header */}
          <Label className="text-sm font-medium">
            Canvas Position (
            {unitName}
            )
          </Label>

          {/* Points list */}
          <div className="space-y-2">
            {/* Start Point */}
            <div className="grid grid-cols-3 gap-2 items-center">
              <div className="text-xs text-muted-foreground">
                Start Point
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">X</Label>
                <Input
                  type="number"
                  value={(() => {
                    // 🔧 修复：使用类型安全的属性访问
                    const startPoint = getPointProperty(element.properties, 'start', { x: 0, y: 0, z: 0 })
                    const position = getPosition(element)
                    const absoluteX = position.x + startPoint.x
                    return Number(toDisplayUnit(absoluteX)).toFixed(2)
                  })()}
                  onChange={(e) => {
                    // 🔧 修复：使用类型安全的属性访问
                    const newCanvasValue = Number.parseFloat(e.target.value) || 0
                    const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
                    const position = getPosition(element)
                    const relativeX = newCanvasPixelValue - position.x

                    const currentStart = getPointProperty(element.properties, 'start', { x: 0, y: 0, z: 0 })
                    const updatedStart = { ...currentStart, x: relativeX }

                    // 🔧 关键修复：使用直接store更新，确保坐标控制正确工作
                    if (window.__ZUSTAND_SHAPES_STORE__) {
                      const shapesStore = window.__ZUSTAND_SHAPES_STORE__
                      const state = shapesStore.getState()

                      // 创建更新后的元素
                      const updatedElement = {
                        ...element,
                        properties: {
                          ...element.properties,
                          start: updatedStart,
                        },
                      }

                      // 更新store中的shapes数组
                      const newShapes = state.shapes.map(s =>
                        s.id === element.id ? updatedElement : s,
                      )

                      // 保持选中状态
                      shapesStore.setState({
                        ...state,
                        shapes: newShapes,
                        selectedShapeIds: [element.id],
                      })
                    }
                  }}
                  placeholder="X"
                  className="h-8 text-xs"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Y</Label>
                <Input
                  type="number"
                  value={(() => {
                    // 🔧 修复：使用类型安全的属性访问
                    const startPoint = getPointProperty(element.properties, 'start', { x: 0, y: 0, z: 0 })
                    const position = getPosition(element)
                    const absoluteY = position.y + startPoint.y
                    return Number(toDisplayUnit(absoluteY)).toFixed(2)
                  })()}
                  onChange={(e) => {
                    // 🔧 修复：使用类型安全的属性访问
                    const newCanvasValue = Number.parseFloat(e.target.value) || 0
                    const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
                    const position = getPosition(element)
                    const relativeY = newCanvasPixelValue - position.y

                    const currentStart = getPointProperty(element.properties, 'start', { x: 0, y: 0, z: 0 })
                    const updatedStart = { ...currentStart, y: relativeY }

                    // 🔧 关键修复：使用直接store更新，确保坐标控制正确工作
                    if (window.__ZUSTAND_SHAPES_STORE__) {
                      const shapesStore = window.__ZUSTAND_SHAPES_STORE__
                      const state = shapesStore.getState()

                      // 创建更新后的元素
                      const updatedElement = {
                        ...element,
                        properties: {
                          ...element.properties,
                          start: updatedStart,
                        },
                      }

                      // 更新store中的shapes数组
                      const newShapes = state.shapes.map(s =>
                        s.id === element.id ? updatedElement : s,
                      )

                      // 保持选中状态
                      shapesStore.setState({
                        ...state,
                        shapes: newShapes,
                        selectedShapeIds: [element.id],
                      })
                    }
                  }}
                  placeholder="Y"
                  className="h-8 text-xs"
                />
              </div>
            </div>

            {/* Control Point 1 */}
            <div className="grid grid-cols-3 gap-2 items-center">
              <div className="text-xs text-muted-foreground">
                Control 1
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">X</Label>
                <Input
                  type="number"
                  value={(() => {
                    // 🔧 修复：使用类型安全的属性访问
                    const control1Point = getPointProperty(element.properties, 'control1', { x: 33, y: -50, z: 0 })
                    const position = getPosition(element)
                    const absoluteX = position.x + control1Point.x
                    return Number(toDisplayUnit(absoluteX)).toFixed(2)
                  })()}
                  onChange={(e) => {
                    // 🔧 修复：使用类型安全的属性访问
                    const newCanvasValue = Number.parseFloat(e.target.value) || 0
                    const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
                    const position = getPosition(element)
                    const relativeX = newCanvasPixelValue - position.x

                    const currentControl1 = getPointProperty(element.properties, 'control1', { x: 33, y: -50, z: 0 })
                    const updatedControl1 = { ...currentControl1, x: relativeX }

                    updateProperty?.('properties.control1', updatedControl1)
                  }}
                  placeholder="X"
                  className="h-8 text-xs"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Y</Label>
                <Input
                  type="number"
                  value={(() => {
                    // 🔧 修复：使用类型安全的属性访问
                    const control1Point = getPointProperty(element.properties, 'control1', { x: 33, y: -50, z: 0 })
                    const position = getPosition(element)
                    const absoluteY = position.y + control1Point.y
                    return Number(toDisplayUnit(absoluteY)).toFixed(2)
                  })()}
                  onChange={(e) => {
                    // 🔧 修复：使用与圆弧和多线相同的简单直接模式
                    const newCanvasValue = Number.parseFloat(e.target.value) || 0
                    const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
                    const position = element.position ?? { x: 0, y: 0, z: 0 }
                    const relativeY = newCanvasPixelValue - position.y

                    const currentControl1 = element.properties?.control1 ?? { x: 33, y: -50, z: 0 }
                    const updatedControl1 = { ...currentControl1, y: relativeY }

                    updateProperty?.('properties.control1', updatedControl1)
                  }}
                  placeholder="Y"
                  className="h-8 text-xs"
                />
              </div>
            </div>

            {/* Control Point 2 */}
            <div className="grid grid-cols-3 gap-2 items-center">
              <div className="text-xs text-muted-foreground">
                Control 2
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">X</Label>
                <Input
                  type="number"
                  value={(() => {
                    // 🔧 修复：使用类型安全的属性访问
                    const control2Point = getPointProperty(element.properties, 'control2', { x: 67, y: 50, z: 0 })
                    const position = getPosition(element)
                    const absoluteX = position.x + control2Point.x
                    return Number(toDisplayUnit(absoluteX)).toFixed(2)
                  })()}
                  onChange={(e) => {
                    // 🔧 修复：使用与圆弧和多线相同的简单直接模式
                    const newCanvasValue = Number.parseFloat(e.target.value) || 0
                    const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
                    const position = element.position ?? { x: 0, y: 0, z: 0 }
                    const relativeX = newCanvasPixelValue - position.x

                    const currentControl2 = element.properties?.control2 ?? { x: 67, y: 50, z: 0 }
                    const updatedControl2 = { ...currentControl2, x: relativeX }

                    updateProperty?.('properties.control2', updatedControl2)
                  }}
                  placeholder="X"
                  className="h-8 text-xs"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Y</Label>
                <Input
                  type="number"
                  value={(() => {
                    // 🔧 修复：使用类型安全的属性访问
                    const control2Point = getPointProperty(element.properties, 'control2', { x: 67, y: 50, z: 0 })
                    const position = getPosition(element)
                    const absoluteY = position.y + control2Point.y
                    return Number(toDisplayUnit(absoluteY)).toFixed(2)
                  })()}
                  onChange={(e) => {
                    // 🔧 修复：使用与圆弧和多线相同的简单直接模式
                    const newCanvasValue = Number.parseFloat(e.target.value) || 0
                    const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
                    const position = element.position ?? { x: 0, y: 0, z: 0 }
                    const relativeY = newCanvasPixelValue - position.y

                    const currentControl2 = element.properties?.control2 ?? { x: 67, y: 50, z: 0 }
                    const updatedControl2 = { ...currentControl2, y: relativeY }

                    updateProperty?.('properties.control2', updatedControl2)
                  }}
                  placeholder="Y"
                  className="h-8 text-xs"
                />
              </div>
            </div>

            {/* End Point */}
            <div className="grid grid-cols-3 gap-2 items-center">
              <div className="text-xs text-muted-foreground">
                End Point
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">X</Label>
                <Input
                  type="number"
                  value={(() => {
                    // 🔧 修复：使用类型安全的属性访问
                    const endPoint = getPointProperty(element.properties, 'end', { x: 100, y: 0, z: 0 })
                    const position = getPosition(element)
                    const absoluteX = position.x + endPoint.x
                    return Number(toDisplayUnit(absoluteX)).toFixed(2)
                  })()}
                  onChange={(e) => {
                    // 🔧 修复：使用类型安全的属性访问
                    const newCanvasValue = Number.parseFloat(e.target.value) || 0
                    const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
                    const position = getPosition(element)
                    const relativeX = newCanvasPixelValue - position.x

                    const currentEnd = getPointProperty(element.properties, 'end', { x: 100, y: 0, z: 0 })
                    const updatedEnd = { ...currentEnd, x: relativeX }

                    // 🔧 关键修复：使用直接store更新，确保坐标控制正确工作
                    const windowWithStore = window as WindowWithStore
                    if (windowWithStore.__ZUSTAND_SHAPES_STORE__) {
                      const shapesStore = windowWithStore.__ZUSTAND_SHAPES_STORE__
                      const state = shapesStore.getState()

                      // 创建更新后的元素
                      const updatedElement = {
                        ...element,
                        properties: {
                          ...element.properties,
                          end: updatedEnd,
                        },
                      }

                      // 更新store中的shapes数组
                      const newShapes = state.shapes.map(s =>
                        s.id === element.id ? updatedElement : s,
                      )

                      // 保持选中状态
                      shapesStore.setState({
                        ...state,
                        shapes: newShapes,
                        selectedShapeIds: [element.id],
                      })
                    }
                  }}
                  placeholder="X"
                  className="h-8 text-xs"
                />
              </div>
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Y</Label>
                <Input
                  type="number"
                  value={(() => {
                    // 🔧 修复：使用类型安全的属性访问
                    const endPoint = getPointProperty(element.properties, 'end', { x: 100, y: 0, z: 0 })
                    const position = getPosition(element)
                    const absoluteY = position.y + endPoint.y
                    return Number(toDisplayUnit(absoluteY)).toFixed(2)
                  })()}
                  onChange={(e) => {
                    // 🔧 修复：使用类型安全的属性访问
                    const newCanvasValue = Number.parseFloat(e.target.value) || 0
                    const newCanvasPixelValue = toInternalUnit(newCanvasValue) || 0
                    const position = getPosition(element)
                    const relativeY = newCanvasPixelValue - position.y

                    const currentEnd = getPointProperty(element.properties, 'end', { x: 100, y: 0, z: 0 })
                    const updatedEnd = { ...currentEnd, y: relativeY }

                    // 🔧 关键修复：使用直接store更新，确保坐标控制正确工作
                    const windowWithStore = window as WindowWithStore
                    if (windowWithStore.__ZUSTAND_SHAPES_STORE__) {
                      const shapesStore = windowWithStore.__ZUSTAND_SHAPES_STORE__
                      const state = shapesStore.getState()

                      // 创建更新后的元素
                      const updatedElement = {
                        ...element,
                        properties: {
                          ...element.properties,
                          end: updatedEnd,
                        },
                      }

                      // 更新store中的shapes数组
                      const newShapes = state.shapes.map(s =>
                        s.id === element.id ? updatedElement : s,
                      )

                      // 保持选中状态
                      shapesStore.setState({
                        ...state,
                        shapes: newShapes,
                        selectedShapeIds: [element.id],
                      })
                    }
                  }}
                  placeholder="Y"
                  className="h-8 text-xs"
                />
              </div>
            </div>
          </div>
        </div>
      </GeometrySection>

      {/* Note: No standard Position or Transform sections for cubic curves */}
      {/* Curve position and orientation are fully defined by the control points */}
    </>
  )
}
