/**
 * Computation Strategy Registry
 *
 * Manages the registration and retrieval of various computation strategies
 * (e.g., for area, perimeter, cost) applicable to different element types.
 * This class implements the Strategy design pattern, allowing for flexible
 * and interchangeable computation algorithms.
 *
 * @remarks
 * An instance of `StrategyRegistry` should be created, and then specific
 * strategy objects (implementing interfaces like {@link AreaCalculatorStrategy},
 * {@link CostCalculatorStrategy}, etc.) should be registered with it, typically
 * during the application's dependency injection setup or within the main
 * compute module initialization.
 *
 * The registry uses separate maps for each type of computation strategy to ensure
 * type safety and clarity. It supports case-insensitive lookup for element types.
 *
 * @see {@link ComputeFacade} - Uses this registry to delegate computations.
 * @see {@link AreaCalculatorStrategy}
 * @see {@link PerimeterCalculatorStrategy}
 * @see {@link CostCalculatorStrategy}
 * @module core/compute/StrategyRegistry
 */

import type {
  AreaCalculatorStrategy,
  BoundingBoxCalculatorStrategy,
  ComputeOperationType,
  CostCalculatorStrategy,
  DistanceCalculatorStrategy,
  MaterialCalculatorStrategy,
  PerimeterCalculatorStrategy,
  SpacePlanningStrategy,
} from '@/types/core/compute'
import type { ElementType } from '@/types/core/elementDefinitions'

// Point-inside and transform interfaces have been removed as they are not needed for the decoration design software

// The following strategy imports are not directly used in this file
// These strategies are registered to the StrategyRegistry instance through dependency injection in other files
// These imports are kept as reference to help developers understand available strategies

/* The following imports are not directly used in this file, but are registered to the StrategyRegistry instance elsewhere
// Rectangle strategies
// import { RectangleAreaStrategy } from './strategies/area/RectangleAreaStrategy';
// import { RectanglePerimeterStrategy } from './strategies/perimeter/RectanglePerimeterStrategy';
// import { RectangleBoundingBoxStrategy } from './strategies/bounding-box/RectangleBoundingBoxStrategy';
// import { RectangleIsPointInsideStrategy } from './strategies/is-point-inside/RectangleIsPointInsideStrategy';

// Circle strategies
// import { CircleAreaStrategy } from './strategies/area/CircleAreaStrategy';
// import { CirclePerimeterStrategy } from './strategies/perimeter/CirclePerimeterStrategy';
// import { CircleBoundingBoxStrategy } from './strategies/bounding-box/CircleBoundingBoxStrategy';
// import { CircleIsPointInsideStrategy } from './strategies/is-point-inside/CircleIsPointInsideStrategy';

// Ellipse strategies
// import { EllipsePerimeterStrategy } from './strategies/perimeter/EllipsePerimeterStrategy';
// import { EllipseAreaStrategy } from './strategies/area/EllipseAreaStrategy';

// Polygon strategies
// import { PolygonAreaStrategy } from './strategies/area/PolygonAreaStrategy';
// import { PolygonPerimeterStrategy } from './strategies/perimeter/PolygonPerimeterStrategy';

// Line strategies
// import { LinePerimeterStrategy } from './strategies/perimeter/LinePerimeterStrategy';
*/

/**
 * Registry for managing and providing computation strategies.
 *
 * @remarks
 * This class allows for the registration of different calculation strategies
 * (e.g., for area, perimeter, cost) for various element types. It enables
 * the {@link ComputeFacade} to dynamically select and use the appropriate
 * strategy based on the element type and the requested operation.
 *
 * Strategies should be registered externally after an instance of this class
 * is created, typically during application setup or dependency injection.
 */
export class StrategyRegistry {
  /**
   * Stores area calculation strategies. Key: lowercase element type string.
   * @private
   */
  private areaStrategies = new Map<string, AreaCalculatorStrategy>()
  /**
   * Stores perimeter/length calculation strategies. Key: lowercase element type string.
   * @private
   */
  private perimeterStrategies = new Map<string, PerimeterCalculatorStrategy>()
  /**
   * Stores bounding box calculation strategies. Key: lowercase element type string.
   * @private
   */
  private boundingBoxStrategies = new Map<string, BoundingBoxCalculatorStrategy>()
  // Point-inside and transform strategy maps have been removed.
  /**
   * Stores distance calculation strategies. Key: lowercase element type string.
   * @private
   */
  private distanceStrategies = new Map<string, DistanceCalculatorStrategy>()
  /**
   * Stores cost calculation strategies. Key: lowercase element type string.
   * @private
   */
  private costStrategies = new Map<string, CostCalculatorStrategy>()
  /**
   * Stores material calculation strategies. Key: lowercase element type string.
   * @private
   */
  private materialStrategies = new Map<string, MaterialCalculatorStrategy>()
  /**
   * Stores space planning strategies. Key: lowercase space type string.
   * @private
   */
  private spacePlanningStrategies = new Map<string, SpacePlanningStrategy>()
  /**
   * Generic strategy store.
   * Key: element type string (lowercase) or 'default'.
   * Value: Map where key is {@link ComputeOperationType} and value is the strategy instance.
   *
   * @private
   * @deprecated Prefer using the specific, type-safe strategy maps (e.g., `areaStrategies`).
   *             This map is maintained for potential backward compatibility or highly generic scenarios
   *             but its direct use is discouraged.
   */
  // eslint-disable-next-line ts/no-explicit-any
  private strategies: Map<string, Map<string, any>> = new Map()

  /**
   * Initializes the strategy maps.
   *
   * @remarks
   * Strategy registration should be handled externally after instantiation.
   */
  constructor() {
    // Constructor is now empty. Registration happens externally (e.g., in index.ts).
    console.warn('StrategyRegistry initialized (empty).')
  }

  /**
   * Registers an area calculation strategy.
   *
   * @remarks
   * The element type from the strategy is converted to lowercase for case-insensitive storage and retrieval.
   *
   * @param strategy - The {@link AreaCalculatorStrategy} instance to register.
   */
  public registerAreaStrategy(strategy: AreaCalculatorStrategy): void {
    const elementTypes = strategy.getElementType()
    if (Array.isArray(elementTypes)) {
      elementTypes.forEach(type => this.areaStrategies.set(type.toLowerCase(), strategy))
    }
    else {
      this.areaStrategies.set(elementTypes.toLowerCase(), strategy)
    }
  }

  /**
   * Registers a perimeter calculation strategy.
   *
   * @remarks
   * The element type from the strategy is converted to lowercase for case-insensitive storage and retrieval.
   *
   * @param strategy - The {@link PerimeterCalculatorStrategy} instance to register.
   */
  public registerPerimeterStrategy(strategy: PerimeterCalculatorStrategy): void {
    const elementTypes = strategy.getElementType()
    if (Array.isArray(elementTypes)) {
      elementTypes.forEach(type => this.perimeterStrategies.set(type.toLowerCase(), strategy))
    }
    else {
      this.perimeterStrategies.set(elementTypes.toLowerCase(), strategy)
    }
  }

  /**
   * Registers a bounding box calculation strategy.
   *
   * @remarks
   * The element type from the strategy is converted to lowercase for case-insensitive storage and retrieval.
   *
   * @param strategy - The {@link BoundingBoxCalculatorStrategy} instance to register.
   */
  public registerBoundingBoxStrategy(strategy: BoundingBoxCalculatorStrategy): void {
    const elementTypes = strategy.getElementType()
    if (Array.isArray(elementTypes)) {
      elementTypes.forEach(type => this.boundingBoxStrategies.set(type.toLowerCase(), strategy))
    }
    else {
      this.boundingBoxStrategies.set(elementTypes.toLowerCase(), strategy)
    }
  }

  // registerIsPointInsideStrategy and registerTransformStrategy methods have been removed.

  /**
   * Bulk registers multiple core geometric strategies (area, perimeter, bounding box)
   * for the same element type at once.
   *
   * @remarks
   * This is a convenience method. It assumes all provided strategies are for the same element type,
   * as determined by each strategy's `getElementType()` method.
   *
   * @param areaStrategy - An {@link AreaCalculatorStrategy} instance.
   * @param perimeterStrategy - A {@link PerimeterCalculatorStrategy} instance.
   * @param boundingBoxStrategy - A {@link BoundingBoxCalculatorStrategy} instance.
   */
  public registerStrategies(
    areaStrategy: AreaCalculatorStrategy,
    perimeterStrategy: PerimeterCalculatorStrategy,
    boundingBoxStrategy: BoundingBoxCalculatorStrategy,
    // Potentially add other common strategies here if needed for bulk registration
  ): void {
    this.registerAreaStrategy(areaStrategy)
    this.registerPerimeterStrategy(perimeterStrategy)
    this.registerBoundingBoxStrategy(boundingBoxStrategy)
  }

  /**
   * Retrieves the area calculation strategy for the specified element type.
   *
   * @remarks
   * Performs a case-insensitive lookup based on the element type string.
   *
   * @param elementType - The element type string (e.g., 'rectangle', 'circle').
   * @returns The corresponding {@link AreaCalculatorStrategy}.
   * @throws {@link Error} if no strategy is found for the specified element type.
   */
  public getAreaStrategy(elementType: string): AreaCalculatorStrategy {
    const strategy = this.areaStrategies.get(elementType.toLowerCase())
    if (!strategy) {
      throw new Error(`No area calculation strategy found for element type: ${elementType}`)
    }
    return strategy
  }

  /**
   * Retrieves the perimeter calculation strategy for the specified element type.
   *
   * @remarks
   * Performs a case-insensitive lookup based on the element type string.
   *
   * @param elementType - The element type string (e.g., 'rectangle', 'line').
   * @returns The corresponding {@link PerimeterCalculatorStrategy}.
   * @throws {@link Error} if no strategy is found for the specified element type.
   */
  public getPerimeterStrategy(elementType: string): PerimeterCalculatorStrategy {
    const strategy = this.perimeterStrategies.get(elementType.toLowerCase())
    if (!strategy) {
      throw new Error(`No perimeter calculation strategy found for element type: ${elementType}`)
    }
    return strategy
  }

  /**
   * Retrieves the bounding box calculation strategy for the specified element type.
   *
   * @remarks
   * Performs a case-insensitive lookup based on the element type string.
   *
   * @param elementType - The element type string (e.g., 'ellipse', 'polygon').
   * @returns The corresponding {@link BoundingBoxCalculatorStrategy}.
   * @throws {@link Error} if no strategy is found for the specified element type.
   */
  public getBoundingBoxStrategy(elementType: string): BoundingBoxCalculatorStrategy {
    const strategy = this.boundingBoxStrategies.get(elementType.toLowerCase())
    if (!strategy) {
      throw new Error(`No bounding box calculation strategy found for element type: ${elementType}`)
    }
    return strategy
  }

  // getIsPointInsideStrategy and getTransformStrategy methods have been removed.

  /**
   * Checks if a specific strategy exists for the given element type and calculation type.
   *
   * @remarks
   * Performs a case-insensitive lookup for the element type.
   *
   * @param elementType - The element type string to check (case-insensitive).
   * @param calculationType - The calculation type identifier (e.g., 'area', 'cost').
   * @returns `true` if a strategy is registered for the combination, `false` otherwise.
   */
  public hasStrategy(elementType: string, calculationType: 'area' | 'perimeter' | 'boundingBox' | 'distance' | 'cost' | 'material' | 'spacePlanning'): boolean {
    const normalizedElementType = elementType.toLowerCase()

    switch (calculationType) {
      case 'area':
        return this.areaStrategies.has(normalizedElementType)
      case 'perimeter':
        return this.perimeterStrategies.has(normalizedElementType)
      case 'boundingBox':
        return this.boundingBoxStrategies.has(normalizedElementType)
      case 'distance':
        return this.distanceStrategies.has(normalizedElementType)
      case 'cost':
        return this.costStrategies.has(normalizedElementType)
      case 'material':
        return this.materialStrategies.has(normalizedElementType)
      case 'spacePlanning': // For space planning, elementType might refer to a space type rather than an element.
        return this.spacePlanningStrategies.has(normalizedElementType)
      /* istanbul ignore next */
      default:
        // This case should be unreachable due to the explicit type union for calculationType.
        // Adding a console warning for robustness during development.
        console.warn(`[StrategyRegistry] Unknown calculation type encountered in hasStrategy: ${String(calculationType)}`)
        return false
    }
  }

  /**
   * Gets a list of element types (as strings, lowercase) that have a registered strategy
   * for the specified calculation type.
   *
   * @param calculationType - The calculation type identifier (e.g., 'area', 'perimeter').
   * @returns An array of lowercase element type strings for which the specified calculation is supported.
   *          For 'spacePlanning', it returns supported space types.
   */
  public getSupportedElementTypes(calculationType: 'area' | 'perimeter' | 'boundingBox' | 'distance' | 'cost' | 'material' | 'spacePlanning'): string[] {
    switch (calculationType) {
      case 'area':
        return Array.from(this.areaStrategies.keys())
      case 'perimeter':
        return Array.from(this.perimeterStrategies.keys())
      case 'boundingBox':
        return Array.from(this.boundingBoxStrategies.keys())
      case 'distance':
        return Array.from(this.distanceStrategies.keys())
      case 'cost':
        return Array.from(this.costStrategies.keys())
      case 'material':
        return Array.from(this.materialStrategies.keys())
      case 'spacePlanning':
        return Array.from(this.spacePlanningStrategies.keys())
        /* istanbul ignore next */
      default:
        // This case should be unreachable.
        console.warn(`[StrategyRegistry] Unknown calculation type encountered in getSupportedElementTypes: ${String(calculationType)}`)
        return []
    }
  }

  /**
   * Registers a distance calculation strategy.
   *
   * @remarks
   * The element type from the strategy is converted to lowercase for case-insensitive storage and retrieval.
   *
   * @param strategy - The {@link DistanceCalculatorStrategy} instance to register.
   */
  public registerDistanceStrategy(strategy: DistanceCalculatorStrategy): void {
    const elementTypes = strategy.getElementType()
    if (Array.isArray(elementTypes)) {
      elementTypes.forEach(type => this.distanceStrategies.set(type.toLowerCase(), strategy))
    }
    else {
      this.distanceStrategies.set(elementTypes.toLowerCase(), strategy)
    }
  }

  /**
   * Registers a cost calculation strategy.
   *
   * @remarks
   * The element type from the strategy is converted to lowercase for case-insensitive storage and retrieval.
   *
   * @param strategy - The {@link CostCalculatorStrategy} instance to register.
   */
  public registerCostStrategy(strategy: CostCalculatorStrategy): void {
    const elementTypes = strategy.getElementType()
    if (Array.isArray(elementTypes)) {
      elementTypes.forEach(type => this.costStrategies.set(type.toLowerCase(), strategy))
    }
    else {
      this.costStrategies.set(elementTypes.toLowerCase(), strategy)
    }
  }

  /**
   * Registers a material calculation strategy.
   *
   * @remarks
   * The element type from the strategy is converted to lowercase for case-insensitive storage and retrieval.
   *
   * @param strategy - The {@link MaterialCalculatorStrategy} instance to register.
   */
  public registerMaterialStrategy(strategy: MaterialCalculatorStrategy): void {
    const elementTypes = strategy.getElementType()
    if (Array.isArray(elementTypes)) {
      elementTypes.forEach(type => this.materialStrategies.set(type.toLowerCase(), strategy))
    }
    else {
      this.materialStrategies.set(elementTypes.toLowerCase(), strategy)
    }
  }

  /**
   * Registers a space planning strategy.
   *
   * @remarks
   * The space type (e.g., 'kitchen', 'bedroom') from the strategy is converted to lowercase
   * for case-insensitive storage and retrieval.
   *
   * @param strategy - The {@link SpacePlanningStrategy} instance to register.
   */
  public registerSpacePlanningStrategy(strategy: SpacePlanningStrategy): void {
    const spaceTypeKey = strategy.getSpaceType().toLowerCase()
    this.spacePlanningStrategies.set(spaceTypeKey, strategy)
  }

  /**
   * Retrieves the distance calculation strategy for the specified element type.
   *
   * @remarks
   * Performs a case-insensitive lookup.
   *
   * @param elementType - The element type string.
   * @returns The corresponding {@link DistanceCalculatorStrategy}.
   * @throws {@link Error} if no strategy is found.
   */
  public getDistanceStrategy(elementType: string): DistanceCalculatorStrategy {
    const strategy = this.distanceStrategies.get(elementType.toLowerCase())
    if (!strategy) {
      throw new Error(`No distance calculation strategy found for element type: ${elementType}`)
    }
    return strategy
  }

  /**
   * Retrieves the cost calculation strategy for the specified element type.
   *
   * @remarks
   * Performs a case-insensitive lookup.
   *
   * @param elementType - The element type string.
   * @returns The corresponding {@link CostCalculatorStrategy}.
   * @throws {@link Error} if no strategy is found.
   */
  public getCostStrategy(elementType: string): CostCalculatorStrategy {
    const strategy = this.costStrategies.get(elementType.toLowerCase())
    if (!strategy) {
      throw new Error(`No cost calculation strategy found for element type: ${elementType}`)
    }
    return strategy
  }

  /**
   * Retrieves the material calculation strategy for the specified element type.
   *
   * @remarks
   * Performs a case-insensitive lookup.
   *
   * @param elementType - The element type string.
   * @returns The corresponding {@link MaterialCalculatorStrategy}.
   * @throws {@link Error} if no strategy is found.
   */
  public getMaterialStrategy(elementType: string): MaterialCalculatorStrategy {
    const strategy = this.materialStrategies.get(elementType.toLowerCase())
    if (!strategy) {
      throw new Error(`No material calculation strategy found for element type: ${elementType}`)
    }
    return strategy
  }

  /**
   * Retrieves the space planning strategy for the specified space type.
   *
   * @remarks
   * Performs a case-insensitive lookup based on the space type string (e.g., 'kitchen').
   *
   * @param spaceType - The space type string.
   * @returns The corresponding {@link SpacePlanningStrategy}.
   * @throws {@link Error} if no strategy is found.
   */
  public getSpacePlanningStrategy(spaceType: string): SpacePlanningStrategy {
    const strategy = this.spacePlanningStrategies.get(spaceType.toLowerCase())
    if (!strategy) {
      throw new Error(`No space planning strategy found for space type: ${spaceType}`)
    }
    return strategy
  }

  /**
   * Registers a computation strategy instance using a generic, less type-safe map.
   *
   * @param elementType - The type of element the strategy applies to (from {@link ElementType}),
   *                      or 'default' for a fallback strategy.
   * @param operation - The type of computation operation (from {@link ComputeOperationType}).
   * @param strategy - The strategy instance. Due to the generic nature of this deprecated method,
   *                   the type is `any`.
   * @deprecated This method uses a generic, less type-safe map and is intended for removal.
   *             Prefer using the specific `registerAreaStrategy`, `registerPerimeterStrategy`, etc., methods
   *             for better type safety and clarity.
   */
  register(
    elementType: ElementType | 'default', // Keep ElementType for consistency with original signature
    operation: ComputeOperationType,
    // eslint-disable-next-line ts/no-explicit-any
    strategy: any, // Strategy instance, type `any` due to the generic nature of this deprecated method
  ): void {
    console.warn('[StrategyRegistry] The generic register() method is deprecated. Please use specific register<CalculationType>Strategy methods for type safety.')
    const elementTypeKey = typeof elementType === 'string' ? elementType.toLowerCase() : elementType // Handle 'default' or ElementType enum
    let shapeMap = this.strategies.get(elementTypeKey)
    if (!shapeMap) {
      // eslint-disable-next-line ts/no-explicit-any
      shapeMap = new Map<string, any>()
      this.strategies.set(elementTypeKey, shapeMap)
    }
    shapeMap.set(operation, strategy)
    // console.log(`[StrategyRegistry] Registered generic strategy for ${elementTypeKey} - ${operation}`);
  }

  /**
   * Retrieves a computation strategy from the generic map, with fallback to a 'default' strategy.
   *
   * @param elementType - The type of element (from {@link ElementType}).
   * @param operation - The type of computation operation (from {@link ComputeOperationType}).
   * @returns The corresponding strategy instance if found (including fallback to 'default'),
   *          or `undefined` if no specific or default strategy is registered for the operation.
   *          The return type is `any` due to the generic nature of this deprecated method.
   * @deprecated This method uses a generic, less type-safe map and is intended for removal.
   *             Prefer using the specific `getAreaStrategy`, `getPerimeterStrategy`, etc., methods
   *             for better type safety and clarity.
   */
  getStrategy(
    elementType: ElementType, // Keep ElementType for consistency
    operation: ComputeOperationType,
  // eslint-disable-next-line ts/no-explicit-any
  ): any | undefined { // Return type `any` due to the generic nature of this deprecated method
    console.warn('[StrategyRegistry] The generic getStrategy() method is deprecated. Please use specific get<CalculationType>Strategy methods for type safety.')
    const elementTypeKey = elementType.toLowerCase()
    const shapeMap = this.strategies.get(elementTypeKey)
    // eslint-disable-next-line ts/no-unsafe-assignment, ts/no-explicit-any
    let strategy: any = shapeMap?.get(operation)

    if (strategy === null || strategy === undefined) {
      const defaultMap = this.strategies.get('default')
      // eslint-disable-next-line ts/no-unsafe-assignment
      strategy = defaultMap?.get(operation)
      // if (strategy) {
      //   console.debug(`[StrategyRegistry] Using default strategy for ${elementTypeKey} - ${operation}`);
      // }
    }

    // if (!strategy) {
    //   console.warn(`[StrategyRegistry] Generic strategy not found for ${elementTypeKey} - ${operation} (including default)`);
    // }
    return strategy
  }
}

// Singleton instance creation and export has been removed.
// Consumers should instantiate StrategyRegistry and register strategies as needed.
