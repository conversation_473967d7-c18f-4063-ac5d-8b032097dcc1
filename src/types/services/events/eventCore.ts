/**
 * Event Core Types
 *
 * Defines the core types for the event system, including base event interfaces,
 * event bus interface, and related configuration options.
 *
 * @module types/services/events
 */

import type { AppEventType } from './eventTypes'

// ----------------------
// Basic event types
// ----------------------

/**
 * Base event interface - foundation for all event types
 */
export interface BaseEvent {
  /** Event type */
  type: AppEventType | string

  /** Event timestamp (optional) */
  timestamp?: number

  /** Event payload */
  payload: unknown
}

/**
 * Base event interface with required timestamp (for internal use)
 */
export interface BaseEventWithTimestamp extends BaseEvent {
  timestamp: number
}

/**
 * Generic event interface - provides type-safe event payloads
 */
export interface TypedEvent<T extends Record<string, unknown>> extends BaseEvent {
  payload: T
}

// ----------------------
// Event handling related types
// ----------------------

/**
 * Event handler type - supports generic event types
 */
export type EventHandler<T extends BaseEvent = BaseEvent> = (event: T) => void

/**
 * Event subscription options
 */
export interface EventSubscriptionOptions {
  /** Whether to execute only once */
  once?: boolean

  /** Priority (higher priority handlers execute first) */
  priority?: number

  /** Context object for the handler */
  context?: unknown

  /** Subscription description */
  description?: string

  /** Filter function - determines whether to execute the handler */
  filter?: <T extends BaseEvent>(event: T) => boolean

  /** Whether to execute asynchronously */
  async?: boolean

  /** Debounce time (milliseconds) */
  debounce?: number

  /** Throttle time (milliseconds) */
  throttle?: number
}

/**
 * Event handler with options
 */
export interface EventHandlerWithOptions {
  handler: EventHandler
  options: EventSubscriptionOptions
}

// ----------------------
// Event bus configuration and interface
// ----------------------

/**
 * Event bus configuration options
 */
export interface EventBusConfig {
  /** Enable event logging */
  enableLogging?: boolean

  /** Default handler priority */
  defaultPriority?: number

  /** Maximum number of async handlers */
  maxAsyncHandlers?: number

  /** Default debounce time (milliseconds) */
  defaultDebounceTime?: number

  /** Default throttle time (milliseconds) */
  defaultThrottleTime?: number

  /** Whether to enable performance tracking */
  enablePerformanceTracking?: boolean

  /** Maximum number of handlers per event type */
  maxHandlersPerEventType?: number
}

/**
 * Event bus base interface - provides core subscription and publishing functionality
 *
 * @template EventTypes - Type of event keys (string literals)
 * @template EventPayload - Type of event payloads (must extend BaseEvent)
 */
export interface EventBus<
  EventMap extends Record<string, BaseEvent> = Record<string, BaseEvent>, // EventMap maps event type string to event payload type
> {
  /**
   * Subscribe to an event
   * @param eventType Event type (a key from EventMap)
   * @param handler Event handler for the specific event type
   * @param options Subscription options
   * @returns Unsubscribe function
   */
  subscribe: <K extends keyof EventMap>(
    eventType: K,
    handler: EventHandler<EventMap[K]>,
    options?: EventSubscriptionOptions
  ) => () => void

  /**
   * Publish an event
   * @param event Event data (an instance of a type in EventMap)
   * @returns Whether the event was successfully published
   */
  publish: <K extends keyof EventMap>(event: EventMap[K]) => boolean

  /**
   * Unsubscribe from an event
   * @param eventType Event type (a key from EventMap)
   * @param handler Event handler for the specific event type
   * @returns Whether the handler was successfully unsubscribed
   */
  unsubscribe: <K extends keyof EventMap>(eventType: K, handler: EventHandler<EventMap[K]>) => boolean

  /**
   * Unsubscribe all handlers for an event type
   * @param eventType Event type (a key from EventMap)
   * @returns Whether any handlers were unsubscribed
   */
  unsubscribeAll: (eventType: keyof EventMap) => boolean

  /**
   * Clear all subscriptions
   */
  clear: () => void

  /**
   * Reset the event bus
   */
  reset: () => void

  /**
   * Get all subscriptions
   * @returns Map of event types to handlers
   */
  getSubscriptions: () => Map<keyof EventMap, Array<{
    handler: EventHandler
    options: EventSubscriptionOptions
  }>>

  /**
   * Configure the event bus
   * @param config Configuration options
   */
  configure?: (config: EventBusConfig) => void
}

/**
 * Extended event bus interface - provides more convenient methods
 *
 * @template EventTypes - Type of event keys (string literals)
 * @template EventPayload - Type of event payloads (must extend BaseEvent)
 */
export interface ExtendedEventBus<
  EventMap extends Record<string, BaseEvent> = Record<string, BaseEvent>,
> extends EventBus<EventMap> {
  /**
   * Configure the event bus
   * @param config Configuration options
   */
  configure: (config: Partial<EventBusConfig>) => void

  /**
   * Alias for subscribe
   */
  on: <K extends keyof EventMap>(
    eventType: K,
    handler: EventHandler<EventMap[K]>,
    options?: EventSubscriptionOptions
  ) => () => void

  /**
   * Alias for unsubscribe
   */
  off: <K extends keyof EventMap>(
    eventType: K,
    handler: EventHandler<EventMap[K]>
  ) => boolean

  /**
   * Alias for publish
   */
  emit: <K extends keyof EventMap>(event: EventMap[K]) => boolean

  /**
   * Publish an event asynchronously
   */
  publishAsync: <K extends keyof EventMap>(event: EventMap[K]) => Promise<unknown[]>

  /**
   * Alias for publishAsync
   */
  emitAsync: <K extends keyof EventMap>(event: EventMap[K]) => Promise<unknown[]>

  /**
   * Subscribe to an event once
   */
  once: <K extends keyof EventMap>(
    eventType: K,
    handler: EventHandler<EventMap[K]>,
    options?: Omit<EventSubscriptionOptions, 'once'>
  ) => () => void
}
