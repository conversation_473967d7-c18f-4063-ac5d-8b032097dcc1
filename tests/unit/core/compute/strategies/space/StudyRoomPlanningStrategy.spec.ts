import { beforeEach, describe, expect, it, vi } from 'vitest'
import { StudyRoomPlanningStrategy } from '@/core/compute/strategies/space/StudyRoomPlanningStrategy'
import { CoreError } from '@/services/errors'

describe('studyRoomPlanningStrategy', () => {
  let strategy: StudyRoomPlanningStrategy
  let mockElements: any[]
  let mockRoomBoundary: any
  let mockDeskElement: any
  let mockChairElement: any
  let mockPathways: any[]

  beforeEach(() => {
    strategy = new StudyRoomPlanningStrategy()

    // 创建模拟房间边界
    mockRoomBoundary = {
      id: 'room-boundary',
      type: 'room',
      getBoundingBox: vi.fn().mockReturnValue({
        x: 0,
        y: 0,
        width: 4,
        height: 5,
      }),
      getArea: vi.fn().mockReturnValue(20), // 20平方米
    }

    // 创建模拟书桌
    mockDeskElement = {
      id: 'desk',
      type: 'desk',
      getBoundingBox: vi.fn().mockReturnValue({
        x: 1,
        y: 1,
        width: 1.5,
        height: 0.7,
      }),
    }

    // 创建模拟椅子
    mockChairElement = {
      id: 'chair',
      type: 'chair',
      getBoundingBox: vi.fn().mockReturnValue({
        x: 1.2,
        y: 1.8,
        width: 0.5,
        height: 0.5,
      }),
    }

    // 创建模拟元素数组
    mockElements = [
      mockRoomBoundary,
      mockDeskElement,
      mockChairElement,
      {
        id: 'bookshelf',
        type: 'bookshelf',
        getBoundingBox: vi.fn().mockReturnValue({
          x: 3,
          y: 0.5,
          width: 1,
          height: 0.4,
        }),
        levels: 5, // 5层书架
      },
    ]

    // 创建模拟通道
    mockPathways = [
      {
        start: { x: 0, y: 2.5 },
        end: { x: 4, y: 2.5 },
      },
    ]
  })

  it('should be defined', () => {
    expect(strategy).toBeDefined()
  })

  it('should return the correct space type', () => {
    expect(strategy.getSpaceType()).toBe('study')
  })

  it('should calculate space utilization correctly', () => {
    const utilization = strategy.calculateSpaceUtilization(mockElements, mockRoomBoundary)

    expect(utilization).toBeDefined()
    expect(typeof utilization).toBe('number')
    expect(utilization).toBeGreaterThan(0)
    expect(utilization).toBeLessThanOrEqual(1)
  })

  it('should check pathway width correctly', () => {
    const minWidth = 0.8
    const result = strategy.checkPathwayWidth(mockElements, mockPathways, minWidth)

    expect(result).toBeDefined()
    expect(Array.isArray(result)).toBe(true)
    expect(result.length).toBe(1)
    expect(result[0].isValid).toBeDefined()
  })

  it('should evaluate ergonomics correctly', () => {
    const result = strategy.evaluateErgonomics(mockElements, mockDeskElement, mockChairElement)

    expect(result).toBeDefined()
    expect(result.isValid).toBeDefined()
    expect(Array.isArray(result.issues)).toBe(true)
    expect(Array.isArray(result.recommendations)).toBe(true)
    expect(result.recommendations.length).toBeGreaterThan(0)
  })

  it('should calculate bookshelf capacity correctly', () => {
    const bookshelfElements = mockElements.filter(el => el.type === 'bookshelf')
    const result = strategy.calculateBookshelfCapacity(mockElements, bookshelfElements)

    expect(result).toBeDefined()
    expect(typeof result.totalShelfLength).toBe('number')
    expect(typeof result.estimatedBookCapacity).toBe('number')
    expect(Array.isArray(result.recommendations)).toBe(true)
  })

  it('should evaluate lighting correctly', () => {
    const lightingElements = [
      {
        id: 'ceiling-light',
        type: 'light',
        lightType: 'ceiling',
        getBoundingBox: vi.fn().mockReturnValue({
          x: 2,
          y: 2.5,
          width: 0.5,
          height: 0.5,
        }),
      },
    ]

    const result = strategy.evaluateLighting(mockElements, lightingElements)

    expect(result).toBeDefined()
    expect(result.isValid).toBeDefined()
    expect(Array.isArray(result.issues)).toBe(true)
    expect(Array.isArray(result.recommendations)).toBe(true)
  })

  it('should throw error for invalid room boundary', () => {
    const invalidRoomBoundary = { id: 'invalid' }

    expect(() => {
      strategy.calculateSpaceUtilization(mockElements, invalidRoomBoundary as any)
    }).toThrow(CoreError)
  })

  it('should throw error for invalid pathway parameters', () => {
    expect(() => {
      strategy.checkPathwayWidth(null as any, mockPathways, 0.8)
    }).toThrow(CoreError)

    expect(() => {
      strategy.checkPathwayWidth(mockElements, null as any, 0.8)
    }).toThrow(CoreError)

    expect(() => {
      strategy.checkPathwayWidth(mockElements, mockPathways, -1)
    }).toThrow(CoreError)
  })
})
