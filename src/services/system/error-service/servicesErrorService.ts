/**
 * Extends the base error service to provide additional service-specific functionality
 * for handling and logging errors originating from different application services.
 *
 * @module services/system/error-service/servicesErrorService
 */

import type { ErrorContext, ErrorDetails as ErrorDetailsType } from '@/types/services/core/errorService'
import type { AppEventMap, EventBus } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import { ErrorSeverity, ErrorType } from '@/types/services/errors' // ICoreError removed as unused
import { ErrorService } from './errorService'
// createErrorContext is not exported from errorUtils.ts, context will be created directly.

/**
 * Extends the base {@link ErrorService} to provide more specific methods for handling
 * errors that originate from other services within the application.
 * It allows for more detailed context when logging service-related errors.
 */
export class ServicesErrorService extends ErrorService {
  /**
   * Creates an instance of ServicesErrorService.
   * @param {EventBus<AppEventMap>} eventBus - The application's event bus instance.
   * @param {LoggerService} logger - The logger service instance.
   */
  constructor(
    eventBus: EventBus<AppEventMap>,
    logger: LoggerService,
  ) {
    super(eventBus, logger)
    this.logger.info('[ServicesErrorService] Initialized.')
  }

  /**
   * Handles an error originating from a specific service.
   * It constructs a detailed error context and uses the base handleError method.
   * @param {string} serviceName - The name of the service where the error occurred.
   * @param {Error} error - The error object.
   * @param {string} operation - The operation being performed when the error occurred.
   * @param {Record<string, any>} [metadata] - Additional metadata about the error.
   */
  public handleServiceError(
    serviceName: string,
    error: Error,
    operation: string,
    metadata?: Record<string, unknown>,
  ): void {
    const context: ErrorContext = {
      component: `Service:${serviceName}`,
      operation,
      metadata,
      stack: error.stack,
    }

    const errorDetails: ErrorDetailsType = {
      message: error.message,
      code: (error as Error & { code?: string }).code ?? ErrorType.Runtime.toString(), // Use PascalCase
      originalError: error,
      context,
      severity: ErrorSeverity.High, // Default severity for service errors
    }
    super.handleError(errorDetails)
  }

  /**
   * Logs a warning message specific to a service operation.
   * @param {string} serviceName - The name of the service.
   * @param {string} message - The warning message.
   * @param {string} operation - The operation during which the warning occurred.
   * @param {Record<string, unknown>} [metadata] - Additional metadata.
   */
  public logServiceWarning(
    serviceName: string,
    message: string,
    operation: string,
    metadata?: Record<string, unknown>,
  ): void {
    const context: ErrorContext = {
      component: `Service:${serviceName}`,
      operation,
      metadata,
    }

    const warningDetails: ErrorDetailsType = {
      message,
      code: ErrorType.Warning.toString(), // Use PascalCase
      severity: ErrorSeverity.Medium,
      context,
    }
    this.logServiceErrorDetails(warningDetails)
  }

  /**
   * Logs service error details using the base logger service, formatting the message
   * and choosing the log level based on severity.
   * @private
   * @param {ErrorDetailsType} details - The error details to log.
   */
  private logServiceErrorDetails(details: ErrorDetailsType): void {
    // This method now primarily uses the logger, as super.logError also calls logErrorInternal.
    // To avoid double logging from the base class's logErrorInternal, we can call logger directly here.
    // super.logError(details); // This would call logErrorInternal in the base class.

    const severity = details.severity ?? ErrorSeverity.Medium // Use PascalCase
    const code = (details.code !== null && details.code !== undefined && details.code !== '') ? details.code : ErrorType.UnknownError.toString() // Use PascalCase
    const logMessage = `Service Log [${code}][${severity}]: ${details.message}`

    const logContext = {
      ...(details.context || {}),
      originalError: details.originalError, // Ensure originalError is passed if present
    }

    switch (severity) {
      case ErrorSeverity.Critical: // Use PascalCase
      case ErrorSeverity.High: // Use PascalCase
        this.logger.error(logMessage, logContext)
        break
      case ErrorSeverity.Medium: // Use PascalCase
        this.logger.warn(logMessage, logContext)
        break
      case ErrorSeverity.Low: // Use PascalCase
      default:
        this.logger.info(logMessage, logContext)
        break
    }
  }
  // Removed unused and deprecated private method logServiceError
}
