// Import the interfaces
import type {
  CanvasMouseMoveEvent,
  ModalExportOpenEvent,
  ModalTemplateOpenEvent,
  NotificationAddEvent,
  SelectionModeChangeEvent,
  ToastShowEvent,
  ToolChangeEvent,
  ViewPanToggleEvent,
  ViewZoomEvent,
} from '@/types/services/events/uiEvents'

import { describe, expect, it } from 'vitest'

// Import AppEventType for type checking
import { AppEventType } from '@/types/services/events/eventTypes'

describe('uI Event Types', () => {
  it('should allow declaration using ToolChangeEvent interface', () => {
    const event: ToolChangeEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: ToolChangeEvent = { type: AppEventType.TOOL_CHANGE, payload: { tool: 'select' } }
    expect(example.type).toBe(AppEventType.TOOL_CHANGE)
    expect(example.payload.tool).toBe('select')
  })

  it('should allow declaration using ViewPanToggleEvent interface', () => {
    const event: ViewPanToggleEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: ViewPanToggleEvent = { type: AppEventType.VIEW_PAN_TOGGLE, payload: { enabled: true } }
    expect(example.type).toBe(AppEventType.VIEW_PAN_TOGGLE)
    expect(example.payload.enabled).toBe(true)
  })

  it('should allow declaration using ViewZoomEvent interface', () => {
    const event: ViewZoomEvent | undefined = undefined
    expect(event).toBeUndefined()
    const zoomIn: ViewZoomEvent = { type: AppEventType.VIEW_ZOOM_IN, payload: { scale: 1.2 } }
    expect(zoomIn.type).toBe(AppEventType.VIEW_ZOOM_IN)
    expect(zoomIn.payload.scale).toBe(1.2)
    const zoomReset: ViewZoomEvent = { type: AppEventType.VIEW_ZOOM_RESET, payload: {} }
    expect(zoomReset.type).toBe(AppEventType.VIEW_ZOOM_RESET)
    expect(zoomReset.payload.scale).toBeUndefined()
  })

  it('should allow declaration using SelectionModeChangeEvent interface', () => {
    const event: SelectionModeChangeEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: SelectionModeChangeEvent = { type: AppEventType.SELECTION_MODE_CHANGE, payload: { mode: 'lasso' } }
    expect(example.type).toBe(AppEventType.SELECTION_MODE_CHANGE)
    expect(example.payload.mode).toBe('lasso')
  })

  it('should allow declaration using CanvasMouseMoveEvent interface', () => {
    const event: CanvasMouseMoveEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: CanvasMouseMoveEvent = { type: AppEventType.CANVAS_MOUSE_MOVE, payload: { x: 100, y: 200, hoveredElementId: 'shape-1' } }
    expect(example.type).toBe(AppEventType.CANVAS_MOUSE_MOVE)
    expect(example.payload.x).toBe(100)
    expect(example.payload.hoveredElementId).toBe('shape-1')
  })

  it('should allow declaration using ToastShowEvent interface', () => {
    const event: ToastShowEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: ToastShowEvent = { type: AppEventType.TOAST_SHOW, payload: { message: 'Saved!', type: 'success' } }
    expect(example.type).toBe(AppEventType.TOAST_SHOW)
    expect(example.payload.message).toBe('Saved!')
    expect(example.payload.type).toBe('success')
  })

  it('should allow declaration using NotificationAddEvent interface', () => {
    const event: NotificationAddEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: NotificationAddEvent = { type: AppEventType.NOTIFICATION_ADD, payload: { message: 'Error occurred', type: 'error' } }
    expect(example.type).toBe(AppEventType.NOTIFICATION_ADD)
    expect(example.payload.type).toBe('error')
  })

  it('should allow declaration using ModalExportOpenEvent interface', () => {
    const event: ModalExportOpenEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: ModalExportOpenEvent = { type: AppEventType.MODAL_EXPORT_OPEN, payload: {} }
    expect(example.type).toBe(AppEventType.MODAL_EXPORT_OPEN)
    expect(example.payload).toEqual({})
  })

  it('should allow declaration using ModalTemplateOpenEvent interface', () => {
    const event: ModalTemplateOpenEvent | undefined = undefined
    expect(event).toBeUndefined()
    const example: ModalTemplateOpenEvent = { type: AppEventType.MODAL_TEMPLATE_OPEN, payload: {} }
    expect(example.type).toBe(AppEventType.MODAL_TEMPLATE_OPEN)
    expect(example.payload).toEqual({})
  })
})
