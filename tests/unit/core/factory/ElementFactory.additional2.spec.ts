/**
 * @file ElementFactory.additional2.spec.ts
 * @description Additional unit tests for ElementFactory
 */

import { beforeEach, describe, expect, it, vi } from 'vitest'
import { CoreError } from '@/core/errors'
import { ElementFactory } from '@/core/factory/ElementFactory'
import { Point } from '@/types/core/element/geometry/point'
import { ElementType } from '@/types/core/shape'

// Mock creators
const mockRectangleCreator = {
  create: vi.fn(),
  createDefault: vi.fn(),
}

const mockCircleCreator = {
  create: vi.fn(),
  createDefault: vi.fn(),
}

const mockLineCreator = {
  create: vi.fn(),
  createDefault: vi.fn(),
}

const mockPolygonCreator = {
  create: vi.fn(),
  createDefault: vi.fn(),
}

const mockEllipseCreator = {
  create: vi.fn(),
  createDefault: vi.fn(),
}

// Mock path factories
const mockArcPathFactory = vi.fn()
const mockBezierPathFactory = vi.fn()
const mockQuadraticPathFactory = vi.fn()

describe('elementFactory - Additional Tests 2', () => {
  let factory: ElementFactory

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()

    // Create mock return values
    mockRectangleCreator.create.mockResolvedValue({ id: 'rect1', type: ElementType.RECTANGLE })
    mockRectangleCreator.createDefault.mockResolvedValue({ id: 'rect1', type: ElementType.RECTANGLE })

    mockCircleCreator.create.mockResolvedValue({ id: 'circle1', type: ElementType.CIRCLE })
    mockCircleCreator.createDefault.mockResolvedValue({ id: 'circle1', type: ElementType.CIRCLE })

    mockLineCreator.create.mockResolvedValue({ id: 'line1', type: ElementType.LINE })
    mockLineCreator.createDefault.mockResolvedValue({ id: 'line1', type: ElementType.LINE })

    mockPolygonCreator.create.mockResolvedValue({ id: 'polygon1', type: ElementType.POLYGON })
    mockPolygonCreator.createDefault.mockResolvedValue({ id: 'polygon1', type: ElementType.POLYGON })

    mockEllipseCreator.create.mockResolvedValue({ id: 'ellipse1', type: ElementType.ELLIPSE })
    mockEllipseCreator.createDefault.mockResolvedValue({ id: 'ellipse1', type: ElementType.ELLIPSE })

    mockArcPathFactory.mockResolvedValue({ id: 'arc1', type: 'arc' })
    mockBezierPathFactory.mockResolvedValue({ id: 'bezier1', type: 'bezier' })
    mockQuadraticPathFactory.mockResolvedValue({ id: 'quadratic1', type: 'quadratic' })

    // Create factory with mocked creators
    factory = new ElementFactory();

    // Register mock creators
    (factory as any).creators = {
      [ElementType.RECTANGLE]: mockRectangleCreator,
      [ElementType.CIRCLE]: mockCircleCreator,
      [ElementType.LINE]: mockLineCreator,
      [ElementType.POLYGON]: mockPolygonCreator,
      [ElementType.ELLIPSE]: mockEllipseCreator,
    };

    // Register mock path factories
    (factory as any).pathFactories = {
      arc: mockArcPathFactory,
      bezier: mockBezierPathFactory,
      quadratic: mockQuadraticPathFactory,
    }
  })

  describe('error Handling', () => {
    it('should throw error when creating shape with invalid type', async () => {
      // Act & Assert
      await expect(factory.createShape('invalid' as ElementType, {})).rejects.toThrow(CoreError)
      await expect(factory.createShape('invalid' as ElementType, {})).rejects.toThrow('No creator registered for shape type')
    })

    it('should throw error when creating default shape with invalid type', async () => {
      // Act & Assert
      await expect(factory.createDefaultShape('invalid' as ElementType, 'id1', new Point(0, 0))).rejects.toThrow(CoreError)
      await expect(factory.createDefaultShape('invalid' as ElementType, 'id1', new Point(0, 0))).rejects.toThrow('No creator registered for shape type')
    })

    it('should throw error when creating path with invalid type', async () => {
      // Act & Assert
      await expect(factory.createPath('invalid', {})).rejects.toThrow(CoreError)
      await expect(factory.createPath('invalid', {})).rejects.toThrow('No path factory registered for path type')
    })

    it('should throw error when creating shape with null or undefined type', async () => {
      // Act & Assert
      await expect(factory.createShape(null as any, {})).rejects.toThrow(CoreError)
      await expect(factory.createShape(undefined as any, {})).rejects.toThrow(CoreError)
    })

    it('should throw error when creating default shape with null or undefined type', async () => {
      // Act & Assert
      await expect(factory.createDefaultShape(null as any, 'id1', new Point(0, 0))).rejects.toThrow(CoreError)
      await expect(factory.createDefaultShape(undefined as any, 'id1', new Point(0, 0))).rejects.toThrow(CoreError)
    })

    it('should throw error when creating path with null or undefined type', async () => {
      // Act & Assert
      await expect(factory.createPath(null as any, {})).rejects.toThrow(CoreError)
      await expect(factory.createPath(undefined as any, {})).rejects.toThrow(CoreError)
    })
  })

  describe('iD Generation', () => {
    it('should generate ID for rectangle if not provided', async () => {
      // Arrange
      const params = { width: 100, height: 50 }

      // Act
      await factory.createRectangle(params)

      // Assert
      expect(mockRectangleCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: expect.stringMatching(/^rectangle-[a-z0-9]+$/),
        }),
      )
    })

    it('should generate ID for circle if not provided', async () => {
      // Arrange
      const params = { radius: 50 }

      // Act
      await factory.createCircle(params)

      // Assert
      expect(mockCircleCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: expect.stringMatching(/^circle-[a-z0-9]+$/),
        }),
      )
    })

    it('should generate ID for line if not provided', async () => {
      // Arrange
      const params = { start: new Point(0, 0), end: new Point(100, 100) }

      // Act
      await factory.createLine(params)

      // Assert
      expect(mockLineCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: expect.stringMatching(/^line-[a-z0-9]+$/),
        }),
      )
    })

    it('should generate ID for polygon if not provided', async () => {
      // Arrange
      const params = { points: [new Point(0, 0), new Point(100, 0), new Point(50, 100)] }

      // Act
      await factory.createCustomPolygon(params)

      // Assert
      expect(mockPolygonCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: expect.stringMatching(/^polygon-[a-z0-9]+$/),
        }),
      )
    })

    it('should generate ID for ellipse if not provided', async () => {
      // Arrange
      const params = { radiusX: 100, radiusY: 50 }

      // Act
      await factory.createEllipse(params)

      // Assert
      expect(mockEllipseCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: expect.stringMatching(/^ellipse-[a-z0-9]+$/),
        }),
      )
    })

    it('should generate ID for path if not provided', async () => {
      // Arrange
      const params = { center: new Point(100, 100), radius: 50, startAngle: 0, endAngle: 180 }

      // Act
      await factory.createPath('arc', params)

      // Assert
      expect(mockArcPathFactory).toHaveBeenCalledWith(
        expect.objectContaining({
          id: expect.stringMatching(/^arc-[a-z0-9]+$/),
        }),
      )
    })
  })

  describe('parameter Handling', () => {
    it('should use provided ID for rectangle', async () => {
      // Arrange
      const params = { id: 'custom-rect', width: 100, height: 50 }

      // Act
      await factory.createRectangle(params)

      // Assert
      expect(mockRectangleCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'custom-rect',
        }),
      )
    })

    it('should use provided position for circle', async () => {
      // Arrange
      const position = new Point(200, 200)
      const params = { radius: 50, position }

      // Act
      await factory.createCircle(params)

      // Assert
      expect(mockCircleCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          position,
        }),
      )
    })

    it('should use provided style properties for line', async () => {
      // Arrange
      const params = {
        start: new Point(0, 0),
        end: new Point(100, 100),
        stroke: '#ff0000',
        strokeWidth: 2,
        strokeDasharray: '5,5',
      }

      // Act
      await factory.createLine(params)

      // Assert
      expect(mockLineCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          stroke: '#ff0000',
          strokeWidth: 2,
          strokeDasharray: '5,5',
        }),
      )
    })

    it('should handle metadata for polygon', async () => {
      // Arrange
      const metadata = { createdBy: 'user1', tags: ['tag1', 'tag2'] }
      const params = {
        points: [new Point(0, 0), new Point(100, 0), new Point(50, 100)],
        metadata,
      }

      // Act
      await factory.createCustomPolygon(params)

      // Assert
      expect(mockPolygonCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata,
        }),
      )
    })

    it('should handle additional properties for ellipse', async () => {
      // Arrange
      const params = {
        radiusX: 100,
        radiusY: 50,
        rotation: 45,
        fill: '#00ff00',
        opacity: 0.5,
      }

      // Act
      await factory.createEllipse(params)

      // Assert
      expect(mockEllipseCreator.create).toHaveBeenCalledWith(
        expect.objectContaining({
          rotation: 45,
          fill: '#00ff00',
          opacity: 0.5,
        }),
      )
    })
  })

  describe('path Creation', () => {
    it('should create an arc path', async () => {
      // Arrange
      const params = {
        center: new Point(100, 100),
        radius: 50,
        startAngle: 0,
        endAngle: 180,
      }

      // Act
      const result = await factory.createPath('arc', params)

      // Assert
      expect(mockArcPathFactory).toHaveBeenCalledWith(expect.objectContaining(params))
      expect(result).toEqual({ id: 'arc1', type: 'arc' })
    })

    it('should create a bezier path', async () => {
      // Arrange
      const params = {
        start: new Point(0, 0),
        end: new Point(100, 100),
        control1: new Point(50, 0),
        control2: new Point(50, 100),
      }

      // Act
      const result = await factory.createPath('bezier', params)

      // Assert
      expect(mockBezierPathFactory).toHaveBeenCalledWith(expect.objectContaining(params))
      expect(result).toEqual({ id: 'bezier1', type: 'bezier' })
    })

    it('should create a quadratic path', async () => {
      // Arrange
      const params = {
        start: new Point(0, 0),
        end: new Point(100, 100),
        control: new Point(50, 0),
      }

      // Act
      const result = await factory.createPath('quadratic', params)

      // Assert
      expect(mockQuadraticPathFactory).toHaveBeenCalledWith(expect.objectContaining(params))
      expect(result).toEqual({ id: 'quadratic1', type: 'quadratic' })
    })
  })
})
