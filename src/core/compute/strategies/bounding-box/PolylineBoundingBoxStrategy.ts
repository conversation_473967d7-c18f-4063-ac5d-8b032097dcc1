/**
 * Bounding Box Calculation Strategy for Polyline Elements
 *
 * @remarks
 * This strategy implements the {@link BoundingBoxCalculatorStrategy} for calculating
 * the axis-aligned bounding box of Polyline elements ({@link CoreElementType.POLYLINE}).
 *
 * The calculation involves:
 * 1. Retrieving the `points` (vertices) of the polyline from the element's properties.
 * 2. Adjusting these points by the polyline element's `position` (inherited from
 *    {@link ShapeElement}) to get absolute coordinates, as polyline points are often relative.
 * 3. Using the static method `BoundingBoxClass.fromPointsArray` to compute the
 *    bounding box from these absolute points.
 *
 * If the polyline has no valid points, a zero-dimension bounding box at the element's
 * nominal position is returned.
 * The strategy validates that the element is of type `POLYLINE`.
 *
 * @module core/compute/strategies/bounding-box/PolylineBoundingBoxStrategy
 * @see {@link Path.Polyline} for the polyline element type definition.
 * @see {@link BoundingBoxInterface} for the bounding box structure.
 * @see {@link BoundingBoxClass} for the utility class used.
 */
import type { BoundingBoxCalculatorStrategy } from '../../../../types/core/compute'
import type { BoundingBox as BoundingBoxInterface } from '../../../../types/core/element/geometry/bounding-box'
import type { PointData as IPoint } from '../../../../types/core/element/geometry/point' // Use PointData, alias as IPoint
import type {
  Element,
  Path,
  ShapeElement,
} from '../../../../types/core/elementDefinitions'
import { BoundingBoxClass } from '../../../../lib/utils/geometry/BoundingBoxClass'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  // Element, // ShapeElement is used
  ElementType as CoreElementType,
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

export class PolylineBoundingBoxStrategy implements BoundingBoxCalculatorStrategy {
  /**
   * Calculates the axis-aligned bounding box of a Polyline element.
   *
   * @param element - The polyline element, expected to be of type {@link ShapeElement}
   *                  and specifically {@link CoreElementType.POLYLINE}, conforming to {@link Path.Polyline}.
   * @returns A {@link BoundingBoxInterface} object representing the bounding box.
   *          Returns a 0x0 bounding box at the element's position if no valid points are found.
   * @throws {@link CoreError} if the provided element is not of type `POLYLINE`.
   */
  public calculateBoundingBox(element: Element): BoundingBoxInterface {
    if (element.type !== CoreElementType.POLYLINE) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `Expected POLYLINE, got ${element.type} for ID: ${element.id}`,
        undefined,
        { component: 'PolylineBoundingBoxStrategy', operation: 'calculateBoundingBox', target: element.id },
      )
    }

    const polylineElement = element as unknown as Path.Polyline // Added unknown for type safety
    const shapeElement = element as ShapeElement
    const relativePoints = polylineElement.properties?.points as IPoint[] | undefined // Points are in properties

    const elPosX = shapeElement.position?.x ?? 0
    const elPosY = shapeElement.position?.y ?? 0
    // const elPosZ = element.position?.z; // Not typically used for 2D BBox of polyline points

    if (!relativePoints || !Array.isArray(relativePoints) || relativePoints.length === 0) {
      // For an empty polyline, return a 0x0 BBox at its nominal position
      return new BoundingBoxClass(elPosX, elPosY, 0, 0, `bbox-empty-${element.id}`)
    }

    const absolutePoints: IPoint[] = []
    for (const relPoint of relativePoints) {
      if (relPoint != null && typeof relPoint.x === 'number' && typeof relPoint.y === 'number' && Number.isFinite(relPoint.x) && Number.isFinite(relPoint.y)) {
        absolutePoints.push({
          x: relPoint.x + elPosX,
          y: relPoint.y + elPosY,
          z: relPoint.z, // Preserve Z from relative points if present
        })
      }
      else {
        // Optionally throw an error for invalid points, or log and skip
        console.warn(`[PolylineBoundingBoxStrategy] Warning: Skipping invalid vertex data in polyline ${element.id}:`, relPoint)
      }
    }

    if (absolutePoints.length === 0) {
      // All points were invalid, return a 0x0 BBox at its nominal position
      return new BoundingBoxClass(elPosX, elPosY, 0, 0, `bbox-empty-invalid-${element.id}`)
    }

    // Delegate to BoundingBoxClass static method
    return BoundingBoxClass.fromPointsArray(absolutePoints)
  }

  /**
   * Returns the element type that this strategy is responsible for.
   * @returns The element type {@link CoreElementType.POLYLINE}
   */
  public getElementType(): CoreElementType {
    return CoreElementType.POLYLINE
  }
}
