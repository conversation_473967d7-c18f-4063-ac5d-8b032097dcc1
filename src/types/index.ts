/**
 * Central Type Definitions Entry Point
 *
 * @remarks
 * This file serves as the main "barrel" or entry point for all type definitions
 * within the RenoPilot.JS.Shapes2 project. It re-exports types from various
 * sub-modules, providing a single, convenient import path for other parts of the application.
 *
 * The type system is broadly organized into the following domains, each typically
 * residing in its own subdirectory:
 * - **Core Types**: Fundamental definitions for geometric primitives, elements, shapes,
 *   canvas interactions, validation, and computation (see `./core/`).
 * - **Service Types**: Interfaces and data structures for application services like
 *   history management, data export, logging, event bus, etc. (see `./services/`).
 * - **Hook Types**: Definitions related to React hooks used for application state
 *   and logic (see `./hooks/`).
 * - **Constants**: Application-wide constant values and enumerations (see `./constants.ts`).
 * - **Configuration**: Types related to application configuration (imported from `../config`).
 *
 * By importing from `@/types`, other modules can access all necessary type definitions
 * without needing to be aware of the internal structure of the `src/types` directory.
 *
 * @module types/index
 */
export * from './canvas.types' // Placed at the top of exports

/**
 * Constants Exports
 *
 * Application-wide constant values for default styles, settings, and identifiers.
 */
// Export constants
export * from './constants'

/**
 * Core Canvas Types
 *
 * Core canvas types explicitly exported to avoid ambiguity.
 * These define the drawing surface and interaction layers.
 */
// Explicitly export from ./core (e.g., canvas types) instead of wildcard if specific control is needed
export type { CanvasLayer } from './core' // Example: Core canvas types

/**
 * Compute Types Exports
 *
 * Types related to computational operations on elements.
 * These support area calculation, distance measurement, and other geometric operations.
 */
// Export compute types from new location
export * from './core/compute'

/**
 * Core Element Types
 *
 * Base element interfaces and metadata definitions.
 * These define the foundation for all elements in the system.
 */
// Export core element types (geometric primitives, elements, shapes)
export * from './core/element'
// export * from './canvas.types' // Removed from here
export * from './core/validator/error-codes'
/**
 * Validator Types Exports
 *
 * Types related to element and data validation.
 * These ensure that elements meet required specifications and constraints.
 */
// Export validator types (interfaces, error codes)
export * from './core/validator/validator'

// export * from './core/validator/validator-interface' // This line might be the one it wants to be after canvas.types, or it's part of the group

/**
 * Hook Types Exports
 *
 * Types for React hooks used throughout the application.
 * These define the interfaces for the custom hooks that encapsulate reusable logic.
 */
// Export hook types
// TODO: Create hooks types when needed
// export * from './hooks'
/**
 * Service Types Exports
 *
 * Types for application services such as history, export, logging, etc.
 * These types define the interfaces and data structures for various services
 * that provide application-wide functionality (e.g., undo/redo, data export).
 *
 * `ExportOptions` is re-exported from `./services` which, in turn, sources it
 * from `../export/ExportTemplateTypes.ts`.
 */
// Export service types (error service, events, history, logging, shapes service, etc.)
export * from './services'

export type { ExportOptions } from './services' // Restore explicit re-export to resolve potential ambiguity

/**
 * Template Types Exports
 *
 * Types for template system and template selection.
 * These define the structure for template metadata and configuration.
 */
export * from './template'

/**
 * Tutorial Types Exports
 *
 * Types for the driver.js tutorial system.
 * These define the structure for tutorial steps and configuration.
 */
export * from './tutorial'

// export * from './storeTypes' // TODO: Consider splitting this if it grows too large - Temporarily commented out
// export * from './uiTypes' // Temporarily commented out
