import { describe, expect, it } from 'vitest'
import * as ShapeTypesModule from '@/types/services/features/shapeTypes'
import * as ShapesModule from '@/types/services/shapes'

describe('shape Types Feature Module', () => {
  it('should be defined as a module', () => {
    // Verify the module exists
    expect(ShapeTypesModule).toBeDefined()
  })

  it('should re-export types from shapes module', () => {
    // Check that the module has the same exports as the shapes module
    const shapeTypesExports = Object.keys(ShapeTypesModule)
    const shapesExports = Object.keys(ShapesModule)

    // Verify that all exports from shapes module are re-exported
    for (const exportName of shapesExports) {
      expect(shapeTypesExports).toContain(exportName)
    }
  })
})
