import type Point from '@/types/core/element/geometry/point'
import type { EventBus } from '@/types/services/events'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import {
  publishSelectionChanged,
  publishShapeCreateRequest,
  publishShapeDeleteRequest,
  publishShapeDeselected,
  publishShapeDuplicationRequest,
  publishShapeEditRequest,
  publishShapeSelectRequest,
} from '@/services/core/event-bus/helpers/publishers/shapePublishers'
import { ElementType } from '@/types/core/elementDefinitions'
import { AppEventType } from '@/types/services/events'
import { SelectionMode } from '@/types/services/shapes'

// Mock EventBus
const mockEventBus = {
  publish: vi.fn(),
  subscribe: vi.fn(),
  unsubscribe: vi.fn(),
  clear: vi.fn(),
} as unknown as EventBus

describe('shape Publishers', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('publishShapeCreateRequest', () => {
    it('should publish shape create request with correct event structure', () => {
      const position: Point = { x: 100, y: 200, z: 0 }
      const properties = { width: 150, height: 100, fill: '#ff0000' }

      publishShapeCreateRequest(mockEventBus, ElementType.RECTANGLE, position, properties)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeCreateRequest,
        payload: {
          ElementType: ElementType.RECTANGLE.toString(),
          position: { x: 100, y: 200 },
          properties,
        },
      })
    })

    it('should handle different element types', () => {
      const position: Point = { x: 50, y: 75, z: 0 }
      const properties = { radius: 25 }

      publishShapeCreateRequest(mockEventBus, ElementType.CIRCLE, position, properties)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeCreateRequest,
        payload: {
          ElementType: ElementType.CIRCLE.toString(),
          position: { x: 50, y: 75 },
          properties,
        },
      })
    })

    it('should handle empty properties', () => {
      const position: Point = { x: 0, y: 0, z: 0 }
      const properties = {}

      publishShapeCreateRequest(mockEventBus, ElementType.POLYGON, position, properties)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeCreateRequest,
        payload: {
          ElementType: ElementType.POLYGON.toString(),
          position: { x: 0, y: 0 },
          properties: {},
        },
      })
    })

    it('should handle complex properties', () => {
      const position: Point = { x: 300, y: 400, z: 1 }
      const properties = {
        width: 200,
        height: 150,
        fill: '#00ff00',
        stroke: '#000000',
        strokeWidth: 2,
        opacity: 0.8,
        rotation: 45,
        metadata: {
          layer: 'foreground',
          category: 'shapes',
        },
      }

      publishShapeCreateRequest(mockEventBus, ElementType.RECTANGLE, position, properties)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeCreateRequest,
        payload: {
          ElementType: ElementType.RECTANGLE.toString(),
          position: { x: 300, y: 400 },
          properties,
        },
      })
    })
  })

  describe('publishShapeEditRequest', () => {
    it('should publish shape edit request with property changes', () => {
      const shapeId = 'shape-123'
      const changes = { width: 250, height: 200, fill: '#0000ff' }

      publishShapeEditRequest(mockEventBus, shapeId, changes)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeEditRequest,
        timestamp: expect.any(Number),
        payload: {
          shapeId,
          properties: {
            width: 250,
            height: 200,
            fill: '#0000ff',
          },
        },
      })
    })

    it('should handle position changes', () => {
      const shapeId = 'shape-456'
      const position: Point = { x: 150, y: 250, z: 1 }
      const changes = { position }

      publishShapeEditRequest(mockEventBus, shapeId, changes)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeEditRequest,
        timestamp: expect.any(Number),
        payload: {
          shapeId,
          properties: {
            x: 150,
            y: 250,
            z: 1,
          },
        },
      })
    })

    it('should handle position without z coordinate', () => {
      const shapeId = 'shape-789'
      const position: Point = { x: 100, y: 200 }
      const changes = { position }

      publishShapeEditRequest(mockEventBus, shapeId, changes)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeEditRequest,
        timestamp: expect.any(Number),
        payload: {
          shapeId,
          properties: {
            x: 100,
            y: 200,
          },
        },
      })
    })

    it('should handle mixed property and position changes', () => {
      const shapeId = 'shape-mixed'
      const position: Point = { x: 75, y: 125, z: 2 }
      const changes = {
        position,
        width: 300,
        fill: '#ff00ff',
        opacity: 0.5,
      }

      publishShapeEditRequest(mockEventBus, shapeId, changes)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeEditRequest,
        timestamp: expect.any(Number),
        payload: {
          shapeId,
          properties: {
            x: 75,
            y: 125,
            z: 2,
            width: 300,
            fill: '#ff00ff',
            opacity: 0.5,
          },
        },
      })
    })

    it('should handle empty changes', () => {
      const shapeId = 'shape-empty'
      const changes = {}

      publishShapeEditRequest(mockEventBus, shapeId, changes)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeEditRequest,
        timestamp: expect.any(Number),
        payload: {
          shapeId,
          properties: {},
        },
      })
    })
  })

  describe('publishShapeDeleteRequest', () => {
    it('should publish delete request for single shape', () => {
      const shapeIds = ['shape-1']

      publishShapeDeleteRequest(mockEventBus, shapeIds)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeDeleteRequest,
        payload: {
          shapeId: shapeIds,
        },
      })
    })

    it('should publish delete request for multiple shapes', () => {
      const shapeIds = ['shape-1', 'shape-2', 'shape-3']

      publishShapeDeleteRequest(mockEventBus, shapeIds)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeDeleteRequest,
        payload: {
          shapeId: shapeIds,
        },
      })
    })

    it('should handle empty shape IDs array', () => {
      const shapeIds: string[] = []

      publishShapeDeleteRequest(mockEventBus, shapeIds)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeDeleteRequest,
        payload: {
          shapeId: [],
        },
      })
    })
  })

  describe('publishShapeDuplicationRequest', () => {
    it('should publish duplication request with correct structure', () => {
      const originalId = 'original-shape-123'
      const position: Point = { x: 200, y: 300, z: 0 }

      publishShapeDuplicationRequest(mockEventBus, originalId, position)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeDuplicateRequest,
        payload: {
          originalId,
          position,
        },
      })
    })

    it('should handle different positions', () => {
      const originalId = 'shape-to-duplicate'
      const position: Point = { x: -50, y: -100, z: 5 }

      publishShapeDuplicationRequest(mockEventBus, originalId, position)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeDuplicateRequest,
        payload: {
          originalId,
          position,
        },
      })
    })
  })

  describe('publishSelectionChanged', () => {
    it('should publish selection changed with selected IDs', () => {
      const selectedShapeIds = ['shape-1', 'shape-2']

      publishSelectionChanged(mockEventBus, selectedShapeIds)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SelectionChanged,
        payload: {
          selectedIds: selectedShapeIds,
        },
      })
    })

    it('should handle empty selection', () => {
      const selectedShapeIds: string[] = []

      publishSelectionChanged(mockEventBus, selectedShapeIds)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SelectionChanged,
        payload: {
          selectedIds: [],
        },
      })
    })

    it('should handle single selection', () => {
      const selectedShapeIds = ['single-shape']

      publishSelectionChanged(mockEventBus, selectedShapeIds)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SelectionChanged,
        payload: {
          selectedIds: selectedShapeIds,
        },
      })
    })
  })

  describe('publishShapeSelectRequest', () => {
    it('should publish select request with default replace mode', () => {
      const shapeIds = ['shape-1', 'shape-2']

      publishShapeSelectRequest(mockEventBus, shapeIds)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeSelectRequest,
        payload: {
          shapeIds,
          selectionMode: SelectionMode.Replace,
        },
      })
    })

    it('should publish select request with add mode', () => {
      const shapeIds = ['shape-3']

      publishShapeSelectRequest(mockEventBus, shapeIds, SelectionMode.Add)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeSelectRequest,
        payload: {
          shapeIds,
          selectionMode: SelectionMode.Add,
        },
      })
    })

    it('should publish select request with remove mode', () => {
      const shapeIds = ['shape-4']

      publishShapeSelectRequest(mockEventBus, shapeIds, SelectionMode.Remove)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeSelectRequest,
        payload: {
          shapeIds,
          selectionMode: SelectionMode.Remove,
        },
      })
    })

    it('should publish select request with clear mode', () => {
      const shapeIds: string[] = []

      publishShapeSelectRequest(mockEventBus, shapeIds, SelectionMode.Clear)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.ShapeSelectRequest,
        payload: {
          shapeIds,
          selectionMode: SelectionMode.Clear,
        },
      })
    })
  })

  describe('publishShapeDeselected', () => {
    it('should publish selection changed with empty array', () => {
      publishShapeDeselected(mockEventBus)

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.SelectionChanged,
        payload: {
          selectedIds: [],
        },
      })
    })
  })

  describe('error handling', () => {
    it('should handle eventBus publish errors gracefully', () => {
      mockEventBus.publish = vi.fn().mockImplementation(() => {
        throw new Error('EventBus error')
      })

      expect(() => {
        publishShapeCreateRequest(mockEventBus, ElementType.RECTANGLE, { x: 0, y: 0 }, {})
      }).toThrow('EventBus error')
    })

    it('should handle null/undefined parameters', () => {
      expect(() => {
        publishShapeCreateRequest(null as any, ElementType.RECTANGLE, { x: 0, y: 0 }, {})
      }).toThrow()

      expect(() => {
        publishShapeEditRequest(mockEventBus, null as any, {})
      }).not.toThrow()

      expect(() => {
        publishShapeDeleteRequest(mockEventBus, null as any)
      }).not.toThrow()
    })
  })
})
