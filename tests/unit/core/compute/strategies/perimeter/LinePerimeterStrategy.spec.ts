import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { LinePerimeterStrategy } from '@/core/compute/strategies/perimeter/LinePerimeterStrategy'
import type { PointData } from '@/types/core/element/geometry/point'
import { Line } from '@/types/core/element/path/linePathTypes'
import { ElementType } from '@/types/core/elementDefinitions'

// Mock Line class
class MockLine {
  id = 'mock-line'
  type = ElementType.LINE
  start = { x: 0, y: 0 }
  end = { x: 0, y: 0 }
  properties = { start: { x: 0, y: 0 }, end: { x: 0, y: 0 } }

  constructor(startX: number, startY: number, endX: number, endY: number) {
    this.start.x = startX
    this.start.y = startY
    this.end.x = endX
    this.end.y = endY
    this.properties.start = { x: startX, y: startY }
    this.properties.end = { x: endX, y: endY }
  }

  getSubType() {
    return 'line'
  }
}

// Mock non-line element
class MockRectangle {
  id = 'mock-rectangle'
  type = 'RECTANGLE'

  getSubType() {
    return 'rectangle'
  }
}

describe('linePerimeterStrategy', () => {
  let strategy: LinePerimeterStrategy
  let consoleSpy: any

  beforeEach(() => {
    strategy = new LinePerimeterStrategy()
    consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    consoleSpy.mockRestore()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('LINE')
  })

  it('should calculate perimeter (length) of a horizontal line correctly', () => {
    const line = new MockLine(0, 0, 10, 0)
    const perimeter = strategy.calculatePerimeter(line as any)
    expect(perimeter).toBe(10)
  })

  it('should calculate perimeter (length) of a vertical line correctly', () => {
    const line = new MockLine(0, 0, 0, 10)
    const perimeter = strategy.calculatePerimeter(line as any)
    expect(perimeter).toBe(10)
  })

  it('should calculate perimeter (length) of a diagonal line correctly', () => {
    const line = new MockLine(0, 0, 3, 4)
    const perimeter = strategy.calculatePerimeter(line as any)
    expect(perimeter).toBe(5) // 3-4-5 triangle
  })

  it('should throw error for a line with invalid points', () => {
    const line = new MockLine(0, 0, 0, 0)
    line.start = null as any
    line.properties.start = null as any

    expect(() => strategy.calculatePerimeter(line as any)).toThrow('missing valid start or end points')
  })

  it('should throw error if element is not a line', () => {
    const rectangle = new MockRectangle()
    expect(() => strategy.calculatePerimeter(rectangle as any)).toThrow('Expected element type LINE')
  })

  it('should handle real Line instance', () => {
    // Create a mock that looks like a real Line instance
    const realLine = {
      id: 'real-line-id',
      type: ElementType.LINE,
      constructor: { name: 'Line' },
      start: { x: 0, y: 0 } as PointData,
      end: { x: 3, y: 4 } as PointData,
      properties: {
        start: { x: 0, y: 0 } as PointData,
        end: { x: 3, y: 4 } as PointData,
      },
      getSubType: () => 'line',
    }

    // Make instanceof check pass
    if (Line?.prototype) {
      Object.setPrototypeOf(realLine, Line.prototype)
    }

    const perimeter = strategy.calculatePerimeter(realLine as any)
    expect(perimeter).toBe(5) // 3-4-5 triangle
  })

  it('should handle invalid start point in real Line instance', () => {
    // Create a mock that looks like a real Line instance with invalid start point
    const realLine = {
      id: 'real-line-id',
      type: ElementType.LINE,
      constructor: { name: 'Line' },
      start: null,
      end: { x: 3, y: 4 } as PointData,
      properties: {
        start: null,
        end: { x: 3, y: 4 } as PointData,
      },
      getSubType: () => 'line',
    }

    // Make instanceof check pass
    if (Line?.prototype) {
      Object.setPrototypeOf(realLine, Line.prototype)
    }

    // Should throw an error for invalid points
    expect(() => strategy.calculatePerimeter(realLine as any)).toThrow('missing valid start or end points')
  })

  it('should handle invalid end point in real Line instance', () => {
    // Create a mock that looks like a real Line instance with invalid end point
    const realLine = {
      id: 'real-line-id',
      type: ElementType.LINE,
      constructor: { name: 'Line' },
      start: { x: 0, y: 0 } as PointData,
      end: null,
      properties: {
        start: { x: 0, y: 0 } as PointData,
        end: null,
      },
      getSubType: () => 'line',
    }

    // Make instanceof check pass
    if (Line?.prototype) {
      Object.setPrototypeOf(realLine, Line.prototype)
    }

    // Should throw an error for invalid points
    expect(() => strategy.calculatePerimeter(realLine as any)).toThrow('missing valid start or end points')
  })

  it('should handle error when accessing properties on Line', () => {
    // Create a mock that looks like a real Line instance but throws when start is accessed
    const realLine = {
      id: 'real-line-id',
      type: ElementType.LINE,
      constructor: { name: 'Line' },
      get start() { throw new Error('Test error') },
      end: { x: 3, y: 4 } as PointData,
      properties: {
        get start() { throw new Error('Test error') },
        end: { x: 3, y: 4 } as PointData,
      },
      getSubType: () => 'line',
    }

    // Make instanceof check pass
    if (Line?.prototype) {
      Object.setPrototypeOf(realLine, Line.prototype)
    }

    // Wrap in try-catch to handle the error
    try {
      const perimeter = strategy.calculatePerimeter(realLine as any)
      // The perimeter could be NaN or 0 depending on implementation
      expect(perimeter !== perimeter || perimeter === 0).toBe(true)
      expect(consoleSpy).toHaveBeenCalled()
    }
    catch (error) {
      // If it throws, that's also acceptable
      expect(error).toBeDefined()
    }
  })
})
