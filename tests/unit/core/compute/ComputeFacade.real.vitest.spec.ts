import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import type { Mocked } from 'vitest'
import { ComputeFacade } from '@/core/compute/ComputeFacade'
import type { StrategyRegistry } from '@/core/compute/StrategyRegistry'
import type { ShapeRepository } from '@/core/state/ShapeRepository'
import type { ElementFactory } from '@/core/factory/ElementFactory'
import type { Element, ShapeElement } from '@/types/core/elementDefinitions'
import { ElementType } from '@/types/core/elementDefinitions'
import { MajorCategory } from '@/types/core/majorMinorTypes'
import type {
  AreaCalculatorStrategy,
  PerimeterCalculatorStrategy,
  BoundingBox,
  ComputeResult,
} from '@/types/core/compute'

// Mock dependencies
vi.mock('@/core/compute/StrategyRegistry')
vi.mock('@/core/state/ShapeRepository')
vi.mock('@/core/factory/ElementFactory')

describe('ComputeFacade (Real Implementation)', () => {
  let computeFacade: ComputeFacade
  let mockStrategyRegistry: Mocked<StrategyRegistry>
  let mockShapeRepository: Mocked<ShapeRepository>
  let mockElementFactory: Mocked<ElementFactory>
  let testElement: Element
  let testShapeElement: ShapeElement

  beforeEach(() => {
    // Mock StrategyRegistry
    mockStrategyRegistry = {
      getAreaStrategy: vi.fn(),
      getPerimeterStrategy: vi.fn(),
      getBoundingBoxStrategy: vi.fn(),
      getDistanceStrategy: vi.fn(),
      getCostStrategy: vi.fn(),
      getMaterialStrategy: vi.fn(),
      getSpacePlanningStrategy: vi.fn(),
      hasStrategy: vi.fn().mockReturnValue(true),
      registerAreaStrategy: vi.fn(),
      registerPerimeterStrategy: vi.fn(),
      registerBoundingBoxStrategy: vi.fn(),
      registerDistanceStrategy: vi.fn(),
      registerCostStrategy: vi.fn(),
      registerMaterialStrategy: vi.fn(),
      registerSpacePlanningStrategy: vi.fn(),
    } as unknown as Mocked<StrategyRegistry>

    // Mock ShapeRepository
    mockShapeRepository = {
      getById: vi.fn(),
      getAll: vi.fn().mockReturnValue([]),
      add: vi.fn(),
      update: vi.fn(),
      remove: vi.fn(),
      clear: vi.fn(),
      getSelectedIds: vi.fn().mockReturnValue([]),
      setSelectedIds: vi.fn(),
      addToSelection: vi.fn(),
      removeFromSelection: vi.fn(),
      clearSelection: vi.fn(),
      setShapesFromExternal: vi.fn(),
    } as unknown as Mocked<ShapeRepository>

    // Mock ElementFactory
    mockElementFactory = {
      createShape: vi.fn(),
      createElement: vi.fn(),
      createDefaultElement: vi.fn(),
      registerCreator: vi.fn(),
      normalizePositionInput: vi.fn(),
    } as unknown as Mocked<ElementFactory>

    // Test elements
    testElement = {
      id: 'test-element',
      type: ElementType.RECTANGLE,
      position: { x: 0, y: 0 },
      properties: { width: 100, height: 50 },
      majorCategory: MajorCategory.BASE,
      minorCategory: 'architecture',
    }

    testShapeElement = {
      ...testElement,
      intraLayerZIndex: 0,
    } as ShapeElement

    // Create ComputeFacade instance
    computeFacade = new ComputeFacade(
      mockStrategyRegistry,
      mockShapeRepository,
      mockElementFactory,
    )
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Constructor', () => {
    it('should create ComputeFacade instance', () => {
      expect(computeFacade).toBeDefined()
      expect(computeFacade).toBeInstanceOf(ComputeFacade)
    })

    it('should initialize with provided dependencies', () => {
      expect(computeFacade.strategyRegistry).toBe(mockStrategyRegistry)
      expect(computeFacade.shapeRepository).toBe(mockShapeRepository)
      expect(computeFacade.elementFactory).toBe(mockElementFactory)
    })
  })

  describe('Area Computation', () => {
    it('should compute area for valid shape', async () => {
      const mockAreaStrategy: AreaCalculatorStrategy = {
        calculateArea: vi.fn().mockReturnValue(5000),
        getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      }

      mockShapeRepository.getById.mockReturnValue(testShapeElement)
      mockStrategyRegistry.getAreaStrategy.mockReturnValue(mockAreaStrategy)

      const result = await computeFacade.computeArea('test-element')

      expect(result).toBe(5000)
      expect(mockShapeRepository.getById).toHaveBeenCalledWith('test-element')
      expect(mockStrategyRegistry.getAreaStrategy).toHaveBeenCalledWith(ElementType.RECTANGLE)
      expect(mockAreaStrategy.calculateArea).toHaveBeenCalledWith(testShapeElement)
    })

    it('should throw error for non-existent shape', async () => {
      mockShapeRepository.getById.mockReturnValue(undefined)

      await expect(computeFacade.computeArea('non-existent')).rejects.toThrow()
      expect(mockShapeRepository.getById).toHaveBeenCalledWith('non-existent')
    })

    it('should handle strategy errors', async () => {
      const mockAreaStrategy: AreaCalculatorStrategy = {
        calculateArea: vi.fn().mockImplementation(() => {
          throw new Error('Strategy error')
        }),
        getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      }

      mockShapeRepository.getById.mockReturnValue(testShapeElement)
      mockStrategyRegistry.getAreaStrategy.mockReturnValue(mockAreaStrategy)

      await expect(computeFacade.computeArea('test-element')).rejects.toThrow()
    })

    it('should handle missing strategy', async () => {
      mockShapeRepository.getById.mockReturnValue(testShapeElement)
      mockStrategyRegistry.getAreaStrategy.mockImplementation(() => {
        throw new Error('No strategy found')
      })

      await expect(computeFacade.computeArea('test-element')).rejects.toThrow()
    })
  })

  describe('Perimeter Computation', () => {
    it('should compute perimeter for valid shape', async () => {
      const mockPerimeterStrategy: PerimeterCalculatorStrategy = {
        calculatePerimeter: vi.fn().mockReturnValue(300),
        getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      }

      mockShapeRepository.getById.mockReturnValue(testShapeElement)
      mockStrategyRegistry.getPerimeterStrategy.mockReturnValue(mockPerimeterStrategy)

      const result = await computeFacade.computePerimeter('test-element')

      expect(result).toBe(300)
      expect(mockShapeRepository.getById).toHaveBeenCalledWith('test-element')
      expect(mockStrategyRegistry.getPerimeterStrategy).toHaveBeenCalledWith(ElementType.RECTANGLE)
      expect(mockPerimeterStrategy.calculatePerimeter).toHaveBeenCalledWith(testShapeElement)
    })

    it('should throw error for non-existent shape', async () => {
      mockShapeRepository.getById.mockReturnValue(undefined)

      await expect(computeFacade.computePerimeter('non-existent')).rejects.toThrow()
    })

    it('should handle strategy calculation errors', async () => {
      const mockPerimeterStrategy: PerimeterCalculatorStrategy = {
        calculatePerimeter: vi.fn().mockImplementation(() => {
          throw new Error('Calculation failed')
        }),
        getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      }

      mockShapeRepository.getById.mockReturnValue(testShapeElement)
      mockStrategyRegistry.getPerimeterStrategy.mockReturnValue(mockPerimeterStrategy)

      await expect(computeFacade.computePerimeter('test-element')).rejects.toThrow()
    })
  })

  describe('Bounding Box Computation', () => {
    it('should compute bounding box for valid shape', () => {
      const expectedBoundingBox: BoundingBox = {
        x: 0,
        y: 0,
        width: 100,
        height: 50,
      }

      const mockBoundingBoxStrategy = {
        calculateBoundingBox: vi.fn().mockReturnValue(expectedBoundingBox),
        getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      }

      mockShapeRepository.getById.mockReturnValue(testShapeElement)
      mockStrategyRegistry.getBoundingBoxStrategy.mockReturnValue(mockBoundingBoxStrategy)

      const result = computeFacade.computeBoundingBox('test-element')

      expect(result).toEqual(expectedBoundingBox)
      expect(mockShapeRepository.getById).toHaveBeenCalledWith('test-element')
      expect(mockStrategyRegistry.getBoundingBoxStrategy).toHaveBeenCalledWith(ElementType.RECTANGLE)
      expect(mockBoundingBoxStrategy.calculateBoundingBox).toHaveBeenCalledWith(testShapeElement)
    })

    it('should throw error for non-existent shape', () => {
      mockShapeRepository.getById.mockReturnValue(undefined)

      expect(() => computeFacade.computeBoundingBox('non-existent')).toThrow()
    })

    it('should handle strategy errors', () => {
      const mockBoundingBoxStrategy = {
        calculateBoundingBox: vi.fn().mockImplementation(() => {
          throw new Error('Bounding box calculation failed')
        }),
        getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      }

      mockShapeRepository.getById.mockReturnValue(testShapeElement)
      mockStrategyRegistry.getBoundingBoxStrategy.mockReturnValue(mockBoundingBoxStrategy)

      expect(() => computeFacade.computeBoundingBox('test-element')).toThrow()
    })
  })

  describe('Point Inside Detection', () => {
    it('should detect point inside shape', () => {
      const mockStrategy = {
        isPointInside: vi.fn().mockReturnValue(true),
        getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      }

      mockShapeRepository.getById.mockReturnValue(testShapeElement)
      mockStrategyRegistry.getBoundingBoxStrategy.mockReturnValue(mockStrategy)

      const result = computeFacade.isPointInside('test-element', { x: 50, y: 25 })

      expect(result).toBe(true)
      expect(mockStrategy.isPointInside).toHaveBeenCalledWith(testShapeElement, { x: 50, y: 25 })
    })

    it('should detect point outside shape', () => {
      const mockStrategy = {
        isPointInside: vi.fn().mockReturnValue(false),
        getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      }

      mockShapeRepository.getById.mockReturnValue(testShapeElement)
      mockStrategyRegistry.getBoundingBoxStrategy.mockReturnValue(mockStrategy)

      const result = computeFacade.isPointInside('test-element', { x: 150, y: 75 })

      expect(result).toBe(false)
      expect(mockStrategy.isPointInside).toHaveBeenCalledWith(testShapeElement, { x: 150, y: 75 })
    })

    it('should handle non-existent shape', () => {
      mockShapeRepository.getById.mockReturnValue(undefined)

      expect(() => computeFacade.isPointInside('non-existent', { x: 0, y: 0 })).toThrow()
    })
  })

  describe('Strategy Availability', () => {
    it('should check if strategy exists', () => {
      mockStrategyRegistry.hasStrategy.mockReturnValue(true)

      const result = computeFacade.hasStrategy(ElementType.RECTANGLE, 'area')

      expect(result).toBe(true)
      expect(mockStrategyRegistry.hasStrategy).toHaveBeenCalledWith('rectangle', 'area')
    })

    it('should return false for non-existent strategy', () => {
      mockStrategyRegistry.hasStrategy.mockReturnValue(false)

      const result = computeFacade.hasStrategy(ElementType.RECTANGLE, 'nonexistent')

      expect(result).toBe(false)
    })

    it('should handle invalid element type', () => {
      const result = computeFacade.hasStrategy('invalid' as any, 'area')

      expect(result).toBe(false)
    })

    it('should handle invalid operation', () => {
      const result = computeFacade.hasStrategy(ElementType.RECTANGLE, 'invalid')

      expect(result).toBe(false)
    })
  })

  describe('Generic Compute Method', () => {
    it('should compute area using generic method', async () => {
      const mockAreaStrategy: AreaCalculatorStrategy = {
        calculateArea: vi.fn().mockReturnValue(5000),
        getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      }

      mockShapeRepository.getById.mockReturnValue(testShapeElement)
      mockStrategyRegistry.getAreaStrategy.mockReturnValue(mockAreaStrategy)

      const result = await computeFacade.compute('area', ['test-element'])

      expect(result.success).toBe(true)
      expect(result.data).toBe(5000)
      expect(result.metadata?.operationName).toBe('area')
    })

    it('should compute perimeter using generic method', async () => {
      const mockPerimeterStrategy: PerimeterCalculatorStrategy = {
        calculatePerimeter: vi.fn().mockReturnValue(300),
        getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      }

      mockShapeRepository.getById.mockReturnValue(testShapeElement)
      mockStrategyRegistry.getPerimeterStrategy.mockReturnValue(mockPerimeterStrategy)

      const result = await computeFacade.compute('perimeter', ['test-element'])

      expect(result.success).toBe(true)
      expect(result.data).toBe(300)
    })

    it('should handle empty shape IDs', async () => {
      await expect(computeFacade.compute('area', [])).rejects.toThrow()
    })

    it('should handle null shape IDs', async () => {
      await expect(computeFacade.compute('area', null as any)).rejects.toThrow()
    })

    it('should handle invalid operation', async () => {
      await expect(computeFacade.compute('invalid', ['test-element'])).rejects.toThrow()
    })

    it('should include execution metadata', async () => {
      const mockAreaStrategy: AreaCalculatorStrategy = {
        calculateArea: vi.fn().mockReturnValue(5000),
        getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      }

      mockShapeRepository.getById.mockReturnValue(testShapeElement)
      mockStrategyRegistry.getAreaStrategy.mockReturnValue(mockAreaStrategy)

      const result = await computeFacade.compute('area', ['test-element'])

      expect(result.metadata).toBeDefined()
      expect(result.metadata?.operationName).toBe('area')
      expect(result.metadata?.executionTime).toBeGreaterThanOrEqual(0)
      expect(result.metadata?.timestamp).toBeDefined()
    })
  })

  describe('Error Handling', () => {
    it('should handle repository errors', async () => {
      mockShapeRepository.getById.mockImplementation(() => {
        throw new Error('Repository error')
      })

      await expect(computeFacade.computeArea('test-element')).rejects.toThrow()
    })

    it('should handle strategy registry errors', async () => {
      mockShapeRepository.getById.mockReturnValue(testShapeElement)
      mockStrategyRegistry.getAreaStrategy.mockImplementation(() => {
        throw new Error('Strategy registry error')
      })

      await expect(computeFacade.computeArea('test-element')).rejects.toThrow()
    })

    it('should handle element factory errors', async () => {
      mockElementFactory.createElement.mockImplementation(() => {
        throw new Error('Element factory error')
      })

      // This would be tested if the facade uses the factory for element creation
      expect(computeFacade).toBeDefined()
    })
  })

  describe('Performance', () => {
    it('should handle multiple computations efficiently', async () => {
      const mockAreaStrategy: AreaCalculatorStrategy = {
        calculateArea: vi.fn().mockReturnValue(5000),
        getElementType: vi.fn().mockReturnValue(ElementType.RECTANGLE),
      }

      mockShapeRepository.getById.mockReturnValue(testShapeElement)
      mockStrategyRegistry.getAreaStrategy.mockReturnValue(mockAreaStrategy)

      const startTime = Date.now()

      const promises = Array.from({ length: 100 }, () =>
        computeFacade.computeArea('test-element')
      )

      await Promise.all(promises)

      const endTime = Date.now()

      expect(endTime - startTime).toBeLessThan(1000) // Should complete quickly
      expect(mockAreaStrategy.calculateArea).toHaveBeenCalledTimes(100)
    })
  })
})
