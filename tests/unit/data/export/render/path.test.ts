import { describe, it, expect, vi, beforeEach } from 'vitest';
import * as pathModule from '@/data/export/render/path';

vi.mock('@/data/export/render/general', () => ({
  getShapeStyle: vi.fn(() => ({
    stroke: '#123',
    strokeWidth: 2,
    fill: '#abc'
  })),
  getTransform: vi.fn((rotation, pos) =>
    rotation ? ` transform="rotate(${rotation},${pos.x},${pos.y})"` : ''
  ),
}));

describe('render/path', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renderLine should render line with transform', () => {
    const line = {
      start: { x: 1, y: 2 },
      end: { x: 3, y: 4 },
      rotation: 45
    };
    const result = pathModule.renderLine(line as any);
    expect(result).toContain('<line');
    expect(result).toContain('x1="1"');
    expect(result).toContain('y1="2"');
    expect(result).toContain('x2="3"');
    expect(result).toContain('y2="4"');
    expect(result).toContain('stroke="#123"');
    expect(result).toContain('stroke-width="2"');
    expect(result).toContain('transform="rotate(45,1,2)"');
  });

  it('renderPolyline should render polyline with transform', () => {
    const polyline = {
      points: [{ x: 0, y: 0 }, { x: 10, y: 10 }],
      stroke: '#f00',
      strokeWidth: 1,
      rotation: 30
    };
    const result = pathModule.renderPolyline(polyline as any);
    expect(result).toContain('<polyline');
    expect(result).toContain('points="0,0 10,10"');
    expect(result).toContain('stroke="#123"');
    expect(result).toContain('stroke-width="2"');
    expect(result).toContain('transform="rotate(30,0,0)"');
  });

  it('renderArc should render arc path with transform', () => {
    const arc = {
      position: { x: 50, y: 50 },
      radius: 20,
      startAngle: 0,
      endAngle: 90,
      closed: false,
      rotation: 15
    };
    const result = pathModule.renderArc(arc as any);
    expect(result).toContain('<path');
    expect(result).toContain('stroke="#123"');
    expect(result).toContain('stroke-width="2"');
    expect(result).toContain('fill="none"');
    expect(result).toContain('transform="rotate(15,50,50)"');
  });

  it('renderArc should render closed arc with fill', () => {
    const arc = {
      position: { x: 0, y: 0 },
      radius: 10,
      startAngle: 0,
      endAngle: 180,
      closed: true,
      rotation: 0
    };
    const result = pathModule.renderArc(arc as any);
    expect(result).toContain('fill="#abc"');
    expect(result).not.toContain('transform="rotate('); // rotation为0
  });

  it('renderQuadratic should render quadratic bezier with transform', () => {
    const quadratic = {
      start: { x: 1, y: 2 },
      control: { x: 3, y: 4 },
      end: { x: 5, y: 6 },
      rotation: 60
    };
    const result = pathModule.renderQuadratic(quadratic as any);
    expect(result).toContain('<path');
    expect(result).toContain('Q 3 4 5 6');
    expect(result).toContain('stroke="#123"');
    expect(result).toContain('stroke-width="2"');
    expect(result).toContain('transform="rotate(60,1,2)"');
  });

  it('renderCubic should render cubic bezier with transform', () => {
    const cubic = {
      start: { x: 1, y: 2 },
      control1: { x: 3, y: 4 },
      control2: { x: 5, y: 6 },
      end: { x: 7, y: 8 },
      rotation: -45
    };
    const result = pathModule.renderCubic(cubic as any);
    expect(result).toContain('<path');
    expect(result).toContain('C 3 4 5 6 7 8');
    expect(result).toContain('stroke="#123"');
    expect(result).toContain('stroke-width="2"');
    expect(result).toContain('transform="rotate(-45,1,2)"');
  });
});