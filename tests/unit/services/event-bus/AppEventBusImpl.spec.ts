import type { EventSubscriptionOptions } from '@/types/services/events'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { AppEventBusImpl } from '@/services/event-bus/AppEventBus'
import { AppEventType } from '@/types/services/events/eventTypes'

describe('appEventBusImpl', () => {
  let eventBus: AppEventBusImpl

  beforeEach(() => {
    eventBus = new AppEventBusImpl()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('basic functionality', () => {
    it('should initialize with empty handlers', () => {
      expect(eventBus.getSubscriptions().size).toBe(0)
    })

    it('should allow subscribing to events', () => {
      const handler = vi.fn()
      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      expect(eventBus.getSubscriptions().size).toBe(1)
      expect(eventBus.getSubscriptions().has(AppEventType.SHAPE_CREATE_REQUEST)).toBe(true)
    })

    it('should call handlers when events are published', () => {
      const handler = vi.fn()
      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const event = { type: AppEventType.SHAPE_CREATE_REQUEST, payload: { ElementType: 'rectangle' } }
      eventBus.publish(event)

      expect(handler).toHaveBeenCalledWith(event)
    })

    it('should allow unsubscribing from events', () => {
      const handler = vi.fn()
      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      eventBus.unsubscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      expect(eventBus.getSubscriptions().size).toBe(0)
    })

    it('should provide an unsubscribe function from subscribe', () => {
      const handler = vi.fn()
      const unsubscribe = eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      unsubscribe()

      expect(eventBus.getSubscriptions().size).toBe(0)
    })

    it('should support on/off aliases', () => {
      const handler = vi.fn()
      eventBus.on(AppEventType.SHAPE_CREATE_REQUEST, handler)

      expect(eventBus.getSubscriptions().size).toBe(1)

      eventBus.off(AppEventType.SHAPE_CREATE_REQUEST, handler)

      expect(eventBus.getSubscriptions().size).toBe(0)
    })

    it('should support emit alias for publish', () => {
      const handler = vi.fn()
      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const event = { type: AppEventType.SHAPE_CREATE_REQUEST, payload: { ElementType: 'rectangle' } }
      eventBus.emit(event)

      expect(handler).toHaveBeenCalledWith(event)
    })
  })

  describe('advanced features', () => {
    it('should support once option', () => {
      const handler = vi.fn()
      eventBus.once(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const event = { type: AppEventType.SHAPE_CREATE_REQUEST, payload: { ElementType: 'rectangle' } }
      eventBus.publish(event)
      eventBus.publish(event)

      expect(handler).toHaveBeenCalledTimes(1)
    })

    it('should support filter option', () => {
      const handler = vi.fn()
      const options: EventSubscriptionOptions = {
        filter: event => event.payload.ElementType === 'rectangle',
      }

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler, options)

      const rectangleEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'rectangle' },
      }
      const circleEvent = {
        type: AppEventType.SHAPE_CREATE_REQUEST,
        payload: { ElementType: 'circle' },
      }

      eventBus.publish(rectangleEvent)
      eventBus.publish(circleEvent)

      expect(handler).toHaveBeenCalledTimes(1)
      expect(handler).toHaveBeenCalledWith(rectangleEvent)
    })

    it('should support context option', () => {
      const context = { value: 42 }
      const handler = vi.fn(function (this: typeof context, event) {
        expect(this.value).toBe(42)
      })

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler, { context })

      const event = { type: AppEventType.SHAPE_CREATE_REQUEST, payload: { ElementType: 'rectangle' } }
      eventBus.publish(event)

      expect(handler).toHaveBeenCalled()
    })

    it('should support debounce option', () => {
      const handler = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler, { debounce: 100 })

      const event = { type: AppEventType.SHAPE_CREATE_REQUEST, payload: { ElementType: 'rectangle' } }
      eventBus.publish(event)
      eventBus.publish(event)
      eventBus.publish(event)

      expect(handler).not.toHaveBeenCalled()

      vi.advanceTimersByTime(150)

      expect(handler).toHaveBeenCalledTimes(1)
    })

    it('should support throttle option', () => {
      const handler = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler, { throttle: 100 })

      const event = { type: AppEventType.SHAPE_CREATE_REQUEST, payload: { ElementType: 'rectangle' } }
      eventBus.publish(event) // Should be called
      eventBus.publish(event) // Should be throttled

      expect(handler).toHaveBeenCalledTimes(1)

      vi.advanceTimersByTime(150)

      eventBus.publish(event) // Should be called again after throttle time

      expect(handler).toHaveBeenCalledTimes(2)
    })
  })

  describe('error handling', () => {
    it('should catch errors in handlers', () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const handler = vi.fn().mockImplementation(() => {
        throw new Error('Test error')
      })

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const event = { type: AppEventType.SHAPE_CREATE_REQUEST, payload: { ElementType: 'rectangle' } }
      eventBus.publish(event)

      expect(handler).toHaveBeenCalled()
      expect(consoleErrorSpy).toHaveBeenCalled()

      consoleErrorSpy.mockRestore()
    })

    it('should continue calling other handlers after an error', () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const errorHandler = vi.fn().mockImplementation(() => {
        throw new Error('Test error')
      })
      const successHandler = vi.fn()

      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, errorHandler)
      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, successHandler)

      const event = { type: AppEventType.SHAPE_CREATE_REQUEST, payload: { ElementType: 'rectangle' } }
      eventBus.publish(event)

      expect(errorHandler).toHaveBeenCalled()
      expect(successHandler).toHaveBeenCalled()

      consoleErrorSpy.mockRestore()
    })
  })

  describe('configuration', () => {
    it('should allow configuring the event bus', () => {
      eventBus.configure({ enableLogging: true })

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

      const handler = vi.fn()
      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const event = { type: AppEventType.SHAPE_CREATE_REQUEST, payload: { ElementType: 'rectangle' } }
      eventBus.publish(event)

      expect(consoleSpy).toHaveBeenCalled()

      consoleSpy.mockRestore()
    })

    it('should reset to default configuration', () => {
      eventBus.configure({ enableLogging: true })
      eventBus.reset()

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

      const handler = vi.fn()
      eventBus.subscribe(AppEventType.SHAPE_CREATE_REQUEST, handler)

      const event = { type: AppEventType.SHAPE_CREATE_REQUEST, payload: { ElementType: 'rectangle' } }
      eventBus.publish(event)

      expect(consoleSpy).not.toHaveBeenCalled()

      consoleSpy.mockRestore()
    })
  })
})
