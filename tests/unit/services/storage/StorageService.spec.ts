import type { AppEventMap, EventBus } from '@/types/services/events'
import type { DesignData, StorageEvent } from '@/types/services/events/dataEvents'
import type { LoggerService } from '@/types/services/logging'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { StorageService } from '@/services/storage/storageService'
import { AppEventType } from '@/types/services/events/eventTypes'

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

// Mock Zustand store
vi.mock('@/store/shapesStore', () => ({
  useShapesStore: {
    getState: vi.fn(() => ({
      shapes: [
        {
          id: 'shape-1',
          type: 'RECTANGLE',
          position: { x: 100, y: 100, z: 0 },
          properties: { width: 200, height: 150 },
        },
      ],
      selectedShapeIds: ['shape-1'],
    })),
  },
}))

// Mock dependencies
const mockEventBus = {
  publish: vi.fn(),
  subscribe: vi.fn(),
  unsubscribe: vi.fn(),
  clear: vi.fn(),
} as unknown as EventBus<AppEventMap>

const mockLogger = {
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  debug: vi.fn(),
  setContext: vi.fn(),
  getConfig: vi.fn(),
  setConfig: vi.fn(),
} as unknown as LoggerService

describe('storageService', () => {
  let storageService: StorageService
  let subscribeCallbacks: Map<string, Function>

  beforeEach(() => {
    vi.clearAllMocks()
    subscribeCallbacks = new Map()

    // Mock subscribe to capture callbacks
    mockEventBus.subscribe = vi.fn((eventType: string, callback: Function) => {
      subscribeCallbacks.set(eventType, callback)
      return vi.fn() // unsubscribe function
    })

    storageService = new StorageService(mockEventBus, mockLogger)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('constructor', () => {
    it('should initialize and subscribe to storage events', () => {
      expect(storageService).toBeDefined()
      expect(mockLogger.info).toHaveBeenCalledWith('StorageService initialized')

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.StorageSaveRequest,
        expect.any(Function),
      )
      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.StorageLoadRequest,
        expect.any(Function),
      )
    })
  })

  describe('handleStorageSaveRequest', () => {
    it('should save provided data to localStorage', async () => {
      const designData: DesignData = {
        id: 'test-design',
        title: 'Test Design',
        shapes: {
          'shape-1': {
            id: 'shape-1',
            type: 'RECTANGLE',
            position: { x: 50, y: 50, z: 0 },
            properties: { width: 100, height: 100 },
          },
        },
        createdAt: Date.now(),
        updatedAt: Date.now(),
      }

      const event: StorageEvent = {
        type: AppEventType.StorageSaveRequest,
        timestamp: Date.now(),
        payload: {
          operation: 'save',
          data: designData,
          source: 'TestSource',
        },
      }

      const saveCallback = subscribeCallbacks.get(AppEventType.StorageSaveRequest)
      expect(saveCallback).toBeDefined()

      await saveCallback(event)

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'reno-pilot-shapes-storage',
        expect.stringContaining('"shapes"'),
      )

      expect(mockLogger.info).toHaveBeenCalledWith(
        'StorageService handling StorageSaveRequest:',
        event.payload,
      )

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.StorageSaveComplete,
        timestamp: expect.any(Number),
        payload: {
          operation: 'save',
          data: designData,
          source: 'StorageService',
        },
      })
    })

    it('should save current store state when no data provided', async () => {
      const event: StorageEvent = {
        type: AppEventType.StorageSaveRequest,
        timestamp: Date.now(),
        payload: {
          operation: 'save',
          source: 'TestSource',
        },
      }

      const saveCallback = subscribeCallbacks.get(AppEventType.StorageSaveRequest)
      await saveCallback(event)

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'reno-pilot-shapes-storage',
        expect.stringContaining('"selectedShapeIds"'),
      )

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.StorageSaveComplete,
        timestamp: expect.any(Number),
        payload: {
          operation: 'save',
          data: expect.objectContaining({
            id: 'current_design',
            title: 'Current Design',
          }),
          source: 'StorageService',
        },
      })
    })

    it('should handle localStorage errors gracefully', async () => {
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('Storage quota exceeded')
      })

      const event: StorageEvent = {
        type: AppEventType.StorageSaveRequest,
        timestamp: Date.now(),
        payload: {
          operation: 'save',
          source: 'TestSource',
        },
      }

      const saveCallback = subscribeCallbacks.get(AppEventType.StorageSaveRequest)

      // The callback doesn't return a promise, so we just call it
      await saveCallback(event)

      expect(mockLogger.error).toHaveBeenCalledWith(
        'StorageService failed to save data to localStorage',
        expect.any(Error),
      )
    })

    it('should log completion message', async () => {
      const event: StorageEvent = {
        type: AppEventType.StorageSaveRequest,
        timestamp: Date.now(),
        payload: {
          operation: 'save',
          source: 'TestSource',
        },
      }

      const saveCallback = subscribeCallbacks.get(AppEventType.StorageSaveRequest)
      await saveCallback(event)

      expect(mockLogger.info).toHaveBeenCalledWith(
        'StorageService completed save operation',
      )
    })
  })

  describe('handleStorageLoadRequest', () => {
    it('should handle load requests', async () => {
      const event: StorageEvent = {
        type: AppEventType.StorageLoadRequest,
        timestamp: Date.now(),
        payload: {
          operation: 'load',
          source: 'TestSource',
        },
      }

      const loadCallback = subscribeCallbacks.get(AppEventType.StorageLoadRequest)
      expect(loadCallback).toBeDefined()

      await loadCallback(event)

      expect(mockLogger.info).toHaveBeenCalledWith(
        'StorageService handling StorageLoadRequest:',
        event.payload,
      )

      expect(mockEventBus.publish).toHaveBeenCalledWith({
        type: AppEventType.StorageLoadComplete,
        timestamp: expect.any(Number),
        payload: {
          operation: 'load',
          source: 'StorageService',
        },
      })
    })

    it('should log completion message for load operations', async () => {
      const event: StorageEvent = {
        type: AppEventType.StorageLoadRequest,
        timestamp: Date.now(),
        payload: {
          operation: 'load',
          source: 'TestSource',
        },
      }

      const loadCallback = subscribeCallbacks.get(AppEventType.StorageLoadRequest)
      await loadCallback(event)

      expect(mockLogger.info).toHaveBeenCalledWith(
        'StorageService completed load operation',
      )
    })

    it('should handle load requests without payload', async () => {
      const event: StorageEvent = {
        type: AppEventType.StorageLoadRequest,
        timestamp: Date.now(),
        payload: {
          operation: 'load',
          source: 'TestSource',
        },
      }

      const loadCallback = subscribeCallbacks.get(AppEventType.StorageLoadRequest)

      // The callback doesn't return a promise, so we just call it
      await loadCallback(event)

      // Should not throw
      expect(true).toBe(true)
    })
  })

  describe('error handling', () => {
    it('should handle malformed events gracefully', async () => {
      const malformedEvent = {
        type: AppEventType.StorageSaveRequest,
        // Missing required properties
      } as any

      const saveCallback = subscribeCallbacks.get(AppEventType.StorageSaveRequest)

      // The callback doesn't return a promise, so we just call it
      await saveCallback(malformedEvent)

      // Should not throw
      expect(true).toBe(true)
    })

    it('should handle null events gracefully', async () => {
      const saveCallback = subscribeCallbacks.get(AppEventType.StorageSaveRequest)

      // The callback doesn't return a promise, so we just call it
      await saveCallback(null)

      // Should not throw
      expect(true).toBe(true)
    })

    it('should handle undefined events gracefully', async () => {
      const loadCallback = subscribeCallbacks.get(AppEventType.StorageLoadRequest)

      // The callback doesn't return a promise, so we just call it
      await loadCallback(undefined)

      // Should not throw
      expect(true).toBe(true)
    })
  })

  describe('data serialization', () => {
    it('should properly serialize complex shape data', async () => {
      const complexData: DesignData = {
        id: 'complex-design',
        title: 'Complex Design',
        shapes: {
          'polygon-1': {
            id: 'polygon-1',
            type: 'POLYGON',
            position: { x: 100, y: 100, z: 0 },
            properties: {
              sides: 6,
              radius: 50,
              points: [
                { x: 0, y: 50, z: 0 },
                { x: 43.3, y: 25, z: 0 },
                { x: 43.3, y: -25, z: 0 },
                { x: 0, y: -50, z: 0 },
                { x: -43.3, y: -25, z: 0 },
                { x: -43.3, y: 25, z: 0 },
              ],
            },
          },
        },
        createdAt: Date.now(),
        updatedAt: Date.now(),
      }

      const event: StorageEvent = {
        type: AppEventType.StorageSaveRequest,
        timestamp: Date.now(),
        payload: {
          operation: 'save',
          data: complexData,
          source: 'TestSource',
        },
      }

      const saveCallback = subscribeCallbacks.get(AppEventType.StorageSaveRequest)
      await saveCallback(event)

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'reno-pilot-shapes-storage',
        expect.stringContaining('polygon-1'),
      )
    })
  })
})
