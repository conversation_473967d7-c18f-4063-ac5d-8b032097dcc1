/**
 * Provides a centralized registry for managing service instances across the application.
 *
 * @remarks
 * This module includes the {@link ServiceRegistry} class, a {@link ServiceFactory} interface
 * and implementation, service identifiers (re-exported), and initialization functions.
 * The registry uses a singleton pattern to ensure a single source of truth for service instances.
 *
 * @module services/core/registry
 */

import type { ElementFactory } from '@/core/factory'
import type { AppEventMap, EventBus } from '@/types/services/events'
import type { KeyboardService } from '@/types/services/keyboard'
/**
 * Manages service instances, providing registration and retrieval capabilities.
 * Implements the Singleton pattern to ensure a single registry instance.
 */
// Import necessary types
import type { LoggerService } from '@/types/services/logging'

import { ElementValidator } from '@/core/validator'
import { ElementCreationService } from '../../elements/element-actions/elementCreationService'
import { ElementDeleteService } from '../../elements/element-actions/elementDeleteService'
import { ElementEditServiceImpl } from '../../elements/element-actions/elementEditService'
import { ElementSelectionService } from '../../elements/element-actions/elementSelectionService'
import { KeyboardServiceImpl } from '../../input/keyboard-service'
import { ErrorService as SystemErrorServiceImpl } from '../../system/error-service/errorService'
import { ConsoleLoggerService } from '../../system/logging/consoleLoggerService'
import { getEventBus } from '../event-bus'

export class ServiceRegistry {
  private static instance: ServiceRegistry
  private registry: Map<string, unknown> = new Map()

  private constructor() {
    // Private constructor ensures singleton pattern
  }

  /**
   * Gets the singleton instance of the ServiceRegistry.
   * @returns {ServiceRegistry} The singleton ServiceRegistry instance.
   */
  static getInstance(): ServiceRegistry {
    if (typeof ServiceRegistry.instance === 'undefined') {
      ServiceRegistry.instance = new ServiceRegistry()
    }
    return ServiceRegistry.instance
  }

  /**
   * Registers a service instance with a given identifier.
   * @param {string} id - The unique identifier for the service.
   * @param {unknown} service - The service instance to register.
   */
  register(id: string, service: unknown): void {
    this.registry.set(id, service)
  }

  /**
   * Retrieves a registered service instance by its identifier.
   * @template T - The expected type of the service.
   * @param {string} id - The unique identifier of the service to retrieve.
   * @returns {T} The service instance.
   * @throws {Error} If no service is registered with the given identifier.
   */
  getById<T>(id: string): T {
    const service = this.registry.get(id)
    if (typeof service === 'undefined') {
      throw new TypeError(`Service with id '${id}' not registered`)
    }
    return service as T
  }

  /**
   * Checks if a service is registered with the given identifier.
   * @param {string} id - The unique identifier of the service to check.
   * @returns {boolean} True if the service is registered, false otherwise.
   */
  has(id: string): boolean {
    return this.registry.has(id)
  }

  /**
   * Clears all registered services from the registry.
   */
  clear(): void {
    this.registry.clear()
  }
}

/**
 * Convenience function to get the singleton instance of the ServiceRegistry.
 * @returns {ServiceRegistry} The singleton ServiceRegistry instance.
 */
export const getServiceRegistry = ServiceRegistry.getInstance.bind(ServiceRegistry)

/**
 * Defines the contract for a factory that creates service instances.
 * This allows for centralized creation and dependency injection of services.
 */
export interface ServiceFactory {
  /** Creates an instance of the EventBus. */
  createEventBus: () => EventBus<AppEventMap>
  /** Creates an instance of the LoggerService. */
  createLogger: () => LoggerService
  /**
   * Creates an instance of the system's error service implementation.
   * @param {LoggerService} logger - The logger service to be used by the error service.
   * @returns {SystemErrorServiceImpl} An instance of the error service.
   */
  createErrorService: (logger: LoggerService) => SystemErrorServiceImpl
  /**
   * Creates an instance of the ElementValidator (validation service).
   * @param {LoggerService} logger - The logger service.
   * @returns {ElementValidator} An instance of the validation service.
   */
  createValidationService: (logger: LoggerService) => ElementValidator

  /**
   * Creates an instance of the ElementCreationService.
   * @param {ElementFactory} elementFactory - The factory for creating elements.
   * @param {LoggerService} logger - The logger service.
   * @returns {ElementCreationService} An instance of the element creation service.
   */
  createElementCreationService: (elementFactory: ElementFactory, logger: LoggerService) => ElementCreationService

  /**
   * Creates an instance of the ElementEditService.
   * @param {EventBus<AppEventMap>} eventBus - The application event bus.
   * @param {LoggerService} logger - The logger service.
   * @returns {ElementEditServiceImpl} An instance of the element edit service.
   */
  createElementEditService: (
    eventBus: EventBus<AppEventMap>,
    logger: LoggerService
  ) => ElementEditServiceImpl

  /**
   * Creates an instance of the ElementDeleteService.
   * @param {EventBus<AppEventMap>} eventBus - The application event bus.
   * @param {LoggerService} logger - The logger service.
   * @returns {ElementDeleteService} An instance of the element delete service.
   */
  createElementDeleteService: (
    eventBus: EventBus<AppEventMap>,
    logger: LoggerService
  ) => ElementDeleteService

  /**
   * Creates an instance of the ElementSelectionService.
   * @param {EventBus<AppEventMap>} eventBus - The application event bus.
   * @param {LoggerService} logger - The logger service.
   * @returns {ElementSelectionService} An instance of the element selection service.
   */
  createElementSelectionService: (
    eventBus: EventBus<AppEventMap>,
    logger: LoggerService
  ) => ElementSelectionService

  /**
   * Creates an instance of the KeyboardService.
   * @param {LoggerService} logger - The logger service.
   * @returns {KeyboardService} An instance of the keyboard service.
   */
  createKeyboardService: (logger: LoggerService) => KeyboardService
}

/**
 * Concrete implementation of the ServiceFactory.
 * Provides methods to create instances of all core application services.
 */
export const serviceFactory: ServiceFactory = {
  createEventBus: () => getEventBus(),
  createLogger: () => ConsoleLoggerService.create(),
  createErrorService: (logger: LoggerService) => new SystemErrorServiceImpl(getEventBus(), logger),
  createValidationService: (_logger: LoggerService) => new ElementValidator(),
  createElementCreationService: (elementFactory: ElementFactory, logger: LoggerService) => ElementCreationService.create(elementFactory, logger),
  createElementEditService: (eventBus: EventBus<AppEventMap>, logger: LoggerService) => new ElementEditServiceImpl(eventBus, logger),
  createElementDeleteService: (eventBus: EventBus<AppEventMap>, logger: LoggerService) => new ElementDeleteService(eventBus, logger),
  createElementSelectionService: (eventBus: EventBus<AppEventMap>, logger: LoggerService) => new ElementSelectionService(eventBus, logger),
  createKeyboardService: (logger: LoggerService) => KeyboardServiceImpl.create(logger),
}

/**
 * Gets the singleton instance of the ServiceFactory.
 * @returns {ServiceFactory} The service factory instance.
 */
export const getServiceFactory = (): ServiceFactory => serviceFactory

// Export initialization functions
export {
  cleanupServices,
  getService,
  initializeServices,
} from './initialize'

// Export ServiceId type
export { ServiceId } from '@/types/services/core/serviceIdentifier'
