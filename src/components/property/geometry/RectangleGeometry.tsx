/**
 * Rectangle Geometry Component
 *
 * Implements the standardized geometry layout for Rectangle elements
 * according to the design document specifications.
 *
 * Layout:
 * - Position & Transform (2 columns): Position X, Position Y
 * - Dimensions (2 columns): Width, Height
 * - Element Properties (1 column): Corner Radius
 * - Transform (1 column): Rotation
 * - Calculation & Cost (unified layout)
 */

import type { ShapeElement } from '@/types/core/elementDefinitions'

import { ElementType } from '@/types/core/elementDefinitions'
import { GeometrySection, PropertyField } from '../shared'

interface RectangleGeometryProps {
  /** The rectangle element being edited */
  element: ShapeElement
  /** Function to get input values with proper formatting */
  getValueForInput: (path: string) => string
  /** Function to get placeholder values */
  getPlaceholderForInput: (path: string, exampleValue?: number | string) => string | undefined
  /** Function to handle input changes */
  handleInputChange: (path: string, value: string, isNumeric?: boolean, convertToPixels?: boolean) => void
  /** Unit name for display (e.g., "mm", "cm") */
  unitName: string
}

/**
 * RectangleGeometry component for editing rectangle properties.
 *
 * Provides a standardized layout for rectangle geometry editing
 * with proper grouping and responsive design.
 */
export function RectangleGeometry({
  element,
  getValueForInput: _getValueForInput,
  getPlaceholderForInput: _getPlaceholderForInput,
  handleInputChange,
  unitName,
}: RectangleGeometryProps) {
  // Verify this is a rectangle element
  if (element.type !== ElementType.RECTANGLE) {
    return null
  }

  return (
    <>
      {/* Position (2 columns) */}
      <GeometrySection type="position" columns={2}>
        <PropertyField
          label={`Position X (${unitName})`}
          type="number"
          value={_getValueForInput('position.x')}
          onChange={value => handleInputChange('position.x', value)}
          placeholder={_getPlaceholderForInput('position.x')}
        />
        <PropertyField
          label={`Position Y (${unitName})`}
          type="number"
          value={_getValueForInput('position.y')}
          onChange={value => handleInputChange('position.y', value)}
          placeholder={_getPlaceholderForInput('position.y')}
        />
      </GeometrySection>

      {/* Scale (2 columns) */}
      <GeometrySection type="dimensions" columns={2}>
        <PropertyField
          label={`Width (${unitName})`}
          type="number"
          value={_getValueForInput('width')}
          onChange={value => handleInputChange('width', value)}
          placeholder="0"
        />
        <PropertyField
          label={`Height (${unitName})`}
          type="number"
          value={_getValueForInput('height')}
          onChange={value => handleInputChange('height', value)}
          placeholder="0"
        />
      </GeometrySection>

      {/* Transform (1 column) */}
      <GeometrySection type="transform" columns={1}>
        <PropertyField
          label={`Corner Radius (${unitName})`}
          type="number"
          value={_getValueForInput('cornerRadius')}
          onChange={value => handleInputChange('cornerRadius', value)}
          placeholder="0"
        />
        <PropertyField
          label="Rotation (°)"
          type="number"
          value={_getValueForInput('rotation')}
          onChange={value => handleInputChange('rotation', value, true, false)}
          placeholder="0"
        />
      </GeometrySection>
    </>
  )
}
