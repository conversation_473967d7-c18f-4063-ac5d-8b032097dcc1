import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import type { Mocked } from 'vitest'
import { CoreCoordinator } from '@/core/CoreCoordinator'
import type { ComputeFacade } from '@/core/compute/ComputeFacade'
import type { ElementFactory } from '@/core/factory/ElementFactory'
import type { ShapeRepository } from '@/core/state/ShapeRepository'
import type { ElementValidator } from '@/core/validator/ElementValidator'
import type { EventBus } from '@/types/services/events'
import type { LoggerService } from '@/types/services/logging'
import type { ErrorService } from '@/services/system/error-service/errorService'
import type { AppEventMap } from '@/types/services/events/eventRegistry'
import { ElementType } from '@/types/core/elementDefinitions'

describe('CoreCoordinator', () => {
  let coordinator: CoreCoordinator
  let mockEventBus: Mocked<EventBus>
  let mockShapeRepository: Mocked<ShapeRepository>
  let mockValidator: Mocked<ElementValidator>
  let mockElementFactory: Mocked<ElementFactory>
  let mockLogger: Mocked<LoggerService>
  let mockErrorService: Mocked<ErrorService>
  let mockComputeFacade: Mocked<ComputeFacade>

  beforeEach(() => {
    // Mock EventBus
    mockEventBus = {
      publish: vi.fn(),
      subscribe: vi.fn(),
      on: vi.fn(),
      off: vi.fn(),
      configure: vi.fn(),
    } as unknown as Mocked<EventBus>

    // Mock ShapeRepository
    mockShapeRepository = {
      add: vi.fn(),
      getById: vi.fn(),
      getAll: vi.fn(),
      update: vi.fn(),
      remove: vi.fn(),
      clear: vi.fn(),
      selectShape: vi.fn(),
      deselectShape: vi.fn(),
      clearSelection: vi.fn(),
      getSelectedShapes: vi.fn().mockReturnValue([]),
      getSelectedShapeIds: vi.fn().mockReturnValue([]),
    } as unknown as Mocked<ShapeRepository>

    // Mock ElementValidator
    mockValidator = {
      validateElement: vi.fn().mockReturnValue({ valid: true, errors: [] }),
      validateElements: vi.fn().mockReturnValue([]),
      validateElementAsync: vi.fn().mockResolvedValue({ valid: true, errors: [] }),
      formatErrorMessage: vi.fn().mockReturnValue('Test error'),
    } as unknown as Mocked<ElementValidator>

    // Mock ElementFactory
    mockElementFactory = {
      createShape: vi.fn().mockResolvedValue({
        id: 'test-shape',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: { width: 100, height: 50 },
      }),
      createElement: vi.fn(),
      createDefaultElement: vi.fn(),
      registerCreator: vi.fn(),
      normalizePositionInput: vi.fn().mockReturnValue({ x: 0, y: 0 }),
    } as unknown as Mocked<ElementFactory>

    // Mock Logger
    mockLogger = {
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    } as unknown as Mocked<LoggerService>

    // Mock ErrorService
    mockErrorService = {
      handleError: vi.fn(),
      reportError: vi.fn(),
      getLastError: vi.fn(),
    } as unknown as Mocked<ErrorService>

    // Mock ComputeFacade
    mockComputeFacade = {
      computeArea: vi.fn().mockReturnValue(100),
      computePerimeter: vi.fn().mockReturnValue(40),
      computeBoundingBox: vi.fn().mockReturnValue({
        x: 0, y: 0, width: 100, height: 50,
      }),
      isPointInside: vi.fn().mockReturnValue(false),
      transformElement: vi.fn(),
    } as unknown as Mocked<ComputeFacade>

    // Create CoreCoordinator instance
    coordinator = new CoreCoordinator(
      mockEventBus,
      mockShapeRepository,
      mockValidator,
      mockElementFactory,
      mockLogger,
      mockErrorService,
      undefined, // config
      mockComputeFacade,
    )
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Constructor', () => {
    it('should create CoreCoordinator instance', () => {
      expect(coordinator).toBeDefined()
      expect(coordinator).toBeInstanceOf(CoreCoordinator)
    })

    it('should initialize with provided dependencies', () => {
      expect(coordinator.shapeRepository).toBe(mockShapeRepository)
      expect(coordinator.validator).toBe(mockValidator)
      expect(coordinator.elementFactory).toBe(mockElementFactory)
      expect(coordinator.computeFacade).toBe(mockComputeFacade)
    })

    it('should set up event subscriptions', () => {
      expect(mockEventBus.subscribe).toHaveBeenCalled()
    })
  })

  describe('Shape Creation', () => {
    it('should create a shape successfully', async () => {
      const shapeData = {
        type: ElementType.RECTANGLE,
        position: { x: 10, y: 20 },
        properties: { width: 100, height: 50 },
      }

      const result = await coordinator.createShape(shapeData)

      expect(mockValidator.validateElement).toHaveBeenCalled()
      expect(mockElementFactory.createShape).toHaveBeenCalled()
      expect(mockShapeRepository.add).toHaveBeenCalled()
      expect(result).toBeDefined()
    })

    it('should handle validation errors during shape creation', async () => {
      mockValidator.validateElement.mockReturnValue({
        valid: false,
        errors: ['Invalid shape data'],
      })

      const shapeData = {
        type: ElementType.RECTANGLE,
        position: { x: 10, y: 20 },
        properties: { width: -100, height: 50 }, // Invalid width
      }

      await expect(coordinator.createShape(shapeData)).rejects.toThrow()
      expect(mockErrorService.handleError).toHaveBeenCalled()
    })
  })

  describe('Shape Operations', () => {
    it('should update shape successfully', async () => {
      const shapeId = 'test-shape-1'
      const updates = { properties: { width: 200 } }

      mockShapeRepository.getById.mockReturnValue({
        id: shapeId,
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: { width: 100, height: 50 },
      })

      await coordinator.updateShape(shapeId, updates)

      expect(mockShapeRepository.getById).toHaveBeenCalledWith(shapeId)
      expect(mockShapeRepository.update).toHaveBeenCalled()
    })

    it('should delete shape successfully', async () => {
      const shapeId = 'test-shape-1'

      mockShapeRepository.getById.mockReturnValue({
        id: shapeId,
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: { width: 100, height: 50 },
      })

      await coordinator.deleteShape(shapeId)

      expect(mockShapeRepository.remove).toHaveBeenCalledWith(shapeId)
    })

    it('should handle deletion of non-existent shape', async () => {
      const shapeId = 'non-existent-shape'
      mockShapeRepository.getById.mockReturnValue(undefined)

      await expect(coordinator.deleteShape(shapeId)).rejects.toThrow()
      expect(mockErrorService.handleError).toHaveBeenCalled()
    })
  })

  describe('Selection Management', () => {
    it('should select shape', () => {
      const shapeId = 'test-shape-1'
      coordinator.selectShape(shapeId)

      expect(mockShapeRepository.selectShape).toHaveBeenCalledWith(shapeId)
    })

    it('should clear selection', () => {
      coordinator.clearSelection()

      expect(mockShapeRepository.clearSelection).toHaveBeenCalled()
    })

    it('should get selected shapes', () => {
      const selectedShapes = [
        { id: 'shape-1', type: ElementType.RECTANGLE },
        { id: 'shape-2', type: ElementType.ELLIPSE },
      ]
      mockShapeRepository.getSelectedShapes.mockReturnValue(selectedShapes)

      const result = coordinator.getSelectedShapes()

      expect(result).toEqual(selectedShapes)
      expect(mockShapeRepository.getSelectedShapes).toHaveBeenCalled()
    })
  })

  describe('Compute Integration', () => {
    it('should compute area using ComputeFacade', () => {
      const shape = {
        id: 'test-shape',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: { width: 100, height: 50 },
      }

      const area = coordinator.computeArea(shape)

      expect(mockComputeFacade.computeArea).toHaveBeenCalledWith(shape)
      expect(area).toBe(100)
    })

    it('should compute perimeter using ComputeFacade', () => {
      const shape = {
        id: 'test-shape',
        type: ElementType.RECTANGLE,
        position: { x: 0, y: 0 },
        properties: { width: 100, height: 50 },
      }

      const perimeter = coordinator.computePerimeter(shape)

      expect(mockComputeFacade.computePerimeter).toHaveBeenCalledWith(shape)
      expect(perimeter).toBe(40)
    })
  })

  describe('Error Handling', () => {
    it('should handle errors gracefully', async () => {
      mockElementFactory.createShape.mockRejectedValue(new Error('Factory error'))

      const shapeData = {
        type: ElementType.RECTANGLE,
        position: { x: 10, y: 20 },
        properties: { width: 100, height: 50 },
      }

      await expect(coordinator.createShape(shapeData)).rejects.toThrow()
      expect(mockErrorService.handleError).toHaveBeenCalled()
    })

    it('should log errors appropriately', async () => {
      const error = new Error('Test error')
      mockElementFactory.createShape.mockRejectedValue(error)

      const shapeData = {
        type: ElementType.RECTANGLE,
        position: { x: 10, y: 20 },
        properties: { width: 100, height: 50 },
      }

      try {
        await coordinator.createShape(shapeData)
      } catch {
        // Expected to throw
      }

      expect(mockLogger.error).toHaveBeenCalled()
    })
  })
})
