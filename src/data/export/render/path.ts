/**
 * Path SVG Renderers
 *
 * This module provides rendering functions for open and curved paths,
 * including lines, polylines, arcs, and quadratic/cubic Bezier curves.
 *
 * All renderers support rotation via the `rotation` property:
 * - For lines, polylines, and Bezier curves, rotation is around the start point.
 * - For arcs, rotation is around the arc's center (`position`).
 *
 * @module render/path
 */
import type { Arc, Cubic, Line, Polyline, Quadratic } from '@/types/core'
import type Point from '@/types/core/element/geometry/point'
import { getShapeStyle } from './general'

export async function renderLine(e: Line): Promise<string> {
  const pos = e.position
  const { start, end } = e.properties
  const { stroke, strokeWidth } = getShapeStyle(e)

  return (
    `<line x1="${start.x + pos.x}" y1="${start.y + pos.y}"`
    + ` x2="${end.x + pos.x}" y2="${end.y + pos.y}"`
    + ` stroke="${stroke}" stroke-width="${strokeWidth}" />`
  )
}

export async function renderPolyline(e: Polyline): Promise<string> {
  const pos = e.position
  const points = e.properties.points.map(
    (p: Point) => `${p.x + pos.x},${p.y + pos.y}`,
  ).join(' ')
  const { stroke, strokeWidth } = getShapeStyle(e)

  return (
    `<polyline points="${points}" fill="none"`
    + ` stroke="${stroke}" stroke-width="${strokeWidth}" />`
  )
}

export async function renderArc(e: Arc): Promise<string> {
  const pos = e.position
  const { radius, startAngle, endAngle, closed } = e.properties
  const { stroke, strokeWidth, fill } = getShapeStyle(e)

  // Angles to radians
  const toRad = (deg: number) => (deg * Math.PI) / 180
  const startRad = toRad(startAngle)
  const endRad = toRad(endAngle)

  // Start and end points
  const x1 = pos.x + radius! * Math.cos(startRad)
  const y1 = pos.y + radius! * Math.sin(startRad)
  const x2 = pos.x + radius! * Math.cos(endRad)
  const y2 = pos.y + radius! * Math.sin(endRad)

  // Is a superior arc or not
  let delta = endAngle - startAngle
  if (delta < 0)
    delta += 360
  const largeArcFlag = delta > 180 ? 1 : 0
  const sweepFlag = 1

  let d = `M ${x1} ${y1} A ${radius} ${radius} 0 `
    + `${largeArcFlag} ${sweepFlag} ${x2} ${y2}`
  if (closed) {
    d += ` L ${pos.x} ${pos.y} Z`
  }

  return (
    `<path d="${d}" fill="${closed ? (fill || '#ccc') : 'none'}" `
    + `stroke="${stroke}" stroke-width="${strokeWidth}" />`
  )
}

export async function renderQuadratic(e: Quadratic): Promise<string> {
  const pos = e.position
  const { start, control, end } = e.properties
  const { stroke, strokeWidth } = getShapeStyle(e)
  const d = `M ${start.x + pos.x} ${start.y + pos.y} Q ${control.x + pos.x} `
    + `${control.y + pos.y} ${end.x + pos.x} ${end.y + pos.y}`

  return (
    `<path d="${d}" fill="none" stroke="${stroke}" `
    + `stroke-width="${strokeWidth}" />`
  )
}

export async function renderCubic(e: Cubic): Promise<string> {
  const pos = e.position
  const { start, control1, control2, end } = e.properties
  const { stroke, strokeWidth } = getShapeStyle(e)
  const d = `M ${start.x + pos.x} ${start.y + pos.y} `
    + `C ${control1.x + pos.x} ${control1.y + pos.y} `
    + `${control2.x + pos.x} ${control2.y + pos.y} `
    + `${end.x + pos.x} ${end.y + pos.y}`

  return (
    `<path d="${d}" fill="none" stroke="${stroke}" `
    + `stroke-width="${strokeWidth}" />`
  )
}
