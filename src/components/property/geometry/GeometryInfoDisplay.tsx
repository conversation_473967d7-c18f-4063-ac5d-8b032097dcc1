/**
 * Geometry Information Display Component
 *
 * Displays calculated geometric properties for selected elements:
 * - Closed shapes: Area and Perimeter
 * - Path elements: Length
 * - Text/Image: No geometric measurements
 *
 * Features:
 * - Uses stored calculation results with real-time fallback
 * - Supports unit conversion (mm, cm, m)
 * - Consistent UI with GeometrySection layout
 * - Handles 14 basic element types
 */

import type { ComputeStatus } from '@/types/core/computeStatus'
import type { ShapeElement } from '@/types/core/elementDefinitions'

import React, { useMemo } from 'react'

import { Label } from '@/components/ui/label'
import { calculateCubicBezierLengthGauss, calculateQuadraticBezierLengthGauss } from '@/lib/utils/geometry/bezierUtils'
import { ElementType } from '@/types/core/elementDefinitions'
import { GeometrySection } from '../shared'
import { ComputeStatusIndicator } from './ComputeStatusIndicator'
import { getComputeStatus } from '@/utils/compute-status'

interface GeometryInfoDisplayProps {
  /** The element to display geometry information for */
  element: ShapeElement
  /** Conversion factor from pixels to millimeters */
  pixelsPerMM: number
  /** Function to get common property values */
  getCommonValue: (path: string) => unknown
}

// 14个基本元素分类
const CLOSED_SHAPE_TYPES = [
  ElementType.RECTANGLE,
  ElementType.SQUARE,
  ElementType.CIRCLE,
  ElementType.ELLIPSE,
  ElementType.TRIANGLE,
  ElementType.PENTAGON,
  ElementType.HEXAGON,
]

const PATH_TYPES = [
  ElementType.LINE,
  ElementType.POLYLINE,
  ElementType.ARC,
  ElementType.QUADRATIC,
  ElementType.CUBIC,
]

const NO_MEASUREMENT_TYPES = [
  ElementType.TEXT,
  ElementType.IMAGE,
]

// Type guard functions for safe property access
function hasPoints(element: ShapeElement): element is ShapeElement & { points: Array<{ x: number, y: number }> } {
  const elementWithPoints = element as unknown as { points?: unknown }
  return Array.isArray(elementWithPoints.points)
}

function getPointsProperty(properties: Record<string, unknown> | undefined, key: string): Array<{ x: number, y: number }> {
  const value = properties?.[key]
  return Array.isArray(value) ? value as Array<{ x: number, y: number }> : []
}

function getPointProperty(properties: Record<string, unknown> | undefined, key: string, defaultValue: { x: number, y: number, z?: number }): { x: number, y: number, z?: number } {
  const value = properties?.[key]
  if (typeof value === 'object' && value !== null) {
    const pointValue = value as Record<string, unknown>
    if (typeof pointValue.x === 'number' && typeof pointValue.y === 'number') {
      return value as { x: number, y: number, z?: number }
    }
  }
  return defaultValue
}

/**
 * GeometryInfoDisplay component for showing calculated geometric properties.
 */
function GeometryInfoDisplayComponent({
  element,
  pixelsPerMM,
  getCommonValue: _getCommonValue,
}: GeometryInfoDisplayProps) {
  // 确定元素类型分类
  const isClosedShape = CLOSED_SHAPE_TYPES.includes(element.type as ElementType)
  const isPathType = PATH_TYPES.includes(element.type as ElementType)
  const showMeasurements = !NO_MEASUREMENT_TYPES.includes(element.type as ElementType)

  // 单位转换函数
  const formatValue = (valueInPixels: number, unit: 'area' | 'length'): string => {
    if (!valueInPixels || !Number.isFinite(valueInPixels))
      return 'N/A'

    if (unit === 'area') {
      // 面积单位转换：像素² -> mm² -> cm² -> m²
      // 面积需要除以 pixelsPerMM 的平方
      const valueInMM2 = valueInPixels / (pixelsPerMM * pixelsPerMM)

      if (valueInMM2 < 10000) { // < 100cm²
        return `${valueInMM2.toFixed(1)} mm²`
      }
      else if (valueInMM2 < 1000000) { // < 1m²
        return `${(valueInMM2 / 100).toFixed(2)} cm²`
      }
      else {
        return `${(valueInMM2 / 1000000).toFixed(3)} m²`
      }
    }
    else {
      // 长度单位转换：像素 -> mm -> cm -> m
      const valueInMM = valueInPixels / pixelsPerMM

      if (valueInMM < 100) {
        return `${valueInMM.toFixed(1)} mm`
      }
      else if (valueInMM < 10000) {
        return `${(valueInMM / 10).toFixed(2)} cm`
      }
      else {
        return `${(valueInMM / 1000).toFixed(3)} m`
      }
    }
  }

  // 计算几何属性（实时计算作为回退）
  const geometryValues = useMemo(() => {
    if (!showMeasurements)
      return null

    // 优先使用存储的计算结果
    const storedArea = element.properties?.computedArea as number
    const storedPerimeter = element.properties?.computedPerimeter as number
    const storedLength = element.properties?.computedLength as number

    let area = 0
    let perimeter = 0
    let length = 0

    // 如果有存储的结果，优先使用
    if (storedArea && Number.isFinite(storedArea)) {
      area = storedArea
    }
    if (storedPerimeter && Number.isFinite(storedPerimeter)) {
      perimeter = storedPerimeter
    }
    if (storedLength && Number.isFinite(storedLength)) {
      length = storedLength
    }

    // 如果没有存储结果，进行简单的实时计算作为回退
    if ((!area && !perimeter && isClosedShape) || (!length && isPathType)) {
      const width = (element.properties?.width as number) || 0
      const height = (element.properties?.height as number) || 0
      const radius = (element.properties?.radius as number) || 0
      const radiusX = (element.properties?.radiusX as number) || radius
      const radiusY = (element.properties?.radiusY as number) || radius

      switch (element.type) {
        case ElementType.RECTANGLE:
        case ElementType.SQUARE:
          area = width * height
          perimeter = 2 * (width + height)
          break
        case ElementType.CIRCLE:
          area = Math.PI * radius * radius
          perimeter = 2 * Math.PI * radius
          break
        case ElementType.ELLIPSE:
          area = Math.PI * radiusX * radiusY
          // 椭圆周长近似公式
          perimeter = Math.PI * (3 * (radiusX + radiusY) - Math.sqrt((3 * radiusX + radiusY) * (radiusX + 3 * radiusY)))
          break
        case ElementType.TRIANGLE: {
          // 对于三角形，如果有点坐标，使用更准确的计算
          const trianglePoints = hasPoints(element) ? element.points : []
          if (trianglePoints.length === 3) {
            // 使用鞋带公式计算面积
            const [p1, p2, p3] = trianglePoints
            area = Math.abs((p1.x * (p2.y - p3.y) + p2.x * (p3.y - p1.y) + p3.x * (p1.y - p2.y)) / 2)
            // 计算三边长度
            const side1 = Math.sqrt((p2.x - p1.x) ** 2 + (p2.y - p1.y) ** 2)
            const side2 = Math.sqrt((p3.x - p2.x) ** 2 + (p3.y - p2.y) ** 2)
            const side3 = Math.sqrt((p1.x - p3.x) ** 2 + (p1.y - p3.y) ** 2)
            perimeter = side1 + side2 + side3
          }
          else {
            // 回退到简化计算（假设等腰直角三角形）
            area = 0.5 * width * height
            perimeter = width + height + Math.sqrt(width * width + height * height)
          }
          break
        }
        case ElementType.PENTAGON: {
          // 对于五边形，如果有点坐标，使用更准确的计算
          const pentagonPoints = hasPoints(element) ? element.points : []
          if (pentagonPoints.length === 5) {
            // 使用鞋带公式计算面积
            area = Math.abs(pentagonPoints.reduce((sum, point, i) => {
              const nextPoint = pentagonPoints[(i + 1) % pentagonPoints.length]
              return sum + (point.x * nextPoint.y - nextPoint.x * point.y)
            }, 0) / 2)
            // 计算周长
            perimeter = pentagonPoints.reduce((sum, point, i) => {
              const nextPoint = pentagonPoints[(i + 1) % pentagonPoints.length]
              return sum + Math.sqrt((nextPoint.x - point.x) ** 2 + (nextPoint.y - point.y) ** 2)
            }, 0)
          }
          else {
            // 回退到简化计算（假设正五边形）
            const side = Math.min(width, height) * 0.8
            area = (5 * side * side) / (4 * Math.tan(Math.PI / 5))
            perimeter = 5 * side
          }
          break
        }
        case ElementType.HEXAGON: {
          // 对于六边形，如果有点坐标，使用更准确的计算
          const hexagonPoints = hasPoints(element) ? element.points : []
          if (hexagonPoints.length === 6) {
            // 使用鞋带公式计算面积
            area = Math.abs(hexagonPoints.reduce((sum, point, i) => {
              const nextPoint = hexagonPoints[(i + 1) % hexagonPoints.length]
              return sum + (point.x * nextPoint.y - nextPoint.x * point.y)
            }, 0) / 2)
            // 计算周长
            perimeter = hexagonPoints.reduce((sum, point, i) => {
              const nextPoint = hexagonPoints[(i + 1) % hexagonPoints.length]
              return sum + Math.sqrt((nextPoint.x - point.x) ** 2 + (nextPoint.y - point.y) ** 2)
            }, 0)
          }
          else {
            // 回退到简化计算（假设正六边形）
            const side = Math.min(width, height) * 0.8
            area = (3 * Math.sqrt(3) * side * side) / 2
            perimeter = 6 * side
          }
          break
        }
        case ElementType.LINE: {
          // LINE 元素的数据结构：properties.start 和 properties.end
          const startPoint = getPointProperty(element.properties, 'start', { x: 0, y: 0 })
          const endPoint = getPointProperty(element.properties, 'end', { x: 100, y: 0 })
          length = Math.sqrt((endPoint.x - startPoint.x) ** 2 + (endPoint.y - startPoint.y) ** 2)
          break
        }
        case ElementType.ARC: {
          const arcRadius = radius || 50
          const startAngle = (element.properties?.startAngle as number) || 0
          const endAngle = (element.properties?.endAngle as number) || 90
          const angleRange = Math.abs(endAngle - startAngle) * Math.PI / 180
          length = arcRadius * angleRange
          break
        }
        case ElementType.POLYLINE: {
          // POLYLINE 元素的数据结构：properties.points
          const points = getPointsProperty(element.properties, 'points')
          if (points.length >= 2) {
            length = 0
            for (let i = 0; i < points.length - 1; i++) {
              const p1 = points[i]
              const p2 = points[i + 1]
              length += Math.sqrt((p2.x - p1.x) ** 2 + (p2.y - p1.y) ** 2)
            }
          }
          break
        }
        case ElementType.QUADRATIC: {
          // QUADRATIC 元素的数据结构：properties.start, properties.control, properties.end
          const quadStart = getPointProperty(element.properties, 'start', { x: 0, y: 0 })
          const quadControl = getPointProperty(element.properties, 'control', { x: 50, y: 50 })
          const quadEnd = getPointProperty(element.properties, 'end', { x: 100, y: 0 })
          // 使用与专门策略相同的高斯积分方法
          try {
            length = calculateQuadraticBezierLengthGauss(quadStart, quadControl, quadEnd)
          }
          catch (error) {
            console.warn('[GeometryInfoDisplay] 二次曲线长度计算失败，使用回退方法:', error)
            // 回退到简单的控制点距离估算
            const dist1 = Math.sqrt((quadControl.x - quadStart.x) ** 2 + (quadControl.y - quadStart.y) ** 2)
            const dist2 = Math.sqrt((quadEnd.x - quadControl.x) ** 2 + (quadEnd.y - quadControl.y) ** 2)
            length = dist1 + dist2
          }
          break
        }
        case ElementType.CUBIC: {
          // CUBIC 元素的数据结构：properties.start, properties.control1, properties.control2, properties.end
          const cubicStart = getPointProperty(element.properties, 'start', { x: 0, y: 0 })
          const cubicControl1 = getPointProperty(element.properties, 'control1', { x: 33, y: 33 })
          const cubicControl2 = getPointProperty(element.properties, 'control2', { x: 67, y: 67 })
          const cubicEnd = getPointProperty(element.properties, 'end', { x: 100, y: 0 })
          // 使用与专门策略相同的高斯积分方法
          try {
            length = calculateCubicBezierLengthGauss(cubicStart, cubicControl1, cubicControl2, cubicEnd)
          }
          catch (error) {
            console.warn('[GeometryInfoDisplay] 三次曲线长度计算失败，使用回退方法:', error)
            // 回退到简单的控制点距离估算
            const dist1 = Math.sqrt((cubicControl1.x - cubicStart.x) ** 2 + (cubicControl1.y - cubicStart.y) ** 2)
            const dist2 = Math.sqrt((cubicControl2.x - cubicControl1.x) ** 2 + (cubicControl2.y - cubicControl1.y) ** 2)
            const dist3 = Math.sqrt((cubicEnd.x - cubicControl2.x) ** 2 + (cubicEnd.y - cubicControl2.y) ** 2)
            length = dist1 + dist2 + dist3
          }
          break
        }
      }
    }

    return { area, perimeter, length }
  }, [element, showMeasurements, isClosedShape, isPathType])

  if (!showMeasurements || !geometryValues) {
    return null
  }

  return (
    <GeometrySection type="calculation" title="Material Measurements" columns={isClosedShape ? 2 : 1}>

      {isClosedShape && (
        <>
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Label className="text-sm font-medium">Area</Label>
              <ComputeStatusIndicator
                status={getComputeStatus(
                  geometryValues.area, // 使用实际显示的值
                  element.properties?.computedAreaStatus as ComputeStatus,
                  element.properties?.computedAreaError as string,
                )}
                size="sm"
              />
            </div>
            <div className="text-sm text-muted-foreground bg-muted px-2 py-1 rounded">
              {formatValue(geometryValues.area, 'area')}
            </div>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Label className="text-sm font-medium">Perimeter</Label>
              <ComputeStatusIndicator
                status={getComputeStatus(
                  geometryValues.perimeter, // 使用实际显示的值
                  element.properties?.computedPerimeterStatus as ComputeStatus,
                  element.properties?.computedPerimeterError as string,
                )}
                size="sm"
              />
            </div>
            <div className="text-sm text-muted-foreground bg-muted px-2 py-1 rounded">
              {formatValue(geometryValues.perimeter, 'length')}
            </div>
          </div>
        </>
      )}

      {isPathType && (
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Label className="text-sm font-medium">Length</Label>
            <ComputeStatusIndicator
              status={getComputeStatus(
                geometryValues.length, // 使用实际显示的值
                element.properties?.computedLengthStatus as ComputeStatus,
                element.properties?.computedLengthError as string,
              )}
              size="sm"
            />
          </div>
          <div className="text-sm text-muted-foreground bg-muted px-2 py-1 rounded">
            {formatValue(geometryValues.length, 'length')}
          </div>
        </div>
      )}
    </GeometrySection>
  )
}

// 使用 React.memo 优化渲染性能，添加自定义比较函数避免不必要的重新渲染
export const GeometryInfoDisplay = React.memo(GeometryInfoDisplayComponent, (prevProps, nextProps) => {
  // 只有在关键属性真正变化时才重新渲染
  return (
    prevProps.element.id === nextProps.element.id
    && prevProps.element.type === nextProps.element.type
    && prevProps.pixelsPerMM === nextProps.pixelsPerMM
    && JSON.stringify(prevProps.element.properties) === JSON.stringify(nextProps.element.properties)
  )
})
