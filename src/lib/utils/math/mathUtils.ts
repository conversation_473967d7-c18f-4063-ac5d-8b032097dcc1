/**
 * General Mathematical Utility Functions
 *
 * @remarks
 * This module provides a collection of common mathematical utility functions
 * useful in various parts of an application, especially in graphics, animation,
 * and data processing. Functions include:
 * - Angle conversions (degrees to radians, radians to degrees).
 * - Clamping a value within a min/max range.
 * - Linear interpolation (lerp).
 * - Mapping a value from one numerical range to another.
 * - Rounding numbers to a specified number of decimal places.
 * - Generating random integers and floating-point numbers within a range.
 * - Checking for approximate equality between two numbers.
 * - Normalizing a value to a 0-1 range.
 * - Calculating a percentage of a value within a range.
 *
 * All functions expect numerical inputs and will throw an Error if invalid
 * (non-numeric) parameters are provided.
 *
 * @module lib/utils/math/mathUtils
 */

/**
 * Converts an angle from radians to degrees.
 * @param radians - The angle in radians.
 * @returns The angle in degrees.
 * @throws {@link Error} if the input `radians` is not a number.
 */
export function toDegrees(radians: number): number {
  if (typeof radians !== 'number') {
    throw new TypeError('Input must be a number')
  }
  return radians * (180 / Math.PI)
}

/**
 * Converts an angle from degrees to radians.
 * @param degrees - The angle in degrees.
 * @returns The angle in radians.
 * @throws {@link Error} if the input `degrees` is not a number.
 */
export function toRadians(degrees: number): number {
  if (typeof degrees !== 'number') {
    throw new TypeError('Input must be a number')
  }
  return degrees * (Math.PI / 180)
}

/**
 * Clamps a numeric value to be within a specified minimum and maximum range.
 * @param value - The number to clamp.
 * @param min - The minimum allowed value.
 * @param max - The maximum allowed value.
 * @returns The clamped value, such that `min <= result <= max`.
 * @throws {@link Error} if any of the input parameters (`value`, `min`, `max`) is not a number.
 */
export function clamp(value: number, min: number, max: number): number {
  if (typeof value !== 'number' || typeof min !== 'number' || typeof max !== 'number') {
    throw new TypeError('All parameters must be numbers')
  }
  return Math.min(Math.max(value, min), max)
}

/**
 * Performs linear interpolation between two numbers.
 *
 * @param start - The starting value (when `t` is 0).
 * @param end - The ending value (when `t` is 1).
 * @param t - The interpolation factor, typically a value between 0.0 (returns `start`) and 1.0 (returns `end`).
 *            Values outside this range will extrapolate.
 * @returns The interpolated value: `start * (1 - t) + end * t`.
 * @throws {@link Error} if `start`, `end`, or `t` is not a number.
 */
export function lerp(start: number, end: number, t: number): number {
  if (typeof start !== 'number' || typeof end !== 'number' || typeof t !== 'number') {
    throw new TypeError('All parameters must be numbers')
  }
  return start * (1 - t) + end * t
}

/**
 * Re-maps a number from one range to another.
 *
 * @param value - The number to re-map.
 * @param inMin - The lower bound of the value's current range.
 * @param inMax - The upper bound of the value's current range.
 * @param outMin - The lower bound of the target range.
 * @param outMax - The upper bound of the target range.
 * @returns The value re-mapped to the target range. If `inMin` equals `inMax`, returns `outMin`.
 * @throws {@link Error} if any parameter is not a number.
 */
export function map(
  value: number,
  inMin: number,
  inMax: number,
  outMin: number,
  outMax: number,
): number {
  if (typeof value !== 'number'
    || typeof inMin !== 'number'
    || typeof inMax !== 'number'
    || typeof outMin !== 'number'
    || typeof outMax !== 'number') {
    throw new TypeError('All parameters must be numbers')
  }
  if (inMin === inMax) {
    return outMin
  }
  return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin
}

/**
 * Rounds a number to a specified number of decimal places.
 * @param value - The number to round.
 * @param decimals - The number of decimal places to round to.
 * @returns The rounded number.
 * @throws {@link Error} if `value` or `decimals` is not a number.
 */
export function roundToDecimals(value: number, decimals: number): number {
  if (typeof value !== 'number' || typeof decimals !== 'number') {
    throw new TypeError('Both value and decimals must be numbers')
  }
  const factor = 10 ** decimals
  return Math.round(value * factor) / factor
}

/**
 * Generates a random integer between a minimum (inclusive) and maximum (inclusive) value.
 * @param min - The minimum possible integer value.
 * @param max - The maximum possible integer value.
 * @returns A random integer within the specified range.
 * @throws {@link Error} if `min` or `max` is not a number.
 */
export function getRandomInt(min: number, max: number): number {
  if (typeof min !== 'number' || typeof max !== 'number') {
    throw new TypeError('Both min and max must be numbers')
  }
  min = Math.ceil(min)
  max = Math.floor(max)
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * Generates a random floating-point number between a minimum (inclusive) and maximum (exclusive) value.
 * @param min - The minimum possible float value.
 * @param max - The maximum possible float value (exclusive).
 * @returns A random floating-point number within the specified range.
 * @throws {@link Error} if `min` or `max` is not a number.
 */
export function getRandomFloat(min: number, max: number): number {
  if (typeof min !== 'number' || typeof max !== 'number') {
    throw new TypeError('Both min and max must be numbers')
  }
  return Math.random() * (max - min) + min
}

/**
 * Checks if two numbers are approximately equal within a specified tolerance.
 * @param a - The first number.
 * @param b - The second number.
 * @param tolerance - The maximum absolute difference allowed for the numbers to be considered equal. Defaults to `1e-10`.
 * @returns `true` if the absolute difference between `a` and `b` is less than `tolerance`, `false` otherwise.
 * @throws {@link Error} if `a`, `b`, or `tolerance` is not a number.
 */
export function areNumbersEqual(
  a: number,
  b: number,
  tolerance: number = 1e-10,
): boolean {
  if (typeof a !== 'number' || typeof b !== 'number' || typeof tolerance !== 'number') {
    throw new TypeError('All parameters must be numbers')
  }
  return Math.abs(a - b) < tolerance
}

/**
 * Normalizes a given value from an input range to a 0-1 range.
 *
 * @param value - The value to normalize.
 * @param min - The minimum value of the input range.
 * @param max - The maximum value of the input range.
 * @returns The normalized value (a number between 0 and 1). If `min` equals `max`, returns 0.
 * @throws {@link Error} if any parameter is not a number.
 */
export function normalize(value: number, min: number, max: number): number {
  if (typeof value !== 'number' || typeof min !== 'number' || typeof max !== 'number') {
    throw new TypeError('All parameters must be numbers')
  }
  if (min === max)
    return 0
  return (value - min) / (max - min)
}

/**
 * Calculates the percentage representation of a value within a given range.
 *
 * @remarks This function first normalizes the value to a 0-1 range using {@link normalize},
 *          then multiplies by 100.
 *
 * @param value - The value whose percentage in the range is to be calculated.
 * @param min - The minimum value of the range.
 * @param max - The maximum value of the range.
 * @returns The percentage (a number between 0 and 100).
 * @throws {@link Error} if any parameter is not a number (propagated from `normalize`).
 */
export function percentage(value: number, min: number, max: number): number {
  return normalize(value, min, max) * 100
}
