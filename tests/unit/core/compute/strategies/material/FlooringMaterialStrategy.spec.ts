import { beforeEach, describe, expect, it, vi } from 'vitest'
import { FlooringMaterialStrategy } from '@/core/compute/strategies/material/FlooringMaterialStrategy'
import { CoreError } from '@/services/errors'

describe('flooringMaterialStrategy', () => {
  let strategy: FlooringMaterialStrategy
  let mockElement: any

  beforeEach(() => {
    strategy = new FlooringMaterialStrategy()

    // 创建模拟元素
    mockElement = {
      id: 'test-element',
      type: 'shape',
      getSubType: () => 'rectangle',
      getArea: vi.fn().mockReturnValue(10), // 10平方米
    }
  })

  it('should be defined', () => {
    expect(strategy).toBeDefined()
  })

  it('should return the correct shape type', () => {
    expect(strategy.getElementType()).toBe('rectangle')
  })

  it('should calculate hardwood flooring amount correctly', () => {
    const result = strategy.calculateMaterialAmount(mockElement, 'hardwood', {
      unitSize: { width: 0.2, height: 1.2 },
      wastageRate: 0.1,
    })

    expect(result).toBeDefined()
    expect(result.amount).toBe(10)
    expect(result.unit).toBe('m²')
    expect(result.amountWithWastage).toBeGreaterThan(10)
    expect(result.unitType).toBe('plank')
  })

  it('should calculate carpet flooring amount correctly', () => {
    const result = strategy.calculateMaterialAmount(mockElement, 'carpet', {
      wastageRate: 0.05,
    })

    expect(result).toBeDefined()
    expect(result.amount).toBe(10)
    expect(result.unit).toBe('m²')
    expect(result.amountWithWastage).toBeGreaterThan(10)
    expect(result.unitType).toBe('roll')
  })

  it('should calculate tile flooring amount correctly', () => {
    const result = strategy.calculateMaterialAmount(mockElement, 'tile', {
      unitSize: { width: 0.3, height: 0.3 },
      wastageRate: 0.1,
      includeJoints: true,
      jointWidth: 0.002,
    })

    expect(result).toBeDefined()
    expect(result.amount).toBe(10)
    expect(result.unit).toBe('m²')
    expect(result.amountWithWastage).toBeGreaterThan(10)
    expect(result.unitType).toBe('tile')
  })

  it('should throw error for invalid element type', () => {
    const invalidElement = { ...mockElement, type: 'invalid' }

    expect(() => {
      strategy.calculateMaterialAmount(invalidElement, 'hardwood')
    }).toThrow(CoreError)
  })

  it('should throw error for unsupported material type', () => {
    expect(() => {
      strategy.calculateMaterialAmount(mockElement, 'unsupported')
    }).toThrow(CoreError)
  })
})
