import type {
  BaseEvent,
  BaseEventWithTimestamp,
  EventBus,
  EventBusConfig,
  EventHandler,
  EventHandlerWithOptions,
  EventSubscriptionOptions,
  ExtendedEventBus,
  TypedEvent,
} from '@/types/services/events/eventCore'
import { describe, expect, it } from 'vitest'
import { AppEventType } from '@/types/services/events/eventTypes'

describe('eventCore Types', () => {
  describe('baseEvent', () => {
    it('should define the required properties', () => {
      const event: BaseEvent = {
        type: AppEventType.APP_INITIALIZED,
        payload: { success: true },
      }

      expect(event).toHaveProperty('type')
      expect(event).toHaveProperty('payload')
      expect(event.type).toBe(AppEventType.APP_INITIALIZED)
      expect(event.payload).toEqual({ success: true })
    })

    it('should allow optional timestamp', () => {
      const now = Date.now()
      const event: BaseEvent = {
        type: AppEventType.APP_INITIALIZED,
        timestamp: now,
        payload: { success: true },
      }

      expect(event).toHaveProperty('timestamp')
      expect(event.timestamp).toBe(now)
    })
  })

  describe('baseEventWithTimestamp', () => {
    it('should require timestamp property', () => {
      const now = Date.now()
      const event: BaseEventWithTimestamp = {
        type: AppEventType.APP_INITIALIZED,
        timestamp: now,
        payload: { success: true },
      }

      expect(event).toHaveProperty('timestamp')
      expect(event.timestamp).toBe(now)
    })
  })

  describe('typedEvent', () => {
    it('should provide type safety for payload', () => {
      interface TestPayload {
        id: string
        value: number
      }

      const event: TypedEvent<TestPayload> = {
        type: AppEventType.APP_INITIALIZED,
        payload: {
          id: 'test-123',
          value: 42,
        },
      }

      expect(event.payload).toHaveProperty('id')
      expect(event.payload).toHaveProperty('value')
      expect(event.payload.id).toBe('test-123')
      expect(event.payload.value).toBe(42)
    })
  })

  describe('eventHandler', () => {
    it('should define a function that accepts an event', () => {
      const handler: EventHandler = (event: BaseEvent) => {
        // Handler implementation
      }

      expect(typeof handler).toBe('function')
    })

    it('should support generic event types', () => {
      interface TestEvent extends BaseEvent {
        payload: {
          id: string
        }
      }

      const handler: EventHandler<TestEvent> = (event: TestEvent) => {
        // Handler implementation
      }

      expect(typeof handler).toBe('function')
    })
  })

  describe('eventSubscriptionOptions', () => {
    it('should define all optional properties', () => {
      const options: EventSubscriptionOptions = {
        once: true,
        priority: 10,
        context: { userId: 'user-123' },
        description: 'Test handler',
        filter: event => event.type === AppEventType.APP_INITIALIZED,
        async: true,
        debounce: 300,
        throttle: 500,
      }

      expect(options).toHaveProperty('once')
      expect(options).toHaveProperty('priority')
      expect(options).toHaveProperty('context')
      expect(options).toHaveProperty('description')
      expect(options).toHaveProperty('filter')
      expect(options).toHaveProperty('async')
      expect(options).toHaveProperty('debounce')
      expect(options).toHaveProperty('throttle')

      expect(options.once).toBe(true)
      expect(options.priority).toBe(10)
      expect(options.context).toEqual({ userId: 'user-123' })
      expect(options.description).toBe('Test handler')
      expect(typeof options.filter).toBe('function')
      expect(options.async).toBe(true)
      expect(options.debounce).toBe(300)
      expect(options.throttle).toBe(500)
    })
  })

  describe('eventHandlerWithOptions', () => {
    it('should combine handler and options', () => {
      const handlerWithOptions: EventHandlerWithOptions = {
        handler: (event: BaseEvent) => {
          // Handler implementation
        },
        options: {
          once: true,
          priority: 5,
        },
      }

      expect(handlerWithOptions).toHaveProperty('handler')
      expect(handlerWithOptions).toHaveProperty('options')
      expect(typeof handlerWithOptions.handler).toBe('function')
      expect(handlerWithOptions.options.once).toBe(true)
      expect(handlerWithOptions.options.priority).toBe(5)
    })
  })

  describe('eventBusConfig', () => {
    it('should define all configuration options', () => {
      const config: EventBusConfig = {
        enableLogging: true,
        defaultPriority: 0,
        maxAsyncHandlers: 10,
        defaultDebounceTime: 300,
        defaultThrottleTime: 500,
        enablePerformanceTracking: true,
        maxHandlersPerEventType: 50,
      }

      expect(config).toHaveProperty('enableLogging')
      expect(config).toHaveProperty('defaultPriority')
      expect(config).toHaveProperty('maxAsyncHandlers')
      expect(config).toHaveProperty('defaultDebounceTime')
      expect(config).toHaveProperty('defaultThrottleTime')
      expect(config).toHaveProperty('enablePerformanceTracking')
      expect(config).toHaveProperty('maxHandlersPerEventType')

      expect(config.enableLogging).toBe(true)
      expect(config.defaultPriority).toBe(0)
      expect(config.maxAsyncHandlers).toBe(10)
      expect(config.defaultDebounceTime).toBe(300)
      expect(config.defaultThrottleTime).toBe(500)
      expect(config.enablePerformanceTracking).toBe(true)
      expect(config.maxHandlersPerEventType).toBe(50)
    })
  })

  describe('eventBus', () => {
    it('should define all required methods', () => {
      // Create a mock implementation of EventBus
      const eventBus: EventBus = {
        subscribe: vi.fn(),
        publish: vi.fn(),
        unsubscribe: vi.fn(),
        unsubscribeAll: vi.fn(),
        clear: vi.fn(),
        reset: vi.fn(),
        getSubscriptions: vi.fn(),
        configure: vi.fn(),
      }

      expect(eventBus).toHaveProperty('subscribe')
      expect(eventBus).toHaveProperty('publish')
      expect(eventBus).toHaveProperty('unsubscribe')
      expect(eventBus).toHaveProperty('unsubscribeAll')
      expect(eventBus).toHaveProperty('clear')
      expect(eventBus).toHaveProperty('reset')
      expect(eventBus).toHaveProperty('getSubscriptions')
      expect(eventBus).toHaveProperty('configure')

      expect(typeof eventBus.subscribe).toBe('function')
      expect(typeof eventBus.publish).toBe('function')
      expect(typeof eventBus.unsubscribe).toBe('function')
      expect(typeof eventBus.unsubscribeAll).toBe('function')
      expect(typeof eventBus.clear).toBe('function')
      expect(typeof eventBus.reset).toBe('function')
      expect(typeof eventBus.getSubscriptions).toBe('function')
      expect(typeof eventBus.configure).toBe('function')
    })
  })

  describe('extendedEventBus', () => {
    it('should define all required methods including extended ones', () => {
      // Create a mock implementation of ExtendedEventBus
      const extendedEventBus: ExtendedEventBus = {
        subscribe: vi.fn(),
        publish: vi.fn(),
        unsubscribe: vi.fn(),
        unsubscribeAll: vi.fn(),
        clear: vi.fn(),
        reset: vi.fn(),
        getSubscriptions: vi.fn(),
        configure: vi.fn(),
        on: vi.fn(),
        off: vi.fn(),
        emit: vi.fn(),
        publishAsync: vi.fn(),
        emitAsync: vi.fn(),
        once: vi.fn(),
      }

      // Check base EventBus methods
      expect(extendedEventBus).toHaveProperty('subscribe')
      expect(extendedEventBus).toHaveProperty('publish')
      expect(extendedEventBus).toHaveProperty('unsubscribe')
      expect(extendedEventBus).toHaveProperty('unsubscribeAll')
      expect(extendedEventBus).toHaveProperty('clear')
      expect(extendedEventBus).toHaveProperty('reset')
      expect(extendedEventBus).toHaveProperty('getSubscriptions')
      expect(extendedEventBus).toHaveProperty('configure')

      // Check extended methods
      expect(extendedEventBus).toHaveProperty('on')
      expect(extendedEventBus).toHaveProperty('off')
      expect(extendedEventBus).toHaveProperty('emit')
      expect(extendedEventBus).toHaveProperty('publishAsync')
      expect(extendedEventBus).toHaveProperty('emitAsync')
      expect(extendedEventBus).toHaveProperty('once')

      expect(typeof extendedEventBus.on).toBe('function')
      expect(typeof extendedEventBus.off).toBe('function')
      expect(typeof extendedEventBus.emit).toBe('function')
      expect(typeof extendedEventBus.publishAsync).toBe('function')
      expect(typeof extendedEventBus.emitAsync).toBe('function')
      expect(typeof extendedEventBus.once).toBe('function')
    })
  })
})
