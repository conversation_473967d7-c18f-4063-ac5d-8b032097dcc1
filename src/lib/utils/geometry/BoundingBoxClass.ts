/**
 * Class for Representing and Manipulating Axis-Aligned Bounding Boxes.
 *
 * @remarks
 * This module provides the {@link BoundingBoxClass}, a concrete implementation for
 * creating and working with axis-aligned bounding boxes (AABBs). A bounding box is
 * defined by its top-left corner's position (`x`, `y`) and its `width` and `height`.
 *
 * This class implements the {@link BoundingBoxProperties} interface (which itself
 * extends {@link ShapeElement}), meaning instances of `BoundingBoxClass` also carry
 * common element properties like `id`, `type` (defaulted to `RECTANGLE`), `position`, etc.
 * This allows bounding boxes to be treated similarly to other shape elements if needed,
 * though they are primarily used for geometric calculations and spatial queries.
 *
 * Key features include:
 * - Constructors for creating bounding boxes from coordinates or other inputs.
 * - Static factory methods: `fromObject`, `fromPointsArray`, `fromTwoPoints`, `fromCenter`.
 * - Instance methods: `containsPoint`, `intersects` (with another BoundingBox), `union`.
 * - A `points` getter that returns the 9 characteristic points of the bounding box (corners, midpoints, center).
 * - A `toJson` method for serialization.
 *
 * @module lib/utils/geometry/BoundingBoxClass
 * @see {@link BoundingBoxProperties} - The interface this class implements.
 * @see {@link PointClass} - Used for representing points.
 * @see {@link PointData} - Interface for point data.
 */

import type { BoundingBox as BoundingBoxProperties } from '@/types/core/element/geometry/bounding-box'
import type { PointData } from '@/types/core/element/geometry/point'
import type { ShapeElement } from '@/types/core/elementDefinitions'
import { ElementType } from '../../../types/core/elementDefinitions'
import { MajorCategory } from '../../../types/core/majorMinorTypes'
import { PointClass } from './PointClass'

/**
 * Represents an axis-aligned bounding box and provides methods for its manipulation.
 *
 * @remarks
 * The `position` property of this class (inherited from `ShapeElement` via `BoundingBoxProperties`)
 * represents the top-left corner of the bounding box.
 * The `points` getter calculates 9 characteristic points: top-left, top-center, top-right,
 * middle-left, center, middle-right, bottom-left, bottom-center, and bottom-right.
 *
 * @implements BoundingBoxProperties
 */
export class BoundingBoxClass implements BoundingBoxProperties {
  // Properties from BoundingBoxProperties
  public width: number
  public height: number

  // Properties from ShapeElement (extended by BoundingBoxProperties)
  public id: string
  public type: ElementType = ElementType.RECTANGLE // Defaulting to RECTANGLE as BBox is rectangular
  public visible: boolean = true
  public locked: boolean = false
  public metadata?: ShapeElement['metadata']
  public position: PointClass // Top-left corner, now PointClass
  public rotation: number = 0 // Bounding boxes are typically axis-aligned, so rotation is 0
  public selectable: boolean = false // Usually not selectable themselves
  public draggable: boolean = false // Usually not draggable themselves
  public showHandles: boolean = false // Usually don't show handles themselves
  public zIndex?: number
  public layer?: string
  public properties?: Record<string, any>
  public fill?: string = 'none' // Default to no fill
  public stroke?: string = '#00FFFF' // Default to cyan for visibility if rendered
  public strokeWidth?: number = 1
  public opacity?: number = 0.5
  public strokeDasharray?: string
  public majorCategory: MajorCategory = MajorCategory.FURNITURE // Default category

  // BoundingBoxProperties specific readonly points
  // This will be a getter to calculate points on demand or based on constructor.
  // The interface defines it as a tuple of 9 points.
  public get points(): [PointClass, PointClass, PointClass, PointClass, PointClass, PointClass, PointClass, PointClass, PointClass] {
    return this._calculateControlPoints()
  }

  /**
   * Creates a new `BoundingBoxClass` instance.
   *
   * @remarks
   * If `width` or `height` are negative, they are made positive, and the `x` or `y`
   * coordinate of the `position` (top-left corner) is adjusted accordingly to maintain
   * the intended spatial coverage.
   *
   * @param x - The x-coordinate of the top-left corner.
   * @param y - The y-coordinate of the top-left corner.
   * @param width - The width of the bounding box.
   * @param height - The height of the bounding box.
   * @param id - Optional: A unique identifier for the bounding box. Defaults to a generated ID.
   */
  constructor(x: number, y: number, width: number, height: number, id?: string) {
    let adjustedX = x
    let adjustedY = y
    let adjustedWidth = width
    let adjustedHeight = height

    if (adjustedWidth < 0) {
      adjustedX = adjustedX + adjustedWidth // Adjust x if width is negative
      adjustedWidth = -adjustedWidth
    }
    if (adjustedHeight < 0) {
      adjustedY = adjustedY + adjustedHeight // Adjust y if height is negative
      adjustedHeight = -adjustedHeight
    }

    this.position = new PointClass(adjustedX, adjustedY)
    this.width = adjustedWidth
    this.height = adjustedHeight
    this.id = id || `bbox_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`
    // Note: `this.points` is a getter.
  }

  private _calculateControlPoints(): [PointClass, PointClass, PointClass, PointClass, PointClass, PointClass, PointClass, PointClass, PointClass] {
    const x = this.position.x
    const y = this.position.y
    const w = this.width
    const h = this.height
    const midX = x + w / 2
    const midY = y + h / 2

    return [
      new PointClass(x, y), // topLeft
      new PointClass(midX, y), // topCenter
      new PointClass(x + w, y), // topRight
      new PointClass(x, midY), // middleLeft
      new PointClass(midX, midY), // center
      new PointClass(x + w, midY), // middleRight
      new PointClass(x, y + h), // bottomLeft
      new PointClass(midX, y + h), // bottomCenter
      new PointClass(x + w, y + h), // bottomRight
    ]
  }

  /**
   * Creates a `BoundingBoxClass` instance from a simple object containing `x`, `y`, `width`, and `height`.
   *
   * @param obj - An object with `x`, `y` (top-left corner), `width`, and `height` properties.
   * @returns A new {@link BoundingBoxClass} instance.
   */
  static fromObject(obj: { x: number, y: number, width: number, height: number }): BoundingBoxClass {
    return new BoundingBoxClass(obj.x, obj.y, obj.width, obj.height)
  }

  /**
   * Creates a `BoundingBoxClass` instance that minimally encompasses an array of {@link PointData} objects.
   *
   * @param points - An array of {@link PointData} objects.
   * @returns A new {@link BoundingBoxClass} instance. If the `points` array is empty or null,
   *          a bounding box with zero width and height at position (0,0) is returned.
   */
  static fromPointsArray(points: PointData[]): BoundingBoxClass {
    if (!points || points.length === 0) {
      return new BoundingBoxClass(0, 0, 0, 0)
    }

    let minX = points[0].x
    let minY = points[0].y
    let maxX = points[0].x
    let maxY = points[0].y

    for (let i = 1; i < points.length; i++) {
      const p = points[i]
      minX = Math.min(minX, p.x)
      minY = Math.min(minY, p.y)
      maxX = Math.max(maxX, p.x)
      maxY = Math.max(maxY, p.y)
    }
    return new BoundingBoxClass(minX, minY, maxX - minX, maxY - minY)
  }

  /**
   * Creates a `BoundingBoxClass` instance from two diagonally opposite points.
   * The order of the points does not matter.
   *
   * @param p1 - The first {@link PointData} object.
   * @param p2 - The second {@link PointData} object.
   * @returns A new {@link BoundingBoxClass} instance.
   */
  static fromTwoPoints(p1: PointData, p2: PointData): BoundingBoxClass {
    const minX = Math.min(p1.x, p2.x)
    const minY = Math.min(p1.y, p2.y)
    const maxX = Math.max(p1.x, p2.x)
    const maxY = Math.max(p1.y, p2.y)
    return new BoundingBoxClass(minX, minY, maxX - minX, maxY - minY)
  }

  /**
   * Alias for `fromTwoPoints`, potentially for compatibility or clearer semantics in certain contexts.
   * @deprecated Prefer {@link BoundingBoxClass.fromTwoPoints} or {@link BoundingBoxClass.fromPointsArray} for clarity.
   */
  static fromPoints = BoundingBoxClass.fromTwoPoints

  /**
   * Creates a `BoundingBoxClass` instance from a center point, width, and height.
   *
   * @param center - The center {@link PointData} of the desired bounding box.
   * @param width - The width of the bounding box.
   * @param height - The height of the bounding box.
   * @returns A new {@link BoundingBoxClass} instance.
   */
  static fromCenter(center: PointData, width: number, height: number): BoundingBoxClass {
    const x = center.x - width / 2
    const y = center.y - height / 2
    return new BoundingBoxClass(x, y, width, height)
  }

  /**
   * Checks if this bounding box contains a given point (inclusive of boundaries).
   *
   * @param point - The {@link PointData} to check.
   * @returns `true` if the point is inside or on the boundary of this bounding box, `false` otherwise.
   */
  containsPoint(point: PointData): boolean {
    return (
      point.x >= this.position.x
      && point.x <= this.position.x + this.width
      && point.y >= this.position.y
      && point.y <= this.position.y + this.height
    )
  }

  /**
   * Checks if this bounding box intersects with another `BoundingBoxClass` instance.
   *
   * @param other - The other {@link BoundingBoxClass} instance to check for intersection.
   * @returns `true` if the two bounding boxes intersect (i.e., overlap), `false` otherwise.
   */
  intersects(other: BoundingBoxClass): boolean {
    return (
      this.position.x < other.position.x + other.width
      && this.position.x + this.width > other.position.x
      && this.position.y < other.position.y + other.height
      && this.position.y + this.height > other.position.y
    )
  }

  /**
   * Creates a new `BoundingBoxClass` that is the union of this bounding box and another one.
   * The resulting bounding box will encompass both original bounding boxes.
   *
   * @param other - The other {@link BoundingBoxClass} instance to union with.
   * @returns A new {@link BoundingBoxClass} instance representing the union.
   */
  union(other: BoundingBoxClass): BoundingBoxClass {
    const x1 = Math.min(this.position.x, other.position.x)
    const y1 = Math.min(this.position.y, other.position.y)
    const x2 = Math.max(this.position.x + this.width, other.position.x + other.width)
    const y2 = Math.max(this.position.y + this.height, other.position.y + other.height)
    return new BoundingBoxClass(x1, y1, x2 - x1, y2 - y1)
  }

  /**
   * Returns a plain JavaScript object representation of the `BoundingBoxClass` instance,
   * conforming to a partial {@link BoundingBoxProperties} structure.
   *
   * @remarks
   * This is useful for serialization or when a plain object is needed instead of a class instance.
   * The `points` getter is typically not included in this minimal JSON representation.
   *
   * @returns A plain object representing the bounding box's properties.
   */
  toJson(): Partial<BoundingBoxProperties> {
    const json: Partial<BoundingBoxProperties> = {
      id: this.id,
      type: this.type,
      position: this.position.toJson(), // Serialize PointClass to PointData compatible object
      width: this.width,
      height: this.height,
      visible: this.visible,
      locked: this.locked,
      rotation: this.rotation,
      selectable: this.selectable,
      draggable: this.draggable,
      showHandles: this.showHandles,
      majorCategory: this.majorCategory, // Add majorCategory
      // Optional properties from ShapeElement
      ...(this.metadata && { metadata: this.metadata }),
      ...(this.zIndex !== undefined && { zIndex: this.zIndex }),
      ...(this.layer && { layer: this.layer }),
      ...(this.properties && { properties: this.properties }),
      ...(this.fill && { fill: this.fill }),
      ...(this.stroke && { stroke: this.stroke }),
      ...(this.strokeWidth !== undefined && { strokeWidth: this.strokeWidth }),
      ...(this.opacity !== undefined && { opacity: this.opacity }),
      ...(this.strokeDasharray && { strokeDasharray: this.strokeDasharray }),
      // 'points' is a getter and readonly in BoundingBoxProperties, typically not part of minimal JSON.
      // If needed: points: this.points.map(p => p.toJson()) as [PointData, PointData, PointData, PointData, PointData, PointData, PointData, PointData, PointData],
    }
    return json
  }
}
