/**
 * Distance Calculation Strategies Index
 *
 * @remarks
 * This module serves as a barrel export for all concrete strategy implementations
 * related to distance calculation between elements or points. It re-exports the
 * `DistanceCalculatorStrategy` interface (and other relevant compute types) from
 * `@/types/core/compute/distanceComputeTypes` and `@/types/core/compute`,
 * and then exports all specific distance calculation strategy classes defined
 * within this directory (e.g., {@link ArcDistanceStrategy}, {@link RectangleDistanceStrategy}).
 *
 * Note: `LineDistanceStrategy` and `PolylineDistanceStrategy` are commented out
 * as they might not exist or are handled by other generic strategies.
 *
 * @module core/compute/strategies/distance/index
 */

// Export concrete strategy implementations from the current directory
export * from './ArcDistanceStrategy'
export * from './CubicDistanceStrategy'

export * from './EllipseDistanceStrategy'
export * from './PolygonDistanceStrategy'
export * from './QuadraticDistanceStrategy'
export * from './RectangleDistanceStrategy'
// Also export the main BoundingBox type if it's used by any distance strategies, or general compute types
export * from '@/types/core/compute'
// Export interface first for better code organization
export * from '@/types/core/compute/distanceComputeTypes' // This exports DistanceCalculatorStrategy etc.
// Add LineDistanceStrategy and PolylineDistanceStrategy if they exist in this directory
// export * from './LineDistanceStrategy';
// export * from './PolylineDistanceStrategy';
