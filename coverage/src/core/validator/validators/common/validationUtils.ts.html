
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/core/validator/validators/common/validationUtils.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> / <a href="index.html">src/core/validator/validators/common</a> validationUtils.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/98</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/98</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * @file validationUtils.ts
 * @description Provides common utility functions used across different validators within the core module.
 * These functions help check the validity of common data types and structures such as points, colors, and numeric ranges.
 * They are essential for ensuring data integrity before processing or storing element information.
 *
 * @module core/validator/validators/common/validationUtils
 */
&nbsp;
import type Point from '@/types/core/element/geometry/point'
import type { ValidationError, ValidationResult } from '@/types/core/validator/validator-interface'
<span class="cstat-no" title="statement not covered" >import { ValidationErrorCode } from '@/types/core/validator/error-codes'</span>
&nbsp;
/**
 * Color type alias for string to improve readability in function signatures.
 * @private
 */
type Color = string
&nbsp;
/**
 * Checks if a given object represents a valid point with finite number coordinates.
 *
 * @remarks
 * Accepts either a Point instance or a plain object with `x` and `y` number properties.
 *
 * @param point - The point object or Point instance to check.
 * @returns `true` if the input is a valid point object or Point instance with finite `x` and `y` coordinates, `false` otherwise.
 */
<span class="cstat-no" title="statement not covered" >export function isValidPoint(point: { x: number, y: number } | Point | null | undefined): point is { x: number, y: number } | Point {</span>
  // Explicitly check for null or undefined first
<span class="cstat-no" title="statement not covered" >  if (point === null || point === undefined) {</span>
<span class="cstat-no" title="statement not covered" >    return false</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Check for finite number properties x and y
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    typeof point.x === 'number'</span>
<span class="cstat-no" title="statement not covered" >    &amp;&amp; typeof point.y === 'number'</span>
<span class="cstat-no" title="statement not covered" >    &amp;&amp; Number.isFinite(point.x) // Use Number.isFinite to exclude NaN and Infinity</span>
<span class="cstat-no" title="statement not covered" >    &amp;&amp; Number.isFinite(point.y)</span>
  )
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Checks if a given string represents a valid CSS color value.
 *
 * @remarks
 * Supports common formats:
 * - Hexadecimal: `#RGB`, `#RRGGBB` (Red, Green, Blue color values in hexadecimal)
 * - RGB: `rgb(r, g, b)` (values 0-255)
 * - RGBA: `rgba(r, g, b, a)` (rgb 0-255, alpha 0.0-1.0)
 * - Common CSS color names (lowercase check).
 *
 * @param color - The color string to validate.
 * @returns `true` if the string is a valid color representation in one of the supported formats, `false` otherwise.
 */
<span class="cstat-no" title="statement not covered" >export function isValidColor(color: Color | null | undefined): color is Color {</span>
  // Check if color exists and is a non-empty string
<span class="cstat-no" title="statement not covered" >  if (color == null || typeof color !== 'string' || color.trim() === '') {</span>
<span class="cstat-no" title="statement not covered" >    return false</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const trimmedColor = color.trim()</span>
&nbsp;
  // Support multiple color formats
  // #RGB or #RRGGBB hexadecimal format (cspell:disable-line)
<span class="cstat-no" title="statement not covered" >  if (/^#(?:[0-9A-F]{3}){1,2}$/i.test(trimmedColor)) { // Added 'i' flag for case-insensitivity</span>
<span class="cstat-no" title="statement not covered" >    return true</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // rgb(r,g,b) or rgba(r,g,b,a) format
  // More robust regex for rgb/rgba, allowing spaces and validating values
  // cspell:disable-next-line
<span class="cstat-no" title="statement not covered" >  const rgbRegex = /^rgb\(\s*0*(?:\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\s*,\s*0*(?:\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\s*,\s*0*(?:\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\s*\)$/i</span>
  // cspell:disable-next-line
<span class="cstat-no" title="statement not covered" >  const rgbaRegex = /^rgba\(\s*0*(?:\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\s*,\s*0*(?:\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\s*,\s*0*(?:\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\s*,\s*(?:1|0\.\d+|1\.0*)\s*\)$/i</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (rgbRegex.test(trimmedColor)) {</span>
<span class="cstat-no" title="statement not covered" >    return true // Regex now ensures values are in range</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (rgbaRegex.test(trimmedColor)) {</span>
<span class="cstat-no" title="statement not covered" >    return true // Regex now ensures values are in range</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Common CSS color names (lowercase check)
  // Based on CSS Color Module Level 3/4 standard names
<span class="cstat-no" title="statement not covered" >  const basicColors = new Set([</span>
<span class="cstat-no" title="statement not covered" >    'black',</span>
<span class="cstat-no" title="statement not covered" >    'silver',</span>
<span class="cstat-no" title="statement not covered" >    'gray',</span>
<span class="cstat-no" title="statement not covered" >    'white',</span>
<span class="cstat-no" title="statement not covered" >    'maroon',</span>
<span class="cstat-no" title="statement not covered" >    'red',</span>
<span class="cstat-no" title="statement not covered" >    'purple',</span>
<span class="cstat-no" title="statement not covered" >    'fuchsia',</span>
<span class="cstat-no" title="statement not covered" >    'green',</span>
<span class="cstat-no" title="statement not covered" >    'lime',</span>
<span class="cstat-no" title="statement not covered" >    'olive',</span>
<span class="cstat-no" title="statement not covered" >    'yellow',</span>
<span class="cstat-no" title="statement not covered" >    'navy',</span>
<span class="cstat-no" title="statement not covered" >    'blue',</span>
<span class="cstat-no" title="statement not covered" >    'teal',</span>
<span class="cstat-no" title="statement not covered" >    'aqua',</span>
<span class="cstat-no" title="statement not covered" >    'orange', // Added orange as common</span>
<span class="cstat-no" title="statement not covered" >    'transparent', // Include transparent</span>
    // Add more comprehensive list if needed, but this covers common cases
<span class="cstat-no" title="statement not covered" >  ])</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (basicColors.has(trimmedColor.toLowerCase())) {</span>
<span class="cstat-no" title="statement not covered" >    return true</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Could add hsl/hsla checks if needed
&nbsp;
<span class="cstat-no" title="statement not covered" >  return false</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Checks if a polygon defined by an array of points is considered closed.
 *
 * @remarks
 * A polygon is closed if its first and last points are sufficiently close (coincident).
 * Uses Euclidean distance and a small tolerance (`epsilon`) for comparison.
 * It also checks if the input is a valid array with at least 3 valid points.
 *
 * @param points - An array of point objects or Point instances representing the polygon vertices.
 * @param epsilon - Optional tolerance for considering points coincident. Defaults to 0.001.
 * @returns `true` if the array has at least 3 valid points and the distance between the first and last point is less than or equal to `epsilon`, `false` otherwise.
 */
<span class="cstat-no" title="statement not covered" >export function isPolygonClosed(points: ({ x: number, y: number } | Point)[], epsilon: number = 0.001): boolean {</span>
<span class="cstat-no" title="statement not covered" >  if (points == null || !Array.isArray(points) || points.length &lt; 3) {</span>
<span class="cstat-no" title="statement not covered" >    return false // Need at least 3 points for a polygon</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Check validity of first and last points specifically
<span class="cstat-no" title="statement not covered" >  const firstPoint = points[0]</span>
<span class="cstat-no" title="statement not covered" >  const lastPoint = points[points.length - 1]</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (!isValidPoint(firstPoint) || !isValidPoint(lastPoint)) {</span>
<span class="cstat-no" title="statement not covered" >    console.warn('[isPolygonClosed] First or last point is invalid.')</span>
<span class="cstat-no" title="statement not covered" >    return false // Cannot check closure if endpoints are invalid</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Calculate distance using Math.hypot for robustness (avoids intermediate squaring/sqrt issues)
<span class="cstat-no" title="statement not covered" >  const distance = Math.hypot(lastPoint.x - firstPoint.x, lastPoint.y - firstPoint.y)</span>
<span class="cstat-no" title="statement not covered" >  return distance &lt;= epsilon</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Checks if a given numerical value falls within a specified range (inclusive).
 *
 * @param value - The number to check.
 * @param min - The minimum allowed value (inclusive).
 * @param max - The maximum allowed value (inclusive).
 * @returns `true` if `value` is a finite number such that `min &lt;= value &lt;= max`, `false` otherwise.
 */
<span class="cstat-no" title="statement not covered" >export function isValueInRange(value: number | null | undefined, min: number, max: number): value is number {</span>
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    typeof value === 'number'</span>
<span class="cstat-no" title="statement not covered" >    &amp;&amp; Number.isFinite(value) // Ensure it's not NaN or Infinity</span>
<span class="cstat-no" title="statement not covered" >    &amp;&amp; value &gt;= min</span>
<span class="cstat-no" title="statement not covered" >    &amp;&amp; value &lt;= max</span>
  )
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
// --- Moved from CoreError.ts --- //
&nbsp;
/**
 * Creates a standard ValidationResult object.
 *
 * @remarks
 * Simplifies the process of constructing validation results, especially when dealing
 * with a mix of error messages (strings) and structured ValidationError objects.
 *
 * If error messages (strings) are provided in the `errors` array, they are automatically
 * converted into ValidationError objects with a default code (`GENERIC_VALIDATION`).
 * The top-level `message` for the result is determined automatically from the first error
 * if not explicitly provided.
 *
 * @param isValid - Boolean flag indicating if the overall validation passed.
 * @param errors - Optional array containing error details.
 *        Can be a mix of string messages and ValidationError objects.
 * @param message - Optional explicit top-level summary message for the ValidationResult.
 *        If omitted and errors exist, the message from the first error in the errors array will be used.
 * @returns A populated ValidationResult object.
 */
<span class="cstat-no" title="statement not covered" >export function createValidationResult(</span>
<span class="cstat-no" title="statement not covered" >  isValid: boolean,</span>
<span class="cstat-no" title="statement not covered" >  errors?: (ValidationError | string)[],</span>
<span class="cstat-no" title="statement not covered" >  message?: string,</span>
<span class="cstat-no" title="statement not covered" >): ValidationResult {</span>
<span class="cstat-no" title="statement not covered" >  const validationErrors: ValidationError[] = []</span>
&nbsp;
  // Process the input errors array
<span class="cstat-no" title="statement not covered" >  if (errors &amp;&amp; Array.isArray(errors)) {</span>
<span class="cstat-no" title="statement not covered" >    errors.forEach((err) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (typeof err === 'string' &amp;&amp; err.trim() !== '') {</span>
        // Convert non-empty strings to ValidationError objects
<span class="cstat-no" title="statement not covered" >        validationErrors.push({ code: ValidationErrorCode.VALIDATION_SPECIFIC_ERROR, message: err })</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else if (typeof err === 'object' &amp;&amp; err?.code != null &amp;&amp; err?.message != null) {</span>
        // Add valid ValidationError objects directly
<span class="cstat-no" title="statement not covered" >        validationErrors.push(err) // Cast is safe due to checks</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      else {</span>
<span class="cstat-no" title="statement not covered" >        console.warn('[createValidationResult] Skipping invalid item in errors array:', err)</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    })</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Determine the top-level message: use provided message, or first error message, or undefined
<span class="cstat-no" title="statement not covered" >  const topLevelMessage = message ?? (validationErrors.length &gt; 0 ? validationErrors[0].message : undefined)</span>
&nbsp;
  // Construct the final ValidationResult object
<span class="cstat-no" title="statement not covered" >  return {</span>
<span class="cstat-no" title="statement not covered" >    valid: isValid &amp;&amp; validationErrors.length === 0, // Ensure isValid reflects error presence</span>
<span class="cstat-no" title="statement not covered" >    message: topLevelMessage,</span>
    // Include errors array only if it contains errors
<span class="cstat-no" title="statement not covered" >    errors: validationErrors.length &gt; 0 ? validationErrors : undefined,</span>
    // `warnings` property is not handled by this helper, defaults to undefined
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
// --- Moved from CoreError.ts --- //
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-11T09:58:01.486Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    