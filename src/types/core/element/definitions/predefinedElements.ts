/**
 * Predefined Home Furnishing Elements and Supporting Types
 *
 * @remarks
 * This file contains a list of common home furnishing elements, categorized for use
 * in UI components like an element drawer. It also includes supporting type definitions
 * like FixtureMountingType, which were previously in separate files.
 */

import type { MinorCategory } from '../../majorMinorTypes'
import { MajorCategory } from '../../majorMinorTypes'

/**
 * Defines the common mounting types for various fixtures/elements.
 */
export enum FixtureMountingType {
  WALL_MOUNTED = 'wall-mounted',
  FLOOR_MOUNTED = 'floor-mounted',
  CEILING_MOUNTED = 'ceiling-mounted',
  COUNTERTOP = 'countertop',
  RECESSED = 'recessed',
  FREESTANDING = 'freestanding',
  BUILT_IN = 'built-in',
  NONE = 'none', // Added for elements where mounting type is not applicable
}

export interface PredefinedElement {
  id: string
  name: string
  description: string
  majorCategory: MajorCategory // Using the enum now
  minorCategory: MinorCategory // Using the type alias now
  imagePath: string
  defaultWidth?: number // in mm
  defaultDepth?: number // in mm
  defaultHeight?: number // in mm
  tags?: string[]
  mountingType?: FixtureMountingType // Added from fixtureDesignTypes
  // Add other relevant properties from FurnitureProperties/FixtureProperties as needed
  // For example, material, color, specific attributes like 'isUpholstered', 'hasDrawers' etc.
  // For now, keeping it concise. These can be added to a generic 'attributes: Record<string, any>' or as specific optional fields.
  attributes?: Record<string, unknown> // For additional specific properties
}

// The predefinedElements array will be populated here later with updated structure and new items.
// For now, an empty array or the old content structure might be placed by the apply model.
// The important part is establishing the new interface and types.
export const predefinedElements: PredefinedElement[] = [
  // == Base Elements (Formerly Floor Elements) ==
  // Minor Category: Coverings
  {
    id: 'base_covering_ceramic_tile',
    name: 'Ceramic Tile',
    description: 'Durable and versatile ceramic tiles, suitable for various rooms.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'coverings',
    imagePath: 'assets/images/floor/coverings/ceramic_tile.png',
    tags: ['tile', 'ceramic', 'bathroom', 'kitchen', 'base', 'floor'],
    mountingType: FixtureMountingType.NONE,
  },
  {
    id: 'base_covering_porcelain_tile',
    name: 'Porcelain Tile',
    description: 'Highly durable and water-resistant porcelain tiles, ideal for high-traffic areas.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'coverings',
    imagePath: 'assets/images/floor/coverings/porcelain_tile.png',
    tags: ['tile', 'porcelain', 'durable', 'base', 'floor'],
    mountingType: FixtureMountingType.NONE,
  },
  {
    id: 'base_covering_vinyl_tile',
    name: 'Vinyl Tile',
    description: 'Cost-effective and resilient vinyl tiles, available in many designs.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'coverings',
    imagePath: 'assets/images/floor/coverings/vinyl_tile.png',
    tags: ['tile', 'vinyl', 'resilient', 'base', 'floor'],
    mountingType: FixtureMountingType.NONE,
  },
  {
    id: 'base_covering_hardwood_oak',
    name: 'Oak Hardwood Flooring',
    description: 'Classic and durable oak hardwood flooring for a timeless look.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'coverings',
    imagePath: 'assets/images/floor/coverings/hardwood_oak.png',
    tags: ['wood', 'hardwood', 'oak', 'plank', 'base', 'floor'],
    mountingType: FixtureMountingType.NONE,
  },
  {
    id: 'base_covering_laminate',
    name: 'Laminate Flooring',
    description: 'Versatile and affordable laminate flooring that mimics wood or stone.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'coverings',
    imagePath: 'assets/images/floor/coverings/laminate.png',
    tags: ['laminate', 'wood look', 'stone look', 'base', 'floor'],
    mountingType: FixtureMountingType.NONE,
  },
  {
    id: 'base_covering_carpet_plush',
    name: 'Plush Carpet',
    description: 'Soft and luxurious plush carpet for comfort in bedrooms and living areas.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'coverings',
    imagePath: '/icon/floor/floor-coverings/carpet.svg',
    tags: ['carpet', 'soft', 'bedroom', 'living room', 'base', 'floor'],
    mountingType: FixtureMountingType.NONE,
  },
  {
    id: 'base_coverings_area_rug_modern',
    name: 'Modern Area Rug',
    description: 'Stylish modern area rug to define spaces and add texture.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'coverings',
    imagePath: 'assets/images/floor/coverings/area_rug_modern.png',
    defaultWidth: 2400,
    defaultDepth: 1800,
    tags: ['rug', 'area rug', 'modern', 'living room', 'coverings', 'base', 'floor'],
    mountingType: FixtureMountingType.NONE,
  },
  {
    id: 'base_covering_polished_concrete',
    name: 'Polished Concrete Floor',
    description: 'Sleek and durable polished concrete flooring for a modern, industrial look.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'coverings',
    imagePath: 'assets/images/floor/coverings/polished_concrete.png',
    tags: ['concrete', 'polished', 'modern', 'industrial', 'base', 'floor'],
    mountingType: FixtureMountingType.NONE,
  },

  // Minor Category: Architecture (New section under BASE for architectural tools)
  {
    id: 'base_architecture_room_definition_tool',
    name: 'Room Definition Tool',
    description: 'Tool to define room areas on the canvas. Uses Lucide Home icon.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'architecture',
    imagePath: 'lucide:Home', // Placeholder for drawer icon source
    tags: ['room', 'area', 'floor plan', 'architectural', 'tool', 'base'],
    mountingType: FixtureMountingType.NONE, // Not a physical object
  },
  {
    id: 'base_architecture_wall_structure_tool',
    name: 'Wall Structure Tool',
    description: 'Tool to draw or place wall structures. Uses Lucide LayoutGrid icon.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'architecture',
    imagePath: 'lucide:LayoutGrid', // Placeholder for drawer icon source
    tags: ['wall', 'structure', 'floor plan', 'architectural', 'tool', 'base'],
    mountingType: FixtureMountingType.NONE,
  },
  {
    id: 'base_architecture_door_single_swing_tool',
    name: 'Door (Single Swing) Tool',
    description: 'Tool to place a single swing door. Uses Lucide DoorOpen icon.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'architecture',
    imagePath: 'lucide:DoorOpen', // Placeholder for drawer icon source
    tags: ['door', 'opening', 'single swing', 'architectural', 'tool', 'base'],
    mountingType: FixtureMountingType.NONE,
  },
  {
    id: 'base_architecture_door_sliding_tool',
    name: 'Door (Sliding) Tool',
    description: 'Tool to place a sliding door. Uses Lucide DoorOpen icon.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'architecture',
    imagePath: 'lucide:DoorOpen', // Placeholder for drawer icon source
    tags: ['door', 'opening', 'sliding', 'architectural', 'tool', 'base'],
    mountingType: FixtureMountingType.NONE,
  },
  {
    id: 'base_architecture_window_fixed_tool',
    name: 'Window (Fixed) Tool',
    description: 'Tool to place a fixed window. Uses Lucide Square icon.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'architecture',
    imagePath: 'lucide:Square', // Placeholder for drawer icon source
    tags: ['window', 'opening', 'fixed', 'architectural', 'tool', 'base'],
    mountingType: FixtureMountingType.NONE,
  },
  {
    id: 'base_architecture_window_sliding_tool',
    name: 'Window (Sliding) Tool',
    description: 'Tool to place a sliding window. Uses Lucide Square icon.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'architecture',
    imagePath: 'lucide:Square', // Placeholder for drawer icon source
    tags: ['window', 'opening', 'sliding', 'architectural', 'tool', 'base'],
    mountingType: FixtureMountingType.NONE,
  },
  {
    id: 'base_architecture_paint_can_tool',
    name: 'Paint Can Tool',
    description: 'Tool to apply paint finishes. Uses Lucide Palette icon.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'architecture',
    imagePath: 'lucide:Palette', // Placeholder for drawer icon source
    tags: ['paint', 'finish', 'wall treatment', 'decor', 'tool', 'base'],
    mountingType: FixtureMountingType.NONE,
  },
  {
    id: 'base_architecture_wallpaper_roll_tool',
    name: 'Wallpaper Roll Tool',
    description: 'Tool to apply wallpaper. Uses Lucide Wallpaper icon.',
    majorCategory: MajorCategory.BASE,
    minorCategory: 'architecture',
    imagePath: 'lucide:Wallpaper', // Placeholder for drawer icon source
    tags: ['wallpaper', 'finish', 'wall treatment', 'decor', 'tool', 'base'],
    mountingType: FixtureMountingType.NONE,
  },

  // == Ceiling Elements ==
  // Minor Category: Utilities
  {
    id: 'ceiling_utility_hvac_vent_square',
    name: 'Square HVAC Vent',
    description: 'Standard square HVAC vent for air circulation.',
    majorCategory: MajorCategory.CEILING,
    minorCategory: 'utilities',
    imagePath: '/icon/ceiling/utilities/pipeline.svg',
    defaultWidth: 300,
    defaultDepth: 300,
    defaultHeight: 50,
    tags: ['hvac', 'vent', 'air', 'ceiling', 'duct'],
    mountingType: FixtureMountingType.CEILING_MOUNTED,
  },
  {
    id: 'ceiling_utility_exhaust_fan_vent',
    name: 'Exhaust Fan Vent',
    description: 'Ceiling-mounted exhaust fan vent, typically for bathrooms or kitchens.',
    majorCategory: MajorCategory.CEILING,
    minorCategory: 'utilities',
    imagePath: '/icon/ceiling/utilities/pipeline-variant.svg',
    defaultWidth: 200,
    defaultDepth: 200,
    defaultHeight: 50,
    tags: ['exhaust', 'fan', 'vent', 'bathroom', 'kitchen', 'air', 'ceiling'],
    mountingType: FixtureMountingType.CEILING_MOUNTED,
  },
  {
    id: 'ceiling_utility_smoke_detector',
    name: 'Smoke Detector',
    description: 'Essential ceiling-mounted smoke detector for fire safety.',
    majorCategory: MajorCategory.CEILING,
    minorCategory: 'utilities',
    imagePath: 'assets/images/ceiling/utilities/smoke_detector.png',
    defaultWidth: 120,
    defaultDepth: 120,
    defaultHeight: 50,
    tags: ['safety', 'fire', 'alarm', 'detector'],
    mountingType: FixtureMountingType.CEILING_MOUNTED,
  },
  {
    id: 'ceiling_utility_sprinkler_head',
    name: 'Fire Sprinkler Head',
    description: 'Ceiling-mounted fire sprinkler head for fire suppression.',
    majorCategory: MajorCategory.CEILING,
    minorCategory: 'utilities',
    imagePath: 'assets/images/ceiling/utilities/sprinkler_head.png',
    defaultWidth: 50,
    defaultDepth: 50,
    defaultHeight: 50,
    tags: ['safety', 'fire', 'sprinkler'],
    mountingType: FixtureMountingType.CEILING_MOUNTED,
  },
  // Minor Category: Lighting (from predefinedElements, will add from FixtureCategory.LIGHT later if needed)
  {
    id: 'ceiling_lighting_recessed_round',
    name: 'Recessed Light (Round)',
    description: 'Round recessed (downlight) for general ambient lighting.',
    majorCategory: MajorCategory.CEILING,
    minorCategory: 'lighting',
    imagePath: '/icon/ceiling/lighting/lamp.svg',
    defaultWidth: 100,
    defaultDepth: 100,
    defaultHeight: 75,
    tags: ['light', 'downlight', 'pot light', 'recessed'],
    mountingType: FixtureMountingType.RECESSED,
  },
  {
    id: 'ceiling_lighting_pendant_modern',
    name: 'Modern Pendant Light',
    description: 'Stylish modern pendant light, often used over kitchen islands or dining tables.',
    majorCategory: MajorCategory.CEILING,
    minorCategory: 'lighting',
    imagePath: 'assets/images/ceiling/lighting/pendant_modern.png',
    defaultWidth: 400,
    defaultDepth: 400,
    defaultHeight: 600,
    tags: ['light', 'pendant', 'hanging', 'modern'],
    mountingType: FixtureMountingType.CEILING_MOUNTED,
  },
  {
    id: 'ceiling_lighting_chandelier_classic',
    name: 'Classic Chandelier',
    description: 'Elegant classic chandelier for formal spaces.',
    majorCategory: MajorCategory.CEILING,
    minorCategory: 'lighting',
    imagePath: 'assets/images/ceiling/lighting/chandelier_classic.png',
    defaultWidth: 800,
    defaultDepth: 800,
    defaultHeight: 700,
    tags: ['light', 'chandelier', 'formal', 'classic'],
    mountingType: FixtureMountingType.CEILING_MOUNTED,
  },
  {
    id: 'ceiling_lighting_flush_mount_simple',
    name: 'Simple Flush Mount Light',
    description: 'Basic flush mount ceiling light for general illumination in rooms with lower ceilings.',
    majorCategory: MajorCategory.CEILING,
    minorCategory: 'lighting',
    imagePath: 'assets/images/ceiling/lighting/flush_mount_simple.png',
    defaultWidth: 300,
    defaultDepth: 300,
    defaultHeight: 150,
    tags: ['light', 'flush mount', 'ceiling'],
    mountingType: FixtureMountingType.CEILING_MOUNTED,
  },
  // Minor Category: Fans (from predefinedElements, FixtureCategory.FAN can be added if distinct icon)
  {
    id: 'ceiling_lighting_fan_standard_with_light',
    name: 'Ceiling Fan with Light',
    description: 'Standard ceiling fan with integrated light kit.',
    majorCategory: MajorCategory.CEILING,
    minorCategory: 'lighting',
    imagePath: 'assets/images/ceiling/fans/fan_with_light.png',
    defaultWidth: 1200,
    defaultDepth: 1200,
    defaultHeight: 400,
    tags: ['fan', 'ceiling fan', 'light', 'circulation', 'lighting'],
    mountingType: FixtureMountingType.CEILING_MOUNTED,
  },
  {
    id: 'ceiling_lighting_fan_modern_no_light',
    name: 'Modern Ceiling Fan (No Light)',
    description: 'Sleek modern ceiling fan without a light kit.',
    majorCategory: MajorCategory.CEILING,
    minorCategory: 'lighting',
    imagePath: 'assets/images/ceiling/fans/fan_modern_no_light.png',
    defaultWidth: 1300,
    defaultDepth: 1300,
    defaultHeight: 350,
    tags: ['fan', 'ceiling fan', 'modern', 'circulation', 'lighting'],
    mountingType: FixtureMountingType.CEILING_MOUNTED,
  },

  // == Furniture Elements (incorporating former Fixtures and Furniture items) ==
  // Minor Category: Storage
  {
    id: 'furniture_storage_bookshelf_tall',
    name: 'Tall Bookshelf',
    description: 'Tall wooden bookshelf with multiple shelves for books and decor.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'storage',
    imagePath: '/icon/furniture/storage/bookshelf.svg',
    defaultWidth: 800,
    defaultDepth: 300,
    defaultHeight: 1800,
    tags: ['bookshelf', 'storage', 'shelves', 'wood'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_storage_cabinet_base',
    name: 'Base Cabinet (2-door)',
    description: 'Standard two-door base cabinet for kitchen or utility storage.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'storage',
    imagePath: 'assets/images/furniture/storage/cabinet_base.png',
    defaultWidth: 900,
    defaultDepth: 600,
    defaultHeight: 850,
    tags: ['cabinet', 'storage', 'kitchen', 'base'],
    mountingType: FixtureMountingType.FLOOR_MOUNTED,
  },
  {
    id: 'furniture_storage_dresser_6drawer',
    name: '6-Drawer Dresser',
    description: 'Wooden dresser with six drawers for bedroom storage.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'storage',
    imagePath: 'assets/images/furniture/storage/dresser_6drawer.png',
    defaultWidth: 1500,
    defaultDepth: 500,
    defaultHeight: 800,
    tags: ['dresser', 'storage', 'bedroom', 'drawers'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_storage_nightstand_2drawer',
    name: 'Nightstand (2-Drawer)',
    description: 'Bedside nightstand with two drawers.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'storage',
    imagePath: '/icon/furniture/storage/nightstand.svg',
    defaultWidth: 500,
    defaultDepth: 400,
    defaultHeight: 600,
    tags: ['nightstand', 'bedside', 'storage', 'bedroom'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_table_tv_console_modern',
    name: 'Modern TV Console',
    description: 'Sleek modern TV console for media storage and display.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'tables',
    imagePath: '/icon/furniture/tables/tv-table.svg',
    defaultWidth: 1800,
    defaultDepth: 400,
    defaultHeight: 500,
    tags: ['tv stand', 'media console', 'tables', 'living room', 'entertainment'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_storage_wardrobe_2door',
    name: '2-Door Wardrobe',
    description: 'Two-door wardrobe for clothes storage.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'storage',
    imagePath: '/icon/furniture/storage/double-doors.svg',
    defaultWidth: 1000,
    defaultDepth: 600,
    defaultHeight: 2000,
    tags: ['wardrobe', 'armoire', 'closet', 'storage', 'bedroom'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_storage_bench_with_storage',
    name: 'Storage Bench',
    description: 'A bench with built-in storage, often used in entryways or bedrooms.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'storage',
    imagePath: '/icon/furniture/storage/fitness-bench.svg',
    defaultWidth: 1200,
    defaultDepth: 400,
    defaultHeight: 450,
    tags: ['bench', 'storage bench', 'entryway', 'bedroom', 'seating', 'storage'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  // Minor Category: Beds
  {
    id: 'furniture_bed_single',
    name: 'Single Bed',
    description: 'Standard single bed frame with headboard.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'beds',
    imagePath: '/icon/furniture/beds/single-bed.svg',
    defaultWidth: 900,
    defaultDepth: 2000,
    defaultHeight: 900,
    tags: ['bed', 'single', 'twin', 'bedroom'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_bed_queen',
    name: 'Queen Bed',
    description: 'Queen-size bed frame with headboard.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'beds',
    imagePath: '/icon/furniture/beds/double-bed.svg',
    defaultWidth: 1600,
    defaultDepth: 2100,
    defaultHeight: 1000,
    tags: ['bed', 'queen', 'bedroom'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_bed_king',
    name: 'King Bed',
    description: 'King-size bed frame with headboard.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'beds',
    imagePath: '/icon/furniture/beds/double-bed.svg',
    defaultWidth: 1900,
    defaultDepth: 2100,
    defaultHeight: 1000,
    tags: ['bed', 'king', 'bedroom'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_bed_bunk',
    name: 'Bunk Bed',
    description: 'A bed frame with two or more beds stacked vertically.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'beds',
    imagePath: '/icon/furniture/beds/single-bed-variant.svg',
    defaultWidth: 1000,
    defaultDepth: 2000,
    defaultHeight: 1800,
    tags: ['bed', 'bunk bed', 'bedroom', 'kids room', 'space saving'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  // Minor Category: Tables
  {
    id: 'furniture_table_dining_rect_6seater',
    name: 'Dining Table (Rectangular, 6-Seater)',
    description: 'Rectangular wooden dining table, seats six.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'tables',
    imagePath: '/icon/furniture/tables/table.svg',
    defaultWidth: 1800,
    defaultDepth: 900,
    defaultHeight: 750,
    tags: ['table', 'dining', 'kitchen', 'wood'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_table_coffee_modern',
    name: 'Modern Coffee Table',
    description: 'Modern rectangular coffee table for living rooms.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'tables',
    imagePath: '/icon/furniture/tables/coffee-table.svg',
    defaultWidth: 1200,
    defaultDepth: 600,
    defaultHeight: 450,
    tags: ['table', 'coffee table', 'living room', 'modern'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_table_side_round',
    name: 'Round Side Table',
    description: 'Small round side table or end table.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'tables',
    imagePath: '/icon/furniture/tables/table-variant.svg',
    defaultWidth: 450,
    defaultDepth: 450,
    defaultHeight: 550,
    tags: ['table', 'side table', 'end table', 'living room'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_table_desk_simple',
    name: 'Simple Office Desk',
    description: 'Simple desk for home office or study areas.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'tables',
    imagePath: '/icon/furniture/tables/desk.svg',
    defaultWidth: 1200,
    defaultDepth: 600,
    defaultHeight: 750,
    tags: ['table', 'desk', 'office', 'study'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_table_desk_l_shaped',
    name: 'L-Shaped Desk',
    description: 'An L-shaped desk providing ample workspace, suitable for corners.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'tables',
    imagePath: '/icon/furniture/tables/desk-variant.svg',
    defaultWidth: 1500,
    defaultDepth: 1500,
    defaultHeight: 750,
    tags: ['table', 'desk', 'office', 'study', 'l-shaped', 'corner desk'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_table_desk_standing',
    name: 'Standing Desk',
    description: 'An adjustable or fixed-height standing desk for ergonomic work.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'tables',
    imagePath: '/icon/furniture/tables/desk-variant2.svg',
    defaultWidth: 1200,
    defaultDepth: 700,
    defaultHeight: 1100,
    tags: ['table', 'desk', 'office', 'study', 'standing desk', 'ergonomic'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_table_console',
    name: 'Console Table',
    description: 'A narrow table typically placed against a wall in hallways or living rooms.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'tables',
    imagePath: '/icon/furniture/tables/table-second-variant.svg',
    defaultWidth: 1200,
    defaultDepth: 350,
    defaultHeight: 800,
    tags: ['table', 'console table', 'hallway', 'entryway', 'living room', 'decor'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  // Minor Category: Seating
  {
    id: 'furniture_seating_sofa_3seater_modern',
    name: 'Modern 3-Seater Sofa',
    description: 'Comfortable modern sofa that seats three.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'seating',
    imagePath: '/icon/furniture/seating/sofa.svg',
    defaultWidth: 2200,
    defaultDepth: 900,
    defaultHeight: 850,
    tags: ['sofa', 'couch', 'seating', 'living room', 'modern'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_seating_loveseat',
    name: 'Loveseat',
    description: 'A small sofa designed to seat two people.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'seating',
    imagePath: '/icon/furniture/seating/sofa-variant.svg',
    defaultWidth: 1600,
    defaultDepth: 900,
    defaultHeight: 850,
    tags: ['sofa', 'loveseat', 'couch', 'seating', 'living room', 'two-seater'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_seating_sofa_sectional',
    name: 'Sectional Sofa',
    description: 'A multi-piece sofa, often L-shaped or U-shaped.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'seating',
    imagePath: '/icon/furniture/seating/sofa-second-variant.svg',
    defaultWidth: 2800,
    defaultDepth: 1800,
    defaultHeight: 850, // Example dimensions for an L-shape
    tags: ['sofa', 'sectional', 'couch', 'seating', 'living room', 'modular'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_seating_armchair_accent',
    name: 'Accent Armchair',
    description: 'Stylish accent armchair for living rooms or bedrooms.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'seating',
    imagePath: '/icon/furniture/seating/chair.svg',
    defaultWidth: 800,
    defaultDepth: 750,
    defaultHeight: 900,
    tags: ['armchair', 'chair', 'seating', 'living room', 'accent'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_seating_dining_chair_upholstered',
    name: 'Upholstered Dining Chair',
    description: 'Comfortable upholstered dining chair.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'seating',
    imagePath: '/icon/furniture/seating/chair.svg',
    defaultWidth: 500,
    defaultDepth: 550,
    defaultHeight: 950,
    tags: ['chair', 'dining chair', 'seating', 'upholstered'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_seating_office_chair_ergonomic',
    name: 'Ergonomic Office Chair',
    description: 'Ergonomic office chair with wheels and swivel.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'seating',
    imagePath: '/icon/furniture/seating/desk-chair.svg',
    defaultWidth: 650,
    defaultDepth: 650,
    defaultHeight: 1100,
    tags: ['chair', 'office chair', 'seating', 'ergonomic'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_seating_stool_bar',
    name: 'Bar Stool',
    description: 'Tall bar stool for kitchen counters or home bars.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'seating',
    imagePath: 'assets/images/furniture/seating/stool_bar.png',
    defaultWidth: 400,
    defaultDepth: 400,
    defaultHeight: 750,
    tags: ['stool', 'bar stool', 'seating', 'kitchen'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_seating_ottoman_storage',
    name: 'Storage Ottoman',
    description: 'Ottoman with built-in storage.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'seating',
    imagePath: 'assets/images/furniture/seating/ottoman_storage.png',
    defaultWidth: 800,
    defaultDepth: 400,
    defaultHeight: 450,
    tags: ['ottoman', 'footstool', 'seating', 'storage'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  // Minor Category: Appliances (incorporating former Fixture items)
  {
    id: 'furniture_appliance_refrigerator_french_door',
    name: 'French Door Refrigerator',
    description: 'Large French door style refrigerator with bottom freezer.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: 'assets/images/furniture/appliances/refrigerator_french_door.png',
    defaultWidth: 900,
    defaultDepth: 750,
    defaultHeight: 1800,
    tags: ['refrigerator', 'fridge', 'kitchen', 'appliance'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_appliance_oven_range_electric',
    name: 'Electric Range Oven',
    description: 'Standard electric range with oven and cooktop.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: '/icon/furniture/appliances/gas-burner.svg',
    defaultWidth: 760,
    defaultDepth: 650,
    defaultHeight: 910,
    tags: ['oven', 'range', 'cooktop', 'kitchen', 'appliance', 'stove'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_appliance_cooktop_gas',
    name: 'Gas Cooktop',
    description: 'Built-in gas cooktop.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: '/icon/furniture/appliances/gas-burner-variant.svg',
    defaultWidth: 750,
    defaultDepth: 520,
    defaultHeight: 50,
    tags: ['cooktop', 'stove', 'kitchen', 'appliance', 'gas'],
    mountingType: FixtureMountingType.BUILT_IN,
  },
  {
    id: 'furniture_appliance_microwave_countertop',
    name: 'Countertop Microwave',
    description: 'Standard countertop microwave oven.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: 'assets/images/furniture/appliances/microwave_countertop.png',
    defaultWidth: 500,
    defaultDepth: 400,
    defaultHeight: 300,
    tags: ['microwave', 'kitchen', 'appliance'],
    mountingType: FixtureMountingType.COUNTERTOP,
  },
  {
    id: 'furniture_appliance_dishwasher_built_in',
    name: 'Built-in Dishwasher',
    description: 'Standard built-in dishwasher.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: 'assets/images/furniture/appliances/dishwasher_built_in.png',
    defaultWidth: 600,
    defaultDepth: 600,
    defaultHeight: 850,
    tags: ['dishwasher', 'kitchen', 'appliance'],
    mountingType: FixtureMountingType.BUILT_IN,
  },
  {
    id: 'furniture_appliance_washing_machine_front_load',
    name: 'Front-Load Washing Machine',
    description: 'Front-load washing machine for laundry.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: 'assets/images/furniture/appliances/washing_machine_front_load.png',
    defaultWidth: 600,
    defaultDepth: 650,
    defaultHeight: 850,
    tags: ['washing machine', 'laundry', 'appliance'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_appliance_dryer_front_load',
    name: 'Front-Load Dryer',
    description: 'Front-load clothes dryer for laundry.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: 'assets/images/furniture/appliances/dryer_front_load.png',
    defaultWidth: 600,
    defaultDepth: 650,
    defaultHeight: 850,
    tags: ['dryer', 'laundry', 'appliance'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_appliance_sink_kitchen',
    name: 'Kitchen Sink',
    description: 'Standard kitchen sink.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: '/icon/furniture/appliances/sink.svg',
    defaultWidth: 800,
    defaultDepth: 500,
    defaultHeight: 200,
    tags: ['sink', 'kitchen', 'plumbing', 'appliance'],
    mountingType: FixtureMountingType.COUNTERTOP,
  },
  {
    id: 'furniture_appliance_sink_bathroom',
    name: 'Bathroom Sink (Vanity)',
    description: 'Bathroom sink, often part of a vanity unit.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: '/icon/furniture/appliances/sink-variant.svg',
    defaultWidth: 600,
    defaultDepth: 450,
    defaultHeight: 180,
    tags: ['sink', 'bathroom', 'vanity', 'plumbing', 'appliance'],
    mountingType: FixtureMountingType.COUNTERTOP,
  },
  {
    id: 'furniture_appliance_sink_utility',
    name: 'Utility Sink',
    description: 'A deep sink for utility rooms, laundry, or workshops.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: '/icon/furniture/appliances/sink-second-variant.svg',
    defaultWidth: 600,
    defaultDepth: 500,
    defaultHeight: 300, // Sink depth, not overall height with stand
    tags: ['sink', 'utility sink', 'laundry', 'workshop', 'plumbing', 'appliance'],
    mountingType: FixtureMountingType.FLOOR_MOUNTED, // Or BUILT_IN
  },
  {
    id: 'furniture_appliance_toilet',
    name: 'Toilet',
    description: 'Standard floor-mounted toilet.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: '/icon/furniture/appliances/toilet.svg',
    defaultWidth: 400,
    defaultDepth: 700,
    defaultHeight: 750,
    tags: ['toilet', 'bathroom', 'plumbing', 'appliance'],
    mountingType: FixtureMountingType.FLOOR_MOUNTED,
  },
  {
    id: 'furniture_appliance_bathtub_alcove',
    name: 'Alcove Bathtub',
    description: 'Standard alcove bathtub.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: '/icon/furniture/appliances/bathtub.svg',
    defaultWidth: 1500,
    defaultDepth: 750,
    defaultHeight: 500,
    tags: ['bathtub', 'bath', 'bathroom', 'plumbing', 'appliance'],
    mountingType: FixtureMountingType.BUILT_IN,
  },
  {
    id: 'furniture_appliance_shower_stall',
    name: 'Shower Stall',
    description: 'Prefabricated shower stall.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: '/icon/furniture/appliances/shower.svg',
    defaultWidth: 900,
    defaultDepth: 900,
    defaultHeight: 2000,
    tags: ['shower', 'bathroom', 'plumbing', 'appliance'],
    mountingType: FixtureMountingType.FLOOR_MOUNTED,
  },
  {
    id: 'furniture_appliance_shower_panel',
    name: 'Shower Panel System',
    description: 'A wall-mounted shower panel with multiple jets and controls.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: '/icon/furniture/appliances/shower-variant.svg',
    defaultWidth: 300,
    defaultDepth: 80,
    defaultHeight: 1500,
    tags: ['shower', 'shower panel', 'bathroom', 'plumbing', 'appliance', 'luxury'],
    mountingType: FixtureMountingType.WALL_MOUNTED,
  },
  {
    id: 'furniture_appliance_bidet',
    name: 'Bidet',
    description: 'Floor-mounted bidet.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: '/icon/furniture/appliances/bidet.svg',
    defaultWidth: 380,
    defaultDepth: 580,
    defaultHeight: 400,
    tags: ['bidet', 'bathroom', 'plumbing', 'appliance'],
    mountingType: FixtureMountingType.FLOOR_MOUNTED,
  },
  {
    id: 'furniture_appliance_treadmill',
    name: 'Treadmill',
    description: 'Exercise treadmill.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: '/icon/furniture/appliances/treadmill.svg',
    defaultWidth: 800,
    defaultDepth: 1800,
    defaultHeight: 1400,
    tags: ['treadmill', 'exercise', 'fitness', 'gym', 'appliance'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_appliance_stationary_bike',
    name: 'Stationary Bike',
    description: 'Exercise stationary bike.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: '/icon/furniture/appliances/stationary-bike.svg',
    defaultWidth: 600,
    defaultDepth: 1100,
    defaultHeight: 1300,
    tags: ['stationary bike', 'exercise', 'fitness', 'gym', 'appliance'],
    mountingType: FixtureMountingType.FREESTANDING,
  },

  // Minor Category: Decor
  {
    id: 'furniture_decor_potted_plant_large_floor',
    name: 'Large Floor Plant',
    description: 'Large potted plant to add greenery to a room.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'decor',
    imagePath: '/icon/furniture/decor/plant.svg',
    defaultWidth: 500,
    defaultDepth: 500,
    defaultHeight: 1200,
    tags: ['plant', 'decor', 'potted plant', 'greenery'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_decor_potted_plant_variant',
    name: 'Potted Plant (Variant)',
    description: 'Alternative style potted plant.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'decor',
    imagePath: '/icon/furniture/decor/plant-variant.svg',
    defaultWidth: 400,
    defaultDepth: 400,
    defaultHeight: 600,
    tags: ['plant', 'decor', 'potted plant', 'greenery'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_decor_table_lamp_modern',
    name: 'Modern Table Lamp',
    description: 'Modern table lamp for desks or side tables.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'decor',
    imagePath: 'assets/images/furniture/decor/table_lamp_modern.png',
    defaultWidth: 300,
    defaultDepth: 300,
    defaultHeight: 600,
    tags: ['lamp', 'table lamp', 'lighting', 'decor'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_decor_floor_lamp_arc',
    name: 'Arc Floor Lamp',
    description: 'Stylish arc floor lamp for living areas.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'decor',
    imagePath: 'assets/images/furniture/decor/floor_lamp_arc.png',
    defaultWidth: 1000,
    defaultDepth: 400,
    defaultHeight: 1800,
    tags: ['lamp', 'floor lamp', 'lighting', 'decor'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_decor_wall_mirror_round',
    name: 'Round Wall Mirror',
    description: 'Decorative round wall mirror.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'decor',
    imagePath: 'assets/images/furniture/decor/wall_mirror_round.png',
    defaultWidth: 700,
    defaultDepth: 30,
    defaultHeight: 700,
    tags: ['mirror', 'wall mirror', 'decor', 'round'],
    mountingType: FixtureMountingType.WALL_MOUNTED,
  },
  {
    id: 'furniture_decor_picture_frame_set',
    name: 'Picture Frame Set',
    description: 'Set of picture frames for wall gallery or tabletop.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'decor',
    imagePath: 'assets/images/furniture/decor/picture_frame_set.png',
    defaultWidth: 500,
    defaultDepth: 25,
    defaultHeight: 400,
    tags: ['picture frame', 'art', 'wall decor', 'decor'],
    mountingType: FixtureMountingType.WALL_MOUNTED,
  },
  {
    id: 'furniture_decor_curtains_pair',
    name: 'Curtains (Pair)',
    description: 'Pair of window curtains.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'decor',
    imagePath: 'assets/images/furniture/decor/curtains_pair.png',
    defaultWidth: 1500,
    defaultDepth: 10,
    defaultHeight: 2400,
    tags: ['curtains', 'drapes', 'window treatment', 'decor'],
    mountingType: FixtureMountingType.WALL_MOUNTED,
  },
  {
    id: 'furniture_decor_piano',
    name: 'Piano',
    description: 'Upright or grand piano.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'decor',
    imagePath: '/icon/furniture/decor/piano.svg',
    defaultWidth: 1500,
    defaultDepth: 600,
    defaultHeight: 1200,
    tags: ['piano', 'music', 'instrument', 'decor'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_decor_umbrella_stand',
    name: 'Umbrella Stand',
    description: 'A stand for holding umbrellas, typically placed near an entryway.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'decor',
    imagePath: '/icon/furniture/decor/umbrella.svg',
    defaultWidth: 300,
    defaultDepth: 300,
    defaultHeight: 600,
    tags: ['umbrella', 'stand', 'holder', 'entryway', 'decor'],
    mountingType: FixtureMountingType.FREESTANDING,
    attributes: { material: 'metal' },
  },
  {
    id: 'furniture_decor_fireplace_traditional',
    name: 'Traditional Fireplace',
    description: 'A traditional indoor fireplace feature.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'decor',
    imagePath: '/icon/placeholder/fireplace.svg',
    defaultWidth: 1500,
    defaultDepth: 500,
    defaultHeight: 1200,
    tags: ['fireplace', 'mantel', 'hearth', 'living room', 'decor'],
    mountingType: FixtureMountingType.BUILT_IN,
  },
  {
    id: 'furniture_decor_person_placeholder',
    name: 'Person Placeholder',
    description: 'A placeholder representing a person for scale or layout.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'decor',
    imagePath: '/icon/furniture/decor/person.svg',
    defaultWidth: 600,
    defaultDepth: 300,
    defaultHeight: 1750,
    tags: ['person', 'human', 'scale', 'placeholder', 'decor'],
    mountingType: FixtureMountingType.FREESTANDING,
    attributes: { type: 'placeholder' },
  },
  {
    id: 'furniture_appliance_fitness_bench',
    name: 'Fitness Bench',
    description: 'A bench for workouts and weight training.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: '/icon/furniture/storage/fitness-bench.svg',
    defaultWidth: 1200,
    defaultDepth: 400,
    defaultHeight: 450,
    tags: ['fitness', 'bench', 'workout', 'gym', 'exercise', 'appliance'],
    mountingType: FixtureMountingType.FREESTANDING,
    attributes: { weight_capacity_kg: 150 },
  },
  {
    id: 'furniture_utility_stairs_straight',
    name: 'Straight Stairs',
    description: 'A straight flight of indoor stairs.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'appliances',
    imagePath: '/icon/placeholder/stairs.svg',
    defaultWidth: 900,
    defaultDepth: 3000,
    defaultHeight: 2800,
    tags: ['stairs', 'staircase', 'indoor', 'structure', 'navigation', 'utility', 'appliance'],
    mountingType: FixtureMountingType.BUILT_IN,
  },
  {
    id: 'furniture_outdoor_structure_fence_panel_wood',
    name: 'Wooden Fence Panel',
    description: 'A standard wooden fence panel for outdoor boundaries.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'decor',
    imagePath: '/icon/placeholder/fence_panel.svg',
    defaultWidth: 1800,
    defaultDepth: 50,
    defaultHeight: 1800,
    tags: ['fence', 'outdoor', 'boundary', 'garden', 'wood', 'structure', 'decor'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_table_patio_round',
    name: 'Round Patio Table',
    description: 'A round table suitable for outdoor patios.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'tables',
    imagePath: '/icon/furniture/tables/table.svg',
    defaultWidth: 1200,
    defaultDepth: 1200,
    defaultHeight: 750,
    tags: ['table', 'patio', 'outdoor', 'garden', 'dining'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
  {
    id: 'furniture_seating_patio_chair',
    name: 'Patio Chair',
    description: 'A chair suitable for outdoor patios.',
    majorCategory: MajorCategory.FURNITURE,
    minorCategory: 'seating',
    imagePath: '/icon/furniture/seating/chair.svg',
    defaultWidth: 600,
    defaultDepth: 600,
    defaultHeight: 800,
    tags: ['chair', 'patio', 'outdoor', 'garden', 'seating'],
    mountingType: FixtureMountingType.FREESTANDING,
  },
]

// Placeholder to make it a valid module for now, will be replaced by the full list
if (predefinedElements.length === 0) {
  console.warn('Predefined elements array is currently a placeholder and will be fully populated.')
}
