/**
 * Canvas Selection Utilities
 *
 * This module provides utility functions for handling canvas selection logic,
 * including single selection, multi-selection, and marquee selection.
 *
 * Key features:
 * - Keyboard modifier detection (Shift, Ctrl, Cmd)
 * - Selection mode determination
 * - Cross-platform compatibility (Mac/PC)
 */

/**
 * Selection modes for canvas elements
 */
export type SelectionMode = 'replace' | 'add' | 'toggle' | 'clear'

/**
 * Keyboard modifiers interface
 */
export interface KeyboardModifiers {
  shiftKey: boolean
  ctrlKey: boolean
  metaKey: boolean
  altKey: boolean
}

/**
 * Detects if any multi-select modifier key is pressed
 * Supports Shift, Ctrl (PC), and Cmd (Mac)
 *
 * @param modifiers - Keyboard modifiers from mouse/keyboard event
 * @returns true if any multi-select key is pressed
 */
export function isMultiSelectKeyPressed(modifiers: Partial<KeyboardModifiers>): boolean {
  return Boolean(modifiers.shiftKey || modifiers.ctrlKey || modifiers.metaKey)
}

/**
 * Determines the selection mode based on keyboard modifiers
 *
 * @param modifiers - Keyboard modifiers from mouse/keyboard event
 * @param hasCurrentSelection - Whether there are currently selected elements
 * @returns The appropriate selection mode
 */
export function getSelectionMode(
  modifiers: Partial<KeyboardModifiers>,
  hasCurrentSelection: boolean = false,
): SelectionMode {
  const isMultiSelect = isMultiSelectKeyPressed(modifiers)

  if (isMultiSelect) {
    // When multi-select key is pressed:
    // - For single element clicks: toggle the element
    // - For marquee selection: add to current selection
    return hasCurrentSelection ? 'add' : 'toggle'
  }

  // Default behavior: replace current selection
  return 'replace'
}

/**
 * Extracts keyboard modifiers from a React mouse event
 *
 * @param event - React mouse event or native mouse event
 * @returns Keyboard modifiers object
 */
export function extractKeyboardModifiers(
  event: React.MouseEvent | MouseEvent | KeyboardEvent | Partial<KeyboardModifiers>,
): KeyboardModifiers {
  return {
    shiftKey: Boolean(event.shiftKey),
    ctrlKey: Boolean(event.ctrlKey),
    metaKey: Boolean(event.metaKey),
    altKey: Boolean(event.altKey),
  }
}

/**
 * Determines if a background click should clear the selection
 *
 * @param modifiers - Keyboard modifiers from the click event
 * @returns true if selection should be cleared
 */
export function shouldClearSelectionOnBackgroundClick(modifiers: Partial<KeyboardModifiers>): boolean {
  // Only clear selection if no multi-select key is pressed
  return !isMultiSelectKeyPressed(modifiers)
}

/**
 * Calculates the new selection based on current selection and new elements
 *
 * @param currentSelection - Array of currently selected element IDs
 * @param newElements - Array of new element IDs to process
 * @param mode - Selection mode to apply
 * @returns New selection array
 */
export function calculateNewSelection(
  currentSelection: string[],
  newElements: string[],
  mode: SelectionMode,
): string[] {
  const currentSet = new Set(currentSelection)

  switch (mode) {
    case 'replace':
      return [...newElements]

    case 'add':
      newElements.forEach(id => currentSet.add(id))
      return Array.from(currentSet)

    case 'toggle':
      newElements.forEach((id) => {
        if (currentSet.has(id)) {
          currentSet.delete(id)
        }
        else {
          currentSet.add(id)
        }
      })
      return Array.from(currentSet)

    case 'clear':
      return []

    default:
      console.warn(`Unknown selection mode: ${mode}`)
      return currentSelection
  }
}

/**
 * Handles single element selection logic
 *
 * @param elementId - ID of the element to select
 * @param currentSelection - Array of currently selected element IDs
 * @param modifiers - Keyboard modifiers from the click event
 * @returns New selection array
 */
export function handleSingleElementSelection(
  elementId: string,
  currentSelection: string[],
  modifiers: Partial<KeyboardModifiers>,
): string[] {
  const isMultiSelect = isMultiSelectKeyPressed(modifiers)
  const isCurrentlySelected = currentSelection.includes(elementId)

  if (isMultiSelect) {
    // Multi-select mode: toggle the element
    if (isCurrentlySelected) {
      return currentSelection.filter(id => id !== elementId)
    }
    else {
      return [...currentSelection, elementId]
    }
  }
  else {
    // Single select mode: replace selection with this element
    return [elementId]
  }
}

/**
 * Handles marquee (box) selection logic
 *
 * @param selectedElements - Array of element IDs selected by marquee
 * @param currentSelection - Array of currently selected element IDs
 * @param modifiers - Keyboard modifiers from the selection event
 * @returns New selection array
 */
export function handleMarqueeSelection(
  selectedElements: string[],
  currentSelection: string[],
  modifiers: Partial<KeyboardModifiers>,
): string[] {
  const mode = getSelectionMode(modifiers, currentSelection.length > 0)
  return calculateNewSelection(currentSelection, selectedElements, mode)
}

/**
 * Selection utility class for managing canvas selection state
 */
export class SelectionManager {
  private currentSelection: Set<string> = new Set()

  constructor(initialSelection: string[] = []) {
    this.currentSelection = new Set(initialSelection)
  }

  /**
   * Gets the current selection as an array
   */
  getSelection(): string[] {
    return Array.from(this.currentSelection)
  }

  /**
   * Sets the selection to the provided elements
   */
  setSelection(elementIds: string[]): void {
    this.currentSelection = new Set(elementIds)
  }

  /**
   * Adds elements to the current selection
   */
  addToSelection(elementIds: string[]): void {
    elementIds.forEach(id => this.currentSelection.add(id))
  }

  /**
   * Removes elements from the current selection
   */
  removeFromSelection(elementIds: string[]): void {
    elementIds.forEach(id => this.currentSelection.delete(id))
  }

  /**
   * Toggles elements in the selection
   */
  toggleSelection(elementIds: string[]): void {
    elementIds.forEach((id) => {
      if (this.currentSelection.has(id)) {
        this.currentSelection.delete(id)
      }
      else {
        this.currentSelection.add(id)
      }
    })
  }

  /**
   * Clears the entire selection
   */
  clearSelection(): void {
    this.currentSelection.clear()
  }

  /**
   * Checks if an element is selected
   */
  isSelected(elementId: string): boolean {
    return this.currentSelection.has(elementId)
  }

  /**
   * Gets the number of selected elements
   */
  getSelectionCount(): number {
    return this.currentSelection.size
  }

  /**
   * Checks if there are any selected elements
   */
  hasSelection(): boolean {
    return this.currentSelection.size > 0
  }
}
