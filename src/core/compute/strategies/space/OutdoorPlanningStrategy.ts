// import Point from '@/types/core/element/geometry/point'; // Point interface (IPoint) // Unused
import type {
  ErgonomicsEvaluationResult,
  PathwayCheckResult,
  SpacePlanningStrategy,
  SpaceType,
} from '@/types/core/compute'
/**
 * Outdoor space planning strategy implementation.
 *
 * @remarks
 * Provides planning functionality for outdoor spaces, including space utilization calculation, pathway width check, etc.
 */
import type {
  Element,
  Shape,
  // Path, // Unused
  ShapeElement, // For properties access
} from '@/types/core/elementDefinitions'
// import {
//     calculateSpaceUtilization,
//     checkPathwaysWidth
// } from '@/lib/utils/space/spacePlanning'; // Module not found
import { BoundingBoxClass } from '@/lib/utils/geometry' // BoundingBoxClass is exported from here
import { CoreError, ErrorType } from '@/services/system/error-service'
import {
  ElementType as CoreElementType, // For properties access
} from '@/types/core/elementDefinitions'
// import { calculatePointToPointDistance } from '@/lib/utils/core/common/commonUtils';

/**
 * Helper to get BoundingBoxClass from an Element.
 *
 * @param element - The element to get the bounding box for.
 * @returns A BoundingBoxClass instance or null if the element type is not supported.
 * @private
 */
function getElementBoundingBoxClass(element: Element): BoundingBoxClass | null {
  if (element === null || element === undefined)
    return null
  if (element.type === CoreElementType.RECTANGLE || element.type === CoreElementType.SQUARE) {
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement.position !== null && shapeElement.position !== undefined && shapeElement.properties !== null && shapeElement.properties !== undefined
      && typeof shapeElement.properties.width === 'number'
      && typeof shapeElement.properties.height === 'number') {
      return new BoundingBoxClass(
        shapeElement.position.x - shapeElement.properties.width / 2,
        shapeElement.position.y - shapeElement.properties.height / 2,
        shapeElement.properties.width,
        shapeElement.properties.height,
      )
    }
  }
  if (element.type === CoreElementType.CIRCLE) {
    const shapeElement = element as unknown as ShapeElement
    if (shapeElement.position !== null && shapeElement.position !== undefined && shapeElement.properties !== null && shapeElement.properties !== undefined
      && typeof shapeElement.properties.radius === 'number') {
      return new BoundingBoxClass(
        shapeElement.position.x - shapeElement.properties.radius,
        shapeElement.position.y - shapeElement.properties.radius,
        shapeElement.properties.radius * 2,
        shapeElement.properties.radius * 2,
      )
    }
  }
  // This helper might need to be expanded or use ComputeFacade for complex shapes
  console.warn(`[OutdoorPlanningStrategy] getElementBoundingBoxClass: Bounding box for type ${element.type} not fully implemented or properties missing.`)
  return null
}

/**
 * Strategy for planning outdoor spaces with appropriate furniture, materials, and environmental considerations.
 *
 * @implements SpacePlanningStrategy
 */
export class OutdoorPlanningStrategy implements SpacePlanningStrategy {
  /**
   * Calculates how efficiently the outdoor space is being utilized.
   *
   * @returns A utilization percentage between 0 and 100.
   * @throws {CoreError} If the space boundary element is invalid.
   */
  public calculateSpaceUtilization(/* elements: Element[], roomBoundaryElement: Element */): number { // Params unused due to commented out body
    // const roomBBoxClass = getElementBoundingBoxClass(roomBoundaryElement);
    // if (!roomBBoxClass) {
    //   throw new CoreError( ErrorType.INVALID_PARAMETER, 'Space boundary must be an element from which a bounding box can be derived.');
    // }
    // return calculateSpaceUtilization(elements, roomBBoxClass);
    console.warn('[OutdoorPlanningStrategy] calculateSpaceUtilization is temporarily disabled due to missing dependencies.')
    return 0
  }

  /**
   * Checks if pathways in the outdoor space have sufficient width for comfortable movement.
   *
   * @returns An array of pathway check results.
   * @throws {CoreError} If any parameters are invalid.
   */
  public checkPathwayWidth(/* elements: Element[], pathways: Path.Line[], minWidth: number */): PathwayCheckResult[] { // Params unused due to commented out body
    // if (!Array.isArray(elements)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Elements must be an array');
    // if (!Array.isArray(pathways)) throw new CoreError(ErrorType.INVALID_PARAMETER, 'Pathways must be an array');
    // if (typeof minWidth !== 'number' || minWidth <= 0) {
    //   throw new CoreError(ErrorType.INVALID_PARAMETER, 'Minimum width must be a positive number');
    // }

    // const formattedPathways = pathways.map((path, index) => {
    //     const lineForUtil = {
    //         id: path.id ?? `pathway-${index}`,
    //         start: path.start as Point,
    //         end: path.end as Point,
    //         type: CoreElementType.LINE,
    //         getSubType: () => CoreElementType.LINE,
    //     } as any;
    //     return { id: path.id ?? `pathway-${index + 1}`, line: lineForUtil as import('@/lib/utils/element/path/lineImplementation').Line };
    // });

    // return checkPathwaysWidth(elements, formattedPathways, minWidth);
    console.warn('[OutdoorPlanningStrategy] checkPathwayWidth is temporarily disabled due to missing dependencies.')
    return []
  }

  /**
   * Evaluates the ergonomics of the outdoor workspace setup.
   *
   * @param elements - The elements within the outdoor space.
   * @param deskElement - The desk element for outdoor workspace evaluation.
   * @param chairElement - The chair element for outdoor workspace evaluation.
   * @returns An ergonomics evaluation result with issues and recommendations.
   * @throws {CoreError} If desk or chair elements are invalid.
   */
  public evaluateErgonomics(
    elements: Element[],
    deskElement: Element,
    chairElement: Element,
  ): ErgonomicsEvaluationResult {
    const issues: string[] = []
    const recommendations: string[] = []

    if (!deskElement?.type || !chairElement?.type) {
      throw new CoreError(ErrorType.InvalidParameter, 'Desk and chair elements must be provided and have a type.')
    }

    const deskShape = deskElement as unknown as Shape.Rectangle
    const chairShape = chairElement as unknown as Shape.Rectangle
    const chairShapeElement = chairShape as unknown as ShapeElement // Added for properties access

    const deskHeight = deskShape.height ?? 0
    const chairProperties = chairShapeElement.properties as { seatHeight?: number } | undefined
    const chairSeatHeight = chairProperties?.seatHeight ?? (chairShape.height ? chairShape.height * 0.6 : 0.45)

    if (!this.checkForWeatherProtection(elements, deskElement)) {
      issues.push('Outdoor workspace lacks weather protection.')
      recommendations.push('Add weather-resistant canopy or umbrella.')
    }

    if (deskHeight < 0.7 || deskHeight > 0.76) {
      issues.push(`Desk height ${deskHeight.toFixed(2)}m is not ideal (0.7-0.76m).`)
    }
    if (chairSeatHeight < 0.42 || chairSeatHeight > 0.48) {
      issues.push(`Chair height ${chairSeatHeight.toFixed(2)}m is not ideal (0.42-0.48m).`)
    }

    const deskMaterial = (deskElement as ShapeElement).properties?.material as string || ''
    const chairMaterial = (chairElement as ShapeElement).properties?.material as string || ''
    if (!this.isOutdoorSuitableMaterial(deskMaterial))
      issues.push(`Desk material "${deskMaterial}" may not be outdoor suitable.`)
    if (!this.isOutdoorSuitableMaterial(chairMaterial))
      issues.push(`Chair material "${chairMaterial}" may not be outdoor suitable.`)

    recommendations.push('Choose weather-resistant materials.')
    recommendations.push('Position workspace to avoid direct sunlight on screens.')

    return { isValid: issues.length === 0, issues, recommendations }
  }

  /**
   * Checks if a desk element has adequate weather protection.
   *
   * @param elements - All elements in the outdoor space.
   * @param deskElement - The desk element to check for weather protection.
   * @returns True if the desk has adequate weather protection, false otherwise.
   * @private
   */
  private checkForWeatherProtection(elements: Element[], deskElement: Element): boolean {
    const deskPos = (deskElement as ShapeElement).position
    if (deskPos === null || deskPos === undefined)
      return false

    const protectionElements = elements.filter((el) => {
      const props = (el as ShapeElement).properties
      return props?.customType === 'umbrella' || props?.customType === 'canopy' || props?.customType === 'pergola' || props?.customType === 'awning' || props?.category === 'shade_structure'
    })

    for (const protEl of protectionElements) {
      const protBBox = getElementBoundingBoxClass(protEl)
      if (protBBox
        && deskPos.x >= protBBox.position.x && deskPos.x <= protBBox.position.x + protBBox.width
        && deskPos.y >= protBBox.position.y && deskPos.y <= protBBox.position.y + protBBox.height) {
        return true
      }
    }
    return false
  }

  /**
   * Evaluates the drainage design of the outdoor space.
   *
   * @param _elements - The elements within the outdoor space (not used in current implementation).
   * @param hasDrainage - Whether the space has a drainage system.
   * @param slopePercentage - The slope percentage of the ground for water runoff.
   * @returns An object with validation result and any issues found.
   */
  public checkDrainage(_elements: Element[], hasDrainage: boolean, slopePercentage: number): { isValid: boolean, issues: string[] } {
    const issues: string[] = []
    if (!hasDrainage)
      issues.push('Outdoor space lacks drainage design.')
    if (typeof slopePercentage !== 'number' || slopePercentage < 1 || slopePercentage > 2) { // Typical 1-2%
      issues.push(`Ground slope of ${slopePercentage}% is outside recommended 1-2%.`)
    }
    return { isValid: issues.length === 0, issues }
  }

  /**
   * Checks if the materials used in the outdoor space are suitable for outdoor conditions.
   *
   * @param elements - The elements within the outdoor space.
   * @returns An object with validation result and recommendations.
   */
  public checkMaterials(elements: Element[]): { isValid: boolean, recommendations: string[] } {
    const recommendations: string[] = []
    let isValid = true
    elements.forEach((el) => {
      const material = (el as ShapeElement).properties?.material as string || ''
      if ((el.type === CoreElementType.FURNITURE || (el as ShapeElement).properties?.category === 'floor_surface') && !this.isOutdoorSuitableMaterial(material)) {
        isValid = false
        recommendations.push(`Element ${el.id} material "${material}" may not be outdoor suitable.`)
      }
    })
    if (isValid)
      recommendations.push('Material choices appear suitable for outdoor use.')
    return { isValid, recommendations }
  }

  /**
   * Evaluates the lighting design of the outdoor space.
   *
   * @param elements - The elements within the outdoor space.
   * @returns An object with validation result and recommendations for lighting.
   */
  public checkLighting(elements: Element[]): { isValid: boolean, recommendations: string[] } {
    const recommendations: string[] = []
    const lightingElements = elements.filter(el => el.type === CoreElementType.LIGHT || (el as ShapeElement).properties?.category === 'lighting')
    if (lightingElements.length === 0) {
      recommendations.push('No outdoor lighting detected.')
      return { isValid: false, recommendations }
    }
    // Further checks could involve coverage, type (e.g., weatherproof)
    recommendations.push('Outdoor lighting is present. Ensure it is weatherproof and provides adequate coverage.')
    return { isValid: true, recommendations }
  }

  /**
   * Determines if a material is suitable for outdoor use.
   *
   * @param material - The material name to check.
   * @returns True if the material is suitable for outdoor use, false otherwise.
   * @private
   */
  private isOutdoorSuitableMaterial(material: string): boolean {
    if (!material)
      return false
    const lowerMat = material.toLowerCase()
    const outdoorMaterials = ['stone', 'concrete', 'brick', 'tile', 'teak', 'cedar', 'redwood', 'aluminum', 'stainless steel', 'resin', 'polyethylene', 'outdoor fabric']
    return outdoorMaterials.some(m => lowerMat.includes(m))
  }

  /**
   * Gets the type of space this strategy is designed for.
   *
   * @returns The space type identifier ('outdoor').
   */
  public getSpaceType(): SpaceType {
    return 'outdoor'
  }
}
