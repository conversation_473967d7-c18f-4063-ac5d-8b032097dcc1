/**
 * Distance Calculation Strategy for Polygon Elements
 *
 * @remarks
 * This strategy implements the {@link DistanceCalculatorStrategy} for calculating
 * the distance between a Polygon-like element (e.g., {@link CoreElementType.POLYGON},
 * {@link CoreElementType.TRIANGLE}) and another {@link ShapeElement}.
 *
 * It supports calculating distances to:
 * - Other Polygons.
 * - Rectangles/Squares.
 * - Circles/Ellipses.
 * - Lines.
 *
 * The calculations involve complex geometric operations, many of which are currently
 * implemented as local helper functions within this file (e.g.,
 * `calculatePointToLineSegmentDistance`, `doLineSegmentsIntersect`, `isPointInPolygon`,
 * `calculatePolygonToPolygonDistance`, `calculatePolygonToLineSegmentDistance`).
 * **FIXME:** These helper functions should be refactored and moved to appropriate
 * utility modules in `src/lib/utils/geometry/`.
 *
 * The strategy validates element types and their geometric properties (vertices, position).
 * It converts relative polygon vertices to absolute coordinates before performing
 * distance calculations.
 *
 * @module core/compute/strategies/distance/PolygonDistanceStrategy
 * @see {@link Shape.Polygon}
 * @see {@link DistanceCalculatorStrategy}
 */
import type { DistanceCalculatorStrategy } from '../../../../types/core/compute'
import type { PointData as PointInterface } from '../../../../types/core/element/geometry/point'
import type {
  Path,
  Shape,
  ShapeElement,
} from '../../../../types/core/elementDefinitions'
import { PointClass } from '../../../../lib/utils/geometry/PointClass'
import { calculateDistance as calculatePointToPointDistance } from '../../../../lib/utils/geometry/pointUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import {
  ElementType as CoreElementType,
} from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

// FIXME: All helper functions below should be moved to appropriate files within src/lib/utils/geometry/
// (e.g., distanceUtils.ts, intersectionUtils.ts, polygonUtils.ts).

/**
 * Calculates the shortest distance from a point to a line segment.
 * @param p - The point.
 * @param a - Start point of the line segment.
 * @param b - End point of the line segment.
 * @returns The shortest distance.
 */
function calculatePointToLineSegmentDistance(p: PointInterface, a: PointInterface, b: PointInterface): number {
  const l2 = (a.x - b.x) ** 2 + (a.y - b.y) ** 2
  if (l2 === 0)
    return calculatePointToPointDistance(new PointClass(p.x, p.y, p.z), new PointClass(a.x, a.y, a.z))
  let t = ((p.x - a.x) * (b.x - a.x) + (p.y - a.y) * (b.y - a.y)) / l2
  t = Math.max(0, Math.min(1, t))
  const closestPoint: PointInterface = {
    x: a.x + t * (b.x - a.x),
    y: a.y + t * (b.y - a.y),
    z: (a.z !== undefined && b.z !== undefined) ? (a.z + t * (b.z - a.z)) : (a.z ?? b.z ?? p.z),
  }
  return calculatePointToPointDistance(new PointClass(p.x, p.y, p.z), new PointClass(closestPoint.x, closestPoint.y, closestPoint.z))
}

/**
 * Determines the orientation of an ordered triplet (p, q, r).
 * @returns 0 if colinear, 1 if clockwise, 2 if counterclockwise.
 */
function orientation(p: PointInterface, q: PointInterface, r: PointInterface): number {
  const val = (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y)
  if (val === 0)
    return 0
  return (val > 0) ? 1 : 2
}

/**
 * Checks if point q lies on segment pr.
 */
function onSegment(p: PointInterface, q: PointInterface, r: PointInterface): boolean {
  return (q.x <= Math.max(p.x, r.x) && q.x >= Math.min(p.x, r.x)
    && q.y <= Math.max(p.y, r.y) && q.y >= Math.min(p.y, r.y))
}

/**
 * Checks if line segment p1q1 intersects with line segment p2q2.
 */
function doLineSegmentsIntersect(p1: PointInterface, q1: PointInterface, p2: PointInterface, q2: PointInterface): boolean {
  const o1 = orientation(p1, q1, p2)
  const o2 = orientation(p1, q1, q2)
  const o3 = orientation(p2, q2, p1)
  const o4 = orientation(p2, q2, q1)

  if (o1 !== o2 && o3 !== o4)
    return true
  if (o1 === 0 && onSegment(p1, p2, q1))
    return true
  if (o2 === 0 && onSegment(p1, q2, q1))
    return true
  if (o3 === 0 && onSegment(p2, p1, q2))
    return true
  if (o4 === 0 && onSegment(p2, q1, q2))
    return true
  return false
}

/**
 * Calculates the shortest distance between two line segments.
 */
function calculateLineSegmentToLineSegmentDistance(p1: PointInterface, p2: PointInterface, p3: PointInterface, p4: PointInterface): number {
  if (doLineSegmentsIntersect(p1, p2, p3, p4))
    return 0
  return Math.min(
    calculatePointToLineSegmentDistance(p1, p3, p4),
    calculatePointToLineSegmentDistance(p2, p3, p4),
    calculatePointToLineSegmentDistance(p3, p1, p2),
    calculatePointToLineSegmentDistance(p4, p1, p2),
  )
}

/**
 * Checks if a point is inside a polygon using the ray casting algorithm.
 */
function isPointInPolygon(point: PointInterface, polygon: PointInterface[]): boolean {
  if (polygon.length < 3)
    return false
  let inside = false
  const x = point.x
  const y = point.y
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const xi = polygon[i].x
    const yi = polygon[i].y
    const xj = polygon[j].x
    const yj = polygon[j].y
    const intersect = ((yi > y) !== (yj > y))
      && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)
    if (intersect)
      inside = !inside
  }
  return inside
}

/**
 * Calculates the shortest distance between two polygons.
 */
function calculatePolygonToPolygonDistance(poly1: PointInterface[], poly2: PointInterface[]): number {
  let minDistance = Number.POSITIVE_INFINITY
  for (let i = 0; i < poly1.length; i++) {
    for (let j = 0; j < poly2.length; j++) {
      minDistance = Math.min(minDistance, calculateLineSegmentToLineSegmentDistance(
        poly1[i],
        poly1[(i + 1) % poly1.length],
        poly2[j],
        poly2[(j + 1) % poly2.length],
      ))
    }
  }
  if (poly1.some(p => isPointInPolygon(p, poly2)) || poly2.some(p => isPointInPolygon(p, poly1))) {
    return 0
  }
  return minDistance
}

/**
 * Calculates the shortest distance from a polygon to a line segment.
 */
function calculatePolygonToLineSegmentDistance(poly: PointInterface[], lineStart: PointInterface, lineEnd: PointInterface): number {
  let minDistance = Number.POSITIVE_INFINITY
  for (let i = 0; i < poly.length; i++) {
    minDistance = Math.min(minDistance, calculateLineSegmentToLineSegmentDistance(
      poly[i],
      poly[(i + 1) % poly.length],
      lineStart,
      lineEnd,
    ))
  }
  if (isPointInPolygon(lineStart, poly) || isPointInPolygon(lineEnd, poly))
    return 0
  return minDistance
}
// END: Helper functions

/**
 * Implements the {@link DistanceCalculatorStrategy} for Polygon-like elements.
 */
export class PolygonDistanceStrategy implements DistanceCalculatorStrategy {
  /**
   * Calculates the distance between a Polygon-like element (element1) and another {@link ShapeElement} (element2).
   *
   * @param element1 - The first shape element, which must be one of the valid polygon types
   *                   (e.g., {@link CoreElementType.POLYGON}, {@link CoreElementType.TRIANGLE}).
   * @param element2 - The second shape element.
   * @returns The calculated distance between the two elements.
   * @throws {@link CoreError} if `element1` is not a supported polygon type.
   * @throws {@link CoreError} if `element2` is of an unsupported type for distance calculation with a polygon.
   * @throws {@link CoreError} if either element has invalid or missing geometric properties.
   */
  public calculateDistance(element1: ShapeElement, element2: ShapeElement): number {
    const validPolygonTypes = [
      CoreElementType.POLYGON,
      CoreElementType.TRIANGLE,
      CoreElementType.HEXAGON,
      CoreElementType.QUADRILATERAL,
      CoreElementType.PENTAGON,
      CoreElementType.OCTAGON,
      CoreElementType.NONAGON,
      CoreElementType.DECAGON,
    ]

    if (!validPolygonTypes.includes(element1.type as CoreElementType)) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `PolygonDistanceStrategy's first element must be a polygon type, got ${element1.type}`,
        undefined,
        { component: 'PolygonDistanceStrategy', operation: 'calculateDistance', target: element1.id },
      )
    }

    const polygonElement1 = element1 as unknown as Shape.Polygon // Added unknown
    const vertices1_rel = polygonElement1.points // Points are directly on Shape.Polygon

    if (vertices1_rel === null || vertices1_rel === undefined || !Array.isArray(vertices1_rel) || vertices1_rel.length < 3 || !vertices1_rel.every(p => p !== null && p !== undefined && typeof p.x === 'number' && typeof p.y === 'number')) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Polygon (ID: ${element1.id}) has invalid or insufficient vertices.`,
        undefined,
        { component: 'PolygonDistanceStrategy', operation: 'calculateDistance', target: element1.id, metadata: { numPoints: vertices1_rel?.length } },
      )
    }
    const el1PosX = element1.position.x // position is on ShapeElement (element1)
    const el1PosY = element1.position.y
    const vertices1_abs = vertices1_rel.map(p => ({ x: p.x + el1PosX, y: p.y + el1PosY, z: p.z }))

    switch (element2.type) {
      case CoreElementType.POLYGON:
      case CoreElementType.TRIANGLE:
      case CoreElementType.HEXAGON:
      case CoreElementType.QUADRILATERAL:
      case CoreElementType.PENTAGON:
      case CoreElementType.OCTAGON:
      case CoreElementType.NONAGON:
      case CoreElementType.DECAGON: {
        const polygonElement2 = element2 as unknown as Shape.Polygon // Added unknown
        const vertices2_rel = polygonElement2.points
        if (vertices2_rel === null || vertices2_rel === undefined || !Array.isArray(vertices2_rel) || vertices2_rel.length < 3 || !vertices2_rel.every(p => p !== null && p !== undefined && typeof p.x === 'number' && typeof p.y === 'number')) {
          throw new CoreError(
            ErrorType.InvalidParameter,
            `Target polygon (ID: ${element2.id}) has invalid or insufficient vertices.`,
            undefined,
            { component: 'PolygonDistanceStrategy', operation: 'calculateDistance', target: element2.id, metadata: { numPoints: vertices2_rel?.length } },
          )
        }
        const el2PosX_poly = element2.position.x // position is on ShapeElement (element2)
        const el2PosY_poly = element2.position.y
        const vertices2_abs = vertices2_rel.map(p => ({ x: p.x + el2PosX_poly, y: p.y + el2PosY_poly, z: p.z }))
        return calculatePolygonToPolygonDistance(vertices1_abs, vertices2_abs)
      }

      case CoreElementType.RECTANGLE:
      case CoreElementType.SQUARE: {
        const rectElement = element2 as unknown as Shape.Rectangle // Added unknown
        const rectPos = element2.position // position is on ShapeElement (element2)
        const rectWidth = (rectElement.properties?.width as number) ?? 0
        const rectHeight = (rectElement.properties?.height as number) ?? 0
        if (rectPos === null || rectPos === undefined || typeof rectPos.x !== 'number' || typeof rectPos.y !== 'number'
          || typeof rectWidth !== 'number' || typeof rectHeight !== 'number' || !Number.isFinite(rectWidth) || !Number.isFinite(rectHeight) || rectWidth < 0 || rectHeight < 0) {
          throw new CoreError(
            ErrorType.InvalidParameter,
            `Target rectangle (ID: ${element2.id}) has invalid parameters.`,
            undefined,
            { component: 'PolygonDistanceStrategy', operation: 'calculateDistance', target: element2.id, metadata: { pos: rectPos, width: rectWidth, height: rectHeight } },
          )
        }
        // Rectangle vertices are defined from its top-left position, width, and height
        const rectVertices_abs: PointInterface[] = [
          { x: rectPos.x, y: rectPos.y, z: rectPos.z },
          { x: rectPos.x + rectWidth, y: rectPos.y, z: rectPos.z },
          { x: rectPos.x + rectWidth, y: rectPos.y + rectHeight, z: rectPos.z },
          { x: rectPos.x, y: rectPos.y + rectHeight, z: rectPos.z },
        ]
        return calculatePolygonToPolygonDistance(vertices1_abs, rectVertices_abs)
      }

      case CoreElementType.CIRCLE:
      case CoreElementType.ELLIPSE: {
        const circleOrEllipse = element2 as unknown as (Shape.Circle | Shape.Ellipse) // Added unknown
        const center2 = element2.position // position is on ShapeElement (element2)
        let radius2: number = 0
        if (element2.type === CoreElementType.CIRCLE) {
          const circleRadius = ((circleOrEllipse as Shape.Circle).properties?.radius)
          radius2 = typeof circleRadius === 'number' ? circleRadius : 0
        }
        else {
          const ellipseRadiusX = ((circleOrEllipse as Shape.Ellipse).properties?.radiusX) as number | undefined
          const ellipseRadiusY = ((circleOrEllipse as Shape.Ellipse).properties?.radiusY) as number | undefined
          const radiusX = typeof ellipseRadiusX === 'number' ? ellipseRadiusX : 0
          const radiusY = typeof ellipseRadiusY === 'number' ? ellipseRadiusY : 0
          radius2 = Math.min(radiusX, radiusY) // Approx with min radius
        }

        if (center2 === null || center2 === undefined || typeof center2.x !== 'number' || typeof center2.y !== 'number'
          || typeof radius2 !== 'number' || !Number.isFinite(radius2) || radius2 < 0) {
          throw new CoreError(
            ErrorType.InvalidParameter,
            `Target circle/ellipse (ID: ${element2.id}) has invalid parameters.`,
            undefined,
            { component: 'PolygonDistanceStrategy', operation: 'calculateDistance', target: element2.id, metadata: { center2, radius2 } },
          )
        }

        let minDistanceToCenter = Number.POSITIVE_INFINITY
        for (let i = 0; i < vertices1_abs.length; i++) {
          const p1 = vertices1_abs[i]
          const p2 = vertices1_abs[(i + 1) % vertices1_abs.length]
          minDistanceToCenter = Math.min(minDistanceToCenter, calculatePointToLineSegmentDistance(center2, p1, p2))
        }
        if (isPointInPolygon(center2, vertices1_abs))
          return 0
        return Math.max(0, minDistanceToCenter - radius2)
      }

      case CoreElementType.LINE: {
        const lineElement = element2 as unknown as Path.Line // Added unknown
        const lineStart_rel = lineElement.properties.start
        const lineEnd_rel = lineElement.properties.end
        const el2Pos_line = element2.position // position is on ShapeElement (element2)

        if (lineStart_rel === null || lineStart_rel === undefined || lineEnd_rel === null || lineEnd_rel === undefined || el2Pos_line === null || el2Pos_line === undefined
          || typeof lineStart_rel.x !== 'number' || typeof lineStart_rel.y !== 'number'
          || typeof lineEnd_rel.x !== 'number' || typeof lineEnd_rel.y !== 'number'
          || typeof el2Pos_line.x !== 'number' || typeof el2Pos_line.y !== 'number') {
          throw new CoreError(
            ErrorType.InvalidParameter,
            `Target line (ID: ${element2.id}) has invalid points or position.`,
            undefined,
            { component: 'PolygonDistanceStrategy', operation: 'calculateDistance', target: element2.id, metadata: { lineStart_rel, lineEnd_rel, el2Pos_line } },
          )
        }
        const lineStart_abs: PointInterface = { x: lineStart_rel.x + el2Pos_line.x, y: lineStart_rel.y + el2Pos_line.y, z: typeof lineStart_rel.z === 'number' ? lineStart_rel.z : 0 }
        const lineEnd_abs: PointInterface = { x: lineEnd_rel.x + el2Pos_line.x, y: lineEnd_rel.y + el2Pos_line.y, z: typeof lineEnd_rel.z === 'number' ? lineEnd_rel.z : 0 }
        return calculatePolygonToLineSegmentDistance(vertices1_abs, lineStart_abs, lineEnd_abs)
      }

      default:
        throw new CoreError(
          ErrorType.InvalidElementType,
          `Distance calculation between POLYGON and ${element2.type} is not supported by PolygonDistanceStrategy.`,
          undefined,
          { component: 'PolygonDistanceStrategy', operation: 'calculateDistance', metadata: { type1: element1.type, type2: element2.type } },
        )
    }
  }

  /**
   * Returns the primary element type that this strategy handles as the first argument.
   * @remarks This strategy handles various polygon-like types.
   * @returns {@link CoreElementType.POLYGON} - The polygon element type that this strategy handles
   */
  public getElementType(): CoreElementType {
    return CoreElementType.POLYGON
  }
}
