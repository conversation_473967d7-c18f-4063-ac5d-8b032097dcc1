import type { PointData as IPoint } from '../../../types/core/element/geometry/point'
import type { BoundingBoxClass as BoundingBox } from './BoundingBoxClass'
import { calculatePointToLineDistance } from './coordinates'
/**
 * Geometric Distance Calculation Utilities
 *
 * @remarks
 * This module provides a collection of utility functions for calculating distances
 * between various geometric entities, such as:
 * - Point to Point
 * - Point to Line Segment
 * - Point to Polygon
 * - Point to Circle
 * - Point to Ellipse
 * - Bounding Box to Bounding Box
 *
 * It also includes a helper function `isPointInPolygon` for point-in-polygon tests.
 * The functions generally expect input coordinates or shapes conforming to {@link IPoint}
 * (aliased from `PointData`) or {@link BoundingBox} (aliased from `BoundingBoxClass`).
 * Some functions may instantiate {@link PointClass} internally for calculations.
 *
 * Note: Some distance functions (e.g., `calculatePointToCircleDistance`,
 * `calculatePointToEllipseDistance`) are imported from `./pointUtils` and re-exported
 * here via the `DistanceUtils` namespace.
 *
 * @module lib/utils/geometry/distance
 */
// Removed Chinese performance optimization comments from the top of the file.
import { PointClass } from './PointClass'
import {
  calculatePointToCircleDistance,
  calculatePointToEllipseDistance,
  calculatePointToPointDistance,
} from './pointUtils' // Corrected import path

// calculatePointToPointDistance is imported from commonUtils
// calculatePointToLineDistance is imported from coordinates
// calculatePointToCircleDistance is imported from commonUtils
// calculatePointToEllipseDistance is imported from commonUtils

/**
 * Calculates the shortest distance from a point to a polygon.
 *
 * @remarks
 * If the point is inside the polygon (checked using {@link isPointInPolygon}), the distance is 0.
 * Otherwise, it calculates the minimum distance from the point to any of the polygon's edges
 * using {@link calculatePointToLineDistance}.
 *
 * @param point - The {@link IPoint} from which to calculate the distance.
 * @param polygonPoints - An array of {@link IPoint} objects defining the vertices of the polygon.
 *                        Must have at least 3 vertices.
 * @returns The shortest distance from the point to the polygon. Returns 0 if the point is inside.
 * @throws {@link Error} if `point` is not provided or `polygonPoints` is invalid (less than 3 vertices).
 */
export function calculatePointToPolygonDistance(
  point: IPoint,
  polygonPoints: IPoint[],
): number {
  if (!point) {
    throw new Error('Point must be provided')
  }

  if (!polygonPoints || !Array.isArray(polygonPoints) || polygonPoints.length < 3) {
    throw new Error('Polygon must have at least 3 vertices')
  }

  // Check if the point is inside the polygon
  if (isPointInPolygon(point, polygonPoints)) {
    return 0
  }

  // Calculate the minimum distance from the point to any edge of the polygon
  let minDistance = Number.POSITIVE_INFINITY
  const n = polygonPoints.length

  for (let i = 0; i < n; i++) {
    const start = polygonPoints[i]
    const end = polygonPoints[(i + 1) % n]

    // calculatePointToLineDistance expects PointClass instances
    const distance = calculatePointToLineDistance(
      new PointClass(point.x, point.y, point.z),
      new PointClass(start.x, start.y, start.z),
      new PointClass(end.x, end.y, end.z),
    )
    minDistance = Math.min(minDistance, distance)
  }

  return minDistance
}

/**
 * Calculates the shortest distance from a point to a circle.
 *
 * @remarks
 * This function is typically imported from `./pointUtils` and re-exported here.
 * If the point is inside or on the circle, the distance is 0. Otherwise, it's the
 * distance from the point to the circle's circumference.
 *
 * @param point - The {@link IPoint} to calculate distance from.
 * @param center - The center {@link IPoint} of the circle.
 * @param radius - The radius of the circle.
 * @returns The shortest distance from the point to the circle.
 */
// Note: calculatePointToCircleDistance is imported from './pointUtils'.
// The TSDoc here describes its expected behavior.

/**
 * Calculates the shortest distance from a point to an ellipse.
 *
 * @remarks
 * This function is typically imported from `./pointUtils` and re-exported here.
 * It computes an approximation of the minimum distance. If the point is inside
 * or on the ellipse, the distance is 0.
 *
 * @param point - The {@link IPoint} to calculate distance from.
 * @param center - The center {@link IPoint} of the ellipse.
 * @param radiusX - The radius of the ellipse along the x-axis.
 * @param radiusY - The radius of the ellipse along the y-axis.
 * @returns The approximate shortest distance from the point to the ellipse.
 */
// Note: calculatePointToEllipseDistance is imported from './pointUtils'.
// The TSDoc here describes its expected behavior.

/**
 * Determines if a point is inside a polygon using the ray casting algorithm.
 *
 * @remarks
 * This algorithm casts a ray from the point in a fixed direction (typically positive x-axis)
 * and counts how many times it intersects the edges of the polygon. An odd number of
 * intersections means the point is inside; an even number means it is outside.
 * This function handles simple polygons (non-self-intersecting).
 *
 * @param point - The {@link IPoint} to check.
 * @param polygon - An array of {@link IPoint} objects defining the vertices of the polygon in order.
 *                  Must have at least 3 vertices.
 * @returns `true` if the point is inside the polygon, `false` otherwise.
 * @throws {@link Error} if `point` is not provided. Returns `false` if `polygon` is invalid.
 */
export function isPointInPolygon(
  point: IPoint,
  polygon: IPoint[],
): boolean {
  if (!point) {
    throw new Error('Point must be provided')
  }

  if (!polygon || !Array.isArray(polygon) || polygon.length < 3) {
    return false // A polygon must have at least 3 vertices
  }

  let inside = false
  const x = point.x
  const y = point.y

  // Loop through all edges of the polygon
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const xi = polygon[i].x
    const yi = polygon[i].y
    const xj = polygon[j].x
    const yj = polygon[j].y

    // Check if the ray from the point in the positive x direction intersects this edge
    const intersect = ((yi > y) !== (yj > y))
      && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)

    // If there's an intersection, toggle the inside flag
    if (intersect)
      inside = !inside
  }

  return inside
}

/**
 * Calculates the shortest distance between two axis-aligned bounding boxes.
 *
 * @remarks
 * If the bounding boxes overlap, the distance is 0.
 * If they don't overlap but are aligned on one axis (e.g., one is directly above the other),
 * the distance is the gap between them on the other axis.
 * If they don't overlap on either axis, the distance is the Euclidean distance
 * between their closest corners.
 *
 * @param bbox1 - The first {@link BoundingBox}. Its `position` is the top-left corner.
 * @param bbox2 - The second {@link BoundingBox}. Its `position` is the top-left corner.
 * @returns The shortest distance between the two bounding boxes. Returns 0 if they overlap.
 * @throws {@link Error} if either `bbox1` or `bbox2` is not provided.
 */
export function calculateBoundingBoxDistance(
  bbox1: BoundingBox,
  bbox2: BoundingBox,
): number {
  if (!bbox1 || !bbox2) {
    throw new Error('Both bounding boxes must be provided')
  }

  // Check horizontal overlap
  const overlapX = Math.max(0, Math.min(bbox1.position.x + bbox1.width, bbox2.position.x + bbox2.width)
    - Math.max(bbox1.position.x, bbox2.position.x))

  // Check vertical overlap
  const overlapY = Math.max(0, Math.min(bbox1.position.y + bbox1.height, bbox2.position.y + bbox2.height)
    - Math.max(bbox1.position.y, bbox2.position.y))

  // If both dimensions overlap, the boxes intersect and distance is 0
  if (overlapX > 0 && overlapY > 0) {
    return 0
  }

  // If only one dimension overlaps, the distance is the minimum distance in the other dimension
  if (overlapX > 0) {
    // Calculate minimum vertical distance
    return Math.min(
      Math.abs(bbox1.position.y - (bbox2.position.y + bbox2.height)),
      Math.abs(bbox2.position.y - (bbox1.position.y + bbox1.height)),
    )
  }

  if (overlapY > 0) {
    // Calculate minimum horizontal distance
    return Math.min(
      Math.abs(bbox1.position.x - (bbox2.position.x + bbox2.width)),
      Math.abs(bbox2.position.x - (bbox1.position.x + bbox1.width)),
    )
  }

  // If no overlap in either dimension, distance is between the closest corners
  const corners1 = [
    { x: bbox1.position.x, y: bbox1.position.y },
    { x: bbox1.position.x + bbox1.width, y: bbox1.position.y },
    { x: bbox1.position.x, y: bbox1.position.y + bbox1.height },
    { x: bbox1.position.x + bbox1.width, y: bbox1.position.y + bbox1.height },
  ]

  const corners2 = [
    { x: bbox2.position.x, y: bbox2.position.y },
    { x: bbox2.position.x + bbox2.width, y: bbox2.position.y },
    { x: bbox2.position.x, y: bbox2.position.y + bbox2.height },
    { x: bbox2.position.x + bbox2.width, y: bbox2.position.y + bbox2.height },
  ]

  let minDistance = Number.MAX_VALUE

  // Find the minimum distance between any pair of corners
  // 性能优化：嵌套循环可能导致 O(n²) 时间复杂度
  // 建议优化方法：
  // 1. 使用 Map 或 Set 数据结构减少查找时间
  // 2. 预处理数据，避免重复计算
  // 3. 考虑使用更高效的算法
  // 性能优化：嵌套循环可能导致 O(n²) 时间复杂度
  // 建议优化方法：
  // 1. 使用 Map 或 Set 数据结构减少查找时间
  // 2. 预处理数据，避免重复计算
  // 3. 考虑使用更高效的算法
  for (const corner1 of corners1) {
    for (const corner2 of corners2) {
      const distance = calculatePointToPointDistance(corner1, corner2)
      minDistance = Math.min(minDistance, distance)
    }
  }

  return minDistance
}

/**
 * Namespace object for convenient access to distance calculation utility functions.
 *
 * @remarks
 * This object groups various distance-related functions from this module,
 * allowing for usage like `DistanceUtils.calculatePointToPolygonDistance(...)`.
 */
export const DistanceUtils = {
  // Point-to-shape distances
  calculatePointToPointDistance,
  calculatePointToLineDistance,
  calculatePointToPolygonDistance,
  calculatePointToCircleDistance,
  calculatePointToEllipseDistance,

  // Shape-to-shape distances
  calculateBoundingBoxDistance,

  // Utility functions
  isPointInPolygon,
}
