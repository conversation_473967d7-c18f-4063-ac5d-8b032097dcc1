/**
 * Path Preview Component
 *
 * A specialized component that provides real-time visual feedback during path drawing operations.
 * This component renders preview elements for various path types including lines, polylines,
 * arcs, and Bezier curves, helping users visualize their drawing before completion.
 *
 * Features:
 * - Real-time preview for all supported path types
 * - Multi-step drawing visualization (polylines, arcs, curves)
 * - Visual markers for control points and endpoints
 * - Intelligent arc calculation from three points
 * - Bezier curve preview with control point visualization
 * - Responsive styling with dashed preview lines
 *
 * Path Type Support:
 * - LINE: Simple two-point line preview
 * - POLYLINE: Multi-segment line with accumulated points
 * - ARC: Three-point arc with center, start, and end points
 * - QUADRATIC: Quadratic Bezier curve with control point
 * - CUBIC: Cubic <PERSON>zier curve with dual control points
 *
 * @example
 * ```tsx
 * <PathPreview
 *   isDrawing={true}
 *   pathType={ElementType.LINE}
 *   startPoint={{ x: 0, y: 0, z: 0 }}
 *   currentPoint={{ x: 100, y: 50, z: 0 }}
 * />
 * ```
 */

import type Point from '@/types/core/element/geometry/point'

import React from 'react'
import { ElementType } from '@/types/core/elementDefinitions'

// 常量定义，避免不稳定的默认props
const EMPTY_POINTS_ARRAY: Point[] = []

// generateArcPath 函数已删除

/**
 * Props for the PathPreview component
 */
interface PathPreviewProps {
  /** Whether a path is currently being drawn */
  isDrawing: boolean
  /** The type of path being drawn */
  pathType: ElementType | null
  /** The starting point of the path */
  startPoint: Point | null
  /** The current mouse/cursor position */
  currentPoint: Point | null
  /** Array of all points for multi-step drawing (polylines, etc.) */
  allPoints?: Point[]
  /** Number of clicks completed for multi-step drawing */
  clickCount?: number
}

/**
 * PathPreview component that renders real-time visual feedback during path drawing.
 *
 * This component provides immediate visual feedback to users as they draw various
 * types of paths. It handles different drawing states and renders appropriate
 * preview elements based on the path type and current drawing progress.
 *
 * The component automatically adapts its rendering based on the drawing state,
 * showing different visual elements for different stages of multi-step drawing
 * operations (like arcs and Bezier curves).
 *
 * @param props - The component props
 * @param props.isDrawing - Whether drawing is in progress
 * @param props.pathType - Type of path being drawn
 * @param props.startPoint - Starting point of the path
 * @param props.currentPoint - Current cursor position
 * @param props.allPoints - All points for multi-step drawing
 * @param props.clickCount - Number of completed clicks
 * @returns The rendered path preview or null if no preview needed
 */
function PathPreview({
  isDrawing,
  pathType,
  startPoint,
  currentPoint,
  allPoints = EMPTY_POINTS_ARRAY,
  clickCount = 0,
}: PathPreviewProps) {
  // 使用稳定的组件实例ID，避免重复渲染
  const componentInstanceId = React.useMemo(
    () => `PathPreview-SINGLE-INSTANCE-${pathType ?? 'UNKNOWN'}`,
    [pathType],
  )

  // 如果没有路径类型或当前点，不显示预览
  if (pathType == null || currentPoint == null) {
    console.warn(`[${componentInstanceId}] 不显示预览 - 缺少路径类型或当前点:`, { pathType, currentPoint })
    return null
  }

  // 只有在正在绘制且有起点时才显示预览
  if (!isDrawing || startPoint == null) {
    console.warn(`[${componentInstanceId}] 不显示预览 - 不在绘制模式或缺少起点:`, { isDrawing, startPoint })
    return null
  }

  // 使用起点（此时必须存在）
  const effectiveStartPoint = startPoint

  // 计算通用参数
  const dx = currentPoint.x - effectiveStartPoint.x
  const dy = currentPoint.y - effectiveStartPoint.y
  const distance = Math.sqrt(dx * dx + dy * dy)
  const midPoint = {
    x: (effectiveStartPoint.x + currentPoint.x) / 2,
    y: (effectiveStartPoint.y + currentPoint.y) / 2,
  }

  // 添加调试信息
  console.warn('[PathPreview] Calculated parameters:', {
    effectiveStartPoint,
    currentPoint,
    dx,
    dy,
    distance,
    midPoint,
  })

  // 通用样式
  const previewStroke = '#00AAFF'
  const previewStrokeWidth = 2
  const previewDashArray = '5,5'

  // 根据路径类型生成不同的预览
  switch (pathType as ElementType.LINE | ElementType.POLYLINE | ElementType.ARC | ElementType.QUADRATIC | ElementType.CUBIC) {
    case ElementType.LINE: {
      console.warn('[PathPreview] LINE preview:', {
        effectiveStartPoint,
        currentPoint,
        distance,
      })

      // 如果起点和当前点相同（距离为0），不显示预览
      if (distance === 0) {
        return null
      }

      // 直接使用当前点作为预览终点，不进行延长
      const previewEndX = currentPoint.x
      const previewEndY = currentPoint.y

      return (
        <>
          {/* 线段预览 */}
          <line
            x1={effectiveStartPoint.x}
            y1={effectiveStartPoint.y}
            x2={previewEndX}
            y2={previewEndY}
            stroke={previewStroke}
            strokeWidth={previewStrokeWidth}
            strokeDasharray={previewDashArray}
          />
          {/* 起点标记 */}
          <circle
            cx={effectiveStartPoint.x}
            cy={effectiveStartPoint.y}
            r={4}
            fill="#00AAFF"
            stroke="#FFFFFF"
            strokeWidth={1}
          />
          {/* 终点标记 */}
          <circle
            cx={previewEndX}
            cy={previewEndY}
            r={4}
            fill="#00AAFF"
            stroke="#FFFFFF"
            strokeWidth={1}
          />
        </>
      )
    }

    case ElementType.POLYLINE: {
      // 对于多线，显示已绘制的线段 + 当前预览线段
      // 确定当前线段的起点：如果有已绘制的点，使用最后一个点；否则使用effectiveStartPoint
      const currentLineStartPoint = allPoints.length > 0 ? allPoints[allPoints.length - 1] : effectiveStartPoint

      return (
        <>
          {/* 显示已绘制的线段 */}
          {allPoints.length > 1 && allPoints.map((point, index) => {
            if (index === 0) {
              return null
            } // 跳过第一个点
            const prevPoint = allPoints[index - 1]
            return (
              <line
                key={`polyline-segment-${prevPoint.x}-${prevPoint.y}-${point.x}-${point.y}`}
                x1={prevPoint.x}
                y1={prevPoint.y}
                x2={point.x}
                y2={point.y}
                stroke="#00AAFF"
                strokeWidth={2}
                opacity={0.8}
              />
            )
          })}

          {/* 当前线段预览 */}
          <line
            x1={currentLineStartPoint.x}
            y1={currentLineStartPoint.y}
            x2={currentPoint.x}
            y2={currentPoint.y}
            stroke={previewStroke}
            strokeWidth={previewStrokeWidth}
            strokeDasharray={previewDashArray}
          />

          {/* 显示所有已绘制的点 */}
          {allPoints.map(point => (
            <circle
              key={`polyline-point-${point.x}-${point.y}`}
              cx={point.x}
              cy={point.y}
              r={3}
              fill={point === allPoints[0] ? '#00AAFF' : '#FF6600'}
              stroke="#FFFFFF"
              strokeWidth={1}
            />
          ))}

          {/* 当前预览点 */}
          <circle
            cx={currentPoint.x}
            cy={currentPoint.y}
            r={4}
            fill="#FF6600"
            stroke="#FFFFFF"
            strokeWidth={1}
          />
        </>
      )
    }

    case ElementType.ARC: {
      // 三点绘制模式：第一个点是圆心，第二个点是圆弧起点，第三个点是圆弧终点
      if (clickCount === 1) {
        // 第一次点击后，显示从圆心到当前鼠标位置的直线预览（半径）
        return (
          <>
            <line
              x1={effectiveStartPoint.x}
              y1={effectiveStartPoint.y}
              x2={currentPoint.x}
              y2={currentPoint.y}
              stroke={previewStroke}
              strokeWidth={previewStrokeWidth}
              strokeDasharray={previewDashArray}
            />
            <circle
              cx={effectiveStartPoint.x}
              cy={effectiveStartPoint.y}
              r={4}
              fill="#00AAFF"
              stroke="#FFFFFF"
              strokeWidth={1}
            />
            <circle
              cx={currentPoint.x}
              cy={currentPoint.y}
              r={4}
              fill="#FF6600"
              stroke="#FFFFFF"
              strokeWidth={1}
            />
          </>
        )
      }
      else if (clickCount === 2 && allPoints.length >= 2) {
        // 第二次点击后，显示圆弧预览
        const center = allPoints[0]
        const startPoint = allPoints[1]
        const endPoint = currentPoint

        // 计算半径
        const dx1 = startPoint.x - center.x
        const dy1 = startPoint.y - center.y
        const radius = Math.sqrt(dx1 * dx1 + dy1 * dy1)

        // 计算角度
        const startAngle = Math.atan2(dy1, dx1)
        const dx2 = endPoint.x - center.x
        const dy2 = endPoint.y - center.y
        const endAngle = Math.atan2(dy2, dx2)

        // 计算扫描角度
        let sweepAngle = endAngle - startAngle
        if (sweepAngle < 0) {
          sweepAngle += 2 * Math.PI
        }

        // 生成圆弧路径
        const startX = center.x + radius * Math.cos(startAngle)
        const startY = center.y + radius * Math.sin(startAngle)
        const endX = center.x + radius * Math.cos(endAngle)
        const endY = center.y + radius * Math.sin(endAngle)

        const largeArcFlag = sweepAngle > Math.PI ? 1 : 0
        const sweepFlag = 1 // 顺时针

        const pathData = `M ${startX} ${startY} A ${radius} ${radius} 0 ${largeArcFlag} ${sweepFlag} ${endX} ${endY}`

        return (
          <>
            {/* 圆弧预览 */}
            <path
              d={pathData}
              fill="none"
              stroke={previewStroke}
              strokeWidth={previewStrokeWidth}
              strokeDasharray={previewDashArray}
            />
            {/* 圆心标记 */}
            <circle
              cx={center.x}
              cy={center.y}
              r={4}
              fill="#00AAFF"
              stroke="#FFFFFF"
              strokeWidth={1}
            />
            {/* 起点标记 */}
            <circle
              cx={startPoint.x}
              cy={startPoint.y}
              r={4}
              fill="#FF6600"
              stroke="#FFFFFF"
              strokeWidth={1}
            />
            {/* 终点预览标记 */}
            <circle
              cx={endPoint.x}
              cy={endPoint.y}
              r={4}
              fill="#FF6600"
              stroke="#FFFFFF"
              strokeWidth={1}
            />
            {/* 半径辅助线 */}
            <line
              x1={center.x}
              y1={center.y}
              x2={startPoint.x}
              y2={startPoint.y}
              stroke={previewStroke}
              strokeWidth={1}
              strokeDasharray="2,2"
              opacity={0.3}
            />
            <line
              x1={center.x}
              y1={center.y}
              x2={endPoint.x}
              y2={endPoint.y}
              stroke={previewStroke}
              strokeWidth={1}
              strokeDasharray="2,2"
              opacity={0.3}
            />
          </>
        )
      }
      else {
        // 第一次点击前或其他状态，显示简单的点标记
        return (
          <circle
            cx={currentPoint.x}
            cy={currentPoint.y}
            r={4}
            fill="#FF6600"
            stroke="#FFFFFF"
            strokeWidth={1}
          />
        )
      }
    }

    case ElementType.QUADRATIC: {
      // 三点绘制模式：起点 -> 控制点 -> 终点
      if (clickCount === 1) {
        // 第一次点击后，显示从起点到当前鼠标位置的直线预览
        return (
          <>
            <line
              x1={effectiveStartPoint.x}
              y1={effectiveStartPoint.y}
              x2={currentPoint.x}
              y2={currentPoint.y}
              stroke={previewStroke}
              strokeWidth={previewStrokeWidth}
              strokeDasharray={previewDashArray}
            />
            <circle
              cx={effectiveStartPoint.x}
              cy={effectiveStartPoint.y}
              r={4}
              fill="#00AAFF"
              stroke="#FFFFFF"
              strokeWidth={1}
            />
            <circle
              cx={currentPoint.x}
              cy={currentPoint.y}
              r={4}
              fill="#FF6600"
              stroke="#FFFFFF"
              strokeWidth={1}
            />
          </>
        )
      }
      else if (clickCount === 2 && allPoints.length >= 2) {
        // 第二次点击后，显示二次贝塞尔曲线预览
        const startPoint = allPoints[0]
        const controlPoint = allPoints[1]
        const endPoint = currentPoint

        return (
          <>
            {/* 贝塞尔曲线预览 */}
            <path
              d={`M ${startPoint.x} ${startPoint.y} Q ${controlPoint.x} ${controlPoint.y} ${endPoint.x} ${endPoint.y}`}
              fill="none"
              stroke={previewStroke}
              strokeWidth={previewStrokeWidth}
              strokeDasharray={previewDashArray}
            />
            {/* 起点标记 */}
            <circle
              cx={startPoint.x}
              cy={startPoint.y}
              r={4}
              fill="#00AAFF"
              stroke="#FFFFFF"
              strokeWidth={1}
            />
            {/* 控制点标记 */}
            <circle
              cx={controlPoint.x}
              cy={controlPoint.y}
              r={4}
              fill="#FF6600"
              stroke="#FFFFFF"
              strokeWidth={1}
            />
            {/* 终点预览标记 */}
            <circle
              cx={endPoint.x}
              cy={endPoint.y}
              r={4}
              fill="#FF6600"
              stroke="#FFFFFF"
              strokeWidth={1}
            />
            {/* 控制点辅助线 */}
            <line
              x1={startPoint.x}
              y1={startPoint.y}
              x2={controlPoint.x}
              y2={controlPoint.y}
              stroke={previewStroke}
              strokeWidth={1}
              strokeDasharray="2,2"
              opacity={0.3}
            />
            <line
              x1={controlPoint.x}
              y1={controlPoint.y}
              x2={endPoint.x}
              y2={endPoint.y}
              stroke={previewStroke}
              strokeWidth={1}
              strokeDasharray="2,2"
              opacity={0.3}
            />
          </>
        )
      }

      // 默认情况（第一次点击或计算失败）
      return (
        <>
          <line
            x1={effectiveStartPoint.x}
            y1={effectiveStartPoint.y}
            x2={currentPoint.x}
            y2={currentPoint.y}
            stroke={previewStroke}
            strokeWidth={previewStrokeWidth}
            strokeDasharray={previewDashArray}
          />
          <circle
            cx={effectiveStartPoint.x}
            cy={effectiveStartPoint.y}
            r={4}
            fill="#00AAFF"
            stroke="#FFFFFF"
            strokeWidth={1}
          />
          <circle
            cx={currentPoint.x}
            cy={currentPoint.y}
            r={4}
            fill="#FF6600"
            stroke="#FFFFFF"
            strokeWidth={1}
          />
        </>
      )
    }

    case ElementType.CUBIC: {
      // 三点绘制模式：起点 -> 第一个控制点 -> 终点（第二个控制点自动计算）
      if (clickCount === 1) {
        // 第一次点击后，显示从起点到当前鼠标位置的直线预览
        return (
          <>
            <line
              x1={effectiveStartPoint.x}
              y1={effectiveStartPoint.y}
              x2={currentPoint.x}
              y2={currentPoint.y}
              stroke={previewStroke}
              strokeWidth={previewStrokeWidth}
              strokeDasharray={previewDashArray}
            />
            <circle
              cx={effectiveStartPoint.x}
              cy={effectiveStartPoint.y}
              r={4}
              fill="#00AAFF"
              stroke="#FFFFFF"
              strokeWidth={1}
            />
            <circle
              cx={currentPoint.x}
              cy={currentPoint.y}
              r={4}
              fill="#FF6600"
              stroke="#FFFFFF"
              strokeWidth={1}
            />
          </>
        )
      }
      else if (clickCount === 2 && allPoints.length >= 2) {
        // 第二次点击后，显示三次贝塞尔曲线预览
        const startPoint = allPoints[0]
        const control1Point = allPoints[1]
        const endPoint = currentPoint

        // 自动计算第二个控制点：使其与第一个控制点关于起点-终点连线对称
        const midX = (startPoint.x + endPoint.x) / 2
        const midY = (startPoint.y + endPoint.y) / 2
        const control2X = midX + (midX - control1Point.x)
        const control2Y = midY + (midY - control1Point.y)

        return (
          <>
            {/* 贝塞尔曲线预览 */}
            <path
              d={`M ${startPoint.x} ${startPoint.y} C ${control1Point.x} ${control1Point.y} ${control2X} ${control2Y} ${endPoint.x} ${endPoint.y}`}
              fill="none"
              stroke={previewStroke}
              strokeWidth={previewStrokeWidth}
              strokeDasharray={previewDashArray}
            />
            {/* 起点标记 */}
            <circle
              cx={startPoint.x}
              cy={startPoint.y}
              r={4}
              fill="#00AAFF"
              stroke="#FFFFFF"
              strokeWidth={1}
            />
            {/* 第一个控制点标记 */}
            <circle
              cx={control1Point.x}
              cy={control1Point.y}
              r={4}
              fill="#FF6600"
              stroke="#FFFFFF"
              strokeWidth={1}
            />
            {/* 终点预览标记 */}
            <circle
              cx={endPoint.x}
              cy={endPoint.y}
              r={4}
              fill="#FF6600"
              stroke="#FFFFFF"
              strokeWidth={1}
            />
            {/* 第二个控制点标记（自动计算） */}
            <circle
              cx={control2X}
              cy={control2Y}
              r={3}
              fill="#FFAA00"
              stroke="#FFFFFF"
              strokeWidth={1}
              opacity={0.7}
            />
            {/* 控制点辅助线 */}
            <line
              x1={startPoint.x}
              y1={startPoint.y}
              x2={control1Point.x}
              y2={control1Point.y}
              stroke={previewStroke}
              strokeWidth={1}
              strokeDasharray="2,2"
              opacity={0.3}
            />
            <line
              x1={control2X}
              y1={control2Y}
              x2={endPoint.x}
              y2={endPoint.y}
              stroke={previewStroke}
              strokeWidth={1}
              strokeDasharray="2,2"
              opacity={0.3}
            />
          </>
        )
      }

      // 默认情况（第一次点击或计算失败）
      return (
        <>
          <line
            x1={effectiveStartPoint.x}
            y1={effectiveStartPoint.y}
            x2={currentPoint.x}
            y2={currentPoint.y}
            stroke={previewStroke}
            strokeWidth={previewStrokeWidth}
            strokeDasharray={previewDashArray}
          />
          <circle
            cx={effectiveStartPoint.x}
            cy={effectiveStartPoint.y}
            r={4}
            fill="#00AAFF"
            stroke="#FFFFFF"
            strokeWidth={1}
          />
          <circle
            cx={currentPoint.x}
            cy={currentPoint.y}
            r={4}
            fill="#FF6600"
            stroke="#FFFFFF"
            strokeWidth={1}
          />
        </>
      )
    }

    default:
      // This component only handles path-related element types
      // Other element types should not reach this switch statement
      console.warn('[PathPreview] Unhandled path type:', pathType)
      return null
  }
}

export default PathPreview
