import type {
  AreaCalculatorStrategy,
  BoundingBoxCalculatorStrategy,
  CostCalculatorStrategy,
  DistanceCalculatorStrategy,
  MaterialCalculatorStrategy,
  PerimeterCalculatorStrategy,
  SpacePlanningStrategy,
} from '@/types/core/compute'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { StrategyRegistry } from '@/core/compute/StrategyRegistry'
import { ElementType } from '@/types/core/elementDefinitions'

// Mock Strategies
const mockAreaStrategy: AreaCalculatorStrategy = {
  getElementType: () => ElementType.RECTANGLE,
  calculateArea: vi.fn().mockReturnValue(100),
}
const mockPerimeterStrategy: PerimeterCalculatorStrategy = {
  getElementType: () => ElementType.RECTANGLE,
  calculatePerimeter: vi.fn().mockReturnValue(40),
}
const mockBoundingBoxStrategy: BoundingBoxCalculatorStrategy = {
  getElementType: () => ElementType.RECTANGLE,
  calculateBoundingBox: vi.fn().mockReturnValue({ x: 0, y: 0, width: 10, height: 10 }),
}
const mockCircleAreaStrategy: AreaCalculatorStrategy = {
  getElementType: () => ElementType.CIRCLE,
  calculateArea: vi.fn().mockReturnValue(314),
}

const mockDistanceStrategy: DistanceCalculatorStrategy = {
  getElementType: () => ElementType.LINE,
  calculateDistance: vi.fn().mockReturnValue(10),
}

const mockCostStrategy: CostCalculatorStrategy = {
  getElementType: () => ElementType.RECTANGLE,
  calculateCost: vi.fn().mockReturnValue(50),
}

const mockMaterialStrategy: MaterialCalculatorStrategy = {
  getElementType: () => ElementType.RECTANGLE,
  calculateMaterial: vi.fn().mockReturnValue({ type: 'wood', amount: 5 }),
}

const mockSpacePlanningStrategy: SpacePlanningStrategy = {
  getSpaceType: () => 'kitchen',
  planSpace: vi.fn().mockReturnValue({ layout: 'optimal' }),
}

const mockMultiTypeAreaStrategy: AreaCalculatorStrategy = {
  getElementType: () => [ElementType.RECTANGLE, ElementType.SQUARE],
  calculateArea: vi.fn().mockReturnValue(100),
}

describe('strategyRegistry', () => {
  let registry: StrategyRegistry
  let consoleSpy: any

  beforeEach(() => {
    registry = new StrategyRegistry()
    // Reset mocks before each test if needed, though registry is new each time
    vi.clearAllMocks()
    // Spy on console methods
    consoleSpy = {
      log: vi.spyOn(console, 'log').mockImplementation(() => {}),
      warn: vi.spyOn(console, 'warn').mockImplementation(() => {}),
      error: vi.spyOn(console, 'error').mockImplementation(() => {}),
      debug: vi.spyOn(console, 'debug').mockImplementation(() => {}),
    }
  })

  it('should initialize with empty strategy maps', () => {
    expect(registry.getSupportedElementTypes('area')).toEqual([])
    expect(registry.getSupportedElementTypes('perimeter')).toEqual([])
    expect(registry.getSupportedElementTypes('boundingBox')).toEqual([])
    // 已删除 isPointInside 策略相关代码
  })

  // Test Area Strategy
  describe('area Strategy', () => {
    it('should register and retrieve an area strategy', () => {
      registry.registerAreaStrategy(mockAreaStrategy)
      expect(registry.getAreaStrategy('rectangle')).toBe(mockAreaStrategy)
      expect(registry.hasStrategy('rectangle', 'area')).toBe(true)
    })

    it('should handle case-insensitivity for retrieval', () => {
      registry.registerAreaStrategy(mockAreaStrategy)
      expect(registry.getAreaStrategy('RECTANGLE')).toBe(mockAreaStrategy)
      expect(registry.hasStrategy('RECTANGLE', 'area')).toBe(true)
    })

    it('should throw an error if no area strategy is found', () => {
      expect(() => registry.getAreaStrategy('unknown')).toThrow('No area calculation strategy found for element type: unknown')
      expect(registry.hasStrategy('unknown', 'area')).toBe(false)
    })

    it('should overwrite existing strategy for the same type', () => {
      const newMockAreaStrategy: AreaCalculatorStrategy = {
        getElementType: () => ElementType.RECTANGLE,
        calculateArea: vi.fn().mockReturnValue(200),
      }
      registry.registerAreaStrategy(mockAreaStrategy)
      registry.registerAreaStrategy(newMockAreaStrategy)
      expect(registry.getAreaStrategy('rectangle')).toBe(newMockAreaStrategy)
    })
  })

  // Test Perimeter Strategy (similar tests)
  describe('perimeter Strategy', () => {
    it('should register and retrieve a perimeter strategy', () => {
      registry.registerPerimeterStrategy(mockPerimeterStrategy)
      expect(registry.getPerimeterStrategy('rectangle')).toBe(mockPerimeterStrategy)
      expect(registry.hasStrategy('rectangle', 'perimeter')).toBe(true)
    })

    it('should throw an error if no perimeter strategy is found', () => {
      expect(() => registry.getPerimeterStrategy('unknown')).toThrow('No perimeter calculation strategy found for element type: unknown')
      expect(registry.hasStrategy('unknown', 'perimeter')).toBe(false)
    })
  })

  // Test BoundingBox Strategy (similar tests)
  describe('boundingBox Strategy', () => {
    it('should register and retrieve a bounding box strategy', () => {
      registry.registerBoundingBoxStrategy(mockBoundingBoxStrategy)
      expect(registry.getBoundingBoxStrategy('rectangle')).toBe(mockBoundingBoxStrategy)
      expect(registry.hasStrategy('rectangle', 'boundingBox')).toBe(true)
    })

    it('should throw an error if no bounding box strategy is found', () => {
      expect(() => registry.getBoundingBoxStrategy('unknown')).toThrow('No bounding box calculation strategy found for element type: unknown')
      expect(registry.hasStrategy('unknown', 'boundingBox')).toBe(false)
    })
  })

  // 已删除 IsPointInside 和 Transform 策略相关代码

  // Test getSupportedElementTypes
  describe('getSupportedElementTypes', () => {
    it('should return an empty array initially', () => {
      expect(registry.getSupportedElementTypes('area')).toEqual([])
    })

    it('should return registered shape types for a specific calculation type', () => {
      registry.registerAreaStrategy(mockAreaStrategy)
      registry.registerAreaStrategy(mockCircleAreaStrategy)
      registry.registerPerimeterStrategy(mockPerimeterStrategy) // Different type

      const areaTypes = registry.getSupportedElementTypes('area')
      expect(areaTypes).toHaveLength(2)
      expect(areaTypes).toContain('rectangle')
      expect(areaTypes).toContain('circle')

      expect(registry.getSupportedElementTypes('perimeter')).toEqual(['rectangle'])
    })
  })

  // Test registerStrategies (optional but good)
  describe('registerStrategies', () => {
    it('should register all strategy types for a shape', () => {
      registry.registerStrategies(
        mockAreaStrategy,
        mockPerimeterStrategy,
        mockBoundingBoxStrategy,
      )
      expect(registry.hasStrategy('rectangle', 'area')).toBe(true)
      expect(registry.hasStrategy('rectangle', 'perimeter')).toBe(true)
      expect(registry.hasStrategy('rectangle', 'boundingBox')).toBe(true)
    })
  })

  // 已删除弃用的方法测试

  // Test Distance Strategy
  describe('distance Strategy', () => {
    it('should register and retrieve distance strategy', () => {
      registry.registerDistanceStrategy(mockDistanceStrategy)
      expect(registry.getDistanceStrategy('line')).toBe(mockDistanceStrategy)
      expect(registry.hasStrategy('line', 'distance')).toBe(true)
    })

    it('should throw error when distance strategy not found', () => {
      expect(() => registry.getDistanceStrategy('unknown')).toThrow(
        'No distance calculation strategy found for element type: unknown',
      )
    })
  })

  // Test Cost Strategy
  describe('cost Strategy', () => {
    it('should register and retrieve cost strategy', () => {
      registry.registerCostStrategy(mockCostStrategy)
      expect(registry.getCostStrategy('rectangle')).toBe(mockCostStrategy)
      expect(registry.hasStrategy('rectangle', 'cost')).toBe(true)
    })

    it('should throw error when cost strategy not found', () => {
      expect(() => registry.getCostStrategy('unknown')).toThrow(
        'No cost calculation strategy found for element type: unknown',
      )
    })
  })

  // Test Material Strategy
  describe('material Strategy', () => {
    it('should register and retrieve material strategy', () => {
      registry.registerMaterialStrategy(mockMaterialStrategy)
      expect(registry.getMaterialStrategy('rectangle')).toBe(mockMaterialStrategy)
      expect(registry.hasStrategy('rectangle', 'material')).toBe(true)
    })

    it('should throw error when material strategy not found', () => {
      expect(() => registry.getMaterialStrategy('unknown')).toThrow(
        'No material calculation strategy found for element type: unknown',
      )
    })
  })

  // Test Space Planning Strategy
  describe('spacePlanning Strategy', () => {
    it('should register and retrieve space planning strategy', () => {
      registry.registerSpacePlanningStrategy(mockSpacePlanningStrategy)
      expect(registry.getSpacePlanningStrategy('kitchen')).toBe(mockSpacePlanningStrategy)
      expect(registry.hasStrategy('kitchen', 'spacePlanning')).toBe(true)
    })

    it('should handle case-insensitive space type lookup', () => {
      registry.registerSpacePlanningStrategy(mockSpacePlanningStrategy)
      expect(registry.getSpacePlanningStrategy('KITCHEN')).toBe(mockSpacePlanningStrategy)
      expect(registry.getSpacePlanningStrategy('Kitchen')).toBe(mockSpacePlanningStrategy)
    })

    it('should throw error when space planning strategy not found', () => {
      expect(() => registry.getSpacePlanningStrategy('unknown')).toThrow(
        'No space planning strategy found for space type: unknown',
      )
    })
  })

  // Test Multi-type Strategy Registration
  describe('multi-type strategy registration', () => {
    it('should register strategy for multiple element types', () => {
      registry.registerAreaStrategy(mockMultiTypeAreaStrategy)
      expect(registry.getAreaStrategy('rectangle')).toBe(mockMultiTypeAreaStrategy)
      expect(registry.getAreaStrategy('square')).toBe(mockMultiTypeAreaStrategy)
      expect(registry.hasStrategy('rectangle', 'area')).toBe(true)
      expect(registry.hasStrategy('square', 'area')).toBe(true)
    })
  })

  // Test edge cases for hasStrategy and getSupportedElementTypes
  describe('edge Cases', () => {
    it('should handle unknown calculation type in hasStrategy', () => {
      // @ts-ignore - Testing runtime behavior with invalid input
      const result = registry.hasStrategy('rectangle', 'unknown')
      expect(result).toBe(false)
      expect(consoleSpy.warn).toHaveBeenCalledWith('[StrategyRegistry] Unknown calculation type encountered in hasStrategy: unknown')
    })

    it('should handle unknown calculation type in getSupportedElementTypes', () => {
      // @ts-ignore - Testing runtime behavior with invalid input
      const result = registry.getSupportedElementTypes('unknown')
      expect(result).toEqual([])
      expect(consoleSpy.warn).toHaveBeenCalledWith('[StrategyRegistry] Unknown calculation type encountered in getSupportedElementTypes: unknown')
    })
  })
})
