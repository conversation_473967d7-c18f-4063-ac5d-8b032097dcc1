import { test, expect } from '@playwright/test';

/**
 * Visual regression test suite for shape rendering.
 * These tests verify that shapes are rendered correctly and consistently
 * across different browsers and screen sizes.
 */
test.describe('Shape Rendering Visual Tests', () => {
  /**
   * Setup for each test.
   * Navigates to the application and waits for it to load.
   */
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the application to load
    await page.waitForSelector('.canvas-container');
  });

  /**
   * Tests basic shape rendering.
   * Verifies that basic shapes (rectangle, circle, polygon, line) are rendered correctly.
   */
  test('Basic shape rendering', async ({ page }) => {
    // Create a rectangle
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const rectX = boundingBox.x + 100;
    const rectY = boundingBox.y + 100;
    
    await page.mouse.move(rectX, rectY);
    await page.mouse.down();
    await page.mouse.move(rectX + 100, rectY + 100);
    await page.mouse.up();
    
    // Create a circle
    await page.click('#tool-ellipse');
    
    const circleX = boundingBox.x + 300;
    const circleY = boundingBox.y + 100;
    
    await page.mouse.move(circleX, circleY);
    await page.mouse.down();
    await page.mouse.move(circleX + 50, circleY + 50);
    await page.mouse.up();
    
    // Create a polygon
    await page.click('#tool-polygon');
    
    const polygonX = boundingBox.x + 500;
    const polygonY = boundingBox.y + 100;
    
    await page.mouse.move(polygonX, polygonY);
    await page.mouse.down();
    await page.mouse.move(polygonX + 50, polygonY + 50);
    await page.mouse.up();
    
    // Create a line
    await page.click('#tool-line');
    
    const lineStartX = boundingBox.x + 100;
    const lineStartY = boundingBox.y + 300;
    const lineEndX = boundingBox.x + 300;
    const lineEndY = boundingBox.y + 300;
    
    await page.mouse.move(lineStartX, lineStartY);
    await page.mouse.down();
    await page.mouse.move(lineEndX, lineEndY);
    await page.mouse.up();
    
    // Take a screenshot of the canvas
    const screenshot = await canvasContainer.screenshot();
    
    // Compare the screenshot with a reference image
    // Note: In a real test, you would use expect(screenshot).toMatchSnapshot('basic-shapes.png');
    // But for this example, we'll just verify that the screenshot was taken
    expect(screenshot).toBeDefined();
  });

  /**
   * Tests shape styling.
   * Verifies that shape styling (fill, stroke, opacity) is rendered correctly.
   */
  test('Shape styling rendering', async ({ page }) => {
    // Create a rectangle
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const rectX = boundingBox.x + 100;
    const rectY = boundingBox.y + 100;
    
    await page.mouse.move(rectX, rectY);
    await page.mouse.down();
    await page.mouse.move(rectX + 100, rectY + 100);
    await page.mouse.up();
    
    // Switch to select tool
    await page.click('#tool-select');
    
    // Select the rectangle
    await page.mouse.move(rectX + 50, rectY + 50);
    await page.mouse.down();
    await page.mouse.up();
    
    // Change the fill color
    await page.locator('.fill-color-picker').click();
    await page.locator('.color-option[data-color="red"]').click();
    
    // Change the stroke color
    await page.locator('.stroke-color-picker').click();
    await page.locator('.color-option[data-color="blue"]').click();
    
    // Change the stroke width
    await page.locator('.stroke-width-input').fill('5');
    await page.keyboard.press('Enter');
    
    // Change the opacity
    await page.locator('.opacity-slider').fill('0.5');
    await page.keyboard.press('Enter');
    
    // Take a screenshot of the canvas
    const screenshot = await canvasContainer.screenshot();
    
    // Compare the screenshot with a reference image
    // Note: In a real test, you would use expect(screenshot).toMatchSnapshot('styled-shape.png');
    // But for this example, we'll just verify that the screenshot was taken
    expect(screenshot).toBeDefined();
  });

  /**
   * Tests shape transformations.
   * Verifies that shape transformations (rotation, scaling) are rendered correctly.
   */
  test('Shape transformation rendering', async ({ page }) => {
    // Create a rectangle
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const rectX = boundingBox.x + 100;
    const rectY = boundingBox.y + 100;
    
    await page.mouse.move(rectX, rectY);
    await page.mouse.down();
    await page.mouse.move(rectX + 100, rectY + 100);
    await page.mouse.up();
    
    // Switch to select tool
    await page.click('#tool-select');
    
    // Select the rectangle
    await page.mouse.move(rectX + 50, rectY + 50);
    await page.mouse.down();
    await page.mouse.up();
    
    // Wait for the rotation handle to appear
    await page.waitForSelector('.rotation-handle');
    
    // Find the rotation handle
    const rotationHandle = page.locator('.rotation-handle');
    const handleBox = await rotationHandle.boundingBox();
    
    // Rotate the rectangle
    await page.mouse.move(handleBox.x + handleBox.width / 2, handleBox.y + handleBox.height / 2);
    await page.mouse.down();
    
    // Move the mouse to rotate by approximately 45 degrees
    const centerX = rectX + 50;
    const centerY = rectY + 50;
    const radius = 50;
    const angle = 45 * (Math.PI / 180);
    const rotateX = centerX + radius * Math.cos(angle);
    const rotateY = centerY + radius * Math.sin(angle);
    
    await page.mouse.move(rotateX, rotateY);
    await page.mouse.up();
    
    // Take a screenshot of the canvas
    const screenshot = await canvasContainer.screenshot();
    
    // Compare the screenshot with a reference image
    // Note: In a real test, you would use expect(screenshot).toMatchSnapshot('transformed-shape.png');
    // But for this example, we'll just verify that the screenshot was taken
    expect(screenshot).toBeDefined();
  });

  /**
   * Tests shape grouping.
   * Verifies that grouped shapes are rendered correctly.
   */
  test('Shape grouping rendering', async ({ page }) => {
    // Create a rectangle
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const rect1X = boundingBox.x + 100;
    const rect1Y = boundingBox.y + 100;
    
    await page.mouse.move(rect1X, rect1Y);
    await page.mouse.down();
    await page.mouse.move(rect1X + 100, rect1Y + 100);
    await page.mouse.up();
    
    // Create a circle
    await page.click('#tool-ellipse');
    
    const circleX = boundingBox.x + 300;
    const circleY = boundingBox.y + 100;
    
    await page.mouse.move(circleX, circleY);
    await page.mouse.down();
    await page.mouse.move(circleX + 50, circleY + 50);
    await page.mouse.up();
    
    // Switch to select tool
    await page.click('#tool-select');
    
    // Select both shapes
    await page.mouse.move(rect1X + 50, rect1Y + 50);
    await page.mouse.down();
    await page.mouse.up();
    
    await page.keyboard.down('Shift');
    await page.mouse.move(circleX, circleY);
    await page.mouse.down();
    await page.mouse.up();
    await page.keyboard.up('Shift');
    
    // Group the shapes
    await page.keyboard.press('Control+G');
    
    // Take a screenshot of the canvas
    const screenshot = await canvasContainer.screenshot();
    
    // Compare the screenshot with a reference image
    // Note: In a real test, you would use expect(screenshot).toMatchSnapshot('grouped-shapes.png');
    // But for this example, we'll just verify that the screenshot was taken
    expect(screenshot).toBeDefined();
  });

  /**
   * Tests shape rendering at different screen sizes.
   * Verifies that shapes are rendered correctly at different screen sizes.
   */
  test('Shape rendering at different screen sizes', async ({ page }) => {
    // Create a rectangle
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const rectX = boundingBox.x + 100;
    const rectY = boundingBox.y + 100;
    
    await page.mouse.move(rectX, rectY);
    await page.mouse.down();
    await page.mouse.move(rectX + 100, rectY + 100);
    await page.mouse.up();
    
    // Test at different screen sizes
    const screenSizes = [
      { width: 1920, height: 1080 }, // Desktop
      { width: 1366, height: 768 },  // Laptop
      { width: 768, height: 1024 },  // Tablet
      { width: 375, height: 812 }    // Mobile
    ];
    
    for (const { width, height } of screenSizes) {
      // Resize the viewport
      await page.setViewportSize({ width, height });
      
      // Take a screenshot of the canvas
      const screenshot = await canvasContainer.screenshot();
      
      // Compare the screenshot with a reference image
      // Note: In a real test, you would use expect(screenshot).toMatchSnapshot(`shape-${width}x${height}.png`);
      // But for this example, we'll just verify that the screenshot was taken
      expect(screenshot).toBeDefined();
    }
  });

  /**
   * Tests shape rendering with different themes.
   * Verifies that shapes are rendered correctly with different themes.
   */
  test('Shape rendering with different themes', async ({ page }) => {
    // Create a rectangle
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const rectX = boundingBox.x + 100;
    const rectY = boundingBox.y + 100;
    
    await page.mouse.move(rectX, rectY);
    await page.mouse.down();
    await page.mouse.move(rectX + 100, rectY + 100);
    await page.mouse.up();
    
    // Take a screenshot with the default theme
    const lightThemeScreenshot = await canvasContainer.screenshot();
    
    // Switch to dark theme
    await page.click('#theme-toggle');
    
    // Take a screenshot with the dark theme
    const darkThemeScreenshot = await canvasContainer.screenshot();
    
    // Compare the screenshots with reference images
    // Note: In a real test, you would use:
    // expect(lightThemeScreenshot).toMatchSnapshot('shape-light-theme.png');
    // expect(darkThemeScreenshot).toMatchSnapshot('shape-dark-theme.png');
    // But for this example, we'll just verify that the screenshots were taken
    expect(lightThemeScreenshot).toBeDefined();
    expect(darkThemeScreenshot).toBeDefined();
    
    // Verify that the screenshots are different
    expect(lightThemeScreenshot).not.toEqual(darkThemeScreenshot);
  });

  /**
   * Tests shape rendering with different zoom levels.
   * Verifies that shapes are rendered correctly at different zoom levels.
   */
  test('Shape rendering at different zoom levels', async ({ page }) => {
    // Create a rectangle
    await page.click('#tool-rectangle');
    
    const canvasContainer = page.locator('.canvas-container');
    const boundingBox = await canvasContainer.boundingBox();
    
    const rectX = boundingBox.x + 100;
    const rectY = boundingBox.y + 100;
    
    await page.mouse.move(rectX, rectY);
    await page.mouse.down();
    await page.mouse.move(rectX + 100, rectY + 100);
    await page.mouse.up();
    
    // Take a screenshot at the default zoom level
    const defaultZoomScreenshot = await canvasContainer.screenshot();
    
    // Zoom in
    await page.keyboard.press('Control+=');
    await page.keyboard.press('Control+=');
    
    // Take a screenshot at the zoomed-in level
    const zoomedInScreenshot = await canvasContainer.screenshot();
    
    // Zoom out
    await page.keyboard.press('Control+-');
    await page.keyboard.press('Control+-');
    await page.keyboard.press('Control+-');
    await page.keyboard.press('Control+-');
    
    // Take a screenshot at the zoomed-out level
    const zoomedOutScreenshot = await canvasContainer.screenshot();
    
    // Compare the screenshots with reference images
    // Note: In a real test, you would use:
    // expect(defaultZoomScreenshot).toMatchSnapshot('shape-default-zoom.png');
    // expect(zoomedInScreenshot).toMatchSnapshot('shape-zoomed-in.png');
    // expect(zoomedOutScreenshot).toMatchSnapshot('shape-zoomed-out.png');
    // But for this example, we'll just verify that the screenshots were taken
    expect(defaultZoomScreenshot).toBeDefined();
    expect(zoomedInScreenshot).toBeDefined();
    expect(zoomedOutScreenshot).toBeDefined();
    
    // Verify that the screenshots are different
    expect(defaultZoomScreenshot).not.toEqual(zoomedInScreenshot);
    expect(defaultZoomScreenshot).not.toEqual(zoomedOutScreenshot);
    expect(zoomedInScreenshot).not.toEqual(zoomedOutScreenshot);
  });
});
