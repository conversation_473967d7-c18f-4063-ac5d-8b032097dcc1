import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ArcPerimeterStrategy } from '@/core/compute/strategies/perimeter/ArcPerimeterStrategy'
import { CoreError } from '@/services/system/error-service'
import { ElementType } from '@/types/core/elementDefinitions'

// Mock the utility functions
vi.mock('@/lib/utils/geometry/arcUtils', () => ({
  calculateArcLength: vi.fn((radius: number, angleRad: number) => {
    // Simple mock implementation: arc length = radius * angle
    return radius * angleRad
  }),
}))

vi.mock('@/lib/utils/math', () => ({
  toRadians: vi.fn((degrees: number) => degrees * Math.PI / 180),
}))

describe('arcPerimeterStrategy', () => {
  let strategy: ArcPerimeterStrategy

  beforeEach(() => {
    strategy = new ArcPerimeterStrategy()
    vi.clearAllMocks()
  })

  describe('constructor and Basic Properties', () => {
    it('should be defined and instantiated', () => {
      expect(strategy).toBeDefined()
      expect(strategy).toBeInstanceOf(ArcPerimeterStrategy)
    })

    it('should return correct element type', () => {
      expect(strategy.getElementType()).toBe(ElementType.ARC)
    })

    it('should have required methods', () => {
      expect(typeof strategy.calculatePerimeter).toBe('function')
      expect(typeof strategy.getElementType).toBe('function')
    })
  })

  describe('perimeter Calculation', () => {
    it('should calculate perimeter for open arc correctly', () => {
      const arcElement = {
        id: 'arc-1',
        type: ElementType.ARC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          radius: 10,
          startAngle: 0,
          endAngle: 90,
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(arcElement as any)

      // Expected: arc length only = radius * angle = 10 * (π/2) ≈ 15.71
      expect(perimeter).toBeCloseTo(15.71, 2)
    })

    it('should calculate perimeter for closed arc correctly', () => {
      const arcElement = {
        id: 'arc-2',
        type: ElementType.ARC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          radius: 10,
          startAngle: 0,
          endAngle: 90,
          closed: true,
        },
      }

      const perimeter = strategy.calculatePerimeter(arcElement as any)

      // Expected: arc length + 2 * radius = 10 * (π/2) + 2 * 10 ≈ 35.71
      expect(perimeter).toBeCloseTo(35.71, 2)
    })

    it('should calculate perimeter for full circle arc', () => {
      const arcElement = {
        id: 'arc-full',
        type: ElementType.ARC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          radius: 5,
          startAngle: 0,
          endAngle: 360,
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(arcElement as any)

      // Expected: arc length = radius * 2π = 5 * 2π ≈ 31.42
      expect(perimeter).toBeCloseTo(31.42, 2)
    })

    it('should handle negative angle differences', () => {
      const arcElement = {
        id: 'arc-negative',
        type: ElementType.ARC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          radius: 8,
          startAngle: 270,
          endAngle: 90,
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(arcElement as any)
      expect(perimeter).toBeGreaterThan(0)
    })

    it('should handle small arc angles', () => {
      const arcElement = {
        id: 'arc-small',
        type: ElementType.ARC,
        position: { x: 0, y: 0, z: 0 },
        properties: {
          radius: 15,
          startAngle: 0,
          endAngle: 1,
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(arcElement as any)
      expect(perimeter).toBeGreaterThan(0)
      expect(perimeter).toBeLessThan(1)
    })

    it('should handle missing closed property (defaults to false)', () => {
      const arcElement = {
        id: 'arc-no-closed',
        type: ElementType.ARC,
        properties: {
          radius: 10,
          startAngle: 0,
          endAngle: 90,
          // closed property missing
        },
      }

      const perimeter = strategy.calculatePerimeter(arcElement as any)

      // Should be treated as open arc
      expect(perimeter).toBeCloseTo(15.71, 2)
    })
  })

  describe('error Handling', () => {
    it('should throw error for non-arc element', () => {
      const rectangleElement = {
        id: 'rect-1',
        type: ElementType.RECTANGLE,
        properties: {},
      }

      expect(() => strategy.calculatePerimeter(rectangleElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculatePerimeter(rectangleElement as any))
        .toThrow('ArcPerimeterStrategy can only calculate perimeter for ARC elements')
    })

    it('should throw error for invalid radius', () => {
      const arcElement = {
        id: 'arc-invalid-radius',
        type: ElementType.ARC,
        properties: {
          radius: 0,
          startAngle: 0,
          endAngle: 90,
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(arcElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculatePerimeter(arcElement as any))
        .toThrow('Invalid radius')
    })

    it('should throw error for negative radius', () => {
      const arcElement = {
        id: 'arc-negative-radius',
        type: ElementType.ARC,
        properties: {
          radius: -5,
          startAngle: 0,
          endAngle: 90,
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(arcElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for infinite radius', () => {
      const arcElement = {
        id: 'arc-infinite-radius',
        type: ElementType.ARC,
        properties: {
          radius: Infinity,
          startAngle: 0,
          endAngle: 90,
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(arcElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for NaN radius', () => {
      const arcElement = {
        id: 'arc-nan-radius',
        type: ElementType.ARC,
        properties: {
          radius: Number.NaN,
          startAngle: 0,
          endAngle: 90,
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(arcElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for invalid start angle', () => {
      const arcElement = {
        id: 'arc-invalid-start',
        type: ElementType.ARC,
        properties: {
          radius: 10,
          startAngle: Number.NaN,
          endAngle: 90,
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(arcElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculatePerimeter(arcElement as any))
        .toThrow('Invalid angles')
    })

    it('should throw error for invalid end angle', () => {
      const arcElement = {
        id: 'arc-invalid-end',
        type: ElementType.ARC,
        properties: {
          radius: 10,
          startAngle: 0,
          endAngle: Infinity,
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(arcElement as any))
        .toThrow(CoreError)
    })

    it('should throw error for non-numeric angles', () => {
      const arcElement = {
        id: 'arc-string-angle',
        type: ElementType.ARC,
        properties: {
          radius: 10,
          startAngle: '0' as any,
          endAngle: 90,
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(arcElement as any))
        .toThrow(CoreError)
    })

    it('should throw error when arc length calculation returns NaN', () => {
      const { calculateArcLength } = require('@/lib/utils/geometry/arcUtils')
      calculateArcLength.mockReturnValue(Number.NaN)

      const arcElement = {
        id: 'arc-nan-result',
        type: ElementType.ARC,
        properties: {
          radius: 10,
          startAngle: 0,
          endAngle: 90,
          closed: false,
        },
      }

      expect(() => strategy.calculatePerimeter(arcElement as any))
        .toThrow(CoreError)
      expect(() => strategy.calculatePerimeter(arcElement as any))
        .toThrow('resulted in invalid arc length')
    })
  })

  describe('edge Cases', () => {
    it('should handle very large radius', () => {
      const arcElement = {
        id: 'arc-large-radius',
        type: ElementType.ARC,
        properties: {
          radius: 1000000,
          startAngle: 0,
          endAngle: 1,
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(arcElement as any)
      expect(perimeter).toBeGreaterThan(0)
      expect(Number.isFinite(perimeter)).toBe(true)
    })

    it('should handle very small radius', () => {
      const arcElement = {
        id: 'arc-small-radius',
        type: ElementType.ARC,
        properties: {
          radius: 0.001,
          startAngle: 0,
          endAngle: 90,
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(arcElement as any)
      expect(perimeter).toBeGreaterThan(0)
      expect(perimeter).toBeLessThan(0.01)
    })

    it('should handle zero angle difference', () => {
      const arcElement = {
        id: 'arc-zero-angle',
        type: ElementType.ARC,
        properties: {
          radius: 10,
          startAngle: 45,
          endAngle: 45,
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(arcElement as any)
      expect(perimeter).toBe(0)
    })

    it('should handle angles greater than 360 degrees', () => {
      const arcElement = {
        id: 'arc-large-angle',
        type: ElementType.ARC,
        properties: {
          radius: 5,
          startAngle: 0,
          endAngle: 450, // 1.25 full rotations
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(arcElement as any)
      expect(perimeter).toBeGreaterThan(0)
    })

    it('should handle negative angles', () => {
      const arcElement = {
        id: 'arc-negative-angles',
        type: ElementType.ARC,
        properties: {
          radius: 10,
          startAngle: -90,
          endAngle: -45,
          closed: false,
        },
      }

      const perimeter = strategy.calculatePerimeter(arcElement as any)
      expect(perimeter).toBeGreaterThan(0)
    })
  })
})
