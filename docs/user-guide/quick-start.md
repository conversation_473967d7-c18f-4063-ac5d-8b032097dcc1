# Quick Start Guide

Welcome to RenoPilot.JS.Shapes2! This guide will help you quickly get started with the basic functionalities of the application.

## 1. Accessing the Application

First, ensure you have access to the application. As mentioned in the [Installation Guide](./installation.md), this usually means opening a specific URL in your web browser.

*   **Public URL:** `https://your-renopilot-instance.com` (Replace with the actual URL if deployed)
*   **Local Development URL:** `http://localhost:5173` (If running locally)

## 2. Understanding the Interface

Once the application loads, you'll see the main interface. It typically consists of:

*   **Canvas Area:** The central part of the screen where you can draw and manipulate shapes. This is your primary workspace.
*   **Toolbar:** Usually located at the top or side, containing tools for selecting shapes, drawing different types of shapes (e.g., rectangles, circles, lines), and other actions.
*   **Properties Panel:** (If applicable) A panel that displays and allows you to edit the properties of the selected shape (e.g., color, size, position).
*   **Menu Bar/Options:** May include options for file operations (New, Save, Load - if implemented), undo/redo, zoom, etc.

## 3. Basic Operations

Here's how to perform common tasks:

### Drawing a Shape

1.  **Select a Shape Tool:** Click on a shape tool (e.g., Rectangle tool, Circle tool) from the toolbar.
2.  **Draw on the Canvas:**
    *   Click and drag on the canvas area to define the size and position of the shape.
    *   Release the mouse button to place the shape.

### Selecting a Shape

1.  **Select the Selection Tool:** Click on the selection tool (often an arrow icon) from the toolbar.
2.  **Click on a Shape:** Click on any shape on the canvas to select it. The selected shape might show handles or a bounding box.

### Moving a Shape

1.  **Select the Shape:** Ensure the shape you want to move is selected.
2.  **Drag and Drop:** Click and hold the selected shape, then drag it to a new position on the canvas. Release the mouse button.

### Resizing a Shape

1.  **Select the Shape:** Ensure the shape you want to resize is selected.
2.  **Use Resize Handles:** Click and drag the resize handles (small squares or circles on the border of the selected shape) to change its dimensions.

### Changing Shape Properties (e.g., Color)

1.  **Select the Shape:** Select the shape whose properties you want to change.
2.  **Use the Properties Panel:** Look for options in the Properties Panel (or a color palette in the toolbar) to change attributes like fill color, stroke color, etc.

### Deleting a Shape

1.  **Select the Shape:** Select the shape you want to delete.
2.  **Press the Delete Key:** Press the `Delete` or `Backspace` key on your keyboard.
    *   Alternatively, there might be a delete button or option in the toolbar or context menu.

### Undo and Redo

*   **Undo:** To reverse your last action, look for an "Undo" button (often a counter-clockwise arrow icon) in the toolbar or menu. You can also try common keyboard shortcuts like `Ctrl+Z` (Windows/Linux) or `Cmd+Z` (macOS).
*   **Redo:** To reapply an action you just undid, look for a "Redo" button (often a clockwise arrow icon) or use shortcuts like `Ctrl+Y` (Windows/Linux) or `Cmd+Shift+Z` (macOS).

## 4. Saving and Loading (If Implemented)

*   **Saving:** If the application supports saving your work, look for a "Save" button or menu option. You might be prompted to save the file to your local computer or cloud storage.
*   **Loading:** To open a previously saved drawing, look for a "Load" or "Open" button/menu option.

    *(Note: Based on the current project structure, explicit file saving/loading to a backend or local system might not be fully implemented yet. State might be managed in browser local storage or be ephemeral.)*

## 5. Exploring Further

*   **Experiment:** The best way to learn is by trying out different tools and options.
*   **Hover for Tooltips:** Hover your mouse over buttons and icons; tooltips might appear explaining their function.
*   **Check for Context Menus:** Right-clicking on the canvas or on shapes might reveal context-specific menus with more actions.

This quick start should give you a good foundation for using RenoPilot.JS.Shapes2. For more detailed information, refer to other sections of the User Guide or specific feature documentation as it becomes available.