/**
 * Area Calculation Strategy for Rectangle and Square Elements
 *
 * @remarks
 * This strategy implements the {@link AreaCalculatorStrategy} for calculating the
 * area of Rectangle ({@link CoreElementType.RECTANGLE}) and Square
 * ({@link CoreElementType.SQUARE}) elements.
 *
 * It utilizes the `width` and `height` properties of the rectangle/square element
 * and delegates the actual multiplication to the `calculateRectangleAreaUtil`
 * function from `../../../../lib/utils/geometry/rectangleUtils`.
 *
 * The strategy validates that the element is of the correct type and that its
 * dimensions (width and height) are valid, finite, non-negative numbers.
 *
 * @module core/compute/strategies/area/RectangleAreaStrategy
 * @see {@link Shape.Rectangle}
 * @see {@link Shape.Square}
 */
import type { AreaCalculatorStrategy } from '../../../../types/core/compute'
import type { Element, Shape } from '../../../../types/core/elementDefinitions'
import { calculateRectangleArea as calculateRectangleAreaUtil } from '../../../../lib/utils/geometry/rectangleUtils'
import { CoreError } from '../../../../services/system/error-service/coreError'
import { ElementType as CoreElementType } from '../../../../types/core/elementDefinitions'
import { ErrorType } from '../../../../types/services/errors'

export class RectangleAreaStrategy implements AreaCalculatorStrategy {
  /**
   * Calculates the area of a Rectangle or Square element.
   *
   * @param element - The rectangle or square element. Expected to be of type
   *                  {@link CoreElementType.RECTANGLE} or {@link CoreElementType.SQUARE}
   *                  and conform to {@link Shape.Rectangle}.
   * @returns The calculated area (width * height).
   * @throws {@link CoreError} if the element is `null` or `undefined`.
   * @throws {@link CoreError} if the element type is not `RECTANGLE` or `SQUARE`.
   * @throws {@link CoreError} if the `width` or `height` properties are not finite numbers
   *         or if the `calculateRectangleAreaUtil` returns `NaN` (e.g., for negative dimensions).
   */
  public calculateArea(element: Element): number {
    if (element == null) {
      throw new CoreError(
        ErrorType.InvalidParameter,
        'Invalid element: null or undefined',
        undefined,
        { component: 'RectangleAreaStrategy', operation: 'calculateArea' },
      )
    }

    if (element.type !== CoreElementType.RECTANGLE && element.type !== CoreElementType.SQUARE) {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `Expected element type RECTANGLE or SQUARE, got '${element.type}' for ID ${element.id}`,
        undefined,
        { component: 'RectangleAreaStrategy', operation: 'calculateArea', target: element.id },
      )
    }

    // After checking the type, we can safely cast to Shape.Rectangle
    const rectElement = element as Shape.Rectangle

    // Access width and height from the nested properties object
    // Safely access width and height, they might be undefined if properties is missing or incomplete
    const width: unknown = rectElement.properties?.width
    const height: unknown = rectElement.properties?.height

    if (typeof width !== 'number' || typeof height !== 'number'
      || !Number.isFinite(width) || !Number.isFinite(height) || width < 0 || height < 0) { // Added check for non-negative
      throw new CoreError(
        ErrorType.InvalidParameter,
        `Rectangle element (ID: ${element.id}) must have finite, non-negative width and height in its properties. Received width: ${String(width)}, height: ${String(height)}`,
        undefined,
        { component: 'RectangleAreaStrategy', operation: 'calculateArea', target: element.id, metadata: { width: String(width), height: String(height) } },
      )
    }

    // Delegate to the utility function for the actual calculation
    // At this point, width and height are guaranteed to be finite, non-negative numbers due to the check above.
    const area = calculateRectangleAreaUtil(width, height)

    // Check if the calculated area is valid (should not be NaN with valid inputs, but defensive check)
    if (Number.isNaN(area)) {
      throw new CoreError(
        ErrorType.ComputationError,
        `Rectangle element (ID: ${element.id}) resulted in NaN area calculation. Received width: ${String(width)}, height: ${String(height)}`,
        undefined,
        { component: 'RectangleAreaStrategy', operation: 'calculateArea', target: element.id, metadata: { width: String(width), height: String(height) } },
      )
    }

    return area
  }

  /**
   * Returns the primary element type this strategy is registered for.
   *
   * @remarks
   * This strategy handles both `RECTANGLE` and `SQUARE` types, as a square
   * is a special case of a rectangle. It is typically registered under
   * {@link CoreElementType.RECTANGLE}.
   *
   * @returns {@link CoreElementType.RECTANGLE} - The rectangle element type that this strategy handles
   */
  public getElementType(): CoreElementType {
    // This strategy can handle both RECTANGLE and SQUARE.
    // RECTANGLE is the base type.
    return CoreElementType.RECTANGLE
  }
}
