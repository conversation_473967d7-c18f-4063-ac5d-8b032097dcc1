import type { RoomProperties } from '@/types/core/element/design/roomDesignTypes'
import { describe, expect, it } from 'vitest'
import { RoomType } from '@/types/core/element/design/roomDesignTypes'
import { MaterialType } from '@/types/core/element/elementMaterialTypes'

describe('room Types Module', () => {
  describe('roomType Enum', () => {
    it('should define standard room types', () => {
      // Verify that the enum exists and has the expected values
      expect(RoomType).toBeDefined()

      // Test living spaces
      expect(RoomType.LIVING_ROOM).toBe('living-room')
      expect(RoomType.DINING_ROOM).toBe('dining-room')
      expect(RoomType.KITCHEN).toBe('kitchen')

      // Test sleeping spaces
      expect(RoomType.BEDROOM).toBe('bedroom')
      expect(RoomType.CHILDREN).toBe('children')

      // Test utility spaces
      expect(RoomType.BATHROOM).toBe('bathroom')
      expect(RoomType.OFFICE).toBe('office')
      expect(RoomType.LAUNDRY).toBe('laundry')
      expect(RoomType.STORAGE).toBe('storage')

      // Test circulation spaces
      expect(RoomType.HALLWAY).toBe('hallway')
      expect(RoomType.ENTRYWAY).toBe('entryway')

      // Test special spaces
      expect(RoomType.GARAGE).toBe('garage')
      expect(RoomType.BASEMENT).toBe('basement')
      expect(RoomType.BALCONY).toBe('balcony')
      expect(RoomType.PATIO).toBe('patio')

      // Test specialized room types
      expect(RoomType.ELDERLY_FRIENDLY).toBe('elderly-friendly')
      expect(RoomType.MULTIFUNCTION).toBe('multifunction')
      expect(RoomType.STUDY).toBe('study')

      // Test custom option
      expect(RoomType.CUSTOM).toBe('custom')
    })

    it('should be usable in switch statements', () => {
      // Define a function that uses the enum in a switch statement
      function getRoomCategory(roomType: RoomType): string {
        switch (roomType) {
          case RoomType.LIVING_ROOM:
          case RoomType.DINING_ROOM:
            return 'social'

          case RoomType.BEDROOM:
          case RoomType.CHILDREN:
            return 'sleeping'

          case RoomType.BATHROOM:
          case RoomType.LAUNDRY:
            return 'utility'

          case RoomType.BALCONY:
          case RoomType.PATIO:
            return 'outdoor'

          default:
            return 'other'
        }
      }

      // Test the function with different enum values
      expect(getRoomCategory(RoomType.LIVING_ROOM)).toBe('social')
      expect(getRoomCategory(RoomType.BEDROOM)).toBe('sleeping')
      expect(getRoomCategory(RoomType.BATHROOM)).toBe('utility')
      expect(getRoomCategory(RoomType.BALCONY)).toBe('outdoor')
      expect(getRoomCategory(RoomType.OFFICE)).toBe('other')
    })

    it('should be usable as object keys', () => {
      // Create an object using enum values as keys
      const roomSizes: Record<RoomType, number> = {
        [RoomType.LIVING_ROOM]: 25,
        [RoomType.DINING_ROOM]: 15,
        [RoomType.KITCHEN]: 12,
        [RoomType.BEDROOM]: 14,
        [RoomType.BATHROOM]: 6,
        [RoomType.OFFICE]: 10,
        [RoomType.HALLWAY]: 8,
        [RoomType.ENTRYWAY]: 5,
        [RoomType.LAUNDRY]: 4,
        [RoomType.GARAGE]: 20,
        [RoomType.BASEMENT]: 30,
        [RoomType.BALCONY]: 8,
        [RoomType.PATIO]: 15,
        [RoomType.CHILDREN]: 12,
        [RoomType.ELDERLY_FRIENDLY]: 16,
        [RoomType.MULTIFUNCTION]: 18,
        [RoomType.STORAGE]: 6,
        [RoomType.STUDY]: 9,
        [RoomType.CUSTOM]: 0,
      }

      // Verify that we can access values using enum keys
      expect(roomSizes[RoomType.LIVING_ROOM]).toBe(25)
      expect(roomSizes[RoomType.BEDROOM]).toBe(14)
      expect(roomSizes[RoomType.BATHROOM]).toBe(6)
    })
  })

  describe('roomProperties Interface', () => {
    it('should define a valid room with required properties', () => {
      // Create a room object that conforms to the RoomProperties interface
      const livingRoom: RoomProperties = {
        type: 'room',
        roomType: RoomType.LIVING_ROOM,
        name: 'Main Living Room',
        floorLevel: 0,
        ceilingHeight: 2700,
        floorMaterial: MaterialType.HARDWOOD,
        wallMaterial: MaterialType.DRYWALL,
        ceilingMaterial: MaterialType.DRYWALL,
        wallIds: ['wall-1', 'wall-2', 'wall-3', 'wall-4'],
        openingIds: ['door-1', 'window-1', 'window-2'],
        furnitureIds: ['sofa-1', 'coffee-table-1', 'tv-stand-1'],
        fixtureIds: ['light-1', 'light-2', 'outlet-1', 'outlet-2'],
      }

      // Verify the room has the expected properties
      expect(livingRoom.type).toBe('room')
      expect(livingRoom.roomType).toBe(RoomType.LIVING_ROOM)
      expect(livingRoom.name).toBe('Main Living Room')
      expect(livingRoom.floorLevel).toBe(0)
      expect(livingRoom.ceilingHeight).toBe(2700)
      expect(livingRoom.floorMaterial).toBe(MaterialType.HARDWOOD)
      expect(livingRoom.wallMaterial).toBe(MaterialType.DRYWALL)
      expect(livingRoom.ceilingMaterial).toBe(MaterialType.DRYWALL)

      // Verify arrays
      expect(livingRoom.wallIds.length).toBe(4)
      expect(livingRoom.openingIds.length).toBe(3)
      expect(livingRoom.furnitureIds.length).toBe(3)
      expect(livingRoom.fixtureIds.length).toBe(4)
    })

    it('should support custom material types as strings', () => {
      // Create a room with custom material types
      const customRoom: RoomProperties = {
        type: 'room',
        roomType: RoomType.CUSTOM,
        name: 'Custom Room',
        floorLevel: 1,
        ceilingHeight: 3000,
        floorMaterial: 'bamboo', // Custom material as string
        wallMaterial: 'textured-plaster', // Custom material as string
        ceilingMaterial: MaterialType.DRYWALL, // Standard material
        wallIds: ['wall-5', 'wall-6', 'wall-7', 'wall-8'],
        openingIds: ['door-2'],
        furnitureIds: [],
        fixtureIds: [],
      }

      // Verify custom materials are handled correctly
      expect(customRoom.floorMaterial).toBe('bamboo')
      expect(customRoom.wallMaterial).toBe('textured-plaster')
      expect(customRoom.ceilingMaterial).toBe(MaterialType.DRYWALL)
    })

    it('should be usable in arrays and collections', () => {
      // Create an array of rooms
      const rooms: RoomProperties[] = [
        {
          type: 'room',
          roomType: RoomType.LIVING_ROOM,
          name: 'Living Room',
          floorLevel: 0,
          ceilingHeight: 2700,
          floorMaterial: MaterialType.HARDWOOD,
          wallMaterial: MaterialType.DRYWALL,
          ceilingMaterial: MaterialType.DRYWALL,
          wallIds: ['wall-1', 'wall-2', 'wall-3', 'wall-4'],
          openingIds: ['door-1', 'window-1'],
          furnitureIds: ['sofa-1'],
          fixtureIds: ['light-1'],
        },
        {
          type: 'room',
          roomType: RoomType.BEDROOM,
          name: 'Master Bedroom',
          floorLevel: 0,
          ceilingHeight: 2700,
          floorMaterial: MaterialType.CARPET,
          wallMaterial: MaterialType.DRYWALL,
          ceilingMaterial: MaterialType.DRYWALL,
          wallIds: ['wall-5', 'wall-6', 'wall-7', 'wall-8'],
          openingIds: ['door-2', 'window-2'],
          furnitureIds: ['bed-1', 'dresser-1'],
          fixtureIds: ['light-2'],
        },
      ]

      // Verify array operations
      expect(rooms.length).toBe(2)
      expect(rooms[0].roomType).toBe(RoomType.LIVING_ROOM)
      expect(rooms[1].roomType).toBe(RoomType.BEDROOM)

      // Test array methods
      const roomNames = rooms.map(room => room.name)
      expect(roomNames).toEqual(['Living Room', 'Master Bedroom'])

      const hardwoodRooms = rooms.filter(room => room.floorMaterial === MaterialType.HARDWOOD)
      expect(hardwoodRooms.length).toBe(1)
      expect(hardwoodRooms[0].name).toBe('Living Room')
    })

    it('should be usable as function parameters and return values', () => {
      // Define a function that takes a RoomProperties parameter
      function calculateRoomArea(room: RoomProperties, wallThickness: number = 100): number {
        // This is a simplified calculation that assumes rectangular rooms
        // In a real application, this would use the actual wall geometries
        const wallCount = room.wallIds.length
        if (wallCount !== 4) {
          return 0 // Only handle rectangular rooms in this example
        }

        // Dummy calculation for testing purposes
        return 20 * (wallCount - wallThickness / 100)
      }

      // Create a room to test with
      const bedroom: RoomProperties = {
        type: 'room',
        roomType: RoomType.BEDROOM,
        name: 'Guest Bedroom',
        floorLevel: 0,
        ceilingHeight: 2700,
        floorMaterial: MaterialType.CARPET,
        wallMaterial: MaterialType.DRYWALL,
        ceilingMaterial: MaterialType.DRYWALL,
        wallIds: ['wall-9', 'wall-10', 'wall-11', 'wall-12'],
        openingIds: ['door-3', 'window-3'],
        furnitureIds: ['bed-2', 'nightstand-1'],
        fixtureIds: ['light-3'],
      }

      // Test the function
      const area = calculateRoomArea(bedroom)
      expect(area).toBe(20 * (4 - 100 / 100)) // 60

      // Test with a different wall thickness
      const areaWithThickerWalls = calculateRoomArea(bedroom, 200)
      expect(areaWithThickerWalls).toBe(20 * (4 - 200 / 100)) // 40
    })
  })
})
