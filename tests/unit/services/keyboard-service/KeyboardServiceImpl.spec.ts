import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import {
  cleanupKeyboardService,
  getKeyboardService,
  initializeKeyboardService,
} from '@/services/keyboard-service'

describe('keyboardServiceImpl', () => {
  let mockEventBus: any
  let mockLogger: any
  let mockDocument: any
  let originalDocument: Document

  beforeEach(() => {
    // Save original document
    originalDocument = global.document

    // Mock event bus
    mockEventBus = {
      publish: vi.fn(),
    }

    // Mock logger
    mockLogger = {
      info: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    }

    // Mock document
    mockDocument = {
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    }

    // Replace global document with mock
    global.document = mockDocument

    // Initialize keyboard service
    initializeKeyboardService(mockEventBus, mockLogger)
  })

  afterEach(() => {
    // Clean up keyboard service
    cleanupKeyboardService()

    // Restore original document
    global.document = originalDocument
  })

  describe('initialization and cleanup', () => {
    it('should initialize correctly', () => {
      expect(mockDocument.addEventListener).toHaveBeenCalledTimes(2)
      expect(mockDocument.addEventListener.mock.calls[0][0]).toBe('keydown')
      expect(mockDocument.addEventListener.mock.calls[1][0]).toBe('keyup')
      expect(mockLogger.info).toHaveBeenCalledWith('Keyboard service initialized')
    })

    it('should clean up correctly', () => {
      cleanupKeyboardService()

      expect(mockDocument.removeEventListener).toHaveBeenCalledTimes(2)
      expect(mockDocument.removeEventListener.mock.calls[0][0]).toBe('keydown')
      expect(mockDocument.removeEventListener.mock.calls[1][0]).toBe('keyup')
      expect(mockLogger.info).toHaveBeenCalledWith('Keyboard service cleaned up')
    })

    it('should warn if trying to initialize an already initialized service', () => {
      initializeKeyboardService(mockEventBus, mockLogger)

      expect(mockLogger.warn).toHaveBeenCalledWith('Keyboard service already initialized.')
    })

    it('should warn if trying to clean up a service that is not initialized', () => {
      cleanupKeyboardService() // First cleanup
      const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      cleanupKeyboardService() // Second cleanup

      expect(consoleWarnSpy).toHaveBeenCalledWith('Keyboard service not initialized or already cleaned up.')

      consoleWarnSpy.mockRestore()
    })
  })

  describe('key binding registration', () => {
    it('should register a key binding', () => {
      const service = getKeyboardService()
      const callback = vi.fn()

      const unregister = service?.registerKeyBinding('ctrl+s', callback)

      expect(service?.getKeyBindings().size).toBe(1)
      expect(service?.getKeyBindings().has('ctrl+s')).toBe(true)
    })

    it('should unregister a key binding', () => {
      const service = getKeyboardService()
      const callback = vi.fn()

      service?.registerKeyBinding('ctrl+s', callback)
      service?.unregisterKeyBinding('ctrl+s')

      expect(service?.getKeyBindings().size).toBe(0)
    })

    it('should return an unregister function from registerKeyBinding', () => {
      const service = getKeyboardService()
      const callback = vi.fn()

      const unregister = service?.registerKeyBinding('ctrl+s', callback)
      unregister?.()

      expect(service?.getKeyBindings().size).toBe(0)
    })

    it('should normalize key strings', () => {
      const service = getKeyboardService()
      const callback = vi.fn()

      service?.registerKeyBinding('CTRL+S', callback)

      expect(service?.getKeyBindings().has('ctrl+s')).toBe(true)
    })

    it('should use default options if not provided', () => {
      const service = getKeyboardService()
      const callback = vi.fn()

      service?.registerKeyBinding('ctrl+s', callback)

      const binding = service?.getKeyBindings().get('ctrl+s')
      expect(binding?.options.preventDefault).toBe(true)
      expect(binding?.options.stopPropagation).toBe(false)
      expect(binding?.options.onKeyUp).toBe(false)
    })

    it('should use provided options', () => {
      const service = getKeyboardService()
      const callback = vi.fn()

      service?.registerKeyBinding('ctrl+s', callback, {
        preventDefault: false,
        stopPropagation: true,
        onKeyUp: true,
        description: 'Save',
      })

      const binding = service?.getKeyBindings().get('ctrl+s')
      expect(binding?.options.preventDefault).toBe(false)
      expect(binding?.options.stopPropagation).toBe(true)
      expect(binding?.options.onKeyUp).toBe(true)
      expect(binding?.options.description).toBe('Save')
    })
  })

  describe('event handling', () => {
    it('should handle keydown events', () => {
      // Get the keydown handler that was registered
      const keydownHandler = mockDocument.addEventListener.mock.calls.find(
        call => call[0] === 'keydown',
      )[1]

      // Create a mock keyboard event
      const mockEvent = {
        key: 'a',
        code: 'KeyA',
        altKey: false,
        ctrlKey: false,
        shiftKey: false,
        metaKey: false,
        preventDefault: vi.fn(),
        stopPropagation: vi.fn(),
      }

      // Call the handler with the mock event
      keydownHandler(mockEvent)

      // Verify the event was published
      expect(mockEventBus.publish).toHaveBeenCalled()
    })

    it('should handle keyup events', () => {
      // Get the keyup handler that was registered
      const keyupHandler = mockDocument.addEventListener.mock.calls.find(
        call => call[0] === 'keyup',
      )[1]

      // Create a mock keyboard event
      const mockEvent = {
        key: 'a',
        code: 'KeyA',
        altKey: false,
        ctrlKey: false,
        shiftKey: false,
        metaKey: false,
        preventDefault: vi.fn(),
        stopPropagation: vi.fn(),
      }

      // Call the handler with the mock event
      keyupHandler(mockEvent)

      // Verify the event was published
      expect(mockEventBus.publish).toHaveBeenCalled()
    })
  })

  describe('key event listeners', () => {
    it('should add a keydown listener', () => {
      const service = getKeyboardService()
      const listener = vi.fn()

      // Reset mock to clear previous calls
      mockDocument.addEventListener.mockClear()

      const unsubscribe = service?.addKeyDownListener(listener)

      expect(mockDocument.addEventListener).toHaveBeenCalledTimes(1)
      expect(mockDocument.addEventListener.mock.calls[0][0]).toBe('keydown')
    })

    it('should add a keyup listener', () => {
      const service = getKeyboardService()
      const listener = vi.fn()

      // Reset mock to clear previous calls
      mockDocument.addEventListener.mockClear()

      const unsubscribe = service?.addKeyUpListener(listener)

      expect(mockDocument.addEventListener).toHaveBeenCalledTimes(1)
      expect(mockDocument.addEventListener.mock.calls[0][0]).toBe('keyup')
    })

    it('should return an unsubscribe function from addKeyDownListener', () => {
      const service = getKeyboardService()
      const listener = vi.fn()

      const unsubscribe = service?.addKeyDownListener(listener)

      // Reset mock to clear previous calls
      mockDocument.removeEventListener.mockClear()

      unsubscribe?.()

      expect(mockDocument.removeEventListener).toHaveBeenCalledTimes(1)
      expect(mockDocument.removeEventListener.mock.calls[0][0]).toBe('keydown')
    })

    it('should return an unsubscribe function from addKeyUpListener', () => {
      const service = getKeyboardService()
      const listener = vi.fn()

      const unsubscribe = service?.addKeyUpListener(listener)

      // Reset mock to clear previous calls
      mockDocument.removeEventListener.mockClear()

      unsubscribe?.()

      expect(mockDocument.removeEventListener).toHaveBeenCalledTimes(1)
      expect(mockDocument.removeEventListener.mock.calls[0][0]).toBe('keyup')
    })
  })
})
