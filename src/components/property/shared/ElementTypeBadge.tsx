/**
 * Element Type Badge Component
 *
 * A visual badge component that displays the element type with appropriate styling and colors.
 * Provides consistent visual identification for different element categories.
 *
 * Features:
 * - Category-based color coding
 * - Consistent typography and spacing
 * - Responsive design
 * - Accessible contrast ratios
 * - Support for custom styling
 *
 * @example
 * ```tsx
 * <ElementTypeBadge type={ElementType.RECTANGLE} />
 * <ElementTypeBadge type={ElementType.TEXT} />
 * <ElementTypeBadge type={ElementType.LINE} />
 * ```
 */

import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { ElementType } from '@/types/core/elementDefinitions'

/**
 * Props for the ElementTypeBadge component
 */
interface ElementTypeBadgeProps {
  /** The element type to display */
  type: ElementType
  /** Additional CSS classes */
  className?: string
  /** Badge variant override */
  variant?: 'default' | 'secondary' | 'destructive' | 'outline'
}

/**
 * Maps element types to their display names
 */
const ELEMENT_TYPE_DISPLAY_NAMES: Record<ElementType, string> = {
  // Shape elements
  [ElementType.RECTANGLE]: 'Rectangle',
  [ElementType.SQUARE]: 'Square',
  [ElementType.CIRCLE]: 'Circle',
  [ElementType.ELLIPSE]: 'Ellipse',
  [ElementType.POLYGON]: 'Polygon',
  [ElementType.TRIANGLE]: 'Triangle',
  [ElementType.QUADRILATERAL]: 'Quadrilateral',
  [ElementType.PENTAGON]: 'Pentagon',
  [ElementType.HEXAGON]: 'Hexagon',
  [ElementType.HEPTAGON]: 'Heptagon',
  [ElementType.OCTAGON]: 'Octagon',
  [ElementType.NONAGON]: 'Nonagon',
  [ElementType.DECAGON]: 'Decagon',

  // Path elements
  [ElementType.LINE]: 'Line',
  [ElementType.POLYLINE]: 'Polyline',
  [ElementType.ARC]: 'Arc',
  [ElementType.QUADRATIC]: 'Quadratic Curve',
  [ElementType.CUBIC]: 'Cubic Curve',

  // Text elements
  [ElementType.TEXT]: 'Text',
  [ElementType.TEXT_LABEL]: 'Text Label',

  // Image elements
  [ElementType.IMAGE]: 'Image',

  // Design elements
  [ElementType.WALL]: 'Wall',
  [ElementType.DOOR]: 'Door',
  [ElementType.WINDOW]: 'Window',
  [ElementType.ROOM]: 'Room',

  // Special elements
  [ElementType.FURNITURE]: 'Furniture',
  [ElementType.FIXTURE]: 'Fixture',
  [ElementType.LIGHT]: 'Light',
  [ElementType.FLOOR_AREA]: 'Floor Area',
  [ElementType.WALL_PAINT]: 'Wall Paint',
  [ElementType.WALL_PAPER]: 'Wallpaper',
  [ElementType.OPENING]: 'Opening',
  [ElementType.APPLIANCE]: 'Appliance',
  [ElementType.HANDRAIL]: 'Handrail',
  [ElementType.ELECTRICAL_OUTLET]: 'Electrical Outlet',
  [ElementType.ROOM_BOUNDARY]: 'Room Boundary',

  // Utility elements
  [ElementType.GROUP]: 'Group',
}

/**
 * Maps element types to their category colors
 */
const ELEMENT_TYPE_COLORS: Record<string, string> = {
  // Shape elements - Blue theme
  shape: 'bg-blue-100 text-blue-800 border-blue-200',

  // Path elements - Green theme
  path: 'bg-green-100 text-green-800 border-green-200',

  // Text elements - Purple theme
  text: 'bg-purple-100 text-purple-800 border-purple-200',

  // Image elements - Orange theme
  image: 'bg-orange-100 text-orange-800 border-orange-200',

  // Design elements - Indigo theme
  design: 'bg-indigo-100 text-indigo-800 border-indigo-200',

  // Special elements - Teal theme
  special: 'bg-teal-100 text-teal-800 border-teal-200',

  // Utility elements - Gray theme
  utility: 'bg-gray-100 text-gray-800 border-gray-200',
}

/**
 * Determines the category of an element type
 */
function getElementCategory(type: ElementType): string {
  // Shape elements
  if ([
    ElementType.RECTANGLE,
    ElementType.SQUARE,
    ElementType.CIRCLE,
    ElementType.ELLIPSE,
    ElementType.POLYGON,
    ElementType.TRIANGLE,
    ElementType.QUADRILATERAL,
    ElementType.PENTAGON,
    ElementType.HEXAGON,
    ElementType.HEPTAGON,
    ElementType.OCTAGON,
    ElementType.NONAGON,
    ElementType.DECAGON,
  ].includes(type)) {
    return 'shape'
  }

  // Path elements
  if ([
    ElementType.LINE,
    ElementType.POLYLINE,
    ElementType.ARC,
    ElementType.QUADRATIC,
    ElementType.CUBIC,
  ].includes(type)) {
    return 'path'
  }

  // Text elements
  if ([ElementType.TEXT, ElementType.TEXT_LABEL].includes(type)) {
    return 'text'
  }

  // Image elements
  if ([ElementType.IMAGE].includes(type)) {
    return 'image'
  }

  // Design elements
  if ([ElementType.WALL, ElementType.DOOR, ElementType.WINDOW, ElementType.ROOM].includes(type)) {
    return 'design'
  }

  // Special elements
  if ([
    ElementType.FURNITURE,
    ElementType.FIXTURE,
    ElementType.LIGHT,
    ElementType.FLOOR_AREA,
    ElementType.WALL_PAINT,
    ElementType.WALL_PAPER,
    ElementType.OPENING,
    ElementType.APPLIANCE,
    ElementType.HANDRAIL,
    ElementType.ELECTRICAL_OUTLET,
    ElementType.ROOM_BOUNDARY,
  ].includes(type)) {
    return 'special'
  }

  // Utility elements
  return 'utility'
}

/**
 * ElementTypeBadge component for displaying element type with category-based styling.
 *
 * This component provides a visual indicator of the element type with consistent
 * styling and color coding based on element categories.
 *
 * @param props - The component props
 * @param props.type - The element type to display
 * @param props.className - Additional CSS classes to apply
 * @param props.variant - The badge variant style
 * @returns The rendered element type badge
 */
export function ElementTypeBadge({ type, className, variant }: ElementTypeBadgeProps) {
  const displayName = ELEMENT_TYPE_DISPLAY_NAMES[type] || type
  const category = getElementCategory(type)
  const colorClasses = ELEMENT_TYPE_COLORS[category] || ELEMENT_TYPE_COLORS.utility

  return (
    <Badge
      variant={variant || 'outline'}
      className={cn(
        'text-xs font-medium border',
        !variant && colorClasses,
        className,
      )}
    >
      {displayName}
    </Badge>
  )
}
