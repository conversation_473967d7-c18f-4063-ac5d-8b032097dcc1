import type {
  ExportOptions,
  ExportPayload,
  ExportResult,
} from '@/types/services/events/exportEvents'
import { describe, expect, it } from 'vitest'
import {
  ExportEventTypes,
  ExportFormat,
} from '@/types/services/events/exportEvents'

describe('export Events', () => {
  describe('exportFormat', () => {
    it('should define all expected export formats', () => {
      expect(ExportFormat.SVG).toBeDefined()
      expect(ExportFormat.PNG).toBeDefined()
      expect(ExportFormat.PDF).toBeDefined()
      expect(ExportFormat.JSON).toBeDefined()
    })

    it('should have the correct string values', () => {
      expect(ExportFormat.SVG).toBe('svg')
      expect(ExportFormat.PNG).toBe('png')
      expect(ExportFormat.PDF).toBe('pdf')
      expect(ExportFormat.JSON).toBe('json')
    })
  })

  describe('exportEventTypes', () => {
    it('should define all expected event types', () => {
      // Basic export events
      expect(ExportEventTypes.EXPORT_REQUEST).toBeDefined()
      expect(ExportEventTypes.EXPORT_PREPARE).toBeDefined()
      expect(ExportEventTypes.EXPORT_PROGRESS).toBeDefined()
      expect(ExportEventTypes.EXPORT_COMPLETE).toBeDefined()
      expect(ExportEventTypes.EXPORT_ERROR).toBeDefined()

      // Preview events
      expect(ExportEventTypes.EXPORT_PREVIEW_REQUEST).toBeDefined()
      expect(ExportEventTypes.EXPORT_PREVIEW_GENERATE).toBeDefined()
      expect(ExportEventTypes.EXPORT_PREVIEW_COMPLETE).toBeDefined()
      expect(ExportEventTypes.EXPORT_PREVIEW_ERROR).toBeDefined()

      // History events
      expect(ExportEventTypes.EXPORT_HISTORY_UPDATE).toBeDefined()
      expect(ExportEventTypes.EXPORT_HISTORY_CLEAR).toBeDefined()

      // Template events
      expect(ExportEventTypes.EXPORT_TEMPLATE_CREATED).toBeDefined()
      expect(ExportEventTypes.EXPORT_TEMPLATE_UPDATED).toBeDefined()
      expect(ExportEventTypes.EXPORT_TEMPLATE_DELETED).toBeDefined()

      // Batch export events
      expect(ExportEventTypes.EXPORT_BATCH_START).toBeDefined()
      expect(ExportEventTypes.EXPORT_BATCH_PROGRESS).toBeDefined()
      expect(ExportEventTypes.EXPORT_BATCH_COMPLETE).toBeDefined()
      expect(ExportEventTypes.EXPORT_BATCH_ERROR).toBeDefined()

      // Queue events
      expect(ExportEventTypes.EXPORT_QUEUE_UPDATE).toBeDefined()
      expect(ExportEventTypes.EXPORT_QUEUE_TASK_START).toBeDefined()
      expect(ExportEventTypes.EXPORT_QUEUE_TASK_COMPLETE).toBeDefined()
      expect(ExportEventTypes.EXPORT_QUEUE_TASK_ERROR).toBeDefined()
    })

    it('should have the correct string values', () => {
      // Check a few representative values
      expect(ExportEventTypes.EXPORT_REQUEST).toBe('export.request')
      expect(ExportEventTypes.EXPORT_PROGRESS).toBe('export.progress')
      expect(ExportEventTypes.EXPORT_PREVIEW_REQUEST).toBe('export.preview.request')
      expect(ExportEventTypes.EXPORT_HISTORY_UPDATE).toBe('export.history.update')
      expect(ExportEventTypes.EXPORT_TEMPLATE_CREATED).toBe('export.template.created')
      expect(ExportEventTypes.EXPORT_BATCH_START).toBe('export.batch.start')
      expect(ExportEventTypes.EXPORT_QUEUE_UPDATE).toBe('export.queue.update')
    })
  })

  describe('exportOptions', () => {
    it('should allow creating export options', () => {
      const options: ExportOptions = {
        format: ExportFormat.PNG,
        fileName: 'design.png',
        quality: 0.9,
        scale: 2,
        selectedOnly: true,
        includeBackground: true,
        backgroundColor: '#ffffff',
        includeMetadata: true,
        metadata: { author: 'Test User' },
        dimensions: { width: 800, height: 600 },
        watermark: {
          enabled: true,
          text: 'DRAFT',
          position: 'center',
          rotation: 45,
          opacity: 0.3,
        },
      }

      expect(options.format).toBe(ExportFormat.PNG)
      expect(options.fileName).toBe('design.png')
      expect(options.quality).toBe(0.9)
      expect(options.scale).toBe(2)
      expect(options.selectedOnly).toBe(true)
      expect(options.includeBackground).toBe(true)
      expect(options.backgroundColor).toBe('#ffffff')
      expect(options.includeMetadata).toBe(true)
      expect(options.metadata?.author).toBe('Test User')
      expect(options.dimensions?.width).toBe(800)
      expect(options.dimensions?.height).toBe(600)
      expect(options.watermark?.enabled).toBe(true)
      expect(options.watermark?.text).toBe('DRAFT')
      expect(options.watermark?.position).toBe('center')
      expect(options.watermark?.rotation).toBe(45)
      expect(options.watermark?.opacity).toBe(0.3)
    })
  })

  describe('exportResult', () => {
    it('should allow creating export results', () => {
      const result: ExportResult = {
        fileName: 'design.png',
        format: ExportFormat.PNG,
        fileUrl: 'https://example.com/design.png',
        metadata: { size: 1024 },
      }

      expect(result.fileName).toBe('design.png')
      expect(result.format).toBe(ExportFormat.PNG)
      expect(result.fileUrl).toBe('https://example.com/design.png')
      expect(result.metadata?.size).toBe(1024)
    })
  })

  describe('exportPayload', () => {
    it('should allow creating basic export payloads', () => {
      const payload: ExportPayload = {
        format: ExportFormat.PNG,
        data: new Blob(['test']),
        progress: 0.5,
        error: 'Error message',
      }

      expect(payload.format).toBe(ExportFormat.PNG)
      expect(payload.data).toBeInstanceOf(Blob)
      expect(payload.progress).toBe(0.5)
      expect(payload.error).toBe('Error message')
    })
  })
})
