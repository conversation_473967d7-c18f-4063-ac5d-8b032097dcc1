/**
 * Services Types Module
 *
 * This module exports all service-related type definitions and interfaces
 * used throughout the application.
 *
 * @module types/services
 */

import * as ErrorTypes from './errors'
import * as EventCore from './events/eventCore'
import * as EventRegistry from './events/eventRegistry'
// Core types
// Re-export domain-specific types using namespaces to avoid conflicts
import * as EventTypes from './events/eventTypes'
import * as HistoryTypes from './history'
import * as KeyboardTypes from './keyboard'
import * as LoggingTypes from './logging'
import * as ShapeTypes from './shapes'
import * as ValidationTypes from './validation'

// Export ExportOptions interface for backward compatibility
export type { ExportOptions } from '../export/ExportTemplateTypes'

// Export all namespaces
export {
  ErrorTypes,
  EventCore,
  EventRegistry,
  EventTypes,
  HistoryTypes,
  KeyboardTypes,
  LoggingTypes,
  ShapeTypes,
  ValidationTypes,
}

export * from './core/serviceResult'

// Re-export only the main event types for backward compatibility
export { AppEventType } from './events/eventTypes'
