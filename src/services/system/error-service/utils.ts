/**
 * Provides helper functions for error handling.
 */

import type { ErrorContext } from '@/types/services/core/errorService'
import {
  // createErrorContext as createContextUtil, // Removed, as errorUtils.ts does not export this
  // formatErrorMessage as formatMessageUtil, // Removed
  // extractStackTrace as extractStackUtil,   // Removed
  ensureError as ensureErrorUtil,
} from '@/lib/utils/errorUtils' // Corrected path
import { ErrorSeverity, ErrorType } from '@/types/services/errors' // ICoreError removed as unused
import { CoreError as CoreErrorClass } from './coreError' // The actual class for instantiation

/**
 * Creates an error context object.
 */
export function createErrorContext(
  component: string,
  operation: string,
  options?: Partial<Omit<ErrorContext, 'component' | 'operation'>>,
): ErrorContext {
  // Assuming createContextUtil is correctly imported and works as expected
  // If createContextUtil is not available, construct manually:
  return {
    component,
    operation,
    timestamp: Date.now(),
    ...options,
  }
}

/**
 * Formats an error message for user display.
 */
export function formatErrorMessage(
  error: Error,
  defaultMessage: string = 'Operation failed',
): string {
  // Directly implement or use a different utility if formatMessageUtil was a placeholder
  return error.message || defaultMessage
}

/**
 * Safely extracts stack trace from an error.
 */
export function extractStackTrace(error: Error): string {
  // Directly implement or use a different utility if extractStackUtil was a placeholder
  return (error.stack !== null && error.stack !== undefined && error.stack !== '') ? error.stack : ''
}

/**
 * Ensures that a value is an Error instance.
 */
export function ensureError(value: unknown): Error {
  return ensureErrorUtil(value)
}

/**
 * Ensures that a value is a CoreError instance (actually ICoreError compatible).
 * If not, it wraps it in a new CoreErrorClass instance.
 */
export function ensureCoreError(value: unknown, defaultMessage?: string): CoreErrorClass {
  // Check if it already behaves like an ICoreError (has type and severity)
  if (
    typeof value === 'object'
    && value !== null
    && 'type' in value
    && 'severity' in value
    && value instanceof Error
  ) {
    // It might be a CoreErrorClass instance or an object matching ICoreError structure
    // If it's already a CoreErrorClass instance, return it.
    if (value instanceof CoreErrorClass)
      return value
    // Otherwise, it's an Error that looks like ICoreError, wrap it if necessary or cast carefully.
    // For simplicity, we'll re-wrap to ensure it's a CoreErrorClass instance.
  }

  const error = ensureErrorUtil(value) // Ensures we have an Error object

  return new CoreErrorClass(
    (error as Error & { type?: ErrorType }).type ?? ErrorType.UnknownError,
    defaultMessage ?? error.message,
    (error as Error & { severity?: ErrorSeverity }).severity ?? ErrorSeverity.Medium,
    {
      // component: 'ensureCoreError', // Optional: add component/operation if meaningful
      // operation: 'ensureCoreError',
      metadata: { originalError: error }, // Store originalError in metadata
      stack: error.stack,
    },
  )
}
