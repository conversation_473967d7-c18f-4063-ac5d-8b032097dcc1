/**
 * @file StrategyRegistry.additional2.spec.ts
 * @description Additional unit tests for StrategyRegistry
 */

import { beforeEach, describe, expect, it, vi } from 'vitest'
import { StrategyRegistry } from '@/core/compute/StrategyRegistry'
import { CoreError } from '@/core/errors'
import { CalculationType } from '@/types/core/compute'
import { ElementType } from '@/types/core/shape'

describe('strategyRegistry - Additional Tests 2', () => {
  let registry: StrategyRegistry

  beforeEach(() => {
    registry = new StrategyRegistry()
    console.log = vi.fn()
  })

  describe('edge Cases', () => {
    it('should handle case-insensitivity for calculation type', () => {
      // Arrange
      const mockStrategy = { calculate: vi.fn() }
      registry.registerStrategy(CalculationType.AREA, ElementType.RECTANGLE, mockStrategy)

      // Act & Assert
      expect(registry.hasStrategy('area' as CalculationType, ElementType.RECTANGLE)).toBe(true)
      expect(registry.hasStrategy('AREA' as CalculationType, ElementType.RECTANGLE)).toBe(true)
      expect(registry.hasStrategy('Area' as CalculationType, ElementType.RECTANGLE)).toBe(true)
    })

    it('should handle case-insensitivity for shape type', () => {
      // Arrange
      const mockStrategy = { calculate: vi.fn() }
      registry.registerStrategy(CalculationType.AREA, ElementType.RECTANGLE, mockStrategy)

      // Act & Assert
      expect(registry.hasStrategy(CalculationType.AREA, 'rectangle' as ElementType)).toBe(true)
      expect(registry.hasStrategy(CalculationType.AREA, 'RECTANGLE' as ElementType)).toBe(true)
      expect(registry.hasStrategy(CalculationType.AREA, 'Rectangle' as ElementType)).toBe(true)
    })

    it('should handle null or undefined calculation type', () => {
      // Act & Assert
      expect(registry.hasStrategy(null as any, ElementType.RECTANGLE)).toBe(false)
      expect(registry.hasStrategy(undefined as any, ElementType.RECTANGLE)).toBe(false)
    })

    it('should handle null or undefined shape type', () => {
      // Act & Assert
      expect(registry.hasStrategy(CalculationType.AREA, null as any)).toBe(false)
      expect(registry.hasStrategy(CalculationType.AREA, undefined as any)).toBe(false)
    })

    it('should handle empty string calculation type', () => {
      // Act & Assert
      expect(registry.hasStrategy('' as CalculationType, ElementType.RECTANGLE)).toBe(false)
    })

    it('should handle empty string shape type', () => {
      // Act & Assert
      expect(registry.hasStrategy(CalculationType.AREA, '' as ElementType)).toBe(false)
    })
  })

  describe('error Handling', () => {
    it('should throw error when registering with null or undefined calculation type', () => {
      // Arrange
      const mockStrategy = { calculate: vi.fn() }

      // Act & Assert
      expect(() => registry.registerStrategy(null as any, ElementType.RECTANGLE, mockStrategy)).toThrow(CoreError)
      expect(() => registry.registerStrategy(undefined as any, ElementType.RECTANGLE, mockStrategy)).toThrow(CoreError)
    })

    it('should throw error when registering with null or undefined shape type', () => {
      // Arrange
      const mockStrategy = { calculate: vi.fn() }

      // Act & Assert
      expect(() => registry.registerStrategy(CalculationType.AREA, null as any, mockStrategy)).toThrow(CoreError)
      expect(() => registry.registerStrategy(CalculationType.AREA, undefined as any, mockStrategy)).toThrow(CoreError)
    })

    it('should throw error when registering with null or undefined strategy', () => {
      // Act & Assert
      expect(() => registry.registerStrategy(CalculationType.AREA, ElementType.RECTANGLE, null as any)).toThrow(CoreError)
      expect(() => registry.registerStrategy(CalculationType.AREA, ElementType.RECTANGLE, undefined as any)).toThrow(CoreError)
    })

    it('should throw error when registering with empty string calculation type', () => {
      // Arrange
      const mockStrategy = { calculate: vi.fn() }

      // Act & Assert
      expect(() => registry.registerStrategy('' as CalculationType, ElementType.RECTANGLE, mockStrategy)).toThrow(CoreError)
    })

    it('should throw error when registering with empty string shape type', () => {
      // Arrange
      const mockStrategy = { calculate: vi.fn() }

      // Act & Assert
      expect(() => registry.registerStrategy(CalculationType.AREA, '' as ElementType, mockStrategy)).toThrow(CoreError)
    })

    it('should throw error when getting strategy for non-existent calculation type', () => {
      // Act & Assert
      expect(() => registry.getStrategy('nonexistent' as CalculationType, ElementType.RECTANGLE)).toThrow(CoreError)
    })

    it('should throw error when getting strategy for non-existent shape type', () => {
      // Arrange
      const mockStrategy = { calculate: vi.fn() }
      registry.registerStrategy(CalculationType.AREA, ElementType.RECTANGLE, mockStrategy)

      // Act & Assert
      expect(() => registry.getStrategy(CalculationType.AREA, 'nonexistent' as ElementType)).toThrow(CoreError)
    })
  })

  describe('multiple Registrations', () => {
    it('should allow overriding a strategy for the same calculation and shape type', () => {
      // Arrange
      const mockStrategy1 = { calculate: vi.fn().mockReturnValue(100) }
      const mockStrategy2 = { calculate: vi.fn().mockReturnValue(200) }

      // Act
      registry.registerStrategy(CalculationType.AREA, ElementType.RECTANGLE, mockStrategy1)
      registry.registerStrategy(CalculationType.AREA, ElementType.RECTANGLE, mockStrategy2)

      // Assert
      const strategy = registry.getStrategy(CalculationType.AREA, ElementType.RECTANGLE)
      expect(strategy).toBe(mockStrategy2)
      expect(strategy.calculate()).toBe(200)
    })

    it('should maintain separate strategies for different calculation types', () => {
      // Arrange
      const mockAreaStrategy = { calculate: vi.fn().mockReturnValue(100) }
      const mockPerimeterStrategy = { calculate: vi.fn().mockReturnValue(40) }

      // Act
      registry.registerStrategy(CalculationType.AREA, ElementType.RECTANGLE, mockAreaStrategy)
      registry.registerStrategy(CalculationType.PERIMETER, ElementType.RECTANGLE, mockPerimeterStrategy)

      // Assert
      const areaStrategy = registry.getStrategy(CalculationType.AREA, ElementType.RECTANGLE)
      const perimeterStrategy = registry.getStrategy(CalculationType.PERIMETER, ElementType.RECTANGLE)

      expect(areaStrategy).toBe(mockAreaStrategy)
      expect(perimeterStrategy).toBe(mockPerimeterStrategy)
      expect(areaStrategy.calculate()).toBe(100)
      expect(perimeterStrategy.calculate()).toBe(40)
    })

    it('should maintain separate strategies for different shape types', () => {
      // Arrange
      const mockRectangleStrategy = { calculate: vi.fn().mockReturnValue(100) }
      const mockCircleStrategy = { calculate: vi.fn().mockReturnValue(200) }

      // Act
      registry.registerStrategy(CalculationType.AREA, ElementType.RECTANGLE, mockRectangleStrategy)
      registry.registerStrategy(CalculationType.AREA, ElementType.CIRCLE, mockCircleStrategy)

      // Assert
      const rectangleStrategy = registry.getStrategy(CalculationType.AREA, ElementType.RECTANGLE)
      const circleStrategy = registry.getStrategy(CalculationType.AREA, ElementType.CIRCLE)

      expect(rectangleStrategy).toBe(mockRectangleStrategy)
      expect(circleStrategy).toBe(mockCircleStrategy)
      expect(rectangleStrategy.calculate()).toBe(100)
      expect(circleStrategy.calculate()).toBe(200)
    })
  })

  describe('getSupportedElementTypes', () => {
    it('should return an empty array for non-existent calculation type', () => {
      // Act
      const result = registry.getSupportedElementTypes('nonexistent' as CalculationType)

      // Assert
      expect(result).toEqual([])
    })

    it('should return all shape types registered for a calculation type', () => {
      // Arrange
      const mockStrategy = { calculate: vi.fn() }
      registry.registerStrategy(CalculationType.AREA, ElementType.RECTANGLE, mockStrategy)
      registry.registerStrategy(CalculationType.AREA, ElementType.CIRCLE, mockStrategy)
      registry.registerStrategy(CalculationType.AREA, ElementType.LINE, mockStrategy)

      // Act
      const result = registry.getSupportedElementTypes(CalculationType.AREA)

      // Assert
      expect(result).toContain(ElementType.RECTANGLE)
      expect(result).toContain(ElementType.CIRCLE)
      expect(result).toContain(ElementType.LINE)
      expect(result).toHaveLength(3)
    })

    it('should handle case-insensitivity for calculation type in getSupportedElementTypes', () => {
      // Arrange
      const mockStrategy = { calculate: vi.fn() }
      registry.registerStrategy(CalculationType.AREA, ElementType.RECTANGLE, mockStrategy)

      // Act & Assert
      expect(registry.getSupportedElementTypes('area' as CalculationType)).toContain(ElementType.RECTANGLE)
      expect(registry.getSupportedElementTypes('AREA' as CalculationType)).toContain(ElementType.RECTANGLE)
      expect(registry.getSupportedElementTypes('Area' as CalculationType)).toContain(ElementType.RECTANGLE)
    })
  })
})
