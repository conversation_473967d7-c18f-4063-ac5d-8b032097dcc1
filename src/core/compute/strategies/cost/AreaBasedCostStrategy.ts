/**
 * Cost Calculation Strategy Based on Element Area
 *
 * @remarks
 * This strategy implements the {@link CostCalculatorStrategy} to calculate costs
 * for elements where the primary cost driver is their surface area (e.g., flooring,
 * paint, wallpaper).
 *
 * **Important Dependency:** This strategy currently assumes that the provided `element`
 * object has a method `compute.area()` that returns its calculated area. This is a
 * point of coupling that might be refactored in the future to use a dedicated
 * area calculation service (like {@link ComputeFacade}) or to receive the area
 * as a direct input, promoting better separation of concerns.
 *
 * The core calculation logic is delegated to the `calculateAreaBasedCost` utility
 * function from `../../../../lib/utils/cost/costUtils`, which takes the computed
 * area, a unit cost, and optional cost parameters (like tax or discount rates).
 *
 * The `getElementType()` method returns a generic string 'all_with_area', indicating
 * its potential applicability to any element type that can provide an area, rather
 * than being tied to a specific {@link ElementType}. The {@link StrategyRegistry}
 * would need to handle how such a generic strategy is mapped or prioritized.
 *
 * @module core/compute/strategies/cost/AreaBasedCostStrategy
 * @see {@link CostCalculatorStrategy}
 * @see {@link CostCalculationOptions}
 * @see {@link calculateAreaBasedCost}
 */
import type { CostCalculationOptions, CostCalculatorStrategy } from '../../../../types/core/compute'
import type { Element } from '../../../../types/core/elementDefinitions'
// TODO: Cannot find module '../../../../lib/utils/cost/costUtils' or its corresponding type declarations.
// import { calculateAreaBasedCost } from '../../../../lib/utils/cost/costUtils';
import { CoreError } from '../../../../services/system/error-service/coreError'
import { ErrorType } from '../../../../types/services/errors'

export class AreaBasedCostStrategy implements CostCalculatorStrategy {
  /**
   * Calculates the cost for an element based on its area and a specified unit cost.
   *
   * @param element - The element (expected to have a `compute.area()` method) for which to calculate the cost.
   * @param unitCost - The cost per unit of area (e.g., cost per square meter).
   * @param options - Optional {@link CostCalculationOptions} such as tax rate, discount rate, or additional fixed costs.
   * @returns The total calculated cost.
   * @throws {@link CoreError} if the element does not have a `compute.area()` method.
   * @throws {@link CoreError} if the `compute.area()` method fails or returns an invalid area (NaN, negative).
   * @throws {@link CoreError} if `unitCost` is invalid.
   */
  public calculateCost(element: Element, unitCost: number, options?: CostCalculationOptions): number {
    // TODO: Refactor to decouple area calculation from the element instance.
    // This strategy should ideally use an AreaCalculatorStrategy or receive area as a parameter.
    console.warn(`[AreaBasedCostStrategy][calculateCost] Received unitCost: ${unitCost}, Type: ${typeof unitCost}`)
    const computableElement = element as { compute?: { area?: () => number } } // Element type doesn't guarantee compute.area()

    if (computableElement.compute == null || typeof computableElement.compute.area !== 'function') {
      throw new CoreError(
        ErrorType.InvalidElementType,
        `AreaBasedCostStrategy requires an element with a compute.area() method. Element ID: ${element.id}, Type: ${element.type}`,
        undefined,
        { component: 'AreaBasedCostStrategy', operation: 'calculateCost', target: element.id },
      )
    }

    let area: number
    try {
      // FIXME: This direct call to element.compute.area() bypasses the centralized ComputeFacade
      // and does not ensure that geometry utils are used for area calculation if compute.area() is implemented differently.
      area = computableElement.compute.area()
    }
    catch (error) {
      throw new CoreError(
        ErrorType.ComputationError,
        `Failed to calculate area for element (ID: ${element.id}): ${error instanceof Error ? error.message : String(error)}`,
        undefined,
        { component: 'AreaBasedCostStrategy', operation: 'calculateCost', target: element.id, metadata: { originalError: error } },
      )
    }

    if (typeof area !== 'number' || Number.isNaN(area) || area < 0) {
      throw new CoreError(
        ErrorType.InvalidParameter, // Changed from INVALID_RESULT as it's an input to cost calculation
        `Invalid area value computed for element (ID: ${element.id}): ${area}. Area must be a non-negative number.`,
        undefined,
        { component: 'AreaBasedCostStrategy', operation: 'calculateCost', target: element.id, metadata: { computedArea: area } },
      )
    }

    // TODO: calculateAreaBasedCost function is missing or its import path is incorrect.
    // Assuming a simple calculation for now, or this needs to be implemented/fixed.
    // return calculateAreaBasedCost(area, unitCost, {
    //   additionalCost: options?.additionalCost,
    //   discountRate: options?.discountRate,
    //   taxRate: options?.taxRate
    // });
    // 计算总成本
    // 基本成本 = 面积 * 单位成本
    let totalCost = area * unitCost

    // 应用乘数（如果有）
    if (options?.multiplier != null && typeof options.multiplier === 'number' && !Number.isNaN(options.multiplier)) {
      totalCost *= options.multiplier
    }

    // 应用额外成本（如果有）
    if (options?.additionalCost != null && typeof options.additionalCost === 'number' && !Number.isNaN(options.additionalCost)) {
      totalCost += options.additionalCost
    }

    // 应用折扣（如果有）
    if (options?.discountRate != null && typeof options.discountRate === 'number' && !Number.isNaN(options.discountRate)) {
      totalCost *= (1 - options.discountRate)
    }

    // 应用税率（如果有）
    if (options?.taxRate != null && typeof options.taxRate === 'number' && !Number.isNaN(options.taxRate)) {
      totalCost *= (1 + options.taxRate)
    }

    console.warn(`[AreaBasedCostStrategy][calculateCost] Area: ${area}, UnitCost: ${unitCost}, Multiplier: ${options?.multiplier}, Total: ${totalCost}`)
    return totalCost
  }

  /**
   * Returns a string indicating the applicability of this strategy.
   *
   * @remarks
   * Since this strategy calculates cost based on area, it's potentially applicable
   * to any element type that can provide an area via a `compute.area()` method.
   * It returns a generic identifier string rather than a specific {@link ElementType}.
   * The {@link StrategyRegistry} would determine how to map or use such a generic strategy.
   *
   * @returns The string 'all_with_area' to signify its generic applicability.
   */
  public getElementType(): string {
    // This strategy is generic and applies to any element that can provide its area.
    // The string 'all_with_area' is a convention to indicate this.
    return 'all_with_area'
  }
}
