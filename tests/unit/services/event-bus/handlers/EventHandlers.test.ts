import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import { setupEventHandlers } from '@/services/event-bus/handlers/EventHandlers'

// Import after mocking
import { getEventBus } from '@/services/event-bus/index'
import { AppEventType } from '@/types/services/events'

// Mock the event bus module before importing the handlers
vi.mock('@/services/event-bus/index', () => {
  return {
    getEventBus: vi.fn().mockReturnValue({
      subscribe: vi.fn(),
      publish: vi.fn(),
      unsubscribe: vi.fn(),
      reset: vi.fn(),
    }),
  }
})

describe('eventHandlers', () => {
  let mockEventBus: any

  beforeEach(() => {
    // Get the mocked event bus
    mockEventBus = getEventBus()

    // Clear all mocks before each test
    vi.clearAllMocks()

    // Mock console methods
    vi.spyOn(console, 'log').mockImplementation(() => {})
  })

  afterEach(() => {
    // Restore console methods
    vi.restoreAllMocks()
  })

  describe('setupEventHandlers', () => {
    it('should set up all event handlers', () => {
      // Call the function
      setupEventHandlers()

      // Verify that subscribe was called for all expected event types
      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.CANVAS_CLEARED,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.CANVAS_RESIZED,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.CANVAS_MOUSE_MOVE,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.TOOL_CHANGED,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.HISTORY_CHECKPOINT,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.HISTORY_UNDO,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.HISTORY_REDO,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.LAYER_VISIBILITY_CHANGE,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.LAYER_LOCK_CHANGE,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.LAYER_ORDER_CHANGE,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.FILE_IMPORTED,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.FILE_EXPORTED,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.VIEW_ZOOMED,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.VIEW_PANNED,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.TEMPLATE_APPLY,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.NOTIFICATION_ADD,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.TOAST_SHOW,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.SIDEBAR_LEFT_TOGGLE,
        expect.any(Function),
      )

      expect(mockEventBus.subscribe).toHaveBeenCalledWith(
        AppEventType.SIDEBAR_RIGHT_TOGGLE,
        expect.any(Function),
      )
    })
  })

  describe('event Handlers', () => {
    it('should log canvas cleared events', () => {
      setupEventHandlers()

      // Find the canvas cleared handler
      const canvasClearedHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.CANVAS_CLEARED,
      )[1]

      // Call the handler with a mock event
      canvasClearedHandler({
        type: AppEventType.CANVAS_CLEARED,
        payload: { canvasId: 'test-canvas' },
      })

      expect(console.log).toHaveBeenCalledWith('Canvas cleared:', 'test-canvas')
    })

    it('should log canvas resized events', () => {
      setupEventHandlers()

      const canvasResizedHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.CANVAS_RESIZED,
      )[1]

      canvasResizedHandler({
        type: AppEventType.CANVAS_RESIZED,
        payload: { width: 800, height: 600 },
      })

      expect(console.log).toHaveBeenCalledWith('Canvas resized:', 800, 600)
    })

    it('should log canvas mouse move events occasionally', () => {
      setupEventHandlers()

      const canvasMouseMoveHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.CANVAS_MOUSE_MOVE,
      )[1]

      // Mock Math.random to return a value less than 0.01 to ensure logging
      const originalRandom = Math.random
      Math.random = vi.fn().mockReturnValue(0.005)

      canvasMouseMoveHandler({
        type: AppEventType.CANVAS_MOUSE_MOVE,
        payload: { x: 100, y: 200 },
      })

      expect(console.log).toHaveBeenCalledWith('Canvas mouse move:', 100, 200)

      // Restore Math.random
      Math.random = originalRandom
    })

    it('should not log canvas mouse move events most of the time', () => {
      setupEventHandlers()

      const canvasMouseMoveHandler = mockEventBus.subscribe.mock.calls.find(
        call => call[0] === AppEventType.CANVAS_MOUSE_MOVE,
      )[1]

      // Mock Math.random to return a value greater than 0.01 to prevent logging
      const originalRandom = Math.random
      Math.random = vi.fn().mockReturnValue(0.5)

      canvasMouseMoveHandler({
        type: AppEventType.CANVAS_MOUSE_MOVE,
        payload: { x: 100, y: 200 },
      })

      expect(console.log).not.toHaveBeenCalled()

      // Restore Math.random
      Math.random = originalRandom
    })
  })
})
