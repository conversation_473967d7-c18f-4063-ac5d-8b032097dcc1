/**
 * Logger Service Registration
 *
 * This module provides functions for registering the logger service with the service registry.
 *
 * @module services/system/logging/Registry
 */

import type { ServiceRegistry } from '@/services/core/registry'
import type { LoggerService } from '@/types/services/logging'
import { getServiceFactory } from '@/services/core/registry' // Added getServiceFactory
import { ServiceId } from '@/types/services/core/serviceIdentifier'
// import { ConsoleLoggerService } from './consoleLoggerService'; // Unused import

/**
 * 注册日志服务到服务注册表
 *
 * @param registry 服务注册表
 * @returns 注册的日志服务实例
 */
export function registerLoggerService(
  registry: ServiceRegistry,
): LoggerService {
  // 创建日志服务实例
  // Create logger service instance using the factory
  const factory = getServiceFactory()
  const loggerService = factory.createLogger()

  // 注册到注册表
  registry.register(ServiceId.Logger, loggerService)

  // console.log('日志服务注册成功')

  return loggerService
}
