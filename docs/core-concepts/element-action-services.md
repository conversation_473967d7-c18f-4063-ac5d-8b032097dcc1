# Element Action Services

Located in `src/services/elements/element-actions/`, these services are responsible for handling the core CRUD (Create, Read, Update, Delete) and selection operations for elements (shapes) on the canvas. They encapsulate the business logic for these actions, interacting with the `shapesStore` and potentially other services like `EventBus` or `ShapeRepository`.

## Key Services and Their Responsibilities

### 1. `ElementCreationService.ts`

*   **Responsibility:** Manages the creation of new shape elements.
*   **Key Functions/Methods:**
    *   `createElement(type: ElementType, properties: Partial<ShapeProperties>, parentId?: string): Shape | null`:
        *   Takes the type of element to create (e.g., 'rectangle', 'ellipse') and its initial properties (position, size, style).
        *   Validates the input properties.
        *   Generates a unique ID for the new shape.
        *   Constructs the full shape object.
        *   Adds the new shape to the `shapesStore`.
        *   May publish an event (e.g., `ELEMENT_CREATED`) via `EventBus`.
        *   Returns the created shape object or null if creation fails.
    *   `createElements(elementsData: Array<{ type: ElementType, properties: Partial<ShapeProperties> }>): Shape[]`:
        *   Handles batch creation of multiple elements.
*   **Interactions:**
    *   `shapesStore`: To add the new shape(s).
    *   `ElementFactory` (potentially): To get default properties or construct shape objects based on type.
    *   `ElementValidator`: To validate input properties before creation.
    *   `EventBus`: To notify other parts of the application about the new element.

### 2. `ElementEditService.ts` (often `ElementEditServiceImpl.ts`)

*   **Responsibility:** Manages the modification of existing shape elements.
*   **Key Functions/Methods:**
    *   `updateElementProperties(elementId: string, updates: Partial<ShapeProperties>): boolean`:
        *   Takes the ID of the element to update and a partial object containing the properties to change.
        *   Validates the updates.
        *   Updates the corresponding shape in the `shapesStore`.
        *   May publish an event (e.g., `ELEMENT_UPDATED`) via `EventBus`.
        *   Returns true if successful, false otherwise.
    *   `updateMultipleElementsProperties(updates: Array<{ id: string; changes: Partial<ShapeProperties> }>): boolean`:
        *   Handles batch updates for multiple elements.
    *   Specific transformation methods like:
        *   `moveElement(elementId: string, deltaX: number, deltaY: number)`
        *   `resizeElement(elementId: string, newWidth: number, newHeight: number, anchor?: string)`
        *   `rotateElement(elementId: string, angle: number)`
*   **Interactions:**
    *   `shapesStore`: To update shape properties.
    *   `ElementValidator`: To validate the changes.
    *   `EventBus`: To notify about element updates.
    *   `ComputeFacade` or geometry utilities: For complex transformations like resizing with aspect ratio or rotation around a point.

### 3. `ElementDeleteService.ts`

*   **Responsibility:** Manages the deletion of shape elements.
*   **Key Functions/Methods:**
    *   `deleteElement(elementId: string): boolean`:
        *   Removes the specified element from the `shapesStore`.
        *   Also removes it from the `selectedShapeIds` in `shapesStore` if it was selected.
        *   May publish an event (e.g., `ELEMENT_DELETED`) via `EventBus`.
        *   Returns true if successful, false otherwise.
    *   `deleteElements(elementIds: string[]): boolean`:
        *   Handles batch deletion of multiple elements.
*   **Interactions:**
    *   `shapesStore`: To remove the shape(s) and update selection.
    *   `EventBus`: To notify about element deletion.

### 4. `ElementSelectionService.ts`

*   **Responsibility:** Manages the selection state of shape elements.
*   **Key Functions/Methods:**
    *   `selectElement(elementId: string, multiSelect: boolean = false)`:
        *   If `multiSelect` is false, clears previous selection and selects the given element.
        *   If `multiSelect` is true, adds the element to the current selection (or toggles its selection).
        *   Updates `selectedShapeIds` in `shapesStore`.
        *   Publishes an event (e.g., `SELECTION_CHANGED`) via `EventBus`.
    *   `deselectElement(elementId: string)`:
        *   Removes the element from the current selection.
    *   `clearSelection()`:
        *   Deselects all currently selected elements.
    *   `setSelectedElements(elementIds: string[])`:
        *   Sets the selection to a specific list of element IDs.
    *   `selectElementsInArea(rect: { x: number, y: number, width: number, height: number })`:
        *   Selects all elements that fall within a given rectangular area (marquee selection).
*   **Interactions:**
    *   `shapesStore`: To read shape data (for area selection) and update `selectedShapeIds`.
    *   `EventBus`: To notify about selection changes.
    *   Geometry utilities: To check if shapes intersect with the selection rectangle.

## General Design Considerations

*   **Interface-Driven:** These services often implement interfaces (e.g., `IElementCreationService`) to allow for dependency injection and easier testing/mocking. The `CoreCoordinator` would then hold references to these interface types.
*   **Dependency Injection:** Services are typically instantiated and managed by a central coordinator (like `CoreCoordinator`) or a DI container, which injects dependencies like `shapesStore` and `EventBus`.
*   **Error Handling:** Robust error handling is implemented, potentially using a centralized `ErrorService` to report issues.
*   **Event Emission:** Changes made by these services (creation, update, deletion, selection) usually trigger events on an `EventBus`. This allows other parts of the application (e.g., UI, rendering engine, history/undo-redo service) to react to these changes without direct coupling.
*   **Single Responsibility Principle:** Each service focuses on a specific aspect of element manipulation, promoting modularity and maintainability.

## Example Flow: Creating a Rectangle

1.  User clicks the "Rectangle" tool in the UI and then clicks on the canvas.
2.  The canvas interaction handler (e.g., in `CanvasInteraction.tsx`) captures the click event and desired properties (position, initial size).
3.  It calls `coreCoordinator.elementCreationService.createElement('rectangle', { x, y, width, height })`.
4.  `ElementCreationService`:
    a.  Validates the input.
    b.  Generates a unique ID.
    c.  Creates the rectangle shape object.
    d.  Calls `shapesStore.addShape(newRectangle)`.
    e.  Publishes `AppEvent.ELEMENT_CREATED` with the new rectangle data.
5.  `shapesStore` updates its state, triggering re-renders in subscribed components (like the canvas).
6.  The canvas renderer draws the new rectangle.
7.  Other services (e.g., history service) might listen to `ELEMENT_CREATED` to record an undoable action.

These element action services form the backbone of user interaction with shapes on the canvas, providing a structured and maintainable way to manage element lifecycles and states.