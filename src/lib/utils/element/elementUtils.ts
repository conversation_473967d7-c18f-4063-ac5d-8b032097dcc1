/**
 * General Element Utility Functions
 *
 * @remarks
 * This module provides a collection of common utility functions for working with
 * various types of elements ({@link ShapeModel}) in the application. These utilities
 * cover a range of operations including:
 *
 * - **Bounding Box Calculations**:
 *   - `calculateElementsBoundingBox`: Computes a bounding box that encompasses multiple elements.
 * - **Geometric Queries**:
 *   - `getCenter`: Retrieves the center point of an element.
 *   - `distanceBetween`: Calculates the distance between the centers of two elements.
 *   - `doElementsOverlap`: Checks if the bounding boxes of two elements intersect.
 * - **Collection Operations**:
 *   - `sortByZIndex`: Sorts elements by their `zIndex` property.
 *   - `groupByLayer`: Groups elements by their `layer` property.
 *   - `filterByType`: Filters elements by their `type` property.
 *   - `filterByCategory`: Filters elements based on their category (derived from `getElementCategory`).
 * - **State Checking**:
 *   - `isVisible`: Checks if an element is currently visible.
 * - **Data Extraction and Manipulation**:
 *   - `points`: Extracts an array of points from an element's properties.
 *   - `ensureCompleteMetadata`: Ensures a metadata object has `createdAt` and `updatedAt` timestamps.
 *
 * Most functions are provided as static methods of the {@link ElementUtils} class and are
 * also re-exported directly for convenience.
 *
 * @module lib/utils/element/elementUtils
 */

import type { PointData as IPoint } from '../../../types/core/element/geometry/point' // Use PointData and alias to IPoint
// Removed unused import for IRectangle
// import { merge as mergeBoundingBoxes } from '../geometry/boundingBox'; // Removed problematic import
import type { ElementType, MetadataProperties, ShapeElement as ShapeModel } from '@/types/core/elementDefinitions' // Added MetadataProperties
// points function will be defined in this file.

import { BoundingBoxClass } from '../geometry/BoundingBoxClass'
import { PointClass } from '../geometry/PointClass'
import { getElementCategory } from './elementTypeUtils'
// import { calculateBoundingBox } from '../geometry/boundingBox'; // This specific import might be incorrect; BoundingBox class has static methods

/**
 * Provides static utility methods for common operations on elements.
 */
export class ElementUtils {
  /**
   * Calculates the bounding box that encompasses an array of {@link ShapeModel} elements.
   *
   * @remarks
   * This function iterates through the provided elements, determines the bounding box
   * for each (either from its properties or by calling a `getBoundingBox` method if available),
   * and then computes a union of all these individual bounding boxes to find the overall
   * bounding box that contains all elements.
   *
   * It attempts to derive bounding boxes for common shapes (rectangles, circles, ellipses,
   * polygons, lines) based on their standard properties.
   *
   * @param elements - An array of {@link ShapeModel} instances.
   * @returns A {@link BoundingBoxClass} instance representing the combined bounding box,
   *          or `null` if the input array is empty or no valid bounding boxes could be determined.
   *
   * @example
   * ```typescript
   * const elements = [rectangleElement, circleElement];
   * const combinedBBox = ElementUtils.calculateElementsBoundingBox(elements);
   * if (combinedBBox) {
   *   // Use combinedBBox.x, combinedBBox.y, combinedBBox.width, combinedBBox.height
   * }
   * ```
   */
  static calculateElementsBoundingBox(elements: ShapeModel[]): BoundingBoxClass | null {
    if (!elements || elements.length === 0) {
      return null
    }

    let combinedBBox: BoundingBoxClass | null = null

    for (const element of elements) {
      if (!element?.position)
        continue

      let elementBBox: BoundingBoxClass | null = null

      // Try to get BBox from properties first
      if (element.properties) {
        if (typeof element.properties.width === 'number' && typeof element.properties.height === 'number') {
          elementBBox = new BoundingBoxClass(element.position.x, element.position.y, element.properties.width, element.properties.height)
        }
        else if (typeof element.properties.radius === 'number') { // Circle
          elementBBox = new BoundingBoxClass(
            element.position.x - element.properties.radius,
            element.position.y - element.properties.radius,
            element.properties.radius * 2,
            element.properties.radius * 2,
          )
        }
        else if (typeof element.properties.radiusX === 'number' && typeof element.properties.radiusY === 'number') { // Ellipse
          elementBBox = new BoundingBoxClass(
            element.position.x - element.properties.radiusX,
            element.position.y - element.properties.radiusY,
            element.properties.radiusX * 2,
            element.properties.radiusY * 2,
          )
        }
        else if (Array.isArray(element.properties.points) && element.properties.points.length > 0) {
          const absPoints = element.properties.points.map((p: IPoint) =>
            new PointClass(p.x + element.position.x, p.y + element.position.y, p.z), // Use PointClass
          )
          if (absPoints.length > 0) {
            elementBBox = BoundingBoxClass.fromPointsArray(absPoints) // Use BoundingBoxClass
          }
        }
        else if (element.properties.start && element.properties.end) { // For lines
          const start = element.properties.start as IPoint
          const end = element.properties.end as IPoint
          const absPoints = [
            new PointClass(start.x + element.position.x, start.y + element.position.y, start.z), // Use PointClass
            new PointClass(end.x + element.position.x, end.y + element.position.y, end.z), // Use PointClass
          ]
          elementBBox = BoundingBoxClass.fromPointsArray(absPoints) // Use BoundingBoxClass
        }
      }

      // If no BBox from properties, try calling getBoundingBox() method on the element
      if (!elementBBox && typeof (element as any).getBoundingBox === 'function') {
        try {
          const bboxFromMethod = (element as any).getBoundingBox()
          if (bboxFromMethod) {
            if (bboxFromMethod instanceof BoundingBoxClass) { // Check against BoundingBoxClass
              elementBBox = bboxFromMethod
            }
            else if (typeof bboxFromMethod.x === 'number'
              && typeof bboxFromMethod.y === 'number'
              && typeof bboxFromMethod.width === 'number'
              && typeof bboxFromMethod.height === 'number') {
              elementBBox = new BoundingBoxClass(bboxFromMethod.x, bboxFromMethod.y, bboxFromMethod.width, bboxFromMethod.height)
            }
          }
        }
        catch (e) {
          console.warn(`Error getting bounding box for element ${element.id} via getBoundingBox():`, e)
        }
      }

      // Combine with the overall bounding box
      if (elementBBox) {
        if (combinedBBox) {
          // BoundingBoxClass.union returns a new BoundingBoxClass instance
          combinedBBox = combinedBBox.union(elementBBox)
        }
        else {
          combinedBBox = elementBBox
        }
      }
    }
    return combinedBBox
  }

  /**
   * Gets the center point of a {@link ShapeModel}.
   *
   * @remarks
   * For simplicity, this currently defaults to returning the element's `position` property.
   * For shapes where `position` might not represent the geometric center (e.g., if it's a
   * top-left corner for a rectangle), a more sophisticated calculation based on the
   * element's type and properties (like its bounding box) would be required for true centering.
   *
   * @param element - The {@link ShapeModel} instance.
   * @returns The {@link IPoint} representing the element's position (assumed center for now).
   */
  static getCenter(element: ShapeModel): IPoint {
    // A more robust getCenter might look at element.type and properties
    // For example, for a rectangle where position is top-left:
    // if (element.properties && typeof element.properties.width === 'number' && typeof element.properties.height === 'number') {
    //   return new Point(element.position.x + element.properties.width / 2, element.position.y + element.properties.height / 2, element.position.z);
    // }
    return element.position // Default to the element's defined position
  }

  /**
   * Calculates the Euclidean distance between the center points of two {@link ShapeModel} elements.
   *
   * @remarks
   * This function uses {@link ElementUtils.getCenter} to determine the center of each element.
   *
   * @param element1 - The first {@link ShapeModel}.
   * @param element2 - The second {@link ShapeModel}.
   * @returns The distance between the elements' centers. Units are consistent with the input coordinates.
   * @throws {@link Error} if either element is null or undefined.
   *
   * @example
   * ```typescript
   * const dist = ElementUtils.distanceBetween(shapeA, shapeB);
   * ```
   */
  static distanceBetween(element1: ShapeModel, element2: ShapeModel): number {
    if (!element1 || !element2) {
      throw new Error('Both elements must be provided to calculate distance')
    }

    const center1 = ElementUtils.getCenter(element1)
    const center2 = ElementUtils.getCenter(element2)

    const dx = center2.x - center1.x
    const dy = center2.y - center1.y

    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * Checks if the bounding boxes of two {@link ShapeModel} elements overlap.
   *
   * @remarks
   * This function uses an axis-aligned bounding box (AABB) intersection test.
   * It first calculates the bounding box for each element using {@link ElementUtils.calculateElementsBoundingBox}.
   *
   * @param element1 - The first {@link ShapeModel}.
   * @param element2 - The second {@link ShapeModel}.
   * @returns `true` if their bounding boxes overlap, `false` otherwise or if bounding boxes cannot be determined.
   * @throws {@link Error} if either element is null or undefined.
   *
   * @example
   * ```typescript
   * if (ElementUtils.doElementsOverlap(shapeA, shapeB)) {
   *   // Handle overlap
   * }
   * ```
   */
  static doElementsOverlap(element1: ShapeModel, element2: ShapeModel): boolean {
    if (!element1 || !element2) {
      throw new Error('Both elements must be provided to check overlap')
    }

    const bbox1 = ElementUtils.calculateElementsBoundingBox([element1])
    const bbox2 = ElementUtils.calculateElementsBoundingBox([element2])

    if (!bbox1 || !bbox2) {
      return false
    }

    // Check for intersection using axis-aligned bounding box algorithm
    return (
      bbox1.position.x < bbox2.position.x + bbox2.width
      && bbox1.position.x + bbox1.width > bbox2.position.x
      && bbox1.position.y < bbox2.position.y + bbox2.height
      && bbox1.position.y + bbox1.height > bbox2.position.y
    )
  }

  /**
   * Sorts an array of {@link ShapeModel} elements by their `zIndex` property in ascending order.
   *
   * @remarks
   * Elements without a `zIndex` property are treated as having a `zIndex` of 0 for sorting purposes.
   * This method returns a new sorted array, leaving the original array unchanged.
   *
   * @param elements - An array of {@link ShapeModel} instances to sort.
   * @returns A new array containing the elements sorted by `zIndex`. Returns an empty array if input is invalid.
   *
   * @example
   * ```typescript
   * const sorted = ElementUtils.sortByZIndex(myElements);
   * ```
   */
  static sortByZIndex(elements: ShapeModel[]): ShapeModel[] {
    if (!elements || !Array.isArray(elements)) {
      return []
    }

    return [...elements].sort((a, b) => {
      const zIndexA = a.zIndex ?? 0 // Use nullish coalescing for better semantics
      const zIndexB = b.zIndex ?? 0
      return zIndexA - zIndexB
    })
  }

  /**
   * Groups an array of {@link ShapeModel} elements by their layer information.
   *
   * @remarks
   * Elements are grouped by their `zLevelId` if available, otherwise by `majorCategory`.
   * Elements without either property are grouped under a 'default' layer key.
   * This replaces the deprecated `layer` property with the new layer system.
   *
   * @param elements - An array of {@link ShapeModel} instances to group.
   * @returns An object where keys are layer identifiers (strings) and values are arrays
   *          of {@link ShapeModel} instances belonging to that layer. Returns an empty
   *          object if the input is invalid.
   *
   * @example
   * ```typescript
   * const groupedByLayer = ElementUtils.groupByLayer(myElements);
   * const baseLayer = groupedByLayer['BASE'];
   * ```
   */
  static groupByLayer(elements: ShapeModel[]): Record<string, ShapeModel[]> {
    if (!elements || !Array.isArray(elements)) {
      return {}
    }

    const result: Record<string, ShapeModel[]> = {}

    for (const element of elements) {
      if (!element)
        continue

      // Use zLevelId if available, otherwise fall back to majorCategory, then to 'default'
      const layerKey = element.zLevelId || element.majorCategory || 'default'
      if (!result[layerKey]) {
        result[layerKey] = []
      }
      result[layerKey].push(element)
    }

    return result
  }

  /**
   * Filters an array of {@link ShapeModel} elements to include only those matching a specific `type`.
   *
   * @param elements - An array of {@link ShapeModel} instances to filter.
   * @param type - The element type string (e.g., 'RECTANGLE', 'CIRCLE', corresponding to {@link ElementType}) to filter by.
   * @returns A new array containing only elements of the specified type. Returns an empty array if input is invalid.
   *
   * @example
   * ```typescript
   * const rectangles = ElementUtils.filterByType(myElements, ElementType.RECTANGLE);
   * ```
   */
  static filterByType(elements: ShapeModel[], type: string): ShapeModel[] {
    if (!elements || !Array.isArray(elements) || !type) {
      return []
    }

    return elements.filter(element => element && element.type === type)
  }

  /**
   * Filters an array of {@link ShapeModel} elements to include only those belonging to a specific category.
   *
   * @remarks
   * Element categories are determined using the `getElementCategory` utility function.
   *
   * @param elements - An array of {@link ShapeModel} instances to filter.
   * @param category - The category string (e.g., 'BASIC_SHAPES', 'PATHS', as keys of {@link ElementCategories}) to filter by.
   * @returns A new array containing only elements of the specified category. Returns an empty array if input is invalid.
   *
   * @example
   * ```typescript
   * const pathElements = ElementUtils.filterByCategory(myElements, 'PATHS');
   * ```
   */
  static filterByCategory(elements: ShapeModel[], category: string): ShapeModel[] {
    if (!elements || !Array.isArray(elements) || !category) {
      return []
    }

    return elements.filter(element =>
      element && getElementCategory(element.type as ElementType) === category,
    )
  }

  /**
   * Checks if a {@link ShapeModel} is currently marked as visible.
   * @param element - The {@link ShapeModel} to check.
   * @returns `true` if the element's `visible` property is true, `false` otherwise.
   */
  static isVisible(element: ShapeModel): boolean {
    return element.visible
  }
}

/**
 * Extracts an array of points from a {@link ShapeModel}'s `properties.points`.
 *
 * @remarks
 * This function is designed to safely access an array of points if it exists
 * within an element's `properties` object. It ensures that the returned points
 * conform to the {@link IPoint} structure.
 *
 * @param element - The element model, expected to potentially have a `properties` object
 *                  with an optional `points` array (e.g., for polygons, polylines).
 * @returns An array of {@link IPoint}-compatible objects. Returns an empty array if
 *          `properties.points` is not found or is not a valid array.
 */
export function points(element: { properties?: { points?: IPoint[] } } | any): IPoint[] { // `any` is used for flexibility with various element structures
  if (element?.properties && Array.isArray(element.properties.points)) {
    // Ensure returned points conform to IPoint structure if they don't already
    return element.properties.points.map((p: any) => ({ x: p.x, y: p.y, z: p.z })) // Map to ensure structure
  }
  return []
}

// Export all static methods from ElementUtils for direct use
export const {
  calculateElementsBoundingBox,
  getCenter,
  isVisible,
  distanceBetween,
  doElementsOverlap,
  sortByZIndex,
  groupByLayer,
  filterByType,
  filterByCategory,
} = ElementUtils

// The `points` function is already exported as a standalone function.

/**
 * Ensures that a metadata object includes `createdAt` and `updatedAt` timestamps.
 *
 * @remarks
 * If the provided `metadata` object is undefined or lacks these timestamps,
 * they are added with the current time. If they exist, they are preserved.
 * This is useful for standardizing metadata across newly created or updated elements.
 *
 * @param metadata - An optional, partial {@link MetadataProperties} object.
 * @returns A complete {@link MetadataProperties} object with `createdAt` and `updatedAt` guaranteed to be present.
 */
export function ensureCompleteMetadata(metadata?: Partial<MetadataProperties>): MetadataProperties {
  const now = Date.now()
  const defaults: MetadataProperties = {
    createdAt: now,
    updatedAt: now,
  }
  const result = {
    ...defaults,
    ...metadata,
  }
  result.createdAt = typeof result.createdAt === 'number' ? result.createdAt : now
  result.updatedAt = typeof result.updatedAt === 'number' ? result.updatedAt : now
  return result
}
