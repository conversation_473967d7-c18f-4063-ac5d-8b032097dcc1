import type { ITransformStrategy } from '@/core/compute/strategies/transform/TransformStrategy.interface'
import type { ElementType } from '@/types/core/shape-type'
/**
 * Additional unit tests for StrategyRegistry
 */
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { StrategyRegistry } from '@/core/compute/StrategyRegistry'

// Mock Strategies
const mockTransformStrategy: ITransformStrategy = {
  getElementType: () => 'rectangle' as ElementType,
  execute: vi.fn().mockImplementation(element => element),
}

describe('strategyRegistry - Additional Tests', () => {
  let registry: StrategyRegistry
  let consoleSpy: any

  beforeEach(() => {
    registry = new StrategyRegistry()
    // Reset mocks before each test
    vi.clearAllMocks()
    // Spy on console methods
    consoleSpy = {
      log: vi.spyOn(console, 'log').mockImplementation(() => {}),
      warn: vi.spyOn(console, 'warn').mockImplementation(() => {}),
      error: vi.spyOn(console, 'error').mockImplementation(() => {}),
      debug: vi.spyOn(console, 'debug').mockImplementation(() => {}),
    }
  })

  describe('transform Strategy', () => {
    it('should register and retrieve a transform strategy', () => {
      registry.registerTransformStrategy(mockTransformStrategy)
      expect(registry.getTransformStrategy('rectangle')).toBe(mockTransformStrategy)
      expect(registry.hasStrategy('rectangle', 'transform')).toBe(true)
    })

    it('should throw an error if no transform strategy is found', () => {
      expect(() => registry.getTransformStrategy('unknown')).toThrow('No transform strategy found for shape type: unknown')
      expect(registry.hasStrategy('unknown', 'transform')).toBe(false)
    })

    it('should handle case-insensitivity for transform strategy retrieval', () => {
      registry.registerTransformStrategy(mockTransformStrategy)
      expect(registry.getTransformStrategy('RECTANGLE')).toBe(mockTransformStrategy)
      expect(registry.hasStrategy('RECTANGLE', 'transform')).toBe(true)
    })
  })

  describe('getSupportedElementTypes with transform', () => {
    it('should return registered shape types for transform calculation type', () => {
      const circleTransformStrategy: ITransformStrategy = {
        getElementType: () => 'circle' as ElementType,
        execute: vi.fn().mockImplementation(element => element),
      }

      registry.registerTransformStrategy(mockTransformStrategy)
      registry.registerTransformStrategy(circleTransformStrategy)

      const transformTypes = registry.getSupportedElementTypes('transform')
      expect(transformTypes).toHaveLength(2)
      expect(transformTypes).toContain('rectangle')
      expect(transformTypes).toContain('circle')
    })
  })

  describe('edge Cases', () => {
    it('should handle unknown calculation type in hasStrategy for transform', () => {
      // @ts-ignore - Testing runtime behavior with invalid input
      const result = registry.hasStrategy('rectangle', 'unknown')
      expect(result).toBe(false)
      expect(consoleSpy.warn).toHaveBeenCalledWith(expect.stringContaining('Unknown calculation type in hasStrategy'))
    })

    it('should handle unknown calculation type in getSupportedElementTypes for transform', () => {
      // @ts-ignore - Testing runtime behavior with invalid input
      const result = registry.getSupportedElementTypes('unknown')
      expect(result).toEqual([])
      expect(consoleSpy.warn).toHaveBeenCalledWith(expect.stringContaining('Unknown calculation type in getSupportedElementTypes'))
    })
  })
})
