import { beforeEach, describe, expect, it } from 'vitest'
import { RectanglePerimeterStrategy } from '@/core/compute/strategies/perimeter/RectanglePerimeterStrategy'
import { Rectangle } from '@/types/core/element/shape/rectangleShapeTypes'
import { ElementType } from '@/types/core/elementDefinitions'

// Mock Element class
class MockElement {
  id = 'mock-element'
  type = ElementType.RECTANGLE

  getSubType() {
    return 'rectangle'
  }
}

// Mock Rectangle class for testing
class MockRectangle extends MockElement {
  private width: number
  private height: number

  constructor(width: number, height: number) {
    super()
    this.width = width
    this.height = height
  }

  getWidth(): number {
    return this.width
  }

  getHeight(): number {
    return this.height
  }
}

describe('rectanglePerimeterStrategy', () => {
  let strategy: RectanglePerimeterStrategy

  beforeEach(() => {
    strategy = new RectanglePerimeterStrategy()
  })

  it('should be defined', () => {
    expect(RectanglePerimeterStrategy).toBeDefined()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('RECTANGLE')
  })

  it('should calculate perimeter correctly', () => {
    const rectangle = new MockRectangle(5, 10) as any
    const result = strategy.calculatePerimeter(rectangle)
    expect(result).toBe(30) // 2 * (width + height) = 2 * (5 + 10) = 30
  })

  it('should handle zero values', () => {
    const rectangle = new MockRectangle(0, 0) as any
    const result = strategy.calculatePerimeter(rectangle)
    expect(result).toBe(0)
  })

  it('should handle negative values', () => {
    const rectangle = new MockRectangle(-5, -10) as any
    const result = strategy.calculatePerimeter(rectangle)
    expect(result).toBe(-30) // Assuming the implementation allows negative values
  })

  it('should throw error for non-rectangle elements', () => {
    const nonRectangle = {
      id: 'non-rectangle',
      type: 'CIRCLE',
      getSubType: () => 'circle',
    } as any

    expect(() => strategy.calculatePerimeter(nonRectangle)).toThrow('Expected element type RECTANGLE')
  })

  it('should handle real Rectangle instance', () => {
    // Create a mock that looks like a real Rectangle instance
    const realRectangle = {
      id: 'real-rectangle-id',
      constructor: { name: 'Rectangle' },
      position: { x: 0, y: 0 },
      width: 100,
      height: 50,
      getSubType: () => 'rectangle',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realRectangle, Rectangle.prototype)

    const perimeter = strategy.calculatePerimeter(realRectangle as any)
    expect(perimeter).toBe(300) // 2 * (100 + 50) = 300
  })

  it('should handle invalid dimensions in real Rectangle instance', () => {
    // Create a mock that looks like a real Rectangle instance with invalid dimensions
    const realRectangle = {
      id: 'real-rectangle-id',
      constructor: { name: 'Rectangle' },
      position: { x: 0, y: 0 },
      width: Number.NaN,
      height: 50,
      getSubType: () => 'rectangle',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realRectangle, Rectangle.prototype)

    const perimeter = strategy.calculatePerimeter(realRectangle as any)
    expect(isNaN(perimeter)).toBe(true)
  })

  it('should handle error when accessing properties on Rectangle', () => {
    // Create a mock that looks like a real Rectangle instance but throws when width is accessed
    const realRectangle = {
      id: 'real-rectangle-id',
      constructor: { name: 'Rectangle' },
      get width() { throw new Error('Test error') },
      height: 50,
      getSubType: () => 'rectangle',
    }

    // Make instanceof check pass
    Object.setPrototypeOf(realRectangle, Rectangle.prototype)

    // Wrap in try-catch to handle the error
    try {
      const perimeter = strategy.calculatePerimeter(realRectangle as any)
      expect(isNaN(perimeter)).toBe(true)
    }
    catch (error) {
      // If it throws, that's also acceptable
      expect(error).toBeDefined()
    }
  })
})
