{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@components/*": ["./src/components/*"],
      "@ui/*": ["./src/components/ui/*"],
      "@toolbar/*": ["./src/components/ui/toolbar/*"],
      "@panel/*": ["./src/components/ui/panel/*"],
      "@status/*": ["./src/components/ui/status/*"],
      "@modals/*": ["./src/components/ui/modals/*"],
      "@core/*": ["./src/core/*"],
      "@types_core/*": ["./src/types/core/*"], // New alias for types/core
      "@element/*": ["./src/types/core/element/*"],
      "@path/*": ["./src/types/core/element/path/*"],
      "@shape/*": ["./src/types/core/element/shape/*"],
      "@services/*": ["./src/services/*"],
      "@services/types/*": ["./src/types/services/*"]
    }
  }
}
