import type { BaseEvent } from './eventCore'
/**
 * Grid Events Type Definitions
 *
 * This file defines event types related to grid operations,
 * such as enabling/disabling the grid, changing its size or color,
 * and toggling snap-to-grid functionality.
 *
 * @module types/services/events
 */
import type { AppEventType } from './eventTypes'

/**
 * Event triggered when the grid visibility is toggled.
 */
export interface GridEnableEvent extends BaseEvent {
  /** The event type identifier. */
  type: AppEventType.GridEnabled
  /** Event payload containing grid enable state. */
  payload: {
    /** Whether the grid is enabled. */
    enabled: boolean
  }
}

/**
 * Event triggered when the grid size changes.
 */
export interface GridSizeChangeEvent extends BaseEvent {
  /** The event type identifier. */
  type: AppEventType.GridSizeChanged
  /** Event payload containing the new grid size. */
  payload: {
    /** New grid size. */
    size: number
  }
}

/**
 * Event triggered when the grid color changes.
 */
export interface GridColorChangeEvent extends BaseEvent {
  /** The event type identifier. */
  type: AppEventType.GridColorChanged
  /** Event payload containing the new grid color. */
  payload: {
    /** New grid color. */
    color: string
  }
}

/**
 * Event triggered when the snap-to-grid setting changes.
 */
export interface GridSnapChangeEvent extends BaseEvent {
  /** The event type identifier. */
  type: AppEventType.GridSnapChanged
  /** Event payload containing grid snap state. */
  payload: {
    /** Whether snap-to-grid is enabled. */
    enabled: boolean
  }
}
