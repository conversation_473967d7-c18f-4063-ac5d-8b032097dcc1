import type { WallProperties } from '@/types/core/element/design/wallDesignTypes'
import type Point from '@/types/core/element/geometry/point'
import { describe, expect, it } from 'vitest'
import { WallType } from '@/types/core/element/design/wallDesignTypes'

describe('wall Types Module', () => {
  describe('wallType Enum', () => {
    it('should define standard wall types', () => {
      // Verify that the enum exists and has the expected values
      expect(WallType).toBeDefined()

      // Test standard wall types
      expect(WallType.STANDARD).toBe('standard')
      expect(WallType.LOAD_BEARING).toBe('load-bearing')
      expect(WallType.PARTITION).toBe('partition')
      expect(WallType.EXTERIOR).toBe('exterior')
      expect(WallType.CURTAIN).toBe('curtain')
      expect(WallType.HALF_HEIGHT).toBe('half-height')
    })

    it('should be usable in switch statements', () => {
      // Define a function that uses the enum in a switch statement
      function getWallCategory(wallType: WallType): string {
        switch (wallType) {
          case WallType.STANDARD:
          case WallType.PARTITION:
          case WallType.HALF_HEIGHT:
            return 'interior'

          case WallType.EXTERIOR:
          case WallType.CURTAIN:
            return 'exterior'

          case WallType.LOAD_BEARING:
            return 'structural'

          default:
            return 'unknown'
        }
      }

      // Test the function with different enum values
      expect(getWallCategory(WallType.STANDARD)).toBe('interior')
      expect(getWallCategory(WallType.PARTITION)).toBe('interior')
      expect(getWallCategory(WallType.EXTERIOR)).toBe('exterior')
      expect(getWallCategory(WallType.LOAD_BEARING)).toBe('structural')
    })

    it('should be usable as object keys', () => {
      // Create an object using enum values as keys
      const wallThicknesses: Record<WallType, number> = {
        [WallType.STANDARD]: 100,
        [WallType.LOAD_BEARING]: 200,
        [WallType.PARTITION]: 75,
        [WallType.EXTERIOR]: 300,
        [WallType.CURTAIN]: 50,
        [WallType.HALF_HEIGHT]: 100,
      }

      // Verify that we can access values using enum keys
      expect(wallThicknesses[WallType.STANDARD]).toBe(100)
      expect(wallThicknesses[WallType.LOAD_BEARING]).toBe(200)
      expect(wallThicknesses[WallType.EXTERIOR]).toBe(300)
    })
  })

  describe('wallProperties Interface', () => {
    it('should define a valid wall with required properties', () => {
      // Create a wall object that conforms to the WallProperties interface
      const wall: WallProperties = {
        type: 'wall',
        wallType: WallType.STANDARD,
        thickness: 100,
        height: 2700,
        centerline: [
          { x: 0, y: 0 },
          { x: 3000, y: 0 },
        ],
        openingIds: ['door-1', 'window-1'],
      }

      // Verify the wall has the expected properties
      expect(wall.type).toBe('wall')
      expect(wall.wallType).toBe(WallType.STANDARD)
      expect(wall.thickness).toBe(100)
      expect(wall.height).toBe(2700)
      expect(wall.centerline.length).toBe(2)
      expect(wall.centerline[0].x).toBe(0)
      expect(wall.centerline[0].y).toBe(0)
      expect(wall.centerline[1].x).toBe(3000)
      expect(wall.centerline[1].y).toBe(0)
      expect(wall.openingIds.length).toBe(2)
      expect(wall.openingIds[0]).toBe('door-1')
      expect(wall.openingIds[1]).toBe('window-1')
    })

    it('should support walls with complex centerlines', () => {
      // Create a wall with a more complex centerline
      const complexWall: WallProperties = {
        type: 'wall',
        wallType: WallType.EXTERIOR,
        thickness: 300,
        height: 3000,
        centerline: [
          { x: 0, y: 0 },
          { x: 3000, y: 0 },
          { x: 3000, y: 2000 },
          { x: 5000, y: 2000 },
        ],
        openingIds: ['window-2', 'window-3', 'door-2'],
      }

      // Verify the complex centerline
      expect(complexWall.centerline.length).toBe(4)
      expect(complexWall.centerline[0]).toEqual({ x: 0, y: 0 })
      expect(complexWall.centerline[1]).toEqual({ x: 3000, y: 0 })
      expect(complexWall.centerline[2]).toEqual({ x: 3000, y: 2000 })
      expect(complexWall.centerline[3]).toEqual({ x: 5000, y: 2000 })
    })

    it('should be usable in arrays and collections', () => {
      // Create an array of walls
      const walls: WallProperties[] = [
        {
          type: 'wall',
          wallType: WallType.STANDARD,
          thickness: 100,
          height: 2700,
          centerline: [
            { x: 0, y: 0 },
            { x: 4000, y: 0 },
          ],
          openingIds: ['door-1'],
        },
        {
          type: 'wall',
          wallType: WallType.STANDARD,
          thickness: 100,
          height: 2700,
          centerline: [
            { x: 4000, y: 0 },
            { x: 4000, y: 3000 },
          ],
          openingIds: ['window-1'],
        },
        {
          type: 'wall',
          wallType: WallType.STANDARD,
          thickness: 100,
          height: 2700,
          centerline: [
            { x: 4000, y: 3000 },
            { x: 0, y: 3000 },
          ],
          openingIds: ['window-2'],
        },
        {
          type: 'wall',
          wallType: WallType.STANDARD,
          thickness: 100,
          height: 2700,
          centerline: [
            { x: 0, y: 3000 },
            { x: 0, y: 0 },
          ],
          openingIds: [],
        },
      ]

      // Verify array operations
      expect(walls.length).toBe(4)

      // Test array methods
      const wallsWithOpenings = walls.filter(wall => wall.openingIds.length > 0)
      expect(wallsWithOpenings.length).toBe(3)

      const totalOpenings = walls.reduce((sum, wall) => sum + wall.openingIds.length, 0)
      expect(totalOpenings).toBe(3)
    })

    it('should be usable as function parameters and return values', () => {
      // Define a function that takes a WallProperties parameter
      function calculateWallLength(wall: WallProperties): number {
        let length = 0
        for (let i = 1; i < wall.centerline.length; i++) {
          const p1 = wall.centerline[i - 1]
          const p2 = wall.centerline[i]
          const dx = p2.x - p1.x
          const dy = p2.y - p1.y
          length += Math.sqrt(dx * dx + dy * dy)
        }
        return length
      }

      // Define a function that returns a WallProperties object
      function createWall(start: Point, end: Point, wallType: WallType = WallType.STANDARD): WallProperties {
        return {
          type: 'wall',
          wallType,
          thickness: wallType === WallType.EXTERIOR ? 300 : 100,
          height: 2700,
          centerline: [start, end],
          openingIds: [],
        }
      }

      // Test the functions
      const wall = createWall({ x: 0, y: 0 }, { x: 3000, y: 4000 })
      expect(wall.wallType).toBe(WallType.STANDARD)
      expect(wall.thickness).toBe(100)
      expect(wall.centerline[0]).toEqual({ x: 0, y: 0 })
      expect(wall.centerline[1]).toEqual({ x: 3000, y: 4000 })

      const length = calculateWallLength(wall)
      expect(length).toBe(5000) // 3-4-5 triangle

      // Test with a different wall type
      const exteriorWall = createWall({ x: 0, y: 0 }, { x: 5000, y: 0 }, WallType.EXTERIOR)
      expect(exteriorWall.wallType).toBe(WallType.EXTERIOR)
      expect(exteriorWall.thickness).toBe(300)

      const exteriorLength = calculateWallLength(exteriorWall)
      expect(exteriorLength).toBe(5000)
    })
  })
})
