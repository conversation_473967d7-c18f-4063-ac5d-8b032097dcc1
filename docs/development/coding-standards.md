# Coding Standards

This document outlines the coding standards and conventions to be followed when contributing to the RenoPilot.JS.Shapes2 project. Adhering to these standards ensures code consistency, readability, and maintainability.

## 1. Code Formatting (Prettier)

*   **Tool:** [Prettier](https://prettier.io/) is used for automatic code formatting.
*   **Configuration:** Prettier rules are defined in `.prettierrc.json` (or `package.json` under the `prettier` key) and `.prettierignore`.
*   **Enforcement:**
    *   It's highly recommended to configure your editor to format on save using Prettier.
    *   A pre-commit hook (via Husky and lint-staged) should automatically format staged files.
    *   You can manually check formatting with `npm run format:check` and apply formatting with `npm run format`.
*   **Key Principles (Typical Prettier Defaults):**
    *   Consistent indentation (usually 2 spaces).
    *   Maximum line length (e.g., 80 or 100 characters).
    *   Consistent use of quotes (e.g., single quotes for JavaScript/TypeScript, double quotes for JSX attributes).
    *   Trailing commas where appropriate.
    *   Consistent spacing around operators and keywords.

## 2. Code Linting (ESLint)

*   **Tool:** [ESLint](https://eslint.org/) is used for identifying and reporting on patterns in JavaScript and TypeScript code.
*   **Configuration:** ESLint rules are defined in `eslint.config.ts` (or `.eslintrc.js`, `.eslintrc.json`). This includes rules from plugins like `@typescript-eslint/eslint-plugin`, `eslint-plugin-react`, `eslint-plugin-react-hooks`, `eslint-plugin-jsx-a11y`, etc.
*   **Enforcement:**
    *   Integrate ESLint into your editor for real-time feedback.
    *   A pre-commit hook (via Husky and lint-staged) should run ESLint on staged files.
    *   CI/CD pipelines should run ESLint to catch issues before merging.
    *   You can manually run ESLint with `npm run lint` and attempt to auto-fix issues with `npm run lint -- --fix`.
*   **Key Principles:**
    *   **No Unused Variables/Imports:** Remove or comment out unused code.
    *   **Type Safety:** Leverage TypeScript's type system. Avoid `any` where possible; provide specific types.
    *   **React Best Practices:** Follow rules for hooks, keys in lists, accessibility (jsx-a11y), etc.
    *   **Modularity:** Keep files and functions focused on a single responsibility.
    *   **Readability:** Use clear and descriptive naming for variables, functions, and classes.
    *   **Error Handling:** Implement proper error handling mechanisms.

## 3. Naming Conventions

*   **Variables and Functions:** Use `camelCase` (e.g., `myVariable`, `calculateValue`).
*   **Classes and Interfaces (TypeScript):** Use `PascalCase` (e.g., `MyClass`, `ShapeProperties`).
*   **React Components:** Use `PascalCase` for component names (e.g., `ToolbarComponent.tsx`). File names should also be `PascalCase.tsx`.
*   **Constants:** Use `UPPER_SNAKE_CASE` for constants that are truly immutable and widely used (e.g., `MAX_SHAPES`). For constants local to a module or component, `camelCase` might be acceptable if they are not exported or are part of a configuration object.
*   **Files and Directories:** Use `kebab-case` for general directories and non-component files (e.g., `src/core/utils`, `src/services/element-creation-service.ts`). For React components, use `PascalCase` as mentioned.
*   **Boolean Variables:** Prefix with `is`, `has`, `should` (e.g., `isVisible`, `hasError`, `shouldUpdate`).
*   **Event Handlers:** Prefix with `handle` (e.g., `handleClick`, `handleSubmit`). For props that are event handlers, use `on` (e.g., `onClick`, `onSubmit`).

## 4. TypeScript Best Practices

*   **Explicit Types:** Provide explicit types for function parameters, return values, and variable declarations where type inference is not obvious or for public APIs of modules.
*   **Interfaces vs. Types:** Use `interface` for defining the shape of objects and classes. Use `type` for unions, intersections, primitives, or more complex type manipulations.
*   **Readonly:** Use `readonly` for properties that should not be modified after an object is created.
*   **Enums:** Use string enums for better readability and debugging, or `as const` for simple sets of string constants.
    ```typescript
    // String Enum
    export enum ShapeType {
      Rectangle = 'RECTANGLE',
      Ellipse = 'ELLIPSE',
    }

    // As Const
    export const ToolTypes = {
      SELECT: 'select',
      RECTANGLE: 'rectangle',
    } as const;
    export type ToolType = typeof ToolTypes[keyof typeof ToolTypes];
    ```
*   **Avoid `any`:** Strive to use specific types. If `any` is necessary, consider `unknown` first and perform type checking.
*   **Non-null Assertion Operator (`!`):** Use sparingly and only when you are certain that a value will not be `null` or `undefined`.

## 5. React Best Practices

*   **Functional Components and Hooks:** Prefer functional components with Hooks over class components.
*   **Keys:** Always provide unique `key` props when rendering lists of elements.
*   **Props:** Keep props minimal and specific. Use object destructuring for props.
*   **State Management:** Use Zustand for global state. For local component state, `useState` is appropriate.
*   **Memoization:** Use `React.memo`, `useMemo`, and `useCallback` judiciously to optimize performance, but only after profiling and identifying bottlenecks.
*   **Accessibility (a11y):** Follow ARIA guidelines and use semantic HTML. Leverage `eslint-plugin-jsx-a11y`.
*   **Component Structure:** Organize components into logical directories. Consider co-locating styles and tests with their components if not using global styles extensively.

## 6. Commit Messages (Conventional Commits)

*   **Standard:** Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification.
*   **Tool:** `commitlint` (configured in `commitlint.config.ts`) is used to enforce this standard.
*   **Format:**
    ```
    <type>[optional scope]: <description>

    [optional body]

    [optional footer(s)]
    ```
*   **Common Types:**
    *   `feat`: A new feature.
    *   `fix`: A bug fix.
    *   `docs`: Documentation only changes.
    *   `style`: Changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc).
    *   `refactor`: A code change that neither fixes a bug nor adds a feature.
    *   `perf`: A code change that improves performance.
    *   `test`: Adding missing tests or correcting existing tests.
    *   `build`: Changes that affect the build system or external dependencies (example scopes: gulp, broccoli, npm).
    *   `ci`: Changes to our CI configuration files and scripts (example scopes: Travis, Circle, BrowserStack, SauceLabs).
    *   `chore`: Other changes that don't modify `src` or `test` files.
*   **Example:**
    ```
    feat(shapes): add support for polygon tool

    Implemented the polygon drawing tool allowing users to create multi-sided shapes.
    Users can click to define vertices and double-click to complete the shape.

    Fixes #123
    ```

## 7. Comments

*   **Purpose:** Write comments to explain *why* something is done, not *what* is being done (the code should be self-explanatory for the *what*).
*   **Clarity:** Comments should be clear, concise, and up-to-date.
*   **JSDoc/TSDoc:** For public functions, classes, and complex logic, use JSDoc/TSDoc comments to explain parameters, return values, and purpose.
    ```typescript
    /**
     * Calculates the area of a given shape.
     * @param shape - The shape object for which to calculate the area.
     * @returns The calculated area, or 0 if the shape type is unsupported.
     */
    function calculateArea(shape: Shape): number {
      // ... implementation ...
    }
    ```
*   **TODOs:** Use `// TODO:` for tasks that need to be done later. Include context or a ticket number if possible.
*   **FIXMEs:** Use `// FIXME:` for known issues that need to be addressed.

## 8. General Principles

*   **DRY (Don't Repeat Yourself):** Avoid duplicating code. Use functions, components, and hooks to encapsulate reusable logic.
*   **KISS (Keep It Simple, Stupid):** Prefer simple solutions over complex ones.
*   **YAGNI (You Ain't Gonna Need It):** Don't implement features or write code that isn't currently required.
*   **Readability:** Write code that is easy for other developers (and your future self) to understand.

By following these coding standards, we can maintain a high-quality, collaborative, and efficient development process.