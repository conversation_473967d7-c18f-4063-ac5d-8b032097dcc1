/**
 * Validation Service Types
 *
 * This file defines types related to the validation service functionality.
 *
 * @module types/services/validation
 */

/**
 * Validation severity levels
 */
export enum ValidationSeverity {
  /** Information-level validation issues */
  INFO = 'info',
  /** Warning-level validation issues */
  WARNING = 'warning',
  /** Error-level validation issues */
  ERROR = 'error',
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  /** Whether validation passed */
  valid: boolean
  /** List of validation issues */
  issues: ValidationIssue[]
  /** Original data that was validated */
  data: unknown
}

/**
 * Validation issue interface
 */
export interface ValidationIssue {
  /** Severity level */
  severity: ValidationSeverity
  /** Error message */
  message: string
  /** Affected property path */
  path?: string
  /** Error code */
  code?: string
  /** Additional context */
  context?: Record<string, unknown>
}

/**
 * Validation options interface
 */
export interface ValidationOptions {
  /** Whether to stop on first error */
  stopOnFirstError?: boolean
  /** Custom validation context */
  context?: Record<string, unknown>
  /** Specific properties to validate */
  properties?: string[]
  /** Whether to validate all or just specified properties */
  validateOnly?: boolean
}

/**
 * Validator function type
 */
export type ValidatorFn<T = unknown> = (
  data: T,
  options?: ValidationOptions
) => ValidationResult

/**
 * Validation service interface
 */
export interface ValidationService {
  /** Validate data against schema or rules */
  validate: <T>(data: T, schema: unknown, options?: ValidationOptions) => ValidationResult
  /** Register a custom validator */
  registerValidator: (name: string, validator: ValidatorFn) => void
  /** Get a registered validator */
  getValidator: (name: string) => ValidatorFn | undefined
  /** Create a validation result */
  createResult: (valid: boolean, data: unknown, issues?: ValidationIssue[]) => ValidationResult
  /** Create a validation issue */
  createIssue: (severity: ValidationSeverity, message: string, path?: string, code?: string, context?: Record<string, unknown>) => ValidationIssue
}
