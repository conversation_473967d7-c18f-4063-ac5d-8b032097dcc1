```mermaid
sequenceDiagram
    actor User
    participant <PERSON><PERSON><PERSON> as "Toolbar Component (React)"
    participant <PERSON><PERSON> as "Canvas Component (React)"
    participant <PERSON>hapeStore as "Shape Store (Zustand)"
    participant HistoryStore as "History Store (Zustand)"
    participant CoreLogic as "Core Shape Logic (TypeScript)"
    participant React as "React Renderer"

    User->>Toolbar: Selects 'Rectangle' tool
    Toolbar->>ShapeStore: updateSelectedTool('rectangle')
    ShapeStore-->>React: Notifies UI update (e.g., tool icon highlighted)
    React-->>Toolbar: Re-renders Toolbar

    User->>Canvas: Clicks and drags to draw rectangle (mouseDown, mouseMove, mouseUp)
    Canvas->>CoreLogic: calculateNewShape(eventData, 'rectangle')
    CoreLogic-->>Canvas: Returns newRectangleData
    
    Canvas->>ShapeStore: addShape(newRectangleData)
    activate ShapeStore
    ShapeStore->>HistoryStore: recordAction({ type: 'ADD_SHAPE', payload: newRectangleData })
    activate HistoryStore
    HistoryStore-->>ShapeStore: Action recorded
    deactivate HistoryStore
    ShapeStore-->>React: Notifies state change (new shape added)
    deactivate ShapeStore

    React-->>Canvas: Re-renders Canvas with new rectangle
    React-->>User: Displays new rectangle

    %% Example: User selects the new shape and changes its color
    User->>Canvas: Clicks on the new rectangle
    Canvas->>ShapeStore: setSelectedShape(rectangleId)
    activate ShapeStore
    ShapeStore-->>React: Notifies state change (shape selected)
    deactivate ShapeStore
    React-->>Canvas: Re-renders Canvas (shape shows selection handles)
    React-->>User: Shows selection handles

    User->>Toolbar: Selects 'Red' color from color picker
    Toolbar->>ShapeStore: updateShapeProperties(rectangleId, { fill: 'red' })
    activate ShapeStore
    ShapeStore->>HistoryStore: recordAction({ type: 'UPDATE_SHAPE', payload: { id: rectangleId, changes: { fill: 'red' } } })
    activate HistoryStore
    HistoryStore-->>ShapeStore: Action recorded
    deactivate HistoryStore
    ShapeStore-->>React: Notifies state change (shape color updated)
    deactivate ShapeStore

    React-->>Canvas: Re-renders Canvas (rectangle is now red)
    React-->>User: Displays rectangle in red color

```