/**
 * Provides helper functions for publishing data-related events to the application's event bus.
 *
 * @remarks
 * This module covers history operations (undo/redo), template application, data export, and state management.
 * All functions follow dependency injection principles by accepting an EventBus instance as their first parameter.
 *
 * @module services/core/event-bus/helpers/publishers/dataPublishers
 */

import type { CoreConfig } from '@/config'
import type { EventBus } from '@/types/services/events'
import type { ExportEvent, HistoryEvent, TemplateEvent } from '@/types/services/events/dataEvents'
import type { TypedEvent } from '@/types/services/events/eventCore'
import { AppEventType } from '@/types/services/events'

// --- History Events ---

/**
 * Publishes a history undo event.
 *
 * @remarks
 * Requests an undo operation on the application's history stack.
 *
 * @param eventBus - The EventBus instance to use for publishing.
 */
export function publishHistoryUndo(eventBus: EventBus): void {
  const event: HistoryEvent = { type: AppEventType.HistoryUndo, payload: {} }
  eventBus.publish(event)
}

/**
 * Publishes a history redo event.
 *
 * @remarks
 * Requests a redo operation on the application's history stack.
 *
 * @param eventBus - The EventBus instance to use for publishing.
 */
export function publishHistoryRedo(eventBus: EventBus): void {
  const event: HistoryEvent = { type: AppEventType.HistoryRedo, payload: {} }
  eventBus.publish(event)
}

// --- Template Events ---

/**
 * Publishes a template apply event.
 *
 * @remarks
 * Requests application of a template to the current document.
 *
 * @param eventBus - The EventBus instance to use for publishing.
 * @param templateId - ID of the template to apply.
 */
export function publishTemplateApply(eventBus: EventBus, templateId: string): void {
  const event: TemplateEvent = {
    type: AppEventType.TemplateApply,
    payload: { templateId },
  }
  eventBus.publish(event)
}

// --- Export Events ---

/**
 * Publishes an export request event.
 *
 * @remarks
 * This function allows you to publish export-related events to the
 * application's event bus, enabling decoupled communication between modules.
 *
 * @param eventBus - The EventBus instance to use for publishing.
 * @param phase - The phase of the process (request, prepare, progress, complete, error)
 * @param payload - The payload data for the export event.
 */
export function publishExportRequest(
  eventBus: EventBus,
  phase: 'request' | 'prepare' | 'progress' | 'complete' | 'error',
  payload: ExportEvent['payload'],
): void {
  let eventType: AppEventType

  switch (phase) {
    case 'request': eventType = AppEventType.ExportRequest; break
    case 'prepare': eventType = AppEventType.ExportPrepare; break
    case 'progress': eventType = AppEventType.ExportProgress; break
    case 'complete': eventType = AppEventType.ExportComplete; break
    case 'error': eventType = AppEventType.ExportError; break
    default: throw new Error(`Invalid export phase: ${phase}`)
  }

  const event: ExportEvent = {
    type: eventType,
    payload,
  }
  eventBus.publish(event)
}

// --- Config Events ---

/**
 * Publishes a config updated event.
 *
 * @remarks
 * Notifies the system that the core configuration has been updated.
 *
 * @param eventBus - The EventBus instance to use for publishing.
 * @param config - Partial configuration object containing updated settings.
 */
export function publishConfigUpdated(eventBus: EventBus, config: Partial<CoreConfig>): void {
  const event: TypedEvent<{ config: CoreConfig }> = {
    type: AppEventType.ConfigUpdated,
    payload: { config: config as CoreConfig },
  }
  eventBus.publish(event)
}
