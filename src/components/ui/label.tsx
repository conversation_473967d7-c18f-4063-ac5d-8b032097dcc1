/**
 * Label Component
 *
 * A styled label component built on top of Radix UI's Label primitive.
 * Provides consistent styling and accessibility features for form labels.
 *
 * Features:
 * - Automatic peer styling with form controls
 * - Proper ARIA attributes
 * - Disabled state handling
 * - Customizable via className
 */

import type { VariantProps } from 'class-variance-authority'

import * as LabelPrimitive from '@radix-ui/react-label'
import { cva } from 'class-variance-authority'
import * as React from 'react'

import { cn } from '@/lib/utils'

/**
 * Label style variants using class-variance-authority
 * Defines the base styles for the label component
 */
const labelVariants = cva(
  'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
)

/**
 * Label component for form controls
 *
 * @example
 * ```tsx
 * <Label htmlFor="email">Email address</Label>
 * <Input id="email" type="email" />
 * ```
 */
function Label({ ref, className, ...props }: React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> & VariantProps<typeof labelVariants> & { ref?: React.RefObject<React.ComponentRef<typeof LabelPrimitive.Root> | null> }) {
  return (
    <LabelPrimitive.Root
      ref={ref}
      className={cn(labelVariants(), className)}
      {...props}
    />
  )
}
Label.displayName = LabelPrimitive.Root.displayName

export { Label }
