import type { Arc } from '@/types/core/element/path/arcPathTypes'
import type { Cubic } from '@/types/core/element/path/cubicPathTypes'
import type { Line } from '@/types/core/element/path/linePathTypes'
import type { Polyline } from '@/types/core/element/path/polylinePathTypes'
import type { Quadratic } from '@/types/core/element/path/quadraticPathTypes'

/**
 * Wall element type definitions.
 * @module types/core/element/design/wallDesignTypes
 * @remarks
 * This module contains type definitions for architectural wall elements,
 * including wall classifications and geometric properties.
 * @see {@link OpeningProperties} for related opening element definitions
 */

/**
 * Enumerates standard types of wall constructions used in architectural design.
 *
 * @remarks
 * These classifications help define the structural and functional characteristics of walls.
 */
export enum WallType {
  /** A typical interior wall, often non-load-bearing. */
  STANDARD = 'standard',
  /** A wall designed to support structural loads from other parts of the building. */
  LOAD_BEARING = 'load-bearing',
  /** An interior wall that divides spaces but does not support structural loads. */
  PARTITION = 'partition',
  /** An outer wall of a building, exposed to the weather. */
  EXTERIOR = 'exterior',
  /** A non-structural outer covering of a building, typically of glass. */
  CURTAIN = 'curtain',
  /** A wall that does not extend to the full ceiling height, used as a divider. */
  HALF_HEIGHT = 'half-height',
  /** A wall designed to resist lateral pressure of soil or water. */
  RETAINING = 'retaining', // Added from WallCreator context
  Interior = 'interior',
  Structural = 'structural',
}

/**
 * Enumerates common types of wall construction methods or materials.
 */
export enum WallConstruction {
  /** Standard wood or metal stud framing with sheathing (e.g., drywall). */
  FRAMED = 'framed',
  /** Solid masonry construction (e.g., brick, concrete block). */
  MASONRY = 'masonry',
  /** Poured concrete construction. */
  CONCRETE = 'concrete',
  /** Glass panel construction, often used for curtain walls or interior partitions. */
  GLASS_PANEL = 'glass-panel',
  /** Other or custom construction type. */
  OTHER = 'other',
  Drywall = 'drywall',
  Brick = 'brick',
  Wood = 'wood',
  Glass = 'glass',
}

/**
 * Defines the geometric and material properties of a wall element.
 *
 * @remarks
 * This interface specifies the complete set of attributes required to represent a wall
 * in architectural designs. It includes its structural classification, dimensions (thickness, height),
 * geometric definition via a centerline, and references to any openings (doors, windows) it contains.
 * It is assumed that all dimensional measurements are in millimeters unless otherwise specified.
 *
 * @see {@link Point} for the definition of geometric points used in the centerline.
 * @see {@link OpeningProperties} for properties of openings that might be contained within the wall.
 * @see {@link WallType} for the classification of the wall's structural type.
 */
export interface WallProperties {
  /**
   * The general type identifier for this element, e.g., 'wall'.
   * This helps in distinguishing it from other element types.
   */
  type: string

  /** The structural or functional classification of the wall, as defined by {@link WallType}. */
  wallType: WallType

  /** The thickness of the wall, typically measured in millimeters. */
  thickness: number

  /** The height of the wall, typically measured in millimeters. */
  height: number

  /**
   * The geometric path defining the wall's shape and direction.
   * This can be a Line, Polyline, Arc, Quadratic Bezier curve, or Cubic Bezier curve.
   * The wall's geometry is typically extruded from this path based on its thickness.
   */
  path: Line | Polyline | Arc | Quadratic | Cubic

  /**
   * An array of unique string identifiers for opening elements (e.g., doors, windows)
   * that are contained within this wall.
   */
  openingIds: string[]
}

/**
 * Wall design element types
 */

// Wall element type
export enum WallElementType {
  Wall = 'wall',
  Paint = 'paint',
  Wallpaper = 'wallpaper',
  Trim = 'trim',
  Panel = 'panel',
  Tile = 'tile',
  Molding = 'molding',
}

// Paint types
export enum PaintType {
  Flat = 'flat',
  Eggshell = 'eggshell',
  Satin = 'satin',
  SemiGloss = 'semi-gloss',
  HighGloss = 'high-gloss',
}

// Wallpaper types
export enum WallpaperType {
  Solid = 'solid',
  Printed = 'printed',
  Textured = 'textured',
  Vinyl = 'vinyl',
  Grasscloth = 'grasscloth',
}

// Trim types
export enum TrimType {
  Baseboard = 'baseboard',
  Crown = 'crown',
  Casing = 'casing',
  Chair = 'chair',
  Picture = 'picture',
}
