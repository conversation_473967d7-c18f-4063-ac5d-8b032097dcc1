import type { StateC<PERSON>, StoreApi, UseBoundStore } from 'zustand'
import type { TaskModule, ZLevel } from '@/types/core/layerPanelTypes'
import type { MinorCategory } from '@/types/core/majorMinorTypes'

import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'
import { TaskStatus } from '@/types/core/layerPanelTypes'
import { MajorCategory } from '@/types/core/majorMinorTypes'

// Define the state structure
export interface LayerState {
  modules: TaskModule[]
  currentModuleId: MajorCategory | null
  currentStepId: MinorCategory | null
  currentZLevelId: string | null
  // TODO: Add other states like editing states if needed
}

// Define actions
export interface LayerActions {
  setModules: (modules: TaskModule[]) => void
  selectLayerIdentifiers: (moduleIdString: string | null, stepIdString?: string | null, zLevelId?: string | null) => void
  setCurrentZLevelFocus: (moduleId: MajorCategory, stepId: MinorCategory, zLevelId: string) => void
  updateStepStatus: (moduleId: MajorCategory, stepId: MinorCategory, status: TaskStatus) => void
  toggleZLevelActive: (moduleId: MajorCategory, stepId: MinorCategory, zLevelId: string) => void
  deleteZLevel: (moduleId: MajorCategory, stepId: MinorCategory, zLevelId: string) => void
  addZLevel: (moduleId: MajorCategory, stepId: MinorCategory, name?: string) => void
  renameZLevel: (moduleId: MajorCategory, stepId: MinorCategory, zLevelId: string, newName: string) => void
  resetLayers: () => void
  // TODO: Add other actions: renameZLevel, etc.
}

// Initial State - Placeholder, should be populated from a default config or empty
const initialModules: TaskModule[] = [
  // Example structure, replace with actual default or load logic
  // {
  //   id: MajorCategory.BASE, name: 'Base', status: TaskStatus.PENDING, progress: 0, order: 1,
  //   steps: [
  //     {
  //       id: 'architecture' as MinorCategory, name: 'Architecture', status: TaskStatus.PENDING, order: 1,
  //       targetMajorCategory: MajorCategory.BASE, targetMinorCategory: 'architecture' as MinorCategory,
  //       zLevels: [{ id: 'base-arch-default', name: 'Default', active: true, zIndex: 0 }]
  //     },
  //     {
  //       id: 'coverings' as MinorCategory, name: 'Coverings', status: TaskStatus.PENDING, order: 2,
  //       targetMajorCategory: MajorCategory.BASE, targetMinorCategory: 'coverings' as MinorCategory,
  //       zLevels: [{ id: 'base-cov-default', name: 'Default', active: true, zIndex: 10 }]
  //     }
  //   ]
  // },
  // Add other major categories (CEILING, FURNITURE) similarly

  // Copied from EditorLayout.tsx placeholderLayerPanelModules
  {
    id: MajorCategory.BASE,
    name: 'Base',
    status: TaskStatus.PENDING,
    progress: 0,
    order: 1,
    steps: [
      {
        id: 'architecture' as MinorCategory,
        name: 'Architecture', // Order 1
        zLevels: [{ id: 'base-arch-1', name: 'Default Architecture', active: true, zIndex: 0 }],
        status: TaskStatus.PENDING,
        order: 1,
        targetMajorCategory: MajorCategory.BASE,
        targetMinorCategory: 'architecture' as MinorCategory,
      },
      {
        id: 'coverings' as MinorCategory,
        name: 'Coverings', // Order 2 (includes rugs now)
        zLevels: [{ id: 'base-cov-1', name: 'Default Covering', active: true, zIndex: 10 }],
        status: TaskStatus.PENDING,
        order: 2,
        targetMajorCategory: MajorCategory.BASE,
        targetMinorCategory: 'coverings' as MinorCategory,
      },
    ],
  },
  {
    id: MajorCategory.CEILING,
    name: 'Ceiling',
    status: TaskStatus.PENDING,
    progress: 0,
    order: 2,
    steps: [
      {
        id: 'utilities' as MinorCategory,
        name: 'Utilities', // Order 1
        zLevels: [{ id: 'ceil-util-1', name: 'Default Utility', active: true, zIndex: 20 }],
        status: TaskStatus.PENDING,
        order: 1,
        targetMajorCategory: MajorCategory.CEILING,
        targetMinorCategory: 'utilities' as MinorCategory,
      },
      {
        id: 'lighting' as MinorCategory,
        name: 'Lighting', // Order 2 (Includes fans)
        zLevels: [{ id: 'ceil-light-1', name: 'Default Light/Fan', active: true, zIndex: 30 }],
        status: TaskStatus.PENDING,
        order: 2,
        targetMajorCategory: MajorCategory.CEILING,
        targetMinorCategory: 'lighting' as MinorCategory,
      },
    ],
  },
  {
    id: MajorCategory.FURNITURE,
    name: 'Furniture',
    status: TaskStatus.PENDING,
    progress: 0,
    order: 3,
    steps: [
      {
        id: 'storage' as MinorCategory,
        name: 'Storage', // Order 1
        zLevels: [{ id: 'furn-store-1', name: 'Default Storage', active: true, zIndex: 40 }],
        status: TaskStatus.PENDING,
        order: 1,
        targetMajorCategory: MajorCategory.FURNITURE,
        targetMinorCategory: 'storage' as MinorCategory,
      },
      {
        id: 'appliances' as MinorCategory,
        name: 'Appliances', // Order 2 (includes stairs)
        zLevels: [{ id: 'furn-app-1', name: 'Default Appliance', active: true, zIndex: 50 }],
        status: TaskStatus.PENDING,
        order: 2,
        targetMajorCategory: MajorCategory.FURNITURE,
        targetMinorCategory: 'appliances' as MinorCategory,
      },
      {
        id: 'beds' as MinorCategory,
        name: 'Beds', // Order 3
        zLevels: [{ id: 'furn-bed-1', name: 'Default Bed', active: true, zIndex: 60 }],
        status: TaskStatus.PENDING,
        order: 3,
        targetMajorCategory: MajorCategory.FURNITURE,
        targetMinorCategory: 'beds' as MinorCategory,
      },
      {
        id: 'tables' as MinorCategory,
        name: 'Tables', // Order 4
        zLevels: [{ id: 'furn-table-1', name: 'Default Table', active: true, zIndex: 70 }],
        status: TaskStatus.PENDING,
        order: 4,
        targetMajorCategory: MajorCategory.FURNITURE,
        targetMinorCategory: 'tables' as MinorCategory,
      },
      {
        id: 'seating' as MinorCategory,
        name: 'Seating', // Order 5
        zLevels: [{ id: 'furn-seat-1', name: 'Default Seating', active: true, zIndex: 80 }],
        status: TaskStatus.PENDING,
        order: 5,
        targetMajorCategory: MajorCategory.FURNITURE,
        targetMinorCategory: 'seating' as MinorCategory,
      },
      {
        id: 'decor' as MinorCategory,
        name: 'Decor', // Order 6 (Exclusive to Furniture for workflow steps)
        zLevels: [{ id: 'furn-decor-1', name: 'Default Decor', active: true, zIndex: 90 }],
        status: TaskStatus.PENDING,
        order: 6,
        targetMajorCategory: MajorCategory.FURNITURE,
        targetMinorCategory: 'decor' as MinorCategory,
      },
    ],
  },
]

// Define ALL_MINOR_CATEGORIES for validation as MinorCategory is a type alias
const ALL_MINOR_CATEGORIES: MinorCategory[] = ['coverings', 'architecture', 'decor', 'utilities', 'lighting', 'storage', 'beds', 'tables', 'seating', 'appliances']

const initialState: LayerState = {
  modules: initialModules,
  currentModuleId: initialModules.length > 0 ? initialModules[0].id : null,
  currentStepId: initialModules.length > 0 && initialModules[0].steps.length > 0 ? initialModules[0].steps[0].id : null,
  currentZLevelId: initialModules.length > 0 && initialModules[0].steps.length > 0 && initialModules[0].steps[0].zLevels.length > 0
    ? initialModules[0].steps[0].zLevels[0].id
    : null,
}

// Define the full store type
type LayerStoreType = LayerState & LayerActions

// Create the store state creator function
const layerStateCreator: StateCreator<LayerStoreType, [], []> = (set, get) => ({
  ...initialState,

  setModules: modules => set({ modules }),

  resetLayers: () => set({
    modules: initialModules, // Reset modules to initialModules
    currentModuleId: initialModules.length > 0 ? initialModules[0].id : null,
    currentStepId: initialModules.length > 0 && initialModules[0].steps.length > 0 ? initialModules[0].steps[0].id : null,
    currentZLevelId: initialModules.length > 0 && initialModules[0].steps.length > 0 && initialModules[0].steps[0].zLevels.length > 0
      ? initialModules[0].steps[0].zLevels[0].id
      : null,
  }), // End of resetLayers action

  selectLayerIdentifiers: (moduleIdString, stepIdString = null, zLevelId = null) => {
    const state = get()
    let validatedModuleId: MajorCategory | null = null
    let validatedStepId: MinorCategory | null = null
    let validatedZLevelId: string | null = zLevelId

    // Validate moduleIdString
    if (moduleIdString !== null && (Object.values(MajorCategory) as string[]).includes(moduleIdString)) {
      validatedModuleId = moduleIdString as MajorCategory
    }
    else if (moduleIdString !== null) {
      console.warn(`[layerStore] Invalid Module ID string provided: ${moduleIdString}. Resetting selection.`)
    }
    // if moduleIdString is null, validatedModuleId remains null

    // Validate stepIdString only if moduleId is valid
    if (validatedModuleId !== null && stepIdString !== null && ALL_MINOR_CATEGORIES.includes(stepIdString as MinorCategory)) {
      const currentModule = state.modules.find(m => m.id === validatedModuleId)
      // Also check if this step actually exists in the current module structure
      if (currentModule?.steps.find(s => s.id === stepIdString)) {
        validatedStepId = stepIdString as MinorCategory
      }
      else {
        console.warn(`[layerStore] Step ID string ${stepIdString} not found in module ${validatedModuleId}. Resetting step/zLevel.`)
        // validatedStepId remains null
      }
    }
    else if (validatedModuleId !== null && stepIdString !== null) {
      console.warn(`[layerStore] Invalid Step ID string provided: ${stepIdString} for module ${validatedModuleId}. Resetting step/zLevel.`)
      // validatedStepId remains null
    }
    // if stepIdString is null, validatedStepId remains null

    // If step became invalid or was null, zLevelId must also be nullified
    if (validatedStepId === null) {
      validatedZLevelId = null
    }

    // Validate zLevelId only if module and step are valid
    if (validatedModuleId !== null && validatedStepId !== null && validatedZLevelId !== null) {
      const currentModule = state.modules.find(m => m.id === validatedModuleId)
      const currentStep = currentModule?.steps.find(s => s.id === validatedStepId)
      if (!currentStep?.zLevels.find(z => z.id === validatedZLevelId)) {
        console.warn(`[layerStore] Z-Level ID ${validatedZLevelId} not found in step ${validatedStepId}. Resetting zLevel.`)
        validatedZLevelId = null
      }
    }

    // Auto-select first step/zLevel if only a valid module is provided and no step was given
    if (validatedModuleId !== null && (stepIdString === null || stepIdString === '') && validatedStepId === null) {
      const module = state.modules.find(m => m.id === validatedModuleId)
      if (module && module.steps.length > 0) {
        const firstStepId = module.steps[0].id
        if (ALL_MINOR_CATEGORIES.includes(firstStepId)) {
          validatedStepId = firstStepId
          // Auto-select first Z-Level of this newly selected first step
          if (module.steps[0].zLevels.length > 0) {
            validatedZLevelId = module.steps[0].zLevels[0].id
          }
          else {
            validatedZLevelId = null // Step has no Z-Levels
          }
        }
        else {
          console.warn(`[layerStore] Auto-select: First step ID '${firstStepId}' in module ${validatedModuleId} is not a valid MinorCategory.`)
          validatedStepId = null
          validatedZLevelId = null
        }
      }
      else {
        validatedStepId = null // No steps in the module
        validatedZLevelId = null
      }
    }
    else if (validatedModuleId !== null && validatedStepId !== null && validatedZLevelId === null) {
      // NEW LOGIC: If module and step are valid, but zLevel is null (e.g., user selected a step, or previous zLevel was invalid)
      // Try to auto-select the first Z-Level of the current validatedStepId
      const module = state.modules.find(m => m.id === validatedModuleId)
      const step = module?.steps.find(s => s.id === validatedStepId)
      if (step && step.zLevels.length > 0) {
        validatedZLevelId = step.zLevels[0].id
        console.warn(`[layerStore] Auto-selected first Z-Level '${validatedZLevelId}' for step '${validatedStepId}' in module '${validatedModuleId}'.`)
      }
      else {
        // Step has no Z-Levels, so validatedZLevelId remains null
        console.warn(`[layerStore] Step '${validatedStepId}' in module '${validatedModuleId}' has no Z-Levels. currentZLevelId remains null.`)
      }
    }

    set({
      currentModuleId: validatedModuleId, // This is now MajorCategory | null
      currentStepId: validatedStepId, // This is now MinorCategory | null
      currentZLevelId: validatedZLevelId,
    })
  },

  setCurrentZLevelFocus: (moduleId, stepId, zLevelId) => set({
    currentModuleId: moduleId,
    currentStepId: stepId,
    currentZLevelId: zLevelId,
  }), // END OF setCurrentZLevelFocus

  updateStepStatus: (moduleId, stepId, status) => set(state => ({
    modules: state.modules.map(module =>
      module.id === moduleId // moduleId is MajorCategory here
        ? {
            ...module,
            steps: module.steps.map(step =>
              step.id === stepId ? { ...step, status } : step, // stepId is MinorCategory here
            ),
          }
        : module,
    ),
  })),

  toggleZLevelActive: (moduleId, stepId, zLevelId) => set((state) => {
    const newModules = state.modules.map((module) => {
      if (module.id === moduleId) {
        return {
          ...module,
          steps: module.steps.map((step) => {
            if (step.id === stepId) {
              return {
                ...step,
                zLevels: step.zLevels.map(zLayer => ({
                  ...zLayer,
                  // MODIFIED: Only toggle active state for the clicked layer
                  active: zLayer.id === zLevelId ? !zLayer.active : zLayer.active,
                })),
              }
            }
            return step
          }),
        }
      }
      return module
    })
    return {
      modules: newModules,
      // REMOVED: Do not update currentModuleId, currentStepId, or currentZLevelId here
      // currentModuleId: moduleId,
      // currentStepId: stepId,
      // currentZLevelId: zLevelId,
    }
  }),

  deleteZLevel: (moduleId, stepId, zLevelIdToDelete) => set((state) => {
    // moduleId, stepId are MajorCategory, MinorCategory
    let newCurrentZLevelId = state.currentZLevelId
    const newModules = state.modules.map((module) => {
      if (module.id === moduleId) {
        return {
          ...module,
          steps: module.steps.map((step) => {
            if (step.id === stepId) {
              const remainingZLevels = step.zLevels.filter(z => z.id !== zLevelIdToDelete)
              let newActiveZLevelInStep: ZLevel | undefined

              if (remainingZLevels.length > 0) {
                if (state.currentZLevelId === zLevelIdToDelete || !remainingZLevels.find(z => z.id === state.currentZLevelId)) {
                  // If deleted was current, or current is no longer in list, make first remaining active
                  newActiveZLevelInStep = remainingZLevels[0]
                  newCurrentZLevelId = newActiveZLevelInStep.id
                  remainingZLevels.forEach(z => z.active = (z.id === newCurrentZLevelId))
                }
                else {
                  // Current ZLevel is still valid and was not deleted, ensure it stays active
                  remainingZLevels.forEach(z => z.active = (z.id === state.currentZLevelId))
                  newCurrentZLevelId = state.currentZLevelId // Keep current active ZLevelId
                }
              }
              else {
                // No ZLevels left in this step
                newCurrentZLevelId = null
              }
              return { ...step, zLevels: remainingZLevels }
            }
            return step
          }),
        }
      }
      return module
    })

    // If the deleted zLevel was part of the current selection path,
    // and no new zLevel was selected in its place (e.g., step became empty of zLevels),
    // we might need to adjust currentStepId or currentModuleId.
    // For now, newCurrentZLevelId handles the zLevel part.
    // If newCurrentZLevelId is null but currentStepId is still the one we modified,
    // then the UI will reflect no selected ZLevel for that step.
    return {
      modules: newModules,
      currentZLevelId: newCurrentZLevelId,
      // currentModuleId and currentStepId remain, selectLayerIdentifiers can be called separately if needed.
    }
  }),

  addZLevel: (moduleId, stepId, name) => set((state) => {
    // moduleId, stepId are MajorCategory, MinorCategory from interface
    const newZLevelId = `z-${Date.now()}`
    let newZLevelName = name
    let newZIndex = 0

    const newModules = state.modules.map((module) => {
      if (module.id === moduleId) {
        return {
          ...module,
          steps: module.steps.map((step) => {
            if (step.id === stepId) {
              // Determine new name if not provided
              if (newZLevelName === undefined || newZLevelName === null || newZLevelName === '') {
                let nextLayerNum = 1
                const newLayerNamePattern = /^New Layer (\d+)$/
                const existingNewLayerNums: number[] = step.zLevels
                  .map((z) => {
                    const match = z.name.match(newLayerNamePattern)
                    return match ? Number.parseInt(match[1], 10) : null
                  })
                  .filter((num): num is number => num !== null && !Number.isNaN(num))

                if (existingNewLayerNums.length > 0) {
                  existingNewLayerNums.sort((a, b) => {
                    return a - b
                  })
                  for (let i = 0; i < existingNewLayerNums.length; i++) {
                    if (existingNewLayerNums[i] === nextLayerNum) {
                      nextLayerNum++
                    }
                    else if (existingNewLayerNums[i] > nextLayerNum) {
                      break
                    }
                  }
                }
                newZLevelName = `New Layer ${nextLayerNum}`
              }

              // Determine new zIndex
              if (step.zLevels.length > 0) {
                newZIndex = Math.max(...step.zLevels.map(z => z.zIndex)) + 1
              }

              const newZLevel: ZLevel = {
                id: newZLevelId,
                name: newZLevelName, // Should be defined by now
                active: true, // Default new layer to visible/active
                zIndex: newZIndex,
              }

              // When adding a new layer, make IT the current focused/editing layer,
              // and ensure it's active. Other layers' active status remains unchanged by this action directly.
              // The previous logic of making others inactive is removed, as toggling visibility
              // is now separate.
              // const updatedZLevels = step.zLevels.map(z => ({ ...z, active: false }))
              const updatedZLevels = [...step.zLevels] // Keep existing active states
              updatedZLevels.push(newZLevel)
              return { ...step, zLevels: updatedZLevels }
            }
            return step
          }),
        }
      }
      return module
    })

    return {
      modules: newModules,
      currentModuleId: moduleId, // Set as MajorCategory
      currentStepId: stepId, // Set as MinorCategory
      currentZLevelId: newZLevelId,
    }
  }),

  renameZLevel: (moduleId, stepId, zLevelId, newName) => set(state => ({
    // moduleId, stepId are MajorCategory, MinorCategory
    modules: state.modules.map(module =>
      module.id === moduleId
        ? {
            ...module,
            steps: module.steps.map(step =>
              step.id === stepId
                ? {
                    ...step,
                    zLevels: step.zLevels.map(zLevel =>
                      zLevel.id === zLevelId ? { ...zLevel, name: newName } : zLevel,
                    ),
                  }
                : step,
            ),
          }
        : module,
    ),
  })),
})

// Create the store with persist middleware and explicit typing for the hook
export const useLayerStore: UseBoundStore<StoreApi<LayerStoreType>> = create<LayerStoreType>()(
  persist(
    layerStateCreator,
    {
      name: 'reno-pilot-layer-storage', // name of the item in the storage (must be unique)
      storage: createJSONStorage(() => localStorage), // (optional) by default, 'localStorage' is used
      partialize: state => ({ modules: state.modules }), // Persist only the modules array
    },
  ),
)

// Getter to retrieve the active ZLevel object
export function getActiveZLevel(): ZLevel | undefined {
  const state = useLayerStore.getState()
  if (state.currentModuleId === null || state.currentStepId === null || state.currentZLevelId === null) {
    return undefined
  }
  const module = state.modules.find(m => m.id === state.currentModuleId)
  if (!module) {
    return undefined
  }

  const step = module.steps.find(s => s.id === state.currentStepId)
  if (!step) {
    return undefined
  }

  return step.zLevels.find(z => z.id === state.currentZLevelId)
}

// Getter to retrieve the zIndex of the currently active ZLevel
export function getActiveZLevelZIndex(): number | undefined {
  const activeZLevel = getActiveZLevel()
  return activeZLevel?.zIndex
}
