import { beforeEach, describe, expect, it } from 'vitest'
import { CircleBoundingBoxStrategy } from '../../../../../../src/core/compute/strategies/bounding-box/CircleBoundingBoxStrategy'
import { Point } from '../../../../../../src/types/core/element/geometry/point'

// Import Circle class
import { Circle } from '../../../../../../src/types/core/element/shape/ellipseShapeTypes'

// Import Rectangle class
import { Rectangle } from '../../../../../../src/types/core/element/shape/rectangleShapeTypes'

// Mock Circle class
class MockCircle extends Circle {
  constructor(x: number, y: number, radius: number) {
    super('mock-circle', new Point(x, y), { type: 'circle', radius: Math.max(0.1, Math.abs(radius)) })
    // Store the original radius for test assertions
    this._originalRadius = radius
  }

  // Store original radius for test assertions
  private _originalRadius: number

  // Override getRadius to return the original radius (including negative values)
  override getRadius(): number {
    return this._originalRadius
  }
}

// Mock non-circle element
class MockRectangle extends Rectangle {
  constructor() {
    super('mock-rectangle', new Point(0, 0), { type: 'rectangle', width: 10, height: 10 })
  }
}

/**
 * Unit tests for the CircleBoundingBoxStrategy class
 */
describe('circleBoundingBoxStrategy', () => {
  let strategy: CircleBoundingBoxStrategy

  beforeEach(() => {
    strategy = new CircleBoundingBoxStrategy()
  })

  it('should return correct shape type', () => {
    expect(strategy.getElementType()).toBe('circle')
  })

  it('should calculate bounding box correctly', () => {
    const x = 100
    const y = 100
    const radius = 50
    const circle = new MockCircle(x, y, radius)

    const boundingBox = strategy.calculateBoundingBox(circle as any)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBe(x - radius)
    expect(boundingBox.y).toBe(y - radius)
    expect(boundingBox.width).toBe(2 * radius)
    expect(boundingBox.height).toBe(2 * radius)
  })

  it('should handle zero radius', () => {
    const circle = new MockCircle(100, 100, 0)
    const boundingBox = strategy.calculateBoundingBox(circle as any)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBe(100)
    expect(boundingBox.y).toBe(100)
    expect(boundingBox.width).toBe(0)
    expect(boundingBox.height).toBe(0)
  })

  it('should handle negative radius', () => {
    const circle = new MockCircle(100, 100, -50)
    const boundingBox = strategy.calculateBoundingBox(circle as any)

    expect(boundingBox).toBeDefined()
    // Since we're using absolute value in the strategy, we expect positive values
    expect(boundingBox.x).toBe(50) // 100 - 50
    expect(boundingBox.y).toBe(50) // 100 - 50
    expect(boundingBox.width).toBe(100) // 2 * 50
    expect(boundingBox.height).toBe(100) // 2 * 50
  })

  it('should handle non-circle elements', () => {
    const rectangle = new MockRectangle()
    const boundingBox = strategy.calculateBoundingBox(rectangle as any)

    expect(boundingBox).toBeDefined()
    expect(boundingBox.x).toBe(0)
    expect(boundingBox.y).toBe(0)
    expect(boundingBox.width).toBe(0)
    expect(boundingBox.height).toBe(0)
  })
})
